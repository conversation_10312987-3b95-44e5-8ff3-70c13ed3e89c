import request from '@/utils/request';
import { useUserStoreHook } from '@/store/modules/user';
const HTTP_PREFIX = '/manage/api/v1'
const workspace_id = localStorage.getItem('workspace_id')
export function bindDevice(data) {
  return request({
    url: `${HTTP_PREFIX}/devices/${data.device_sn}/binding`,
    method: 'post',
    data
  });
}

export function getDeviceBySn(queryParams) {
  return request({
    url: `${HTTP_PREFIX}/devices/${workspace_id}/devices/${queryParams.device_sn}`,
    method: 'get',
    params: {}
  });
}

export function getPlatformInfo(queryParams) {
  return request({
    url: `${HTTP_PREFIX}/workspaces/current`,
    method: 'get',
    params: queryParams
  });
}

export function getUserInfo(queryParams) {
  return request({
    url: `${HTTP_PREFIX}/users/current`,
    method: 'get',
    params: queryParams
  });
}