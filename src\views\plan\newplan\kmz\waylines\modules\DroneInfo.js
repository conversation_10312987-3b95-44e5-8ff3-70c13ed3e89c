import { isDroneEnumValueValid } from '../../hocks';

class DroneInfo {
  hasSubEnumValue = false;
  constructor(options = {}) {
    if (!options.droneEnumValue) {
      this.hasSubEnumValue = false;
      this.wpml_droneEnumValue = this.wpml_droneSubEnumValue = null;
    } else {
      this.hasSubEnumValue = isDroneEnumValueValid(options.droneEnumValue); // 返回 true  /  false
      this.wpml_droneEnumValue = this.hasSubEnumValue
        ? options.droneEnumValue
        : null;
      this.wpml_droneSubEnumValue = this.hasSubEnumValue
        ? options.droneSubEnumValue
        : null;
    }
  }

  setDroneEnumValue(value) {
    this.wpml_droneEnumValue = value;
  }
  getDroneEnumValue() {
    return this.wpml_droneEnumValue;
  }

  setDroneSubEnumValue(value) {
    this.wpml_droneSubEnumValue = value;
  }
  getDroneSubEnumValue() {
    return this.wpml_droneSubEnumValue;
  }
}
export { DroneInfo };
