<!-- 版本信息 -->
<template>
  <div>
    <div v-loading="tableLoading">
      <el-scrollbar
        v-if="tablePage.length"
        ref="projectBarRef"
        class="message-content"
      >
        <div
          v-infinite-scroll="loadData"
          :infinite-scroll-disabled="isInfiniteScrollDisabled"
          :infinite-scroll-distance="10"
        >
          <div
            v-for="(item, index) in tablePage"
            :key="index"
            class="content-item"
          >
            <div class="content-item-left">
              <div v-if="!item.hasRead" class="news-status" />
              <div class="message-icon">
                <img src="@/assets/version.png" class="w-full h-full" />
              </div>
              <div class="content-item-time">
                <div class="time-title">
                  {{ $t('page.dialog.versionUpdate') }}
                </div>
                <div class="left-subtitle center-content">
                  {{ $t('page.dialog.versionNumber') }}: v{{ item.version }}，{{
                    $t('page.dialog.updatedContent')
                  }}：
                  <div class="content-text">{{ setText(item) }}</div>
                </div>
                <div class="left-subtitle">{{ item.noticeTime }}</div>
              </div>
            </div>
            <div class="content-item-right">
              <el-button
                class="handle-button"
                type="primary"
                @click="handleClick(item)"
              >
                {{ $t('page.check') }}
              </el-button>
            </div>
          </div>
        </div>
      </el-scrollbar>
      <el-empty v-else />
    </div>
    <div class="page-foot">
      {{ $t('page.dialog.currentVersion') }}: v{{ version }}
    </div>
    <PopVesionMessage ref="popVesionMessageRef" @updateCount="updateCount" />
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n';
import PopVesionMessage from '../components/popVesionMessage.vue';
import { getVersionMessagePage } from '@/api/messageCenter';
import { onMounted } from 'vue';

const { locale } = useI18n();
const projectBarRef = ref('projectBarRef');
const totalNum = ref('');
const tableLoading = ref(false);
const tablePage = ref([]);
const isInfiniteScrollDisabled = ref(false);
const viewData = ref({});
const version = import.meta.env.VITE_CLIENT_VERSION || '';
const emit = defineEmits(['changeCount']);

const popVesionMessageRef = ref('popVesionMessageRef');

const tableQueryData = ref({
  pageSize: 10,
  pageNum: 1
});

const handleClick = data => {
  popVesionMessageRef.value.handleOpen(data);
  viewData.value = data;
};

const setText = data => {
  let textContent = '';
  data.updateContentPointList.forEach((item, index) => {
    textContent += `\u00A0(${index + 1}) ${
      locale.value === 'zh' ? item.contentOfChinese : item.contentOfEnglish
    } `;
  });
  return textContent;
};

const getTablePage = async () => {
  tableLoading.value = true;
  isInfiniteScrollDisabled.value = true;
  try {
    const res = await getVersionMessagePage(tableQueryData.value);
    tablePage.value = Object.assign(tablePage.value, res.data.records);
    totalNum.value = res.data.pages; // 总页码
    emit('changeCount');
    nextTick(() => {
      if (res.data.total) {
        closeTooltip();
      }
    });
    isInfiniteScrollDisabled.value = false;
  } catch (err) {
    console.log('获取分页信息错误', err);
  }
  tableLoading.value = false;
};

const loadData = () => {
  if (tableQueryData.value.pageNum < totalNum.value) {
    tableQueryData.value.pageNum++;
    getTablePage();
  }
};

const closeTooltip = () => {
  projectBarRef.value.wrapRef.onscroll = () => {
    const list = document.getElementsByClassName('el-tooltip__popper');
    if (list.length > 0) {
      list[list.length - 1].style.display = 'none';
    }
  };
};

const updateCount = () => {
  viewData.value.hasRead = 1;
  emit('changeCount');
};

onMounted(() => {
  getTablePage();
});
</script>

<style lang="scss" scoped>
.message-content {
  height: 455px;

  :deep(.el-scrollbar__wrap) {
    overflow-x: auto;
  }
}

.content-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border-bottom: 1px solid #ececec;

  .left-subtitle {
    line-height: 25px;
  }

  .content-text {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .center-content {
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: grid;
    width: 100%;
  }

  .content-item-left {
    display: flex;
    align-items: center;
    width: 100%;

    .message-icon {
      min-width: 64px;
      min-height: 64px;
      width: 64px;
      height: 64px;
      background-color: rgba($color: #097efc, $alpha: 0.1);
      color: #097efc;
      margin: 0 15px;
      padding: 12px;
      border-radius: 50%;
    }

    .content-item-time {
      font-size: 14px;
      color: #999999;
      line-height: 14px;
      font-weight: 400;
      margin-right: 15px;

      .time-title {
        font-size: 16px;
        // line-height: 40px;
        margin-bottom: 10px;
        font-weight: 600;
        color: #333;
      }
    }
  }

  .content-item-right {
    width: 70px;
    margin-left: 10px;
  }
}

.news-status {
  width: 10px;
  height: 10px;
  color: red;
  background: red;
  border-radius: 50%;
  min-width: 10px;
}
</style>
