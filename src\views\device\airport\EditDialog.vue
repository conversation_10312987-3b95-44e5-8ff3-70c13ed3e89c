<template>
  <el-dialog
    :title="title"
    v-if="visible"
    :model-value="visible"
    width="700px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form class="app-form" ref="dataFormRef" :model="form" :rules="rules" label-width="120px">
      <el-row :gutter="20">
        <!-- <el-col :span="12"> -->
        <el-form-item label="机场型号" prop="device_name">
          <el-input
            disabled
            v-model="form.device_name"
            placeholder="请输入机场型号"
            maxlength="64"
            @blur="form.device_name = $event.target.value.trim()"
          />
        </el-form-item>
        <el-form-item label="设备SN" prop="device_sn">
          <el-input
            disabled
            v-model="form.device_sn"
            placeholder="请输入型号"
            maxlength="64"
            @blur="form.device_sn = $event.target.value.trim()"
          />
        </el-form-item>
        <el-form-item label="机场名称" prop="nickname">
          <el-input
            v-model="form.nickname"
            placeholder="请输入机场名称"
            maxlength="50"
            @blur="form.nickname = $event.target.value.trim()"
          />
        </el-form-item>
        <el-form-item label="机场覆盖范围" prop="max_flight_distance">
          <el-input-number
            controls-position="right"
            :precision="0"
            placeholder="请输入机场覆盖范围"
            style="width: 180px;margin-right: 5px"
            v-model="form.max_flight_distance"
            :min="100"
            :max="10000"
          /> 
          米
        </el-form-item>
        <!-- <el-form-item label="机场地址" prop="airport_address">
          <el-input
            v-model="form.airport_address"
            placeholder="请输入机场地址"
            maxlength="50"
            @blur="form.airport_address = $event.target.value.trim()"
          />
        </el-form-item> -->
        <el-form-item label="固件版本" prop="firmware_version">
          <el-input
            disabled
            v-model="form.firmware_version"
            placeholder="请输入固件版本"
            maxlength="64"
            @blur="form.firmware_version = $event.target.value.trim()"
          />
        </el-form-item>
        <el-form-item label="备降点经纬度" prop="alternate_land_point_coordinate">
          <el-input
            disabled
            v-model="form.alternate_land_point_coordinate"
            placeholder="请输入备降点经纬度"
            maxlength="64"
            @blur="form.alternate_land_point_coordinate = $event.target.value.trim()"
          />
        </el-form-item>
        <el-form-item label="无人机名称" prop="child_device_sn">
          <el-input
            disabled
            v-model="form.children.nickname"
            placeholder="请输入无人机名称"
            maxlength="64"
            @blur="form.children.nickname = $event.target.value.trim()"
          />
        </el-form-item>
        <el-form-item label="无人机型号" prop="device_name">
          <el-input
            disabled
            v-model="form.children.device_name"
            placeholder="请输入无人机型号"
            maxlength="64"
            @blur="form.children.device_name = $event.target.value.trim()"
          />
        </el-form-item>
         <el-form-item label="加入组织时间" prop="bound_time">
          <el-input
            disabled
            v-model="form.bound_time"
            placeholder="请输入加入组织时间"
            maxlength="64"
            @blur="form.bound_time = $event.target.value.trim()"
          />
        </el-form-item>
        <!-- <el-form-item label="机场图片" required>
          <div style="display: flex;flex-direction: column;">
            <div v-if="form.image" class="mt-[16px]" style="min-width: 100px">
              <el-image
                style="max-width: 100px; max-height: 100px"
                :src="form.image"
                :previewSrcList="[form.image]"
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                fit="cover"
              />
            </div>
            <div>
              <el-upload
                class="upload-area"
                v-model="form.uploadImg"
                limit="1"
                :show-file-list="false"
                list-type="picture"
                accept=".png,.jpg"
                :before-upload="handleBeforeUpload"
                :on-change="uploadFile"
              >
                <template #trigger>
                  <el-button type="primary">上传图片</el-button>
                </template>
                <template #tip>
                  <div class="el-upload__tip">
                    支持JPG, PNG, 最大不超过3MB
                  </div>
                </template>
              </el-upload>
            </div>
          </div>
        </el-form-item> -->
        <!-- </el-col> -->
        <!-- <el-col :span="12">
          <el-form-item label="机场型号" prop="type">
            <el-select
              v-model="form.type"
              placeholder="请选择应用类型"
              :disabled="form.id"
            >
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col> -->
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-loading="loading">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { updateDeviceInfo } from '@/api/devices';
import { number } from 'echarts';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  formData: {
    type: Object,
    default(rawProps) {
      return {};
    }
  }
});
const form = reactive({});
const dataFormRef = ref(ElForm);

watch(
  () => props.formData,
  (newVal, oldVal) => {
    Object.assign(form, newVal);
  },
  { deep: true }
);
const emit = defineEmits(['update:visible', 'submit']);
const rules = reactive({
  nickname: [{ required: true, message: '请输入机场名称', trigger: 'blur' }],
  max_flight_distance: [{ required: true, message: '请输入机场覆盖范围', trigger: 'blur' }],
  airport_address: [{ required: true, message: '请输入机场地址', trigger: 'blur' }],
});
defineExpose({ setDefaultValue });
// 设置默认值
function setDefaultValue() {
  if (!form.type && typeOptions.value.length > 0) {
    form.type = typeOptions.value[0].value;
  }
}
const typeOptions = ref([]);
const loading = ref(false);

function getTypeOptiopn() {
  typeOptions.value = [];
}

	/**
 * 限制用户上传文件的格式和大小
 */
 function handleBeforeUpload(file) {
  let typeList = ['image/jpeg','image/jpeg','image/png']
  if (typeList.indexOf(file.type) == -1) {
    ElMessage.warning('请上传jpg, png类型的文件');
    return false;
  }
  if (file.size > 3 * 1048 * 1048) {
    ElMessage.warning('上传文件不能大于3M');
    return false;
  }
  return true;
}

/**
 * 自定义图片上传
 *
 * @param options
 */
 async function uploadFile(options) {
  const reader = new FileReader();
  reader.onload = () => {
    form.uploadImg = reader.result;
    form.image = reader.result;
  };
  reader.readAsDataURL(options.raw);
}


// 关闭弹窗
function closeDialog() {
  resetForm();
  emit('update:visible', false);
}
/**
 * 重置表单
 */
function resetForm() {
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  loading.value = false;
  Object.keys(form).map(key => {
    delete form[key];
  });
}

function handleSubmit() {
  dataFormRef.value.validate(isValid => {
    if (isValid) {
      let params = { nickname: form?.nickname, max_flight_distance: form.max_flight_distance };

      loading.value = true;
      updateDeviceInfo(form.device_sn, params)
        .then(res => {
          loading.value = false;

          ElMessage.success('更新成功');

          closeDialog();
          emit('submit');
        })
        .catch(e => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

onMounted(() => {
  getTypeOptiopn();
});
</script>
