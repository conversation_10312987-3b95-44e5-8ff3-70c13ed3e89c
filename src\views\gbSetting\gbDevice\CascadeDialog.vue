<template>
  <el-dialog
    title="国标平台设置"
    v-if="visible"
    :model-value="visible"
    width="500px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form v-loading="getLoading" class="app-form" ref="dataFormRef" :model="form" :rules="rules" label-width="120px">
      <el-row :gutter="20">
        <el-form-item label="平台来源" prop="source">
          <el-select v-model="form.source" placeholder="请选择平台来源" clearable style="width: 330px">
            <el-option
              v-for="(item, index) in optionData.gbPlatform"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-row>
      <el-row :gutter="20">
        <el-form-item label="服务器IP" prop="server_ip">
          <el-input style="width: 330px" v-model="form.server_ip" placeholder="请输入服务器IP地址" maxlength="64" />
        </el-form-item>
      </el-row>
      <el-row :gutter="20">
        <el-form-item label="服务器端口" prop="server_port">
          <el-input style="width: 330px" v-model="form.server_port" placeholder="请输入服务器端口" maxlength="8" />
        </el-form-item>
      </el-row>
      <el-row :gutter="20">
        <el-form-item label="服务器ID" prop="server_id">
          <el-input style="width: 330px" v-model="form.server_id" placeholder="请输入服务器ID" maxlength="64" />
        </el-form-item>
      </el-row>
      <el-row :gutter="20">
        <el-form-item label="视频类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择视频类型" clearable style="width: 330px">
            <el-option
              v-for="(item, index) in optionData.gbVideoType"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-row>
      <el-row :gutter="20">
        <el-form-item label="流传输类型" prop="stream_transfer_type">
          <el-select v-model="form.stream_transfer_type" placeholder="请选择流传输类型" clearable style="width: 330px">
            <el-option
              v-for="(item, index) in optionData.streamTransferType"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-row>
      <el-row :gutter="20">
        <el-form-item label="推流方式">
          <el-select v-model="form.url_type" placeholder="请选择推流方式" clearable style="width: 330px">
            <el-option
              v-for="(item, index) in optionData.gbUrlType"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-row>
      <el-row :gutter="20">
        <el-form-item label="代理密码" prop="agent_password">
          <el-input style="width: 330px" v-model="form.agent_password" placeholder="请输入代理密码" maxlength="64" />
        </el-form-item>
      </el-row>
      <el-row :gutter="20">
        <el-form-item label="机场本地端口" prop="dock_local_port">
          <el-input
            style="width: 330px"
            v-model="form.dock_local_port"
            placeholder="请输入机场本地端口"
            maxlength="64"
          />
        </el-form-item>
      </el-row>
      <el-row :gutter="20">
        <el-form-item label="无人机本地端口" prop="drone_local_port" required>
          <el-input
            style="width: 330px"
            v-model="form.drone_local_port"
            placeholder="请输入无人机本地端口"
            maxlength="64"
          />
        </el-form-item>
      </el-row>
      <el-row :gutter="20">
        <el-form-item label="视频起始编码" prop="no">
          <el-input style="width: 330px" v-model="form.no" placeholder="请输入视频起始编码" maxlength="64" />
        </el-form-item>
      </el-row>
      <el-row :gutter="20">
        <el-form-item label="是否启用" prop="enabled">
          <el-switch v-model="form.enabled" />
        </el-form-item>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-loading="loading">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { getDeviceVideoConfig, updateDeviceVideoConfig } from '@/api/wayline';
import optionData from '@/utils/option-data';
const loading = ref(false);
const getLoading = ref(false);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const form = reactive({
  server_ip: '',
  server_port: '',
  server_id: '',
  enabled: false,
  source: '',
  type: '',
  stream_transfer_type: '',
  url_type: '',
  agent_password: '',
  dock_local_port: '',
  drone_local_port: '',
  no: ''
});

// IP地址验证正则表达式
const ipPattern = /^(\d{1,3}\.){3}\d{1,3}$/;
// 端口号验证正则表达式
const portPattern = /^([1-9]\d{0,4})$/;

// 表单校验规则
const rules = reactive({
  source: [{ required: true, message: '请选择平台来源', trigger: 'change' }],
  server_ip: [
    { required: true, message: '请输入服务器IP地址', trigger: 'blur' },
    { pattern: ipPattern, message: '请输入正确的IP地址格式', trigger: 'blur' }
  ],
  server_port: [
    { required: true, message: '请输入服务器端口', trigger: 'blur' },
    { pattern: portPattern, message: '端口号必须为1-65535之间的数字', trigger: 'blur' }
  ],
  server_id: [{ required: true, message: '请输入服务器ID', trigger: 'blur' }],
  type: [{ required: true, message: '请选择视频类型', trigger: 'change' }],
  // url_type: [{ required: true, message: '请选择推流方式', trigger: 'change' }],
  dock_local_port: [
    { required: true, message: '请输入机场本地端口', trigger: 'blur' },
    { pattern: portPattern, message: '端口号必须为1-65535之间的数字', trigger: 'blur' }
  ],
  drone_local_port: [
    { required: true, message: '请输入无人机本地端口', trigger: 'blur' },
    { pattern: portPattern, message: '端口号必须为1-65535之间的数字', trigger: 'blur' }
  ]
});

const dataFormRef = ref(null);

watch(
  () => props.visible,
  newVal => {
    if (newVal) {
      initDeviceVideoConfig();
    }
  },
  { immediate: true }
);

const emit = defineEmits(['update:visible', 'setCascade']);

function initDeviceVideoConfig() {
  getLoading.value = true;
  getDeviceVideoConfig()
    .then(res => {
      Object.assign(form, res);
    })
    .finally(() => {
      getLoading.value = false;
    });
}

// 关闭弹窗
function closeDialog() {
  resetForm();
  emit('update:visible', false);
}

/**
 * 重置表单
 */
function resetForm() {
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
    dataFormRef.value.clearValidate();
  }
  loading.value = false;
  // 重置表单数据
  Object.keys(form).forEach(key => {
    if (key === 'enabled') {
      form[key] = false;
    } else {
      form[key] = '';
    }
  });
}

function handleSubmit() {
  dataFormRef.value.validate(isValid => {
    if (isValid) {
      const params = {
        server_ip: form.server_ip,
        server_port: form.server_port,
        server_id: form.server_id,
        enabled: form.enabled,
        source: form.source,
        type: form.type,
        stream_transfer_type: form.stream_transfer_type,
        url_type: form.url_type,
        agent_password: form.agent_password,
        dock_local_port: form.dock_local_port,
        drone_local_port: form.drone_local_port,
        no: form.no
      };

      loading.value = true;
      updateDeviceVideoConfig(params)
        .then(res => {
          loading.value = false;
          ElMessage.success('更新成功');
          emit('setCascade');
          closeDialog();
        })
        .catch(e => {
          loading.value = false;
          ElMessage.error(e.message || '更新失败');
        });
    } else {
      loading.value = false;
    }
  });
}
</script>
