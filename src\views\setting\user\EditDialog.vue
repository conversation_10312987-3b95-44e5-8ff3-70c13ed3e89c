<template>
  <el-dialog
    :title="title"
    v-if="visible"
    :model-value="visible"
    width="800px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form ref="dataFormRef" :model="form" :rules="rules" label-width="100px" v-loading="loading">
      <div class="user-add-from">
        <div class="addFrom-left">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="username" label="账号名称" class="mt-[16px]">
                <el-input
                  maxlength="20"
                  minlength="4"
                  v-model="form.username"
                  placeholder="请输入账号名称"
                  :disabled="title == '编辑用户'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="密码"
                prop="password"
                class="mt-[16px]"
                :rules="[{ required: form.id ? false : true, message: '密码不能为空', trigger: 'blur' }]"
              >
                <el-input
                  v-model="form.password"
                  type="password"
                  show-password
                  placeholder="请输入密码"
                  maxlength="20"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="nickname" label="用户名称" class="mt-[16px]">
                <el-input maxlength="50" v-model="form.nickname" placeholder="请输入用户名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="组织选择" prop="dept_id" class="mt-[16px]">
                <el-tree-select
                  v-model="form.dept_id"
                  :data="deptOptions"
                  :props="{ value: 'id', label: 'name', children: 'children' }"
                  value-key="id"
                  placeholder="请选择组织"
                  check-strictly
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="电子邮箱" prop="email" class="mt-[16px]">
                <el-input v-model="form.email" type="text" placeholder="请输入电子邮箱" maxlength="30" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="手机号码" prop="mobile" class="mt-[16px]">
                <el-input v-model="form.mobile" type="text" placeholder="请输入手机号码" maxlength="11" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否飞手" class="mt-[16px]" prop="pilot">
                <el-radio-group v-model="form.pilot">
                  <el-radio
                    v-for="(item, index) in optionData.isPilotOption"
                    :key="index"
                    :value="item.value"
                    :label="item.value"
                    >{{ item.label }}</el-radio
                  >
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="成员状态" class="mt-[16px]">
                <el-radio-group v-model="form.status">
                  <el-radio v-for="item in optionData.deptStatusOption" :key="item.value" :label="item.value">{{
                    item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <!-- 超管权限 -->
            <el-col :span="12" v-if="is_super_admin">
              <el-form-item label="角色选择" prop="role_ids" class="mt-[16px]" ref="roleIdsFormItem">
                <el-select v-model="form.role_ids" multiple placeholder="请选择角色" style="width: 100%">
                  <el-option v-for="item in roleList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-loading="loading">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElForm, ElMessage } from 'element-plus';
import optionData from '@/utils/option-data';
import handleTree from '@/utils/handleTree';
import { useUserStoreHook } from '@/store/modules/user';
import { addUser, updateUser } from '@/api/system/user';
import { listDept, getRolePage } from '@/api/system/dept';
const userStore = useUserStoreHook();
const { userData } = userStore;
const { workspace_id, is_super_admin } = userData;
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  formData: {
    type: Object,
    default(rawProps) {
      return {};
    }
  }
});
const form = reactive({
  status: 0,
  pilot: null,
  role_ids: []
});
const roleIdsFormItem = ref(null); // 获取 el-form-item 的 ref
const dataFormRef = ref(ElForm);
const deptList = ref([]);
const roleList = ref([]);
const deptOptions = ref([]);

watch(
  () => props.visible,
  (newVal, oldVal) => {
    if (newVal) {
      loading.value = true;
      setTimeout(() => {
        dataFormRef.value.clearValidate();
        loading.value = false;
      }, 50);
    }
  },
  { deep: true }
);

watch(
  () => props.formData,
  (newVal, oldVal) => {
    if (newVal.pilot !== undefined) {
      newVal.pilot = newVal.pilot == true ? 1 : 0;
    }
    Object.assign(form, newVal);
  },
  { deep: true }
);

const emit = defineEmits(['update:visible', 'submit']);
const rules = reactive({
  username: [
    {
      required: true,
      message: '账号名称不能为空',
      trigger: 'blur'
    },
    {
      pattern: /[A-Za-z0-9,.;?@]+/,
      message: '账号名称不能包含中文和特殊字符',
      trigger: 'blur'
    },
    {
      pattern: /^[^#%&*\/|:<>?\"]*$/,
      message: '账号名称不能包含特殊符号',
      trigger: 'blur'
    },
    {
      pattern: /^[^\u4e00-\u9fa5]+$/,
      message: '账号名称不能包含中文',
      trigger: 'blur'
    },
    {
      min: 4,
      max: 20,
      message: '账号名称长度为4-20个字符',
      trigger: 'blur'
    }
  ],
  nickname: [
    {
      required: true,
      pattern: /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/,
      message: '成员名称只能包含中文、英文、数字',
      trigger: 'blur'
    }
  ],
  dept_id: [{ required: true, message: '组织不能为空', trigger: ['blur', 'change'] }],
  password: [
    {
      pattern:
        /^(?=.*\d)(?=.*?[a-zA-Z])(?=.*[~`!@#$%^&*()_\-+=<>?:"{}|,.\/;'\[\]·~@#%&*])[\da-zA-Z~`!@#$%^&*()_\-+=<>?:"{}|,./;'\[\]·~@#%&*]{6,20}$/,
      message: '请输入6-20位由数字+字母+符号组成的密码',
      trigger: 'blur'
    },
    {
      min: 6,
      max: 20,
      message: '请输入6-20位由数字+字母+符号组成的密码',
      trigger: 'blur'
    }
  ],
  role_ids: [{ required: true, message: '角色不能为空', trigger: ['blur', 'change'] }],
  email: [
    {
      pattern: /\w[-\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\.)+[A-Za-z]{2,14}/,
      message: '请输入正确的邮箱地址',
      trigger: 'blur'
    }
  ],
  mobile: [
    {
      pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
      message: '请输入正确的手机号码',
      trigger: 'blur'
    }
  ],
  pilot: [
    {
      required: true,
      message: '请选择是否飞手',
      trigger: ['blur', 'change']
    }
  ]
});

const loading = ref(false);

// 关闭弹窗
function closeDialog() {
  resetForm();
  emit('update:visible', false);
}
/**
 * 重置表单
 */
function resetForm() {
  dataFormRef?.value && dataFormRef.value.resetFields();
  dataFormRef?.value && dataFormRef.value.clearValidate();
  loading.value = false;
  Object.keys(form).map(key => {
    delete form[key];
  });
  form.status = 0;
  form.pilot = null;
}

function handleSubmit() {
  dataFormRef.value.validate(isValid => {
    if (isValid) {
      let params = { ...form };
      console.log('此时标题', props.title, '要提交的参数', params);
      loading.value = true;
      if (props.title == '新增用户') {
        addUser({
          ...params,
          pilot: params.pilot === null ? false : params.pilot,
          workspaceId: workspace_id
        })
          .then(res => {
            loading.value = false;
            ElMessage.success('新增成功');
            closeDialog();
            emit('submit');
          })
          .catch(e => {
            loading.value = false;
          });
      } else {
        //编辑成员
        updateUser(params)
          .then(res => {
            loading.value = false;
            ElMessage.success('更新成功');
            closeDialog();
            emit('submit');
          })
          .catch(e => {
            loading.value = false;
          });
      }
    } else {
      loading.value = false;
    }
  });
}

onMounted(() => {
  //获取组织列表
  listDept().then(response => {
    deptOptions.value = handleTree(response, 'id');
  });
  //获取角色列表
  getRolePage().then(response => {
    roleList.value = response.list || [];
  });
});

defineExpose({ resetForm });
</script>
<style scoped lang="scss">
.input-serach {
  width: 200px;
}
.app-form {
  .select-time {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 5px 0;
  }
  ::v-deep {
    .el-input-number,
    .el-select {
      width: 100%;
    }
    .el-input-number .el-input__inner {
      text-align: left;
    }
    .el-input-number.is-controls-right .el-input__wrapper {
      padding-left: 11px;
    }
    .el-date-editor.el-input,
    .el-date-editor.el-input__wrapper {
      width: 100%;
    }
  }
}
</style>
