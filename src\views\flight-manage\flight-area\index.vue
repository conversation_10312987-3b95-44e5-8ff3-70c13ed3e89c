<template>
  <div class="flight-area-container">
    <div id="container"></div>
    <div class="left-wrap" :class="{ 'left-wrap-hide': panelRect.show }">
      <div class="left-content-panel bg-light-blue">
        <div class="action-wrap">
          <div class="action-list">
            <div
              class="action-item"
              :class="{ 'action-active': flightAreaRect.nfz }"
              @click="changeShow(FLIGHTAREATYPE.NFZ)"
            >
              限飞区
            </div>
            <div
              class="action-item"
              :class="{ 'action-active': flightAreaRect.dfence }"
              @click="changeShow(FLIGHTAREATYPE.DFENCE)"
            >
              作业区
            </div>
          </div>
        </div>
        <div class="action-content-wrap bg-dark-blue">
          <!-- <div style="color: aliceblue"> {{ flightDataListaRect.list.length }} </div> -->
          <div v-show="flightAreaRect.dfence" class="action-content">
            <div class="area-item nodata" v-if="!flightDataListaRect.dfence.length">您还未添加作业区</div>
            <div v-for="df_data in flightDataListaRect.dfence" :key="df_data">
              <AreaItem
                class="area-item"
                :flightData="df_data"
                @onZoomToAreaHandle="zoomToAreaHandle"
                @onDeleteHandle="deleteHandle"
                @onSetAreaVisibleHandle="setAreaVisibleHandle"
              />
            </div>
          </div>
          <div v-show="flightAreaRect.nfz" class="action-content">
            <div class="area-item nodata" v-if="!flightDataListaRect.nfz.length">您还未添加限飞区</div>
            <div v-for="nfz_data in flightDataListaRect.nfz" :key="nfz_data">
              <AreaItem
                class="area-item"
                :flightData="nfz_data"
                @onZoomToAreaHandle="zoomToAreaHandle"
                @onDeleteHandle="deleteHandle"
                @onSetAreaVisibleHandle="setAreaVisibleHandle"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="hide-btn" @click="onChangeHadle">
        <el-image style="width: 20px; height: 20px" :src="panelRect.imageUrl" fit="fill" />
      </div>
    </div>
    <TooolBar class="toolbar" />
    <GeomInfoDialog class="GeomInfoDialog" />
  </div>
</template>
<script>
export default { name: 'flight-area' };
</script>
<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import AreaItem from '../components/AreaItem.vue';
import TooolBar from '../components/TooolBar.vue';
import GeomInfoDialog from '../components/GeomInfoDialog.vue';
import {
  viewer,
  initMap,
  GEOMTYPE,
  initMapClick,
  initDataList,
  FLIGHTAREATYPE,
  flightDataListaRect,
  COLOR_TYPE_STRING,
  editStatue,
  dialogDataRect
} from './flightAreaHandle';
import {
  getOrCreateCesiumEngineInstance,
  CesiumLayerManager,
  imglayer,
  cialayer,
  zoomtoLonglats,
  zoomtoLonglat
} from '../../../components/Cesium/libs/cesium';
import { addFlightArea, updateFlighArea, getFllightAreas, deleteFlighArea } from '@/api/flightArea';
import { generateKey } from '@/utils';
import { ElMessage, ElMessageBox } from 'element-plus';
import { cancelCircleEdit } from '@/components/Cesium/libs/cesium/superEdit/CircleEditer';
import { cancelPolygonEdit } from '@/components/Cesium/libs/cesium/superEdit/PolygonEditer';
import { cancelPolygonAdd } from '@/components/Cesium/libs/cesium/superEdit/PolygonCreater';
import { cancelCircleAdd } from '@/components/Cesium/libs/cesium/superEdit/CircleCreater';
const rightArrow = new URL('@/assets/plan/right-arrow.png', import.meta.url).href;
const leftArrow = new URL('@/assets/plan/left-arrow.png', import.meta.url).href;
//#region 构建对象
let flightAreaRect = reactive({
  nfz: true,
  dfence: false
});
const flightAreaData = reactive({
  title: 'set',
  id: '',
  time: '',
  type: FLIGHTAREATYPE.NFZ
});

let panelRect = reactive({
  show: false,
  imageUrl: rightArrow
});
//#endregion

//#region 方法

/**
 * 定位到区域 OK
 * @param data
 */
function zoomToAreaHandle(data) {
  if (!viewer) {
    return;
  }
  let area_id = data.area_id;
  if (!area_id) {
    ElMessage.warning('这条记录中未找到对应id信息');
  }
  resetDialogSatue();
  if (data.content.geometry.type === 'Circle') {
    let radius = data.content.geometry.radius + 1000;
    zoomtoLonglat(viewer, data.content.geometry.coordinates, radius);
  } else if (data.content.geometry.type === 'Polygon') {
    zoomtoLonglats(viewer, data.content.geometry.coordinates[0], 1000);
  }
}

const resetDialogSatue = () => {
  try {
    dialogDataRect.visible = false;
    editStatue.value = false;
    cancelCircleEdit();
    cancelPolygonEdit();
    cancelPolygonAdd();
    cancelCircleAdd();
  } catch (error) {
    dialogDataRect.visible = false;
    editStatue.value = false;
    cancelCircleEdit();
    cancelPolygonEdit();
    cancelPolygonAdd();
    cancelCircleAdd();
  }
};

/**
 * 移除区域 OK
 * @param data
 */
function deleteHandle(data) {
  resetDialogSatue();
  // 提示
  ElMessageBox.confirm('确定删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    closeOnClickModal: false,
    type: 'warning'
  })
    .then(() => {
      // 删除
      deleteFlighArea(data.area_id)
        .then(res => {
          ElMessage.success('删除成功');
          initDataList();
        })
        .catch(err => {
          ElMessage.error('删除失败:' + err);
          initDataList();
        });
    })
    .catch(() => {
      initDataList();
      console.log('取消删除', data);
    });
}

/**
 * 显示或者隐藏对应区块
 */
function setAreaVisibleHandle(options) {
  const { data = null, flag = true } = options;
  if (!data) {
    return;
  }
  resetDialogSatue();
  // 这里需要根据 flag 的布尔值 设置提示词
  const confirmTitle = flag ? '确定开启当前飞行区吗？' : '确定关闭当前飞行区吗？';
  ElMessageBox.confirm(confirmTitle, '提示', {
    confirmButtonText: '确定',
    closeOnClickModal: false,
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      let area_id = data.area_id;
      if (!area_id) {
        ElMessage.warning('这条记录中未找到对应id信息');
      }
      const updateData = data;
      updateData.status = flag;
      updateData.content.properties.color = getColorString(updateData);
      updateFlighArea(updateData, area_id)
        .then(res => {
          ElMessage.success(flag ? '开启成功' : '关闭成功');
          initDataList();
        })
        .catch(() => {
          let entity = viewer.entities.getById(area_id);
          entity.show = !flag;
          data.status = !flag;
        });
    })
    .catch(() => {
      data.status = !flag;
    });
}
const getColorString = option => {
  try {
    if (option.type === FLIGHTAREATYPE.DFENCE) {
      return COLOR_TYPE_STRING[FLIGHTAREATYPE.DFENCE];
    } else if (option.type === FLIGHTAREATYPE.NFZ) {
      return COLOR_TYPE_STRING[FLIGHTAREATYPE.NFZ];
    }
  } catch (error) {
    return COLOR_TYPE_STRING[FLIGHTAREATYPE.DFENCE];
  }
};

function changeShow(val) {
  switch (val) {
    case FLIGHTAREATYPE.NFZ:
      flightAreaRect.nfz = true;
      flightAreaRect.dfence = false;
      break;
    case FLIGHTAREATYPE.DFENCE:
      flightAreaRect.dfence = true;
      flightAreaRect.nfz = false;
      break;
    default:
      flightAreaRect.nfz = true;
      flightAreaRect.dfence = false;
  }
}

function onChangeHadle() {
  panelRect.show = !panelRect.show; // 切换显示
  if (panelRect.show) {
    panelRect.imageUrl = leftArrow;
  } else {
    panelRect.imageUrl = rightArrow;
  }
}

function init() {
  initMap();
  ElMessage({
    message: '数据初始化中请稍后！',
    type: 'info',
    duration: 1000 // 单位为毫秒，这里设置显示3秒
  });
  initDataList({ zoom: true });
}

//#endregion
onMounted(() => {
  init();
  editStatue.value = false;
  dialogDataRect.visible = false;
  initMapClick();
});

onUnmounted(() => {});
</script>
<style lang="scss" scoped>
@import '@/styles/common/global.scss';

#container {
  width: 100%;
  height: 100%;
  background-color: #171717;
  z-index: 1;
  position: absolute;
  left: 0px;
  top: 0px;
  right: 0px;
  bottom: 0px;
}

.flight-area-container {
  width: 100%;
  height: 100%;
  background-color: #171717;
  user-select: none;
  position: relative;
}
.left-wrap {
  height: 100%;
  width: 350px;
  position: absolute;
  left: 0px;
  transition: left 0.3s ease-in-out; /* 添加过渡效果 */
  top: 0px;
}
.hide-btn {
  position: absolute;
  left: 350px;
  top: calc(50%);
  padding: 0 5px;
  height: 100px;
  background-color: #011129d7;
  display: flex;
  flex-direction: column;
  justify-content: center;
  transition: left 0.3s ease-in-out; /* 添加过渡效果 */
  z-index: 2;
  &:hover {
    background-color: #011129;
    color: #ffffff;
    cursor: pointer !important;
  }
  clip-path: polygon(0 0%, 100% 15%, 100% 85%, 0 100%);
}

.left-content-panel {
  height: 100%;
  width: 350px;
  position: absolute;
  left: 0px;
  top: 0px;
  transition: left 0.3s ease-in-out; /* 添加过渡效果 */
  z-index: 2;
  background-color: #011129;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  background-color: #1f2f49;
}

.left-wrap-hide {
  left: -350px; /* 收起时宽度为 0 */
}

.left-content-panel .title {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 600;
  color: #fff;
}

.left-content-panel .tab-wrap {
  width: 100%;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #a83c3c;
}

.left-content-panel .action-wrap {
  width: 100%;
  height: 38px;
  display: flex;
  justify-content: center;
}
.left-content-panel .action-wrap .action-list {
  width: 100%;
  display: flex;
  justify-content: center; /* 使得子元素在容器中水平居中 */
  align-items: center; /* 如果需要同时垂直居中 */
}

.action-item {
  position: relative; /* 为伪元素定位准备 */
  top: -2px;
  width: 50%;
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #98a2b3;
  line-height: 38px;
  font-weight: 400;
  padding: 0px 5px; /* 添加一些内边距使其更易点击 */
  padding-bottom: 5px;
  display: flex;
  justify-content: center;
  cursor: pointer; /* 鼠标变为手型，表示可点击 */
}

.action-item::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  height: 5px;
  margin-bottom: 0px;
  background-color: #2e90fa; /* 横线的颜色 */
  transition: width 0.3s ease; /* 动画效果 */
}

.action-item.action-active::after {
  width: 100%; /* 选中时横线扩展到整个宽度 */
}
.action-active {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #2e90fa;
  text-align: center;
  font-weight: 400;
}

.toolbar {
  position: absolute;
  padding: 5px;
  right: 10px;
  top: 20px;
  z-index: 2;
}
.GeomInfoDialog {
  position: absolute;
  right: 150px;
  top: 20px;
  z-index: 2;
}

.action-content-wrap {
  width: 100%;
  height: calc(100% - 38px);
  background-color: #001129 !important;
}
.action-content-wrap .action-content {
  padding: 0px 10px;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  color: #2e90fa;
}

.area-item {
  margin: 12px 0px;
}
.nodata {
  padding: 20px 0px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #11253e;
  color: #ffffffa7;
}
// 第一个和最后一个 不做margin
.item:first-child,
.item:last-child {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

/* 滚动条样式 */
/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 8px !important;
}
/* 滚动槽 */
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(190, 92, 92, 0.2);
  color: #175192;
  border-radius: 2px;
}

// 上箭头
// ::-webkit-scrollbar-button:start {
//   background-image: url('../../../assets/up-arrow.png');
//   background-size: 14px !important;
//   background-repeat: no-repeat;
//   background-position: center center;
// }
// ::-webkit-scrollbar-button:end {
//   background-image: url('../../../assets/down-arrow.png');
//   background-repeat: no-repeat;
//   background-size: 14px !important;
//   background-position: center center;
// }
/* 滚动条滑块（里面小方块） */
::-webkit-scrollbar-thumb {
  border-radius: 2px;
  width: 12px !important;
  background: #175192 !important;
  -webkit-box-shadow: inset 0 0 6px #175192 !important;
}
/* 滚动条轨道 */
</style>
