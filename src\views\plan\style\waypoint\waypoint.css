.wayline-point-header {
  position: relative;
  top: 0px;
  left: 0px;
  z-index: 100;
  height: 50px;
  width: 100%;
  display: flex;
  align-items: center;
  user-select: none;
}
.wayline-point-header .action-bar {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.action {
  margin-left: 20px;
  line-height: 100%;
}
.action:hover {
  cursor: pointer;
}
.back {
  margin-left: 10px;
  font-size: 25px;
  color: whitesmoke;
}
.save {
  margin-left: 20px;
  font-size: 22px;
}
.wayline-preset-info {
  position: absolute;
  left: 50%;
  user-select: none;
  transform: translateX(-50%);
}
.item {
  height: 100%;
  color: rgba(255, 255, 255, 0.721);
  cursor: pointer;

  background-color: #3c3c3cc9;
  padding: 10px 10px;
  border-radius: 5px;
}
.item:hover {
  color: whitesmoke;
}
.return-left {
  margin-left: 20px;
  display: flex;
  align-items: center;
}
.return-left:hover {
  cursor: pointer;
}

.wayline-point-header .title {
  position: absolute;
  top: 50%; /* 垂直居中 */
  left: 50%; /* 水平居中 */
  transform: translate(-50%, -50%); /* 通过 transform 调整位置 */
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  user-select: none;
  color: aliceblue;
}

/* sidebar */

.left-sidebar-wrap {
  position: absolute;
  top: 50px;
  left: 0px;
  z-index: 100;
  width: 350px;
  height: calc(100% - 50px);
  background-color: #3c3c3cc9;
  user-select: none;
}
.left-sidebar-wrap .action-wrap {
  width: 100%;
  height: 30px;
  display: flex;
  justify-content: center;
}
.left-sidebar-wrap .action-wrap .action-list {
  width: 100%;
  display: flex;
  justify-content: center; /* 使得子元素在容器中水平居中 */
  align-items: center; /* 如果需要同时垂直居中 */
}

.action-item {
  position: relative; /* 为伪元素定位准备 */
  top: -2px;
  margin: 0px 10px;
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #98a2b3;
  line-height: 30px;
  font-weight: 400;
  padding: 0px 5px; /* 添加一些内边距使其更易点击 */
  padding-bottom: 5px;
  cursor: pointer; /* 鼠标变为手型，表示可点击 */
}

.action-item::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 3px;
  margin-bottom: 0px;
  background-color: #2e90fa; /* 横线的颜色 */
  transition: width 0.3s ease; /* 动画效果 */
}

.action-item.action-active::after {
  width: 100%; /* 选中时横线扩展到整个宽度 */
}
.action-active {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #2e90fa;
  text-align: center;
  font-weight: 400;
}

.action-content-wrap {
  width: 100%;
  height: calc(100% - 130px);
}
.action-content-wrap .action-content {
  padding: 10px;
  height: 100%;
  width: 100%;
  overflow-y: scroll;
}
.action-content-wrap .action-content-2 {
  height: 100%;
  width: 100%;
}
