<template>
  <div class="homeMainMap">
    <mainMapWidget ref="mainMapWidgetRef" :showPoltLyr="true" :showLayerControl="showLayerControl" />
  </div>
</template>
<script>
export default {
  name: 'HomeFlyMap'
};
</script>
<script setup>
import { onMounted, onUnmounted, onActivated, onBeforeUnmount, nextTick } from 'vue';
import { EBizCode, DOMAIN } from '@/utils/constants';
import { getDevicesBound } from '@/api/devices';
import * as Cesium from 'cesium';
import { getCesiumEngineInstance, flyTo, setCameraLookAt } from '@/components/Cesium/libs/cesium';
import mainMapWidget from '../components/homeMainMap.vue';
import { useConnectWebSocket } from '@/hooks/useConnectWebSocket';
import { useDeviceStateStore } from '@/store/modules/deviceState.js';
import {
  createDockInfoPopup,
  updatePoi,
  updateDockInfo,
  createNavInfoPopup,
  updateNavInfo,
  createDockNamePopup
} from './popup/popup';
import { getInfoByAlarmId, getCarLocation, getRunningJob } from '@/api/homeMap';
import Wayline from '../components/wayline';
import { addWaylineOnMap, delWayline } from '@/views/plan/surface/hocks/modules/wayLineRouteCtrl';
import * as turf from '@turf/turf';

const deviceStateStore = useDeviceStateStore();
const mainMapWidgetRef = ref(null);

const props = defineProps({
  // 是否显示图层控制
  showLayerControl: {
    type: Boolean,
    default: true
  }
});

// 无人机、机场属性定时器
let intervalTimer = null;
// 轮询当前出动消防车定时器
let car_intervalTimer = null;
// 无人机模型
const airModel = new URL('/resource/models/wrj2.glb', import.meta.url).href;
// 机场在线图标
let airport_live_url = new URL('@/assets/gis/airport_live.png', import.meta.url).href;
// 机场正在作业图标
let airport_working_url = new URL('@/assets/gis/airport_working2.png', import.meta.url).href;
// 机场离线图标
let airport_off_url = new URL('@/assets/gis/airport_off.png', import.meta.url).href;
// 灾害点图标
let firePnt_url = new URL('@/assets/gis/firePnt.png', import.meta.url).href;
// 消防车图标
let fireCar_url = new URL('@/assets/gis/fireCar.png', import.meta.url).href;
// 消防站图标
let fireStation_url = new URL('@/assets/gis/fireStation.png', import.meta.url).href;

// 机场、无人机所属关系绑定
const deviceList = ref([]);
// 无人机列表
const navList = ref({});
// 机场列表
const dockList = ref({});
// 当前警情ID
const nowAlarmId = ref('');
// 当前警情出动消防车ids
const nowFireCarModels = ref([]);
// 当前灾害点Entity
let fireEntity = null;
// 机场当前Wayline实例 航线
const dockWaylines = ref([]);
// 机场当前WaylinePolygon  航面
const dockWaylinePolygons = ref([]);

useConnectWebSocket(payload => {
  if (!payload) {
    return;
  }
  switch (payload.biz_code) {
    // 机场信息更新
    case EBizCode.DockOsd: {
      const info = payload.data;
      deviceStateStore.setCurrentDock(info);
      break;
    }
    // 遥控器信息更新
    case EBizCode.GatewayOsd: {
      // console.log('----gateway_osd', payload);
      break;
    }
    // 飞机信息更新
    case EBizCode.DeviceOsd: {
      const info = payload.data;
      deviceStateStore.setCurrentNav(info);
      break;
    }
  }
});

/**
 * 查询机场以及附属无人机条件
 */
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  queryType: 2,
  keyWord: ''
});

/**
 * 查询机场以及附属无人机
 */
function handleQuery() {
  // 创建一个空的Promise数组来存放所有的请求
  const promises = [];

  // 将第一个getDevicesBound请求加入到promises数组
  promises.push(
    getDevicesBound({
      domain: DOMAIN.DOCK,
      page: queryParams.pageNum,
      page_size: queryParams.pageSize
    }).then(data => {
      const { list, pagination } = data;
      if (list.length > 0) {
        const dockPromises = list.map(item => {
          // 根据机场SN为主键
          let oneItem = {
            nickname: item.nickname, //机场名
            dock_sn: item.device_sn, //机场SN号
            device_nickname: item.children.nickname, //无人机名
            device_sn: item.children.device_sn, //无人机SN号
            airline_id: null,
            dock_xyz: { x: item.longitude, y: item.latitude, z: item.altitude }
          };
          deviceList.value.push(oneItem);

          // 如果有最大飞行距离，则加载机场覆盖范围
          if (item.max_flight_distance !== undefined && item.max_flight_distance > 0) {
            return new Promise(resolve => {
              loadAirPortArea(item);
              resolve();
            });
          }
          return Promise.resolve();
        });
        return Promise.all(dockPromises);
      }
      return Promise.resolve();
    })
  );

  // 同理将第二个getDevicesBound请求加入到promises数组
  promises.push(
    getDevicesBound({
      domain: DOMAIN.RC,
      page: queryParams.pageNum,
      page_size: queryParams.pageSize
    }).then(data => {
      const { list, pagination } = data;
      if (list.length > 0) {
        list.forEach(item => {
          if (item.child_device_sn !== '' && item.children !== undefined) {
            // 根据遥控器SN为主键
            let oneItem = {
              nickname: null, //机场名
              dock_sn: null, //机场SN号
              device_nickname: item.children.nickname, //无人机名
              device_sn: item.children.device_sn, //无人机SN号
              rc_sn: item.device_sn, //遥控器SN号
              rc_nickname: item.nickname //遥控器名
            };
            deviceList.value.push(oneItem);
          }
        });
      }
      return Promise.resolve();
    })
  );

  // 返回一个Promise.all，确保所有请求及其后续操作完成
  return Promise.all(promises);
}

// 加载机场覆盖范围
const loadAirPortArea = item => {
  return new Promise(resolve => {
    try {
      const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
      if (!flyView) {
        console.error('Cesium viewer not initialized');
        resolve();
        return;
      }

      const targetPoint = {
        lon: item.longitude || 0,
        lat: item.latitude || 0,
        height: item.altitude || 0
      };

      // Validate coordinates
      if (!isFinite(targetPoint.lon) || !isFinite(targetPoint.lat)) {
        console.error('Invalid coordinates for airport area:', targetPoint);
        resolve();
        return;
      }

      const center = turf.point([targetPoint.lon, targetPoint.lat]); // 中心点坐标
      const radius = item.max_flight_distance; // 半径

      // Validate radius
      if (!isFinite(radius) || radius <= 0) {
        console.error('Invalid radius for airport area:', radius);
        resolve();
        return;
      }

      const options = { steps: 64, units: 'meters' }; // 设置步数以控制精度，单位为米
      
      let circleFeature;
      try {
        circleFeature = turf.circle(center, radius, options);
      } catch (err) {
        console.error('Failed to create turf circle:', err);
        resolve();
        return;
      }

      if (!circleFeature || !circleFeature.geometry || !circleFeature.geometry.coordinates || 
          !Array.isArray(circleFeature.geometry.coordinates[0])) {
        console.error('Invalid circle feature geometry');
        resolve();
        return;
      }

      // 将Turf的坐标转换为Cesium的Cartesian3坐标
      const positions = circleFeature.geometry.coordinates[0]
        .filter(coord => Array.isArray(coord) && coord.length >= 2)
        .map(coord => {
          try {
            return Cesium.Cartesian3.fromDegrees(coord[0], coord[1], 40);
          } catch (err) {
            console.error('Failed to convert coordinate to Cartesian3:', coord, err);
            return null;
          }
        })
        .filter(position => position !== null);

      if (positions.length < 3) {
        console.error('Not enough valid positions for polygon creation');
        resolve();
        return;
      }

      // 闭合环状
      positions.push(positions[0]);
      
      // 机场覆盖范围添加到Viewer中
      try {
        let airPortAreaLyr = flyView.dataSources.getByName('airPortAreaLyr')[0];
        if (!airPortAreaLyr) {
          // 机场覆盖范围数据源
          airPortAreaLyr = new Cesium.CustomDataSource('airPortAreaLyr');
          airPortAreaLyr.show = false;
          flyView.dataSources.add(airPortAreaLyr);
        }

        // 添加到机场覆盖图层中
        airPortAreaLyr.entities.add({
          id: `airPortArea_` + item.device_sn,
          polygon: {
            hierarchy: new Cesium.PolygonHierarchy(positions),
            material: Cesium.Color.fromCssColorString('#2E90FA').withAlpha(0.3), // 设置填充颜色
            outline: true,
            outlineColor: Cesium.Color.fromCssColorString('#2E90FA') // 设置轮廓颜色
          }
        });
      } catch (err) {
        console.error('Failed to add airport area to layer:', err);
      }

      resolve();
    } catch (err) {
      console.error('Error in loadAirPortArea:', err);
      resolve();
    }
  });
};

/**
 * 定位无人机
 * @param {*} nav 无人机实时OSD数据
 * @param {*} deviceInfo 无人机基础信息
 */
function setNavModel(nav, deviceInfo) {
  if (nav === undefined || nav === null) {
    return;
  }
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  let device_sn = deviceInfo.device_sn;
  // 下线移出无人机
  if (nav.mode_code === 14) {
    if (navList.value[device_sn] !== null || navList.value[device_sn] !== undefined) {
      flyView.entities.remove(navList.value[device_sn]);
      navList.value[device_sn] = null;
      flyView.entities.getById('dock_' + deviceInfo.dock_sn).billboard.show = true;
    }
    return;
  }
  const position = Cesium.Cartesian3.fromDegrees(nav.longitude, nav.latitude, nav.height);
  let hpr = new Cesium.HeadingPitchRoll(
    Cesium.Math.toRadians(nav.attitude_head),
    Cesium.Math.toRadians(nav.attitude_pitch),
    Cesium.Math.toRadians(nav.attitude_roll)
  );
  let orientation = Cesium.Transforms.headingPitchRollQuaternion(position, hpr);
  if (navList.value[device_sn] === null || navList.value[device_sn] === undefined) {
    deviceInfo.device_xyz = { x: nav.longitude, y: nav.latitude, z: nav.height };
    let navModel = flyView.entities.add({
      id: 'nav_' + deviceInfo.device_sn,
      name: deviceInfo[device_sn],
      position: position,
      orientation: orientation,
      model: {
        uri: airModel,
        minimumPixelSize: 64,
        maximumScale: 20000,
        scale: 0.15,
        incrementallyLoadTextures: true, // 加载模型后纹理是否可以绯续流入
        runAnimations: true, //是否启动模型中指定的gltf 动画
        clampAnimations: true, //指定 gltf 动画是否在没有关键帧的持续时间内保持最后一个姿势
        shadows: Cesium.ShadowMode.ENABLED,
        heightReference: Cesium.HeightReference.NONE
      }
    });
    navModel.distanceDisplayCondition = new Cesium.DistanceDisplayCondition(10000.0, 1000000.0);
    navList.value[device_sn] = navModel;
  } else {
    navList.value[device_sn].position = position;
    navList.value[device_sn].orientation = orientation;
  }

  // 判断机场无人机处于 航线飞行,指令飞行
  if (deviceInfo.dock_sn !== null) {
    // 查询正在作业的任务
    if (nav.mode_code === 5 || nav.mode_code === 17) {
      if (deviceInfo.airline_id === null) {
        navList.value[device_sn].model.scale = 0.05;
        getDockRunningJob(deviceInfo);
      }
    } else {
      if (deviceInfo.airline_id !== null) {
        navList.value[device_sn].model.scale = 0.15;
        removeWayline(deviceInfo);
      }
    }

    // 以下状态则隐藏机场
    if (nav.mode_code === 0 || nav.mode_code === 1 || nav.mode_code === 2) {
      flyView.entities.getById('dock_' + deviceInfo.dock_sn).billboard.show = false;
    } else {
      flyView.entities.getById('dock_' + deviceInfo.dock_sn).billboard.show = true;
    }
  }
}

// 初始化展示所有机场
function initDockModel() {
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  deviceList.value.forEach(oneItem => {
    if (oneItem.dock_sn !== null) {
      const position = Cesium.Cartesian3.fromDegrees(oneItem.dock_xyz.x, oneItem.dock_xyz.y, oneItem.dock_xyz.z);
      let navModel = flyView.entities.add({
        id: 'dock_' + oneItem.dock_sn,
        name: oneItem.dock_sn,
        position: position,
        billboard: {
          show: true,
          image: airport_off_url,
          width: 40,
          height: 52,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          disableDepthTestDistance: Number.POSITIVE_INFINITY
        }
      });
      dockList.value[oneItem.dock_sn] = navModel;
      createDockNamePopup(flyView, oneItem);
    }
  });
}

/**
 * 定位机场
 * @param {*} dock 机场实时OSD数据
 * @param {*} deviceInfo 机场基础信息
 */
function setDockModel(dock, deviceInfo) {
  if (dock === undefined || dock === null) {
    return;
  }
  if (dock.basic_osd === undefined) {
    return;
  }
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  const position = Cesium.Cartesian3.fromDegrees(
    dock.basic_osd.longitude,
    dock.basic_osd.latitude,
    dock.basic_osd.height
  );
  let dock_sn = deviceInfo.dock_sn;
  if (dockList.value[dock_sn] === null || dockList.value[dock_sn] === undefined) {
    // 将坐标放到设备列表中
    deviceInfo.dock_xyz = { x: dock.basic_osd.longitude, y: dock.basic_osd.latitude, z: dock.basic_osd.height };
    let navModel = flyView.entities.add({
      id: 'dock_' + deviceInfo.dock_sn,
      name: deviceInfo.dock_sn,
      position: position,
      billboard: {
        show: true,
        image: airport_live_url,
        width: 40,
        height: 52,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        disableDepthTestDistance: Number.POSITIVE_INFINITY
      }
    });
    dockList.value[dock_sn] = navModel;
    // createDockNamePopup(flyView, deviceInfo);
  } else {
    // 位置有变化再更新
    let oldPosition = dockList.value[dock_sn].position._value;
    if (oldPosition.x !== position.x && oldPosition.y !== position.y && oldPosition.z !== position.z) {
      dockList.value[dock_sn].position = position;
    }
    // 更新图片状态
    if (dock.basic_osd.mode_code === 4) {
      dockList.value[dock_sn].billboard.image = airport_working_url;
    } else if (dock.basic_osd.mode_code === -1) {
      dockList.value[dock_sn].billboard.image = airport_off_url;
    } else {
      dockList.value[dock_sn].billboard.image = airport_live_url;
    }
  }
}

/**
 * 获取机场正在执行的任务
 */
function getDockRunningJob(deviceInfo) {
  getRunningJob(deviceInfo.dock_sn).then(res => {
    if (res !== undefined) {
      deviceInfo.airline_id = res.airline_id;
      deviceInfo.airline_json = res.airline_json;
      deviceInfo.wayline_type = res.wayline_type;
      drawWayline(deviceInfo);
    }
  });
}

// 绘制正在飞行的航线
function drawWayline(deviceInfo) {
  setTimeout(() => {
    const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
    if (deviceInfo.wayline_type === 0) {
      const planJson = JSON.parse(deviceInfo.airline_json);
      let waylineInstace = new Wayline(flyView, planJson);
      waylineInstace.createWayLine();
      dockWaylines.value[deviceInfo.dock_sn] = waylineInstace;
    } else if (deviceInfo.wayline_type === 1) {
      const planJson = JSON.parse(deviceInfo.airline_json);
      const waylinePolygonId = 'waylinePolygon_' + deviceInfo.dock_sn;
      addWaylineOnMap(flyView, {
        id: waylinePolygonId,
        airline_json: planJson
      });
      dockWaylinePolygons.value[deviceInfo.dock_sn] = waylinePolygonId;
    }
  }, 1000);
}

// 移出飞行完的航线
function removeWayline(deviceInfo) {
  if (dockWaylines.value[deviceInfo.dock_sn] !== undefined) {
    dockWaylines.value[deviceInfo.dock_sn].removeCustomData();
    deviceInfo.airline_id = null;
    deviceInfo.airline_json = null;
    deviceInfo.wayline_type = null;
  }
  if (dockWaylinePolygons.value[deviceInfo.dock_sn] !== undefined) {
    delWayline({
      id: dockWaylinePolygons.value[deviceInfo.dock_sn]
    });
    deviceInfo.airline_id = null;
    deviceInfo.airline_json = null;
    deviceInfo.wayline_type = null;
  }
}

/**
 * 机场点击事件
 * @param {*} feature_id 机场entityid(dock_****)
 */
function clickDockModel(feature_id) {
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  const click_sn = feature_id.split('_')[1];
  deviceList.value.forEach(oneDevice => {
    if (oneDevice.dock_sn === click_sn) {
      createDockInfoPopup(flyView, oneDevice);
    }
  });
}

/**
 * 无人机点击事件
 * @param {*} feature_id 无人机entityid(nav_****)
 */
function clickNavModel(feature_id) {
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  const click_sn = feature_id.split('_')[1];
  deviceList.value.forEach(oneDevice => {
    if (oneDevice.device_sn === click_sn) {
      createNavInfoPopup(flyView, oneDevice, false);
    }
  });
}

/**
 * 设置警情灾害点
 * @param {*} item 警情相关信息
 */
function setFireInfo(item) {
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  const position = Cesium.Cartesian3.fromDegrees(item.longitude, item.latitude, 10);
  let entityId = 'firePnt_' + item.alarm_name;
  if (!fireEntity) {
    fireEntity = flyView.entities.add({
      id: entityId,
      position: position,
      billboard: {
        show: true,
        image: firePnt_url,
        width: 40,
        height: 52,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        disableDepthTestDistance: Number.POSITIVE_INFINITY
      }
    });
  } else {
    fireEntity._id = entityId;
    fireEntity.position = position;
  }
  // 定位到警情灾害点
  flyTo(flyView, item.longitude, item.latitude, 5000, 0, -90, 0, 1);

  const requestJson = {
    alarm_id: item.alarm_id
  };
  // 根据警情ID获取警情相关信息
  getInfoByAlarmId(requestJson)
    .then(res => {
      if (res) {
        setFireStationsAndCar(res);
      }
    })
    .catch(err => {
      console.log(err);
    });
}

/**
 * 设置消防车、消防站位置
 * @param {*} res 警情相关消防站、消防车信息
 */
function setFireStationsAndCar(res) {
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  const fireCarDataSource = flyView.dataSources.getByName('fireCarLyr')[0];
  fireCarDataSource.entities.removeAll();
  nowFireCarModels.value = [];
  if (car_intervalTimer) {
    clearInterval(car_intervalTimer);
  }
  // 消防站
  if (res.station_list.length > 0) {
    res.station_list.forEach(oneStation => {
      const position = Cesium.Cartesian3.fromDegrees(oneStation.lon, oneStation.lat, 10);
      fireCarDataSource.entities.add({
        id: 'fireStation' + oneStation.station_id,
        position: position,
        billboard: {
          show: true,
          image: fireStation_url,
          width: 40,
          height: 52,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          disableDepthTestDistance: Number.POSITIVE_INFINITY
        }
      });
    });
  }
  // 消防车轮询
  if (res.car_ids.length > 0) {
    const requestJson = {
      car_ids: res.car_ids
    };
    setCarLocation(requestJson, fireCarDataSource);
    car_intervalTimer = setInterval(() => {
      setCarLocation(requestJson, fireCarDataSource);
    }, 5000);
  }
}

// 消防车定位
function setCarLocation(requestJson, fireCarDataSource) {
  getCarLocation(requestJson)
    .then(res => {
      if (res) {
        res.forEach(oneCar => {
          const position = Cesium.Cartesian3.fromDegrees(oneCar.lon, oneCar.lat, 10);
          const carId = 'fireCar' + oneCar.car_id;
          if (nowFireCarModels.value[carId] === null || nowFireCarModels.value[carId] === undefined) {
            let fireCarModel = fireCarDataSource.entities.add({
              id: carId,
              position: position,
              billboard: {
                show: true,
                image: fireCar_url,
                width: 40,
                height: 52,
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                disableDepthTestDistance: Number.POSITIVE_INFINITY
              }
            });
            nowFireCarModels.value[carId] = fireCarModel;
          } else {
            nowFireCarModels.value[carId].position = position;
          }
        });
      }
    })
    .catch(err => {
      console.log(err);
    });
}

function initThisViewer() {
  handleQuery()
    .then(() => {
      try {
        const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
        
        if (!flyView) {
          console.error('Cesium viewer not initialized in initThisViewer');
          return;
        }

        // 初始化鼠标事件处理器
        try {
          const mouseHandler = new Cesium.ScreenSpaceEventHandler(flyView.scene.canvas);
          mouseHandler.setInputAction(function (movement) {
            try {
              const feature = flyView.scene.pick(movement.position);
              if (Cesium.defined(feature) && feature.id && feature.id.id) {
                if (feature.hasOwnProperty('id') && feature.id instanceof Cesium.Entity) {
                  if (feature.id.id.indexOf('dock_') >= 0) {
                    clickDockModel(feature.id.id);
                  } else if (feature.id.id.indexOf('nav_') >= 0) {
                    clickNavModel(feature.id.id);
                  }
                }
              }
            } catch (err) {
              console.error('Error in mouse click handler:', err);
            }
          }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
        } catch (err) {
          console.error('Error setting up mouse handler:', err);
        }

        // 初始化机场模型
        try {
          initDockModel();
        } catch (err) {
          console.error('Error initializing dock model:', err);
        }

        // 更新POI点
        try {
          updatePoi(flyView);
        } catch (err) {
          console.error('Error updating POI:', err);
        }

        // 启动定时器更新设备状态
        try {
          intervalTimer = setInterval(() => {
            try {
              if (deviceList.value.length > 0) {
                deviceList.value.forEach(oneDevice => {
                  try {
                    if (oneDevice.device_sn !== null) {
                      let dockOptions = { dockInfo: oneDevice, osdInfo: deviceStateStore.getDockBySn(oneDevice.dock_sn) };
                      setDockModel(dockOptions.osdInfo, dockOptions.dockInfo);
                      updateDockInfo(dockOptions);
                    }
                    let navOptions = { navInfo: oneDevice, osdInfo: deviceStateStore.getNavBySn(oneDevice.device_sn) };
                    setNavModel(navOptions.osdInfo, navOptions.navInfo);
                    updateNavInfo(navOptions);
                  } catch (deviceErr) {
                    console.error('Error updating device state:', deviceErr);
                  }
                });
              }
            } catch (intervalErr) {
              console.error('Error in interval timer callback:', intervalErr);
            }
          }, 1000);
        } catch (err) {
          console.error('Error setting up interval timer:', err);
        }
      } catch (err) {
        console.error('Error in initThisViewer:', err);
      }
    })
    .catch(error => {
      console.error('初始化地图失败:', error);
    });
}

/**
 * 警情信息获取处理
 * @param {*} item 当前警情相关信息
 */
function fireAlarmJump(item) {
  nowAlarmId.value = item.alarm_id;
  setFireInfo(item);
  // 传给图层控制组件
  if (mainMapWidgetRef.value) {
    mainMapWidgetRef.value.fireAlarmIDJump(item.alarm_id);
  }
}

/**
 * 定位到机场或无人机
 * @param {*} item 机场信息
 */
 function flyToDock(item) {
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  flyTo(flyView, item.longitude, item.latitude, 5000, 0, -90, 0, 1);
}

/**
 * 机场列表右侧点击
 * @param {*} item
 */
function clickDockOrNav(item) {
  let domain = item.domain;
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  const click_sn = item.device_sn;
  // 机场
  if (domain === 3) {
    deviceList.value.forEach(oneDevice => {
      if (oneDevice.dock_sn === click_sn) {
        createDockInfoPopup(flyView, oneDevice);
        if (dockList.value[click_sn] !== null && dockList.value[click_sn] !== undefined) {
          const cartesian = dockList.value[click_sn].position.getValue(Cesium.JulianDate.now());
          if (cartesian !== undefined) {
            setDockOrNavLocation(cartesian);
          }
        }
      }
    });
  }
  // 无人机
  else if (domain === 0) {
    deviceList.value.forEach(oneDevice => {
      if (oneDevice.device_sn === click_sn) {
        if (navList.value[click_sn] !== null && navList.value[click_sn] !== undefined) {
          const cartesian = navList.value[click_sn].position.getValue(Cesium.JulianDate.now());
          if (cartesian !== undefined) {
            setDockOrNavLocation(cartesian);
          }
          createNavInfoPopup(flyView, oneDevice, false);
        }
      }
    });
  }
}

function setDockOrNavLocation(cartesian) {
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  let cartographic = Cesium.Cartographic.fromCartesian(cartesian);
  let obj = {};
  obj.lng = Cesium.Math.toDegrees(cartographic.longitude);
  obj.lat = Cesium.Math.toDegrees(cartographic.latitude);
  obj.height = cartographic.height;
  setCameraLookAt(flyView, {
    lon: Number(obj.lng),
    lat: Number(obj.lat),
    height: Number(obj.height),
    offsetH: 500,
    distance: 2000
  });
}

// 暴露方法给父组件
defineExpose({
  flyToDock,
  fireAlarmJump,
  clickDockOrNav
});

onMounted(() => {
  try {
    // Use a slightly longer timeout to ensure DOM is fully rendered
    setTimeout(() => {
      try {
        initThisViewer();
      } catch (err) {
        console.error('Failed to initialize Cesium viewer in timeout callback:', err);
      }
    }, 100);
  } catch (err) {
    console.error('Error in onMounted:', err);
  }
});

onUnmounted(() => {
  clearInterval(intervalTimer);
  if (car_intervalTimer) {
    clearInterval(car_intervalTimer);
  }
});
onBeforeUnmount(() => {});
</script>

<style lang="scss" scoped>
.homeMainMap {
  height: 100%;
  width: 100%;
  position: relative;

  .tool {
    position: absolute;
    top: 50px;
    color: red;
    background-color: antiquewhite;
    z-index: 100;
  }
}
</style>
