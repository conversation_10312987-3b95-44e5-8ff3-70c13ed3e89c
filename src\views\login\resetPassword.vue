<!-- 找回密码 -->
<template>
  <div>
    <PageBox :show-leftimg="false">
      <div class="pb-5 flex-1 max-h-screen overflow-y-scroll">
        <PageHead
          custom-class="custom-head"
          :show-language="true"
          :logo="logo"
        />
        <div class="flex justify-center">
          <el-card class="card-box mt-10">
            <div class="card-head">{{ $t('retrieve password') }}</div>
            <el-form
              ref="formRef"
              :model="formData"
              :rules="rules"
              class="demo-form"
            >
              <template v-if="step === 0">
                <el-form-item
                  v-if="validType === 'EMAIL'"
                  prop="email"
                  class="flex-item"
                >
                  <el-input
                    v-model="formData.email"
                    autocomplete="new-password"
                    class="custom-reset-input"
                    maxlength="200"
                    :placeholder="$t('Please enter your email')"
                  />
                  <el-button
                    v-if="rules.email[0].required"
                    :disabled="!!emailTimer"
                    type="primary"
                    class="ml-2 code-button"
                    @click="getEmailCode(60)"
                  >
                    {{
                      _.isNaN(emailCodeText * 1)
                        ? emailCodeText
                        : `${emailCodeText} s`
                    }}
                  </el-button>
                </el-form-item>
                <el-form-item
                  v-if="validType === 'MOBILE'"
                  prop="phoneNumber"
                  class="flex-item"
                >
                  <el-input
                    v-model="formData.phoneNumber"
                    autocomplete="new-password"
                    maxlength="11"
                    class="custom-reset-input"
                    :placeholder="$t('Please enter your phone')"
                  />
                  <el-button
                    v-if="rules.phoneNumber.required"
                    type="primary"
                    class="ml-2 code-button"
                    :disabled="!!phoneTimer"
                    @click="getPhoneCode(60)"
                  >
                    {{
                      _.isNaN(phoneCodeText * 1)
                        ? phoneCodeText
                        : `${phoneCodeText} s`
                    }}
                  </el-button>
                </el-form-item>
                <el-form-item prop="verifyCode">
                  <el-input
                    v-model="formData.verifyCode"
                    autocomplete="new-password"
                    class="custom-reset-input"
                    :placeholder="$t('Please enter the verification code')"
                  />
                </el-form-item>
                <div class="text-center">
                  <el-link
                    v-if="validType === 'MOBILE'"
                    :underline="false"
                    type="primary"
                    @click="changeType"
                  >
                    {{ $t('Retrieve by email') }}
                  </el-link>
                  <el-link
                    v-else
                    :underline="false"
                    type="primary"
                    @click="changeType"
                  >
                    {{ $t('Retrieve by SMS') }}
                  </el-link>
                </div>
              </template>
              <template v-if="step === 1">
                <el-form-item prop="password" class="relative-error">
                  <el-input
                    v-model="formData.password"
                    autocomplete="new-password"
                    :type="newPasswordShow"
                    class="custom-reset-input"
                    :placeholder="$t('Please enter a new password')"
                  >
                    <template #suffix>
                      <span
                        class="show-pwd cursor-pointer"
                        @click="
                          newPasswordShow = newPasswordShow ? '' : 'password'
                        "
                      >
                        <i
                          class="ff-cloud-icon"
                          :class="
                            newPasswordShow === 'password'
                              ? 'clound-open-eye'
                              : 'clound-close-eye'
                          "
                        />
                      </span>
                    </template>
                  </el-input>
                </el-form-item>
                <el-form-item prop="confirmPassword">
                  <el-input
                    v-model="formData.confirmPassword"
                    autocomplete="new-password"
                    :type="confirmPasswordShow"
                    class="custom-reset-input"
                    :placeholder="$t('Please enter the confirmation password')"
                  >
                    <template #suffix>
                      <span
                        class="show-pwd cursor-pointer"
                        @click="
                          confirmPasswordShow = confirmPasswordShow
                            ? ''
                            : 'password'
                        "
                      >
                        <i
                          class="ff-cloud-icon"
                          :class="
                            confirmPasswordShow === 'password'
                              ? 'clound-open-eye'
                              : 'clound-close-eye'
                          "
                        />
                      </span>
                    </template>
                  </el-input>
                </el-form-item>
              </template>
            </el-form>
            <el-button
              v-if="step === 0"
              type="primary"
              class="login-button"
              @click="nextStep"
            >
              {{ $t('next step') }}
            </el-button>
            <el-button
              v-if="step === 1"
              type="primary"
              class="login-button"
              @click="submitForm"
            >
              {{ $t('confirm') }}
            </el-button>
            <div class="flex justify-center items-center mt-6 space-x-4">
              <el-link
                :underline="false"
                type="primary"
                @click="router.push({ path: '/login', query: route.query })"
              >
                {{ $t('Return to login') }}
              </el-link>
              <el-link
                :underline="false"
                type="primary"
                @click="router.replace({ path: '/register' })"
              >
                {{ $t('Rapid registration') }}
              </el-link>
            </div>
          </el-card>
        </div>
      </div>
    </PageBox>
  </div>
</template>

<script>
export default {
  name: 'ResetPassword'
};
</script>

<script setup>
import _ from 'lodash';
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';

import router from '@/router';
import i18n from '@/lang';

import { validPhone, valid_9, validatenull } from '@/utils/helper';

import {
  resetNoPermission,
  sendNoPermission,
  verifyNoPermission
} from '@/api/retrievePassword';

import logo from '@/assets/sx_logo_primary.png';
import { useRoute } from 'vue-router';
const route = useRoute();
const formRef = ref('formRef');
// 手机号
const validatePhone = (rule, value, callback) => {
  if (validatenull(value)) {
    return callback(new Error(i18n.global.t('Please enter your phone')));
  } else if (!validPhone(value)) {
    return callback(
      new Error(i18n.global.t('Please enter the correct mobile phone number'))
    );
  } else {
    callback();
  }
};

// 密码
const validatePass = (rule, value, callback) => {
  if (validatenull(value)) {
    return callback(
      new Error(
        i18n.global.t(
          'Please enter a 6-20 digit password consisting of numbers letters and special characters'
        )
      )
    );
  } else if (!valid_9(value)) {
    return callback(
      new Error(
        i18n.global.t(
          'Please enter a 6-20 digit password consisting of numbers letters and special characters'
        )
      )
    );
  } else {
    callback();
  }
};

// 再次输入密码
const checkPass = (rule, value, callback) => {
  if (validatenull(value)) {
    callback(new Error(i18n.global.t('Please enter the password again')));
  } else if (value !== formData.value.password) {
    callback(
      new Error(i18n.global.t('The passwords entered twice are inconsistent'))
    );
  } else {
    callback();
  }
};

const formData = ref(initFrom());
const step = ref(0);
const validType = ref('MOBILE');
const phoneCodeText = ref(i18n.global.t('Get Code'));
const emailCodeText = ref(i18n.global.t('Get Code'));
const phoneTimer = ref(null);
const emailTimer = ref(null);
const subLoading = ref(false);
const newPasswordShow = ref('password');
const confirmPasswordShow = ref('password');
// const logo = logo; // Replace with the actual logo import
const rules = ref({
  email: [
    {
      required: true,
      message: i18n.global.t('Please enter the email address'),
      trigger: ['change', 'blur']
    },
    {
      type: 'email',
      message: i18n.global.t('Please enter the correct email address'),
      trigger: ['blur', 'change']
    }
  ],
  phoneNumber: {
    required: true,
    validator: validatePhone,
    trigger: ['change', 'blur']
  },
  verifyCode: {
    required: true,
    message: i18n.global.t('Please enter the verification code'),
    trigger: ['change', 'blur']
  },
  password: [
    {
      required: true,
      validator: validatePass,
      trigger: ['change', 'blur']
    },
    {
      min: 6,
      max: 20,
      message: i18n.global.t(
        'Please enter a 6-20 digit password consisting of numbers letters and special characters'
      ),
      trigger: ['change', 'blur']
    }
  ],
  confirmPassword: [
    { required: true, validator: checkPass, trigger: ['change', 'blur'] }
  ]
});

function initConfig() {
  let startTime = null;
  const nowTime = Date.now();
  if (i18n.global.locale.value === 'zh') {
    startTime = sessionStorage.getItem('retrievePwdPhoneCodeText');
    if (startTime) {
      const time = Math.floor((nowTime - startTime) / 1000);
      setCodeText(60 - time, 'phone');
    }
  } else {
    startTime = sessionStorage.getItem('retrievePwdEmailCodeText');
    if (startTime) {
      const time = Math.floor((nowTime - startTime) / 1000);
      setCodeText(60 - time, 'email');
    }
  }
}

onMounted(() => {
  initConfig();
});

function initFrom() {
  return {
    username: '', // 登录账号
    phoneNumber: '', // 手机号码
    email: '', // 邮箱
    verifyCode: '', // 验证码
    password: '', // 登录密码
    confirmPassword: '' // 确认密码
  };
}

// 获取手机验证码
async function getPhoneCode(num) {
  formRef.value.validateField('phoneNumber', async valid => {
    if (valid) {
      const res = await sendCode({ mobile: formData.value.phoneNumber });
      if (res) {
        setCodeText(num, 'phone');
      }
    }
  });
}

// 获取邮箱验证码
async function getEmailCode(num) {
  formRef.value.validateField('email', async valid => {
    if (valid) {
      const res = await sendCode({ email: formData.value.email });
      if (res) {
        setCodeText(num, 'email');
      }
    }
  });
}

// 设置codeText
function setCodeText(num, type) {
  const codeType = {
    email: {
      text: emailCodeText,
      timer: emailTimer,
      name: 'retrievePwdEmailCodeText'
    },
    phone: {
      text: phoneCodeText,
      timer: phoneTimer,
      name: 'retrievePwdPhoneCodeText'
    }
  };
  const startTime = sessionStorage.getItem(codeType[type].name);
  if (!startTime) {
    sessionStorage.setItem(codeType[type].name, Date.parse(new Date()));
  }
  codeType[type].text.value =
    num <= 0 ? i18n.global.t('register.getCode') : num;
  if (num <= 0) {
    sessionStorage.removeItem(codeType[type].name);
    return;
  }
  codeType[type].timer.value = setInterval(() => {
    codeType[type].text.value = Math.max(0, codeType[type].text.value - 1);
    if (codeType[type].text.value <= 0) {
      clearInterval(codeType[type].timer.value);
      codeType[type].text.value = i18n.global.t('Get Code');
      codeType[type].timer.value = null;
      sessionStorage.removeItem(codeType[type].name);
    }
  }, 1000);
}

// 发送验证码
async function sendCode() {
  try {
    const sendCodeForm = {
      type: validType.value,
      addr:
        validType.value === 'MOBILE'
          ? formData.value.phoneNumber
          : formData.value.email
    };
    const res = await sendNoPermission(sendCodeForm);
    return res.code * 1 === 200;
  } catch (err) {
    return false;
  }
}

function changeType() {
  validType.value = validType.value === 'MOBILE' ? 'EMAIL' : 'MOBILE';
  nextTick(() => {
    formRef.value.resetFields();
  });
}

function nextStep() {
  formRef.value.validate(async valid => {
    if (valid) {
      const validCodeForm = {
        type: validType.value,
        addr:
          validType.value === 'MOBILE'
            ? formData.value.phoneNumber
            : formData.value.email,
        verifyCode: formData.value.verifyCode
      };
      try {
        await verifyNoPermission(validCodeForm);
        step.value++;
      } catch {
        console.log('验证码校验错误');
      }
    }
  });
}

// 提交
function submitForm() {
  subLoading.value = true;
  formRef.value.validate(async valid => {
    if (valid) {
      try {
        const resetPasswordForm = {
          type: validType.value,
          addr:
            validType.value === 'MOBILE'
              ? formData.value.phoneNumber
              : formData.value.email,
          confirmPassword: formData.value.confirmPassword,
          password: formData.value.password
        };
        await resetNoPermission(resetPasswordForm);
        ElMessage.success(i18n.global.t('Password reset succeeded'));
        router.push({
          path: '/login'
        });
      } catch (error) {
        console.log('error: ', error);
      }
    } else {
      subLoading.value = false;
      console.log('error submit!!');
      return false;
    }
  });
  subLoading.value = false;
}
</script>

<style scoped lang="scss">
:deep() {
  .card-box {
    width: 500px;

    .card-head {
      font-size: 28px;
      font-family: AliMedium;
      font-weight: 500;
      color: #222222;
      // height: 100px;
      padding-top: 30px;
      padding-bottom: 15px;
      line-height: 26px;
      text-align: center;
    }

    .card-tab {
      /*去掉tabs底部的下划线*/
      .el-tabs__nav-wrap::after {
        position: static !important;
      }

      .el-tabs__active-bar {
        border-radius: 2px;
        width: 30px !important;
        left: 15px;
        height: 4px;
      }

      .el-tabs__item {
        font-family: dfFont;
        font-weight: 500;
      }
    }
  }
  .flex-item {
    .el-form-item__content {
      display: flex;
      .ml-2 {
        margin-left: 0.5rem !important;
      }
    }
  }
  .justify-center-item {
    .el-form-item__content {
      justify-content: center;
    }
  }
  .justify-start-item {
    .el-form-item__content {
      justify-content: start;
    }
  }

  .el-input__wrapper {
    background-color: #f8fafe;
    box-shadow: none;
  }

  .custom-reset-input {
    .el-input__inner {
      background: #f8fafe;
      border: none;
      height: 40px;
      border-radius: 4px;
    }
  }

  .code-button {
    min-width: 90px;
    position: absolute;
    right: 5px;
    top: 4px;
    background: rgba(9, 126, 252, 0.1);
    color: #097efc;
    border: none;
  }

  .el-checkbox:last-of-type {
    margin-right: 10px;
  }

  .login-button {
    margin-top: 20px;
    padding: 15px;
    width: 100%;
  }

  .custom-head {
    padding-top: 50px;
    .logo-text {
      font-size: 28px;
      color: #097efc;
    }
  }
}
</style>
