<template>
  <div class="map-100">
    <mainMapWidget />
    <div class="alarminfo" v-show="alarm_content !== '' && showAlarm">
      <img src="@/assets/gis/alarm.png" alt="" class="alarm-img" />
      <div class="alarm-content">{{ alarm_content }}</div>
    </div>
  </div>
  <SituationPlotting
    v-if="showFlag"
    :ffCesium="ffCesium"
    :plotDataArray="plotDataArray"
    :alarmId="nowAlarmId"
    useType="edit"
    style="position: absolute; top: 10px; right: 50px; width: 250px; background-color: white; z-index: 999"
  ></SituationPlotting>
</template>
<script>
export default {
  name: 'RealTimeFlyMap'
};
</script>
<script setup>
import { onMounted, onUnmounted, onBeforeUpdate, onBeforeUnmount, nextTick } from 'vue';
import * as Cesium from 'cesium';
import { getCesiumEngineInstance, flyTo } from '@/components/Cesium/libs/cesium';
import mainMapWidget from '../components/homeMainMap.vue';
import { updatePoi, createDockNamePopup } from '../homeMap/popup/popup';
import { getInfoByAlarmId, getCarLocation, getRunningJob } from '@/api/homeMap';
import Wayline from '../components/wayline';
import { addWaylineOnMap, delWayline } from '@/views/plan/surface/hocks/modules/wayLineRouteCtrl';
//SituationPlotting参数与逻辑-start

import FFCesium from './SituationPlotting/FFCesium/core/index.js';
import SituationPlotting from './SituationPlotting/index.vue';
import { plotList, plotDelete } from '@/api/plot/index.js';
const props = defineProps({
  showAlarm: {
    type: Boolean,
    default: true
  }
});
let ffCesium = null;
const showFlag = ref(false);

let plotDataArray = [];
let nowAlarmId = '1805158661761273858';
// let alarmId = '123456';

const initSituationPlotting = () => {
  if (!ffCesium) {
    const viewerTemp = getCesiumEngineInstance('homeMap-fly').viewer;
    ffCesium = new FFCesium('mainMapContainer', { viewer: viewerTemp });
    //取消双击选中跟踪对象
    var handler = new ffCesium.Cesium.ScreenSpaceEventHandler(ffCesium.viewer.scene.canvas);
    handler.setInputAction(function (event) {
      ffCesium.viewer.trackedEntity = undefined;
    }, ffCesium.Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);
  }
  plotDataArray = [];
  //删除数据
  plotList(nowAlarmId).then(res => {
    res.forEach(item => {
      item.plotObj = JSON.parse(item.plot_obj);
      plotDataArray.push(item);
    });
    showFlag.value = true;
  });
};

//SituationPlotting参数与逻辑-end

// 警情内容
const alarm_content = ref('');
// 定时器
let intervalTimer = null;
// 轮询当前出动消防车定时器
let car_intervalTimer = null;
// 无人机模型
// const airModel = new URL('/resource/models/dajiang.glb', import.meta.url).href;
const airModel = new URL('/resource/models/wrj2.glb', import.meta.url).href;
// 机场在线图标
let airport_live_url = new URL('@/assets/gis/airport_live.png', import.meta.url).href;
// 机场正在作业图标
let airport_working_url = new URL('@/assets/gis/airport_working2.png', import.meta.url).href;
// 机场离线图标
let airport_off_url = new URL('@/assets/gis/airport_off.png', import.meta.url).href;
// 灾害点图标
let firePnt_url = new URL('@/assets/gis/firePnt.png', import.meta.url).href;
// 消防车图标
let fireCar_url = new URL('@/assets/gis/fireCar.png', import.meta.url).href;
// 消防站图标
let fireStation_url = new URL('@/assets/gis/fireStation.png', import.meta.url).href;
// 无人机列表
const navList = ref({});
// 机场列表
const dockList = ref({});
// 当前警情出动消防车ids
const nowFireCarModels = ref([]);
// 机场当前Wayline实例 航线
const dockWaylines = ref([]);
// 机场当前WaylinePolygon  航面
const dockWaylinePolygons = ref([]);

// 当前航线信息
const nowAirlineInfo = ref({ airline_id: null, airline_json: null, wayline_type: null });

/**
 * 定位无人机
 * @param {*} nav 无人机实时OSD数据
 * @param {*} deviceInfo 无人机基础信息
 */
function setNavModel(nav, deviceInfo) {
  if (nav === undefined || nav === null) {
    // Todo 下线移出无人机
    return;
  }
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  const position = Cesium.Cartesian3.fromDegrees(nav.longitude, nav.latitude, nav.height);
  let device_sn = deviceInfo.device_sn;
  // Todo 下线移出无人机
  if (nav.mode_code === 14) {
    console.log('飞机下线');
    if (navList.value[device_sn] !== null || navList.value[device_sn] !== undefined) {
      flyView.entities.remove(navList.value[device_sn]);
      navList.value[device_sn] = null;
      flyView.entities.getById('dock_' + deviceInfo.dock_sn).billboard.show = true;
    }
    return;
  }

  let hpr = new Cesium.HeadingPitchRoll(
    Cesium.Math.toRadians(nav.attitude_head),
    Cesium.Math.toRadians(nav.attitude_pitch),
    Cesium.Math.toRadians(nav.attitude_roll)
  );
  let orientation = Cesium.Transforms.headingPitchRollQuaternion(position, hpr);
  if (navList.value[device_sn] === null || navList.value[device_sn] === undefined) {
    deviceInfo.device_xyz = { x: nav.longitude, y: nav.latitude, z: nav.height };
    deviceInfo.airline_id = null;
    let navModel = flyView.entities.add({
      id: 'nav_' + deviceInfo.device_sn,
      name: deviceInfo[device_sn],
      position: position,
      orientation: orientation,
      model: {
        uri: airModel,
        minimumPixelSize: 64,
        maximumScale: 20000,
        scale: 0.15,
        incrementallyLoadTextures: true, // 加载模型后纹理是否可以绯续流入
        runAnimations: true, //是否启动模型中指定的gltf 动画
        clampAnimations: true, //指定 gltf 动画是否在没有关键帧的持续时间内保持最后一个姿势
        shadows: Cesium.ShadowMode.ENABLED,
        heightReference: Cesium.HeightReference.NONE
      }
    });
    navList.value[device_sn] = navModel;
  } else {
    navList.value[device_sn].position = position;
    navList.value[device_sn].orientation = orientation;
  }
  // 判断机场无人机处于 航线飞行,指令飞行
  // 查询正在作业的任务
  if (nav.mode_code === 5 || nav.mode_code === 17) {
    if (nowAirlineInfo.value.airline_id === null) {
      navList.value[device_sn].model.scale = 0.05;
      getDockRunningJob(deviceInfo);
    }
  } else {
    if (nowAirlineInfo.value.airline_id !== null) {
      navList.value[device_sn].model.scale = 0.15;
      removeWayline(deviceInfo);
    }
  }

  // 以下状态则隐藏机场
  if (nav.mode_code === 0 || nav.mode_code === 1 || nav.mode_code === 2) {
    flyView.entities.getById('dock_' + deviceInfo.dock_sn).billboard.show = false;
  } else {
    flyView.entities.getById('dock_' + deviceInfo.dock_sn).billboard.show = true;
  }
}

/**
 * 定位机场
 * @param {*} dock 机场实时OSD数据
 * @param {*} deviceInfo 机场基础信息
 */
function setDockModel(dock, deviceInfo) {
  if (dock === undefined || dock === null) {
    return;
  }
  if (dock.basic_osd === undefined) {
    return;
  }
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  const position = Cesium.Cartesian3.fromDegrees(
    dock.basic_osd.longitude,
    dock.basic_osd.latitude,
    dock.basic_osd.height
  );
  let dock_sn = deviceInfo.dock_sn;
  if (dockList.value[dock_sn] === null || dockList.value[dock_sn] === undefined) {
    // 将坐标放到设备列表中
    deviceInfo.dock_xyz = { x: dock.basic_osd.longitude, y: dock.basic_osd.latitude, z: dock.basic_osd.height };
    let navModel = flyView.entities.add({
      id: 'dock_' + deviceInfo.dock_sn,
      name: deviceInfo.dock_sn,
      position: position,
      billboard: {
        show: true,
        image: airport_live_url,
        width: 40,
        height: 52,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        disableDepthTestDistance: Number.POSITIVE_INFINITY
      }
    });
    dockList.value[dock_sn] = navModel;
    setTimeout(() => {
      createDockNamePopup(flyView, deviceInfo);
    }, 200);
  } else {
    // 位置有变化再更新
    let oldPosition = dockList.value[dock_sn].position._value;
    if (oldPosition.x !== position.x && oldPosition.y !== position.y && oldPosition.z !== position.z) {
      dockList.value[dock_sn].position = position;
    }
    // 更新图片状态
    if (dock.basic_osd.mode_code === 4) {
      dockList.value[dock_sn].billboard.image = airport_working_url;
    } else if (dock.basic_osd.mode_code === -1) {
      dockList.value[dock_sn].billboard.image = airport_off_url;
    } else {
      dockList.value[dock_sn].billboard.image = airport_live_url;
    }
  }
}

/**
 * 获取机场正在执行的任务、关联的警情
 */
function getDockRunningJob(deviceInfo) {
  getRunningJob(deviceInfo.dock_sn).then(res => {
    if (res !== undefined) {
      console.log('getRunningJob', res);
      nowAirlineInfo.value.airline_id = res.airline_id;
      nowAirlineInfo.value.airline_json = res.airline_json;
      nowAirlineInfo.value.wayline_type = res.wayline_type;
      drawWayline(deviceInfo);
      // res.alarm_id = '1805158661761273858';
      if (res.alarm_id !== undefined) {
        // nowAlarmId = res.alarm_id;
        // showFlag.value = false;
        // initSituationPlotting();
        setFireInfo();
      }
    }
  });
}

// 绘制正在飞行的航线(多航线未做处理)
function drawWayline(deviceInfo) {
  console.log('----deviceInfo', deviceInfo);
  setTimeout(() => {
    const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
    if (nowAirlineInfo.value.wayline_type === 0) {
      const planJson = JSON.parse(nowAirlineInfo.value.airline_json);
      let waylineInstace = new Wayline(flyView, planJson);
      waylineInstace.createWayLine();
      dockWaylines.value[deviceInfo.dock_sn] = waylineInstace;
    } else if (nowAirlineInfo.value.wayline_type === 1) {
      const planJson = JSON.parse(nowAirlineInfo.value.airline_json);
      const waylinePolygonId = 'waylinePolygon_' + deviceInfo.dock_sn;
      addWaylineOnMap(flyView, {
        id: waylinePolygonId,
        airline_json: planJson
      });
      dockWaylinePolygons.value[deviceInfo.dock_sn] = waylinePolygonId;
    }
  }, 1000);
}

// 移出飞行完的航线
function removeWayline(deviceInfo) {
  if (dockWaylines.value[deviceInfo.dock_sn] !== undefined) {
    dockWaylines.value[deviceInfo.dock_sn].removeCustomData();
    nowAirlineInfo.value.airline_id = null;
    nowAirlineInfo.value.airline_json = null;
    nowAirlineInfo.value.wayline_type = null;
    dockWaylines.value[deviceInfo.dock_sn] = null;
  }
  if (dockWaylinePolygons.value[deviceInfo.dock_sn] !== undefined) {
    delWayline({
      id: dockWaylinePolygons.value[deviceInfo.dock_sn]
    });
    nowAirlineInfo.value.airline_id = null;
    nowAirlineInfo.value.airline_json = null;
    nowAirlineInfo.value.wayline_type = null;
    dockWaylinePolygons.value[deviceInfo.dock_sn] = null;
  }
}

/**
 * 设置警情灾害点
 * @param {*} item 警情相关信息
 */
function setFireInfo() {
  const requestJson = {
    alarm_id: nowAlarmId
  };
  // 根据警情ID获取警情相关信息
  getInfoByAlarmId(requestJson)
    .then(res => {
      if (res) {
        setFireStationsAndCar(res);
      }
    })
    .catch(err => {
      console.log(err);
    });
}

/**
 * 设置消防车、消防站位置
 * @param {*} res 警情相关消防站、消防车信息
 */
function setFireStationsAndCar(res) {
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  const fireCarDataSource = flyView.dataSources.getByName('fireCarLyr')[0];
  fireCarDataSource.entities.removeAll();
  nowFireCarModels.value = [];
  if (car_intervalTimer) {
    clearInterval(car_intervalTimer);
  }
  // 警情内容
  alarm_content.value = res.alarm_name;
  // 灾害点
  const firePosition = Cesium.Cartesian3.fromDegrees(res.lon, res.lat, 10);
  fireCarDataSource.entities.add({
    id: 'firePnt',
    position: firePosition,
    billboard: {
      show: true,
      image: firePnt_url,
      width: 40,
      height: 52,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      disableDepthTestDistance: Number.POSITIVE_INFINITY
    }
  });

  // 消防站
  if (res.station_list.length > 0) {
    res.station_list.forEach(oneStation => {
      const position = Cesium.Cartesian3.fromDegrees(oneStation.lon, oneStation.lat, 10);
      fireCarDataSource.entities.add({
        id: 'fireStation' + oneStation.station_id,
        position: position,
        billboard: {
          show: true,
          image: fireStation_url,
          width: 40,
          height: 52,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          disableDepthTestDistance: Number.POSITIVE_INFINITY
        }
      });
    });
  }
  // 消防车轮询
  if (res.car_ids.length > 0) {
    const requestJson = {
      car_ids: res.car_ids
    };
    setCarLocation(requestJson, fireCarDataSource);
    car_intervalTimer = setInterval(() => {
      setCarLocation(requestJson, fireCarDataSource);
    }, 5000);
  }
}

// 消防车定位
function setCarLocation(requestJson, fireCarDataSource) {
  getCarLocation(requestJson)
    .then(res => {
      if (res) {
        res.forEach(oneCar => {
          const position = Cesium.Cartesian3.fromDegrees(oneCar.lon, oneCar.lat, 10);
          const carId = 'fireCar' + oneCar.car_id;
          if (nowFireCarModels.value[carId] === null || nowFireCarModels.value[carId] === undefined) {
            let fireCarModel = fireCarDataSource.entities.add({
              id: carId,
              position: position,
              billboard: {
                show: true,
                image: fireCar_url,
                width: 40,
                height: 52,
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                disableDepthTestDistance: Number.POSITIVE_INFINITY
              }
            });
            nowFireCarModels.value[carId] = fireCarModel;
          } else {
            nowFireCarModels.value[carId].position = position;
          }
        });
      }
    })
    .catch(err => {
      console.log(err);
    });
}

/**
 * 处理当前机场相关信息
 * @param {*} dockInfo 当前机场相关信息
 */
// function setDockInfo(dockInfo) {
//   if (dockInfo.alarm_id !== '') {
//     alarmId = dockInfo.alarm_id;
//     setTimeout(() => {
//       // initSituationPlotting();
//       // setFireInfo(dockInfo);
//     }, 200);
//   }
// }

// 暴露方法给父组件
defineExpose({
  // setDockInfo,
  setNavModel,
  setDockModel
});

onMounted(() => {
  intervalTimer = setInterval(() => {
    const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
    updatePoi(flyView);
    initSituationPlotting();
  }, 1000);
});
onUnmounted(() => {
  clearInterval(intervalTimer);
  if (car_intervalTimer) {
    clearInterval(car_intervalTimer);
  }
});
onBeforeUnmount(() => {});
</script>

<style lang="scss" scoped>
.map-100 {
  height: 100%;
  width: 100%;
  // position: relative;

  .tool {
    position: absolute;
    top: 50px;
    color: red;
    background-color: antiquewhite;
    z-index: 100;
  }

  .alarminfo {
    position: absolute;
    top: 56px;
    opacity: 0.7;
    background: #242424;
    width: calc(100% - 150px);
    height: 30px;
    left: 30px;
    display: flex;
    .alarm-img {
      width: 22px;
      height: 22px;
      margin: 5px;
    }
    .alarm-content {
      font-family: SourceHanSansSC-Regular;
      font-size: 14px;
      color: #ffffff;
      text-align: left;
      line-height: 30px;
      font-weight: 400;
    }
  }
}
</style>
