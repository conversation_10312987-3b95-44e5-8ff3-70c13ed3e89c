import * as Cesium from 'cesium';
class CesiumEngine {
  static instance = null;
  el = 'cesiumContainer';
  viewer = null;
  constructor() {}
  init(el) {
    this.el = el || 'cesiumContainer';
    // Cesium.Ion.defaultAccessToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI1ZjEyMzNkNi1jOGYzLTQxOTgtOGVlYy05MjA0NjYxY2JkMTUiLCJpZCI6NTI3NjIsImlhdCI6MTcwMzI5MjY4NH0.WS3siUOPAi42Ckpxi1-SuKJhyWIlZaEk35K2z_hKiCk";
    this.viewer = new Cesium.Viewer(this.el, {
      // 是否显示信息窗口
      // infoBox: true,
      // 是否创建动画
      animation: true,
      // 动画自动播放
      shouldAnimate: true,
      // 是否显示图层选择器
      baseLayerPicker: false,
      // 是否显示全屏按钮
      fullscreenButton: false,
      // 是否显示右上角的查询按钮
      geocoder: false,
      // 是否显示HOME按钮
      homeButton: false,
      // 选择框去掉
      selectionIndicator: false,
      // 是否显示场景控制按钮
      sceneModePicker: false,
      // 是否显示帮助按钮
      navigationHelpButton: false,
      // 是否显示时间轴
      timeline: true,
      //是否指定仅为三维模式，全部使用三维模式可节约GPU资源
      scene3DOnly: false,
      //是否显示点击要素之后显示的信息
      infoBox: false,
      // 去掉访问官网底图
      imageryProvider: new Cesium.SingleTileImageryProvider({
        url: 'data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWNgYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg=='
      })
    });
    // 隐藏版权信息
    this.viewer._cesiumWidget._creditContainer.style.display = 'none'; // 隐藏版权信息
    // 删除默认底图
    this.viewer.imageryLayers.removeAll();
    // this.viewer.scene.skyAtmosphere.show = false; // 隐藏大气层
    this.viewer.scene.globe.depthTestAgainstTerrain = true;
    this.viewer.animation.viewModel.dateFormatter = DateTimeFormatter;
    this.viewer.animation.viewModel.timeFormatter = TimeFormatter;
    this.viewer.timeline.makeLabel = DateTimeFormatter;
    function TimeFormatter(time, viewModel) {
      return DateTimeFormatter(time, viewModel, true);
    }
    function DateTimeFormatter(datetime, viewModel, ignoredate) {
      var julianDT = new Cesium.JulianDate();
      Cesium.JulianDate.addHours(datetime, 8, julianDT);
      var gregorianDT = Cesium.JulianDate.toGregorianDate(julianDT);
      var objDT;
      if (ignoredate) {
        objDT = "";
      } else {
        objDT = new Date(
          gregorianDT.year,
          gregorianDT.month - 1,
          gregorianDT.day
        );
        objDT =
          gregorianDT.year +
          "年" +
          gregorianDT.month.toString().padStart(2, "0") +
          "月" +
          gregorianDT.day +
          "日";
        if (viewModel || gregorianDT.hour + gregorianDT.minute === 0) {
          return objDT;
          objDT += "";
        }
      }
      return (
        objDT +
        `${gregorianDT.hour.toString().padStart(2, "0")}:${gregorianDT.minute.toString().padStart(2, "0")}:${gregorianDT.second.toString().padStart(2, "0")}`
      );
    }
  }

  static getInstance() {
    if (!CesiumEngine.instance) {
      CesiumEngine.instance = new CesiumEngine();
    }
    return CesiumEngine.instance;
  }
  destroyed() {
    this.viewer.entities.removeAll();
  }
}

export default CesiumEngine;
