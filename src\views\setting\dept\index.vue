<!--组织管理-->
<script>
export default {
  name: 'DeptManage'
};
</script>

<script setup>
import { reactive } from 'vue';
import optionData from '@/utils/option-data';
import handleTree from '@/utils/handleTree';
import { listDept, getDept, delDept, addDept, updateDept } from '@/api/system/dept';
import { authorityShow } from '@/utils/authority';
const { proxy } = getCurrentInstance();
const deptList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref('');
const deptOptions = ref([]);
const isExpandAll = ref(true);
const refreshTable = ref(true);
const deptRef = ref(null);

const data = reactive({
  form: {},
  queryParams: {
    name: undefined,
    status: undefined
  },
  rules: {
    // parent_id: [{ required: true, message: '上级组织不能为空', trigger: 'blur' }],
    name: [{ required: true, message: '组织名称不能为空', trigger: 'blur' }],
    sort: [{ required: true, message: '显示排序不能为空', trigger: 'blur' }],
    email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
    phone: [{ pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询组织列表 */
function getList() {
  loading.value = true;
  listDept({})
    .then(response => {
      deptList.value = handleTree(response, 'id');
      loading.value = false;
    })
    .catch(error => {
      deptList.value = [];
      loading.value = false;
    });
}

function handleSearch() {
  if (queryParams.value.name == '') {
    queryParams.value.name = '';
    queryParams.value.status = '';
    getList();
  } else {
    deptList.value = filterTree(deptList.value).filter(item => item);
  }
}

function fileterChildren(data) {
  return data.map(item => {
    if (item?.name.indexOf(queryParams.value.name) > -1) {
      if (item.children) {
        item.children = fileterChildren(item.children).filter(item => item) || [];
      }
      return toRaw(item);
    } else {
      if (item.children) {
        if (fileterChildren(item.children).filter(item => item).length >= 0) {
          item.children = fileterChildren(item.children).filter(item => item) || [];
          if (item.children.length == 0) {
            return undefined;
          } else {
            return toRaw(item);
          }
        }
      }
    }
  });
}

function filterTree(data) {
  return data.map(item => {
    if (item?.name.indexOf(queryParams.value.name) > -1) {
      if (item.children) {
        if (fileterChildren(item.children).filter(item => item).length >= 0) {
          item.children = fileterChildren(item.children).filter(item => item) || [];
          return toRaw(item);
        }
      }
    }
    if (item.children) {
      if (fileterChildren(item.children).filter(item => item).length >= 0) {
        item.children = fileterChildren(item.children).filter(item => item) || [];
        if (item.children.length == 0) {
          return undefined;
        } else {
          return toRaw(item);
        }
      }
    }
  });
}
/**
 * 重置查询
 */
function resetQuery() {
  queryParams.value.name = '';
  queryParams.value.status = '';
  getList();
}
//清空表单校验和值
const clearFormValidation = () => {
  nextTick(() => {
    deptRef.value.resetFields();
    deptRef.value.clearValidate();
  });
};
/** 弹窗表单重置 */
function reset() {
  clearFormValidation();
  form.value = {
    id: undefined,
    parent_id: undefined,
    name: undefined,
    sort: undefined,
    status: 0
  };
  listDept().then(response => {
    deptOptions.value = handleTree(response, 'id');
  });
  loading.value = false;
}
/** 取消按钮 */
function cancel() {
  reset();
  open.value = false;
}
/** 新增按钮操作 */
function handleAdd(row) {
  open.value = true;
  reset();
  nextTick(() => {
    if (row != undefined) {
      form.value.parent_id = row.id;
    }
  });
  title.value = '新增组织';
}
/** 修改按钮操作 */
function handleUpdate(row) {
  open.value = true;
  reset();
  getDept(row.id).then(response => {
    form.value = response;
    title.value = '编辑组织';
  });
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs['deptRef'].validate(valid => {
    if (valid) {
      form.value.status = 0;
      if (form.value.id != undefined) {
        updateDept(form.value).then(response => {
          ElMessage.success('更新成功');
          open.value = false;
          getList();
        });
      } else {
        addDept(form.value).then(response => {
          ElMessage.success('新增成功');
          open.value = false;
          getList();
        });
      }
    }
  });
}

/**
 * 删除
 */
function handleDelete(row) {
  ElMessageBox.confirm(`确认后将删除此组织，且无法进行恢复`, '确认删除所选组织？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    autofocus: false,
    closeOnClickModal: false,
    type: 'warning'
  }).then(() => {
    delDept(row.id).then(data => {
      ElMessage.success('删除成功');
      loading.value = true;
      listDept({})
        .then(response => {
          deptList.value = handleTree(response, 'id');
          loading.value = false;
        })
        .catch(error => {
          deptList.value = [];
          loading.value = false;
        });
    });
  });
}

onMounted(() => {
  getList();
});
</script>

<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
          <el-form-item label="" prop="name">
            <el-input
              class="input-serach"
              v-model="queryParams.name"
              placeholder="请输入组织名称"
              clearable
              @blur="queryParams.name = $event.target.value.trim()"
              @keyup.enter="handleSearch"
              maxlength="32"
            />
          </el-form-item>
          <el-input style="display: none" />
          <!-- <el-form-item label="状态：" prop="status">
            <el-select class="input-serach" v-model="queryParams.status" placeholder="请选择状态" clearable>
              <el-option
                v-for="(item, index) in optionData.deptStatusOption"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item> -->
        </el-form>
      </div>
      <div class="search-btn">
        <el-button type="primary" @click="handleSearch()"><i-ep-search />搜索</el-button>
        <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
      </div>
    </div>
    <el-card shadow="never">
      <template #header v-if="authorityShow('createDept')">
        <el-button type="primary" @click="handleAdd()"><i-ep-plus />新增组织</el-button>
      </template>
      <el-table
        v-loading="loading"
        :data="deptList"
        row-key="id"
        height="540"
        :default-expand-all="isExpandAll"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        stripe
      >
        <el-table-column prop="name" label="组织名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sort" label="排序" show-overflow-tooltip></el-table-column>
        <!-- <el-table-column prop="status" label="状态" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ optionData.deptStatusOption.find(item => item.value == scope.row.status).label || '-' }}</span>
          </template>
        </el-table-column> -->
        <!-- <el-table-column label="创建时间" align="center" prop="createTime" show-overflow-tooltip /> -->
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          v-if="authorityShow('editDept') || authorityShow('createChildDept') || authorityShow('deleteDept')"
        >
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-if="authorityShow('editDept')"
              >编辑</el-button
            >
            <el-button
              link
              type="primary"
              icon="Plus"
              @click="handleAdd(scope.row)"
              v-if="authorityShow('createChildDept')"
              >新增</el-button
            >
            <el-button
              link
              type="danger"
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-if="authorityShow('deleteDept')"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加或修改组织对话框 -->
    <el-dialog :title="title" v-if="open" v-model="open" width="700px" append-to-body :close-on-click-modal="false">
      <el-form ref="deptRef" :model="form" :rules="rules" label-width="80px">
        <el-row :gutter="20">
          <el-col :span="24" v-if="form.parent_id != 0">
            <el-form-item label="上级组织" prop="parent_id">
              <el-tree-select
                v-model="form.parent_id"
                :data="deptOptions"
                :props="{ value: 'id', label: 'name', children: 'children' }"
                value-key="id"
                placeholder="选择上级组织"
                check-strictly
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="组织名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入组织名称" maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="sort">
              <el-input-number style="width: 100%" v-model="form.sort" controls-position="right" :min="0" :max="9999" />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="负责人" prop="leader">
              <el-input v-model="form.leader" placeholder="请输入负责人" maxlength="20" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
            </el-form-item>
          </el-col> -->
          <!-- <el-col :span="12">
            <el-form-item label="组织状态">
              <el-radio-group v-model="form.status">
                <el-radio v-for="item in optionData.deptStatusOption" :key="item.value" :label="item.value">{{
                  item.label
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col> -->
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<style scoped lang="scss">
.input-serach {
  width: 200px;
}
.flex-center {
  display: flex;
  align-items: center;
  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
}
.search {
  display: flex;
  align-items: center;
  padding: 0 24px;
  .search-form {
    padding-top: 16px;
    flex: 1;
  }
}
</style>
