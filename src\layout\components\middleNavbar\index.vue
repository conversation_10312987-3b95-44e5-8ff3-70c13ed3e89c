<template>
  <el-menu
    :default-active="activeIndex"
    class="el-menu-demo"
    mode="horizontal"
    background-color="#11253e"
    text-color="#fff"
    active-text-color="#4582E6"
    :ellipsis="false"
    menu-trigger="click"
    unique-opened="true"
    @select="handleSelect"
    close-on-click-outside="true"
  >
    <sidebar-item
      v-for="route in newRoutes"
      :item="route"
      :key="route.path"
      :base-path="route.path"
      :is-collapse="!appStore.sidebar.opened"
    />
  </el-menu>
  <ChangePwd ref="changePwdRef" />
</template>

<script>
export default {
  name: 'avatarArea'
};
</script>

<script setup>
import ChangePwd from '../ChangePwd/index.vue';
import { constantRoutes } from '@/router/index';
import SidebarItem from '../Sidebar/SidebarItem.vue';
import { useAppStore } from '@/store/modules/app';
import { onMounted, onUnmounted } from 'vue';
import { refreshToken } from '@/api/devices';
import { useStorage } from '@vueuse/core';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store/modules/user';

const router = useRouter();
const changePwdRef = ref('changePwdRef');
const appStore = useAppStore();
const activeIndex = ref('/');
const newRoutes = ref([]);
const userStore = useUserStore();
let authority = localStorage.getItem('menu') && JSON?.parse(localStorage.getItem('menu'));
let timerRef = null;
const token = useStorage('accessToken', '');

function handleSelect(key, keyPath) {
  console.log(key, keyPath);
  // activeIndex.value = keyPath[0] || 0
}

watch(
  () => authority,
  val => {
    console.log('val', val);
  }
);

function handleMenu() {
  if (authority === null) {
    authority = localStorage.getItem('menu') && JSON?.parse(localStorage.getItem('menu'));
  }
  let arr = [];
  arr = constantRoutes?.filter(item => {
    // 普通权限判断
    const inMenu = authority?.indexOf(item.path?.replace('/', '')) > -1;
    // 超级管理员且路由菜单有admin权限
    const isForSuperAdmin = Array.isArray(item.authority) && item.authority.includes('admin') && userStore.userData.is_super_admin;
    return inMenu || isForSuperAdmin;
  });
  arr.forEach(res => {
    if (res.children && res.children.length) {
      res.children = res.children.filter(resp => {
        const inMenu = authority?.indexOf(resp.path?.replace('/', '')) > -1;
        const isForSuperAdmin = Array.isArray(resp.authority) && resp.authority.includes('admin') && userStore.userData.is_super_admin;
        return inMenu || isForSuperAdmin;
      });
    }
  });
  newRoutes.value = arr;
}

onMounted(() => {
  activeIndex.value = router.currentRoute.value.fullPath;
  handleMenu();
  timerRef = setInterval(() => {
    refreshToken({}).then(res => {
      token.value = res.access_token;
    });
  }, 1000 * 60 * 30);
});

onUnmounted(() => {
  window.$bus.off('dialogBeforeClose');
  clearInterval(timerRef);
});
</script>

<style lang="scss">
.el-popper.is-light {
  border: none !important;
  box-shadow: 0 2px 4px #000;
}
.el-select-dropdown,
.el-picker-panel__body {
  box-shadow: 0 2px 4px rgba(0,0,0,0.5);
}
.side-link {
  .el-menu-item.is-active:hover {
    color: #fff !important;
  }
}
.el-menu-item.is-active:not(.is-disabled):hover {
  background-color: #4582e6 !important;
}
.el-menu-item:not(.is-disabled):hover {
  background-color: #001129 !important;
}
</style>

<style lang="scss" scoped>
:deep(.el-sub-menu__title) {
  padding: 0;
}
:deep(.el-menu-item) {
  padding-left: 0;
}
.active-color {
  background: #4582e6 !important;
}
.el-dropdown-link {
  width: 114px;
  height: 40px;
  display: inline-block;
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #ffffff;
  text-align: center;
  line-height: 40px;
  font-weight: 400;
}
.el-menu--horizontal.el-menu {
  height: 48px;
  border: none;
}
.el-menu-demo {
  // :global(.el-menu-item .is-active) {
  //   background: #4582E6;
  // }
  :deep(.el-menu-item .is-active) {
    background: #344054;
    color: #ffffff;
  }
  :deep(.side-link .el-menu-item.is-active) {
    background: #4582e6;
    color: #ffffff;
  }
  :deep(.el-sub-menu) {
    min-width: 116px;
  }
  :deep(.el-sub-menu.is-active .el-sub-menu__title) {
    background: #4582e6;
    color: #ffffff;
  }
  :global(.el-menu-item) {
    width: 98%;
    margin: 0 auto;
    color: #fff;
    display: block;
    text-align: center;
  }
  :global(.el-menu--popup) {
    min-width: 138px;
    text-align: center;
    margin: 0 auto;
  }
}

.username {
  margin: 0 10px 0 10px;
  color: #fff;
}
.arrow-down {
  color: #fff;
}
</style>
