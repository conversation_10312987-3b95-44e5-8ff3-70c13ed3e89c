import request from '@/utils/request';
import { MAIN_PATH,CONTROL_PATH, APPLICTION_LOGIN, API_VERSION,APPLICTION_WORKSPACES } from '../config/index';
import { useUserStoreHook } from '@/store/modules/user';


// 主路径
const BASE_URL = MAIN_PATH + API_VERSION + APPLICTION_LOGIN;

// 模块路径
const MODULES_LOGIN_AUTH_PATH = '/tenant-sys-auth/login/';
const MODULES_LOGIN_AUTH = '/tenant-sys-auth/';

const MODULES_LOGOUT_AUTH_PATH = '/tenant-sys-auth/logout/';
const MODULES_REGISTER_AUTH_PATH = '/tenant-sys-register/';

// 登录API
export function loginApi(data) {
  return request({
    url: `${BASE_URL}`,
    method: 'post',
    data
  });
}

// 获取 mqtt 连接认证
export function getMqttInfo(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${CONTROL_PATH}${API_VERSION}${APPLICTION_WORKSPACES}/${workspace_id}/drc/connect`,
    method: 'post',
    data
  });
}



// 短信登录API
export function loginSms(data) {
  return request({
    url: `${BASE_URL}${MODULES_LOGIN_AUTH_PATH}sms`,
    method: 'post',
    data
  });
}

// 登出API
export function logoutApi() {
  return request({
    url: `${BASE_URL}${MODULES_LOGOUT_AUTH_PATH}`,
    method: 'get'
  });
}

// 企业注册
export function company(data) {
  return request({
    url: `${BASE_URL}${MODULES_REGISTER_AUTH_PATH}company`,
    method: 'post',
    data
  });
}
// 个人注册
export function individual(data) {
  return request({
    url: `${BASE_URL}${MODULES_REGISTER_AUTH_PATH}individual`,
    method: 'post',
    data
  });
}
// 发送验证码
export function sendSMSCode(params) {
  return request({
    url: `${BASE_URL}${MODULES_REGISTER_AUTH_PATH}send`,
    method: 'get',
    params
  });
}

export function changePassword(data) {
  return request({
    url: `${BASE_URL}${MODULES_LOGIN_AUTH}passwd`,
    method: 'put',
    data
  });
}

export function exclusiveLogin(form) {
  const { data, tenantId } = form;
  return request({
    url: `${BASE_URL}${MODULES_LOGIN_AUTH_PATH}pass/${tenantId}`,
    method: 'post',
    data
  });
}

export function exclusiveLoginSms(form) {
  const { data, tenantId } = form;

  return request({
    url: `${BASE_URL}${MODULES_LOGIN_AUTH_PATH}sms/${tenantId}`,
    method: 'post',
    data
  });
}

export function subLogin(data) {
  return request({
    url: `${BASE_URL}${MODULES_LOGIN_AUTH_PATH}child`,
    method: 'post',
    data
  });
}

export function sendNote(data) {
  return request({
    url: `${BASE_URL}${MODULES_LOGIN_AUTH_PATH}send`,
    method: 'get',
    params: data
  });
}


/**
 * 获取验证码
 */
export function getCaptchaApi() {
  return request({
    url: `captcha`,
    method: 'post'
  });
}