import router from '@/router';
import { useUserStoreHook } from '@/store/modules/user';
import { usePermissionStoreHook } from '@/store/modules/permission';
import { useMqttStoreHook } from '@/store/modules/mqttService';

import { translateRouteTitleI18n } from './utils/i18n';

import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
// import { useWebsocketStoreHook } from './store/modules/websocket';
// import MainSocketService from '@/utils/websocket/MainSocketService';
import JJSocketService from './utils/websocket/JJSocketService';

NProgress.configure({ showSpinner: false }); // 进度条

const permissionStore = usePermissionStoreHook();
const userStore = useUserStoreHook();
// const { generateWebsocket, destroyWebsocket } = useWebsocketStoreHook();

// 白名单路由
const whiteList = ['/login', '/register', '/reset-password', '/pilot-login','/pilot-home','/h5-video'];

router.beforeEach(async (to, from, next) => {
  NProgress.start();
  const hasToken = localStorage.getItem('accessToken');
  const { userData } = userStore;

  if (hasToken) {
    if (to.path === '/login') {
      // 如果已登录，跳转首页
      next({ path: '/' });
      NProgress.done();
    } else {
      if (userData.username) {
        if (to.matched.length === 0) {
          // 如果未匹配到路由 则跳转404页面
          from.name ? next({ name: from.name }) : next('/404');
        } else {
          if (to.meta?.title) {
            document.title = translateRouteTitleI18n(to.meta.title);
          }
          next();
        }
      } else {
        try {
          // 开启websocket
          initWebsocket();
          // 获取用户信息
          await userStore.getInfo();
          // // 生成路由
          // permissionStore.generateRoutes();
          // // 动态添加可访问路由

          // permissionStore.routes.forEach(route => {
          //   router.addRoute(route);
          // });

          // hack方法 确保addRoutes已完成

          next({ ...to, replace: true });
        } catch (error) {
          // 销毁websocket
          destroyedWs();
          // 移除 token 并跳转登录页
          await userStore.resetToken();
          next(`/login?redirect=${to.path}`);
          NProgress.done();
        }
      }
    }
  } else {
    // 未登录可以访问白名单页面
    if (whiteList.indexOf(to.path) !== -1) {
      next();
    } else {
      // 销毁websocket
      // destroyWebsocket();
      next(`/login?redirect=${to.path}`);
      NProgress.done();
    }
  }
});
const initWebsocket = () => {
  // const jjwsIns = JJSocketService.Instance;
  // jjwsIns.connect(() => {});
  
  // const wsIns = MainSocketService.Instance;
  // wsIns.connect(() => {});
};
const destroyedWs = () => {
  const jjwsIns = JJSocketService.Instance;
  jjwsIns.disconnect();

  // const wsIns = MainSocketService.Instance;
  // wsIns.disconnect();
};

router.afterEach(() => {
  NProgress.done();
});
