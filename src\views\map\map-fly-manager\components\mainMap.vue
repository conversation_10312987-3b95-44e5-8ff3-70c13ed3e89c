<template>
  <div id="mainMapContainer" class="map-container">
    <!-- 地图2d/3d切换 -->
    <div class="viewer-ctrl" @click="handleViewerCtrlClick">
      {{ map_type }}
    </div>
    <!-- 图层管理 -->
    <div class="lyr-manager">
      <div class="padding-10">
        <el-checkbox v-model="lyr_img" label="天地图影像" @change="handleCheckboxChange('天地图影像')" />
        <el-checkbox v-model="lyr_vec" label="天地图矢量" @change="handleCheckboxChange('天地图矢量')" />
        <el-checkbox v-model="lyr_noFly" label="禁飞区" @change="handleCheckboxChange('禁飞区')" />
        <el-checkbox v-model="lyr_limitHeight" label="限高区" @change="handleCheckboxChange('限高区')" />
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'mainMap'
};
</script>
<script setup>
import { onMounted, onUnmounted, onBeforeUpdate, onBeforeUnmount } from 'vue';
import * as Cesium from 'cesium';
import {
  getOrCreateCesiumEngineInstance,
  getOrCreateTimeLineCesiumEngineInstance,
  CesiumLayerManager,
  imglayer,
  veclayer,
  cialayer,
  cvalayer,
  getNoFlyZoneData,
  flyTo,
  CesiumGLBLoader,
  startAnimation,
  stopAnimation,
  createPointEntity,
  CesiumFlight,
  interpolatePoints,
  addImageIcon,
  toCartesian3,
  projectCustomTerrainProvider,
  projectTerrainOnline,
  delCesiumEngineInstance,
  globalConfigResource
} from '@/components/Cesium/libs/cesium';
// import { map } from 'lodash';

const props = defineProps({
  showTimeline: {
    type: Boolean,
    default: false
  }
});

let mapEngine = null;
// 地图2d/3d切换
const map_type = ref('3D');
// 图层控制显影
const lyr_img = ref(true);
const lyr_vec = ref(false);
const lyr_noFly = ref(false);
const lyr_limitHeight = ref(false);
// 禁飞区数据源
const noFlyZoneLyr = new Cesium.CustomDataSource('noFlyZoneLyr');
// 限高区数据源
const limitHeightLyr = new Cesium.CustomDataSource('limitHeightLyr');
const initCesium = () => {
  if (!props.showTimeline) {
    mapEngine = getOrCreateCesiumEngineInstance('mainMap-fly');
  } else {
    mapEngine = getOrCreateTimeLineCesiumEngineInstance('mainMap-fly');
  }
  // 挂载主地图
  mapEngine?.init('mainMapContainer');
  // 天地图影像、矢量、影像注记、矢量注记
  const imageryLayers = mapEngine.viewer.imageryLayers;
  imageryLayers.addImageryProvider(imglayer, 0);
  imageryLayers.addImageryProvider(cialayer, 1);
  imageryLayers.addImageryProvider(veclayer, 2);
  imageryLayers.addImageryProvider(cvalayer, 3);
  imageryLayers.get(2).show = lyr_vec.value;
  imageryLayers.get(3).show = lyr_vec.value;
  // 定位到厦门
  const targetPointOption = {
    lon: globalConfigResource?.init?.lon || 0,
    lat: globalConfigResource?.init?.lat || 0,
    viewHeight: globalConfigResource?.init?.viewHeight || 0,
    heading: globalConfigResource?.init?.viewHeight || 0,
    pitch: globalConfigResource?.init?.viewHeight || 0,
    roll: globalConfigResource?.init?.roll || 0,
    duration: globalConfigResource?.init?.duration || 2
  };
  flyTo(
    mapEngine.viewer,
    targetPointOption.lon,
    targetPointOption.lat,
    targetPointOption.viewHeight,
    targetPointOption.heading,
    targetPointOption.pitch,
    targetPointOption.roll,
    targetPointOption.duration
  );
  // 禁飞区、限高区添加到Viewer中
  noFlyZoneLyr.show = lyr_noFly.value;
  limitHeightLyr.show = lyr_limitHeight.value;
  mapEngine.viewer.dataSources.add(noFlyZoneLyr);
  mapEngine.viewer.dataSources.add(limitHeightLyr);
  // 加载禁飞区、限高区数据
  loadNoFlyZoneData();
  loadTerrain();
};

// 加载禁飞区、限高区数据
const loadNoFlyZoneData = async () => {
  const noFlyZoneData = await getNoFlyZoneData();
  noFlyZoneData.forEach(item => {
    const sub_areas = item.sub_areas;
    if (sub_areas != null) {
      sub_areas.forEach((childItem, index) => {
        if (childItem.polygon_points != null) {
          const hierarchyPolygon = [];
          childItem.polygon_points.forEach(points => {
            const cartesianPoints = points.map(point => {
              const longitude = point[0];
              const latitude = point[1];
              return Cesium.Cartesian3.fromDegrees(longitude, latitude);
            });
            hierarchyPolygon.push(cartesianPoints);
          });
          const itemColor = childItem.color;
          // 区分禁飞区、限高区
          if (itemColor === '#979797') {
            limitHeightLyr.entities.add({
              id: `limitHeight_${item.area_id}_${index}`,
              polygon: {
                hierarchy: new Cesium.PolygonHierarchy(hierarchyPolygon[0]),
                material: Cesium.Color.fromCssColorString(itemColor).withAlpha(0.4)
              }
            });
          } else if (itemColor === '#DE4329') {
            noFlyZoneLyr.entities.add({
              id: `noFlyZone_${item.area_id}_${index}`,
              polygon: {
                hierarchy: new Cesium.PolygonHierarchy(hierarchyPolygon[0]),
                material: Cesium.Color.fromCssColorString(itemColor).withAlpha(0.4)
              }
            });
          }
        }
      });
    }
  });
};

// 添加地形
const loadTerrain = () => {
  // 大疆
  // const djTerrainProvider = new Cesium.CesiumTerrainProvider({
  //   url: 'https://digital-elevation.djicdn.com/terrain/global_ellipsoid/{z}/{x}/{y}.terrain?v=1.1.0&response-cache-control=public%2Cmax-age&response-content-disposition=attachment&filename=10433.terrain&response-content-encoding=gzip&ak=10B5A0D04665415CB24D1A8AB88A4FA6',
  //   requestVertexNormals: true,
  //   requestWaterMask: true
  // });
  // 默认地形
  const terrainProvider = Cesium.createWorldTerrain({
    requestWaterMask: false,
    requestVertexNormals: true
  });
  // 添加福建省地形
  // const customTerrainProvider = new Cesium.CesiumTerrainProvider({
  //   // url: 'http://*************/FJDX'
  //   url: 'http://************:24518/gis-mapServer/FJDX'
  // });
  if(projectCustomTerrainProvider){
    mapEngine.viewer.terrainProvider = terrainProvider;
  }
  else{
    mapEngine.viewer.terrainProvider = projectCustomTerrainProvider; // customTerrainProvider;
  }
};

// 图层控制
const handleCheckboxChange = lyr => {
  if (lyr === '天地图影像') {
    const imageryLayers = mapEngine.viewer.imageryLayers;
    imageryLayers.get(0).show = lyr_img.value;
    imageryLayers.get(1).show = lyr_img.value;
  } else if (lyr === '天地图矢量') {
    const imageryLayers = mapEngine.viewer.imageryLayers;
    imageryLayers.get(2).show = lyr_vec.value;
    imageryLayers.get(3).show = lyr_vec.value;
  } else if (lyr === '禁飞区') {
    noFlyZoneLyr.show = lyr_noFly.value;
  } else if (lyr === '限高区') {
    limitHeightLyr.show = lyr_limitHeight.value;
  }
};

// 切换2d/3d 地图
const handleViewerCtrlClick = () => {
  const camera = mapEngine.viewer.camera;
  const currentHeading = camera.heading;
  let desiredPitchInRadians = Cesium.Math.toRadians(0);
  const currentRoll = camera.roll;

  // 获取当前相机位置，增加对currentPosition有效性的检查
  const currentPosition = camera.positionCartographic
    ? camera.positionCartographic.clone()
    : console.error('当前相机位置无效');
  if (map_type.value === '3D') {
    desiredPitchInRadians = Cesium.Math.toRadians(-90);
    map_type.value = '2D';
  } else {
    desiredPitchInRadians = Cesium.Math.toRadians(-30);
    map_type.value = '3D';
  }
  try {
    // 保持当前位置，仅修改视角
    camera.flyTo({
      destination: Cesium.Ellipsoid.WGS84.cartographicToCartesian(currentPosition),
      orientation: {
        heading: currentHeading,
        pitch: desiredPitchInRadians,
        roll: currentRoll
      }
    });
  } catch (error) {
    // 异常处理逻辑
    console.error('视图切换过程中发生错误:', error);
  }
};

onMounted(() => {
  setTimeout(() => {
    initCesium();
  }, 50);
});
onUnmounted(() => {
  delCesiumEngineInstance('mainMap-fly');
});
onBeforeUnmount(() => {
  // delCesiumEngineInstance('mainMap-fly');
});
</script>

<style lang="scss" scoped>
.map-container {
  // height: 100%;
  // width: 100%;
  // position: relative;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
  bottom: 0px;
  right: 0px;
  margin: 0;
  padding: 0;
  position: absolute;

  .lyr-manager {
    position: fixed;
    width: 120px;
    height: 125px;
    bottom: 30px;
    right: 5px;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 2;
    border-radius: 2px;
  }

  .padding-10 {
    padding-left: 10px;
  }

  .viewer-ctrl {
    position: fixed;
    width: 32px;
    height: 32px;
    bottom: 160px;
    right: 5px;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 2;
    border-radius: 2px;
    line-height: 32px;
    text-align: center;
    cursor: pointer;
  }
}
</style>
