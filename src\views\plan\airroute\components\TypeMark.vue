<template>
  <div class="box-bg">
    <div class="create-box box" v-show="import_type === 0">{{ '创建' }}</div>
    <div class="input-box box" v-show="import_type === 1">{{ '导入' }}</div>
    <div class="input-box box" v-show="import_type === 2">{{ '导入' }}</div>
  </div>
</template>

<script setup>
import { defineProps, onMounted, onUnmounted } from 'vue';
const props = defineProps({
  import_type: {
    type: Number,
    default: 0
  }
});
onMounted(() => {});

onUnmounted(() => {});
</script>
<style lang="scss" scoped>
// 背景横向渐变
.box-bg {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  user-select: none;
  margin-right: 5px;
}

.box {
  padding: 2px 5px;
  font-size: 14px;
  border-radius: 2px;
  width: 40px;
}

.input-box {
  color: #64bca5;
  border: 1px solid #64bca5;
}

.create-box {
  color: #4192f7;
  border: 1px solid #4192f7;
}
</style>
