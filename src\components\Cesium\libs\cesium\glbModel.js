import * as Cesium from 'cesium';
import { vModelDynamic } from 'vue';
export class GlbModel {
  /**
   *
   * @param {*} viewer 视图
   * @param {*} url 模型地址
   * @param {*} position 模型位置 c3对象
   */
  constructor(viewer, url, position) {
    this.viewer = viewer;
    this.url = url;
    this.position = position;
    this.entity = null;
    this.heading = 0;
    this.pitch = 0;
    this.roll = 0;
    this.orientation = null;
    this.createModel(url, position);
  }

  /**
   * 创建模型
   * @param {*} url 模型地址
   * @param {*} position 模型位置 c3对象
   */
  createModel(url, position) {
    this.position = position;
    const orientation = this.createOrientation({
      position: this.position,
      heading: this.heading,
      pitch: this.pitch,
      roll: this.roll
    });
    this.orientation = orientation;
    this.entity = this.viewer.entities.add({
      name: 'feiji',
      position: position,
      orientation: orientation,
      // disableDepthTestDistance: Number.POSITIVE_INFINITY, // 点总是在最上层
      model: {
        uri: url,
        scale: 1.0, // 缩放比例
        minimumPixelSize: 128, // 最小像素大小
        maximumScale: 10, // 模型的最大比例尺大小
        incrementallyLoadTextures: true, // 加载模型后纹理是否可以继续流入
        runAnimations: true, // 是否启动模型中指定的 gltf 动画
        clampAnimations: true, // 指定 gltf 动画是否在没有关键帧的持续时间内保持最后一个姿势
        shadows: Cesium.ShadowMode.ENABLED
      },
      zIndex: 12
    });
    // this.viewer.trackedEntity = this.entity; // 聚焦模型
    // this.viewer.zoomTo(this.entity);
    // setTimeout(() => {
    //   this.viewer.trackedEntity = this.entity; // 聚焦模型
    //   this.viewer.zoomTo(this.entity);
    // }, 300);
  }

  /**
   * 设置位置
   * @param {*} position 模型位置 c3对象
   */
  setPosition(position) {
    this.entity.position = position;
    this.position = position;
    this.orientation = this.entity.orientation.getValue(this.viewer.clock.currentTime);
  }
  getPosition() {
    let cartographic = Cesium.Cartographic.fromCartesian(this.position);
    var latitude = Cesium.Math.toDegrees(cartographic.latitude); //经度
    var longitude = Cesium.Math.toDegrees(cartographic.longitude); //纬度
    var height = cartographic.height;
    return {
      position: this.position,
      latitude,
      longitude,
      height
    };
  }

  /**
   * 设置旋转四元数
   * @param {*} orientation
   */
  setOrientation(orientation) {
    this.orientation = orientation;
    this.entity.orientation = orientation;
  }

  /**
   * 设置模型盖度
   * @param {*} height
   */
  setHeight(height) {
    const currentPosition = this.entity.position.getValue(this.viewer.clock.currentTime);
    const newPosition = new Cesium.Cartesian3(currentPosition.x, currentPosition.y, height);
    this.setPosition(newPosition);
  }

  /**
   * 设置相机 heading 值
   * @param {*} heading  角度值 0-360 度
   */
  setHeading(heading) {
    this.heading = heading;
    const orientation = this.createOrientation({
      heading: this.heading
    });
    this.orientation = orientation;
    this.entity.orientation = orientation;
  }

  getHeading() {
    return this.heading;
  }

  // 测试使用 ok
  setHeading2(heading) {
    console.log('初始四元素:', this.orientation);
    this.heading = heading;
    const currentOrientation = this.entity.orientation.getValue(this.viewer.clock.currentTime);
    const hpr = Cesium.HeadingPitchRoll.fromQuaternion(currentOrientation);
    hpr.heading = Cesium.Math.toRadians(this.heading);
    hpr.pitch = Cesium.Math.toRadians(this.pitch);
    hpr.roll = Cesium.Math.toRadians(this.roll);
    // 方法 1 ok
    // const orientation = Cesium.Quaternion.fromHeadingPitchRoll(hpr);
    // 方法 2 构建四元素 ok
    const orientation = Cesium.Transforms.headingPitchRollQuaternion(this.position, hpr);
    this.entity.orientation = orientation;
  }

  /**
   * 设置相机 pitch 值
   * @param {*} pitch  角度值 0-360 度
   */
  setPitch(pitch) {
    this.pitch = pitch;
    const orientation = this.createOrientation({
      pitch: this.pitch
    });
    this.orientation = orientation;
    this.entity.orientation = orientation;
  }

  /**
   * 设置相机 roll 值
   * @param {*} roll  角度值 0-360 度
   */
  setRoll(roll) {
    this.roll = roll;
    const orientation = this.createOrientation({
      roll: this.roll
    });
    this.orientation = orientation;
    this.entity.orientation = orientation;
  }

  /**
   * 设置相机 scale 值
   * @param {*} scale 倍数
   */
  setScale(scale) {
    this.entity.model.scale = scale;
  }

  /**
   * 是否跟随
   * @param {*} isFollow
   */
  setFollow(isFollow = true) {
    if (isFollow) {
      this.viewer.trackedEntity = null;
      this.viewer.trackedEntity = isFollow ? this.entity : undefined;
    }
  }
  /**
   * 获取弧度值及hpr对象
   * @returns
   */
  getHpr() {
    const h = Cesium.Math.toRadians(this.heading); // 0度转弧度
    const p = Cesium.Math.toRadians(this.pitch);
    const r = Cesium.Math.toRadians(this.roll);
    const hpr = new Cesium.HeadingPitchRoll(h, p, r);
    let opt = {
      hpr,
      h,
      p,
      r
    };
    return opt;
  }

  zoomToModel() {
    this.viewer.zoomTo(this.entity);
  }
  // 创建四元素
  createOrientation(options) {
    const { position = null, heading = null, pitch = null, roll = null } = options;
    this.heading = heading || this.heading;
    this.pitch = pitch || this.pitch;
    this.roll = roll || this.roll;
    this.position = position || this.position;
    const h = Cesium.Math.toRadians(heading || this.heading); // 0度转弧度
    const p = Cesium.Math.toRadians(pitch || this.pitch);
    const r = Cesium.Math.toRadians(roll || this.roll);
    const hpr = new Cesium.HeadingPitchRoll(h, p, r);
    const orientation = Cesium.Transforms.headingPitchRollQuaternion(position || this.position, hpr);
    this.orientation = orientation;
    return orientation;
  }
}

// // 使用示例
// const model = new Model(
//   "https://example.com/path/to/your/model.glb",
//   Cesium.Cartesian3.fromDegrees(118.14112, 24.497, 500)
// );

// // 修改位置
// model.setPosition(Cesium.Cartesian3.fromDegrees(118.14112, 24.497, 600));

// // 修改高度
// model.setHeight(550);

// // 修改朝向
// model.setHeading(45); // 设置航向角为45度
// model.setPitch(30); // 设置俯仰角为30度
// model.setRoll(15); // 设置横滚角为15度

// // 修改缩放比例
// model.setScale(1.5);

// // 设置是否跟随
// model.setFollow(true);

// // 缩放到模型位置
// model.zoomToModel();
