# 项目介绍

基于四信云项目 vue2 升级的 `Vue3` 版本后台管理框架，使用 Vue3、Vite4、Pinia、Element Plus 当前主流技术栈开发。

## 项目特色

- 基于 `vue-element-admin` 升级的 `Vue3` 版本，主流技术栈，无过度自定义封装，极易上手，减少学习成本；
- 系统功能：用户、角色、菜单、系统管理；
- 基础设施：动态路由，按钮权限，常用组件封装。
- 支持国际化只需添加国际化的文案即可
- 支持页签模式和面包屑模式切换(src/settings.js 内配置 tagsView)
- 支持多级路由

## 技术栈

| 技术栈       | 描述                                   | 官网                                   |
| ------------ | -------------------------------------- | -------------------------------------- |
| Vue3         | 渐进式 JavaScript 框架                 | <https://v3.cn.vuejs.org/>             |
| Vite         | 前端开发与构建工具                     | <https://cn.vitejs.dev/>               |
| Element Plus | 基于 Vue 3，面向设计师和开发者的组件库 | <https://element-plus.gitee.io/zh-CN/> |
| Pinia        | 新一代状态管理工具                     | <https://pinia.vuejs.org/>             |
| Vue Router   | Vue.js 的官方路由                      | <https://router.vuejs.org/zh/>         |

## 项目地址

|                    | SVN                       | GitLab                    |
| ------------------ | ------------------------- | ------------------------- |
| vue3-element-admin | [vue3-element-admin](xxx) | [vue3-element-admin](xxx) |

## 接口地址
  
``` shell 
  修改 resoucre/config.json 文件  
  "VITE_APP_BASE_URL测试": "http://**************:24177/serv/",
  "VITE_APP_BASE_URL敬团": "http://*************:6789/",
  "VITE_APP_BASE_URL育鑫": "http://192.168.15.64:24437/",
  "VITE_APP_BASE_URL天津": "http://117.131.245.106:8086/serv",
  "VITE_APP_BASE_URL门头沟": "http://10.88.188.216/serv"
```

## 环境要求

- Node 环境

  版本：16+

- 开发工具

  VSCode

- 必装插件

  - Vue Language Features (Volar)

- 环境配置文件(在项目根目录下)

  - .env.development
  - .env.production

## 项目启动

```bash

# 安装依赖
yarn

# 项目运行
yarn dev

# 项目打包
yarn build:prod

```

## 代码提交 commit 规范

| 类型     | 描述                     |
| -------- | ------------------------ |
| feat     | 新功能（feature）        |
| fix      | 修补 bug                 |
| docs     | 文档（documentation）    |
| style    | 格式方面的优化           |
| refactor | 重构                     |
| test     | 测试                     |
| chore    | 构建过程或辅助工具的变动 |

## git 建议

建议项目使用 git init 命令初始化项目，为项目赋予 git 的生命周期配合各类自动化脚本实现项目自动化的校检，无需提交 git remote 本地就是使用一下 git 的生命周期

## 代码校检

- `prettier` 格式化
  - 基于 prettier 提供的基准和 eslint 的标准进行格式化统一代码风格
- `eslint` 代码规范
  - 基于 vue3-essential 和 recommended 两个标准进行代码规范校检

<span style="color:red;">基于 git 生命周期下代码未通过 eslint 校检是无法提交的</span>

## 项目样式

- 此项目已经接入 UI 的规范标准，开发人员使用时只需根据具体的设计稿配色 界面结构 简单更改位于 styles 文件夹下的 variables.scss 加快界面的板块样式开发
- 整个样式整体风格可以使用这样文件夹结构组织起来![image.png](https://s2.loli.net/2024/01/05/uHL79rokb6Gwlsy.png) font 文件夹放项目使用到的字体 iconfont 放项目使用到的图标 index.scss 放项目使用的样式表

## 项目使用说明

1. 使用了 vite 打包项目，接入中间件自动引入预先声明的组件。所以在 src/components 下的组件可以直接在项目内使用，无需再次 import 声明
2. 使用到<code>element-plus vue @vueuse/core</code>这些依赖包时无需在项目内再次 import 声明直接使用即可，使用时会自动导入
3. 项目使用环境变量配置，本地运行时 api 地址通过环境变量注入![image.png](https://s2.loli.net/2024/01/05/cl2izGxQuLfsr7a.png)






