<template>
  <div>
  <div class="width100 flex-column flex-justify-start flex-align-start" style="background-color: white;">

    <p class="fz16 ml10 mt15 mb10 color-text-title color-font-bold" style="color: #939393">
      启用后，照片和视频将自动上传到该服务器
    </p>
    <div
      class="flex-row flex-align-center mt20"
      style="width: 100%;"
    >
      <p class="ml10 mb0 fz16" style="margin-right: 73vw;">自动照片上传</p> 
      <el-switch
        v-model="enablePhotoUpload"
        @change="onPhotoUpload"
      ></el-switch>
    </div>
    <div
      class="flex-row flex-align-center flex-justify-between"
      style="width: 100%"
    >
      <el-radio-group
        class="mt10 ml20"
        v-if="enablePhotoUpload === true"
        v-model="photoType"
        @change="onPhototype"
      >
        <el-radio :label="EPhotoType.Original">原始的照片</el-radio>
        <el-radio class="ml20" :label="EPhotoType.Preview">预览照片</el-radio>
      </el-radio-group>
    </div>
    <div class="ml10 mr10" style="width: 96%; margin-top: -10px;">
      <el-divider />
    </div>
    <div
      class="flex-row flex-align-center"
      style="width: 100%; margin-top: -10px;"
    >
      <p class="ml10 mb0 fz16" style="margin-right: 73vw;">视频自动上传</p>
      <el-switch
        @change="onVideoUpload"
        v-model="enableVideoUpload"
      ></el-switch>
    </div>
    <div class="ml10 mr10" style="width: 96%; margin-top: -10px;">
      <el-divider />
    </div>
    <div
      class="flex-row flex-align-center flex-justify-between mb15"
      style="width: 100%; margin-top: -10px;"
    >
      <p class="ml10 mb0 fz16 color-font-bold">
        双控模式下媒体资源上传路径
      </p>
      <el-radio-group
        class="mt0 mb0"
        v-model="uploadPath"
        @change="onUploadPath"
      >
        <el-radio-button :label="EDownloadOwner.Mine">我的</el-radio-button>
        <el-radio-button :label="EDownloadOwner.Others">其他</el-radio-button>
      </el-radio-group>
    </div>
  </div>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { onMounted, ref } from 'vue'
import apiPilot from '@/api/pilot-login/pilot-bridge';
import { EComponentName, EPhotoType, EDownloadOwner } from '@/utils/constants'

const enablePhotoUpload = ref(apiPilot.getAutoUploadPhoto())
const enableVideoUpload = ref(apiPilot.getAutoUploadVideo())
const photoType = ref(apiPilot.getUploadPhotoType())
const uploadPath = ref(apiPilot.getDownloadOwner())

const onPhotoUpload = () => {
  apiPilot.setAutoUploadPhoto(enablePhotoUpload.value)
}
const onVideoUpload = () => {
  apiPilot.setAutoUploadVideo(enableVideoUpload.value)
}
const onPhototype = () => {
  apiPilot.setUploadPhotoType(photoType.value)
}
const onUploadPath = () => {
  apiPilot.setDownloadOwner(uploadPath.value)
}
onMounted(() => {
  localStorage.setItem('isHome', 'false')
  apiPilot.onBackClickReg()
  console.error(apiPilot.getUploadPhotoType())
  console.error(apiPilot.getAutoUploadVideo())
})
</script>

<style lang="scss" scoped>
// @import '/@/styles/index.scss';
</style>
