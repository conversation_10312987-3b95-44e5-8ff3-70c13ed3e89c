<template>
  <div class="list-container">
    <div>
      <div class="title" style="display: flex; justify-content: space-between; align-items: center">
        <span>航点</span>
        <el-tooltip
          class="box-item"
          effect="dark"
          show-after="500"
          content="点击新增后再点击地图新增航点"
          raw-content="true"
          placement="right"
        >
          <el-button type="primary" style="border-radius: 15px" @click="setTool('add')">新增</el-button>
        </el-tooltip>
      </div>
    </div>

    <div class="point-wrapper">
      <div
        v-for="(point, i) in pointList"
        :key="point.uuid"
        class="point-item"
        :class="currentPointId === point.uuid ? 'point-item-active' : ''"
        @click="changeIndex(point)"
        @contextmenu.prevent="openActionMenu(i, null, DELETE_ACTION_TYPE.point, $event, point)"
      >
        <img v-if="point.warning" class="arrow-down" src="@/assets/plan/倒三角红.png" />
        <img v-else class="arrow-down" src="@/assets/plan/倒三角.png" />

        <!-- 索引编号 -->
        <span class="point-index">{{ i + 1 }}</span>
        <!-- 动作组 列表 -->
        <div class="action-view">
          <div
            v-for="action in point?.action"
            :key="action.actionUuid"
            :class="[
              action.warning ? 'action-item-warning' : 'action-item',
              currentPointId === point.uuid && currentActionId === action.actionUuid ? 'action-item-active' : ''
            ]"
            @click.stop="changeAction(point, action)"
            @contextmenu.prevent.stop="openActionMenu(i, action, DELETE_ACTION_TYPE.action, $event, point)"
          >
            <!--展示对应图标 鼠标滑过提示 -->
            <el-tooltip
              v-if="action.warning"
              class="box-item"
              effect="dark"
              :content="action?.warningMsg"
              placement="top-start"
            >
              <img :src="action.url" width="24" height="24" />
            </el-tooltip>
            <el-tooltip
              v-else
              class="box-item"
              effect="dark"
              :content="getTooltipContent(action)"
              placement="top-start"
            >
              <img :src="action.url" width="24" height="24" />
            </el-tooltip>
          </div>
        </div>
      </div>
      <!-- 操作菜单 -->
      <ul v-show="actionMenuVisible" class="action-menu" :style="{ left: left + 'px', top: top + 'px' }">
        <li
          class="color-white"
          v-if="deleteType === DELETE_ACTION_TYPE.action"
          @click="deleteActionOrPoint(selectedAction)"
        >
          删除动作
        </li>
        <li
          class="color-white"
          v-if="deleteType === DELETE_ACTION_TYPE.point"
          @click="deleteActionOrPoint(selectedAction)"
        >
          删除航点
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WayPointList'
};
</script>

<script setup>
import * as Cesium from 'cesium';
import { getCurrentInstance, onUnmounted, ref } from 'vue';
import { onMounted } from 'vue';
import { useWayPointStore } from '@/store/modules/wayPointInfo.js';
import { storeToRefs } from 'pinia';
import { ACTION_ACTUATOR_FUNC_ICON, ACTION_ACTUATOR_FUNC_NAME, DELETE_ACTION_TYPE } from '@/utils/constants';
import {
  getActionActuatorFuncParam,
  setCurrentPlaceMark,
  getActionIndex,
  setActionComponentVisible
} from '../newplan/kmz/hocks/modules/waylineshandle';
import {
  clearFrustum,
  createFrustum,
  getFrustum,
  calculateFOV,
  updateFrustumWithActionValue
} from '../newplan/kmz/hocks/modules/actionFrustumHandle';
import { getPrevHeadingPitchRollZoom } from '../newplan/kmz/hocks/modules/actionHandle';
import { ACTION_ACTUATOR_FUNC } from '@/utils/constants';
import { deleteActionFromGroup, deletePlaceMark } from '../newplan/kmz/hocks/modules/waylineshandle';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getActionInfo, getFrustumObjectByActionUuid, updateAllFrustum } from '../newplan/kmz/hocks';
import { useEditTrackerStore } from '@/store/modules/dataEditTracker.js';
const editTrackerStore = useEditTrackerStore();
const wayPointStore = useWayPointStore();
const { currentPointId, currentActionId, pointList = [] } = storeToRefs(wayPointStore);
const deleteType = ref(DELETE_ACTION_TYPE.point); // 删除类型
const { proxy } = getCurrentInstance();

let plan; //航线计划对象
const props = defineProps({
  selectPlanPoint: {
    type: Function,
    default: () => {}
  }
});
function getTooltipContent(action) {
  const { title = null } = getActionInfo(action.actionTriggerType, action.actionActuatorFunc);
  return title || ACTION_ACTUATOR_FUNC_NAME[action?.actionActuatorFunc] || '';
}
onMounted(() => {
  // 清空航点和动作
  wayPointStore.clearAll();
});

onUnmounted(() => {
  clearFrustum();
});

/*******tmj 20240420********/
/**
 * 赋值航线计划对象
 * @param {*} planObj
 */
function setPlan(planObj) {
  plan = planObj;
}

/*******tmj 20240422********/
/**
 * 设置当前的操作工具
 * @param {*} tool
 */
function setTool(tool) {
  if (tool == 'add') {
    if (plan.flyStartPoint != null) {
      plan.setTool(tool);
    } else {
      ElMessage.error('请先设置起飞点');
    }
  } else {
    plan.setTool(tool);
  }
}

function handleOpen() {}

/**
 * 选中不同航点
 * @param {*} point
 */
function changeIndex(point) {
  // 设置选中的点位当前点
  selectWayPointIndex(point);
  const pointJson = selectMapPoint(point);
  setActionComponentVisible();
  // 判断动作数组是否为空
  if (point.action && point.action.length > 0) {
    const { action = null, actionUuid = null } = point.action[0];
    const { currentActionId, currentPointId } = wayPointStore.getCurrentPointAndAction();
    // 如果不在同一行
    if (currentPointId !== point.uuid) {
      // 获取第一个动作
      let firstAction = action;
      if (!action) {
        const { action: curAction = null } = getActionIndex({ actionUuid: actionUuid });
        firstAction = curAction;
      }
      const { lon = null, lat = null, height = null, UAVHPR: hpr = null } = pointJson;
      let frustun = getFrustum('action');
      if (frustun) {
        const op = frustun?.getOptions() ?? {};
        const options = {
          ...op,
          position: [lon, lat, height]
        };
        frustun.update(options);
      }
    } else {
      if (currentActionId === actionUuid) {
        const { lon = null, lat = null, height = null } = pointJson;
        let options = null;
        // 获取action对应的视锥体参数
        const { frustumOptions } = getFrustumObjectByActionUuid(actionUuid);
        if (frustumOptions) {
          options = {
            ...frustumOptions,
            position: [lon, lat, height]
          };
        } else {
          // 如果当前动作没有被记录那么就按照相机的视锥体设置
          let frustun = getFrustum('camera');
          if (frustun) {
            options = {
              ...frustun.getOptions(),
              position: [lon, lat, height]
            };
          }
        }
        // 获得第一个点的动作
        let prevHeadingPitchRollZoom = getPrevHeadingPitchRollZoom({ actionUuid });
        options = {
          ...options,
          ...prevHeadingPitchRollZoom
        };
        options && updateAllFrustum(options);
      }
    }
  } else {
    clearFrustum();
    wayPointStore.setCurrentActionNull();
  }
}

/**
 * 设置当前选中航点
 * @param {*} point
 */
function selectWayPointIndex(point) {
  wayPointStore.setCurrentPoint(point);
  setCurrentPlaceMark(point);
}

function selectMapPoint(point) {
  if (!plan || !point) {
    return null;
  }
  let index = -1;
  for (let i = 0; i < pointList.value.length; i++) {
    if (pointList.value[i].uuid == point.uuid) {
      index = i;
      break;
    }
  }
  //选中地图上的航点
  const json = plan.selectPlanPoint(index, true);
  if (json) {
    props.selectPlanPoint(json);
  }
  return json || null;
}

/**
 * 点击动作对象
 * @param {*} point
 * @param {*} actionObj 非动作对象 ，是用于渲染页面的动作信息
 */
function changeAction(point, actionObj) {
  if (!actionObj.action) {
    console.log('未正确获取动作及动作组信息');
    return;
  }

  try {
    // 设置选中的点位当前点
    selectWayPointIndex(point);
    // 1 获取动作参数、设置动作参数
    const actionInfo = getActionActuatorFuncParam({
      point,
      action: actionObj
    });
    if (!actionInfo) {
      console.log('未正确获取动作及动作组信息');
      return;
    }
    window.$bus.emit('setAction', {
      key: actionObj.actionActuatorFunc,
      title: ACTION_ACTUATOR_FUNC_NAME[actionObj.actionActuatorFunc],
      url: ACTION_ACTUATOR_FUNC_ICON[actionObj.actionActuatorFunc],
      actionUuid: actionObj.actionUuid,
      actionFuncParam: actionInfo.wpml_actionActuatorFuncParam || null,
      action: actionInfo.action || actionObj.action,
      actionGroupUuid: actionObj.actionGroupUuid,
      actionGroup: actionInfo?.actionGroup || null
    });

    // 2 设置当前动作
    wayPointStore.setCurrentAction(actionObj.actionUuid);
    // 3 设置当前动作的视锥体
    const pointJson = selectMapPoint(point);
    if (point.action && point.action.length > 0) {
      // 获取位置
      const { lon = null, lat = null, height = null } = pointJson;
      // 获取action对应的视锥体参数
      let cameraFrustun = getFrustum('camera') ?? null;
      if (!cameraFrustun) {
        return;
      }
      // 获取前面所有点的相关参数
      let prevHeadingPitchRollZoom = getPrevHeadingPitchRollZoom({ actionUuid: actionObj.actionUuid });
      let actionFrustun = getFrustum('action') ?? null;
      if (!actionFrustun) {
        // 创建动作视锥体
        actionFrustun = createFrustum(
          {
            ...cameraFrustun.getOptions(),
            colorType: 'action'
          },
          'action'
        );
      }
      let curFrustumOption = actionFrustun.getOptions() ?? null;
      curFrustumOption = {
        ...curFrustumOption,
        position: [lon, lat, height]
      };

      if (actionObj?.action) {
        // 构建配置参数  这里heading roll 是进行初始配置参数
        curFrustumOption = {
          ...curFrustumOption,
          heading: 0,
          roll: -90,
          ...prevHeadingPitchRollZoom
        };
      }

      if (actionObj?.action.wpml_actionActuatorFunc === ACTION_ACTUATOR_FUNC.zoom) {
        let sensorSize = 22.5; //12.0823; //
        let focalLength = actionObj?.action.wpml_actionActuatorFuncParam.wpml_focalLength;
        let fovDeg = calculateFOV(focalLength, sensorSize);
        const options = {
          action: actionObj?.action,
          actionUuid: actionObj?.action.uuid,
          type: ACTION_ACTUATOR_FUNC.zoom,
          value: fovDeg,
          zoom: Number(focalLength / 24)
        };
        updateFrustumWithActionValue(options);
      } else {
        const options = {
          action: actionObj?.action,
          actionUuid: actionObj?.action.uuid,
          type: ACTION_ACTUATOR_FUNC.zoom,
          value: prevHeadingPitchRollZoom.fov,
          zoom: Number(prevHeadingPitchRollZoom.focalLength / 24)
        };
        updateFrustumWithActionValue(options);
      }
      updateAllFrustum(curFrustumOption);
    }
  } catch (error) {
    console.log('error', error);
  }
}

const left = ref(0);
const top = ref(0);
const selectedAction = ref({});
const actionMenuVisible = ref(false); // 标签操作菜单显示状态
watch(actionMenuVisible, value => {
  if (value) {
    document.body.addEventListener('click', closeActionMenu);
  } else {
    document.body.removeEventListener('click', closeActionMenu);
  }
});

/**
 * 在指定位置打开标签菜单。
 *
 * @param {object} action - 为其打开菜单的标签。
 * @param {Event} e - 单击事件。
 */
function openActionMenu(pointIndex, action, type, e, point) {
  setCurrentPlaceMark(point);
  // 动作存在则属于移除动作
  if (!action) {
    // 移除点
    wayPointStore.setCurrentPoint(point, false);
    wayPointStore.setCurrentAction(null);
  }
  deleteType.value = type;
  const offsetLeft = proxy?.$el.getBoundingClientRect().left; // container margin left
  const offsetTop = proxy?.$el.getBoundingClientRect().top; // container margin left
  left.value = e.clientX - offsetLeft;
  top.value = e.clientY - offsetTop + 170; // 这里需要进行调试 75 调整到合适位置
  // left.value = e.clientX;
  // top.value = e.clientY; // 这里需要进行调试 75 调整到合适位置
  actionMenuVisible.value = true;
  selectedAction.value = { pointIndex, action, point };
  if (type === DELETE_ACTION_TYPE.action) {
    e.stopPropagation();
  }
}

/**
 * 关闭标签菜单。
 */
function closeActionMenu() {
  actionMenuVisible.value = false;
}

/**
 * 删除动作 或者 删除点
 * @param {*} select
 */
function deleteActionOrPoint(select) {
  if (deleteType.value === DELETE_ACTION_TYPE.point) {
    ElMessageBox.confirm('确定删除此航点?', '警告', {
      autofocus: false,
      closeOnClickModal: false,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        editTrackerStore.dataTracker.markAsModified();
        /*******tmj 20240423********/
        if (plan) {
          //删除地图上的航点
          plan.deletePoint(select.pointIndex);
          //说明删除的是最后一个航点，则选中前一个航点
          if (select.pointIndex + 1 == plan.planPointJson.length) plan.selectPlanPoint(select.pointIndex - 1);
          else plan.selectPlanPoint(select.pointIndex);
        }
        wayPointStore.deletePoint(select);
        // select = {
        //   pointId: 1714127072411,
        //   index: 1,
        //   placemarkIndex: 1,
        //   longitude: '118.1288885968773',
        //   latitude: '24.494915595177083',
        //   actionGroup: [],
        //   action: []
        // };
        // 移除placemark 移除航点及动作组和动作
        // 会对各uuid进行重新计算？
        deletePlaceMark(select);
      })
      .catch(() => {});
  } else if (deleteType.value === DELETE_ACTION_TYPE.action) {
    // 移除动作操作
    deleteActionFromGroup(select);
    wayPointStore.deleteAction(select);
    setActionComponentVisible();
    editTrackerStore.dataTracker.markAsModified();
  }
}

defineExpose({ handleOpen, setPlan });

onMounted(() => {
  setTimeout(() => {
    editTrackerStore.dataTracker.reset();
  }, 500);
});
</script>

<style lang="scss" scoped>
@import '../../../styles/common/global.scss';

.demo {
  max-height: 200px;
  overflow-y: scroll;
  user-select: auto;
}
.list-container {
  height: 100%;
  overflow: hidden;
  .title {
    padding: 10px;
    color: #fff;
    border-bottom: 1px solid #223143;
  }

  .point-wrapper {
    color: #fff;
    overflow: auto;
    max-height: calc(100% - 60px);
    .point-item {
      cursor: pointer;
      padding: 0 20px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .arrow-down {
        width: 16px;
        height: 16px;
        margin-right: 5px;
      }

      .point-index {
        font-size: 14px;
        color: #fff;
        width: 25px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .action-view {
        flex: 1;
        border-bottom: 1px solid #2c2c2c;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: flex-start;
        align-items: center;
        margin-left: 10px;
        min-height: 40px;
        color: #fff;
        .action-item {
          position: relative;
          display: inline-flex;
          align-items: center;
          padding: 6px;
          border-radius: 2px;
          margin: 3px; /* 在元素下方添加间距 */
          margin-right: 4px;
          cursor: pointer;
          img {
            width: 16px;
            height: 16px;
          }
        }
        .action-item-active {
          background: #2d8cf0 !important;
        }
        .action-item:hover {
          background: rgba($color: #2d8cf0, $alpha: 0.75);
        }

        .action-item-warning {
          position: relative;
          display: inline-flex;
          margin-right: 4px;
          align-items: center;
          padding: 6px;
          border-radius: 2px;
          margin: 3px; /* 在元素下方添加间距 */
          cursor: pointer;
          img {
            width: 16px;
            height: 16px;
          }
        }
        .action-item-warning::before {
          content: '';
          position: absolute;
          top: 0px;
          right: 0;
          border-style: solid;
          border-width: 0 10px 10px 0;
          border-color: transparent rgb(243, 123, 11) transparent transparent; /* 将 #f00 替换为所需的颜色 */
          animation: blink 1s infinite;
        }
        .action-item-warning:hover {
          background: rgba($color: #2d8cf0, $alpha: 0.75);
        }

        @keyframes blink {
          0% {
            opacity: 1;
          }
          50% {
            opacity: 0.7;
          }
          100% {
            opacity: 1;
          }
        }
      }
    }

    .point-item-active {
      background-color: #3c3c3c;
    }
  }
}

.action-menu {
  position: absolute;
  z-index: 99;
  font-size: 12px;
  background: #3c3c3c;
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);

  li {
    padding: 8px 16px;
    cursor: pointer;

    &:hover {
      background: rgba($color: #3c3c3c, $alpha: 0.75);
    }
  }
}

.color-white {
  color: rgb(196, 196, 196);
  &:hover {
    color: white;
  }
}

/* 滚动条样式 */
/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 8px !important;
}
/* 滚动槽 */
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(190, 92, 92, 0.2);
  color: #175192;
  border-radius: 2px;
}

// 上箭头
// ::-webkit-scrollbar-button:start {
//   // background-color: slateblue;
//   background-image: url('../../../assets/up-arrow.png');
//   background-size: 14px !important;
//   background-repeat: no-repeat;
//   background-position: center center;
// }
// ::-webkit-scrollbar-button:end {
//   background-image: url('../../../assets/down-arrow.png');
//   background-repeat: no-repeat;
//   background-size: 14px !important;
//   background-position: center center;
// }
/* 滚动条滑块（里面小方块） */
::-webkit-scrollbar-thumb {
  border-radius: 2px;
  width: 12px !important;
  background: #175192 !important;
  -webkit-box-shadow: inset 0 0 6px #175192 !important;
}
</style>
