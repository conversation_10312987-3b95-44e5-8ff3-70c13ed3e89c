export default {
  data() {
    return {
      // 筛选条件
      screen: this.initScreen(),
      // 当前搜索条件 点击查询后生效
      search_screen: this.initScreen(),

      // 总条数
      total: 0,
      // table加载状态
      table_loading: false,
      // 搜索结果文本
      page_message: '',
      // 列表数据
      table_page: [],
      tableHeight: '450'
    };
  },

  methods: {
    // 搜索
    search() {
      this.$refs.table?.clearSort();
      const screen = this.deepClone(this.screen);
      screen.pageSize = this.search_screen.pageSize;
      this.search_screen = screen;
      this.getTablePage();
    },
    // 重置
    reset() {
      this.$refs.table?.clearSort();
      this.screen = this.initScreen();
      this.search_screen = this.initScreen();
      this.getTablePage();
    },
    handleSort(data) {
      const sort_data = this.deepClone(this.sort_data);
      sort_data[data.prop] = data.order;
      Object.assign(this.search_screen, sort_data);
      this.getTablePage();
    },
    handleSizeChange() {
      this.search_screen.pageNum = 1;
      this.getTablePage();
    },
    // 动态设置高度
    setPageTableHeight() {
      // 滚动条偏移量  3
      // padding (上下 ) 24
      // 分页高度 72(paginationEl)
      let pageTableHeight = 500;
      nextTick(() => {
        const winHeight = window.innerHeight;
        const pageTableEl = document.querySelector('.el-table');
        const paginationEl = document.querySelector('.el-pagination');
        if (pageTableEl) {
          const pageTableTop = pageTableEl.getBoundingClientRect().top;
          const paginationHeight = paginationEl ? 72 : 0;
          pageTableHeight =
            winHeight - pageTableTop - paginationHeight - 3 - 24;
          // const tableEl = pageTableEl.querySelector('.ff-table__body-wrapper')
          pageTableEl.style.height = pageTableHeight + 'px';
          // tableEl.style.height = pageTableHeight - 55 + 'px'
          this.tableHeight = pageTableHeight - 55 + 'px';
        }
      });
    }
  }
};
