<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <span>GB28181上级级联列表</span>
      </div>
      <div class="search-btn">
        <el-button type="primary" @click="onAdd()">添加上级平台</el-button>
      </div>
    </div>
    <el-card shadow="never">
      <div style="margin: 15px 0"></div>
      <el-table :data="filteredData" style="width: 100%; margin-top: 10px">
        <el-table-column prop="name" label="名称" min-width="80" />
        <el-table-column prop="superCode" label="上级编码" min-width="160" />
        <el-table-column prop="superIp" label="上级IP地址" min-width="120" />
        <el-table-column prop="superPort" label="上级端口" min-width="80" />
        <el-table-column prop="localCode" label="本级编码" min-width="160" />
        <el-table-column prop="localIp" label="本级IP地址" min-width="120" />
        <el-table-column prop="localPort" label="本级端口" min-width="80" />
        <el-table-column prop="username" label="用户名" min-width="80" />
        <el-table-column prop="password" label="密码" min-width="80">
          <template #default="scope"> *** </template>
        </el-table-column>
        <el-table-column prop="enabled" label="是否启用" min-width="80" />
        <el-table-column label="操作" min-width="100">
          <template #default="scope">
            <el-link type="primary" @click="onEdit(scope.row)">编辑</el-link>
            <el-link type="danger" style="margin-left: 8px" @click="onDelete(scope.row)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-content">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :background="true"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="filteredData.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    <EditDialog
      v-model:visible="editDialog.visible"
      :title="editDialog.title"
      :form-data="formData"
      @submit="resetQuery"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import EditDialog from './EditDialog.vue';
const searchOrg = ref('');
const page = ref({ current: 1, size: 10 });

const editDialog = reactive({
  visible: false
});
const tableData = ref([
  {
    name: '海康',
    superCode: '34020000002000000001',
    superIp: '127.0.0.1',
    superPort: '8082',
    localCode: '34020000002000000001',
    localIp: '************',
    localPort: '33315',
    username: '-',
    password: '***',
    enabled: '是', 
  },
  {
    name: '宇视',
    superCode: '34020000002000000001',
    superIp: '127.0.0.1',
    superPort: '8081',
    localCode: '34020000002000000001',
    localIp: '************',
    localPort: '8081',
    username: '-',
    password: '***',
    enabled: '是', 
  }
]);

const total = ref(0);
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  queryType: 2,
  keyWord: ''
});

const filteredData = computed(() => {
  let data = tableData.value;
  if (searchOrg.value) {
    data = data.filter(item => item.org.includes(searchOrg.value));
  }
  // 分页
  const start = (page.value.current - 1) * page.value.size;
  const end = start + page.value.size;
  return data.slice(start, end);
});

function onAdd() {
  openEditDialog();
}
function onEdit(row) {
  // 占位：编辑
  alert('编辑: ' + row.name);
}
function onDelete(row) {
  // 占位：删除
  alert('删除: ' + row.name);
}
function handleSizeChange(val) {
  page.value.size = val;
  page.value.current = 1;
}
function handleCurrentChange(val) {
  page.value.current = val;
}

/**
 * 打开表单弹窗
 */
 function openEditDialog(row) {
  editDialog.visible = true;
  Object.keys(formData).map(key => {
    delete formData[key];
  });
  if (row) {
    editDialog.title = '编辑GB28181上级级联信息';
    Object.assign(formData, { ...row });
  } else {
    editDialog.title = '新增GB28181上级级联信息';
    nextTick(() => {
      editDialogRef.value.setDefaultValue();
    });
  }
}
</script>

<style lang="scss" scoped>
.pagination-content {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;

  &.hidden {
    display: none;
  }
}
.flex-center {
  display: flex;
  align-items: center;
  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #4caf51;
  }
  .unstatus {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: red;
  }
}
.search {
  display: flex;
  align-items: center;
  padding: 24px;
  height: 64px;
  .search-form {
    flex: 1;
    color: #fff;
  }
}
</style>
