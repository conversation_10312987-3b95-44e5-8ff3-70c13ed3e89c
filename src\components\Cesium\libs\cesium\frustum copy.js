import * as Cesium from 'cesium';
import { toCartesian3 } from './common';
class CreateFrustum {
  constructor(options) {
    this.position = toCartesian3(options.position);
    this.fov = options.fov || 30;
    this.near = options.near || 10;
    this.far = options.far || 100;
    this.aspectRatio = options.aspectRatio || 1;
    this.orientation = options.orientation;
    this.viewer = options.viewer;
    this.eyeViewer = options.eyeViewer;
    this.colorType = options.colorType || 'camera';
    this.add();
  }

  colorConfig = {
    camera: {
      outlineColor: Cesium.Color.fromAlpha(Cesium.Color.GREEN, 1),
      frustumColor: Cesium.Color.fromAlpha(Cesium.Color.LIMEGREEN, 0.2)
    },
    action: {
      outlineColor: Cesium.Color.fromAlpha(Cesium.Color.BLUE, 1),
      frustumColor: Cesium.Color.fromAlpha(Cesium.Color.ORANGE, 0.2)
    }
  };

  // 更新视锥体的姿态
  update(options) {
    const { position, fov, near, far, orientation } = options;
    this.position = toCartesian3(position);
    this.fov = fov;
    this.far = far;
    this.near = near;
    this.orientation = orientation;
    this.add();
  }

  // 创建视锥体和轮廓线
  add() {
    this.clear();
    this.setOrientation();
    this.addFrustum();
    this.addOutline();
  }

  // 清除视锥体和轮廓线
  clear() {
    this.clearFrustum();
    this.clearOutline();
  }

  // 清除视锥体
  clearFrustum() {
    if (this.frustumPrimitive) {
      this.viewer.scene.primitives.remove(this.frustumPrimitive);
      this.frustumPrimitive = null;
    }
  }

  // 清除轮廓线
  clearOutline() {
    if (this.outlinePrimitive) {
      this.viewer.scene.primitives.remove(this.outlinePrimitive);
      this.outlinePrimitive = null;
    }
  }

  // 创建视锥体
  addFrustum() {
    let camera = new Cesium.Camera(this.eyeViewer.scene);
    camera.frustum.fov = Cesium.Math.toRadians(this.fov);
    camera.frustum.near = this.near;
    camera.frustum.far = this.far;
    let geometry = new Cesium.FrustumGeometry({
      frustum: camera.frustum,
      origin: toCartesian3(this.position),
      orientation: this.orientation,
      vertexFormat: Cesium.VertexFormat.POSITION_ONLY
    });
    let instance = new Cesium.GeometryInstance({
      geometry: geometry,
      attributes: {
        color: Cesium.ColorGeometryInstanceAttribute.fromColor(this.colorConfig[this.colorType].frustumColor)
      }
    });
    let primitive = new Cesium.Primitive({
      geometryInstances: instance,
      appearance: new Cesium.PerInstanceColorAppearance({
        closed: true,
        flat: true
      }),
      asynchronous: false
    });
    this.frustumPrimitive = this.viewer.scene.primitives.add(primitive);
  }

  // 创建轮廓线
  addOutline() {
    let camera = new Cesium.Camera(this.eyeViewer.scene);
    camera.frustum.fov = Cesium.Math.toRadians(this.fov);
    camera.frustum.near = this.near;
    camera.frustum.far = this.far;
    //this.orientation 这里已经井盖计算
    let geometry = new Cesium.FrustumOutlineGeometry({
      frustum: camera.frustum,
      origin: toCartesian3(this.position),
      orientation: this.orientation,
      vertexFormat: Cesium.VertexFormat.POSITION_ONLY
    });
    let instance = new Cesium.GeometryInstance({
      geometry: geometry,
      attributes: {
        color: Cesium.ColorGeometryInstanceAttribute.fromColor(this.colorConfig[this.colorType].outlineColor)
      }
    });
    let primitive = new Cesium.Primitive({
      geometryInstances: instance,
      appearance: new Cesium.PerInstanceColorAppearance({
        closed: true,
        flat: true
      }),
      asynchronous: false
    });
    this.outlinePrimitive = this.viewer.scene.primitives.add(primitive);
  }
  setOrientation() {
    let camera = new Cesium.Camera(this.eyeViewer.scene);
    camera.frustum.fov = Cesium.Math.toRadians(this.fov);
    camera.frustum.near = this.near;
    camera.frustum.far = this.far;
    let scratchRight = new Cesium.Cartesian3();
    let scratchRotation = new Cesium.Matrix3();
    var scratchOrientation = new Cesium.Quaternion();

    let directions = this.eyeViewer.camera.directionWC;
    let up = this.eyeViewer.camera.upWC;
    let right = this.eyeViewer.camera.rightWC;
    right = Cesium.Cartesian3.negate(right, scratchRight);
    let rotation = scratchRotation;
    Cesium.Matrix3.setColumn(rotation, 0, right, rotation);
    Cesium.Matrix3.setColumn(rotation, 1, up, rotation);
    Cesium.Matrix3.setColumn(rotation, 2, directions, rotation);
    //计算视锥姿态
    let orientation = Cesium.Quaternion.fromRotationMatrix(rotation, scratchOrientation);
    let normalizedOrientation = new Cesium.Quaternion();
    Cesium.Quaternion.normalize(orientation, normalizedOrientation);
    if (this.orientation) {
      this.orientation = Cesium.Quaternion.multiply(normalizedOrientation, this.orientation, new Cesium.Quaternion());
    }
    // this.orientation = orientation;
  }
  setOrientation2() {}
}

export default CreateFrustum;
