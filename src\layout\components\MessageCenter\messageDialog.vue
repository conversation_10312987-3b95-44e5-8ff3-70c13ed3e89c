<!-- 消息中心 -->
<template>
  <el-dialog
    v-model="dialogVisible"
    v-if="dialogVisible"
    width="840px"
    :append-to-body="true"
    :close-on-click-modal="false"
    :before-close="dialogCancel"
    :show-close="false"
    class="special-custom-dialog"
    :title="$t('1_3_0.Message center')"
  >
    <div class="message-center">
      <div v-loading="showLoading" class="message-box">
        <div class="p-3">
          <div class="custom-btn-group">
            <div class="custom-active" />
            <div
              v-for="item in optionsList"
              :ref="`custom-btn-${item.value}`"
              :key="item.value"
              :class="[activeType === item.value ? 'active' : '']"
              class="custom-btn"
              @click="changeType(item.value)"
            >
              {{ item.label }}
              <span>
                <!-- ({{ item.messageCount }}) -->
                ({{ messageCount[item.countField] || 0 }})
              </span>
            </div>
          </div>
        </div>
        <component
          :is="componentName[activeName]"
          :ref="activeName"
          @changeCount="changeCount"
        />
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import i18n from '@/lang/index';
import useDialog from '@/hooks/useDialog';

import WarmMeassage from './model/warmMeassage.vue';
import VersionMeassage from './model/versionMeassage.vue';
import PlatformMessage from './model/platformMessage.vue';

const componentName = {
  PlatformMessage,
  WarmMeassage,
  VersionMeassage
  // ComAssignmentTable
};
const activeName = ref('WarmMeassage');
const messageCount = ref({});
const optionsList = [
  {
    label: i18n.global.t('page.dialog.alarmMessage'),
    value: 'WarmMeassage',
    countField: 'warnMessageCount'
  },
  {
    label: i18n.global.t('page.dialog.versionMessage'),
    value: 'VersionMeassage',
    countField: 'versionMessageCount'
  },
  {
    label: i18n.global.t('page.dialog.platformNotification'),
    value: 'PlatformMessage',
    countField: 'stationMessageCount'
  }
];
const activeType = ref('WarmMeassage');
const showLoading = ref(false);

const { dialogVisible, dialogOpen, dialogCancel } = useDialog();

const props = defineProps({
  getMessageCount: {
    type: Function,
    default: () => {}
  }
});
const handleOpen = () => {
  dialogOpen();
  // setActiveStyle();
};

const changeType = value => {
  activeType.value = value;
  activeName.value = value;
};

const changeCount = async () => {
  try {
    const countRes = await props.getMessageCount();
    messageCount.value = countRes;
  } catch (error) {
    console.log('error: ', error);
  }
};

defineExpose({
  handleOpen
});
</script>

<style lang="scss" scoped>
.message-center {
  .message-header {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #dcdfe6;
    padding: 20px;

    .header-title {
      font-size: 16px;
      color: #222222;
      line-height: 16px;
      font-weight: 600;
    }
  }

  :deep(.page-foot) {
    background: #f7f9fd;
    padding: 20px;
    color: #097efc;
  }
}

.custom-btn-group {
  position: relative;
  display: inline-flex;
  padding: 4px 6px;
  border-radius: 100px;
  font-weight: 500;
  background-color: #f0f0f0;
  line-height: 1;
  align-items: center;
  width: 100%;
  padding: 4px 4px 4px 4px;

  .custom-btn {
    z-index: 1;
    padding: 6px 12px;
    color: #444;
    font-size: 14px;
    width: 100%;
    text-align: center;
    cursor: pointer;

    &.active {
      color: #097efc;
      padding: 6px 12px;
      height: 26px;
      border-radius: 100px;
      background-color: #fff;
      transition: all linear 0.1s;
    }
  }

  .custom-btn-show {
    padding: 6px 20px;
    &.active {
      padding: 6px 20px;
    }
  }
  /*
  .custom-active {
    position: absolute;
    top: 8px;
    bottom: 8px;
    left: 4px;
    right: 4px;
    padding: 6px 12px;
    border-radius: 100px;
    background-color: #fff;
    transition: all linear 0.1s;
  } */
}
</style>
