<script setup>
import i18n from '@/lang';

import AvatarArea from './AvatarArea/index.vue';
// import LangSelect from './LangSelect/index.vue';
import Settings from './Settings/index.vue';
import MessageCenter from './MessageCenter/index.vue';

// import IconEpSunny from '~icons/ep/sunny';
// import IconEpMoon from '~icons/ep/moon';
// import { useAppStore } from '@/store/modules/app.js';
import { useSettingsStore } from '@/store/modules/settings';

// const appStore = useAppStore();
const settingsStore = useSettingsStore();
// const isDark = useDark();
// const toggleDark = () => useToggle(isDark);

const showTagsView = computed(() => settingsStore.tagsView);

// function toggleSideBar() {
//   appStore.toggleSidebar(true);
// }

const manualUrl = computed(() => {
  return i18n.global.locale.value === 'zh'
    ? 'https://k15ycvt17w.feishu.cn/docx/KdecdG0H8ocXk5xTydEc6kILnCW?from=from_copylink'
    : 'https://nabwsoo0bcz.feishu.cn/docx/Zz4VdePjboH8XZxnaDYcjG2bnJA';
});

const isProductionByEn = computed(() => {
  return import.meta.env.VITE_APP_NODE_ENV === 'productionByEn';
});
</script>

<template>
  <!-- 顶部导航栏 -->
  <div class="navbar">
    <!-- 左侧面包屑 -->
    <div class="flex">
      <!-- <hamburger
        :is-active="appStore.sidebar.opened"
        @toggleClick="toggleSideBar"
      /> -->
      <Breadcrumb v-if="!showTagsView" />
    </div>
    <!-- 右侧导航设置 -->
    <div class="flex items-center">
      <!-- <el-link
        v-if="!isProductionByEn"
        class="setting-container"
        :href="manualUrl"
        target="_blank"
        :underline="false"
      >
        <img
          src="@/assets/manual.png"
          class="manual-icon mx-3"
          :title="$t('1_3_0.User guide')"
        />
      </el-link>
      <MessageCenter /> -->
      <!-- <Settings /> -->
      <!-- <LangSelect /> -->
      <AvatarArea :show-dashboard="false" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.navbar {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 0 1px #0003;
  background: linear-gradient(135deg, #3f5bfa 0%, #097efc 100%);
  padding-right: 10px;
  :deep(.el-switch__core) {
    border: 1px solid #fff;
  }

  .setting-container {
    display: flex;
    align-items: center;
    color: #fff;
  }

  .manual-icon {
    width: 21px;
    height: 21px;
  }
}
</style>
