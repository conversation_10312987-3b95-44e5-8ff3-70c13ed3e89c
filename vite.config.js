import vue from '@vitejs/plugin-vue';

import { loadEnv, defineConfig } from 'vite';

import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';

import Icons from 'unplugin-icons/vite';
import IconsResolver from 'unplugin-icons/resolver';

import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import cesium from 'vite-plugin-cesium';
import UnoCSS from 'unocss/vite';

import path from 'path';
const pathSrc = path.resolve(__dirname, 'src');

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  return {
    base: './',
    resolve: {
      symlinks: false,
      alias: {
        '@': pathSrc,
        'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js',
        vue: path.resolve('./node_modules/vue')
      },
      extensions: ['.js', '.ts']
    },
    css: {
      // CSS 预处理器
      preprocessorOptions: {
        //define global scss variable
        scss: {
          javascriptEnabled: true,
          additionalData: `
            @use "@/styles/variables.scss" as *;
            @use "@/styles/element/index.scss" as *;
            @use '@/styles/ff-cloud/index.scss' as *;
          `
        }
      }
    },
    server: {
      host: '0.0.0.0',
      port: Number(env.VITE_APP_PORT),
      open: true, // 运行是否自动打开浏览器
      // 反向代理解决跨域

      proxy: {
        [env.VITE_APP_BASE_API]: {
          // 线上接口API地址

          // target: env.VITE_APP_BASE_URL,
          target: env.VITE_APP_BASE_URL,
          // target: 'http://**************:11322',

          // 本地接口API地址
          // target: 'http://localhost:8989',
          changeOrigin: true,
          rewrite: path => {
            return path.replace(new RegExp('^' + env.VITE_APP_BASE_API), '');
          }
          // bypass(req, res, option) {
          //   if (env.VITE_APP_NODE_ENV === 'development') {
          //     // @ts-ignore
          //     const proxyUrl =
          //       new URL(option.rewrite(req.url) || '', option.target)?.href ||
          //       '';
          //     console.log(
          //       '🚀 ~ file: vite.config.js:57 ~ bypass ~ proxyUrl:',
          //       proxyUrl
          //     );
          //     res.setHeader('x-res-proxyUrl', proxyUrl);
          //   }
          // }
        },
        '/socket': {
          // target: 'ws://*************:8999',
          // target: 'ws://**************:11322',
          target: 'ws://**************:5000',
          // target: 'http://**************:24109/',
          changeOrigin: true,
          rewrite: path => path.replace(/\/socket/, '')
        }
      },
      watch: {
        usePolling: true
      }
    },
    plugins: [
      vue(),
      cesium(),
      UnoCSS({
        /* options */
      }),
      AutoImport({
        // 自动导入 Vue 相关函数，如：ref, reactive, toRef 等
        imports: ['vue', '@vueuse/core'],
        eslintrc: {
          enabled: false, //  Default `false`
          filepath: './.eslintrc-auto-import.json', // Default `./.eslintrc-auto-import.json`
          globalsPropValue: true // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
        },
        resolvers: [
          // 自动导入 Element Plus 相关函数，如：ElMessage, ElMessageBox... (带样式)
          ElementPlusResolver({
            importStyle: 'sass'
          }),
          // 自动导入图标组件
          IconsResolver({})
        ],
        vueTemplate: true // 是否在 vue 模板中自动导入
      }),

      Components({
        resolvers: [
          // 自动注册图标组件
          IconsResolver({
            enabledCollections: ['ep'] //@iconify-json/ep 是 Element Plus 的图标库
          }),
          // 自动导入 Element Plus 组件
          ElementPlusResolver()
        ],
        dts: path.resolve(pathSrc, 'types', 'components.d.ts') //  自动导入组件类型声明文件位置，默认根目录; false 关闭自动生成
      }),

      Icons({
        // 自动安装图标库
        autoInstall: true
      }),

      createSvgIconsPlugin({
        // 指定需要缓存的图标文件夹
        iconDirs: [path.resolve(pathSrc, 'assets/icons')],
        // 指定symbolId格式
        symbolId: 'icon-[dir]-[name]'
      })
    ],
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        'axios',
        'element-plus/es/utils/index.mjs',
        'element-plus/es/components/form/style/css',
        'element-plus/es/components/form-item/style/css',
        'element-plus/es/components/button/style/css',
        'element-plus/es/components/input/style/css',
        'element-plus/es/components/input-number/style/css',
        'element-plus/es/components/switch/style/css',
        'element-plus/es/components/upload/style/css',
        'element-plus/es/components/menu/style/css',
        'element-plus/es/components/col/style/css',
        'element-plus/es/components/icon/style/css',
        'element-plus/es/components/row/style/css',
        'element-plus/es/components/tag/style/css',
        'element-plus/es/components/dialog/style/css',
        'element-plus/es/components/loading/style/css',
        'element-plus/es/components/radio/style/css',
        'element-plus/es/components/radio-group/style/css',
        'element-plus/es/components/popover/style/css',
        'element-plus/es/components/scrollbar/style/css',
        'element-plus/es/components/tooltip/style/css',
        'element-plus/es/components/dropdown/style/css',
        'element-plus/es/components/dropdown-menu/style/css',
        'element-plus/es/components/dropdown-item/style/css',
        'element-plus/es/components/sub-menu/style/css',
        'element-plus/es/components/menu-item/style/css',
        'element-plus/es/components/divider/style/css',
        'element-plus/es/components/card/style/css',
        'element-plus/es/components/link/style/css',
        'element-plus/es/components/breadcrumb/style/css',
        'element-plus/es/components/breadcrumb-item/style/css',
        'element-plus/es/components/table/style/css',
        'element-plus/es/components/tree-select/style/css',
        'element-plus/es/components/table-column/style/css',
        'element-plus/es/components/select/style/css',
        'element-plus/es/components/option/style/css',
        'element-plus/es/components/pagination/style/css',
        'element-plus/es/components/tree/style/css',
        'element-plus/es/components/alert/style/css',
        '@vueuse/core',

        'path-to-regexp',
        'echarts',
        '@wangeditor/editor',
        '@wangeditor/editor-for-vue',
        'vue-i18n'
      ]
    },
    build: {
      target: 'esnext' // target: ["chrome89", "edge89", "firefox89", "safari15"]
    }
  };
});
