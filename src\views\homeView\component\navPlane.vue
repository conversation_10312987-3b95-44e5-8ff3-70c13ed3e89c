<script>
export default { name: 'NavPlane' };
</script>

<script setup>
import { ref, defineExpose } from 'vue';
import { EModeCode } from '@/views/map/map-fly-manager/components/osdInfo';
import { getTerrainHeight } from '@/components/Cesium/libs/cesium/index';
import * as Cesium from 'cesium';
import { getCesiumEngineInstance } from '@/components/Cesium/libs/cesium';
const emit = defineEmits(['OneClickDelivery']);

const props = defineProps({
  // 是否是运载机
  isCarrier: {
    type: Boolean,
    default: false
  }
});

let dockInfo = reactive({
  nickname: '', //机场名
  dock_sn: '', //机场SN号
  dock_xyz: '', //机场XYZ
  device_nickname: '', //无人机名
  device_sn: '', //无人机SN号
  device_xyz: '', //无人机XYZ
  alarm_id: '', //警情ID
  flight_id: '', //飞行任务ID
  wayline_id: '' //航线文件ID
});

let osdInfo = reactive({
  mode_code: 14, // 状态
  capacity_percent: '--', //电量
  height: '--', //飞行高度
  horizontal_speed: '--', // 水平速度
  vertical_speed: '--', // 垂直速度
  gps_number: '--', //GPS数量
  rtk_number: '--', //RTK数量
  home_distance: '--', // 离机场距离
  transform: 'rotate(90deg)',
  gimbalPitchPercent: '50%',
  weight: '0' // 运载机载重量
});

function oneClickDelibery() {
  emit('OneClickDelivery');
}

// 枚举获取值
const getEnumKey = (enumObject, value) => {
  return Object.keys(enumObject).find(key => enumObject[key] === value);
};

/**
 * 设置组件数据
 * @param {*} options {donkInfo:{},osdInfo:{}}
 */
const setComponentData = options => {
  if (options && typeof options === 'object') {
    const nowNavInfo = options.navInfo;
    for (const key in dockInfo) {
      if (nowNavInfo.hasOwnProperty(key)) {
        dockInfo[key] = nowNavInfo[key];
      }
    }
    const str = '--';
    const nowOsdInfo = options.osdInfo;
    if (nowOsdInfo?.mode_code === 14) {
      osdInfo.mode_code = 14;
      osdInfo.capacity_percent = '--';
      osdInfo.height = '--';
      osdInfo.horizontal_speed = '--';
      osdInfo.vertical_speed = '--';
      osdInfo.gps_number = '--';
      osdInfo.rtk_number = '--';
      osdInfo.home_distance = '--';
      osdInfo.transform = 'rotate(90deg)';
      osdInfo.imbalPitchPercent = '50%';
      osdInfo.weight = '0';
      return;
    }
    if (nowOsdInfo !== null && nowOsdInfo !== undefined) {
      osdInfo.mode_code = nowOsdInfo?.mode_code;
      osdInfo.capacity_percent = nowOsdInfo?.battery.capacity_percent ?? str;
      osdInfo.height = nowOsdInfo?.height.toFixed(2) ?? str;
      osdInfo.horizontal_speed = nowOsdInfo?.horizontal_speed.toFixed(2) ?? str;
      osdInfo.vertical_speed = nowOsdInfo?.vertical_speed.toFixed(2) ?? str;
      osdInfo.gps_number = nowOsdInfo?.position_state.gps_number ?? str;
      osdInfo.rtk_number = nowOsdInfo?.position_state.rtk_number ?? str;
      osdInfo.home_distance = nowOsdInfo?.home_distance.toFixed(2) ?? str;

      // 偏航轴角度与真北角（经线）的角度，0到6点钟方向为正值，6到12点钟方向为负值。
      let degrees = -Number(nowOsdInfo.attitude_head.toFixed(2));
      osdInfo.heading = -degrees + '°';
      osdInfo.transform = 'rotate(' + degrees + 'deg)';

      // AGL 地面以上的高度（height-terrainheight）
      let navPosition = Cesium.Cartesian3.fromDegrees(nowOsdInfo.longitude, nowOsdInfo.latitude, 0);
      const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
      getTerrainHeight(flyView, navPosition).then(obj => {
        osdInfo.aglHeight = Number(nowOsdInfo.height - obj.terrainHeight).toFixed(2);
      });
      // 有weight则除以1000
      osdInfo.weight =
        nowOsdInfo.ext_profile?.hoist_detail?.weight !== undefined
          ? Number(nowOsdInfo.ext_profile.hoist_detail.weight) / 1000
          : str;
      // 没有拿到负载的OSD云台俯仰角
      // const nowGimbalPitch = nowOsdInfo.payloads[0]?.gimbalPitch ?? undefined;
      // if (nowGimbalPitch !== undefined) {
      //   osdInfo.gimbalPitch = nowGimbalPitch.toFixed(2) + '°';
      //   const pitchRatioTimes100 = (nowGimbalPitch / 90) * 100;
      //   let gimbalPitchPercent = nowGimbalPitch >= 0 ? 50 - pitchRatioTimes100 : Math.abs(pitchRatioTimes100);
      //   osdInfo.gimbalPitchPercent = `${gimbalPitchPercent.toFixed(2)}%`;
      // }
    }
  }
};
// 对外抛出方法
defineExpose({
  setComponentData
});

onMounted(() => {});
onUnmounted(() => {});
onBeforeUnmount(() => {});
</script>

<template>
  <div style="height: 100%">
    <div class="alarm-title flex">
      <div>
        <svg-icon
          icon-class="drone"
          style="width: 14px; height: 14px; margin-left: 5px; margin-top: 12px; margin-right: 8px"
        />
        <span class="nick"> {{ dockInfo.device_nickname }}</span>
      </div>
      <div class="status" :class="{ 'is-active': osdInfo.mode_code === 14 }">
        {{ getEnumKey(EModeCode, osdInfo.mode_code) }}
      </div>
    </div>
    <div class="info-container flex">
      <div class="info-left">
        <el-row class="c-row">
          <el-col :span="5" class="c-title">电量</el-col>
          <el-col :span="7" class="c-text">{{ osdInfo.capacity_percent + ' %' }} </el-col>
          <el-col :span="5" class="c-title">绝对高度</el-col>
          <el-col :span="7" class="c-text">{{ osdInfo.height + ' m' }}</el-col>
        </el-row>
        <el-row class="c-row">
          <el-col :span="5" class="c-title">水平速度</el-col>
          <el-col :span="7" class="c-text">{{ osdInfo.horizontal_speed + ' m/s' }}</el-col>
          <el-col :span="5" class="c-title">垂直速度</el-col>
          <el-col :span="7" class="c-text">{{ osdInfo.vertical_speed + ' m/s' }}</el-col>
        </el-row>
        <el-row class="c-row">
          <el-col :span="5" class="c-title">RTK</el-col>
          <el-col :span="7" class="c-text">{{ osdInfo.rtk_number }}</el-col>
          <el-col :span="5" class="c-title">卫星数量</el-col>
          <el-col :span="7" class="c-text">{{ osdInfo.gps_number }}</el-col>
        </el-row>
        <el-row class="c-row">
          <el-col :span="6" class="c-title" style="transform: translateX(-15px)">距离返航点</el-col>
          <el-col :span="6" class="c-text" style="transform: translateX(-15px)">{{
            osdInfo.home_distance + ' m'
          }}</el-col>
          <el-col :span="5" class="c-title" v-show="isCarrier">载重</el-col>
          <el-col :span="7" class="c-text" v-show="isCarrier">{{ osdInfo.weight + ' kg' }}</el-col>
        </el-row>
      </div>
      <div class="info-right">
        <!-- 指南针 -->
        <el-row class="c-row">
          <el-col :span="3"></el-col>
          <el-col :span="20">
            <div class="c-compass">
              <div class="attitude-view attitude-view-md compass-item">
                <div class="pitch-editor">
                  <span class="gimbal-pitch angle-number map-text-shadow">{{ osdInfo.gimbalPitch }}</span>
                  <div class="pitch-bar" style="background: currentcolor"></div>
                  <div class="marker" style="top: 37.5%"></div>
                  <div class="marker zero-marker"></div>
                  <div class="marker" style="top: 68.75%"></div>
                  <div class="marker" style="top: 87.5%"></div>
                  <div class="marker limit-marker" style="top: 31.25%"></div>
                  <span class="gimbal-icon2" style="font-size: 10px" :style="{ top: osdInfo.gimbalPitchPercent }">
                    <span class="gimbal-icon-inner" style="font-size: 8px"></span>
                  </span>
                </div>
                <div class="yaw-editor with-button">
                  <div class="yaw-limit">
                    <div class="limit-marker" style="transform: rotate(-90deg)"></div>
                    <div class="limit-marker" style="transform: rotate(90deg)"></div>
                  </div>
                  <div class="yaw-compass active">
                    <img src="@/assets/plan/znz.png" :style="{ transform: osdInfo.transform }" />
                  </div>
                  <div class="yaw-drone">
                    <img src="@/assets/plan/纸飞机.png" />
                  </div>
                  <div class="yaw-gimbal">
                    <span class="gimbal-icon" style="font-size: 14px; transform: rotate(0deg)"
                      ><span class="gimbal-icon-inner" style="font-size: 12px"></span
                    ></span>
                  </div>
                  <span class="angle-number degree drone map-text-shadow">{{ osdInfo.heading }}</span>
                </div>
                <div class="obstacle-info">
                  <div class="obstacle-bar"></div>
                  <div class="marker zero-marker"></div>
                  <span class="agl-number map-text-shadow">{{ osdInfo.aglHeight }}<span class="unit">m AGL</span></span>
                </div>
              </div>
              <div class="operation-earth">
                <img src="@/assets/plan/flow.png" width="24" />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.alarm-title {
  height: 21.6%;
  max-height: 38px;
  line-height: 38px;
  background: #11253e;
  color: #fff;
  font-family: SourceHanSansSC-Bold;
  font-size: 14px;
  text-align: left;
  font-weight: 700;
  border-bottom: 1px solid #344054;
  padding-left: 8px;
  .nick {
    font-size: 14px;
    color: #f5f6f8;
    text-align: left;
    font-weight: 400;
  }
  .status {
    margin-top: 7px;
    margin-right: 8px;
    background: rgba(42, 139, 125, 0.5);
    border-radius: 2px;
    width: 110px;
    height: 24px;
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;
    color: #39bfa4;
    text-align: center;
    line-height: 22px;
    font-weight: 400;
    &.is-active {
      color: #98a2b3;
      background: rgba(44, 62, 86, 0.5);
    }
  }
}
.info-container {
  color: #fff;
  width: 100%;
  height: 90%;
  background: #001129;
  overflow: hidden;
  // padding-top: 20px;
  .info-left {
    width: 50%;
    overflow: hidden;
  }
  .info-right {
    width: 50%;
    overflow: hidden;
  }
  .c-row {
    font-size: 16px;
    color: #e4e7ec;
    line-height: 40px;
    font-weight: 500;
    padding: 10px;
    height: 40px;
    padding-bottom: 0;
  }
  .c-title {
    text-align: right;
    font-family: SourceHanSansSC-Bold;
    font-size: 14px;
    color: #e4e7ec;
    text-align: right;
    line-height: 22px;
    font-weight: 700;
  }
  .c-text {
    text-align: left;
    padding-left: 14px;
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;
    color: #e4e7ec;
    text-align: left;
    line-height: 22px;
    font-weight: 400;
  }
}
.info-item {
  width: 50%;
  text-align: center;
}
.flex {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.c-compass {
  padding-top: 10px;
  margin: 10px 16px;
  width: 288px;
  height: 190px;
  .compass-item {
    padding-top: 25px;
    padding-left: 40px;
  }
}
.operation-earth {
  position: relative;
  left: 229px;
  top: -150px;
}
.gimbal-pitch {
  margin-left: -15px;
  margin-top: -30px;
}
</style>
<style lang="scss" scoped>
@import '@/styles/plan/plan.scss';
</style>
