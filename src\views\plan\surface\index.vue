<template>
  <div class="wayline-container">
    <div class="wayline-edit-header bg-dark-blue">
      <div class="action-bar">
        <div class="return-left" @click="quiteAndSave('saveAndBack')">
          <el-image style="width: 24px; height: 20px" :src="returnLeftIconUrl" />
        </div>
        <div class="action" @click="save('save')">
          <el-image style="width: 20px; height: 20px" :src="saveIconUrl" />
        </div>
      </div>
      <div class="wayline-preset-info">
        <span class="project title-item">{{ parmsInfoRect.planName }}</span>
      </div>
    </div>
    <div class="middle">
      <div class="sidebar bg-light-blue">
        <wayline-base-info v-model="parmsInfoRect" />
        <div class="sidebar-header bg-dark-blue">
          <div class="stats">
            <div class="stats-item">
              <div class="label">面积</div>
              <div class="value">{{ parmsInfoRect.area }}</div>
            </div>
            <div class="stats-item">
              <div class="label">航线总长度</div>
              <div class="value">{{ parmsInfoRect.length }}</div>
            </div>
            <div class="stats-item">
              <div class="label">预计时间</div>
              <div class="value">{{ parmsInfoRect.time }}</div>
            </div>
          </div>
        </div>
        <div class="sidebar-content bg-dark-blue">
          <parms-section v-model="parmsInfoRect" @setAirPlace="onSetAirPlace" @parmsChangeHandle="reBuildWayLines" />
        </div>
      </div>
      <div class="cesium-Container" id="cesiumContainer" ref="cesiumContainerRef"></div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'Waylines'
};
</script>
<script setup>
import './style/common.css';
import * as Cesium from 'cesium';
import { onMounted, onUnmounted, computed } from 'vue';
import 'element-plus/dist/index.css';
import ParmsSection from './components/ParmsSection.vue';
import WaylineBaseInfo from './components/WaylineBaseInfo.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { appendChildToBtns } from '../common/el-btn.js';
import { removeMapToolTips, setMapToolTips } from '../common/tips';
import {
  canvas2base64,
  getOrCreateCesiumEngineInstance,
  setCameraView,
  DrawPolygon,
  ScreenEventHandler,
  ScreenSpaceEventTypes,
  createAirPortPoint,
  CesiumLayerManager,
  imglayer,
  cialayer,
  projectTerrainOnline,
  projectCustomTerrainProvider,
  getTerrainHeight,
  c3toDegress,
  px2LongitudeAndLatitude,
  toDegrees,
  globalConfigResource,
  zoomtoLonglats
} from '@/components/Cesium/libs/cesium/index';
import {
  aircraft,
  setView,
  parmsInfoRect,
  dataInfoRect,
  goResume,
  dispose,
  drawHandle,
  reBuildWayLines,
  initDataInfoRect,
  uploadWaylinesJson,
  clearWayLineEntity,
  getConfigFromPlanData,
  isAlreadySetAirPlace
} from './hocks/index.js';
import { TEMPLATE_TYPE_NUM_ENUM } from '../newplan/kmz/props';
import { createUploadJson } from './hocks/modules/surfaceKmzHandle';
import { createOrUpdateWayline, getDeptSysSetting } from '@/api/wayline';
import { getWaylines } from '@/api/wayline';
import { useRouter, useRoute } from 'vue-router';
import DataTracker from '@/views/plan/libs/DataTracker';
import { useDeviceStore } from '@/store/modules/device.js';
import { usePlanInfoStore } from '@/store/modules/planInfo.js';
import { useEditTrackerStore } from '@/store/modules/dataEditTracker.js';
const editTrackerStore = useEditTrackerStore();
const planInfoStore = usePlanInfoStore();
const deviceStore = useDeviceStore();
const returnLeftIconUrl = new URL('@/assets/return-left.png', import.meta.url).href;
const saveIconUrl = new URL('@/assets/save.png', import.meta.url).href;
const router = useRouter();
const route = useRoute();
const cesiumContainerRef = ref(null);
let engine, viewer, drawer;
let eventHandler = null;
//#region 组件功能方法变更
const onSetAirPlace = (flag = true) => {
  if (!flag) {
    eventHandler && eventHandler.removeAllEventListeners();
    eventHandler = null;
    return;
  }
  eventHandler = new ScreenEventHandler(viewer);
  if (drawer.state !== 0) {
    initDataInfoRect();
    clearWayLineEntity();
    drawer?.disposeInputActionAndEntity();
  }
  eventHandler.addEventListener(ScreenSpaceEventTypes.LEFT_CLICK, position => {
    isAlreadySetAirPlace.value = true;
    removeMapToolTips();
    if (aircraft.airPlaceMark) {
      viewer.entities.remove(aircraft.airPlaceMark);
    }
    // 转成经纬度
    let latlng = px2LongitudeAndLatitude(viewer, position);
    let airPortPosition = Cesium.Cartesian3.fromDegrees(latlng.lng, latlng.lat, 0);
    getTerrainHeight(viewer, airPortPosition).then(obj => {
      // 设置机场位置
      aircraft.airPlaceMark = createAirPortPoint(viewer, {
        id: 'airport',
        position: obj.position
      });
      dataInfoRect.airPlaceMarkPosition = toDegrees(obj.position);
      // 刚初始化时是绝对高度模式这里是在预设的120m的基础上增加了地形高度 防止初始点在地下
      parmsInfoRect.flightHight += Number(obj.terrainHeight);
      parmsInfoRect.alreadySetAirPlace = true;
      // 设置飞机初始高度
      parmsInfoRect.airPortPlaceASL = obj.terrainHeight;
      // 设置飞机位置 这里需要设置经纬度数组
      const { longitude, latitude, height } = c3toDegress(obj.position);
      parmsInfoRect.airPortPlace = [longitude, latitude, height]; // obj.position;
      // 这里需要重新绘制
      reBuildWayLines();
      if (!dataInfoRect.done) {
        setMapToolTips('点击地图生成测绘区域');
      }
      // 移除监听
      eventHandler.removeAllEventListeners();
      if (drawer.state !== 0) {
        drawer.drawing(drawHandle);
      }
    });
  });
};

// 初始化地图
const initViewer = () => {
  engine = getOrCreateCesiumEngineInstance('test');
  engine?.init('cesiumContainer');
  viewer = engine.viewer;
  const layerManager = new CesiumLayerManager(engine.viewer);
  layerManager.addLayer(imglayer);
  layerManager.addLayer(cialayer);
  // 地形赋值
  // viewer.terrainProvider = projectCustomTerrainProvider;
  // 默认地形
  const onlineTerrainProvider = Cesium.createWorldTerrain({
    requestWaterMask: false,
    requestVertexNormals: true
  });
  if (projectTerrainOnline) {
    viewer.terrainProvider = onlineTerrainProvider;
  } else {
    viewer.terrainProvider = projectCustomTerrainProvider || customTerrainProvider;
  }
};

// 询问保存
const save = (saveType = 'save', callback = null) => {
  removeMapToolTips();
  try {
    // 检查是否完成绘制
    if (parmsInfoRect.planName.length === 0) {
      ElMessage.warning('请输入航线名称');
      return;
    }

    // 检查是否完成绘制
    if (!dataInfoRect.done) {
      ElMessage.warning('请先完成航线绘制');
      return;
    }

    // 判断机场位置是否有值
    if (parmsInfoRect.airPortPlace.length < 2) {
      ElMessage.warning('请设置机场位置');
      return;
    }
    //提交后端的最终json
    drawer?.resetStatue();
    zoomtoLonglats(viewer, dataInfoRect.aircraftRoutePositions);
    // 获取航线资料
    let uploadJson = createUploadJson();
    const deviceInfo = deviceStore.getCurrentDevice();
    let base64 = canvas2base64(viewer) ?? ' ';
    uploadWaylinesJson.wayline_id = uploadWaylinesJson.wayline_id ?? null;
    uploadWaylinesJson.name = parmsInfoRect.planName || '新建面状航线';
    uploadWaylinesJson.drone_model = deviceInfo.droneModelKey;
    uploadWaylinesJson.thumbnail = base64;
    uploadWaylinesJson.payload_model = deviceInfo.payloadModelKey;
    uploadWaylinesJson.template_type = TEMPLATE_TYPE_NUM_ENUM[uploadJson.template.Folder[0].wpml_templateType] || 1;
    uploadWaylinesJson.airline_json = JSON.stringify({
      template: uploadJson.template,
      wayLines: uploadJson.wayLines,
      config: uploadJson.config || {}
    });
    console.log('uploadJson:', uploadJson);
    //提交
    createOrUpdateWayline(uploadWaylinesJson)
      .then(res => {
        if (res) {
          if (res === '航线文件名已存在') {
            ElMessage.warning('该航线名称已存在，请重新命名');
            dataInfoRect.done = true;
            return;
          }
          uploadWaylinesJson.wayline_id = res;
          ElMessage.success('保存航线成功');
          callback && callback();
          // 标记已保存
          editTrackerStore.dataTracker.save();
          if (saveType === 'saveAndBack') {
            setTimeout(() => {
              router.back();
              editTrackerStore.dataTracker.reset();
              deviceStore?.setCameraSelect([]); // 使用可选链和空值合并操作符
              deviceStore?.setWayLineCameraType([]); // 使用可选链和空值合并操作符
            }, 500);
          }
        }
      })
      .catch(err => {
        ElMessage.error('保存航线错误！原因：' + err.message);
      });
  } catch (error) {}
};

const quiteAndSave = (saveType = 'saveAndBack') => {
  removeMapToolTips();
  // 检查面是否完成绘制
  if (!dataInfoRect.done) {
    setTimeout(() => {
      appendChildToBtns();
    }, 100);
    ElMessageBox.confirm(`航面还未绘制是否退出？`, '注意', {
      confirmButtonText: '退出',
      cancelButtonText: '不退出',
      type: 'warning',
      closeOnClickModal: false,
      showClose: false
    })
      .then(() => {
        setTimeout(() => {
          router.back();
          editTrackerStore.dataTracker.reset();
          deviceStore?.setCameraSelect([]); // 使用可选链和空值合并操作符
          deviceStore?.setWayLineCameraType([]); // 使用可选链和空值合并操作符
          dispose();
        }, 100);
      })
      .catch(() => {});
    return;
  }
  // 面完成绘制后检查是否完成编辑
  const dataStatus = editTrackerStore.dataTracker.getDataStatus();
  // 已经保存了可以直接返回
  if (dataStatus === DataTracker.DataStatus.SAVED_AND_UNMODIFIED) {
    dispose();
    deviceStore?.setCameraSelect([]); // 使用可选链和空值合并操作符
    deviceStore?.setWayLineCameraType([]); // 使用可选链和空值合并操作符
    editTrackerStore.dataTracker.reset();
    router.back();
  } else if (dataStatus === DataTracker.DataStatus.MODIFIED_AND_UNSAVED) {
    setTimeout(() => {
      appendChildToBtns();
    }, 100);
    // 数据已修改但未保存
    ElMessageBox.confirm(`返回将退出航线编辑，尚未保存的内容将丢失，是否确定退出？`, '注意', {
      confirmButtonText: '保存并退出',
      cancelButtonText: '不保存',
      type: 'warning',
      closeOnClickModal: false,
      showClose: false
    })
      .then(() => {
        save(saveType, () => {
          dispose();
          deviceStore?.setCameraSelect([]); // 使用可选链和空值合并操作符
          deviceStore?.setWayLineCameraType([]); // 使用可选链和空值合并操作符
        });
      })
      .catch(() => {
        setTimeout(() => {
          router.back();
          editTrackerStore.dataTracker.reset();
        }, 100);
      });
  } else {
    // 数据未修改且未保存
    editTrackerStore.dataTracker.reset();
    router.back();
  }
};

/**
 * 初始化相机视角
 * @param {*} viewer
 */
const initViewerCamera = async viewer => {
  await getDeptSysSetting({}).then(res => {
    const { wayline_config = {} } = res;
    // 设置相机视角
    setCameraView(viewer, {
      destination: Cesium.Cartesian3.fromDegrees(
        Number(wayline_config.longitude),
        Number(wayline_config.latitude),
        2000
      ),
      orientation: new Cesium.HeadingPitchRoll(
        Cesium.Math.toRadians(45),
        Cesium.Math.toRadians(-90),
        Cesium.Math.toRadians(45)
      ),
      duration: 0.75
    });
  });
};

//#endregion
onMounted(() => {
  removeMapToolTips();
  isAlreadySetAirPlace.value = true;
  initViewer();
  if (!viewer) {
    throw new Error('viewer is null');
  }
  setView(viewer);
  initViewerCamera(viewer);

  dataInfoRect.done = false;
  uploadWaylinesJson.wayline_id = null;
  // 绘制航线
  drawer = new DrawPolygon(viewer);
  let id = route.query.id;
  let import_type = route.query.import_type;
  if (!id) {
    // 新建航线
    goResume(viewer, drawer, reBuildWayLines);
  } else {
    // 通过ID获取航线信息进行恢复
    getWaylines({
      order_by: 'update_time desc',
      wayline_id: id
    }).then(data => {
      const { list = [] } = data;
      if (list?.length > 0) {
        let data = list[0] ?? '';
        uploadWaylinesJson.wayline_id = data.id;
        planInfoStore.setCurPlanData(data);
        if ([1, '1', '2', 2].includes(import_type)) {
          data.airline_json = getConfigFromPlanData(data);
        }
        goResume(viewer, drawer, reBuildWayLines);
        zoomtoLonglats(viewer, dataInfoRect.aircraftRoutePositions);
      }
    });
  }
  // 添加鼠标移入和移出事件的处理函数
  cesiumContainerRef.value.addEventListener('mouseleave', handleMouseLeave);
});

watch(
  () => isAlreadySetAirPlace.value,
  val => {
    if (val) {
      autoAddCursor();
    } else {
      autoRemoveCursor();
    }
  }
);
const autoAddCursor = () => {
  var cesiumContainers = document.querySelectorAll('.cesium-Container');
  cesiumContainers.forEach(function (container) {
    container.classList.add('custom-cursor');
  });
};
const autoRemoveCursor = () => {
  var cesiumContainers = document.querySelectorAll('.cesium-Container');
  cesiumContainers.forEach(function (container) {
    container.classList.remove('custom-cursor');
  });
};

const handleMouseLeave = () => {
  // 当鼠标移出时,设置鼠标指针样式为 'default'
  document.body.style.cursor = 'default';
};
onBeforeUnmount(() => {
  dispose();
  removeMapToolTips();
});
onUnmounted(() => {
  dispose();
  removeMapToolTips();
  drawer && drawer.dispose();
  drawer = null;
  uploadWaylinesJson.wayline_id = null;
  isAlreadySetAirPlace.value = true;
});
</script>

<style lang="scss" scoped>
::v-deep.el-popper.el-popover {
  background-color: #313131;
  width: 300px !important;
}
.custom-cursor {
  cursor: url('../../../assets/cloud-control.png'), auto;
}
.wayline-container {
  width: 100%;
  height: 100%;
  background-color: #171717;
  user-select: none;
}

.wayline-edit-header {
  height: 50px;
  width: 100%;
  background-color: #232323;
  display: flex;
  align-items: center;
  position: relative;

  .action-bar {
    position: absolute;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    user-select: none;
    .action {
      margin-left: 20px;
      &:hover {
        cursor: pointer;
      }
      line-height: 100%; // 与容器高度保持一致
    }
    .back {
      margin-left: 10px;
      font-size: 25px;
      color: whitesmoke;
    }
    .save {
      margin-left: 20px;
      font-size: 22px;
    }
  }

  .wayline-preset-info {
    position: absolute;
    left: 50%;
    user-select: none;
    transform: translateX(-50%);
    .title-item {
      height: 100%;
      color: rgba(255, 255, 255, 0.721);
      cursor: pointer;
      &:hover {
        color: whitesmoke;
      }
      background-color: #3c3c3cc9;
      padding: 10px 10px;
      border-radius: 5px;
    }
    .project {
    }
  }
}

.middle {
  width: 100%;
  height: calc(100% - 50px);
  display: flex;
}

.middle .sidebar {
  width: 450px;
  color: white;
  box-sizing: border-box;
  .sidebar-header {
    padding: 0px 10px;
    height: 100px;
    border: 1px solid transparent;
    .title {
      color: white;
      padding: 10px 15px;
      font-size: 20px;
      user-select: none;
    }
    .stats {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 16px 10px;
      margin-top: 16px;
      margin-bottom: 20px;
      .stats-item {
        .label {
          text-align: center;
          padding: 5px;
          font-family: SourceHanSansSC-Bold;
          font-size: 14px;
          color: #ffffff;
          text-align: left;
          line-height: 22px;
          font-weight: 600;
          text-align: center;
        }
        .value {
          margin-top: 5px;
          font-family: SourceHanSansSC-Bold;
          font-size: 20px;
          color: #ffffff;
          text-align: center;
          line-height: 28px;
          font-weight: 700;
        }
      }
    }
    // border-bottom-color: rgba(240, 248, 255, 0.164);
  }
  .sidebar-content {
    height: calc(100% - 200px);
    padding-bottom: 10px; // 让出底部
  }
}

#cesiumContainer {
  height: 100%;
  width: 100%;
}

.delete-popconfirm {
  width: 300px !important;
  .el-popper {
    font-size: 13px;
  }
  .el-popconfirm__main {
  }
  .el-popconfirm__action {
  }
}

.return-left {
  margin-left: 20px;
  display: flex;
  align-items: center;
  &:hover {
    cursor: pointer;
  }
}
</style>
