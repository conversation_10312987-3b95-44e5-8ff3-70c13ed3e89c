<template>
  <div>
    <div class="msg-content">
      <el-tooltip
        class="box-item"
        show-after="500"
        effect="dark"
        content="“起飞点”仅做航线规划参考，飞机执行<br/>航线时以飞机真实的起飞点为准。"
        raw-content="true"
        placement="right"
      >
        <el-button
          v-if="templateJsons.wpml_missionConfig.wpml_takeOffRefPoint == ''"
          type="primary"
          style="border-radius: 5px; width: 100%"
          @click="setTool('start')"
          >参考起点未设置</el-button
        >
        <div
          v-if="templateJsons.wpml_missionConfig.wpml_takeOffRefPoint != ''"
          style="display: flex; justify-content: space-between"
        >
          <label>已设置参考起飞点</label>
          <el-link type="primary" @click="setTool('start')">
            <img src="@/assets/plan/起飞点.png" width="20" style="margin-right: 5px" />
            重设起飞点</el-link
          >
        </div>
      </el-tooltip>
    </div>
    <div class="msg-content pad10">
      <CameraSelect @changeHandle="onCameraTypeChangeHandle" />
    </div>

    <el-tooltip
      class="box-item"
      effect="dark"
      show-after="500"
      content="垂直爬升：飞行器爬升到航线起始点高度<br/>后，再飞向航线起始点。<br/><br/>倾斜爬升：飞行器爬升到“安全起飞<br/>高度”后，再直线飞到航线起始点。<br/><br/>安全起飞高度：是相对起飞点的高度值。<br/><br/>
飞行器起飞后，会先上升至“安全起飞高<br/>度”，再飞向航线起始点"
      raw-content="true"
      placement="right"
    >
      <div class="msg-content">
        <el-row>
          <el-col :span="12">
            <el-button
              id="btn-safely"
              type="primary"
              :color="colorJson.flyToWaylineMode.safely"
              style="width: 100%"
              @click="setFlyToWaylineMode('safely')"
              >垂直爬升</el-button
            ></el-col
          >
          <el-col :span="12">
            <el-button
              id="btn-pointToPoint"
              type="primary"
              :color="colorJson.flyToWaylineMode.pointToPoint"
              style="width: 100%"
              @click="setFlyToWaylineMode('pointToPoint')"
              >倾斜爬升</el-button
            ></el-col
          >
        </el-row>
        <el-row style="margin-top: 5px;">
          <el-col :span="18">
            <div class="img-content-box">
              <img src="@/assets/plan/takeoff.svg"  width="200" />
            </div>
          </el-col>
          <el-col :span="6">
            <div class="p-content-box">
              <p class="p-content">
                <el-button class="btn1" @click="setSafelyHeight(100)">+100</el-button>
              </p>
              <p class="p-content">
                <el-button class="btn1" @click="setSafelyHeight(10)">+10</el-button>
              </p>

              <p class="p-content center">
                <div style="font-size: 22px; color: #2d8cf0; font-weight: bold">{{
                  templateJsons.wpml_missionConfig.wpml_takeOffSecurityHeight
                }}</div
                >m
              </p>
              <p class="p-content">
                <el-button class="btn1" @click="setSafelyHeight(-10)">-10</el-button>
              </p>
              <p class="p-content">
                <el-button class="btn1" @click="setSafelyHeight(-100)">-100</el-button>
              </p>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-tooltip>
    <el-tooltip
      class="box-item"
      effect="dark"
      show-after="500"
      :content="heightTooltip"
      raw-content="true"
      placement="right"
    >
      <div class="msg-content">
        <el-row>
          <el-col :span="12">
            <el-button
              type="primary"
              :color="colorJson.heightMode.EGM96"
              style="width: 100%"
              @click="setHeightMode('EGM96')"
              >绝对高度</el-button
            ></el-col
          >
          <el-col :span="12">
            <el-button
              style="width: 100%"
              :color="colorJson.heightMode.relativeToStartPoint"
              @click="setHeightMode('relativeToStartPoint')"
              >相对起飞的高度</el-button
            >
          </el-col>
        </el-row>
        <el-row style="margin-top: 5px;">
          <el-col :span="18">
            <div class="img-content-box">      
                 <img :src="heightModeImg" width="200" /> 
            </div>
          </el-col>
          <el-col :span="6">
            <div class="p-content-box">
              <p class="p-content">
              <el-button class="btn1" @click="setTakeOffRefPointAGLHeight(100)">+100</el-button>
            </p>
            <p class="p-content">
              <el-button class="btn1" @click="setTakeOffRefPointAGLHeight(10)">+10</el-button>
            </p>

              <p class="p-content center">
                <div style="font-size: 22px; color: #2d8cf0; font-weight: bold">{{heights.curHeight }}</div
                >m
              </p> 

              <p class="p-content">
              <el-button class="btn1" @click="setTakeOffRefPointAGLHeight(-10)">-10</el-button>
            </p>
            <p class="p-content">
              <el-button class="btn1" @click="setTakeOffRefPointAGLHeight(-100)">-100</el-button>
            </p>
            </div>
          </el-col>
        </el-row> 
      </div>
    </el-tooltip>

    <div class="msg-content">
      <div class="msg-content-title"><span>全航线速度</span></div>
      <div class="msg-content-wrap">
        <el-button class="btn2" @click="setAutoFlightSpeed(-1)"><span style="font-size: 28px;">-</span></el-button>
        <span style="font-size: 22px">
          <label style="color: #2d8cf0; font-weight: bold">{{ templateJsons.Folder.wpml_autoFlightSpeed }}</label>
          m/s
        </span>
        <el-button class="btn2" @click="setAutoFlightSpeed(1)">+</el-button>
      </div>
    </div>
    <div class="msg-content">
      <div class="msg-content-title"><span>起飞速度</span></div>
      <div class="msg-content-wrap">
        <el-button class="btn2" @click="setGlobalTransitionalSpeed(-1)"><span style="font-size: 28px;">-</span></el-button>
        <span style="font-size: 22px">
          <label id="wrj_" style="color: #2d8cf0; font-weight: bold">{{
            templateJsons.wpml_missionConfig.wpml_globalTransitionalSpeed
          }}</label>
          m/s
        </span>
        <el-button class="btn2" @click="setGlobalTransitionalSpeed(1)">+</el-button>
      </div>

      <div>
        <div v-if="!isIncidentType" style="margin: 0px 0px 10px 10px">
          <span>航点类型</span>
          <div style="margin: 10px 5px 10px 0px; display: flex; justify-content: space-between">
            <el-select
              v-model="templateJsons.Folder.wpml_globalWaypointTurnMode"
              style="width: 300px"
              @change="batchUpdateWaypointTurnMode()"
            >
              <el-option
                v-for="item in globalWaypointTurnMode"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                style="height: 130px"
              >
                <div>
                  <p><img :src="item.img" style="width: 250px" /></p>
                  <p>{{ item.label }}</p>
                </div>
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
      <!-- 要修改 20241022 -->
      <el-tooltip
      v-if="!isIncidentType"
         class="box-item"
          effect="dark"
        show-after="500"
        content="沿航线方向：飞行器机头沿着航线方向飞至下一航点<br/> 
锁定当前偏航角：飞行器机头保持执行完航点动作后的飞行器偏航角飞至下一航点<br/> 
朝向兴趣点：该模式的使用范围是在警情中创建的航线中使用，使用该模式可以让<br/>飞行器朝向兴趣点，并且保持航向不变" raw-content="true"
      placement="right"
    >
      <div>   
        <div style="margin: 0px 0px 10px 10px">
          <span>飞行器偏航角模式</span>
          <div style="margin: 10px 5px 10px 0px; display: flex; justify-content: space-between">
            <el-select
              v-model="templateJsons.Folder.wpml_globalWaypointHeadingParam.wpml_waypointHeadingMode"
              style="width: 300px"
              @change="batchUpdateWaypointTurnMode()"
            >
              <el-option v-for="item in waypointHeadingMode" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
        </div> 
      </div> 
    </el-tooltip>
    
      <div v-if="!isIncidentType">
        <div style="margin: 0px 0px 10px 10px">
          <span>航点间云台俯仰角控制模式</span>
          <div style="margin: 10px 5px 10px 0px; display: flex; justify-content: space-between">
            <el-select
              v-model="templateJsons.Folder.wpml_gimbalPitchMode"
              @change="updateTrackerStatue()"
              style="width: 300px"
            >
              <el-option v-for="item in gimbalPitchMode" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
      
      <div>
        <div style="margin: 0px 0px 10px 10px">
          <span>完成动作</span>
          <div style="margin: 10px 5px 10px 0px; display: flex; justify-content: space-between">
            <el-select
              v-model="templateJsons.wpml_missionConfig.wpml_finishAction"
              @change="updateTrackerStatue()"
              style="width: 300px"
            >
              <el-option v-for="item in finishAction" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'PlanRouteSet'
};
</script>
<script setup>
import { onMounted, onUnmounted, ref } from 'vue';
import { ElMessage, ElInput, ElButton } from 'element-plus';
import { Minus, Plus } from '@element-plus/icons-vue';
import 'element-plus/dist/index.css';
import CameraSelect from '@/views/plan/components/CameraSelect.vue';
import { updateActionFrustum } from '../newplan/kmz/hocks/modules/actionFrustumHandle';
import { useWayPointStore } from '@/store/modules/wayPointInfo.js';
import { toCartesian3 } from '@/components/Cesium/libs/cesium';
import { createFrustum, getFrustum, updateAllFrustum } from '@/views/plan/newplan/kmz/hocks';
import { useEditTrackerStore } from '@/store/modules/dataEditTracker.js'; 
import { WAY_POINT_HEADING_MODE   } from '@/views/plan/newplan/kmz/props/index.js';
import { useDeviceStore } from '@/store/modules/device.js';
import{setMapToolTips,removeMapToolTips} from '../common/tips.js';
import { usePlanInfoStore } from '@/store/modules/planInfo.js';
const deviceStore = useDeviceStore();
const editTrackerStore = useEditTrackerStore();
const wayPointStore = useWayPointStore();
const planInfoStore = usePlanInfoStore();
// 航线是否是告警类型
let isIncidentType = ref(false);
let plan;
let viewer;
let eyeViewer;
defineExpose({
  setFirstPoint, //设置参考起飞点
  setDronePayload, //设置飞行器及负载
  setPlan, //航线计划对象
  getTemplateJsons, //返回templateJsons
  setTemplateJsons //设置TemplateJson
});
const props = defineProps({
  wrjInfoSet: {
    type: Function,
    default: () => {}
  }
});
//照片
let imageFormatArr = [];
//高度值计算
let heights = ref({
  //mode: "EGM96",//EGM96：使用海拔高编辑；relativeToStartPoint：使用相对点的高度进行编辑；aboveGroundLevel：使用地形数据
  curHeight: 120, //根据当前模式计算高度
  aslHeight: 120, //绝对高度
  altHeight: 0, //相对起飞点高度
  aglHeight: 0 //相对地形高度
});

//精准复拍动作，动作中的参数，actionActuatorFunc
let orientedShoot = ref({
  orientedPhotoMode: 'normalPhoto' // normalPhoto: 普通拍照;lowLightSmartShooting：低光智能拍照
});
let devInfo = deviceStore.getCurrentDevice(); 
//航线文件模版template.kml的主要属性(航点模式)
let templateJsons = ref({
  wpml_author: '', //文件创建作者
  wpml_createTime: '', //文件创建时间（Unix Timestamp）
  wpml_updateTime: '', //文件更新时间（Unix Timestamp)
  wpml_missionConfig: {
    wpml_flyToWaylineMode: 'safely', //飞向首航点模式
    wpml_finishAction: 'goHome', //航线结束动作
    wpml_exitOnRCLost: 'goContinue', //失控是否继续执行航线
    wpml_executeRCLostAction: 'goBack', //失控动作类型
    wpml_takeOffSecurityHeight: 20, //安全起飞高度
    wpml_globalTransitionalSpeed: 15, //全局航线过渡速度,(起飞速度)
    wpml_globalRTHHeight: 100, //全局返航高度
    wpml_takeOffRefPoint: '', //参考起飞点
    wpml_takeOffRefPointAGLHeight: null, //参考起飞点海拔高度
    //飞行器机型信息
    wpml_droneInfo: {
      wpml_droneEnumValue: devInfo.droneEnumVal ?? 91, //Matrice 3TD
      wpml_droneSubEnumValue: devInfo.droneSubEnumVal ?? 1 //0:Matrice 3D; 1:Matrice 3TD
    },
    //负载机型信息
    wpml_payloadInfo: {
      wpml_payloadEnumValue:  devInfo.payloadEnumVal ?? 81,  //80:Matrice 3D Camera，81:Matrice 3TD camera
      wpml_payloadSubEnumValue:devInfo.payloadSubEnumVal ?? 0,
      wpml_payloadPositionIndex: 0
    }
  },
  Folder: {
    wpml_templateType: 'waypoint', //预定义模板类型，waypoint：航点飞行；mapping2d：建图航拍；mapping3d：倾斜摄影；mappingStrip：航带飞行
    wpml_templateId: 0, //模板ID，* 注：在一个kmz文件内该ID唯一。建议从0开始单调连续递增。在template.kml和waylines.wpml文件中，将使用该id将模板与所生成的可执行航线进行关联。
    wpml_globalWaypointTurnMode: 'toPointAndStopWithDiscontinuityCurvature', //全局航点类型（全局航点转弯模式）
    wpml_gimbalPitchMode: 'usePointSetting', //云台俯仰角控制模式，//usePointSetting时，航点中必须设置 wpml_gimbalPitchAngle（航点云台俯仰角）
    wpml_autoFlightSpeed: 10, //全局航线飞行速度
    wpml_globalUseStraightLine: 0, //全局航段轨迹是否尽量贴合直线,0：航段轨迹全程为曲线；1：航段轨迹尽量贴合两点连线
    wpml_globalHeight: 120, //全局航线高度（相对起飞点高度）
    //坐标系参数信息
    wpml_waylineCoordinateSysParam: {
      wpml_coordinateMode: 'WGS84', //经纬度坐标系
      wpml_heightMode: 'EGM96' //航点高程参考平面,EGM96：使用海拔高编辑;relativeToStartPoint：使用相对点的高度进行编辑;aboveGroundLevel：使用地形数据，AGL下编辑(仅支持司空2平台)
      // wpml_globalShootHeight: 0,//飞行器离被摄面高度（相对地面高）。* 注：仅适用于模板类型mapping2d，mapping3d，mappingStrip
      // wpml_surfaceFollowModeEnable: 0,//是否开启仿地飞行。* 注：仅适用于模板类型mapping2d，mapping3d，mappingStrip
      // wpml_surfaceRelativeHeight: 0,//仿地飞行高度（相对地面高）。* 注：仅适用于模板类型mapping2d，mapping3d，mappingStrip
      // wpml_surfaceFollowMode: 0,//仿地飞行模式。* 注：仅适用于模板类型mapping2d，mapping3d，mappingStrip
    },
    wpml_globalWaypointHeadingParam: {
      wpml_waypointHeadingMode: 'followWayline', //飞行器偏航角模式
      wpml_waypointHeadingPathMode: 'followBadArc' //飞行器偏航角转动方向。clockwise：顺时针旋转飞行器偏航角；counterClockwise：逆时针旋转飞行器偏航角；followBadArc：沿最短路径旋转飞行器偏航角
    },
    wpml_payloadParam: {
      wpml_payloadPositionIndex: 0, //负载挂载位置,0：飞行器1号挂载位置。M300 RTK，M350 RTK机型，对应机身左前方。其它机型，对应主云台。1：飞行器2号挂载位置。M300 RTK，M350 RTK机型，对应机身右前方。2：飞行器3号挂载位置。M300 RTK，M350 RTK机型，对应机身上方。
      wpml_focusMode: 'firstPoint',
      wpml_meteringMode: 'average',
      wpml_returnMode: 'singleReturnFirst',
      wpml_samplingRate: 240000,
      wpml_scanningMode: 'repetitive',
      wpml_imageFormat: 'visable,ir' //图像格式列表，	wide：存储广角镜头照片；zoom：存储变焦镜头照片；ir：存储红外镜头照片；narrow_band: 存储窄带镜头拍摄照片,visable：可见光照片
    },
    //航点信息（包括航点经纬度和高度等）
    Placemark: []
  }
});

//航点高度模式图片
const heightModeImg = ref(new URL('@/assets/plan/asl.svg', import.meta.url).href);
const heightTooltip = ref('绝对高度：航点高度值相对于海平面高度<br/>保持不变。');
//全局航点类型（全局航点转弯模式）
const globalWaypointTurnMode = ref([
  {
    value: 'coordinateTurn',
    label: '协调转弯，不过点，提前转弯',
    img: new URL('@/assets/plan/coordinated-turn.svg', import.meta.url).href
  },
  {
    value: 'toPointAndStopWithDiscontinuityCurvature',
    label: '直线飞行，飞行器到点停',
    img: new URL('@/assets/plan/straight-flight.svg', import.meta.url).href
  },
  {
    value: 'toPointAndStopWithContinuityCurvature',
    label: '曲线飞行，飞行器到点停',
    img: new URL('@/assets/plan/curve-flight1.svg', import.meta.url).href
  },
  {
    value: 'toPointAndPassWithContinuityCurvature',
    label: '曲线飞行，飞行器过点不停',
    img: new URL('@/assets/plan/curve-flight2.svg', import.meta.url).href
  }
]);

//飞行器偏航角模式 
const waypointHeadingMode =  [
  {
    value:WAY_POINT_HEADING_MODE.followWayline,// 'followWayline',
    label: '沿航线方向'
  }, 
  {
    value:WAY_POINT_HEADING_MODE.fixed,// 'fixed',
    label: '锁定当前偏航角'
  },
  // {
  //   value:WAY_POINT_HEADING_MODE.towardPOI, //'towardPOI',
  //   label: '朝向兴趣点'
  // }
];

//云台俯仰角控制模式
const gimbalPitchMode = ref([
  // {
  //   value: 'manual',
  //   label: '手动控制'
  // },
  {
    value: 'usePointSetting',
    label: '依照每个航点设置'
  }
]);

//完成动作
const finishAction = ref([
  {
    value: 'goHome',
    label: '自动返航'
  },
  {
    value: 'gotoFirstWaypoint',
    label: '返回航线起始点悬停'
  }
  // ,
  // {
  //   value: 'noAction',
  //   label: '退出航线模式'
  // },
  // {
  //   value: 'autoLand',
  //   label: '原地降落'
  // }
]);

//失控动作

//按钮颜色设置，#2E90FA代表蓝色开启，#353436代表灰色关闭
let colorJson = ref({
  imageFormat: {
    wide: '#555658', //默认广角开启
    zoom: '#555658', //默认变焦开启
    visable: '#2E90FA', //可见光照片
    ir: '#2E90FA', //红外照片
    narrow_band: '#555658' //存储窄带镜头拍摄照片
  },
  flyToWaylineMode: {
    safely: '',
    pointToPoint: '#555658'
  },
  heightMode: {
    EGM96: '', //使用海拔高编辑
    relativeToStartPoint: '#555658', //使用相对点的高度进行编辑
    aboveGroundLevel: '#555658' //使用地形数据，AGL下编辑(仅支持司空2平台)
  }
});

/**
 * 赋值航线计划对象
 * @param {*} planObj
 */
function setPlan(planObj) {
  plan = planObj;
}

/**
 * 设置参考起飞点
 * @param {*} lon 经度
 * @param {*} lat 纬度
 * @param {*} height 海拔
 */
function setFirstPoint(lon, lat, height) {
  templateJsons.value.wpml_missionConfig.wpml_takeOffRefPoint = lat.toFixed(8) + ',' + lon.toFixed(8) + ',' + height;
  templateJsons.value.wpml_missionConfig.wpml_takeOffRefPointAGLHeight = height; //海拔
  editTrackerStore.dataTracker.markAsModified();
}

/**
 * 设置飞行器及负载信息
 * @param {*} droneEnumVal 飞行器类型
 * @param {*} droneSubEnumVal 飞行器子类型，型号
 * @param {*} payloadEnumVal 负载类型
 * @param {*} payloadSubEnumVal 负载子类型
 */
function setDronePayload(droneEnumVal, droneSubEnumVal, payloadEnumVal, payloadSubEnumVal) {
  templateJsons.value.wpml_missionConfig.wpml_droneInfo.wpml_droneEnumValue = droneEnumVal;
  templateJsons.value.wpml_missionConfig.wpml_droneInfo.wpml_droneSubEnumValue = droneSubEnumVal;
  templateJsons.value.wpml_missionConfig.wpml_payloadInfo.wpml_payloadEnumValue = payloadEnumVal;
  templateJsons.value.wpml_missionConfig.wpml_payloadInfo.wpml_payloadSubEnumValue = payloadSubEnumVal;
}

/**
 * 处理摄像头类型的改变事件。
 * @param {Array<string>} imageFormatArr - 图像格式数组。
 */
function onCameraTypeChangeHandle(imageFormatArr = []) {
  const joinedImageFormats = imageFormatArr.length > 0 ? imageFormatArr.join(',') : '';
  templateJsons.value.Folder.wpml_payloadParam.wpml_imageFormat = joinedImageFormats;
}

/**
 * 飞向首航点模式
 * @param {*} mode safely：安全模式;pointToPoint：倾斜飞行模式
 */
function setFlyToWaylineMode(mode) {
  if (mode === 'safely') {
    colorJson.value.flyToWaylineMode.safely = '#2E90FA';
    colorJson.value.flyToWaylineMode.pointToPoint = '#555658';
    templateJsons.value.wpml_missionConfig.wpml_flyToWaylineMode = 'safely';
  } else if (mode === 'pointToPoint') {
    colorJson.value.flyToWaylineMode.safely = '#555658';
    colorJson.value.flyToWaylineMode.pointToPoint = '#2E90FA';
    templateJsons.value.wpml_missionConfig.wpml_flyToWaylineMode = 'pointToPoint';
  }

  editTrackerStore.dataTracker.markAsModified();
  //重新更新起飞点到第一个航点的航线
  if (plan) {
    if (plan.planPointJson.length > 0) {
      let flyPoint = plan.planPointJson[0];
      flyPoint = plan.setFlyPoint(
        flyPoint.lon,
        flyPoint.lat,
        flyPoint.startHeight,
        templateJsons.value.wpml_missionConfig.wpml_flyToWaylineMode,
        templateJsons.value.wpml_missionConfig.wpml_takeOffSecurityHeight
      );
      if (plan.curPlanPointJson != null) {
        const planPoint = plan.selectPlanPoint(plan.curPlanPointJson.index);
        props.wrjInfoSet(planPoint);
      } else {
        props.wrjInfoSet({
          lon: flyPoint.lon,
          lat: flyPoint.lat,
          startHeight: flyPoint.startHeight,
          terrainHeight: flyPoint.terrainHeight,
          height: plan.aslHeight,
          UAVHPR: flyPoint.UAVHPR
        });
      }
    }
  }
}
/**
 * 高度模式设置
 * @param {*} mode EGM96：使用海拔高编辑；relativeToStartPoint：使用相对点的高度进行编辑；aboveGroundLevel：使用地形数据，AGL下编辑(仅支持司空2平台)
 */
function setHeightMode(mode) {
  //#region 设置飞机当下位置及姿态
  let cameraFrust = null;
  if (plan?.model) {
    const { latitude, longitude, height: model_Height } = plan.model.getPosition();
    cameraFrust = getFrustum('camera');
    wayPointStore?.setCurrentModelInfo({
      latitude,
      longitude,
      height: model_Height,
      heading: cameraFrust.heading ?? 0,
      pitch: cameraFrust.pitch ?? 0,
      roll: cameraFrust.roll
    });
  }
  //#endregion

  if (mode === 'EGM96') {
    colorJson.value.heightMode.EGM96 = '#2E90FA';
    colorJson.value.heightMode.relativeToStartPoint = '#555658';
    colorJson.value.heightMode.aboveGroundLevel = '#555658';
    templateJsons.value.Folder.wpml_waylineCoordinateSysParam.wpml_heightMode = 'EGM96';
    heightModeImg.value = new URL('@/assets/plan/asl.svg', import.meta.url).href;
    heightTooltip.value = '绝对高度：航点高度值相对于海平面高度<br/>保持不变。';
    const preHeight = heights.value.aslHeight;
    //计算当前高度模式下，几个高度值
    heights.value.aslHeight = heights.value.curHeight; //绝对高度
    heights.value.altHeight =
      heights.value.curHeight - templateJsons.value.wpml_missionConfig.wpml_takeOffRefPointAGLHeight; //相对起飞点高度
    heights.value.aglHeight =
      heights.value.curHeight - templateJsons.value.wpml_missionConfig.wpml_takeOffRefPointAGLHeight; //相对地形高度,暂赋予起飞点地形高度
    if (plan) {
      plan.heightModel = 'EGM96';
      const h = heights.value.aslHeight - preHeight; //如果是绝对高度模式，则为0，如果是相对高度切换过来的，则高度应该变小，减了起飞点地形高度的值，值为负数
      plan.aslHeight = heights.value.curHeight; //绝对高度
      plan.globalHeight = heights.value.curHeight; //设置高度
      if (plan.planPointJson.length > 0) {
        const flyPoint = plan.refreshPoint(wayPointStore.currentPointIndex.value || 0, h, true);
        if (plan.curPlanPointJson != null) {
          const planPoint = plan.selectPlanPoint(plan.curPlanPointJson.index);
          props.wrjInfoSet(planPoint);
        } else {
          props.wrjInfoSet({
            lon: flyPoint.lon,
            lat: flyPoint.lat,
            startHeight: flyPoint.startHeight,
            terrainHeight: flyPoint.terrainHeight,
            height: plan.aslHeight,
            UAVHPR: flyPoint.UAVHPR
          });
        }
      }
    }
  } else if (mode === 'relativeToStartPoint') {
    colorJson.value.heightMode.EGM96 = '#555658';
    colorJson.value.heightMode.relativeToStartPoint = '#2E90FA';
    colorJson.value.heightMode.aboveGroundLevel = '#555658';
    templateJsons.value.Folder.wpml_waylineCoordinateSysParam.wpml_heightMode = 'relativeToStartPoint';
    heightModeImg.value = new URL('@/assets/plan/alt.svg', import.meta.url).href;
    heightTooltip.value = '相对起飞点高度（ALT）：航点高度值相对<br/>起飞点的高度保持不变。';
    const preHeight = heights.value.aslHeight; //之前的绝对高度

    //计算当前高度模式下，几个高度值
    heights.value.aslHeight =
      heights.value.curHeight + templateJsons.value.wpml_missionConfig.wpml_takeOffRefPointAGLHeight; //绝对高度
    heights.value.altHeight = heights.value.curHeight; //相对起飞点高度
    heights.value.aglHeight = heights.value.curHeight; //相对地形高度,暂赋予起飞点地形高度

    if (plan) {
      plan.heightModel = 'relativeToStartPoint';
      const h = heights.value.aslHeight - preHeight; //如果是相对高度模式，则为0，如果是相对高度切换过来的，则高度应该变大，加了起飞点地形高度的值，值为正数
      plan.aslHeight = heights.value.curHeight + templateJsons.value.wpml_missionConfig.wpml_takeOffRefPointAGLHeight;
      plan.globalHeight = heights.value.curHeight; //设置高度
      if (plan.planPointJson.length > 0) {
        const flyPoint = plan.refreshPoint(wayPointStore.currentPointIndex.value || 0, h, true);
        if (plan.curPlanPointJson != null) {
          const planPoint = plan.selectPlanPoint(plan.curPlanPointJson.index);
          props.wrjInfoSet(planPoint);
        } else {
          props.wrjInfoSet({
            lon: flyPoint.lon,
            lat: flyPoint.lat,
            startHeight: flyPoint.startHeight,
            terrainHeight: flyPoint.terrainHeight,
            height: plan.aslHeight,
            UAVHPR: flyPoint.UAVHPR
          });
        }
      }
    }
  } else if (mode === 'aboveGroundLevel') {
    //司空2才支持
    colorJson.value.heightMode.EGM96 = '#555658';
    colorJson.value.heightMode.relativeToStartPoint = '#555658';
    colorJson.value.heightMode.aboveGroundLevel = '#2E90FA';
    templateJsons.value.Folder.wpml_waylineCoordinateSysParam.wpml_heightMode = 'aboveGroundLevel';
    heightModeImg.value = new URL('@/assets/plan/agl.svg', import.meta.url).href;
    heightTooltip.value = '相对地形的高度（AGL）：航点高度值相对<br/>地形/模型高度保持不变。';

    const preHeight = heights.value.aslHeight; //之前的绝对高度
    //计算当前高度模式下，几个高度值
    heights.value.aslHeight =
      heights.value.curHeight + templateJsons.value.wpml_missionConfig.wpml_takeOffRefPointAGLHeight; //绝对高度
    heights.value.altHeight = heights.value.curHeight; //相对起飞点高度
    heights.value.aglHeight = heights.value.curHeight; //相对地形高度,暂赋予起飞点地形高度

    if (plan) {
      const h = heights.value.aslHeight - preHeight; //如果是相对高度模式，则为0，如果是相对高度切换过来的，则高度应该变大，加了起飞点地形高度的值，值为正数
      plan.aslHeight = heights.value.curHeight + templateJsons.value.wpml_missionConfig.wpml_takeOffRefPointAGLHeight;
      plan.globalHeight = heights.value.curHeight; //设置高度
      if (plan.planPointJson.length > 0) {
        const flyPoint = plan.refreshPoint(wayPointStore.currentPointIndex.value || 0, h, true);
        if (plan.curPlanPointJson != null) {
          const planPoint = plan.selectPlanPoint(plan.curPlanPointJson.index);
          props.wrjInfoSet(planPoint);
        } else {
          props.wrjInfoSet({
            lon: flyPoint.lon,
            lat: flyPoint.lat,
            startHeight: flyPoint.startHeight,
            terrainHeight: flyPoint.terrainHeight,
            height: plan.aslHeight,
            UAVHPR: flyPoint.UAVHPR
          });
        }
      }
    }
  }
  editTrackerStore.dataTracker.markAsModified();
  //#region 重新设置无人机和视锥体
  let modelInfo = wayPointStore?.getCurrentModelInfo();
  let newModelPosition = toCartesian3([modelInfo.longitude, modelInfo.latitude, plan.aslHeight]);
  plan?.model && plan?.model?.setPosition(newModelPosition);
  // 设置修改视锥体
  cameraFrust &&
    updateAllFrustum({
      position: newModelPosition,
      heading: modelInfo.heading,
      pitch: modelInfo.pitch,
      roll: modelInfo.roll
    });
  //#endregion
}

/**
 * 设置安全起飞高度
 * @param {*} height
 */
function setSafelyHeight(height) {
  const h = templateJsons.value.wpml_missionConfig.wpml_takeOffSecurityHeight;

  templateJsons.value.wpml_missionConfig.wpml_takeOffSecurityHeight = height + h;
  if (templateJsons.value.wpml_missionConfig.wpml_takeOffSecurityHeight < 2) {
    templateJsons.value.wpml_missionConfig.wpml_takeOffSecurityHeight = 2;
  } else if (templateJsons.value.wpml_missionConfig.wpml_takeOffSecurityHeight > 1500) {
    templateJsons.value.wpml_missionConfig.wpml_takeOffSecurityHeight = 1500;
  }
  if (plan && plan.planPointJson.length > 0) {
    let flyPoint = plan.planPointJson[0];
    flyPoint = plan.setFlyPoint(
      flyPoint.lon,
      flyPoint.lat,
      flyPoint.startHeight,
      templateJsons.value.wpml_missionConfig.wpml_flyToWaylineMode,
      templateJsons.value.wpml_missionConfig.wpml_takeOffSecurityHeight
    );
    if (plan.curPlanPointJson != null) {
      const planPoint = plan.selectPlanPoint(plan.curPlanPointJson.index);
      props.wrjInfoSet(planPoint);
    } else {
      props.wrjInfoSet({
        lon: flyPoint.lon,
        lat: flyPoint.lat,
        startHeight: flyPoint.startHeight,
        terrainHeight: flyPoint.terrainHeight,
        height: plan.aslHeight,
        UAVHPR: flyPoint.UAVHPR
      });
    }
  }
  editTrackerStore.dataTracker.markAsModified();
}

/**
 * 设置高度
 * @param {*} height
 */
function setTakeOffRefPointAGLHeight(height) {
  //#region 设置飞机当下位置及姿态
  let cameraFrust = null;
  if (plan?.model) {
    const { latitude, longitude, height: model_Height } = plan.model.getPosition();
    cameraFrust = getFrustum('camera');
    wayPointStore?.setCurrentModelInfo({
      latitude,
      longitude,
      height: model_Height,
      heading: cameraFrust.heading ?? 0,
      pitch: cameraFrust.pitch ?? 0,
      roll: cameraFrust.roll
    });
  }

  //#endregion

  const h = heights.value.curHeight;
  heights.value.curHeight = height + h;
  if (templateJsons.value.Folder.wpml_waylineCoordinateSysParam.wpml_heightMode == 'EGM96') {
    if (heights.value.curHeight < -2000) {
      heights.value.curHeight = -2000;
    }
    //计算当前高度模式下，几个高度值
    heights.value.aslHeight = heights.value.curHeight; //绝对高度
    heights.value.altHeight =
      heights.value.aslHeight - templateJsons.value.wpml_missionConfig.wpml_takeOffRefPointAGLHeight; //相对起飞点高度
    heights.value.aglHeight =
      heights.value.aslHeight - templateJsons.value.wpml_missionConfig.wpml_takeOffRefPointAGLHeight; //相对地形高度
  } else if (templateJsons.value.Folder.wpml_waylineCoordinateSysParam.wpml_heightMode == 'relativeToStartPoint') {
    if (heights.value.curHeight < -1500) {
      heights.value.curHeight = -1500;
    } else if (heights.value.curHeight > 1500) {
      heights.value.curHeight = 1500;
    }

    heights.value.aslHeight =
      heights.value.curHeight + templateJsons.value.wpml_missionConfig.wpml_takeOffRefPointAGLHeight;
    heights.value.altHeight = heights.value.curHeight;
    heights.value.aglHeight = heights.value.curHeight;
  } else if (templateJsons.value.Folder.wpml_waylineCoordinateSysParam.wpml_heightMode == 'aboveGroundLevel') {
    if (heights.value.curHeight < 0) {
      heights.value.curHeight = 0;
    } else if (heights.value.curHeight > 1500) {
      heights.value.curHeight = 1500;
    }

    heights.value.aslHeight =
      heights.value.curHeight + templateJsons.value.wpml_missionConfig.wpml_takeOffRefPointAGLHeight;
    heights.value.altHeight = heights.value.curHeight;
    heights.value.aglHeight = heights.value.curHeight;
  }
  if (plan) {
    plan.aslHeight = heights.value.aslHeight; //绝对高度
    plan.globalHeight = heights.value.curHeight; //设置高度
  }

  //全局航线高度（相对起飞点高度）
  templateJsons.value.Folder.wpml_globalHeight = heights.value.curHeight;
  //设置返航高度
  //templateJsons.value.wpml_missionConfig.wpml_globalRTHHeight = heights.value.curHeight;
  if (plan) {
    // 获取当前被选中的节点
    const flyPoint = plan.refreshPoint(wayPointStore.currentPointIndex.value || 0, height);
    if (plan.curPlanPointJson != null) {
      const planPointJson = plan.selectPlanPoint(plan.curPlanPointJson.index);
      //#region 这里更新动作视锥体
      const position = planPointJson.position ?? null;
      const { heading, pitch, roll } = planPointJson.UAVHPR ?? {};
      const opt = {
        position: position,
        heading,
        pitch,
        roll
      };
      updateActionFrustum(opt);
      //#endregion

      props.wrjInfoSet(planPointJson);
    } else {
      props.wrjInfoSet({
        lon: flyPoint.lon,
        lat: flyPoint.lat,
        startHeight: flyPoint.startHeight,
        terrainHeight: flyPoint.terrainHeight,
        height: plan.aslHeight,
        UAVHPR: flyPoint.UAVHPR
      });
    }

    //#region 重新设置无人机和视锥体
    let modelInfo = wayPointStore?.getCurrentModelInfo();
    let newModelPosition = toCartesian3([modelInfo.longitude, modelInfo.latitude, plan.aslHeight]);
    plan?.model && plan?.model?.setPosition(newModelPosition);
    // 设置修改视锥体
    cameraFrust &&
      updateAllFrustum({
        position: newModelPosition,
        heading: modelInfo.heading,
        pitch: modelInfo.pitch,
        roll: modelInfo.roll
      });

    //#endregion
  }
  editTrackerStore.dataTracker.markAsModified();
}

/**
 * 全局航线过渡速度，起飞速度
 * @param {*} speed 速度增加量
 */
function setGlobalTransitionalSpeed(speed) {
  const s = templateJsons.value.wpml_missionConfig.wpml_globalTransitionalSpeed;
  if (s == 20 && speed > 0) return;
  if (s == 1 && speed < 0) return;
  templateJsons.value.wpml_missionConfig.wpml_globalTransitionalSpeed = s + speed;
  editTrackerStore.dataTracker.markAsModified();
}

/**
 * 设置全局航线飞行速度
 * @param {*} speed 速度增加量
 */
function setAutoFlightSpeed(speed) {
  const s = templateJsons.value.Folder.wpml_autoFlightSpeed;
  if (s == 20 && speed > 0) return;
  if (s == 1 && speed < 0) return;
  templateJsons.value.Folder.wpml_autoFlightSpeed = s + speed;
  if (plan) {
    plan.wrjSpeed = templateJsons.value.Folder.wpml_autoFlightSpeed;
    //更新选中航点
    if (plan.curPlanPointJson != null) {
      plan.setPlacemark(plan.curPlanPointJson.index, {
        lon: null,
        lat: null,
        wpml_ellipsoidHeight: null,
        wpml_height: null,
        wpml_waypointSpeed: templateJsons.value.Folder.wpml_autoFlightSpeed,
        wpml_waypointHeadingMode: null,
        wpml_waypointTurnMode: null
      });
    }
  }
  editTrackerStore.dataTracker.markAsModified();
}

/**
 * 更新航点信息
 */
function updatePlacemark() {
  //更新选中航点
  if (plan && plan.curPlanPointJson != null) {
    plan.setPlacemark(plan.curPlanPointJson.index, {
      lon: null,
      lat: null,
      wpml_ellipsoidHeight: null,
      wpml_height: null,
      wpml_waypointSpeed: null, 
      wpml_waypointHeadingMode: templateJsons.value.Folder.wpml_globalWaypointHeadingParam.wpml_waypointHeadingMode,
      wpml_waypointTurnMode: templateJsons.value.Folder.wpml_globalWaypointTurnMode
    });
    editTrackerStore.dataTracker.markAsModified();
  }
}
 

/**
 * 批量更新航点信息 飞行器偏航角模式 是全局的 并非是某个点的设置
 */
 function batchUpdateWaypointTurnMode() { 
  //更新选中航点
  if (plan && plan.placemarkJson != null) { 
    plan.placemarkJson.forEach((obj,index) => {
      plan.setPlacemark(index, {
      lon: null,
      lat: null,
      wpml_ellipsoidHeight: null,
      wpml_height: null,
      wpml_waypointSpeed: null, 
      wpml_waypointHeadingMode: templateJsons.value.Folder.wpml_globalWaypointHeadingParam.wpml_waypointHeadingMode,
      wpml_waypointTurnMode: templateJsons.value.Folder.wpml_globalWaypointTurnMode
    });
    }); 
    editTrackerStore.dataTracker.markAsModified();
  }
}

function updateTrackerStatue() {
  editTrackerStore.dataTracker.markAsModified();
}

/**
 * 设置当前的操作工具
 * @param {*} tool
 */
function setTool(tool) {
  if (tool == 'add') {
    if (plan.flyStartPoint != null) {
      plan.setTool(tool);
    } else { 
      ElMessage.warning('请先设置起飞点')
    }
  } else {
    if (tool == 'start') {
      setMapToolTips('点击地图设置参考起飞点')
    }
    plan.setTool(tool);
  }
}

/**
 * 返回templateJsons
 */
function getTemplateJsons() {
  return templateJsons;
}

function setTemplateJsons(template) {
  templateJsons.value = template;
  const imageFormatArr = templateJsons.value.Folder.wpml_payloadParam.wpml_imageFormat.split(',');
  colorJson.value.imageFormat.visable = '#555658';
  colorJson.value.imageFormat.ir = '#555658';
  //拍照设置
  imageFormatArr.forEach((item, index) => {
    if (item == 'visable') {
      colorJson.value.imageFormat.visable = '#2E90FA';
    } else if (item == 'ir') {
      colorJson.value.imageFormat.ir = '#2E90FA';
    }
  });

  //安全高度
  templateJsons.value.wpml_missionConfig.wpml_flyToWaylineMode = template.wpml_missionConfig.wpml_flyToWaylineMode;
  setFlyToWaylineMode(templateJsons.value.wpml_missionConfig.wpml_flyToWaylineMode);
  templateJsons.value.wpml_missionConfig.wpml_takeOffSecurityHeight = parseInt(
    template.wpml_missionConfig.wpml_takeOffSecurityHeight
  );

  //航线高度
  heights.value.curHeight = parseInt(templateJsons.value.Folder.wpml_globalHeight);
  setHeightMode(template.Folder.wpml_waylineCoordinateSysParam.wpml_heightMode);

  //速度
  templateJsons.value.wpml_missionConfig.wpml_globalTransitionalSpeed = parseInt(
    template.wpml_missionConfig.wpml_globalTransitionalSpeed
  );
  templateJsons.value.Folder.wpml_autoFlightSpeed = parseInt(template.Folder.wpml_autoFlightSpeed);

  //航点类型
  templateJsons.value.Folder.wpml_globalWaypointTurnMode = template.Folder.wpml_globalWaypointTurnMode;
 
  //飞行器偏航角模式
  templateJsons.value.Folder.wpml_globalWaypointHeadingParam.wpml_waypointHeadingMode =
    template.Folder.wpml_globalWaypointHeadingParam.wpml_waypointHeadingMode;
  //航点间云台俯仰角控制模式
  templateJsons.value.Folder.wpml_gimbalPitchMode = template.Folder.wpml_gimbalPitchMode;
  //完成动作
  templateJsons.value.wpml_missionConfig.wpml_finishAction = template.wpml_missionConfig.wpml_finishAction; 
  isIncidentType.value = planInfoStore.getCurPlanJobType() === 1 ? true :false; 
}

onMounted(() => {
  imageFormatArr = ['visable', 'ir'];
  templateJsons.value.Folder.wpml_payloadParam.wpml_imageFormat = 'visable,ir'; 
  isIncidentType.value = planInfoStore.getCurPlanJobType() === 1 ? true :false; 
  setTimeout(() => {
    editTrackerStore.dataTracker.reset();
  }, 500);
});

onUnmounted(() => {
  isIncidentType.value = false;
  planInfoStore.setCurPlanJobType(0);
});
</script>
<style lang="scss">
@import '../../../styles/plan/plan.scss';
 
/* 滚动条样式 -----*/
/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 8px !important;
}
/* 滚动槽 */
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(190, 92, 92, 0.2);
  color: #175192;
  border-radius: 2px;
}

// 上箭头
// ::-webkit-scrollbar-button:start {
//   background-image: url('../../../assets/up-arrow.png');
//   background-size: 14px !important;
//   background-repeat: no-repeat;
//   background-position: center center;
// }
// ::-webkit-scrollbar-button:end {
//   background-image: url('../../../assets/down-arrow.png');
//   background-repeat: no-repeat;
//   background-size: 14px !important;
//   background-position: center center;
// }
/* 滚动条滑块（里面小方块） */
::-webkit-scrollbar-thumb {
  border-radius: 2px;
  width: 12px !important;
  background: #175192 !important;
  -webkit-box-shadow: inset 0 0 6px #175192 !important;
}
/* ---------*/
</style>
