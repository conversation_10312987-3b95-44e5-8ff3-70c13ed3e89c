import { onMounted, onUnmounted } from 'vue';
import ConnectWebSocket from '@/utils/websocket/broadcastWs/index';

import { useUserStoreHook } from '@/store/modules/user';

/**
 * 接收一个message函数
 * @param messageHandler
 * 
 * 用法
  import { useConnectWebSocket } from '@/hooks/useConnectWebSocket'
  import { EBizCode } from '@/utils/constants';
  useConnectWebSocket(payload => {
    if (!payload) {
        return
    }
    switch (payload.biz_code) {
        case EBizCode.DeviceUpgrade: {
        window.$bus.emit('deviceUpgrade', payload)
        break
        }
        case EBizCode.DeviceLogUploadProgress: {
        window.$bus.emit('deviceLogUploadProgress', payload)
        break
        }
    }
  })
 */
export function useConnectWebSocket(messageHandler) {
  const userStore = useUserStoreHook();

  let baseUrl = '';
  if (import.meta.env.VITE_APP_NODE_ENV === 'development') {
    baseUrl = import.meta.env.VITE_WS_BASE_URL;
  } else {
    baseUrl = location.host + import.meta.env.VITE_APP_BASE_API + '/';
  }
  const wsRequestUrl = `ws://${baseUrl}api/v1/ws?x-auth-token=${userStore.token}`;
  const webSocket = new ConnectWebSocket(wsRequestUrl);

  onMounted(() => {
    webSocket?.registerMessageHandler(messageHandler);
    webSocket?.initSocket();
  });

  onUnmounted(() => {
    webSocket?.close();
  });
}
