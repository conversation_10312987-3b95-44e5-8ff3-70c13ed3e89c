<!--机场管理-->
<script>
export default {
  name: 'Scheme'
};
</script>

<script setup>
import { reactive } from 'vue';
import SchemeEditDialog from './components/schemeEditDialog.vue';
import { getSchemeList } from '@/api/devices/maintenance';

const loading = ref(false);
const total = ref(0);
const editDialogRef = ref(null);
const queryParams = reactive({
  page_num: 1,
  page_size: 10,
  scheme_name: '',
});
const dataList = ref([]);
const editDialog = reactive({
  visible: false
});
let formData = reactive({});
/**
 * 查询
 */
function handleQuery() {
  // Calculate the new total pages based on the new page size
  const newTotalPages = Math.ceil(total.value / queryParams.page_size);
  
  // If current page is greater than new total pages, adjust to the last page
  if (queryParams.page_num > newTotalPages) {
    queryParams.page_num = newTotalPages || 1;
  }
  getSchemeList({
    ...queryParams,
  }).then(data => {
    const { records } = data;
    dataList.value = records || [];
    total.value = data.total;
  });
}
function handleSearch() {
  queryParams.begin_time = queryParams.range?.length === 2 ? `${queryParams.range[0]} 00:00:00` : '',
  queryParams.end_time = queryParams.range?.length === 2 ? `${queryParams.range[0]} 00:00:00` : '',
  queryParams.page_num = 1;
  // delete queryParams.range;
  handleQuery();
}
/**
 * 重置查询
 */
function resetQuery() {
  queryParams.scheme_name = ''
  queryParams.page_num = 1,
  queryParams.page_size = 10,
  handleQuery();
}

/**
 * 打开表单弹窗
 */
function addAndEditScheme(row) {
  editDialog.visible = true;
  Object.keys(formData).map(key => {
    delete formData[key];
  });
	editDialog.title = '新增维保方案';
  if (row) {
    editDialog.title = '编辑维保方案';
    Object.assign(formData, { ...row });
  }
}

onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped>
.input-serach {
  width: 200px;
}
</style>
<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" @submit.native.prevent>
          <el-form-item label="" prop="scheme_name">
            <el-input
              class="input-serach"
              v-model="queryParams.scheme_name"
              placeholder="请输入方案名称"
              clearable
              @keyup.enter="handleSearch"
              maxlength="50"
            />
          </el-form-item>
        </el-form>
      </div>
      <div class="search-btn">
        <el-button type="primary" @click="handleSearch()"
          ><i-ep-search />搜索</el-button
        >
        <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
      </div>
    </div>
    <el-card shadow="never">
			<template #header>
        <el-button type="primary" @click="addAndEditScheme()">新增维保方案</el-button>
      </template>
      <el-table highlight-current-row :data="dataList" v-loading="loading" stripe height="540">
        <el-table-column label="序号" width="80">
          <template #default="scope">
            <span>{{ scope.$index + 1 + queryParams.page_size * (queryParams.page_num - 1) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="方案名称" prop="scheme_name" show-overflow-tooltip />
        <el-table-column label="创建人" prop="creator" show-overflow-tooltip />
        <el-table-column fixed="right" label="操作" width="250">
          <template #default="scope">
            <el-button type="primary" link @click.stop="addAndEditScheme(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.page_num"
        v-model:limit="queryParams.page_size"
        @pagination="handleQuery"
      />
    </el-card>
		<SchemeEditDialog
      ref="editDialogRef"
      v-model:visible="editDialog.visible"
      :title="editDialog.title"
      :form-data="formData"
      @submit="resetQuery"
    />
  </div>
</template>
<style scoped lang="scss">
.flex-center {
  display: flex;
  align-items: center;
  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #4caf51;
  }
  .unstatus {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: red;
  }
}
.search {
  display: flex;
  align-items: center;
  padding: 24px;
  height: 64px;
  .search-form {
    flex: 1;
    padding-top: 16px;
  }
}
</style>
