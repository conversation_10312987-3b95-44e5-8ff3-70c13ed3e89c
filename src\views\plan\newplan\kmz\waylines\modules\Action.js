import { generateKey } from '@/utils';
import { ACTION_TRIGGER_TYPE } from '@/utils/constants';
/**
 * 动作类
 */
class Action {
  /**
   * @param {Object} options - 动作的选项
   * @param {number} options.uuid - 动作的唯一标识符
   * @param {number} options.actionId - 动作的ID
   * @param {string} options.actionActuatorFunc - 动作的类型
   * @param {Map} options.actionActuatorFuncParam - 动作相关的参数，使用 Map 集合存储
   */
  constructor(options) {
    this.uuid = options.uuid || options?.actionUuid || generateKey();
    this.wpml_actionId = options.actionId || 0;
    this.wpml_actionActuatorFunc = options.actionActuatorFunc || '';
    this.wpml_actionActuatorFuncParam = options.actionActuatorFuncParam || new Map();
    this.type = options.type || ACTION_TRIGGER_TYPE.reachPoint; // 默认为正常的动作类型, 有些属于非正常的包括停止间隔动作等
    this.trigger = options.trigger || ACTION_TRIGGER_TYPE.reachPoint; // 默认为正常的动作类型, 有些属于非正常的包括停止间隔动作等
  }
  /**
   * 设置动作id
   */
  getActionId() {
    return this.wpml_actionId;
  }

  /**
   * 设置动作名称
   * @param {number} id
   */
  setActionId(id) {
    this.wpml_actionId = id;
  }
  /**
   * 设置动作名称
   * @param {string} funName
   */
  setActionActuatorFunc(funName) {
    this.wpml_actionActuatorFunc = funName;
  }

  // 添加动作相关参数
  addActionActuatorFuncParam(key, value) {
    if (!key || !value) {
      return;
    }
    this.wpml_actionActuatorFuncParam.set(key, value);
  }

  // 动作排序 需要根据实际来写
  sortActions() {
    this.actions.sort((a, b) => a.actionOrder - b.actionOrder);
  }
}

export { Action };
