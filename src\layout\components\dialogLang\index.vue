<template>
  <el-dialog
    v-if="dialogVisible"
    :title="$t('Language Setting')"
    v-model="dialogVisible"
    width="400px"
    :append-to-body="true"
    :close-on-click-modal="false"
    :before-close="dialogCancel"
  >
    <LangSelect />
  </el-dialog>
</template>

<script setup>
import useDialog from '@/hooks/useDialog';
import LangSelect from '../LangSelect/index.vue';

const { dialogVisible, dialogOpen, dialogCancel } = useDialog();

const handleOpen = () => {
  dialogOpen();
};
defineExpose({ handleOpen });
</script>
