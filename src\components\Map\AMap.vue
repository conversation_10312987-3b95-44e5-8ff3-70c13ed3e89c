<template>
  <div v-loading="map_loading" class="mapDiv">
    <div :id="mapId" class="mapDiv" />
  </div>
</template>

<script>
import _ from 'lodash';
import AMapLoader from '@amap/amap-jsapi-loader';

import {
  AMAP_KEY,
  AMAP_VERSION,
  AMAP_CENTER,
  AMAP_CITY_TYPE,
  AMAP_MAP_TYPE
} from '@/config/Map';

import i18n from '@/lang';

import markPoint from '@/assets/gis/gis_point.png';
import clusterIcon from '@/assets/gis/gis_bg.png';
import singleMarker from '@/assets/gis/singleMarker.png';
import placeMarker from '@/assets/gis/placeMarker.png';

window._AMapSecurityConfig = {
  securityJsCode: '3c8d10a7afafcff4c2daf65a0d8e66bc'
};
let map = null;
let AMapInstant = null;
let single_marker = null;
let place_maker = null;
let clustererObject = null;
let markList = [];

export default {
  name: 'AMap',
  props: {
    // 开启地图点击查询功能
    clickFun: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 此处不声明 map 对象，可以直接使用 this.map赋值或者采用非响应式的普通对象来存储。
      map_loading: false,
      geocode: null // 逆地理
      // markList: [], // 聚合数组
      // clustererObject: null // 聚合对象
    };
  },
  computed: {
    mapId() {
      return `map${_.random(10000, 99999)}`;
    }
  },
  mounted() {
    this.init();
  },
  beforeUnmount() {
    this.removeMapClick();
  },
  methods: {
    // 初始化地图
    async init() {
      const language =
        import.meta.env.VITE_APP_AREA_VERSION !== 'CHINA'
          ? 'en'
          : localStorage.getItem('language') || 'zh';

      try {
        const AMap = await AMapLoader.load({
          key: AMAP_KEY,
          version: AMAP_VERSION,
          plugins: []
        });
        AMapInstant = AMap;
        map = new AMap.Map(this.mapId, {
          zoom: 11,
          center: AMAP_CENTER,
          lang: language
        });
        // 设置地图类型
        AMap.plugin('AMap.Geocoder', () => {
          this.geocode = new AMap.Geocoder({
            city: AMAP_CITY_TYPE,
            radius: 1000
          });
        });

        if (this.clickFun) {
          this.initMapClick();
        }

        this.$emit('initMap', map);

        this.map_loading = false;
      } catch (error) {
        console.log(error);
      }
    },
    // 注册地图点击事件
    initMapClick() {
      map.on('click', this.mapClick);
    },
    // 注销地图点击事件
    removeMapClick() {
      if (map) {
        map.off('click', this.mapClick);
      }
    },
    // 设置图层类型
    setMapType(type) {
      map.getLayers().forEach(layer => {
        map.removeOverLay(layer);
      });

      const arr = this.layer_config[type];

      if (arr) {
        _.forEach(arr, layer => {
          map.addLayer(layer);
        });
      } else {
        map.setMapType(window[type || AMAP_MAP_TYPE]);
      }
    },
    // 地图点击事件(逆地理)
    mapClick(e) {
      const lnglat = e.lnglat;
      const siteLng = lnglat.lng;
      const siteLat = lnglat.lat;

      this.addSingleMarker(siteLng, siteLat);
    },
    // 逆地理查询
    getLocation(siteLng, siteLat) {
      this.map_loading = true;
      this.geocode.getAddress([siteLng, siteLat], (status, result) => {
        let detailedAddress = '';

        if (status === 'complete' && result.regeocode) {
          detailedAddress = result.regeocode.formattedAddress;
        } else {
          ElMessage.error(
            i18n.global.t('page.dialog.actionFb.lngandlatQueryFailed')
          );
        }
        this.$emit('mapClickInfo', {
          detailedAddress,
          siteLng,
          siteLat
        });
        this.map_loading = false;
      });
    },
    // 添加单个默认覆盖物(点击地图)
    addSingleMarker(siteLng, siteLat, zoom) {
      if (single_marker) {
        map.remove(single_marker);
      }

      // 创建标注对象
      single_marker = new AMapInstant.Marker({
        icon: new AMapInstant.Icon({
          size: new AMapInstant.Size(32, 32), // 设置图标大小
          image: singleMarker, // 图标的图片地址
          imageSize: new AMapInstant.Size(32, 32) // 图标显示的大小
        }),
        position: [siteLng, siteLat],
        offset: new AMapInstant.Pixel(-16, -16) // 偏移量
      });
      // 向地图上添加标注
      map.add(single_marker);
      map.setZoomAndCenter(zoom, [siteLng, siteLat]);
      this.getLocation(siteLng, siteLat);
    },
    // 添加覆盖物(地名查询)
    addPlaceMarker(siteLng, siteLat, zoom) {
      if (place_maker) map.remove(place_maker);
      // 创建标注对象
      place_maker = new AMapInstant.Marker({
        icon: new AMapInstant.Icon({
          size: new AMapInstant.Size(32, 32), // 设置图标大小
          image: placeMarker, // 图标的图片地址
          imageSize: new AMapInstant.Size(32, 32) // 图标显示的大小
        }),
        position: [siteLng, siteLat],
        offset: new AMapInstant.Pixel(-16, -32) // 偏移量
      });
      // 向地图上添加标注
      map.add(place_maker);
      map.setZoomAndCenter(zoom, [siteLng, siteLat]);
    },
    // 地图定点定位(预留经纬度定位方法)
    locationFun(siteLng, siteLat, zoom = 15) {
      map.setZoomAndCenter(zoom, [siteLng, siteLat]);
    },
    // 定制方法-批量点位并设置聚合
    async initSiteData(monitorPoints) {
      // 初始化所有数据
      if (clustererObject) {
        clustererObject.setMap(null);
        map.clearMap();
      }
      if (!monitorPoints || !monitorPoints.length) return;
      markList = [];
      // 预留可异步的遍历方法
      for (const item of monitorPoints.filter(
        obj => obj.deviceLng && obj.deviceLat
      )) {
        const marker = new AMapInstant.Marker({
          icon: new AMapInstant.Icon({
            size: new AMapInstant.Size(32, 32), // 设置图标大小
            image: markPoint, // 图标的图片地址
            imageSize: new AMapInstant.Size(32, 32) // 图标显示的大小
          }),
          position: [item.deviceLng, item.deviceLat],
          offset: new AMapInstant.Pixel(-16, -32) // 偏移量
        });
        // 添加自定义弹窗
        const infoWindow = new AMapInstant.InfoWindow({
          isCustom: true, // 使用自定义窗口
          content: `
              <div class="mark_info">
                <div class="mark_info_title">
                  <span>${this.$t('Device Name')}：${item.deviceName}</span>
                </div>
                <div class="mark_info_content">
                  <div class="item">${this.$t('Device Code')}: ${
            item.deviceCode
          }</div>
                  <div class="item">${this.$t('Installation Address')}: ${
            item.address
          }</div>
                </div>
              </div>`, // 设置窗口内容
          offset: new AMapInstant.Pixel(0, -35) // 窗口位置偏移
        });
        marker.on('mouseover', () => {
          infoWindow.open(map, marker.getPosition());
        });
        marker.on('mouseout', () => {
          infoWindow.close();
        });
        // 聚合数组
        markList.push(marker);
      }
      // 添加聚合组件
      map.plugin(['AMap.MarkerClusterer'], () => {
        const image = new Image();
        image.src = clusterIcon;
        image.onload = () => {
          // 创建聚合实例
          clustererObject = new AMapInstant.MarkerClusterer(
            map, // 地图实例
            markList, // 数组对象，数据中需包含经纬度信息字段
            {
              gridSize: 60, // 聚合的网格大小，单位：像素
              maxZoom: 15, // 最大缩放级别
              styles: [
                {
                  url: clusterIcon,
                  size: new AMapInstant.Size(image.width, image.height),
                  offset: new AMapInstant.Pixel(
                    image.width / -2,
                    image.height / -2
                  ), // 偏移量为负的宽高一半
                  textColor: '#ffff'
                }
              ]
            }
          );
          // 单独设置聚合的方法
          // clustererObject.setMarkers(markList)
        };
      });
      if (markList.length) {
        // 自适应缩放至能看到所有 marker 的位置
        map.setFitView(markList);
      }
    },
    // 定位当前位置
    geoLocation() {
      let geolocation = {};
      map.plugin('AMap.Geolocation', () => {
        geolocation = new AMapInstant.Geolocation({
          enableHighAccuracy: true, // 是否使用高精度定位，默认:true
          timeout: 10000, // 超过10秒后停止定位，默认：无穷大
          maximumAge: 0, // 定位结果缓存0毫秒，默认：0
          convert: true, // 自动偏移坐标，偏移后的坐标为高德坐标，默认：true
          showButton: true, // 显示定位按钮，默认：true
          buttonPosition: 'LB', // 定位按钮停靠位置，默认：'LB'，左下角
          buttonOffset: new AMapInstant.Pixel(10, 20), // 定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
          showMarker: false, // 定位成功后在定位到的位置显示点标记，默认：true
          showCircle: false, // 定位成功后用圆圈表示定位精度范围，默认：true
          panToLocation: true, // 定位成功后将定位到的位置作为地图中心点，默认：true
          zoomToAccuracy: true // 定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
        });
        map.addControl(geolocation);
        geolocation.getCurrentPosition();
        AMapInstant.event.addListener(geolocation, 'complete', this.onComplete); // 返回定位信息
      });
    },
    onComplete(data) {
      const { lng, lat } = data.position;
      this.addSingleMarker(lng, lat, 18);
    }
  }
};
</script>

<style lang="scss" scoped>
.mapDiv {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
