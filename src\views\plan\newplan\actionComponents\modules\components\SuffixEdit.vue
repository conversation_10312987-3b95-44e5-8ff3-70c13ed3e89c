<template>
  <div class="suffix-wrapper">
    <div class="content">
      <span class="left">文件后缀 </span>
      <div class="middle">
        <el-input v-show="isEdit" v-model="data" placeholder="" clearable />
        <div v-show="!isEdit">{{ originalTitle || data }}</div>
      </div>
      <div class="s-right">
        <div v-show="isEdit" class="action" @click="save">
          <el-image style="width: 20px; height: 20px" :src="saveIcon" />
        </div>
        <div v-show="!isEdit" class="action" @click="edit">
          <el-image style="width: 20px; height: 20px" :src="eidtIcon" />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'SuffixEdit'
};
</script>
<script setup>
import { ElInput } from 'element-plus';
import 'element-plus/dist/index.css';
import { onMounted, onUnmounted, ref, computed } from 'vue';
const saveIcon = new URL('@/assets/plan/bx-save.png', import.meta.url).href;
const eidtIcon = new URL('@/assets/plan/bx-edit.png', import.meta.url).href;
//#region 数据双向绑定
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
const data = computed({
  get: () => props.modelValue,
  set: value => {
    emits('update:modelValue', value);
  }
});
// 对外定义事件
const emits = defineEmits(['update:modelValue', 'changeHandle']); // 触发事件
const isEdit = ref(false);
const originalTitle = ref('');
const save = () => {
  originalTitle.value = data.value;
  isEdit.value = false;
  emits('changeHandle', data.value);
};

const edit = () => {
  isEdit.value = true;
};
//#endregion
onMounted(() => {
  originalTitle.value = data.value;
});
onUnmounted(() => {
  originalTitle.value = '';
});
</script>
<style lang="scss" scoped>
::v-deep.el-input {
  background-color: #313131;
  color: white;
}

::v-deep.el-input-number .el-input__inner {
  background-color: #11253e;
  color: white;
  font-size: 20px !important;
  text-align: center;
  color: #2d8cf0 !important;
  font-weight: 600;
}
::v-deep .el-input-numbert.is-disabled .el-input__wrapper {
  background-color: #11253e;
}

::v-deep.el-input-number .el-input__wrapper {
  background-color: #11253e;
}

::v-deep.el-input-number .el-input__wrapper {
  background-color: #11253e;
  box-shadow: 0 0 0 1px #cfcfcf8f inset;
}
.suffix-wrapper {
  background-color: #11253e !important;
  color: white;
  padding: 2px 0px;
  width: 100%;
  user-select: none;
  .content {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
    .left {
      display: flex;
      align-items: center;
      margin-right: 15px;
    }
    .middle {
      display: flex;
      align-items: center;
      flex: 1;
    }
    .s-right {
      display: flex;

      justify-content: center;
      align-items: center;
      .action {
        width: 35px;
        margin-left: 5px;
        height: 35px;
        background-color: #5d5f61d5;
        display: flex;
        justify-content: center;
        border-radius: 3px;
        align-items: center;
      }
    }
  }
}
</style>
