<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-input
          v-model="queryParams.featureName"
          placeholder="请输入图元名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleSearch"
        />
        <el-select
          v-model="queryParams.layerId"
          placeholder="所属图层"
          clearable
          style="width: 180px; margin-left: 10px"
          filterable
        >
          <el-option v-for="item in layerOptions" :key="item.id" :label="item.layerName" :value="item.id" />
        </el-select>
        <el-input
          v-model="queryParams.featureAddr"
          placeholder="请输入地址"
          clearable
          style="width: 200px; margin-left: 10px"
          @keyup.enter="handleSearch"
        />
        <el-input
          v-model="queryParams.contact"
          placeholder="请输入联系人"
          clearable
          style="width: 150px; margin-left: 10px"
          @keyup.enter="handleSearch"
        />
      </div>
      <div class="search-btn">
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button v-if="authorityShow('createFeature')" type="primary" @click="handleAdd">新增</el-button>
      </div>
    </div>
    <el-card shadow="never">
      <el-table
        :data="dataList"
        v-loading="loading"
        stripe
        height="630"
        style="width: 100%; margin-top: 10px"
        row-key="id"
      >
        <el-table-column prop="featureName" label="图元名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="layerName" label="所属图层" min-width="120" />
        <el-table-column prop="featureAddr" label="地址" min-width="200" show-overflow-tooltip />
        <el-table-column label="坐标" min-width="180" align="center">
          <template #default="scope"> {{ scope.row.longitude }}, {{ scope.row.latitude }} </template>
        </el-table-column>
        <el-table-column prop="contact" label="联系人" min-width="100" />
        <el-table-column prop="telephone" label="联系电话" min-width="120" />
        <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" width="160" align="center">
          <template #default="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="authorityShow('checkFeature') || authorityShow('editFeature') || authorityShow('deleteFeature')"
          label="操作"
          width="240"
          align="center"
          fixed="right"
        >
          <template #default="scope">
            <el-button v-if="authorityShow('checkFeature')" type="primary" link @click="handleView(scope.row)"
              >详情</el-button
            >
            <el-button v-if="authorityShow('editFeature')" type="primary" link @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button v-if="authorityShow('deleteFeature')" type="danger" link @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-content">
        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="handleSearch"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';
import { getFeatureList, deleteFeature } from '@/api/map/feature';
import { getAllLayers } from '@/api/map/layer';
import Pagination from '@/components/Pagination/index.vue';
import { authorityShow } from '@/utils/authority';
const router = useRouter();
const loading = ref(false);
const dataList = ref([]);
const total = ref(0);
const layerOptions = ref([]);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  featureName: '',
  layerId: null,
  featureAddr: '',
  contact: ''
});

/**
 * 格式化日期时间
 */
function formatDateTime(dateTime) {
  if (!dateTime) return '';
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

/**
 * 字段名转换：下划线转驼峰
 */
function toCamelCase(obj) {
  if (Array.isArray(obj)) {
    return obj.map(item => toCamelCase(item));
  } else if (obj !== null && typeof obj === 'object') {
    const newObj = {};
    Object.keys(obj).forEach(key => {
      const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
      newObj[camelKey] = toCamelCase(obj[key]);
    });
    return newObj;
  }
  return obj;
}

/**
 * 获取图层选项
 */
async function loadLayerOptions() {
  try {
    const result = await getAllLayers();
    if (result && result.data && Array.isArray(result.data)) {
      layerOptions.value = toCamelCase(result.data);
    } else if (result && Array.isArray(result)) {
      layerOptions.value = toCamelCase(result);
    }
  } catch (error) {
    console.error('获取图层选项失败:', error);
  }
}

/**
 * 查询图元列表
 */
async function handleQuery() {
  loading.value = true;
  try {
    const params = {
      page_num: queryParams.pageNum,
      page_size: queryParams.pageSize
    };

    // 添加查询条件
    if (queryParams.featureName) {
      params.feature_name = queryParams.featureName.trim();
    }
    if (queryParams.layerId) {
      params.layer_id = queryParams.layerId;
    }
    if (queryParams.featureAddr) {
      params.feature_addr = queryParams.featureAddr.trim();
    }
    if (queryParams.contact) {
      params.contact = queryParams.contact.trim();
    }

    const result = await getFeatureList(params);
    if (result && result.data && Array.isArray(result.data)) {
      // 转换字段名为驼峰格式
      dataList.value = toCamelCase(result.data);
      total.value = result.total || result.data.length;
    } else if (result && Array.isArray(result)) {
      // 兼容直接返回数组的情况
      dataList.value = toCamelCase(result);
      total.value = result.length;
    } else {
      dataList.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('获取图元列表失败:', error);
    ElMessage.error('获取图元列表失败');
    dataList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
}

/**
 * 搜索
 */
function handleSearch() {
  queryParams.pageNum = 1;
  handleQuery();
}

/**
 * 重置
 */
function handleReset() {
  queryParams.featureName = '';
  queryParams.layerId = null;
  queryParams.featureAddr = '';
  queryParams.contact = '';
  handleSearch();
}

/**
 * 新增
 */
function handleAdd() {
  router.push({
    path: '/feature-edit',
    query: { mode: 'add' }
  });
}

/**
 * 查看详情
 */
function handleView(row) {
  router.push({
    path: '/feature-edit',
    query: {
      mode: 'detail',
      id: row.id
    }
  });
}

/**
 * 编辑
 */
function handleEdit(row) {
  router.push({
    path: '/feature-edit',
    query: {
      mode: 'edit',
      id: row.id
    }
  });
}

/**
 * 删除
 */
async function handleDelete(row) {
  try {
    await ElMessageBox.confirm(`确定要删除图元"${row.featureName}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    await deleteFeature(row.id);
    ElMessage.success('删除成功');
    handleQuery();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除图元失败:', error);
      ElMessage.error('删除失败');
    }
  }
}

// 初始化
onMounted(async () => {
  await loadLayerOptions();
  handleQuery();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 16px 20px;
  .search {
    display: flex;
    align-items: center;
    padding: 24px;
    height: 64px;
    .search-form {
      flex: 1;
      color: #fff;
    }
    .search-btn {
      margin-left: 16px;
    }
  }
  .app-content {
    width: 100%;
    max-height: calc(100vh - 154px);
    padding: 16px 24px;
    background: #fff;
    overflow: auto;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
    border-radius: 4px;
    &::-webkit-scrollbar {
      width: 10px !important;
      height: 10px !important;
      background: #e4e7ec;
      border-radius: 4px;
    }
    &::-webkit-scrollbar-thumb {
      width: 10px !important;
      min-height: 20px !important;
      background: #b7d9fd !important;
      border-radius: 4px !important;
    }
    .btn-box {
      margin-bottom: 16px;
    }
    .textHidden {
      width: 180px;
      height: 20px;
      line-height: 20px;
      text-align: left;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
}
.pagination-content {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;

  &.hidden {
    display: none;
  }
}
</style>
