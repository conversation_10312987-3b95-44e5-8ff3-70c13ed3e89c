/**
* 按钮默认样式
*/
.btn {
  margin: 3px 3px 3px 3px;

  cursor: pointer;
  height: 30px;
  border-radius: 3px;
  font-size: 14px;
  color: #fff;
  border: 2px solid #353436;
  background-color: rgb(53, 52, 54);
  /* border: 2px solid #2d8cf0;
    background-color:#2d8cf0; */
}

.btn-blue {
  background-color: #2d8cf0;
  border: 2px solid #2d8cf0;
}

.btn:hover {
  /* -webkit-box-shadow: inset 0 1px 1px rgba(214, 15, 15, 0.075), 0 0 18px #5cadff;
    box-shadow: inset 0 1px 1px rgba(223, 33, 33, 0.075), 0 0 18px #5cadff; */
  border: 2px solid #5d5f61;
  background-color: #5d5f61;
}

.img-content-box {
  padding: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.p-content-box {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.p-content {
}
.center {
  // padding: 10px 5px;
  text-align: center;
  width: 60px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-button:hover {
  background-color: #5d5f61;
}
.action-button {
  width: 32px;
  height: 32px;
  background-color: #3c3c3c;
  border-radius: 5px;
  color: #fff;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.li-plan {
  display: flex;
  justify-content: space-between;
}

.li-plan:hover {
  background-color: #4b9bf1;
  border-radius: 5px;
}

.msg {
  background-color: #101010;
  color: white;
  position: absolute;
  border: 2px solid #707070;
}

.msg-title {
  height: 40px;
  text-align: center;
  border: 2px solid #2e90fa;
  padding-top: 7px;
  background-color: #2e90fa;
}

.msg-content {
  background-color: #11253e;
  color: white;
  margin: 10px 5px;
  padding: 10px 5px;
  border-radius: 5px;
}
.pad10 {
  padding: 10px 15px;
}

//中间指北针
.flight-controller {
  pointer-events: auto;
  display: flex;
  justify-content: center;
}

// .flight-controller {
//   pointer-events: auto;
//   display: flex;
//   position: absolute;
//   justify-content: center;
//   bottom: 20px;
//   left: 40%;
// }

.controller-horizontal {
  margin-right: 24px;
}

.controller-area {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.text-wrapper {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  line-height: 20px;
  line-height: 12px;
  font-weight: 600;
}

.speed-text {
  width: 105px;
}

.text-wrapper .other-text {
  margin-top: 4px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  color: #fff;
}

.map-text-shadow {
  text-shadow: 0 0 4px rgba(0, 0, 0, 0.5), -1px -1px 0 rgba(0, 0, 0, 0.5), 1px -1px 0 rgba(0, 0, 0, 0.5),
    -1px 1px 0 rgba(0, 0, 0, 0.5), 1px 1px 0 rgba(0, 0, 0, 0.5);
}

.attitude-view {
  display: flex;
  align-items: center;
}

.attitude-view .pitch-editor {
  flex-shrink: 0;
}

.pitch-editor {
  padding-left: 7px;
  padding-right: 5px;
  position: relative;
}

.attitude-view .yaw-editor.with-button {
  margin-left: 12px;
  margin-right: 12px;
}

.attitude-view .yaw-editor {
  margin-left: 6px;
  flex-shrink: 0;
}

.attitude-view-md .yaw-editor {
  width: 150px;
  height: 150px;
}

.yaw-editor {
  position: relative;
  width: 94px;
  height: 94px;
}

// .attitude-view.with-background .yaw-limit {
//   background: radial-gradient(
//     circle,
//     transparent 46.5%,
//     rgba(0, 0, 0, 0.5) 0,
//     rgba(0, 0, 0, 0.5) 70%,
//     transparent 0
//   );
// }
.yaw-limit {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.yaw-compass {
  overflow: hidden;
}

.yaw-drone {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.attitude-view-md .yaw-gimbal {
  padding: 10px;
}

.yaw-gimbal {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  padding: 5px;
}

.attitude-view .angle-number {
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
  color: #00ee8b;
  display: flex;
}

.angle-number.drone {
  top: 2px;
  left: 50%;
  transform: translate(-50%, -100%);
  display: flex;
  flex-direction: column-reverse;
  align-items: center;
}

.angle-number.drone:before {
  display: block;
  content: '';
  width: 3px;
  height: 10px;
  background: #00ee8b;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.5), -1px -1px 0 rgba(0, 0, 0, 0.5), 1px -1px 0 rgba(0, 0, 0, 0.5),
    -1px 1px 0 rgba(0, 0, 0, 0.5), 1px 1px 0 rgba(0, 0, 0, 0.5);
}

.angle-number {
  position: absolute;
}

.operation-mode {
  left: 326px;
  top: 0;
}

.operation {
  width: 24px;
  height: 24px;
  border-radius: 2px;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.obstacle-info {
  padding-left: 5px;
  position: relative;
}

.operation.active {
  color: #2d8cf0;
}

.text-wrapper.height-text {
  right: 0;
  top: calc(50% - 2px);
}

.controller-vertical {
  margin-left: 64px;
  margin-top: -4px;
}

.controller-area {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.hotkeys-card {
  background: rgba(0, 0, 0, 0.65);
  border-radius: 8px;
  display: flex;
  padding: 4px 8px 8px;
}

.main-text {
  display: flex;
  align-items: center;
  color: #00ee8b;
}

.main-unit {
  display: flex;
  flex-direction: column;
  text-align: right;
  margin-left: 4px;
  justify-content: center;
}

.main-status {
  width: 100px;
  text-align: center;
  font-size: 16px;
  line-height: 24px;
}

.main-number {
  font-size: 26px;
  line-height: 24px;
}

.obstacle-bar {
  width: 7px;
  height: 80px;
  background: hsla(0, 0%, 100%, 0.2);
  box-shadow: 0 0 0 0.4px rgba(0, 0, 0, 0.6);
}

.pitch-bar {
  width: 7px;
  height: 111px;
  color: hsla(0, 0%, 100%, 0.2);
  box-shadow: 0 0 0 0.4px rgba(0, 0, 0, 0.6);
}

.marker {
  width: 7px;
  height: 2px;
  background-color: #fff;
  position: absolute;
  transform: translateY(-50%);
  box-shadow: 0 0 0 0.4px rgba(0, 0, 0, 0.6);
}

.marker.zero-marker {
  width: auto;
  right: 0;
  left: 7px;
  top: 50%;
}

.agl-number {
  position: absolute;
  left: 50%;
  bottom: -2px;
  transform: translate(-50%, 100%);
  white-space: nowrap;
  color: #fff;
  font-size: 12px;
  line-height: 20px;
  font-weight: 600;
}

.gimbal-icon {
  position: absolute;
  transform: rotate(90deg) translateX(-50%);

  display: block;
}

.gimbal-icon2 {
  position: absolute;
  transform: rotate(90deg) translateX(-50%);
  left: -6px;
  display: block;
}

.gimbal-icon-inner {
  margin: 0.5px 1px 1px;
  display: block;
  width: 1em;
  height: 1em;
  background-color: #1fa3f6;
  position: relative;
}

.gimbal-icon-inner:before {
  content: '';
  display: block;
  width: 0;
  height: 0;
  border-left: 0.5em solid transparent;
  border-right: 0.5em solid transparent;
  border-bottom: 0.5em solid #1fa3f6;
  position: absolute;
  bottom: 100%;
}

.hotkeys-card .hotkey-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 43px;
  justify-content: center;
}

.hotkeys-card.bottom .hotkey-item {
  flex-direction: column-reverse;
}

.msg-content-wrap {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 10px;
  margin: 10px 0px;
}

.msg-content-title {
  margin: 0px 0px 10px 10px;
}

// 组件的样式穿透处理
::v-deep .el-button.btn1 {
  margin: 3px 3px 3px 3px;
  cursor: pointer;
  height: 30px;
  border-radius: 3px;
  font-size: 14px;
  color: #fff;
  border: transparent !important;
  background-color: #344054 !important;
  width: 60px !important;
  &:hover {
    background-color: #3d4b63 !important;
  }
}
::v-deep .el-button.btn2 {
  background-color: #344054 !important;
  font-size: 22px;
  line-height: 22px;
  width: 24px;
  color: white;
  border: transparent !important;
  &:hover {
    background-color: #3d4b63 !important;
  }
}

::v-deep .el-select .el-input {
  border: 1px solid #cfcfcf8f !important;
}
::v-deep .el-select .el-input__inner {
  border: 1px solid transparent !important;
  color: #dadada !important;
}
::v-deep .el-select .el-input__wrapper {
  background-color: #11253e !important;
  color: #dadada;
  box-shadow: none !important;
}
::v-deep .el-select .el-input .el-select__caret {
  color: #fff !important;
}
::v-deep .el-input .el-input__wrapper.is-focus {
  box-shadow: 0 0 0 1px #cfcfcf8f inset;
}
