<template>
  <div v-loading="mapLoading" class="mapDiv">
    <div :id="mapId" class="mapDiv" />
  </div>
</template>

<script>
import { MarkerClusterer } from '@googlemaps/markerclusterer';

import markPoint from '@/assets/home/<USER>';
import clusterIcon from '@/assets/home/<USER>';
import singleMarker from '@/assets/gis/singleMarker.png';
import placeMarker from '@/assets/gis/placeMarker.png';

let google;
let single_marker = null;
export default {
  name: 'GoogleMap',
  props: {
    // 开启地图点击查询功能
    clickFun: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 此处不声明 map 对象，可以直接使用 this.map赋值或者采用非响应式的普通对象来存储。
      // map:null,
      mapLoading: false,
      geocode: null, // 逆地理
      // place_maker: null,
      markList: [], // 聚合数组
      clustererObject: null // 聚合对象
    };
  },
  computed: {
    mapId() {
      return 'map' + parseInt(Math.random() * 999999);
    }
  },
  mounted() {
    this.loadScript();
  },
  beforeUnmount() {
    this.removeMapClick();
  },
  methods: {
    // 动态加载script标签
    loadScript() {
      if (document.getElementById('googleMapScript')) {
        google = window.google;
        this.init();
      } else {
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.async = true;
        script.defer = true;
        script.id = 'googleMapScript';
        script.src =
          'https://maps.googleapis.com/maps/api/js?key=AIzaSyD6jGvdge-UAAG4HCeZIjizFdi-LVZwUr0&language=en&callback=Function.prototype';
        document.head.appendChild(script);
        script.onload = () => {
          google = window.google;
          this.init();
        };
      }
    },
    // 初始化地图
    init() {
      // 定义一个区域，该区域包含了全球的范围
      const worldBounds = new google.maps.LatLngBounds(
        new google.maps.LatLng(-85, -180),
        new google.maps.LatLng(85, 180)
      );
      this.map = new google.maps.Map(document.getElementById(this.mapId), {
        center: {
          lng: -74.049693,
          lat: 40.662892
        },
        zoom: 10,
        disableDefaultUI: true,
        restriction: {
          latLngBounds: worldBounds,
          strictBounds: true
        }
      });
      // 创建逆地理编码对象
      this.geocode = new google.maps.Geocoder();
      if (this.clickFun) {
        this.initMapClick();
      }

      this.$emit('initMap', this.map);
    },
    // 注册地图点击事件
    initMapClick() {
      this.mapClickListen = this.map.addListener('click', this.mapClick);
    },
    // 注销地图点击事件
    removeMapClick() {
      if (this.mapClickListen) {
        google.maps.event.removeListener(this.mapClickListen);
        this.mapClickListen = null;
      }
    },
    // 地图点击事件(逆地理)
    mapClick(e) {
      const lnglat = e.latLng;
      const siteLng = lnglat.lng();
      const siteLat = lnglat.lat();
      this.addSingleMarker(siteLng, siteLat);
    },
    // 逆地理查询
    getLocation(siteLng, siteLat) {
      let detailedAddress = '';
      this.mapLoading = true;
      var latlng = {
        lng: siteLng,
        lat: siteLat
      };
      this.geocode.geocode(
        {
          location: latlng
        },
        (results, status) => {
          if (status === 'OK') {
            // 如果成功返回结果
            if (results[0]) {
              // 获取返回的第一个结果
              detailedAddress = results[0].formatted_address;
            } else {
              console.log('No results found');
            }
          } else {
            console.log('Geocoder failed due to: ' + status);
          }
          this.$emit('mapClickInfo', {
            detailedAddress,
            siteLng: siteLng.toFixed(6),
            siteLat: siteLat.toFixed(6)
          });
          this.mapLoading = false;
        }
      );
    },
    // 添加单个默认覆盖物(点击地图)
    addSingleMarker(siteLng, siteLat, zoom) {
      if (single_marker) {
        single_marker.setMap(null);
      }
      // 创建标注对象
      single_marker = new google.maps.Marker({
        icon: {
          url: singleMarker,
          size: new google.maps.Size(32, 32),
          scaledSize: new google.maps.Size(32, 32), // 标记图标的缩放大小
          // origin: new google.maps.Point(0, 0), // 标记图标的原点位置
          anchor: new google.maps.Point(16, 16) // 标记图标的锚点位置
        },
        map: this.map,
        position: {
          lng: siteLng,
          lat: siteLat
        }
      });
      this.map.panTo({
        lng: siteLng,
        lat: siteLat
      });
      if (zoom) this.map.setZoom(zoom);
      this.getLocation(siteLng, siteLat);
    },
    // 添加覆盖物(地名查询)
    addPlaceMarker(siteLng, siteLat, zoom = 15) {
      if (this.place_maker) {
        this.place_maker.setMap(null);
      }
      // 创建标注对象
      this.place_maker = new google.maps.Marker({
        icon: {
          url: placeMarker, // 图标的图片地址
          size: new google.maps.Size(32, 32),
          scaledSize: new google.maps.Size(32, 32), // 标记图标的缩放大小
          // origin: new google.maps.Point(0, 0), // 标记图标的原点位置
          anchor: new google.maps.Point(16, 32) // 标记图标的锚点位置
        },
        map: this.map,
        position: {
          lng: siteLng,
          lat: siteLat
        }
      });
      this.map.panTo({
        lng: siteLng,
        lat: siteLat
      });
      if (zoom) this.map.setZoom(zoom);
    },
    // 地图定点定位(预留经纬度定位方法)
    locationFun(siteLng, siteLat, zoom = 15) {
      this.map.setZoomAndCenter(zoom, [siteLng, siteLat]);
    },
    // 定制方法-批量点位并设置聚合
    async initSiteData(monitorPoints) {
      if (this.clustererObject) {
        this.clustererObject.clearMarkers();
        this.map.setMap(null);
      }
      if (!monitorPoints || !monitorPoints.length) return;
      const google = window.google;
      this.markList = [];
      // 创建一个 LatLngBounds 对象，包含所有标记
      const bounds = new google.maps.LatLngBounds();
      for (const item of monitorPoints.filter(
        obj => obj.deviceLng && obj.deviceLat
      )) {
        // 向地图上添加自定义标注
        const marker = new google.maps.Marker({
          icon: {
            url: markPoint, // 图标的图片地址
            size: new google.maps.Size(32, 32),
            scaledSize: new google.maps.Size(32, 32), // 标记图标的缩放大小
            // origin: new google.maps.Point(0, 0), // 标记图标的原点位置
            anchor: new google.maps.Point(16, 32) // 标记图标的锚点位置
          },
          map: this.map,
          position: {
            lng: item.deviceLng,
            lat: item.deviceLat
          }
        });
        bounds.extend(marker.getPosition());
        // 添加自定义弹窗
        const infoWindow = new google.maps.InfoWindow();
        // infoWindow.setOffset(new T.Point(0, 0))
        let sContent = `
              <div class="mark_info">
                <div class="mark_info_title">
                  <span>${this.$t('Device Name')}：${item.deviceName}</span>
                </div>
                <div class="mark_info_content">
                  <div class="item">${this.$t('Device Code')}: ${
          item.deviceCode
        }</div>
                  <div class="item">${this.$t('Installation Address')}: ${
          item.address
        }</div>
                `;
        sContent += `</div></div>`;
        infoWindow.setContent(sContent);

        marker.addListener('mouseover', () => {
          infoWindow.open({
            anchor: marker,
            map: this.map
          });
        });
        marker.addListener('mouseout', () => {
          infoWindow.close();
        });
        // 聚合数组
        this.markList.push(marker);
      }
      const renderer = {
        render: ({ count, position }) =>
          new google.maps.Marker({
            icon: {
              url: clusterIcon, // 图标的图片地址
              size: new google.maps.Size(42, 42),
              scaledSize: new google.maps.Size(42, 42), // 标记图标的缩放大小
              // origin: new google.maps.Point(0, 0), // 标记图标的原点位置
              anchor: new google.maps.Point(24, 24) // 标记图标的锚点位置
            },
            label: {
              text: String(count),
              color: 'white',
              fontSize: '14px',
              fontWeight: '700'
            },
            position,
            zIndex: Number(google.maps.Marker.MAX_ZINDEX) + count
          })
      };
      // 创建一个MarkerClusterer对象，并将标记添加到该对象中
      this.clustererObject = new MarkerClusterer({
        map: this.map,
        markers: this.markList,
        renderer,
        zoomOnClick: true
      });
      // this.clustererObject = new MarkerClusterer(this.map, this.markList, { imagePath: clusterIcon })
      // 将地图位置设为标记的中心点，并缩放视口
      this.map.setCenter(bounds.getCenter());
      this.map.fitBounds(bounds);
    }
  }
};
</script>

<style lang="scss" scoped>
.mapDiv {
  width: 100%;
  height: 100%;
  position: relative;
}

:deep() {
  .gm-style .gm-style-iw-c {
    padding: 0;
    border-radius: 0;
    .gm-ui-hover-effect {
      right: 0 !important;
      top: 0 !important;
    }
  }
  .gm-style .gm-style-iw-d {
    max-height: 500px !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
  }
}
</style>
