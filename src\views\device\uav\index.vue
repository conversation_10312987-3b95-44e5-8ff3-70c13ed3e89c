<!--应用管理-->
<script>
export default {
  name: 'uav'
};
</script>

<script setup>
import { reactive, ref, onMounted, nextTick } from 'vue';
import { debounce } from 'lodash-es';
import DetailDialog from './DetailDialog.vue';
import { getDevicesBound, getDevicesBySn, getDeviceModal, deleteDevices, carrierSyncData } from '@/api/devices';
import { DOMAIN } from '@/utils/constants';
import EditDialog from './EditDialog.vue';
import AlarmListDailog from '../airport/components/AlarmListDailog.vue';
import optionData from '@/utils/option-data';
import { authorityShow } from '@/utils/authority';
const editDialogRef = ref(null);
const loading = ref(false);
const total = ref(0);
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  queryType: 2,
  keyWord: ''
});
const modalList = ref([]); //型号列表
const dataList = ref([]);

const dialog = reactive({
  visible: false
});
const editDialog = reactive({
  visible: false
});
const alarmDialog = reactive({
  visible: false
});

let formData = reactive({});

/**
 * 查询
 */
function handleQuery() {
  loading.value = true;
  getDevicesBound({
    ...queryParams,
    domain: DOMAIN.DRONE
  })
    .then(data => {
      const { list, pagination } = data;
      dataList.value = list || [];
      total.value = pagination.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

// 添加防抖的搜索处理
const debouncedSearch = debounce(() => {
  handleSearch();
}, 300);

function handleSearch() {
  queryParams.pageNum = 1;
  handleQuery();
}
/**
 * 重置查询
 */
function resetQuery() {
  queryParams.nickname = '';
  queryParams.deviceName = '';
  queryParams.status = null;
  queryParams.deviceSn = '';
  queryParams.pageNum = 1;
  queryParams.pageSize = 10;
  handleQuery();
}

// 打开告警信息
function openAlarmDialog(row) {
  alarmDialog.visible = true;
  Object.keys(formData).map(key => {
    delete formData[key];
  });
  if (row) {
    alarmDialog.title = '编辑信息';
    Object.assign(formData, { ...row });
  }
}

/**
 * 打开应用表单弹窗
 *
 * @param dicTypeId 应用ID
 */
function openDialog(row) {
  Object.keys(formData).map(key => {
    delete formData[key];
  });
  if (row) {
    Object.assign(formData, { ...row });
    dialog.title = '无人机详情';
  } else {
    dialog.title = '新增无人机';
    nextTick(() => {
      editDialogRef.value.setDefaultValue();
    });
  }
  dialog.visible = true;
}

/**
 * 打开表单弹窗
 */
function openEditDialog(row) {
  editDialog.visible = true;
  Object.keys(formData).map(key => {
    delete formData[key];
  });
  if (row) {
    editDialog.title = '编辑无人机信息';
    Object.assign(formData, { ...row });
  } else {
    editDialog.title = '新增';
    nextTick(() => {
      editDialogRef.value.setDefaultValue();
    });
  }
}

function initModal() {
  getDeviceModal({
    domain: '0'
  }).then(res => {
    modalList.value = res;
  });
}

/**
 * 删除
 */
function handleDelete(row) {
  ElMessageBox.confirm(`确定后将解绑【${row.nickname}】`, '确定解绑？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    autofocus: false,
    closeOnClickModal: false,
    type: 'warning'
  }).then(() => {
    deleteDevices(row.device_sn).then(data => {
      ElMessage.success('解绑成功');
      handleQuery();
    });
  });
}

const handleSizeChange = val => {
  // Calculate the new total pages based on the new page size
  const newTotalPages = Math.ceil(total.value / val);
  
  // If current page is greater than new total pages, adjust to the last page
  if (queryParams.pageNum > newTotalPages) {
    queryParams.pageNum = newTotalPages || 1;
  }
  queryParams.pageSize = val;
  handleQuery({ ...queryParams });
};

const handleCurrentChange = val => {
  queryParams.pageNum = val;
  handleQuery({ ...queryParams });
};

// 优化表格配置
const tableConfig = reactive({
  height: 'calc(100vh - 280px)', // 动态计算表格高度
  rowKey: 'device_sn', // 添加唯一标识
  border: true,
  stripe: true
});

// 运载机同步 carrierSync
function handleCarrierSync() {
  carrierSyncData()
    .then(res => {
      ElMessage.success('成功同步' + res.success_count + '个设备');
    })
    .catch(err => {
      ElMessage.error('同步失败:' + err);
    });
}

onMounted(() => {
  initModal();
  handleQuery();
});
</script>

<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
          <el-form-item label="" prop="nickname">
            <el-input
              class="input-serach"
              v-model="queryParams.nickname"
              placeholder="请输入无人机名称"
              clearable
              @input="debouncedSearch"
              maxlength="64"
            />
          </el-form-item>
          <el-form-item label="" prop="device_Sn">
            <el-input
              class="input-serach"
              v-model="queryParams.deviceSn"
              placeholder="请输入设备SN"
              clearable
              @input="debouncedSearch"
              maxlength="64"
            />
          </el-form-item>
          <el-form-item label="" prop="deviceName">
            <el-select
              class="input-serach"
              v-model="queryParams.deviceName"
              placeholder="请选择型号"
              clearable
              @change="handleSearch"
            >
              <el-option
                v-for="item in modalList"
                :key="item.device_name"
                :label="item.device_name"
                :value="item.device_name"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="status">
            <el-select
              class="input-serach"
              v-model="queryParams.status"
              placeholder="请选择无人机状态"
              clearable
              @change="handleSearch"
            >
              <el-option
                v-for="item in optionData.statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="search-btn">
        <el-button @click="handleCarrierSync()" v-show="queryParams.nickname === 'carrierSync'"
          ><i-ep-switch />运载机同步</el-button
        >
        <el-button type="primary" @click="handleSearch()"><i-ep-search />搜索</el-button>
        <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
      </div>
    </div>
    <el-card shadow="never">
      <div style="margin: 15px 0"></div>
      <el-table v-loading="loading" :data="dataList" v-bind="tableConfig" highlight-current-row>
        <el-table-column label="序号" width="80" fixed>
          <template #default="scope">
            <span>{{ scope.$index + 1 + queryParams.pageSize * (queryParams.pageNum - 1) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="组织名称" prop="dept_name" width="180px" />
        <el-table-column label="无人机名称" prop="nickname" width="450px" />
        <el-table-column label="设备SN" prop="device_sn" width="230px" />
        <el-table-column label="型号" prop="device_name" />
        <el-table-column prop="status" label="无人机状态" width="130px">
          <template #default="scope">
            <el-tag :type="scope.row.status ? 'success' : 'info'">
              {{ scope.row.status ? '在线' : '离线' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="加入时间" prop="bound_time" width="200" />
        <el-table-column label="最后上线时间" prop="login_time" width="200" />
        <el-table-column
          fixed="right"
          label="操作"
          width="200"
          v-if="
            authorityShow('editUav') ||
            authorityShow('checkUav') ||
            authorityShow('hmsUav') ||
            authorityShow('unLockUav')
          "
        >
          <template #default="scope">
            <el-button type="primary" link @click.stop="openEditDialog(scope.row)" v-if="authorityShow('editUav')"
              >编辑</el-button
            >
            <el-button type="primary" link @click.stop="openDialog(scope.row)" v-if="authorityShow('checkUav')"
              >详情</el-button
            >
            <el-button
              v-if="!scope.row.is_carrier && authorityShow('hmsUav')"
              type="primary"
              link
              @click.stop="openAlarmDialog(scope.row)"
              >告警</el-button
            >
            <el-button type="danger" link @click.stop="handleDelete(scope.row)" v-if="authorityShow('unLockUav')"
              >解绑</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-content">
        <el-pagination
          v-if="total > 0"
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :background="true"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <EditDialog
      v-model:visible="editDialog.visible"
      :title="editDialog.title"
      :form-data="formData"
      @submit="resetQuery"
    />
    <DetailDialog v-model:visible="dialog.visible" :form-data="formData" />
    <AlarmListDailog v-model:visible="alarmDialog.visible" :form-data="formData" :domain="DOMAIN.DRONE" />
  </div>
</template>
<style lang="scss" scoped>
.pagination-content {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;

  &.hidden {
    display: none;
  }
}
.flex-center {
  display: flex;
  align-items: center;
  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #4caf51;
  }
  .unstatus {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: red;
  }
}
.online,
.outline {
  display: inline-block;
  width: 45px;
  height: 24px;
  line-height: 24px;
  border-radius: 2px;
  font-family: SourceHanSansSC-Regular;
  font-size: 12px;
  color: rgb(57, 191, 164);
  text-align: center;
  font-weight: 400;
  background: rgba(42, 139, 125, 0.3);
  outline-style: none;
}
.outline {
  color: rgb(152, 162, 179);
  background: rgba(152, 162, 179, 0.3);
}
.input-serach {
  width: 200px;
}
.search {
  display: flex;
  align-items: center;
  padding: 24px;
  height: 64px;
  .search-form {
    flex: 1;
    padding-top: 16px;
  }
}
</style>
