!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("crypto")):"function"==typeof define&&define.amd?define(["crypto"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).crypto$1)}(this,(function(e){"use strict";function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var r,n=t(e),s=(r="undefined"==typeof document&&"undefined"==typeof location?new(require("url").URL)("file:"+__filename).href:"undefined"==typeof document?location.href:document.currentScript&&document.currentScript.src||new URL("decoder-pro-hard.js",document.baseURI).href,function(e){var t,i;(e=void 0!==(e=e||{})?e:{}).ready=new Promise((function(e,r){t=e,i=r})),(e=void 0!==e?e:{}).locateFile=function(e){return"decoder-pro-audio.wasm"==e&&"undefined"!=typeof JESSIBUCA_PRO_AUDIO_WASM_URL&&""!=JESSIBUCA_PRO_AUDIO_WASM_URL?JESSIBUCA_PRO_AUDIO_WASM_URL:e};var n,s,a,o,d,l,c=Object.assign({},e),u="./this.program",h="object"==typeof window,f="function"==typeof importScripts,p="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,_="";p?(_=f?require("path").dirname(_)+"/":__dirname+"/",l=()=>{d||(o=require("fs"),d=require("path"))},n=function(e,t){return l(),e=d.normalize(e),o.readFileSync(e,t?void 0:"utf8")},a=e=>{var t=n(e,!0);return t.buffer||(t=new Uint8Array(t)),t},s=(e,t,r)=>{l(),e=d.normalize(e),o.readFile(e,(function(e,i){e?r(e):t(i.buffer)}))},process.argv.length>1&&(u=process.argv[1].replace(/\\/g,"/")),process.argv.slice(2),process.on("uncaughtException",(function(e){if(!(e instanceof J))throw e})),process.on("unhandledRejection",(function(e){throw e})),e.inspect=function(){return"[Emscripten Module object]"}):(h||f)&&(f?_=self.location.href:"undefined"!=typeof document&&document.currentScript&&(_=document.currentScript.src),r&&(_=r),_=0!==_.indexOf("blob:")?_.substr(0,_.replace(/[?#].*/,"").lastIndexOf("/")+1):"",n=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},f&&(a=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),s=(e,t,r)=>{var i=new XMLHttpRequest;i.open("GET",e,!0),i.responseType="arraybuffer",i.onload=()=>{200==i.status||0==i.status&&i.response?t(i.response):r()},i.onerror=r,i.send(null)});var m,g,y=e.print||console.log.bind(console),v=e.printErr||console.warn.bind(console);Object.assign(e,c),c=null,e.arguments&&e.arguments,e.thisProgram&&(u=e.thisProgram),e.quit&&e.quit,e.wasmBinary&&(m=e.wasmBinary),e.noExitRuntime,"object"!=typeof WebAssembly&&Y("no native wasm support detected");var b=!1;function w(e,t){e||Y(t)}var S,E,A,B,x,U,T,k,C,D,I="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function F(e,t,r){for(var i=t+r,n=t;e[n]&&!(n>=i);)++n;if(n-t>16&&e.buffer&&I)return I.decode(e.subarray(t,n));for(var s="";t<n;){var a=e[t++];if(128&a){var o=63&e[t++];if(192!=(224&a)){var d=63&e[t++];if((a=224==(240&a)?(15&a)<<12|o<<6|d:(7&a)<<18|o<<12|d<<6|63&e[t++])<65536)s+=String.fromCharCode(a);else{var l=a-65536;s+=String.fromCharCode(55296|l>>10,56320|1023&l)}}else s+=String.fromCharCode((31&a)<<6|o)}else s+=String.fromCharCode(a)}return s}function P(e,t){return e?F(A,e,t):""}function L(e,t,r,i){if(!(i>0))return 0;for(var n=r,s=r+i-1,a=0;a<e.length;++a){var o=e.charCodeAt(a);if(o>=55296&&o<=57343&&(o=65536+((1023&o)<<10)|1023&e.charCodeAt(++a)),o<=127){if(r>=s)break;t[r++]=o}else if(o<=2047){if(r+1>=s)break;t[r++]=192|o>>6,t[r++]=128|63&o}else if(o<=65535){if(r+2>=s)break;t[r++]=224|o>>12,t[r++]=128|o>>6&63,t[r++]=128|63&o}else{if(r+3>=s)break;t[r++]=240|o>>18,t[r++]=128|o>>12&63,t[r++]=128|o>>6&63,t[r++]=128|63&o}}return t[r]=0,r-n}function M(e){for(var t=0,r=0;r<e.length;++r){var i=e.charCodeAt(r);i<=127?t++:i<=2047?t+=2:i>=55296&&i<=57343?(t+=4,++r):t+=3}return t}e.INITIAL_MEMORY;var R,z,N,O,G=[],H=[],$=[],V=0,W=null;function j(t){V++,e.monitorRunDependencies&&e.monitorRunDependencies(V)}function q(t){if(V--,e.monitorRunDependencies&&e.monitorRunDependencies(V),0==V&&W){var r=W;W=null,r()}}function Y(t){e.onAbort&&e.onAbort(t),v(t="Aborted("+t+")"),b=!0,t+=". Build with -sASSERTIONS for more info.";var r=new WebAssembly.RuntimeError(t);throw i(r),r}function K(e){return e.startsWith("data:application/octet-stream;base64,")}function X(e){return e.startsWith("file://")}function Z(e){try{if(e==R&&m)return new Uint8Array(m);if(a)return a(e);throw"both async and sync fetching of the wasm failed"}catch(e){Y(e)}}function J(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function Q(t){for(;t.length>0;)t.shift()(e)}function ee(e){this.excPtr=e,this.ptr=e-24,this.set_type=function(e){T[this.ptr+4>>2]=e},this.get_type=function(){return T[this.ptr+4>>2]},this.set_destructor=function(e){T[this.ptr+8>>2]=e},this.get_destructor=function(){return T[this.ptr+8>>2]},this.set_refcount=function(e){U[this.ptr>>2]=e},this.set_caught=function(e){e=e?1:0,E[this.ptr+12>>0]=e},this.get_caught=function(){return 0!=E[this.ptr+12>>0]},this.set_rethrown=function(e){e=e?1:0,E[this.ptr+13>>0]=e},this.get_rethrown=function(){return 0!=E[this.ptr+13>>0]},this.init=function(e,t){this.set_adjusted_ptr(0),this.set_type(e),this.set_destructor(t),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var e=U[this.ptr>>2];U[this.ptr>>2]=e+1},this.release_ref=function(){var e=U[this.ptr>>2];return U[this.ptr>>2]=e-1,1===e},this.set_adjusted_ptr=function(e){T[this.ptr+16>>2]=e},this.get_adjusted_ptr=function(){return T[this.ptr+16>>2]},this.get_exception_ptr=function(){if(jt(this.get_type()))return T[this.excPtr>>2];var e=this.get_adjusted_ptr();return 0!==e?e:this.excPtr}}e.locateFile?K(R="decoder-pro-audio.wasm")||(z=R,R=e.locateFile?e.locateFile(z,_):_+z):R=new URL("decoder-pro-audio.wasm","undefined"==typeof document&&"undefined"==typeof location?new(require("url").URL)("file:"+__filename).href:"undefined"==typeof document?location.href:document.currentScript&&document.currentScript.src||new URL("decoder-pro-hard.js",document.baseURI).href).toString();var te={isAbs:e=>"/"===e.charAt(0),splitPath:e=>/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1),normalizeArray:(e,t)=>{for(var r=0,i=e.length-1;i>=0;i--){var n=e[i];"."===n?e.splice(i,1):".."===n?(e.splice(i,1),r++):r&&(e.splice(i,1),r--)}if(t)for(;r;r--)e.unshift("..");return e},normalize:e=>{var t=te.isAbs(e),r="/"===e.substr(-1);return(e=te.normalizeArray(e.split("/").filter((e=>!!e)),!t).join("/"))||t||(e="."),e&&r&&(e+="/"),(t?"/":"")+e},dirname:e=>{var t=te.splitPath(e),r=t[0],i=t[1];return r||i?(i&&(i=i.substr(0,i.length-1)),r+i):"."},basename:e=>{if("/"===e)return"/";var t=(e=(e=te.normalize(e)).replace(/\/$/,"")).lastIndexOf("/");return-1===t?e:e.substr(t+1)},join:function(){var e=Array.prototype.slice.call(arguments,0);return te.normalize(e.join("/"))},join2:(e,t)=>te.normalize(e+"/"+t)},re={resolve:function(){for(var e="",t=!1,r=arguments.length-1;r>=-1&&!t;r--){var i=r>=0?arguments[r]:oe.cwd();if("string"!=typeof i)throw new TypeError("Arguments to path.resolve must be strings");if(!i)return"";e=i+"/"+e,t=te.isAbs(i)}return(t?"/":"")+(e=te.normalizeArray(e.split("/").filter((e=>!!e)),!t).join("/"))||"."},relative:(e,t)=>{function r(e){for(var t=0;t<e.length&&""===e[t];t++);for(var r=e.length-1;r>=0&&""===e[r];r--);return t>r?[]:e.slice(t,r-t+1)}e=re.resolve(e).substr(1),t=re.resolve(t).substr(1);for(var i=r(e.split("/")),n=r(t.split("/")),s=Math.min(i.length,n.length),a=s,o=0;o<s;o++)if(i[o]!==n[o]){a=o;break}var d=[];for(o=a;o<i.length;o++)d.push("..");return(d=d.concat(n.slice(a))).join("/")}};function ie(e,t,r){var i=r>0?r:M(e)+1,n=new Array(i),s=L(e,n,0,n.length);return t&&(n.length=s),n}var ne={ttys:[],init:function(){},shutdown:function(){},register:function(e,t){ne.ttys[e]={input:[],output:[],ops:t},oe.registerDevice(e,ne.stream_ops)},stream_ops:{open:function(e){var t=ne.ttys[e.node.rdev];if(!t)throw new oe.ErrnoError(43);e.tty=t,e.seekable=!1},close:function(e){e.tty.ops.flush(e.tty)},flush:function(e){e.tty.ops.flush(e.tty)},read:function(e,t,r,i,n){if(!e.tty||!e.tty.ops.get_char)throw new oe.ErrnoError(60);for(var s=0,a=0;a<i;a++){var o;try{o=e.tty.ops.get_char(e.tty)}catch(e){throw new oe.ErrnoError(29)}if(void 0===o&&0===s)throw new oe.ErrnoError(6);if(null==o)break;s++,t[r+a]=o}return s&&(e.node.timestamp=Date.now()),s},write:function(e,t,r,i,n){if(!e.tty||!e.tty.ops.put_char)throw new oe.ErrnoError(60);try{for(var s=0;s<i;s++)e.tty.ops.put_char(e.tty,t[r+s])}catch(e){throw new oe.ErrnoError(29)}return i&&(e.node.timestamp=Date.now()),s}},default_tty_ops:{get_char:function(e){if(!e.input.length){var t=null;if(p){var r=Buffer.alloc(256),i=0;try{i=o.readSync(process.stdin.fd,r,0,256,-1)}catch(e){if(!e.toString().includes("EOF"))throw e;i=0}t=i>0?r.slice(0,i).toString("utf-8"):null}else"undefined"!=typeof window&&"function"==typeof window.prompt?null!==(t=window.prompt("Input: "))&&(t+="\n"):"function"==typeof readline&&null!==(t=readline())&&(t+="\n");if(!t)return null;e.input=ie(t,!0)}return e.input.shift()},put_char:function(e,t){null===t||10===t?(y(F(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},flush:function(e){e.output&&e.output.length>0&&(y(F(e.output,0)),e.output=[])}},default_tty1_ops:{put_char:function(e,t){null===t||10===t?(v(F(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},flush:function(e){e.output&&e.output.length>0&&(v(F(e.output,0)),e.output=[])}}};function se(e){e=function(e,t){return Math.ceil(e/t)*t}(e,65536);var t=Wt(65536,e);return t?(function(e,t){A.fill(0,e,e+t)}(t,e),t):0}var ae={ops_table:null,mount:function(e){return ae.createNode(null,"/",16895,0)},createNode:function(e,t,r,i){if(oe.isBlkdev(r)||oe.isFIFO(r))throw new oe.ErrnoError(63);ae.ops_table||(ae.ops_table={dir:{node:{getattr:ae.node_ops.getattr,setattr:ae.node_ops.setattr,lookup:ae.node_ops.lookup,mknod:ae.node_ops.mknod,rename:ae.node_ops.rename,unlink:ae.node_ops.unlink,rmdir:ae.node_ops.rmdir,readdir:ae.node_ops.readdir,symlink:ae.node_ops.symlink},stream:{llseek:ae.stream_ops.llseek}},file:{node:{getattr:ae.node_ops.getattr,setattr:ae.node_ops.setattr},stream:{llseek:ae.stream_ops.llseek,read:ae.stream_ops.read,write:ae.stream_ops.write,allocate:ae.stream_ops.allocate,mmap:ae.stream_ops.mmap,msync:ae.stream_ops.msync}},link:{node:{getattr:ae.node_ops.getattr,setattr:ae.node_ops.setattr,readlink:ae.node_ops.readlink},stream:{}},chrdev:{node:{getattr:ae.node_ops.getattr,setattr:ae.node_ops.setattr},stream:oe.chrdev_stream_ops}});var n=oe.createNode(e,t,r,i);return oe.isDir(n.mode)?(n.node_ops=ae.ops_table.dir.node,n.stream_ops=ae.ops_table.dir.stream,n.contents={}):oe.isFile(n.mode)?(n.node_ops=ae.ops_table.file.node,n.stream_ops=ae.ops_table.file.stream,n.usedBytes=0,n.contents=null):oe.isLink(n.mode)?(n.node_ops=ae.ops_table.link.node,n.stream_ops=ae.ops_table.link.stream):oe.isChrdev(n.mode)&&(n.node_ops=ae.ops_table.chrdev.node,n.stream_ops=ae.ops_table.chrdev.stream),n.timestamp=Date.now(),e&&(e.contents[t]=n,e.timestamp=n.timestamp),n},getFileDataAsTypedArray:function(e){return e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array(0)},expandFileStorage:function(e,t){var r=e.contents?e.contents.length:0;if(!(r>=t)){t=Math.max(t,r*(r<1048576?2:1.125)>>>0),0!=r&&(t=Math.max(t,256));var i=e.contents;e.contents=new Uint8Array(t),e.usedBytes>0&&e.contents.set(i.subarray(0,e.usedBytes),0)}},resizeFileStorage:function(e,t){if(e.usedBytes!=t)if(0==t)e.contents=null,e.usedBytes=0;else{var r=e.contents;e.contents=new Uint8Array(t),r&&e.contents.set(r.subarray(0,Math.min(t,e.usedBytes))),e.usedBytes=t}},node_ops:{getattr:function(e){var t={};return t.dev=oe.isChrdev(e.mode)?e.id:1,t.ino=e.id,t.mode=e.mode,t.nlink=1,t.uid=0,t.gid=0,t.rdev=e.rdev,oe.isDir(e.mode)?t.size=4096:oe.isFile(e.mode)?t.size=e.usedBytes:oe.isLink(e.mode)?t.size=e.link.length:t.size=0,t.atime=new Date(e.timestamp),t.mtime=new Date(e.timestamp),t.ctime=new Date(e.timestamp),t.blksize=4096,t.blocks=Math.ceil(t.size/t.blksize),t},setattr:function(e,t){void 0!==t.mode&&(e.mode=t.mode),void 0!==t.timestamp&&(e.timestamp=t.timestamp),void 0!==t.size&&ae.resizeFileStorage(e,t.size)},lookup:function(e,t){throw oe.genericErrors[44]},mknod:function(e,t,r,i){return ae.createNode(e,t,r,i)},rename:function(e,t,r){if(oe.isDir(e.mode)){var i;try{i=oe.lookupNode(t,r)}catch(e){}if(i)for(var n in i.contents)throw new oe.ErrnoError(55)}delete e.parent.contents[e.name],e.parent.timestamp=Date.now(),e.name=r,t.contents[r]=e,t.timestamp=e.parent.timestamp,e.parent=t},unlink:function(e,t){delete e.contents[t],e.timestamp=Date.now()},rmdir:function(e,t){var r=oe.lookupNode(e,t);for(var i in r.contents)throw new oe.ErrnoError(55);delete e.contents[t],e.timestamp=Date.now()},readdir:function(e){var t=[".",".."];for(var r in e.contents)e.contents.hasOwnProperty(r)&&t.push(r);return t},symlink:function(e,t,r){var i=ae.createNode(e,t,41471,0);return i.link=r,i},readlink:function(e){if(!oe.isLink(e.mode))throw new oe.ErrnoError(28);return e.link}},stream_ops:{read:function(e,t,r,i,n){var s=e.node.contents;if(n>=e.node.usedBytes)return 0;var a=Math.min(e.node.usedBytes-n,i);if(a>8&&s.subarray)t.set(s.subarray(n,n+a),r);else for(var o=0;o<a;o++)t[r+o]=s[n+o];return a},write:function(e,t,r,i,n,s){if(!i)return 0;var a=e.node;if(a.timestamp=Date.now(),t.subarray&&(!a.contents||a.contents.subarray)){if(s)return a.contents=t.subarray(r,r+i),a.usedBytes=i,i;if(0===a.usedBytes&&0===n)return a.contents=t.slice(r,r+i),a.usedBytes=i,i;if(n+i<=a.usedBytes)return a.contents.set(t.subarray(r,r+i),n),i}if(ae.expandFileStorage(a,n+i),a.contents.subarray&&t.subarray)a.contents.set(t.subarray(r,r+i),n);else for(var o=0;o<i;o++)a.contents[n+o]=t[r+o];return a.usedBytes=Math.max(a.usedBytes,n+i),i},llseek:function(e,t,r){var i=t;if(1===r?i+=e.position:2===r&&oe.isFile(e.node.mode)&&(i+=e.node.usedBytes),i<0)throw new oe.ErrnoError(28);return i},allocate:function(e,t,r){ae.expandFileStorage(e.node,t+r),e.node.usedBytes=Math.max(e.node.usedBytes,t+r)},mmap:function(e,t,r,i,n){if(!oe.isFile(e.node.mode))throw new oe.ErrnoError(43);var s,a,o=e.node.contents;if(2&n||o.buffer!==S){if((r>0||r+t<o.length)&&(o=o.subarray?o.subarray(r,r+t):Array.prototype.slice.call(o,r,r+t)),a=!0,!(s=se(t)))throw new oe.ErrnoError(48);E.set(o,s)}else a=!1,s=o.byteOffset;return{ptr:s,allocated:a}},msync:function(e,t,r,i,n){if(!oe.isFile(e.node.mode))throw new oe.ErrnoError(43);return 2&n||ae.stream_ops.write(e,t,0,i,r,!1),0}}},oe={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(e=re.resolve(oe.cwd(),e)))return{path:"",node:null};var r={follow_mount:!0,recurse_count:0};if(t=Object.assign(r,t),t.recurse_count>8)throw new oe.ErrnoError(32);for(var i=te.normalizeArray(e.split("/").filter((e=>!!e)),!1),n=oe.root,s="/",a=0;a<i.length;a++){var o=a===i.length-1;if(o&&t.parent)break;if(n=oe.lookupNode(n,i[a]),s=te.join2(s,i[a]),oe.isMountpoint(n)&&(!o||o&&t.follow_mount)&&(n=n.mounted.root),!o||t.follow)for(var d=0;oe.isLink(n.mode);){var l=oe.readlink(s);s=re.resolve(te.dirname(s),l);var c=oe.lookupPath(s,{recurse_count:t.recurse_count+1});if(n=c.node,d++>40)throw new oe.ErrnoError(32)}}return{path:s,node:n}},getPath:e=>{for(var t;;){if(oe.isRoot(e)){var r=e.mount.mountpoint;return t?"/"!==r[r.length-1]?r+"/"+t:r+t:r}t=t?e.name+"/"+t:e.name,e=e.parent}},hashName:(e,t)=>{for(var r=0,i=0;i<t.length;i++)r=(r<<5)-r+t.charCodeAt(i)|0;return(e+r>>>0)%oe.nameTable.length},hashAddNode:e=>{var t=oe.hashName(e.parent.id,e.name);e.name_next=oe.nameTable[t],oe.nameTable[t]=e},hashRemoveNode:e=>{var t=oe.hashName(e.parent.id,e.name);if(oe.nameTable[t]===e)oe.nameTable[t]=e.name_next;else for(var r=oe.nameTable[t];r;){if(r.name_next===e){r.name_next=e.name_next;break}r=r.name_next}},lookupNode:(e,t)=>{var r=oe.mayLookup(e);if(r)throw new oe.ErrnoError(r,e);for(var i=oe.hashName(e.id,t),n=oe.nameTable[i];n;n=n.name_next){var s=n.name;if(n.parent.id===e.id&&s===t)return n}return oe.lookup(e,t)},createNode:(e,t,r,i)=>{var n=new oe.FSNode(e,t,r,i);return oe.hashAddNode(n),n},destroyNode:e=>{oe.hashRemoveNode(e)},isRoot:e=>e===e.parent,isMountpoint:e=>!!e.mounted,isFile:e=>32768==(61440&e),isDir:e=>16384==(61440&e),isLink:e=>40960==(61440&e),isChrdev:e=>8192==(61440&e),isBlkdev:e=>24576==(61440&e),isFIFO:e=>4096==(61440&e),isSocket:e=>49152==(49152&e),flagModes:{r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090},modeStringToFlags:e=>{var t=oe.flagModes[e];if(void 0===t)throw new Error("Unknown file open mode: "+e);return t},flagsToPermissionString:e=>{var t=["r","w","rw"][3&e];return 512&e&&(t+="w"),t},nodePermissions:(e,t)=>oe.ignorePermissions||(!t.includes("r")||292&e.mode)&&(!t.includes("w")||146&e.mode)&&(!t.includes("x")||73&e.mode)?0:2,mayLookup:e=>{var t=oe.nodePermissions(e,"x");return t||(e.node_ops.lookup?0:2)},mayCreate:(e,t)=>{try{return oe.lookupNode(e,t),20}catch(e){}return oe.nodePermissions(e,"wx")},mayDelete:(e,t,r)=>{var i;try{i=oe.lookupNode(e,t)}catch(e){return e.errno}var n=oe.nodePermissions(e,"wx");if(n)return n;if(r){if(!oe.isDir(i.mode))return 54;if(oe.isRoot(i)||oe.getPath(i)===oe.cwd())return 10}else if(oe.isDir(i.mode))return 31;return 0},mayOpen:(e,t)=>e?oe.isLink(e.mode)?32:oe.isDir(e.mode)&&("r"!==oe.flagsToPermissionString(t)||512&t)?31:oe.nodePermissions(e,oe.flagsToPermissionString(t)):44,MAX_OPEN_FDS:4096,nextfd:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:oe.MAX_OPEN_FDS;for(var r=e;r<=t;r++)if(!oe.streams[r])return r;throw new oe.ErrnoError(33)},getStream:e=>oe.streams[e],createStream:(e,t,r)=>{oe.FSStream||(oe.FSStream=function(){this.shared={}},oe.FSStream.prototype={},Object.defineProperties(oe.FSStream.prototype,{object:{get:function(){return this.node},set:function(e){this.node=e}},isRead:{get:function(){return 1!=(2097155&this.flags)}},isWrite:{get:function(){return 0!=(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}},flags:{get:function(){return this.shared.flags},set:function(e){this.shared.flags=e}},position:{get:function(){return this.shared.position},set:function(e){this.shared.position=e}}})),e=Object.assign(new oe.FSStream,e);var i=oe.nextfd(t,r);return e.fd=i,oe.streams[i]=e,e},closeStream:e=>{oe.streams[e]=null},chrdev_stream_ops:{open:e=>{var t=oe.getDevice(e.node.rdev);e.stream_ops=t.stream_ops,e.stream_ops.open&&e.stream_ops.open(e)},llseek:()=>{throw new oe.ErrnoError(70)}},major:e=>e>>8,minor:e=>255&e,makedev:(e,t)=>e<<8|t,registerDevice:(e,t)=>{oe.devices[e]={stream_ops:t}},getDevice:e=>oe.devices[e],getMounts:e=>{for(var t=[],r=[e];r.length;){var i=r.pop();t.push(i),r.push.apply(r,i.mounts)}return t},syncfs:(e,t)=>{"function"==typeof e&&(t=e,e=!1),oe.syncFSRequests++,oe.syncFSRequests>1&&v("warning: "+oe.syncFSRequests+" FS.syncfs operations in flight at once, probably just doing extra work");var r=oe.getMounts(oe.root.mount),i=0;function n(e){return oe.syncFSRequests--,t(e)}function s(e){if(e)return s.errored?void 0:(s.errored=!0,n(e));++i>=r.length&&n(null)}r.forEach((t=>{if(!t.type.syncfs)return s(null);t.type.syncfs(t,e,s)}))},mount:(e,t,r)=>{var i,n="/"===r,s=!r;if(n&&oe.root)throw new oe.ErrnoError(10);if(!n&&!s){var a=oe.lookupPath(r,{follow_mount:!1});if(r=a.path,i=a.node,oe.isMountpoint(i))throw new oe.ErrnoError(10);if(!oe.isDir(i.mode))throw new oe.ErrnoError(54)}var o={type:e,opts:t,mountpoint:r,mounts:[]},d=e.mount(o);return d.mount=o,o.root=d,n?oe.root=d:i&&(i.mounted=o,i.mount&&i.mount.mounts.push(o)),d},unmount:e=>{var t=oe.lookupPath(e,{follow_mount:!1});if(!oe.isMountpoint(t.node))throw new oe.ErrnoError(28);var r=t.node,i=r.mounted,n=oe.getMounts(i);Object.keys(oe.nameTable).forEach((e=>{for(var t=oe.nameTable[e];t;){var r=t.name_next;n.includes(t.mount)&&oe.destroyNode(t),t=r}})),r.mounted=null;var s=r.mount.mounts.indexOf(i);r.mount.mounts.splice(s,1)},lookup:(e,t)=>e.node_ops.lookup(e,t),mknod:(e,t,r)=>{var i=oe.lookupPath(e,{parent:!0}).node,n=te.basename(e);if(!n||"."===n||".."===n)throw new oe.ErrnoError(28);var s=oe.mayCreate(i,n);if(s)throw new oe.ErrnoError(s);if(!i.node_ops.mknod)throw new oe.ErrnoError(63);return i.node_ops.mknod(i,n,t,r)},create:(e,t)=>(t=void 0!==t?t:438,t&=4095,t|=32768,oe.mknod(e,t,0)),mkdir:(e,t)=>(t=void 0!==t?t:511,t&=1023,t|=16384,oe.mknod(e,t,0)),mkdirTree:(e,t)=>{for(var r=e.split("/"),i="",n=0;n<r.length;++n)if(r[n]){i+="/"+r[n];try{oe.mkdir(i,t)}catch(e){if(20!=e.errno)throw e}}},mkdev:(e,t,r)=>(void 0===r&&(r=t,t=438),t|=8192,oe.mknod(e,t,r)),symlink:(e,t)=>{if(!re.resolve(e))throw new oe.ErrnoError(44);var r=oe.lookupPath(t,{parent:!0}).node;if(!r)throw new oe.ErrnoError(44);var i=te.basename(t),n=oe.mayCreate(r,i);if(n)throw new oe.ErrnoError(n);if(!r.node_ops.symlink)throw new oe.ErrnoError(63);return r.node_ops.symlink(r,i,e)},rename:(e,t)=>{var r,i,n=te.dirname(e),s=te.dirname(t),a=te.basename(e),o=te.basename(t);if(r=oe.lookupPath(e,{parent:!0}).node,i=oe.lookupPath(t,{parent:!0}).node,!r||!i)throw new oe.ErrnoError(44);if(r.mount!==i.mount)throw new oe.ErrnoError(75);var d,l=oe.lookupNode(r,a),c=re.relative(e,s);if("."!==c.charAt(0))throw new oe.ErrnoError(28);if("."!==(c=re.relative(t,n)).charAt(0))throw new oe.ErrnoError(55);try{d=oe.lookupNode(i,o)}catch(e){}if(l!==d){var u=oe.isDir(l.mode),h=oe.mayDelete(r,a,u);if(h)throw new oe.ErrnoError(h);if(h=d?oe.mayDelete(i,o,u):oe.mayCreate(i,o))throw new oe.ErrnoError(h);if(!r.node_ops.rename)throw new oe.ErrnoError(63);if(oe.isMountpoint(l)||d&&oe.isMountpoint(d))throw new oe.ErrnoError(10);if(i!==r&&(h=oe.nodePermissions(r,"w")))throw new oe.ErrnoError(h);oe.hashRemoveNode(l);try{r.node_ops.rename(l,i,o)}catch(e){throw e}finally{oe.hashAddNode(l)}}},rmdir:e=>{var t=oe.lookupPath(e,{parent:!0}).node,r=te.basename(e),i=oe.lookupNode(t,r),n=oe.mayDelete(t,r,!0);if(n)throw new oe.ErrnoError(n);if(!t.node_ops.rmdir)throw new oe.ErrnoError(63);if(oe.isMountpoint(i))throw new oe.ErrnoError(10);t.node_ops.rmdir(t,r),oe.destroyNode(i)},readdir:e=>{var t=oe.lookupPath(e,{follow:!0}).node;if(!t.node_ops.readdir)throw new oe.ErrnoError(54);return t.node_ops.readdir(t)},unlink:e=>{var t=oe.lookupPath(e,{parent:!0}).node;if(!t)throw new oe.ErrnoError(44);var r=te.basename(e),i=oe.lookupNode(t,r),n=oe.mayDelete(t,r,!1);if(n)throw new oe.ErrnoError(n);if(!t.node_ops.unlink)throw new oe.ErrnoError(63);if(oe.isMountpoint(i))throw new oe.ErrnoError(10);t.node_ops.unlink(t,r),oe.destroyNode(i)},readlink:e=>{var t=oe.lookupPath(e).node;if(!t)throw new oe.ErrnoError(44);if(!t.node_ops.readlink)throw new oe.ErrnoError(28);return re.resolve(oe.getPath(t.parent),t.node_ops.readlink(t))},stat:(e,t)=>{var r=oe.lookupPath(e,{follow:!t}).node;if(!r)throw new oe.ErrnoError(44);if(!r.node_ops.getattr)throw new oe.ErrnoError(63);return r.node_ops.getattr(r)},lstat:e=>oe.stat(e,!0),chmod:(e,t,r)=>{var i;if(!(i="string"==typeof e?oe.lookupPath(e,{follow:!r}).node:e).node_ops.setattr)throw new oe.ErrnoError(63);i.node_ops.setattr(i,{mode:4095&t|-4096&i.mode,timestamp:Date.now()})},lchmod:(e,t)=>{oe.chmod(e,t,!0)},fchmod:(e,t)=>{var r=oe.getStream(e);if(!r)throw new oe.ErrnoError(8);oe.chmod(r.node,t)},chown:(e,t,r,i)=>{var n;if(!(n="string"==typeof e?oe.lookupPath(e,{follow:!i}).node:e).node_ops.setattr)throw new oe.ErrnoError(63);n.node_ops.setattr(n,{timestamp:Date.now()})},lchown:(e,t,r)=>{oe.chown(e,t,r,!0)},fchown:(e,t,r)=>{var i=oe.getStream(e);if(!i)throw new oe.ErrnoError(8);oe.chown(i.node,t,r)},truncate:(e,t)=>{if(t<0)throw new oe.ErrnoError(28);var r;if(!(r="string"==typeof e?oe.lookupPath(e,{follow:!0}).node:e).node_ops.setattr)throw new oe.ErrnoError(63);if(oe.isDir(r.mode))throw new oe.ErrnoError(31);if(!oe.isFile(r.mode))throw new oe.ErrnoError(28);var i=oe.nodePermissions(r,"w");if(i)throw new oe.ErrnoError(i);r.node_ops.setattr(r,{size:t,timestamp:Date.now()})},ftruncate:(e,t)=>{var r=oe.getStream(e);if(!r)throw new oe.ErrnoError(8);if(0==(2097155&r.flags))throw new oe.ErrnoError(28);oe.truncate(r.node,t)},utime:(e,t,r)=>{var i=oe.lookupPath(e,{follow:!0}).node;i.node_ops.setattr(i,{timestamp:Math.max(t,r)})},open:(t,r,i)=>{if(""===t)throw new oe.ErrnoError(44);var n;if(i=void 0===i?438:i,i=64&(r="string"==typeof r?oe.modeStringToFlags(r):r)?4095&i|32768:0,"object"==typeof t)n=t;else{t=te.normalize(t);try{n=oe.lookupPath(t,{follow:!(131072&r)}).node}catch(e){}}var s=!1;if(64&r)if(n){if(128&r)throw new oe.ErrnoError(20)}else n=oe.mknod(t,i,0),s=!0;if(!n)throw new oe.ErrnoError(44);if(oe.isChrdev(n.mode)&&(r&=-513),65536&r&&!oe.isDir(n.mode))throw new oe.ErrnoError(54);if(!s){var a=oe.mayOpen(n,r);if(a)throw new oe.ErrnoError(a)}512&r&&!s&&oe.truncate(n,0),r&=-131713;var o=oe.createStream({node:n,path:oe.getPath(n),flags:r,seekable:!0,position:0,stream_ops:n.stream_ops,ungotten:[],error:!1});return o.stream_ops.open&&o.stream_ops.open(o),!e.logReadFiles||1&r||(oe.readFiles||(oe.readFiles={}),t in oe.readFiles||(oe.readFiles[t]=1)),o},close:e=>{if(oe.isClosed(e))throw new oe.ErrnoError(8);e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(e){throw e}finally{oe.closeStream(e.fd)}e.fd=null},isClosed:e=>null===e.fd,llseek:(e,t,r)=>{if(oe.isClosed(e))throw new oe.ErrnoError(8);if(!e.seekable||!e.stream_ops.llseek)throw new oe.ErrnoError(70);if(0!=r&&1!=r&&2!=r)throw new oe.ErrnoError(28);return e.position=e.stream_ops.llseek(e,t,r),e.ungotten=[],e.position},read:(e,t,r,i,n)=>{if(i<0||n<0)throw new oe.ErrnoError(28);if(oe.isClosed(e))throw new oe.ErrnoError(8);if(1==(2097155&e.flags))throw new oe.ErrnoError(8);if(oe.isDir(e.node.mode))throw new oe.ErrnoError(31);if(!e.stream_ops.read)throw new oe.ErrnoError(28);var s=void 0!==n;if(s){if(!e.seekable)throw new oe.ErrnoError(70)}else n=e.position;var a=e.stream_ops.read(e,t,r,i,n);return s||(e.position+=a),a},write:(e,t,r,i,n,s)=>{if(i<0||n<0)throw new oe.ErrnoError(28);if(oe.isClosed(e))throw new oe.ErrnoError(8);if(0==(2097155&e.flags))throw new oe.ErrnoError(8);if(oe.isDir(e.node.mode))throw new oe.ErrnoError(31);if(!e.stream_ops.write)throw new oe.ErrnoError(28);e.seekable&&1024&e.flags&&oe.llseek(e,0,2);var a=void 0!==n;if(a){if(!e.seekable)throw new oe.ErrnoError(70)}else n=e.position;var o=e.stream_ops.write(e,t,r,i,n,s);return a||(e.position+=o),o},allocate:(e,t,r)=>{if(oe.isClosed(e))throw new oe.ErrnoError(8);if(t<0||r<=0)throw new oe.ErrnoError(28);if(0==(2097155&e.flags))throw new oe.ErrnoError(8);if(!oe.isFile(e.node.mode)&&!oe.isDir(e.node.mode))throw new oe.ErrnoError(43);if(!e.stream_ops.allocate)throw new oe.ErrnoError(138);e.stream_ops.allocate(e,t,r)},mmap:(e,t,r,i,n)=>{if(0!=(2&i)&&0==(2&n)&&2!=(2097155&e.flags))throw new oe.ErrnoError(2);if(1==(2097155&e.flags))throw new oe.ErrnoError(2);if(!e.stream_ops.mmap)throw new oe.ErrnoError(43);return e.stream_ops.mmap(e,t,r,i,n)},msync:(e,t,r,i,n)=>e&&e.stream_ops.msync?e.stream_ops.msync(e,t,r,i,n):0,munmap:e=>0,ioctl:(e,t,r)=>{if(!e.stream_ops.ioctl)throw new oe.ErrnoError(59);return e.stream_ops.ioctl(e,t,r)},readFile:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(t.flags=t.flags||0,t.encoding=t.encoding||"binary","utf8"!==t.encoding&&"binary"!==t.encoding)throw new Error('Invalid encoding type "'+t.encoding+'"');var r,i=oe.open(e,t.flags),n=oe.stat(e),s=n.size,a=new Uint8Array(s);return oe.read(i,a,0,s,0),"utf8"===t.encoding?r=F(a,0):"binary"===t.encoding&&(r=a),oe.close(i),r},writeFile:function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};r.flags=r.flags||577;var i=oe.open(e,r.flags,r.mode);if("string"==typeof t){var n=new Uint8Array(M(t)+1),s=L(t,n,0,n.length);oe.write(i,n,0,s,void 0,r.canOwn)}else{if(!ArrayBuffer.isView(t))throw new Error("Unsupported data type");oe.write(i,t,0,t.byteLength,void 0,r.canOwn)}oe.close(i)},cwd:()=>oe.currentPath,chdir:e=>{var t=oe.lookupPath(e,{follow:!0});if(null===t.node)throw new oe.ErrnoError(44);if(!oe.isDir(t.node.mode))throw new oe.ErrnoError(54);var r=oe.nodePermissions(t.node,"x");if(r)throw new oe.ErrnoError(r);oe.currentPath=t.path},createDefaultDirectories:()=>{oe.mkdir("/tmp"),oe.mkdir("/home"),oe.mkdir("/home/<USER>")},createDefaultDevices:()=>{oe.mkdir("/dev"),oe.registerDevice(oe.makedev(1,3),{read:()=>0,write:(e,t,r,i,n)=>i}),oe.mkdev("/dev/null",oe.makedev(1,3)),ne.register(oe.makedev(5,0),ne.default_tty_ops),ne.register(oe.makedev(6,0),ne.default_tty1_ops),oe.mkdev("/dev/tty",oe.makedev(5,0)),oe.mkdev("/dev/tty1",oe.makedev(6,0));var e=function(){if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues){var e=new Uint8Array(1);return()=>(crypto.getRandomValues(e),e[0])}if(p)try{var t=require("crypto");return()=>t.randomBytes(1)[0]}catch(e){}return()=>Y("randomDevice")}();oe.createDevice("/dev","random",e),oe.createDevice("/dev","urandom",e),oe.mkdir("/dev/shm"),oe.mkdir("/dev/shm/tmp")},createSpecialDirectories:()=>{oe.mkdir("/proc");var e=oe.mkdir("/proc/self");oe.mkdir("/proc/self/fd"),oe.mount({mount:()=>{var t=oe.createNode(e,"fd",16895,73);return t.node_ops={lookup:(e,t)=>{var r=+t,i=oe.getStream(r);if(!i)throw new oe.ErrnoError(8);var n={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:()=>i.path}};return n.parent=n,n}},t}},{},"/proc/self/fd")},createStandardStreams:()=>{e.stdin?oe.createDevice("/dev","stdin",e.stdin):oe.symlink("/dev/tty","/dev/stdin"),e.stdout?oe.createDevice("/dev","stdout",null,e.stdout):oe.symlink("/dev/tty","/dev/stdout"),e.stderr?oe.createDevice("/dev","stderr",null,e.stderr):oe.symlink("/dev/tty1","/dev/stderr"),oe.open("/dev/stdin",0),oe.open("/dev/stdout",1),oe.open("/dev/stderr",1)},ensureErrnoError:()=>{oe.ErrnoError||(oe.ErrnoError=function(e,t){this.node=t,this.setErrno=function(e){this.errno=e},this.setErrno(e),this.message="FS error"},oe.ErrnoError.prototype=new Error,oe.ErrnoError.prototype.constructor=oe.ErrnoError,[44].forEach((e=>{oe.genericErrors[e]=new oe.ErrnoError(e),oe.genericErrors[e].stack="<generic error, no stack>"})))},staticInit:()=>{oe.ensureErrnoError(),oe.nameTable=new Array(4096),oe.mount(ae,{},"/"),oe.createDefaultDirectories(),oe.createDefaultDevices(),oe.createSpecialDirectories(),oe.filesystems={MEMFS:ae}},init:(t,r,i)=>{oe.init.initialized=!0,oe.ensureErrnoError(),e.stdin=t||e.stdin,e.stdout=r||e.stdout,e.stderr=i||e.stderr,oe.createStandardStreams()},quit:()=>{oe.init.initialized=!1;for(var e=0;e<oe.streams.length;e++){var t=oe.streams[e];t&&oe.close(t)}},getMode:(e,t)=>{var r=0;return e&&(r|=365),t&&(r|=146),r},findObject:(e,t)=>{var r=oe.analyzePath(e,t);return r.exists?r.object:null},analyzePath:(e,t)=>{try{e=(i=oe.lookupPath(e,{follow:!t})).path}catch(e){}var r={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var i=oe.lookupPath(e,{parent:!0});r.parentExists=!0,r.parentPath=i.path,r.parentObject=i.node,r.name=te.basename(e),i=oe.lookupPath(e,{follow:!t}),r.exists=!0,r.path=i.path,r.object=i.node,r.name=i.node.name,r.isRoot="/"===i.path}catch(e){r.error=e.errno}return r},createPath:(e,t,r,i)=>{e="string"==typeof e?e:oe.getPath(e);for(var n=t.split("/").reverse();n.length;){var s=n.pop();if(s){var a=te.join2(e,s);try{oe.mkdir(a)}catch(e){}e=a}}return a},createFile:(e,t,r,i,n)=>{var s=te.join2("string"==typeof e?e:oe.getPath(e),t),a=oe.getMode(i,n);return oe.create(s,a)},createDataFile:(e,t,r,i,n,s)=>{var a=t;e&&(e="string"==typeof e?e:oe.getPath(e),a=t?te.join2(e,t):e);var o=oe.getMode(i,n),d=oe.create(a,o);if(r){if("string"==typeof r){for(var l=new Array(r.length),c=0,u=r.length;c<u;++c)l[c]=r.charCodeAt(c);r=l}oe.chmod(d,146|o);var h=oe.open(d,577);oe.write(h,r,0,r.length,0,s),oe.close(h),oe.chmod(d,o)}return d},createDevice:(e,t,r,i)=>{var n=te.join2("string"==typeof e?e:oe.getPath(e),t),s=oe.getMode(!!r,!!i);oe.createDevice.major||(oe.createDevice.major=64);var a=oe.makedev(oe.createDevice.major++,0);return oe.registerDevice(a,{open:e=>{e.seekable=!1},close:e=>{i&&i.buffer&&i.buffer.length&&i(10)},read:(e,t,i,n,s)=>{for(var a=0,o=0;o<n;o++){var d;try{d=r()}catch(e){throw new oe.ErrnoError(29)}if(void 0===d&&0===a)throw new oe.ErrnoError(6);if(null==d)break;a++,t[i+o]=d}return a&&(e.node.timestamp=Date.now()),a},write:(e,t,r,n,s)=>{for(var a=0;a<n;a++)try{i(t[r+a])}catch(e){throw new oe.ErrnoError(29)}return n&&(e.node.timestamp=Date.now()),a}}),oe.mkdev(n,s,a)},forceLoadFile:e=>{if(e.isDevice||e.isFolder||e.link||e.contents)return!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!n)throw new Error("Cannot load without read() or XMLHttpRequest.");try{e.contents=ie(n(e.url),!0),e.usedBytes=e.contents.length}catch(e){throw new oe.ErrnoError(29)}},createLazyFile:(e,t,r,i,n)=>{function s(){this.lengthKnown=!1,this.chunks=[]}if(s.prototype.get=function(e){if(!(e>this.length-1||e<0)){var t=e%this.chunkSize,r=e/this.chunkSize|0;return this.getter(r)[t]}},s.prototype.setDataGetter=function(e){this.getter=e},s.prototype.cacheLength=function(){var e=new XMLHttpRequest;if(e.open("HEAD",r,!1),e.send(null),!(e.status>=200&&e.status<300||304===e.status))throw new Error("Couldn't load "+r+". Status: "+e.status);var t,i=Number(e.getResponseHeader("Content-length")),n=(t=e.getResponseHeader("Accept-Ranges"))&&"bytes"===t,s=(t=e.getResponseHeader("Content-Encoding"))&&"gzip"===t,a=1048576;n||(a=i);var o=this;o.setDataGetter((e=>{var t=e*a,n=(e+1)*a-1;if(n=Math.min(n,i-1),void 0===o.chunks[e]&&(o.chunks[e]=((e,t)=>{if(e>t)throw new Error("invalid range ("+e+", "+t+") or no bytes requested!");if(t>i-1)throw new Error("only "+i+" bytes available! programmer error!");var n=new XMLHttpRequest;if(n.open("GET",r,!1),i!==a&&n.setRequestHeader("Range","bytes="+e+"-"+t),n.responseType="arraybuffer",n.overrideMimeType&&n.overrideMimeType("text/plain; charset=x-user-defined"),n.send(null),!(n.status>=200&&n.status<300||304===n.status))throw new Error("Couldn't load "+r+". Status: "+n.status);return void 0!==n.response?new Uint8Array(n.response||[]):ie(n.responseText||"",!0)})(t,n)),void 0===o.chunks[e])throw new Error("doXHR failed!");return o.chunks[e]})),!s&&i||(a=i=1,i=this.getter(0).length,a=i,y("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=i,this._chunkSize=a,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){if(!f)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var a=new s;Object.defineProperties(a,{length:{get:function(){return this.lengthKnown||this.cacheLength(),this._length}},chunkSize:{get:function(){return this.lengthKnown||this.cacheLength(),this._chunkSize}}});var o={isDevice:!1,contents:a}}else o={isDevice:!1,url:r};var d=oe.createFile(e,t,o,i,n);o.contents?d.contents=o.contents:o.url&&(d.contents=null,d.url=o.url),Object.defineProperties(d,{usedBytes:{get:function(){return this.contents.length}}});var l={};function c(e,t,r,i,n){var s=e.node.contents;if(n>=s.length)return 0;var a=Math.min(s.length-n,i);if(s.slice)for(var o=0;o<a;o++)t[r+o]=s[n+o];else for(o=0;o<a;o++)t[r+o]=s.get(n+o);return a}return Object.keys(d.stream_ops).forEach((e=>{var t=d.stream_ops[e];l[e]=function(){return oe.forceLoadFile(d),t.apply(null,arguments)}})),l.read=(e,t,r,i,n)=>(oe.forceLoadFile(d),c(e,t,r,i,n)),l.mmap=(e,t,r,i,n)=>{oe.forceLoadFile(d);var s=se(t);if(!s)throw new oe.ErrnoError(48);return c(e,E,s,t,r),{ptr:s,allocated:!0}},d.stream_ops=l,d},createPreloadedFile:(e,t,r,i,n,a,o,d,l,c)=>{var u=t?re.resolve(te.join2(e,t)):e;function h(r){function s(r){c&&c(),d||oe.createDataFile(e,t,r,i,n,l),a&&a(),q()}Browser.handledByPreloadPlugin(r,u,s,(()=>{o&&o(),q()}))||s(r)}j(),"string"==typeof r?function(e,t,r,i){var n=i?"":"al "+e;s(e,(r=>{w(r,'Loading data file "'+e+'" failed (no arrayBuffer).'),t(new Uint8Array(r)),n&&q()}),(t=>{if(!r)throw'Loading data file "'+e+'" failed.';r()})),n&&j()}(r,(e=>h(e)),o):h(r)},indexedDB:()=>window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB,DB_NAME:()=>"EM_FS_"+window.location.pathname,DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:(e,t,r)=>{t=t||(()=>{}),r=r||(()=>{});var i=oe.indexedDB();try{var n=i.open(oe.DB_NAME(),oe.DB_VERSION)}catch(e){return r(e)}n.onupgradeneeded=()=>{y("creating db"),n.result.createObjectStore(oe.DB_STORE_NAME)},n.onsuccess=()=>{var i=n.result.transaction([oe.DB_STORE_NAME],"readwrite"),s=i.objectStore(oe.DB_STORE_NAME),a=0,o=0,d=e.length;function l(){0==o?t():r()}e.forEach((e=>{var t=s.put(oe.analyzePath(e).object.contents,e);t.onsuccess=()=>{++a+o==d&&l()},t.onerror=()=>{o++,a+o==d&&l()}})),i.onerror=r},n.onerror=r},loadFilesFromDB:(e,t,r)=>{t=t||(()=>{}),r=r||(()=>{});var i=oe.indexedDB();try{var n=i.open(oe.DB_NAME(),oe.DB_VERSION)}catch(e){return r(e)}n.onupgradeneeded=r,n.onsuccess=()=>{var i=n.result;try{var s=i.transaction([oe.DB_STORE_NAME],"readonly")}catch(e){return void r(e)}var a=s.objectStore(oe.DB_STORE_NAME),o=0,d=0,l=e.length;function c(){0==d?t():r()}e.forEach((e=>{var t=a.get(e);t.onsuccess=()=>{oe.analyzePath(e).exists&&oe.unlink(e),oe.createDataFile(te.dirname(e),te.basename(e),t.result,!0,!0,!0),++o+d==l&&c()},t.onerror=()=>{d++,o+d==l&&c()}})),s.onerror=r},n.onerror=r}},de={DEFAULT_POLLMASK:5,calculateAt:function(e,t,r){if(te.isAbs(t))return t;var i;if(-100===e)i=oe.cwd();else{var n=oe.getStream(e);if(!n)throw new oe.ErrnoError(8);i=n.path}if(0==t.length){if(!r)throw new oe.ErrnoError(44);return i}return te.join2(i,t)},doStat:function(e,t,r){try{var i=e(t)}catch(e){if(e&&e.node&&te.normalize(t)!==te.normalize(oe.getPath(e.node)))return-54;throw e}return U[r>>2]=i.dev,U[r+4>>2]=0,U[r+8>>2]=i.ino,U[r+12>>2]=i.mode,U[r+16>>2]=i.nlink,U[r+20>>2]=i.uid,U[r+24>>2]=i.gid,U[r+28>>2]=i.rdev,U[r+32>>2]=0,O=[i.size>>>0,(N=i.size,+Math.abs(N)>=1?N>0?(0|Math.min(+Math.floor(N/4294967296),4294967295))>>>0:~~+Math.ceil((N-+(~~N>>>0))/4294967296)>>>0:0)],U[r+40>>2]=O[0],U[r+44>>2]=O[1],U[r+48>>2]=4096,U[r+52>>2]=i.blocks,O=[Math.floor(i.atime.getTime()/1e3)>>>0,(N=Math.floor(i.atime.getTime()/1e3),+Math.abs(N)>=1?N>0?(0|Math.min(+Math.floor(N/4294967296),4294967295))>>>0:~~+Math.ceil((N-+(~~N>>>0))/4294967296)>>>0:0)],U[r+56>>2]=O[0],U[r+60>>2]=O[1],U[r+64>>2]=0,O=[Math.floor(i.mtime.getTime()/1e3)>>>0,(N=Math.floor(i.mtime.getTime()/1e3),+Math.abs(N)>=1?N>0?(0|Math.min(+Math.floor(N/4294967296),4294967295))>>>0:~~+Math.ceil((N-+(~~N>>>0))/4294967296)>>>0:0)],U[r+72>>2]=O[0],U[r+76>>2]=O[1],U[r+80>>2]=0,O=[Math.floor(i.ctime.getTime()/1e3)>>>0,(N=Math.floor(i.ctime.getTime()/1e3),+Math.abs(N)>=1?N>0?(0|Math.min(+Math.floor(N/4294967296),4294967295))>>>0:~~+Math.ceil((N-+(~~N>>>0))/4294967296)>>>0:0)],U[r+88>>2]=O[0],U[r+92>>2]=O[1],U[r+96>>2]=0,O=[i.ino>>>0,(N=i.ino,+Math.abs(N)>=1?N>0?(0|Math.min(+Math.floor(N/4294967296),4294967295))>>>0:~~+Math.ceil((N-+(~~N>>>0))/4294967296)>>>0:0)],U[r+104>>2]=O[0],U[r+108>>2]=O[1],0},doMsync:function(e,t,r,i,n){var s=A.slice(e,e+r);oe.msync(t,s,n,r,i)},varargs:void 0,get:function(){return de.varargs+=4,U[de.varargs-4>>2]},getStr:function(e){return P(e)},getStreamFromFD:function(e){var t=oe.getStream(e);if(!t)throw new oe.ErrnoError(8);return t}};function le(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}var ce=void 0;function ue(e){for(var t="",r=e;A[r];)t+=ce[A[r++]];return t}var he={},fe={},pe={};function _e(e){if(void 0===e)return"_unknown";var t=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return t>=48&&t<=57?"_"+e:e}function me(e,t){return e=_e(e),new Function("body","return function "+e+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(t)}function ge(e,t){var r=me(t,(function(e){this.name=t,this.message=e;var r=new Error(e).stack;void 0!==r&&(this.stack=this.toString()+"\n"+r.replace(/^Error(:[^\n]*)?\n/,""))}));return r.prototype=Object.create(e.prototype),r.prototype.constructor=r,r.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},r}var ye=void 0;function ve(e){throw new ye(e)}var be=void 0;function we(e){throw new be(e)}function Se(e,t,r){function i(t){var i=r(t);i.length!==e.length&&we("Mismatched type converter count");for(var n=0;n<e.length;++n)Ee(e[n],i[n])}e.forEach((function(e){pe[e]=t}));var n=new Array(t.length),s=[],a=0;t.forEach(((e,t)=>{fe.hasOwnProperty(e)?n[t]=fe[e]:(s.push(e),he.hasOwnProperty(e)||(he[e]=[]),he[e].push((()=>{n[t]=fe[e],++a===s.length&&i(n)})))})),0===s.length&&i(n)}function Ee(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");var i=t.name;if(e||ve('type "'+i+'" must have a positive integer typeid pointer'),fe.hasOwnProperty(e)){if(r.ignoreDuplicateRegistrations)return;ve("Cannot register type '"+i+"' twice")}if(fe[e]=t,delete pe[e],he.hasOwnProperty(e)){var n=he[e];delete he[e],n.forEach((e=>e()))}}function Ae(e){if(!(this instanceof je))return!1;if(!(e instanceof je))return!1;for(var t=this.$$.ptrType.registeredClass,r=this.$$.ptr,i=e.$$.ptrType.registeredClass,n=e.$$.ptr;t.baseClass;)r=t.upcast(r),t=t.baseClass;for(;i.baseClass;)n=i.upcast(n),i=i.baseClass;return t===i&&r===n}function Be(e){ve(e.$$.ptrType.registeredClass.name+" instance already deleted")}var xe=!1;function Ue(e){}function Te(e){e.count.value-=1,0===e.count.value&&function(e){e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)}(e)}function ke(e,t,r){if(t===r)return e;if(void 0===r.baseClass)return null;var i=ke(e,t,r.baseClass);return null===i?null:r.downcast(i)}var Ce={};function De(){return Object.keys(Re).length}function Ie(){var e=[];for(var t in Re)Re.hasOwnProperty(t)&&e.push(Re[t]);return e}var Fe=[];function Pe(){for(;Fe.length;){var e=Fe.pop();e.$$.deleteScheduled=!1,e.delete()}}var Le=void 0;function Me(e){Le=e,Fe.length&&Le&&Le(Pe)}var Re={};function ze(e,t){return t=function(e,t){for(void 0===t&&ve("ptr should not be undefined");e.baseClass;)t=e.upcast(t),e=e.baseClass;return t}(e,t),Re[t]}function Ne(e,t){return t.ptrType&&t.ptr||we("makeClassHandle requires ptr and ptrType"),!!t.smartPtrType!=!!t.smartPtr&&we("Both smartPtrType and smartPtr must be specified"),t.count={value:1},Ge(Object.create(e,{$$:{value:t}}))}function Oe(e){var t=this.getPointee(e);if(!t)return this.destructor(e),null;var r=ze(this.registeredClass,t);if(void 0!==r){if(0===r.$$.count.value)return r.$$.ptr=t,r.$$.smartPtr=e,r.clone();var i=r.clone();return this.destructor(e),i}function n(){return this.isSmartPointer?Ne(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:t,smartPtrType:this,smartPtr:e}):Ne(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var s,a=this.registeredClass.getActualType(t),o=Ce[a];if(!o)return n.call(this);s=this.isConst?o.constPointerType:o.pointerType;var d=ke(t,this.registeredClass,s.registeredClass);return null===d?n.call(this):this.isSmartPointer?Ne(s.registeredClass.instancePrototype,{ptrType:s,ptr:d,smartPtrType:this,smartPtr:e}):Ne(s.registeredClass.instancePrototype,{ptrType:s,ptr:d})}function Ge(e){return"undefined"==typeof FinalizationRegistry?(Ge=e=>e,e):(xe=new FinalizationRegistry((e=>{Te(e.$$)})),Ge=e=>{var t=e.$$;if(t.smartPtr){var r={$$:t};xe.register(e,r,e)}return e},Ue=e=>xe.unregister(e),Ge(e))}function He(){if(this.$$.ptr||Be(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e,t=Ge(Object.create(Object.getPrototypeOf(this),{$$:{value:(e=this.$$,{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType})}}));return t.$$.count.value+=1,t.$$.deleteScheduled=!1,t}function $e(){this.$$.ptr||Be(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&ve("Object already scheduled for deletion"),Ue(this),Te(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function Ve(){return!this.$$.ptr}function We(){return this.$$.ptr||Be(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&ve("Object already scheduled for deletion"),Fe.push(this),1===Fe.length&&Le&&Le(Pe),this.$$.deleteScheduled=!0,this}function je(){}function qe(e,t,r){if(void 0===e[t].overloadTable){var i=e[t];e[t]=function(){return e[t].overloadTable.hasOwnProperty(arguments.length)||ve("Function '"+r+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[t].overloadTable+")!"),e[t].overloadTable[arguments.length].apply(this,arguments)},e[t].overloadTable=[],e[t].overloadTable[i.argCount]=i}}function Ye(e,t,r,i,n,s,a,o){this.name=e,this.constructor=t,this.instancePrototype=r,this.rawDestructor=i,this.baseClass=n,this.getActualType=s,this.upcast=a,this.downcast=o,this.pureVirtualFunctions=[]}function Ke(e,t,r){for(;t!==r;)t.upcast||ve("Expected null or instance of "+r.name+", got an instance of "+t.name),e=t.upcast(e),t=t.baseClass;return e}function Xe(e,t){if(null===t)return this.isReference&&ve("null is not a valid "+this.name),0;t.$$||ve('Cannot pass "'+wt(t)+'" as a '+this.name),t.$$.ptr||ve("Cannot pass deleted object as a pointer of type "+this.name);var r=t.$$.ptrType.registeredClass;return Ke(t.$$.ptr,r,this.registeredClass)}function Ze(e,t){var r;if(null===t)return this.isReference&&ve("null is not a valid "+this.name),this.isSmartPointer?(r=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,r),r):0;t.$$||ve('Cannot pass "'+wt(t)+'" as a '+this.name),t.$$.ptr||ve("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&t.$$.ptrType.isConst&&ve("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);var i=t.$$.ptrType.registeredClass;if(r=Ke(t.$$.ptr,i,this.registeredClass),this.isSmartPointer)switch(void 0===t.$$.smartPtr&&ve("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:t.$$.smartPtrType===this?r=t.$$.smartPtr:ve("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:r=t.$$.smartPtr;break;case 2:if(t.$$.smartPtrType===this)r=t.$$.smartPtr;else{var n=t.clone();r=this.rawShare(r,bt.toHandle((function(){n.delete()}))),null!==e&&e.push(this.rawDestructor,r)}break;default:ve("Unsupporting sharing policy")}return r}function Je(e,t){if(null===t)return this.isReference&&ve("null is not a valid "+this.name),0;t.$$||ve('Cannot pass "'+wt(t)+'" as a '+this.name),t.$$.ptr||ve("Cannot pass deleted object as a pointer of type "+this.name),t.$$.ptrType.isConst&&ve("Cannot convert argument of type "+t.$$.ptrType.name+" to parameter type "+this.name);var r=t.$$.ptrType.registeredClass;return Ke(t.$$.ptr,r,this.registeredClass)}function Qe(e){return this.fromWireType(U[e>>2])}function et(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e}function tt(e){this.rawDestructor&&this.rawDestructor(e)}function rt(e){null!==e&&e.delete()}function it(e,t,r,i,n,s,a,o,d,l,c){this.name=e,this.registeredClass=t,this.isReference=r,this.isConst=i,this.isSmartPointer=n,this.pointeeType=s,this.sharingPolicy=a,this.rawGetPointee=o,this.rawConstructor=d,this.rawShare=l,this.rawDestructor=c,n||void 0!==t.baseClass?this.toWireType=Ze:i?(this.toWireType=Xe,this.destructorFunction=null):(this.toWireType=Je,this.destructorFunction=null)}var nt=[];function st(e){var t=nt[e];return t||(e>=nt.length&&(nt.length=e+1),nt[e]=t=D.get(e)),t}function at(t,r,i){return t.includes("j")?function(t,r,i){var n=e["dynCall_"+t];return i&&i.length?n.apply(null,[r].concat(i)):n.call(null,r)}(t,r,i):st(r).apply(null,i)}function ot(e,t){var r,i,n,s=(e=ue(e)).includes("j")?(r=e,i=t,n=[],function(){return n.length=0,Object.assign(n,arguments),at(r,i,n)}):st(t);return"function"!=typeof s&&ve("unknown function pointer with signature "+e+": "+t),s}var dt=void 0;function lt(e){var t=Ht(e),r=ue(t);return Ot(t),r}function ct(e,t){var r=[],i={};throw t.forEach((function e(t){i[t]||fe[t]||(pe[t]?pe[t].forEach(e):(r.push(t),i[t]=!0))})),new dt(e+": "+r.map(lt).join([", "]))}function ut(e,t){for(var r=[],i=0;i<e;i++)r.push(T[t+4*i>>2]);return r}function ht(e){for(;e.length;){var t=e.pop();e.pop()(t)}}function ft(e,t){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var r=me(e.name||"unknownFunctionName",(function(){}));r.prototype=e.prototype;var i=new r,n=e.apply(i,t);return n instanceof Object?n:i}function pt(e,t,r,i,n){var s=t.length;s<2&&ve("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var a=null!==t[1]&&null!==r,o=!1,d=1;d<t.length;++d)if(null!==t[d]&&void 0===t[d].destructorFunction){o=!0;break}var l="void"!==t[0].name,c="",u="";for(d=0;d<s-2;++d)c+=(0!==d?", ":"")+"arg"+d,u+=(0!==d?", ":"")+"arg"+d+"Wired";var h="return function "+_e(e)+"("+c+") {\nif (arguments.length !== "+(s-2)+") {\nthrowBindingError('function "+e+" called with ' + arguments.length + ' arguments, expected "+(s-2)+" args!');\n}\n";o&&(h+="var destructors = [];\n");var f=o?"destructors":"null",p=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],_=[ve,i,n,ht,t[0],t[1]];for(a&&(h+="var thisWired = classParam.toWireType("+f+", this);\n"),d=0;d<s-2;++d)h+="var arg"+d+"Wired = argType"+d+".toWireType("+f+", arg"+d+"); // "+t[d+2].name+"\n",p.push("argType"+d),_.push(t[d+2]);if(a&&(u="thisWired"+(u.length>0?", ":"")+u),h+=(l?"var rv = ":"")+"invoker(fn"+(u.length>0?", ":"")+u+");\n",o)h+="runDestructors(destructors);\n";else for(d=a?1:2;d<t.length;++d){var m=1===d?"thisWired":"arg"+(d-2)+"Wired";null!==t[d].destructorFunction&&(h+=m+"_dtor("+m+"); // "+t[d].name+"\n",p.push(m+"_dtor"),_.push(t[d].destructorFunction))}return l&&(h+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),h+="}\n",p.push(h),ft(Function,p).apply(null,_)}var _t=[],mt=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function gt(e){e>4&&0==--mt[e].refcount&&(mt[e]=void 0,_t.push(e))}function yt(){for(var e=0,t=5;t<mt.length;++t)void 0!==mt[t]&&++e;return e}function vt(){for(var e=5;e<mt.length;++e)if(void 0!==mt[e])return mt[e];return null}var bt={toValue:e=>(e||ve("Cannot use deleted val. handle = "+e),mt[e].value),toHandle:e=>{switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var t=_t.length?_t.pop():mt.length;return mt[t]={refcount:1,value:e},t}}};function wt(e){if(null===e)return"null";var t=typeof e;return"object"===t||"array"===t||"function"===t?e.toString():""+e}function St(e,t){switch(t){case 2:return function(e){return this.fromWireType(k[e>>2])};case 3:return function(e){return this.fromWireType(C[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}function Et(e,t,r){switch(t){case 0:return r?function(e){return E[e]}:function(e){return A[e]};case 1:return r?function(e){return B[e>>1]}:function(e){return x[e>>1]};case 2:return r?function(e){return U[e>>2]}:function(e){return T[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}var At="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function Bt(e,t){for(var r=e,i=r>>1,n=i+t/2;!(i>=n)&&x[i];)++i;if((r=i<<1)-e>32&&At)return At.decode(A.subarray(e,r));for(var s="",a=0;!(a>=t/2);++a){var o=B[e+2*a>>1];if(0==o)break;s+=String.fromCharCode(o)}return s}function xt(e,t,r){if(void 0===r&&(r=2147483647),r<2)return 0;for(var i=t,n=(r-=2)<2*e.length?r/2:e.length,s=0;s<n;++s){var a=e.charCodeAt(s);B[t>>1]=a,t+=2}return B[t>>1]=0,t-i}function Ut(e){return 2*e.length}function Tt(e,t){for(var r=0,i="";!(r>=t/4);){var n=U[e+4*r>>2];if(0==n)break;if(++r,n>=65536){var s=n-65536;i+=String.fromCharCode(55296|s>>10,56320|1023&s)}else i+=String.fromCharCode(n)}return i}function kt(e,t,r){if(void 0===r&&(r=2147483647),r<4)return 0;for(var i=t,n=i+r-4,s=0;s<e.length;++s){var a=e.charCodeAt(s);if(a>=55296&&a<=57343&&(a=65536+((1023&a)<<10)|1023&e.charCodeAt(++s)),U[t>>2]=a,(t+=4)+4>n)break}return U[t>>2]=0,t-i}function Ct(e){for(var t=0,r=0;r<e.length;++r){var i=e.charCodeAt(r);i>=55296&&i<=57343&&++r,t+=4}return t}var Dt={},It=[],Ft=[],Pt={};function Lt(){if(!Lt.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:u||"./this.program"};for(var t in Pt)void 0===Pt[t]?delete e[t]:e[t]=Pt[t];var r=[];for(var t in e)r.push(t+"="+e[t]);Lt.strings=r}return Lt.strings}var Mt=function(e,t,r,i){e||(e=this),this.parent=e,this.mount=e.mount,this.mounted=null,this.id=oe.nextInode++,this.name=t,this.mode=r,this.node_ops={},this.stream_ops={},this.rdev=i},Rt=365,zt=146;Object.defineProperties(Mt.prototype,{read:{get:function(){return(this.mode&Rt)===Rt},set:function(e){e?this.mode|=Rt:this.mode&=-366}},write:{get:function(){return(this.mode&zt)===zt},set:function(e){e?this.mode|=zt:this.mode&=-147}},isFolder:{get:function(){return oe.isDir(this.mode)}},isDevice:{get:function(){return oe.isChrdev(this.mode)}}}),oe.FSNode=Mt,oe.staticInit(),function(){for(var e=new Array(256),t=0;t<256;++t)e[t]=String.fromCharCode(t);ce=e}(),ye=e.BindingError=ge(Error,"BindingError"),be=e.InternalError=ge(Error,"InternalError"),je.prototype.isAliasOf=Ae,je.prototype.clone=He,je.prototype.delete=$e,je.prototype.isDeleted=Ve,je.prototype.deleteLater=We,e.getInheritedInstanceCount=De,e.getLiveInheritedInstances=Ie,e.flushPendingDeletes=Pe,e.setDelayFunction=Me,it.prototype.getPointee=et,it.prototype.destructor=tt,it.prototype.argPackAdvance=8,it.prototype.readValueFromPointer=Qe,it.prototype.deleteObject=rt,it.prototype.fromWireType=Oe,dt=e.UnboundTypeError=ge(Error,"UnboundTypeError"),e.count_emval_handles=yt,e.get_first_emval=vt;var Nt={q:function(e){return Vt(e+24)+24},p:function(e,t,r){throw new ee(e).init(t,r),e},C:function(e,t,r){de.varargs=r;try{var i=de.getStreamFromFD(e);switch(t){case 0:return(n=de.get())<0?-28:oe.createStream(i,n).fd;case 1:case 2:case 6:case 7:return 0;case 3:return i.flags;case 4:var n=de.get();return i.flags|=n,0;case 5:return n=de.get(),B[n+0>>1]=2,0;case 16:case 8:default:return-28;case 9:return s=28,U[Gt()>>2]=s,-1}}catch(e){if(void 0===oe||!(e instanceof oe.ErrnoError))throw e;return-e.errno}var s},w:function(e,t,r,i){de.varargs=i;try{t=de.getStr(t),t=de.calculateAt(e,t);var n=i?de.get():0;return oe.open(t,r,n).fd}catch(e){if(void 0===oe||!(e instanceof oe.ErrnoError))throw e;return-e.errno}},u:function(e,t,r,i,n){},E:function(e,t,r,i,n){var s=le(r);Ee(e,{name:t=ue(t),fromWireType:function(e){return!!e},toWireType:function(e,t){return t?i:n},argPackAdvance:8,readValueFromPointer:function(e){var i;if(1===r)i=E;else if(2===r)i=B;else{if(4!==r)throw new TypeError("Unknown boolean type size: "+t);i=U}return this.fromWireType(i[e>>s])},destructorFunction:null})},t:function(t,r,i,n,s,a,o,d,l,c,u,h,f){u=ue(u),a=ot(s,a),d&&(d=ot(o,d)),c&&(c=ot(l,c)),f=ot(h,f);var p=_e(u);!function(t,r,i){e.hasOwnProperty(t)?((void 0===i||void 0!==e[t].overloadTable&&void 0!==e[t].overloadTable[i])&&ve("Cannot register public name '"+t+"' twice"),qe(e,t,t),e.hasOwnProperty(i)&&ve("Cannot register multiple overloads of a function with the same number of arguments ("+i+")!"),e[t].overloadTable[i]=r):(e[t]=r,void 0!==i&&(e[t].numArguments=i))}(p,(function(){ct("Cannot construct "+u+" due to unbound types",[n])})),Se([t,r,i],n?[n]:[],(function(r){var i,s;r=r[0],s=n?(i=r.registeredClass).instancePrototype:je.prototype;var o=me(p,(function(){if(Object.getPrototypeOf(this)!==l)throw new ye("Use 'new' to construct "+u);if(void 0===h.constructor_body)throw new ye(u+" has no accessible constructor");var e=h.constructor_body[arguments.length];if(void 0===e)throw new ye("Tried to invoke ctor of "+u+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(h.constructor_body).toString()+") parameters instead!");return e.apply(this,arguments)})),l=Object.create(s,{constructor:{value:o}});o.prototype=l;var h=new Ye(u,o,l,f,i,a,d,c),_=new it(u,h,!0,!1,!1),m=new it(u+"*",h,!1,!1,!1),g=new it(u+" const*",h,!1,!0,!1);return Ce[t]={pointerType:m,constPointerType:g},function(t,r,i){e.hasOwnProperty(t)||we("Replacing nonexistant public symbol"),void 0!==e[t].overloadTable&&void 0!==i?e[t].overloadTable[i]=r:(e[t]=r,e[t].argCount=i)}(p,o),[_,m,g]}))},r:function(e,t,r,i,n,s){w(t>0);var a=ut(t,r);n=ot(i,n),Se([],[e],(function(e){var r="constructor "+(e=e[0]).name;if(void 0===e.registeredClass.constructor_body&&(e.registeredClass.constructor_body=[]),void 0!==e.registeredClass.constructor_body[t-1])throw new ye("Cannot register multiple constructors with identical number of parameters ("+(t-1)+") for class '"+e.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return e.registeredClass.constructor_body[t-1]=()=>{ct("Cannot construct "+e.name+" due to unbound types",a)},Se([],a,(function(i){return i.splice(1,0,null),e.registeredClass.constructor_body[t-1]=pt(r,i,null,n,s),[]})),[]}))},d:function(e,t,r,i,n,s,a,o){var d=ut(r,i);t=ue(t),s=ot(n,s),Se([],[e],(function(e){var i=(e=e[0]).name+"."+t;function n(){ct("Cannot call "+i+" due to unbound types",d)}t.startsWith("@@")&&(t=Symbol[t.substring(2)]),o&&e.registeredClass.pureVirtualFunctions.push(t);var l=e.registeredClass.instancePrototype,c=l[t];return void 0===c||void 0===c.overloadTable&&c.className!==e.name&&c.argCount===r-2?(n.argCount=r-2,n.className=e.name,l[t]=n):(qe(l,t,i),l[t].overloadTable[r-2]=n),Se([],d,(function(n){var o=pt(i,n,e,s,a);return void 0===l[t].overloadTable?(o.argCount=r-2,l[t]=o):l[t].overloadTable[r-2]=o,[]})),[]}))},D:function(e,t){Ee(e,{name:t=ue(t),fromWireType:function(e){var t=bt.toValue(e);return gt(e),t},toWireType:function(e,t){return bt.toHandle(t)},argPackAdvance:8,readValueFromPointer:Qe,destructorFunction:null})},n:function(e,t,r){var i=le(r);Ee(e,{name:t=ue(t),fromWireType:function(e){return e},toWireType:function(e,t){return t},argPackAdvance:8,readValueFromPointer:St(t,i),destructorFunction:null})},c:function(e,t,r,i,n){t=ue(t);var s=le(r),a=e=>e;if(0===i){var o=32-8*r;a=e=>e<<o>>>o}var d=t.includes("unsigned");Ee(e,{name:t,fromWireType:a,toWireType:d?function(e,t){return this.name,t>>>0}:function(e,t){return this.name,t},argPackAdvance:8,readValueFromPointer:Et(t,s,0!==i),destructorFunction:null})},b:function(e,t,r){var i=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];function n(e){var t=T,r=t[e>>=2],n=t[e+1];return new i(S,n,r)}Ee(e,{name:r=ue(r),fromWireType:n,argPackAdvance:8,readValueFromPointer:n},{ignoreDuplicateRegistrations:!0})},m:function(e,t){var r="std::string"===(t=ue(t));Ee(e,{name:t,fromWireType:function(e){var t,i=T[e>>2],n=e+4;if(r)for(var s=n,a=0;a<=i;++a){var o=n+a;if(a==i||0==A[o]){var d=P(s,o-s);void 0===t?t=d:(t+=String.fromCharCode(0),t+=d),s=o+1}}else{var l=new Array(i);for(a=0;a<i;++a)l[a]=String.fromCharCode(A[n+a]);t=l.join("")}return Ot(e),t},toWireType:function(e,t){var i;t instanceof ArrayBuffer&&(t=new Uint8Array(t));var n="string"==typeof t;n||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int8Array||ve("Cannot pass non-string to std::string"),i=r&&n?M(t):t.length;var s=Vt(4+i+1),a=s+4;if(T[s>>2]=i,r&&n)L(t,A,a,i+1);else if(n)for(var o=0;o<i;++o){var d=t.charCodeAt(o);d>255&&(Ot(a),ve("String has UTF-16 code units that do not fit in 8 bits")),A[a+o]=d}else for(o=0;o<i;++o)A[a+o]=t[o];return null!==e&&e.push(Ot,s),s},argPackAdvance:8,readValueFromPointer:Qe,destructorFunction:function(e){Ot(e)}})},h:function(e,t,r){var i,n,s,a,o;r=ue(r),2===t?(i=Bt,n=xt,a=Ut,s=()=>x,o=1):4===t&&(i=Tt,n=kt,a=Ct,s=()=>T,o=2),Ee(e,{name:r,fromWireType:function(e){for(var r,n=T[e>>2],a=s(),d=e+4,l=0;l<=n;++l){var c=e+4+l*t;if(l==n||0==a[c>>o]){var u=i(d,c-d);void 0===r?r=u:(r+=String.fromCharCode(0),r+=u),d=c+t}}return Ot(e),r},toWireType:function(e,i){"string"!=typeof i&&ve("Cannot pass non-string to C++ string type "+r);var s=a(i),d=Vt(4+s+t);return T[d>>2]=s>>o,n(i,d+4,s+t),null!==e&&e.push(Ot,d),d},argPackAdvance:8,readValueFromPointer:Qe,destructorFunction:function(e){Ot(e)}})},o:function(e,t){Ee(e,{isVoid:!0,name:t=ue(t),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,t){}})},f:function(){return Date.now()},g:function(e,t,r,i){var n,s;(e=It[e])(t=bt.toValue(t),r=void 0===(s=Dt[n=r])?ue(n):s,null,i)},j:gt,i:function(e,t){var r=function(e,t){for(var r,i,n,s=new Array(e),a=0;a<e;++a)s[a]=(r=T[t+4*a>>2],i="parameter "+a,n=void 0,void 0===(n=fe[r])&&ve(i+" has unknown type "+lt(r)),n);return s}(e,t),i=r[0],n=i.name+"_$"+r.slice(1).map((function(e){return e.name})).join("_")+"$",s=Ft[n];if(void 0!==s)return s;for(var a=["retType"],o=[i],d="",l=0;l<e-1;++l)d+=(0!==l?", ":"")+"arg"+l,a.push("argType"+l),o.push(r[1+l]);var c="return function "+_e("methodCaller_"+n)+"(handle, name, destructors, args) {\n",u=0;for(l=0;l<e-1;++l)c+="    var arg"+l+" = argType"+l+".readValueFromPointer(args"+(u?"+"+u:"")+");\n",u+=r[l+1].argPackAdvance;for(c+="    var rv = handle[name]("+d+");\n",l=0;l<e-1;++l)r[l+1].deleteObject&&(c+="    argType"+l+".deleteObject(arg"+l+");\n");i.isVoid||(c+="    return retType.toWireType(destructors, rv);\n"),c+="};\n",a.push(c);var h,f,p=ft(Function,a).apply(null,o);return h=p,f=It.length,It.push(h),s=f,Ft[n]=s,s},a:function(){Y("")},A:function(e,t,r){A.copyWithin(e,t,t+r)},v:function(e){A.length,Y("OOM")},y:function(e,t){var r=0;return Lt().forEach((function(i,n){var s=t+r;T[e+4*n>>2]=s,function(e,t,r){for(var i=0;i<e.length;++i)E[t++>>0]=e.charCodeAt(i);r||(E[t>>0]=0)}(i,s),r+=i.length+1})),0},z:function(e,t){var r=Lt();T[e>>2]=r.length;var i=0;return r.forEach((function(e){i+=e.length+1})),T[t>>2]=i,0},l:function(e){try{var t=de.getStreamFromFD(e);return oe.close(t),0}catch(e){if(void 0===oe||!(e instanceof oe.ErrnoError))throw e;return e.errno}},x:function(e,t){try{var r=de.getStreamFromFD(e),i=r.tty?2:oe.isDir(r.mode)?3:oe.isLink(r.mode)?7:4;return E[t>>0]=i,0}catch(e){if(void 0===oe||!(e instanceof oe.ErrnoError))throw e;return e.errno}},B:function(e,t,r,i){try{var n=function(e,t,r,i){for(var n=0,s=0;s<r;s++){var a=T[t>>2],o=T[t+4>>2];t+=8;var d=oe.read(e,E,a,o,i);if(d<0)return-1;if(n+=d,d<o)break}return n}(de.getStreamFromFD(e),t,r);return U[i>>2]=n,0}catch(e){if(void 0===oe||!(e instanceof oe.ErrnoError))throw e;return e.errno}},s:function(e,t,r,i,n){try{var s=(d=r)+2097152>>>0<4194305-!!(o=t)?(o>>>0)+4294967296*d:NaN;if(isNaN(s))return 61;var a=de.getStreamFromFD(e);return oe.llseek(a,s,i),O=[a.position>>>0,(N=a.position,+Math.abs(N)>=1?N>0?(0|Math.min(+Math.floor(N/4294967296),4294967295))>>>0:~~+Math.ceil((N-+(~~N>>>0))/4294967296)>>>0:0)],U[n>>2]=O[0],U[n+4>>2]=O[1],a.getdents&&0===s&&0===i&&(a.getdents=null),0}catch(e){if(void 0===oe||!(e instanceof oe.ErrnoError))throw e;return e.errno}var o,d},k:function(e,t,r,i){try{var n=function(e,t,r,i){for(var n=0,s=0;s<r;s++){var a=T[t>>2],o=T[t+4>>2];t+=8;var d=oe.write(e,E,a,o,i);if(d<0)return-1;n+=d}return n}(de.getStreamFromFD(e),t,r);return T[i>>2]=n,0}catch(e){if(void 0===oe||!(e instanceof oe.ErrnoError))throw e;return e.errno}},e:function(e){}};!function(){var t={a:Nt};function r(t,r){var i,n,s=t.exports;e.asm=s,g=e.asm.F,i=g.buffer,S=i,e.HEAP8=E=new Int8Array(i),e.HEAP16=B=new Int16Array(i),e.HEAP32=U=new Int32Array(i),e.HEAPU8=A=new Uint8Array(i),e.HEAPU16=x=new Uint16Array(i),e.HEAPU32=T=new Uint32Array(i),e.HEAPF32=k=new Float32Array(i),e.HEAPF64=C=new Float64Array(i),D=e.asm.I,n=e.asm.G,H.unshift(n),q()}function n(e){r(e.instance)}function a(e){return function(){if(!m&&(h||f)){if("function"==typeof fetch&&!X(R))return fetch(R,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+R+"'";return e.arrayBuffer()})).catch((function(){return Z(R)}));if(s)return new Promise((function(e,t){s(R,(function(t){e(new Uint8Array(t))}),t)}))}return Promise.resolve().then((function(){return Z(R)}))}().then((function(e){return WebAssembly.instantiate(e,t)})).then((function(e){return e})).then(e,(function(e){v("failed to asynchronously prepare wasm: "+e),Y(e)}))}if(j(),e.instantiateWasm)try{return e.instantiateWasm(t,r)}catch(e){return v("Module.instantiateWasm callback failed with error: "+e),!1}(m||"function"!=typeof WebAssembly.instantiateStreaming||K(R)||X(R)||p||"function"!=typeof fetch?a(n):fetch(R,{credentials:"same-origin"}).then((function(e){return WebAssembly.instantiateStreaming(e,t).then(n,(function(e){return v("wasm streaming compile failed: "+e),v("falling back to ArrayBuffer instantiation"),a(n)}))}))).catch(i)}(),e.___wasm_call_ctors=function(){return(e.___wasm_call_ctors=e.asm.G).apply(null,arguments)};var Ot=e._free=function(){return(Ot=e._free=e.asm.H).apply(null,arguments)},Gt=e.___errno_location=function(){return(Gt=e.___errno_location=e.asm.J).apply(null,arguments)},Ht=e.___getTypeName=function(){return(Ht=e.___getTypeName=e.asm.K).apply(null,arguments)};e.___embind_register_native_and_builtin_types=function(){return(e.___embind_register_native_and_builtin_types=e.asm.L).apply(null,arguments)};var $t,Vt=e._malloc=function(){return(Vt=e._malloc=e.asm.M).apply(null,arguments)},Wt=e._emscripten_builtin_memalign=function(){return(Wt=e._emscripten_builtin_memalign=e.asm.N).apply(null,arguments)},jt=e.___cxa_is_pointer_type=function(){return(jt=e.___cxa_is_pointer_type=e.asm.O).apply(null,arguments)};function qt(r){function i(){$t||($t=!0,e.calledRun=!0,b||(e.noFSInit||oe.init.initialized||oe.init(),oe.ignorePermissions=!1,Q(H),t(e),e.onRuntimeInitialized&&e.onRuntimeInitialized(),function(){if(e.postRun)for("function"==typeof e.postRun&&(e.postRun=[e.postRun]);e.postRun.length;)t=e.postRun.shift(),$.unshift(t);var t;Q($)}()))}V>0||(function(){if(e.preRun)for("function"==typeof e.preRun&&(e.preRun=[e.preRun]);e.preRun.length;)t=e.preRun.shift(),G.unshift(t);var t;Q(G)}(),V>0||(e.setStatus?(e.setStatus("Running..."),setTimeout((function(){setTimeout((function(){e.setStatus("")}),1),i()}),1)):i()))}if(e.dynCall_viiijj=function(){return(e.dynCall_viiijj=e.asm.P).apply(null,arguments)},e.dynCall_jij=function(){return(e.dynCall_jij=e.asm.Q).apply(null,arguments)},e.dynCall_jii=function(){return(e.dynCall_jii=e.asm.R).apply(null,arguments)},e.dynCall_jiji=function(){return(e.dynCall_jiji=e.asm.S).apply(null,arguments)},W=function e(){$t||qt(),$t||(W=e)},e.preInit)for("function"==typeof e.preInit&&(e.preInit=[e.preInit]);e.preInit.length>0;)e.preInit.pop()();return qt(),e.ready}),a=1e-6,o="undefined"!=typeof Float32Array?Float32Array:Array;function d(){var e=new o(16);return o!=Float32Array&&(e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[11]=0,e[12]=0,e[13]=0,e[14]=0),e[0]=1,e[5]=1,e[10]=1,e[15]=1,e}function l(e){return e[0]=1,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=1,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=1,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,e}Math.hypot||(Math.hypot=function(){for(var e=0,t=arguments.length;t--;)e+=arguments[t]*arguments[t];return Math.sqrt(e)});var c,u=function(e,t,r,i,n,s,a){var o=1/(t-r),d=1/(i-n),l=1/(s-a);return e[0]=-2*o,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=-2*d,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=2*l,e[11]=0,e[12]=(t+r)*o,e[13]=(n+i)*d,e[14]=(a+s)*l,e[15]=1,e};function h(e,t,r){var i=new o(3);return i[0]=e,i[1]=t,i[2]=r,i}c=new o(3),o!=Float32Array&&(c[0]=0,c[1]=0,c[2]=0);var f=(e,t)=>{t&&e.pixelStorei(e.UNPACK_ALIGNMENT,1);const r=function(){const t=_(e.VERTEX_SHADER,"\n            attribute vec4 aVertexPosition;\n            attribute vec2 aTexturePosition;\n            uniform mat4 uModelMatrix;\n            uniform mat4 uViewMatrix;\n            uniform mat4 uProjectionMatrix;\n            varying lowp vec2 vTexturePosition;\n            void main(void) {\n              gl_Position = uProjectionMatrix * uViewMatrix * uModelMatrix * aVertexPosition;\n              vTexturePosition = aTexturePosition;\n            }\n        "),r=_(e.FRAGMENT_SHADER,"\n            precision highp float;\n            varying highp vec2 vTexturePosition;\n            uniform int isyuv;\n            uniform sampler2D rgbaTexture;\n            uniform sampler2D yTexture;\n            uniform sampler2D uTexture;\n            uniform sampler2D vTexture;\n\n            const mat4 YUV2RGB = mat4( 1.1643828125, 0, 1.59602734375, -.87078515625,\n                                       1.1643828125, -.39176171875, -.81296875, .52959375,\n                                       1.1643828125, 2.017234375, 0, -1.081390625,\n                                       0, 0, 0, 1);\n\n\n            void main(void) {\n\n                if (isyuv>0) {\n\n                    highp float y = texture2D(yTexture,  vTexturePosition).r;\n                    highp float u = texture2D(uTexture,  vTexturePosition).r;\n                    highp float v = texture2D(vTexture,  vTexturePosition).r;\n                    gl_FragColor = vec4(y, u, v, 1) * YUV2RGB;\n\n                } else {\n                    gl_FragColor =  texture2D(rgbaTexture, vTexturePosition);\n                }\n            }\n        "),i=e.createProgram();if(e.attachShader(i,t),e.attachShader(i,r),e.linkProgram(i),!e.getProgramParameter(i,e.LINK_STATUS))return console.log("Unable to initialize the shader program: "+e.getProgramInfoLog(i)),null;return i}();let i={program:r,attribLocations:{vertexPosition:e.getAttribLocation(r,"aVertexPosition"),texturePosition:e.getAttribLocation(r,"aTexturePosition")},uniformLocations:{projectionMatrix:e.getUniformLocation(r,"uProjectionMatrix"),modelMatrix:e.getUniformLocation(r,"uModelMatrix"),viewMatrix:e.getUniformLocation(r,"uViewMatrix"),rgbatexture:e.getUniformLocation(r,"rgbaTexture"),ytexture:e.getUniformLocation(r,"yTexture"),utexture:e.getUniformLocation(r,"uTexture"),vtexture:e.getUniformLocation(r,"vTexture"),isyuv:e.getUniformLocation(r,"isyuv")}},n=function(){const t=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,t);e.bufferData(e.ARRAY_BUFFER,new Float32Array([-1,-1,-1,1,-1,-1,1,1,-1,-1,1,-1]),e.STATIC_DRAW);var r=[];r=r.concat([0,1],[1,1],[1,0],[0,0]);const i=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,i),e.bufferData(e.ARRAY_BUFFER,new Float32Array(r),e.STATIC_DRAW);const n=e.createBuffer();e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,n);return e.bufferData(e.ELEMENT_ARRAY_BUFFER,new Uint16Array([0,1,2,0,2,3]),e.STATIC_DRAW),{position:t,texPosition:i,indices:n}}(),s=p(),o=p(),c=p(),f=p();function p(){let t=e.createTexture();return e.bindTexture(e.TEXTURE_2D,t),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),t}function _(t,r){const i=e.createShader(t);return e.shaderSource(i,r),e.compileShader(i),e.getShaderParameter(i,e.COMPILE_STATUS)?i:(console.log("An error occurred compiling the shaders: "+e.getShaderInfoLog(i)),e.deleteShader(i),null)}function m(t,r){e.viewport(0,0,t,r),e.clearColor(0,0,0,0),e.clearDepth(1),e.enable(e.DEPTH_TEST),e.depthFunc(e.LEQUAL),e.clear(e.COLOR_BUFFER_BIT|e.DEPTH_BUFFER_BIT);const s=d();u(s,-1,1,-1,1,.1,100);const p=d();l(p);const _=d();!function(e,t,r,i){var n,s,o,d,c,u,h,f,p,_,m=t[0],g=t[1],y=t[2],v=i[0],b=i[1],w=i[2],S=r[0],E=r[1],A=r[2];Math.abs(m-S)<a&&Math.abs(g-E)<a&&Math.abs(y-A)<a?l(e):(h=m-S,f=g-E,p=y-A,n=b*(p*=_=1/Math.hypot(h,f,p))-w*(f*=_),s=w*(h*=_)-v*p,o=v*f-b*h,(_=Math.hypot(n,s,o))?(n*=_=1/_,s*=_,o*=_):(n=0,s=0,o=0),d=f*o-p*s,c=p*n-h*o,u=h*s-f*n,(_=Math.hypot(d,c,u))?(d*=_=1/_,c*=_,u*=_):(d=0,c=0,u=0),e[0]=n,e[1]=d,e[2]=h,e[3]=0,e[4]=s,e[5]=c,e[6]=f,e[7]=0,e[8]=o,e[9]=u,e[10]=p,e[11]=0,e[12]=-(n*m+s*g+o*y),e[13]=-(d*m+c*g+u*y),e[14]=-(h*m+f*g+p*y),e[15]=1)}(_,h(0,0,0),h(0,0,-1),h(0,1,0));{const t=3,r=e.FLOAT,s=!1,a=0,o=0;e.bindBuffer(e.ARRAY_BUFFER,n.position),e.vertexAttribPointer(i.attribLocations.vertexPosition,t,r,s,a,o),e.enableVertexAttribArray(i.attribLocations.vertexPosition)}{const t=2,r=e.FLOAT,s=!1,a=0,o=0;e.bindBuffer(e.ARRAY_BUFFER,n.texPosition),e.vertexAttribPointer(i.attribLocations.texturePosition,t,r,s,a,o),e.enableVertexAttribArray(i.attribLocations.texturePosition)}e.activeTexture(e.TEXTURE0+3),e.bindTexture(e.TEXTURE_2D,o),e.activeTexture(e.TEXTURE0+4),e.bindTexture(e.TEXTURE_2D,c),e.activeTexture(e.TEXTURE0+5),e.bindTexture(e.TEXTURE_2D,f),e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,n.indices),e.useProgram(i.program),e.uniformMatrix4fv(i.uniformLocations.projectionMatrix,!1,s),e.uniformMatrix4fv(i.uniformLocations.modelMatrix,!1,p),e.uniformMatrix4fv(i.uniformLocations.viewMatrix,!1,_),e.uniform1i(i.uniformLocations.rgbatexture,2),e.uniform1i(i.uniformLocations.ytexture,3),e.uniform1i(i.uniformLocations.utexture,4),e.uniform1i(i.uniformLocations.vtexture,5),e.uniform1i(i.uniformLocations.isyuv,1);{const t=6,r=e.UNSIGNED_SHORT,i=0;e.drawElements(e.TRIANGLES,t,r,i)}}return{render:function(t,r,i,n,s){e.activeTexture(e.TEXTURE0),e.bindTexture(e.TEXTURE_2D,o),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t,r,0,e.LUMINANCE,e.UNSIGNED_BYTE,i),e.activeTexture(e.TEXTURE1),e.bindTexture(e.TEXTURE_2D,c),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t/2,r/2,0,e.LUMINANCE,e.UNSIGNED_BYTE,n),e.activeTexture(e.TEXTURE2),e.bindTexture(e.TEXTURE_2D,f),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t/2,r/2,0,e.LUMINANCE,e.UNSIGNED_BYTE,s),m(t,r)},renderYUV:function(t,r,i){let n=i.slice(0,t*r),s=i.slice(t*r,t*r*5/4),a=i.slice(t*r*5/4,t*r*3/2);e.activeTexture(e.TEXTURE0),e.bindTexture(e.TEXTURE_2D,o),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t,r,0,e.LUMINANCE,e.UNSIGNED_BYTE,n),e.activeTexture(e.TEXTURE1),e.bindTexture(e.TEXTURE_2D,c),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t/2,r/2,0,e.LUMINANCE,e.UNSIGNED_BYTE,s),e.activeTexture(e.TEXTURE2),e.bindTexture(e.TEXTURE_2D,f),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t/2,r/2,0,e.LUMINANCE,e.UNSIGNED_BYTE,a),m(t,r)},destroy:function(){e.deleteProgram(i.program),e.deleteBuffer(n.position),e.deleteBuffer(n.texPosition),e.deleteBuffer(n.indices),e.deleteTexture(s),e.deleteTexture(o),e.deleteTexture(c),e.deleteTexture(f),i=null,n=null,s=null,o=null,c=null,f=null}}};const p=1,_=2,m="fetch",g="websocket",y="player",v="playbackTF",b="mp4",w="debug",S="warn",E=36e5,A=4080,B=12,x={playType:y,container:"",videoBuffer:1e3,videoBufferDelay:1e3,networkDelay:1e4,isResize:!0,isFullResize:!1,isFlv:!1,isHls:!1,isFmp4:!1,isFmp4Private:!1,isWebrtc:!1,isWebrtcForZLM:!1,isWebrtcForSRS:!1,isWebrtcForOthers:!1,isNakedFlow:!1,isMpeg4:!1,isAliyunRtc:!1,isTs:!1,debug:!1,debugLevel:S,debugUuid:"",isMulti:!0,multiIndex:-1,hotKey:!1,loadingTimeout:10,heartTimeout:10,timeout:10,pageVisibilityHiddenTimeout:300,loadingTimeoutReplay:!0,heartTimeoutReplay:!0,loadingTimeoutReplayTimes:3,heartTimeoutReplayTimes:3,heartTimeoutReplayUseLastFrameShow:!0,replayUseLastFrameShow:!0,replayShowLoadingIcon:!1,supportDblclickFullscreen:!1,showBandwidth:!1,showPerformance:!1,mseCorrectTimeDuration:20,mseCorrectAudioTimeDuration:20,keepScreenOn:!0,isNotMute:!1,muted:!0,hasAudio:!0,hasVideo:!0,operateBtns:{fullscreen:!1,screenshot:!1,play:!1,audio:!1,record:!1,ptz:!1,quality:!1,zoom:!1,close:!1,scale:!1,performance:!1,logSave:!1,aiFace:!1,aiObject:!1,aiOcclusion:!1,fullscreenFn:null,fullscreenExitFn:null,screenshotFn:null,playFn:null,pauseFn:null,recordFn:null,recordStopFn:null},extendOperateBtns:[],contextmenuBtns:[],watermarkConfig:{},controlAutoHide:!1,hasControl:!1,loadingIcon:!0,loadingIconStyle:{},loadingText:"",background:"",poster:"",backgroundLoadingShow:!0,loadingBackground:"",loadingBackgroundWidth:0,loadingBackgroundHeight:0,decoder:"decoder-pro.js",decoderAudio:"decoder-pro-audio.js",decoderHard:"decoder-pro-hard.js",decoderHardNotWasm:"decoder-pro-hard-not-wasm.js",wasmMp4RecorderDecoder:"jessibuca-pro-mp4-recorder-decoder.js",decoderWASM:"",isDecoderUseCDN:!1,url:"",rotate:0,mirrorRotate:"none",aspectRatio:"default",playbackConfig:{playList:[],fps:"",showControl:!0,controlType:"normal",duration:0,startTime:"",showRateBtn:!1,rateConfig:[],showPrecision:"",showPrecisionBtn:!0,isCacheBeforeDecodeForFpsRender:!1,uiUsePlaybackPause:!1,isPlaybackPauseClearCache:!0,isUseFpsRender:!1,isUseLocalCalculateTime:!1,localOneFrameTimestamp:40,supportWheel:!1,useWCS:!1,useMSE:!1},qualityConfig:[],defaultStreamQuality:"",scaleConfig:["拉伸","缩放","正常"],forceNoOffscreen:!0,hiddenAutoPause:!1,protocol:_,demuxType:"flv",useWasm:!1,useMSE:!1,useWCS:!1,useSIMD:!0,useMThreading:!1,wcsUseVideoRender:!0,wcsUseWebgl2Render:!0,wasmUseVideoRender:!0,mseUseCanvasRender:!1,hlsUseCanvasRender:!1,webrtcUseCanvasRender:!1,useOffscreen:!1,useWebGPU:!1,mseDecodeErrorReplay:!0,wcsDecodeErrorReplay:!0,wasmDecodeErrorReplay:!0,simdDecodeErrorReplay:!0,simdDecodeErrorReplayType:"wasm",autoWasm:!0,decoderErrorAutoWasm:!0,hardDecodingNotSupportAutoWasm:!0,webglAlignmentErrorReplay:!0,webglContextLostErrorReplay:!0,openWebglAlignment:!1,syncAudioAndVideo:!1,syncAudioAndVideoDiff:500,playbackDelayTime:1e3,playbackFps:25,playbackForwardMaxRateDecodeIFrame:4,playbackCurrentTimeMove:!0,useVideoRender:!0,useCanvasRender:!1,networkDelayTimeoutReplay:!1,recordType:b,checkFirstIFrame:!0,nakedFlowFps:25,audioEngine:null,isShowRecordingUI:!0,isShowZoomingUI:!0,useFaceDetector:!1,useObjectDetector:!1,useImageDetector:!1,useOcclusionDetector:!1,ptzPositionConfig:{},ptzShowType:"vertical",ptzClickType:"click",ptzStopEmitDelay:.3,ptzZoomShow:!1,ptzApertureShow:!1,ptzFocusShow:!1,ptzMoreArrowShow:!1,ptzCruiseShow:!1,ptzFogShow:!1,ptzWiperShow:!1,ptzSupportDraggable:!1,weiXinInAndroidAudioBufferSize:4800,isM7sCrypto:!1,m7sCryptoAudio:!1,isSm4Crypto:!1,isXorCrypto:!1,sm4CryptoKey:"",m7sCryptoKey:"",xorCryptoKey:"",cryptoKey:"",cryptoIV:"",cryptoKeyUrl:"",autoResize:!1,useWebFullScreen:!1,ptsMaxDiff:3600,aiFaceDetectLevel:2,aiFaceDetectWidth:240,aiFaceDetectShowRect:!0,aiFaceDetectInterval:1e3,aiFaceDetectRectConfig:{},aiObjectDetectLevel:2,aiObjectDetectWidth:240,aiObjectDetectShowRect:!0,aiObjectDetectInterval:1e3,aiObjectDetectRectConfig:{},aiOcclusionDetectInterval:1e3,aiImageDetectDrop:!1,aiImageDetectActive:!1,videoRenderSupportScale:!0,mediaSourceTsIsMaxDiffReplay:!0,controlHtml:"",isH265:!1,isWebrtcH265:!1,supportLockScreenPlayAudio:!0,supportHls265:!1,isEmitSEI:!1,pauseAndNextPlayUseLastFrameShow:!1,demuxUseWorker:!0,playFailedAndReplay:!0,showMessageConfig:{webglAlignmentError:"Webgl 渲染失败",webglContextLostError:"webgl 上下文丢失",mediaSourceH265NotSupport:"不支持硬解码H265",mediaSourceFull:"缓冲区已满",mediaSourceAppendBufferError:"初始化解码器失败",mseSourceBufferError:"解码失败",mseAddSourceBufferError:"初始化解码器失败",mediaSourceDecoderConfigurationError:"初始化解码器失败",mediaSourceTsIsMaxDiff:"流异常",mseWidthOrHeightChange:"流异常",mediaSourceAudioG711NotSupport:"硬解码不支持G711a/u音频格式",mediaSourceUseCanvasRenderPlayFailed:"MediaSource解码使用canvas渲染失败",webcodecsH265NotSupport:"不支持硬解码H265",webcodecsUnsupportedConfigurationError:"初始化解码器失败",webcodecsDecodeConfigureError:"初始化解码器失败",webcodecsDecodeError:"解码失败",wcsWidthOrHeightChange:"解码失败",wasmDecodeError:"解码失败",simdDecodeError:"解码失败",wasmWidthOrHeightChange:"流异常",wasmUseVideoRenderError:"video自动渲染失败",videoElementPlayingFailed:"video自动渲染失败",simdH264DecodeVideoWidthIsTooLarge:"不支持该分辨率的视频",networkDelayTimeout:"网络超时重播失败",fetchError:"请求失败",streamEnd:"请求结束",websocketError:"请求失败",webrtcError:"请求失败",hlsError:"请求失败",decoderWorkerInitError:"初始化worker失败",videoElementPlayingFailedForWebrtc:"video自动渲染失败",videoInfoError:"解析视频分辨率失败",webrtcStreamH265:"webrtc不支持H265",delayTimeout:"播放超时重播失败",loadingTimeout:"加载超时重播失败",loadingTimeoutRetryEnd:"加载超时重播失败",delayTimeoutRetryEnd:"播放超时重播失败"},videoElementPlayingFailedReplay:!0,mp4RecordUseWasm:!0,mseAutoCleanupSourceBuffer:!0,mseAutoCleanupMaxBackwardDuration:30,mseAutoCleanupMinBackwardDuration:10,widthOrHeightChangeReplay:!0,simdH264DecodeVideoWidthIsTooLargeReplay:!0,mediaSourceAudioG711NotSupportReplay:!0,mediaSourceAudioInitTimeoutReplay:!0,mediaSourceUseCanvasRenderPlayFailedReplay:!0,mediaSourceUseCanvasRenderPlayFailedReplayType:"video",widthOrHeightChangeReplayDelayTime:0,ghostWatermarkConfig:{on:5,off:5,content:"",fontSize:12,color:"white",opacity:.15,speed:.2},dynamicWatermarkConfig:{content:"",speed:.2,fontSize:12,color:"white",opacity:.15},isDropSameTimestampGop:!1,mseDecodeAudio:!1,nakedFlowH265DemuxUseNew:!0,extendDomConfig:{html:"",showBeforePlay:!1,showAfterLoading:!0},disableContextmenu:!1,mseDecoderUseWorker:!1,openMemoryLog:!1,mainThreadFetchUseWorker:!0,playFailedAndPausedShowPlayBtn:!0,mseCorrectionTimestamp:!0,flvDemuxBufferSizeTooLargeReplay:!1,flvDemuxBufferSizeTooLargeEmitFailed:!1,flvDemuxBufferSizeMaxLarge:1048576,isCheckInView:!1,hiddenControl:!1},U="init",T="initVideo",k="render",C="playAudio",D="initAudio",I="audioCode",F="audioNalu",P="audioAACSequenceHeader",L="videoCode",M="videoCodec",R="videoNalu",z="videoPayload",N="audioPayload",O="workerFetch",G="iframeIntervalTs",H="isDropping",$="playbackStreamVideoFps",V="wasmWidthOrHeightChange",W="simdDecodeError",j="simdH264DecodeVideoWidthIsTooLarge",q="closeEnd",Y="tempStream",K="videoSEI",X="flvScriptData",Z="aacSequenceHeader",J="videoSequenceHeader",Q="flvBufferData",ee="checkFirstIFrame",te="mseHandle",re="mseFirstRenderTime",ie="mseError",ne=1,se=2,ae=8,oe=9,de=18,le="init",ce="decode",ue="audioDecode",he="videoDecode",fe="close",pe="updateConfig",_e="clearBuffer",me="fetchStream",ge="sendWsMessage",ye="mseUpdateVideoTimestamp",ve="delayTimeout",be="loadingTimeout",we="streamEnd",Se="streamRate",Ee="streamAbps",Ae="streamVbps",Be="streamDts",xe="streamSuccess",Ue="streamStats",Te="networkDelayTimeout",ke="websocketOpen",Ce={playError:"playIsNotPauseOrUrlIsNull",fetchError:"fetchError",websocketError:"websocketError",webcodecsH265NotSupport:"webcodecsH265NotSupport",webcodecsDecodeError:"webcodecsDecodeError",webcodecsUnsupportedConfigurationError:"webcodecsUnsupportedConfigurationError",webcodecsDecodeConfigureError:"webcodecsDecodeConfigureError",mediaSourceH265NotSupport:"mediaSourceH265NotSupport",mediaSourceAudioG711NotSupport:"mediaSourceAudioG711NotSupport",mediaSourceAudioInitTimeout:"mediaSourceAudioInitTimeout",mediaSourceAudioNoDataTimeout:"mediaSourceAudioNoDataTimeout",mediaSourceDecoderConfigurationError:"mediaSourceDecoderConfigurationError",mediaSourceFull:"mseSourceBufferFull",mseSourceBufferError:"mseSourceBufferError",mseAddSourceBufferError:"mseAddSourceBufferError",mediaSourceAppendBufferError:"mediaSourceAppendBufferError",mediaSourceTsIsMaxDiff:"mediaSourceTsIsMaxDiff",mediaSourceUseCanvasRenderPlayFailed:"mediaSourceUseCanvasRenderPlayFailed",mediaSourceBufferedIsZeroError:"mediaSourceBufferedIsZeroError",wasmDecodeError:"wasmDecodeError",wasmUseVideoRenderError:"wasmUseVideoRenderError",hlsError:"hlsError",webrtcError:"webrtcError",webrtcClosed:"webrtcClosed",webrtcIceCandidateError:"webrtcIceCandidateError",webglAlignmentError:"webglAlignmentError",wasmWidthOrHeightChange:"wasmWidthOrHeightChange",mseWidthOrHeightChange:"mseWidthOrHeightChange",wcsWidthOrHeightChange:"wcsWidthOrHeightChange",widthOrHeightChange:"widthOrHeightChange",tallWebsocketClosedByError:"tallWebsocketClosedByError",flvDemuxBufferSizeTooLarge:"flvDemuxBufferSizeTooLarge",wasmDecodeVideoNoResponseError:"wasmDecodeVideoNoResponseError",audioChannelError:"audioChannelError",simdH264DecodeVideoWidthIsTooLarge:"simdH264DecodeVideoWidthIsTooLarge",simdDecodeError:"simdDecodeError",webglContextLostError:"webglContextLostError",videoElementPlayingFailed:"videoElementPlayingFailed",videoElementPlayingFailedForWebrtc:"videoElementPlayingFailedForWebrtc",decoderWorkerInitError:"decoderWorkerInitError",videoInfoError:"videoInfoError",videoCodecIdError:"videoCodecIdError",streamEnd:we,websocket1006Error:"websocket1006Error",delayTimeout:ve,loadingTimeout:be,networkDelayTimeout:Te,aliyunRtcError:"aliyunRtcError",...{talkStreamError:"talkStreamError",talkStreamClose:"talkStreamClose"}},De=1,Ie=7,Fe=12,Pe=99,Le="H264(AVC)",Me="H265(HEVC)",Re=10,ze=7,Ne=8,Oe=2,Ge=7,He=8,$e=5,Ve=1,We=5,je=6,qe=7,Ye=8,Ke=14,Xe=19,Ze=20,Je=21,Qe=32,et=32,tt=33,rt=33,it=34,nt=34,st=39,at=39,ot=40,dt="key",lt="delta",ct='video/mp4; codecs="avc1.64002A"',ut='video/mp4; codecs="hev1.1.6.L123.b0"',ht='video/mp4;codecs="hev1.1.6.L120.90"',ft='video/mp4;codecs="hev1.2.4.L120.90"',pt='video/mp4;codecs="hev1.3.E.L120.90"',_t='video/mp4;codecs="hev1.4.10.L120.90"',mt="ended",gt="open",yt="closed",vt="sourceclose",bt="sourceopen",wt="sourceended",St="avc",Et="hevc",At="AbortError",Bt=0,xt=1,Ut=1,Tt="idle",kt="buffering",Ct="complete",Dt=1,It=2,Ft=128,Pt=0,Lt=1,Mt=3,Rt=16;var zt="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function Nt(e,t){return e(t={exports:{}},t.exports),t.exports}Nt((function(e){!function(){var t="undefined"!=typeof window&&void 0!==window.document?window.document:{},r=e.exports,i=function(){for(var e,r=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],i=0,n=r.length,s={};i<n;i++)if((e=r[i])&&e[1]in t){for(i=0;i<e.length;i++)s[r[0][i]]=e[i];return s}return!1}(),n={change:i.fullscreenchange,error:i.fullscreenerror},s={request:function(e,r){return new Promise(function(n,s){var a=function(){this.off("change",a),n()}.bind(this);this.on("change",a);var o=(e=e||t.documentElement)[i.requestFullscreen](r);o instanceof Promise&&o.then(a).catch(s)}.bind(this))},exit:function(){return new Promise(function(e,r){if(this.isFullscreen){var n=function(){this.off("change",n),e()}.bind(this);this.on("change",n);var s=t[i.exitFullscreen]();s instanceof Promise&&s.then(n).catch(r)}else e()}.bind(this))},toggle:function(e,t){return this.isFullscreen?this.exit():this.request(e,t)},onchange:function(e){this.on("change",e)},onerror:function(e){this.on("error",e)},on:function(e,r){var i=n[e];i&&t.addEventListener(i,r,!1)},off:function(e,r){var i=n[e];i&&t.removeEventListener(i,r,!1)},raw:i};i?(Object.defineProperties(s,{isFullscreen:{get:function(){return Boolean(t[i.fullscreenElement])}},element:{enumerable:!0,get:function(){return t[i.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return Boolean(t[i.fullscreenEnabled])}}}),r?e.exports=s:window.screenfull=s):r?e.exports={isEnabled:!1}:window.screenfull={isEnabled:!1}}()})).isEnabled;class Ot{constructor(e){this._buffer=e,this._buffer_index=0,this._total_bytes=e.byteLength,this._total_bits=8*e.byteLength,this._current_word=0,this._current_word_bits_left=0}destroy(){this._buffer=null}_fillCurrentWord(){let e=this._total_bytes-this._buffer_index;if(e<=0)return void console.error("ExpGolomb: _fillCurrentWord() but no bytes available",this._total_bytes,this._buffer_index);let t=Math.min(4,e),r=new Uint8Array(4);r.set(this._buffer.subarray(this._buffer_index,this._buffer_index+t)),this._current_word=new DataView(r.buffer).getUint32(0,!1),this._buffer_index+=t,this._current_word_bits_left=8*t}readBits(e){if(e>32&&console.error("ExpGolomb: readBits() bits exceeded max 32bits!"),e<=this._current_word_bits_left){let t=this._current_word>>>32-e;return this._current_word<<=e,this._current_word_bits_left-=e,t}let t=this._current_word_bits_left?this._current_word:0;t>>>=32-this._current_word_bits_left;let r=e-this._current_word_bits_left;this._fillCurrentWord();let i=Math.min(r,this._current_word_bits_left),n=this._current_word>>>32-i;return this._current_word<<=i,this._current_word_bits_left-=i,t=t<<i|n,t}readBool(){return 1===this.readBits(1)}readByte(){return this.readBits(8)}_skipLeadingZero(){let e;for(e=0;e<this._current_word_bits_left;e++)if(0!=(this._current_word&2147483648>>>e))return this._current_word<<=e,this._current_word_bits_left-=e,e;return this._fillCurrentWord(),e+this._skipLeadingZero()}readUEG(){let e=this._skipLeadingZero();return this.readBits(e+1)-1}readSEG(){let e=this.readUEG();return 1&e?e+1>>>1:-1*(e>>>1)}}const Gt=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350,-1,-1,-1],Ht=Gt,$t=Gt;function Vt(e){let{profile:t,sampleRate:r,channel:i}=e;return new Uint8Array([175,0,t<<3|(14&r)>>1,(1&r)<<7|i<<3])}function Wt(e){return jt(e)&&e[1]===Bt}function jt(e){return e[0]>>4===Re}const qt=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350];function Yt(e){let t=new Uint8Array(e),r=null,i=0,n=0,s=0,a=null;if(i=n=t[0]>>>3,s=(7&t[0])<<1|t[1]>>>7,s<0||s>=qt.length)return void console.error("Flv: AAC invalid sampling frequency index!");let o=qt[s],d=(120&t[1])>>>3;if(d<0||d>=8)return void console.log("Flv: AAC invalid channel configuration");5===i&&(a=(7&t[1])<<1|t[2]>>>7,t[2]);let l=self.navigator.userAgent.toLowerCase();return-1!==l.indexOf("firefox")?s>=6?(i=5,r=new Array(4),a=s-3):(i=2,r=new Array(2),a=s):-1!==l.indexOf("android")?(i=2,r=new Array(2),a=s):(i=5,a=s,r=new Array(4),s>=6?a=s-3:1===d&&(i=2,r=new Array(2),a=s)),r[0]=i<<3,r[0]|=(15&s)>>>1,r[1]=(15&s)<<7,r[1]|=(15&d)<<3,5===i&&(r[1]|=(15&a)>>>1,r[2]=(1&a)<<7,r[2]|=8,r[3]=0),{audioType:"aac",config:r,sampleRate:o,channelCount:d,objectType:i,codec:"mp4a.40."+i,originalCodec:"mp4a.40."+n}}class Kt{constructor(e){this.data_=e,this.eof_flag_=!1,this.current_syncword_offset_=this.findNextSyncwordOffset(0),this.eof_flag_&&console.error("Could not found ADTS syncword until payload end")}findNextSyncwordOffset(e){let t=e,r=this.data_;for(;;){if(t+7>=r.byteLength)return this.eof_flag_=!0,r.byteLength;if(4095===(r[t+0]<<8|r[t+1])>>>4)return t;t++}}readNextAACFrame(){let e=this.data_,t=null;for(;null==t&&!this.eof_flag_;){let r=this.current_syncword_offset_,i=(8&e[r+1])>>>3,n=(6&e[r+1])>>>1,s=1&e[r+1],a=(192&e[r+2])>>>6,o=(60&e[r+2])>>>2,d=(1&e[r+2])<<2|(192&e[r+3])>>>6,l=(3&e[r+3])<<11|e[r+4]<<3|(224&e[r+5])>>>5;if(e[r+6],r+l>this.data_.byteLength){this.eof_flag_=!0,this.has_last_incomplete_data=!0;break}let c=1===s?7:9,u=l-c;r+=c;let h=this.findNextSyncwordOffset(r+u);if(this.current_syncword_offset_=h,0!==i&&1!==i||0!==n)continue;let f=e.subarray(r,r+u);t={},t.audio_object_type=a+1,t.sampling_freq_index=o,t.sampling_frequency=Ht[o],t.channel_config=d,t.data=f}return t}hasIncompleteData(){return this.has_last_incomplete_data}getIncompleteData(){return this.has_last_incomplete_data?this.data_.subarray(this.current_syncword_offset_):null}}class Xt{constructor(e){this.data_=e,this.eof_flag_=!1,this.current_syncword_offset_=this.findNextSyncwordOffset(0),this.eof_flag_&&console.error("Could not found ADTS syncword until payload end")}findNextSyncwordOffset(e){let t=e,r=this.data_;for(;;){if(t+1>=r.byteLength)return this.eof_flag_=!0,r.byteLength;if(695===(r[t+0]<<3|r[t+1]>>>5))return t;t++}}getLATMValue(e){let t=e.readBits(2),r=0;for(let i=0;i<=t;i++)r<<=8,r|=e.readByte();return r}readNextAACFrame(e){let t=this.data_,r=null;for(;null==r&&!this.eof_flag_;){let i=this.current_syncword_offset_,n=(31&t[i+1])<<8|t[i+2];if(i+3+n>=this.data_.byteLength){this.eof_flag_=!0,this.has_last_incomplete_data=!0;break}let s=new Ot(t.subarray(i+3,i+3+n)),a=null;if(s.readBool()){if(null==e){console.warn("StreamMuxConfig Missing"),this.current_syncword_offset_=this.findNextSyncwordOffset(i+3+n),s.destroy();continue}a=e}else{let e=s.readBool();if(e&&s.readBool()){console.error("audioMuxVersionA is Not Supported"),s.destroy();break}if(e&&this.getLATMValue(s),!s.readBool()){console.error("allStreamsSameTimeFraming zero is Not Supported"),s.destroy();break}if(0!==s.readBits(6)){console.error("more than 2 numSubFrames Not Supported"),s.destroy();break}if(0!==s.readBits(4)){console.error("more than 2 numProgram Not Supported"),s.destroy();break}if(0!==s.readBits(3)){console.error("more than 2 numLayer Not Supported"),s.destroy();break}let t=e?this.getLATMValue(s):0,r=s.readBits(5);t-=5;let i=s.readBits(4);t-=4;let n=s.readBits(4);t-=4,s.readBits(3),t-=3,t>0&&s.readBits(t);let o=s.readBits(3);if(0!==o){console.error(`frameLengthType = ${o}. Only frameLengthType = 0 Supported`),s.destroy();break}s.readByte();let d=s.readBool();if(d)if(e)this.getLATMValue(s);else{let e=0;for(;;){e<<=8;let t=s.readBool();if(e+=s.readByte(),!t)break}console.log(e)}s.readBool()&&s.readByte(),a={},a.audio_object_type=r,a.sampling_freq_index=i,a.sampling_frequency=Ht[a.sampling_freq_index],a.channel_config=n,a.other_data_present=d}let o=0;for(;;){let e=s.readByte();if(o+=e,255!==e)break}let d=new Uint8Array(o);for(let e=0;e<o;e++)d[e]=s.readByte();r={},r.audio_object_type=a.audio_object_type,r.sampling_freq_index=a.sampling_freq_index,r.sampling_frequency=Ht[a.sampling_freq_index],r.channel_config=a.channel_config,r.other_data_present=a.other_data_present,r.data=d,this.current_syncword_offset_=this.findNextSyncwordOffset(i+3+n)}return r}hasIncompleteData(){return this.has_last_incomplete_data}getIncompleteData(){return this.has_last_incomplete_data?this.data_.subarray(this.current_syncword_offset_):null}}function Zt(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(e[t]<<24>>>0)+(e[t+1]<<16)+(e[t+2]<<8)+(e[t+3]||0)}function Jt(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4;if(e.length<4)return;const r=e.length,i=[];let n,s=0;for(;s+t<r;)if(n=Zt(e,s),3===t&&(n>>>=8),s+=t,n){if(s+n>r)break;i.push(e.subarray(s,s+n)),s+=n}return i}function Qt(e){const t=e.byteLength,r=new Uint8Array(4);r[0]=t>>>24&255,r[1]=t>>>16&255,r[2]=t>>>8&255,r[3]=255&t;const i=new Uint8Array(t+4);return i.set(r,0),i.set(e,4),i}function er(e,t){let r=null;return t?e.length>=28&&(r=1+(3&e[26])):e.length>=12&&(r=1+(3&e[9])),r}function tr(){return(new Date).getTime()}function rr(e,t,r){return Math.max(Math.min(e,Math.max(t,r)),Math.min(t,r))}function ir(){return performance&&"function"==typeof performance.now?performance.now():Date.now()}function nr(e){let t=0,r=ir();return i=>{if(n=i,"[object Number]"!==Object.prototype.toString.call(n))return;var n;t+=i;const s=ir(),a=s-r;a>=1e3&&(e(t/a*1e3),r=s,t=0)}}function sr(){const e=window.navigator.userAgent.toLowerCase();return/firefox/i.test(e)}function ar(){let e=!1;return"MediaSource"in self&&(self.MediaSource.isTypeSupported(ut)||self.MediaSource.isTypeSupported(ht)||self.MediaSource.isTypeSupported(ft)||self.MediaSource.isTypeSupported(pt)||self.MediaSource.isTypeSupported(_t))&&(e=!0),e}function or(e){return null==e}function dr(e){return!or(e)}function lr(e){return"function"==typeof e}function cr(e){let t=null,r=31&e[0];return r!==Ge&&r!==He||(t=Le),t||(r=(126&e[0])>>1,r!==Qe&&r!==tt&&r!==it||(t=Me)),t}function ur(){return"undefined"!=typeof WritableStream}function hr(e){e.close()}function fr(e,t){t&&(e=e.filter((e=>e.type&&e.type===t)));let r=e[0],i=null,n=1;if(e.length>0){let t=e[1];t&&t.ts-r.ts>1e5&&(r=t,n=2)}if(r)for(let s=n;s<e.length;s++){let n=e[s];if(t&&n.type&&n.type!==t&&(n=null),n){if(n.ts-r.ts>=1e3){e[s-1].ts-r.ts<1e3&&(i=s+1)}}}return i}function pr(e){return e.ok&&e.status>=200&&e.status<=299}function _r(){return function(e){let t="";if("object"==typeof e)try{t=JSON.stringify(e),t=JSON.parse(t)}catch(r){t=e}else t=e;return t}(x)}function mr(e){return e[0]>>4===xt&&e[1]===Bt}function gr(e){return!0===e||"true"===e}function yr(e){return!0!==e&&"true"!==e}function vr(){return!!(self.Worker&&self.MediaSource&&"canConstructInDedicatedWorker"in self.MediaSource&&!0===self.MediaSource.canConstructInDedicatedWorker)}(()=>{try{if("object"==typeof WebAssembly&&"function"==typeof WebAssembly.instantiate){const e=new WebAssembly.Module(Uint8Array.of(0,97,115,109,1,0,0,0));if(e instanceof WebAssembly.Module)return new WebAssembly.Instance(e)instanceof WebAssembly.Instance}}catch(e){}})();var br=function(e,t,r,i){return new(r||(r=Promise))((function(n,s){function a(e){try{d(i.next(e))}catch(e){s(e)}}function o(e){try{d(i.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,o)}d((i=i.apply(e,t||[])).next())}))};const wr=Symbol(32),Sr=Symbol(16),Er=Symbol(8);class Ar{constructor(e){this.g=e,this.consumed=0,e&&(this.need=e.next().value)}setG(e){this.g=e,this.demand(e.next().value,!0)}consume(){this.buffer&&this.consumed&&(this.buffer.copyWithin(0,this.consumed),this.buffer=this.buffer.subarray(0,this.buffer.length-this.consumed),this.consumed=0)}demand(e,t){return t&&this.consume(),this.need=e,this.flush()}read(e){return br(this,void 0,void 0,(function*(){return this.lastReadPromise&&(yield this.lastReadPromise),this.lastReadPromise=new Promise(((t,r)=>{var i;this.reject=r,this.resolve=e=>{delete this.lastReadPromise,delete this.resolve,delete this.need,t(e)};this.demand(e,!0)||null===(i=this.pull)||void 0===i||i.call(this,e)}))}))}readU32(){return this.read(wr)}readU16(){return this.read(Sr)}readU8(){return this.read(Er)}close(){var e;this.g&&this.g.return(),this.buffer&&this.buffer.subarray(0,0),null===(e=this.reject)||void 0===e||e.call(this,new Error("EOF")),delete this.lastReadPromise}flush(){if(!this.buffer||!this.need)return;let e=null;const t=this.buffer.subarray(this.consumed);let r=0;const i=e=>t.length<(r=e);if("number"==typeof this.need){if(i(this.need))return;e=t.subarray(0,r)}else if(this.need===wr){if(i(4))return;e=t[0]<<24|t[1]<<16|t[2]<<8|t[3]}else if(this.need===Sr){if(i(2))return;e=t[0]<<8|t[1]}else if(this.need===Er){if(i(1))return;e=t[0]}else if("buffer"in this.need){if("byteOffset"in this.need){if(i(this.need.byteLength-this.need.byteOffset))return;new Uint8Array(this.need.buffer,this.need.byteOffset).set(t.subarray(0,r)),e=this.need}else if(this.g)return void this.g.throw(new Error("Unsupported type"))}else{if(i(this.need.byteLength))return;new Uint8Array(this.need).set(t.subarray(0,r)),e=this.need}return this.consumed+=r,this.g?this.demand(this.g.next(e).value,!0):this.resolve&&this.resolve(e),e}write(e){if(e instanceof Uint8Array?this.malloc(e.length).set(e):"buffer"in e?this.malloc(e.byteLength).set(new Uint8Array(e.buffer,e.byteOffset,e.byteLength)):this.malloc(e.byteLength).set(new Uint8Array(e)),!this.g&&!this.resolve)return new Promise((e=>this.pull=e));this.flush()}writeU32(e){this.malloc(4).set([e>>24&255,e>>16&255,e>>8&255,255&e]),this.flush()}writeU16(e){this.malloc(2).set([e>>8&255,255&e]),this.flush()}writeU8(e){this.malloc(1)[0]=e,this.flush()}malloc(e){if(this.buffer){const t=this.buffer.length,r=t+e;if(r<=this.buffer.buffer.byteLength-this.buffer.byteOffset)this.buffer=new Uint8Array(this.buffer.buffer,this.buffer.byteOffset,r);else{const e=new Uint8Array(r);e.set(this.buffer),this.buffer=e}return this.buffer.subarray(t,r)}return this.buffer=new Uint8Array(e),this.buffer}}Ar.U32=wr,Ar.U16=Sr,Ar.U8=Er;class Br{constructor(e){this.log=function(t){if(e._opt.debug&&e._opt.debugLevel==w){const s=e._opt.debugUuid?`[${e._opt.debugUuid}]`:"";for(var r=arguments.length,i=new Array(r>1?r-1:0),n=1;n<r;n++)i[n-1]=arguments[n];console.log(`JbPro${s}[✅✅✅][${t}]`,...i)}},this.warn=function(t){if(e._opt.debug&&(e._opt.debugLevel==w||e._opt.debugLevel==S)){const s=e._opt.debugUuid?`[${e._opt.debugUuid}]`:"";for(var r=arguments.length,i=new Array(r>1?r-1:0),n=1;n<r;n++)i[n-1]=arguments[n];console.log(`JbPro${s}[❗❗❗][${t}]`,...i)}},this.error=function(t){const r=e._opt.debugUuid?`[${e._opt.debugUuid}]`:"";for(var i=arguments.length,n=new Array(i>1?i-1:0),s=1;s<i;s++)n[s-1]=arguments[s];console.error(`JbPro${r}[❌❌❌][${t}]`,...n)}}}class xr{static _ebsp2rbsp(e){let t=e,r=t.byteLength,i=new Uint8Array(r),n=0;for(let e=0;e<r;e++)e>=2&&3===t[e]&&0===t[e-1]&&0===t[e-2]||(i[n]=t[e],n++);return new Uint8Array(i.buffer,0,n)}static parseSPS(e){let t=xr._ebsp2rbsp(e),r=new Ot(t);r.readByte();let i=r.readByte();r.readByte();let n=r.readByte();r.readUEG();let s=xr.getProfileString(i),a=xr.getLevelString(n),o=1,d=420,l=[0,420,422,444],c=8;if((100===i||110===i||122===i||244===i||44===i||83===i||86===i||118===i||128===i||138===i||144===i)&&(o=r.readUEG(),3===o&&r.readBits(1),o<=3&&(d=l[o]),c=r.readUEG()+8,r.readUEG(),r.readBits(1),r.readBool())){let e=3!==o?8:12;for(let t=0;t<e;t++)r.readBool()&&(t<6?xr._skipScalingList(r,16):xr._skipScalingList(r,64))}r.readUEG();let u=r.readUEG();if(0===u)r.readUEG();else if(1===u){r.readBits(1),r.readSEG(),r.readSEG();let e=r.readUEG();for(let t=0;t<e;t++)r.readSEG()}let h=r.readUEG();r.readBits(1);let f=r.readUEG(),p=r.readUEG(),_=r.readBits(1);0===_&&r.readBits(1),r.readBits(1);let m=0,g=0,y=0,v=0;r.readBool()&&(m=r.readUEG(),g=r.readUEG(),y=r.readUEG(),v=r.readUEG());let b=1,w=1,S=0,E=!0,A=0,B=0;if(r.readBool()){if(r.readBool()){let e=r.readByte(),t=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2],i=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1];e>0&&e<16?(b=t[e-1],w=i[e-1]):255===e&&(b=r.readByte()<<8|r.readByte(),w=r.readByte()<<8|r.readByte())}if(r.readBool()&&r.readBool(),r.readBool()&&(r.readBits(4),r.readBool()&&r.readBits(24)),r.readBool()&&(r.readUEG(),r.readUEG()),r.readBool()){let e=r.readBits(32),t=r.readBits(32);E=r.readBool(),A=t,B=2*e,S=A/B}}let x=1;1===b&&1===w||(x=b/w);let U=0,T=0;if(0===o)U=1,T=2-_;else{U=3===o?1:2,T=(1===o?2:1)*(2-_)}let k=16*(f+1),C=16*(p+1)*(2-_);k-=(m+g)*U,C-=(y+v)*T;let D=Math.ceil(k*x);return r.destroy(),r=null,{profile_string:s,level_string:a,bit_depth:c,ref_frames:h,chroma_format:d,chroma_format_string:xr.getChromaFormatString(d),frame_rate:{fixed:E,fps:S,fps_den:B,fps_num:A},sar_ratio:{width:b,height:w},codec_size:{width:k,height:C},present_size:{width:D,height:C}}}static parseSPS$2(e){let t=e.subarray(1,4),r="avc1.";for(let e=0;e<3;e++){let i=t[e].toString(16);i.length<2&&(i="0"+i),r+=i}let i=xr._ebsp2rbsp(e),n=new Ot(i);n.readByte();let s=n.readByte();n.readByte();let a=n.readByte();n.readUEG();let o=xr.getProfileString(s),d=xr.getLevelString(a),l=1,c=420,u=[0,420,422,444],h=8,f=8;if((100===s||110===s||122===s||244===s||44===s||83===s||86===s||118===s||128===s||138===s||144===s)&&(l=n.readUEG(),3===l&&n.readBits(1),l<=3&&(c=u[l]),h=n.readUEG()+8,f=n.readUEG()+8,n.readBits(1),n.readBool())){let e=3!==l?8:12;for(let t=0;t<e;t++)n.readBool()&&(t<6?xr._skipScalingList(n,16):xr._skipScalingList(n,64))}n.readUEG();let p=n.readUEG();if(0===p)n.readUEG();else if(1===p){n.readBits(1),n.readSEG(),n.readSEG();let e=n.readUEG();for(let t=0;t<e;t++)n.readSEG()}let _=n.readUEG();n.readBits(1);let m=n.readUEG(),g=n.readUEG(),y=n.readBits(1);0===y&&n.readBits(1),n.readBits(1);let v=0,b=0,w=0,S=0;n.readBool()&&(v=n.readUEG(),b=n.readUEG(),w=n.readUEG(),S=n.readUEG());let E=1,A=1,B=0,x=!0,U=0,T=0;if(n.readBool()){if(n.readBool()){let e=n.readByte(),t=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2],r=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1];e>0&&e<16?(E=t[e-1],A=r[e-1]):255===e&&(E=n.readByte()<<8|n.readByte(),A=n.readByte()<<8|n.readByte())}if(n.readBool()&&n.readBool(),n.readBool()&&(n.readBits(4),n.readBool()&&n.readBits(24)),n.readBool()&&(n.readUEG(),n.readUEG()),n.readBool()){let e=n.readBits(32),t=n.readBits(32);x=n.readBool(),U=t,T=2*e,B=U/T}}let k=1;1===E&&1===A||(k=E/A);let C=0,D=0;if(0===l)C=1,D=2-y;else{C=3===l?1:2,D=(1===l?2:1)*(2-y)}let I=16*(m+1),F=16*(g+1)*(2-y);I-=(v+b)*C,F-=(w+S)*D;let P=Math.ceil(I*k);return n.destroy(),n=null,{codec_mimetype:r,profile_idc:s,level_idc:a,profile_string:o,level_string:d,chroma_format_idc:l,bit_depth:h,bit_depth_luma:h,bit_depth_chroma:f,ref_frames:_,chroma_format:c,chroma_format_string:xr.getChromaFormatString(c),frame_rate:{fixed:x,fps:B,fps_den:T,fps_num:U},sar_ratio:{width:E,height:A},codec_size:{width:I,height:F},present_size:{width:P,height:F}}}static _skipScalingList(e,t){let r=8,i=8,n=0;for(let s=0;s<t;s++)0!==i&&(n=e.readSEG(),i=(r+n+256)%256),r=0===i?r:i}static getProfileString(e){switch(e){case 66:return"Baseline";case 77:return"Main";case 88:return"Extended";case 100:return"High";case 110:return"High10";case 122:return"High422";case 244:return"High444";default:return"Unknown"}}static getLevelString(e){return(e/10).toFixed(1)}static getChromaFormatString(e){switch(e){case 420:return"4:2:0";case 422:return"4:2:2";case 444:return"4:4:4";default:return"Unknown"}}}class Ur{constructor(e){this.buffer=e,this.buflen=e.length,this.bufpos=0,this.bufoff=0,this.iserro=!1}read(e){let t=0,r=0;for(;e;){if(e<0||this.bufpos>=this.buflen)return this.iserro=!0,0;this.iserro=!1,r=this.bufoff+e>8?8-this.bufoff:e,t<<=r,t+=this.buffer[this.bufpos]>>8-this.bufoff-r&255>>8-r,this.bufoff+=r,e-=r,8==this.bufoff&&(this.bufpos++,this.bufoff=0)}return t}look(e){let t=this.bufpos,r=this.bufoff,i=this.read(e);return this.bufpos=t,this.bufoff=r,i}read_golomb(){let e;for(e=0;0===this.read(1)&&!this.iserro;e++);return(1<<e)+this.read(e)-1}}function Tr(e){const t={};let r=function(){let e=new ArrayBuffer(2);return new DataView(e).setInt16(0,256,!0),256===new Int16Array(e)[0]}();const i=new DataView(e.buffer);let n=i.getUint8(0),s=i.getUint8(1);if(i.getUint8(2),i.getUint8(3),1!==n||0===s)return{};const a=1+(3&i.getUint8(4));if(3!==a&&4!==a)return{};let o=31&i.getUint8(5);if(0===o)return{};let d=6;for(let n=0;n<o;n++){let s=i.getUint16(d,!r);if(d+=2,0===s)continue;let a=new Uint8Array(e.buffer,d,s);d+=s;let o=xr.parseSPS(a);if(0!==n)continue;t.sps=a,t.timescale=1e3,t.codecWidth=o.codec_size.width,t.codecHeight=o.codec_size.height,t.presentWidth=o.present_size.width,t.presentHeight=o.present_size.height,t.profile=o.profile_string,t.level=o.level_string,t.bitDepth=o.bit_depth,t.chromaFormat=o.chroma_format,t.sarRatio=o.sar_ratio,t.frameRate=o.frame_rate,!1!==o.frame_rate.fixed&&0!==o.frame_rate.fps_num&&0!==o.frame_rate.fps_den||(t.frameRate={fixed:!0,fps:25,fps_num:25e3,fps_den:1e3});let l=t.frameRate.fps_den,c=t.frameRate.fps_num;t.refSampleDuration=t.timescale*(l/c);let u=a.subarray(1,4),h="avc1.";for(let e=0;e<3;e++){let t=u[e].toString(16);t.length<2&&(t="0"+t),h+=t}t.codec=h}let l=i.getUint8(d);if(0===l)return{};d++;for(let n=0;n<l;n++){let n=i.getUint16(d,!r);if(d+=2,0===n)continue;let s=new Uint8Array(e.buffer,d,n);d+=n,t.pps=s}if(t.videoType=St,t.sps){const e=t.sps.byteLength,r=new Uint8Array([e>>>24&255,e>>>16&255,e>>>8&255,255&e]),i=new Uint8Array(e+4);i.set(r,0),i.set(t.sps,4),t.sps=i}if(t.pps){const e=t.pps.byteLength,r=new Uint8Array([e>>>24&255,e>>>16&255,e>>>8&255,255&e]),i=new Uint8Array(e+4);i.set(r,0),i.set(t.pps,4),t.pps=i}return t}function kr(e){let{sps:t,pps:r}=e;const i=[23,0,0,0,0,1,66,0,30,255];i[0]=23,i[6]=t[1],i[7]=t[2],i[8]=t[3],i[10]=225,i[11]=t.byteLength>>8&255,i[12]=255&t.byteLength,i.push(...t,1,r.byteLength>>8&255,255&r.byteLength,...r);return new Uint8Array(i)}function Cr(e){let{sps:t,pps:r}=e,i=8+t.byteLength+1+2+r.byteLength,n=!1;const s=xr.parseSPS$2(t);66!==t[3]&&77!==t[3]&&88!==t[3]&&(n=!0,i+=4);let a=new Uint8Array(i);a[0]=1,a[1]=t[1],a[2]=t[2],a[3]=t[3],a[4]=255,a[5]=225;let o=t.byteLength;a[6]=o>>>8,a[7]=255&o;let d=8;a.set(t,8),d+=o,a[d]=1;let l=r.byteLength;a[d+1]=l>>>8,a[d+2]=255&l,a.set(r,d+3),d+=3+l,n&&(a[d]=252|s.chroma_format_idc,a[d+1]=248|s.bit_depth_luma-8,a[d+2]=248|s.bit_depth_chroma-8,a[d+3]=0,d+=4);const c=[23,0,0,0,0],u=new Uint8Array(c.length+a.byteLength);return u.set(c,0),u.set(a,c.length),u}function Dr(e,t){let r=[];r[0]=t?23:39,r[1]=1,r[2]=0,r[3]=0,r[4]=0,r[5]=e.byteLength>>24&255,r[6]=e.byteLength>>16&255,r[7]=e.byteLength>>8&255,r[8]=255&e.byteLength;const i=new Uint8Array(r.length+e.byteLength);return i.set(r,0),i.set(e,r.length),i}function Ir(e,t){let r=[];r[0]=t?23:39,r[1]=1,r[2]=0,r[3]=0,r[4]=0;const i=new Uint8Array(r.length+e.byteLength);return i.set(r,0),i.set(e,r.length),i}function Fr(e){return 31&e[0]}function Pr(e){return e===je}function Lr(e){return!function(e){return e===Ge||e===He}(e)&&!Pr(e)}function Mr(e){return e===$e}function Rr(e){if(0===e.length)return!1;const t=Fr(e[0]);for(let r=1;r<e.length;r++)if(t!==Fr(e[r]))return!1;return!0}class zr{constructor(e){this.data=e,this.eofFlag=!1,this.currentStartcodeOffset=this.findNextStartCodeOffset(0),this.eofFlag&&console.error("Could not find H264 startcode until payload end!")}findNextStartCodeOffset(e){let t=e,r=this.data;for(;;){if(t+3>=r.byteLength)return this.eofFlag=!0,r.byteLength;let e=r[t+0]<<24|r[t+1]<<16|r[t+2]<<8|r[t+3],i=r[t+0]<<16|r[t+1]<<8|r[t+2];if(1===e||1===i)return t;t++}}readNextNaluPayload(){let e=this.data,t=null;for(;null==t&&!this.eofFlag;){let r=this.currentStartcodeOffset;r+=1===(e[r]<<24|e[r+1]<<16|e[r+2]<<8|e[r+3])?4:3;let i=31&e[r],n=(128&e[r])>>>7,s=this.findNextStartCodeOffset(r);this.currentStartcodeOffset=s,i>=Ke||0===n&&(t={type:i,data:e.subarray(r,s)})}return t}}class Nr{constructor(e){let t=e.data.byteLength;this.type=e.type,this.data=new Uint8Array(4+t),new DataView(this.data.buffer).setUint32(0,t),this.data.set(e.data,4)}}const Or=e=>{let t=e,r=t.byteLength,i=new Uint8Array(r),n=0;for(let e=0;e<r;e++)e>=2&&3===t[e]&&0===t[e-1]&&0===t[e-2]||(i[n]=t[e],n++);return new Uint8Array(i.buffer,0,n)},Gr=e=>{switch(e){case 0:return"4:0:0";case 1:return"4:2:0";case 2:return"4:2:2";case 3:return"4:4:4";default:return"Unknown"}};class Hr{static _ebsp2rbsp(e){let t=e,r=t.byteLength,i=new Uint8Array(r),n=0;for(let e=0;e<r;e++)e>=2&&3===t[e]&&0===t[e-1]&&0===t[e-2]||(i[n]=t[e],n++);return new Uint8Array(i.buffer,0,n)}static parseVPS(e){let t=Hr._ebsp2rbsp(e),r=new Ot(t);return r.readByte(),r.readByte(),r.readBits(4),r.readBits(2),r.readBits(6),{num_temporal_layers:r.readBits(3)+1,temporal_id_nested:r.readBool()}}static parseSPS(e){let t=Hr._ebsp2rbsp(e),r=new Ot(t);r.readByte(),r.readByte();let i=0,n=0,s=0,a=0;r.readBits(4);let o=r.readBits(3);r.readBool();let d=r.readBits(2),l=r.readBool(),c=r.readBits(5),u=r.readByte(),h=r.readByte(),f=r.readByte(),p=r.readByte(),_=r.readByte(),m=r.readByte(),g=r.readByte(),y=r.readByte(),v=r.readByte(),b=r.readByte(),w=r.readByte(),S=[],E=[];for(let e=0;e<o;e++)S.push(r.readBool()),E.push(r.readBool());if(o>0)for(let e=o;e<8;e++)r.readBits(2);for(let e=0;e<o;e++)S[e]&&(r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte()),E[e]&&r.readByte();r.readUEG();let A=r.readUEG();3==A&&r.readBits(1);let B=r.readUEG(),x=r.readUEG();r.readBool()&&(i+=r.readUEG(),n+=r.readUEG(),s+=r.readUEG(),a+=r.readUEG());let U=r.readUEG(),T=r.readUEG(),k=r.readUEG();for(let e=r.readBool()?0:o;e<=o;e++)r.readUEG(),r.readUEG(),r.readUEG();if(r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG(),r.readBool()){if(r.readBool())for(let e=0;e<4;e++)for(let t=0;t<(3===e?2:6);t++){if(r.readBool()){let t=Math.min(64,1<<4+(e<<1));e>1&&r.readSEG();for(let e=0;e<t;e++)r.readSEG()}else r.readUEG()}}r.readBool(),r.readBool(),r.readBool()&&(r.readByte(),r.readUEG(),r.readUEG(),r.readBool());let C=r.readUEG(),D=0;for(let e=0;e<C;e++){let t=!1;if(0!==e&&(t=r.readBool()),t){e===C&&r.readUEG(),r.readBool(),r.readUEG();let t=0;for(let e=0;e<=D;e++){let e=r.readBool(),i=!1;e||(i=r.readBool()),(e||i)&&t++}D=t}else{let e=r.readUEG(),t=r.readUEG();D=e+t;for(let t=0;t<e;t++)r.readUEG(),r.readBool();for(let e=0;e<t;e++)r.readUEG(),r.readBool()}}if(r.readBool()){let e=r.readUEG();for(let t=0;t<e;t++){for(let e=0;e<k+4;e++)r.readBits(1);r.readBits(1)}}let I=!1,F=0,P=1,L=1,M=!1,R=1,z=1;if(r.readBool(),r.readBool(),r.readBool()){if(r.readBool()){let e=r.readByte(),t=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2],i=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1];e>0&&e<=16?(P=t[e-1],L=i[e-1]):255===e&&(P=r.readBits(16),L=r.readBits(16))}if(r.readBool()&&r.readBool(),r.readBool()){r.readBits(3),r.readBool(),r.readBool()&&(r.readByte(),r.readByte(),r.readByte())}if(r.readBool()&&(r.readUEG(),r.readUEG()),r.readBool(),r.readBool(),r.readBool(),I=r.readBool(),I&&(r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG()),r.readBool()){if(R=r.readBits(32),z=r.readBits(32),r.readBool()&&r.readUEG(),r.readBool()){let e=!1,t=!1,i=!1;e=r.readBool(),t=r.readBool(),(e||t)&&(i=r.readBool(),i&&(r.readByte(),r.readBits(5),r.readBool(),r.readBits(5)),r.readBits(4),r.readBits(4),i&&r.readBits(4),r.readBits(5),r.readBits(5),r.readBits(5));for(let n=0;n<=o;n++){let n=r.readBool();M=n;let s=!0,a=1;n||(s=r.readBool());let o=!1;if(s?r.readUEG():o=r.readBool(),o||(a=r.readUEG()+1),e){for(let e=0;e<a;e++)r.readUEG(),r.readUEG(),i&&(r.readUEG(),r.readUEG());r.readBool()}if(t){for(let e=0;e<a;e++)r.readUEG(),r.readUEG(),i&&(r.readUEG(),r.readUEG());r.readBool()}}}}r.readBool()&&(r.readBool(),r.readBool(),r.readBool(),F=r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG())}r.readBool();let N=`hvc1.${c}.1.L${w}.B0`,O=B-(i+n)*(1===A||2===A?2:1),G=x-(s+a)*(1===A?2:1),H=1;return 1!==P&&1!==L&&(H=P/L),r.destroy(),r=null,{codec_mimetype:N,profile_string:Hr.getProfileString(c),level_string:Hr.getLevelString(w),profile_idc:c,bit_depth:U+8,ref_frames:1,chroma_format:A,chroma_format_string:Hr.getChromaFormatString(A),general_level_idc:w,general_profile_space:d,general_tier_flag:l,general_profile_idc:c,general_profile_compatibility_flags_1:u,general_profile_compatibility_flags_2:h,general_profile_compatibility_flags_3:f,general_profile_compatibility_flags_4:p,general_constraint_indicator_flags_1:_,general_constraint_indicator_flags_2:m,general_constraint_indicator_flags_3:g,general_constraint_indicator_flags_4:y,general_constraint_indicator_flags_5:v,general_constraint_indicator_flags_6:b,min_spatial_segmentation_idc:F,constant_frame_rate:0,chroma_format_idc:A,bit_depth_luma_minus8:U,bit_depth_chroma_minus8:T,frame_rate:{fixed:M,fps:z/R,fps_den:R,fps_num:z},sar_ratio:{width:P,height:L},codec_size:{width:O,height:G},present_size:{width:O*H,height:G}}}static parsePPS(e){let t=Hr._ebsp2rbsp(e),r=new Ot(t);r.readByte(),r.readByte(),r.readUEG(),r.readUEG(),r.readBool(),r.readBool(),r.readBits(3),r.readBool(),r.readBool(),r.readUEG(),r.readUEG(),r.readSEG(),r.readBool(),r.readBool(),r.readBool()&&r.readUEG(),r.readSEG(),r.readSEG(),r.readBool(),r.readBool(),r.readBool(),r.readBool();let i=r.readBool(),n=r.readBool(),s=1;return n&&i?s=0:n?s=3:i&&(s=2),{parallelismType:s}}static getChromaFormatString(e){switch(e){case 0:return"4:0:0";case 1:return"4:2:0";case 2:return"4:2:2";case 3:return"4:4:4";default:return"Unknown"}}static getProfileString(e){switch(e){case 1:return"Main";case 2:return"Main10";case 3:return"MainSP";case 4:return"Rext";case 9:return"SCC";default:return"Unknown"}}static getLevelString(e){return(e/30).toFixed(1)}}function $r(e){let t={codecWidth:0,codecHeight:0,videoType:Et,width:0,height:0,profile:0,level:0};e=e.slice(5);do{let r={};if(e.length<23){console.warn("parseHEVCDecoderConfigurationRecord$2",`arrayBuffer.length ${e.length} < 23`);break}if(r.configurationVersion=e[0],1!=r.configurationVersion)break;r.general_profile_space=e[1]>>6&3,r.general_tier_flag=e[1]>>5&1,r.general_profile_idc=31&e[1],r.general_profile_compatibility_flags=e[2]<<24|e[3]<<16|e[4]<<8|e[5],r.general_constraint_indicator_flags=e[6]<<24|e[7]<<16|e[8]<<8|e[9],r.general_constraint_indicator_flags=r.general_constraint_indicator_flags<<16|e[10]<<8|e[11],r.general_level_idc=e[12],r.min_spatial_segmentation_idc=(15&e[13])<<8|e[14],r.parallelismType=3&e[15],r.chromaFormat=3&e[16],r.bitDepthLumaMinus8=7&e[17],r.bitDepthChromaMinus8=7&e[18],r.avgFrameRate=e[19]<<8|e[20],r.constantFrameRate=e[21]>>6&3,r.numTemporalLayers=e[21]>>3&7,r.temporalIdNested=e[21]>>2&1,r.lengthSizeMinusOne=3&e[21];let i=e[22],n=e.slice(23);for(let e=0;e<i&&!(n.length<3);e++){let e=63&n[0],i=n[1]<<8|n[2];n=n.slice(3);for(let s=0;s<i&&!(n.length<2);s++){let i=n[0]<<8|n[1];if(n.length<2+i)break;if(n=n.slice(2),33==e){let e=new Uint8Array(i);e.set(n.slice(0,i),0),r.psps=Wr(e,r),t.profile=r.general_profile_idc,t.level=r.general_level_idc/30,t.width=r.psps.pic_width_in_luma_samples-(r.psps.conf_win_left_offset+r.psps.conf_win_right_offset),t.height=r.psps.pic_height_in_luma_samples-(r.psps.conf_win_top_offset+r.psps.conf_win_bottom_offset)}n=n.slice(i)}}}while(0);return t.codecWidth=t.width||1920,t.codecHeight=t.height||1080,t.presentHeight=t.codecHeight,t.presentWidth=t.codecWidth,t.timescale=1e3,t.refSampleDuration=1e3/23976*1e3,t}function Vr(e){const t=e;if(t.length<22)return console.error(`Invalid HEVCDecoderConfigurationRecord, lack of data! ${t.length} < 22`),{};let r={codecWidth:0,codecHeight:0,videoType:Et},i=function(){let e=new ArrayBuffer(2);return new DataView(e).setInt16(0,256,!0),256===new Int16Array(e)[0]}(),n=new DataView(t.buffer),s=n.getUint8(0),a=31&n.getUint8(1);if(1!==s||0===a)return console.error(`Invalid HEVCDecoderConfigurationRecord,version is ${s}, hevcProfile is ${a}`),{};let o=1+(3&n.getUint8(21));if(3!==o&&4!==o)return console.error("Invalid HEVCDecoderConfigurationRecord, Strange NaluLengthSizeMinusOne: "+(o-1)),{};let d=n.getUint8(22);for(let e=0,s=23;e<d;e++){let e=63&n.getUint8(s+0),a=n.getUint16(s+1,!i);s+=3;for(let o=0;o<a;o++){let a=n.getUint16(s+0,!i);if(0===o)if(33===e){s+=2;let e=new Uint8Array(t.buffer,s,a),i=Hr.parseSPS(e);r.codecWidth=i.codec_size.width,r.codecHeight=i.codec_size.height,r.presentWidth=i.present_size.width,r.presentHeight=i.present_size.height,r.profile=i.profile_string,r.level=i.level_string,r.bitDepth=i.bit_depth,r.chromaFormat=i.chroma_format,r.sarRatio=i.sar_ratio,r.frameRate=i.frame_rate,!1!==i.frame_rate.fixed&&0!==i.frame_rate.fps_num&&0!==i.frame_rate.fps_den||(r.frameRate={fixed:!0,fps:23.976,fps_num:23976,fps_den:1e3}),r.frameRate.fps_den,r.frameRate.fps_num,r.codec=i.codec_mimetype,s+=a}else s+=2+a;else s+=2+a}}return r.hvcc=new Uint8Array(t),r}function Wr(e,t){let r={},i=e.length,n=[],s=new Ur(e);s.read(1),s.read(6),s.read(6),s.read(3);for(let e=2;e<i;e++)e+2<i&&3==s.look(24)?(n.push(s.read(8)),n.push(s.read(8)),e+=2,s.read(8)):n.push(s.read(8));let a=new Uint8Array(n),o=new Ur(a);if(r.sps_video_parameter_set_id=o.read(4),r.sps_max_sub_layers_minus1=o.read(3),r.sps_temporal_id_nesting_flag=o.read(1),r.profile_tier_level=function(e,t,r){let i={};i.profile_space=e.read(2),i.tier_flag=e.read(1),i.profile_idc=e.read(5),i.profile_compatibility_flags=e.read(32),i.general_progressive_source_flag=e.read(1),i.general_interlaced_source_flag=e.read(1),i.general_non_packed_constraint_flag=e.read(1),i.general_frame_only_constraint_flag=e.read(1),e.read(32),e.read(12),i.level_idc=e.read(8),i.sub_layer_profile_present_flag=[],i.sub_layer_level_present_flag=[];for(let t=0;t<r;t++)i.sub_layer_profile_present_flag[t]=e.read(1),i.sub_layer_level_present_flag[t]=e.read(1);if(r>0)for(let t=r;t<8;t++)e.read(2);i.sub_layer_profile_space=[],i.sub_layer_tier_flag=[],i.sub_layer_profile_idc=[],i.sub_layer_profile_compatibility_flag=[],i.sub_layer_progressive_source_flag=[],i.sub_layer_interlaced_source_flag=[],i.sub_layer_non_packed_constraint_flag=[],i.sub_layer_frame_only_constraint_flag=[],i.sub_layer_level_idc=[];for(let t=0;t<r;t++)i.sub_layer_profile_present_flag[t]&&(i.sub_layer_profile_space[t]=e.read(2),i.sub_layer_tier_flag[t]=e.read(1),i.sub_layer_profile_idc[t]=e.read(5),i.sub_layer_profile_compatibility_flag[t]=e.read(32),i.sub_layer_progressive_source_flag[t]=e.read(1),i.sub_layer_interlaced_source_flag[t]=e.read(1),i.sub_layer_non_packed_constraint_flag[t]=e.read(1),i.sub_layer_frame_only_constraint_flag[t]=e.read(1),e.read(32),e.read(12)),i.sub_layer_level_present_flag[t]?i.sub_layer_level_idc[t]=e.read(8):i.sub_layer_level_idc[t]=1;return i}(o,0,r.sps_max_sub_layers_minus1),r.sps_seq_parameter_set_id=o.read_golomb(),r.chroma_format_idc=o.read_golomb(),3==r.chroma_format_idc?r.separate_colour_plane_flag=o.read(1):r.separate_colour_plane_flag=0,r.pic_width_in_luma_samples=o.read_golomb(),r.pic_height_in_luma_samples=o.read_golomb(),r.conformance_window_flag=o.read(1),r.conformance_window_flag){let e=1+(r.chroma_format_idc<2),t=1+(r.chroma_format_idc<3);r.conf_win_left_offset=o.read_golomb()*t,r.conf_win_right_offset=o.read_golomb()*t,r.conf_win_top_offset=o.read_golomb()*e,r.conf_win_bottom_offset=o.read_golomb()*e}else r.conf_win_left_offset=0,r.conf_win_right_offset=0,r.conf_win_top_offset=0,r.conf_win_bottom_offset=0;return r}function jr(e){let{vps:t,pps:r,sps:i}=e,n={configurationVersion:1};const s=(e=>{let t=Or(e),r=new Ot(t);return r.readByte(),r.readByte(),r.readBits(4),r.readBits(2),r.readBits(6),{num_temporal_layers:r.readBits(3)+1,temporal_id_nested:r.readBool()}})(t),a=(e=>{let t=Or(e),r=new Ot(t);r.readByte(),r.readByte();let i=0,n=0,s=0,a=0;r.readBits(4);let o=r.readBits(3);r.readBool();let d=r.readBits(2),l=r.readBool(),c=r.readBits(5),u=r.readByte(),h=r.readByte(),f=r.readByte(),p=r.readByte(),_=r.readByte(),m=r.readByte(),g=r.readByte(),y=r.readByte(),v=r.readByte(),b=r.readByte(),w=r.readByte(),S=[],E=[];for(let e=0;e<o;e++)S.push(r.readBool()),E.push(r.readBool());if(o>0)for(let e=o;e<8;e++)r.readBits(2);for(let e=0;e<o;e++)S[e]&&(r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte(),r.readByte()),S[e]&&r.readByte();r.readUEG();let A=r.readUEG();3==A&&r.readBits(1);let B=r.readUEG(),x=r.readUEG();r.readBool()&&(i+=r.readUEG(),n+=r.readUEG(),s+=r.readUEG(),a+=r.readUEG());let U=r.readUEG(),T=r.readUEG(),k=r.readUEG();for(let e=r.readBool()?0:o;e<=o;e++)r.readUEG(),r.readUEG(),r.readUEG();if(r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG(),r.readBool()&&r.readBool())for(let e=0;e<4;e++)for(let t=0;t<(3===e?2:6);t++)if(r.readBool()){let t=Math.min(64,1<<4+(e<<1));e>1&&r.readSEG();for(let e=0;e<t;e++)r.readSEG()}else r.readUEG();r.readBool(),r.readBool(),r.readBool()&&(r.readByte(),r.readUEG(),r.readUEG(),r.readBool());let C=r.readUEG(),D=0;for(let e=0;e<C;e++){let t=!1;if(0!==e&&(t=r.readBool()),t){e===C&&r.readUEG(),r.readBool(),r.readUEG();let t=0;for(let e=0;e<=D;e++){let e=r.readBool(),i=!1;e||(i=r.readBool()),(e||i)&&t++}D=t}else{let e=r.readUEG(),t=r.readUEG();D=e+t;for(let t=0;t<e;t++)r.readUEG(),r.readBool();for(let e=0;e<t;e++)r.readUEG(),r.readBool()}}if(r.readBool()){let e=r.readUEG();for(let t=0;t<e;t++){for(let e=0;e<k+4;e++)r.readBits(1);r.readBits(1)}}let I=!1,F=0,P=1,L=1,M=!1,R=1,z=1;if(r.readBool(),r.readBool(),r.readBool()){if(r.readBool()){let e=r.readByte(),t=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2],i=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1];e>0&&e<16?(P=t[e-1],L=i[e-1]):255===e&&(P=r.readBits(16),L=r.readBits(16))}if(r.readBool()&&r.readBool(),r.readBool()&&(r.readBits(3),r.readBool(),r.readBool()&&(r.readByte(),r.readByte(),r.readByte())),r.readBool()&&(r.readUEG(),r.readUEG()),r.readBool(),r.readBool(),r.readBool(),I=r.readBool(),I&&(i+=r.readUEG(),n+=r.readUEG(),s+=r.readUEG(),a+=r.readUEG()),r.readBool()&&(R=r.readBits(32),z=r.readBits(32),r.readBool()&&(r.readUEG(),r.readBool()))){let e=!1,t=!1,i=!1;e=r.readBool(),t=r.readBool(),(e||t)&&(i=r.readBool(),i&&(r.readByte(),r.readBits(5),r.readBool(),r.readBits(5)),r.readBits(4),r.readBits(4),i&&r.readBits(4),r.readBits(5),r.readBits(5),r.readBits(5));for(let n=0;n<=o;n++){let n=r.readBool();M=n;let s=!1,a=1;n||(s=r.readBool());let o=!1;if(s?r.readSEG():o=r.readBool(),o||(cpbcnt=r.readUEG()+1),e)for(let e=0;e<a;e++)r.readUEG(),r.readUEG(),i&&(r.readUEG(),r.readUEG());if(t)for(let e=0;e<a;e++)r.readUEG(),r.readUEG(),i&&(r.readUEG(),r.readUEG())}}r.readBool()&&(r.readBool(),r.readBool(),r.readBool(),F=r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG(),r.readUEG())}r.readBool();let N=`hvc1.${c}.1.L${w}.B0`,O=B,G=x,H=1;return 1!==P&&1!==L&&(H=P/L),r.destroy(),r=null,{codec_mimetype:N,level_string:($=w,($/30).toFixed(1)),profile_idc:c,bit_depth:U+8,ref_frames:1,chroma_format:A,chroma_format_string:Gr(A),general_level_idc:w,general_profile_space:d,general_tier_flag:l,general_profile_idc:c,general_profile_compatibility_flags_1:u,general_profile_compatibility_flags_2:h,general_profile_compatibility_flags_3:f,general_profile_compatibility_flags_4:p,general_constraint_indicator_flags_1:_,general_constraint_indicator_flags_2:m,general_constraint_indicator_flags_3:g,general_constraint_indicator_flags_4:y,general_constraint_indicator_flags_5:v,general_constraint_indicator_flags_6:b,min_spatial_segmentation_idc:F,constant_frame_rate:0,chroma_format_idc:A,bit_depth_luma_minus8:U,bit_depth_chroma_minus8:T,frame_rate:{fixed:M,fps:z/R,fps_den:R,fps_num:z},sar_ratio:{width:P,height:L},codec_size:{width:O,height:G},present_size:{width:O*H,height:G}};var $})(i),o=(e=>{let t=Or(e),r=new Ot(t);r.readByte(),r.readByte(),r.readUEG(),r.readUEG(),r.readBool(),r.readBool(),r.readBits(3),r.readBool(),r.readBool(),r.readUEG(),r.readUEG(),r.readSEG(),r.readBool(),r.readBool(),r.readBool()&&r.readUEG(),r.readSEG(),r.readSEG(),r.readBool(),r.readBool(),r.readBool(),r.readBool();let i=r.readBool(),n=r.readBool(),s=1;return n&&i?s=0:n?s=3:i&&(s=2),{parallelismType:s}})(r);n=Object.assign(n,s,a,o);let d=23+(5+t.byteLength)+(5+i.byteLength)+(5+r.byteLength),l=new Uint8Array(d);l[0]=1,l[1]=(3&n.general_profile_space)<<6|(n.general_tier_flag?1:0)<<5|31&n.general_profile_idc,l[2]=n.general_profile_compatibility_flags_1||0,l[3]=n.general_profile_compatibility_flags_2||0,l[4]=n.general_profile_compatibility_flags_3||0,l[5]=n.general_profile_compatibility_flags_4||0,l[6]=n.general_constraint_indicator_flags_1||0,l[7]=n.general_constraint_indicator_flags_2||0,l[8]=n.general_constraint_indicator_flags_3||0,l[9]=n.general_constraint_indicator_flags_4||0,l[10]=n.general_constraint_indicator_flags_5||0,l[11]=n.general_constraint_indicator_flags_6||0,l[12]=60,l[13]=240|(3840&n.min_spatial_segmentation_idc)>>8,l[14]=255&n.min_spatial_segmentation_idc,l[15]=252|3&n.parallelismType,l[16]=252|3&n.chroma_format_idc,l[17]=248|7&n.bit_depth_luma_minus8,l[18]=248|7&n.bit_depth_chroma_minus8,l[19]=0,l[20]=0,l[21]=(3&n.constant_frame_rate)<<6|(7&n.num_temporal_layers)<<3|(n.temporal_id_nested?1:0)<<2|3,l[22]=3,l[23]=128|Qe,l[24]=0,l[25]=1,l[26]=(65280&t.byteLength)>>8,l[27]=(255&t.byteLength)>>0,l.set(t,28),l[23+(5+t.byteLength)+0]=128|tt,l[23+(5+t.byteLength)+1]=0,l[23+(5+t.byteLength)+2]=1,l[23+(5+t.byteLength)+3]=(65280&i.byteLength)>>8,l[23+(5+t.byteLength)+4]=(255&i.byteLength)>>0,l.set(i,23+(5+t.byteLength)+5),l[23+(5+t.byteLength+5+i.byteLength)+0]=128|it,l[23+(5+t.byteLength+5+i.byteLength)+1]=0,l[23+(5+t.byteLength+5+i.byteLength)+2]=1,l[23+(5+t.byteLength+5+i.byteLength)+3]=(65280&r.byteLength)>>8,l[23+(5+t.byteLength+5+i.byteLength)+4]=(255&r.byteLength)>>0,l.set(r,23+(5+t.byteLength+5+i.byteLength)+5);const c=[28,0,0,0,0],u=new Uint8Array(c.length+l.byteLength);return u.set(c,0),u.set(l,c.length),u}function qr(e,t){let r=[];r[0]=t?28:44,r[1]=1,r[2]=0,r[3]=0,r[4]=0,r[5]=e.byteLength>>24&255,r[6]=e.byteLength>>16&255,r[7]=e.byteLength>>8&255,r[8]=255&e.byteLength;const i=new Uint8Array(r.length+e.byteLength);return i.set(r,0),i.set(e,r.length),i}function Yr(e,t){let r=[];r[0]=t?28:44,r[1]=1,r[2]=0,r[3]=0,r[4]=0;const i=new Uint8Array(r.length+e.byteLength);return i.set(r,0),i.set(e,r.length),i}function Kr(e){return(126&e[0])>>1}function Xr(e){return e===st}function Zr(e){return!function(e){return e>=32&&e<=40}(e)}function Jr(e){return e>=16&&e<=21}function Qr(e){if(0===e.length)return!1;const t=Kr(e[0]);for(let r=1;r<e.length;r++)if(t!==Kr(e[r]))return!1;return!0}class ei{constructor(e){this.data=e,this.eofFlag=!1,this.currentStartcodeOffset=this.findNextStartCodeOffset(0),this.eofFlag&&console.error("Could not find H265 startcode until payload end!")}findNextStartCodeOffset(e){let t=e,r=this.data;for(;;){if(t+3>=r.byteLength)return this.eofFlag=!0,r.byteLength;let e=r[t+0]<<24|r[t+1]<<16|r[t+2]<<8|r[t+3],i=r[t+0]<<16|r[t+1]<<8|r[t+2];if(1===e||1===i)return t;t++}}readNextNaluPayload(){let e=this.data,t=null;for(;null==t&&!this.eofFlag;){let r=this.currentStartcodeOffset;r+=1===(e[r]<<24|e[r+1]<<16|e[r+2]<<8|e[r+3])?4:3;let i=e[r]>>1&63,n=(128&e[r])>>>7,s=this.findNextStartCodeOffset(r);this.currentStartcodeOffset=s,0===n&&(t={type:i,data:e.subarray(r,s)})}return t}}class ti{constructor(e){let t=e.data.byteLength;this.type=e.type,this.data=new Uint8Array(4+t),new DataView(this.data.buffer).setUint32(0,t),this.data.set(e.data,4)}}function ri(e){return parseInt(e)===e}function ii(e){if(!ri(e.length))return!1;for(var t=0;t<e.length;t++)if(!ri(e[t])||e[t]<0||e[t]>255)return!1;return!0}function ni(e,t){if(e.buffer&&"Uint8Array"===e.name)return t&&(e=e.slice?e.slice():Array.prototype.slice.call(e)),e;if(Array.isArray(e)){if(!ii(e))throw new Error("Array contains invalid value: "+e);return new Uint8Array(e)}if(ri(e.length)&&ii(e))return new Uint8Array(e);throw new Error("unsupported array-like object")}function si(e){return new Uint8Array(e)}function ai(e,t,r,i,n){null==i&&null==n||(e=e.slice?e.slice(i,n):Array.prototype.slice.call(e,i,n)),t.set(e,r)}var oi,di={toBytes:function(e){var t=[],r=0;for(e=encodeURI(e);r<e.length;){var i=e.charCodeAt(r++);37===i?(t.push(parseInt(e.substr(r,2),16)),r+=2):t.push(i)}return ni(t)},fromBytes:function(e){for(var t=[],r=0;r<e.length;){var i=e[r];i<128?(t.push(String.fromCharCode(i)),r++):i>191&&i<224?(t.push(String.fromCharCode((31&i)<<6|63&e[r+1])),r+=2):(t.push(String.fromCharCode((15&i)<<12|(63&e[r+1])<<6|63&e[r+2])),r+=3)}return t.join("")}},li=(oi="0123456789abcdef",{toBytes:function(e){for(var t=[],r=0;r<e.length;r+=2)t.push(parseInt(e.substr(r,2),16));return t},fromBytes:function(e){for(var t=[],r=0;r<e.length;r++){var i=e[r];t.push(oi[(240&i)>>4]+oi[15&i])}return t.join("")}}),ci={16:10,24:12,32:14},ui=[1,2,4,8,16,32,64,128,27,54,108,216,171,77,154,47,94,188,99,198,151,53,106,212,179,125,250,239,197,145],hi=[99,124,119,123,242,107,111,197,48,1,103,43,254,215,171,118,202,130,201,125,250,89,71,240,173,212,162,175,156,164,114,192,183,253,147,38,54,63,247,204,52,165,229,241,113,216,49,21,4,199,35,195,24,150,5,154,7,18,128,226,235,39,178,117,9,131,44,26,27,110,90,160,82,59,214,179,41,227,47,132,83,209,0,237,32,252,177,91,106,203,190,57,74,76,88,207,208,239,170,251,67,77,51,133,69,249,2,127,80,60,159,168,81,163,64,143,146,157,56,245,188,182,218,33,16,255,243,210,205,12,19,236,95,151,68,23,196,167,126,61,100,93,25,115,96,129,79,220,34,42,144,136,70,238,184,20,222,94,11,219,224,50,58,10,73,6,36,92,194,211,172,98,145,149,228,121,231,200,55,109,141,213,78,169,108,86,244,234,101,122,174,8,186,120,37,46,28,166,180,198,232,221,116,31,75,189,139,138,112,62,181,102,72,3,246,14,97,53,87,185,134,193,29,158,225,248,152,17,105,217,142,148,155,30,135,233,206,85,40,223,140,161,137,13,191,230,66,104,65,153,45,15,176,84,187,22],fi=[82,9,106,213,48,54,165,56,191,64,163,158,129,243,215,251,124,227,57,130,155,47,255,135,52,142,67,68,196,222,233,203,84,123,148,50,166,194,35,61,238,76,149,11,66,250,195,78,8,46,161,102,40,217,36,178,118,91,162,73,109,139,209,37,114,248,246,100,134,104,152,22,212,164,92,204,93,101,182,146,108,112,72,80,253,237,185,218,94,21,70,87,167,141,157,132,144,216,171,0,140,188,211,10,247,228,88,5,184,179,69,6,208,44,30,143,202,63,15,2,193,175,189,3,1,19,138,107,58,145,17,65,79,103,220,234,151,242,207,206,240,180,230,115,150,172,116,34,231,173,53,133,226,249,55,232,28,117,223,110,71,241,26,113,29,41,197,137,111,183,98,14,170,24,190,27,252,86,62,75,198,210,121,32,154,219,192,254,120,205,90,244,31,221,168,51,136,7,199,49,177,18,16,89,39,128,236,95,96,81,127,169,25,181,74,13,45,229,122,159,147,201,156,239,160,224,59,77,174,42,245,176,200,235,187,60,131,83,153,97,23,43,4,126,186,119,214,38,225,105,20,99,85,33,12,125],pi=[3328402341,4168907908,4000806809,4135287693,4294111757,3597364157,3731845041,2445657428,1613770832,33620227,3462883241,1445669757,3892248089,3050821474,1303096294,3967186586,2412431941,528646813,2311702848,4202528135,4026202645,2992200171,2387036105,4226871307,1101901292,3017069671,1604494077,1169141738,597466303,1403299063,3832705686,2613100635,1974974402,3791519004,1033081774,1277568618,1815492186,2118074177,4126668546,2211236943,1748251740,1369810420,3521504564,4193382664,3799085459,2883115123,1647391059,706024767,134480908,2512897874,1176707941,2646852446,806885416,932615841,168101135,798661301,235341577,605164086,461406363,3756188221,3454790438,1311188841,2142417613,3933566367,302582043,495158174,1479289972,874125870,907746093,3698224818,3025820398,1537253627,2756858614,1983593293,3084310113,2108928974,1378429307,3722699582,1580150641,327451799,2790478837,3117535592,0,3253595436,1075847264,3825007647,2041688520,3059440621,3563743934,2378943302,1740553945,1916352843,2487896798,2555137236,2958579944,2244988746,3151024235,3320835882,1336584933,3992714006,2252555205,2588757463,1714631509,293963156,2319795663,3925473552,67240454,4269768577,2689618160,2017213508,631218106,1269344483,2723238387,1571005438,2151694528,93294474,1066570413,563977660,1882732616,4059428100,1673313503,2008463041,2950355573,1109467491,537923632,3858759450,4260623118,3218264685,2177748300,403442708,638784309,3287084079,3193921505,899127202,2286175436,773265209,2479146071,1437050866,4236148354,2050833735,3362022572,3126681063,840505643,3866325909,3227541664,427917720,2655997905,2749160575,1143087718,1412049534,999329963,193497219,2353415882,3354324521,1807268051,672404540,2816401017,3160301282,369822493,2916866934,3688947771,1681011286,1949973070,336202270,2454276571,201721354,1210328172,3093060836,2680341085,3184776046,1135389935,3294782118,965841320,831886756,3554993207,4068047243,3588745010,2345191491,1849112409,3664604599,26054028,2983581028,2622377682,1235855840,3630984372,2891339514,4092916743,3488279077,3395642799,4101667470,1202630377,268961816,1874508501,4034427016,1243948399,1546530418,941366308,1470539505,1941222599,2546386513,3421038627,2715671932,3899946140,1042226977,2521517021,1639824860,227249030,260737669,3765465232,2084453954,1907733956,3429263018,2420656344,100860677,4160157185,470683154,3261161891,1781871967,2924959737,1773779408,394692241,2579611992,974986535,664706745,3655459128,3958962195,731420851,571543859,3530123707,2849626480,126783113,865375399,765172662,1008606754,361203602,3387549984,2278477385,2857719295,1344809080,2782912378,59542671,1503764984,160008576,437062935,1707065306,3622233649,2218934982,3496503480,2185314755,697932208,1512910199,504303377,2075177163,2824099068,1841019862,739644986],_i=[2781242211,2230877308,2582542199,2381740923,234877682,3184946027,2984144751,1418839493,1348481072,50462977,2848876391,2102799147,434634494,1656084439,3863849899,2599188086,1167051466,2636087938,1082771913,2281340285,368048890,3954334041,3381544775,201060592,3963727277,1739838676,4250903202,3930435503,3206782108,4149453988,2531553906,1536934080,3262494647,484572669,2923271059,1783375398,1517041206,1098792767,49674231,1334037708,1550332980,4098991525,886171109,150598129,2481090929,1940642008,1398944049,1059722517,201851908,1385547719,1699095331,1587397571,674240536,2704774806,252314885,3039795866,151914247,908333586,2602270848,1038082786,651029483,1766729511,3447698098,2682942837,454166793,2652734339,1951935532,775166490,758520603,3000790638,4004797018,4217086112,4137964114,1299594043,1639438038,3464344499,2068982057,1054729187,1901997871,2534638724,4121318227,1757008337,0,750906861,1614815264,535035132,3363418545,3988151131,3201591914,1183697867,3647454910,1265776953,3734260298,3566750796,3903871064,1250283471,1807470800,717615087,3847203498,384695291,3313910595,3617213773,1432761139,2484176261,3481945413,283769337,100925954,2180939647,4037038160,1148730428,3123027871,3813386408,4087501137,4267549603,3229630528,2315620239,2906624658,3156319645,1215313976,82966005,3747855548,3245848246,1974459098,1665278241,807407632,451280895,251524083,1841287890,1283575245,337120268,891687699,801369324,3787349855,2721421207,3431482436,959321879,1469301956,4065699751,2197585534,1199193405,2898814052,3887750493,724703513,2514908019,2696962144,2551808385,3516813135,2141445340,1715741218,2119445034,2872807568,2198571144,3398190662,700968686,3547052216,1009259540,2041044702,3803995742,487983883,1991105499,1004265696,1449407026,1316239930,504629770,3683797321,168560134,1816667172,3837287516,1570751170,1857934291,4014189740,2797888098,2822345105,2754712981,936633572,2347923833,852879335,1133234376,1500395319,3084545389,2348912013,1689376213,3533459022,3762923945,3034082412,4205598294,133428468,634383082,2949277029,2398386810,3913789102,403703816,3580869306,2297460856,1867130149,1918643758,607656988,4049053350,3346248884,1368901318,600565992,2090982877,2632479860,557719327,3717614411,3697393085,2249034635,2232388234,2430627952,1115438654,3295786421,2865522278,3633334344,84280067,33027830,303828494,2747425121,1600795957,4188952407,3496589753,2434238086,1486471617,658119965,3106381470,953803233,334231800,3005978776,857870609,3151128937,1890179545,2298973838,2805175444,3056442267,574365214,2450884487,550103529,1233637070,4289353045,2018519080,2057691103,2399374476,4166623649,2148108681,387583245,3664101311,836232934,3330556482,3100665960,3280093505,2955516313,2002398509,287182607,3413881008,4238890068,3597515707,975967766],mi=[1671808611,2089089148,2006576759,2072901243,4061003762,1807603307,1873927791,3310653893,810573872,16974337,1739181671,729634347,4263110654,3613570519,2883997099,1989864566,3393556426,2191335298,3376449993,2106063485,4195741690,1508618841,1204391495,4027317232,2917941677,3563566036,2734514082,2951366063,2629772188,2767672228,1922491506,3227229120,3082974647,4246528509,2477669779,644500518,911895606,1061256767,4144166391,3427763148,878471220,2784252325,3845444069,4043897329,1905517169,3631459288,827548209,356461077,67897348,3344078279,593839651,3277757891,405286936,2527147926,84871685,2595565466,118033927,305538066,2157648768,3795705826,3945188843,661212711,2999812018,1973414517,152769033,2208177539,745822252,439235610,455947803,1857215598,1525593178,2700827552,1391895634,994932283,3596728278,3016654259,695947817,3812548067,795958831,2224493444,1408607827,3513301457,0,3979133421,543178784,4229948412,2982705585,1542305371,1790891114,3410398667,3201918910,961245753,1256100938,1289001036,1491644504,3477767631,3496721360,4012557807,2867154858,4212583931,1137018435,1305975373,861234739,2241073541,1171229253,4178635257,33948674,2139225727,1357946960,1011120188,2679776671,2833468328,1374921297,2751356323,1086357568,2408187279,2460827538,2646352285,944271416,4110742005,3168756668,3066132406,3665145818,560153121,271589392,4279952895,4077846003,3530407890,3444343245,202643468,322250259,3962553324,1608629855,2543990167,1154254916,389623319,3294073796,2817676711,2122513534,1028094525,1689045092,1575467613,422261273,1939203699,1621147744,2174228865,1339137615,3699352540,577127458,712922154,2427141008,2290289544,1187679302,3995715566,3100863416,339486740,3732514782,1591917662,186455563,3681988059,3762019296,844522546,978220090,169743370,1239126601,101321734,611076132,1558493276,3260915650,3547250131,2901361580,1655096418,2443721105,2510565781,3828863972,2039214713,3878868455,3359869896,928607799,1840765549,2374762893,3580146133,1322425422,2850048425,1823791212,1459268694,4094161908,3928346602,1706019429,2056189050,2934523822,135794696,3134549946,2022240376,628050469,779246638,472135708,2800834470,3032970164,3327236038,3894660072,3715932637,1956440180,522272287,1272813131,3185336765,2340818315,2323976074,1888542832,1044544574,3049550261,1722469478,1222152264,50660867,4127324150,236067854,1638122081,895445557,1475980887,3117443513,2257655686,3243809217,489110045,2662934430,3778599393,4162055160,2561878936,288563729,1773916777,3648039385,2391345038,2493985684,2612407707,505560094,2274497927,3911240169,3460925390,1442818645,678973480,3749357023,2358182796,2717407649,2306869641,219617805,3218761151,3862026214,1120306242,1756942440,1103331905,2578459033,762796589,252780047,2966125488,1425844308,3151392187,372911126],gi=[1667474886,2088535288,2004326894,2071694838,4075949567,1802223062,1869591006,3318043793,808472672,16843522,1734846926,724270422,4278065639,3621216949,2880169549,1987484396,3402253711,2189597983,3385409673,2105378810,4210693615,1499065266,1195886990,4042263547,2913856577,3570689971,2728590687,2947541573,2627518243,2762274643,1920112356,3233831835,3082273397,4261223649,2475929149,640051788,909531756,1061110142,4160160501,3435941763,875846760,2779116625,3857003729,4059105529,1903268834,3638064043,825316194,353713962,67374088,3351728789,589522246,3284360861,404236336,2526454071,84217610,2593830191,117901582,303183396,2155911963,3806477791,3958056653,656894286,2998062463,1970642922,151591698,2206440989,741110872,437923380,454765878,1852748508,1515908788,2694904667,1381168804,993742198,3604373943,3014905469,690584402,3823320797,791638366,2223281939,1398011302,3520161977,0,3991743681,538992704,4244381667,2981218425,1532751286,1785380564,3419096717,3200178535,960056178,1246420628,1280103576,1482221744,3486468741,3503319995,4025428677,2863326543,4227536621,1128514950,1296947098,859002214,2240123921,1162203018,4193849577,33687044,2139062782,1347481760,1010582648,2678045221,2829640523,1364325282,2745433693,1077985408,2408548869,2459086143,2644360225,943212656,4126475505,3166494563,3065430391,3671750063,555836226,269496352,4294908645,4092792573,3537006015,3452783745,202118168,320025894,3974901699,1600119230,2543297077,1145359496,387397934,3301201811,2812801621,2122220284,1027426170,1684319432,1566435258,421079858,1936954854,1616945344,2172753945,1330631070,3705438115,572679748,707427924,2425400123,2290647819,1179044492,4008585671,3099120491,336870440,3739122087,1583276732,185277718,3688593069,3772791771,842159716,976899700,168435220,1229577106,101059084,606366792,1549591736,3267517855,3553849021,2897014595,1650632388,2442242105,2509612081,3840161747,2038008818,3890688725,3368567691,926374254,1835907034,2374863873,3587531953,1313788572,2846482505,1819063512,1448540844,4109633523,3941213647,1701162954,2054852340,2930698567,134748176,3132806511,2021165296,623210314,774795868,471606328,2795958615,3031746419,3334885783,3907527627,3722280097,1953799400,522133822,1263263126,3183336545,2341176845,2324333839,1886425312,1044267644,3048588401,1718004428,1212733584,50529542,4143317495,235803164,1633788866,892690282,1465383342,3115962473,2256965911,3250673817,488449850,2661202215,3789633753,4177007595,2560144171,286339874,1768537042,3654906025,2391705863,2492770099,2610673197,505291324,2273808917,3924369609,3469625735,1431699370,673740880,3755965093,2358021891,2711746649,2307489801,218961690,3217021541,3873845719,1111672452,1751693520,1094828930,2576986153,757954394,252645662,2964376443,1414855848,3149649517,370555436],yi=[1374988112,2118214995,437757123,975658646,1001089995,530400753,2902087851,1273168787,540080725,2910219766,2295101073,4110568485,1340463100,3307916247,641025152,3043140495,3736164937,632953703,1172967064,1576976609,3274667266,2169303058,2370213795,1809054150,59727847,361929877,3211623147,2505202138,3569255213,1484005843,1239443753,2395588676,1975683434,4102977912,2572697195,666464733,3202437046,4035489047,3374361702,2110667444,1675577880,3843699074,2538681184,1649639237,2976151520,3144396420,4269907996,4178062228,1883793496,2403728665,2497604743,1383856311,2876494627,1917518562,3810496343,1716890410,3001755655,800440835,2261089178,3543599269,807962610,599762354,33778362,3977675356,2328828971,2809771154,4077384432,1315562145,1708848333,101039829,3509871135,3299278474,875451293,2733856160,92987698,2767645557,193195065,1080094634,1584504582,3178106961,1042385657,2531067453,3711829422,1306967366,2438237621,1908694277,67556463,1615861247,429456164,3602770327,2302690252,1742315127,2968011453,126454664,3877198648,2043211483,2709260871,2084704233,4169408201,0,159417987,841739592,504459436,1817866830,4245618683,260388950,1034867998,908933415,168810852,1750902305,2606453969,607530554,202008497,2472011535,3035535058,463180190,2160117071,1641816226,1517767529,470948374,3801332234,3231722213,1008918595,303765277,235474187,4069246893,766945465,337553864,1475418501,2943682380,4003061179,2743034109,4144047775,1551037884,1147550661,1543208500,2336434550,3408119516,3069049960,3102011747,3610369226,1113818384,328671808,2227573024,2236228733,3535486456,2935566865,3341394285,496906059,3702665459,226906860,2009195472,733156972,2842737049,294930682,1206477858,2835123396,2700099354,1451044056,573804783,2269728455,3644379585,2362090238,2564033334,2801107407,2776292904,3669462566,1068351396,742039012,1350078989,1784663195,1417561698,4136440770,2430122216,775550814,2193862645,2673705150,1775276924,1876241833,3475313331,3366754619,270040487,3902563182,3678124923,3441850377,1851332852,3969562369,2203032232,3868552805,2868897406,566021896,4011190502,3135740889,1248802510,3936291284,699432150,832877231,708780849,3332740144,899835584,1951317047,4236429990,3767586992,866637845,4043610186,1106041591,2144161806,395441711,1984812685,1139781709,3433712980,3835036895,2664543715,1282050075,3240894392,1181045119,2640243204,25965917,4203181171,4211818798,3009879386,2463879762,3910161971,1842759443,2597806476,933301370,1509430414,3943906441,3467192302,3076639029,3776767469,2051518780,2631065433,1441952575,404016761,1942435775,1408749034,1610459739,3745345300,2017778566,3400528769,3110650942,941896748,3265478751,371049330,3168937228,675039627,4279080257,967311729,135050206,3635733660,1683407248,2076935265,3576870512,1215061108,3501741890],vi=[1347548327,1400783205,3273267108,2520393566,3409685355,4045380933,2880240216,2471224067,1428173050,4138563181,2441661558,636813900,4233094615,3620022987,2149987652,2411029155,1239331162,1730525723,2554718734,3781033664,46346101,310463728,2743944855,3328955385,3875770207,2501218972,3955191162,3667219033,768917123,3545789473,692707433,1150208456,1786102409,2029293177,1805211710,3710368113,3065962831,401639597,1724457132,3028143674,409198410,2196052529,1620529459,1164071807,3769721975,2226875310,486441376,2499348523,1483753576,428819965,2274680428,3075636216,598438867,3799141122,1474502543,711349675,129166120,53458370,2592523643,2782082824,4063242375,2988687269,3120694122,1559041666,730517276,2460449204,4042459122,2706270690,3446004468,3573941694,533804130,2328143614,2637442643,2695033685,839224033,1973745387,957055980,2856345839,106852767,1371368976,4181598602,1033297158,2933734917,1179510461,3046200461,91341917,1862534868,4284502037,605657339,2547432937,3431546947,2003294622,3182487618,2282195339,954669403,3682191598,1201765386,3917234703,3388507166,0,2198438022,1211247597,2887651696,1315723890,4227665663,1443857720,507358933,657861945,1678381017,560487590,3516619604,975451694,2970356327,261314535,3535072918,2652609425,1333838021,2724322336,1767536459,370938394,182621114,3854606378,1128014560,487725847,185469197,2918353863,3106780840,3356761769,2237133081,1286567175,3152976349,4255350624,2683765030,3160175349,3309594171,878443390,1988838185,3704300486,1756818940,1673061617,3403100636,272786309,1075025698,545572369,2105887268,4174560061,296679730,1841768865,1260232239,4091327024,3960309330,3497509347,1814803222,2578018489,4195456072,575138148,3299409036,446754879,3629546796,4011996048,3347532110,3252238545,4270639778,915985419,3483825537,681933534,651868046,2755636671,3828103837,223377554,2607439820,1649704518,3270937875,3901806776,1580087799,4118987695,3198115200,2087309459,2842678573,3016697106,1003007129,2802849917,1860738147,2077965243,164439672,4100872472,32283319,2827177882,1709610350,2125135846,136428751,3874428392,3652904859,3460984630,3572145929,3593056380,2939266226,824852259,818324884,3224740454,930369212,2801566410,2967507152,355706840,1257309336,4148292826,243256656,790073846,2373340630,1296297904,1422699085,3756299780,3818836405,457992840,3099667487,2135319889,77422314,1560382517,1945798516,788204353,1521706781,1385356242,870912086,325965383,2358957921,2050466060,2388260884,2313884476,4006521127,901210569,3990953189,1014646705,1503449823,1062597235,2031621326,3212035895,3931371469,1533017514,350174575,2256028891,2177544179,1052338372,741876788,1606591296,1914052035,213705253,2334669897,1107234197,1899603969,3725069491,2631447780,2422494913,1635502980,1893020342,1950903388,1120974935],bi=[2807058932,1699970625,2764249623,1586903591,1808481195,1173430173,1487645946,59984867,4199882800,1844882806,1989249228,1277555970,3623636965,3419915562,1149249077,2744104290,1514790577,459744698,244860394,3235995134,1963115311,4027744588,2544078150,4190530515,1608975247,2627016082,2062270317,1507497298,2200818878,567498868,1764313568,3359936201,2305455554,2037970062,1047239e3,1910319033,1337376481,2904027272,2892417312,984907214,1243112415,830661914,861968209,2135253587,2011214180,2927934315,2686254721,731183368,1750626376,4246310725,1820824798,4172763771,3542330227,48394827,2404901663,2871682645,671593195,3254988725,2073724613,145085239,2280796200,2779915199,1790575107,2187128086,472615631,3029510009,4075877127,3802222185,4107101658,3201631749,1646252340,4270507174,1402811438,1436590835,3778151818,3950355702,3963161475,4020912224,2667994737,273792366,2331590177,104699613,95345982,3175501286,2377486676,1560637892,3564045318,369057872,4213447064,3919042237,1137477952,2658625497,1119727848,2340947849,1530455833,4007360968,172466556,266959938,516552836,0,2256734592,3980931627,1890328081,1917742170,4294704398,945164165,3575528878,958871085,3647212047,2787207260,1423022939,775562294,1739656202,3876557655,2530391278,2443058075,3310321856,547512796,1265195639,437656594,3121275539,719700128,3762502690,387781147,218828297,3350065803,2830708150,2848461854,428169201,122466165,3720081049,1627235199,648017665,4122762354,1002783846,2117360635,695634755,3336358691,4234721005,4049844452,3704280881,2232435299,574624663,287343814,612205898,1039717051,840019705,2708326185,793451934,821288114,1391201670,3822090177,376187827,3113855344,1224348052,1679968233,2361698556,1058709744,752375421,2431590963,1321699145,3519142200,2734591178,188127444,2177869557,3727205754,2384911031,3215212461,2648976442,2450346104,3432737375,1180849278,331544205,3102249176,4150144569,2952102595,2159976285,2474404304,766078933,313773861,2570832044,2108100632,1668212892,3145456443,2013908262,418672217,3070356634,2594734927,1852171925,3867060991,3473416636,3907448597,2614737639,919489135,164948639,2094410160,2997825956,590424639,2486224549,1723872674,3157750862,3399941250,3501252752,3625268135,2555048196,3673637356,1343127501,4130281361,3599595085,2957853679,1297403050,81781910,3051593425,2283490410,532201772,1367295589,3926170974,895287692,1953757831,1093597963,492483431,3528626907,1446242576,1192455638,1636604631,209336225,344873464,1015671571,669961897,3375740769,3857572124,2973530695,3747192018,1933530610,3464042516,935293895,3454686199,2858115069,1863638845,3683022916,4085369519,3292445032,875313188,1080017571,3279033885,621591778,1233856572,2504130317,24197544,3017672716,3835484340,3247465558,2220981195,3060847922,1551124588,1463996600],wi=[4104605777,1097159550,396673818,660510266,2875968315,2638606623,4200115116,3808662347,821712160,1986918061,3430322568,38544885,3856137295,718002117,893681702,1654886325,2975484382,3122358053,3926825029,4274053469,796197571,1290801793,1184342925,3556361835,2405426947,2459735317,1836772287,1381620373,3196267988,1948373848,3764988233,3385345166,3263785589,2390325492,1480485785,3111247143,3780097726,2293045232,548169417,3459953789,3746175075,439452389,1362321559,1400849762,1685577905,1806599355,2174754046,137073913,1214797936,1174215055,3731654548,2079897426,1943217067,1258480242,529487843,1437280870,3945269170,3049390895,3313212038,923313619,679998e3,3215307299,57326082,377642221,3474729866,2041877159,133361907,1776460110,3673476453,96392454,878845905,2801699524,777231668,4082475170,2330014213,4142626212,2213296395,1626319424,1906247262,1846563261,562755902,3708173718,1040559837,3871163981,1418573201,3294430577,114585348,1343618912,2566595609,3186202582,1078185097,3651041127,3896688048,2307622919,425408743,3371096953,2081048481,1108339068,2216610296,0,2156299017,736970802,292596766,1517440620,251657213,2235061775,2933202493,758720310,265905162,1554391400,1532285339,908999204,174567692,1474760595,4002861748,2610011675,3234156416,3693126241,2001430874,303699484,2478443234,2687165888,585122620,454499602,151849742,2345119218,3064510765,514443284,4044981591,1963412655,2581445614,2137062819,19308535,1928707164,1715193156,4219352155,1126790795,600235211,3992742070,3841024952,836553431,1669664834,2535604243,3323011204,1243905413,3141400786,4180808110,698445255,2653899549,2989552604,2253581325,3252932727,3004591147,1891211689,2487810577,3915653703,4237083816,4030667424,2100090966,865136418,1229899655,953270745,3399679628,3557504664,4118925222,2061379749,3079546586,2915017791,983426092,2022837584,1607244650,2118541908,2366882550,3635996816,972512814,3283088770,1568718495,3499326569,3576539503,621982671,2895723464,410887952,2623762152,1002142683,645401037,1494807662,2595684844,1335535747,2507040230,4293295786,3167684641,367585007,3885750714,1865862730,2668221674,2960971305,2763173681,1059270954,2777952454,2724642869,1320957812,2194319100,2429595872,2815956275,77089521,3973773121,3444575871,2448830231,1305906550,4021308739,2857194700,2516901860,3518358430,1787304780,740276417,1699839814,1592394909,2352307457,2272556026,188821243,1729977011,3687994002,274084841,3594982253,3613494426,2701949495,4162096729,322734571,2837966542,1640576439,484830689,1202797690,3537852828,4067639125,349075736,3342319475,4157467219,4255800159,1030690015,1155237496,2951971274,1757691577,607398968,2738905026,499347990,3794078908,1011452712,227885567,2818666809,213114376,3034881240,1455525988,3414450555,850817237,1817998408,3092726480],Si=[0,235474187,470948374,303765277,941896748,908933415,607530554,708780849,1883793496,2118214995,1817866830,1649639237,1215061108,1181045119,1417561698,1517767529,3767586992,4003061179,4236429990,4069246893,3635733660,3602770327,3299278474,3400528769,2430122216,2664543715,2362090238,2193862645,2835123396,2801107407,3035535058,3135740889,3678124923,3576870512,3341394285,3374361702,3810496343,3977675356,4279080257,4043610186,2876494627,2776292904,3076639029,3110650942,2472011535,2640243204,2403728665,2169303058,1001089995,899835584,666464733,699432150,59727847,226906860,530400753,294930682,1273168787,1172967064,1475418501,1509430414,1942435775,2110667444,1876241833,1641816226,2910219766,2743034109,2976151520,3211623147,2505202138,2606453969,2302690252,2269728455,3711829422,3543599269,3240894392,3475313331,3843699074,3943906441,4178062228,4144047775,1306967366,1139781709,1374988112,1610459739,1975683434,2076935265,1775276924,1742315127,1034867998,866637845,566021896,800440835,92987698,193195065,429456164,395441711,1984812685,2017778566,1784663195,1683407248,1315562145,1080094634,1383856311,1551037884,101039829,135050206,437757123,337553864,1042385657,807962610,573804783,742039012,2531067453,2564033334,2328828971,2227573024,2935566865,2700099354,3001755655,3168937228,3868552805,3902563182,4203181171,4102977912,3736164937,3501741890,3265478751,3433712980,1106041591,1340463100,1576976609,1408749034,2043211483,2009195472,1708848333,1809054150,832877231,1068351396,766945465,599762354,159417987,126454664,361929877,463180190,2709260871,2943682380,3178106961,3009879386,2572697195,2538681184,2236228733,2336434550,3509871135,3745345300,3441850377,3274667266,3910161971,3877198648,4110568485,4211818798,2597806476,2497604743,2261089178,2295101073,2733856160,2902087851,3202437046,2968011453,3936291284,3835036895,4136440770,4169408201,3535486456,3702665459,3467192302,3231722213,2051518780,1951317047,1716890410,1750902305,1113818384,1282050075,1584504582,1350078989,168810852,67556463,371049330,404016761,841739592,1008918595,775550814,540080725,3969562369,3801332234,4035489047,4269907996,3569255213,3669462566,3366754619,3332740144,2631065433,2463879762,2160117071,2395588676,2767645557,2868897406,3102011747,3069049960,202008497,33778362,270040487,504459436,875451293,975658646,675039627,641025152,2084704233,1917518562,1615861247,1851332852,1147550661,1248802510,1484005843,1451044056,933301370,967311729,733156972,632953703,260388950,25965917,328671808,496906059,1206477858,1239443753,1543208500,1441952575,2144161806,1908694277,1675577880,1842759443,3610369226,3644379585,3408119516,3307916247,4011190502,3776767469,4077384432,4245618683,2809771154,2842737049,3144396420,3043140495,2673705150,2438237621,2203032232,2370213795],Ei=[0,185469197,370938394,487725847,741876788,657861945,975451694,824852259,1483753576,1400783205,1315723890,1164071807,1950903388,2135319889,1649704518,1767536459,2967507152,3152976349,2801566410,2918353863,2631447780,2547432937,2328143614,2177544179,3901806776,3818836405,4270639778,4118987695,3299409036,3483825537,3535072918,3652904859,2077965243,1893020342,1841768865,1724457132,1474502543,1559041666,1107234197,1257309336,598438867,681933534,901210569,1052338372,261314535,77422314,428819965,310463728,3409685355,3224740454,3710368113,3593056380,3875770207,3960309330,4045380933,4195456072,2471224067,2554718734,2237133081,2388260884,3212035895,3028143674,2842678573,2724322336,4138563181,4255350624,3769721975,3955191162,3667219033,3516619604,3431546947,3347532110,2933734917,2782082824,3099667487,3016697106,2196052529,2313884476,2499348523,2683765030,1179510461,1296297904,1347548327,1533017514,1786102409,1635502980,2087309459,2003294622,507358933,355706840,136428751,53458370,839224033,957055980,605657339,790073846,2373340630,2256028891,2607439820,2422494913,2706270690,2856345839,3075636216,3160175349,3573941694,3725069491,3273267108,3356761769,4181598602,4063242375,4011996048,3828103837,1033297158,915985419,730517276,545572369,296679730,446754879,129166120,213705253,1709610350,1860738147,1945798516,2029293177,1239331162,1120974935,1606591296,1422699085,4148292826,4233094615,3781033664,3931371469,3682191598,3497509347,3446004468,3328955385,2939266226,2755636671,3106780840,2988687269,2198438022,2282195339,2501218972,2652609425,1201765386,1286567175,1371368976,1521706781,1805211710,1620529459,2105887268,1988838185,533804130,350174575,164439672,46346101,870912086,954669403,636813900,788204353,2358957921,2274680428,2592523643,2441661558,2695033685,2880240216,3065962831,3182487618,3572145929,3756299780,3270937875,3388507166,4174560061,4091327024,4006521127,3854606378,1014646705,930369212,711349675,560487590,272786309,457992840,106852767,223377554,1678381017,1862534868,1914052035,2031621326,1211247597,1128014560,1580087799,1428173050,32283319,182621114,401639597,486441376,768917123,651868046,1003007129,818324884,1503449823,1385356242,1333838021,1150208456,1973745387,2125135846,1673061617,1756818940,2970356327,3120694122,2802849917,2887651696,2637442643,2520393566,2334669897,2149987652,3917234703,3799141122,4284502037,4100872472,3309594171,3460984630,3545789473,3629546796,2050466060,1899603969,1814803222,1730525723,1443857720,1560382517,1075025698,1260232239,575138148,692707433,878443390,1062597235,243256656,91341917,409198410,325965383,3403100636,3252238545,3704300486,3620022987,3874428392,3990953189,4042459122,4227665663,2460449204,2578018489,2226875310,2411029155,3198115200,3046200461,2827177882,2743944855],Ai=[0,218828297,437656594,387781147,875313188,958871085,775562294,590424639,1750626376,1699970625,1917742170,2135253587,1551124588,1367295589,1180849278,1265195639,3501252752,3720081049,3399941250,3350065803,3835484340,3919042237,4270507174,4085369519,3102249176,3051593425,2734591178,2952102595,2361698556,2177869557,2530391278,2614737639,3145456443,3060847922,2708326185,2892417312,2404901663,2187128086,2504130317,2555048196,3542330227,3727205754,3375740769,3292445032,3876557655,3926170974,4246310725,4027744588,1808481195,1723872674,1910319033,2094410160,1608975247,1391201670,1173430173,1224348052,59984867,244860394,428169201,344873464,935293895,984907214,766078933,547512796,1844882806,1627235199,2011214180,2062270317,1507497298,1423022939,1137477952,1321699145,95345982,145085239,532201772,313773861,830661914,1015671571,731183368,648017665,3175501286,2957853679,2807058932,2858115069,2305455554,2220981195,2474404304,2658625497,3575528878,3625268135,3473416636,3254988725,3778151818,3963161475,4213447064,4130281361,3599595085,3683022916,3432737375,3247465558,3802222185,4020912224,4172763771,4122762354,3201631749,3017672716,2764249623,2848461854,2331590177,2280796200,2431590963,2648976442,104699613,188127444,472615631,287343814,840019705,1058709744,671593195,621591778,1852171925,1668212892,1953757831,2037970062,1514790577,1463996600,1080017571,1297403050,3673637356,3623636965,3235995134,3454686199,4007360968,3822090177,4107101658,4190530515,2997825956,3215212461,2830708150,2779915199,2256734592,2340947849,2627016082,2443058075,172466556,122466165,273792366,492483431,1047239e3,861968209,612205898,695634755,1646252340,1863638845,2013908262,1963115311,1446242576,1530455833,1277555970,1093597963,1636604631,1820824798,2073724613,1989249228,1436590835,1487645946,1337376481,1119727848,164948639,81781910,331544205,516552836,1039717051,821288114,669961897,719700128,2973530695,3157750862,2871682645,2787207260,2232435299,2283490410,2667994737,2450346104,3647212047,3564045318,3279033885,3464042516,3980931627,3762502690,4150144569,4199882800,3070356634,3121275539,2904027272,2686254721,2200818878,2384911031,2570832044,2486224549,3747192018,3528626907,3310321856,3359936201,3950355702,3867060991,4049844452,4234721005,1739656202,1790575107,2108100632,1890328081,1402811438,1586903591,1233856572,1149249077,266959938,48394827,369057872,418672217,1002783846,919489135,567498868,752375421,209336225,24197544,376187827,459744698,945164165,895287692,574624663,793451934,1679968233,1764313568,2117360635,1933530610,1343127501,1560637892,1243112415,1192455638,3704280881,3519142200,3336358691,3419915562,3907448597,3857572124,4075877127,4294704398,3029510009,3113855344,2927934315,2744104290,2159976285,2377486676,2594734927,2544078150],Bi=[0,151849742,303699484,454499602,607398968,758720310,908999204,1059270954,1214797936,1097159550,1517440620,1400849762,1817998408,1699839814,2118541908,2001430874,2429595872,2581445614,2194319100,2345119218,3034881240,3186202582,2801699524,2951971274,3635996816,3518358430,3399679628,3283088770,4237083816,4118925222,4002861748,3885750714,1002142683,850817237,698445255,548169417,529487843,377642221,227885567,77089521,1943217067,2061379749,1640576439,1757691577,1474760595,1592394909,1174215055,1290801793,2875968315,2724642869,3111247143,2960971305,2405426947,2253581325,2638606623,2487810577,3808662347,3926825029,4044981591,4162096729,3342319475,3459953789,3576539503,3693126241,1986918061,2137062819,1685577905,1836772287,1381620373,1532285339,1078185097,1229899655,1040559837,923313619,740276417,621982671,439452389,322734571,137073913,19308535,3871163981,4021308739,4104605777,4255800159,3263785589,3414450555,3499326569,3651041127,2933202493,2815956275,3167684641,3049390895,2330014213,2213296395,2566595609,2448830231,1305906550,1155237496,1607244650,1455525988,1776460110,1626319424,2079897426,1928707164,96392454,213114376,396673818,514443284,562755902,679998e3,865136418,983426092,3708173718,3557504664,3474729866,3323011204,4180808110,4030667424,3945269170,3794078908,2507040230,2623762152,2272556026,2390325492,2975484382,3092726480,2738905026,2857194700,3973773121,3856137295,4274053469,4157467219,3371096953,3252932727,3673476453,3556361835,2763173681,2915017791,3064510765,3215307299,2156299017,2307622919,2459735317,2610011675,2081048481,1963412655,1846563261,1729977011,1480485785,1362321559,1243905413,1126790795,878845905,1030690015,645401037,796197571,274084841,425408743,38544885,188821243,3613494426,3731654548,3313212038,3430322568,4082475170,4200115116,3780097726,3896688048,2668221674,2516901860,2366882550,2216610296,3141400786,2989552604,2837966542,2687165888,1202797690,1320957812,1437280870,1554391400,1669664834,1787304780,1906247262,2022837584,265905162,114585348,499347990,349075736,736970802,585122620,972512814,821712160,2595684844,2478443234,2293045232,2174754046,3196267988,3079546586,2895723464,2777952454,3537852828,3687994002,3234156416,3385345166,4142626212,4293295786,3841024952,3992742070,174567692,57326082,410887952,292596766,777231668,660510266,1011452712,893681702,1108339068,1258480242,1343618912,1494807662,1715193156,1865862730,1948373848,2100090966,2701949495,2818666809,3004591147,3122358053,2235061775,2352307457,2535604243,2653899549,3915653703,3764988233,4219352155,4067639125,3444575871,3294430577,3746175075,3594982253,836553431,953270745,600235211,718002117,367585007,484830689,133361907,251657213,2041877159,1891211689,1806599355,1654886325,1568718495,1418573201,1335535747,1184342925];function xi(e){for(var t=[],r=0;r<e.length;r+=4)t.push(e[r]<<24|e[r+1]<<16|e[r+2]<<8|e[r+3]);return t}var Ui=function(e){if(!(this instanceof Ui))throw Error("AES must be instanitated with `new`");Object.defineProperty(this,"key",{value:ni(e,!0)}),this._prepare()};Ui.prototype._prepare=function(){var e=ci[this.key.length];if(null==e)throw new Error("invalid key size (must be 16, 24 or 32 bytes)");this._Ke=[],this._Kd=[];for(var t=0;t<=e;t++)this._Ke.push([0,0,0,0]),this._Kd.push([0,0,0,0]);var r,i=4*(e+1),n=this.key.length/4,s=xi(this.key);for(t=0;t<n;t++)r=t>>2,this._Ke[r][t%4]=s[t],this._Kd[e-r][t%4]=s[t];for(var a,o=0,d=n;d<i;){if(a=s[n-1],s[0]^=hi[a>>16&255]<<24^hi[a>>8&255]<<16^hi[255&a]<<8^hi[a>>24&255]^ui[o]<<24,o+=1,8!=n)for(t=1;t<n;t++)s[t]^=s[t-1];else{for(t=1;t<n/2;t++)s[t]^=s[t-1];a=s[n/2-1],s[n/2]^=hi[255&a]^hi[a>>8&255]<<8^hi[a>>16&255]<<16^hi[a>>24&255]<<24;for(t=n/2+1;t<n;t++)s[t]^=s[t-1]}for(t=0;t<n&&d<i;)l=d>>2,c=d%4,this._Ke[l][c]=s[t],this._Kd[e-l][c]=s[t++],d++}for(var l=1;l<e;l++)for(var c=0;c<4;c++)a=this._Kd[l][c],this._Kd[l][c]=Si[a>>24&255]^Ei[a>>16&255]^Ai[a>>8&255]^Bi[255&a]},Ui.prototype.encrypt=function(e){if(16!=e.length)throw new Error("invalid plaintext size (must be 16 bytes)");for(var t=this._Ke.length-1,r=[0,0,0,0],i=xi(e),n=0;n<4;n++)i[n]^=this._Ke[0][n];for(var s=1;s<t;s++){for(n=0;n<4;n++)r[n]=pi[i[n]>>24&255]^_i[i[(n+1)%4]>>16&255]^mi[i[(n+2)%4]>>8&255]^gi[255&i[(n+3)%4]]^this._Ke[s][n];i=r.slice()}var a,o=si(16);for(n=0;n<4;n++)a=this._Ke[t][n],o[4*n]=255&(hi[i[n]>>24&255]^a>>24),o[4*n+1]=255&(hi[i[(n+1)%4]>>16&255]^a>>16),o[4*n+2]=255&(hi[i[(n+2)%4]>>8&255]^a>>8),o[4*n+3]=255&(hi[255&i[(n+3)%4]]^a);return o},Ui.prototype.decrypt=function(e){if(16!=e.length)throw new Error("invalid ciphertext size (must be 16 bytes)");for(var t=this._Kd.length-1,r=[0,0,0,0],i=xi(e),n=0;n<4;n++)i[n]^=this._Kd[0][n];for(var s=1;s<t;s++){for(n=0;n<4;n++)r[n]=yi[i[n]>>24&255]^vi[i[(n+3)%4]>>16&255]^bi[i[(n+2)%4]>>8&255]^wi[255&i[(n+1)%4]]^this._Kd[s][n];i=r.slice()}var a,o=si(16);for(n=0;n<4;n++)a=this._Kd[t][n],o[4*n]=255&(fi[i[n]>>24&255]^a>>24),o[4*n+1]=255&(fi[i[(n+3)%4]>>16&255]^a>>16),o[4*n+2]=255&(fi[i[(n+2)%4]>>8&255]^a>>8),o[4*n+3]=255&(fi[255&i[(n+1)%4]]^a);return o};var Ti=function(e){if(!(this instanceof Ti))throw Error("AES must be instanitated with `new`");this.description="Electronic Code Block",this.name="ecb",this._aes=new Ui(e)};Ti.prototype.encrypt=function(e){if((e=ni(e)).length%16!=0)throw new Error("invalid plaintext size (must be multiple of 16 bytes)");for(var t=si(e.length),r=si(16),i=0;i<e.length;i+=16)ai(e,r,0,i,i+16),ai(r=this._aes.encrypt(r),t,i);return t},Ti.prototype.decrypt=function(e){if((e=ni(e)).length%16!=0)throw new Error("invalid ciphertext size (must be multiple of 16 bytes)");for(var t=si(e.length),r=si(16),i=0;i<e.length;i+=16)ai(e,r,0,i,i+16),ai(r=this._aes.decrypt(r),t,i);return t};var ki=function(e,t){if(!(this instanceof ki))throw Error("AES must be instanitated with `new`");if(this.description="Cipher Block Chaining",this.name="cbc",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 bytes)")}else t=si(16);this._lastCipherblock=ni(t,!0),this._aes=new Ui(e)};ki.prototype.encrypt=function(e){if((e=ni(e)).length%16!=0)throw new Error("invalid plaintext size (must be multiple of 16 bytes)");for(var t=si(e.length),r=si(16),i=0;i<e.length;i+=16){ai(e,r,0,i,i+16);for(var n=0;n<16;n++)r[n]^=this._lastCipherblock[n];this._lastCipherblock=this._aes.encrypt(r),ai(this._lastCipherblock,t,i)}return t},ki.prototype.decrypt=function(e){if((e=ni(e)).length%16!=0)throw new Error("invalid ciphertext size (must be multiple of 16 bytes)");for(var t=si(e.length),r=si(16),i=0;i<e.length;i+=16){ai(e,r,0,i,i+16),r=this._aes.decrypt(r);for(var n=0;n<16;n++)t[i+n]=r[n]^this._lastCipherblock[n];ai(e,this._lastCipherblock,0,i,i+16)}return t};var Ci=function(e,t,r){if(!(this instanceof Ci))throw Error("AES must be instanitated with `new`");if(this.description="Cipher Feedback",this.name="cfb",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 size)")}else t=si(16);r||(r=1),this.segmentSize=r,this._shiftRegister=ni(t,!0),this._aes=new Ui(e)};Ci.prototype.encrypt=function(e){if(e.length%this.segmentSize!=0)throw new Error("invalid plaintext size (must be segmentSize bytes)");for(var t,r=ni(e,!0),i=0;i<r.length;i+=this.segmentSize){t=this._aes.encrypt(this._shiftRegister);for(var n=0;n<this.segmentSize;n++)r[i+n]^=t[n];ai(this._shiftRegister,this._shiftRegister,0,this.segmentSize),ai(r,this._shiftRegister,16-this.segmentSize,i,i+this.segmentSize)}return r},Ci.prototype.decrypt=function(e){if(e.length%this.segmentSize!=0)throw new Error("invalid ciphertext size (must be segmentSize bytes)");for(var t,r=ni(e,!0),i=0;i<r.length;i+=this.segmentSize){t=this._aes.encrypt(this._shiftRegister);for(var n=0;n<this.segmentSize;n++)r[i+n]^=t[n];ai(this._shiftRegister,this._shiftRegister,0,this.segmentSize),ai(e,this._shiftRegister,16-this.segmentSize,i,i+this.segmentSize)}return r};var Di=function(e,t){if(!(this instanceof Di))throw Error("AES must be instanitated with `new`");if(this.description="Output Feedback",this.name="ofb",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 bytes)")}else t=si(16);this._lastPrecipher=ni(t,!0),this._lastPrecipherIndex=16,this._aes=new Ui(e)};Di.prototype.encrypt=function(e){for(var t=ni(e,!0),r=0;r<t.length;r++)16===this._lastPrecipherIndex&&(this._lastPrecipher=this._aes.encrypt(this._lastPrecipher),this._lastPrecipherIndex=0),t[r]^=this._lastPrecipher[this._lastPrecipherIndex++];return t},Di.prototype.decrypt=Di.prototype.encrypt;var Ii=function(e){if(!(this instanceof Ii))throw Error("Counter must be instanitated with `new`");0===e||e||(e=1),"number"==typeof e?(this._counter=si(16),this.setValue(e)):this.setBytes(e)};Ii.prototype.setValue=function(e){if("number"!=typeof e||parseInt(e)!=e)throw new Error("invalid counter value (must be an integer)");if(e>Number.MAX_SAFE_INTEGER)throw new Error("integer value out of safe range");for(var t=15;t>=0;--t)this._counter[t]=e%256,e=parseInt(e/256)},Ii.prototype.setBytes=function(e){if(16!=(e=ni(e,!0)).length)throw new Error("invalid counter bytes size (must be 16 bytes)");this._counter=e},Ii.prototype.increment=function(){for(var e=15;e>=0;e--){if(255!==this._counter[e]){this._counter[e]++;break}this._counter[e]=0}};var Fi=function(e,t){if(!(this instanceof Fi))throw Error("AES must be instanitated with `new`");this.description="Counter",this.name="ctr",t instanceof Ii||(t=new Ii(t)),this._counter=t,this._remainingCounter=null,this._remainingCounterIndex=16,this._aes=new Ui(e)};Fi.prototype.encrypt=function(e){for(var t=ni(e,!0),r=0;r<t.length;r++)16===this._remainingCounterIndex&&(this._remainingCounter=this._aes.encrypt(this._counter._counter),this._remainingCounterIndex=0,this._counter.increment()),t[r]^=this._remainingCounter[this._remainingCounterIndex++];return t},Fi.prototype.decrypt=Fi.prototype.encrypt;const Pi={AES:Ui,Counter:Ii,ModeOfOperation:{ecb:Ti,cbc:ki,cfb:Ci,ofb:Di,ctr:Fi},utils:{hex:li,utf8:di},padding:{pkcs7:{pad:function(e){var t=16-(e=ni(e,!0)).length%16,r=si(e.length+t);ai(e,r);for(var i=e.length;i<r.length;i++)r[i]=t;return r},strip:function(e){if((e=ni(e,!0)).length<16)throw new Error("PKCS#7 invalid length");var t=e[e.length-1];if(t>16)throw new Error("PKCS#7 padding byte out of range");for(var r=e.length-t,i=0;i<t;i++)if(e[r+i]!==t)throw new Error("PKCS#7 invalid padding byte");var n=si(r);return ai(e,n,0,0,r),n}}},_arrayTest:{coerceArray:ni,createArray:si,copyArray:ai}};var Li=Nt((function(e,t){var r;e.exports=(r=r||function(e,t){var r;if("undefined"!=typeof window&&window.crypto&&(r=window.crypto),"undefined"!=typeof self&&self.crypto&&(r=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(r=globalThis.crypto),!r&&"undefined"!=typeof window&&window.msCrypto&&(r=window.msCrypto),!r&&void 0!==zt&&zt.crypto&&(r=zt.crypto),!r)try{r=n.default}catch(e){}var i=function(){if(r){if("function"==typeof r.getRandomValues)try{return r.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof r.randomBytes)try{return r.randomBytes(4).readInt32LE()}catch(e){}}throw new Error("Native crypto module could not be used to get secure random number.")},s=Object.create||function(){function e(){}return function(t){var r;return e.prototype=t,r=new e,e.prototype=null,r}}(),a={},o=a.lib={},d=o.Base={extend:function(e){var t=s(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},l=o.WordArray=d.extend({init:function(e,r){e=this.words=e||[],this.sigBytes=r!=t?r:4*e.length},toString:function(e){return(e||u).stringify(this)},concat:function(e){var t=this.words,r=e.words,i=this.sigBytes,n=e.sigBytes;if(this.clamp(),i%4)for(var s=0;s<n;s++){var a=r[s>>>2]>>>24-s%4*8&255;t[i+s>>>2]|=a<<24-(i+s)%4*8}else for(var o=0;o<n;o+=4)t[i+o>>>2]=r[o>>>2];return this.sigBytes+=n,this},clamp:function(){var t=this.words,r=this.sigBytes;t[r>>>2]&=4294967295<<32-r%4*8,t.length=e.ceil(r/4)},clone:function(){var e=d.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],r=0;r<e;r+=4)t.push(i());return new l.init(t,e)}}),c=a.enc={},u=c.Hex={stringify:function(e){for(var t=e.words,r=e.sigBytes,i=[],n=0;n<r;n++){var s=t[n>>>2]>>>24-n%4*8&255;i.push((s>>>4).toString(16)),i.push((15&s).toString(16))}return i.join("")},parse:function(e){for(var t=e.length,r=[],i=0;i<t;i+=2)r[i>>>3]|=parseInt(e.substr(i,2),16)<<24-i%8*4;return new l.init(r,t/2)}},h=c.Latin1={stringify:function(e){for(var t=e.words,r=e.sigBytes,i=[],n=0;n<r;n++){var s=t[n>>>2]>>>24-n%4*8&255;i.push(String.fromCharCode(s))}return i.join("")},parse:function(e){for(var t=e.length,r=[],i=0;i<t;i++)r[i>>>2]|=(255&e.charCodeAt(i))<<24-i%4*8;return new l.init(r,t)}},f=c.Utf8={stringify:function(e){try{return decodeURIComponent(escape(h.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return h.parse(unescape(encodeURIComponent(e)))}},p=o.BufferedBlockAlgorithm=d.extend({reset:function(){this._data=new l.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=f.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var r,i=this._data,n=i.words,s=i.sigBytes,a=this.blockSize,o=s/(4*a),d=(o=t?e.ceil(o):e.max((0|o)-this._minBufferSize,0))*a,c=e.min(4*d,s);if(d){for(var u=0;u<d;u+=a)this._doProcessBlock(n,u);r=n.splice(0,d),i.sigBytes-=c}return new l.init(r,c)},clone:function(){var e=d.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});o.Hasher=p.extend({cfg:d.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,r){return new e.init(r).finalize(t)}},_createHmacHelper:function(e){return function(t,r){return new _.HMAC.init(e,r).finalize(t)}}});var _=a.algo={};return a}(Math),r)}));Nt((function(e,t){var r,i,n,s,a,o,d;e.exports=(n=(i=d=Li).lib,s=n.Base,a=n.WordArray,(o=i.x64={}).Word=s.extend({init:function(e,t){this.high=e,this.low=t}}),o.WordArray=s.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=t!=r?t:8*e.length},toX32:function(){for(var e=this.words,t=e.length,r=[],i=0;i<t;i++){var n=e[i];r.push(n.high),r.push(n.low)}return a.create(r,this.sigBytes)},clone:function(){for(var e=s.clone.call(this),t=e.words=this.words.slice(0),r=t.length,i=0;i<r;i++)t[i]=t[i].clone();return e}}),d)})),Nt((function(e,t){var r;e.exports=(r=Li,function(){if("function"==typeof ArrayBuffer){var e=r.lib.WordArray,t=e.init,i=e.init=function(e){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){for(var r=e.byteLength,i=[],n=0;n<r;n++)i[n>>>2]|=e[n]<<24-n%4*8;t.call(this,i,r)}else t.apply(this,arguments)};i.prototype=e}}(),r.lib.WordArray)})),Nt((function(e,t){var r;e.exports=(r=Li,function(){var e=r,t=e.lib.WordArray,i=e.enc;function n(e){return e<<8&4278255360|e>>>8&16711935}i.Utf16=i.Utf16BE={stringify:function(e){for(var t=e.words,r=e.sigBytes,i=[],n=0;n<r;n+=2){var s=t[n>>>2]>>>16-n%4*8&65535;i.push(String.fromCharCode(s))}return i.join("")},parse:function(e){for(var r=e.length,i=[],n=0;n<r;n++)i[n>>>1]|=e.charCodeAt(n)<<16-n%2*16;return t.create(i,2*r)}},i.Utf16LE={stringify:function(e){for(var t=e.words,r=e.sigBytes,i=[],s=0;s<r;s+=2){var a=n(t[s>>>2]>>>16-s%4*8&65535);i.push(String.fromCharCode(a))}return i.join("")},parse:function(e){for(var r=e.length,i=[],s=0;s<r;s++)i[s>>>1]|=n(e.charCodeAt(s)<<16-s%2*16);return t.create(i,2*r)}}}(),r.enc.Utf16)})),Nt((function(e,t){var r;e.exports=(r=Li,function(){var e=r,t=e.lib.WordArray;function i(e,r,i){for(var n=[],s=0,a=0;a<r;a++)if(a%4){var o=i[e.charCodeAt(a-1)]<<a%4*2|i[e.charCodeAt(a)]>>>6-a%4*2;n[s>>>2]|=o<<24-s%4*8,s++}return t.create(n,s)}e.enc.Base64={stringify:function(e){var t=e.words,r=e.sigBytes,i=this._map;e.clamp();for(var n=[],s=0;s<r;s+=3)for(var a=(t[s>>>2]>>>24-s%4*8&255)<<16|(t[s+1>>>2]>>>24-(s+1)%4*8&255)<<8|t[s+2>>>2]>>>24-(s+2)%4*8&255,o=0;o<4&&s+.75*o<r;o++)n.push(i.charAt(a>>>6*(3-o)&63));var d=i.charAt(64);if(d)for(;n.length%4;)n.push(d);return n.join("")},parse:function(e){var t=e.length,r=this._map,n=this._reverseMap;if(!n){n=this._reverseMap=[];for(var s=0;s<r.length;s++)n[r.charCodeAt(s)]=s}var a=r.charAt(64);if(a){var o=e.indexOf(a);-1!==o&&(t=o)}return i(e,t,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),r.enc.Base64)})),Nt((function(e,t){var r;e.exports=(r=Li,function(){var e=r,t=e.lib.WordArray;function i(e,r,i){for(var n=[],s=0,a=0;a<r;a++)if(a%4){var o=i[e.charCodeAt(a-1)]<<a%4*2|i[e.charCodeAt(a)]>>>6-a%4*2;n[s>>>2]|=o<<24-s%4*8,s++}return t.create(n,s)}e.enc.Base64url={stringify:function(e,t){void 0===t&&(t=!0);var r=e.words,i=e.sigBytes,n=t?this._safe_map:this._map;e.clamp();for(var s=[],a=0;a<i;a+=3)for(var o=(r[a>>>2]>>>24-a%4*8&255)<<16|(r[a+1>>>2]>>>24-(a+1)%4*8&255)<<8|r[a+2>>>2]>>>24-(a+2)%4*8&255,d=0;d<4&&a+.75*d<i;d++)s.push(n.charAt(o>>>6*(3-d)&63));var l=n.charAt(64);if(l)for(;s.length%4;)s.push(l);return s.join("")},parse:function(e,t){void 0===t&&(t=!0);var r=e.length,n=t?this._safe_map:this._map,s=this._reverseMap;if(!s){s=this._reverseMap=[];for(var a=0;a<n.length;a++)s[n.charCodeAt(a)]=a}var o=n.charAt(64);if(o){var d=e.indexOf(o);-1!==d&&(r=d)}return i(e,r,s)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"}}(),r.enc.Base64url)})),Nt((function(e,t){var r;e.exports=(r=Li,function(e){var t=r,i=t.lib,n=i.WordArray,s=i.Hasher,a=t.algo,o=[];!function(){for(var t=0;t<64;t++)o[t]=4294967296*e.abs(e.sin(t+1))|0}();var d=a.MD5=s.extend({_doReset:function(){this._hash=new n.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var i=t+r,n=e[i];e[i]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8)}var s=this._hash.words,a=e[t+0],d=e[t+1],f=e[t+2],p=e[t+3],_=e[t+4],m=e[t+5],g=e[t+6],y=e[t+7],v=e[t+8],b=e[t+9],w=e[t+10],S=e[t+11],E=e[t+12],A=e[t+13],B=e[t+14],x=e[t+15],U=s[0],T=s[1],k=s[2],C=s[3];U=l(U,T,k,C,a,7,o[0]),C=l(C,U,T,k,d,12,o[1]),k=l(k,C,U,T,f,17,o[2]),T=l(T,k,C,U,p,22,o[3]),U=l(U,T,k,C,_,7,o[4]),C=l(C,U,T,k,m,12,o[5]),k=l(k,C,U,T,g,17,o[6]),T=l(T,k,C,U,y,22,o[7]),U=l(U,T,k,C,v,7,o[8]),C=l(C,U,T,k,b,12,o[9]),k=l(k,C,U,T,w,17,o[10]),T=l(T,k,C,U,S,22,o[11]),U=l(U,T,k,C,E,7,o[12]),C=l(C,U,T,k,A,12,o[13]),k=l(k,C,U,T,B,17,o[14]),U=c(U,T=l(T,k,C,U,x,22,o[15]),k,C,d,5,o[16]),C=c(C,U,T,k,g,9,o[17]),k=c(k,C,U,T,S,14,o[18]),T=c(T,k,C,U,a,20,o[19]),U=c(U,T,k,C,m,5,o[20]),C=c(C,U,T,k,w,9,o[21]),k=c(k,C,U,T,x,14,o[22]),T=c(T,k,C,U,_,20,o[23]),U=c(U,T,k,C,b,5,o[24]),C=c(C,U,T,k,B,9,o[25]),k=c(k,C,U,T,p,14,o[26]),T=c(T,k,C,U,v,20,o[27]),U=c(U,T,k,C,A,5,o[28]),C=c(C,U,T,k,f,9,o[29]),k=c(k,C,U,T,y,14,o[30]),U=u(U,T=c(T,k,C,U,E,20,o[31]),k,C,m,4,o[32]),C=u(C,U,T,k,v,11,o[33]),k=u(k,C,U,T,S,16,o[34]),T=u(T,k,C,U,B,23,o[35]),U=u(U,T,k,C,d,4,o[36]),C=u(C,U,T,k,_,11,o[37]),k=u(k,C,U,T,y,16,o[38]),T=u(T,k,C,U,w,23,o[39]),U=u(U,T,k,C,A,4,o[40]),C=u(C,U,T,k,a,11,o[41]),k=u(k,C,U,T,p,16,o[42]),T=u(T,k,C,U,g,23,o[43]),U=u(U,T,k,C,b,4,o[44]),C=u(C,U,T,k,E,11,o[45]),k=u(k,C,U,T,x,16,o[46]),U=h(U,T=u(T,k,C,U,f,23,o[47]),k,C,a,6,o[48]),C=h(C,U,T,k,y,10,o[49]),k=h(k,C,U,T,B,15,o[50]),T=h(T,k,C,U,m,21,o[51]),U=h(U,T,k,C,E,6,o[52]),C=h(C,U,T,k,p,10,o[53]),k=h(k,C,U,T,w,15,o[54]),T=h(T,k,C,U,d,21,o[55]),U=h(U,T,k,C,v,6,o[56]),C=h(C,U,T,k,x,10,o[57]),k=h(k,C,U,T,g,15,o[58]),T=h(T,k,C,U,A,21,o[59]),U=h(U,T,k,C,_,6,o[60]),C=h(C,U,T,k,S,10,o[61]),k=h(k,C,U,T,f,15,o[62]),T=h(T,k,C,U,b,21,o[63]),s[0]=s[0]+U|0,s[1]=s[1]+T|0,s[2]=s[2]+k|0,s[3]=s[3]+C|0},_doFinalize:function(){var t=this._data,r=t.words,i=8*this._nDataBytes,n=8*t.sigBytes;r[n>>>5]|=128<<24-n%32;var s=e.floor(i/4294967296),a=i;r[15+(n+64>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),r[14+(n+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),t.sigBytes=4*(r.length+1),this._process();for(var o=this._hash,d=o.words,l=0;l<4;l++){var c=d[l];d[l]=16711935&(c<<8|c>>>24)|4278255360&(c<<24|c>>>8)}return o},clone:function(){var e=s.clone.call(this);return e._hash=this._hash.clone(),e}});function l(e,t,r,i,n,s,a){var o=e+(t&r|~t&i)+n+a;return(o<<s|o>>>32-s)+t}function c(e,t,r,i,n,s,a){var o=e+(t&i|r&~i)+n+a;return(o<<s|o>>>32-s)+t}function u(e,t,r,i,n,s,a){var o=e+(t^r^i)+n+a;return(o<<s|o>>>32-s)+t}function h(e,t,r,i,n,s,a){var o=e+(r^(t|~i))+n+a;return(o<<s|o>>>32-s)+t}t.MD5=s._createHelper(d),t.HmacMD5=s._createHmacHelper(d)}(Math),r.MD5)})),Nt((function(e,t){var r,i,n,s,a,o,d,l;e.exports=(i=(r=l=Li).lib,n=i.WordArray,s=i.Hasher,a=r.algo,o=[],d=a.SHA1=s.extend({_doReset:function(){this._hash=new n.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=this._hash.words,i=r[0],n=r[1],s=r[2],a=r[3],d=r[4],l=0;l<80;l++){if(l<16)o[l]=0|e[t+l];else{var c=o[l-3]^o[l-8]^o[l-14]^o[l-16];o[l]=c<<1|c>>>31}var u=(i<<5|i>>>27)+d+o[l];u+=l<20?1518500249+(n&s|~n&a):l<40?1859775393+(n^s^a):l<60?(n&s|n&a|s&a)-1894007588:(n^s^a)-899497514,d=a,a=s,s=n<<30|n>>>2,n=i,i=u}r[0]=r[0]+i|0,r[1]=r[1]+n|0,r[2]=r[2]+s|0,r[3]=r[3]+a|0,r[4]=r[4]+d|0},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,i=8*e.sigBytes;return t[i>>>5]|=128<<24-i%32,t[14+(i+64>>>9<<4)]=Math.floor(r/4294967296),t[15+(i+64>>>9<<4)]=r,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=s.clone.call(this);return e._hash=this._hash.clone(),e}}),r.SHA1=s._createHelper(d),r.HmacSHA1=s._createHmacHelper(d),l.SHA1)})),Nt((function(e,t){var r;e.exports=(r=Li,function(e){var t=r,i=t.lib,n=i.WordArray,s=i.Hasher,a=t.algo,o=[],d=[];!function(){function t(t){for(var r=e.sqrt(t),i=2;i<=r;i++)if(!(t%i))return!1;return!0}function r(e){return 4294967296*(e-(0|e))|0}for(var i=2,n=0;n<64;)t(i)&&(n<8&&(o[n]=r(e.pow(i,.5))),d[n]=r(e.pow(i,1/3)),n++),i++}();var l=[],c=a.SHA256=s.extend({_doReset:function(){this._hash=new n.init(o.slice(0))},_doProcessBlock:function(e,t){for(var r=this._hash.words,i=r[0],n=r[1],s=r[2],a=r[3],o=r[4],c=r[5],u=r[6],h=r[7],f=0;f<64;f++){if(f<16)l[f]=0|e[t+f];else{var p=l[f-15],_=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,m=l[f-2],g=(m<<15|m>>>17)^(m<<13|m>>>19)^m>>>10;l[f]=_+l[f-7]+g+l[f-16]}var y=i&n^i&s^n&s,v=(i<<30|i>>>2)^(i<<19|i>>>13)^(i<<10|i>>>22),b=h+((o<<26|o>>>6)^(o<<21|o>>>11)^(o<<7|o>>>25))+(o&c^~o&u)+d[f]+l[f];h=u,u=c,c=o,o=a+b|0,a=s,s=n,n=i,i=b+(v+y)|0}r[0]=r[0]+i|0,r[1]=r[1]+n|0,r[2]=r[2]+s|0,r[3]=r[3]+a|0,r[4]=r[4]+o|0,r[5]=r[5]+c|0,r[6]=r[6]+u|0,r[7]=r[7]+h|0},_doFinalize:function(){var t=this._data,r=t.words,i=8*this._nDataBytes,n=8*t.sigBytes;return r[n>>>5]|=128<<24-n%32,r[14+(n+64>>>9<<4)]=e.floor(i/4294967296),r[15+(n+64>>>9<<4)]=i,t.sigBytes=4*r.length,this._process(),this._hash},clone:function(){var e=s.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=s._createHelper(c),t.HmacSHA256=s._createHmacHelper(c)}(Math),r.SHA256)})),Nt((function(e,t){var r,i,n,s,a,o;e.exports=(i=(r=o=Li).lib.WordArray,n=r.algo,s=n.SHA256,a=n.SHA224=s.extend({_doReset:function(){this._hash=new i.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var e=s._doFinalize.call(this);return e.sigBytes-=4,e}}),r.SHA224=s._createHelper(a),r.HmacSHA224=s._createHmacHelper(a),o.SHA224)})),Nt((function(e,t){var r;e.exports=(r=Li,function(){var e=r,t=e.lib.Hasher,i=e.x64,n=i.Word,s=i.WordArray,a=e.algo;function o(){return n.create.apply(n,arguments)}var d=[o(1116352408,3609767458),o(1899447441,602891725),o(3049323471,3964484399),o(3921009573,2173295548),o(961987163,4081628472),o(1508970993,3053834265),o(2453635748,2937671579),o(2870763221,3664609560),o(3624381080,2734883394),o(310598401,1164996542),o(607225278,1323610764),o(1426881987,3590304994),o(1925078388,4068182383),o(2162078206,991336113),o(2614888103,633803317),o(3248222580,3479774868),o(3835390401,2666613458),o(4022224774,944711139),o(264347078,2341262773),o(604807628,2007800933),o(770255983,1495990901),o(1249150122,1856431235),o(1555081692,3175218132),o(1996064986,2198950837),o(2554220882,3999719339),o(2821834349,766784016),o(2952996808,2566594879),o(3210313671,3203337956),o(3336571891,1034457026),o(3584528711,2466948901),o(113926993,3758326383),o(338241895,168717936),o(666307205,1188179964),o(773529912,1546045734),o(1294757372,1522805485),o(1396182291,2643833823),o(1695183700,2343527390),o(1986661051,1014477480),o(2177026350,1206759142),o(2456956037,344077627),o(2730485921,1290863460),o(2820302411,3158454273),o(3259730800,3505952657),o(3345764771,106217008),o(3516065817,3606008344),o(3600352804,1432725776),o(4094571909,1467031594),o(275423344,851169720),o(430227734,3100823752),o(506948616,1363258195),o(659060556,3750685593),o(883997877,3785050280),o(958139571,3318307427),o(1322822218,3812723403),o(1537002063,2003034995),o(1747873779,3602036899),o(1955562222,1575990012),o(2024104815,1125592928),o(2227730452,2716904306),o(2361852424,442776044),o(2428436474,593698344),o(2756734187,3733110249),o(3204031479,2999351573),o(3329325298,3815920427),o(3391569614,3928383900),o(3515267271,566280711),o(3940187606,3454069534),o(4118630271,4000239992),o(116418474,1914138554),o(174292421,2731055270),o(289380356,3203993006),o(460393269,320620315),o(685471733,587496836),o(852142971,1086792851),o(1017036298,365543100),o(1126000580,2618297676),o(1288033470,3409855158),o(1501505948,4234509866),o(1607167915,987167468),o(1816402316,1246189591)],l=[];!function(){for(var e=0;e<80;e++)l[e]=o()}();var c=a.SHA512=t.extend({_doReset:function(){this._hash=new s.init([new n.init(1779033703,4089235720),new n.init(3144134277,2227873595),new n.init(1013904242,4271175723),new n.init(2773480762,1595750129),new n.init(1359893119,2917565137),new n.init(2600822924,725511199),new n.init(528734635,4215389547),new n.init(1541459225,327033209)])},_doProcessBlock:function(e,t){for(var r=this._hash.words,i=r[0],n=r[1],s=r[2],a=r[3],o=r[4],c=r[5],u=r[6],h=r[7],f=i.high,p=i.low,_=n.high,m=n.low,g=s.high,y=s.low,v=a.high,b=a.low,w=o.high,S=o.low,E=c.high,A=c.low,B=u.high,x=u.low,U=h.high,T=h.low,k=f,C=p,D=_,I=m,F=g,P=y,L=v,M=b,R=w,z=S,N=E,O=A,G=B,H=x,$=U,V=T,W=0;W<80;W++){var j,q,Y=l[W];if(W<16)q=Y.high=0|e[t+2*W],j=Y.low=0|e[t+2*W+1];else{var K=l[W-15],X=K.high,Z=K.low,J=(X>>>1|Z<<31)^(X>>>8|Z<<24)^X>>>7,Q=(Z>>>1|X<<31)^(Z>>>8|X<<24)^(Z>>>7|X<<25),ee=l[W-2],te=ee.high,re=ee.low,ie=(te>>>19|re<<13)^(te<<3|re>>>29)^te>>>6,ne=(re>>>19|te<<13)^(re<<3|te>>>29)^(re>>>6|te<<26),se=l[W-7],ae=se.high,oe=se.low,de=l[W-16],le=de.high,ce=de.low;q=(q=(q=J+ae+((j=Q+oe)>>>0<Q>>>0?1:0))+ie+((j+=ne)>>>0<ne>>>0?1:0))+le+((j+=ce)>>>0<ce>>>0?1:0),Y.high=q,Y.low=j}var ue,he=R&N^~R&G,fe=z&O^~z&H,pe=k&D^k&F^D&F,_e=C&I^C&P^I&P,me=(k>>>28|C<<4)^(k<<30|C>>>2)^(k<<25|C>>>7),ge=(C>>>28|k<<4)^(C<<30|k>>>2)^(C<<25|k>>>7),ye=(R>>>14|z<<18)^(R>>>18|z<<14)^(R<<23|z>>>9),ve=(z>>>14|R<<18)^(z>>>18|R<<14)^(z<<23|R>>>9),be=d[W],we=be.high,Se=be.low,Ee=$+ye+((ue=V+ve)>>>0<V>>>0?1:0),Ae=ge+_e;$=G,V=H,G=N,H=O,N=R,O=z,R=L+(Ee=(Ee=(Ee=Ee+he+((ue+=fe)>>>0<fe>>>0?1:0))+we+((ue+=Se)>>>0<Se>>>0?1:0))+q+((ue+=j)>>>0<j>>>0?1:0))+((z=M+ue|0)>>>0<M>>>0?1:0)|0,L=F,M=P,F=D,P=I,D=k,I=C,k=Ee+(me+pe+(Ae>>>0<ge>>>0?1:0))+((C=ue+Ae|0)>>>0<ue>>>0?1:0)|0}p=i.low=p+C,i.high=f+k+(p>>>0<C>>>0?1:0),m=n.low=m+I,n.high=_+D+(m>>>0<I>>>0?1:0),y=s.low=y+P,s.high=g+F+(y>>>0<P>>>0?1:0),b=a.low=b+M,a.high=v+L+(b>>>0<M>>>0?1:0),S=o.low=S+z,o.high=w+R+(S>>>0<z>>>0?1:0),A=c.low=A+O,c.high=E+N+(A>>>0<O>>>0?1:0),x=u.low=x+H,u.high=B+G+(x>>>0<H>>>0?1:0),T=h.low=T+V,h.high=U+$+(T>>>0<V>>>0?1:0)},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,i=8*e.sigBytes;return t[i>>>5]|=128<<24-i%32,t[30+(i+128>>>10<<5)]=Math.floor(r/4294967296),t[31+(i+128>>>10<<5)]=r,e.sigBytes=4*t.length,this._process(),this._hash.toX32()},clone:function(){var e=t.clone.call(this);return e._hash=this._hash.clone(),e},blockSize:32});e.SHA512=t._createHelper(c),e.HmacSHA512=t._createHmacHelper(c)}(),r.SHA512)})),Nt((function(e,t){var r,i,n,s,a,o,d,l;e.exports=(i=(r=l=Li).x64,n=i.Word,s=i.WordArray,a=r.algo,o=a.SHA512,d=a.SHA384=o.extend({_doReset:function(){this._hash=new s.init([new n.init(3418070365,3238371032),new n.init(1654270250,914150663),new n.init(2438529370,812702999),new n.init(355462360,4144912697),new n.init(1731405415,4290775857),new n.init(2394180231,1750603025),new n.init(3675008525,1694076839),new n.init(1203062813,3204075428)])},_doFinalize:function(){var e=o._doFinalize.call(this);return e.sigBytes-=16,e}}),r.SHA384=o._createHelper(d),r.HmacSHA384=o._createHmacHelper(d),l.SHA384)})),Nt((function(e,t){var r;e.exports=(r=Li,function(e){var t=r,i=t.lib,n=i.WordArray,s=i.Hasher,a=t.x64.Word,o=t.algo,d=[],l=[],c=[];!function(){for(var e=1,t=0,r=0;r<24;r++){d[e+5*t]=(r+1)*(r+2)/2%64;var i=(2*e+3*t)%5;e=t%5,t=i}for(e=0;e<5;e++)for(t=0;t<5;t++)l[e+5*t]=t+(2*e+3*t)%5*5;for(var n=1,s=0;s<24;s++){for(var o=0,u=0,h=0;h<7;h++){if(1&n){var f=(1<<h)-1;f<32?u^=1<<f:o^=1<<f-32}128&n?n=n<<1^113:n<<=1}c[s]=a.create(o,u)}}();var u=[];!function(){for(var e=0;e<25;e++)u[e]=a.create()}();var h=o.SHA3=s.extend({cfg:s.cfg.extend({outputLength:512}),_doReset:function(){for(var e=this._state=[],t=0;t<25;t++)e[t]=new a.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(e,t){for(var r=this._state,i=this.blockSize/2,n=0;n<i;n++){var s=e[t+2*n],a=e[t+2*n+1];s=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),a=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),(T=r[n]).high^=a,T.low^=s}for(var o=0;o<24;o++){for(var h=0;h<5;h++){for(var f=0,p=0,_=0;_<5;_++)f^=(T=r[h+5*_]).high,p^=T.low;var m=u[h];m.high=f,m.low=p}for(h=0;h<5;h++){var g=u[(h+4)%5],y=u[(h+1)%5],v=y.high,b=y.low;for(f=g.high^(v<<1|b>>>31),p=g.low^(b<<1|v>>>31),_=0;_<5;_++)(T=r[h+5*_]).high^=f,T.low^=p}for(var w=1;w<25;w++){var S=(T=r[w]).high,E=T.low,A=d[w];A<32?(f=S<<A|E>>>32-A,p=E<<A|S>>>32-A):(f=E<<A-32|S>>>64-A,p=S<<A-32|E>>>64-A);var B=u[l[w]];B.high=f,B.low=p}var x=u[0],U=r[0];for(x.high=U.high,x.low=U.low,h=0;h<5;h++)for(_=0;_<5;_++){var T=r[w=h+5*_],k=u[w],C=u[(h+1)%5+5*_],D=u[(h+2)%5+5*_];T.high=k.high^~C.high&D.high,T.low=k.low^~C.low&D.low}T=r[0];var I=c[o];T.high^=I.high,T.low^=I.low}},_doFinalize:function(){var t=this._data,r=t.words;this._nDataBytes;var i=8*t.sigBytes,s=32*this.blockSize;r[i>>>5]|=1<<24-i%32,r[(e.ceil((i+1)/s)*s>>>5)-1]|=128,t.sigBytes=4*r.length,this._process();for(var a=this._state,o=this.cfg.outputLength/8,d=o/8,l=[],c=0;c<d;c++){var u=a[c],h=u.high,f=u.low;h=16711935&(h<<8|h>>>24)|4278255360&(h<<24|h>>>8),f=16711935&(f<<8|f>>>24)|4278255360&(f<<24|f>>>8),l.push(f),l.push(h)}return new n.init(l,o)},clone:function(){for(var e=s.clone.call(this),t=e._state=this._state.slice(0),r=0;r<25;r++)t[r]=t[r].clone();return e}});t.SHA3=s._createHelper(h),t.HmacSHA3=s._createHmacHelper(h)}(Math),r.SHA3)})),Nt((function(e,t){var r;e.exports=(r=Li,function(e){var t=r,i=t.lib,n=i.WordArray,s=i.Hasher,a=t.algo,o=n.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),d=n.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),l=n.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),c=n.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),u=n.create([0,1518500249,1859775393,2400959708,2840853838]),h=n.create([1352829926,1548603684,1836072691,2053994217,0]),f=a.RIPEMD160=s.extend({_doReset:function(){this._hash=n.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var i=t+r,n=e[i];e[i]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8)}var s,a,f,b,w,S,E,A,B,x,U,T=this._hash.words,k=u.words,C=h.words,D=o.words,I=d.words,F=l.words,P=c.words;for(S=s=T[0],E=a=T[1],A=f=T[2],B=b=T[3],x=w=T[4],r=0;r<80;r+=1)U=s+e[t+D[r]]|0,U+=r<16?p(a,f,b)+k[0]:r<32?_(a,f,b)+k[1]:r<48?m(a,f,b)+k[2]:r<64?g(a,f,b)+k[3]:y(a,f,b)+k[4],U=(U=v(U|=0,F[r]))+w|0,s=w,w=b,b=v(f,10),f=a,a=U,U=S+e[t+I[r]]|0,U+=r<16?y(E,A,B)+C[0]:r<32?g(E,A,B)+C[1]:r<48?m(E,A,B)+C[2]:r<64?_(E,A,B)+C[3]:p(E,A,B)+C[4],U=(U=v(U|=0,P[r]))+x|0,S=x,x=B,B=v(A,10),A=E,E=U;U=T[1]+f+B|0,T[1]=T[2]+b+x|0,T[2]=T[3]+w+S|0,T[3]=T[4]+s+E|0,T[4]=T[0]+a+A|0,T[0]=U},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,i=8*e.sigBytes;t[i>>>5]|=128<<24-i%32,t[14+(i+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),e.sigBytes=4*(t.length+1),this._process();for(var n=this._hash,s=n.words,a=0;a<5;a++){var o=s[a];s[a]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}return n},clone:function(){var e=s.clone.call(this);return e._hash=this._hash.clone(),e}});function p(e,t,r){return e^t^r}function _(e,t,r){return e&t|~e&r}function m(e,t,r){return(e|~t)^r}function g(e,t,r){return e&r|t&~r}function y(e,t,r){return e^(t|~r)}function v(e,t){return e<<t|e>>>32-t}t.RIPEMD160=s._createHelper(f),t.HmacRIPEMD160=s._createHmacHelper(f)}(),r.RIPEMD160)})),Nt((function(e,t){var r,i,n;e.exports=(i=(r=Li).lib.Base,n=r.enc.Utf8,void(r.algo.HMAC=i.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=n.parse(t));var r=e.blockSize,i=4*r;t.sigBytes>i&&(t=e.finalize(t)),t.clamp();for(var s=this._oKey=t.clone(),a=this._iKey=t.clone(),o=s.words,d=a.words,l=0;l<r;l++)o[l]^=1549556828,d[l]^=909522486;s.sigBytes=a.sigBytes=i,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}})))})),Nt((function(e,t){var r,i,n,s,a,o,d,l,c;e.exports=(i=(r=c=Li).lib,n=i.Base,s=i.WordArray,a=r.algo,o=a.SHA256,d=a.HMAC,l=a.PBKDF2=n.extend({cfg:n.extend({keySize:4,hasher:o,iterations:25e4}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r=this.cfg,i=d.create(r.hasher,e),n=s.create(),a=s.create([1]),o=n.words,l=a.words,c=r.keySize,u=r.iterations;o.length<c;){var h=i.update(t).finalize(a);i.reset();for(var f=h.words,p=f.length,_=h,m=1;m<u;m++){_=i.finalize(_),i.reset();for(var g=_.words,y=0;y<p;y++)f[y]^=g[y]}n.concat(h),l[0]++}return n.sigBytes=4*c,n}}),r.PBKDF2=function(e,t,r){return l.create(r).compute(e,t)},c.PBKDF2)})),Nt((function(e,t){var r,i,n,s,a,o,d,l;e.exports=(i=(r=l=Li).lib,n=i.Base,s=i.WordArray,a=r.algo,o=a.MD5,d=a.EvpKDF=n.extend({cfg:n.extend({keySize:4,hasher:o,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r,i=this.cfg,n=i.hasher.create(),a=s.create(),o=a.words,d=i.keySize,l=i.iterations;o.length<d;){r&&n.update(r),r=n.update(e).finalize(t),n.reset();for(var c=1;c<l;c++)r=n.finalize(r),n.reset();a.concat(r)}return a.sigBytes=4*d,a}}),r.EvpKDF=function(e,t,r){return d.create(r).compute(e,t)},l.EvpKDF)})),Nt((function(e,t){var r;e.exports=void((r=Li).lib.Cipher||function(e){var t=r,i=t.lib,n=i.Base,s=i.WordArray,a=i.BufferedBlockAlgorithm,o=t.enc;o.Utf8;var d=o.Base64,l=t.algo.EvpKDF,c=i.Cipher=a.extend({cfg:n.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,r){this.cfg=this.cfg.extend(r),this._xformMode=e,this._key=t,this.reset()},reset:function(){a.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function e(e){return"string"==typeof e?v:g}return function(t){return{encrypt:function(r,i,n){return e(i).encrypt(t,r,i,n)},decrypt:function(r,i,n){return e(i).decrypt(t,r,i,n)}}}}()});i.StreamCipher=c.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var u=t.mode={},h=i.BlockCipherMode=n.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),f=u.CBC=function(){var t=h.extend();function r(t,r,i){var n,s=this._iv;s?(n=s,this._iv=e):n=this._prevBlock;for(var a=0;a<i;a++)t[r+a]^=n[a]}return t.Encryptor=t.extend({processBlock:function(e,t){var i=this._cipher,n=i.blockSize;r.call(this,e,t,n),i.encryptBlock(e,t),this._prevBlock=e.slice(t,t+n)}}),t.Decryptor=t.extend({processBlock:function(e,t){var i=this._cipher,n=i.blockSize,s=e.slice(t,t+n);i.decryptBlock(e,t),r.call(this,e,t,n),this._prevBlock=s}}),t}(),p=(t.pad={}).Pkcs7={pad:function(e,t){for(var r=4*t,i=r-e.sigBytes%r,n=i<<24|i<<16|i<<8|i,a=[],o=0;o<i;o+=4)a.push(n);var d=s.create(a,i);e.concat(d)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}};i.BlockCipher=c.extend({cfg:c.cfg.extend({mode:f,padding:p}),reset:function(){var e;c.reset.call(this);var t=this.cfg,r=t.iv,i=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=i.createEncryptor:(e=i.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,r&&r.words):(this._mode=e.call(i,this,r&&r.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4});var _=i.CipherParams=n.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),m=(t.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext,r=e.salt;return(r?s.create([1398893684,1701076831]).concat(r).concat(t):t).toString(d)},parse:function(e){var t,r=d.parse(e),i=r.words;return 1398893684==i[0]&&1701076831==i[1]&&(t=s.create(i.slice(2,4)),i.splice(0,4),r.sigBytes-=16),_.create({ciphertext:r,salt:t})}},g=i.SerializableCipher=n.extend({cfg:n.extend({format:m}),encrypt:function(e,t,r,i){i=this.cfg.extend(i);var n=e.createEncryptor(r,i),s=n.finalize(t),a=n.cfg;return _.create({ciphertext:s,key:r,iv:a.iv,algorithm:e,mode:a.mode,padding:a.padding,blockSize:e.blockSize,formatter:i.format})},decrypt:function(e,t,r,i){return i=this.cfg.extend(i),t=this._parse(t,i.format),e.createDecryptor(r,i).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),y=(t.kdf={}).OpenSSL={execute:function(e,t,r,i,n){if(i||(i=s.random(8)),n)a=l.create({keySize:t+r,hasher:n}).compute(e,i);else var a=l.create({keySize:t+r}).compute(e,i);var o=s.create(a.words.slice(t),4*r);return a.sigBytes=4*t,_.create({key:a,iv:o,salt:i})}},v=i.PasswordBasedCipher=g.extend({cfg:g.cfg.extend({kdf:y}),encrypt:function(e,t,r,i){var n=(i=this.cfg.extend(i)).kdf.execute(r,e.keySize,e.ivSize,i.salt,i.hasher);i.iv=n.iv;var s=g.encrypt.call(this,e,t,n.key,i);return s.mixIn(n),s},decrypt:function(e,t,r,i){i=this.cfg.extend(i),t=this._parse(t,i.format);var n=i.kdf.execute(r,e.keySize,e.ivSize,t.salt,i.hasher);return i.iv=n.iv,g.decrypt.call(this,e,t,n.key,i)}})}())})),Nt((function(e,t){var r;e.exports=((r=Li).mode.CFB=function(){var e=r.lib.BlockCipherMode.extend();function t(e,t,r,i){var n,s=this._iv;s?(n=s.slice(0),this._iv=void 0):n=this._prevBlock,i.encryptBlock(n,0);for(var a=0;a<r;a++)e[t+a]^=n[a]}return e.Encryptor=e.extend({processBlock:function(e,r){var i=this._cipher,n=i.blockSize;t.call(this,e,r,n,i),this._prevBlock=e.slice(r,r+n)}}),e.Decryptor=e.extend({processBlock:function(e,r){var i=this._cipher,n=i.blockSize,s=e.slice(r,r+n);t.call(this,e,r,n,i),this._prevBlock=s}}),e}(),r.mode.CFB)})),Nt((function(e,t){var r,i,n;e.exports=((n=Li).mode.CTR=(r=n.lib.BlockCipherMode.extend(),i=r.Encryptor=r.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize,n=this._iv,s=this._counter;n&&(s=this._counter=n.slice(0),this._iv=void 0);var a=s.slice(0);r.encryptBlock(a,0),s[i-1]=s[i-1]+1|0;for(var o=0;o<i;o++)e[t+o]^=a[o]}}),r.Decryptor=i,r),n.mode.CTR)})),Nt((function(e,t){var r;e.exports=((r=Li).mode.CTRGladman=function(){var e=r.lib.BlockCipherMode.extend();function t(e){if(255==(e>>24&255)){var t=e>>16&255,r=e>>8&255,i=255&e;255===t?(t=0,255===r?(r=0,255===i?i=0:++i):++r):++t,e=0,e+=t<<16,e+=r<<8,e+=i}else e+=1<<24;return e}function i(e){return 0===(e[0]=t(e[0]))&&(e[1]=t(e[1])),e}var n=e.Encryptor=e.extend({processBlock:function(e,t){var r=this._cipher,n=r.blockSize,s=this._iv,a=this._counter;s&&(a=this._counter=s.slice(0),this._iv=void 0),i(a);var o=a.slice(0);r.encryptBlock(o,0);for(var d=0;d<n;d++)e[t+d]^=o[d]}});return e.Decryptor=n,e}(),r.mode.CTRGladman)})),Nt((function(e,t){var r,i,n;e.exports=((n=Li).mode.OFB=(r=n.lib.BlockCipherMode.extend(),i=r.Encryptor=r.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize,n=this._iv,s=this._keystream;n&&(s=this._keystream=n.slice(0),this._iv=void 0),r.encryptBlock(s,0);for(var a=0;a<i;a++)e[t+a]^=s[a]}}),r.Decryptor=i,r),n.mode.OFB)})),Nt((function(e,t){var r,i;e.exports=((i=Li).mode.ECB=((r=i.lib.BlockCipherMode.extend()).Encryptor=r.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),r.Decryptor=r.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),r),i.mode.ECB)})),Nt((function(e,t){var r;e.exports=((r=Li).pad.AnsiX923={pad:function(e,t){var r=e.sigBytes,i=4*t,n=i-r%i,s=r+n-1;e.clamp(),e.words[s>>>2]|=n<<24-s%4*8,e.sigBytes+=n},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},r.pad.Ansix923)})),Nt((function(e,t){var r;e.exports=((r=Li).pad.Iso10126={pad:function(e,t){var i=4*t,n=i-e.sigBytes%i;e.concat(r.lib.WordArray.random(n-1)).concat(r.lib.WordArray.create([n<<24],1))},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},r.pad.Iso10126)})),Nt((function(e,t){var r;e.exports=((r=Li).pad.Iso97971={pad:function(e,t){e.concat(r.lib.WordArray.create([2147483648],1)),r.pad.ZeroPadding.pad(e,t)},unpad:function(e){r.pad.ZeroPadding.unpad(e),e.sigBytes--}},r.pad.Iso97971)})),Nt((function(e,t){var r;e.exports=((r=Li).pad.ZeroPadding={pad:function(e,t){var r=4*t;e.clamp(),e.sigBytes+=r-(e.sigBytes%r||r)},unpad:function(e){var t=e.words,r=e.sigBytes-1;for(r=e.sigBytes-1;r>=0;r--)if(t[r>>>2]>>>24-r%4*8&255){e.sigBytes=r+1;break}}},r.pad.ZeroPadding)})),Nt((function(e,t){var r;e.exports=((r=Li).pad.NoPadding={pad:function(){},unpad:function(){}},r.pad.NoPadding)})),Nt((function(e,t){var r;e.exports=(r=Li,function(e){var t=r,i=t.lib.CipherParams,n=t.enc.Hex;t.format.Hex={stringify:function(e){return e.ciphertext.toString(n)},parse:function(e){var t=n.parse(e);return i.create({ciphertext:t})}}}(),r.format.Hex)})),Nt((function(e,t){var r;e.exports=(r=Li,function(){var e=r,t=e.lib.BlockCipher,i=e.algo,n=[],s=[],a=[],o=[],d=[],l=[],c=[],u=[],h=[],f=[];!function(){for(var e=[],t=0;t<256;t++)e[t]=t<128?t<<1:t<<1^283;var r=0,i=0;for(t=0;t<256;t++){var p=i^i<<1^i<<2^i<<3^i<<4;p=p>>>8^255&p^99,n[r]=p,s[p]=r;var _=e[r],m=e[_],g=e[m],y=257*e[p]^16843008*p;a[r]=y<<24|y>>>8,o[r]=y<<16|y>>>16,d[r]=y<<8|y>>>24,l[r]=y,y=16843009*g^65537*m^257*_^16843008*r,c[p]=y<<24|y>>>8,u[p]=y<<16|y>>>16,h[p]=y<<8|y>>>24,f[p]=y,r?(r=_^e[e[e[g^_]]],i^=e[e[i]]):r=i=1}}();var p=[0,1,2,4,8,16,32,64,128,27,54],_=i.AES=t.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e=this._keyPriorReset=this._key,t=e.words,r=e.sigBytes/4,i=4*((this._nRounds=r+6)+1),s=this._keySchedule=[],a=0;a<i;a++)a<r?s[a]=t[a]:(l=s[a-1],a%r?r>6&&a%r==4&&(l=n[l>>>24]<<24|n[l>>>16&255]<<16|n[l>>>8&255]<<8|n[255&l]):(l=n[(l=l<<8|l>>>24)>>>24]<<24|n[l>>>16&255]<<16|n[l>>>8&255]<<8|n[255&l],l^=p[a/r|0]<<24),s[a]=s[a-r]^l);for(var o=this._invKeySchedule=[],d=0;d<i;d++){if(a=i-d,d%4)var l=s[a];else l=s[a-4];o[d]=d<4||a<=4?l:c[n[l>>>24]]^u[n[l>>>16&255]]^h[n[l>>>8&255]]^f[n[255&l]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,a,o,d,l,n)},decryptBlock:function(e,t){var r=e[t+1];e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,c,u,h,f,s),r=e[t+1],e[t+1]=e[t+3],e[t+3]=r},_doCryptBlock:function(e,t,r,i,n,s,a,o){for(var d=this._nRounds,l=e[t]^r[0],c=e[t+1]^r[1],u=e[t+2]^r[2],h=e[t+3]^r[3],f=4,p=1;p<d;p++){var _=i[l>>>24]^n[c>>>16&255]^s[u>>>8&255]^a[255&h]^r[f++],m=i[c>>>24]^n[u>>>16&255]^s[h>>>8&255]^a[255&l]^r[f++],g=i[u>>>24]^n[h>>>16&255]^s[l>>>8&255]^a[255&c]^r[f++],y=i[h>>>24]^n[l>>>16&255]^s[c>>>8&255]^a[255&u]^r[f++];l=_,c=m,u=g,h=y}_=(o[l>>>24]<<24|o[c>>>16&255]<<16|o[u>>>8&255]<<8|o[255&h])^r[f++],m=(o[c>>>24]<<24|o[u>>>16&255]<<16|o[h>>>8&255]<<8|o[255&l])^r[f++],g=(o[u>>>24]<<24|o[h>>>16&255]<<16|o[l>>>8&255]<<8|o[255&c])^r[f++],y=(o[h>>>24]<<24|o[l>>>16&255]<<16|o[c>>>8&255]<<8|o[255&u])^r[f++],e[t]=_,e[t+1]=m,e[t+2]=g,e[t+3]=y},keySize:8});e.AES=t._createHelper(_)}(),r.AES)})),Nt((function(e,t){var r;e.exports=(r=Li,function(){var e=r,t=e.lib,i=t.WordArray,n=t.BlockCipher,s=e.algo,a=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],o=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],d=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],l=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],c=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],u=s.DES=n.extend({_doReset:function(){for(var e=this._key.words,t=[],r=0;r<56;r++){var i=a[r]-1;t[r]=e[i>>>5]>>>31-i%32&1}for(var n=this._subKeys=[],s=0;s<16;s++){var l=n[s]=[],c=d[s];for(r=0;r<24;r++)l[r/6|0]|=t[(o[r]-1+c)%28]<<31-r%6,l[4+(r/6|0)]|=t[28+(o[r+24]-1+c)%28]<<31-r%6;for(l[0]=l[0]<<1|l[0]>>>31,r=1;r<7;r++)l[r]=l[r]>>>4*(r-1)+3;l[7]=l[7]<<5|l[7]>>>27}var u=this._invSubKeys=[];for(r=0;r<16;r++)u[r]=n[15-r]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._subKeys)},decryptBlock:function(e,t){this._doCryptBlock(e,t,this._invSubKeys)},_doCryptBlock:function(e,t,r){this._lBlock=e[t],this._rBlock=e[t+1],h.call(this,4,252645135),h.call(this,16,65535),f.call(this,2,858993459),f.call(this,8,16711935),h.call(this,1,1431655765);for(var i=0;i<16;i++){for(var n=r[i],s=this._lBlock,a=this._rBlock,o=0,d=0;d<8;d++)o|=l[d][((a^n[d])&c[d])>>>0];this._lBlock=a,this._rBlock=s^o}var u=this._lBlock;this._lBlock=this._rBlock,this._rBlock=u,h.call(this,1,1431655765),f.call(this,8,16711935),f.call(this,2,858993459),h.call(this,16,65535),h.call(this,4,252645135),e[t]=this._lBlock,e[t+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function h(e,t){var r=(this._lBlock>>>e^this._rBlock)&t;this._rBlock^=r,this._lBlock^=r<<e}function f(e,t){var r=(this._rBlock>>>e^this._lBlock)&t;this._lBlock^=r,this._rBlock^=r<<e}e.DES=n._createHelper(u);var p=s.TripleDES=n.extend({_doReset:function(){var e=this._key.words;if(2!==e.length&&4!==e.length&&e.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var t=e.slice(0,2),r=e.length<4?e.slice(0,2):e.slice(2,4),n=e.length<6?e.slice(0,2):e.slice(4,6);this._des1=u.createEncryptor(i.create(t)),this._des2=u.createEncryptor(i.create(r)),this._des3=u.createEncryptor(i.create(n))},encryptBlock:function(e,t){this._des1.encryptBlock(e,t),this._des2.decryptBlock(e,t),this._des3.encryptBlock(e,t)},decryptBlock:function(e,t){this._des3.decryptBlock(e,t),this._des2.encryptBlock(e,t),this._des1.decryptBlock(e,t)},keySize:6,ivSize:2,blockSize:2});e.TripleDES=n._createHelper(p)}(),r.TripleDES)})),Nt((function(e,t){var r;e.exports=(r=Li,function(){var e=r,t=e.lib.StreamCipher,i=e.algo,n=i.RC4=t.extend({_doReset:function(){for(var e=this._key,t=e.words,r=e.sigBytes,i=this._S=[],n=0;n<256;n++)i[n]=n;n=0;for(var s=0;n<256;n++){var a=n%r,o=t[a>>>2]>>>24-a%4*8&255;s=(s+i[n]+o)%256;var d=i[n];i[n]=i[s],i[s]=d}this._i=this._j=0},_doProcessBlock:function(e,t){e[t]^=s.call(this)},keySize:8,ivSize:0});function s(){for(var e=this._S,t=this._i,r=this._j,i=0,n=0;n<4;n++){r=(r+e[t=(t+1)%256])%256;var s=e[t];e[t]=e[r],e[r]=s,i|=e[(e[t]+e[r])%256]<<24-8*n}return this._i=t,this._j=r,i}e.RC4=t._createHelper(n);var a=i.RC4Drop=n.extend({cfg:n.cfg.extend({drop:192}),_doReset:function(){n._doReset.call(this);for(var e=this.cfg.drop;e>0;e--)s.call(this)}});e.RC4Drop=t._createHelper(a)}(),r.RC4)})),Nt((function(e,t){var r;e.exports=(r=Li,function(){var e=r,t=e.lib.StreamCipher,i=e.algo,n=[],s=[],a=[],o=i.Rabbit=t.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,r=0;r<4;r++)e[r]=16711935&(e[r]<<8|e[r]>>>24)|4278255360&(e[r]<<24|e[r]>>>8);var i=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],n=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];for(this._b=0,r=0;r<4;r++)d.call(this);for(r=0;r<8;r++)n[r]^=i[r+4&7];if(t){var s=t.words,a=s[0],o=s[1],l=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),c=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),u=l>>>16|4294901760&c,h=c<<16|65535&l;for(n[0]^=l,n[1]^=u,n[2]^=c,n[3]^=h,n[4]^=l,n[5]^=u,n[6]^=c,n[7]^=h,r=0;r<4;r++)d.call(this)}},_doProcessBlock:function(e,t){var r=this._X;d.call(this),n[0]=r[0]^r[5]>>>16^r[3]<<16,n[1]=r[2]^r[7]>>>16^r[5]<<16,n[2]=r[4]^r[1]>>>16^r[7]<<16,n[3]=r[6]^r[3]>>>16^r[1]<<16;for(var i=0;i<4;i++)n[i]=16711935&(n[i]<<8|n[i]>>>24)|4278255360&(n[i]<<24|n[i]>>>8),e[t+i]^=n[i]},blockSize:4,ivSize:2});function d(){for(var e=this._X,t=this._C,r=0;r<8;r++)s[r]=t[r];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<s[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<s[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<s[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<s[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<s[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<s[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<s[6]>>>0?1:0)|0,this._b=t[7]>>>0<s[7]>>>0?1:0,r=0;r<8;r++){var i=e[r]+t[r],n=65535&i,o=i>>>16,d=((n*n>>>17)+n*o>>>15)+o*o,l=((4294901760&i)*i|0)+((65535&i)*i|0);a[r]=d^l}e[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,e[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,e[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,e[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,e[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,e[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,e[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,e[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}e.Rabbit=t._createHelper(o)}(),r.Rabbit)})),Nt((function(e,t){var r;e.exports=(r=Li,function(){var e=r,t=e.lib.StreamCipher,i=e.algo,n=[],s=[],a=[],o=i.RabbitLegacy=t.extend({_doReset:function(){var e=this._key.words,t=this.cfg.iv,r=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],i=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];this._b=0;for(var n=0;n<4;n++)d.call(this);for(n=0;n<8;n++)i[n]^=r[n+4&7];if(t){var s=t.words,a=s[0],o=s[1],l=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),c=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),u=l>>>16|4294901760&c,h=c<<16|65535&l;for(i[0]^=l,i[1]^=u,i[2]^=c,i[3]^=h,i[4]^=l,i[5]^=u,i[6]^=c,i[7]^=h,n=0;n<4;n++)d.call(this)}},_doProcessBlock:function(e,t){var r=this._X;d.call(this),n[0]=r[0]^r[5]>>>16^r[3]<<16,n[1]=r[2]^r[7]>>>16^r[5]<<16,n[2]=r[4]^r[1]>>>16^r[7]<<16,n[3]=r[6]^r[3]>>>16^r[1]<<16;for(var i=0;i<4;i++)n[i]=16711935&(n[i]<<8|n[i]>>>24)|4278255360&(n[i]<<24|n[i]>>>8),e[t+i]^=n[i]},blockSize:4,ivSize:2});function d(){for(var e=this._X,t=this._C,r=0;r<8;r++)s[r]=t[r];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<s[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<s[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<s[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<s[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<s[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<s[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<s[6]>>>0?1:0)|0,this._b=t[7]>>>0<s[7]>>>0?1:0,r=0;r<8;r++){var i=e[r]+t[r],n=65535&i,o=i>>>16,d=((n*n>>>17)+n*o>>>15)+o*o,l=((4294901760&i)*i|0)+((65535&i)*i|0);a[r]=d^l}e[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,e[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,e[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,e[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,e[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,e[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,e[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,e[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}e.RabbitLegacy=t._createHelper(o)}(),r.RabbitLegacy)})),Nt((function(e,t){var r;e.exports=(r=Li,function(){var e=r,t=e.lib.BlockCipher,i=e.algo;const n=16,s=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731],a=[[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946],[1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055],[3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504],[976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462]];var o={pbox:[],sbox:[]};function d(e,t){let r=t>>24&255,i=t>>16&255,n=t>>8&255,s=255&t,a=e.sbox[0][r]+e.sbox[1][i];return a^=e.sbox[2][n],a+=e.sbox[3][s],a}function l(e,t,r){let i,s=t,a=r;for(let t=0;t<n;++t)s^=e.pbox[t],a=d(e,s)^a,i=s,s=a,a=i;return i=s,s=a,a=i,a^=e.pbox[n],s^=e.pbox[n+1],{left:s,right:a}}function c(e,t,r){let i,s=t,a=r;for(let t=n+1;t>1;--t)s^=e.pbox[t],a=d(e,s)^a,i=s,s=a,a=i;return i=s,s=a,a=i,a^=e.pbox[1],s^=e.pbox[0],{left:s,right:a}}function u(e,t,r){for(let t=0;t<4;t++){e.sbox[t]=[];for(let r=0;r<256;r++)e.sbox[t][r]=a[t][r]}let i=0;for(let a=0;a<n+2;a++)e.pbox[a]=s[a]^t[i],i++,i>=r&&(i=0);let o=0,d=0,c=0;for(let t=0;t<n+2;t+=2)c=l(e,o,d),o=c.left,d=c.right,e.pbox[t]=o,e.pbox[t+1]=d;for(let t=0;t<4;t++)for(let r=0;r<256;r+=2)c=l(e,o,d),o=c.left,d=c.right,e.sbox[t][r]=o,e.sbox[t][r+1]=d;return!0}var h=i.Blowfish=t.extend({_doReset:function(){if(this._keyPriorReset!==this._key){var e=this._keyPriorReset=this._key,t=e.words,r=e.sigBytes/4;u(o,t,r)}},encryptBlock:function(e,t){var r=l(o,e[t],e[t+1]);e[t]=r.left,e[t+1]=r.right},decryptBlock:function(e,t){var r=c(o,e[t],e[t+1]);e[t]=r.left,e[t+1]=r.right},blockSize:2,keySize:4,ivSize:2});e.Blowfish=t._createHelper(h)}(),r.Blowfish)}));function Mi(e){return e[3]|e[2]<<8|e[1]<<16|e[0]<<24}function Ri(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];t=new Uint8Array(t),r=new Uint8Array(r);const n=e.byteLength;let s=5;for(;s<n;){let a=Mi(e.slice(s,s+4));if(a>n)break;let o=e[s+4],d=!1;if(i?(o=o>>>1&63,d=[0,1,2,3,4,5,6,7,8,9,16,17,18,19,20,21].includes(o)):(o&=31,d=1===o||5===o),d){const i=e.slice(s+4+2,s+4+a);let n=new Pi.ModeOfOperation.ctr(t,new Pi.Counter(r));const o=n.decrypt(i);n=null,e.set(o,s+4+2)}s=s+4+a}return e}function zi(e,t,r){if(e.byteLength<=30)return e;const i=e.slice(32);let n=new Pi.ModeOfOperation.ctr(t,new Pi.Counter(r));const s=n.decrypt(i);return n=null,e.set(s,32),e}Nt((function(e,t){e.exports=Li}));var Ni=Nt((function(e,t){var r,n,s,a=(r=new Date,n=4,s={setLogLevel:function(e){n=e==this.debug?1:e==this.info?2:e==this.warn?3:(this.error,4)},debug:function(e,t){void 0===console.debug&&(console.debug=console.log),1>=n&&console.debug("["+a.getDurationString(new Date-r,1e3)+"]","["+e+"]",t)},log:function(e,t){this.debug(e.msg)},info:function(e,t){2>=n&&console.info("["+a.getDurationString(new Date-r,1e3)+"]","["+e+"]",t)},warn:function(e,t){3>=n&&console.warn("["+a.getDurationString(new Date-r,1e3)+"]","["+e+"]",t)},error:function(e,t){4>=n&&console.error("["+a.getDurationString(new Date-r,1e3)+"]","["+e+"]",t)}},s);a.getDurationString=function(e,t){var r;function i(e,t){for(var r=(""+e).split(".");r[0].length<t;)r[0]="0"+r[0];return r.join(".")}e<0?(r=!0,e=-e):r=!1;var n=e/(t||1),s=Math.floor(n/3600);n-=3600*s;var a=Math.floor(n/60),o=1e3*(n-=60*a);return o-=1e3*(n=Math.floor(n)),o=Math.floor(o),(r?"-":"")+s+":"+i(a,2)+":"+i(n,2)+"."+i(o,3)},a.printRanges=function(e){var t=e.length;if(t>0){for(var r="",i=0;i<t;i++)i>0&&(r+=","),r+="["+a.getDurationString(e.start(i))+","+a.getDurationString(e.end(i))+"]";return r}return"(empty)"},t.Log=a;var o=function(e){if(!(e instanceof ArrayBuffer))throw"Needs an array buffer";this.buffer=e,this.dataview=new DataView(e),this.position=0};o.prototype.getPosition=function(){return this.position},o.prototype.getEndPosition=function(){return this.buffer.byteLength},o.prototype.getLength=function(){return this.buffer.byteLength},o.prototype.seek=function(e){var t=Math.max(0,Math.min(this.buffer.byteLength,e));return this.position=isNaN(t)||!isFinite(t)?0:t,!0},o.prototype.isEos=function(){return this.getPosition()>=this.getEndPosition()},o.prototype.readAnyInt=function(e,t){var r=0;if(this.position+e<=this.buffer.byteLength){switch(e){case 1:r=t?this.dataview.getInt8(this.position):this.dataview.getUint8(this.position);break;case 2:r=t?this.dataview.getInt16(this.position):this.dataview.getUint16(this.position);break;case 3:if(t)throw"No method for reading signed 24 bits values";r=this.dataview.getUint8(this.position)<<16,r|=this.dataview.getUint8(this.position+1)<<8,r|=this.dataview.getUint8(this.position+2);break;case 4:r=t?this.dataview.getInt32(this.position):this.dataview.getUint32(this.position);break;case 8:if(t)throw"No method for reading signed 64 bits values";r=this.dataview.getUint32(this.position)<<32,r|=this.dataview.getUint32(this.position+4);break;default:throw"readInt method not implemented for size: "+e}return this.position+=e,r}throw"Not enough bytes in buffer"},o.prototype.readUint8=function(){return this.readAnyInt(1,!1)},o.prototype.readUint16=function(){return this.readAnyInt(2,!1)},o.prototype.readUint24=function(){return this.readAnyInt(3,!1)},o.prototype.readUint32=function(){return this.readAnyInt(4,!1)},o.prototype.readUint64=function(){return this.readAnyInt(8,!1)},o.prototype.readString=function(e){if(this.position+e<=this.buffer.byteLength){for(var t="",r=0;r<e;r++)t+=String.fromCharCode(this.readUint8());return t}throw"Not enough bytes in buffer"},o.prototype.readCString=function(){for(var e=[];;){var t=this.readUint8();if(0===t)break;e.push(t)}return String.fromCharCode.apply(null,e)},o.prototype.readInt8=function(){return this.readAnyInt(1,!0)},o.prototype.readInt16=function(){return this.readAnyInt(2,!0)},o.prototype.readInt32=function(){return this.readAnyInt(4,!0)},o.prototype.readInt64=function(){return this.readAnyInt(8,!1)},o.prototype.readUint8Array=function(e){for(var t=new Uint8Array(e),r=0;r<e;r++)t[r]=this.readUint8();return t},o.prototype.readInt16Array=function(e){for(var t=new Int16Array(e),r=0;r<e;r++)t[r]=this.readInt16();return t},o.prototype.readUint16Array=function(e){for(var t=new Int16Array(e),r=0;r<e;r++)t[r]=this.readUint16();return t},o.prototype.readUint32Array=function(e){for(var t=new Uint32Array(e),r=0;r<e;r++)t[r]=this.readUint32();return t},o.prototype.readInt32Array=function(e){for(var t=new Int32Array(e),r=0;r<e;r++)t[r]=this.readInt32();return t},t.MP4BoxStream=o;var d=function(e,t,r){this._byteOffset=t||0,e instanceof ArrayBuffer?this.buffer=e:"object"==typeof e?(this.dataView=e,t&&(this._byteOffset+=t)):this.buffer=new ArrayBuffer(e||0),this.position=0,this.endianness=null==r?d.LITTLE_ENDIAN:r};d.prototype={},d.prototype.getPosition=function(){return this.position},d.prototype._realloc=function(e){if(this._dynamicSize){var t=this._byteOffset+this.position+e,r=this._buffer.byteLength;if(t<=r)t>this._byteLength&&(this._byteLength=t);else{for(r<1&&(r=1);t>r;)r*=2;var i=new ArrayBuffer(r),n=new Uint8Array(this._buffer);new Uint8Array(i,0,n.length).set(n),this.buffer=i,this._byteLength=t}}},d.prototype._trimAlloc=function(){if(this._byteLength!=this._buffer.byteLength){var e=new ArrayBuffer(this._byteLength),t=new Uint8Array(e),r=new Uint8Array(this._buffer,0,t.length);t.set(r),this.buffer=e}},d.BIG_ENDIAN=!1,d.LITTLE_ENDIAN=!0,d.prototype._byteLength=0,Object.defineProperty(d.prototype,"byteLength",{get:function(){return this._byteLength-this._byteOffset}}),Object.defineProperty(d.prototype,"buffer",{get:function(){return this._trimAlloc(),this._buffer},set:function(e){this._buffer=e,this._dataView=new DataView(this._buffer,this._byteOffset),this._byteLength=this._buffer.byteLength}}),Object.defineProperty(d.prototype,"byteOffset",{get:function(){return this._byteOffset},set:function(e){this._byteOffset=e,this._dataView=new DataView(this._buffer,this._byteOffset),this._byteLength=this._buffer.byteLength}}),Object.defineProperty(d.prototype,"dataView",{get:function(){return this._dataView},set:function(e){this._byteOffset=e.byteOffset,this._buffer=e.buffer,this._dataView=new DataView(this._buffer,this._byteOffset),this._byteLength=this._byteOffset+e.byteLength}}),d.prototype.seek=function(e){var t=Math.max(0,Math.min(this.byteLength,e));this.position=isNaN(t)||!isFinite(t)?0:t},d.prototype.isEof=function(){return this.position>=this._byteLength},d.prototype.mapUint8Array=function(e){this._realloc(1*e);var t=new Uint8Array(this._buffer,this.byteOffset+this.position,e);return this.position+=1*e,t},d.prototype.readInt32Array=function(e,t){e=null==e?this.byteLength-this.position/4:e;var r=new Int32Array(e);return d.memcpy(r.buffer,0,this.buffer,this.byteOffset+this.position,e*r.BYTES_PER_ELEMENT),d.arrayToNative(r,null==t?this.endianness:t),this.position+=r.byteLength,r},d.prototype.readInt16Array=function(e,t){e=null==e?this.byteLength-this.position/2:e;var r=new Int16Array(e);return d.memcpy(r.buffer,0,this.buffer,this.byteOffset+this.position,e*r.BYTES_PER_ELEMENT),d.arrayToNative(r,null==t?this.endianness:t),this.position+=r.byteLength,r},d.prototype.readInt8Array=function(e){e=null==e?this.byteLength-this.position:e;var t=new Int8Array(e);return d.memcpy(t.buffer,0,this.buffer,this.byteOffset+this.position,e*t.BYTES_PER_ELEMENT),this.position+=t.byteLength,t},d.prototype.readUint32Array=function(e,t){e=null==e?this.byteLength-this.position/4:e;var r=new Uint32Array(e);return d.memcpy(r.buffer,0,this.buffer,this.byteOffset+this.position,e*r.BYTES_PER_ELEMENT),d.arrayToNative(r,null==t?this.endianness:t),this.position+=r.byteLength,r},d.prototype.readUint16Array=function(e,t){e=null==e?this.byteLength-this.position/2:e;var r=new Uint16Array(e);return d.memcpy(r.buffer,0,this.buffer,this.byteOffset+this.position,e*r.BYTES_PER_ELEMENT),d.arrayToNative(r,null==t?this.endianness:t),this.position+=r.byteLength,r},d.prototype.readUint8Array=function(e){e=null==e?this.byteLength-this.position:e;var t=new Uint8Array(e);return d.memcpy(t.buffer,0,this.buffer,this.byteOffset+this.position,e*t.BYTES_PER_ELEMENT),this.position+=t.byteLength,t},d.prototype.readFloat64Array=function(e,t){e=null==e?this.byteLength-this.position/8:e;var r=new Float64Array(e);return d.memcpy(r.buffer,0,this.buffer,this.byteOffset+this.position,e*r.BYTES_PER_ELEMENT),d.arrayToNative(r,null==t?this.endianness:t),this.position+=r.byteLength,r},d.prototype.readFloat32Array=function(e,t){e=null==e?this.byteLength-this.position/4:e;var r=new Float32Array(e);return d.memcpy(r.buffer,0,this.buffer,this.byteOffset+this.position,e*r.BYTES_PER_ELEMENT),d.arrayToNative(r,null==t?this.endianness:t),this.position+=r.byteLength,r},d.prototype.readInt32=function(e){var t=this._dataView.getInt32(this.position,null==e?this.endianness:e);return this.position+=4,t},d.prototype.readInt16=function(e){var t=this._dataView.getInt16(this.position,null==e?this.endianness:e);return this.position+=2,t},d.prototype.readInt8=function(){var e=this._dataView.getInt8(this.position);return this.position+=1,e},d.prototype.readUint32=function(e){var t=this._dataView.getUint32(this.position,null==e?this.endianness:e);return this.position+=4,t},d.prototype.readUint16=function(e){var t=this._dataView.getUint16(this.position,null==e?this.endianness:e);return this.position+=2,t},d.prototype.readUint8=function(){var e=this._dataView.getUint8(this.position);return this.position+=1,e},d.prototype.readFloat32=function(e){var t=this._dataView.getFloat32(this.position,null==e?this.endianness:e);return this.position+=4,t},d.prototype.readFloat64=function(e){var t=this._dataView.getFloat64(this.position,null==e?this.endianness:e);return this.position+=8,t},d.endianness=new Int8Array(new Int16Array([1]).buffer)[0]>0,d.memcpy=function(e,t,r,i,n){var s=new Uint8Array(e,t,n),a=new Uint8Array(r,i,n);s.set(a)},d.arrayToNative=function(e,t){return t==this.endianness?e:this.flipArrayEndianness(e)},d.nativeToEndian=function(e,t){return this.endianness==t?e:this.flipArrayEndianness(e)},d.flipArrayEndianness=function(e){for(var t=new Uint8Array(e.buffer,e.byteOffset,e.byteLength),r=0;r<e.byteLength;r+=e.BYTES_PER_ELEMENT)for(var i=r+e.BYTES_PER_ELEMENT-1,n=r;i>n;i--,n++){var s=t[n];t[n]=t[i],t[i]=s}return e},d.prototype.failurePosition=0,String.fromCharCodeUint8=function(e){for(var t=[],r=0;r<e.length;r++)t[r]=e[r];return String.fromCharCode.apply(null,t)},d.prototype.readString=function(e,t){return null==t||"ASCII"==t?String.fromCharCodeUint8.apply(null,[this.mapUint8Array(null==e?this.byteLength-this.position:e)]):new TextDecoder(t).decode(this.mapUint8Array(e))},d.prototype.readCString=function(e){var t=this.byteLength-this.position,r=new Uint8Array(this._buffer,this._byteOffset+this.position),i=t;null!=e&&(i=Math.min(e,t));for(var n=0;n<i&&0!==r[n];n++);var s=String.fromCharCodeUint8.apply(null,[this.mapUint8Array(n)]);return null!=e?this.position+=i-n:n!=t&&(this.position+=1),s};var l=Math.pow(2,32);d.prototype.readInt64=function(){return this.readInt32()*l+this.readUint32()},d.prototype.readUint64=function(){return this.readUint32()*l+this.readUint32()},d.prototype.readInt64=function(){return this.readUint32()*l+this.readUint32()},d.prototype.readUint24=function(){return(this.readUint8()<<16)+(this.readUint8()<<8)+this.readUint8()},t.DataStream=d,d.prototype.save=function(e){var t=new Blob([this.buffer]);if(!window.URL||!URL.createObjectURL)throw"DataStream.save: Can't create object URL.";var r=window.URL.createObjectURL(t),i=document.createElement("a");document.body.appendChild(i),i.setAttribute("href",r),i.setAttribute("download",e),i.setAttribute("target","_self"),i.click(),window.URL.revokeObjectURL(r)},d.prototype._dynamicSize=!0,Object.defineProperty(d.prototype,"dynamicSize",{get:function(){return this._dynamicSize},set:function(e){e||this._trimAlloc(),this._dynamicSize=e}}),d.prototype.shift=function(e){var t=new ArrayBuffer(this._byteLength-e),r=new Uint8Array(t),i=new Uint8Array(this._buffer,e,r.length);r.set(i),this.buffer=t,this.position-=e},d.prototype.writeInt32Array=function(e,t){if(this._realloc(4*e.length),e instanceof Int32Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapInt32Array(e.length,t);else for(var r=0;r<e.length;r++)this.writeInt32(e[r],t)},d.prototype.writeInt16Array=function(e,t){if(this._realloc(2*e.length),e instanceof Int16Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapInt16Array(e.length,t);else for(var r=0;r<e.length;r++)this.writeInt16(e[r],t)},d.prototype.writeInt8Array=function(e){if(this._realloc(1*e.length),e instanceof Int8Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapInt8Array(e.length);else for(var t=0;t<e.length;t++)this.writeInt8(e[t])},d.prototype.writeUint32Array=function(e,t){if(this._realloc(4*e.length),e instanceof Uint32Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapUint32Array(e.length,t);else for(var r=0;r<e.length;r++)this.writeUint32(e[r],t)},d.prototype.writeUint16Array=function(e,t){if(this._realloc(2*e.length),e instanceof Uint16Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapUint16Array(e.length,t);else for(var r=0;r<e.length;r++)this.writeUint16(e[r],t)},d.prototype.writeUint8Array=function(e){if(this._realloc(1*e.length),e instanceof Uint8Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapUint8Array(e.length);else for(var t=0;t<e.length;t++)this.writeUint8(e[t])},d.prototype.writeFloat64Array=function(e,t){if(this._realloc(8*e.length),e instanceof Float64Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapFloat64Array(e.length,t);else for(var r=0;r<e.length;r++)this.writeFloat64(e[r],t)},d.prototype.writeFloat32Array=function(e,t){if(this._realloc(4*e.length),e instanceof Float32Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapFloat32Array(e.length,t);else for(var r=0;r<e.length;r++)this.writeFloat32(e[r],t)},d.prototype.writeInt32=function(e,t){this._realloc(4),this._dataView.setInt32(this.position,e,null==t?this.endianness:t),this.position+=4},d.prototype.writeInt16=function(e,t){this._realloc(2),this._dataView.setInt16(this.position,e,null==t?this.endianness:t),this.position+=2},d.prototype.writeInt8=function(e){this._realloc(1),this._dataView.setInt8(this.position,e),this.position+=1},d.prototype.writeUint32=function(e,t){this._realloc(4),this._dataView.setUint32(this.position,e,null==t?this.endianness:t),this.position+=4},d.prototype.writeUint16=function(e,t){this._realloc(2),this._dataView.setUint16(this.position,e,null==t?this.endianness:t),this.position+=2},d.prototype.writeUint8=function(e){this._realloc(1),this._dataView.setUint8(this.position,e),this.position+=1},d.prototype.writeFloat32=function(e,t){this._realloc(4),this._dataView.setFloat32(this.position,e,null==t?this.endianness:t),this.position+=4},d.prototype.writeFloat64=function(e,t){this._realloc(8),this._dataView.setFloat64(this.position,e,null==t?this.endianness:t),this.position+=8},d.prototype.writeUCS2String=function(e,t,r){null==r&&(r=e.length);for(var i=0;i<e.length&&i<r;i++)this.writeUint16(e.charCodeAt(i),t);for(;i<r;i++)this.writeUint16(0)},d.prototype.writeString=function(e,t,r){var i=0;if(null==t||"ASCII"==t)if(null!=r){var n=Math.min(e.length,r);for(i=0;i<n;i++)this.writeUint8(e.charCodeAt(i));for(;i<r;i++)this.writeUint8(0)}else for(i=0;i<e.length;i++)this.writeUint8(e.charCodeAt(i));else this.writeUint8Array(new TextEncoder(t).encode(e.substring(0,r)))},d.prototype.writeCString=function(e,t){var r=0;if(null!=t){var i=Math.min(e.length,t);for(r=0;r<i;r++)this.writeUint8(e.charCodeAt(r));for(;r<t;r++)this.writeUint8(0)}else{for(r=0;r<e.length;r++)this.writeUint8(e.charCodeAt(r));this.writeUint8(0)}},d.prototype.writeStruct=function(e,t){for(var r=0;r<e.length;r+=2){var i=e[r+1];this.writeType(i,t[e[r]],t)}},d.prototype.writeType=function(e,t,r){var i;if("function"==typeof e)return e(this,t);if("object"==typeof e&&!(e instanceof Array))return e.set(this,t,r);var n=null,s="ASCII",a=this.position;switch("string"==typeof e&&/:/.test(e)&&(i=e.split(":"),e=i[0],n=parseInt(i[1])),"string"==typeof e&&/,/.test(e)&&(i=e.split(","),e=i[0],s=parseInt(i[1])),e){case"uint8":this.writeUint8(t);break;case"int8":this.writeInt8(t);break;case"uint16":this.writeUint16(t,this.endianness);break;case"int16":this.writeInt16(t,this.endianness);break;case"uint32":this.writeUint32(t,this.endianness);break;case"int32":this.writeInt32(t,this.endianness);break;case"float32":this.writeFloat32(t,this.endianness);break;case"float64":this.writeFloat64(t,this.endianness);break;case"uint16be":this.writeUint16(t,d.BIG_ENDIAN);break;case"int16be":this.writeInt16(t,d.BIG_ENDIAN);break;case"uint32be":this.writeUint32(t,d.BIG_ENDIAN);break;case"int32be":this.writeInt32(t,d.BIG_ENDIAN);break;case"float32be":this.writeFloat32(t,d.BIG_ENDIAN);break;case"float64be":this.writeFloat64(t,d.BIG_ENDIAN);break;case"uint16le":this.writeUint16(t,d.LITTLE_ENDIAN);break;case"int16le":this.writeInt16(t,d.LITTLE_ENDIAN);break;case"uint32le":this.writeUint32(t,d.LITTLE_ENDIAN);break;case"int32le":this.writeInt32(t,d.LITTLE_ENDIAN);break;case"float32le":this.writeFloat32(t,d.LITTLE_ENDIAN);break;case"float64le":this.writeFloat64(t,d.LITTLE_ENDIAN);break;case"cstring":this.writeCString(t,n);break;case"string":this.writeString(t,s,n);break;case"u16string":this.writeUCS2String(t,this.endianness,n);break;case"u16stringle":this.writeUCS2String(t,d.LITTLE_ENDIAN,n);break;case"u16stringbe":this.writeUCS2String(t,d.BIG_ENDIAN,n);break;default:if(3==e.length){for(var o=e[1],l=0;l<t.length;l++)this.writeType(o,t[l]);break}this.writeStruct(e,t)}null!=n&&(this.position=a,this._realloc(n),this.position=a+n)},d.prototype.writeUint64=function(e){var t=Math.floor(e/l);this.writeUint32(t),this.writeUint32(4294967295&e)},d.prototype.writeUint24=function(e){this.writeUint8((16711680&e)>>16),this.writeUint8((65280&e)>>8),this.writeUint8(255&e)},d.prototype.adjustUint32=function(e,t){var r=this.position;this.seek(e),this.writeUint32(t),this.seek(r)},d.prototype.mapInt32Array=function(e,t){this._realloc(4*e);var r=new Int32Array(this._buffer,this.byteOffset+this.position,e);return d.arrayToNative(r,null==t?this.endianness:t),this.position+=4*e,r},d.prototype.mapInt16Array=function(e,t){this._realloc(2*e);var r=new Int16Array(this._buffer,this.byteOffset+this.position,e);return d.arrayToNative(r,null==t?this.endianness:t),this.position+=2*e,r},d.prototype.mapInt8Array=function(e){this._realloc(1*e);var t=new Int8Array(this._buffer,this.byteOffset+this.position,e);return this.position+=1*e,t},d.prototype.mapUint32Array=function(e,t){this._realloc(4*e);var r=new Uint32Array(this._buffer,this.byteOffset+this.position,e);return d.arrayToNative(r,null==t?this.endianness:t),this.position+=4*e,r},d.prototype.mapUint16Array=function(e,t){this._realloc(2*e);var r=new Uint16Array(this._buffer,this.byteOffset+this.position,e);return d.arrayToNative(r,null==t?this.endianness:t),this.position+=2*e,r},d.prototype.mapFloat64Array=function(e,t){this._realloc(8*e);var r=new Float64Array(this._buffer,this.byteOffset+this.position,e);return d.arrayToNative(r,null==t?this.endianness:t),this.position+=8*e,r},d.prototype.mapFloat32Array=function(e,t){this._realloc(4*e);var r=new Float32Array(this._buffer,this.byteOffset+this.position,e);return d.arrayToNative(r,null==t?this.endianness:t),this.position+=4*e,r};var c=function(e){this.buffers=[],this.bufferIndex=-1,e&&(this.insertBuffer(e),this.bufferIndex=0)};(c.prototype=new d(new ArrayBuffer,0,d.BIG_ENDIAN)).initialized=function(){var e;return this.bufferIndex>-1||(this.buffers.length>0?0===(e=this.buffers[0]).fileStart?(this.buffer=e,this.bufferIndex=0,a.debug("MultiBufferStream","Stream ready for parsing"),!0):(a.warn("MultiBufferStream","The first buffer should have a fileStart of 0"),this.logBufferLevel(),!1):(a.warn("MultiBufferStream","No buffer to start parsing from"),this.logBufferLevel(),!1))},ArrayBuffer.concat=function(e,t){a.debug("ArrayBuffer","Trying to create a new buffer of size: "+(e.byteLength+t.byteLength));var r=new Uint8Array(e.byteLength+t.byteLength);return r.set(new Uint8Array(e),0),r.set(new Uint8Array(t),e.byteLength),r.buffer},c.prototype.reduceBuffer=function(e,t,r){var i;return(i=new Uint8Array(r)).set(new Uint8Array(e,t,r)),i.buffer.fileStart=e.fileStart+t,i.buffer.usedBytes=0,i.buffer},c.prototype.insertBuffer=function(e){for(var t=!0,r=0;r<this.buffers.length;r++){var i=this.buffers[r];if(e.fileStart<=i.fileStart){if(e.fileStart===i.fileStart){if(e.byteLength>i.byteLength){this.buffers.splice(r,1),r--;continue}a.warn("MultiBufferStream","Buffer (fileStart: "+e.fileStart+" - Length: "+e.byteLength+") already appended, ignoring")}else e.fileStart+e.byteLength<=i.fileStart||(e=this.reduceBuffer(e,0,i.fileStart-e.fileStart)),a.debug("MultiBufferStream","Appending new buffer (fileStart: "+e.fileStart+" - Length: "+e.byteLength+")"),this.buffers.splice(r,0,e),0===r&&(this.buffer=e);t=!1;break}if(e.fileStart<i.fileStart+i.byteLength){var n=i.fileStart+i.byteLength-e.fileStart,s=e.byteLength-n;if(!(s>0)){t=!1;break}e=this.reduceBuffer(e,n,s)}}t&&(a.debug("MultiBufferStream","Appending new buffer (fileStart: "+e.fileStart+" - Length: "+e.byteLength+")"),this.buffers.push(e),0===r&&(this.buffer=e))},c.prototype.logBufferLevel=function(e){var t,r,i,n,s,o=[],d="";for(i=0,n=0,t=0;t<this.buffers.length;t++)r=this.buffers[t],0===t?(s={},o.push(s),s.start=r.fileStart,s.end=r.fileStart+r.byteLength,d+="["+s.start+"-"):s.end===r.fileStart?s.end=r.fileStart+r.byteLength:((s={}).start=r.fileStart,d+=o[o.length-1].end-1+"], ["+s.start+"-",s.end=r.fileStart+r.byteLength,o.push(s)),i+=r.usedBytes,n+=r.byteLength;o.length>0&&(d+=s.end-1+"]");var l=e?a.info:a.debug;0===this.buffers.length?l("MultiBufferStream","No more buffer in memory"):l("MultiBufferStream",this.buffers.length+" stored buffer(s) ("+i+"/"+n+" bytes), continuous ranges: "+d)},c.prototype.cleanBuffers=function(){var e,t;for(e=0;e<this.buffers.length;e++)(t=this.buffers[e]).usedBytes===t.byteLength&&(a.debug("MultiBufferStream","Removing buffer #"+e),this.buffers.splice(e,1),e--)},c.prototype.mergeNextBuffer=function(){var e;if(this.bufferIndex+1<this.buffers.length){if((e=this.buffers[this.bufferIndex+1]).fileStart===this.buffer.fileStart+this.buffer.byteLength){var t=this.buffer.byteLength,r=this.buffer.usedBytes,i=this.buffer.fileStart;return this.buffers[this.bufferIndex]=ArrayBuffer.concat(this.buffer,e),this.buffer=this.buffers[this.bufferIndex],this.buffers.splice(this.bufferIndex+1,1),this.buffer.usedBytes=r,this.buffer.fileStart=i,a.debug("ISOFile","Concatenating buffer for box parsing (length: "+t+"->"+this.buffer.byteLength+")"),!0}return!1}return!1},c.prototype.findPosition=function(e,t,r){var i,n=null,s=-1;for(i=!0===e?0:this.bufferIndex;i<this.buffers.length&&(n=this.buffers[i]).fileStart<=t;)s=i,r&&(n.fileStart+n.byteLength<=t?n.usedBytes=n.byteLength:n.usedBytes=t-n.fileStart,this.logBufferLevel()),i++;return-1!==s&&(n=this.buffers[s]).fileStart+n.byteLength>=t?(a.debug("MultiBufferStream","Found position in existing buffer #"+s),s):-1},c.prototype.findEndContiguousBuf=function(e){var t,r,i,n=void 0!==e?e:this.bufferIndex;if(r=this.buffers[n],this.buffers.length>n+1)for(t=n+1;t<this.buffers.length&&(i=this.buffers[t]).fileStart===r.fileStart+r.byteLength;t++)r=i;return r.fileStart+r.byteLength},c.prototype.getEndFilePositionAfter=function(e){var t=this.findPosition(!0,e,!1);return-1!==t?this.findEndContiguousBuf(t):e},c.prototype.addUsedBytes=function(e){this.buffer.usedBytes+=e,this.logBufferLevel()},c.prototype.setAllUsedBytes=function(){this.buffer.usedBytes=this.buffer.byteLength,this.logBufferLevel()},c.prototype.seek=function(e,t,r){var i;return-1!==(i=this.findPosition(t,e,r))?(this.buffer=this.buffers[i],this.bufferIndex=i,this.position=e-this.buffer.fileStart,a.debug("MultiBufferStream","Repositioning parser at buffer position: "+this.position),!0):(a.debug("MultiBufferStream","Position "+e+" not found in buffered data"),!1)},c.prototype.getPosition=function(){if(-1===this.bufferIndex||null===this.buffers[this.bufferIndex])throw"Error accessing position in the MultiBufferStream";return this.buffers[this.bufferIndex].fileStart+this.position},c.prototype.getLength=function(){return this.byteLength},c.prototype.getEndPosition=function(){if(-1===this.bufferIndex||null===this.buffers[this.bufferIndex])throw"Error accessing position in the MultiBufferStream";return this.buffers[this.bufferIndex].fileStart+this.byteLength},c.prototype.destroy=function(){this.buffers=[],this.bufferIndex},t.MultiBufferStream=c;var u=function(){var e=[];e[3]="ES_Descriptor",e[4]="DecoderConfigDescriptor",e[5]="DecoderSpecificInfo",e[6]="SLConfigDescriptor",this.getDescriptorName=function(t){return e[t]};var t=this,r={};return this.parseOneDescriptor=function(t){var i,n,s,o=0;for(i=t.readUint8(),s=t.readUint8();128&s;)o=(127&s)<<7,s=t.readUint8();return o+=127&s,a.debug("MPEG4DescriptorParser","Found "+(e[i]||"Descriptor "+i)+", size "+o+" at position "+t.getPosition()),(n=e[i]?new r[e[i]](o):new r.Descriptor(o)).parse(t),n},r.Descriptor=function(e,t){this.tag=e,this.size=t,this.descs=[]},r.Descriptor.prototype.parse=function(e){this.data=e.readUint8Array(this.size)},r.Descriptor.prototype.findDescriptor=function(e){for(var t=0;t<this.descs.length;t++)if(this.descs[t].tag==e)return this.descs[t];return null},r.Descriptor.prototype.parseRemainingDescriptors=function(e){for(var r=e.position;e.position<r+this.size;){var i=t.parseOneDescriptor(e);this.descs.push(i)}},r.ES_Descriptor=function(e){r.Descriptor.call(this,3,e)},r.ES_Descriptor.prototype=new r.Descriptor,r.ES_Descriptor.prototype.parse=function(e){if(this.ES_ID=e.readUint16(),this.flags=e.readUint8(),this.size-=3,128&this.flags?(this.dependsOn_ES_ID=e.readUint16(),this.size-=2):this.dependsOn_ES_ID=0,64&this.flags){var t=e.readUint8();this.URL=e.readString(t),this.size-=t+1}else this.URL="";32&this.flags?(this.OCR_ES_ID=e.readUint16(),this.size-=2):this.OCR_ES_ID=0,this.parseRemainingDescriptors(e)},r.ES_Descriptor.prototype.getOTI=function(e){var t=this.findDescriptor(4);return t?t.oti:0},r.ES_Descriptor.prototype.getAudioConfig=function(e){var t=this.findDescriptor(4);if(!t)return null;var r=t.findDescriptor(5);if(r&&r.data){var i=(248&r.data[0])>>3;return 31===i&&r.data.length>=2&&(i=32+((7&r.data[0])<<3)+((224&r.data[1])>>5)),i}return null},r.DecoderConfigDescriptor=function(e){r.Descriptor.call(this,4,e)},r.DecoderConfigDescriptor.prototype=new r.Descriptor,r.DecoderConfigDescriptor.prototype.parse=function(e){this.oti=e.readUint8(),this.streamType=e.readUint8(),this.upStream=0!=(this.streamType>>1&1),this.streamType=this.streamType>>>2,this.bufferSize=e.readUint24(),this.maxBitrate=e.readUint32(),this.avgBitrate=e.readUint32(),this.size-=13,this.parseRemainingDescriptors(e)},r.DecoderSpecificInfo=function(e){r.Descriptor.call(this,5,e)},r.DecoderSpecificInfo.prototype=new r.Descriptor,r.SLConfigDescriptor=function(e){r.Descriptor.call(this,6,e)},r.SLConfigDescriptor.prototype=new r.Descriptor,this};t.MPEG4DescriptorParser=u;var h={ERR_INVALID_DATA:-1,ERR_NOT_ENOUGH_DATA:0,OK:1,BASIC_BOXES:["mdat","idat","free","skip","meco","strk"],FULL_BOXES:["hmhd","nmhd","iods","xml ","bxml","ipro","mere"],CONTAINER_BOXES:[["moov",["trak","pssh"]],["trak"],["edts"],["mdia"],["minf"],["dinf"],["stbl",["sgpd","sbgp"]],["mvex",["trex"]],["moof",["traf"]],["traf",["trun","sgpd","sbgp"]],["vttc"],["tref"],["iref"],["mfra",["tfra"]],["meco"],["hnti"],["hinf"],["strk"],["strd"],["sinf"],["rinf"],["schi"],["trgr"],["udta",["kind"]],["iprp",["ipma"]],["ipco"],["grpl"],["j2kH"],["etyp",["tyco"]]],boxCodes:[],fullBoxCodes:[],containerBoxCodes:[],sampleEntryCodes:{},sampleGroupEntryCodes:[],trackGroupTypes:[],UUIDBoxes:{},UUIDs:[],initialize:function(){h.FullBox.prototype=new h.Box,h.ContainerBox.prototype=new h.Box,h.SampleEntry.prototype=new h.Box,h.TrackGroupTypeBox.prototype=new h.FullBox,h.BASIC_BOXES.forEach((function(e){h.createBoxCtor(e)})),h.FULL_BOXES.forEach((function(e){h.createFullBoxCtor(e)})),h.CONTAINER_BOXES.forEach((function(e){h.createContainerBoxCtor(e[0],null,e[1])}))},Box:function(e,t,r){this.type=e,this.size=t,this.uuid=r},FullBox:function(e,t,r){h.Box.call(this,e,t,r),this.flags=0,this.version=0},ContainerBox:function(e,t,r){h.Box.call(this,e,t,r),this.boxes=[]},SampleEntry:function(e,t,r,i){h.ContainerBox.call(this,e,t),this.hdr_size=r,this.start=i},SampleGroupEntry:function(e){this.grouping_type=e},TrackGroupTypeBox:function(e,t){h.FullBox.call(this,e,t)},createBoxCtor:function(e,t){h.boxCodes.push(e),h[e+"Box"]=function(t){h.Box.call(this,e,t)},h[e+"Box"].prototype=new h.Box,t&&(h[e+"Box"].prototype.parse=t)},createFullBoxCtor:function(e,t){h[e+"Box"]=function(t){h.FullBox.call(this,e,t)},h[e+"Box"].prototype=new h.FullBox,h[e+"Box"].prototype.parse=function(e){this.parseFullHeader(e),t&&t.call(this,e)}},addSubBoxArrays:function(e){if(e){this.subBoxNames=e;for(var t=e.length,r=0;r<t;r++)this[e[r]+"s"]=[]}},createContainerBoxCtor:function(e,t,r){h[e+"Box"]=function(t){h.ContainerBox.call(this,e,t),h.addSubBoxArrays.call(this,r)},h[e+"Box"].prototype=new h.ContainerBox,t&&(h[e+"Box"].prototype.parse=t)},createMediaSampleEntryCtor:function(e,t,r){h.sampleEntryCodes[e]=[],h[e+"SampleEntry"]=function(e,t){h.SampleEntry.call(this,e,t),h.addSubBoxArrays.call(this,r)},h[e+"SampleEntry"].prototype=new h.SampleEntry,t&&(h[e+"SampleEntry"].prototype.parse=t)},createSampleEntryCtor:function(e,t,r,i){h.sampleEntryCodes[e].push(t),h[t+"SampleEntry"]=function(r){h[e+"SampleEntry"].call(this,t,r),h.addSubBoxArrays.call(this,i)},h[t+"SampleEntry"].prototype=new h[e+"SampleEntry"],r&&(h[t+"SampleEntry"].prototype.parse=r)},createEncryptedSampleEntryCtor:function(e,t,r){h.createSampleEntryCtor.call(this,e,t,r,["sinf"])},createSampleGroupCtor:function(e,t){h[e+"SampleGroupEntry"]=function(t){h.SampleGroupEntry.call(this,e,t)},h[e+"SampleGroupEntry"].prototype=new h.SampleGroupEntry,t&&(h[e+"SampleGroupEntry"].prototype.parse=t)},createTrackGroupCtor:function(e,t){h[e+"TrackGroupTypeBox"]=function(t){h.TrackGroupTypeBox.call(this,e,t)},h[e+"TrackGroupTypeBox"].prototype=new h.TrackGroupTypeBox,t&&(h[e+"TrackGroupTypeBox"].prototype.parse=t)},createUUIDBox:function(e,t,r,i){h.UUIDs.push(e),h.UUIDBoxes[e]=function(i){t?h.FullBox.call(this,"uuid",i,e):r?h.ContainerBox.call(this,"uuid",i,e):h.Box.call(this,"uuid",i,e)},h.UUIDBoxes[e].prototype=t?new h.FullBox:r?new h.ContainerBox:new h.Box,i&&(h.UUIDBoxes[e].prototype.parse=t?function(e){this.parseFullHeader(e),i&&i.call(this,e)}:i)}};function f(e,t){this.x=e,this.y=t}function p(e,t){this.bad_pixel_row=e,this.bad_pixel_column=t}h.initialize(),h.TKHD_FLAG_ENABLED=1,h.TKHD_FLAG_IN_MOVIE=2,h.TKHD_FLAG_IN_PREVIEW=4,h.TFHD_FLAG_BASE_DATA_OFFSET=1,h.TFHD_FLAG_SAMPLE_DESC=2,h.TFHD_FLAG_SAMPLE_DUR=8,h.TFHD_FLAG_SAMPLE_SIZE=16,h.TFHD_FLAG_SAMPLE_FLAGS=32,h.TFHD_FLAG_DUR_EMPTY=65536,h.TFHD_FLAG_DEFAULT_BASE_IS_MOOF=131072,h.TRUN_FLAGS_DATA_OFFSET=1,h.TRUN_FLAGS_FIRST_FLAG=4,h.TRUN_FLAGS_DURATION=256,h.TRUN_FLAGS_SIZE=512,h.TRUN_FLAGS_FLAGS=1024,h.TRUN_FLAGS_CTS_OFFSET=2048,h.Box.prototype.add=function(e){return this.addBox(new h[e+"Box"])},h.Box.prototype.addBox=function(e){return this.boxes.push(e),this[e.type+"s"]?this[e.type+"s"].push(e):this[e.type]=e,e},h.Box.prototype.set=function(e,t){return this[e]=t,this},h.Box.prototype.addEntry=function(e,t){var r=t||"entries";return this[r]||(this[r]=[]),this[r].push(e),this},t.BoxParser=h,h.parseUUID=function(e){return h.parseHex16(e)},h.parseHex16=function(e){for(var t="",r=0;r<16;r++){var i=e.readUint8().toString(16);t+=1===i.length?"0"+i:i}return t},h.parseOneBox=function(e,t,r){var i,n,s,o=e.getPosition(),d=0;if(e.getEndPosition()-o<8)return a.debug("BoxParser","Not enough data in stream to parse the type and size of the box"),{code:h.ERR_NOT_ENOUGH_DATA};if(r&&r<8)return a.debug("BoxParser","Not enough bytes left in the parent box to parse a new box"),{code:h.ERR_NOT_ENOUGH_DATA};var l=e.readUint32(),c=e.readString(4),u=c;if(a.debug("BoxParser","Found box of type '"+c+"' and size "+l+" at position "+o),d=8,"uuid"==c){if(e.getEndPosition()-e.getPosition()<16||r-d<16)return e.seek(o),a.debug("BoxParser","Not enough bytes left in the parent box to parse a UUID box"),{code:h.ERR_NOT_ENOUGH_DATA};d+=16,u=s=h.parseUUID(e)}if(1==l){if(e.getEndPosition()-e.getPosition()<8||r&&r-d<8)return e.seek(o),a.warn("BoxParser",'Not enough data in stream to parse the extended size of the "'+c+'" box'),{code:h.ERR_NOT_ENOUGH_DATA};l=e.readUint64(),d+=8}else if(0===l)if(r)l=r;else if("mdat"!==c)return a.error("BoxParser","Unlimited box size not supported for type: '"+c+"'"),i=new h.Box(c,l),{code:h.OK,box:i,size:i.size};return 0!==l&&l<d?(a.error("BoxParser","Box of type "+c+" has an invalid size "+l+" (too small to be a box)"),{code:h.ERR_NOT_ENOUGH_DATA,type:c,size:l,hdr_size:d,start:o}):0!==l&&r&&l>r?(a.error("BoxParser","Box of type '"+c+"' has a size "+l+" greater than its container size "+r),{code:h.ERR_NOT_ENOUGH_DATA,type:c,size:l,hdr_size:d,start:o}):0!==l&&o+l>e.getEndPosition()?(e.seek(o),a.info("BoxParser","Not enough data in stream to parse the entire '"+c+"' box"),{code:h.ERR_NOT_ENOUGH_DATA,type:c,size:l,hdr_size:d,start:o}):t?{code:h.OK,type:c,size:l,hdr_size:d,start:o}:(h[c+"Box"]?i=new h[c+"Box"](l):"uuid"!==c?(a.warn("BoxParser","Unknown box type: '"+c+"'"),(i=new h.Box(c,l)).has_unparsed_data=!0):h.UUIDBoxes[s]?i=new h.UUIDBoxes[s](l):(a.warn("BoxParser","Unknown uuid type: '"+s+"'"),(i=new h.Box(c,l)).uuid=s,i.has_unparsed_data=!0),i.hdr_size=d,i.start=o,i.write===h.Box.prototype.write&&"mdat"!==i.type&&(a.info("BoxParser","'"+u+"' box writing not yet implemented, keeping unparsed data in memory for later write"),i.parseDataAndRewind(e)),i.parse(e),(n=e.getPosition()-(i.start+i.size))<0?(a.warn("BoxParser","Parsing of box '"+u+"' did not read the entire indicated box data size (missing "+-n+" bytes), seeking forward"),e.seek(i.start+i.size)):n>0&&(a.error("BoxParser","Parsing of box '"+u+"' read "+n+" more bytes than the indicated box data size, seeking backwards"),0!==i.size&&e.seek(i.start+i.size)),{code:h.OK,box:i,size:i.size})},h.Box.prototype.parse=function(e){"mdat"!=this.type?this.data=e.readUint8Array(this.size-this.hdr_size):0===this.size?e.seek(e.getEndPosition()):e.seek(this.start+this.size)},h.Box.prototype.parseDataAndRewind=function(e){this.data=e.readUint8Array(this.size-this.hdr_size),e.position-=this.size-this.hdr_size},h.FullBox.prototype.parseDataAndRewind=function(e){this.parseFullHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size),this.hdr_size-=4,e.position-=this.size-this.hdr_size},h.FullBox.prototype.parseFullHeader=function(e){this.version=e.readUint8(),this.flags=e.readUint24(),this.hdr_size+=4},h.FullBox.prototype.parse=function(e){this.parseFullHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size)},h.ContainerBox.prototype.parse=function(e){for(var t,r;e.getPosition()<this.start+this.size;){if((t=h.parseOneBox(e,!1,this.size-(e.getPosition()-this.start))).code!==h.OK)return;if(r=t.box,this.boxes.push(r),this.subBoxNames&&-1!=this.subBoxNames.indexOf(r.type))this[this.subBoxNames[this.subBoxNames.indexOf(r.type)]+"s"].push(r);else{var i="uuid"!==r.type?r.type:r.uuid;this[i]?a.warn("Box of type "+i+" already stored in field of this type"):this[i]=r}}},h.Box.prototype.parseLanguage=function(e){this.language=e.readUint16();var t=[];t[0]=this.language>>10&31,t[1]=this.language>>5&31,t[2]=31&this.language,this.languageString=String.fromCharCode(t[0]+96,t[1]+96,t[2]+96)},h.SAMPLE_ENTRY_TYPE_VISUAL="Visual",h.SAMPLE_ENTRY_TYPE_AUDIO="Audio",h.SAMPLE_ENTRY_TYPE_HINT="Hint",h.SAMPLE_ENTRY_TYPE_METADATA="Metadata",h.SAMPLE_ENTRY_TYPE_SUBTITLE="Subtitle",h.SAMPLE_ENTRY_TYPE_SYSTEM="System",h.SAMPLE_ENTRY_TYPE_TEXT="Text",h.SampleEntry.prototype.parseHeader=function(e){e.readUint8Array(6),this.data_reference_index=e.readUint16(),this.hdr_size+=8},h.SampleEntry.prototype.parse=function(e){this.parseHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size)},h.SampleEntry.prototype.parseDataAndRewind=function(e){this.parseHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size),this.hdr_size-=8,e.position-=this.size-this.hdr_size},h.SampleEntry.prototype.parseFooter=function(e){h.ContainerBox.prototype.parse.call(this,e)},h.createMediaSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_HINT),h.createMediaSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_METADATA),h.createMediaSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_SUBTITLE),h.createMediaSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_SYSTEM),h.createMediaSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_TEXT),h.createMediaSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,(function(e){var t;this.parseHeader(e),e.readUint16(),e.readUint16(),e.readUint32Array(3),this.width=e.readUint16(),this.height=e.readUint16(),this.horizresolution=e.readUint32(),this.vertresolution=e.readUint32(),e.readUint32(),this.frame_count=e.readUint16(),t=Math.min(31,e.readUint8()),this.compressorname=e.readString(t),t<31&&e.readString(31-t),this.depth=e.readUint16(),e.readUint16(),this.parseFooter(e)})),h.createMediaSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_AUDIO,(function(e){this.parseHeader(e),e.readUint32Array(2),this.channel_count=e.readUint16(),this.samplesize=e.readUint16(),e.readUint16(),e.readUint16(),this.samplerate=e.readUint32()/65536,this.parseFooter(e)})),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"avc1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"avc2"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"avc3"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"avc4"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"av01"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"dav1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"hvc1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"hev1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"hvt1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"lhe1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"dvh1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"dvhe"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"vvc1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"vvi1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"vvs1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"vvcN"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"vp08"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"vp09"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"avs3"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"j2ki"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"mjp2"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"mjpg"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"uncv"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_AUDIO,"mp4a"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_AUDIO,"ac-3"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_AUDIO,"ac-4"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_AUDIO,"ec-3"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_AUDIO,"Opus"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_AUDIO,"mha1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_AUDIO,"mha2"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_AUDIO,"mhm1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_AUDIO,"mhm2"),h.createEncryptedSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"encv"),h.createEncryptedSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_AUDIO,"enca"),h.createEncryptedSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_SUBTITLE,"encu"),h.createEncryptedSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_SYSTEM,"encs"),h.createEncryptedSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_TEXT,"enct"),h.createEncryptedSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_METADATA,"encm"),h.createBoxCtor("a1lx",(function(e){var t=16*(1+(1&(1&e.readUint8())));this.layer_size=[];for(var r=0;r<3;r++)this.layer_size[r]=16==t?e.readUint16():e.readUint32()})),h.createBoxCtor("a1op",(function(e){this.op_index=e.readUint8()})),h.createFullBoxCtor("auxC",(function(e){this.aux_type=e.readCString();var t=this.size-this.hdr_size-(this.aux_type.length+1);this.aux_subtype=e.readUint8Array(t)})),h.createBoxCtor("av1C",(function(e){var t=e.readUint8();if(t>>7&!1)a.error("av1C marker problem");else if(this.version=127&t,1===this.version)if(t=e.readUint8(),this.seq_profile=t>>5&7,this.seq_level_idx_0=31&t,t=e.readUint8(),this.seq_tier_0=t>>7&1,this.high_bitdepth=t>>6&1,this.twelve_bit=t>>5&1,this.monochrome=t>>4&1,this.chroma_subsampling_x=t>>3&1,this.chroma_subsampling_y=t>>2&1,this.chroma_sample_position=3&t,t=e.readUint8(),this.reserved_1=t>>5&7,0===this.reserved_1){if(this.initial_presentation_delay_present=t>>4&1,1===this.initial_presentation_delay_present)this.initial_presentation_delay_minus_one=15&t;else if(this.reserved_2=15&t,0!==this.reserved_2)return void a.error("av1C reserved_2 parsing problem");var r=this.size-this.hdr_size-4;this.configOBUs=e.readUint8Array(r)}else a.error("av1C reserved_1 parsing problem");else a.error("av1C version "+this.version+" not supported")})),h.createBoxCtor("avcC",(function(e){var t,r;for(this.configurationVersion=e.readUint8(),this.AVCProfileIndication=e.readUint8(),this.profile_compatibility=e.readUint8(),this.AVCLevelIndication=e.readUint8(),this.lengthSizeMinusOne=3&e.readUint8(),this.nb_SPS_nalus=31&e.readUint8(),r=this.size-this.hdr_size-6,this.SPS=[],t=0;t<this.nb_SPS_nalus;t++)this.SPS[t]={},this.SPS[t].length=e.readUint16(),this.SPS[t].nalu=e.readUint8Array(this.SPS[t].length),r-=2+this.SPS[t].length;for(this.nb_PPS_nalus=e.readUint8(),r--,this.PPS=[],t=0;t<this.nb_PPS_nalus;t++)this.PPS[t]={},this.PPS[t].length=e.readUint16(),this.PPS[t].nalu=e.readUint8Array(this.PPS[t].length),r-=2+this.PPS[t].length;r>0&&(this.ext=e.readUint8Array(r))})),h.createBoxCtor("btrt",(function(e){this.bufferSizeDB=e.readUint32(),this.maxBitrate=e.readUint32(),this.avgBitrate=e.readUint32()})),h.createFullBoxCtor("ccst",(function(e){var t=e.readUint8();this.all_ref_pics_intra=128==(128&t),this.intra_pred_used=64==(64&t),this.max_ref_per_pic=(63&t)>>2,e.readUint24()})),h.createBoxCtor("cdef",(function(e){var t;for(this.channel_count=e.readUint16(),this.channel_indexes=[],this.channel_types=[],this.channel_associations=[],t=0;t<this.channel_count;t++)this.channel_indexes.push(e.readUint16()),this.channel_types.push(e.readUint16()),this.channel_associations.push(e.readUint16())})),h.createBoxCtor("clap",(function(e){this.cleanApertureWidthN=e.readUint32(),this.cleanApertureWidthD=e.readUint32(),this.cleanApertureHeightN=e.readUint32(),this.cleanApertureHeightD=e.readUint32(),this.horizOffN=e.readUint32(),this.horizOffD=e.readUint32(),this.vertOffN=e.readUint32(),this.vertOffD=e.readUint32()})),h.createBoxCtor("clli",(function(e){this.max_content_light_level=e.readUint16(),this.max_pic_average_light_level=e.readUint16()})),h.createFullBoxCtor("cmex",(function(e){1&this.flags&&(this.pos_x=e.readInt32()),2&this.flags&&(this.pos_y=e.readInt32()),4&this.flags&&(this.pos_z=e.readInt32()),8&this.flags&&(0==this.version?16&this.flags?(this.quat_x=e.readInt32(),this.quat_y=e.readInt32(),this.quat_z=e.readInt32()):(this.quat_x=e.readInt16(),this.quat_y=e.readInt16(),this.quat_z=e.readInt16()):this.version),32&this.flags&&(this.id=e.readUint32())})),h.createFullBoxCtor("cmin",(function(e){this.focal_length_x=e.readInt32(),this.principal_point_x=e.readInt32(),this.principal_point_y=e.readInt32(),1&this.flags&&(this.focal_length_y=e.readInt32(),this.skew_factor=e.readInt32())})),h.createBoxCtor("cmpd",(function(e){for(this.component_count=e.readUint32(),this.component_types=[],this.component_type_urls=[],i=0;i<this.component_count;i++){var t=e.readUint16();this.component_types.push(t),t>=32768&&this.component_type_urls.push(e.readCString())}})),h.createFullBoxCtor("co64",(function(e){var t,r;if(t=e.readUint32(),this.chunk_offsets=[],0===this.version)for(r=0;r<t;r++)this.chunk_offsets.push(e.readUint64())})),h.createFullBoxCtor("CoLL",(function(e){this.maxCLL=e.readUint16(),this.maxFALL=e.readUint16()})),h.createBoxCtor("colr",(function(e){if(this.colour_type=e.readString(4),"nclx"===this.colour_type){this.colour_primaries=e.readUint16(),this.transfer_characteristics=e.readUint16(),this.matrix_coefficients=e.readUint16();var t=e.readUint8();this.full_range_flag=t>>7}else("rICC"===this.colour_type||"prof"===this.colour_type)&&(this.ICC_profile=e.readUint8Array(this.size-4))})),h.createFullBoxCtor("cprt",(function(e){this.parseLanguage(e),this.notice=e.readCString()})),h.createFullBoxCtor("cslg",(function(e){0===this.version&&(this.compositionToDTSShift=e.readInt32(),this.leastDecodeToDisplayDelta=e.readInt32(),this.greatestDecodeToDisplayDelta=e.readInt32(),this.compositionStartTime=e.readInt32(),this.compositionEndTime=e.readInt32())})),h.createFullBoxCtor("ctts",(function(e){var t,r;if(t=e.readUint32(),this.sample_counts=[],this.sample_offsets=[],0===this.version)for(r=0;r<t;r++){this.sample_counts.push(e.readUint32());var i=e.readInt32();i<0&&a.warn("BoxParser","ctts box uses negative values without using version 1"),this.sample_offsets.push(i)}else if(1==this.version)for(r=0;r<t;r++)this.sample_counts.push(e.readUint32()),this.sample_offsets.push(e.readInt32())})),h.createBoxCtor("dac3",(function(e){var t=e.readUint8(),r=e.readUint8(),i=e.readUint8();this.fscod=t>>6,this.bsid=t>>1&31,this.bsmod=(1&t)<<2|r>>6&3,this.acmod=r>>3&7,this.lfeon=r>>2&1,this.bit_rate_code=3&r|i>>5&7})),h.createBoxCtor("dec3",(function(e){var t=e.readUint16();this.data_rate=t>>3,this.num_ind_sub=7&t,this.ind_subs=[];for(var r=0;r<this.num_ind_sub+1;r++){var i={};this.ind_subs.push(i);var n=e.readUint8(),s=e.readUint8(),a=e.readUint8();i.fscod=n>>6,i.bsid=n>>1&31,i.bsmod=(1&n)<<4|s>>4&15,i.acmod=s>>1&7,i.lfeon=1&s,i.num_dep_sub=a>>1&15,i.num_dep_sub>0&&(i.chan_loc=(1&a)<<8|e.readUint8())}})),h.createFullBoxCtor("dfLa",(function(e){var t=[],r=["STREAMINFO","PADDING","APPLICATION","SEEKTABLE","VORBIS_COMMENT","CUESHEET","PICTURE","RESERVED"];for(this.parseFullHeader(e);;){var i=e.readUint8(),n=Math.min(127&i,r.length-1);if(n?e.readUint8Array(e.readUint24()):(e.readUint8Array(13),this.samplerate=e.readUint32()>>12,e.readUint8Array(20)),t.push(r[n]),128&i)break}this.numMetadataBlocks=t.length+" ("+t.join(", ")+")"})),h.createBoxCtor("dimm",(function(e){this.bytessent=e.readUint64()})),h.createBoxCtor("dmax",(function(e){this.time=e.readUint32()})),h.createBoxCtor("dmed",(function(e){this.bytessent=e.readUint64()})),h.createBoxCtor("dOps",(function(e){if(this.Version=e.readUint8(),this.OutputChannelCount=e.readUint8(),this.PreSkip=e.readUint16(),this.InputSampleRate=e.readUint32(),this.OutputGain=e.readInt16(),this.ChannelMappingFamily=e.readUint8(),0!==this.ChannelMappingFamily){this.StreamCount=e.readUint8(),this.CoupledCount=e.readUint8(),this.ChannelMapping=[];for(var t=0;t<this.OutputChannelCount;t++)this.ChannelMapping[t]=e.readUint8()}})),h.createFullBoxCtor("dref",(function(e){var t,r;this.entries=[];for(var i=e.readUint32(),n=0;n<i;n++){if((t=h.parseOneBox(e,!1,this.size-(e.getPosition()-this.start))).code!==h.OK)return;r=t.box,this.entries.push(r)}})),h.createBoxCtor("drep",(function(e){this.bytessent=e.readUint64()})),h.createFullBoxCtor("elng",(function(e){this.extended_language=e.readString(this.size-this.hdr_size)})),h.createFullBoxCtor("elst",(function(e){this.entries=[];for(var t=e.readUint32(),r=0;r<t;r++){var i={};this.entries.push(i),1===this.version?(i.segment_duration=e.readUint64(),i.media_time=e.readInt64()):(i.segment_duration=e.readUint32(),i.media_time=e.readInt32()),i.media_rate_integer=e.readInt16(),i.media_rate_fraction=e.readInt16()}})),h.createFullBoxCtor("emsg",(function(e){1==this.version?(this.timescale=e.readUint32(),this.presentation_time=e.readUint64(),this.event_duration=e.readUint32(),this.id=e.readUint32(),this.scheme_id_uri=e.readCString(),this.value=e.readCString()):(this.scheme_id_uri=e.readCString(),this.value=e.readCString(),this.timescale=e.readUint32(),this.presentation_time_delta=e.readUint32(),this.event_duration=e.readUint32(),this.id=e.readUint32());var t=this.size-this.hdr_size-(16+(this.scheme_id_uri.length+1)+(this.value.length+1));1==this.version&&(t-=4),this.message_data=e.readUint8Array(t)})),h.createEntityToGroupCtor=function(e,t){h[e+"Box"]=function(t){h.FullBox.call(this,e,t)},h[e+"Box"].prototype=new h.FullBox,h[e+"Box"].prototype.parse=function(e){if(this.parseFullHeader(e),t)t.call(this,e);else for(this.group_id=e.readUint32(),this.num_entities_in_group=e.readUint32(),this.entity_ids=[],i=0;i<this.num_entities_in_group;i++){var r=e.readUint32();this.entity_ids.push(r)}}},h.createEntityToGroupCtor("aebr"),h.createEntityToGroupCtor("afbr"),h.createEntityToGroupCtor("albc"),h.createEntityToGroupCtor("altr"),h.createEntityToGroupCtor("brst"),h.createEntityToGroupCtor("dobr"),h.createEntityToGroupCtor("eqiv"),h.createEntityToGroupCtor("favc"),h.createEntityToGroupCtor("fobr"),h.createEntityToGroupCtor("iaug"),h.createEntityToGroupCtor("pano"),h.createEntityToGroupCtor("slid"),h.createEntityToGroupCtor("ster"),h.createEntityToGroupCtor("tsyn"),h.createEntityToGroupCtor("wbbr"),h.createEntityToGroupCtor("prgr"),h.createFullBoxCtor("esds",(function(e){var t=e.readUint8Array(this.size-this.hdr_size),r=new u;this.esd=r.parseOneDescriptor(new d(t.buffer,0,d.BIG_ENDIAN))})),h.createBoxCtor("fiel",(function(e){this.fieldCount=e.readUint8(),this.fieldOrdering=e.readUint8()})),h.createBoxCtor("frma",(function(e){this.data_format=e.readString(4)})),h.createBoxCtor("ftyp",(function(e){var t=this.size-this.hdr_size;this.major_brand=e.readString(4),this.minor_version=e.readUint32(),t-=8,this.compatible_brands=[];for(var r=0;t>=4;)this.compatible_brands[r]=e.readString(4),t-=4,r++})),h.createFullBoxCtor("hdlr",(function(e){0===this.version&&(e.readUint32(),this.handler=e.readString(4),e.readUint32Array(3),this.name=e.readString(this.size-this.hdr_size-20),"\0"===this.name[this.name.length-1]&&(this.name=this.name.slice(0,-1)))})),h.createBoxCtor("hvcC",(function(e){var t,r,i,n;this.configurationVersion=e.readUint8(),n=e.readUint8(),this.general_profile_space=n>>6,this.general_tier_flag=(32&n)>>5,this.general_profile_idc=31&n,this.general_profile_compatibility=e.readUint32(),this.general_constraint_indicator=e.readUint8Array(6),this.general_level_idc=e.readUint8(),this.min_spatial_segmentation_idc=4095&e.readUint16(),this.parallelismType=3&e.readUint8(),this.chroma_format_idc=3&e.readUint8(),this.bit_depth_luma_minus8=7&e.readUint8(),this.bit_depth_chroma_minus8=7&e.readUint8(),this.avgFrameRate=e.readUint16(),n=e.readUint8(),this.constantFrameRate=n>>6,this.numTemporalLayers=(13&n)>>3,this.temporalIdNested=(4&n)>>2,this.lengthSizeMinusOne=3&n,this.nalu_arrays=[];var s=e.readUint8();for(t=0;t<s;t++){var a=[];this.nalu_arrays.push(a),n=e.readUint8(),a.completeness=(128&n)>>7,a.nalu_type=63&n;var o=e.readUint16();for(r=0;r<o;r++){var d={};a.push(d),i=e.readUint16(),d.data=e.readUint8Array(i)}}})),h.createFullBoxCtor("iinf",(function(e){var t;0===this.version?this.entry_count=e.readUint16():this.entry_count=e.readUint32(),this.item_infos=[];for(var r=0;r<this.entry_count;r++){if((t=h.parseOneBox(e,!1,this.size-(e.getPosition()-this.start))).code!==h.OK)return;"infe"!==t.box.type&&a.error("BoxParser","Expected 'infe' box, got "+t.box.type),this.item_infos[r]=t.box}})),h.createFullBoxCtor("iloc",(function(e){var t;t=e.readUint8(),this.offset_size=t>>4&15,this.length_size=15&t,t=e.readUint8(),this.base_offset_size=t>>4&15,1===this.version||2===this.version?this.index_size=15&t:this.index_size=0,this.items=[];var r=0;if(this.version<2)r=e.readUint16();else{if(2!==this.version)throw"version of iloc box not supported";r=e.readUint32()}for(var i=0;i<r;i++){var n={};if(this.items.push(n),this.version<2)n.item_ID=e.readUint16();else{if(2!==this.version)throw"version of iloc box not supported";n.item_ID=e.readUint32()}switch(1===this.version||2===this.version?n.construction_method=15&e.readUint16():n.construction_method=0,n.data_reference_index=e.readUint16(),this.base_offset_size){case 0:n.base_offset=0;break;case 4:n.base_offset=e.readUint32();break;case 8:n.base_offset=e.readUint64();break;default:throw"Error reading base offset size"}var s=e.readUint16();n.extents=[];for(var a=0;a<s;a++){var o={};if(n.extents.push(o),1===this.version||2===this.version)switch(this.index_size){case 0:o.extent_index=0;break;case 4:o.extent_index=e.readUint32();break;case 8:o.extent_index=e.readUint64();break;default:throw"Error reading extent index"}switch(this.offset_size){case 0:o.extent_offset=0;break;case 4:o.extent_offset=e.readUint32();break;case 8:o.extent_offset=e.readUint64();break;default:throw"Error reading extent index"}switch(this.length_size){case 0:o.extent_length=0;break;case 4:o.extent_length=e.readUint32();break;case 8:o.extent_length=e.readUint64();break;default:throw"Error reading extent index"}}}})),h.createBoxCtor("imir",(function(e){var t=e.readUint8();this.reserved=t>>7,this.axis=1&t})),h.createFullBoxCtor("infe",(function(e){if(0!==this.version&&1!==this.version||(this.item_ID=e.readUint16(),this.item_protection_index=e.readUint16(),this.item_name=e.readCString(),this.content_type=e.readCString(),this.content_encoding=e.readCString()),1===this.version)return this.extension_type=e.readString(4),a.warn("BoxParser","Cannot parse extension type"),void e.seek(this.start+this.size);this.version>=2&&(2===this.version?this.item_ID=e.readUint16():3===this.version&&(this.item_ID=e.readUint32()),this.item_protection_index=e.readUint16(),this.item_type=e.readString(4),this.item_name=e.readCString(),"mime"===this.item_type?(this.content_type=e.readCString(),this.content_encoding=e.readCString()):"uri "===this.item_type&&(this.item_uri_type=e.readCString()))})),h.createFullBoxCtor("ipma",(function(e){var t,r;for(entry_count=e.readUint32(),this.associations=[],t=0;t<entry_count;t++){var i={};this.associations.push(i),this.version<1?i.id=e.readUint16():i.id=e.readUint32();var n=e.readUint8();for(i.props=[],r=0;r<n;r++){var s=e.readUint8(),a={};i.props.push(a),a.essential=(128&s)>>7==1,1&this.flags?a.property_index=(127&s)<<8|e.readUint8():a.property_index=127&s}}})),h.createFullBoxCtor("iref",(function(e){var t,r;for(this.references=[];e.getPosition()<this.start+this.size;){if((t=h.parseOneBox(e,!0,this.size-(e.getPosition()-this.start))).code!==h.OK)return;(r=0===this.version?new h.SingleItemTypeReferenceBox(t.type,t.size,t.hdr_size,t.start):new h.SingleItemTypeReferenceBoxLarge(t.type,t.size,t.hdr_size,t.start)).write===h.Box.prototype.write&&"mdat"!==r.type&&(a.warn("BoxParser",r.type+" box writing not yet implemented, keeping unparsed data in memory for later write"),r.parseDataAndRewind(e)),r.parse(e),this.references.push(r)}})),h.createBoxCtor("irot",(function(e){this.angle=3&e.readUint8()})),h.createFullBoxCtor("ispe",(function(e){this.image_width=e.readUint32(),this.image_height=e.readUint32()})),h.createFullBoxCtor("kind",(function(e){this.schemeURI=e.readCString(),this.value=e.readCString()})),h.createFullBoxCtor("leva",(function(e){var t=e.readUint8();this.levels=[];for(var r=0;r<t;r++){var i={};this.levels[r]=i,i.track_ID=e.readUint32();var n=e.readUint8();switch(i.padding_flag=n>>7,i.assignment_type=127&n,i.assignment_type){case 0:i.grouping_type=e.readString(4);break;case 1:i.grouping_type=e.readString(4),i.grouping_type_parameter=e.readUint32();break;case 2:case 3:break;case 4:i.sub_track_id=e.readUint32();break;default:a.warn("BoxParser","Unknown leva assignement type")}}})),h.createBoxCtor("lsel",(function(e){this.layer_id=e.readUint16()})),h.createBoxCtor("maxr",(function(e){this.period=e.readUint32(),this.bytes=e.readUint32()})),f.prototype.toString=function(){return"("+this.x+","+this.y+")"},h.createBoxCtor("mdcv",(function(e){this.display_primaries=[],this.display_primaries[0]=new f(e.readUint16(),e.readUint16()),this.display_primaries[1]=new f(e.readUint16(),e.readUint16()),this.display_primaries[2]=new f(e.readUint16(),e.readUint16()),this.white_point=new f(e.readUint16(),e.readUint16()),this.max_display_mastering_luminance=e.readUint32(),this.min_display_mastering_luminance=e.readUint32()})),h.createFullBoxCtor("mdhd",(function(e){1==this.version?(this.creation_time=e.readUint64(),this.modification_time=e.readUint64(),this.timescale=e.readUint32(),this.duration=e.readUint64()):(this.creation_time=e.readUint32(),this.modification_time=e.readUint32(),this.timescale=e.readUint32(),this.duration=e.readUint32()),this.parseLanguage(e),e.readUint16()})),h.createFullBoxCtor("mehd",(function(e){1&this.flags&&(a.warn("BoxParser","mehd box incorrectly uses flags set to 1, converting version to 1"),this.version=1),1==this.version?this.fragment_duration=e.readUint64():this.fragment_duration=e.readUint32()})),h.createFullBoxCtor("meta",(function(e){this.boxes=[],h.ContainerBox.prototype.parse.call(this,e)})),h.createFullBoxCtor("mfhd",(function(e){this.sequence_number=e.readUint32()})),h.createFullBoxCtor("mfro",(function(e){this._size=e.readUint32()})),h.createFullBoxCtor("mskC",(function(e){this.bits_per_pixel=e.readUint8()})),h.createFullBoxCtor("mvhd",(function(e){1==this.version?(this.creation_time=e.readUint64(),this.modification_time=e.readUint64(),this.timescale=e.readUint32(),this.duration=e.readUint64()):(this.creation_time=e.readUint32(),this.modification_time=e.readUint32(),this.timescale=e.readUint32(),this.duration=e.readUint32()),this.rate=e.readUint32(),this.volume=e.readUint16()>>8,e.readUint16(),e.readUint32Array(2),this.matrix=e.readUint32Array(9),e.readUint32Array(6),this.next_track_id=e.readUint32()})),h.createBoxCtor("npck",(function(e){this.packetssent=e.readUint32()})),h.createBoxCtor("nump",(function(e){this.packetssent=e.readUint64()})),h.createFullBoxCtor("padb",(function(e){var t=e.readUint32();this.padbits=[];for(var r=0;r<Math.floor((t+1)/2);r++)this.padbits=e.readUint8()})),h.createBoxCtor("pasp",(function(e){this.hSpacing=e.readUint32(),this.vSpacing=e.readUint32()})),h.createBoxCtor("payl",(function(e){this.text=e.readString(this.size-this.hdr_size)})),h.createBoxCtor("payt",(function(e){this.payloadID=e.readUint32();var t=e.readUint8();this.rtpmap_string=e.readString(t)})),h.createFullBoxCtor("pdin",(function(e){var t=(this.size-this.hdr_size)/8;this.rate=[],this.initial_delay=[];for(var r=0;r<t;r++)this.rate[r]=e.readUint32(),this.initial_delay[r]=e.readUint32()})),h.createFullBoxCtor("pitm",(function(e){0===this.version?this.item_id=e.readUint16():this.item_id=e.readUint32()})),h.createFullBoxCtor("pixi",(function(e){var t;for(this.num_channels=e.readUint8(),this.bits_per_channels=[],t=0;t<this.num_channels;t++)this.bits_per_channels[t]=e.readUint8()})),h.createBoxCtor("pmax",(function(e){this.bytes=e.readUint32()})),h.createFullBoxCtor("prdi",(function(e){if(this.step_count=e.readUint16(),this.item_count=[],2&this.flags)for(var t=0;t<this.step_count;t++)this.item_count[t]=e.readUint16()})),h.createFullBoxCtor("prft",(function(e){this.ref_track_id=e.readUint32(),this.ntp_timestamp=e.readUint64(),0===this.version?this.media_time=e.readUint32():this.media_time=e.readUint64()})),h.createFullBoxCtor("pssh",(function(e){if(this.system_id=h.parseHex16(e),this.version>0){var t=e.readUint32();this.kid=[];for(var r=0;r<t;r++)this.kid[r]=h.parseHex16(e)}var i=e.readUint32();i>0&&(this.data=e.readUint8Array(i))})),h.createFullBoxCtor("clef",(function(e){this.width=e.readUint32(),this.height=e.readUint32()})),h.createFullBoxCtor("enof",(function(e){this.width=e.readUint32(),this.height=e.readUint32()})),h.createFullBoxCtor("prof",(function(e){this.width=e.readUint32(),this.height=e.readUint32()})),h.createContainerBoxCtor("tapt",null,["clef","prof","enof"]),h.createBoxCtor("rtp ",(function(e){this.descriptionformat=e.readString(4),this.sdptext=e.readString(this.size-this.hdr_size-4)})),h.createFullBoxCtor("saio",(function(e){1&this.flags&&(this.aux_info_type=e.readUint32(),this.aux_info_type_parameter=e.readUint32());var t=e.readUint32();this.offset=[];for(var r=0;r<t;r++)0===this.version?this.offset[r]=e.readUint32():this.offset[r]=e.readUint64()})),h.createFullBoxCtor("saiz",(function(e){1&this.flags&&(this.aux_info_type=e.readUint32(),this.aux_info_type_parameter=e.readUint32()),this.default_sample_info_size=e.readUint8();var t=e.readUint32();if(this.sample_info_size=[],0===this.default_sample_info_size)for(var r=0;r<t;r++)this.sample_info_size[r]=e.readUint8()})),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_METADATA,"mett",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.mime_format=e.readCString(),this.parseFooter(e)})),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_METADATA,"metx",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.namespace=e.readCString(),this.schema_location=e.readCString(),this.parseFooter(e)})),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_SUBTITLE,"sbtt",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.mime_format=e.readCString(),this.parseFooter(e)})),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_SUBTITLE,"stpp",(function(e){this.parseHeader(e),this.namespace=e.readCString(),this.schema_location=e.readCString(),this.auxiliary_mime_types=e.readCString(),this.parseFooter(e)})),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_SUBTITLE,"stxt",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.mime_format=e.readCString(),this.parseFooter(e)})),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_SUBTITLE,"tx3g",(function(e){this.parseHeader(e),this.displayFlags=e.readUint32(),this.horizontal_justification=e.readInt8(),this.vertical_justification=e.readInt8(),this.bg_color_rgba=e.readUint8Array(4),this.box_record=e.readInt16Array(4),this.style_record=e.readUint8Array(12),this.parseFooter(e)})),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_METADATA,"wvtt",(function(e){this.parseHeader(e),this.parseFooter(e)})),h.createSampleGroupCtor("alst",(function(e){var t,r=e.readUint16();for(this.first_output_sample=e.readUint16(),this.sample_offset=[],t=0;t<r;t++)this.sample_offset[t]=e.readUint32();var i=this.description_length-4-4*r;for(this.num_output_samples=[],this.num_total_samples=[],t=0;t<i/4;t++)this.num_output_samples[t]=e.readUint16(),this.num_total_samples[t]=e.readUint16()})),h.createSampleGroupCtor("avll",(function(e){this.layerNumber=e.readUint8(),this.accurateStatisticsFlag=e.readUint8(),this.avgBitRate=e.readUint16(),this.avgFrameRate=e.readUint16()})),h.createSampleGroupCtor("avss",(function(e){this.subSequenceIdentifier=e.readUint16(),this.layerNumber=e.readUint8();var t=e.readUint8();this.durationFlag=t>>7,this.avgRateFlag=t>>6&1,this.durationFlag&&(this.duration=e.readUint32()),this.avgRateFlag&&(this.accurateStatisticsFlag=e.readUint8(),this.avgBitRate=e.readUint16(),this.avgFrameRate=e.readUint16()),this.dependency=[];for(var r=e.readUint8(),i=0;i<r;i++){var n={};this.dependency.push(n),n.subSeqDirectionFlag=e.readUint8(),n.layerNumber=e.readUint8(),n.subSequenceIdentifier=e.readUint16()}})),h.createSampleGroupCtor("dtrt",(function(e){a.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),h.createSampleGroupCtor("mvif",(function(e){a.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),h.createSampleGroupCtor("prol",(function(e){this.roll_distance=e.readInt16()})),h.createSampleGroupCtor("rap ",(function(e){var t=e.readUint8();this.num_leading_samples_known=t>>7,this.num_leading_samples=127&t})),h.createSampleGroupCtor("rash",(function(e){if(this.operation_point_count=e.readUint16(),this.description_length!==2+(1===this.operation_point_count?2:6*this.operation_point_count)+9)a.warn("BoxParser","Mismatch in "+this.grouping_type+" sample group length"),this.data=e.readUint8Array(this.description_length-2);else{if(1===this.operation_point_count)this.target_rate_share=e.readUint16();else{this.target_rate_share=[],this.available_bitrate=[];for(var t=0;t<this.operation_point_count;t++)this.available_bitrate[t]=e.readUint32(),this.target_rate_share[t]=e.readUint16()}this.maximum_bitrate=e.readUint32(),this.minimum_bitrate=e.readUint32(),this.discard_priority=e.readUint8()}})),h.createSampleGroupCtor("roll",(function(e){this.roll_distance=e.readInt16()})),h.SampleGroupEntry.prototype.parse=function(e){a.warn("BoxParser","Unknown Sample Group type: "+this.grouping_type),this.data=e.readUint8Array(this.description_length)},h.createSampleGroupCtor("scif",(function(e){a.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),h.createSampleGroupCtor("scnm",(function(e){a.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),h.createSampleGroupCtor("seig",(function(e){this.reserved=e.readUint8();var t=e.readUint8();this.crypt_byte_block=t>>4,this.skip_byte_block=15&t,this.isProtected=e.readUint8(),this.Per_Sample_IV_Size=e.readUint8(),this.KID=h.parseHex16(e),this.constant_IV_size=0,this.constant_IV=0,1===this.isProtected&&0===this.Per_Sample_IV_Size&&(this.constant_IV_size=e.readUint8(),this.constant_IV=e.readUint8Array(this.constant_IV_size))})),h.createSampleGroupCtor("stsa",(function(e){a.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),h.createSampleGroupCtor("sync",(function(e){var t=e.readUint8();this.NAL_unit_type=63&t})),h.createSampleGroupCtor("tele",(function(e){var t=e.readUint8();this.level_independently_decodable=t>>7})),h.createSampleGroupCtor("tsas",(function(e){a.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),h.createSampleGroupCtor("tscl",(function(e){a.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),h.createSampleGroupCtor("vipr",(function(e){a.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),h.createFullBoxCtor("sbgp",(function(e){this.grouping_type=e.readString(4),1===this.version?this.grouping_type_parameter=e.readUint32():this.grouping_type_parameter=0,this.entries=[];for(var t=e.readUint32(),r=0;r<t;r++){var i={};this.entries.push(i),i.sample_count=e.readInt32(),i.group_description_index=e.readInt32()}})),p.prototype.toString=function(){return"[row: "+this.bad_pixel_row+", column: "+this.bad_pixel_column+"]"},h.createFullBoxCtor("sbpm",(function(e){var t;for(this.component_count=e.readUint16(),this.component_index=[],t=0;t<this.component_count;t++)this.component_index.push(e.readUint16());var r=e.readUint8();for(this.correction_applied=128==(128&r),this.num_bad_rows=e.readUint32(),this.num_bad_cols=e.readUint32(),this.num_bad_pixels=e.readUint32(),this.bad_rows=[],this.bad_columns=[],this.bad_pixels=[],t=0;t<this.num_bad_rows;t++)this.bad_rows.push(e.readUint32());for(t=0;t<this.num_bad_cols;t++)this.bad_columns.push(e.readUint32());for(t=0;t<this.num_bad_pixels;t++){var i=e.readUint32(),n=e.readUint32();this.bad_pixels.push(new p(i,n))}})),h.createFullBoxCtor("schm",(function(e){this.scheme_type=e.readString(4),this.scheme_version=e.readUint32(),1&this.flags&&(this.scheme_uri=e.readString(this.size-this.hdr_size-8))})),h.createBoxCtor("sdp ",(function(e){this.sdptext=e.readString(this.size-this.hdr_size)})),h.createFullBoxCtor("sdtp",(function(e){var t,r=this.size-this.hdr_size;this.is_leading=[],this.sample_depends_on=[],this.sample_is_depended_on=[],this.sample_has_redundancy=[];for(var i=0;i<r;i++)t=e.readUint8(),this.is_leading[i]=t>>6,this.sample_depends_on[i]=t>>4&3,this.sample_is_depended_on[i]=t>>2&3,this.sample_has_redundancy[i]=3&t})),h.createFullBoxCtor("senc"),h.createFullBoxCtor("sgpd",(function(e){this.grouping_type=e.readString(4),a.debug("BoxParser","Found Sample Groups of type "+this.grouping_type),1===this.version?this.default_length=e.readUint32():this.default_length=0,this.version>=2&&(this.default_group_description_index=e.readUint32()),this.entries=[];for(var t=e.readUint32(),r=0;r<t;r++){var i;i=h[this.grouping_type+"SampleGroupEntry"]?new h[this.grouping_type+"SampleGroupEntry"](this.grouping_type):new h.SampleGroupEntry(this.grouping_type),this.entries.push(i),1===this.version&&0===this.default_length?i.description_length=e.readUint32():i.description_length=this.default_length,i.write===h.SampleGroupEntry.prototype.write&&(a.info("BoxParser","SampleGroup for type "+this.grouping_type+" writing not yet implemented, keeping unparsed data in memory for later write"),i.data=e.readUint8Array(i.description_length),e.position-=i.description_length),i.parse(e)}})),h.createFullBoxCtor("sidx",(function(e){this.reference_ID=e.readUint32(),this.timescale=e.readUint32(),0===this.version?(this.earliest_presentation_time=e.readUint32(),this.first_offset=e.readUint32()):(this.earliest_presentation_time=e.readUint64(),this.first_offset=e.readUint64()),e.readUint16(),this.references=[];for(var t=e.readUint16(),r=0;r<t;r++){var i={};this.references.push(i);var n=e.readUint32();i.reference_type=n>>31&1,i.referenced_size=2147483647&n,i.subsegment_duration=e.readUint32(),n=e.readUint32(),i.starts_with_SAP=n>>31&1,i.SAP_type=n>>28&7,i.SAP_delta_time=268435455&n}})),h.SingleItemTypeReferenceBox=function(e,t,r,i){h.Box.call(this,e,t),this.hdr_size=r,this.start=i},h.SingleItemTypeReferenceBox.prototype=new h.Box,h.SingleItemTypeReferenceBox.prototype.parse=function(e){this.from_item_ID=e.readUint16();var t=e.readUint16();this.references=[];for(var r=0;r<t;r++)this.references[r]={},this.references[r].to_item_ID=e.readUint16()},h.SingleItemTypeReferenceBoxLarge=function(e,t,r,i){h.Box.call(this,e,t),this.hdr_size=r,this.start=i},h.SingleItemTypeReferenceBoxLarge.prototype=new h.Box,h.SingleItemTypeReferenceBoxLarge.prototype.parse=function(e){this.from_item_ID=e.readUint32();var t=e.readUint16();this.references=[];for(var r=0;r<t;r++)this.references[r]={},this.references[r].to_item_ID=e.readUint32()},h.createFullBoxCtor("SmDm",(function(e){this.primaryRChromaticity_x=e.readUint16(),this.primaryRChromaticity_y=e.readUint16(),this.primaryGChromaticity_x=e.readUint16(),this.primaryGChromaticity_y=e.readUint16(),this.primaryBChromaticity_x=e.readUint16(),this.primaryBChromaticity_y=e.readUint16(),this.whitePointChromaticity_x=e.readUint16(),this.whitePointChromaticity_y=e.readUint16(),this.luminanceMax=e.readUint32(),this.luminanceMin=e.readUint32()})),h.createFullBoxCtor("smhd",(function(e){this.balance=e.readUint16(),e.readUint16()})),h.createFullBoxCtor("ssix",(function(e){this.subsegments=[];for(var t=e.readUint32(),r=0;r<t;r++){var i={};this.subsegments.push(i),i.ranges=[];for(var n=e.readUint32(),s=0;s<n;s++){var a={};i.ranges.push(a),a.level=e.readUint8(),a.range_size=e.readUint24()}}})),h.createFullBoxCtor("stco",(function(e){var t;if(t=e.readUint32(),this.chunk_offsets=[],0===this.version)for(var r=0;r<t;r++)this.chunk_offsets.push(e.readUint32())})),h.createFullBoxCtor("stdp",(function(e){var t=(this.size-this.hdr_size)/2;this.priority=[];for(var r=0;r<t;r++)this.priority[r]=e.readUint16()})),h.createFullBoxCtor("sthd"),h.createFullBoxCtor("stri",(function(e){this.switch_group=e.readUint16(),this.alternate_group=e.readUint16(),this.sub_track_id=e.readUint32();var t=(this.size-this.hdr_size-8)/4;this.attribute_list=[];for(var r=0;r<t;r++)this.attribute_list[r]=e.readUint32()})),h.createFullBoxCtor("stsc",(function(e){var t,r;if(t=e.readUint32(),this.first_chunk=[],this.samples_per_chunk=[],this.sample_description_index=[],0===this.version)for(r=0;r<t;r++)this.first_chunk.push(e.readUint32()),this.samples_per_chunk.push(e.readUint32()),this.sample_description_index.push(e.readUint32())})),h.createFullBoxCtor("stsd",(function(e){var t,r,i,n;for(this.entries=[],i=e.readUint32(),t=1;t<=i;t++){if((r=h.parseOneBox(e,!0,this.size-(e.getPosition()-this.start))).code!==h.OK)return;h[r.type+"SampleEntry"]?((n=new h[r.type+"SampleEntry"](r.size)).hdr_size=r.hdr_size,n.start=r.start):(a.warn("BoxParser","Unknown sample entry type: "+r.type),n=new h.SampleEntry(r.type,r.size,r.hdr_size,r.start)),n.write===h.SampleEntry.prototype.write&&(a.info("BoxParser","SampleEntry "+n.type+" box writing not yet implemented, keeping unparsed data in memory for later write"),n.parseDataAndRewind(e)),n.parse(e),this.entries.push(n)}})),h.createFullBoxCtor("stsg",(function(e){this.grouping_type=e.readUint32();var t=e.readUint16();this.group_description_index=[];for(var r=0;r<t;r++)this.group_description_index[r]=e.readUint32()})),h.createFullBoxCtor("stsh",(function(e){var t,r;if(t=e.readUint32(),this.shadowed_sample_numbers=[],this.sync_sample_numbers=[],0===this.version)for(r=0;r<t;r++)this.shadowed_sample_numbers.push(e.readUint32()),this.sync_sample_numbers.push(e.readUint32())})),h.createFullBoxCtor("stss",(function(e){var t,r;if(r=e.readUint32(),0===this.version)for(this.sample_numbers=[],t=0;t<r;t++)this.sample_numbers.push(e.readUint32())})),h.createFullBoxCtor("stsz",(function(e){var t;if(this.sample_sizes=[],0===this.version)for(this.sample_size=e.readUint32(),this.sample_count=e.readUint32(),t=0;t<this.sample_count;t++)0===this.sample_size?this.sample_sizes.push(e.readUint32()):this.sample_sizes[t]=this.sample_size})),h.createFullBoxCtor("stts",(function(e){var t,r,i;if(t=e.readUint32(),this.sample_counts=[],this.sample_deltas=[],0===this.version)for(r=0;r<t;r++)this.sample_counts.push(e.readUint32()),(i=e.readInt32())<0&&(a.warn("BoxParser","File uses negative stts sample delta, using value 1 instead, sync may be lost!"),i=1),this.sample_deltas.push(i)})),h.createFullBoxCtor("stvi",(function(e){var t=e.readUint32();this.single_view_allowed=3&t,this.stereo_scheme=e.readUint32();var r,i,n=e.readUint32();for(this.stereo_indication_type=e.readString(n),this.boxes=[];e.getPosition()<this.start+this.size;){if((r=h.parseOneBox(e,!1,this.size-(e.getPosition()-this.start))).code!==h.OK)return;i=r.box,this.boxes.push(i),this[i.type]=i}})),h.createBoxCtor("styp",(function(e){h.ftypBox.prototype.parse.call(this,e)})),h.createFullBoxCtor("stz2",(function(e){var t,r;if(this.sample_sizes=[],0===this.version)if(this.reserved=e.readUint24(),this.field_size=e.readUint8(),r=e.readUint32(),4===this.field_size)for(t=0;t<r;t+=2){var i=e.readUint8();this.sample_sizes[t]=i>>4&15,this.sample_sizes[t+1]=15&i}else if(8===this.field_size)for(t=0;t<r;t++)this.sample_sizes[t]=e.readUint8();else if(16===this.field_size)for(t=0;t<r;t++)this.sample_sizes[t]=e.readUint16();else a.error("BoxParser","Error in length field in stz2 box")})),h.createFullBoxCtor("subs",(function(e){var t,r,i,n;for(i=e.readUint32(),this.entries=[],t=0;t<i;t++){var s={};if(this.entries[t]=s,s.sample_delta=e.readUint32(),s.subsamples=[],(n=e.readUint16())>0)for(r=0;r<n;r++){var a={};s.subsamples.push(a),1==this.version?a.size=e.readUint32():a.size=e.readUint16(),a.priority=e.readUint8(),a.discardable=e.readUint8(),a.codec_specific_parameters=e.readUint32()}}})),h.createFullBoxCtor("tenc",(function(e){if(e.readUint8(),0===this.version)e.readUint8();else{var t=e.readUint8();this.default_crypt_byte_block=t>>4&15,this.default_skip_byte_block=15&t}this.default_isProtected=e.readUint8(),this.default_Per_Sample_IV_Size=e.readUint8(),this.default_KID=h.parseHex16(e),1===this.default_isProtected&&0===this.default_Per_Sample_IV_Size&&(this.default_constant_IV_size=e.readUint8(),this.default_constant_IV=e.readUint8Array(this.default_constant_IV_size))})),h.createFullBoxCtor("tfdt",(function(e){1==this.version?this.baseMediaDecodeTime=e.readUint64():this.baseMediaDecodeTime=e.readUint32()})),h.createFullBoxCtor("tfhd",(function(e){var t=0;this.track_id=e.readUint32(),this.size-this.hdr_size>t&&this.flags&h.TFHD_FLAG_BASE_DATA_OFFSET?(this.base_data_offset=e.readUint64(),t+=8):this.base_data_offset=0,this.size-this.hdr_size>t&&this.flags&h.TFHD_FLAG_SAMPLE_DESC?(this.default_sample_description_index=e.readUint32(),t+=4):this.default_sample_description_index=0,this.size-this.hdr_size>t&&this.flags&h.TFHD_FLAG_SAMPLE_DUR?(this.default_sample_duration=e.readUint32(),t+=4):this.default_sample_duration=0,this.size-this.hdr_size>t&&this.flags&h.TFHD_FLAG_SAMPLE_SIZE?(this.default_sample_size=e.readUint32(),t+=4):this.default_sample_size=0,this.size-this.hdr_size>t&&this.flags&h.TFHD_FLAG_SAMPLE_FLAGS?(this.default_sample_flags=e.readUint32(),t+=4):this.default_sample_flags=0})),h.createFullBoxCtor("tfra",(function(e){this.track_ID=e.readUint32(),e.readUint24();var t=e.readUint8();this.length_size_of_traf_num=t>>4&3,this.length_size_of_trun_num=t>>2&3,this.length_size_of_sample_num=3&t,this.entries=[];for(var r=e.readUint32(),i=0;i<r;i++)1===this.version?(this.time=e.readUint64(),this.moof_offset=e.readUint64()):(this.time=e.readUint32(),this.moof_offset=e.readUint32()),this.traf_number=e["readUint"+8*(this.length_size_of_traf_num+1)](),this.trun_number=e["readUint"+8*(this.length_size_of_trun_num+1)](),this.sample_number=e["readUint"+8*(this.length_size_of_sample_num+1)]()})),h.createFullBoxCtor("tkhd",(function(e){1==this.version?(this.creation_time=e.readUint64(),this.modification_time=e.readUint64(),this.track_id=e.readUint32(),e.readUint32(),this.duration=e.readUint64()):(this.creation_time=e.readUint32(),this.modification_time=e.readUint32(),this.track_id=e.readUint32(),e.readUint32(),this.duration=e.readUint32()),e.readUint32Array(2),this.layer=e.readInt16(),this.alternate_group=e.readInt16(),this.volume=e.readInt16()>>8,e.readUint16(),this.matrix=e.readInt32Array(9),this.width=e.readUint32(),this.height=e.readUint32()})),h.createBoxCtor("tmax",(function(e){this.time=e.readUint32()})),h.createBoxCtor("tmin",(function(e){this.time=e.readUint32()})),h.createBoxCtor("totl",(function(e){this.bytessent=e.readUint32()})),h.createBoxCtor("tpay",(function(e){this.bytessent=e.readUint32()})),h.createBoxCtor("tpyl",(function(e){this.bytessent=e.readUint64()})),h.TrackGroupTypeBox.prototype.parse=function(e){this.parseFullHeader(e),this.track_group_id=e.readUint32()},h.createTrackGroupCtor("msrc"),h.TrackReferenceTypeBox=function(e,t,r,i){h.Box.call(this,e,t),this.hdr_size=r,this.start=i},h.TrackReferenceTypeBox.prototype=new h.Box,h.TrackReferenceTypeBox.prototype.parse=function(e){this.track_ids=e.readUint32Array((this.size-this.hdr_size)/4)},h.trefBox.prototype.parse=function(e){for(var t,r;e.getPosition()<this.start+this.size;){if((t=h.parseOneBox(e,!0,this.size-(e.getPosition()-this.start))).code!==h.OK)return;(r=new h.TrackReferenceTypeBox(t.type,t.size,t.hdr_size,t.start)).write===h.Box.prototype.write&&"mdat"!==r.type&&(a.info("BoxParser","TrackReference "+r.type+" box writing not yet implemented, keeping unparsed data in memory for later write"),r.parseDataAndRewind(e)),r.parse(e),this.boxes.push(r)}},h.createFullBoxCtor("trep",(function(e){for(this.track_ID=e.readUint32(),this.boxes=[];e.getPosition()<this.start+this.size;){if(ret=h.parseOneBox(e,!1,this.size-(e.getPosition()-this.start)),ret.code!==h.OK)return;box=ret.box,this.boxes.push(box)}})),h.createFullBoxCtor("trex",(function(e){this.track_id=e.readUint32(),this.default_sample_description_index=e.readUint32(),this.default_sample_duration=e.readUint32(),this.default_sample_size=e.readUint32(),this.default_sample_flags=e.readUint32()})),h.createBoxCtor("trpy",(function(e){this.bytessent=e.readUint64()})),h.createFullBoxCtor("trun",(function(e){var t=0;if(this.sample_count=e.readUint32(),t+=4,this.size-this.hdr_size>t&&this.flags&h.TRUN_FLAGS_DATA_OFFSET?(this.data_offset=e.readInt32(),t+=4):this.data_offset=0,this.size-this.hdr_size>t&&this.flags&h.TRUN_FLAGS_FIRST_FLAG?(this.first_sample_flags=e.readUint32(),t+=4):this.first_sample_flags=0,this.sample_duration=[],this.sample_size=[],this.sample_flags=[],this.sample_composition_time_offset=[],this.size-this.hdr_size>t)for(var r=0;r<this.sample_count;r++)this.flags&h.TRUN_FLAGS_DURATION&&(this.sample_duration[r]=e.readUint32()),this.flags&h.TRUN_FLAGS_SIZE&&(this.sample_size[r]=e.readUint32()),this.flags&h.TRUN_FLAGS_FLAGS&&(this.sample_flags[r]=e.readUint32()),this.flags&h.TRUN_FLAGS_CTS_OFFSET&&(0===this.version?this.sample_composition_time_offset[r]=e.readUint32():this.sample_composition_time_offset[r]=e.readInt32())})),h.createFullBoxCtor("tsel",(function(e){this.switch_group=e.readUint32();var t=(this.size-this.hdr_size-4)/4;this.attribute_list=[];for(var r=0;r<t;r++)this.attribute_list[r]=e.readUint32()})),h.createFullBoxCtor("txtC",(function(e){this.config=e.readCString()})),h.createBoxCtor("tyco",(function(e){var t=(this.size-this.hdr_size)/4;this.compatible_brands=[];for(var r=0;r<t;r++)this.compatible_brands[r]=e.readString(4)})),h.createFullBoxCtor("udes",(function(e){this.lang=e.readCString(),this.name=e.readCString(),this.description=e.readCString(),this.tags=e.readCString()})),h.createFullBoxCtor("uncC",(function(e){var t;if(this.profile=e.readUint32(),1==this.version);else if(0==this.version){for(this.component_count=e.readUint32(),this.component_index=[],this.component_bit_depth_minus_one=[],this.component_format=[],this.component_align_size=[],t=0;t<this.component_count;t++)this.component_index.push(e.readUint16()),this.component_bit_depth_minus_one.push(e.readUint8()),this.component_format.push(e.readUint8()),this.component_align_size.push(e.readUint8());this.sampling_type=e.readUint8(),this.interleave_type=e.readUint8(),this.block_size=e.readUint8();var r=e.readUint8();this.component_little_endian=r>>7&1,this.block_pad_lsb=r>>6&1,this.block_little_endian=r>>5&1,this.block_reversed=r>>4&1,this.pad_unknown=r>>3&1,this.pixel_size=e.readUint32(),this.row_align_size=e.readUint32(),this.tile_align_size=e.readUint32(),this.num_tile_cols_minus_one=e.readUint32(),this.num_tile_rows_minus_one=e.readUint32()}})),h.createFullBoxCtor("url ",(function(e){1!==this.flags&&(this.location=e.readCString())})),h.createFullBoxCtor("urn ",(function(e){this.name=e.readCString(),this.size-this.hdr_size-this.name.length-1>0&&(this.location=e.readCString())})),h.createUUIDBox("********************************",!0,!1,(function(e){this.LiveServerManifest=e.readString(this.size-this.hdr_size).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")})),h.createUUIDBox("********************************",!0,!1,(function(e){this.system_id=h.parseHex16(e);var t=e.readUint32();t>0&&(this.data=e.readUint8Array(t))})),h.createUUIDBox("********************************",!0,!1),h.createUUIDBox("********************************",!0,!1,(function(e){this.default_AlgorithmID=e.readUint24(),this.default_IV_size=e.readUint8(),this.default_KID=h.parseHex16(e)})),h.createUUIDBox("********************************",!0,!1,(function(e){this.fragment_count=e.readUint8(),this.entries=[];for(var t=0;t<this.fragment_count;t++){var r={},i=0,n=0;1===this.version?(i=e.readUint64(),n=e.readUint64()):(i=e.readUint32(),n=e.readUint32()),r.absolute_time=i,r.absolute_duration=n,this.entries.push(r)}})),h.createUUIDBox("********************************",!0,!1,(function(e){1===this.version?(this.absolute_time=e.readUint64(),this.duration=e.readUint64()):(this.absolute_time=e.readUint32(),this.duration=e.readUint32())})),h.createFullBoxCtor("vmhd",(function(e){this.graphicsmode=e.readUint16(),this.opcolor=e.readUint16Array(3)})),h.createFullBoxCtor("vpcC",(function(e){var t;1===this.version?(this.profile=e.readUint8(),this.level=e.readUint8(),t=e.readUint8(),this.bitDepth=t>>4,this.chromaSubsampling=t>>1&7,this.videoFullRangeFlag=1&t,this.colourPrimaries=e.readUint8(),this.transferCharacteristics=e.readUint8(),this.matrixCoefficients=e.readUint8(),this.codecIntializationDataSize=e.readUint16(),this.codecIntializationData=e.readUint8Array(this.codecIntializationDataSize)):(this.profile=e.readUint8(),this.level=e.readUint8(),t=e.readUint8(),this.bitDepth=t>>4&15,this.colorSpace=15&t,t=e.readUint8(),this.chromaSubsampling=t>>4&15,this.transferFunction=t>>1&7,this.videoFullRangeFlag=1&t,this.codecIntializationDataSize=e.readUint16(),this.codecIntializationData=e.readUint8Array(this.codecIntializationDataSize))})),h.createBoxCtor("vttC",(function(e){this.text=e.readString(this.size-this.hdr_size)})),h.createFullBoxCtor("vvcC",(function(e){var t,r,i={held_bits:void 0,num_held_bits:0,stream_read_1_bytes:function(e){this.held_bits=e.readUint8(),this.num_held_bits=8},stream_read_2_bytes:function(e){this.held_bits=e.readUint16(),this.num_held_bits=16},extract_bits:function(e){var t=this.held_bits>>this.num_held_bits-e&(1<<e)-1;return this.num_held_bits-=e,t}};if(i.stream_read_1_bytes(e),i.extract_bits(5),this.lengthSizeMinusOne=i.extract_bits(2),this.ptl_present_flag=i.extract_bits(1),this.ptl_present_flag){if(i.stream_read_2_bytes(e),this.ols_idx=i.extract_bits(9),this.num_sublayers=i.extract_bits(3),this.constant_frame_rate=i.extract_bits(2),this.chroma_format_idc=i.extract_bits(2),i.stream_read_1_bytes(e),this.bit_depth_minus8=i.extract_bits(3),i.extract_bits(5),i.stream_read_2_bytes(e),i.extract_bits(2),this.num_bytes_constraint_info=i.extract_bits(6),this.general_profile_idc=i.extract_bits(7),this.general_tier_flag=i.extract_bits(1),this.general_level_idc=e.readUint8(),i.stream_read_1_bytes(e),this.ptl_frame_only_constraint_flag=i.extract_bits(1),this.ptl_multilayer_enabled_flag=i.extract_bits(1),this.general_constraint_info=new Uint8Array(this.num_bytes_constraint_info),this.num_bytes_constraint_info){for(t=0;t<this.num_bytes_constraint_info-1;t++){var n=i.extract_bits(6);i.stream_read_1_bytes(e);var s=i.extract_bits(2);this.general_constraint_info[t]=n<<2|s}this.general_constraint_info[this.num_bytes_constraint_info-1]=i.extract_bits(6)}else i.extract_bits(6);if(this.num_sublayers>1){for(i.stream_read_1_bytes(e),this.ptl_sublayer_present_mask=0,r=this.num_sublayers-2;r>=0;--r){var a=i.extract_bits(1);this.ptl_sublayer_present_mask|=a<<r}for(r=this.num_sublayers;r<=8&&this.num_sublayers>1;++r)i.extract_bits(1);for(this.sublayer_level_idc=[],r=this.num_sublayers-2;r>=0;--r)this.ptl_sublayer_present_mask&1<<r&&(this.sublayer_level_idc[r]=e.readUint8())}if(this.ptl_num_sub_profiles=e.readUint8(),this.general_sub_profile_idc=[],this.ptl_num_sub_profiles)for(t=0;t<this.ptl_num_sub_profiles;t++)this.general_sub_profile_idc.push(e.readUint32());this.max_picture_width=e.readUint16(),this.max_picture_height=e.readUint16(),this.avg_frame_rate=e.readUint16()}this.nalu_arrays=[];var o=e.readUint8();for(t=0;t<o;t++){var d=[];this.nalu_arrays.push(d),i.stream_read_1_bytes(e),d.completeness=i.extract_bits(1),i.extract_bits(2),d.nalu_type=i.extract_bits(5);var l=1;for(13!=d.nalu_type&&12!=d.nalu_type&&(l=e.readUint16()),r=0;r<l;r++){var c=e.readUint16();d.push({data:e.readUint8Array(c),length:c})}}})),h.createFullBoxCtor("vvnC",(function(e){var t=strm.readUint8();this.lengthSizeMinusOne=3&t})),h.SampleEntry.prototype.isVideo=function(){return!1},h.SampleEntry.prototype.isAudio=function(){return!1},h.SampleEntry.prototype.isSubtitle=function(){return!1},h.SampleEntry.prototype.isMetadata=function(){return!1},h.SampleEntry.prototype.isHint=function(){return!1},h.SampleEntry.prototype.getCodec=function(){return this.type.replace(".","")},h.SampleEntry.prototype.getWidth=function(){return""},h.SampleEntry.prototype.getHeight=function(){return""},h.SampleEntry.prototype.getChannelCount=function(){return""},h.SampleEntry.prototype.getSampleRate=function(){return""},h.SampleEntry.prototype.getSampleSize=function(){return""},h.VisualSampleEntry.prototype.isVideo=function(){return!0},h.VisualSampleEntry.prototype.getWidth=function(){return this.width},h.VisualSampleEntry.prototype.getHeight=function(){return this.height},h.AudioSampleEntry.prototype.isAudio=function(){return!0},h.AudioSampleEntry.prototype.getChannelCount=function(){return this.channel_count},h.AudioSampleEntry.prototype.getSampleRate=function(){return this.samplerate},h.AudioSampleEntry.prototype.getSampleSize=function(){return this.samplesize},h.SubtitleSampleEntry.prototype.isSubtitle=function(){return!0},h.MetadataSampleEntry.prototype.isMetadata=function(){return!0},h.decimalToHex=function(e,t){var r=Number(e).toString(16);for(t=null==t?t=2:t;r.length<t;)r="0"+r;return r},h.avc1SampleEntry.prototype.getCodec=h.avc2SampleEntry.prototype.getCodec=h.avc3SampleEntry.prototype.getCodec=h.avc4SampleEntry.prototype.getCodec=function(){var e=h.SampleEntry.prototype.getCodec.call(this);return this.avcC?e+"."+h.decimalToHex(this.avcC.AVCProfileIndication)+h.decimalToHex(this.avcC.profile_compatibility)+h.decimalToHex(this.avcC.AVCLevelIndication):e},h.hev1SampleEntry.prototype.getCodec=h.hvc1SampleEntry.prototype.getCodec=function(){var e,t=h.SampleEntry.prototype.getCodec.call(this);if(this.hvcC){switch(t+=".",this.hvcC.general_profile_space){case 0:t+="";break;case 1:t+="A";break;case 2:t+="B";break;case 3:t+="C"}t+=this.hvcC.general_profile_idc,t+=".";var r=this.hvcC.general_profile_compatibility,i=0;for(e=0;e<32&&(i|=1&r,31!=e);e++)i<<=1,r>>=1;t+=h.decimalToHex(i,0),t+=".",0===this.hvcC.general_tier_flag?t+="L":t+="H",t+=this.hvcC.general_level_idc;var n=!1,s="";for(e=5;e>=0;e--)(this.hvcC.general_constraint_indicator[e]||n)&&(s="."+h.decimalToHex(this.hvcC.general_constraint_indicator[e],0)+s,n=!0);t+=s}return t},h.vvc1SampleEntry.prototype.getCodec=h.vvi1SampleEntry.prototype.getCodec=function(){var e,t=h.SampleEntry.prototype.getCodec.call(this);if(this.vvcC){t+="."+this.vvcC.general_profile_idc,this.vvcC.general_tier_flag?t+=".H":t+=".L",t+=this.vvcC.general_level_idc;var r="";if(this.vvcC.general_constraint_info){var i,n=[],s=0;for(s|=this.vvcC.ptl_frame_only_constraint<<7,s|=this.vvcC.ptl_multilayer_enabled<<6,e=0;e<this.vvcC.general_constraint_info.length;++e)s|=this.vvcC.general_constraint_info[e]>>2&63,n.push(s),s&&(i=e),s=this.vvcC.general_constraint_info[e]>>2&3;if(void 0===i)r=".CA";else{r=".C";var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",o=0,d=0;for(e=0;e<=i;++e)for(o=o<<8|n[e],d+=8;d>=5;){r+=a[o>>d-5&31],o&=(1<<(d-=5))-1}d&&(r+=a[31&(o<<=5-d)])}}t+=r}return t},h.mp4aSampleEntry.prototype.getCodec=function(){var e=h.SampleEntry.prototype.getCodec.call(this);if(this.esds&&this.esds.esd){var t=this.esds.esd.getOTI(),r=this.esds.esd.getAudioConfig();return e+"."+h.decimalToHex(t)+(r?"."+r:"")}return e},h.stxtSampleEntry.prototype.getCodec=function(){var e=h.SampleEntry.prototype.getCodec.call(this);return this.mime_format?e+"."+this.mime_format:e},h.vp08SampleEntry.prototype.getCodec=h.vp09SampleEntry.prototype.getCodec=function(){var e=h.SampleEntry.prototype.getCodec.call(this),t=this.vpcC.level;0==t&&(t="00");var r=this.vpcC.bitDepth;return 8==r&&(r="08"),e+".0"+this.vpcC.profile+"."+t+"."+r},h.av01SampleEntry.prototype.getCodec=function(){var e,t=h.SampleEntry.prototype.getCodec.call(this),r=this.av1C.seq_level_idx_0;return r<10&&(r="0"+r),2===this.av1C.seq_profile&&1===this.av1C.high_bitdepth?e=1===this.av1C.twelve_bit?"12":"10":this.av1C.seq_profile<=2&&(e=1===this.av1C.high_bitdepth?"10":"08"),t+"."+this.av1C.seq_profile+"."+r+(this.av1C.seq_tier_0?"H":"M")+"."+e},h.Box.prototype.writeHeader=function(e,t){this.size+=8,this.size>l&&(this.size+=8),"uuid"===this.type&&(this.size+=16),a.debug("BoxWriter","Writing box "+this.type+" of size: "+this.size+" at position "+e.getPosition()+(t||"")),this.size>l?e.writeUint32(1):(this.sizePosition=e.getPosition(),e.writeUint32(this.size)),e.writeString(this.type,null,4),"uuid"===this.type&&e.writeUint8Array(this.uuid),this.size>l&&e.writeUint64(this.size)},h.FullBox.prototype.writeHeader=function(e){this.size+=4,h.Box.prototype.writeHeader.call(this,e," v="+this.version+" f="+this.flags),e.writeUint8(this.version),e.writeUint24(this.flags)},h.Box.prototype.write=function(e){"mdat"===this.type?this.data&&(this.size=this.data.length,this.writeHeader(e),e.writeUint8Array(this.data)):(this.size=this.data?this.data.length:0,this.writeHeader(e),this.data&&e.writeUint8Array(this.data))},h.ContainerBox.prototype.write=function(e){this.size=0,this.writeHeader(e);for(var t=0;t<this.boxes.length;t++)this.boxes[t]&&(this.boxes[t].write(e),this.size+=this.boxes[t].size);a.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},h.TrackReferenceTypeBox.prototype.write=function(e){this.size=4*this.track_ids.length,this.writeHeader(e),e.writeUint32Array(this.track_ids)},h.avcCBox.prototype.write=function(e){var t;for(this.size=7,t=0;t<this.SPS.length;t++)this.size+=2+this.SPS[t].length;for(t=0;t<this.PPS.length;t++)this.size+=2+this.PPS[t].length;for(this.ext&&(this.size+=this.ext.length),this.writeHeader(e),e.writeUint8(this.configurationVersion),e.writeUint8(this.AVCProfileIndication),e.writeUint8(this.profile_compatibility),e.writeUint8(this.AVCLevelIndication),e.writeUint8(this.lengthSizeMinusOne+252),e.writeUint8(this.SPS.length+224),t=0;t<this.SPS.length;t++)e.writeUint16(this.SPS[t].length),e.writeUint8Array(this.SPS[t].nalu);for(e.writeUint8(this.PPS.length),t=0;t<this.PPS.length;t++)e.writeUint16(this.PPS[t].length),e.writeUint8Array(this.PPS[t].nalu);this.ext&&e.writeUint8Array(this.ext)},h.co64Box.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.chunk_offsets.length,this.writeHeader(e),e.writeUint32(this.chunk_offsets.length),t=0;t<this.chunk_offsets.length;t++)e.writeUint64(this.chunk_offsets[t])},h.cslgBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=20,this.writeHeader(e),e.writeInt32(this.compositionToDTSShift),e.writeInt32(this.leastDecodeToDisplayDelta),e.writeInt32(this.greatestDecodeToDisplayDelta),e.writeInt32(this.compositionStartTime),e.writeInt32(this.compositionEndTime)},h.cttsBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.sample_counts.length,this.writeHeader(e),e.writeUint32(this.sample_counts.length),t=0;t<this.sample_counts.length;t++)e.writeUint32(this.sample_counts[t]),1===this.version?e.writeInt32(this.sample_offsets[t]):e.writeUint32(this.sample_offsets[t])},h.drefBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4,this.writeHeader(e),e.writeUint32(this.entries.length);for(var t=0;t<this.entries.length;t++)this.entries[t].write(e),this.size+=this.entries[t].size;a.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},h.elngBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=this.extended_language.length,this.writeHeader(e),e.writeString(this.extended_language)},h.elstBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4+12*this.entries.length,this.writeHeader(e),e.writeUint32(this.entries.length);for(var t=0;t<this.entries.length;t++){var r=this.entries[t];e.writeUint32(r.segment_duration),e.writeInt32(r.media_time),e.writeInt16(r.media_rate_integer),e.writeInt16(r.media_rate_fraction)}},h.emsgBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=16+this.message_data.length+(this.scheme_id_uri.length+1)+(this.value.length+1),this.writeHeader(e),e.writeCString(this.scheme_id_uri),e.writeCString(this.value),e.writeUint32(this.timescale),e.writeUint32(this.presentation_time_delta),e.writeUint32(this.event_duration),e.writeUint32(this.id),e.writeUint8Array(this.message_data)},h.ftypBox.prototype.write=function(e){this.size=8+4*this.compatible_brands.length,this.writeHeader(e),e.writeString(this.major_brand,null,4),e.writeUint32(this.minor_version);for(var t=0;t<this.compatible_brands.length;t++)e.writeString(this.compatible_brands[t],null,4)},h.hdlrBox.prototype.write=function(e){this.size=20+this.name.length+1,this.version=0,this.flags=0,this.writeHeader(e),e.writeUint32(0),e.writeString(this.handler,null,4),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeCString(this.name)},h.hvcCBox.prototype.write=function(e){var t,r;for(this.size=23,t=0;t<this.nalu_arrays.length;t++)for(this.size+=3,r=0;r<this.nalu_arrays[t].length;r++)this.size+=2+this.nalu_arrays[t][r].data.length;for(this.writeHeader(e),e.writeUint8(this.configurationVersion),e.writeUint8(this.general_profile_space<<6+this.general_tier_flag<<5+this.general_profile_idc),e.writeUint32(this.general_profile_compatibility),e.writeUint8Array(this.general_constraint_indicator),e.writeUint8(this.general_level_idc),e.writeUint16(this.min_spatial_segmentation_idc+(15<<24)),e.writeUint8(this.parallelismType+252),e.writeUint8(this.chroma_format_idc+252),e.writeUint8(this.bit_depth_luma_minus8+248),e.writeUint8(this.bit_depth_chroma_minus8+248),e.writeUint16(this.avgFrameRate),e.writeUint8((this.constantFrameRate<<6)+(this.numTemporalLayers<<3)+(this.temporalIdNested<<2)+this.lengthSizeMinusOne),e.writeUint8(this.nalu_arrays.length),t=0;t<this.nalu_arrays.length;t++)for(e.writeUint8((this.nalu_arrays[t].completeness<<7)+this.nalu_arrays[t].nalu_type),e.writeUint16(this.nalu_arrays[t].length),r=0;r<this.nalu_arrays[t].length;r++)e.writeUint16(this.nalu_arrays[t][r].data.length),e.writeUint8Array(this.nalu_arrays[t][r].data)},h.kindBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=this.schemeURI.length+1+(this.value.length+1),this.writeHeader(e),e.writeCString(this.schemeURI),e.writeCString(this.value)},h.mdhdBox.prototype.write=function(e){this.size=20,this.flags=0,this.version=0,this.writeHeader(e),e.writeUint32(this.creation_time),e.writeUint32(this.modification_time),e.writeUint32(this.timescale),e.writeUint32(this.duration),e.writeUint16(this.language),e.writeUint16(0)},h.mehdBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4,this.writeHeader(e),e.writeUint32(this.fragment_duration)},h.mfhdBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4,this.writeHeader(e),e.writeUint32(this.sequence_number)},h.mvhdBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=96,this.writeHeader(e),e.writeUint32(this.creation_time),e.writeUint32(this.modification_time),e.writeUint32(this.timescale),e.writeUint32(this.duration),e.writeUint32(this.rate),e.writeUint16(this.volume<<8),e.writeUint16(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32Array(this.matrix),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(this.next_track_id)},h.SampleEntry.prototype.writeHeader=function(e){this.size=8,h.Box.prototype.writeHeader.call(this,e),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint16(this.data_reference_index)},h.SampleEntry.prototype.writeFooter=function(e){for(var t=0;t<this.boxes.length;t++)this.boxes[t].write(e),this.size+=this.boxes[t].size;a.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},h.SampleEntry.prototype.write=function(e){this.writeHeader(e),e.writeUint8Array(this.data),this.size+=this.data.length,a.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},h.VisualSampleEntry.prototype.write=function(e){this.writeHeader(e),this.size+=70,e.writeUint16(0),e.writeUint16(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint16(this.width),e.writeUint16(this.height),e.writeUint32(this.horizresolution),e.writeUint32(this.vertresolution),e.writeUint32(0),e.writeUint16(this.frame_count),e.writeUint8(Math.min(31,this.compressorname.length)),e.writeString(this.compressorname,null,31),e.writeUint16(this.depth),e.writeInt16(-1),this.writeFooter(e)},h.AudioSampleEntry.prototype.write=function(e){this.writeHeader(e),this.size+=20,e.writeUint32(0),e.writeUint32(0),e.writeUint16(this.channel_count),e.writeUint16(this.samplesize),e.writeUint16(0),e.writeUint16(0),e.writeUint32(this.samplerate<<16),this.writeFooter(e)},h.stppSampleEntry.prototype.write=function(e){this.writeHeader(e),this.size+=this.namespace.length+1+this.schema_location.length+1+this.auxiliary_mime_types.length+1,e.writeCString(this.namespace),e.writeCString(this.schema_location),e.writeCString(this.auxiliary_mime_types),this.writeFooter(e)},h.SampleGroupEntry.prototype.write=function(e){e.writeUint8Array(this.data)},h.sbgpBox.prototype.write=function(e){this.version=1,this.flags=0,this.size=12+8*this.entries.length,this.writeHeader(e),e.writeString(this.grouping_type,null,4),e.writeUint32(this.grouping_type_parameter),e.writeUint32(this.entries.length);for(var t=0;t<this.entries.length;t++){var r=this.entries[t];e.writeInt32(r.sample_count),e.writeInt32(r.group_description_index)}},h.sgpdBox.prototype.write=function(e){var t,r;for(this.flags=0,this.size=12,t=0;t<this.entries.length;t++)r=this.entries[t],1===this.version&&(0===this.default_length&&(this.size+=4),this.size+=r.data.length);for(this.writeHeader(e),e.writeString(this.grouping_type,null,4),1===this.version&&e.writeUint32(this.default_length),this.version>=2&&e.writeUint32(this.default_sample_description_index),e.writeUint32(this.entries.length),t=0;t<this.entries.length;t++)r=this.entries[t],1===this.version&&0===this.default_length&&e.writeUint32(r.description_length),r.write(e)},h.sidxBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=20+12*this.references.length,this.writeHeader(e),e.writeUint32(this.reference_ID),e.writeUint32(this.timescale),e.writeUint32(this.earliest_presentation_time),e.writeUint32(this.first_offset),e.writeUint16(0),e.writeUint16(this.references.length);for(var t=0;t<this.references.length;t++){var r=this.references[t];e.writeUint32(r.reference_type<<31|r.referenced_size),e.writeUint32(r.subsegment_duration),e.writeUint32(r.starts_with_SAP<<31|r.SAP_type<<28|r.SAP_delta_time)}},h.smhdBox.prototype.write=function(e){this.version=0,this.flags=1,this.size=4,this.writeHeader(e),e.writeUint16(this.balance),e.writeUint16(0)},h.stcoBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4+4*this.chunk_offsets.length,this.writeHeader(e),e.writeUint32(this.chunk_offsets.length),e.writeUint32Array(this.chunk_offsets)},h.stscBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+12*this.first_chunk.length,this.writeHeader(e),e.writeUint32(this.first_chunk.length),t=0;t<this.first_chunk.length;t++)e.writeUint32(this.first_chunk[t]),e.writeUint32(this.samples_per_chunk[t]),e.writeUint32(this.sample_description_index[t])},h.stsdBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=0,this.writeHeader(e),e.writeUint32(this.entries.length),this.size+=4,t=0;t<this.entries.length;t++)this.entries[t].write(e),this.size+=this.entries[t].size;a.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},h.stshBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.shadowed_sample_numbers.length,this.writeHeader(e),e.writeUint32(this.shadowed_sample_numbers.length),t=0;t<this.shadowed_sample_numbers.length;t++)e.writeUint32(this.shadowed_sample_numbers[t]),e.writeUint32(this.sync_sample_numbers[t])},h.stssBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4+4*this.sample_numbers.length,this.writeHeader(e),e.writeUint32(this.sample_numbers.length),e.writeUint32Array(this.sample_numbers)},h.stszBox.prototype.write=function(e){var t,r=!0;if(this.version=0,this.flags=0,this.sample_sizes.length>0)for(t=0;t+1<this.sample_sizes.length;){if(this.sample_sizes[t+1]!==this.sample_sizes[0]){r=!1;break}t++}else r=!1;this.size=8,r||(this.size+=4*this.sample_sizes.length),this.writeHeader(e),r?e.writeUint32(this.sample_sizes[0]):e.writeUint32(0),e.writeUint32(this.sample_sizes.length),r||e.writeUint32Array(this.sample_sizes)},h.sttsBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.sample_counts.length,this.writeHeader(e),e.writeUint32(this.sample_counts.length),t=0;t<this.sample_counts.length;t++)e.writeUint32(this.sample_counts[t]),e.writeUint32(this.sample_deltas[t])},h.tfdtBox.prototype.write=function(e){var t=Math.pow(2,32)-1;this.version=this.baseMediaDecodeTime>t?1:0,this.flags=0,this.size=4,1===this.version&&(this.size+=4),this.writeHeader(e),1===this.version?e.writeUint64(this.baseMediaDecodeTime):e.writeUint32(this.baseMediaDecodeTime)},h.tfhdBox.prototype.write=function(e){this.version=0,this.size=4,this.flags&h.TFHD_FLAG_BASE_DATA_OFFSET&&(this.size+=8),this.flags&h.TFHD_FLAG_SAMPLE_DESC&&(this.size+=4),this.flags&h.TFHD_FLAG_SAMPLE_DUR&&(this.size+=4),this.flags&h.TFHD_FLAG_SAMPLE_SIZE&&(this.size+=4),this.flags&h.TFHD_FLAG_SAMPLE_FLAGS&&(this.size+=4),this.writeHeader(e),e.writeUint32(this.track_id),this.flags&h.TFHD_FLAG_BASE_DATA_OFFSET&&e.writeUint64(this.base_data_offset),this.flags&h.TFHD_FLAG_SAMPLE_DESC&&e.writeUint32(this.default_sample_description_index),this.flags&h.TFHD_FLAG_SAMPLE_DUR&&e.writeUint32(this.default_sample_duration),this.flags&h.TFHD_FLAG_SAMPLE_SIZE&&e.writeUint32(this.default_sample_size),this.flags&h.TFHD_FLAG_SAMPLE_FLAGS&&e.writeUint32(this.default_sample_flags)},h.tkhdBox.prototype.write=function(e){this.version=0,this.size=80,this.writeHeader(e),e.writeUint32(this.creation_time),e.writeUint32(this.modification_time),e.writeUint32(this.track_id),e.writeUint32(0),e.writeUint32(this.duration),e.writeUint32(0),e.writeUint32(0),e.writeInt16(this.layer),e.writeInt16(this.alternate_group),e.writeInt16(this.volume<<8),e.writeUint16(0),e.writeInt32Array(this.matrix),e.writeUint32(this.width),e.writeUint32(this.height)},h.trexBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=20,this.writeHeader(e),e.writeUint32(this.track_id),e.writeUint32(this.default_sample_description_index),e.writeUint32(this.default_sample_duration),e.writeUint32(this.default_sample_size),e.writeUint32(this.default_sample_flags)},h.trunBox.prototype.write=function(e){this.version=0,this.size=4,this.flags&h.TRUN_FLAGS_DATA_OFFSET&&(this.size+=4),this.flags&h.TRUN_FLAGS_FIRST_FLAG&&(this.size+=4),this.flags&h.TRUN_FLAGS_DURATION&&(this.size+=4*this.sample_duration.length),this.flags&h.TRUN_FLAGS_SIZE&&(this.size+=4*this.sample_size.length),this.flags&h.TRUN_FLAGS_FLAGS&&(this.size+=4*this.sample_flags.length),this.flags&h.TRUN_FLAGS_CTS_OFFSET&&(this.size+=4*this.sample_composition_time_offset.length),this.writeHeader(e),e.writeUint32(this.sample_count),this.flags&h.TRUN_FLAGS_DATA_OFFSET&&(this.data_offset_position=e.getPosition(),e.writeInt32(this.data_offset)),this.flags&h.TRUN_FLAGS_FIRST_FLAG&&e.writeUint32(this.first_sample_flags);for(var t=0;t<this.sample_count;t++)this.flags&h.TRUN_FLAGS_DURATION&&e.writeUint32(this.sample_duration[t]),this.flags&h.TRUN_FLAGS_SIZE&&e.writeUint32(this.sample_size[t]),this.flags&h.TRUN_FLAGS_FLAGS&&e.writeUint32(this.sample_flags[t]),this.flags&h.TRUN_FLAGS_CTS_OFFSET&&(0===this.version?e.writeUint32(this.sample_composition_time_offset[t]):e.writeInt32(this.sample_composition_time_offset[t]))},h["url Box"].prototype.write=function(e){this.version=0,this.location?(this.flags=0,this.size=this.location.length+1):(this.flags=1,this.size=0),this.writeHeader(e),this.location&&e.writeCString(this.location)},h["urn Box"].prototype.write=function(e){this.version=0,this.flags=0,this.size=this.name.length+1+(this.location?this.location.length+1:0),this.writeHeader(e),e.writeCString(this.name),this.location&&e.writeCString(this.location)},h.vmhdBox.prototype.write=function(e){this.version=0,this.flags=1,this.size=8,this.writeHeader(e),e.writeUint16(this.graphicsmode),e.writeUint16Array(this.opcolor)},h.cttsBox.prototype.unpack=function(e){var t,r,i;for(i=0,t=0;t<this.sample_counts.length;t++)for(r=0;r<this.sample_counts[t];r++)e[i].pts=e[i].dts+this.sample_offsets[t],i++},h.sttsBox.prototype.unpack=function(e){var t,r,i;for(i=0,t=0;t<this.sample_counts.length;t++)for(r=0;r<this.sample_counts[t];r++)e[i].dts=0===i?0:e[i-1].dts+this.sample_deltas[t],i++},h.stcoBox.prototype.unpack=function(e){var t;for(t=0;t<this.chunk_offsets.length;t++)e[t].offset=this.chunk_offsets[t]},h.stscBox.prototype.unpack=function(e){var t,r,i,n,s;for(n=0,s=0,t=0;t<this.first_chunk.length;t++)for(r=0;r<(t+1<this.first_chunk.length?this.first_chunk[t+1]:1/0);r++)for(s++,i=0;i<this.samples_per_chunk[t];i++){if(!e[n])return;e[n].description_index=this.sample_description_index[t],e[n].chunk_index=s,n++}},h.stszBox.prototype.unpack=function(e){var t;for(t=0;t<this.sample_sizes.length;t++)e[t].size=this.sample_sizes[t]},h.DIFF_BOXES_PROP_NAMES=["boxes","entries","references","subsamples","items","item_infos","extents","associations","subsegments","ranges","seekLists","seekPoints","esd","levels"],h.DIFF_PRIMITIVE_ARRAY_PROP_NAMES=["compatible_brands","matrix","opcolor","sample_counts","sample_counts","sample_deltas","first_chunk","samples_per_chunk","sample_sizes","chunk_offsets","sample_offsets","sample_description_index","sample_duration"],h.boxEqualFields=function(e,t){if(e&&!t)return!1;var r;for(r in e)if(!(h.DIFF_BOXES_PROP_NAMES.indexOf(r)>-1||e[r]instanceof h.Box||t[r]instanceof h.Box||void 0===e[r]||void 0===t[r]||"function"==typeof e[r]||"function"==typeof t[r]||e.subBoxNames&&e.subBoxNames.indexOf(r.slice(0,4))>-1||t.subBoxNames&&t.subBoxNames.indexOf(r.slice(0,4))>-1||"data"===r||"start"===r||"size"===r||"creation_time"===r||"modification_time"===r||h.DIFF_PRIMITIVE_ARRAY_PROP_NAMES.indexOf(r)>-1||e[r]===t[r]))return!1;return!0},h.boxEqual=function(e,t){if(!h.boxEqualFields(e,t))return!1;for(var r=0;r<h.DIFF_BOXES_PROP_NAMES.length;r++){var i=h.DIFF_BOXES_PROP_NAMES[r];if(e[i]&&t[i]&&!h.boxEqual(e[i],t[i]))return!1}return!0};var _=function(){};_.prototype.parseSample=function(e){var t,r={};r.resources=[];var i=new o(e.data.buffer);if(e.subsamples&&0!==e.subsamples.length){if(r.documentString=i.readString(e.subsamples[0].size),e.subsamples.length>1)for(t=1;t<e.subsamples.length;t++)r.resources[t]=i.readUint8Array(e.subsamples[t].size)}else r.documentString=i.readString(e.data.length);return"undefined"!=typeof DOMParser&&(r.document=(new DOMParser).parseFromString(r.documentString,"application/xml")),r};var m=function(){};m.prototype.parseSample=function(e){return new o(e.data.buffer).readString(e.data.length)},m.prototype.parseConfig=function(e){var t=new o(e.buffer);return t.readUint32(),t.readCString()},t.XMLSubtitlein4Parser=_,t.Textin4Parser=m;var g=function(e){this.stream=e||new c,this.boxes=[],this.mdats=[],this.moofs=[],this.isProgressive=!1,this.moovStartFound=!1,this.onMoovStart=null,this.moovStartSent=!1,this.onReady=null,this.readySent=!1,this.onSegment=null,this.onSamples=null,this.onError=null,this.sampleListBuilt=!1,this.fragmentedTracks=[],this.extractedTracks=[],this.isFragmentationInitialized=!1,this.sampleProcessingStarted=!1,this.nextMoofNumber=0,this.itemListBuilt=!1,this.onSidx=null,this.sidxSent=!1};g.prototype.destroy=function(){this.stream&&(this.stream.destroy(),this.stream=null),this.boxes=[],this.mdats=[],this.moofs=[],this.isProgressive=!1,this.moovStartFound=!1,this.onMoovStart=null,this.moovStartSent=!1,this.onReady=null,this.readySent=!1,this.onSegment=null,this.onSamples=null,this.onError=null,this.sampleListBuilt=!1,this.fragmentedTracks=[],this.extractedTracks=[],this.isFragmentationInitialized=!1,this.sampleProcessingStarted=!1,this.nextMoofNumber=0,this.itemListBuilt=!1,this.onSidx=null,this.sidxSent=!1},g.prototype.setSegmentOptions=function(e,t,r){var i=this.getTrackById(e);if(i){var n={};this.fragmentedTracks.push(n),n.id=e,n.user=t,n.trak=i,i.nextSample=0,n.segmentStream=null,n.nb_samples=1e3,n.rapAlignement=!0,r&&(r.nbSamples&&(n.nb_samples=r.nbSamples),r.rapAlignement&&(n.rapAlignement=r.rapAlignement))}},g.prototype.unsetSegmentOptions=function(e){for(var t=-1,r=0;r<this.fragmentedTracks.length;r++){this.fragmentedTracks[r].id==e&&(t=r)}t>-1&&this.fragmentedTracks.splice(t,1)},g.prototype.setExtractionOptions=function(e,t,r){var i=this.getTrackById(e);if(i){var n={};this.extractedTracks.push(n),n.id=e,n.user=t,n.trak=i,i.nextSample=0,n.nb_samples=1e3,n.samples=[],r&&r.nbSamples&&(n.nb_samples=r.nbSamples)}},g.prototype.unsetExtractionOptions=function(e){for(var t=-1,r=0;r<this.extractedTracks.length;r++){this.extractedTracks[r].id==e&&(t=r)}t>-1&&this.extractedTracks.splice(t,1)},g.prototype.parse=function(){var e,t;if(!this.restoreParsePosition||this.restoreParsePosition())for(;;){if(this.hasIncompleteMdat&&this.hasIncompleteMdat()){if(this.processIncompleteMdat())continue;return}if(this.saveParsePosition&&this.saveParsePosition(),(e=h.parseOneBox(this.stream,false)).code===h.ERR_NOT_ENOUGH_DATA){if(this.processIncompleteBox){if(this.processIncompleteBox(e))continue;return}return}var r;switch(r="uuid"!==(t=e.box).type?t.type:t.uuid,this.boxes.push(t),r){case"mdat":this.mdats.push(t);break;case"moof":this.moofs.push(t);break;case"moov":this.moovStartFound=!0,0===this.mdats.length&&(this.isProgressive=!0);default:void 0!==this[r]&&a.warn("ISOFile","Duplicate Box of type: "+r+", overriding previous occurrence"),this[r]=t}this.updateUsedBytes&&this.updateUsedBytes(t,e)}},g.prototype.checkBuffer=function(e){if(null==e)throw"Buffer must be defined and non empty";if(void 0===e.fileStart)throw"Buffer must have a fileStart property";return 0===e.byteLength?(a.warn("ISOFile","Ignoring empty buffer (fileStart: "+e.fileStart+")"),this.stream.logBufferLevel(),!1):(a.info("ISOFile","Processing buffer (fileStart: "+e.fileStart+")"),e.usedBytes=0,this.stream.insertBuffer(e),this.stream.logBufferLevel(),!!this.stream.initialized()||(a.warn("ISOFile","Not ready to start parsing"),!1))},g.prototype.appendBuffer=function(e,t){var r;if(this.checkBuffer(e))return this.parse(),this.moovStartFound&&!this.moovStartSent&&(this.moovStartSent=!0,this.onMoovStart&&this.onMoovStart()),this.moov?(this.sampleListBuilt||(this.buildSampleLists(),this.sampleListBuilt=!0),this.updateSampleLists(),this.onReady&&!this.readySent&&(this.readySent=!0,this.onReady(this.getInfo())),this.processSamples(t),this.nextSeekPosition?(r=this.nextSeekPosition,this.nextSeekPosition=void 0):r=this.nextParsePosition,this.stream.getEndFilePositionAfter&&(r=this.stream.getEndFilePositionAfter(r))):r=this.nextParsePosition?this.nextParsePosition:0,this.sidx&&this.onSidx&&!this.sidxSent&&(this.onSidx(this.sidx),this.sidxSent=!0),this.meta&&(this.flattenItemInfo&&!this.itemListBuilt&&(this.flattenItemInfo(),this.itemListBuilt=!0),this.processItems&&this.processItems(this.onItem)),this.stream.cleanBuffers&&(a.info("ISOFile","Done processing buffer (fileStart: "+e.fileStart+") - next buffer to fetch should have a fileStart position of "+r),this.stream.logBufferLevel(),this.stream.cleanBuffers(),this.stream.logBufferLevel(!0),a.info("ISOFile","Sample data size in memory: "+this.getAllocatedSampleDataSize())),r},g.prototype.getInfo=function(){var e,t,r,i,n,s,a={},o=new Date("1904-01-01T00:00:00Z").getTime();if(this.moov)for(a.hasMoov=!0,a.duration=this.moov.mvhd.duration,a.timescale=this.moov.mvhd.timescale,a.isFragmented=null!=this.moov.mvex,a.isFragmented&&this.moov.mvex.mehd&&(a.fragment_duration=this.moov.mvex.mehd.fragment_duration),a.isProgressive=this.isProgressive,a.hasIOD=null!=this.moov.iods,a.brands=[],a.brands.push(this.ftyp.major_brand),a.brands=a.brands.concat(this.ftyp.compatible_brands),a.created=new Date(o+1e3*this.moov.mvhd.creation_time),a.modified=new Date(o+1e3*this.moov.mvhd.modification_time),a.tracks=[],a.audioTracks=[],a.videoTracks=[],a.subtitleTracks=[],a.metadataTracks=[],a.hintTracks=[],a.otherTracks=[],e=0;e<this.moov.traks.length;e++){if(s=(r=this.moov.traks[e]).mdia.minf.stbl.stsd.entries[0],i={},a.tracks.push(i),i.id=r.tkhd.track_id,i.name=r.mdia.hdlr.name,i.references=[],r.tref)for(t=0;t<r.tref.boxes.length;t++)n={},i.references.push(n),n.type=r.tref.boxes[t].type,n.track_ids=r.tref.boxes[t].track_ids;r.edts&&(i.edits=r.edts.elst.entries),i.created=new Date(o+1e3*r.tkhd.creation_time),i.modified=new Date(o+1e3*r.tkhd.modification_time),i.movie_duration=r.tkhd.duration,i.movie_timescale=a.timescale,i.layer=r.tkhd.layer,i.alternate_group=r.tkhd.alternate_group,i.volume=r.tkhd.volume,i.matrix=r.tkhd.matrix,i.track_width=r.tkhd.width/65536,i.track_height=r.tkhd.height/65536,i.timescale=r.mdia.mdhd.timescale,i.cts_shift=r.mdia.minf.stbl.cslg,i.duration=r.mdia.mdhd.duration,i.samples_duration=r.samples_duration,i.codec=s.getCodec(),i.kind=r.udta&&r.udta.kinds.length?r.udta.kinds[0]:{schemeURI:"",value:""},i.language=r.mdia.elng?r.mdia.elng.extended_language:r.mdia.mdhd.languageString,i.nb_samples=r.samples.length,i.size=r.samples_size,i.bitrate=8*i.size*i.timescale/i.samples_duration,s.isAudio()?(i.type="audio",a.audioTracks.push(i),i.audio={},i.audio.sample_rate=s.getSampleRate(),i.audio.channel_count=s.getChannelCount(),i.audio.sample_size=s.getSampleSize()):s.isVideo()?(i.type="video",a.videoTracks.push(i),i.video={},i.video.width=s.getWidth(),i.video.height=s.getHeight()):s.isSubtitle()?(i.type="subtitles",a.subtitleTracks.push(i)):s.isHint()?(i.type="metadata",a.hintTracks.push(i)):s.isMetadata()?(i.type="metadata",a.metadataTracks.push(i)):(i.type="metadata",a.otherTracks.push(i))}else a.hasMoov=!1;if(a.mime="",a.hasMoov&&a.tracks){for(a.videoTracks&&a.videoTracks.length>0?a.mime+='video/mp4; codecs="':a.audioTracks&&a.audioTracks.length>0?a.mime+='audio/mp4; codecs="':a.mime+='application/mp4; codecs="',e=0;e<a.tracks.length;e++)0!==e&&(a.mime+=","),a.mime+=a.tracks[e].codec;a.mime+='"; profiles="',a.mime+=this.ftyp.compatible_brands.join(),a.mime+='"'}return a},g.prototype.setNextSeekPositionFromSample=function(e){e&&(this.nextSeekPosition?this.nextSeekPosition=Math.min(e.offset+e.alreadyRead,this.nextSeekPosition):this.nextSeekPosition=e.offset+e.alreadyRead)},g.prototype.processSamples=function(e){var t,r;if(this.sampleProcessingStarted){if(this.isFragmentationInitialized&&null!==this.onSegment)for(t=0;t<this.fragmentedTracks.length;t++){var i=this.fragmentedTracks[t];for(r=i.trak;r.nextSample<r.samples.length&&this.sampleProcessingStarted;){a.debug("ISOFile","Creating media fragment on track #"+i.id+" for sample "+r.nextSample);var n=this.createFragment(i.id,r.nextSample,i.segmentStream);if(!n)break;if(i.segmentStream=n,r.nextSample++,(r.nextSample%i.nb_samples==0||e||r.nextSample>=r.samples.length)&&(a.info("ISOFile","Sending fragmented data on track #"+i.id+" for samples ["+Math.max(0,r.nextSample-i.nb_samples)+","+(r.nextSample-1)+"]"),a.info("ISOFile","Sample data size in memory: "+this.getAllocatedSampleDataSize()),this.onSegment&&this.onSegment(i.id,i.user,i.segmentStream.buffer,r.nextSample,e||r.nextSample>=r.samples.length),i.segmentStream=null,i!==this.fragmentedTracks[t]))break}}if(null!==this.onSamples)for(t=0;t<this.extractedTracks.length;t++){var s=this.extractedTracks[t];for(r=s.trak;r.nextSample<r.samples.length&&this.sampleProcessingStarted;){a.debug("ISOFile","Exporting on track #"+s.id+" sample #"+r.nextSample);var o=this.getSample(r,r.nextSample);if(!o){this.setNextSeekPositionFromSample(r.samples[r.nextSample]);break}if(r.nextSample++,s.samples.push(o),(r.nextSample%s.nb_samples==0||r.nextSample>=r.samples.length)&&(a.debug("ISOFile","Sending samples on track #"+s.id+" for sample "+r.nextSample),this.onSamples&&this.onSamples(s.id,s.user,s.samples),s.samples=[],s!==this.extractedTracks[t]))break}}}},g.prototype.getBox=function(e){var t=this.getBoxes(e,!0);return t.length?t[0]:null},g.prototype.getBoxes=function(e,t){var r=[];return g._sweep.call(this,e,r,t),r},g._sweep=function(e,t,r){for(var i in this.type&&this.type==e&&t.push(this),this.boxes){if(t.length&&r)return;g._sweep.call(this.boxes[i],e,t,r)}},g.prototype.getTrackSamplesInfo=function(e){var t=this.getTrackById(e);return t?t.samples:void 0},g.prototype.getTrackSample=function(e,t){var r=this.getTrackById(e);return this.getSample(r,t)},g.prototype.releaseUsedSamples=function(e,t){var r=0,i=this.getTrackById(e);i.lastValidSample||(i.lastValidSample=0);for(var n=i.lastValidSample;n<t;n++)r+=this.releaseSample(i,n);a.info("ISOFile","Track #"+e+" released samples up to "+t+" (released size: "+r+", remaining: "+this.samplesDataSize+")"),i.lastValidSample=t},g.prototype.start=function(){this.sampleProcessingStarted=!0,this.processSamples(!1)},g.prototype.stop=function(){this.sampleProcessingStarted=!1},g.prototype.flush=function(){a.info("ISOFile","Flushing remaining samples"),this.updateSampleLists(),this.processSamples(!0),this.stream.cleanBuffers(),this.stream.logBufferLevel(!0)},g.prototype.seekTrack=function(e,t,r){var i,n,s,o,d=0,l=0;if(0===r.samples.length)return a.info("ISOFile","No sample in track, cannot seek! Using time "+a.getDurationString(0,1)+" and offset: 0"),{offset:0,time:0};for(i=0;i<r.samples.length;i++){if(n=r.samples[i],0===i)l=0,o=n.timescale;else if(n.cts>e*n.timescale){l=i-1;break}t&&n.is_sync&&(d=i)}for(t&&(l=d),e=r.samples[l].cts,r.nextSample=l;r.samples[l].alreadyRead===r.samples[l].size&&r.samples[l+1];)l++;return s=r.samples[l].offset+r.samples[l].alreadyRead,a.info("ISOFile","Seeking to "+(t?"RAP":"")+" sample #"+r.nextSample+" on track "+r.tkhd.track_id+", time "+a.getDurationString(e,o)+" and offset: "+s),{offset:s,time:e/o}},g.prototype.getTrackDuration=function(e){var t;return e.samples?((t=e.samples[e.samples.length-1]).cts+t.duration)/t.timescale:1/0},g.prototype.seek=function(e,t){var r,i,n,s=this.moov,o={offset:1/0,time:1/0};if(this.moov){for(n=0;n<s.traks.length;n++)r=s.traks[n],e>this.getTrackDuration(r)||((i=this.seekTrack(e,t,r)).offset<o.offset&&(o.offset=i.offset),i.time<o.time&&(o.time=i.time));return a.info("ISOFile","Seeking at time "+a.getDurationString(o.time,1)+" needs a buffer with a fileStart position of "+o.offset),o.offset===1/0?o={offset:this.nextParsePosition,time:0}:o.offset=this.stream.getEndFilePositionAfter(o.offset),a.info("ISOFile","Adjusted seek position (after checking data already in buffer): "+o.offset),o}throw"Cannot seek: moov not received!"},g.prototype.equal=function(e){for(var t=0;t<this.boxes.length&&t<e.boxes.length;){var r=this.boxes[t],i=e.boxes[t];if(!h.boxEqual(r,i))return!1;t++}return!0},t.ISOFile=g,g.prototype.lastBoxStartPosition=0,g.prototype.parsingMdat=null,g.prototype.nextParsePosition=0,g.prototype.discardMdatData=!1,g.prototype.processIncompleteBox=function(e){var t;return"mdat"===e.type?(t=new h[e.type+"Box"](e.size),this.parsingMdat=t,this.boxes.push(t),this.mdats.push(t),t.start=e.start,t.hdr_size=e.hdr_size,this.stream.addUsedBytes(t.hdr_size),this.lastBoxStartPosition=t.start+t.size,this.stream.seek(t.start+t.size,!1,this.discardMdatData)?(this.parsingMdat=null,!0):(this.moovStartFound?this.nextParsePosition=this.stream.findEndContiguousBuf():this.nextParsePosition=t.start+t.size,!1)):("moov"===e.type&&(this.moovStartFound=!0,0===this.mdats.length&&(this.isProgressive=!0)),!!this.stream.mergeNextBuffer&&this.stream.mergeNextBuffer()?(this.nextParsePosition=this.stream.getEndPosition(),!0):(e.type?this.moovStartFound?this.nextParsePosition=this.stream.getEndPosition():this.nextParsePosition=this.stream.getPosition()+e.size:this.nextParsePosition=this.stream.getEndPosition(),!1))},g.prototype.hasIncompleteMdat=function(){return null!==this.parsingMdat},g.prototype.processIncompleteMdat=function(){var e;return e=this.parsingMdat,this.stream.seek(e.start+e.size,!1,this.discardMdatData)?(a.debug("ISOFile","Found 'mdat' end in buffered data"),this.parsingMdat=null,!0):(this.nextParsePosition=this.stream.findEndContiguousBuf(),!1)},g.prototype.restoreParsePosition=function(){return this.stream.seek(this.lastBoxStartPosition,!0,this.discardMdatData)},g.prototype.saveParsePosition=function(){this.lastBoxStartPosition=this.stream.getPosition()},g.prototype.updateUsedBytes=function(e,t){this.stream.addUsedBytes&&("mdat"===e.type?(this.stream.addUsedBytes(e.hdr_size),this.discardMdatData&&this.stream.addUsedBytes(e.size-e.hdr_size)):this.stream.addUsedBytes(e.size))},g.prototype.add=h.Box.prototype.add,g.prototype.addBox=h.Box.prototype.addBox,g.prototype.init=function(e){var t=e||{};this.add("ftyp").set("major_brand",t.brands&&t.brands[0]||"iso4").set("minor_version",0).set("compatible_brands",t.brands||["iso4"]);var r=this.add("moov");return r.add("mvhd").set("timescale",t.timescale||600).set("rate",t.rate||65536).set("creation_time",0).set("modification_time",0).set("duration",t.duration||0).set("volume",t.width?0:256).set("matrix",[65536,0,0,0,65536,0,0,0,1073741824]).set("next_track_id",1),r.add("mvex"),this},g.prototype.addTrack=function(e){this.moov||this.init(e);var t=e||{};t.width=t.width||320,t.height=t.height||320,t.id=t.id||this.moov.mvhd.next_track_id,t.type=t.type||"avc1";var r=this.moov.add("trak");this.moov.mvhd.next_track_id=t.id+1,r.add("tkhd").set("flags",h.TKHD_FLAG_ENABLED|h.TKHD_FLAG_IN_MOVIE|h.TKHD_FLAG_IN_PREVIEW).set("creation_time",0).set("modification_time",0).set("track_id",t.id).set("duration",t.duration||0).set("layer",t.layer||0).set("alternate_group",0).set("volume",1).set("matrix",[0,0,0,0,0,0,0,0,0]).set("width",t.width<<16).set("height",t.height<<16);var i=r.add("mdia");i.add("mdhd").set("creation_time",0).set("modification_time",0).set("timescale",t.timescale||1).set("duration",t.media_duration||0).set("language",t.language||"und"),i.add("hdlr").set("handler",t.hdlr||"vide").set("name",t.name||"Track created with MP4Box.js"),i.add("elng").set("extended_language",t.language||"fr-FR");var n=i.add("minf");if(void 0!==h[t.type+"SampleEntry"]){var s=new h[t.type+"SampleEntry"];s.data_reference_index=1;var a="";for(var d in h.sampleEntryCodes)for(var l=h.sampleEntryCodes[d],c=0;c<l.length;c++)if(l.indexOf(t.type)>-1){a=d;break}switch(a){case"Visual":if(n.add("vmhd").set("graphicsmode",0).set("opcolor",[0,0,0]),s.set("width",t.width).set("height",t.height).set("horizresolution",72<<16).set("vertresolution",72<<16).set("frame_count",1).set("compressorname",t.type+" Compressor").set("depth",24),t.avcDecoderConfigRecord){var u=new h.avcCBox;u.parse(new o(t.avcDecoderConfigRecord)),s.addBox(u)}else if(t.hevcDecoderConfigRecord){var f=new h.hvcCBox;f.parse(new o(t.hevcDecoderConfigRecord)),s.addBox(f)}break;case"Audio":n.add("smhd").set("balance",t.balance||0),s.set("channel_count",t.channel_count||2).set("samplesize",t.samplesize||16).set("samplerate",t.samplerate||65536);break;case"Hint":n.add("hmhd");break;case"Subtitle":if(n.add("sthd"),"stpp"===t.type)s.set("namespace",t.namespace||"nonamespace").set("schema_location",t.schema_location||"").set("auxiliary_mime_types",t.auxiliary_mime_types||"");break;default:n.add("nmhd")}t.description&&s.addBox(t.description),t.description_boxes&&t.description_boxes.forEach((function(e){s.addBox(e)})),n.add("dinf").add("dref").addEntry((new h["url Box"]).set("flags",1));var p=n.add("stbl");return p.add("stsd").addEntry(s),p.add("stts").set("sample_counts",[]).set("sample_deltas",[]),p.add("stsc").set("first_chunk",[]).set("samples_per_chunk",[]).set("sample_description_index",[]),p.add("stco").set("chunk_offsets",[]),p.add("stsz").set("sample_sizes",[]),this.moov.mvex.add("trex").set("track_id",t.id).set("default_sample_description_index",t.default_sample_description_index||1).set("default_sample_duration",t.default_sample_duration||0).set("default_sample_size",t.default_sample_size||0).set("default_sample_flags",t.default_sample_flags||0),this.buildTrakSampleLists(r),t.id}},h.Box.prototype.computeSize=function(e){var t=e||new d;t.endianness=d.BIG_ENDIAN,this.write(t)},g.prototype.addSample=function(e,t,r){var i=r||{},n={},s=this.getTrackById(e);if(null!==s){n.number=s.samples.length,n.track_id=s.tkhd.track_id,n.timescale=s.mdia.mdhd.timescale,n.description_index=i.sample_description_index?i.sample_description_index-1:0,n.description=s.mdia.minf.stbl.stsd.entries[n.description_index],n.data=t,n.size=t.byteLength,n.alreadyRead=n.size,n.duration=i.duration||1,n.cts=i.cts||0,n.dts=i.dts||0,n.is_sync=i.is_sync||!1,n.is_leading=i.is_leading||0,n.depends_on=i.depends_on||0,n.is_depended_on=i.is_depended_on||0,n.has_redundancy=i.has_redundancy||0,n.degradation_priority=i.degradation_priority||0,n.offset=0,n.subsamples=i.subsamples,s.samples.push(n),s.samples_size+=n.size,s.samples_duration+=n.duration,void 0===s.first_dts&&(s.first_dts=i.dts),this.processSamples();var a=this.createSingleSampleMoof(n);return this.addBox(a),a.computeSize(),a.trafs[0].truns[0].data_offset=a.size+8,this.add("mdat").data=new Uint8Array(t),n}},g.prototype.createSingleSampleMoof=function(e){var t=0;t=e.is_sync?1<<25:65536;var r=new h.moofBox;r.add("mfhd").set("sequence_number",this.nextMoofNumber),this.nextMoofNumber++;var i=r.add("traf"),n=this.getTrackById(e.track_id);return i.add("tfhd").set("track_id",e.track_id).set("flags",h.TFHD_FLAG_DEFAULT_BASE_IS_MOOF),i.add("tfdt").set("baseMediaDecodeTime",e.dts-(n.first_dts||0)),i.add("trun").set("flags",h.TRUN_FLAGS_DATA_OFFSET|h.TRUN_FLAGS_DURATION|h.TRUN_FLAGS_SIZE|h.TRUN_FLAGS_FLAGS|h.TRUN_FLAGS_CTS_OFFSET).set("data_offset",0).set("first_sample_flags",0).set("sample_count",1).set("sample_duration",[e.duration]).set("sample_size",[e.size]).set("sample_flags",[t]).set("sample_composition_time_offset",[e.cts-e.dts]),r},g.prototype.lastMoofIndex=0,g.prototype.samplesDataSize=0,g.prototype.resetTables=function(){var e,t,r,i,n,s;for(this.initial_duration=this.moov.mvhd.duration,this.moov.mvhd.duration=0,e=0;e<this.moov.traks.length;e++){(t=this.moov.traks[e]).tkhd.duration=0,t.mdia.mdhd.duration=0,(t.mdia.minf.stbl.stco||t.mdia.minf.stbl.co64).chunk_offsets=[],(r=t.mdia.minf.stbl.stsc).first_chunk=[],r.samples_per_chunk=[],r.sample_description_index=[],(t.mdia.minf.stbl.stsz||t.mdia.minf.stbl.stz2).sample_sizes=[],(i=t.mdia.minf.stbl.stts).sample_counts=[],i.sample_deltas=[],(n=t.mdia.minf.stbl.ctts)&&(n.sample_counts=[],n.sample_offsets=[]),s=t.mdia.minf.stbl.stss;var a=t.mdia.minf.stbl.boxes.indexOf(s);-1!=a&&(t.mdia.minf.stbl.boxes[a]=null)}},g.initSampleGroups=function(e,t,r,i,n){var s,a,o,d;function l(e,t,r){this.grouping_type=e,this.grouping_type_parameter=t,this.sbgp=r,this.last_sample_in_run=-1,this.entry_index=-1}for(t&&(t.sample_groups_info=[]),e.sample_groups_info||(e.sample_groups_info=[]),a=0;a<r.length;a++){for(d=r[a].grouping_type+"/"+r[a].grouping_type_parameter,o=new l(r[a].grouping_type,r[a].grouping_type_parameter,r[a]),t&&(t.sample_groups_info[d]=o),e.sample_groups_info[d]||(e.sample_groups_info[d]=o),s=0;s<i.length;s++)i[s].grouping_type===r[a].grouping_type&&(o.description=i[s],o.description.used=!0);if(n)for(s=0;s<n.length;s++)n[s].grouping_type===r[a].grouping_type&&(o.fragment_description=n[s],o.fragment_description.used=!0,o.is_fragment=!0)}if(t){if(n)for(a=0;a<n.length;a++)!n[a].used&&n[a].version>=2&&(d=n[a].grouping_type+"/0",(o=new l(n[a].grouping_type,0)).is_fragment=!0,t.sample_groups_info[d]||(t.sample_groups_info[d]=o))}else for(a=0;a<i.length;a++)!i[a].used&&i[a].version>=2&&(d=i[a].grouping_type+"/0",o=new l(i[a].grouping_type,0),e.sample_groups_info[d]||(e.sample_groups_info[d]=o))},g.setSampleGroupProperties=function(e,t,r,i){var n,s;for(n in t.sample_groups=[],i){var a;if(t.sample_groups[n]={},t.sample_groups[n].grouping_type=i[n].grouping_type,t.sample_groups[n].grouping_type_parameter=i[n].grouping_type_parameter,r>=i[n].last_sample_in_run&&(i[n].last_sample_in_run<0&&(i[n].last_sample_in_run=0),i[n].entry_index++,i[n].entry_index<=i[n].sbgp.entries.length-1&&(i[n].last_sample_in_run+=i[n].sbgp.entries[i[n].entry_index].sample_count)),i[n].entry_index<=i[n].sbgp.entries.length-1?t.sample_groups[n].group_description_index=i[n].sbgp.entries[i[n].entry_index].group_description_index:t.sample_groups[n].group_description_index=-1,0!==t.sample_groups[n].group_description_index)a=i[n].fragment_description?i[n].fragment_description:i[n].description,t.sample_groups[n].group_description_index>0?(s=t.sample_groups[n].group_description_index>65535?(t.sample_groups[n].group_description_index>>16)-1:t.sample_groups[n].group_description_index-1,a&&s>=0&&(t.sample_groups[n].description=a.entries[s])):a&&a.version>=2&&a.default_group_description_index>0&&(t.sample_groups[n].description=a.entries[a.default_group_description_index-1])}},g.process_sdtp=function(e,t,r){t&&(e?(t.is_leading=e.is_leading[r],t.depends_on=e.sample_depends_on[r],t.is_depended_on=e.sample_is_depended_on[r],t.has_redundancy=e.sample_has_redundancy[r]):(t.is_leading=0,t.depends_on=0,t.is_depended_on=0,t.has_redundancy=0))},g.prototype.buildSampleLists=function(){var e,t;for(e=0;e<this.moov.traks.length;e++)t=this.moov.traks[e],this.buildTrakSampleLists(t)},g.prototype.buildTrakSampleLists=function(e){var t,r,i,n,s,a,o,d,l,c,u,h,f,p,_,m,y,v,b,w,S,E,A,B;if(e.samples=[],e.samples_duration=0,e.samples_size=0,r=e.mdia.minf.stbl.stco||e.mdia.minf.stbl.co64,i=e.mdia.minf.stbl.stsc,n=e.mdia.minf.stbl.stsz||e.mdia.minf.stbl.stz2,s=e.mdia.minf.stbl.stts,a=e.mdia.minf.stbl.ctts,o=e.mdia.minf.stbl.stss,d=e.mdia.minf.stbl.stsd,l=e.mdia.minf.stbl.subs,h=e.mdia.minf.stbl.stdp,c=e.mdia.minf.stbl.sbgps,u=e.mdia.minf.stbl.sgpds,v=-1,b=-1,w=-1,S=-1,E=0,A=0,B=0,g.initSampleGroups(e,null,c,u),void 0!==n){for(t=0;t<n.sample_sizes.length;t++){var x={};x.number=t,x.track_id=e.tkhd.track_id,x.timescale=e.mdia.mdhd.timescale,x.alreadyRead=0,e.samples[t]=x,x.size=n.sample_sizes[t],e.samples_size+=x.size,0===t?(p=1,f=0,x.chunk_index=p,x.chunk_run_index=f,y=i.samples_per_chunk[f],m=0,_=f+1<i.first_chunk.length?i.first_chunk[f+1]-1:1/0):t<y?(x.chunk_index=p,x.chunk_run_index=f):(p++,x.chunk_index=p,m=0,p<=_||(_=++f+1<i.first_chunk.length?i.first_chunk[f+1]-1:1/0),x.chunk_run_index=f,y+=i.samples_per_chunk[f]),x.description_index=i.sample_description_index[x.chunk_run_index]-1,x.description=d.entries[x.description_index],x.offset=r.chunk_offsets[x.chunk_index-1]+m,m+=x.size,t>v&&(b++,v<0&&(v=0),v+=s.sample_counts[b]),t>0?(e.samples[t-1].duration=s.sample_deltas[b],e.samples_duration+=e.samples[t-1].duration,x.dts=e.samples[t-1].dts+e.samples[t-1].duration):x.dts=0,a?(t>=w&&(S++,w<0&&(w=0),w+=a.sample_counts[S]),x.cts=e.samples[t].dts+a.sample_offsets[S]):x.cts=x.dts,o?(t==o.sample_numbers[E]-1?(x.is_sync=!0,E++):(x.is_sync=!1,x.degradation_priority=0),l&&l.entries[A].sample_delta+B==t+1&&(x.subsamples=l.entries[A].subsamples,B+=l.entries[A].sample_delta,A++)):x.is_sync=!0,g.process_sdtp(e.mdia.minf.stbl.sdtp,x,x.number),x.degradation_priority=h?h.priority[t]:0,l&&l.entries[A].sample_delta+B==t&&(x.subsamples=l.entries[A].subsamples,B+=l.entries[A].sample_delta),(c.length>0||u.length>0)&&g.setSampleGroupProperties(e,x,t,e.sample_groups_info)}t>0&&(e.samples[t-1].duration=Math.max(e.mdia.mdhd.duration-e.samples[t-1].dts,0),e.samples_duration+=e.samples[t-1].duration)}},g.prototype.updateSampleLists=function(){var e,t,r,i,n,s,a,o,d,l,c,u,f,p,_;if(void 0!==this.moov)for(;this.lastMoofIndex<this.moofs.length;)if(d=this.moofs[this.lastMoofIndex],this.lastMoofIndex++,"moof"==d.type)for(l=d,e=0;e<l.trafs.length;e++){for(c=l.trafs[e],u=this.getTrackById(c.tfhd.track_id),f=this.getTrexById(c.tfhd.track_id),i=c.tfhd.flags&h.TFHD_FLAG_SAMPLE_DESC?c.tfhd.default_sample_description_index:f?f.default_sample_description_index:1,n=c.tfhd.flags&h.TFHD_FLAG_SAMPLE_DUR?c.tfhd.default_sample_duration:f?f.default_sample_duration:0,s=c.tfhd.flags&h.TFHD_FLAG_SAMPLE_SIZE?c.tfhd.default_sample_size:f?f.default_sample_size:0,a=c.tfhd.flags&h.TFHD_FLAG_SAMPLE_FLAGS?c.tfhd.default_sample_flags:f?f.default_sample_flags:0,c.sample_number=0,c.sbgps.length>0&&g.initSampleGroups(u,c,c.sbgps,u.mdia.minf.stbl.sgpds,c.sgpds),t=0;t<c.truns.length;t++){var m=c.truns[t];for(r=0;r<m.sample_count;r++){(p={}).moof_number=this.lastMoofIndex,p.number_in_traf=c.sample_number,c.sample_number++,p.number=u.samples.length,c.first_sample_index=u.samples.length,u.samples.push(p),p.track_id=u.tkhd.track_id,p.timescale=u.mdia.mdhd.timescale,p.description_index=i-1,p.description=u.mdia.minf.stbl.stsd.entries[p.description_index],p.size=s,m.flags&h.TRUN_FLAGS_SIZE&&(p.size=m.sample_size[r]),u.samples_size+=p.size,p.duration=n,m.flags&h.TRUN_FLAGS_DURATION&&(p.duration=m.sample_duration[r]),u.samples_duration+=p.duration,u.first_traf_merged||r>0?p.dts=u.samples[u.samples.length-2].dts+u.samples[u.samples.length-2].duration:(c.tfdt?p.dts=c.tfdt.baseMediaDecodeTime:p.dts=0,u.first_traf_merged=!0),p.cts=p.dts,m.flags&h.TRUN_FLAGS_CTS_OFFSET&&(p.cts=p.dts+m.sample_composition_time_offset[r]),_=a,m.flags&h.TRUN_FLAGS_FLAGS?_=m.sample_flags[r]:0===r&&m.flags&h.TRUN_FLAGS_FIRST_FLAG&&(_=m.first_sample_flags),p.is_sync=!(_>>16&1),p.is_leading=_>>26&3,p.depends_on=_>>24&3,p.is_depended_on=_>>22&3,p.has_redundancy=_>>20&3,p.degradation_priority=65535&_;var y=!!(c.tfhd.flags&h.TFHD_FLAG_BASE_DATA_OFFSET),v=!!(c.tfhd.flags&h.TFHD_FLAG_DEFAULT_BASE_IS_MOOF),b=!!(m.flags&h.TRUN_FLAGS_DATA_OFFSET),w=0;w=y?c.tfhd.base_data_offset:v||0===t?l.start:o,p.offset=0===t&&0===r?b?w+m.data_offset:w:o,o=p.offset+p.size,(c.sbgps.length>0||c.sgpds.length>0||u.mdia.minf.stbl.sbgps.length>0||u.mdia.minf.stbl.sgpds.length>0)&&g.setSampleGroupProperties(u,p,p.number_in_traf,c.sample_groups_info)}}if(c.subs){u.has_fragment_subsamples=!0;var S=c.first_sample_index;for(t=0;t<c.subs.entries.length;t++)S+=c.subs.entries[t].sample_delta,(p=u.samples[S-1]).subsamples=c.subs.entries[t].subsamples}}},g.prototype.getSample=function(e,t){var r,i=e.samples[t];if(!this.moov)return null;if(i.data){if(i.alreadyRead==i.size)return i}else i.data=new Uint8Array(i.size),i.alreadyRead=0,this.samplesDataSize+=i.size,a.debug("ISOFile","Allocating sample #"+t+" on track #"+e.tkhd.track_id+" of size "+i.size+" (total: "+this.samplesDataSize+")");for(;;){var n=this.stream.findPosition(!0,i.offset+i.alreadyRead,!1);if(!(n>-1))return null;var s=(r=this.stream.buffers[n]).byteLength-(i.offset+i.alreadyRead-r.fileStart);if(i.size-i.alreadyRead<=s)return a.debug("ISOFile","Getting sample #"+t+" data (alreadyRead: "+i.alreadyRead+" offset: "+(i.offset+i.alreadyRead-r.fileStart)+" read size: "+(i.size-i.alreadyRead)+" full size: "+i.size+")"),d.memcpy(i.data.buffer,i.alreadyRead,r,i.offset+i.alreadyRead-r.fileStart,i.size-i.alreadyRead),r.usedBytes+=i.size-i.alreadyRead,this.stream.logBufferLevel(),i.alreadyRead=i.size,i;if(0===s)return null;a.debug("ISOFile","Getting sample #"+t+" partial data (alreadyRead: "+i.alreadyRead+" offset: "+(i.offset+i.alreadyRead-r.fileStart)+" read size: "+s+" full size: "+i.size+")"),d.memcpy(i.data.buffer,i.alreadyRead,r,i.offset+i.alreadyRead-r.fileStart,s),i.alreadyRead+=s,r.usedBytes+=s,this.stream.logBufferLevel()}},g.prototype.releaseSample=function(e,t){var r=e.samples[t];return r.data?(this.samplesDataSize-=r.size,r.data=null,r.alreadyRead=0,r.size):0},g.prototype.getAllocatedSampleDataSize=function(){return this.samplesDataSize},g.prototype.getCodecs=function(){var e,t="";for(e=0;e<this.moov.traks.length;e++){e>0&&(t+=","),t+=this.moov.traks[e].mdia.minf.stbl.stsd.entries[0].getCodec()}return t},g.prototype.getTrexById=function(e){var t;if(!this.moov||!this.moov.mvex)return null;for(t=0;t<this.moov.mvex.trexs.length;t++){var r=this.moov.mvex.trexs[t];if(r.track_id==e)return r}return null},g.prototype.getTrackById=function(e){if(void 0===this.moov)return null;for(var t=0;t<this.moov.traks.length;t++){var r=this.moov.traks[t];if(r.tkhd.track_id==e)return r}return null},g.prototype.items=[],g.prototype.entity_groups=[],g.prototype.itemsDataSize=0,g.prototype.flattenItemInfo=function(){var e,t,r,i=this.items,n=this.entity_groups,s=this.meta;if(null!=s&&void 0!==s.hdlr&&void 0!==s.iinf){for(e=0;e<s.iinf.item_infos.length;e++)(r={}).id=s.iinf.item_infos[e].item_ID,i[r.id]=r,r.ref_to=[],r.name=s.iinf.item_infos[e].item_name,s.iinf.item_infos[e].protection_index>0&&(r.protection=s.ipro.protections[s.iinf.item_infos[e].protection_index-1]),s.iinf.item_infos[e].item_type?r.type=s.iinf.item_infos[e].item_type:r.type="mime",r.content_type=s.iinf.item_infos[e].content_type,r.content_encoding=s.iinf.item_infos[e].content_encoding;if(s.grpl)for(e=0;e<s.grpl.boxes.length;e++)entity_group={},entity_group.id=s.grpl.boxes[e].group_id,entity_group.entity_ids=s.grpl.boxes[e].entity_ids,entity_group.type=s.grpl.boxes[e].type,n[entity_group.id]=entity_group;if(s.iloc)for(e=0;e<s.iloc.items.length;e++){var o=s.iloc.items[e];switch(r=i[o.item_ID],0!==o.data_reference_index&&(a.warn("Item storage with reference to other files: not supported"),r.source=s.dinf.boxes[o.data_reference_index-1]),o.construction_method){case 0:break;case 1:case 2:a.warn("Item storage with construction_method : not supported")}for(r.extents=[],r.size=0,t=0;t<o.extents.length;t++)r.extents[t]={},r.extents[t].offset=o.extents[t].extent_offset+o.base_offset,r.extents[t].length=o.extents[t].extent_length,r.extents[t].alreadyRead=0,r.size+=r.extents[t].length}if(s.pitm&&(i[s.pitm.item_id].primary=!0),s.iref)for(e=0;e<s.iref.references.length;e++){var d=s.iref.references[e];for(t=0;t<d.references.length;t++)i[d.from_item_ID].ref_to.push({type:d.type,id:d.references[t]})}if(s.iprp)for(var l=0;l<s.iprp.ipmas.length;l++){var c=s.iprp.ipmas[l];for(e=0;e<c.associations.length;e++){var u=c.associations[e];if((r=i[u.id])||(r=n[u.id]),r)for(void 0===r.properties&&(r.properties={},r.properties.boxes=[]),t=0;t<u.props.length;t++){var h=u.props[t];if(h.property_index>0&&h.property_index-1<s.iprp.ipco.boxes.length){var f=s.iprp.ipco.boxes[h.property_index-1];r.properties[f.type]=f,r.properties.boxes.push(f)}}}}}},g.prototype.getItem=function(e){var t,r;if(!this.meta)return null;if(!(r=this.items[e]).data&&r.size)r.data=new Uint8Array(r.size),r.alreadyRead=0,this.itemsDataSize+=r.size,a.debug("ISOFile","Allocating item #"+e+" of size "+r.size+" (total: "+this.itemsDataSize+")");else if(r.alreadyRead===r.size)return r;for(var i=0;i<r.extents.length;i++){var n=r.extents[i];if(n.alreadyRead!==n.length){var s=this.stream.findPosition(!0,n.offset+n.alreadyRead,!1);if(!(s>-1))return null;var o=(t=this.stream.buffers[s]).byteLength-(n.offset+n.alreadyRead-t.fileStart);if(!(n.length-n.alreadyRead<=o))return a.debug("ISOFile","Getting item #"+e+" extent #"+i+" partial data (alreadyRead: "+n.alreadyRead+" offset: "+(n.offset+n.alreadyRead-t.fileStart)+" read size: "+o+" full extent size: "+n.length+" full item size: "+r.size+")"),d.memcpy(r.data.buffer,r.alreadyRead,t,n.offset+n.alreadyRead-t.fileStart,o),n.alreadyRead+=o,r.alreadyRead+=o,t.usedBytes+=o,this.stream.logBufferLevel(),null;a.debug("ISOFile","Getting item #"+e+" extent #"+i+" data (alreadyRead: "+n.alreadyRead+" offset: "+(n.offset+n.alreadyRead-t.fileStart)+" read size: "+(n.length-n.alreadyRead)+" full extent size: "+n.length+" full item size: "+r.size+")"),d.memcpy(r.data.buffer,r.alreadyRead,t,n.offset+n.alreadyRead-t.fileStart,n.length-n.alreadyRead),t.usedBytes+=n.length-n.alreadyRead,this.stream.logBufferLevel(),r.alreadyRead+=n.length-n.alreadyRead,n.alreadyRead=n.length}}return r.alreadyRead===r.size?r:null},g.prototype.releaseItem=function(e){var t=this.items[e];if(t.data){this.itemsDataSize-=t.size,t.data=null,t.alreadyRead=0;for(var r=0;r<t.extents.length;r++){t.extents[r].alreadyRead=0}return t.size}return 0},g.prototype.processItems=function(e){for(var t in this.items){var r=this.items[t];this.getItem(r.id),e&&!r.sent&&(e(r),r.sent=!0,r.data=null)}},g.prototype.hasItem=function(e){for(var t in this.items){var r=this.items[t];if(r.name===e)return r.id}return-1},g.prototype.getMetaHandler=function(){return this.meta?this.meta.hdlr.handler:null},g.prototype.getPrimaryItem=function(){return this.meta&&this.meta.pitm?this.getItem(this.meta.pitm.item_id):null},g.prototype.itemToFragmentedTrackFile=function(e){var t=e||{},r=null;if(null==(r=t.itemId?this.getItem(t.itemId):this.getPrimaryItem()))return null;var i=new g;i.discardMdatData=!1;var n={type:r.type,description_boxes:r.properties.boxes};r.properties.ispe&&(n.width=r.properties.ispe.image_width,n.height=r.properties.ispe.image_height);var s=i.addTrack(n);return s?(i.addSample(s,r.data),i):null},g.prototype.write=function(e){for(var t=0;t<this.boxes.length;t++)this.boxes[t].write(e)},g.prototype.createFragment=function(e,t,r){var i=this.getTrackById(e),n=this.getSample(i,t);if(null==n)return this.setNextSeekPositionFromSample(i.samples[t]),null;var s=r||new d;s.endianness=d.BIG_ENDIAN;var o=this.createSingleSampleMoof(n);o.write(s),o.trafs[0].truns[0].data_offset=o.size+8,a.debug("MP4Box","Adjusting data_offset with new value "+o.trafs[0].truns[0].data_offset),s.adjustUint32(o.trafs[0].truns[0].data_offset_position,o.trafs[0].truns[0].data_offset);var l=new h.mdatBox;return l.data=n.data,l.write(s),s},g.writeInitializationSegment=function(e,t,r,i){var n;a.debug("ISOFile","Generating initialization segment");var s=new d;s.endianness=d.BIG_ENDIAN,e.write(s);var o=t.add("mvex");for(r&&o.add("mehd").set("fragment_duration",r),n=0;n<t.traks.length;n++)o.add("trex").set("track_id",t.traks[n].tkhd.track_id).set("default_sample_description_index",1).set("default_sample_duration",i).set("default_sample_size",0).set("default_sample_flags",65536);return t.write(s),s.buffer},g.prototype.save=function(e){var t=new d;t.endianness=d.BIG_ENDIAN,this.write(t),t.save(e)},g.prototype.getBuffer=function(){var e=new d;return e.endianness=d.BIG_ENDIAN,this.write(e),e.buffer},g.prototype.initializeSegmentation=function(){var e,t,r,i;for(null===this.onSegment&&a.warn("MP4Box","No segmentation callback set!"),this.isFragmentationInitialized||(this.isFragmentationInitialized=!0,this.nextMoofNumber=0,this.resetTables()),t=[],e=0;e<this.fragmentedTracks.length;e++){var n=new h.moovBox;n.mvhd=this.moov.mvhd,n.boxes.push(n.mvhd),r=this.getTrackById(this.fragmentedTracks[e].id),n.boxes.push(r),n.traks.push(r),(i={}).id=r.tkhd.track_id,i.user=this.fragmentedTracks[e].user,i.buffer=g.writeInitializationSegment(this.ftyp,n,this.moov.mvex&&this.moov.mvex.mehd?this.moov.mvex.mehd.fragment_duration:void 0,this.moov.traks[e].samples.length>0?this.moov.traks[e].samples[0].duration:0),t.push(i)}return t},h.Box.prototype.printHeader=function(e){this.size+=8,this.size>l&&(this.size+=8),"uuid"===this.type&&(this.size+=16),e.log(e.indent+"size:"+this.size),e.log(e.indent+"type:"+this.type)},h.FullBox.prototype.printHeader=function(e){this.size+=4,h.Box.prototype.printHeader.call(this,e),e.log(e.indent+"version:"+this.version),e.log(e.indent+"flags:"+this.flags)},h.Box.prototype.print=function(e){this.printHeader(e)},h.ContainerBox.prototype.print=function(e){this.printHeader(e);for(var t=0;t<this.boxes.length;t++)if(this.boxes[t]){var r=e.indent;e.indent+=" ",this.boxes[t].print(e),e.indent=r}},g.prototype.print=function(e){e.indent="";for(var t=0;t<this.boxes.length;t++)this.boxes[t]&&this.boxes[t].print(e)},h.mvhdBox.prototype.print=function(e){h.FullBox.prototype.printHeader.call(this,e),e.log(e.indent+"creation_time: "+this.creation_time),e.log(e.indent+"modification_time: "+this.modification_time),e.log(e.indent+"timescale: "+this.timescale),e.log(e.indent+"duration: "+this.duration),e.log(e.indent+"rate: "+this.rate),e.log(e.indent+"volume: "+(this.volume>>8)),e.log(e.indent+"matrix: "+this.matrix.join(", ")),e.log(e.indent+"next_track_id: "+this.next_track_id)},h.tkhdBox.prototype.print=function(e){h.FullBox.prototype.printHeader.call(this,e),e.log(e.indent+"creation_time: "+this.creation_time),e.log(e.indent+"modification_time: "+this.modification_time),e.log(e.indent+"track_id: "+this.track_id),e.log(e.indent+"duration: "+this.duration),e.log(e.indent+"volume: "+(this.volume>>8)),e.log(e.indent+"matrix: "+this.matrix.join(", ")),e.log(e.indent+"layer: "+this.layer),e.log(e.indent+"alternate_group: "+this.alternate_group),e.log(e.indent+"width: "+this.width),e.log(e.indent+"height: "+this.height)};var y={createFile:function(e,t){var r=void 0===e||e,i=new g(t);return i.discardMdatData=!r,i}};t.createFile=y.createFile}));function Oi(e){return e.reduce(((e,t)=>256*e+t))}function Gi(e){const t=[101,103,119,99],r=e.length-28,i=e.slice(r,r+t.length);return t.every(((e,t)=>e===i[t]))}Ni.Log,Ni.MP4BoxStream,Ni.DataStream,Ni.MultiBufferStream,Ni.MPEG4DescriptorParser,Ni.BoxParser,Ni.XMLSubtitlein4Parser,Ni.Textin4Parser,Ni.ISOFile,Ni.createFile;class Hi{constructor(){this.s=null,this.a=null,this.l=0,this.c=0,this.u=1/0,this.A=!1,this.d=!1,this.r=4194304,this.n=new Uint8Array([30,158,90,33,244,57,83,165,2,70,35,87,215,231,226,108]),this.t=this.n.slice().reverse()}destroy(){this.s=null,this.a=null,this.l=0,this.c=0,this.u=1/0,this.A=!1,this.d=!1,this.r=4194304,this.n=null,this.t=null}transport(e){if(!this.s&&this.l>50)return e;if(this.l++,this.d)return e;const t=new Uint8Array(e);if(this.A){if(!(this.c<this.u))return this.a&&this.s?(this.a.set(t,this.r),this.s.parse(null,this.r,t.byteLength),this.a.slice(this.r,this.r+t.byteLength)):(console.error("video_error_2"),this.d=!0,e);Gi(t)&&this.c++}else{const r=function(e,t){const r=function(e,t){for(let r=0;r<e.byteLength-t.length;r++)for(let i=0;i<t.length&&e[r+i]===t[i];i++)if(i===t.length-1)return r;return null}(e,t);if(r){const t=Oi(e.slice(r+16,r+16+8));return[t,Oi(e.slice(r+24,r+24+8)),function(e){return e.map((e=>~e))}(e.slice(r+32,r+32+t))]}return null}(t,this.t);if(!r)return e;const i=function(e){try{if("object"!=typeof WebAssembly||"function"!=typeof WebAssembly.instantiate)throw null;{const e=new WebAssembly.Module(Uint8Array.of(0,97,115,109,1,0,0,0));if(!(e instanceof WebAssembly.Module&&new WebAssembly.Instance(e)instanceof WebAssembly.Instance))throw null}}catch(e){return new Error("video_error_4")}let t;try{t={env:{__handle_stack_overflow:()=>e(new Error("video_error_1")),memory:new WebAssembly.Memory({initial:256,maximum:256})}}}catch(e){return new Error("video_error_5")}return t}(e);if(i instanceof Error)return console.error(i.message),this.d=!0,e;this.A=!0,this.u=r[1],Gi(t)&&this.c++,WebAssembly.instantiate(r[2],i).then((e=>{if("function"!=typeof(t=e.instance.exports).parse||"object"!=typeof t.memory)return this.d=!0,void console.error("video_error_3");var t;this.s=e.instance.exports,this.a=new Uint8Array(this.s.memory.buffer)})).catch((e=>{this.d=!0,console.error("video_error_6")}))}return e}}const $i=16,Vi=[214,144,233,254,204,225,61,183,22,182,20,194,40,251,44,5,43,103,154,118,42,190,4,195,170,68,19,38,73,134,6,153,156,66,80,244,145,239,152,122,51,84,11,67,237,207,172,98,228,179,28,169,201,8,232,149,128,223,148,250,117,143,63,166,71,7,167,252,243,115,23,186,131,89,60,25,230,133,79,168,104,107,129,178,113,100,218,139,248,235,15,75,112,86,157,53,30,36,14,94,99,88,209,162,37,34,124,59,1,33,120,135,212,0,70,87,159,211,39,82,76,54,2,231,160,196,200,158,234,191,138,210,64,199,56,181,163,247,242,206,249,97,21,161,224,174,93,164,155,52,26,85,173,147,50,48,245,140,177,227,29,246,226,46,130,102,202,96,192,41,35,171,13,83,78,111,213,219,55,69,222,253,142,47,3,255,106,114,109,108,91,81,141,27,175,146,187,221,188,127,17,217,92,65,31,16,90,216,10,193,49,136,165,205,123,189,45,116,208,18,184,229,180,176,137,105,151,74,12,150,119,126,101,185,241,9,197,110,198,132,24,240,125,236,58,220,77,32,121,238,95,62,215,203,57,72],Wi=[462357,472066609,943670861,1415275113,1886879365,2358483617,2830087869,3301692121,3773296373,4228057617,404694573,876298825,1347903077,1819507329,2291111581,2762715833,3234320085,3705924337,4177462797,337322537,808926789,1280531041,1752135293,2223739545,2695343797,3166948049,3638552301,4110090761,269950501,741554753,1213159005,1684763257];function ji(e){const t=[];for(let r=0,i=e.length;r<i;r+=2)t.push(parseInt(e.substr(r,2),16));return t}function qi(e){return e.map((e=>1===(e=e.toString(16)).length?"0"+e:e)).join("")}function Yi(e){const t=[];for(let r=0,i=e.length;r<i;r++){const i=e.codePointAt(r);if(i<=127)t.push(i);else if(i<=2047)t.push(192|i>>>6),t.push(128|63&i);else if(i<=55295||i>=57344&&i<=65535)t.push(224|i>>>12),t.push(128|i>>>6&63),t.push(128|63&i);else{if(!(i>=65536&&i<=1114111))throw t.push(i),new Error("input is not supported");r++,t.push(240|i>>>18&28),t.push(128|i>>>12&63),t.push(128|i>>>6&63),t.push(128|63&i)}}return t}function Ki(e){const t=[];for(let r=0,i=e.length;r<i;r++)e[r]>=240&&e[r]<=247?(t.push(String.fromCodePoint(((7&e[r])<<18)+((63&e[r+1])<<12)+((63&e[r+2])<<6)+(63&e[r+3]))),r+=3):e[r]>=224&&e[r]<=239?(t.push(String.fromCodePoint(((15&e[r])<<12)+((63&e[r+1])<<6)+(63&e[r+2]))),r+=2):e[r]>=192&&e[r]<=223?(t.push(String.fromCodePoint(((31&e[r])<<6)+(63&e[r+1]))),r++):t.push(String.fromCodePoint(e[r]));return t.join("")}function Xi(e,t){const r=31&t;return e<<r|e>>>32-r}function Zi(e){return(255&Vi[e>>>24&255])<<24|(255&Vi[e>>>16&255])<<16|(255&Vi[e>>>8&255])<<8|255&Vi[255&e]}function Ji(e){return e^Xi(e,2)^Xi(e,10)^Xi(e,18)^Xi(e,24)}function Qi(e){return e^Xi(e,13)^Xi(e,23)}function en(e,t,r){const i=new Array(4),n=new Array(4);for(let t=0;t<4;t++)n[0]=255&e[4*t],n[1]=255&e[4*t+1],n[2]=255&e[4*t+2],n[3]=255&e[4*t+3],i[t]=n[0]<<24|n[1]<<16|n[2]<<8|n[3];for(let e,t=0;t<32;t+=4)e=i[1]^i[2]^i[3]^r[t+0],i[0]^=Ji(Zi(e)),e=i[2]^i[3]^i[0]^r[t+1],i[1]^=Ji(Zi(e)),e=i[3]^i[0]^i[1]^r[t+2],i[2]^=Ji(Zi(e)),e=i[0]^i[1]^i[2]^r[t+3],i[3]^=Ji(Zi(e));for(let e=0;e<16;e+=4)t[e]=i[3-e/4]>>>24&255,t[e+1]=i[3-e/4]>>>16&255,t[e+2]=i[3-e/4]>>>8&255,t[e+3]=255&i[3-e/4]}function tn(e,t,r){const i=new Array(4),n=new Array(4);for(let t=0;t<4;t++)n[0]=255&e[0+4*t],n[1]=255&e[1+4*t],n[2]=255&e[2+4*t],n[3]=255&e[3+4*t],i[t]=n[0]<<24|n[1]<<16|n[2]<<8|n[3];i[0]^=2746333894,i[1]^=1453994832,i[2]^=1736282519,i[3]^=2993693404;for(let e,r=0;r<32;r+=4)e=i[1]^i[2]^i[3]^Wi[r+0],t[r+0]=i[0]^=Qi(Zi(e)),e=i[2]^i[3]^i[0]^Wi[r+1],t[r+1]=i[1]^=Qi(Zi(e)),e=i[3]^i[0]^i[1]^Wi[r+2],t[r+2]=i[2]^=Qi(Zi(e)),e=i[0]^i[1]^i[2]^Wi[r+3],t[r+3]=i[3]^=Qi(Zi(e));if(0===r)for(let e,r=0;r<16;r++)e=t[r],t[r]=t[31-r],t[31-r]=e}function rn(e,t,r){let{padding:i="pkcs#7",mode:n,iv:s=[],output:a="string"}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if("cbc"===n&&("string"==typeof s&&(s=ji(s)),16!==s.length))throw new Error("iv is invalid");if("string"==typeof t&&(t=ji(t)),16!==t.length)throw new Error("key is invalid");if(e="string"==typeof e?0!==r?Yi(e):ji(e):[...e],("pkcs#5"===i||"pkcs#7"===i)&&0!==r){const t=$i-e.length%$i;for(let r=0;r<t;r++)e.push(t)}const o=new Array(32);tn(t,o,r);const d=[];let l=s,c=e.length,u=0;for(;c>=$i;){const t=e.slice(u,u+16),i=new Array(16);if("cbc"===n)for(let e=0;e<$i;e++)0!==r&&(t[e]^=l[e]);en(t,i,o);for(let e=0;e<$i;e++)"cbc"===n&&0===r&&(i[e]^=l[e]),d[u+e]=i[e];"cbc"===n&&(l=0!==r?i:t),c-=$i,u+=$i}if(("pkcs#5"===i||"pkcs#7"===i)&&0===r){const e=d.length,t=d[e-1];for(let r=1;r<=t;r++)if(d[e-r]!==t)throw new Error("padding is invalid");d.splice(e-t,t)}return"array"!==a?0!==r?qi(d):Ki(d):d}function nn(e){return e[3]|e[2]<<8|e[1]<<16|e[0]<<24}function sn(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const i=e.byteLength;let n=5;for(;n<i;){let s=nn(e.slice(n,n+4));if(s>i)break;let a=e[n+4],o=!1;if(r?(a=a>>>1&63,o=[0,1,2,3,4,5,6,7,8,9,16,17,18,19,20,21].includes(a)):(a&=31,o=1===a||5===a),o){const r=rn(e.slice(n+4+2,n+4+s),t,0,{padding:"none",output:"array"});e.set(r,n+4+2)}n=n+4+s}return e}class an{on(e,t,r){const i=this.e||(this.e={});return(i[e]||(i[e]=[])).push({fn:t,ctx:r}),this}once(e,t,r){const i=this;function n(){i.off(e,n);for(var s=arguments.length,a=new Array(s),o=0;o<s;o++)a[o]=arguments[o];t.apply(r,a)}return n._=t,this.on(e,n,r)}emit(e){const t=((this.e||(this.e={}))[e]||[]).slice();for(var r=arguments.length,i=new Array(r>1?r-1:0),n=1;n<r;n++)i[n-1]=arguments[n];for(let e=0;e<t.length;e+=1)t[e].fn.apply(t[e].ctx,i);return this}off(e,t){const r=this.e||(this.e={});if(!e)return Object.keys(r).forEach((e=>{delete r[e]})),void delete this.e;const i=r[e],n=[];if(i&&t)for(let e=0,r=i.length;e<r;e+=1)i[e].fn!==t&&i[e].fn._!==t&&n.push(i[e]);return n.length?r[e]=n:delete r[e],this}}const on={init:0,findFirstStartCode:1,findSecondStartCode:2};class dn extends an{constructor(e){super(),this.player=e,this.isDestroyed=!1,this.reset()}destroy(){this.isDestroyed=!1,this.off(),this.reset()}reset(){this.stats=on.init,this.tempBuffer=new Uint8Array(0),this.parsedOffset=0,this.versionLayer=0}dispatch(e,t){let r=new Uint8Array(this.tempBuffer.length+e.length);for(r.set(this.tempBuffer,0),r.set(e,this.tempBuffer.length),this.tempBuffer=r;!this.isDestroyed;){if(this.state==on.Init){let e=!1;for(;this.tempBuffer.length-this.parsedOffset>=2&&!this.isDestroyed;)if(255==this.tempBuffer[this.parsedOffset]){if(!(!1&this.tempBuffer[this.parsedOffset+1])){this.versionLayer=this.tempBuffer[this.parsedOffset+1],this.state=on.findFirstStartCode,this.fisrtStartCodeOffset=this.parsedOffset,this.parsedOffset+=2,e=!0;break}this.parsedOffset++}else this.parsedOffset++;if(e)continue;break}if(this.state==on.findFirstStartCode){let e=!1;for(;this.tempBuffer.length-this.parsedOffset>=2&&!this.isDestroyed;)if(255==this.tempBuffer[this.parsedOffset]){if(this.tempBuffer[this.parsedOffset+1]==this.versionLayer){this.state=on.findSecondStartCode,this.secondStartCodeOffset=this.parsedOffset,this.parsedOffset+=2,e=!0;break}this.parsedOffset++}else this.parsedOffset++;if(e)continue;break}if(this.state==on.findSecondStartCode){let e=this.tempBuffer.slice(this.fisrtStartCodeOffset,this.secondStartCodeOffset);this.emit("data",e,t),this.tempBuffer=this.tempBuffer.slice(this.secondStartCodeOffset),this.fisrtStartCodeOffset=0,this.parsedOffset=2,this.state=on.findFirstStartCode}}}}function ln(e,t,r){for(let i=2;i<e.length;++i){const n=i-2,s=t[n%t.length],a=r[n%r.length];e[i]=e[i]^s^a}return e}function cn(e){return e[3]|e[2]<<8|e[1]<<16|e[0]<<24}function un(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];const n=e.byteLength;let s=5;for(;s<n;){let a=cn(e.slice(s,s+4));if(a>n)break;let o=e[s+4],d=!1;if(i?(o=o>>>1&63,d=[0,1,2,3,4,5,6,7,8,9,16,17,18,19,20,21].includes(o)):(o&=31,d=1===o||5===o),d){const i=ln(e.slice(s+4,s+4+a),t,r);e.set(i,s+4)}s=s+4+a}return e}function hn(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];if((t=t.filter(Boolean)).length<2)return t[0];const i=new Uint8Array(t.reduce(((e,t)=>e+t.byteLength),0));let n=0;return t.forEach((e=>{i.set(e,n),n+=e.byteLength})),i}class fn{constructor(e){this.destroys=[],this.proxy=this.proxy.bind(this),this.master=e}proxy(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if(!e)return;if(Array.isArray(t))return t.map((t=>this.proxy(e,t,r,i)));e.addEventListener(t,r,i);const n=()=>{lr(e.removeEventListener)&&e.removeEventListener(t,r,i)};return this.destroys.push(n),n}destroy(){this.master.debug&&this.master.debug.log("Events","destroy"),this.destroys.forEach((e=>e())),this.destroys=[]}}class pn{static init(){pn.types={avc1:[],avcC:[],hvc1:[],hvcC:[],av01:[],av1C:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[],".mp3":[],Opus:[],dOps:[],"ac-3":[],dac3:[],"ec-3":[],dec3:[]};for(let e in pn.types)pn.types.hasOwnProperty(e)&&(pn.types[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]);let e=pn.constants={};e.FTYP=new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,97,118,99,49]),e.STSD_PREFIX=new Uint8Array([0,0,0,0,0,0,0,1]),e.STTS=new Uint8Array([0,0,0,0,0,0,0,0]),e.STSC=e.STCO=e.STTS,e.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),e.HDLR_VIDEO=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),e.HDLR_AUDIO=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),e.DREF=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),e.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),e.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])}static box(e){let t=8,r=null,i=Array.prototype.slice.call(arguments,1),n=i.length;for(let e=0;e<n;e++)t+=i[e].byteLength;r=new Uint8Array(t),r[0]=t>>>24&255,r[1]=t>>>16&255,r[2]=t>>>8&255,r[3]=255&t,r.set(e,4);let s=8;for(let e=0;e<n;e++)r.set(i[e],s),s+=i[e].byteLength;return r}static generateInitSegment(e){let t=pn.box(pn.types.ftyp,pn.constants.FTYP),r=pn.moov(e),i=new Uint8Array(t.byteLength+r.byteLength);return i.set(t,0),i.set(r,t.byteLength),i}static moov(e){let t=pn.mvhd(e.timescale,e.duration),r=pn.trak(e),i=pn.mvex(e);return pn.box(pn.types.moov,t,r,i)}static mvhd(e,t){return pn.box(pn.types.mvhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,e>>>24&255,e>>>16&255,e>>>8&255,255&e,t>>>24&255,t>>>16&255,t>>>8&255,255&t,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]))}static trak(e){return pn.box(pn.types.trak,pn.tkhd(e),pn.mdia(e))}static tkhd(e){let t=e.id,r=e.duration,i=e.presentWidth,n=e.presentHeight;return pn.box(pn.types.tkhd,new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t,0,0,0,0,r>>>24&255,r>>>16&255,r>>>8&255,255&r,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,i>>>8&255,255&i,0,0,n>>>8&255,255&n,0,0]))}static mdia(e){return pn.box(pn.types.mdia,pn.mdhd(e),pn.hdlr(e),pn.minf(e))}static mdhd(e){let t=e.timescale,r=e.duration;return pn.box(pn.types.mdhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t,r>>>24&255,r>>>16&255,r>>>8&255,255&r,85,196,0,0]))}static hdlr(e){let t=null;return t="audio"===e.type?pn.constants.HDLR_AUDIO:pn.constants.HDLR_VIDEO,pn.box(pn.types.hdlr,t)}static minf(e){let t=null;return t="audio"===e.type?pn.box(pn.types.smhd,pn.constants.SMHD):pn.box(pn.types.vmhd,pn.constants.VMHD),pn.box(pn.types.minf,t,pn.dinf(),pn.stbl(e))}static dinf(){return pn.box(pn.types.dinf,pn.box(pn.types.dref,pn.constants.DREF))}static stbl(e){return pn.box(pn.types.stbl,pn.stsd(e),pn.box(pn.types.stts,pn.constants.STTS),pn.box(pn.types.stsc,pn.constants.STSC),pn.box(pn.types.stsz,pn.constants.STSZ),pn.box(pn.types.stco,pn.constants.STCO))}static stsd(e){return"audio"===e.type?"mp3"===e.audioType?pn.box(pn.types.stsd,pn.constants.STSD_PREFIX,pn.mp3(e)):pn.box(pn.types.stsd,pn.constants.STSD_PREFIX,pn.mp4a(e)):"avc"===e.videoType?pn.box(pn.types.stsd,pn.constants.STSD_PREFIX,pn.avc1(e)):pn.box(pn.types.stsd,pn.constants.STSD_PREFIX,pn.hvc1(e))}static mp3(e){let t=e.channelCount,r=e.audioSampleRate,i=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,t,0,16,0,0,0,0,r>>>8&255,255&r,0,0]);return pn.box(pn.types[".mp3"],i)}static mp4a(e){let t=e.channelCount,r=e.audioSampleRate,i=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,t,0,16,0,0,0,0,r>>>8&255,255&r,0,0]);return pn.box(pn.types.mp4a,i,pn.esds(e))}static esds(e){let t=e.config||[],r=t.length,i=new Uint8Array([0,0,0,0,3,23+r,0,1,0,4,15+r,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([r]).concat(t).concat([6,1,2]));return pn.box(pn.types.esds,i)}static avc1(e){let t=e.avcc;const r=e.codecWidth,i=e.codecHeight;let n=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,r>>>8&255,255&r,i>>>8&255,255&i,0,72,0,0,0,72,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return pn.box(pn.types.avc1,n,pn.box(pn.types.avcC,t))}static hvc1(e){let t=e.avcc;const r=e.codecWidth,i=e.codecHeight;let n=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,r>>>8&255,255&r,i>>>8&255,255&i,0,72,0,0,0,72,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return pn.box(pn.types.hvc1,n,pn.box(pn.types.hvcC,t))}static mvex(e){return pn.box(pn.types.mvex,pn.trex(e))}static trex(e){let t=e.id,r=new Uint8Array([0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return pn.box(pn.types.trex,r)}static moof(e,t){return pn.box(pn.types.moof,pn.mfhd(e.sequenceNumber),pn.traf(e,t))}static mfhd(e){let t=new Uint8Array([0,0,0,0,e>>>24&255,e>>>16&255,e>>>8&255,255&e]);return pn.box(pn.types.mfhd,t)}static traf(e,t){let r=e.id,i=pn.box(pn.types.tfhd,new Uint8Array([0,0,0,0,r>>>24&255,r>>>16&255,r>>>8&255,255&r])),n=pn.box(pn.types.tfdt,new Uint8Array([0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t])),s=pn.sdtp(e),a=pn.trun(e,s.byteLength+16+16+8+16+8+8);return pn.box(pn.types.traf,i,n,a,s)}static sdtp(e){let t=new Uint8Array(5),r=e.flags;return t[4]=r.isLeading<<6|r.dependsOn<<4|r.isDependedOn<<2|r.hasRedundancy,pn.box(pn.types.sdtp,t)}static trun(e,t){let r=new Uint8Array(28);t+=36,r.set([0,0,15,1,0,0,0,1,t>>>24&255,t>>>16&255,t>>>8&255,255&t],0);let i=e.duration,n=e.size,s=e.flags,a=e.cts;return r.set([i>>>24&255,i>>>16&255,i>>>8&255,255&i,n>>>24&255,n>>>16&255,n>>>8&255,255&n,s.isLeading<<2|s.dependsOn,s.isDependedOn<<6|s.hasRedundancy<<4|s.isNonSync,0,0,a>>>24&255,a>>>16&255,a>>>8&255,255&a],12),pn.box(pn.types.trun,r)}static mdat(e){return pn.box(pn.types.mdat,e)}}pn.init();var _n,mn=Nt((function(e){e.exports=function(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e},e.exports.__esModule=!0,e.exports.default=e.exports}));(_n=mn)&&_n.__esModule&&Object.prototype.hasOwnProperty.call(_n,"default")&&_n.default;const gn=[44100,48e3,32e3,0],yn=[22050,24e3,16e3,0],vn=[11025,12e3,8e3,0],bn=[0,32,64,96,128,160,192,224,256,288,320,352,384,416,448,-1],wn=[0,32,48,56,64,80,96,112,128,160,192,224,256,320,384,-1],Sn=[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320,-1];function En(e){if(e.length<4)return void console.error("Invalid MP3 packet, header missing!");let t=new Uint8Array(e.buffer),r=null;if(255!==t[0])return void console.error("Invalid MP3 packet, first byte != 0xFF ");let i=t[1]>>>3&3,n=(6&t[1])>>1,s=(240&t[2])>>>4,a=(12&t[2])>>>2,o=3!==(t[3]>>>6&3)?2:1,d=0,l=0;switch(i){case 0:d=vn[a];break;case 2:d=yn[a];break;case 3:d=gn[a]}switch(n){case 1:s<Sn.length&&(l=Sn[s]);break;case 2:s<wn.length&&(l=wn[s]);break;case 3:s<bn.length&&(l=bn[s])}return r={bitRate:l,samplingRate:d,channelCount:o,codec:"mp3",originalCodec:"mp3",audioType:"mp3"},r}const An=3,Bn=4,xn=6,Un=15,Tn=17,kn=129,Cn=135,Dn=21,In=134,Fn=27,Pn=36;class Ln{constructor(){this.slices=[],this.total_length=0,this.expected_length=0,this.random_access_indicator=0}}class Mn{constructor(){this.pid=null,this.data=null,this.stream_type=null,this.random_access_indicator=null}}class Rn{constructor(){this.pid=null,this.stream_id=null,this.len=null,this.data=null,this.pts=null,this.nearest_pts=null,this.dts=null}}const zn=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350];class Nn{constructor(){this.mimeType=null,this.duration=null,this.hasAudio=null,this.hasVideo=null,this.audioCodec=null,this.videoCodec=null,this.audioDataRate=null,this.videoDataRate=null,this.audioSampleRate=null,this.audioChannelCount=null,this.width=null,this.height=null,this.fps=null,this.profile=null,this.level=null,this.refFrames=null,this.chromaFormat=null,this.sarNum=null,this.sarDen=null,this.metadata=null,this.segments=null,this.segmentCount=null,this.hasKeyframesIndex=null,this.keyframesIndex=null}isComplete(){let e=!1===this.hasAudio||!0===this.hasAudio&&null!=this.audioCodec&&null!=this.audioSampleRate&&null!=this.audioChannelCount,t=!1===this.hasVideo||!0===this.hasVideo&&null!=this.videoCodec&&null!=this.width&&null!=this.height&&null!=this.fps&&null!=this.profile&&null!=this.level&&null!=this.refFrames&&null!=this.chromaFormat&&null!=this.sarNum&&null!=this.sarDen;return null!=this.mimeType&&e&&t}isSeekable(){return!0===this.hasKeyframesIndex}getNearestKeyframe(e){if(null==this.keyframesIndex)return null;let t=this.keyframesIndex,r=this._search(t.times,e);return{index:r,milliseconds:t.times[r],fileposition:t.filepositions[r]}}_search(e,t){let r=0,i=e.length-1,n=0,s=0,a=i;for(t<e[0]&&(r=0,s=a+1);s<=a;){if(n=s+Math.floor((a-s)/2),n===i||t>=e[n]&&t<e[n+1]){r=n;break}e[n]<t?s=n+1:a=n-1}return r}}class On{constructor(e){let t=null,r=e.audio_object_type,i=e.audio_object_type,n=e.sampling_freq_index,s=e.channel_config,a=0,o=navigator.userAgent.toLowerCase();-1!==o.indexOf("firefox")?n>=6?(i=5,t=new Array(4),a=n-3):(i=2,t=new Array(2),a=n):-1!==o.indexOf("android")?(i=2,t=new Array(2),a=n):(i=5,a=n,t=new Array(4),n>=6?a=n-3:1===s&&(i=2,t=new Array(2),a=n)),t[0]=i<<3,t[0]|=(15&n)>>>1,t[1]=(15&n)<<7,t[1]|=(15&s)<<3,5===i&&(t[1]|=(15&a)>>>1,t[2]=(1&a)<<7,t[2]|=8,t[3]=0),this.config=t,this.sampling_rate=zn[n],this.sampling_index=n,this.channel_count=s,this.object_type=i,this.original_object_type=r,this.codec_mimetype="mp4a.40."+i,this.original_codec_mimetype="mp4a.40."+r}}Date.now||(Date.now=function(){return(new Date).getTime()}),s({printErr:function(e){console.warn("JbPro[❌❌❌][worker]:",e)}}).then((e=>{!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=[],n=[],s={},a=new AbortController,o=null,d=null,l=null,c=null,u=!1,h=null,w=null,S=!1,x=!1,ve=!!gr(r),be=!1,$e=null,Ke=null,st=null,ht=[],ft=null,pt=null,_t=0,St=0,Et=null,xt=null,zt=0,Nt=0,Ot=!1,Gt=!1,Ht=!1,qt=null,Zt=null,ir=null,br=!1,wr=!0,Sr=()=>{const e=_r();return{debug:e.debug,debugLevel:e.debugLevel,debugUuid:e.debugUuid,useOffscreen:e.useOffscreen,useWCS:e.useWCS,useMSE:e.useMSE,videoBuffer:e.videoBuffer,videoBufferDelay:e.videoBufferDelay,openWebglAlignment:e.openWebglAlignment,playType:e.playType,hasAudio:e.hasAudio,hasVideo:e.hasVideo,playbackRate:1,playbackForwardMaxRateDecodeIFrame:e.playbackForwardMaxRateDecodeIFrame,playbackIsCacheBeforeDecodeForFpsRender:e.playbackConfig.isCacheBeforeDecodeForFpsRender,sampleRate:0,networkDelay:e.networkDelay,visibility:!0,useSIMD:e.useSIMD,isRecording:!1,recordType:e.recordType,isNakedFlow:e.isNakedFlow,checkFirstIFrame:e.checkFirstIFrame,audioBufferSize:1024,isM7sCrypto:e.isM7sCrypto,m7sCryptoAudio:e.m7sCryptoAudio,cryptoKey:e.cryptoKey,cryptoIV:e.cryptoIV,isSm4Crypto:e.isSm4Crypto,sm4CryptoKey:e.sm4CryptoKey,isXorCrypto:e.isXorCrypto,isHls265:!1,isFlv:e.isFlv,isFmp4:e.isFmp4,isMpeg4:e.isMpeg4,isTs:e.isTs,isFmp4Private:e.isFmp4Private,isEmitSEI:e.isEmitSEI,isRecordTypeFlv:!1,isWasmMp4:!1,isChrome:!1,isDropSameTimestampGop:e.isDropSameTimestampGop,mseDecodeAudio:e.mseDecodeAudio,nakedFlowH265DemuxUseNew:e.nakedFlowH265DemuxUseNew,mseDecoderUseWorker:e.mseDecoderUseWorker,mseAutoCleanupSourceBuffer:e.mseAutoCleanupSourceBuffer,mseAutoCleanupMaxBackwardDuration:e.mseAutoCleanupMaxBackwardDuration,mseAutoCleanupMinBackwardDuration:e.mseAutoCleanupMinBackwardDuration,mseCorrectTimeDuration:e.mseCorrectTimeDuration,mseCorrectAudioTimeDuration:e.mseCorrectAudioTimeDuration}};"VideoEncoder"in self&&(s={hasInit:!1,isEmitInfo:!1,offscreenCanvas:null,offscreenCanvasCtx:null,decoder:new VideoDecoder({output:function(e){if(s.isEmitInfo||(ii.debug.log("worker","Webcodecs Video Decoder initSize"),postMessage({cmd:T,w:e.codedWidth,h:e.codedHeight}),s.isEmitInfo=!0,s.offscreenCanvas=new OffscreenCanvas(e.codedWidth,e.codedHeight),s.offscreenCanvasCtx=s.offscreenCanvas.getContext("2d")),lr(e.createImageBitmap))e.createImageBitmap().then((t=>{s.offscreenCanvasCtx.drawImage(t,0,0,e.codedWidth,e.codedHeight);let r=s.offscreenCanvas.transferToImageBitmap();postMessage({cmd:k,buffer:r,delay:ii.delay,ts:0},[r]),hr(e)}));else{s.offscreenCanvasCtx.drawImage(e,0,0,e.codedWidth,e.codedHeight);let t=s.offscreenCanvas.transferToImageBitmap();postMessage({cmd:k,buffer:t,delay:ii.delay,ts:0},[t]),hr(e)}},error:function(e){ii.debug.error("worker","VideoDecoder error",e)}}),decode:function(e,t,r){const i=e[0]>>4==1;if(s.hasInit){const r=new EncodedVideoChunk({data:e.slice(5),timestamp:t,type:i?dt:lt});s.decoder.decode(r)}else if(i&&0===e[1]){const t=15&e[0];postMessage({cmd:L,code:t});const r=new Uint8Array(e);postMessage({cmd:M,buffer:r,codecId:t},[r.buffer]);let i=null,n=null;const a=e.slice(5);t===Ie?(n=Tr(a),i={codec:n.codec,description:a}):t===Fe&&(n=Vr(a),i={codec:n.codec,description:a}),n&&n.codecWidth&&n.codecHeight&&(i.codedHeight=n.codecHeight,i.codedWidth=n.codecWidth);try{s.decoder.configure(i),s.hasInit=!0}catch(e){ii.debug.log("worker","VideoDecoder configure error",e.code,e)}}},reset(){s.hasInit=!1,s.isEmitInfo=!1,s.offscreenCanvas=null,s.offscreenCanvasCtx=null}});let Er=function(){if(br=!0,ii.fetchStatus!==kt||yr(ii._opt.isChrome)){if(a)try{a.abort(),a=null}catch(e){ii.debug.log("worker","abort catch",e)}}else a=null,ii.debug.log("worker",`abort() and not abortController.abort() _status is ${ii.fetchStatus} and _isChrome is ${ii._opt.isChrome}`)},Ur={init(){Ur.lastBuf=null,Ur.vps=null,Ur.sps=null,Ur.pps=null,Ur.streamType=null,Ur.localDts=0,Ur.isSendSeqHeader=!1},destroy(){Ur.lastBuf=null,Ur.vps=null,Ur.sps=null,Ur.pps=null,Ur.streamType=null,Ur.localDts=0,Ur.isSendSeqHeader=!1},dispatch(e){const t=new Uint8Array(e);Ur.extractNALu$2(t)},getNaluDts(){let e=Ur.localDts;return Ur.localDts=Ur.localDts+40,e},getNaluAudioDts(){const e=ii._opt.sampleRate,t=ii._opt.audioBufferSize;return Ur.localDts+parseInt(t/e*1e3)},extractNALu(e){let t,r,i=0,n=e.byteLength,s=0,a=[];for(;i<n;)switch(t=e[i++],s){case 0:0===t&&(s=1);break;case 1:s=0===t?2:0;break;case 2:case 3:0===t?s=3:1===t&&i<n?(r&&a.push(e.subarray(r,i-s-1)),r=i,s=0):s=0}return r&&a.push(e.subarray(r,n)),a},extractNALu$2(e){let t=null;if(!e||e.byteLength<1)return;Ur.lastBuf?(t=new Uint8Array(e.byteLength+Ur.lastBuf.length),t.set(Ur.lastBuf),t.set(new Uint8Array(e),Ur.lastBuf.length)):t=new Uint8Array(e);let r=0,i=-1,n=-2;const s=new Array;for(let e=0;e<t.length;e+=2){const r=t[e],a=t[e+1];0==i&&0==r&&0==a?s.push(e-1):1==a&&0==r&&0==i&&0==n&&s.push(e-2),n=r,i=a}if(s.length>1)for(let e=0;e<s.length-1;++e){const i=t.subarray(s[e],s[e+1]+1);Ur.handleNALu(i),r=s[e+1]}else r=s[0];if(0!=r&&r<t.length)Ur.lastBuf=t.subarray(r);else{Ur.lastBuf||(Ur.lastBuf=t);const r=new Uint8Array(Ur.lastBuf.length+e.byteLength);r.set(Ur.lastBuf),r.set(new Uint8Array(e),Ur.lastBuf.length),Ur.lastBuf=r}},handleNALu(e){e.byteLength<=4?ii.debug.warn("worker",`handleNALu nalu byteLength is ${e.byteLength} <= 4`):(e=e.slice(4),Ur.handleVideoNalu(e))},handleVideoNalu(e){if(Ur.streamType||(Ur.streamType=cr(e),qt=Ur.streamType===Me),Ur.streamType===Le){const t=Ur.handleAddNaluStartCode(e),r=Ur.extractNALu(t);if(0===r.length)return void ii.debug.warn("worker","handleVideoNalu","h264 naluList.length === 0");const i=[];if(r.forEach((e=>{const t=Fr(e);t===He||t===Ge?Ur.handleVideoH264Nalu(e):Lr(t)&&i.push(e)})),1===i.length)Ur.handleVideoH264Nalu(i[0]);else if(Rr(i)){const e=Fr(i[0]),t=Mr(e);Ur.handleVideoH264NaluList(i,t,e)}else i.forEach((e=>{Ur.handleVideoH264Nalu(e)}))}else if(Ur.streamType===Me)if(ii._opt.nakedFlowH265DemuxUseNew){const t=Ur.handleAddNaluStartCode(e),r=Ur.extractNALu(t);if(0===r.length)return void ii.debug.warn("worker","handleVideoNalu","h265 naluList.length === 0");const i=[];if(r.forEach((e=>{const t=Kr(e);t===it||t===tt||t===Qe?Ur.handleVideoH265Nalu(e):Zr(t)&&i.push(e)})),1===i.length)Ur.handleVideoH265Nalu(i[0]);else if(Qr(i)){const e=Kr(i[0]),t=Jr(e);Ur.handleVideoH265NaluList(i,t,e)}else i.forEach((e=>{Ur.handleVideoH265Nalu(e)}))}else Kr(e)===it?Ur.extractH265PPS(e):Ur.handleVideoH265Nalu(e)},extractH264PPS(e){const t=Ur.handleAddNaluStartCode(e);Ur.extractNALu(t).forEach((e=>{Pr(Fr(e))?Ur.extractH264SEI(e):Ur.handleVideoH264Nalu(e)}))},extractH265PPS(e){const t=Ur.handleAddNaluStartCode(e);Ur.extractNALu(t).forEach((e=>{Xr(Kr(e))?Ur.extractH265SEI(e):Ur.handleVideoH265Nalu(e)}))},extractH264SEI(e){const t=Ur.handleAddNaluStartCode(e);Ur.extractNALu(t).forEach((e=>{Ur.handleVideoH264Nalu(e)}))},extractH265SEI(e){const t=Ur.handleAddNaluStartCode(e);Ur.extractNALu(t).forEach((e=>{Ur.handleVideoH265Nalu(e)}))},handleAddNaluStartCode(e){const t=[0,0,0,1],r=new Uint8Array(e.length+t.length);return r.set(t),r.set(e,t.length),r},handleVideoH264Nalu(e){const t=Fr(e);switch(t){case Ge:Ur.sps=e;break;case He:Ur.pps=e}if(Ur.isSendSeqHeader){if(Ur.sps&&Ur.pps){const e=kr({sps:Ur.sps,pps:Ur.pps}),t=Ur.getNaluDts();ii.decode(e,{type:se,ts:t,isIFrame:!0,cts:0}),Ur.sps=null,Ur.pps=null}if(Lr(t)){const r=Mr(t),i=Ur.getNaluDts(),n=Dr(e,r);Ur.doDecode(n,{type:se,ts:i,isIFrame:r,cts:0})}else ii.debug.warn("work",`handleVideoH264Nalu Avc Seq Head is ${t}`)}else if(Ur.sps&&Ur.pps){Ur.isSendSeqHeader=!0;const e=kr({sps:Ur.sps,pps:Ur.pps});ii.decode(e,{type:se,ts:0,isIFrame:!0,cts:0}),Ur.sps=null,Ur.pps=null}},handleVideoH264NaluList(e,t,r){if(Ur.isSendSeqHeader){const i=Ur.getNaluDts(),n=Ir(e.reduce(((e,t)=>{const r=Qt(e),i=Qt(t),n=new Uint8Array(r.byteLength+i.byteLength);return n.set(r,0),n.set(i,r.byteLength),n})),t);Ur.doDecode(n,{type:se,ts:i,isIFrame:t,cts:0}),ii.debug.log("worker",`handleVideoH264NaluList list size is ${e.length} package length is ${n.byteLength} isIFrame is ${t},nalu type is ${r}, dts is ${i}`)}else ii.debug.warn("worker","handleVideoH264NaluList isSendSeqHeader is false")},handleVideoH265Nalu(e){const t=Kr(e);switch(t){case Qe:Ur.vps=e;break;case tt:Ur.sps=e;break;case it:Ur.pps=e}if(Ur.isSendSeqHeader){if(Ur.vps&&Ur.sps&&Ur.pps){const e=jr({vps:Ur.vps,sps:Ur.sps,pps:Ur.pps}),t=Ur.getNaluDts();ii.decode(e,{type:se,ts:t,isIFrame:!0,cts:0}),Ur.vps=null,Ur.sps=null,Ur.pps=null}if(Zr(t)){const r=Jr(t),i=Ur.getNaluDts(),n=qr(e,r);Ur.doDecode(n,{type:se,ts:i,isIFrame:r,cts:0})}else ii.debug.warn("work",`handleVideoH265Nalu HevcSeqHead is ${t}`)}else if(Ur.vps&&Ur.sps&&Ur.pps){Ur.isSendSeqHeader=!0;const e=jr({vps:Ur.vps,sps:Ur.sps,pps:Ur.pps});ii.decode(e,{type:se,ts:0,isIFrame:!0,cts:0}),Ur.vps=null,Ur.sps=null,Ur.pps=null}},handleVideoH265NaluList(e,t,r){if(Ur.isSendSeqHeader){const i=Ur.getNaluDts(),n=Yr(e.reduce(((e,t)=>{const r=Qt(e),i=Qt(t),n=new Uint8Array(r.byteLength+i.byteLength);return n.set(r,0),n.set(i,r.byteLength),n})),t);Ur.doDecode(n,{type:se,ts:i,isIFrame:t,cts:0}),ii.debug.log("worker",`handleVideoH265NaluList list size is ${e.length} package length is ${n.byteLength} isIFrame is ${t},nalu type is ${r}, dts is ${i}`)}else ii.debug.warn("worker","handleVideoH265NaluList isSendSeqHeader is false")},doDecode(e,t){ii.calcNetworkDelay(t.ts),t.isIFrame&&ii.calcIframeIntervalTimestamp(t.ts),ii.decode(e,t)}},Or={LOG_NAME:"worker fmp4Demuxer",mp4Box:null,offset:0,videoTrackId:null,audioTrackId:null,isHevc:!1,listenMp4Box(){Or.mp4Box=Ni.createFile(),Or.mp4Box.onReady=Or.onReady,Or.mp4Box.onError=Or.onError,Or.mp4Box.onSamples=Or.onSamples},initTransportDescarmber(){Or.transportDescarmber=new Hi},_getSeqHeader(e){const t=Or.mp4Box.getTrackById(e.id);for(const e of t.mdia.minf.stbl.stsd.entries)if(e.avcC||e.hvcC){const t=new Ni.DataStream(void 0,0,Ni.DataStream.BIG_ENDIAN);let r=[];e.avcC?(e.avcC.write(t),r=[23,0,0,0,0]):(Or.isHevc=!0,qt=!0,e.hvcC.write(t),r=[28,0,0,0,0]);const i=new Uint8Array(t.buffer,8),n=new Uint8Array(r.length+i.length);return n.set(r,0),n.set(i,r.length),n}return null},onReady(e){ii.debug.log(Or.LOG_NAME,"onReady()");const t=e.videoTracks[0],r=e.audioTracks[0];if(t){Or.videoTrackId=t.id;const e=Or._getSeqHeader(t);e&&(ii.debug.log(Or.LOG_NAME,"seqHeader"),ii.decodeVideo(e,0,!0,0)),Or.mp4Box.setExtractionOptions(t.id)}if(r&&ii._opt.hasAudio){Or.audioTrackId=r.id;const e=r.audio||{},t=$t.indexOf(e.sample_rate),i=r.codec.replace("mp4a.40.","");Or.mp4Box.setExtractionOptions(r.id);const n=Vt({profile:parseInt(i,10),sampleRate:t,channel:e.channel_count});ii.debug.log(Or.LOG_NAME,"aacADTSHeader"),ii.decodeAudio(n,0)}Or.mp4Box.start()},onError(e){ii.debug.error(Or.LOG_NAME,"mp4Box onError",e)},onSamples(e,t,r){if(e===Or.videoTrackId)for(const t of r){const r=t.data,i=t.is_sync,n=1e3*t.cts/t.timescale;t.duration,t.timescale,i&&ii.calcIframeIntervalTimestamp(n);let s=null;s=Or.isHevc?Yr(r,i):Ir(r,i),ii.decode(s,{type:se,ts:n,isIFrame:i,cts:0}),Or.mp4Box.releaseUsedSamples(e,t.number)}else if(e===Or.audioTrackId){if(ii._opt.hasAudio)for(const t of r){const r=t.data,i=1e3*t.cts/t.timescale;t.duration,t.timescale;const n=new Uint8Array(r.byteLength+2);n.set([175,1],0),n.set(r,2),ii.decode(n,{type:ne,ts:i,isIFrame:!1,cts:0}),Or.mp4Box.releaseUsedSamples(e,t.number)}}else ii.debug.warn(Or.LOG_NAME,"onSamples() trackId error",e)},dispatch(e){let t=new Uint8Array(e);Or.transportDescarmber&&(t=Or.transportDescarmber.transport(t)),t.buffer.fileStart=Or.offset,Or.offset+=t.byteLength,Or.mp4Box.appendBuffer(t.buffer)},destroy(){Or.mp4Box&&(Or.mp4Box.stop(),Or.mp4Box.flush(),Or.mp4Box.destroy(),Or.mp4Box=null),Or.transportDescarmber&&(Or.transportDescarmber.destroy(),Or.transportDescarmber=null),Or.offset=0,Or.videoTrackId=null,Or.audioTrackId=null,Or.isHevc=!1}},Gr={LOG_NAME:"worker mpeg4Demuxer",lastBuffer:new Uint8Array(0),parsedOffset:0,firstStartCodeOffset:0,secondStartCodeOffset:0,state:"init",hasInitVideoCodec:!1,localDts:0,dispatch(e){const t=new Uint8Array(e);Gr.extractNALu(t)},destroy(){Gr.lastBuffer=new Uint8Array(0),Gr.parsedOffset=0,Gr.firstStartCodeOffset=0,Gr.secondStartCodeOffset=0,Gr.state="init",Gr.hasInitVideoCodec=!1,Gr.localDts=0},extractNALu(e){if(!e||e.byteLength<1)return void ii.debug.warn(Gr.LOG_NAME,"extractNALu() buffer error",e);const t=new Uint8Array(Gr.lastBuffer.length+e.length);for(t.set(Gr.lastBuffer,0),t.set(new Uint8Array(e),Gr.lastBuffer.length),Gr.lastBuffer=t;;){if("init"===Gr.state){let e=!1;for(;Gr.lastBuffer.length-Gr.parsedOffset>=4;)if(0===Gr.lastBuffer[Gr.parsedOffset])if(0===Gr.lastBuffer[Gr.parsedOffset+1])if(1===Gr.lastBuffer[Gr.parsedOffset+2]){if(182===Gr.lastBuffer[Gr.parsedOffset+3]){Gr.state="findFirstStartCode",Gr.firstStartCodeOffset=Gr.parsedOffset,Gr.parsedOffset+=4,e=!0;break}Gr.parsedOffset++}else Gr.parsedOffset++;else Gr.parsedOffset++;else Gr.parsedOffset++;if(e)continue;break}if("findFirstStartCode"===Gr.state){let e=!1;for(;Gr.lastBuffer.length-Gr.parsedOffset>=4;)if(0===Gr.lastBuffer[Gr.parsedOffset])if(0===Gr.lastBuffer[Gr.parsedOffset+1])if(1===Gr.lastBuffer[Gr.parsedOffset+2]){if(182===Gr.lastBuffer[Gr.parsedOffset+3]){Gr.state="findSecondStartCode",Gr.secondStartCodeOffset=Gr.parsedOffset,Gr.parsedOffset+=4,e=!0;break}Gr.parsedOffset++}else Gr.parsedOffset++;else Gr.parsedOffset++;else Gr.parsedOffset++;if(e)continue;break}if("findSecondStartCode"===Gr.state){if(!(Gr.lastBuffer.length-Gr.parsedOffset>0))break;{let e,t,r=192&Gr.lastBuffer[Gr.parsedOffset];e=0==r?Gr.secondStartCodeOffset-14:Gr.secondStartCodeOffset;let i=0==(192&Gr.lastBuffer[Gr.firstStartCodeOffset+4]);if(i){if(Gr.firstStartCodeOffset-14<0)return void ii.debug.warn(Gr.LOG_NAME,"firstStartCodeOffset -14 is",Gr.firstStartCodeOffset-14);Gr.hasInitVideoCodec||(Gr.hasInitVideoCodec=!0,ii.debug.log(Gr.LOG_NAME,"setCodec"),si.setCodec(Pe,"")),t=Gr.lastBuffer.subarray(Gr.firstStartCodeOffset-14,e)}else t=Gr.lastBuffer.subarray(Gr.firstStartCodeOffset,e);let n=Gr.getNaluDts();Gr.hasInitVideoCodec?(postMessage({cmd:O,type:Ae,value:t.byteLength}),postMessage({cmd:O,type:Be,value:n}),si.decode(t,i?1:0,n)):ii.debug.warn(Gr.LOG_NAME,"has not init video codec"),Gr.lastBuffer=Gr.lastBuffer.subarray(e),Gr.firstStartCodeOffset=0==r?14:0,Gr.parsedOffset=Gr.firstStartCodeOffset+4,Gr.state="findFirstStartCode"}}}},getNaluDts(){let e=Gr.localDts;return Gr.localDts=Gr.localDts+40,e}},Wr={TAG_NAME:"worker TsLoaderV2",first_parse_:!0,tsPacketSize:0,syncOffset:0,pmt_:null,config_:null,media_info_:new Nn,timescale_:90,duration_:0,pat_:{version_number:0,network_pid:0,program_map_pid:{}},current_program_:null,current_pmt_pid_:-1,program_pmt_map_:{},pes_slice_queues_:{},section_slice_queues_:{},video_metadata_:{vps:null,sps:null,pps:null,details:null},audio_metadata_:{codec:null,audio_object_type:null,sampling_freq_index:null,sampling_frequency:null,channel_config:null},last_pcr_:null,audio_last_sample_pts_:void 0,aac_last_incomplete_data_:null,has_video_:!1,has_audio_:!1,video_init_segment_dispatched_:!1,audio_init_segment_dispatched_:!1,video_metadata_changed_:!1,audio_metadata_changed_:!1,loas_previous_frame:null,video_track_:{type:"video",id:1,sequenceNumber:0,samples:[],length:0},audio_track_:{type:"audio",id:2,sequenceNumber:0,samples:[],length:0},_remainingPacketData:null,init(){},destroy(){Wr.media_info_=null,Wr.pes_slice_queues_=null,Wr.section_slice_queues_=null,Wr.video_metadata_=null,Wr.audio_metadata_=null,Wr.aac_last_incomplete_data_=null,Wr.video_track_=null,Wr.audio_track_=null,Wr._remainingPacketData=null},probe(e){let t=new Uint8Array(e),r=-1,i=188;if(t.byteLength<=3*i)return{needMoreData:!0};for(;-1===r;){let e=Math.min(1e3,t.byteLength-3*i);for(let n=0;n<e;){if(71===t[n]&&71===t[n+i]&&71===t[n+2*i]){r=n;break}n++}if(-1===r)if(188===i)i=192;else{if(192!==i)break;i=204}}return-1===r?{match:!1}:(192===i&&r>=4&&(r-=4),{match:!0,consumed:0,ts_packet_size:i,sync_offset:r})},_initPmt:()=>({program_number:0,version_number:0,pcr_pid:0,pid_stream_type:{},common_pids:{h264:void 0,h265:void 0,adts_aac:void 0,loas_aac:void 0,opus:void 0,ac3:void 0,eac3:void 0,mp3:void 0},pes_private_data_pids:{},timed_id3_pids:{},synchronous_klv_pids:{},asynchronous_klv_pids:{},scte_35_pids:{},smpte2038_pids:{}}),dispatch(e){Wr._remainingPacketData&&(e=hn(Wr._remainingPacketData,e),Wr._remainingPacketData=null);let t=e.buffer;const r=Wr.parseChunks(t);r?Wr._remainingPacketData=e.subarray(r):e.length<this.tsPacketSize&&(Wr._remainingPacketData=e)},parseChunks(e){let t=0;if(Wr.first_parse_){Wr.first_parse_=!1;const r=Wr.probe(e);r.match&&(Wr.tsPacketSize=r.ts_packet_size,Wr.syncOffset=r.sync_offset),t=Wr.syncOffset,ii.debug.log(Wr.TAG_NAME,`isFirstDispatch and tsPacketSize = ${Wr.tsPacketSize}, syncOffset = ${Wr.syncOffset}`)}for(;t+Wr.tsPacketSize<=e.byteLength;){192===Wr.tsPacketSize&&(t+=4);const r=new Uint8Array(e,t,188);let i=r[0];if(71!==i){ii.debug.warn(Wr.TAG_NAME,`sync_byte = ${i}, not 0x47`);break}let n=(64&r[1])>>>6;r[1];let s=(31&r[1])<<8|r[2],a=(48&r[3])>>>4,o=15&r[3],d=!(!Wr.pmt_||Wr.pmt_.pcr_pid!==s),l={},c=4;if(2==a||3==a){let e=r[4];if(e>0&&(d||3==a)&&(l.discontinuity_indicator=(128&r[5])>>>7,l.random_access_indicator=(64&r[5])>>>6,l.elementary_stream_priority_indicator=(32&r[5])>>>5,(16&r[5])>>>4)){let e=300*(r[6]<<25|r[7]<<17|r[8]<<9|r[9]<<1|r[10]>>>7)+((1&r[10])<<8|r[11]);Wr.last_pcr_=e}if(2==a||5+e===188){t+=188,204===Wr.tsPacketSize&&(t+=16);continue}c=5+e}if(1==a||3==a)if(0===s||s===Wr.current_pmt_pid_||null!=Wr.pmt_&&Wr.pmt_.pid_stream_type[s]===In){let r=188-c;Wr.handleSectionSlice(e,t+c,r,{pid:s,payload_unit_start_indicator:n,continuity_conunter:o,random_access_indicator:l.random_access_indicator})}else if(null!=Wr.pmt_&&null!=Wr.pmt_.pid_stream_type[s]){let r=188-c,i=Wr.pmt_.pid_stream_type[s];s!==Wr.pmt_.common_pids.h264&&s!==Wr.pmt_.common_pids.h265&&s!==Wr.pmt_.common_pids.adts_aac&&s!==Wr.pmt_.common_pids.loas_aac&&s!==Wr.pmt_.common_pids.ac3&&s!==Wr.pmt_.common_pids.eac3&&s!==Wr.pmt_.common_pids.opus&&s!==Wr.pmt_.common_pids.mp3&&!0!==Wr.pmt_.pes_private_data_pids[s]&&!0!==Wr.pmt_.timed_id3_pids[s]&&!0!==Wr.pmt_.synchronous_klv_pids[s]&&!0!==Wr.pmt_.asynchronous_klv_pids[s]||Wr.handlePESSlice(e,t+c,r,{pid:s,stream_type:i,payload_unit_start_indicator:n,continuity_conunter:o,random_access_indicator:l.random_access_indicator})}t+=188,204===Wr.tsPacketSize&&(t+=16)}return Wr.dispatchAudioVideoMediaSegment(),t},handleSectionSlice(e,t,r,i){let n=new Uint8Array(e,t,r),s=Wr.section_slice_queues_[i.pid];if(i.payload_unit_start_indicator){let a=n[0];if(null!=s&&0!==s.total_length){let n=new Uint8Array(e,t+1,Math.min(r,a));s.slices.push(n),s.total_length+=n.byteLength,s.total_length===s.expected_length?Wr.emitSectionSlices(s,i):Wr.clearSlices(s,i)}for(let o=1+a;o<n.byteLength&&255!==n[o+0];){let a=(15&n[o+1])<<8|n[o+2];Wr.section_slice_queues_[i.pid]=new Ln,s=Wr.section_slice_queues_[i.pid],s.expected_length=a+3,s.random_access_indicator=i.random_access_indicator;let d=new Uint8Array(e,t+o,Math.min(r-o,s.expected_length-s.total_length));s.slices.push(d),s.total_length+=d.byteLength,s.total_length===s.expected_length?Wr.emitSectionSlices(s,i):s.total_length>=s.expected_length&&Wr.clearSlices(s,i),o+=d.byteLength}}else if(null!=s&&0!==s.total_length){let n=new Uint8Array(e,t,Math.min(r,s.expected_length-s.total_length));s.slices.push(n),s.total_length+=n.byteLength,s.total_length===s.expected_length?Wr.emitSectionSlices(s,i):s.total_length>=s.expected_length&&Wr.clearSlices(s,i)}},handlePESSlice(e,t,r,i){let n=new Uint8Array(e,t,r),s=n[0]<<16|n[1]<<8|n[2];n[3];let a=n[4]<<8|n[5];if(i.payload_unit_start_indicator){if(1!==s)return void ii.debug.warn(Wr.TAG_NAME,`handlePESSlice: packet_start_code_prefix should be 1 but with value ${s}`);let e=Wr.pes_slice_queues_[i.pid];e&&(0===e.expected_length||e.expected_length===e.total_length?Wr.emitPESSlices(e,i):Wr.clearSlices(e,i)),Wr.pes_slice_queues_[i.pid]=new Ln,Wr.pes_slice_queues_[i.pid].random_access_indicator=i.random_access_indicator}if(null==Wr.pes_slice_queues_[i.pid])return;let o=Wr.pes_slice_queues_[i.pid];o.slices.push(n),i.payload_unit_start_indicator&&(o.expected_length=0===a?0:a+6),o.total_length+=n.byteLength,o.expected_length>0&&o.expected_length===o.total_length?Wr.emitPESSlices(o,i):o.expected_length>0&&o.expected_length<o.total_length&&Wr.clearSlices(o,i)},emitSectionSlices(e,t){let r=new Uint8Array(e.total_length);for(let t=0,i=0;t<e.slices.length;t++){let n=e.slices[t];r.set(n,i),i+=n.byteLength}e.slices=[],e.expected_length=-1,e.total_length=0;let i={};i.pid=t.pid,i.data=r,i.file_position=e.file_position,i.random_access_indicator=e.random_access_indicator,Wr.parseSection(i)},emitPESSlices(e,t){let r=new Uint8Array(e.total_length);for(let t=0,i=0;t<e.slices.length;t++){let n=e.slices[t];r.set(n,i),i+=n.byteLength}e.slices=[],e.expected_length=-1,e.total_length=0;let i=new Mn;i.pid=t.pid,i.data=r,i.stream_type=t.stream_type,i.random_access_indicator=e.random_access_indicator,Wr.parsePES(i)},clearSlices(e){e.slices=[],e.expected_length=-1,e.total_length=0},parseSection(e){let t=e.data,r=e.pid;0===r?Wr.parsePAT(t):r===Wr.current_pmt_pid_?Wr.parsePMT(t):null!=Wr.pmt_&&Wr.pmt_.scte_35_pids[r]},parsePES(e){let t=e.data,r=t[0]<<16|t[1]<<8|t[2],i=t[3],n=t[4]<<8|t[5];if(1===r){if(188!==i&&190!==i&&191!==i&&240!==i&&241!==i&&255!==i&&242!==i&&248!==i){t[6];let r,s,a=(192&t[7])>>>6,o=t[8];2!==a&&3!==a||(r=536870912*(14&t[9])+4194304*(255&t[10])+16384*(254&t[11])+128*(255&t[12])+(254&t[13])/2,s=3===a?536870912*(14&t[14])+4194304*(255&t[15])+16384*(254&t[16])+128*(255&t[17])+(254&t[18])/2:r);let d,l=9+o;if(0!==n){if(n<3+o)return void ii.debug.warn(Wr.TAG_NAME,"Malformed PES: PES_packet_length < 3 + PES_header_data_length");d=n-3-o}else d=t.byteLength-l;let c=t.subarray(l,l+d);switch(e.stream_type){case An:case Bn:Wr.parseMP3Payload(c,r);break;case xn:Wr.pmt_.common_pids.opus===e.pid||Wr.pmt_.common_pids.ac3===e.pid||Wr.pmt_.common_pids.eac3===e.pid||(Wr.pmt_.asynchronous_klv_pids[e.pid]?Wr.parseAsynchronousKLVMetadataPayload(c,e.pid,i):Wr.pmt_.smpte2038_pids[e.pid]?Wr.parseSMPTE2038MetadataPayload(c,r,s,e.pid,i):Wr.parsePESPrivateDataPayload(c,r,s,e.pid,i));break;case Un:Wr.parseADTSAACPayload(c,r);break;case Tn:Wr.parseLOASAACPayload(c,r);break;case kn:case Cn:break;case Dn:Wr.pmt_.timed_id3_pids[e.pid]?Wr.parseTimedID3MetadataPayload(c,r,s,e.pid,i):Wr.pmt_.synchronous_klv_pids[e.pid]&&Wr.parseSynchronousKLVMetadataPayload(c,r,s,e.pid,i);break;case Fn:Wr.parseH264Payload(c,r,s,e.random_access_indicator);break;case Pn:Wr.parseH265Payload(c,r,s,e.random_access_indicator)}}else if((188===i||191===i||240===i||241===i||255===i||242===i||248===i)&&e.stream_type===xn){let r,s=6;r=0!==n?n:t.byteLength-s;let a=t.subarray(s,s+r);Wr.parsePESPrivateDataPayload(a,void 0,void 0,e.pid,i)}}else ii.debug.error(Wr.TAG_NAME,`parsePES: packet_start_code_prefix should be 1 but with value ${r}`)},parsePAT(e){let t=e[0];if(0!==t)return void Log.e(Wr.TAG,`parsePAT: table_id ${t} is not corresponded to PAT!`);let r=(15&e[1])<<8|e[2];e[3],e[4];let i=(62&e[5])>>>1,n=1&e[5],s=e[6];e[7];let a=null;if(1===n&&0===s)a={version_number:0,network_pid:0,program_pmt_pid:{}},a.version_number=i;else if(a=Wr.pat_,null==a)return;let o=r-5-4,d=-1,l=-1;for(let t=8;t<8+o;t+=4){let r=e[t]<<8|e[t+1],i=(31&e[t+2])<<8|e[t+3];0===r?a.network_pid=i:(a.program_pmt_pid[r]=i,-1===d&&(d=r),-1===l&&(l=i))}1===n&&0===s&&(null==Wr.pat_&&ii.debug.log(Wr.TAG_NAME,`Parsed first PAT: ${JSON.stringify(a)}`),Wr.pat_=a,Wr.current_program_=d,Wr.current_pmt_pid_=l)},parsePMT(e){let t=e[0];if(2!==t)return void ii.debug.error(Wr.TAG_NAME,`parsePMT: table_id ${t} is not corresponded to PMT!`);let r,i=(15&e[1])<<8|e[2],n=e[3]<<8|e[4],s=(62&e[5])>>>1,a=1&e[5],o=e[6];if(e[7],1===a&&0===o)r=Wr._initPmt(),r.program_number=n,r.version_number=s,Wr.program_pmt_map_[n]=r;else if(r=Wr.program_pmt_map_[n],null==r)return;r.pcr_pid=(31&e[8])<<8|e[9];let d=(15&e[10])<<8|e[11],l=12+d,c=i-9-d-4;for(let t=l;t<l+c;){let i=e[t],n=(31&e[t+1])<<8|e[t+2],s=(15&e[t+3])<<8|e[t+4];r.pid_stream_type[n]=i;let a=r.common_pids.h264||r.common_pids.h265,o=r.common_pids.adts_aac||r.common_pids.loas_aac||r.common_pids.ac3||r.common_pids.eac3||r.common_pids.opus||r.common_pids.mp3;if(i!==Fn||a)if(i!==Pn||a)if(i!==Un||o)if(i!==Tn||o)if(i!==kn||o)if(i!==Cn||o)if(i!==An&&i!==Bn||o)if(i===xn){if(r.pes_private_data_pids[n]=!0,s>0){for(let i=t+5;i<t+5+s;){let t=e[i+0],s=e[i+1];if(5===t){let t=String.fromCharCode(...Array.from(e.subarray(i+2,i+2+s)));"VANC"===t?r.smpte2038_pids[n]=!0:"Opus"===t?r.common_pids.opus=n:"KLVA"===t&&(r.asynchronous_klv_pids[n]=!0)}else if(127===t&&n===r.common_pids.opus){let t=null;if(128===e[i+2]&&(t=e[i+3]),null==t){Log.e(Wr.TAG,"Not Supported Opus channel count.");continue}const r={codec:"opus",channel_count:0==(15&t)?2:15&t,channel_config_code:t,sample_rate:48e3},n={codec:"opus",meta:r};0==Wr.audio_init_segment_dispatched_?(Wr.audio_metadata_=r,Wr.dispatchAudioInitSegment(n)):Wr.detectAudioMetadataChange(n)&&(Wr.dispatchAudioMediaSegment(),Wr.dispatchAudioInitSegment(n))}i+=2+s}e.subarray(t+5,t+5+s)}}else if(i===Dn){if(s>0)for(let i=t+5;i<t+5+s;){let t=e[i+0],s=e[i+1];if(38===t){let t=e[i+2]<<8|e[i+3]<<0,s=null;65535===t&&(s=String.fromCharCode(...Array.from(e.subarray(i+4,i+4+4))));let a=null;if(255===e[i+4+(65535===t?4:0)]){let r=4+(65535===t?4:0)+1;a=String.fromCharCode(...Array.from(e.subarray(i+r,i+r+4)))}"ID3 "===s&&"ID3 "===a?r.timed_id3_pids[n]=!0:"KLVA"===a&&(r.synchronous_klv_pids[n]=!0)}i+=2+s}}else i===In&&(r.scte_35_pids[n]=!0);else r.common_pids.mp3=n;else r.common_pids.eac3=n;else r.common_pids.ac3=n;else r.common_pids.loas_aac=n;else r.common_pids.adts_aac=n;else r.common_pids.h265=n;else r.common_pids.h264=n;t+=5+s}n===Wr.current_program_&&(null==Wr.pmt_&&ii.debug.log(Wr.TAG_NAME,`Parsed first PMT: ${JSON.stringify(r)}`),Wr.pmt_=r,(r.common_pids.h264||r.common_pids.h265)&&(Wr.has_video_=!0),(r.common_pids.adts_aac||r.common_pids.loas_aac||r.common_pids.ac3||r.common_pids.opus||r.common_pids.mp3)&&(Wr.has_audio_=!0))},parseSCTE35(e){},parseH264Payload(e,t,r,i){let n=new zr(e),s=null,a=null,o=[],d=0,l=!1;for(;null!=(s=n.readNextNaluPayload());){let e=new Nr(s);if(e.type===qe){let t=xr.parseSPS$2(s.data);Wr.video_init_segment_dispatched_?!0===Wr.detectVideoMetadataChange(e,t)&&(ii.debug.log(Wr.TAG_NAME,"H264: Critical h264 metadata has been changed, attempt to re-generate InitSegment"),Wr.video_metadata_changed_=!0,Wr.video_metadata_={vps:void 0,sps:e,pps:void 0,details:t}):(Wr.video_metadata_.sps=e,Wr.video_metadata_.details=t)}else e.type===Ye?Wr.video_init_segment_dispatched_&&!Wr.video_metadata_changed_||(Wr.video_metadata_.pps=e,Wr.video_metadata_.sps&&Wr.video_metadata_.pps&&(Wr.video_metadata_changed_&&Wr.dispatchVideoMediaSegment(),Wr.dispatchVideoInitSegment())):(e.type===We||e.type===Ve&&1===i)&&(l=!0);Wr.video_init_segment_dispatched_&&(o.push(e),d+=e.data.byteLength)}let c=Math.floor(t/Wr.timescale_),u=Math.floor(r/Wr.timescale_);if(o.length){let e=Wr.video_track_;for(let e=0;e<o.length;e++){let t=o[e];if(null==a)a=t.data;else{let e=new Uint8Array(a.byteLength+t.data.byteLength);e.set(a,0),e.set(t.data,a.byteLength),a=e}}let t={length:d,isIFrame:l,dts:u,pts:c,cts:c-u,payload:a,type:se,isHevc:!1};e.samples.push(t),e.length=a.byteLength}},parseH265Payload(e,t,r,i){let n=new ei(e),s=null,a=null,o=[],d=0,l=!1;for(;null!=(s=n.readNextNaluPayload());){let e=new ti(s);if(e.type===et){if(!Wr.video_init_segment_dispatched_){let t=Hr.parseVPS(s.data);Wr.video_metadata_.vps=e,Wr.video_metadata_.details={...Wr.video_metadata_.details,...t}}}else if(e.type===rt){let t=Hr.parseSPS(s.data);Wr.video_init_segment_dispatched_?!0===Wr.detectVideoMetadataChange(e,t)&&(ii.debug.log(Wr.TAG_NAME,"H265: Critical h265 metadata has been changed, attempt to re-generate InitSegment"),Wr.video_metadata_changed_=!0,Wr.video_metadata_={vps:void 0,sps:e,pps:void 0,details:t}):(Wr.video_metadata_.sps=e,Wr.video_metadata_.details={...Wr.video_metadata_.details,...t})}else if(e.type===nt){if(!Wr.video_init_segment_dispatched_||Wr.video_metadata_changed_){let t=Hr.parsePPS(s.data);Wr.video_metadata_.pps=e,Wr.video_metadata_.details={...Wr.video_metadata_.details,...t},Wr.video_metadata_.vps&&Wr.video_metadata_.sps&&Wr.video_metadata_.pps&&(Wr.video_metadata_changed_&&Wr.dispatchVideoMediaSegment(),Wr.dispatchVideoInitSegment())}}else e.type!==Xe&&e.type!==Ze&&e.type!==Je||(l=!0);Wr.video_init_segment_dispatched_&&(o.push(e),d+=e.data.byteLength)}let c=Math.floor(t/Wr.timescale_),u=Math.floor(r/Wr.timescale_);if(o.length){let e=Wr.video_track_;for(let e=0;e<o.length;e++){let t=o[e];if(null==a)a=t.data;else{let e=new Uint8Array(a.byteLength+t.data.byteLength);e.set(a,0),e.set(t.data,a.byteLength),a=e}}let t={type:se,length:d,isIFrame:l,dts:u,pts:c,cts:c-u,payload:a,isHevc:!0};e.samples.push(t),e.length=a.byteLength}},detectVideoMetadataChange(e,t){if(t.codec_mimetype!==Wr.video_metadata_.details.codec_mimetype)return ii.debug.log(Wr.TAG_NAME,`Video: Codec mimeType changed from ${Wr.video_metadata_.details.codec_mimetype} to ${t.codec_mimetype}`),!0;if(t.codec_size.width!==Wr.video_metadata_.details.codec_size.width||t.codec_size.height!==Wr.video_metadata_.details.codec_size.height){let e=Wr.video_metadata_.details.codec_size,r=t.codec_size;return ii.debug.log(Wr.TAG_NAME,`Video: Coded Resolution changed from ${e.width}x${e.height} to ${r.width}x${r.height}`),!0}return t.present_size.width!==Wr.video_metadata_.details.present_size.width&&(ii.debug.log(Wr.TAG_NAME,`Video: Present resolution width changed from ${Wr.video_metadata_.details.present_size.width} to ${t.present_size.width}`),!0)},isInitSegmentDispatched:()=>Wr.has_video_&&Wr.has_audio_?Wr.video_init_segment_dispatched_&&Wr.audio_init_segment_dispatched_:Wr.has_video_&&!Wr.has_audio_?Wr.video_init_segment_dispatched_:!(Wr.has_video_||!Wr.has_audio_)&&Wr.audio_init_segment_dispatched_,dispatchVideoInitSegment(){let e=Wr.video_metadata_.details,t={type:"video"};t.id=Wr.video_track_.id,t.timescale=1e3,t.duration=Wr.duration_,t.codecWidth=e.codec_size.width,t.codecHeight=e.codec_size.height,t.presentWidth=e.present_size.width,t.presentHeight=e.present_size.height,t.profile=e.profile_string,t.level=e.level_string,t.bitDepth=e.bit_depth,t.chromaFormat=e.chroma_format,t.sarRatio=e.sar_ratio,t.frameRate=e.frame_rate;let r=t.frameRate.fps_den,i=t.frameRate.fps_num;if(t.refSampleDuration=r/i*1e3,t.codec=e.codec_mimetype,Wr.video_metadata_.vps){let e=Wr.video_metadata_.vps.data.subarray(4),r=Wr.video_metadata_.sps.data.subarray(4),i=Wr.video_metadata_.pps.data.subarray(4);t.hvcc=jr({vps:e,sps:r,pps:i}),0==Wr.video_init_segment_dispatched_&&ii.debug.log(Wr.TAG_NAME,`Generated first HEVCDecoderConfigurationRecord for mimeType: ${t.codec}`),t.hvcc&&ii.decodeVideo(t.hvcc,0,!0,0)}else{let e=Wr.video_metadata_.sps.data.subarray(4),r=Wr.video_metadata_.pps.data.subarray(4);t.avcc=Cr({sps:e,pps:r}),0==Wr.video_init_segment_dispatched_&&ii.debug.log(Wr.TAG_NAME,`Generated first AVCDecoderConfigurationRecord for mimeType: ${t.codec}`),t.avcc&&ii.decodeVideo(t.avcc,0,!0,0)}Wr.video_init_segment_dispatched_=!0,Wr.video_metadata_changed_=!1;let n=Wr.media_info_;n.hasVideo=!0,n.width=t.codecWidth,n.height=t.codecHeight,n.fps=t.frameRate.fps,n.profile=t.profile,n.level=t.level,n.refFrames=e.ref_frames,n.chromaFormat=e.chroma_format_string,n.sarNum=t.sarRatio.width,n.sarDen=t.sarRatio.height,n.videoCodec=t.codec,n.hasAudio&&n.audioCodec?n.mimeType=`video/mp2t; codecs="${n.videoCodec},${n.audioCodec}"`:n.mimeType=`video/mp2t; codecs="${n.videoCodec}"`},dispatchVideoMediaSegment(){Wr.isInitSegmentDispatched()&&Wr.video_track_.length&&Wr._preDoDecode()},dispatchAudioMediaSegment(){Wr.isInitSegmentDispatched()&&Wr.audio_track_.length&&Wr._preDoDecode()},dispatchAudioVideoMediaSegment(){Wr.isInitSegmentDispatched()&&(Wr.audio_track_.length||Wr.video_track_.length)&&Wr._preDoDecode()},parseADTSAACPayload(e,t){if(Wr.has_video_&&!Wr.video_init_segment_dispatched_)return;if(Wr.aac_last_incomplete_data_){let t=new Uint8Array(e.byteLength+Wr.aac_last_incomplete_data_.byteLength);t.set(Wr.aac_last_incomplete_data_,0),t.set(e,Wr.aac_last_incomplete_data_.byteLength),e=t}let r,i;if(null!=t&&(i=t/Wr.timescale_),"aac"===Wr.audio_metadata_.codec){if(null==t&&null!=Wr.audio_last_sample_pts_)r=1024/Wr.audio_metadata_.sampling_frequency*1e3,i=Wr.audio_last_sample_pts_+r;else if(null==t)return void ii.debug.warn(Wr.TAG_NAME,"AAC: Unknown pts");if(Wr.aac_last_incomplete_data_&&Wr.audio_last_sample_pts_){r=1024/Wr.audio_metadata_.sampling_frequency*1e3;let e=Wr.audio_last_sample_pts_+r;Math.abs(e-i)>1&&(ii.debug.warn(Wr.TAG_NAME,`AAC: Detected pts overlapped, expected: ${e}ms, PES pts: ${i}ms`),i=e)}}let n,s=new Kt(e),a=null,o=i;for(;null!=(a=s.readNextAACFrame());){r=1024/a.sampling_frequency*1e3;const e={codec:"aac",data:a};0==Wr.audio_init_segment_dispatched_?(Wr.audio_metadata_={codec:"aac",audio_object_type:a.audio_object_type,sampling_freq_index:a.sampling_freq_index,sampling_frequency:a.sampling_frequency,channel_config:a.channel_config},Wr.dispatchAudioInitSegment(e)):Wr.detectAudioMetadataChange(e)&&(Wr.dispatchAudioMediaSegment(),Wr.dispatchAudioInitSegment(e)),n=o;let t=Math.floor(o);const i=new Uint8Array(a.data.length+2);i.set([175,1],0),i.set(a.data,2);let s={payload:i,length:i.byteLength,pts:t,dts:t,type:ne};Wr.audio_track_.samples.push(s),Wr.audio_track_.length+=i.byteLength,o+=r}s.hasIncompleteData()&&(Wr.aac_last_incomplete_data_=s.getIncompleteData()),n&&(Wr.audio_last_sample_pts_=n)},parseLOASAACPayload(e,t){if(Wr.has_video_&&!Wr.video_init_segment_dispatched_)return;if(Wr.aac_last_incomplete_data_){let t=new Uint8Array(e.byteLength+Wr.aac_last_incomplete_data_.byteLength);t.set(Wr.aac_last_incomplete_data_,0),t.set(e,Wr.aac_last_incomplete_data_.byteLength),e=t}let r,i;if(null!=t&&(i=t/Wr.timescale_),"aac"===Wr.audio_metadata_.codec){if(null==t&&null!=Wr.audio_last_sample_pts_)r=1024/Wr.audio_metadata_.sampling_frequency*1e3,i=Wr.audio_last_sample_pts_+r;else if(null==t)return void ii.debug.warn(Wr.TAG_NAME,"AAC: Unknown pts");if(Wr.aac_last_incomplete_data_&&Wr.audio_last_sample_pts_){r=1024/Wr.audio_metadata_.sampling_frequency*1e3;let e=Wr.audio_last_sample_pts_+r;Math.abs(e-i)>1&&(ii.debug.warn(Wr.TAG,`AAC: Detected pts overlapped, expected: ${e}ms, PES pts: ${i}ms`),i=e)}}let n,s=new Xt(e),a=null,o=i;for(;null!=(a=s.readNextAACFrame(or(this.loas_previous_frame)?void 0:this.loas_previous_frame));){Wr.loas_previous_frame=a,r=1024/a.sampling_frequency*1e3;const e={codec:"aac",data:a};0==Wr.audio_init_segment_dispatched_?(Wr.audio_metadata_={codec:"aac",audio_object_type:a.audio_object_type,sampling_freq_index:a.sampling_freq_index,sampling_frequency:a.sampling_frequency,channel_config:a.channel_config},Wr.dispatchAudioInitSegment(e)):Wr.detectAudioMetadataChange(e)&&(Wr.dispatchAudioMediaSegment(),Wr.dispatchAudioInitSegment(e)),n=o;let t=Math.floor(o);const i=new Uint8Array(a.data.length+2);i.set([175,1],0),i.set(a.data,2);let s={payload:i,length:i.byteLength,pts:t,dts:t,type:ne};Wr.audio_track_.samples.push(s),Wr.audio_track_.length+=i.byteLength,o+=r}s.hasIncompleteData()&&(Wr.aac_last_incomplete_data_=s.getIncompleteData()),n&&(Wr.audio_last_sample_pts_=n)},parseAC3Payload(e,t){},parseEAC3Payload(e,t){},parseOpusPayload(e,t){},parseMP3Payload(e,t){if(Wr.has_video_&&!Wr.video_init_segment_dispatched_)return;let r=[44100,48e3,32e3,0],i=[22050,24e3,16e3,0],n=[11025,12e3,8e3,0],s=e[1]>>>3&3,a=(6&e[1])>>1;e[2];let o=(12&e[2])>>>2,d=3!=(e[3]>>>6&3)?2:1,l=0,c=34;switch(s){case 0:l=n[o];break;case 2:l=i[o];break;case 3:l=r[o]}switch(a){case 1:c=34;break;case 2:c=33;break;case 3:c=32}const u={};u.object_type=c,u.sample_rate=l,u.channel_count=d,u.data=e;const h={codec:"mp3",data:u};0==Wr.audio_init_segment_dispatched_?(Wr.audio_metadata_={codec:"mp3",object_type:c,sample_rate:l,channel_count:d},Wr.dispatchAudioInitSegment(h)):Wr.detectAudioMetadataChange(h)&&(Wr.dispatchAudioMediaSegment(),Wr.dispatchAudioInitSegment(h));let f={payload:e,length:e.byteLength,pts:t/Wr.timescale_,dts:t/Wr.timescale_,type:ne};Wr.audio_track_.samples.push(f),Wr.audio_track_.length+=e.byteLength},detectAudioMetadataChange(e){if(e.codec!==Wr.audio_metadata_.codec)return ii.debug.log(Wr.TAG_NAME,`Audio: Audio Codecs changed from ${Wr.audio_metadata_.codec} to ${e.codec}`),!0;if("aac"===e.codec&&"aac"===Wr.audio_metadata_.codec){const t=e.data;if(t.audio_object_type!==Wr.audio_metadata_.audio_object_type)return ii.debug.log(Wr.TAG_NAME,`AAC: AudioObjectType changed from ${Wr.audio_metadata_.audio_object_type} to ${t.audio_object_type}`),!0;if(t.sampling_freq_index!==Wr.audio_metadata_.sampling_freq_index)return ii.debug.log(Wr.TAG_NAME,`AAC: SamplingFrequencyIndex changed from ${Wr.audio_metadata_.sampling_freq_index} to ${t.sampling_freq_index}`),!0;if(t.channel_config!==Wr.audio_metadata_.channel_config)return ii.debug.log(Wr.TAG_NAME,`AAC: Channel configuration changed from ${Wr.audio_metadata_.channel_config} to ${t.channel_config}`),!0}else if("ac-3"===e.codec&&"ac-3"===Wr.audio_metadata_.codec){const t=e.data;if(t.sampling_frequency!==Wr.audio_metadata_.sampling_frequency)return ii.debug.log(Wr.TAG_NAME,`AC3: Sampling Frequency changed from ${Wr.audio_metadata_.sampling_frequency} to ${t.sampling_frequency}`),!0;if(t.bit_stream_identification!==Wr.audio_metadata_.bit_stream_identification)return ii.debug.log(Wr.TAG_NAME,`AC3: Bit Stream Identification changed from ${Wr.audio_metadata_.bit_stream_identification} to ${t.bit_stream_identification}`),!0;if(t.bit_stream_mode!==Wr.audio_metadata_.bit_stream_mode)return ii.debug.log(Wr.TAG_NAME,`AC3: BitStream Mode changed from ${Wr.audio_metadata_.bit_stream_mode} to ${t.bit_stream_mode}`),!0;if(t.channel_mode!==Wr.audio_metadata_.channel_mode)return ii.debug.log(Wr.TAG_NAME,`AC3: Channel Mode changed from ${Wr.audio_metadata_.channel_mode} to ${t.channel_mode}`),!0;if(t.low_frequency_effects_channel_on!==Wr.audio_metadata_.low_frequency_effects_channel_on)return ii.debug.log(Wr.TAG_NAME,`AC3: Low Frequency Effects Channel On changed from ${Wr.audio_metadata_.low_frequency_effects_channel_on} to ${t.low_frequency_effects_channel_on}`),!0}else if("opus"===e.codec&&"opus"===Wr.audio_metadata_.codec){const t=e.meta;if(t.sample_rate!==Wr.audio_metadata_.sample_rate)return ii.debug.log(Wr.TAG_NAME,`Opus: SamplingFrequencyIndex changed from ${Wr.audio_metadata_.sample_rate} to ${t.sample_rate}`),!0;if(t.channel_count!==Wr.audio_metadata_.channel_count)return ii.debug.log(Wr.TAG_NAME,`Opus: Channel count changed from ${Wr.audio_metadata_.channel_count} to ${t.channel_count}`),!0}else if("mp3"===e.codec&&"mp3"===Wr.audio_metadata_.codec){const t=e.data;if(t.object_type!==Wr.audio_metadata_.object_type)return ii.debug.log(Wr.TAG_NAME,`MP3: AudioObjectType changed from ${Wr.audio_metadata_.object_type} to ${t.object_type}`),!0;if(t.sample_rate!==Wr.audio_metadata_.sample_rate)return ii.debug.log(Wr.TAG_NAME,`MP3: SamplingFrequencyIndex changed from ${Wr.audio_metadata_.sample_rate} to ${t.sample_rate}`),!0;if(t.channel_count!==Wr.audio_metadata_.channel_count)return ii.debug.log(Wr.TAG_NAME,`MP3: Channel count changed from ${Wr.audio_metadata_.channel_count} to ${t.channel_count}`),!0}return!1},dispatchAudioInitSegment(e){let t={type:"audio"};if(t.id=Wr.audio_track_.id,t.timescale=1e3,t.duration=Wr.duration_,"aac"===Wr.audio_metadata_.codec){let r="aac"===e.codec?e.data:null,i=new On(r);t.audioSampleRate=i.sampling_rate,t.audioSampleRateIndex=i.sampling_index,t.channelCount=i.channel_count,t.codec=i.codec_mimetype,t.originalCodec=i.original_codec_mimetype,t.config=i.config,t.refSampleDuration=1024/t.audioSampleRate*t.timescale;const n=Vt({profile:ii._opt.mseDecodeAudio?i.object_type:i.original_object_type,sampleRate:t.audioSampleRateIndex,channel:t.channelCount});ii.decodeAudio(n,0)}else"ac-3"===Wr.audio_metadata_.codec||"ec-3"===Wr.audio_metadata_.codec||"opus"===Wr.audio_metadata_.codec||"mp3"===Wr.audio_metadata_.codec&&(t.audioSampleRate=Wr.audio_metadata_.sample_rate,t.channelCount=Wr.audio_metadata_.channel_count,t.codec="mp3",t.originalCodec="mp3",t.config=void 0);0==Wr.audio_init_segment_dispatched_&&ii.debug.log(Wr.TAG_NAME,`Generated first AudioSpecificConfig for mimeType: ${t.codec}`),Wr.audio_init_segment_dispatched_=!0,Wr.video_metadata_changed_=!1;let r=Wr.media_info_;r.hasAudio=!0,r.audioCodec=t.originalCodec,r.audioSampleRate=t.audioSampleRate,r.audioChannelCount=t.channelCount,r.hasVideo&&r.videoCodec?r.mimeType=`video/mp2t; codecs="${r.videoCodec},${r.audioCodec}"`:r.mimeType=`video/mp2t; codecs="${r.audioCodec}"`},dispatchPESPrivateDataDescriptor(e,t,r){},parsePESPrivateDataPayload(e,t,r,i,n){let s=new Rn;if(s.pid=i,s.stream_id=n,s.len=e.byteLength,s.data=e,null!=t){let e=Math.floor(t/Wr.timescale_);s.pts=e}else s.nearest_pts=Wr.getNearestTimestampMilliseconds();if(null!=r){let e=Math.floor(r/Wr.timescale_);s.dts=e}},parseTimedID3MetadataPayload(e,t,r,i,n){ii.debug.log(Wr.TAG_NAME,`Timed ID3 Metadata: pid=${i}, pts=${t}, dts=${r}, stream_id=${n}`)},parseSynchronousKLVMetadataPayload(e,t,r,i,n){ii.debug.log(Wr.TAG_NAME,`Synchronous KLV Metadata: pid=${i}, pts=${t}, dts=${r}, stream_id=${n}`)},parseAsynchronousKLVMetadataPayload(e,t,r){ii.debug.log(Wr.TAG_NAME,`Asynchronous KLV Metadata: pid=${t}, stream_id=${r}`)},parseSMPTE2038MetadataPayload(e,t,r,i,n){ii.debug.log(Wr.TAG_NAME,`SMPTE 2038 Metadata: pid=${i}, pts=${t}, dts=${r}, stream_id=${n}`)},getNearestTimestampMilliseconds:()=>null!=Wr.audio_last_sample_pts_?Math.floor(Wr.audio_last_sample_pts_):null!=Wr.last_pcr_?Math.floor(Wr.last_pcr_/300/Wr.timescale_):void 0,_preDoDecode(){const e=Wr.video_track_,t=Wr.audio_track_;let r=e.samples;t.samples.length>0&&(r=e.samples.concat(t.samples),r=r.sort(((e,t)=>e.dts-t.dts))),r.forEach((e=>{const t=new Uint8Array(e.payload);delete e.payload,e.type===se?Wr._doDecodeVideo({...e,payload:t}):e.type===ne&&Wr._doDecodeAudio({...e,payload:t})})),e.samples=[],e.length=0,t.samples=[],t.length=0},_doDecodeVideo(e){const t=new Uint8Array(e.payload);let r=null;r=e.isHevc?Yr(t,e.isIFrame):Ir(t,e.isIFrame),e.isIFrame&&ii.calcIframeIntervalTimestamp(e.dts);let i=ii.cryptoPayload(r,e.isIFrame);ii.decode(i,{type:se,ts:e.dts,isIFrame:e.isIFrame,cts:e.cts})},_doDecodeAudio(e){const t=new Uint8Array(e.payload);let r=t;gr(ii._opt.m7sCryptoAudio)&&(r=ii.cryptoPayloadAudio(t)),ii.decode(r,{type:ne,ts:e.dts,isIFrame:!1,cts:0})}},ri=null;vr()&&(ri={TAG_NAME:"worker MediaSource",_resetInIt(){ri.isAvc=null,ri.isAAC=null,ri.videoInfo={},ri.videoMeta={},ri.audioMeta={},ri.sourceBuffer=null,ri.audioSourceBuffer=null,ri.hasInit=!1,ri.hasAudioInit=!1,ri.isAudioInitInfo=!1,ri.videoMimeType="",ri.audioMimeType="",ri.cacheTrack={},ri.cacheAudioTrack={},ri.timeInit=!1,ri.sequenceNumber=0,ri.audioSequenceNumber=0,ri.firstRenderTime=null,ri.firstAudioTime=null,ri.mediaSourceAppendBufferFull=!1,ri.mediaSourceAppendBufferError=!1,ri.mediaSourceAddSourceBufferError=!1,ri.mediaSourceBufferError=!1,ri.mediaSourceError=!1,ri.prevTimestamp=null,ri.decodeDiffTimestamp=null,ri.prevDts=null,ri.prevAudioDts=null,ri.prevPayloadBufferSize=0,ri.isWidthOrHeightChanged=!1,ri.prevTs=null,ri.prevAudioTs=null,ri.eventListenList=[],ri.pendingRemoveRanges=[],ri.pendingSegments=[],ri.pendingAudioRemoveRanges=[],ri.pendingAudioSegments=[],ri.supportVideoFrameCallbackHandle=null,ri.audioSourceBufferCheckTimeout=null,ri.audioSourceNoDataCheckTimeout=null,ri.hasPendingEos=!1,ri.$video={currentTime:0,readyState:0}},init(){ri.events=new fn,ri._resetInIt(),ri.mediaSource=new self.MediaSource,ri.isDecodeFirstIIframe=!!yr(ii._opt.checkFirstIFrame),ri._bindMediaSourceEvents()},destroy(){ri.stop(),ri._clearAudioSourceBufferCheckTimeout(),ri.eventListenList&&ri.eventListenList.length&&(ri.eventListenList.forEach((e=>e())),ri.eventListenList=[]),ri._resetInIt(),ri.mediaSource=null},getState:()=>ri.mediaSource&&ri.mediaSource.readyState,isStateOpen:()=>ri.getState()===gt,isStateClosed:()=>ri.getState()===yt,isStateEnded:()=>ri.getState()===mt,_bindMediaSourceEvents(){const{proxy:e}=ri.events,t=e(ri.mediaSource,bt,(()=>{ii.debug.log(ri.TAG_NAME,"sourceOpen"),ri._onMediaSourceSourceOpen()})),r=e(ri.mediaSource,vt,(()=>{ii.debug.log(ri.TAG_NAME,"sourceClose")})),i=e(ri.mediaSource,wt,(()=>{ii.debug.log(ri.TAG_NAME,"sourceended")}));ri.eventListenList.push(t,r,i)},_onMediaSourceSourceOpen(){ri.sourceBuffer||(ii.debug.log(ri.TAG_NAME,"onMediaSourceSourceOpen() sourceBuffer is null and next init"),ri._initSourceBuffer()),ri.audioSourceBuffer||(ii.debug.log(ri.TAG_NAME,"onMediaSourceSourceOpen() audioSourceBuffer is null and next init"),ri._initAudioSourceBuffer()),ri._hasPendingSegments()&&ri._doAppendSegments()},decodeVideo(e,t,r,i){if(ii.isDestroyed)ii.debug.warn(ri.TAG_NAME,"decodeVideo() and decoder is destroyed");else if(yr(ri.hasInit))if(r&&e[1]===Bt){const i=15&e[0];if(i===Fe&&yr(ar()))return void ri.emitError(Ce.mediaSourceH265NotSupport);ri.videoInfo.codec=i,postMessage({cmd:L,code:i});const n=new Uint8Array(e);postMessage({cmd:M,buffer:n,codecId:i},[n.buffer]),ri.hasInit=ri._decodeConfigurationRecord(e,t,r,i)}else ii.debug.warn(ri.TAG_NAME,`decodeVideo has not init , isIframe is ${r} , payload is ${e[1]}`);else if(!ri.isDecodeFirstIIframe&&r&&(ri.isDecodeFirstIIframe=!0),ri.isDecodeFirstIIframe){if(r&&0===e[1]){const t=15&e[0];let r={};t===Ie?r=Tr(e.slice(5)):t===Fe&&(r=$r(e));const i=ri.videoInfo;i&&i.codecWidth&&i.codecWidth&&r&&r.codecWidth&&r.codecHeight&&(r.codecWidth!==i.codecWidth||r.codecHeight!==i.codecWidth)&&(ii.debug.warn(ri.TAG_NAME,`\n                                decodeVideo: video width or height is changed,\n                                old width is ${i.codecWidth}, old height is ${i.codecWidth},\n                                new width is ${r.codecWidth}, new height is ${r.codecHeight},\n                                and emit change event`),ri.isWidthOrHeightChanged=!0,ri.emitError(Ce.mseWidthOrHeightChange))}if(ri.isWidthOrHeightChanged)return void ii.debug.warn(ri.TAG_NAME,"decodeVideo: video width or height is changed, and return");if(mr(e))return void ii.debug.warn(ri.TAG_NAME,"decodeVideo and payload is video sequence header so drop this frame");if(e.byteLength<B)return void ii.debug.warn(ri.TAG_NAME,`decodeVideo and payload is too small , payload length is ${e.byteLength}`);let n=t;if(ii.isPlayer){if(null===ri.firstRenderTime&&(ri.firstRenderTime=t,postMessage({cmd:re,value:ri.firstRenderTime})),n=t-ri.firstRenderTime,n<0&&(ii.debug.warn(ri.TAG_NAME,`decodeVideo\n                                 local dts is < 0 , ts is ${t} and prevTs is ${ri.prevTs},\n                                 firstRenderTime is ${ri.firstRenderTime} and mseCorrectTimeDuration is ${ii._opt.mseCorrectTimeDuration}`),n=null===ri.prevDts?0:ri.prevDts+ii._opt.mseCorrectTimeDuration,ri._checkTsIsMaxDiff(t)))return ii.debug.warn(ri.TAG_NAME,`decodeVideo is max diff , ts is ${t} and prevTs is ${ri.prevTs}, diff is ${ri.prevTs-t}`),void ri.emitError(Ce.mediaSourceTsIsMaxDiff);if(null!==ri.prevDts&&n<=ri.prevDts){if(ii.debug.warn(ri.TAG_NAME,`\n                                decodeVideo dts is less than(or equal) prev dts ,\n                                dts is ${n} and prev dts is ${ri.prevDts} ，\n                                and now ts is ${t} and prev ts is ${ri.prevTs} ，\n                                and diff is ${t-ri.prevTs} and firstRenderTime is ${ri.firstRenderTime} and isIframe is ${r}，\n                                and mseCorrectTimeDuration is ${ii._opt.mseCorrectTimeDuration},\n                                and prevPayloadBufferSize is ${ri.prevPayloadBufferSize} and payload size is ${e.byteLength}`),n===ri.prevDts&&ri.prevPayloadBufferSize===e.byteLength)return void ii.debug.warn(ri.TAG_NAME,"decodeVideo dts is equal to prev dts and payload size is equal to prev payload size so drop this frame");if(n=ri.prevDts+ii._opt.mseCorrectTimeDuration,ri._checkTsIsMaxDiff(t))return ii.debug.warn(ri.TAG_NAME,`decodeVideo is max diff , ts is ${t} and prevTs is ${ri.prevTs}, diff is ${ri.prevTs-t} and emit replay`),void ri.emitError(Ce.mediaSourceTsIsMaxDiff)}}ii.isPlayer?ri._decodeVideo(e,n,r,i,t):ii.isPlayback,ri.prevDts=n,ri.prevPayloadBufferSize=e.byteLength,ri.prevTs=t}else ii.debug.log(ri.TAG_NAME,"decodeVideo first frame is not iFrame")},decodeAudio(e,t){if(ii.isDestroyed)ii.debug.warn(ri.TAG_NAME,"decodeAudio() and decoder is destroyed");else if(yr(ri.hasAudioInit))ri.hasAudioInit=ri._decodeAudioConfigurationRecord(e,t);else{let r=t;if(Wt(e))return void ii.debug.log(ri.TAG_NAME,"decodeAudio and has already initialized and payload is aac codec packet so drop this frame");if(ri._clearAudioNoDataCheckTimeout(),ri.isDecodeFirstIIframe){if(ii.isPlayer){if(null===ri.firstAudioTime&&(ri.firstAudioTime=t,null!==ri.firstRenderTime&&null!==ri.prevTs)){const e=Math.abs(ri.firstRenderTime-ri.prevTs);e>300&&(ri.firstAudioTime-=e,ii.debug.warn(ri.TAG_NAME,`video\n                                    firstAudioTime is ${ri.firstRenderTime} and current time is ${ri.prevTs}\n                                    play time is ${e} and firstAudioTime ${t} - ${e} = ${ri.firstAudioTime}`))}r=t-ri.firstAudioTime,r<0&&(ii.debug.warn(ri.TAG_NAME,`decodeAudio\n                             local dts is < 0 , ts is ${t} and prevTs is ${ri.prevAudioTs},\n                             firstAudioTime is ${ri.firstAudioTime}`),r=null===ri.prevAudioDts?0:ri.prevAudioDts+ii._opt.mseCorrectAudioTimeDuration),null!==ri.prevAudioTs&&r<=ri.prevAudioDts&&(ii.debug.warn(ri.TAG_NAME,`\n                            decodeAudio dts is less than(or equal) prev dts ,\n                            dts is ${r} and prev dts is ${ri.prevAudioDts} ，\n                            and now ts is ${t} and prev ts is ${ri.prevAudioTs} ，\n                            and diff is ${t-ri.prevAudioTs}`),r=ri.prevAudioDts+ii._opt.mseCorrectAudioTimeDuration)}ii.isPlayer?ri._decodeAudio(e,r,t):ii.isPlayback,ri.prevAudioTs=t,ri.prevAudioDts=r}else ii.debug.log(ri.TAG_NAME,"decodeAudio first frame is not iFrame")}},_checkTsIsMaxDiff:e=>ri.prevTs>0&&e<ri.prevTs&&ri.prevTs-e>E,_decodeConfigurationRecord(e,t,r,i){let n=e.slice(5),s={};if(i===Ie?s=Tr(n):i===Fe&&(s=Vr(n)),ri.videoInfo.width=s.codecWidth,ri.videoInfo.height=s.codecHeight,0===s.codecWidth&&0===s.codecHeight)return ii.debug.warn(ri.TAG_NAME,"_decodeConfigurationRecord error",JSON.stringify(s)),ri.emitError(Ce.mediaSourceDecoderConfigurationError),!1;const a={id:Dt,type:"video",timescale:1e3,duration:0,avcc:n,codecWidth:s.codecWidth,codecHeight:s.codecHeight,videoType:s.videoType},o=pn.generateInitSegment(a);ri.isAvc=i===Ie;let d=s.codec;return ri.videoMimeType=d?`video/mp4; codecs="${s.codec}"`:ri.isAvc?ct:ut,postMessage({cmd:T,w:s.codecWidth,h:s.codecHeight}),ri._initSourceBuffer(),ri.appendBuffer(o.buffer),ri.sequenceNumber=0,ri.cacheTrack={},ri.timeInit=!1,!0},_decodeAudioConfigurationRecord(e,t){const r=e[0]>>4,i=e[0]>>1&1,n=r===Oe,s=r===Re;if(yr(s||n))return ii.debug.warn(ri.TAG_NAME,`_decodeAudioConfigurationRecord audio codec is not support , codecId is ${r} ant auto wasm decode`),ri.emitError(Ce.mediaSourceAudioG711NotSupport),!1;const a={id:It,type:"audio",timescale:1e3};let o={};if(Wt(e)){if(o=Yt(e.slice(2)),!o)return!1;a.audioSampleRate=o.sampleRate,a.channelCount=o.channelCount,a.config=o.config,a.refSampleDuration=1024/a.audioSampleRate*a.timescale}else{if(!n)return!1;if(o=En(e),!o)return!1;a.audioSampleRate=o.samplingRate,a.channelCount=o.channelCount,a.refSampleDuration=1152/a.audioSampleRate*a.timescale}a.codec=o.codec,a.duration=0;let d="mp4",l=o.codec,c=null;n&&yr(sr())?(d="mpeg",l="",c=new Uint8Array):c=pn.generateInitSegment(a);let u=`${a.type}/${d}`;return l&&l.length>0&&(u+=`;codecs=${l}`),yr(ri.isAudioInitInfo)&&(ir=r===Re?i?16:8:0===i?8:16,postMessage({cmd:I,code:r}),postMessage({cmd:D,sampleRate:a.audioSampleRate,channels:a.channelCount,depth:ir}),ri.isAudioInitInfo=!0),ri.audioMimeType=u,ri.isAAC=s,ri._initAudioSourceBuffer(),ri.appendAudioBuffer(c.buffer),!0},_initSourceBuffer(){const{proxy:e}=ri.events;if(null===ri.sourceBuffer&&null!==ri.mediaSource&&ri.isStateOpen()&&ri.videoMimeType){try{ri.sourceBuffer=ri.mediaSource.addSourceBuffer(ri.videoMimeType),ii.debug.log(ri.TAG_NAME,"_initSourceBuffer() mseDecoder.mediaSource.addSourceBuffer()",ri.videoMimeType)}catch(e){return ii.debug.error(ri.TAG_NAME,"appendBuffer() mseDecoder.mediaSource.addSourceBuffer()",e.code,e),ri.emitError(Ce.mseAddSourceBufferError,e.code),void(ri.mediaSourceAddSourceBufferError=!0)}if(ri.sourceBuffer){const t=e(ri.sourceBuffer,"error",(e=>{ri.mediaSourceBufferError=!0,ii.debug.error(ri.TAG_NAME,"mseSourceBufferError mseDecoder.sourceBuffer",e),ri.emitError(Ce.mseSourceBufferError,e.code)})),r=e(ri.sourceBuffer,"updateend",(()=>{ri._hasPendingRemoveRanges()?ri._doRemoveRanges():ri._hasPendingSegments()?ri._doAppendSegments():ri.hasPendingEos&&(ii.debug.log(ri.TAG_NAME,"videoSourceBuffer updateend and hasPendingEos is true, next endOfStream()"),ri.endOfStream())}));ri.eventListenList.push(t,r)}}else ii.debug.log(ri.TAG_NAME,`_initSourceBuffer and mseDecoder.isStateOpen is ${ri.isStateOpen()} and mseDecoder.isAvc === null is ${null===ri.isAvc}`)},_initAudioSourceBuffer(){const{proxy:e}=ri.events;if(null===ri.audioSourceBuffer&&null!==ri.mediaSource&&ri.isStateOpen()&&ri.audioMimeType){try{ri.audioSourceBuffer=ri.mediaSource.addSourceBuffer(ri.audioMimeType),ri._clearAudioSourceBufferCheckTimeout(),ii.debug.log(ri.TAG_NAME,"_initAudioSourceBuffer() mseDecoder.mediaSource.addSourceBuffer()",ri.audioMimeType)}catch(e){return ii.debug.error(ri.TAG_NAME,"appendAudioBuffer() mseDecoder.mediaSource.addSourceBuffer()",e.code,e),ri.emitError(Ce.mseAddSourceBufferError,e.code),void(ri.mediaSourceAddSourceBufferError=!0)}if(ri.audioSourceBuffer){const t=e(ri.audioSourceBuffer,"error",(e=>{ri.mediaSourceBufferError=!0,ii.debug.error(ri.TAG_NAME,"mseSourceBufferError mseDecoder.audioSourceBuffer",e),ri.emitError(Ce.mseSourceBufferError,e.code)})),r=e(ri.audioSourceBuffer,"updateend",(()=>{ri._hasPendingRemoveRanges()?ri._doRemoveRanges():ri._hasPendingSegments()?ri._doAppendSegments():ri.hasPendingEos&&(ii.debug.log(ri.TAG_NAME,"audioSourceBuffer updateend and hasPendingEos is true, next endOfStream()"),ri.endOfStream())}));ri.eventListenList.push(t,r),null===ri.audioSourceNoDataCheckTimeout&&(ri.audioSourceNoDataCheckTimeout=setTimeout((()=>{ri._clearAudioNoDataCheckTimeout(),ri.emitError(Ce.mediaSourceAudioNoDataTimeout)}),1e3))}}else ii.debug.log(ri.TAG_NAME,`_initAudioSourceBuffer and mseDecoder.isStateOpen is ${ri.isStateOpen()} and mseDecoder.audioMimeType is ${ri.audioMimeType}`)},_decodeVideo(e,t,r,i,n){let s=e.slice(5),a=s.byteLength;if(0===a)return void ii.debug.warn(ri.TAG_NAME,"_decodeVideo payload bytes is 0 and return");let o=(new Date).getTime(),d=!1;ri.prevTimestamp||(ri.prevTimestamp=o,d=!0);const l=o-ri.prevTimestamp;if(ri.decodeDiffTimestamp=l,l>500&&!d&&ii.isPlayer&&ii.debug.warn(ri.TAG_NAME,`_decodeVideo now time is ${o} and prev time is ${ri.prevTimestamp}, diff time is ${l} ms`),ri.cacheTrack.id&&t>=ri.cacheTrack.dts){let e=8+ri.cacheTrack.size,r=new Uint8Array(e);r[0]=e>>>24&255,r[1]=e>>>16&255,r[2]=e>>>8&255,r[3]=255&e,r.set(pn.types.mdat,4),r.set(ri.cacheTrack.data,8),ri.cacheTrack.duration=t-ri.cacheTrack.dts;let i=pn.moof(ri.cacheTrack,ri.cacheTrack.dts);ri.cacheTrack={};let n=new Uint8Array(i.byteLength+r.byteLength);n.set(i,0),n.set(r,i.byteLength),ri.appendBuffer(n.buffer)}else ii.debug.log(ri.TAG_NAME,`timeInit set false , cacheTrack = {} now dts is ${t}, and ts is ${n} cacheTrack dts is ${ri.cacheTrack&&ri.cacheTrack.dts}`),ri.timeInit=!1,ri.cacheTrack={};ri.cacheTrack||(ri.cacheTrack={}),ri.cacheTrack.id=Dt,ri.cacheTrack.sequenceNumber=++ri.sequenceNumber,ri.cacheTrack.size=a,ri.cacheTrack.dts=t,ri.cacheTrack.cts=i,ri.cacheTrack.isKeyframe=r,ri.cacheTrack.data=s,ri.cacheTrack.flags={isLeading:0,dependsOn:r?2:1,isDependedOn:r?1:0,hasRedundancy:0,isNonSync:r?0:1},ri.prevTimestamp=(new Date).getTime()},_decodeAudio(e,t,r){let i=ri.isAAC?e.slice(2):e.slice(1),n=i.byteLength;if(ri.cacheAudioTrack.id&&t>=ri.cacheAudioTrack.dts){let e=8+ri.cacheAudioTrack.size,r=new Uint8Array(e);r[0]=e>>>24&255,r[1]=e>>>16&255,r[2]=e>>>8&255,r[3]=255&e,r.set(pn.types.mdat,4),r.set(ri.cacheAudioTrack.data,8),ri.cacheAudioTrack.duration=t-ri.cacheAudioTrack.dts;let i=pn.moof(ri.cacheAudioTrack,ri.cacheAudioTrack.dts);ri.cacheAudioTrack={};let n=new Uint8Array(i.byteLength+r.byteLength);n.set(i,0),n.set(r,i.byteLength),ri.appendAudioBuffer(n.buffer)}else ri.cacheAudioTrack={};ri.cacheAudioTrack||(ri.cacheAudioTrack={}),ri.cacheAudioTrack.id=It,ri.cacheAudioTrack.sequenceNumber=++ri.audioSequenceNumber,ri.cacheAudioTrack.size=n,ri.cacheAudioTrack.dts=t,ri.cacheAudioTrack.cts=0,ri.cacheAudioTrack.data=i,ri.cacheAudioTrack.flags={isLeading:0,dependsOn:1,isDependedOn:0,hasRedundancy:0}},appendBuffer(e){ii.isDestroyed?ii.debug.warn(ri.TAG_NAME,"appendBuffer() player is destroyed"):ri.mediaSourceAddSourceBufferError?ii.debug.warn(ri.TAG_NAME,"mseDecoder.mediaSourceAddSourceBufferError is true"):ri.mediaSourceAppendBufferFull?ii.debug.warn(ri.TAG_NAME,"mseDecoder.mediaSourceAppendBufferFull is true"):ri.mediaSourceAppendBufferError?ii.debug.warn(ri.TAG_NAME,"mseDecoder.mediaSourceAppendBufferError is true"):ri.mediaSourceBufferError?ii.debug.warn(ri.TAG_NAME,"mseDecoder.mediaSourceBufferError is true"):(ri.pendingSegments.push(e),ri.sourceBuffer&&(ii._opt.mseAutoCleanupSourceBuffer&&ri._needCleanupSourceBuffer()&&ri._doCleanUpSourceBuffer(),yr(ri.getSourceBufferUpdating())&&ri.isStateOpen()&&yr(ri._hasPendingRemoveRanges()))?ri._doAppendSegments():ri.isStateClosed()?(ri.mediaSourceBufferError=!0,ri.emitError(Ce.mseSourceBufferError,"mediaSource is not attached to video or mediaSource is closed")):ri.isStateEnded()?(ri.mediaSourceBufferError=!0,ri.emitError(Ce.mseSourceBufferError,"mediaSource is end")):ri._hasPendingRemoveRanges()&&ii.debug.log(ri.TAG_NAME,`video has pending remove ranges and video length is ${ri.pendingRemoveRanges.length}, audio length is ${ri.pendingAudioRemoveRanges.length}`))},appendAudioBuffer(e){ii.isDestroyed?ii.debug.warn(ri.TAG_NAME,"appendAudioBuffer() player is destroyed"):ri.mediaSourceAddSourceBufferError?ii.debug.warn(ri.TAG_NAME,"mseDecoder.mediaSourceAddSourceBufferError is true"):ri.mediaSourceAppendBufferFull?ii.debug.warn(ri.TAG_NAME,"mseDecoder.mediaSourceAppendBufferFull is true"):ri.mediaSourceAppendBufferError?ii.debug.warn(ri.TAG_NAME,"mseDecoder.mediaSourceAppendBufferError is true"):ri.mediaSourceBufferError?ii.debug.warn(ri.TAG_NAME,"mseDecoder.mediaSourceBufferError is true"):(ri.pendingAudioSegments.push(e),ri.audioSourceBuffer&&(ii._opt.mseAutoCleanupSourceBuffer&&ri._needCleanupSourceBuffer()&&ri._doCleanUpSourceBuffer(),yr(ri.getAudioSourceBufferUpdating())&&ri.isStateOpen()&&yr(ri._hasPendingRemoveRanges()))?ri._doAppendSegments():ri.isStateClosed()?(ri.mediaSourceBufferError=!0,ri.emitError(Ce.mseSourceBufferError,"mediaSource is not attached to video or mediaSource is closed")):ri.isStateEnded()?(ri.mediaSourceBufferError=!0,ri.emitError(Ce.mseSourceBufferError,"mediaSource is end")):ri._hasPendingRemoveRanges()&&ii.debug.log(ri.TAG_NAME,`audio has pending remove ranges and video length is ${ri.pendingRemoveRanges.length}, audio length is ${ri.pendingAudioRemoveRanges.length}`))},getSourceBufferUpdating:()=>ri.sourceBuffer&&ri.sourceBuffer.updating,getAudioSourceBufferUpdating:()=>ri.audioSourceBuffer&&ri.audioSourceBuffer.updating,stop(){ri.abortSourceBuffer(),ri.removeSourceBuffer(),ri.endOfStream()},clearUpAllSourceBuffer(){if(ri.sourceBuffer){const e=ri.sourceBuffer.buffered;for(let t=0;t<e.length;t++){let r=e.start(t),i=e.end(t);ri.pendingRemoveRanges.push({start:r,end:i})}yr(ri.getSourceBufferUpdating())&&ri._doRemoveRanges()}if(ri.audioSourceBuffer){const e=ri.audioSourceBuffer.buffered;for(let t=0;t<e.length;t++){let r=e.start(t),i=e.end(t);ri.pendingAudioRemoveRanges.push({start:r,end:i})}yr(ri.getAudioSourceBufferUpdating())&&ri._doRemoveRanges()}},endOfStream(){if(ri.isStateOpen()&&wr)if(ri.getSourceBufferUpdating()||ri.getAudioSourceBufferUpdating())ii.debug.log(ri.TAG_NAME,"endOfStream() has pending eos"),ri.hasPendingEos=!0;else{ri.hasPendingEos=!1;try{ii.debug.log(ri.TAG_NAME,"endOfStream()"),ri.mediaSource.endOfStream()}catch(e){ii.debug.warn(ri.TAG_NAME,"endOfStream() error",e)}}},abortSourceBuffer(){if(ri.isStateOpen){if(ri.sourceBuffer){try{ii.debug.log(ri.TAG_NAME,"abortSourceBuffer() abort sourceBuffer"),ri.sourceBuffer.abort()}catch(e){}yr(ri.getSourceBufferUpdating())&&ri._doRemoveRanges()}if(ri.audioSourceBuffer){try{ii.debug.log(ri.TAG_NAME,"abortSourceBuffer() abort audioSourceBuffer"),ri.audioSourceBuffer.abort()}catch(e){}yr(ri.getAudioSourceBufferUpdating())&&ri._doRemoveRanges()}}ri.sourceBuffer=null,ri.audioSourceBuffer=null},removeSourceBuffer(){if(!ri.isStateClosed()&&ri.mediaSource){if(ri.sourceBuffer)try{ii.debug.log(ri.TAG_NAME,"removeSourceBuffer() sourceBuffer"),ri.mediaSource.removeSourceBuffer(ri.sourceBuffer)}catch(e){ii.debug.warn(ri.TAG_NAME,"removeSourceBuffer() sourceBuffer error",e)}if(ri.audioSourceBuffer)try{ii.debug.log(ri.TAG_NAME,"removeSourceBuffer() audioSourceBuffer"),ri.mediaSource.removeSourceBuffer(ri.audioSourceBuffer)}catch(e){ii.debug.warn(ri.TAG_NAME,"removeSourceBuffer() audioSourceBuffer error",e)}}},_hasPendingSegments:()=>ri.pendingSegments.length>0||ri.pendingAudioSegments.length>0,getPendingSegmentsLength:()=>ri.pendingSegments.length,_handleUpdatePlaybackRate(){},_doAppendSegments(){if(ri.isStateClosed()||ri.isStateEnded())ii.debug.log(ri.TAG_NAME,"_doAppendSegments() mediaSource is closed or ended and return");else if(null!==ri.sourceBuffer){if(ri.needInitAudio()&&null===ri.audioSourceBuffer)return ii.debug.log(ri.TAG_NAME,"_doAppendSegments() audioSourceBuffer is null and need init audio source buffer"),void(null===ri.audioSourceBufferCheckTimeout&&(ri.audioSourceBufferCheckTimeout=setTimeout((()=>{ri._clearAudioSourceBufferCheckTimeout(),ri.emitError(Ce.mediaSourceAudioInitTimeout)}),1e3)));if(yr(ri.getSourceBufferUpdating())&&ri.pendingSegments.length>0){const e=ri.pendingSegments.shift();try{ri.sourceBuffer.appendBuffer(e)}catch(e){ii.debug.error(ri.TAG_NAME,"mseDecoder.sourceBuffer.appendBuffer()",e.code,e),22===e.code?(ri.stop(),ri.mediaSourceAppendBufferFull=!0,ri.emitError(Ce.mediaSourceFull)):11===e.code?(ri.stop(),ri.mediaSourceAppendBufferError=!0,ri.emitError(Ce.mediaSourceAppendBufferError)):(ri.stop(),ri.mediaSourceBufferError=!0,ri.emitError(Ce.mseSourceBufferError,e.code))}}if(yr(ri.getAudioSourceBufferUpdating())&&ri.pendingAudioSegments.length>0){const e=ri.pendingAudioSegments.shift();try{ri.audioSourceBuffer.appendBuffer(e)}catch(e){ii.debug.error(ri.TAG_NAME,"mseDecoder.audioSourceBuffer.appendBuffer()",e.code,e),22===e.code?(ri.stop(),ri.mediaSourceAppendBufferFull=!0,ri.emitError(Ce.mediaSourceFull)):11===e.code?(ri.stop(),ri.mediaSourceAppendBufferError=!0,ri.emitError(Ce.mediaSourceAppendBufferError)):(ri.stop(),ri.mediaSourceBufferError=!0,ri.emitError(Ce.mseSourceBufferError,e.code))}}}else ii.debug.log(ri.TAG_NAME,"_doAppendSegments() sourceBuffer is null and wait init and return")},_doCleanUpSourceBuffer(){const e=ri.$video.currentTime;if(ri.sourceBuffer){const t=ri.sourceBuffer.buffered;let r=!1;for(let i=0;i<t.length;i++){let n=t.start(i),s=t.end(i);if(n<=e&&e<s+3){if(e-n>=ii._opt.mseAutoCleanupMaxBackwardDuration){r=!0;let t=e-ii._opt.mseAutoCleanupMinBackwardDuration;ri.pendingRemoveRanges.push({start:n,end:t})}}else s<e&&(r=!0,ri.pendingRemoveRanges.push({start:n,end:s}))}r&&yr(ri.getSourceBufferUpdating())&&ri._doRemoveRanges()}if(ri.audioSourceBuffer){const t=ri.audioSourceBuffer.buffered;let r=!1;for(let i=0;i<t.length;i++){let n=t.start(i),s=t.end(i);if(n<=e&&e<s+3){if(e-n>=ii._opt.mseAutoCleanupMaxBackwardDuration){r=!0;let t=e-ii._opt.mseAutoCleanupMinBackwardDuration;ri.pendingAudioRemoveRanges.push({start:n,end:t})}}else s<e&&(r=!0,ri.pendingAudioRemoveRanges.push({start:n,end:s}))}r&&yr(ri.getAudioSourceBufferUpdating())&&ri._doRemoveRanges()}},_hasPendingRemoveRanges:()=>ri.pendingRemoveRanges.length>0||ri.pendingAudioRemoveRanges.length>0,needInitAudio:()=>ii._opt.hasAudio&&ii._opt.mseDecodeAudio,_doRemoveRanges(){if(ri.sourceBuffer&&yr(ri.getSourceBufferUpdating())){let e=ri.pendingRemoveRanges;for(;e.length&&yr(ri.getSourceBufferUpdating());){let t=e.shift();try{ri.sourceBuffer.remove(t.start,t.end)}catch(e){ii.debug.warn(ri.TAG_NAME,"_doRemoveRanges() sourceBuffer error",e)}}}if(ri.audioSourceBuffer&&yr(ri.getAudioSourceBufferUpdating())){let e=ri.pendingAudioRemoveRanges;for(;e.length&&yr(ri.getAudioSourceBufferUpdating());){let t=e.shift();try{ri.audioSourceBuffer.remove(t.start,t.end)}catch(e){ii.debug.warn(ri.TAG_NAME,"_doRemoveRanges() audioSourceBuffer error",e)}}}},_getPlaybackRate(){},_needCleanupSourceBuffer(){if(yr(ii._opt.mseAutoCleanupSourceBuffer))return!1;const e=ri.$video.currentTime;if(ri.sourceBuffer){let t=ri.sourceBuffer.buffered;if(t.length>=1&&e-t.start(0)>=ii._opt.mseAutoCleanupMaxBackwardDuration)return!0}if(ri.audioSourceBuffer){let t=ri.audioSourceBuffer.buffered;if(t.length>=1&&e-t.start(0)>=ii._opt.mseAutoCleanupMaxBackwardDuration)return!0}return!1},_clearAudioSourceBufferCheckTimeout(){ri.audioSourceBufferCheckTimeout&&(clearTimeout(ri.audioSourceBufferCheckTimeout),ri.audioSourceBufferCheckTimeout=null)},_clearAudioNoDataCheckTimeout(){ri.audioSourceNoDataCheckTimeout&&(clearTimeout(ri.audioSourceNoDataCheckTimeout),ri.audioSourceNoDataCheckTimeout=null)},getHandle:()=>ri.mediaSource.handle,emitError(e){postMessage({cmd:ie,value:e,msg:arguments.length>1&&void 0!==arguments[1]?arguments[1]:""})}});let ii={isPlayer:!0,isPlayback:!1,dropping:!1,isPushDropping:!1,isWorkerFetch:!1,isDestroyed:!1,fetchStatus:Tt,_opt:Sr(),mp3Demuxer:null,delay:-1,pushLatestDelay:-1,firstTimestamp:null,startTimestamp:null,preDelayTimestamp:null,stopId:null,streamFps:null,streamAudioFps:null,streamVideoFps:null,writableStream:null,networkDelay:0,webglObj:null,startStreamRateAndStatsInterval:function(){ii.stopStreamRateAndStatsInterval(),l=setInterval((()=>{d&&d(0);const e=JSON.stringify({demuxBufferDelay:ii.getVideoBufferLength(),audioDemuxBufferDelay:ii.getAudioBufferLength(),streamBufferByteLength:ii.getStreamBufferLength(),netBuf:ii.networkDelay||0,pushLatestDelay:ii.pushLatestDelay||0,latestDelay:ii.delay,isStreamTsMoreThanLocal:be});postMessage({cmd:O,type:Ue,value:e})}),1e3)},stopStreamRateAndStatsInterval:function(){l&&(clearInterval(l),l=null)},useOffscreen:function(){return ii._opt.useOffscreen&&"undefined"!=typeof OffscreenCanvas},getDelay:function(e,t){if(!e||ii._opt.hasVideo&&!ve)return-1;if(t===ne)return ii.delay;if(ii.preDelayTimestamp&&ii.preDelayTimestamp>e)return ii.preDelayTimestamp-e>1e3&&ii.debug.warn("worker",`getDelay() and preDelayTimestamp is ${ii.preDelayTimestamp} > timestamp is ${e} more than ${ii.preDelayTimestamp-e}ms and return ${ii.delay}`),ii.preDelayTimestamp=e,ii.delay;if(ii.firstTimestamp){if(e){const t=Date.now()-ii.startTimestamp,r=e-ii.firstTimestamp;t>=r?(be=!1,ii.delay=t-r):(be=!0,ii.delay=r-t)}}else ii.firstTimestamp=e,ii.startTimestamp=Date.now(),ii.delay=-1;return ii.preDelayTimestamp=e,ii.delay},getDelayNotUpdateDelay:function(e,t){if(!e||ii._opt.hasVideo&&!ve)return-1;if(t===ne)return ii.pushLatestDelay;if(ii.preDelayTimestamp&&ii.preDelayTimestamp-e>1e3)return ii.debug.warn("worker",`getDelayNotUpdateDelay() and preDelayTimestamp is ${ii.preDelayTimestamp} > timestamp is ${e} more than ${ii.preDelayTimestamp-e}ms and return -1`),-1;if(ii.firstTimestamp){let t=-1;if(e){const r=Date.now()-ii.startTimestamp,i=e-ii.firstTimestamp;r>=i?(be=!1,t=r-i):(be=!0,t=i-r)}return t}return-1},resetDelay:function(){ii.firstTimestamp=null,ii.startTimestamp=null,ii.delay=-1,ii.dropping=!1},resetAllDelay:function(){ii.resetDelay(),ii.preDelayTimestamp=null},doDecode:function(e){ii._opt.isEmitSEI&&e.type===se&&ii.isWorkerFetch&&ii.findSei(e.payload,e.ts),ii.isPlayUseMSEAndDecoderInWorker()?e.type===ne?ii._opt.mseDecodeAudio?ri.decodeAudio(e.payload,e.ts):e.decoder.decode(e.payload,e.ts):e.type===se&&ri.decodeVideo(e.payload,e.ts,e.isIFrame,e.cts):ii._opt.useWCS&&ii.useOffscreen()&&e.type===se&&s.decode?s.decode(e.payload,e.ts,e.cts):e.decoder.decode(e.payload,e.ts,e.isIFrame,e.cts)},decodeNext(e){if(0===i.length)return;const t=e.ts,n=i[0],s=e.type===se&&mr(e.payload);if(yr(r))s&&(ii.debug.log("worker",`decode data type is ${e.type} and\n                ts is ${t} next data type is ${n.type} ts is ${n.ts}\n                isVideoSqeHeader is ${s}`),i.shift(),ii.doDecode(n));else{const r=n.ts-t,a=n.type===ne&&e.type===se;(r<=20||a||s)&&(ii.debug.log("worker",`decode data type is ${e.type} and\n                ts is ${t} next data type is ${n.type} ts is ${n.ts}\n                diff is ${r} and isVideoAndNextAudio is ${a} and isVideoSqeHeader is ${s}`),i.shift(),ii.doDecode(n))}},init:function(){ii.debug.log("worker","init and opt is",JSON.stringify(ii._opt));const e=ii._opt.playType===y,t=ii._opt.playType===v;if(Ur.init(),ii.isPlayer=e,ii.isPlayback=t,ii.isPlayUseMSEAndDecoderInWorker()&&ri&&ri.init(),ii.isPlaybackCacheBeforeDecodeForFpsRender())ii.debug.log("worker","playback and playbackIsCacheBeforeDecodeForFpsRender is true");else{ii.debug.log("worker","setInterval()");const t=ii._opt.videoBuffer+ii._opt.videoBufferDelay,r=()=>{let r=null;if(i.length){if(ii.isPushDropping)return void ii.debug.warn("worker",`loop() isPushDropping is true and bufferList length is ${i.length}`);if(ii.dropping){for(r=i.shift(),ii.debug.warn("worker",`loop() dropBuffer is dropping and isIFrame ${r.isIFrame} and delay is ${ii.delay} and bufferlist is ${i.length}`);!r.isIFrame&&i.length;)r=i.shift();const e=ii.getDelayNotUpdateDelay(r.ts,r.type);r.isIFrame&&e<=ii.getNotDroppingDelayTs()&&(ii.debug.log("worker","loop() is dropping = false, is iFrame"),ii.dropping=!1,ii.doDecode(r),ii.decodeNext(r))}else if(ii.isPlayback||ii.isPlayUseMSE()||0===ii._opt.videoBuffer)for(;i.length;)r=i.shift(),ii.doDecode(r);else if(r=i[0],-1===ii.getDelay(r.ts,r.type))ii.debug.log("worker","loop() common dumex delay is -1 ,data.ts is",r.ts),i.shift(),ii.doDecode(r),ii.decodeNext(r);else if(ii.delay>t&&e)ii.hasIframeInBufferList()?(ii.debug.log("worker",`delay is ${ii.delay} > maxDelay ${t}, set dropping is true`),ii.resetAllDelay(),ii.dropping=!0,postMessage({cmd:H})):(i.shift(),ii.doDecode(r),ii.decodeNext(r));else for(;i.length;){if(r=i[0],!(ii.getDelay(r.ts,r.type)>ii._opt.videoBuffer)){ii.delay<0&&ii.debug.warn("worker",`loop() do not decode and delay is ${ii.delay}, bufferList is ${i.length}`);break}i.shift(),ii.doDecode(r)}}else-1!==ii.delay&&ii.debug.log("worker","loop() bufferList is empty and reset delay"),ii.resetAllDelay()};ii.stopId=setInterval((()=>{let e=(new Date).getTime();$e||($e=e);const t=e-$e;t>100&&ii.debug.warn("worker",`loop demux diff time is ${t}`),r(),$e=(new Date).getTime()}),20)}if(yr(ii._opt.checkFirstIFrame)&&(ve=!0),ii.isPlayUseMSEAndDecoderInWorker()&&ri){const e=ri.getHandle();e&&postMessage({cmd:te,mseHandle:e},[e])}},playbackCacheLoop:function(){ii.stopId&&(clearInterval(ii.stopId),ii.stopId=null);const e=()=>{let e=null;i.length&&(e=i.shift(),ii.doDecode(e))};e();const t=Math.ceil(1e3/(ii.streamFps*ii._opt.playbackRate));ii.debug.log("worker",`playbackCacheLoop fragDuration is ${t}, streamFps is ${ii.streamFps}, streamAudioFps is ${ii.streamAudioFps} ,streamVideoFps is ${ii.streamVideoFps} playbackRate is ${ii._opt.playbackRate}`),ii.stopId=setInterval(e,t)},close:function(){if(ii.debug.log("worker","close"),ii.isDestroyed=!0,Er(),!o||1!==o.readyState&&2!==o.readyState?o&&ii.debug.log("worker",`close() and socket.readyState is ${o.readyState}`):(br=!0,o.close(1e3,"Client disconnecting")),o=null,ii.stopStreamRateAndStatsInterval(),ii.stopId&&(clearInterval(ii.stopId),ii.stopId=null),ii.mp3Demuxer&&(ii.mp3Demuxer.destroy(),ii.mp3Demuxer=null),ii.writableStream&&yr(ii.writableStream.locked)&&ii.writableStream.close().catch((e=>{ii.debug.log("worker","close() and writableStream.close() error",e)})),ii.writableStream=null,ni)try{ni.clear&&ni.clear(),ni=null}catch(e){ii.debug.warn("worker","close() and audioDecoder.clear error",e)}if(si)try{si.clear&&si.clear(),si=null}catch(e){ii.debug.warn("worker","close() and videoDecoder.clear error",e)}d=null,$e=null,be=!1,s&&(s.reset&&s.reset(),s=null),ri&&(ri.destroy(),ri=null),ii.firstTimestamp=null,ii.startTimestamp=null,ii.networkDelay=0,ii.streamFps=null,ii.streamAudioFps=null,ii.streamVideoFps=null,ii.delay=-1,ii.pushLatestDelay=-1,ii.preDelayTimestamp=null,ii.dropping=!1,ii.isPushDropping=!1,ii.isPlayer=!0,ii.isPlayback=!1,ii.isWorkerFetch=!1,ii._opt=Sr(),ii.webglObj&&(ii.webglObj.destroy(),ii.offscreenCanvas.removeEventListener("webglcontextlost",ii.onOffscreenCanvasWebglContextLost),ii.offscreenCanvas.removeEventListener("webglcontextrestored",ii.onOffscreenCanvasWebglContextRestored),ii.offscreenCanvas=null,ii.offscreenCanvasGL=null,ii.offscreenCanvasCtx=null),i=[],n=[],c&&(c.buffer=null,c=null),h=null,w=null,S=!1,x=!1,ve=!1,Ot=!1,Gt=!1,Ht=!1,qt=null,Zt=null,ht=[],_t=0,St=0,Ke=null,st=null,Et=null,xt=null,ir=null,zt=0,Nt=0,ft=null,pt=null,ii.fetchStatus=Tt,wr=!0,Ur.destroy(),Or.destroy(),Gr.destroy(),Wr.destroy(),postMessage({cmd:q})},pushBuffer:function(e,t){if(t.type===ne&&Wt(e)){if(ii.debug.log("worker",`pushBuffer audio ts is ${t.ts}, isAacCodecPacket is true`),ii._opt.isRecordTypeFlv){const t=new Uint8Array(e);postMessage({cmd:Z,buffer:t},[t.buffer])}ii.decodeAudio(e,t.ts)}else if(t.type===se&&t.isIFrame&&mr(e)){if(ii.debug.log("worker",`pushBuffer video ts is ${t.ts}, isVideoSequenceHeader is true`),ii._opt.isRecordTypeFlv){const t=new Uint8Array(e);postMessage({cmd:J,buffer:t},[t.buffer])}ii.decodeVideo(e,t.ts,t.isIFrame,t.cts)}else{if(ii._opt.isRecording)if(ii._opt.isRecordTypeFlv){const r=new Uint8Array(e);postMessage({cmd:Q,type:t.type,buffer:r,ts:t.ts},[r.buffer])}else if(ii._opt.recordType===b)if(t.type===se){const r=new Uint8Array(e).slice(5);postMessage({cmd:R,buffer:r,isIFrame:t.isIFrame,ts:t.ts,cts:t.cts},[r.buffer])}else if(t.type===ne&&ii._opt.isWasmMp4){const r=new Uint8Array(e),i=jt(r)?r.slice(2):r.slice(1);postMessage({cmd:F,buffer:i,ts:t.ts},[i.buffer])}if(ii.isPlayer&&zt>0&&xt>0&&t.type===se){const e=t.ts-xt,r=zt+zt/2;e>r&&ii.debug.log("worker",`pushBuffer video\n                    ts is ${t.ts}, preTimestamp is ${xt},\n                    diff is ${e} and preTimestampDuration is ${zt} and maxDiff is ${r}\n                    maybe trigger black screen or flower screen\n                    `)}if(ii.isPlayer&&xt>0&&t.type===se&&t.ts<xt&&xt-t.ts>E&&(ii.debug.warn("worker",`pushBuffer,\n                preTimestamp is ${xt}, options.ts is ${t.ts},\n                diff is ${xt-t.ts} more than 3600000,\n                and resetAllDelay`),ii.resetAllDelay(),xt=null,zt=0),ii.isPlayer&&xt>0&&t.ts<=xt&&t.type===se&&(ii.debug.warn("worker",`pushBuffer() and isIFrame is ${t.isIFrame} and,\n                options.ts is ${t.ts} less than (or equal) preTimestamp is ${xt} and\n                payloadBufferSize is ${e.byteLength} and prevPayloadBufferSize is ${Nt}`),ii._opt.isDropSameTimestampGop&&yr(t.isIFrame)&&ve)){const e=ii.hasIframeInBufferList(),t=yr(ii.isPushDropping);return ii.debug.log("worker",`pushBuffer, isDropSameTimestampGop is true and\n                    hasIframe is ${e} and isNotPushDropping is ${t} and next dropBuffer`),void(e&&t?ii.dropBuffer$2():(ii.clearBuffer(!0),gr(ii._opt.checkFirstIFrame)&&gr(r)&&(ii.isPlayUseMSEAndDecoderInWorker()?ri.isDecodeFirstIIframe=!1:postMessage({cmd:ee}))))}if(ii.isPlayer&&ve){const e=ii._opt.videoBuffer+ii._opt.videoBufferDelay,r=ii.getDelayNotUpdateDelay(t.ts,t.type);ii.pushLatestDelay=r,r>e&&ii.delay<e&&ii.delay>0&&ii.hasIframeInBufferList()&&!1===ii.isPushDropping&&(ii.debug.log("worker",`pushBuffer(), pushLatestDelay is ${r} more than ${e} and decoder.delay is ${ii.delay} and has iIframe and next decoder.dropBuffer$2()`),ii.dropBuffer$2())}if(ii.isPlayer&&t.type===se&&(xt>0&&(zt=t.ts-xt),Nt=e.byteLength,xt=t.ts),t.type===ne?i.push({ts:t.ts,payload:e,decoder:{decode:ii.decodeAudio},type:ne,isIFrame:!1}):t.type===se&&i.push({ts:t.ts,cts:t.cts,payload:e,decoder:{decode:ii.decodeVideo},type:se,isIFrame:t.isIFrame}),ii.isPlaybackCacheBeforeDecodeForFpsRender()&&(or(ii.streamVideoFps)||or(ii.streamAudioFps))){let e=ii.streamVideoFps,t=ii.streamAudioFps;if(or(ii.streamVideoFps)&&(e=fr(i,se),e&&(ii.streamVideoFps=e,postMessage({cmd:$,value:ii.streamVideoFps}),ii.streamFps=t?e+t:e,yr(ii._opt.hasAudio)&&(ii.debug.log("worker","playbackCacheBeforeDecodeForFpsRender, _opt.hasAudio is false and set streamAudioFps is 0"),ii.streamAudioFps=0),ii.playbackCacheLoop())),or(ii.streamAudioFps)&&(t=fr(i,ne),t&&(ii.streamAudioFps=t,ii.streamFps=e?e+t:t,ii.playbackCacheLoop())),or(ii.streamVideoFps)&&or(ii.streamAudioFps)){const r=i.map((e=>({type:e.type,ts:e.ts})));ii.debug.log("worker",`playbackCacheBeforeDecodeForFpsRender, calc streamAudioFps is ${t}, streamVideoFps is ${e}, bufferListLength  is ${i.length}, and ts list is ${JSON.stringify(r)}`)}const r=ii.getAudioBufferLength()>0,n=r?60:40;i.length>=n&&(ii.debug.warn("worker",`playbackCacheBeforeDecodeForFpsRender, bufferListLength  is ${i.length} more than ${n}, and hasAudio is ${r} an set streamFps is 25`),ii.streamVideoFps=25,postMessage({cmd:$,value:ii.streamVideoFps}),r?(ii.streamAudioFps=25,ii.streamFps=ii.streamVideoFps+ii.streamAudioFps):ii.streamFps=ii.streamVideoFps,ii.playbackCacheLoop())}}},getVideoBufferLength(){let e=0;return i.forEach((t=>{t.type===se&&(e+=1)})),e},hasIframeInBufferList:()=>i.some((e=>e.type===se&&e.isIFrame)),isAllIframeInBufferList(){const e=ii.getVideoBufferLength();let t=0;return i.forEach((e=>{e.type===se&&e.isIFrame&&(t+=1)})),e===t},getNotDroppingDelayTs:()=>ii._opt.videoBuffer+ii._opt.videoBufferDelay/2,getAudioBufferLength(){let e=0;return i.forEach((t=>{t.type===ne&&(e+=1)})),e},getStreamBufferLength(){let e=0;return c&&c.buffer&&(e=c.buffer.byteLength),ii._opt.isNakedFlow?Ur.lastBuf&&(e=Ur.lastBuf.byteLength):ii._opt.isTs?Wr._remainingPacketData&&(e=Wr._remainingPacketData.byteLength):ii._opt.isFmp4&&Or.mp4Box&&(e=Or.mp4Box.getAllocatedSampleDataSize()),e},fetchStream:function(e,t){ii.debug.log("worker","fetchStream, url is "+e,"options:",JSON.stringify(t)),ii.isWorkerFetch=!0,t.isFlv?ii._opt.isFlv=!0:t.isFmp4?ii._opt.isFmp4=!0:t.isMpeg4?ii._opt.isMpeg4=!0:t.isNakedFlow?ii._opt.isNakedFlow=!0:t.isTs&&(ii._opt.isTs=!0),d=nr((e=>{postMessage({cmd:O,type:Se,value:e})})),ii.startStreamRateAndStatsInterval(),t.isFmp4&&(Or.listenMp4Box(),ii._opt.isFmp4Private&&Or.initTransportDescarmber()),t.protocol===_?(c=new Ar(ii.demuxFlv()),fetch(e,{signal:a.signal}).then((e=>{if(gr(br))return ii.debug.log("worker","request abort and run res.body.cancel()"),ii.fetchStatus=Tt,void e.body.cancel();if(!pr(e))return ii.debug.warn("worker",`fetch response status is ${e.status} and ok is ${e.ok} and emit error and next abort()`),Er(),void postMessage({cmd:O,type:Ce.fetchError,value:`fetch response status is ${e.status} and ok is ${e.ok}`});if(postMessage({cmd:O,type:xe}),ur())ii.writableStream=new WritableStream({write:e=>a&&a.signal&&a.signal.aborted?(ii.debug.log("worker","writableStream write() and abortController.signal.aborted is true so return"),void(ii.fetchStatus=Ct)):gr(br)?(ii.debug.log("worker","writableStream write() and requestAbort is true so return"),void(ii.fetchStatus=Ct)):void("string"!=typeof e?(ii.fetchStatus=kt,d(e.byteLength),t.isFlv?c.write(e):t.isFmp4?ii.demuxFmp4(e):t.isMpeg4?ii.demuxMpeg4(e):t.isTs&&ii.demuxTs(e)):ii.debug.warn("worker",`writableStream write() and value is "${e}" string so return`)),close:()=>{ii.debug.log("worker","writableStream close()"),ii.fetchStatus=Ct,c=null,Er(),postMessage({cmd:O,type:we,value:m,msg:"fetch done"})},abort:e=>{if(a&&a.signal&&a.signal.aborted)return ii.debug.log("worker","writableStream abort() and abortController.signal.aborted is true so return"),void(ii.fetchStatus=Ct);c=null,e.name!==At?(ii.debug.log("worker",`writableStream abort() and e is ${e.toString()}`),Er(),postMessage({cmd:O,type:Ce.fetchError,value:e.toString()})):ii.debug.log("worker","writableStream abort() and e.name is AbortError so return")}}),e.body.pipeTo(ii.writableStream);else{const r=e.body.getReader(),i=()=>{r.read().then((e=>{let{done:r,value:n}=e;return r?(ii.debug.log("worker","fetchNext().then() and done is true"),ii.fetchStatus=Ct,c=null,Er(),void postMessage({cmd:O,type:we,value:m,msg:"fetch done"})):a&&a.signal&&a.signal.aborted?(ii.debug.log("worker","fetchNext().then() and abortController.signal.aborted is true so return"),void(ii.fetchStatus=Ct)):gr(br)?(ii.debug.log("worker","fetchNext().then() and requestAbort is true so return"),void(ii.fetchStatus=Ct)):void("string"!=typeof n?(ii.fetchStatus=kt,d(n.byteLength),t.isFlv?c.write(n):t.isFmp4?ii.demuxFmp4(n):t.isMpeg4&&ii.demuxMpeg4(n),i()):ii.debug.warn("worker",`fetchNext().then() and value "${n}" is string so return`))})).catch((e=>{if(a&&a.signal&&a.signal.aborted)return ii.debug.log("worker","fetchNext().catch() and abortController.signal.aborted is true so return"),void(ii.fetchStatus=Ct);c=null,e.name!==At?(ii.debug.log("worker",`fetchNext().catch() and e is ${e.toString()}`),Er(),postMessage({cmd:O,type:Ce.fetchError,value:e.toString()})):ii.debug.log("worker","fetchNext().catch() and e.name is AbortError so return")}))};i()}})).catch((e=>{a&&a.signal&&a.signal.aborted?ii.debug.log("worker","fetch().catch() and abortController.signal.aborted is true so return"):e.name!==At?(ii.debug.log("worker",`fetch().catch() and e is ${e.toString()}`),Er(),postMessage({cmd:O,type:Ce.fetchError,value:e.toString()}),c=null):ii.debug.log("worker","fetch().catch() and e.name is AbortError so return")}))):t.protocol===p&&(t.isFlv&&(c=new Ar(ii.demuxFlv())),o=new WebSocket(e),o.binaryType="arraybuffer",o.onopen=()=>{ii.debug.log("worker","fetchStream, WebsocketStream  socket open"),postMessage({cmd:O,type:xe}),postMessage({cmd:O,type:ke})},o.onclose=e=>{u?ii.debug.log("worker","fetchStream, WebsocketStream socket close and isSocketError is true , so return"):(ii.debug.log("worker",`fetchStream, WebsocketStream socket close and code is ${e.code}`),1006===e.code&&ii.debug.error("worker",`fetchStream, WebsocketStream socket close abnormally and code is ${e.code}`),gr(br)?ii.debug.log("worker","fetchStream, WebsocketStream socket close and requestAbort is true so return"):(c=null,postMessage({cmd:O,type:we,value:g,msg:e.code})))},o.onerror=e=>{ii.debug.error("worker","fetchStream, WebsocketStream socket error",e),u=!0,c=null,postMessage({cmd:O,type:Ce.websocketError,value:e.isTrusted?"websocket user aborted":"websocket error"})},o.onmessage=e=>{"string"!=typeof e.data?(d(e.data.byteLength),t.isFlv?c.write(e.data):t.isFmp4?ii.demuxFmp4(e.data):t.isMpeg4?ii.demuxMpeg4(e.data):ii._opt.isNakedFlow?ii.demuxNakedFlow(e.data):ii.demuxM7s(e.data)):ii.debug.warn("worker",`socket on message is string "${e.data}" and return`)})},demuxFlv:function*(){yield 9;const e=new ArrayBuffer(4),t=new Uint8Array(e),r=new Uint32Array(e);for(;;){t[3]=0;const e=yield 15,i=e[4];t[0]=e[7],t[1]=e[6],t[2]=e[5];const n=r[0];t[0]=e[10],t[1]=e[9],t[2]=e[8],t[3]=e[11];let s=r[0];const a=(yield n).slice();switch(i){case ae:if(a.byteLength>0){let e=a;gr(ii._opt.m7sCryptoAudio)&&(e=ii.cryptoPayloadAudio(a)),ii.decode(e,{type:ne,ts:s})}else ii.debug.warn("worker",`demuxFlv() type is audio and payload.byteLength is ${a.byteLength} and return`);break;case oe:if(a.byteLength>=6){const e=a[0];if(ii._isEnhancedH265Header(e))ii._decodeEnhancedH265Video(a,s);else{a[0];let e=a[0]>>4===Ut;if(e&&mr(a)&&null===qt){const e=15&a[0];qt=e===Fe,Zt=er(a,qt),ii.debug.log("worker",`demuxFlv() isVideoSequenceHeader is true and isHevc is ${qt} and nalUnitSize is ${Zt}`)}e&&ii.calcIframeIntervalTimestamp(s),ii.isPlayer&&ii.calcNetworkDelay(s),r[0]=a[4],r[1]=a[3],r[2]=a[2],r[3]=0;let t=r[0],i=ii.cryptoPayload(a,e);ii.decode(i,{type:se,ts:s,isIFrame:e,cts:t})}}else ii.debug.warn("worker",`demuxFlv() type is video and payload.byteLength is ${a.byteLength} and return`);break;case de:postMessage({cmd:X,buffer:a},[a.buffer]);break;default:ii.debug.log("worker",`demuxFlv() type is ${i}`)}}},decode:function(e,t){t.type===ne?ii._opt.hasAudio&&(postMessage({cmd:O,type:Ee,value:e.byteLength}),ii.isPlayer?ii.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts}):ii.isPlayback&&(ii.isPlaybackOnlyDecodeIFrame()||(ii.isPlaybackCacheBeforeDecodeForFpsRender(),ii.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts})))):t.type===se&&ii._opt.hasVideo&&(postMessage({cmd:O,type:Ae,value:e.byteLength}),postMessage({cmd:O,type:Be,value:t.ts}),ii.isPlayer?ii.pushBuffer(e,{type:t.type,ts:t.ts,isIFrame:t.isIFrame,cts:t.cts}):ii.isPlayback&&(ii.isPlaybackOnlyDecodeIFrame()?t.isIFrame&&ii.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts,isIFrame:t.isIFrame}):(ii.isPlaybackCacheBeforeDecodeForFpsRender(),ii.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts,isIFrame:t.isIFrame}))))},cryptoPayload:function(e,t){let r=e;return ii._opt.isM7sCrypto?ii._opt.cryptoIV&&ii._opt.cryptoIV.byteLength>0&&ii._opt.cryptoKey&&ii._opt.cryptoKey.byteLength>0?r=Ri(e,ii._opt.cryptoKey,ii._opt.cryptoIV,qt):ii.debug.error("worker",`isM7sCrypto cryptoKey.length is ${ii._opt.cryptoKey&&ii._opt.cryptoKey.byteLength} or cryptoIV.length is ${ii._opt.cryptoIV&&ii._opt.cryptoIV.byteLength} null`):ii._opt.isSm4Crypto?ii._opt.sm4CryptoKey&&t?r=sn(e,ii._opt.sm4CryptoKey):ii._opt.sm4CryptoKey||ii.debug.error("worker","isSm4Crypto opt.sm4CryptoKey is null"):ii._opt.isXorCrypto&&(ii._opt.cryptoIV&&ii._opt.cryptoIV.byteLength>0&&ii._opt.cryptoKey&&ii._opt.cryptoKey.byteLength>0?r=un(e,ii._opt.cryptoKey,ii._opt.cryptoIV,qt):ii.debug.error("worker",`isXorCrypto cryptoKey.length is ${ii._opt.cryptoKey&&ii._opt.cryptoKey.byteLength} or cryptoIV.length is ${ii._opt.cryptoIV&&ii._opt.cryptoIV.byteLength} null`)),r},cryptoPayloadAudio:function(e){let t=e;return ii._opt.isM7sCrypto&&(ii._opt.cryptoIV&&ii._opt.cryptoIV.byteLength>0&&ii._opt.cryptoKey&&ii._opt.cryptoKey.byteLength>0?e[0]>>4===Re&&(t=zi(e,ii._opt.cryptoKey,ii._opt.cryptoIV)):ii.debug.error("worker",`isM7sCrypto cryptoKey.length is ${ii._opt.cryptoKey&&ii._opt.cryptoKey.byteLength} or cryptoIV.length is ${ii._opt.cryptoIV&&ii._opt.cryptoIV.byteLength} null`)),t},setCodecAudio:function(e,t){const r=e[0]>>4,i=e[0]>>1&1;if(ir=r===Re?i?16:8:0===i?8:16,ni&&ni.setCodec)if(Wt(e)||r===ze||r===Ne||r===Oe){ii.debug.log("worker",`setCodecAudio: init audio codec, codeId is ${r}`);const i=r===Re?e.slice(2):new Uint8Array(0);ni.setCodec(r,ii._opt.sampleRate,i),r===Re&&postMessage({cmd:P,buffer:i},[i.buffer]),x=!0,r!==Re&&(r===Oe?(ii.mp3Demuxer||(ii.mp3Demuxer=new dn(ii),ii.mp3Demuxer.on("data",((e,t)=>{ni.decode(e,t)}))),ii.mp3Demuxer.dispatch(e.slice(1),t)):ni.decode(e.slice(1),t))}else ii.debug.warn("worker","setCodecAudio: hasInitAudioCodec is false, codecId is ",r);else ii.debug.error("worker","setCodecAudio: audioDecoder or audioDecoder.setCodec is null")},decodeAudio:function(e,t){if(ii.isDestroyed)ii.debug.log("worker","decodeAudio, decoder is destroyed and return");else if(ii.isPlayUseMSEAndDecoderInWorkerAndMseDecodeAudio())ri.decodeAudio(e,t);else if(gr(r)&&gr(ii._opt.mseDecodeAudio))postMessage({cmd:N,payload:e,ts:t,cts:t},[e.buffer]);else{const r=e[0]>>4;if(x){if(Wt(e))return void ii.debug.log("worker","decodeAudio and has already initialized and payload is aac codec packet so drop this frame");r===Oe?ii.mp3Demuxer.dispatch(e.slice(1),t):ni.decode(r===Re?e.slice(2):e.slice(1),t)}else ii.setCodecAudio(e,t)}},setCodecVideo:function(e){const t=15&e[0];if(si&&si.setCodec)if(mr(e))if(t===Ie||t===Fe){ii.debug.log("worker",`setCodecVideo: init video codec , codecId is ${t}`);const r=e.slice(5);if(t===Ie&&ii._opt.useSIMD){const e=Tr(r);if(e.codecWidth>A||e.codecHeight>A)return postMessage({cmd:j}),void ii.debug.warn("worker",`setCodecVideo: SIMD H264 decode video width is too large, width is ${e.codecWidth}, height is ${e.codecHeight}`)}const i=new Uint8Array(e);S=!0,si.setCodec(t,r),postMessage({cmd:L,code:t}),postMessage({cmd:M,buffer:i,codecId:t},[i.buffer])}else ii.debug.warn("worker",`setCodecVideo: hasInitVideoCodec is false, codecId is ${t} is not H264 or H265`);else ii.debug.warn("worker",`decodeVideo: hasInitVideoCodec is false, codecId is ${t} and frameType is ${e[0]>>4} and packetType is ${e[1]}`);else ii.debug.error("worker","setCodecVideo: videoDecoder or videoDecoder.setCodec is null")},decodeVideo:function(e,t,i){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;if(ii.isDestroyed)ii.debug.log("worker","decodeVideo, decoder is destroyed and return");else if(ii.isPlayUseMSEAndDecoderInWorker())ri.decodeVideo(e,t,i,n);else if(gr(r))postMessage({cmd:z,payload:e,isIFrame:i,ts:t,cts:n,delay:ii.delay},[e.buffer]);else if(S)if(!ve&&i&&(ve=!0),ve){if(i&&mr(e)){const t=15&e[0];let r={};t===Ie?r=Tr(e.slice(5)):t===Fe&&(r=$r(e)),r.codecWidth&&r.codecHeight&&h&&w&&(r.codecWidth!==h||r.codecHeight!==w)&&(ii.debug.warn("worker",`\n                            decodeVideo: video width or height is changed,\n                            old width is ${h}, old height is ${w},\n                            new width is ${r.codecWidth}, new height is ${r.codecHeight},\n                            and emit change event`),Gt=!0,postMessage({cmd:V}))}if(Gt)return void ii.debug.warn("worker","decodeVideo: video width or height is changed, and return");if(Ht)return void ii.debug.warn("worker","decodeVideo: simd decode error, and return");if(mr(e))return void ii.debug.warn("worker","decodeVideo and payload is video sequence header so drop this frame");if(e.byteLength<B)return void ii.debug.warn("worker",`decodeVideo and payload is too small , payload length is ${e.byteLength}`);const r=e.slice(5);si.decode(r,i?1:0,t)}else ii.debug.log("worker","decodeVideo first frame is not iFrame");else ii.setCodecVideo(e)},clearBuffer:function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];ii.debug.log("worker",`clearBuffer,bufferList length is ${i.length}, need clear is ${e}`),e&&(i=[]),ii.isPlayer&&(ii.resetAllDelay(),gr(ii._opt.checkFirstIFrame)&&(ii.dropping=!0,postMessage({cmd:H}))),gr(ii._opt.checkFirstIFrame)&&yr(r)&&(ve=!1)},dropBuffer$2:function(){if(i.length>0){let e=i.findIndex((e=>gr(e.isIFrame)&&e.type===se));if(ii.isAllIframeInBufferList())for(let t=0;t<i.length;t++){const r=i[t],n=ii.getDelayNotUpdateDelay(r.ts,r.type);if(n>=ii.getNotDroppingDelayTs()){ii.debug.log("worker",`dropBuffer$2() isAllIframeInBufferList() is true, and index is ${t} and tempDelay is ${n} and notDroppingDelayTs is ${ii.getNotDroppingDelayTs()}`),e=t;break}}if(e>=0){ii.isPushDropping=!0,postMessage({cmd:H});const t=i.length;i=i.slice(e);const r=i.shift();ii.resetAllDelay(),ii.getDelay(r.ts,r.type),ii.doDecode(r),ii.isPushDropping=!1,ii.debug.log("worker",`dropBuffer$2() iFrameIndex is ${e},and old bufferList length is ${t} ,new bufferList is ${i.length} and new delay is ${ii.delay} `)}else ii.isPushDropping=!1}0===i.length&&(ii.isPushDropping=!1)},demuxM7s:function(e){const t=new DataView(e),r=t.getUint32(1,!1),i=t.getUint8(0),n=new ArrayBuffer(4),s=new Uint32Array(n);switch(i){case ne:ii.decode(new Uint8Array(e,5),{type:ne,ts:r});break;case se:if(t.byteLength>=11){const i=new Uint8Array(e,5),n=i[0];if(ii._isEnhancedH265Header(n))ii._decodeEnhancedH265Video(i,r);else{const e=t.getUint8(5)>>4==1;if(e&&(ii.calcIframeIntervalTimestamp(r),mr(i)&&null===qt)){const e=15&i[0];qt=e===Fe}ii.isPlayer&&ii.calcNetworkDelay(r),s[0]=i[4],s[1]=i[3],s[2]=i[2],s[3]=0;let n=s[0],a=ii.cryptoPayload(i,e);ii.decode(a,{type:se,ts:r,isIFrame:e,cts:n})}}else ii.debug.warn("worker",`demuxM7s() type is video and arrayBuffer length is ${e.byteLength} and return`)}},demuxNakedFlow:function(e){Ur.dispatch(e)},demuxFmp4:function(e){Or.dispatch(e)},demuxMpeg4:function(e){Gr.dispatch(e)},demuxTs:function(e){Wr.dispatch(e)},_decodeEnhancedH265Video:function(e,t){const r=e[0],i=48&r,n=15&r,s=e.slice(1,5),a=new ArrayBuffer(4),o=new Uint32Array(a),d="a"==String.fromCharCode(s[0]);if(qt=yr(d),n===Pt){if(i===Rt){const r=e.slice(5);if(d);else{const i=new Uint8Array(5+r.length);i.set([28,0,0,0,0],0),i.set(r,5),Zt=er(e,qt),ii.debug.log("worker",`demuxFlv() isVideoSequenceHeader(enhancedH265) is true and isHevc is ${qt} and nalUnitSize is ${Zt}`),ii.decode(i,{type:se,ts:t,isIFrame:!0,cts:0})}}}else if(n===Lt){let r=e,n=0;const s=i===Rt;s&&ii.calcIframeIntervalTimestamp(t),d||(o[0]=e[4],o[1]=e[3],o[2]=e[2],o[3]=0,n=o[0],r=Yr(e.slice(8),s),r=ii.cryptoPayload(r,s),ii.decode(r,{type:se,ts:t,isIFrame:s,cts:n}))}else if(n===Mt){const r=i===Rt;r&&ii.calcIframeIntervalTimestamp(t);let n=Yr(e.slice(5),r);n=ii.cryptoPayload(n,r),ii.decode(n,{type:se,ts:t,isIFrame:r,cts:0})}},_isEnhancedH265Header:function(e){return(e&Ft)===Ft},findSei:function(e,t){let r=4;dr(Zt)&&(r=Zt),Jt(e.slice(5),r).forEach((e=>{const r=qt?e[0]>>>1&63:31&e[0];(qt&&(r===ot||r===at)||yr(qt)&&r===je)&&postMessage({cmd:K,buffer:e,ts:t},[e.buffer])}))},calcNetworkDelay:function(e){if(!(ve&&e>0))return;null===Ke?(Ke=e,st=tr()):e<Ke&&(ii.debug.warn("worker",`calcNetworkDelay, dts is ${e} less than bufferStartDts is ${Ke}`),Ke=e,st=tr());const t=e-Ke,r=tr()-st,i=r>t?r-t:0;ii.networkDelay=i,i>ii._opt.networkDelay&&ii._opt.playType===y&&(ii.debug.warn("worker",`calcNetworkDelay now dts:${e}, start dts is ${Ke} vs start is ${t},local diff is ${r} ,delay is ${i}`),postMessage({cmd:O,type:Te,value:i}))},calcIframeIntervalTimestamp:function(e){null===Et?Et=e:Et<e&&(pt=e-Et,postMessage({cmd:G,value:pt}),Et=e)},canVisibilityDecodeNotDrop:function(){return ii._opt.visibility&&h*w<=2073600},isPlaybackCacheBeforeDecodeForFpsRender:function(){return ii.isPlayback&&ii._opt.playbackIsCacheBeforeDecodeForFpsRender},isPlaybackOnlyDecodeIFrame:function(){return ii._opt.playbackRate>=ii._opt.playbackForwardMaxRateDecodeIFrame},isPlayUseMSE:function(){return ii.isPlayer&&ii._opt.useMSE&&gr(r)},isPlayUseMSEAndDecoderInWorker:function(){return ii.isPlayUseMSE()&&ii._opt.mseDecoderUseWorker},isPlayUseMSEAndDecoderInWorkerAndMseDecodeAudio:function(){return ii.isPlayUseMSEAndDecoderInWorker()&&ii._opt.mseDecodeAudio},playbackUpdatePlaybackRate:function(){ii.clearBuffer(!0)},onOffscreenCanvasWebglContextLost:function(e){ii.debug.error("worker","handleOffscreenCanvasWebglContextLost and next try to create webgl"),e.preventDefault(),Ot=!0,ii.webglObj.destroy(),ii.webglObj=null,ii.offscreenCanvasGL=null,setTimeout((()=>{ii.offscreenCanvasGL=ii.offscreenCanvas.getContext("webgl"),ii.offscreenCanvasGL&&ii.offscreenCanvasGL.getContextAttributes().stencil?(ii.webglObj=f(ii.offscreenCanvasGL,ii._opt.openWebglAlignment),Ot=!1):ii.debug.error("worker","handleOffscreenCanvasWebglContextLost, stencil is false")}),500)},onOffscreenCanvasWebglContextRestored:function(e){ii.debug.log("worker","handleOffscreenCanvasWebglContextRestored"),e.preventDefault()},videoInfo:function(e,t,r){postMessage({cmd:L,code:e}),postMessage({cmd:T,w:t,h:r}),h=t,w=r,ii.useOffscreen()&&(ii.offscreenCanvas=new OffscreenCanvas(t,r),ii.offscreenCanvasGL=ii.offscreenCanvas.getContext("webgl"),ii.webglObj=f(ii.offscreenCanvasGL,ii._opt.openWebglAlignment),ii.offscreenCanvas.addEventListener("webglcontextlost",ii.onOffscreenCanvasWebglContextLost,!1),ii.offscreenCanvas.addEventListener("webglcontextrestored",ii.onOffscreenCanvasWebglContextRestored,!1))},audioInfo:function(e,t,r){postMessage({cmd:I,code:e}),postMessage({cmd:D,sampleRate:t,channels:r,depth:ir}),St=r},yuvData:function(t,r){if(ii.isDestroyed)return void ii.debug.log("worker","yuvData, decoder is destroyed and return");const i=h*w*3/2;let n=e.HEAPU8.subarray(t,t+i),s=new Uint8Array(n);if(ft=null,ii.useOffscreen())try{if(Ot)return;ii.webglObj.renderYUV(h,w,s);let e=ii.offscreenCanvas.transferToImageBitmap();postMessage({cmd:k,buffer:e,delay:ii.delay,ts:r},[e])}catch(e){ii.debug.error("worker","yuvData, transferToImageBitmap error is",e)}else postMessage({cmd:k,output:s,delay:ii.delay,ts:r},[s.buffer])},pcmData:function(e,r,i){if(ii.isDestroyed)return void ii.debug.log("worker","pcmData, decoder is destroyed and return");let s=r,a=[],o=0,d=ii._opt.audioBufferSize;for(let r=0;r<2;r++){let i=t.HEAPU32[(e>>2)+r]>>2;a[r]=t.HEAPF32.subarray(i,i+s)}if(_t){if(!(s>=(r=d-_t)))return _t+=s,n[0]=Float32Array.of(...n[0],...a[0]),void(2==St&&(n[1]=Float32Array.of(...n[1],...a[1])));ht[0]=Float32Array.of(...n[0],...a[0].subarray(0,r)),2==St&&(ht[1]=Float32Array.of(...n[1],...a[1].subarray(0,r))),postMessage({cmd:C,buffer:ht,ts:i},ht.map((e=>e.buffer))),o=r,s-=r}for(_t=s;_t>=d;_t-=d)ht[0]=a[0].slice(o,o+=d),2==St&&(ht[1]=a[1].slice(o-d,o)),postMessage({cmd:C,buffer:ht,ts:i},ht.map((e=>e.buffer)));_t&&(n[0]=a[0].slice(o),2==St&&(n[1]=a[1].slice(o))),a=[]},errorInfo:function(e){null===ft&&(ft=tr());const t=tr(),r=rr(pt>0?2*pt:5e3,1e3,5e3),i=t-ft;i>r&&(ii.debug.warn("worker",`errorInfo() emit simdDecodeError and\n                iframeIntervalTimestamp is ${pt} and diff is ${i} and maxDiff is ${r}\n                and replay`),Ht=!0,postMessage({cmd:W}))},sendWebsocketMessage:function(e){o?o.readyState===De?o.send(e):ii.debug.error("worker","socket is not open"):ii.debug.error("worker","socket is null")},timeEnd:function(){},postStreamToMain(e,t){postMessage({cmd:Y,type:t,buffer:e},[e.buffer])}};ii.debug=new Br(ii);let ni=null;t.AudioDecoder&&(ni=new t.AudioDecoder(ii));let si=null;e.VideoDecoder&&(si=new e.VideoDecoder(ii)),postMessage({cmd:U}),self.onmessage=function(e){let t=e.data;switch(t.cmd){case le:try{ii._opt=Object.assign(ii._opt,JSON.parse(t.opt))}catch(e){}ii.init();break;case ce:ii.pushBuffer(t.buffer,t.options);break;case ue:ii.decodeAudio(t.buffer,t.ts);break;case he:ii.decodeVideo(t.buffer,t.ts,t.isIFrame);break;case _e:ii.clearBuffer(t.needClear);break;case me:ii.fetchStream(t.url,JSON.parse(t.opt));break;case fe:ii.debug.log("worker","close",JSON.stringify(t.options)),t.options&&yr(t.options.isVideoInited)&&(wr=t.options.isVideoInited),ii.close();break;case pe:ii.debug.log("worker","updateConfig",t.key,t.value),ii._opt[t.key]=t.value,"playbackRate"===t.key&&(ii.playbackUpdatePlaybackRate(),ii.isPlaybackCacheBeforeDecodeForFpsRender()&&ii.playbackCacheLoop());break;case ge:ii.sendWebsocketMessage(t.message);break;case ye:ri.$video.currentTime=Number(t.message)}}}(e,e,!0)}))}));
