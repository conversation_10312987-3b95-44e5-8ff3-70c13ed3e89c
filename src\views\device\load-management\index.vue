<!--应用管理-->
<script>
export default {
  name: 'AirportTask'
};
</script>

<script setup>
import { onBeforeUnmount, reactive } from 'vue';
import DetailDialog from './DetailDialog.vue';
import EditDialog from './EditDialog.vue';
import optionData from '@/utils/option-data';
import { toRaw } from '@vue/reactivity';
import { loadManagementList, deleteLoadManage } from '@/api/devices';
import { authorityShow } from '@/utils/authority';

const editDialogRef = ref(null);

const loading = ref(false);
const total = ref(0);

const queryParams = reactive({
  payloadName: '',
  payloadType: '',
  pageNum: 1,
  pageSize: 10
});

const dataList = ref([]);
const dialog = reactive({
  visible: false
});
const editDialog = reactive({
  visible: false
});

let formData = reactive({});
let detailData = reactive({});

/**
 * 查询
 */
function handleQuery() {
  // Calculate the new total pages based on the new page size
  const newTotalPages = Math.ceil(total.value / queryParams.pageSize);
  
  // If current page is greater than new total pages, adjust to the last page
  if (queryParams.pageNum > newTotalPages) {
    queryParams.pageNum = newTotalPages || 1;
  }
  loadManagementList({
    payload_name: queryParams.payloadName,
    payload_type: queryParams.payloadType,
    page: queryParams.pageNum,
    page_size: queryParams.pageSize
  }).then(data => {
    const { list, pagination } = data;
    dataList.value = list || [];
    total.value = pagination.total;
  });
}

function handleSearch() {
  queryParams.pageNum = 1;
  handleQuery();
}
/**
 * 重置查询
 */
function resetQuery(type = '') {
  queryParams.payloadName = '';
  queryParams.payloadType = '';
  queryParams.pageNum = 1;
  queryParams.pageSize = 10;
  handleQuery();
}

function openEditDialog(row) {
  editDialog.visible = true;
  Object.keys(formData).map(key => {
    delete formData[key];
  });
  if (row) {
    editDialog.title = '编辑负载信息';
    Object.assign(formData, { ...row });
  } else {
    editDialog.title = '新增负载信息';
    nextTick(() => {
      editDialogRef.value.setDefaultValue();
    });
  }
}
/**
 * 删除
 */
function handleDelete(row) {
  ElMessageBox.confirm(`确认后将删除此任务及相关的历史执行记录，且无法进行恢复`, '确认删除所选负载？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    autofocus: false,
    closeOnClickModal: false,
    type: 'warning'
  }).then(() => {
    deleteLoadManage({
      id: toRaw(row).id
    }).then(data => {
      ElMessage.success('删除成功');
      handleQuery();
    });
  });
}

onMounted(() => {
  window.addEventListener('setItem', () => {
    handleQuery();
  });

  handleQuery();
});

onBeforeUnmount(() => {
  window.removeEventListener('setItem', () => {});
});
</script>

<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
          <el-form-item label="" prop="payloadType">
            <el-select
              class="input-serach"
              v-model="queryParams.payloadType"
              placeholder="请选择负载类型"
              clearable
              @change="handleSearch"
            >
              <el-option
                v-for="(item, index) in optionData.loadOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="payloadName">
            <el-input
              class="input-serach"
              v-model="queryParams.payloadName"
              placeholder="请输入负载名称"
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
            />
          </el-form-item>
        </el-form>
      </div>
      <div class="search-btn">
        <el-button type="primary" @click="handleSearch()"><i-ep-search />搜索</el-button>
        <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
      </div>
    </div>
    <el-card shadow="never">
      <template #header v-if="authorityShow('createLoad')">
        <el-button type="primary" @click="openEditDialog()"><i-ep-plus />新增</el-button>
      </template>
      <el-table highlight-current-row :data="dataList" v-loading="loading" stripe height="580">
        <el-table-column label="序号" align="center" width="80">
          <template #default="scope">
            <span>{{ scope.$index + 1 + queryParams.pageSize * (queryParams.pageNum - 1) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="组织名称" prop="dept_name" show-overflow-tooltip />
        <el-table-column label="负载名称" prop="payload_name" show-overflow-tooltip />
        <el-table-column label="负载ID" prop="payload_sn" show-overflow-tooltip />

        <el-table-column label="品牌" prop="brand" width="200" show-overflow-tooltip />
        <el-table-column label="型号" prop="model" width="200" show-overflow-tooltip />
        <el-table-column label="类型" show-overflow-tooltip prop="payload_type_name">
          <!-- <template #default="scope">
            <span>{{
              optionData.loadOptions.find(item => item.value === scope.row.payload_type)?.label || '喊话器'
            }}</span>
          </template> -->
        </el-table-column>

        <el-table-column fixed="right" label="操作" align="center" width="200" v-if="authorityShow('editLoad') || authorityShow('deleteLoad')">
          <template #default="scope">
            <el-button type="primary" link @click.stop="openEditDialog(scope.row)" v-if="authorityShow('editLoad')">编辑</el-button>
            <el-button type="danger" link @click.stop="handleDelete(scope.row)" v-if="authorityShow('deleteLoad')">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </el-card>

    <EditDialog
      ref="editDialogRef"
      v-model:visible="editDialog.visible"
      :title="editDialog.title"
      :form-data="formData"
      @submit="resetQuery"
    />

    <DetailDialog v-model:visible="dialog.visible" :form-data="detailData" />
  </div>
</template>
<style scoped lang="scss">
.input-serach {
  width: 200px;
}
.flex-center {
  display: flex;
  align-items: center;
  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
}
.search {
  display: flex;
  align-items: center;
  padding: 24px;
  height: 64px;
  .search-form {
    flex: 1;
    padding-top: 16px;
  }
}
</style>
