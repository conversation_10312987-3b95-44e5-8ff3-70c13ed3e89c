<template>
  <div class="login-container">
    <!-- <PageHead show-language custom-class="login-head" :showLogo="false" /> -->
    <div class="page-head">
		  <!-- <svg-icon icon-class="new-logo" style="margin-left: 25;width: 40px;height: 40px;margin-right: 10px" /> -->
      <el-image
        style="margin-left: 25px;width: 40px;height: 40px;margin-right: 10px;transform: translateY(8px);"
        :src="data.logo"
        :previewSrcList="[data.logo]"
        :zoom-rate="1.2"
        :max-scale="7"
        :min-scale="0.2"
        fit="cover"
        @error="handleImageError"
      />
      <span class="navbar-title">{{ data.name }}</span>
    </div>
    <div class="login-area">
      <!-- <el-tabs v-if="accountType === 0" class="custom-tabs" v-model="loginType">
        <el-tab-pane :label="$t('page.login.pwdLogin')" name="pwdLogin" />
        <el-tab-pane :label="$t('SMS login')" name="smsLogin" />
      </el-tabs> -->
      <PwdLogin v-show="loginType === 'pwdLogin'" ref="pwdLoginRef" @key="handleLogin" />
      <!-- <SmsLgoin v-show="loginType === 'smsLogin'" ref="smsLoginRef" /> -->
      <el-button
        size="default"
        :loading="loginRef.loading"
        type="primary"
        class="login-btn"
        @click.prevent="handleLogin"
      >
        {{ $t('login.login') }}
      </el-button>
      <DragVerify ref="popDragVerify" :loginType="loginType" />
      <!-- 账号密码提示 -->
      <!-- <div class="mt-4 text-white text-sm relative">
        <span class="w-[100px] inline-block"
          >{{ $t('login.account') }}: adminPC</span
        >
        <span class="ml-4"> {{ $t('login.password') }}: adminPC</span>
        <span class="absolute right-0 cursor-pointer" @click="handleRegister">
          {{ $t('register.register') }}
        </span>
      </div> -->
      <!-- <div class="mt-4 text-white text-sm relative">
        <span class="w-[100px] inline-block"
          >{{ $t('login.account') }}: tt123</span
        >
        <span class="ml-4"> {{ $t('login.password') }}: 123456qwe.</span>
      </div> -->
    </div>
  </div>
</template>

<script>
export default {
  name: 'Login'
};
</script>

<script setup>
import DragVerify from './components/dragVerify/index.vue';
import PwdLogin from './components/pwdLogin/index.vue';
import SmsLgoin from './components/smsLogin/index.vue';
import router from '@/router';
import { initPersonalSetting } from '@/api/wayline'
import { onMounted, reactive } from 'vue';
import { useAppStore } from '@/store/modules/app';
const defaultLogoIcon = new URL('/resource/images/default-logo.jpeg', import.meta.url).href;;
const accountType = ref(0);
const pwdLoginRef = ref('pwdLoginRef');
const smsLoginRef = ref('smsLoginRef');
const loginType = ref('pwdLogin');
const popDragVerify = ref('popDragVerify');
const data = reactive({})
const loginRef = computed(() => {
  if (loginType.value === 'pwdLogin') {
    return pwdLoginRef.value;
  } else {
    return smsLoginRef.value;
  }
});

onMounted(()=>{
  initData();
})

function handleLogin() {
  loginRef.value.loginFormRef.validate(valid => {
    if (valid) {
      loginRef.value.loading = true;
      try {
        popDragVerify.value.handleOpen();
      } catch (error) {
        loginRef.value.loading = false;
      }
    }
  });
}

function initData () {
  const appStore = useAppStore();
  initPersonalSetting({}).then(res=>{

    const { base_config = {} } = res;
    data.name = base_config.sys_name
    data.logo = base_config.sys_logo_url
    appStore.setDocumentTitle(base_config.sys_name);
  })
}

// 默认logo图片路径
function handleImageError() {
  data.logo = defaultLogoIcon; 
}
</script>

<style lang="scss" scoped>
:deep(.el-form-item--default) {
  border-radius: 25px;
  background: #344054;
  padding: 0 10px;
}
.login-container {
  position: relative;
  min-height: 100%;
  width: 100%;
  // background-color: #2d3a4b;
  overflow: hidden;
  background-image: url('@/assets/new-login-bg.png');
  .login-area {
    position: absolute;
    top: 317px;
    right: 120px;
    width: 480px;
    height: 346px;
    max-width: 100%;
    border-radius: 8px;
    background: #11253e;
    margin: 0 auto;
    text-align: center;
    overflow: hidden;
    .login-btn {
      position: absolute;
      bottom: 40px;
      left: 40px;
      width: 410px;
      height: 48px;
      border-radius: 25px;
    }
  }
  .page-head {
    color: #fff;
    height: 44px;
    font-family: SourceHanSansSC-Bold;
    font-size: 34px;
    color: #FFFFFF;
    line-height: 44px;
    vertical-align: center;
    font-weight: 700;
    margin-top: 26px;
  }
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__nav-wrap) {
  .el-tabs__item {
    color: #fff;
  }
  .is-active {
    color: var(--el-color-primary);
  }
}

:deep(.lang-select) {
  display: unset;
  width: unset;
}

:deep(.icon-class) {
  color: #fff;
}
</style>
