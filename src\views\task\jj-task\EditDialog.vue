<template>
  <el-dialog
    :title="title"
    v-if="visible"
    :model-value="visible"
    width="600px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form class="app-form" ref="dataFormRef" :model="form" :rules="rules" label-width="180px">
      <el-form-item label="开启自动出警" prop="default_trigger_enable">
        <el-switch v-model="form.default_trigger_enable" />
      </el-form-item>
      <!-- <el-form-item label="触发出警级别（级）" prop="name">
        <el-select v-model="form.task_type" placeholder="请选择">
          <el-option
            v-for="item in optionData.typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="默认航线高度（米）" prop="default_flight_height">
        <el-input-number
          v-model="form.default_flight_height"
          :min="100"
          :max="150"
          :step="1"
          controls-position="right"
          placeholder="请输入默认航线高度"
        />
      </el-form-item>

      <el-form-item label="默认飞行速度（米/秒）" prop="default_flight_speed">
        <el-input-number
          v-model="form.default_flight_speed"
          :min="3"
          :max="15"
          :step="1"
          controls-position="right"
          placeholder="请输入默认飞行速度"
        />
      </el-form-item>

      <el-form-item label="最大飞行距离（米）" prop="max_flight_distance">
        <el-input-number
          v-model="form.max_flight_distance"
          :min="10"
          :max="3000"
          :step="1"
          controls-position="right"
          placeholder="请输入最大飞行距离"
        />
      </el-form-item>

      <el-form-item label="到达目标动作" prop="default_reach_action">
        <el-select v-model="form.default_reach_action" placeholder="请选择" >
          <el-option
            v-for="item in optionData.targetOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="环绕飞行的半径（米）" prop="circle_radius" v-if="form.default_reach_action === TARGET_TYPE.radio">
        <el-input-number
          v-model="form.circle_radius"
          :min="50"
          :max="500"
          :step="10"
          controls-position="right"
          placeholder="请输入环绕飞行的半径"
        />
      </el-form-item>
      <el-form-item label="悬停时间（秒）" prop="default_stay_time" v-if="form.default_reach_action === TARGET_TYPE.hover">
        <el-input-number
          v-model="form.default_stay_time"
          :min="3"
          :max="120"
          :step="1"
          controls-position="right"
          placeholder="请输入悬停时间"
        />
      </el-form-item>

      <el-row :gutter="20" v-if="form.default_reach_action !== TARGET_TYPE.panorama">
        <el-col :span="9">
          <el-form-item label="到达目标拍照" prop="open_photo">
            <el-switch v-model="form.open_photo" />
          </el-form-item>
        </el-col>
        <el-col :span="15" v-if="form.open_photo">
          <el-form-item label="拍照间隔（秒）" prop="photo_interval">
            <el-input-number
              v-model="form.photo_interval"
              :min="1"
              :max="10"
              :step="1"
              controls-position="right"
              style="width: 150px !important"
              placeholder="请输入拍照间隔"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="到达目标录像" prop="open_video" v-if="form.default_reach_action !== TARGET_TYPE.panorama">
        <el-switch v-model="form.open_video" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-loading="loading">保 存</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import optionData from '@/utils/option-data';
import { getJJConfig, updateJJConfig } from '@/api/task';
import { TARGET_TYPE } from '@/utils/constants';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  }
});
const form = reactive({});
const dataFormRef = ref(ElForm);
const emit = defineEmits(['update:visible', 'submit']);
const rules = reactive({
  default_flight_height: [{ required: true, message: '请输入默认航线高度', trigger: 'blur' }],
  default_flight_speed: [{ required: true, message: '请输入默认飞行速度', trigger: 'blur' }],
  max_flight_distance: [{ required: true, message: '请输入最大飞行距离', trigger: 'blur' }],
  default_stay_time: [{ required: true, message: '请输入悬停时间', trigger: 'blur' }],
  photo_interval: [{ required: true, message: '请输入拍照间隔', trigger: 'blur' }],
  circle_radius: [{ required: true, message: '请输入环绕飞行的半径', trigger: 'blur' }],
  default_reach_action: [{ required: true, message: '请选择到达目标动作', trigger: 'blur' }]
});
const loading = ref(false);

onMounted(() => {
  getJJConfigAPI();
});
/**
 * 获取配置信息
 */
function getJJConfigAPI() {
  getJJConfig({}).then(data => {
    // if (data.default_reach_action === TARGET_TYPE.radio) {
    //   form.isRadius = true;
    // }
    Object.assign(form, data);
  });
}

// 切换目标动作
// function changeAction(value) {
//   if (value === TARGET_TYPE.radio) {
//     form.circle_radius = 50;
//     form.isRadius = true;
//   } else if (value === TARGET_TYPE.hover) {
//     form.circle_radius = null;
//     form.default_stay_time = 20;
//     form.isRadius = false;
//   } else {
//     form.isRadius = false;
//   }
// }

// 关闭弹窗
function closeDialog() {
  resetForm();
  emit('update:visible', false);
}
/**
 * 重置表单
 */
function resetForm() {
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  loading.value = false;
  Object.keys(form).map(key => {
    delete form[key];
  });
}
//保存
function handleSubmit() {
  dataFormRef.value.validate(isValid => {
    if (isValid) {
      if (form.open_photo && form.default_reach_action == 0 && form.photo_interval >= form.default_stay_time) {
        ElMessage.error('拍照间隔需小于悬停时长');
        return;
      }
      let params = { ...form };
      loading.value = true;
      updateJJConfig(params)
        .then(res => {
          loading.value = false;
          ElMessage.success('保存成功');
          closeDialog();
          emit('submit');
        })
        .catch(e => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}
</script>
<style scoped lang="scss">
.app-form {
  ::v-deep {
    .el-input-number,
    .el-select {
      width: 100%;
    }
    .el-input-number .el-input__inner {
      text-align: left;
    }
    .el-input-number.is-controls-right .el-input__wrapper {
      padding-left: 11px;
    }
    .el-date-editor.el-input,
    .el-date-editor.el-input__wrapper {
      width: 100%;
    }
  }
}
</style>
