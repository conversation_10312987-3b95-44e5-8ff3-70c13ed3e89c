// 执行提交 编辑 飞行区域操作

import { getUploadDataStruct } from './dataStruct';
import { COLORTYPE, COLOR_TYPE_STRING, FLIGHTAREATYPE, initDataList } from './flightAreaHandle';
import { addFlight<PERSON><PERSON>, updateFlighArea, getFllightAreas, deleteFlighArea } from '@/api/flightArea';
import { c3ArrToDegress, getPointsFromMap, toDegrees } from '@/components/Cesium/libs/cesium';
import { generateKey } from '@/utils';
import { ElMessage, ElMessageBox } from 'element-plus';
import { cancelPolygonEdit } from '@/components/Cesium/libs/cesium/superEdit/PolygonEditer';
import { cancelCircleEdit } from '@/components/Cesium/libs/cesium/superEdit/CircleEditer';

//#region 圆 操作
export const addCircleFlightArea = (data, callBack) => {
  let geoJsonData = getUploadDataStruct();
  geoJsonData.id = data.geomInfo.id || generateKey();
  geoJsonData.name = data.title || '';
  geoJsonData.type = data.flightAreaType ?? FLIGHTAREATYPE.DFENCE;
  geoJsonData.content.geometry.coordinates = [...data.geomInfo.center];
  geoJsonData.content.geometry.radius = data.geomInfo.radius;
  geoJsonData.content.geometry.type = 'Circle';

  if (geoJsonData.type === FLIGHTAREATYPE.DFENCE) {
    geoJsonData.content.properties.color = COLOR_TYPE_STRING[FLIGHTAREATYPE.DFENCE];
  } else if (geoJsonData.type === FLIGHTAREATYPE.NFZ) {
    geoJsonData.content.properties.color = COLOR_TYPE_STRING[FLIGHTAREATYPE.NFZ];
  }
  geoJsonData.content.properties.color = geoJsonData.content.properties.color || '#FF4500';

  geoJsonData.content.properties.clampToGround = true;
  console.log('geoJsonData', geoJsonData);
  addFlightArea(geoJsonData)
    .then(res => {
      ElMessage.success('提交成功！');
      initDataList();
      callBack && callBack(true);
    })
    .catch(err => {
      callBack && callBack(false);
    });
};

export const editCircleFlightArea = (data, callBack) => {
  let geoJsonData = getUploadDataStruct();
  geoJsonData.id = data.geomInfo.id ? data.geomInfo.id : geoJsonData.id;
  geoJsonData.name = data.title || '';
  geoJsonData.type = data.flightAreaType || FLIGHTAREATYPE.DFENCE;
  geoJsonData.content.geometry.coordinates = [...data.geomInfo.center];
  geoJsonData.content.geometry.radius = data.geomInfo.radius;
  geoJsonData.content.geometry.type = 'Circle';
  geoJsonData.content.properties.color = getColorString(geoJsonData);
  geoJsonData.content.properties.clampToGround = true;
  console.log('geoJsonData', geoJsonData);
  updateFlighArea(geoJsonData, geoJsonData.id)
    .then(res => {
      ElMessage.success('更新数据成功！');
      cancelCircleEdit();
      initDataList();
      callBack && callBack(true);
    })
    .catch(err => {
      callBack && callBack(false);
    });
};

export const fillCircleData = data => {
  let geoJsonData = getUploadDataStruct();
  geoJsonData.id = data.geomInfo.id || generateKey();
  geoJsonData.name = data.title || '';
  geoJsonData.type = data.flightAreaType || FLIGHTAREATYPE.DFENCE;
  geoJsonData.content.geometry.coordinates = [...data.geomInfo.center];
  geoJsonData.content.geometry.radius = data.geomInfo.radius;
  geoJsonData.content.geometry.type = 'Circle';
  geoJsonData.content.properties.color = getColorString(geoJsonData);
  geoJsonData.content.properties.clampToGround = true;
  return geoJsonData;
};

//#endregion

//#region 多边形 操作
export const fillPolygonData = data => {
  let geoJsonData = getUploadDataStruct();
  geoJsonData.id = data.geomInfo.id || generateKey();
  geoJsonData.name = data.title || '';
  geoJsonData.type = data.flightAreaType || FLIGHTAREATYPE.DFENCE;
  // 转换坐标
  let longlates = c3ArrToDegress(data.geomInfo.cartesianPoints) ?? [];
  console.log('longlates---fillPolygonData-', longlates);
  // 执行创建中点点图标
  if (longlates.length === 0) {
    throw new Error('获取的多边形坐标组为空！');
  }
  longlates.push(longlates[0]); // 闭合多边形
  geoJsonData.content.geometry.coordinates = [longlates];
  geoJsonData.content.geometry.radius = 0;
  geoJsonData.content.geometry.type = 'Polygon';
  geoJsonData.content.properties.color = getColorString(geoJsonData);
  geoJsonData.content.properties.clampToGround = true;
  return geoJsonData;
};

export const fillPolygonData2 = data => {
  let geoJsonData = getUploadDataStruct();
  geoJsonData.id = data.geomInfo.id;
  geoJsonData.name = data.title || '';
  geoJsonData.type = data.flightAreaType || FLIGHTAREATYPE.DFENCE;
  let longlates = [];
  (data.geomInfo.endEntityPoints || []).forEach(entity => {
    longlates.push(entity.position._value);
  });
  ///TODO... 坐标闭合

  // 转换坐标
  // longlates = c3ArrToDegress(data.geomInfo.cartesianPoints) ?? [];

  longlates = c3ArrToDegress(longlates) ?? [];
  console.log('longlates---fillPolygonData2-', longlates);
  // 执行创建中点点图标
  if (longlates.length === 0) {
    throw new Error('获取的多边形坐标组为空！');
  }
  longlates.push(longlates[0]); // 闭合多边形
  geoJsonData.content.geometry.coordinates = [longlates];
  geoJsonData.content.geometry.radius = 0;
  geoJsonData.content.geometry.type = 'Polygon';
  geoJsonData.content.properties.color = getColorString(geoJsonData);
  geoJsonData.content.properties.clampToGround = true;
  return geoJsonData;
};

export const editPolygonFlightArea = (data, callBack) => {
  let uploadJosn = fillPolygonData2(data);
  updateFlighArea(uploadJosn, uploadJosn.id)
    .then(res => {
      ElMessage.success('更新数据成功！');
      cancelPolygonEdit();
      initDataList();
      callBack && callBack(true);
    })
    .catch(err => {
      console.log('err', err, uploadJosn);
      callBack && callBack(false);
    });
};
export const addPolygonFlightArea = (data, callBack) => {
  let addJson = fillPolygonData(data);
  addFlightArea(addJson)
    .then(res => {
      ElMessage.success('提交成功！');
      initDataList();
      callBack && callBack(true);
    })
    .catch(err => {
      console.log('err', err);
      callBack && callBack(false);
    });
};
//#endregion

//#region 获取飞行区域列表

export const getFlightDataList = () => {
  return new Promise((resolve, reject) => {
    getFllightAreas()
      .then(res => {
        resolve(res); // 成功时解析结果
      })
      .catch(() => {
        reject(new Error('获取飞行区数据失败')); // 失败时拒绝并传递错误
      });
  });
};

// 获取颜色
const getColorString = option => {
  try {
    if (option.type === FLIGHTAREATYPE.DFENCE) {
      return COLOR_TYPE_STRING[FLIGHTAREATYPE.DFENCE];
    } else if (option.type === FLIGHTAREATYPE.NFZ) {
      return COLOR_TYPE_STRING[FLIGHTAREATYPE.NFZ];
    }
  } catch (error) {
    return COLOR_TYPE_STRING[FLIGHTAREATYPE.DFENCE];
  }
};

// const list =
// {
//     "area_id": "112b21ee-2056-4c02-beba-2a0c741237b0",
//     "name": "dfence-2024-08-30 16:19:14",
//     "type": "dfence",
//     "content": {
//         "properties": {
//             "color": "#2D8CF0",
//             "clampToGround": false
//         },
//         "geometry": {
//             "type": "Circle",
//             "coordinates": [
//                 113.93831222402362,
//                 22.58088424786795
//             ],
//             "radius": 55.94
//         }
//     },
//     "status": true,
//     "username": "adminPC",
//     "create_time": 1725005954654,
//     "update_time": 1725005954654
// }
//#endregion
