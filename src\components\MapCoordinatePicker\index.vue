<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick, watch, onActivated, onUnmounted } from 'vue';
import * as Cesium from 'cesium';
import {
  getOrCreateCesiumEngineInstance,
  delCesiumEngineInstance,
  CesiumLayerManager,
  imglayer,
  cialayer
} from '@/components/Cesium/libs/cesium/index';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  settingForm: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:modelValue', 'confirm']);

const mapHandler = ref(null);
const selectedCoordinates = ref(null);
let cesiumEngine = null;
const instanceId = ref(`coordinateSelector_${Date.now()}`); // 生成唯一实例ID

// 初始化Cesium地图
function initCesiumMap() {
  try {
    cesiumEngine = getOrCreateCesiumEngineInstance(instanceId.value);
    if (!cesiumEngine) {
      console.error('Failed to create Cesium engine instance');
      emit('update:modelValue', false);
      return;
    }

    cesiumEngine?.init('cesiumMapContainer');
    const layerManager = new CesiumLayerManager(cesiumEngine.viewer);
    layerManager.addLayer(imglayer);
    layerManager.addLayer(cialayer);

    // 如果已有经纬度，飞到该位置
    if (props.settingForm?.wanyline_longitude && props.settingForm?.wanyline_latitude) {
      const longitude = parseFloat(props.settingForm?.wanyline_longitude);
      const latitude = parseFloat(props.settingForm?.wanyline_latitude);
      selectedCoordinates.value = {
        longitude: longitude,
        latitude: latitude
      };
      if (!isNaN(longitude) && !isNaN(latitude)) {
        cesiumEngine.viewer.camera.flyTo({
          destination: Cesium.Cartesian3.fromDegrees(longitude, latitude, 1000)
        });
        cesiumEngine.viewer.entities.add({
          position: Cesium.Cartesian3.fromDegrees(longitude, latitude),
          point: {
            pixelSize: 15,
            color: Cesium.Color.RED,
            outlineColor: Cesium.Color.WHITE,
            outlineWidth: 2
          },
          label: {
            text: `经度: ${longitude}, 纬度: ${latitude}`,
            font: '16px sans-serif bold',
            fillColor: Cesium.Color.BLACK,
            outlineColor: Cesium.Color.WHITE,
            outlineWidth: 2,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            pixelOffset: new Cesium.Cartesian2(0, -20)
          }
        });
      }
    }

    setupMapClickEvent(cesiumEngine.viewer);
  } catch (error) {
    console.error('Cesium initialization error:', error);
    emit('update:modelValue', false);
  }
}

// 设置地图点击事件
function setupMapClickEvent(viewer) {
  if (mapHandler.value) {
    mapHandler.value.destroy();
    mapHandler.value = null;
  }

  mapHandler.value = new Cesium.ScreenSpaceEventHandler(viewer.canvas);
  mapHandler.value.setInputAction(function (event) {
    const ray = viewer.camera.getPickRay(event.position);
    const cartesian = viewer.scene.globe.pick(ray, viewer.scene);

    if (Cesium.defined(cartesian)) {
      const cartographic = viewer.scene.globe.ellipsoid.cartesianToCartographic(cartesian);
      const longitude = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6);
      const latitude = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6);

      selectedCoordinates.value = {
        longitude: longitude,
        latitude: latitude
      };

      viewer.entities.removeAll();
      viewer.entities.add({
        position: cartesian,
        point: {
          pixelSize: 15,
          color: Cesium.Color.RED,
          outlineColor: Cesium.Color.WHITE,
          outlineWidth: 2
        },
        label: {
          text: `经度: ${longitude}, 纬度: ${latitude}`,
          font: '16px sans-serif bold',
          fillColor: Cesium.Color.BLACK,
          outlineColor: Cesium.Color.WHITE,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new Cesium.Cartesian2(0, -20)
        }
      });
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
}

// 确认选择的坐标
function handleConfirm() {
  if (selectedCoordinates.value) {
    emit('confirm', selectedCoordinates.value);
    emit('update:modelValue', false);
  }
}

// 关闭弹窗
function handleClose() {
  delCesiumEngineInstance(instanceId.value);
  emit('update:modelValue', false);
}

onUnmounted(() => {
  delCesiumEngineInstance(instanceId.value);
});

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  newVal => {
    if (newVal) {
      nextTick(() => {
        initCesiumMap();
      });
    } else {
      delCesiumEngineInstance(instanceId.value);
    }
  }
);
</script>

<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="val => emit('update:modelValue', val)"
    title="选择坐标点"
    width="70%"
    destroy-on-close
    align-center
    :append-to-body="true"
    class="map-dialog"
    @close="handleClose"
  >
    <div class="map-container">
      <div id="cesiumMapContainer" class="cesium-container"></div>
      <div class="map-controls">
        <div class="coordinates-display">
          <div v-if="selectedCoordinates">
            <p>已选择坐标：</p>
            <p>经度：{{ selectedCoordinates.longitude }}</p>
            <p>纬度：{{ selectedCoordinates.latitude }}</p>
          </div>
          <p v-else>请在地图上点击选择坐标点</p>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :disabled="!selectedCoordinates">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.map-container {
  width: 100%;
  height: 500px;
  display: flex;
  flex-direction: column;
  position: relative;
}

.cesium-container {
  position: relative;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
  bottom: 0px;
  right: 0px;
  margin: 0;
  padding: 0;
}

.map-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.6);
  padding: 10px;
  border-radius: 4px;
  color: white;
  z-index: 100;
}

.coordinates-display {
  font-size: 14px;

  p {
    margin: 4px 0;
  }
}

:deep(.map-dialog .el-dialog__body) {
  padding: 10px;
}
</style>
