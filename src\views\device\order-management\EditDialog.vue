<template>
  <el-dialog
    :title="title"
    v-if="visible"
    :model-value="visible"
    width="600px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form class="app-form" ref="dataFormRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="保单号" prop="order_name">
        <el-input
          v-model="form.order_name"
          placeholder="请输入保单号"
          maxlength="100"
          clearable
          @blur="form.order_name = $event.target.value.trim()"
        />
      </el-form-item>
      <el-form-item label="保险公司" prop="insurance_company">
        <el-input
          v-model="form.insurance_company"
          placeholder="请输入保险公司"
          maxlength="50"
          clearable
          @blur="form.insurance_company = $event.target.value.trim()"
        />
      </el-form-item>
      <el-form-item label="保额" prop="insured_amount">
        <el-input-number style="width: 92%" v-model="form.insured_amount" :min="1" :max="1000" placeholder="请输入保额" controls-position="right"/>
        <span style="margin-left: 3px;">万元</span>
        <!-- <el-input
          v-model="form.insured_amount"
          placeholder="请输入保额"
          maxlength="64"
          clearable
          @blur="form.insured_amount = $event.target.value.trim()"
        >
          <template #append>万元</template>
        </el-input> -->
      </el-form-item>
      <el-form-item label="无人机序列号" prop="device_sn">
        <el-select v-model="form.device_sn" placeholder="请选择无人机序列号" @change="selectUav">
          <el-option
            v-for="item in uavList"
            :key="item.device_sn"
            :label="item.device_sn"
            :value="item.device_sn"
            clearable
          />
        </el-select>
      </el-form-item>
      <el-form-item label="无人机名称" prop="nick_name">
        <el-input disabled v-model="form.nick_name" placeholder="名称无需填写，自动带出" />
      </el-form-item>
      <el-form-item label="保险期限" prop="time_range">
        <el-date-picker
          class="input-serach"
          v-model="form.time_range"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          @change="selectTime"
        />
      </el-form-item>
      <el-form-item label="关联用户" required>
        <el-button type="primary" @click.stop="openDialog()">添加关联用户</el-button>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-loading="loading">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
  <div v-if="userVisible">
    <el-dialog v-model="userVisible" title="用户列表" width="1300" :before-close="closeUser" :close-on-click-modal="false">
      <user-transfer
        :data="relevanceData"
        :value="relevanceValue"
        filterable
        :filter-method="filterMethod"
        rowKey="id"
        :titles="['待选用户列表', '已选用户列表']"
        @input="inputValue"
      >
        <template v-slot:table>
          <el-table-column label="用户名称" prop="nickname" width="200" show-overflow-tooltip></el-table-column>
          <el-table-column label="所属组织" prop="dept_name" show-overflow-tooltip></el-table-column>
          <el-table-column label="手机号" prop="mobile" width="160" show-overflow-tooltip></el-table-column>
        </template>
      </user-transfer>
      <div class="fly-bottom">
        <el-button type="primary" style="margin-right: 10px" @click="saveRelevance">保存</el-button>
        <el-button @click="closeUser">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import userTransfer from './components/userTransfer.vue';
import { listUser } from '@/api/system/user';
import { addOrder, editOrder, getLinkUser } from '@/api/devices/order.js';
import { getUavList } from '@/api/devices';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  formData: {
    type: Object,
    default(rawProps) {
      return {};
    }
  }
});
const form = reactive({});
const dataFormRef = ref(ElForm);
const uavList = ref([]);
const userVisible = ref(false);
const relevanceData = ref([])
const relevanceValue = ref([]);
const isSave = ref(false); //是否进行过保存操作
const oldRelevanceValue = ref([]); //用户还原数据

watch(
  () => props.formData,
  (newVal, oldVal) => {
    Object.assign(form, newVal);
    console.log('点击编辑获取详情', form);
    initRelevance(newVal.policy_id)
    form.time_range = [form.insurance_begin_time, form.insurance_end_time];
  },
  { deep: true }
);
const emit = defineEmits(['update:visible', 'submit']);
//保额输入限制正数
const validateAmount = (rule, value, callback) => {
  console.log('输入值', value);
  if (!value) {
    callback(new Error('请输入保额'));
  } else {
    const regex = /^\d*\.?\d+$/;
    if (!regex.test(value) || value == 0) {
      callback(new Error('请输入正数'));
    } else {
      callback();
    }
  }
};
const rules = reactive({
  order_name: [{ required: true, message: '请输入保单号', trigger: 'blur' },{
    required: true,
    pattern: /^[a-zA-Z0-9]+$/,
    message: '保单号只能输入英文和数字',
    trigger: 'blur'
  }],
  insurance_company: [{ required: true, message: '请输入保险公司', trigger: 'blur' }],
  insured_amount: [{ required: true, trigger: 'blur', validator: validateAmount }],
  device_sn: [{ required: true, message: '请选择无人机序列号', trigger: ['change', 'blur'] }],
  time_range: [{ required: true, message: '请选择保险期限', trigger: ['change', 'blur'] }]
});

const loading = ref(false);

// 关闭弹窗
function closeDialog() {
  resetForm();
  emit('update:visible', false);
}

function initRelevance (id) {
  isSave.value = true
  getLinkUser({
    policy_id: id
  }).then(res=>{
    relevanceValue.value = res.map(item => {
      return item.user_id
    });
  })
}

function saveRelevance() {
  userVisible.value = false
  isSave.value = true
}

function closeUser () {
  userVisible.value = false;
  if(!isSave.value) {
    relevanceValue.value = [];
  }else {
    relevanceValue.value = oldRelevanceValue.value
  }
}

function filterMethod(value, row) {
  if(!value.keywords) {
    return row
  }else {
    return row.nickname.indexOf(value.keywords) > -1
  }
}

function inputValue(value) {
  if (Array.isArray(value)) {
    relevanceValue.value = value;
  }
}

function initUserList () {
  listUser({
    deptId: '',
    pageNo: 1,
    pageSize: 9999,
    username: '',
    status: ''
  })
  .then(data => {
    relevanceData.value = data.list || [];
  })
  .catch(error => {
    relevanceData.value = [];
  });
}

function openDialog() {
  userVisible.value = true;
  if(isSave.value) {
    oldRelevanceValue.value = relevanceValue.value
  }
  // alarmId.value = toRaw(row).alarm_id;
  // searchAirList(searchParams);
  // initAlreadyRelevance();
}

/**
 * 重置表单
 */
function resetForm() {
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  loading.value = false;
  Object.keys(form).map(key => {
    delete form[key];
  });
}

function handleSubmit() {
  dataFormRef.value.validate(isValid => {
    if (isValid) {
      if(relevanceValue.value.length <= 0) {
        ElMessage.error('请选择关联用户');
        return;
      }
      let params = { ...form ,user_id_list: relevanceValue.value};
      loading.value = true;
      if (props.title == '新增保单') {
        addOrder(params)
          .then(res => {
            loading.value = false;
            ElMessage.success('新增成功');
            relevanceValue.value = []
            oldRelevanceValue.value = []
            isSave.value = false
            closeDialog();
            emit('submit');
          })
          .catch(e => {
            loading.value = false;
          });
      } else {
        //编辑保单
        editOrder(params)
          .then(res => {
            loading.value = false;
            ElMessage.success('更新成功');
            closeDialog();
            relevanceValue.value = []
            oldRelevanceValue.value = []
            isSave.value = false
            emit('submit');
          })
          .catch(e => {
            loading.value = false;
          });
      }
    } else {
      loading.value = false;
    }
  });
}
//选择保险期限日期范围
function selectTime() {
  form.insurance_begin_time = form.time_range[0] + ' 00:00:00';
  form.insurance_end_time = form.time_range[1] + ' 23:59:59';
}
//选择无人机列表
function selectUav(value) {
  const index = uavList.value.findIndex(item => item.device_sn == value);
  form.nick_name = uavList.value[index].nickname;
}

//获取可选的无人机列表
function getUav() {
  getUavList()
    .then(res => {
      uavList.value = res;
    })
    .catch(e => {});
}
onMounted(() => {
  getUav();
  initUserList();
});

defineExpose({ resetForm });
</script>
<style scoped lang="scss">
.input-serach {
  width: 200px;
}
.fly-bottom {
  margin-top: 40px;
  text-align: center;
}
.app-form {
  .select-time {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 5px 0;
  }
  ::v-deep {
    .el-input-number,
    .el-select {
      width: 100%;
    }
    .el-input-number .el-input__inner {
      text-align: left;
    }
    .el-input-number.is-controls-right .el-input__wrapper {
      padding-left: 11px;
    }
    .el-date-editor.el-input,
    .el-date-editor.el-input__wrapper {
      width: 100%;
    }
  }
}
</style>
