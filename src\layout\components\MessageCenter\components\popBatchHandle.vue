<!-- 消息中心 -->
<template>
  <el-dialog
    v-model="dialogVisible"
    v-if="dialogVisible"
    width="950px"
    :append-to-body="true"
    :close-on-click-modal="false"
    :before-close="dialogCancel"
    :title="$t('page.dialog.batching')"
  >
    <el-form ref="formRef" inline class="form-content" :model="formData">
      <el-form-item>
        <el-select
          v-model="formData.warnType"
          :placeholder="$t('page.dialog.alarmType')"
          filterable
          clearable
        >
          <div v-for="item in warnTypeList" :key="item.dictItemId">
            <el-option :value="item.valueCode" :label="item.valueName" />
          </div>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getTablePage">
          {{ $t('page.search') }}
        </el-button>
        <el-button @click="reset">{{ $t('page.dialog.reset') }}</el-button>
      </el-form-item>
    </el-form>
    <div class="table-content">
      <el-table
        ref="multipleTable"
        v-loading="tableLoading"
        height="500"
        :data="tableData"
        tooltip-effect="dark"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column
          :label="$t('page.dialog.messageTime')"
          width="160"
          prop="noticeTime"
        />
        <el-table-column
          :label="$t('page.dialog.alarmType')"
          width="150"
          prop="warnTypeName"
        />
        <el-table-column :label="$t('page.dialog.messageDescription')">
          <template v-slot="scope">
            {{
              `${$t('page.dialog.deviceCode')}：${
                scope.row.deviceCode || '——'
              } ， ${$t('page.dialog.alarmThreshold')}：${
                scope.row.warnThreshold || '——'
              }， ${$t('page.dialog.triggerValue')}：${
                scope.row.triggerValue || '——'
              }，${$t('page.dialog.pleaseHandle')}`
            }}
          </template>
        </el-table-column>
      </el-table>
      <PopHandleMessage ref="popHandleMessageRef" @handleMessage="resetTable" />
    </div>
    <template #footer>
      <div>
        <el-button @click="dialogCancel">{{ $t('page.Cancel') }}</el-button>
        <el-button type="primary" @click="handleClick">
          {{ $t('page.dialog.batching') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import i18n from '@/lang';
import { ElMessage } from 'element-plus';
import PopHandleMessage from './popHandleMessage.vue';

import useDialog from '@/hooks/useDialog';

import { getWarnMessageList } from '@/api/messageCenter';
import { getDictItemList } from '@/api/product';

const initFormData = () => ({
  warnType: ''
});
const emit = defineEmits(['deleteMessage']);
const formRef = ref('formRef');
const { dialogVisible, dialogOpen, formData, dialogCancel } = useDialog(
  formRef,
  { initFormData }
);

const popHandleMessageRef = ref('popHandleMessageRef');
const tableLoading = ref(false);
const tableData = ref([]);

const chiceMessageList = ref([]);
const warnTypeList = ref([]); // 告警类型

const getWarnType = async () => {
  try {
    const res = await getDictItemList({
      dictCode: 'WarnType'
    });
    warnTypeList.value = res.data || [];
  } catch (e) {
    console.log('productBelongList error', e);
  }
};

const reset = () => {
  screen.warnType = '';
  getTablePage();
};

// 重置表格的同时删除上一侧分页加载的数据
const resetTable = ids => {
  emit('deleteMessage', ids);
  getTablePage();
};

const handleOpen = () => {
  getWarnType();
  getTablePage();
  dialogOpen();
};

const getTablePage = async () => {
  tableLoading.value = true;
  try {
    const res = await getWarnMessageList(screen);
    tableData.value = res.data.records;
  } catch (err) {
    console.log(err);
  } finally {
    tableLoading.value = false;
  }
};

const handleSelectionChange = list => {
  chiceMessageList.value = list;
};

const handleClick = () => {
  if (!chiceMessageList.value.length) {
    ElMessage({
      message: i18n.global.t('page.dialog.selectMessageNeedToBeProcessed'),
      customClass: 'custom-message-box',
      type: 'error'
    });
    return;
  }
  popHandleMessageRef.value.handleOpen(chiceMessageList.value);
};

defineExpose({
  handleOpen
});
</script>
<style lang="scss" scoped>
:deep() {
  .cell {
    word-break: break-word !important;
    overflow-wrap: break-word !important;
  }
}
</style>

<style></style>
