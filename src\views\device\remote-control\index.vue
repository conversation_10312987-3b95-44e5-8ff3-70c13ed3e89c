<!--应用管理-->
<script>
export default {
  name: 'RemoteControl'
};
</script>

<script setup>
import { reactive } from 'vue';
import optionData from '@/utils/option-data';

const loading = ref(false);
const ids = ref([]);
const total = ref(0);

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  queryType: 2,
  keyWord: ''
});

const dataList = ref([]);

/**
 * 查询
 */
function handleQuery() {
  dataList.value = [
    {
      no: '3000460001',
      name: 'DJI RC Pro',
      seriNo: '5YSZK9K00207LS',
      modelNo: 'DJI RC Pro',
      orgName: '厦门四信',
      lastOnlineTime: '2023-11-22 10:53:59'
    }
  ];
  total.value = 1;
}
function handleSearch() {
  queryParams.pageNum = 1;
  handleQuery();
}
/**
 * 重置查询
 */
function resetQuery(type = '') {
  handleQuery();
}

/**
 * 删除
 */
function handleDelete(row) {
  ElMessageBox.confirm(`确定后将解绑【${row.name}】，且无法进行恢复！`, '确定解绑所选遥控器？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    autofocus: false,
    closeOnClickModal: false,
    type: 'warning'
  }).then(() => {
    ElMessage.success('解绑成功');
  });
}

onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped>
.input-serach {
  width: 400px;
}
</style>
<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
          <el-form-item label="产品序列号：" prop="keyWord">
            <el-input
              class="input-serach"
              v-model="queryParams.seriNo"
              placeholder="请输入产品序列号"
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
            />
          </el-form-item>
          <!-- <el-form-item label="遥控器型号：" prop="keyWord">
            <el-select
              class="input-serach"
              v-model="queryParams.deviceModel"
              placeholder="请选择遥控器型号"
              clearable
              @change="handleSearch"
            >
              <el-option
                v-for="(item, index) in optionData"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item> -->
        </el-form>
      </div>
      <div class="search-btn">
        <el-button type="primary" @click="handleSearch()"><i-ep-search />搜索</el-button>
        <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
      </div>
    </div>
    <el-card shadow="never">
      <el-table highlight-current-row :data="dataList" v-loading="loading" stripe height="540">
        <el-table-column label="序号" align="center" width="80">
          <template #default="scope">
            <span>{{ scope.$index + 1 + queryParams.pageSize * (queryParams.pageNum - 1) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="遥控器编号" prop="no" show-overflow-tooltip />
        <el-table-column label="遥控器名称" prop="name" show-overflow-tooltip />
        <el-table-column label="产品序列号" prop="seriNo" show-overflow-tooltip />
        <el-table-column label="型号" prop="modelNo" show-overflow-tooltip> </el-table-column>

        <el-table-column label="所属组织" prop="orgName" show-overflow-tooltip />
        <el-table-column label="最后在线时间" prop="lastOnlineTime" width="200" show-overflow-tooltip />

        <el-table-column fixed="right" label="操作" align="center" width="200">
          <template #default="scope">
            <el-button type="danger" link @click.stop="handleDelete(scope.row)">解绑</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </el-card>
  </div>
</template>
<style lang="scss" scoped>
.input-serach {
  width: 200px;
}
.search {
  display: flex;
  align-items: center;
  padding: 24px;
  .search-form {
    flex: 1;
  }
}
</style>
