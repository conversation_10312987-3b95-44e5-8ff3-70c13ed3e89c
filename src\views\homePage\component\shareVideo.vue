	<template>
		<div v-if="visible">
			<div class="share-title flex">
				<span>直播分享</span>
				<el-icon :size="20" @click="close"><Close style="margin-top: 35px;cursor: pointer;"/></el-icon>
			</div>
			<div class="share-box">
				<div class="share-tip">开启直播分享后，用户可以通过分享链接查看该设备的直播画面。</div>
				<div class="link-box">
					<span>有效期</span>
					<el-select v-model="validityValue" placeholder="请选择链接有效期" style="width: 240px" @change="changeValidity">
						<el-option
							v-for="item in optionData.validityOption"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
				</div>
				<div style="margin-left: 83px;">
					<el-button type="primary" style="width: 240px;" @click="createLink">
						<template #icon>
							<svg-icon  icon-class="attachments" style="color: #fff;"/>
						</template>
						生成分享链接
					</el-button>
				</div>
				<div class="code-box">
					<span>分享链接</span>
					<div>
						<canvas id="QRCode"></canvas>
					</div>
				</div>
				<div class="link-text">{{ shareLink }}</div>
				<div style="margin-left: 83px;margin-top: 20px;">
					<el-button type="primary" style="width: 240px;" @click="copyToClipboard(shareLink)">
						<template #icon>
							<svg-icon  icon-class="attachments" style="color: #fff;"/>
						</template>
						复制分享链接
					</el-button>
				</div>
			</div>
		</div>
	</template>
  
  <script setup>
  import { defineProps, ref, onMounted, watch } from 'vue';
	import optionData from '@/utils/option-data';
	import { crateShareUrl } from '@/api/devices/maintenance'
  import { ElMessage } from 'element-plus';
  import QRCode from "qrcode";
	import { Close } from '@element-plus/icons-vue';
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
		shareData: {
			type: Object,
			default: {}
		}
  });
	const emit = defineEmits(['closeDialog']);
	const validityValue = ref(null);
	const shareLink = ref('')
  
  // 关闭弹窗 
  function close() {
		shareLink.value = ''
		validityValue.value = null
		emit('closeDialog')
  }

	function createLink () {
		console.log('shareData',props.shareData)
		if(!validityValue.value) {
			ElMessage.error('请先选择链接有效期')
			return;
		}
		if(!props.shareData[0].device_sn || !props.shareData[0].source) {
			ElMessage.error('无人机离线')
			return;
		}
		crateShareUrl({
			device_sn: props.shareData[0].device_sn,
			source: props.shareData[0].source,
			expire_day: validityValue.value
		}).then(res=>{
			let url = `${window.location.host}${res}`
			handleGetQRCode(url)
			shareLink.value = url
		})
	}

	function handleGetQRCode(url) {
		const element = document.getElementById("QRCode");
		console.log('url',url)
		QRCode.toCanvas(element, `${url}`);
	}


	async function copyToClipboard(text) {  
		if(!text) {
			ElMessage.error('请先生成分享链接')
			return;
		}
		const el = document.createElement('textarea');
		el.value = text;
		document.body.appendChild(el);
		el.select();
		document.execCommand('copy');
		document.body.removeChild(el);
		ElMessage.success('复制成功')
	}

	function changeValidity (value) {
		validityValue.value = value
	}
  
  </script>
	<style lang="scss">
	.taskEditDialog {
		.el-dialog__body{
			background-color: #001129 !important;
		}
	}
	</style>
  <style scoped lang="scss">
	#QRCode {
		width: 104px !important;
		height: 104px !important;
		overflow: hidden;
		margin: 8px;
	}
	.share-title {
		background: #11253E;
		border-radius: 4px 4px 0 0;
		width: 400px;
		height: 60px;
		line-height: 60px;
		font-family: SourceHanSansSC-Bold;
		font-size: 20px;
		color: #FFFFFF;
		text-align: left;
		font-weight: 700;
		padding: 0 20px;
	}
	.share-box {
		height: 475px;
		width: 400px;
		background: #001129;
		color: #fff;
		padding: 24px;
    z-index: 100;
		border-radius: 0 0 4px 4px;
		.share-tip {
			font-family: SourceHanSansSC-Regular;
			font-size: 12px;
			color: #98A2B3;
			text-align: left;
			font-weight: 400;
			padding-bottom: 24px;
		}
		.link-box {
			margin-bottom: 25px;
			span{
				margin-left: 15px;
				margin-right: 20px;
			}
		}
		.link-text {
			width: 240px;
			height: 88px;
			border: 1px solid #475467;
			border-radius: 2px;
			background: #11253e;
			margin-left: 83px;
			font-family: SourceHanSansSC-Regular;
			font-size: 12px;
			color: #37465b;
			text-align: left;
			line-height: 18px;
			font-weight: 400;
			padding: 5px;
			overflow: hidden;
			word-break: break-all
		}
		.code-box {
			margin-top: 25px;
			display: flex;
			height: 130px;
			div{
				width: 120px;
				height: 120px;
				display: inline-block;
				margin-left: 20px;
				border: 1px solid #475467;
				border-radius: 2px;
				background: #11253e;
			}
		}
	}
	.flex {
		display: flex;
		justify-content: space-between;
	}
  </style>
  