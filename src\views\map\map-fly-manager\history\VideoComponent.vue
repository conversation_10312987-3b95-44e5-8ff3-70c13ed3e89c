<template>
  <div class="video-container" @mouseenter="showButtons" @mouseleave="hideButtons">
    <video
      ref="videoRef"
      :id="videoId"
      preload="auto"
      @click="togglePlay"
      @loadedmetadata="onMetadataLoaded"
      :controls="false"
      style="width: 320px; height: 256px"
    >
      <source :src="src" type="video/mp4" />
    </video>
    <transition name="fade">
      <div v-if="isShowBtns" class="video-play-view">
        <!-- <img v-if="!playing" class="video-play-btn" src="@/assets/home/<USER>" @click="togglePlay()" />
        <img v-else class="video-play-btn" src="@/assets/home/<USER>" @click="togglePlay()" /> -->
        <img class="fullscreen-btn" src="@/assets/home/<USER>" @click="toggleFullscreen()" />
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, onMounted, defineExpose } from 'vue';

// 属性定义
const props = defineProps({
  src: {
    type: String,
    required: true
  },
  videoId: {
    type: String,
    required: true
  }
});

// 数据定义
const videoRef = ref(null);
const playing = ref(false);
const isShowBtns = ref(false);
const isFullscreen = ref(false);

// 播放/暂停
function togglePlay() {
  if (videoRef.value.paused) {
    videoRef.value.play().then(() => {
      playing.value = true;
    }).catch((error) => {
      console.error('Failed to play video:', error);
    });
  } else {
    videoRef.value.pause();
    playing.value = false;
  }
}

// 播放
function togglePlayOld() {
  if (videoRef.value.paused) {
    videoRef.value.play();
    playing.value = true;
  }
}
//暂停
function togglePause() {
  if (!videoRef.value.paused) {
    videoRef.value.pause();
    playing.value = false;
  }
}

// 动态修改当前播放时间
function seekTo(timeInSeconds) {
  if (videoRef.value) {
    videoRef.value.currentTime = timeInSeconds;
  }
}

// 动态修改当前播放倍速
function setPlaybackRate(rate) {
  if (videoRef.value) {
    videoRef.value.playbackRate = rate;
  }
}
// 全屏相关方法
function toggleFullscreen() {
  const videoElement = videoRef.value;
  if (!document.fullscreenElement) {
    // 进入全屏
    if (videoElement.requestFullscreen) {
      videoElement.requestFullscreen();
    } else if (videoElement.mozRequestFullScreen) {
      /* Firefox */
      videoElement.mozRequestFullScreen();
    } else if (videoElement.webkitRequestFullscreen) {
      /* Chrome, Safari and Opera */
      videoElement.webkitRequestFullscreen();
    } else if (videoElement.msRequestFullscreen) {
      /* IE/Edge */
      videoElement.msRequestFullscreen();
    }
    isFullscreen.value = true;
  } else {
    // 退出全屏
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if (document.mozCancelFullScreen) {
      /* Firefox */
      document.mozCancelFullScreen();
    } else if (document.webkitExitFullscreen) {
      /* Chrome, Safari and Opera */
      document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) {
      /* IE/Edge */
      document.msExitFullscreen();
    }
    isFullscreen.value = false;
  }
}

// 元数据加载完毕返回播放时长
function onMetadataLoaded() {
  let videoOption = {
    videoId: props.videoId,
    videoDuration: videoRef.value.duration
  };
  emit('metadata-loaded', videoOption);
}

function showButtons() {
  isShowBtns.value = true;
}

function hideButtons() {
  isShowBtns.value = false;
}

defineExpose({
  togglePlay,
  togglePause,
  seekTo,
  setPlaybackRate
});
const emit = defineEmits(['play-state-changed', 'metadata-loaded']);
// 生命周期钩子
onMounted(() => {
  document.addEventListener('fullscreenchange', () => {
    isFullscreen.value = !!document.fullscreenElement;
  });
});
</script>

<style scoped>
.video-container {
  position: relative;
  width: 320px;
  height: 256px;
  overflow: hidden;
}

.video-play-view {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-play-btn {
  cursor: pointer;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.video-play-view:hover {
  opacity: 1;
}

.fullscreen-btn {
  cursor: pointer;
  position: absolute;
  bottom: 0px;
  right: 5px;
  width: 32px;
  height: 32px;
}

/* 在全局样式文件中添加 */
video::-webkit-media-controls {
  display: none !important;
}

video::-webkit-media-controls-panel {
  display: none !important;
}

video::-webkit-media-controls-overlay-play-button {
  display: none !important;
}

video::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0); /* 透明背景 */
  z-index: 1000; /* 确保覆盖控件 */
  pointer-events: none; /* 不阻止鼠标事件 */
}
</style>
