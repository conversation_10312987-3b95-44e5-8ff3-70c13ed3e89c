import Store from 'store';
// import { hasValue } from './helper';

export const setTagView = data => {
  try {
    Store.set('tagView', data);
  } catch (error) {
    console.log(error);
  }
};

export const getTagView = () => {
  try {
    return Store.get('tagView');
  } catch (error) {
    console.log(error);
  }
};

export const setCountdownTimeDelay = delay => {
  try {
    Store.set('countdownTimeDelay', delay);
  } catch (error) {
    console.log(error);
  }
};

export const getCountdownTimeDelay = () => {
  try {
    return Store.get('countdownTimeDelay');
  } catch (error) {
    console.log(error);
  }
};

export const getLastCountdownEndTime = () => {
  try {
    return Store.get('lastCountdownEndTime');
  } catch (error) {
    console.log(error);
  }
};

export const setLastCountdownEndTime = time => {
  try {
    Store.set('lastCountdownEndTime', time);
  } catch (error) {
    console.log(error);
  }
};

export const removeLastCountdownEndTime = () => {
  try {
    Store.remove('lastCountdownEndTime');
  } catch (error) {
    console.log(error);
  }
};

export const removeCountdownTimeDelay = () => {
  try {
    Store.remove('countdownTimeDelay');
  } catch (error) {
    console.log(error);
  }
};

export const getTenantRememberPwd = () => {
  try {
    return Store.get('tenantRememberPwd');
  } catch (error) {
    console.log(error);
  }
};

export const setTenantRememberPwd = data => {
  try {
    Store.set('tenantRememberPwd', data);
  } catch (error) {
    console.log(error);
  }
};

export const removeTenantRememberPwd = () => {
  try {
    Store.remove('tenantRememberPwd');
  } catch (error) {
    console.log(error);
  }
};
