<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4380157" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon four-faith">&#xe6f9;</span>
                <div class="name">密码</div>
                <div class="code-name">&amp;#xe6f9;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe673;</span>
                <div class="name">租户</div>
                <div class="code-name">&amp;#xe673;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6fa;</span>
                <div class="name">文件</div>
                <div class="code-name">&amp;#xe6fa;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6fb;</span>
                <div class="name">应用</div>
                <div class="code-name">&amp;#xe6fb;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6f8;</span>
                <div class="name">等待</div>
                <div class="code-name">&amp;#xe6f8;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6f2;</span>
                <div class="name">无线</div>
                <div class="code-name">&amp;#xe6f2;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6f3;</span>
                <div class="name">信号</div>
                <div class="code-name">&amp;#xe6f3;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6f4;</span>
                <div class="name">蜂窝</div>
                <div class="code-name">&amp;#xe6f4;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6f5;</span>
                <div class="name">网口</div>
                <div class="code-name">&amp;#xe6f5;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6f6;</span>
                <div class="name">LAN口</div>
                <div class="code-name">&amp;#xe6f6;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6f7;</span>
                <div class="name">WAN口</div>
                <div class="code-name">&amp;#xe6f7;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6f1;</span>
                <div class="name">警示</div>
                <div class="code-name">&amp;#xe6f1;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6e3;</span>
                <div class="name">查看</div>
                <div class="code-name">&amp;#xe6e3;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6e4;</span>
                <div class="name">成功</div>
                <div class="code-name">&amp;#xe6e4;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6e5;</span>
                <div class="name">岗位管理</div>
                <div class="code-name">&amp;#xe6e5;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6e6;</span>
                <div class="name">铃声</div>
                <div class="code-name">&amp;#xe6e6;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6e7;</span>
                <div class="name">垃圾桶</div>
                <div class="code-name">&amp;#xe6e7;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6e8;</span>
                <div class="name">所有设备</div>
                <div class="code-name">&amp;#xe6e8;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6e9;</span>
                <div class="name">设置</div>
                <div class="code-name">&amp;#xe6e9;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6ea;</span>
                <div class="name">下载</div>
                <div class="code-name">&amp;#xe6ea;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6eb;</span>
                <div class="name">消息</div>
                <div class="code-name">&amp;#xe6eb;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6ec;</span>
                <div class="name">右刷新</div>
                <div class="code-name">&amp;#xe6ec;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe66f;</span>
                <div class="name">隐藏</div>
                <div class="code-name">&amp;#xe66f;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6ed;</span>
                <div class="name">删除文件夹</div>
                <div class="code-name">&amp;#xe6ed;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6ee;</span>
                <div class="name">列表</div>
                <div class="code-name">&amp;#xe6ee;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6f0;</span>
                <div class="name">手机端</div>
                <div class="code-name">&amp;#xe6f0;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6ef;</span>
                <div class="name">地图</div>
                <div class="code-name">&amp;#xe6ef;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6d4;</span>
                <div class="name">检测</div>
                <div class="code-name">&amp;#xe6d4;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6d9;</span>
                <div class="name">产品</div>
                <div class="code-name">&amp;#xe6d9;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6da;</span>
                <div class="name">添加文件夹</div>
                <div class="code-name">&amp;#xe6da;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6db;</span>
                <div class="name">网址</div>
                <div class="code-name">&amp;#xe6db;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6dc;</span>
                <div class="name">双勾</div>
                <div class="code-name">&amp;#xe6dc;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6dd;</span>
                <div class="name">关闭</div>
                <div class="code-name">&amp;#xe6dd;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6de;</span>
                <div class="name">更多</div>
                <div class="code-name">&amp;#xe6de;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6df;</span>
                <div class="name">调整</div>
                <div class="code-name">&amp;#xe6df;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6e0;</span>
                <div class="name">交付</div>
                <div class="code-name">&amp;#xe6e0;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6e1;</span>
                <div class="name">返回</div>
                <div class="code-name">&amp;#xe6e1;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6e2;</span>
                <div class="name">感叹</div>
                <div class="code-name">&amp;#xe6e2;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6c3;</span>
                <div class="name">首页</div>
                <div class="code-name">&amp;#xe6c3;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6c4;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xe6c4;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6c5;</span>
                <div class="name">账单</div>
                <div class="code-name">&amp;#xe6c5;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6c6;</span>
                <div class="name">审核</div>
                <div class="code-name">&amp;#xe6c6;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6c7;</span>
                <div class="name">拉伸</div>
                <div class="code-name">&amp;#xe6c7;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6c8;</span>
                <div class="name">折扣</div>
                <div class="code-name">&amp;#xe6c8;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6c9;</span>
                <div class="name">图片</div>
                <div class="code-name">&amp;#xe6c9;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6ca;</span>
                <div class="name">卡包</div>
                <div class="code-name">&amp;#xe6ca;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6cb;</span>
                <div class="name">添加图片</div>
                <div class="code-name">&amp;#xe6cb;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6cc;</span>
                <div class="name">箱子</div>
                <div class="code-name">&amp;#xe6cc;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6cd;</span>
                <div class="name">左推动</div>
                <div class="code-name">&amp;#xe6cd;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6ce;</span>
                <div class="name">购物车</div>
                <div class="code-name">&amp;#xe6ce;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6cf;</span>
                <div class="name">文件夹</div>
                <div class="code-name">&amp;#xe6cf;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6d0;</span>
                <div class="name">放大</div>
                <div class="code-name">&amp;#xe6d0;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6d1;</span>
                <div class="name">聊天</div>
                <div class="code-name">&amp;#xe6d1;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6d2;</span>
                <div class="name">纸飞机</div>
                <div class="code-name">&amp;#xe6d2;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6d3;</span>
                <div class="name">盾牌</div>
                <div class="code-name">&amp;#xe6d3;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6d5;</span>
                <div class="name">设备</div>
                <div class="code-name">&amp;#xe6d5;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6d6;</span>
                <div class="name">循环</div>
                <div class="code-name">&amp;#xe6d6;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6d7;</span>
                <div class="name">应用</div>
                <div class="code-name">&amp;#xe6d7;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6d8;</span>
                <div class="name">文件</div>
                <div class="code-name">&amp;#xe6d8;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6bc;</span>
                <div class="name">饼状图</div>
                <div class="code-name">&amp;#xe6bc;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6bd;</span>
                <div class="name">会员</div>
                <div class="code-name">&amp;#xe6bd;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6be;</span>
                <div class="name">拼图</div>
                <div class="code-name">&amp;#xe6be;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6bf;</span>
                <div class="name">苹果</div>
                <div class="code-name">&amp;#xe6bf;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6c0;</span>
                <div class="name">升级</div>
                <div class="code-name">&amp;#xe6c0;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6c1;</span>
                <div class="name">放大</div>
                <div class="code-name">&amp;#xe6c1;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6c2;</span>
                <div class="name">审核</div>
                <div class="code-name">&amp;#xe6c2;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6b9;</span>
                <div class="name">可视化大屏</div>
                <div class="code-name">&amp;#xe6b9;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6ba;</span>
                <div class="name">角色设置</div>
                <div class="code-name">&amp;#xe6ba;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6bb;</span>
                <div class="name">筛选</div>
                <div class="code-name">&amp;#xe6bb;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6ac;</span>
                <div class="name">指标库</div>
                <div class="code-name">&amp;#xe6ac;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6ad;</span>
                <div class="name">开发接口</div>
                <div class="code-name">&amp;#xe6ad;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6ae;</span>
                <div class="name">报表</div>
                <div class="code-name">&amp;#xe6ae;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6af;</span>
                <div class="name">行列</div>
                <div class="code-name">&amp;#xe6af;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6b0;</span>
                <div class="name">用户设置</div>
                <div class="code-name">&amp;#xe6b0;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6b1;</span>
                <div class="name">数据资源权限</div>
                <div class="code-name">&amp;#xe6b1;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6b2;</span>
                <div class="name">展开</div>
                <div class="code-name">&amp;#xe6b2;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6b3;</span>
                <div class="name">数据资源目录</div>
                <div class="code-name">&amp;#xe6b3;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6b4;</span>
                <div class="name">任务调度</div>
                <div class="code-name">&amp;#xe6b4;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6b5;</span>
                <div class="name">保存</div>
                <div class="code-name">&amp;#xe6b5;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6b6;</span>
                <div class="name">收起</div>
                <div class="code-name">&amp;#xe6b6;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6b7;</span>
                <div class="name">验证码</div>
                <div class="code-name">&amp;#xe6b7;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6b8;</span>
                <div class="name">数据质量监控</div>
                <div class="code-name">&amp;#xe6b8;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6a2;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xe6a2;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6a3;</span>
                <div class="name">数据权限控制</div>
                <div class="code-name">&amp;#xe6a3;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6a4;</span>
                <div class="name">上传中</div>
                <div class="code-name">&amp;#xe6a4;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6a5;</span>
                <div class="name">信息</div>
                <div class="code-name">&amp;#xe6a5;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6a6;</span>
                <div class="name">源数据</div>
                <div class="code-name">&amp;#xe6a6;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6a7;</span>
                <div class="name">添加</div>
                <div class="code-name">&amp;#xe6a7;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6a8;</span>
                <div class="name">下载</div>
                <div class="code-name">&amp;#xe6a8;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6a9;</span>
                <div class="name">查看</div>
                <div class="code-name">&amp;#xe6a9;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6aa;</span>
                <div class="name">图片</div>
                <div class="code-name">&amp;#xe6aa;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6ab;</span>
                <div class="name">时间</div>
                <div class="code-name">&amp;#xe6ab;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe697;</span>
                <div class="name">上线状态</div>
                <div class="code-name">&amp;#xe697;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe698;</span>
                <div class="name">省略</div>
                <div class="code-name">&amp;#xe698;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe699;</span>
                <div class="name">标签</div>
                <div class="code-name">&amp;#xe699;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe69a;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe69a;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe69b;</span>
                <div class="name">系统设置</div>
                <div class="code-name">&amp;#xe69b;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe69c;</span>
                <div class="name">文件夹</div>
                <div class="code-name">&amp;#xe69c;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe69d;</span>
                <div class="name">欢迎使用</div>
                <div class="code-name">&amp;#xe69d;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe69e;</span>
                <div class="name">打标签</div>
                <div class="code-name">&amp;#xe69e;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe69f;</span>
                <div class="name">移动</div>
                <div class="code-name">&amp;#xe69f;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6a0;</span>
                <div class="name">手指</div>
                <div class="code-name">&amp;#xe6a0;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe6a1;</span>
                <div class="name">打勾</div>
                <div class="code-name">&amp;#xe6a1;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe694;</span>
                <div class="name">减</div>
                <div class="code-name">&amp;#xe694;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe695;</span>
                <div class="name">等于</div>
                <div class="code-name">&amp;#xe695;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe696;</span>
                <div class="name">加</div>
                <div class="code-name">&amp;#xe696;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe692;</span>
                <div class="name">乘</div>
                <div class="code-name">&amp;#xe692;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe693;</span>
                <div class="name">除</div>
                <div class="code-name">&amp;#xe693;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe691;</span>
                <div class="name">元数据管理</div>
                <div class="code-name">&amp;#xe691;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe68f;</span>
                <div class="name">元数据管理</div>
                <div class="code-name">&amp;#xe68f;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe690;</span>
                <div class="name">任务调度</div>
                <div class="code-name">&amp;#xe690;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe68c;</span>
                <div class="name">任务实例</div>
                <div class="code-name">&amp;#xe68c;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe68d;</span>
                <div class="name">项目概览</div>
                <div class="code-name">&amp;#xe68d;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe68e;</span>
                <div class="name">概览</div>
                <div class="code-name">&amp;#xe68e;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe68a;</span>
                <div class="name">工作流定义</div>
                <div class="code-name">&amp;#xe68a;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe68b;</span>
                <div class="name">工作流关系</div>
                <div class="code-name">&amp;#xe68b;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe684;</span>
                <div class="name">标准</div>
                <div class="code-name">&amp;#xe684;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe685;</span>
                <div class="name">分组聚合</div>
                <div class="code-name">&amp;#xe685;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe686;</span>
                <div class="name">关联</div>
                <div class="code-name">&amp;#xe686;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe687;</span>
                <div class="name">目标数据</div>
                <div class="code-name">&amp;#xe687;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe676;</span>
                <div class="name">质量</div>
                <div class="code-name">&amp;#xe676;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe688;</span>
                <div class="name">标准</div>
                <div class="code-name">&amp;#xe688;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe689;</span>
                <div class="name">ETL</div>
                <div class="code-name">&amp;#xe689;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe678;</span>
                <div class="name">BI</div>
                <div class="code-name">&amp;#xe678;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe679;</span>
                <div class="name">SQL</div>
                <div class="code-name">&amp;#xe679;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe67a;</span>
                <div class="name">指标管理</div>
                <div class="code-name">&amp;#xe67a;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe677;</span>
                <div class="name">去重</div>
                <div class="code-name">&amp;#xe677;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe67b;</span>
                <div class="name">质量</div>
                <div class="code-name">&amp;#xe67b;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe67c;</span>
                <div class="name">选择字段</div>
                <div class="code-name">&amp;#xe67c;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe67d;</span>
                <div class="name">空值替换</div>
                <div class="code-name">&amp;#xe67d;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe67e;</span>
                <div class="name">多表关联</div>
                <div class="code-name">&amp;#xe67e;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe67f;</span>
                <div class="name">步骤</div>
                <div class="code-name">&amp;#xe67f;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe680;</span>
                <div class="name">数据治理</div>
                <div class="code-name">&amp;#xe680;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe681;</span>
                <div class="name">计算字段</div>
                <div class="code-name">&amp;#xe681;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe682;</span>
                <div class="name">数据筛选</div>
                <div class="code-name">&amp;#xe682;</div>
              </li>
          
            <li class="dib">
              <span class="icon four-faith">&#xe683;</span>
                <div class="name">数据接入</div>
                <div class="code-name">&amp;#xe683;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'four-faith';
  src: url('iconfont.woff2?t=1704447745789') format('woff2'),
       url('iconfont.woff?t=1704447745789') format('woff'),
       url('iconfont.ttf?t=1704447745789') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.four-faith {
  font-family: "four-faith" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="four-faith"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"four-faith" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon four-faith Symbol-cipher"></span>
            <div class="name">
              密码
            </div>
            <div class="code-name">.Symbol-cipher
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-tenantry"></span>
            <div class="name">
              租户
            </div>
            <div class="code-name">.Symbol-tenantry
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-document1"></span>
            <div class="name">
              文件
            </div>
            <div class="code-name">.Symbol-document1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Apply1"></span>
            <div class="name">
              应用
            </div>
            <div class="code-name">.Symbol-Apply1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-await"></span>
            <div class="name">
              等待
            </div>
            <div class="code-name">.Symbol-await
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-wireless"></span>
            <div class="name">
              无线
            </div>
            <div class="code-name">.Symbol-wireless
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-signal"></span>
            <div class="name">
              信号
            </div>
            <div class="code-name">.Symbol-signal
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-honeycomb"></span>
            <div class="name">
              蜂窝
            </div>
            <div class="code-name">.Symbol-honeycomb
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Net-interface"></span>
            <div class="name">
              网口
            </div>
            <div class="code-name">.Symbol-Net-interface
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-LAN"></span>
            <div class="name">
              LAN口
            </div>
            <div class="code-name">.Symbol-LAN
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-WAN"></span>
            <div class="name">
              WAN口
            </div>
            <div class="code-name">.Symbol-WAN
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-caution"></span>
            <div class="name">
              警示
            </div>
            <div class="code-name">.Symbol-caution
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-view"></span>
            <div class="name">
              查看
            </div>
            <div class="code-name">.Symbol-view
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Successful"></span>
            <div class="name">
              成功
            </div>
            <div class="code-name">.Symbol-Successful
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Position-management"></span>
            <div class="name">
              岗位管理
            </div>
            <div class="code-name">.Symbol-Position-management
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-bell"></span>
            <div class="name">
              铃声
            </div>
            <div class="code-name">.Symbol-bell
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-dustbin"></span>
            <div class="name">
              垃圾桶
            </div>
            <div class="code-name">.Symbol-dustbin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-All-equipment"></span>
            <div class="name">
              所有设备
            </div>
            <div class="code-name">.Symbol-All-equipment
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Settings"></span>
            <div class="name">
              设置
            </div>
            <div class="code-name">.Symbol-Settings
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Download"></span>
            <div class="name">
              下载
            </div>
            <div class="code-name">.Symbol-Download
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-bell2"></span>
            <div class="name">
              消息
            </div>
            <div class="code-name">.Symbol-bell2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Right-Refresh"></span>
            <div class="name">
              右刷新
            </div>
            <div class="code-name">.Symbol-Right-Refresh
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Hidden"></span>
            <div class="name">
              隐藏
            </div>
            <div class="code-name">.Symbol-Hidden
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Delete-folder"></span>
            <div class="name">
              删除文件夹
            </div>
            <div class="code-name">.Symbol-Delete-folder
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-list"></span>
            <div class="name">
              列表
            </div>
            <div class="code-name">.Symbol-list
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-phone"></span>
            <div class="name">
              手机端
            </div>
            <div class="code-name">.Symbol-phone
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-map"></span>
            <div class="name">
              地图
            </div>
            <div class="code-name">.Symbol-map
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Detection"></span>
            <div class="name">
              检测
            </div>
            <div class="code-name">.Symbol-Detection
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-product"></span>
            <div class="name">
              产品
            </div>
            <div class="code-name">.Symbol-product
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Add-folder"></span>
            <div class="name">
              添加文件夹
            </div>
            <div class="code-name">.Symbol-Add-folder
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-website"></span>
            <div class="name">
              网址
            </div>
            <div class="code-name">.Symbol-website
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Double-hook"></span>
            <div class="name">
              双勾
            </div>
            <div class="code-name">.Symbol-Double-hook
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Off"></span>
            <div class="name">
              关闭
            </div>
            <div class="code-name">.Symbol-Off
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-more"></span>
            <div class="name">
              更多
            </div>
            <div class="code-name">.Symbol-more
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-adjust"></span>
            <div class="name">
              调整
            </div>
            <div class="code-name">.Symbol-adjust
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-delivery"></span>
            <div class="name">
              交付
            </div>
            <div class="code-name">.Symbol-delivery
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Back"></span>
            <div class="name">
              返回
            </div>
            <div class="code-name">.Symbol-Back
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Exclaim-over"></span>
            <div class="name">
              感叹
            </div>
            <div class="code-name">.Symbol-Exclaim-over
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Home-page"></span>
            <div class="name">
              首页
            </div>
            <div class="code-name">.Symbol-Home-page
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-editor"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.Symbol-editor
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-check"></span>
            <div class="name">
              账单
            </div>
            <div class="code-name">.Symbol-check
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-examine"></span>
            <div class="name">
              审核
            </div>
            <div class="code-name">.Symbol-examine
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-stretch"></span>
            <div class="name">
              拉伸
            </div>
            <div class="code-name">.Symbol-stretch
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-discount"></span>
            <div class="name">
              折扣
            </div>
            <div class="code-name">.Symbol-discount
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Add-picture"></span>
            <div class="name">
              图片
            </div>
            <div class="code-name">.Symbol-Add-picture
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Card-case"></span>
            <div class="name">
              卡包
            </div>
            <div class="code-name">.Symbol-Card-case
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-tianjiatupian"></span>
            <div class="name">
              添加图片
            </div>
            <div class="code-name">.Symbol-tianjiatupian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-box"></span>
            <div class="name">
              箱子
            </div>
            <div class="code-name">.Symbol-box
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Left-push"></span>
            <div class="name">
              左推动
            </div>
            <div class="code-name">.Symbol-Left-push
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Shopping-cart"></span>
            <div class="name">
              购物车
            </div>
            <div class="code-name">.Symbol-Shopping-cart
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-file"></span>
            <div class="name">
              文件夹
            </div>
            <div class="code-name">.Symbol-file
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Blow-up"></span>
            <div class="name">
              放大
            </div>
            <div class="code-name">.Symbol-Blow-up
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-chat"></span>
            <div class="name">
              聊天
            </div>
            <div class="code-name">.Symbol-chat
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Paper-airplane"></span>
            <div class="name">
              纸飞机
            </div>
            <div class="code-name">.Symbol-Paper-airplane
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Shield"></span>
            <div class="name">
              盾牌
            </div>
            <div class="code-name">.Symbol-Shield
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-equipment"></span>
            <div class="name">
              设备
            </div>
            <div class="code-name">.Symbol-equipment
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Loop"></span>
            <div class="name">
              循环
            </div>
            <div class="code-name">.Symbol-Loop
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Apply"></span>
            <div class="name">
              应用
            </div>
            <div class="code-name">.Symbol-Apply
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-document"></span>
            <div class="name">
              文件
            </div>
            <div class="code-name">.Symbol-document
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Pie-chart1"></span>
            <div class="name">
              饼状图
            </div>
            <div class="code-name">.Symbol-Pie-chart1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-member1"></span>
            <div class="name">
              会员
            </div>
            <div class="code-name">.Symbol-member1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Saw-Puzzle1"></span>
            <div class="name">
              拼图
            </div>
            <div class="code-name">.Symbol-Saw-Puzzle1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Apple1"></span>
            <div class="name">
              苹果
            </div>
            <div class="code-name">.Symbol-Apple1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Upgrades1"></span>
            <div class="name">
              升级
            </div>
            <div class="code-name">.Symbol-Upgrades1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Blow-up1"></span>
            <div class="name">
              放大
            </div>
            <div class="code-name">.Symbol-Blow-up1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-examine1"></span>
            <div class="name">
              审核
            </div>
            <div class="code-name">.Symbol-examine1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Large-visual-screen"></span>
            <div class="name">
              可视化大屏
            </div>
            <div class="code-name">.Symbol-Large-visual-screen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Role-setting"></span>
            <div class="name">
              角色设置
            </div>
            <div class="code-name">.Symbol-Role-setting
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-screen"></span>
            <div class="name">
              筛选
            </div>
            <div class="code-name">.Symbol-screen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Index-library"></span>
            <div class="name">
              指标库
            </div>
            <div class="code-name">.Symbol-Index-library
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Development-interface"></span>
            <div class="name">
              开发接口
            </div>
            <div class="code-name">.Symbol-Development-interface
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-statement"></span>
            <div class="name">
              报表
            </div>
            <div class="code-name">.Symbol-statement
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-ranks"></span>
            <div class="name">
              行列
            </div>
            <div class="code-name">.Symbol-ranks
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-User-Settings"></span>
            <div class="name">
              用户设置
            </div>
            <div class="code-name">.Symbol-User-Settings
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Data-resource-permission"></span>
            <div class="name">
              数据资源权限
            </div>
            <div class="code-name">.Symbol-Data-resource-permission
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-unfold"></span>
            <div class="name">
              展开
            </div>
            <div class="code-name">.Symbol-unfold
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Data-resource-catalog"></span>
            <div class="name">
              数据资源目录
            </div>
            <div class="code-name">.Symbol-Data-resource-catalog
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Task-scheduling"></span>
            <div class="name">
              任务调度
            </div>
            <div class="code-name">.Symbol-Task-scheduling
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-save"></span>
            <div class="name">
              保存
            </div>
            <div class="code-name">.Symbol-save
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Pack-up"></span>
            <div class="name">
              收起
            </div>
            <div class="code-name">.Symbol-Pack-up
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Verification-code"></span>
            <div class="name">
              验证码
            </div>
            <div class="code-name">.Symbol-Verification-code
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Data-quality-monitoring"></span>
            <div class="name">
              数据质量监控
            </div>
            <div class="code-name">.Symbol-Data-quality-monitoring
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-editor1"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.Symbol-editor1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Data-permission-control1"></span>
            <div class="name">
              数据权限控制
            </div>
            <div class="code-name">.Symbol-Data-permission-control1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Up-cross1"></span>
            <div class="name">
              上传中
            </div>
            <div class="code-name">.Symbol-Up-cross1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Message1"></span>
            <div class="name">
              信息
            </div>
            <div class="code-name">.Symbol-Message1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Source-data1"></span>
            <div class="name">
              源数据
            </div>
            <div class="code-name">.Symbol-Source-data1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-append1"></span>
            <div class="name">
              添加
            </div>
            <div class="code-name">.Symbol-append1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Download1"></span>
            <div class="name">
              下载
            </div>
            <div class="code-name">.Symbol-Download1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-view1"></span>
            <div class="name">
              查看
            </div>
            <div class="code-name">.Symbol-view1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-picture1"></span>
            <div class="name">
              图片
            </div>
            <div class="code-name">.Symbol-picture1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-time1"></span>
            <div class="name">
              时间
            </div>
            <div class="code-name">.Symbol-time1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-On-line-state1"></span>
            <div class="name">
              上线状态
            </div>
            <div class="code-name">.Symbol-On-line-state1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-omit1"></span>
            <div class="name">
              省略
            </div>
            <div class="code-name">.Symbol-omit1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-tag1"></span>
            <div class="name">
              标签
            </div>
            <div class="code-name">.Symbol-tag1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-delete1"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.Symbol-delete1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-System-setting1"></span>
            <div class="name">
              系统设置
            </div>
            <div class="code-name">.Symbol-System-setting1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-file1"></span>
            <div class="name">
              文件夹
            </div>
            <div class="code-name">.Symbol-file1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-welcome1"></span>
            <div class="name">
              欢迎使用
            </div>
            <div class="code-name">.Symbol-welcome1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Markers1"></span>
            <div class="name">
              打标签
            </div>
            <div class="code-name">.Symbol-Markers1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Move1"></span>
            <div class="name">
              移动
            </div>
            <div class="code-name">.Symbol-Move1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-finger1"></span>
            <div class="name">
              手指
            </div>
            <div class="code-name">.Symbol-finger1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-tick1"></span>
            <div class="name">
              打勾
            </div>
            <div class="code-name">.Symbol-tick1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Reduction-of1"></span>
            <div class="name">
              减
            </div>
            <div class="code-name">.Symbol-Reduction-of1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Equal-to1"></span>
            <div class="name">
              等于
            </div>
            <div class="code-name">.Symbol-Equal-to1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-add1"></span>
            <div class="name">
              加
            </div>
            <div class="code-name">.Symbol-add1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-take1"></span>
            <div class="name">
              乘
            </div>
            <div class="code-name">.Symbol-take1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-In-addition-to1"></span>
            <div class="name">
              除
            </div>
            <div class="code-name">.Symbol-In-addition-to1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Data-management1"></span>
            <div class="name">
              元数据管理
            </div>
            <div class="code-name">.Symbol-Data-management1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Data-management"></span>
            <div class="name">
              元数据管理
            </div>
            <div class="code-name">.Symbol-Data-management
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-dispatch1"></span>
            <div class="name">
              任务调度
            </div>
            <div class="code-name">.Symbol-dispatch1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Living-example"></span>
            <div class="name">
              任务实例
            </div>
            <div class="code-name">.Symbol-Living-example
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Project-overview"></span>
            <div class="name">
              项目概览
            </div>
            <div class="code-name">.Symbol-Project-overview
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-overview"></span>
            <div class="name">
              概览
            </div>
            <div class="code-name">.Symbol-overview
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-definition"></span>
            <div class="name">
              工作流定义
            </div>
            <div class="code-name">.Symbol-definition
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-relation"></span>
            <div class="name">
              工作流关系
            </div>
            <div class="code-name">.Symbol-relation
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Standard1"></span>
            <div class="name">
              标准
            </div>
            <div class="code-name">.Symbol-Standard1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Packet-aggregation1"></span>
            <div class="name">
              分组聚合
            </div>
            <div class="code-name">.Symbol-Packet-aggregation1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-relevance1"></span>
            <div class="name">
              关联
            </div>
            <div class="code-name">.Symbol-relevance1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Target-data1"></span>
            <div class="name">
              目标数据
            </div>
            <div class="code-name">.Symbol-Target-data1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Quality"></span>
            <div class="name">
              质量
            </div>
            <div class="code-name">.Symbol-Quality
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Standard"></span>
            <div class="name">
              标准
            </div>
            <div class="code-name">.Symbol-Standard
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-ETL"></span>
            <div class="name">
              ETL
            </div>
            <div class="code-name">.Symbol-ETL
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-BI"></span>
            <div class="name">
              BI
            </div>
            <div class="code-name">.Symbol-BI
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-SQL"></span>
            <div class="name">
              SQL
            </div>
            <div class="code-name">.Symbol-SQL
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Index-management1"></span>
            <div class="name">
              指标管理
            </div>
            <div class="code-name">.Symbol-Index-management1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-duplicate-removal1"></span>
            <div class="name">
              去重
            </div>
            <div class="code-name">.Symbol-duplicate-removal1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Quality1"></span>
            <div class="name">
              质量
            </div>
            <div class="code-name">.Symbol-Quality1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Select-field1"></span>
            <div class="name">
              选择字段
            </div>
            <div class="code-name">.Symbol-Select-field1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Null-substitution1"></span>
            <div class="name">
              空值替换
            </div>
            <div class="code-name">.Symbol-Null-substitution1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Multiple-table-association1"></span>
            <div class="name">
              多表关联
            </div>
            <div class="code-name">.Symbol-Multiple-table-association1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-procedure1"></span>
            <div class="name">
              步骤
            </div>
            <div class="code-name">.Symbol-procedure1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Data-governance1"></span>
            <div class="name">
              数据治理
            </div>
            <div class="code-name">.Symbol-Data-governance1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Calculated-field1"></span>
            <div class="name">
              计算字段
            </div>
            <div class="code-name">.Symbol-Calculated-field1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Data-screening1"></span>
            <div class="name">
              数据筛选
            </div>
            <div class="code-name">.Symbol-Data-screening1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon four-faith Symbol-Data-access1"></span>
            <div class="name">
              数据接入
            </div>
            <div class="code-name">.Symbol-Data-access1
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="four-faith Symbol-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            four-faith" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-cipher"></use>
                </svg>
                <div class="name">密码</div>
                <div class="code-name">#Symbol-cipher</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-tenantry"></use>
                </svg>
                <div class="name">租户</div>
                <div class="code-name">#Symbol-tenantry</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-document1"></use>
                </svg>
                <div class="name">文件</div>
                <div class="code-name">#Symbol-document1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Apply1"></use>
                </svg>
                <div class="name">应用</div>
                <div class="code-name">#Symbol-Apply1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-await"></use>
                </svg>
                <div class="name">等待</div>
                <div class="code-name">#Symbol-await</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-wireless"></use>
                </svg>
                <div class="name">无线</div>
                <div class="code-name">#Symbol-wireless</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-signal"></use>
                </svg>
                <div class="name">信号</div>
                <div class="code-name">#Symbol-signal</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-honeycomb"></use>
                </svg>
                <div class="name">蜂窝</div>
                <div class="code-name">#Symbol-honeycomb</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Net-interface"></use>
                </svg>
                <div class="name">网口</div>
                <div class="code-name">#Symbol-Net-interface</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-LAN"></use>
                </svg>
                <div class="name">LAN口</div>
                <div class="code-name">#Symbol-LAN</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-WAN"></use>
                </svg>
                <div class="name">WAN口</div>
                <div class="code-name">#Symbol-WAN</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-caution"></use>
                </svg>
                <div class="name">警示</div>
                <div class="code-name">#Symbol-caution</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-view"></use>
                </svg>
                <div class="name">查看</div>
                <div class="code-name">#Symbol-view</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Successful"></use>
                </svg>
                <div class="name">成功</div>
                <div class="code-name">#Symbol-Successful</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Position-management"></use>
                </svg>
                <div class="name">岗位管理</div>
                <div class="code-name">#Symbol-Position-management</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-bell"></use>
                </svg>
                <div class="name">铃声</div>
                <div class="code-name">#Symbol-bell</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-dustbin"></use>
                </svg>
                <div class="name">垃圾桶</div>
                <div class="code-name">#Symbol-dustbin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-All-equipment"></use>
                </svg>
                <div class="name">所有设备</div>
                <div class="code-name">#Symbol-All-equipment</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Settings"></use>
                </svg>
                <div class="name">设置</div>
                <div class="code-name">#Symbol-Settings</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Download"></use>
                </svg>
                <div class="name">下载</div>
                <div class="code-name">#Symbol-Download</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-bell2"></use>
                </svg>
                <div class="name">消息</div>
                <div class="code-name">#Symbol-bell2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Right-Refresh"></use>
                </svg>
                <div class="name">右刷新</div>
                <div class="code-name">#Symbol-Right-Refresh</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Hidden"></use>
                </svg>
                <div class="name">隐藏</div>
                <div class="code-name">#Symbol-Hidden</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Delete-folder"></use>
                </svg>
                <div class="name">删除文件夹</div>
                <div class="code-name">#Symbol-Delete-folder</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-list"></use>
                </svg>
                <div class="name">列表</div>
                <div class="code-name">#Symbol-list</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-phone"></use>
                </svg>
                <div class="name">手机端</div>
                <div class="code-name">#Symbol-phone</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-map"></use>
                </svg>
                <div class="name">地图</div>
                <div class="code-name">#Symbol-map</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Detection"></use>
                </svg>
                <div class="name">检测</div>
                <div class="code-name">#Symbol-Detection</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-product"></use>
                </svg>
                <div class="name">产品</div>
                <div class="code-name">#Symbol-product</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Add-folder"></use>
                </svg>
                <div class="name">添加文件夹</div>
                <div class="code-name">#Symbol-Add-folder</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-website"></use>
                </svg>
                <div class="name">网址</div>
                <div class="code-name">#Symbol-website</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Double-hook"></use>
                </svg>
                <div class="name">双勾</div>
                <div class="code-name">#Symbol-Double-hook</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Off"></use>
                </svg>
                <div class="name">关闭</div>
                <div class="code-name">#Symbol-Off</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-more"></use>
                </svg>
                <div class="name">更多</div>
                <div class="code-name">#Symbol-more</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-adjust"></use>
                </svg>
                <div class="name">调整</div>
                <div class="code-name">#Symbol-adjust</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-delivery"></use>
                </svg>
                <div class="name">交付</div>
                <div class="code-name">#Symbol-delivery</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Back"></use>
                </svg>
                <div class="name">返回</div>
                <div class="code-name">#Symbol-Back</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Exclaim-over"></use>
                </svg>
                <div class="name">感叹</div>
                <div class="code-name">#Symbol-Exclaim-over</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Home-page"></use>
                </svg>
                <div class="name">首页</div>
                <div class="code-name">#Symbol-Home-page</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-editor"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#Symbol-editor</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-check"></use>
                </svg>
                <div class="name">账单</div>
                <div class="code-name">#Symbol-check</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-examine"></use>
                </svg>
                <div class="name">审核</div>
                <div class="code-name">#Symbol-examine</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-stretch"></use>
                </svg>
                <div class="name">拉伸</div>
                <div class="code-name">#Symbol-stretch</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-discount"></use>
                </svg>
                <div class="name">折扣</div>
                <div class="code-name">#Symbol-discount</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Add-picture"></use>
                </svg>
                <div class="name">图片</div>
                <div class="code-name">#Symbol-Add-picture</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Card-case"></use>
                </svg>
                <div class="name">卡包</div>
                <div class="code-name">#Symbol-Card-case</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-tianjiatupian"></use>
                </svg>
                <div class="name">添加图片</div>
                <div class="code-name">#Symbol-tianjiatupian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-box"></use>
                </svg>
                <div class="name">箱子</div>
                <div class="code-name">#Symbol-box</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Left-push"></use>
                </svg>
                <div class="name">左推动</div>
                <div class="code-name">#Symbol-Left-push</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Shopping-cart"></use>
                </svg>
                <div class="name">购物车</div>
                <div class="code-name">#Symbol-Shopping-cart</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-file"></use>
                </svg>
                <div class="name">文件夹</div>
                <div class="code-name">#Symbol-file</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Blow-up"></use>
                </svg>
                <div class="name">放大</div>
                <div class="code-name">#Symbol-Blow-up</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-chat"></use>
                </svg>
                <div class="name">聊天</div>
                <div class="code-name">#Symbol-chat</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Paper-airplane"></use>
                </svg>
                <div class="name">纸飞机</div>
                <div class="code-name">#Symbol-Paper-airplane</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Shield"></use>
                </svg>
                <div class="name">盾牌</div>
                <div class="code-name">#Symbol-Shield</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-equipment"></use>
                </svg>
                <div class="name">设备</div>
                <div class="code-name">#Symbol-equipment</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Loop"></use>
                </svg>
                <div class="name">循环</div>
                <div class="code-name">#Symbol-Loop</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Apply"></use>
                </svg>
                <div class="name">应用</div>
                <div class="code-name">#Symbol-Apply</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-document"></use>
                </svg>
                <div class="name">文件</div>
                <div class="code-name">#Symbol-document</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Pie-chart1"></use>
                </svg>
                <div class="name">饼状图</div>
                <div class="code-name">#Symbol-Pie-chart1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-member1"></use>
                </svg>
                <div class="name">会员</div>
                <div class="code-name">#Symbol-member1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Saw-Puzzle1"></use>
                </svg>
                <div class="name">拼图</div>
                <div class="code-name">#Symbol-Saw-Puzzle1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Apple1"></use>
                </svg>
                <div class="name">苹果</div>
                <div class="code-name">#Symbol-Apple1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Upgrades1"></use>
                </svg>
                <div class="name">升级</div>
                <div class="code-name">#Symbol-Upgrades1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Blow-up1"></use>
                </svg>
                <div class="name">放大</div>
                <div class="code-name">#Symbol-Blow-up1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-examine1"></use>
                </svg>
                <div class="name">审核</div>
                <div class="code-name">#Symbol-examine1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Large-visual-screen"></use>
                </svg>
                <div class="name">可视化大屏</div>
                <div class="code-name">#Symbol-Large-visual-screen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Role-setting"></use>
                </svg>
                <div class="name">角色设置</div>
                <div class="code-name">#Symbol-Role-setting</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-screen"></use>
                </svg>
                <div class="name">筛选</div>
                <div class="code-name">#Symbol-screen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Index-library"></use>
                </svg>
                <div class="name">指标库</div>
                <div class="code-name">#Symbol-Index-library</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Development-interface"></use>
                </svg>
                <div class="name">开发接口</div>
                <div class="code-name">#Symbol-Development-interface</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-statement"></use>
                </svg>
                <div class="name">报表</div>
                <div class="code-name">#Symbol-statement</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-ranks"></use>
                </svg>
                <div class="name">行列</div>
                <div class="code-name">#Symbol-ranks</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-User-Settings"></use>
                </svg>
                <div class="name">用户设置</div>
                <div class="code-name">#Symbol-User-Settings</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Data-resource-permission"></use>
                </svg>
                <div class="name">数据资源权限</div>
                <div class="code-name">#Symbol-Data-resource-permission</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-unfold"></use>
                </svg>
                <div class="name">展开</div>
                <div class="code-name">#Symbol-unfold</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Data-resource-catalog"></use>
                </svg>
                <div class="name">数据资源目录</div>
                <div class="code-name">#Symbol-Data-resource-catalog</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Task-scheduling"></use>
                </svg>
                <div class="name">任务调度</div>
                <div class="code-name">#Symbol-Task-scheduling</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-save"></use>
                </svg>
                <div class="name">保存</div>
                <div class="code-name">#Symbol-save</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Pack-up"></use>
                </svg>
                <div class="name">收起</div>
                <div class="code-name">#Symbol-Pack-up</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Verification-code"></use>
                </svg>
                <div class="name">验证码</div>
                <div class="code-name">#Symbol-Verification-code</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Data-quality-monitoring"></use>
                </svg>
                <div class="name">数据质量监控</div>
                <div class="code-name">#Symbol-Data-quality-monitoring</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-editor1"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#Symbol-editor1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Data-permission-control1"></use>
                </svg>
                <div class="name">数据权限控制</div>
                <div class="code-name">#Symbol-Data-permission-control1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Up-cross1"></use>
                </svg>
                <div class="name">上传中</div>
                <div class="code-name">#Symbol-Up-cross1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Message1"></use>
                </svg>
                <div class="name">信息</div>
                <div class="code-name">#Symbol-Message1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Source-data1"></use>
                </svg>
                <div class="name">源数据</div>
                <div class="code-name">#Symbol-Source-data1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-append1"></use>
                </svg>
                <div class="name">添加</div>
                <div class="code-name">#Symbol-append1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Download1"></use>
                </svg>
                <div class="name">下载</div>
                <div class="code-name">#Symbol-Download1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-view1"></use>
                </svg>
                <div class="name">查看</div>
                <div class="code-name">#Symbol-view1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-picture1"></use>
                </svg>
                <div class="name">图片</div>
                <div class="code-name">#Symbol-picture1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-time1"></use>
                </svg>
                <div class="name">时间</div>
                <div class="code-name">#Symbol-time1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-On-line-state1"></use>
                </svg>
                <div class="name">上线状态</div>
                <div class="code-name">#Symbol-On-line-state1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-omit1"></use>
                </svg>
                <div class="name">省略</div>
                <div class="code-name">#Symbol-omit1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-tag1"></use>
                </svg>
                <div class="name">标签</div>
                <div class="code-name">#Symbol-tag1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-delete1"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#Symbol-delete1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-System-setting1"></use>
                </svg>
                <div class="name">系统设置</div>
                <div class="code-name">#Symbol-System-setting1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-file1"></use>
                </svg>
                <div class="name">文件夹</div>
                <div class="code-name">#Symbol-file1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-welcome1"></use>
                </svg>
                <div class="name">欢迎使用</div>
                <div class="code-name">#Symbol-welcome1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Markers1"></use>
                </svg>
                <div class="name">打标签</div>
                <div class="code-name">#Symbol-Markers1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Move1"></use>
                </svg>
                <div class="name">移动</div>
                <div class="code-name">#Symbol-Move1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-finger1"></use>
                </svg>
                <div class="name">手指</div>
                <div class="code-name">#Symbol-finger1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-tick1"></use>
                </svg>
                <div class="name">打勾</div>
                <div class="code-name">#Symbol-tick1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Reduction-of1"></use>
                </svg>
                <div class="name">减</div>
                <div class="code-name">#Symbol-Reduction-of1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Equal-to1"></use>
                </svg>
                <div class="name">等于</div>
                <div class="code-name">#Symbol-Equal-to1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-add1"></use>
                </svg>
                <div class="name">加</div>
                <div class="code-name">#Symbol-add1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-take1"></use>
                </svg>
                <div class="name">乘</div>
                <div class="code-name">#Symbol-take1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-In-addition-to1"></use>
                </svg>
                <div class="name">除</div>
                <div class="code-name">#Symbol-In-addition-to1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Data-management1"></use>
                </svg>
                <div class="name">元数据管理</div>
                <div class="code-name">#Symbol-Data-management1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Data-management"></use>
                </svg>
                <div class="name">元数据管理</div>
                <div class="code-name">#Symbol-Data-management</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-dispatch1"></use>
                </svg>
                <div class="name">任务调度</div>
                <div class="code-name">#Symbol-dispatch1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Living-example"></use>
                </svg>
                <div class="name">任务实例</div>
                <div class="code-name">#Symbol-Living-example</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Project-overview"></use>
                </svg>
                <div class="name">项目概览</div>
                <div class="code-name">#Symbol-Project-overview</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-overview"></use>
                </svg>
                <div class="name">概览</div>
                <div class="code-name">#Symbol-overview</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-definition"></use>
                </svg>
                <div class="name">工作流定义</div>
                <div class="code-name">#Symbol-definition</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-relation"></use>
                </svg>
                <div class="name">工作流关系</div>
                <div class="code-name">#Symbol-relation</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Standard1"></use>
                </svg>
                <div class="name">标准</div>
                <div class="code-name">#Symbol-Standard1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Packet-aggregation1"></use>
                </svg>
                <div class="name">分组聚合</div>
                <div class="code-name">#Symbol-Packet-aggregation1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-relevance1"></use>
                </svg>
                <div class="name">关联</div>
                <div class="code-name">#Symbol-relevance1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Target-data1"></use>
                </svg>
                <div class="name">目标数据</div>
                <div class="code-name">#Symbol-Target-data1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Quality"></use>
                </svg>
                <div class="name">质量</div>
                <div class="code-name">#Symbol-Quality</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Standard"></use>
                </svg>
                <div class="name">标准</div>
                <div class="code-name">#Symbol-Standard</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-ETL"></use>
                </svg>
                <div class="name">ETL</div>
                <div class="code-name">#Symbol-ETL</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-BI"></use>
                </svg>
                <div class="name">BI</div>
                <div class="code-name">#Symbol-BI</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-SQL"></use>
                </svg>
                <div class="name">SQL</div>
                <div class="code-name">#Symbol-SQL</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Index-management1"></use>
                </svg>
                <div class="name">指标管理</div>
                <div class="code-name">#Symbol-Index-management1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-duplicate-removal1"></use>
                </svg>
                <div class="name">去重</div>
                <div class="code-name">#Symbol-duplicate-removal1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Quality1"></use>
                </svg>
                <div class="name">质量</div>
                <div class="code-name">#Symbol-Quality1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Select-field1"></use>
                </svg>
                <div class="name">选择字段</div>
                <div class="code-name">#Symbol-Select-field1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Null-substitution1"></use>
                </svg>
                <div class="name">空值替换</div>
                <div class="code-name">#Symbol-Null-substitution1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Multiple-table-association1"></use>
                </svg>
                <div class="name">多表关联</div>
                <div class="code-name">#Symbol-Multiple-table-association1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-procedure1"></use>
                </svg>
                <div class="name">步骤</div>
                <div class="code-name">#Symbol-procedure1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Data-governance1"></use>
                </svg>
                <div class="name">数据治理</div>
                <div class="code-name">#Symbol-Data-governance1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Calculated-field1"></use>
                </svg>
                <div class="name">计算字段</div>
                <div class="code-name">#Symbol-Calculated-field1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Data-screening1"></use>
                </svg>
                <div class="name">数据筛选</div>
                <div class="code-name">#Symbol-Data-screening1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#Symbol-Data-access1"></use>
                </svg>
                <div class="name">数据接入</div>
                <div class="code-name">#Symbol-Data-access1</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
