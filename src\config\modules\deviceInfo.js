import { DRONE_TYPE, DEVICE_SUB_TYPE, DEVICE_MODEL_KEY, CAMERA_TYPE_ENUM } from './deviceProps.js';
// 设备型号
export const DEVICE_TYPE_ENUM = {
  DJI_DOCK: 'DJI Dock',
  DJI_DOCK2: 'DJI Dock2',
  MATRICE_30: 'Matrice 30',
  MATRICE_30T: 'Matrice 30T',
  M3D: 'M3D',
  M3TD: 'M3TD'
};

export const DEVICE_NAME_ENUM = {
  [DEVICE_TYPE_ENUM.DJI_DOCK]: 'DJI Dock',
  [DEVICE_TYPE_ENUM.DJI_DOCK2]: 'DJI Dock2',
  [DEVICE_TYPE_ENUM.MATRICE_30]: '经纬 M30',
  [DEVICE_TYPE_ENUM.MATRICE_30T]: '经纬 M30T',
  [DEVICE_TYPE_ENUM.M3D]: 'Matrice 3D',
  [DEVICE_TYPE_ENUM.M3TD]: 'Matrice 3TD'
};

// 设备类型
export const DRONE_ENUM = {
  M30: 'M30', //'经纬 M30 系列',
  MAVIC3: 'MAVIC3', //'Mavic 3 行业系列',
  MATRICE3D: 'MATRICE3D' //Matrice 3D 系列'
};

export const DRONE_NAME_ENUM = {
  [DRONE_ENUM.MATRICE3D]: 'Matrice 3D 系列',
  [DRONE_ENUM.M30]: '经纬 M30 系列',
  [DRONE_ENUM.MAVIC3]: 'Mavic 3 行业系列'
};

// 无人机系列及配置项目
export const DRONE_CONFIG = [
  {
    droneEnumVal: DRONE_TYPE.M3D,
    label: DRONE_NAME_ENUM.MATRICE3D,
    droneSubEnumVal: [
      {
        value: DEVICE_SUB_TYPE.ONE,
        type: DEVICE_TYPE_ENUM.M3TD,
        label: DEVICE_NAME_ENUM.M3TD,
        uuid: DEVICE_MODEL_KEY.M3TD
      },
      {
        value: DEVICE_SUB_TYPE.ZERO,
        type: DEVICE_TYPE_ENUM.M3D,
        label: DEVICE_NAME_ENUM.M3D,
        uuid: DEVICE_MODEL_KEY.M3D
      }
    ]
  },
  {
    droneEnumVal: DRONE_TYPE.M30,
    label: DRONE_NAME_ENUM.M30,
    droneSubEnumVal: [
      {
        value: DEVICE_SUB_TYPE.ZERO,
        type: DEVICE_TYPE_ENUM.MATRICE_30,
        label: DEVICE_NAME_ENUM[DEVICE_TYPE_ENUM.MATRICE_30],
        uuid: DEVICE_MODEL_KEY.M30
      },
      {
        value: DEVICE_SUB_TYPE.ONE,
        type: DEVICE_TYPE_ENUM.MATRICE_30T,
        label: DEVICE_NAME_ENUM[DEVICE_TYPE_ENUM.MATRICE_30T],
        uuid: DEVICE_MODEL_KEY.M30T
      }
    ]
  }
];

// 内置的默认的当前的设备配置信息
// export const CURRENT_DEVICE_CONFIG = {
//   deviceModelKey: DEVICE_MODEL_KEY.M3TD,
//   droneEnumVal: DRONE_ENUM.MATRICE3D,
//   droneLabel: DRONE_NAME_ENUM[DRONE_ENUM.MATRICE3D],
//   droneSubEnumVal: DEVICE_TYPE_ENUM.M3TD,
//   deviceLabel: DEVICE_NAME_ENUM[DEVICE_TYPE_ENUM.M3TD],
//   camera: CAMERA_TYPE_ENUM.visable, // 相机类型
//   cameraLabel: '广角相机', // 相机名称
//   format: 1 / 1.32 // 单元大小
// };
