import request from '@/utils/request';

/**
 * 获取围栏列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回响应结果
 */
export function getFenceList(params) {
  return request({
    url: '/fence/list',
    method: 'get',
    params
  });
}

/**
 * 获取围栏详情
 * @param {Number} id - 围栏ID
 * @returns {Promise} 返回响应结果
 */
export function getFence(id) {
  return request({
    url: '/fence/get',
    method: 'get',
    params: { id }
  });
}

/**
 * 创建围栏
 * @param {Object} data - 围栏数据
 * @returns {Promise} 返回响应结果
 */
export function createFence(data) {
  return request({
    url: '/fence/create',
    method: 'post',
    data
  });
}

/**
 * 更新围栏
 * @param {Object} data - 围栏数据
 * @returns {Promise} 返回响应结果
 */
export function updateFence(data) {
  return request({
    url: '/fence/update',
    method: 'put',
    data
  });
}

/**
 * 删除围栏
 * @param {Number} id - 围栏ID
 * @returns {Promise} 返回响应结果
 */
export function deleteFence(id) {
  return request({
    url: '/fence/delete',
    method: 'delete',
    params: { id }
  });
}

/**
 * 更新围栏状态
 * @param {Number} id - 围栏ID
 * @param {String} status - 状态(active/inactive)
 * @returns {Promise} 返回响应结果
 */
export function updateFenceStatus(id, status) {
  return request({
    url: '/fence/update-status',
    method: 'put',
    params: { id, status }
  });
} 