import { defineStore } from 'pinia';

import {
  exclusiveLogin as _exclusiveLogin,
  loginApi,
  loginSms,
  logoutApi,
  subLogin
} from '@/api/auth';
import { getUserInfo } from '@/api/user';
import { resetRouter } from '@/router';
import { store } from '@/store';

import { useStorage } from '@vueuse/core';

import userEmpty from '@/assets/user_empty.png';

export const useUserStore = defineStore('user', () => {
  // state
  const token = useStorage('accessToken', '');
  const mqttAddress = useStorage('mqttAddress', '');
  const nickname = ref('');
  const avatar = ref('');
  const userData = ref({});
  const roles = ref([]); // 用户角色权限编码集合 → 判断路由权限
  const permsRoutes = ref([]); // 用户权限路由集合 → 判断按钮权限
  const userAllRoutes = ref([]); // 用户所有可见的路由

  function pilotLogin (loginData) {
    return new Promise((resolve, reject) => {
      loginApi(loginData)
        .then(data => {
          const { access_token, mqtt_addr, menu_list } = data;
          localStorage.setItem('menu', JSON.stringify(menu_list));
          token.value = access_token;
          mqttAddress.value = mqtt_addr;
          resolve();
        })
        .catch(error => {
          reject(error);
        });
    });
  }

  /**
   * 登录调用
   *
   * @param {LoginData}
   * @returns
   */
  function login(loginData) {
    return new Promise((resolve, reject) => {
      loginApi(loginData)
        .then(data => {
          const { access_token, mqtt_addr, menu_list } = data;
          localStorage.setItem('menu', JSON.stringify(menu_list));
          token.value = access_token;
          mqttAddress.value = mqtt_addr;
          resolve();
        })
        .catch(error => {
          reject(error);
        });
    });
  }

  function subAccountLogin(loginData) {
    return new Promise((resolve, reject) => {
      subLogin(loginData)
        .then(response => {
          const { tokenValue } = response.data;
          token.value = tokenValue;
          resolve();
        })
        .catch(error => {
          reject(error);
        });
    });
  }

  function exclusiveLogin(loginData) {
    return new Promise((resolve, reject) => {
      _exclusiveLogin(loginData)
        .then(response => {
          const { tokenValue } = response.data;
          token.value = tokenValue;
          resolve();
        })
        .catch(error => {
          reject(error);
        });
    });
  }

  function loginBySms(loginData) {
    return new Promise((resolve, reject) => {
      loginSms(loginData)
        .then(response => {
          const { tokenValue } = response.data;
          token.value = tokenValue;
          resolve();
        })
        .catch(error => {
          reject(error);
        });
    });
  }

  // 获取信息(用户昵称、头像、角色集合、权限集合)
  function getInfo() {
    return new Promise((resolve, reject) => {
      getUserInfo()
        .then((data) => {
          userData.value = data;
          resolve(data);
        })
        .catch(error => {
          reject(error);
        });
    });
  }

  // 注销
  function logout() {
    return new Promise((resolve, reject) => {
      resetRouter();
      resetToken();
     // 清除本地存储的菜单等信息
      localStorage.removeItem('menu');
      resolve();
    });
  }

  function updateHabits(habitsData) {
    const userHabits = userData.value.userHabits;

    const index = userHabits.findIndex(
      item =>
        item.viewCode === habitsData.viewCode &&
        item.viewIdentifyId === habitsData.viewIdentifyId
    );
    if (index >= 0) {
      userHabits.splice(index, 1, habitsData);
    } else {
      userHabits.push(habitsData);
    }
  }

  // 重置
  function resetToken() {
    token.value = '';
    mqttAddress.value = '';
    nickname.value = '';
    avatar.value = '';
    roles.value = [];
    permsRoutes.value = [];
    userData.value = {};
    userAllRoutes.value = [];
  }

  return {
    token,
    nickname,
    avatar,
    roles,
    userData,
    permsRoutes,
    userAllRoutes,
    login,
    pilotLogin,
    loginBySms,
    getInfo,
    logout,
    resetToken,
    updateHabits,
    subAccountLogin,
    exclusiveLogin
  };
});

// 非setup
export function useUserStoreHook() {
  return useUserStore(store);
}
