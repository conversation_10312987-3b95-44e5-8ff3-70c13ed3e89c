!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("path"),require("fs"),require("crypto")):"function"==typeof define&&define.amd?define(["path","fs","crypto"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).path,e.fs,e.crypto$1)}(this,function(t,r,n){"use strict";function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var St=o(t),Dt=o(r),At=o(n);function s(e,t){return e(t={exports:{}},t.exports),t.exports}var i=s(function(j){var y;(y=void 0!==(y=void 0!==y?y:{})?y:{}).locateFile=function(e){return"decoder-pro-audio.wasm"==e&&"undefined"!=typeof JESSIBUCA_PRO_AUDIO_WASM_URL&&""!=JESSIBUCA_PRO_AUDIO_WASM_URL?JESSIBUCA_PRO_AUDIO_WASM_URL:e};var t,m,B,U,W,I,s,L=Object.assign({},y),z="./this.program",N="object"==typeof window,c="function"==typeof importScripts,H="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,e="",q=(H?(e=c?St.default.dirname(e)+"/":__dirname+"/",I=()=>{W||(U=Dt.default,W=St.default)},t=function(e,t){return I(),e=W.normalize(e),U.readFileSync(e,t?void 0:"utf8")},B=e=>{e=t(e,!0);return e=e.buffer?e:new Uint8Array(e)},m=(e,r,n)=>{I(),e=W.normalize(e),U.readFile(e,function(e,t){e?n(e):r(t.buffer)})},1<process.argv.length&&(z=process.argv[1].replace(/\\/g,"/")),process.argv.slice(2),j.exports=y,process.on("uncaughtException",function(e){throw e}),process.on("unhandledRejection",function(e){throw e}),y.inspect=function(){return"[Emscripten Module object]"}):(N||c)&&(c?e=self.location.href:"undefined"!=typeof document&&document.currentScript&&(e=document.currentScript.src),e=0!==e.indexOf("blob:")?e.substr(0,e.replace(/[?#].*/,"").lastIndexOf("/")+1):"",t=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},c&&(B=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),m=(e,t,r)=>{var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=()=>{200==n.status||0==n.status&&n.response?t(n.response):r()},n.onerror=r,n.send(null)}),y.print||console.log.bind(console)),a=y.printErr||console.warn.bind(console),V=(Object.assign(y,L),y.arguments&&y.arguments,y.thisProgram&&(z=y.thisProgram),y.quit&&y.quit,y.wasmBinary&&(s=y.wasmBinary),y.noExitRuntime,"object"!=typeof WebAssembly&&v("no native wasm support detected"),!1);function G(e,t){e||v(t)}var X,d,f,u,J,l,h,K,Z,Q,Y="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function i(e,t,r){for(var n=t+r,o=t;e[o]&&!(n<=o);)++o;if(16<o-t&&e.buffer&&Y)return Y.decode(e.subarray(t,o));for(var s="";t<o;){var i,a,u=e[t++];128&u?(i=63&e[t++],192!=(224&u)?(a=63&e[t++],(u=224==(240&u)?(15&u)<<12|i<<6|a:(7&u)<<18|i<<12|a<<6|63&e[t++])<65536?s+=String.fromCharCode(u):(a=u-65536,s+=String.fromCharCode(55296|a>>10,56320|1023&a))):s+=String.fromCharCode((31&u)<<6|i)):s+=String.fromCharCode(u)}return s}function ee(e,t){return e?i(f,e,t):""}function te(e,t,r,n){if(!(0<n))return 0;for(var o=r,s=r+n-1,i=0;i<e.length;++i){var a=e.charCodeAt(i);if((a=55296<=a&&a<=57343?65536+((1023&a)<<10)|1023&e.charCodeAt(++i):a)<=127){if(s<=r)break;t[r++]=a}else{if(a<=2047){if(s<=r+1)break;t[r++]=192|a>>6}else{if(a<=65535){if(s<=r+2)break;t[r++]=224|a>>12}else{if(s<=r+3)break;t[r++]=240|a>>18,t[r++]=128|a>>12&63}t[r++]=128|a>>6&63}t[r++]=128|63&a}}return t[r]=0,r-o}function re(e){for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);n<=127?t++:n<=2047?t+=2:55296<=n&&n<=57343?(t+=4,++r):t+=3}return t}y.INITIAL_MEMORY;var p,g,w,ne=[],oe=[],se=[],r=0,n=null;function ie(){r++,y.monitorRunDependencies&&y.monitorRunDependencies(r)}function ae(){var e;r--,y.monitorRunDependencies&&y.monitorRunDependencies(r),0==r&&n&&(e=n,n=null,e())}function v(e){throw y.onAbort&&y.onAbort(e),a(e="Aborted("+e+")"),V=!0,e+=". Build with -sASSERTIONS for more info.",new WebAssembly.RuntimeError(e)}function ue(e){return e.startsWith("data:application/octet-stream;base64,")}function le(e){return e.startsWith("file://")}function ce(e){try{if(e==p&&s)return new Uint8Array(s);if(B)return B(e);throw"both async and sync fetching of the wasm failed"}catch(e){v(e)}}function de(e){for(;0<e.length;)e.shift()(y)}function fe(e){this.excPtr=e,this.ptr=e-24,this.set_type=function(e){h[this.ptr+4>>2]=e},this.get_type=function(){return h[this.ptr+4>>2]},this.set_destructor=function(e){h[this.ptr+8>>2]=e},this.get_destructor=function(){return h[this.ptr+8>>2]},this.set_refcount=function(e){l[this.ptr>>2]=e},this.set_caught=function(e){d[this.ptr+12>>0]=e=e?1:0},this.get_caught=function(){return 0!=d[this.ptr+12>>0]},this.set_rethrown=function(e){d[this.ptr+13>>0]=e=e?1:0},this.get_rethrown=function(){return 0!=d[this.ptr+13>>0]},this.init=function(e,t){this.set_adjusted_ptr(0),this.set_type(e),this.set_destructor(t),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var e=l[this.ptr>>2];l[this.ptr>>2]=e+1},this.release_ref=function(){var e=l[this.ptr>>2];return l[this.ptr>>2]=e-1,1===e},this.set_adjusted_ptr=function(e){h[this.ptr+16>>2]=e},this.get_adjusted_ptr=function(){return h[this.ptr+16>>2]},this.get_exception_ptr=function(){var e;return _t(this.get_type())?h[this.excPtr>>2]:0!==(e=this.get_adjusted_ptr())?e:this.excPtr}}ue(p="decoder-pro-audio.wasm")||(L=p,p=y.locateFile?y.locateFile(L,e):e+L);var b={isAbs:e=>"/"===e.charAt(0),splitPath:e=>/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1),normalizeArray:(e,t)=>{for(var r=0,n=e.length-1;0<=n;n--){var o=e[n];"."===o?e.splice(n,1):".."===o?(e.splice(n,1),r++):r&&(e.splice(n,1),r--)}if(t)for(;r;r--)e.unshift("..");return e},normalize:e=>{var t=b.isAbs(e),r="/"===e.substr(-1);return(e=(e=b.normalizeArray(e.split("/").filter(e=>!!e),!t).join("/"))||t?e:".")&&r&&(e+="/"),(t?"/":"")+e},dirname:e=>{var e=b.splitPath(e),t=e[0],e=e[1];return t||e?t+(e=e&&e.substr(0,e.length-1)):"."},basename:e=>{var t;return"/"===e?"/":-1===(t=(e=(e=b.normalize(e)).replace(/\/$/,"")).lastIndexOf("/"))?e:e.substr(t+1)},join:function(){var e=Array.prototype.slice.call(arguments,0);return b.normalize(e.join("/"))},join2:(e,t)=>b.normalize(e+"/"+t)},E={resolve:function(){for(var e="",t=!1,r=arguments.length-1;-1<=r&&!t;r--){var n=0<=r?arguments[r]:k.cwd();if("string"!=typeof n)throw new TypeError("Arguments to path.resolve must be strings");if(!n)return"";e=n+"/"+e,t=b.isAbs(n)}return(t?"/":"")+b.normalizeArray(e.split("/").filter(e=>!!e),!t).join("/")||"."},relative:(e,t)=>{function r(e){for(var t=0;t<e.length&&""===e[t];t++);for(var r=e.length-1;0<=r&&""===e[r];r--);return r<t?[]:e.slice(t,r-t+1)}e=E.resolve(e).substr(1),t=E.resolve(t).substr(1);for(var n=r(e.split("/")),o=r(t.split("/")),s=Math.min(n.length,o.length),i=s,a=0;a<s;a++)if(n[a]!==o[a]){i=a;break}for(var u=[],a=i;a<n.length;a++)u.push("..");return(u=u.concat(o.slice(i))).join("/")}};function he(e,t,r){r=0<r?r:re(e)+1,r=new Array(r),e=te(e,r,0,r.length);return t&&(r.length=e),r}var o={ttys:[],init:function(){},shutdown:function(){},register:function(e,t){o.ttys[e]={input:[],output:[],ops:t},k.registerDevice(e,o.stream_ops)},stream_ops:{open:function(e){var t=o.ttys[e.node.rdev];if(!t)throw new k.ErrnoError(43);e.tty=t,e.seekable=!1},close:function(e){e.tty.ops.flush(e.tty)},flush:function(e){e.tty.ops.flush(e.tty)},read:function(e,t,r,n,o){if(!e.tty||!e.tty.ops.get_char)throw new k.ErrnoError(60);for(var s,i=0,a=0;a<n;a++){try{s=e.tty.ops.get_char(e.tty)}catch(e){throw new k.ErrnoError(29)}if(void 0===s&&0===i)throw new k.ErrnoError(6);if(null==s)break;i++,t[r+a]=s}return i&&(e.node.timestamp=Date.now()),i},write:function(e,t,r,n,o){if(!e.tty||!e.tty.ops.put_char)throw new k.ErrnoError(60);try{for(var s=0;s<n;s++)e.tty.ops.put_char(e.tty,t[r+s])}catch(e){throw new k.ErrnoError(29)}return n&&(e.node.timestamp=Date.now()),s}},default_tty_ops:{get_char:function(e){if(!e.input.length){var t=null;if(H){var r=Buffer.alloc(256),n=0;try{n=U.readSync(process.stdin.fd,r,0,256,-1)}catch(e){if(!e.toString().includes("EOF"))throw e;n=0}t=0<n?r.slice(0,n).toString("utf-8"):null}else"undefined"!=typeof window&&"function"==typeof window.prompt?null!==(t=window.prompt("Input: "))&&(t+="\n"):"function"==typeof readline&&null!==(t=readline())&&(t+="\n");if(!t)return null;e.input=he(t,!0)}return e.input.shift()},put_char:function(e,t){null===t||10===t?(q(i(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},flush:function(e){e.output&&0<e.output.length&&(q(i(e.output,0)),e.output=[])}},default_tty1_ops:{put_char:function(e,t){null===t||10===t?(a(i(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},flush:function(e){e.output&&0<e.output.length&&(a(i(e.output,0)),e.output=[])}}};function pe(e){t=e,e=65536*Math.ceil(t/65536);var t=Et(65536,e);return t?(e=e,f.fill(0,t,t+e),t):0}var _={ops_table:null,mount:function(e){return _.createNode(null,"/",16895,0)},createNode:function(e,t,r,n){if(k.isBlkdev(r)||k.isFIFO(r))throw new k.ErrnoError(63);_.ops_table||(_.ops_table={dir:{node:{getattr:_.node_ops.getattr,setattr:_.node_ops.setattr,lookup:_.node_ops.lookup,mknod:_.node_ops.mknod,rename:_.node_ops.rename,unlink:_.node_ops.unlink,rmdir:_.node_ops.rmdir,readdir:_.node_ops.readdir,symlink:_.node_ops.symlink},stream:{llseek:_.stream_ops.llseek}},file:{node:{getattr:_.node_ops.getattr,setattr:_.node_ops.setattr},stream:{llseek:_.stream_ops.llseek,read:_.stream_ops.read,write:_.stream_ops.write,allocate:_.stream_ops.allocate,mmap:_.stream_ops.mmap,msync:_.stream_ops.msync}},link:{node:{getattr:_.node_ops.getattr,setattr:_.node_ops.setattr,readlink:_.node_ops.readlink},stream:{}},chrdev:{node:{getattr:_.node_ops.getattr,setattr:_.node_ops.setattr},stream:k.chrdev_stream_ops}});r=k.createNode(e,t,r,n);return k.isDir(r.mode)?(r.node_ops=_.ops_table.dir.node,r.stream_ops=_.ops_table.dir.stream,r.contents={}):k.isFile(r.mode)?(r.node_ops=_.ops_table.file.node,r.stream_ops=_.ops_table.file.stream,r.usedBytes=0,r.contents=null):k.isLink(r.mode)?(r.node_ops=_.ops_table.link.node,r.stream_ops=_.ops_table.link.stream):k.isChrdev(r.mode)&&(r.node_ops=_.ops_table.chrdev.node,r.stream_ops=_.ops_table.chrdev.stream),r.timestamp=Date.now(),e&&(e.contents[t]=r,e.timestamp=r.timestamp),r},getFileDataAsTypedArray:function(e){return e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array(0)},expandFileStorage:function(e,t){var r=e.contents?e.contents.length:0;t<=r||(t=Math.max(t,r*(r<1048576?2:1.125)>>>0),0!=r&&(t=Math.max(t,256)),r=e.contents,e.contents=new Uint8Array(t),0<e.usedBytes&&e.contents.set(r.subarray(0,e.usedBytes),0))},resizeFileStorage:function(e,t){var r;e.usedBytes!=t&&(0==t?(e.contents=null,e.usedBytes=0):(r=e.contents,e.contents=new Uint8Array(t),r&&e.contents.set(r.subarray(0,Math.min(t,e.usedBytes))),e.usedBytes=t))},node_ops:{getattr:function(e){var t={};return t.dev=k.isChrdev(e.mode)?e.id:1,t.ino=e.id,t.mode=e.mode,t.nlink=1,t.uid=0,t.gid=0,t.rdev=e.rdev,k.isDir(e.mode)?t.size=4096:k.isFile(e.mode)?t.size=e.usedBytes:k.isLink(e.mode)?t.size=e.link.length:t.size=0,t.atime=new Date(e.timestamp),t.mtime=new Date(e.timestamp),t.ctime=new Date(e.timestamp),t.blksize=4096,t.blocks=Math.ceil(t.size/t.blksize),t},setattr:function(e,t){void 0!==t.mode&&(e.mode=t.mode),void 0!==t.timestamp&&(e.timestamp=t.timestamp),void 0!==t.size&&_.resizeFileStorage(e,t.size)},lookup:function(e,t){throw k.genericErrors[44]},mknod:function(e,t,r,n){return _.createNode(e,t,r,n)},rename:function(e,t,r){if(k.isDir(e.mode)){var n;try{n=k.lookupNode(t,r)}catch(e){}if(n)for(var o in n.contents)throw new k.ErrnoError(55)}delete e.parent.contents[e.name],e.parent.timestamp=Date.now(),e.name=r,t.contents[r]=e,t.timestamp=e.parent.timestamp,e.parent=t},unlink:function(e,t){delete e.contents[t],e.timestamp=Date.now()},rmdir:function(e,t){for(var r in k.lookupNode(e,t).contents)throw new k.ErrnoError(55);delete e.contents[t],e.timestamp=Date.now()},readdir:function(e){var t,r=[".",".."];for(t in e.contents)e.contents.hasOwnProperty(t)&&r.push(t);return r},symlink:function(e,t,r){e=_.createNode(e,t,41471,0);return e.link=r,e},readlink:function(e){if(k.isLink(e.mode))return e.link;throw new k.ErrnoError(28)}},stream_ops:{read:function(e,t,r,n,o){var s=e.node.contents;if(o>=e.node.usedBytes)return 0;var i=Math.min(e.node.usedBytes-o,n);if(8<i&&s.subarray)t.set(s.subarray(o,o+i),r);else for(var a=0;a<i;a++)t[r+a]=s[o+a];return i},write:function(e,t,r,n,o,s){if(!n)return 0;var i=e.node;if(i.timestamp=Date.now(),t.subarray&&(!i.contents||i.contents.subarray)){if(s)return i.contents=t.subarray(r,r+n),i.usedBytes=n;if(0===i.usedBytes&&0===o)return i.contents=t.slice(r,r+n),i.usedBytes=n;if(o+n<=i.usedBytes)return i.contents.set(t.subarray(r,r+n),o),n}if(_.expandFileStorage(i,o+n),i.contents.subarray&&t.subarray)i.contents.set(t.subarray(r,r+n),o);else for(var a=0;a<n;a++)i.contents[o+a]=t[r+a];return i.usedBytes=Math.max(i.usedBytes,o+n),n},llseek:function(e,t,r){if(1===r?t+=e.position:2===r&&k.isFile(e.node.mode)&&(t+=e.node.usedBytes),t<0)throw new k.ErrnoError(28);return t},allocate:function(e,t,r){_.expandFileStorage(e.node,t+r),e.node.usedBytes=Math.max(e.node.usedBytes,t+r)},mmap:function(e,t,r,n,o){if(!k.isFile(e.node.mode))throw new k.ErrnoError(43);var s,i,e=e.node.contents;if(2&o||e.buffer!==X){if((0<r||r+t<e.length)&&(e=e.subarray?e.subarray(r,r+t):Array.prototype.slice.call(e,r,r+t)),i=!0,!(s=pe(t)))throw new k.ErrnoError(48);d.set(e,s)}else i=!1,s=e.byteOffset;return{ptr:s,allocated:i}},msync:function(e,t,r,n,o){if(k.isFile(e.node.mode))return 2&o||_.stream_ops.write(e,t,0,n,r,!1),0;throw new k.ErrnoError(43)}}},k={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(!(e=E.resolve(k.cwd(),e)))return{path:"",node:null};if(8<(t=Object.assign({follow_mount:!0,recurse_count:0},t)).recurse_count)throw new k.ErrnoError(32);for(var r=b.normalizeArray(e.split("/").filter(e=>!!e),!1),n=k.root,o="/",s=0;s<r.length;s++){var i=s===r.length-1;if(i&&t.parent)break;if(n=k.lookupNode(n,r[s]),o=b.join2(o,r[s]),!k.isMountpoint(n)||i&&!t.follow_mount||(n=n.mounted.root),!i||t.follow)for(var a=0;k.isLink(n.mode);){var u=k.readlink(o),o=E.resolve(b.dirname(o),u),n=k.lookupPath(o,{recurse_count:t.recurse_count+1}).node;if(40<a++)throw new k.ErrnoError(32)}}return{path:o,node:n}},getPath:e=>{for(var t,r;;){if(k.isRoot(e))return r=e.mount.mountpoint,t?"/"!==r[r.length-1]?r+"/"+t:r+t:r;t=t?e.name+"/"+t:e.name,e=e.parent}},hashName:(e,t)=>{for(var r=0,n=0;n<t.length;n++)r=(r<<5)-r+t.charCodeAt(n)|0;return(e+r>>>0)%k.nameTable.length},hashAddNode:e=>{var t=k.hashName(e.parent.id,e.name);e.name_next=k.nameTable[t],k.nameTable[t]=e},hashRemoveNode:e=>{var t=k.hashName(e.parent.id,e.name);if(k.nameTable[t]===e)k.nameTable[t]=e.name_next;else for(var r=k.nameTable[t];r;){if(r.name_next===e){r.name_next=e.name_next;break}r=r.name_next}},lookupNode:(e,t)=>{var r=k.mayLookup(e);if(r)throw new k.ErrnoError(r,e);for(var r=k.hashName(e.id,t),n=k.nameTable[r];n;n=n.name_next){var o=n.name;if(n.parent.id===e.id&&o===t)return n}return k.lookup(e,t)},createNode:(e,t,r,n)=>{e=new k.FSNode(e,t,r,n);return k.hashAddNode(e),e},destroyNode:e=>{k.hashRemoveNode(e)},isRoot:e=>e===e.parent,isMountpoint:e=>!!e.mounted,isFile:e=>32768==(61440&e),isDir:e=>16384==(61440&e),isLink:e=>40960==(61440&e),isChrdev:e=>8192==(61440&e),isBlkdev:e=>24576==(61440&e),isFIFO:e=>4096==(61440&e),isSocket:e=>49152==(49152&e),flagModes:{r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090},modeStringToFlags:e=>{var t=k.flagModes[e];if(void 0===t)throw new Error("Unknown file open mode: "+e);return t},flagsToPermissionString:e=>{var t=["r","w","rw"][3&e];return 512&e&&(t+="w"),t},nodePermissions:(e,t)=>k.ignorePermissions||(!t.includes("r")||292&e.mode)&&(!t.includes("w")||146&e.mode)&&(!t.includes("x")||73&e.mode)?0:2,mayLookup:e=>{return k.nodePermissions(e,"x")||(e.node_ops.lookup?0:2)},mayCreate:(e,t)=>{try{return k.lookupNode(e,t),20}catch(e){}return k.nodePermissions(e,"wx")},mayDelete:(e,t,r)=>{var n;try{n=k.lookupNode(e,t)}catch(e){return e.errno}t=k.nodePermissions(e,"wx");if(t)return t;if(r){if(!k.isDir(n.mode))return 54;if(k.isRoot(n)||k.getPath(n)===k.cwd())return 10}else if(k.isDir(n.mode))return 31;return 0},mayOpen:(e,t)=>e?k.isLink(e.mode)?32:k.isDir(e.mode)&&("r"!==k.flagsToPermissionString(t)||512&t)?31:k.nodePermissions(e,k.flagsToPermissionString(t)):44,MAX_OPEN_FDS:4096,nextfd:function(){for(var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:k.MAX_OPEN_FDS,r=e;r<=t;r++)if(!k.streams[r])return r;throw new k.ErrnoError(33)},getStream:e=>k.streams[e],createStream:(e,t,r)=>{k.FSStream||(k.FSStream=function(){this.shared={}},k.FSStream.prototype={},Object.defineProperties(k.FSStream.prototype,{object:{get:function(){return this.node},set:function(e){this.node=e}},isRead:{get:function(){return 1!=(2097155&this.flags)}},isWrite:{get:function(){return 0!=(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}},flags:{get:function(){return this.shared.flags},set:function(e){this.shared.flags=e}},position:{get:function(){return this.shared.position},set:function(e){this.shared.position=e}}})),e=Object.assign(new k.FSStream,e);t=k.nextfd(t,r);return e.fd=t,k.streams[t]=e},closeStream:e=>{k.streams[e]=null},chrdev_stream_ops:{open:e=>{var t=k.getDevice(e.node.rdev);e.stream_ops=t.stream_ops,e.stream_ops.open&&e.stream_ops.open(e)},llseek:()=>{throw new k.ErrnoError(70)}},major:e=>e>>8,minor:e=>255&e,makedev:(e,t)=>e<<8|t,registerDevice:(e,t)=>{k.devices[e]={stream_ops:t}},getDevice:e=>k.devices[e],getMounts:e=>{for(var t=[],r=[e];r.length;){var n=r.pop();t.push(n),r.push.apply(r,n.mounts)}return t},syncfs:(t,r)=>{"function"==typeof t&&(r=t,t=!1),k.syncFSRequests++,1<k.syncFSRequests&&a("warning: "+k.syncFSRequests+" FS.syncfs operations in flight at once, probably just doing extra work");var n=k.getMounts(k.root.mount),o=0;function s(e){return k.syncFSRequests--,r(e)}function i(e){if(e)return i.errored?void 0:(i.errored=!0,s(e));++o>=n.length&&s(null)}n.forEach(e=>{if(!e.type.syncfs)return i(null);e.type.syncfs(e,t,i)})},mount:(e,t,r)=>{var n,o="/"===r,s=!r;if(o&&k.root)throw new k.ErrnoError(10);if(!o&&!s){s=k.lookupPath(r,{follow_mount:!1});if(r=s.path,n=s.node,k.isMountpoint(n))throw new k.ErrnoError(10);if(!k.isDir(n.mode))throw new k.ErrnoError(54)}s={type:e,opts:t,mountpoint:r,mounts:[]},t=e.mount(s);return(t.mount=s).root=t,o?k.root=t:n&&(n.mounted=s,n.mount)&&n.mount.mounts.push(s),t},unmount:e=>{e=k.lookupPath(e,{follow_mount:!1});if(!k.isMountpoint(e.node))throw new k.ErrnoError(28);var e=e.node,t=e.mounted,n=k.getMounts(t),t=(Object.keys(k.nameTable).forEach(e=>{for(var t=k.nameTable[e];t;){var r=t.name_next;n.includes(t.mount)&&k.destroyNode(t),t=r}}),e.mounted=null,e.mount.mounts.indexOf(t));e.mount.mounts.splice(t,1)},lookup:(e,t)=>e.node_ops.lookup(e,t),mknod:(e,t,r)=>{var n=k.lookupPath(e,{parent:!0}).node,e=b.basename(e);if(!e||"."===e||".."===e)throw new k.ErrnoError(28);var o=k.mayCreate(n,e);if(o)throw new k.ErrnoError(o);if(n.node_ops.mknod)return n.node_ops.mknod(n,e,t,r);throw new k.ErrnoError(63)},create:(e,t)=>k.mknod(e,t=(t=void 0!==t?t:438)&4095|32768,0),mkdir:(e,t)=>k.mknod(e,t=(t=void 0!==t?t:511)&1023|16384,0),mkdirTree:(e,t)=>{for(var r=e.split("/"),n="",o=0;o<r.length;++o)if(r[o]){n+="/"+r[o];try{k.mkdir(n,t)}catch(e){if(20!=e.errno)throw e}}},mkdev:(e,t,r)=>(void 0===r&&(r=t,t=438),k.mknod(e,t|=8192,r)),symlink:(e,t)=>{if(!E.resolve(e))throw new k.ErrnoError(44);var r=k.lookupPath(t,{parent:!0}).node;if(!r)throw new k.ErrnoError(44);var t=b.basename(t),n=k.mayCreate(r,t);if(n)throw new k.ErrnoError(n);if(r.node_ops.symlink)return r.node_ops.symlink(r,t,e);throw new k.ErrnoError(63)},rename:(e,t)=>{var r=b.dirname(e),n=b.dirname(t),o=b.basename(e),s=b.basename(t),i=k.lookupPath(e,{parent:!0}).node,a=k.lookupPath(t,{parent:!0}).node;if(!i||!a)throw new k.ErrnoError(44);if(i.mount!==a.mount)throw new k.ErrnoError(75);var u,l=k.lookupNode(i,o);if("."!==E.relative(e,n).charAt(0))throw new k.ErrnoError(28);if("."!==E.relative(t,r).charAt(0))throw new k.ErrnoError(55);try{u=k.lookupNode(a,s)}catch(e){}if(l!==u){n=k.isDir(l.mode),t=k.mayDelete(i,o,n);if(t)throw new k.ErrnoError(t);if(t=u?k.mayDelete(a,s,n):k.mayCreate(a,s))throw new k.ErrnoError(t);if(!i.node_ops.rename)throw new k.ErrnoError(63);if(k.isMountpoint(l)||u&&k.isMountpoint(u))throw new k.ErrnoError(10);if(a!==i&&(t=k.nodePermissions(i,"w")))throw new k.ErrnoError(t);k.hashRemoveNode(l);try{i.node_ops.rename(l,a,s)}catch(e){throw e}finally{k.hashAddNode(l)}}},rmdir:e=>{var t=k.lookupPath(e,{parent:!0}).node,e=b.basename(e),r=k.lookupNode(t,e),n=k.mayDelete(t,e,!0);if(n)throw new k.ErrnoError(n);if(!t.node_ops.rmdir)throw new k.ErrnoError(63);if(k.isMountpoint(r))throw new k.ErrnoError(10);t.node_ops.rmdir(t,e),k.destroyNode(r)},readdir:e=>{e=k.lookupPath(e,{follow:!0}).node;if(e.node_ops.readdir)return e.node_ops.readdir(e);throw new k.ErrnoError(54)},unlink:e=>{var t=k.lookupPath(e,{parent:!0}).node;if(!t)throw new k.ErrnoError(44);var e=b.basename(e),r=k.lookupNode(t,e),n=k.mayDelete(t,e,!1);if(n)throw new k.ErrnoError(n);if(!t.node_ops.unlink)throw new k.ErrnoError(63);if(k.isMountpoint(r))throw new k.ErrnoError(10);t.node_ops.unlink(t,e),k.destroyNode(r)},readlink:e=>{e=k.lookupPath(e).node;if(!e)throw new k.ErrnoError(44);if(e.node_ops.readlink)return E.resolve(k.getPath(e.parent),e.node_ops.readlink(e));throw new k.ErrnoError(28)},stat:(e,t)=>{e=k.lookupPath(e,{follow:!t}).node;if(!e)throw new k.ErrnoError(44);if(e.node_ops.getattr)return e.node_ops.getattr(e);throw new k.ErrnoError(63)},lstat:e=>k.stat(e,!0),chmod:(e,t,r)=>{r="string"==typeof e?k.lookupPath(e,{follow:!r}).node:e;if(!r.node_ops.setattr)throw new k.ErrnoError(63);r.node_ops.setattr(r,{mode:4095&t|-4096&r.mode,timestamp:Date.now()})},lchmod:(e,t)=>{k.chmod(e,t,!0)},fchmod:(e,t)=>{e=k.getStream(e);if(!e)throw new k.ErrnoError(8);k.chmod(e.node,t)},chown:(e,t,r,n)=>{n="string"==typeof e?k.lookupPath(e,{follow:!n}).node:e;if(!n.node_ops.setattr)throw new k.ErrnoError(63);n.node_ops.setattr(n,{timestamp:Date.now()})},lchown:(e,t,r)=>{k.chown(e,t,r,!0)},fchown:(e,t,r)=>{e=k.getStream(e);if(!e)throw new k.ErrnoError(8);k.chown(e.node,t,r)},truncate:(e,t)=>{if(t<0)throw new k.ErrnoError(28);e="string"==typeof e?k.lookupPath(e,{follow:!0}).node:e;if(!e.node_ops.setattr)throw new k.ErrnoError(63);if(k.isDir(e.mode))throw new k.ErrnoError(31);if(!k.isFile(e.mode))throw new k.ErrnoError(28);var r=k.nodePermissions(e,"w");if(r)throw new k.ErrnoError(r);e.node_ops.setattr(e,{size:t,timestamp:Date.now()})},ftruncate:(e,t)=>{e=k.getStream(e);if(!e)throw new k.ErrnoError(8);if(0==(2097155&e.flags))throw new k.ErrnoError(28);k.truncate(e.node,t)},utime:(e,t,r)=>{e=k.lookupPath(e,{follow:!0}).node;e.node_ops.setattr(e,{timestamp:Math.max(t,r)})},open:(e,t,r)=>{if(""===e)throw new k.ErrnoError(44);var n;if(r=void 0===r?438:r,r=64&(t="string"==typeof t?k.modeStringToFlags(t):t)?4095&r|32768:0,"object"==typeof e)n=e;else{e=b.normalize(e);try{n=k.lookupPath(e,{follow:!(131072&t)}).node}catch(e){}}var o=!1;if(64&t)if(n){if(128&t)throw new k.ErrnoError(20)}else n=k.mknod(e,r,0),o=!0;if(!n)throw new k.ErrnoError(44);if(k.isChrdev(n.mode)&&(t&=-513),65536&t&&!k.isDir(n.mode))throw new k.ErrnoError(54);if(!o){r=k.mayOpen(n,t);if(r)throw new k.ErrnoError(r)}512&t&&!o&&k.truncate(n,0),t&=-131713;r=k.createStream({node:n,path:k.getPath(n),flags:t,seekable:!0,position:0,stream_ops:n.stream_ops,ungotten:[],error:!1});return r.stream_ops.open&&r.stream_ops.open(r),!y.logReadFiles||1&t||(k.readFiles||(k.readFiles={}),e in k.readFiles)||(k.readFiles[e]=1),r},close:e=>{if(k.isClosed(e))throw new k.ErrnoError(8);e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(e){throw e}finally{k.closeStream(e.fd)}e.fd=null},isClosed:e=>null===e.fd,llseek:(e,t,r)=>{if(k.isClosed(e))throw new k.ErrnoError(8);if(!e.seekable||!e.stream_ops.llseek)throw new k.ErrnoError(70);if(0!=r&&1!=r&&2!=r)throw new k.ErrnoError(28);return e.position=e.stream_ops.llseek(e,t,r),e.ungotten=[],e.position},read:(e,t,r,n,o)=>{if(n<0||o<0)throw new k.ErrnoError(28);if(k.isClosed(e))throw new k.ErrnoError(8);if(1==(2097155&e.flags))throw new k.ErrnoError(8);if(k.isDir(e.node.mode))throw new k.ErrnoError(31);if(!e.stream_ops.read)throw new k.ErrnoError(28);var s=void 0!==o;if(s){if(!e.seekable)throw new k.ErrnoError(70)}else o=e.position;t=e.stream_ops.read(e,t,r,n,o);return s||(e.position+=t),t},write:(e,t,r,n,o,s)=>{if(n<0||o<0)throw new k.ErrnoError(28);if(k.isClosed(e))throw new k.ErrnoError(8);if(0==(2097155&e.flags))throw new k.ErrnoError(8);if(k.isDir(e.node.mode))throw new k.ErrnoError(31);if(!e.stream_ops.write)throw new k.ErrnoError(28);e.seekable&&1024&e.flags&&k.llseek(e,0,2);var i=void 0!==o;if(i){if(!e.seekable)throw new k.ErrnoError(70)}else o=e.position;t=e.stream_ops.write(e,t,r,n,o,s);return i||(e.position+=t),t},allocate:(e,t,r)=>{if(k.isClosed(e))throw new k.ErrnoError(8);if(t<0||r<=0)throw new k.ErrnoError(28);if(0==(2097155&e.flags))throw new k.ErrnoError(8);if(!k.isFile(e.node.mode)&&!k.isDir(e.node.mode))throw new k.ErrnoError(43);if(!e.stream_ops.allocate)throw new k.ErrnoError(138);e.stream_ops.allocate(e,t,r)},mmap:(e,t,r,n,o)=>{if(0!=(2&n)&&0==(2&o)&&2!=(2097155&e.flags))throw new k.ErrnoError(2);if(1==(2097155&e.flags))throw new k.ErrnoError(2);if(e.stream_ops.mmap)return e.stream_ops.mmap(e,t,r,n,o);throw new k.ErrnoError(43)},msync:(e,t,r,n,o)=>e&&e.stream_ops.msync?e.stream_ops.msync(e,t,r,n,o):0,munmap:e=>0,ioctl:(e,t,r)=>{if(e.stream_ops.ioctl)return e.stream_ops.ioctl(e,t,r);throw new k.ErrnoError(59)},readFile:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(t.flags=t.flags||0,t.encoding=t.encoding||"binary","utf8"!==t.encoding&&"binary"!==t.encoding)throw new Error('Invalid encoding type "'+t.encoding+'"');var r,n=k.open(e,t.flags),e=k.stat(e).size,o=new Uint8Array(e);return k.read(n,o,0,e,0),"utf8"===t.encoding?r=i(o,0):"binary"===t.encoding&&(r=o),k.close(n),r},writeFile:function(e,t){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},e=(r.flags=r.flags||577,k.open(e,r.flags,r.mode));if("string"==typeof t){var n=new Uint8Array(re(t)+1),o=te(t,n,0,n.length);k.write(e,n,0,o,void 0,r.canOwn)}else{if(!ArrayBuffer.isView(t))throw new Error("Unsupported data type");k.write(e,t,0,t.byteLength,void 0,r.canOwn)}k.close(e)},cwd:()=>k.currentPath,chdir:e=>{e=k.lookupPath(e,{follow:!0});if(null===e.node)throw new k.ErrnoError(44);if(!k.isDir(e.node.mode))throw new k.ErrnoError(54);var t=k.nodePermissions(e.node,"x");if(t)throw new k.ErrnoError(t);k.currentPath=e.path},createDefaultDirectories:()=>{k.mkdir("/tmp"),k.mkdir("/home"),k.mkdir("/home/<USER>")},createDefaultDevices:()=>{k.mkdir("/dev"),k.registerDevice(k.makedev(1,3),{read:()=>0,write:(e,t,r,n,o)=>n}),k.mkdev("/dev/null",k.makedev(1,3)),o.register(k.makedev(5,0),o.default_tty_ops),o.register(k.makedev(6,0),o.default_tty1_ops),k.mkdev("/dev/tty",k.makedev(5,0)),k.mkdev("/dev/tty1",k.makedev(6,0));var e=function(){var e;if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues)return e=new Uint8Array(1),()=>(crypto.getRandomValues(e),e[0]);if(H)try{var t=At.default;return()=>t.randomBytes(1)[0]}catch(e){}return()=>v("randomDevice")}();k.createDevice("/dev","random",e),k.createDevice("/dev","urandom",e),k.mkdir("/dev/shm"),k.mkdir("/dev/shm/tmp")},createSpecialDirectories:()=>{k.mkdir("/proc");var t=k.mkdir("/proc/self");k.mkdir("/proc/self/fd"),k.mount({mount:()=>{var e=k.createNode(t,"fd",16895,73);return e.node_ops={lookup:(e,t)=>{var r=k.getStream(+t);if(r)return(t={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:()=>r.path}}).parent=t;throw new k.ErrnoError(8)}},e}},{},"/proc/self/fd")},createStandardStreams:()=>{y.stdin?k.createDevice("/dev","stdin",y.stdin):k.symlink("/dev/tty","/dev/stdin"),y.stdout?k.createDevice("/dev","stdout",null,y.stdout):k.symlink("/dev/tty","/dev/stdout"),y.stderr?k.createDevice("/dev","stderr",null,y.stderr):k.symlink("/dev/tty1","/dev/stderr"),k.open("/dev/stdin",0),k.open("/dev/stdout",1),k.open("/dev/stderr",1)},ensureErrnoError:()=>{k.ErrnoError||(k.ErrnoError=function(e,t){this.node=t,this.setErrno=function(e){this.errno=e},this.setErrno(e),this.message="FS error"},k.ErrnoError.prototype=new Error,k.ErrnoError.prototype.constructor=k.ErrnoError,[44].forEach(e=>{k.genericErrors[e]=new k.ErrnoError(e),k.genericErrors[e].stack="<generic error, no stack>"}))},staticInit:()=>{k.ensureErrnoError(),k.nameTable=new Array(4096),k.mount(_,{},"/"),k.createDefaultDirectories(),k.createDefaultDevices(),k.createSpecialDirectories(),k.filesystems={MEMFS:_}},init:(e,t,r)=>{k.init.initialized=!0,k.ensureErrnoError(),y.stdin=e||y.stdin,y.stdout=t||y.stdout,y.stderr=r||y.stderr,k.createStandardStreams()},quit:()=>{k.init.initialized=!1;for(var e=0;e<k.streams.length;e++){var t=k.streams[e];t&&k.close(t)}},getMode:(e,t)=>{var r=0;return e&&(r|=365),t&&(r|=146),r},findObject:(e,t)=>{e=k.analyzePath(e,t);return e.exists?e.object:null},analyzePath:(e,t)=>{try{e=(n=k.lookupPath(e,{follow:!t})).path}catch(e){}var r={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var n=k.lookupPath(e,{parent:!0});r.parentExists=!0,r.parentPath=n.path,r.parentObject=n.node,r.name=b.basename(e),n=k.lookupPath(e,{follow:!t}),r.exists=!0,r.path=n.path,r.object=n.node,r.name=n.node.name,r.isRoot="/"===n.path}catch(e){r.error=e.errno}return r},createPath:(e,t,r,n)=>{e="string"==typeof e?e:k.getPath(e);for(var o=t.split("/").reverse();o.length;){var s=o.pop();if(s){var i=b.join2(e,s);try{k.mkdir(i)}catch(e){}e=i}}return i},createFile:(e,t,r,n,o)=>{e=b.join2("string"==typeof e?e:k.getPath(e),t),t=k.getMode(n,o);return k.create(e,t)},createDataFile:(e,t,r,n,o,s)=>{var i=t,t=(e&&(e="string"==typeof e?e:k.getPath(e),i=t?b.join2(e,t):e),k.getMode(n,o)),e=k.create(i,t);if(r){if("string"==typeof r){for(var a=new Array(r.length),u=0,l=r.length;u<l;++u)a[u]=r.charCodeAt(u);r=a}k.chmod(e,146|t);n=k.open(e,577);k.write(n,r,0,r.length,0,s),k.close(n),k.chmod(e,t)}return e},createDevice:(e,t,u,i)=>{var e=b.join2("string"==typeof e?e:k.getPath(e),t),t=k.getMode(!!u,!!i),r=(k.createDevice.major||(k.createDevice.major=64),k.makedev(k.createDevice.major++,0));return k.registerDevice(r,{open:e=>{e.seekable=!1},close:e=>{i&&i.buffer&&i.buffer.length&&i(10)},read:(e,t,r,n,o)=>{for(var s,i=0,a=0;a<n;a++){try{s=u()}catch(e){throw new k.ErrnoError(29)}if(void 0===s&&0===i)throw new k.ErrnoError(6);if(null==s)break;i++,t[r+a]=s}return i&&(e.node.timestamp=Date.now()),i},write:(e,t,r,n,o)=>{for(var s=0;s<n;s++)try{i(t[r+s])}catch(e){throw new k.ErrnoError(29)}return n&&(e.node.timestamp=Date.now()),s}}),k.mkdev(e,t,r)},forceLoadFile:e=>{if(e.isDevice||e.isFolder||e.link||e.contents)return!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!t)throw new Error("Cannot load without read() or XMLHttpRequest.");try{e.contents=he(t(e.url),!0),e.usedBytes=e.contents.length}catch(e){throw new k.ErrnoError(29)}},createLazyFile:(e,t,i,r,n)=>{function o(){this.lengthKnown=!1,this.chunks=[]}if(o.prototype.get=function(e){var t;if(!(e>this.length-1||e<0))return t=e%this.chunkSize,e=e/this.chunkSize|0,this.getter(e)[t]},o.prototype.setDataGetter=function(e){this.getter=e},o.prototype.cacheLength=function(){var e=new XMLHttpRequest;if(e.open("HEAD",i,!1),e.send(null),!(200<=e.status&&e.status<300||304===e.status))throw new Error("Couldn't load "+i+". Status: "+e.status);var t,n=Number(e.getResponseHeader("Content-length")),r=(t=e.getResponseHeader("Accept-Ranges"))&&"bytes"===t,e=(t=e.getResponseHeader("Content-Encoding"))&&"gzip"===t,o=1048576,s=(r||(o=n),this);s.setDataGetter(e=>{var t=e*o,r=(e+1)*o-1,r=Math.min(r,n-1);if(void 0===s.chunks[e]&&(s.chunks[e]=((e,t)=>{if(t<e)throw new Error("invalid range ("+e+", "+t+") or no bytes requested!");if(n-1<t)throw new Error("only "+n+" bytes available! programmer error!");var r=new XMLHttpRequest;if(r.open("GET",i,!1),n!==o&&r.setRequestHeader("Range","bytes="+e+"-"+t),r.responseType="arraybuffer",r.overrideMimeType&&r.overrideMimeType("text/plain; charset=x-user-defined"),r.send(null),200<=r.status&&r.status<300||304===r.status)return void 0!==r.response?new Uint8Array(r.response||[]):he(r.responseText||"",!0);throw new Error("Couldn't load "+i+". Status: "+r.status)})(t,r)),void 0===s.chunks[e])throw new Error("doXHR failed!");return s.chunks[e]}),!e&&n||(o=n=1,n=this.getter(0).length,o=n,q("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=n,this._chunkSize=o,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){if(!c)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var s=new o,s=(Object.defineProperties(s,{length:{get:function(){return this.lengthKnown||this.cacheLength(),this._length}},chunkSize:{get:function(){return this.lengthKnown||this.cacheLength(),this._chunkSize}}}),{isDevice:!1,contents:s})}else s={isDevice:!1,url:i};var a=k.createFile(e,t,s,r,n),u=(s.contents?a.contents=s.contents:s.url&&(a.contents=null,a.url=s.url),Object.defineProperties(a,{usedBytes:{get:function(){return this.contents.length}}}),{});function l(e,t,r,n,o){var s=e.node.contents;if(o>=s.length)return 0;var i=Math.min(s.length-o,n);if(s.slice)for(var a=0;a<i;a++)t[r+a]=s[o+a];else for(a=0;a<i;a++)t[r+a]=s.get(o+a);return i}return Object.keys(a.stream_ops).forEach(e=>{var t=a.stream_ops[e];u[e]=function(){return k.forceLoadFile(a),t.apply(null,arguments)}}),u.read=(e,t,r,n,o)=>(k.forceLoadFile(a),l(e,t,r,n,o)),u.mmap=(e,t,r,n,o)=>{k.forceLoadFile(a);var s=pe(t);if(s)return l(e,d,s,t,r),{ptr:s,allocated:!0};throw new k.ErrnoError(48)},a.stream_ops=u,a},createPreloadedFile:(r,n,e,o,s,i,a,u,l,c)=>{var t,d,f,h=n?E.resolve(b.join2(r,n)):r;function p(e){function t(e){c&&c(),u||k.createDataFile(r,n,e,o,s,l),i&&i(),ae()}Browser.handledByPreloadPlugin(e,h,t,()=>{a&&a(),ae()})||t(e)}ie(),"string"==typeof e?(d=a,f="al "+(t=e),m(t,e=>{G(e,'Loading data file "'+t+'" failed (no arrayBuffer).'),p(new Uint8Array(e)),f&&ae()},e=>{if(!d)throw'Loading data file "'+t+'" failed.';d()}),f&&ie()):p(e)},indexedDB:()=>window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB,DB_NAME:()=>"EM_FS_"+window.location.pathname,DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:(i,a,u)=>{a=a||(()=>{}),u=u||(()=>{});var e=k.indexedDB();try{var l=e.open(k.DB_NAME(),k.DB_VERSION)}catch(i){return u(i)}l.onupgradeneeded=()=>{q("creating db"),l.result.createObjectStore(k.DB_STORE_NAME)},l.onsuccess=()=>{var e=l.result.transaction([k.DB_STORE_NAME],"readwrite"),t=e.objectStore(k.DB_STORE_NAME),r=0,n=0,o=i.length;function s(){(0==n?a:u)()}i.forEach(e=>{e=t.put(k.analyzePath(e).object.contents,e);e.onsuccess=()=>{++r+n==o&&s()},e.onerror=()=>{r+ ++n==o&&s()}}),e.onerror=u},l.onerror=u},loadFilesFromDB:(a,u,l)=>{u=u||(()=>{}),l=l||(()=>{});var e=k.indexedDB();try{var c=e.open(k.DB_NAME(),k.DB_VERSION)}catch(a){return l(a)}c.onupgradeneeded=l,c.onsuccess=()=>{var e=c.result;try{var t=e.transaction([k.DB_STORE_NAME],"readonly")}catch(e){return void l(e)}var r=t.objectStore(k.DB_STORE_NAME),n=0,o=0,s=a.length;function i(){(0==o?u:l)()}a.forEach(e=>{var t=r.get(e);t.onsuccess=()=>{k.analyzePath(e).exists&&k.unlink(e),k.createDataFile(b.dirname(e),b.basename(e),t.result,!0,!0,!0),++n+o==s&&i()},t.onerror=()=>{n+ ++o==s&&i()}}),t.onerror=l},c.onerror=l}},S={DEFAULT_POLLMASK:5,calculateAt:function(e,t,r){if(b.isAbs(t))return t;var n;if(-100===e)n=k.cwd();else{e=k.getStream(e);if(!e)throw new k.ErrnoError(8);n=e.path}if(0!=t.length)return b.join2(n,t);if(r)return n;throw new k.ErrnoError(44)},doStat:function(e,t,r){try{var n=e(t)}catch(e){if(e&&e.node&&b.normalize(t)!==b.normalize(k.getPath(e.node)))return-54;throw e}return l[r>>2]=n.dev,l[r+4>>2]=0,l[r+8>>2]=n.ino,l[r+12>>2]=n.mode,l[r+16>>2]=n.nlink,l[r+20>>2]=n.uid,l[r+24>>2]=n.gid,l[r+28>>2]=n.rdev,l[r+32>>2]=0,w=[n.size>>>0,(g=n.size,1<=+Math.abs(g)?0<g?(0|Math.min(+Math.floor(g/4294967296),4294967295))>>>0:~~+Math.ceil((g-(~~g>>>0))/4294967296)>>>0:0)],l[r+40>>2]=w[0],l[r+44>>2]=w[1],l[r+48>>2]=4096,l[r+52>>2]=n.blocks,w=[Math.floor(n.atime.getTime()/1e3)>>>0,(g=Math.floor(n.atime.getTime()/1e3),1<=+Math.abs(g)?0<g?(0|Math.min(+Math.floor(g/4294967296),4294967295))>>>0:~~+Math.ceil((g-(~~g>>>0))/4294967296)>>>0:0)],l[r+56>>2]=w[0],l[r+60>>2]=w[1],l[r+64>>2]=0,w=[Math.floor(n.mtime.getTime()/1e3)>>>0,(g=Math.floor(n.mtime.getTime()/1e3),1<=+Math.abs(g)?0<g?(0|Math.min(+Math.floor(g/4294967296),4294967295))>>>0:~~+Math.ceil((g-(~~g>>>0))/4294967296)>>>0:0)],l[r+72>>2]=w[0],l[r+76>>2]=w[1],l[r+80>>2]=0,w=[Math.floor(n.ctime.getTime()/1e3)>>>0,(g=Math.floor(n.ctime.getTime()/1e3),1<=+Math.abs(g)?0<g?(0|Math.min(+Math.floor(g/4294967296),4294967295))>>>0:~~+Math.ceil((g-(~~g>>>0))/4294967296)>>>0:0)],l[r+88>>2]=w[0],l[r+92>>2]=w[1],l[r+96>>2]=0,w=[n.ino>>>0,(g=n.ino,1<=+Math.abs(g)?0<g?(0|Math.min(+Math.floor(g/4294967296),4294967295))>>>0:~~+Math.ceil((g-(~~g>>>0))/4294967296)>>>0:0)],l[r+104>>2]=w[0],l[r+108>>2]=w[1],0},doMsync:function(e,t,r,n,o){e=f.slice(e,e+r);k.msync(t,e,o,r,n)},varargs:void 0,get:function(){return S.varargs+=4,l[S.varargs-4>>2]},getStr:function(e){return ee(e)},getStreamFromFD:function(e){e=k.getStream(e);if(e)return e;throw new k.ErrnoError(8)}};function me(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}var ye=void 0;function D(e){for(var t="",r=e;f[r];)t+=ye[f[r++]];return t}var A={},C={},ge={};function we(e){var t;return void 0===e?"_unknown":48<=(t=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0))&&t<=57?"_"+e:e}function ve(e,t){return e=we(e),new Function("body","return function "+e+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(t)}function be(e,t){var r=ve(t,function(e){this.name=t,this.message=e;e=new Error(e).stack;void 0!==e&&(this.stack=this.toString()+"\n"+e.replace(/^Error(:[^\n]*)?\n/,""))});return r.prototype=Object.create(e.prototype),(r.prototype.constructor=r).prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},r}var P=void 0;function F(e){throw new P(e)}var Ee=void 0;function _e(e){throw new Ee(e)}function ke(n,t,o){function r(e){var t=o(e);t.length!==n.length&&_e("Mismatched type converter count");for(var r=0;r<n.length;++r)T(n[r],t[r])}n.forEach(function(e){ge[e]=t});var s=new Array(t.length),i=[],a=0;t.forEach((e,t)=>{C.hasOwnProperty(e)?s[t]=C[e]:(i.push(e),A.hasOwnProperty(e)||(A[e]=[]),A[e].push(()=>{s[t]=C[e],++a===i.length&&r(s)}))}),0===i.length&&r(s)}function T(e,t,r){r=2<arguments.length&&void 0!==r?r:{};if(!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");var n=t.name;if(e||F('type "'+n+'" must have a positive integer typeid pointer'),C.hasOwnProperty(e)){if(r.ignoreDuplicateRegistrations)return;F("Cannot register type '"+n+"' twice")}C[e]=t,delete ge[e],A.hasOwnProperty(e)&&(r=A[e],delete A[e],r.forEach(e=>e()))}function Se(e){F(e.$$.ptrType.registeredClass.name+" instance already deleted")}var De=!1;function Ae(e){}function Ce(e){--e.count.value,0===e.count.value&&((e=e).smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr))}var Pe={};var Fe=[];function Te(){for(;Fe.length;){var e=Fe.pop();e.$$.deleteScheduled=!1,e.delete()}}var Me=void 0;var Re={};function Oe(e,t){return t.ptrType&&t.ptr||_e("makeClassHandle requires ptr and ptrType"),!!t.smartPtrType!=!!t.smartPtr&&_e("Both smartPtrType and smartPtr must be specified"),t.count={value:1},$e(Object.create(e,{$$:{value:t}}))}function $e(e){return"undefined"==typeof FinalizationRegistry?($e=e=>e,e):(De=new FinalizationRegistry(e=>{Ce(e.$$)}),Ae=e=>De.unregister(e),($e=e=>{var t=e.$$;return t.smartPtr&&De.register(e,{$$:t},e),e})(e))}function M(){}function xe(e,t,r){var n;void 0===e[t].overloadTable&&(n=e[t],e[t]=function(){return e[t].overloadTable.hasOwnProperty(arguments.length)||F("Function '"+r+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[t].overloadTable+")!"),e[t].overloadTable[arguments.length].apply(this,arguments)},e[t].overloadTable=[],e[t].overloadTable[n.argCount]=n)}function je(e,t,r,n,o,s,i,a){this.name=e,this.constructor=t,this.instancePrototype=r,this.rawDestructor=n,this.baseClass=o,this.getActualType=s,this.upcast=i,this.downcast=a,this.pureVirtualFunctions=[]}function Be(e,t,r){for(;t!==r;)t.upcast||F("Expected null or instance of "+r.name+", got an instance of "+t.name),e=t.upcast(e),t=t.baseClass;return e}function Ue(e,t){if(null===t)return this.isReference&&F("null is not a valid "+this.name),0;t.$$||F('Cannot pass "'+et(t)+'" as a '+this.name),t.$$.ptr||F("Cannot pass deleted object as a pointer of type "+this.name);var r=t.$$.ptrType.registeredClass;return Be(t.$$.ptr,r,this.registeredClass)}function We(e,t){if(null===t)return this.isReference&&F("null is not a valid "+this.name),this.isSmartPointer?(n=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,n),n):0;t.$$||F('Cannot pass "'+et(t)+'" as a '+this.name),t.$$.ptr||F("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&t.$$.ptrType.isConst&&F("Cannot convert argument of type "+(t.$$.smartPtrType||t.$$.ptrType).name+" to parameter type "+this.name);var r,n,o=t.$$.ptrType.registeredClass;if(n=Be(t.$$.ptr,o,this.registeredClass),this.isSmartPointer)switch(void 0===t.$$.smartPtr&&F("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:t.$$.smartPtrType===this?n=t.$$.smartPtr:F("Cannot convert argument of type "+(t.$$.smartPtrType||t.$$.ptrType).name+" to parameter type "+this.name);break;case 1:n=t.$$.smartPtr;break;case 2:t.$$.smartPtrType===this?n=t.$$.smartPtr:(r=t.clone(),n=this.rawShare(n,Ye.toHandle(function(){r.delete()})),null!==e&&e.push(this.rawDestructor,n));break;default:F("Unsupporting sharing policy")}return n}function Ie(e,t){if(null===t)return this.isReference&&F("null is not a valid "+this.name),0;t.$$||F('Cannot pass "'+et(t)+'" as a '+this.name),t.$$.ptr||F("Cannot pass deleted object as a pointer of type "+this.name),t.$$.ptrType.isConst&&F("Cannot convert argument of type "+t.$$.ptrType.name+" to parameter type "+this.name);var r=t.$$.ptrType.registeredClass;return Be(t.$$.ptr,r,this.registeredClass)}function Le(e){return this.fromWireType(l[e>>2])}function R(e,t,r,n,o,s,i,a,u,l,c){this.name=e,this.registeredClass=t,this.isReference=r,this.isConst=n,this.isSmartPointer=o,this.pointeeType=s,this.sharingPolicy=i,this.rawGetPointee=a,this.rawConstructor=u,this.rawShare=l,this.rawDestructor=c,o||void 0!==t.baseClass?this.toWireType=We:(this.toWireType=n?Ue:Ie,this.destructorFunction=null)}var ze=[];function Ne(e){var t=ze[e];return t||(e>=ze.length&&(ze.length=e+1),ze[e]=t=Q.get(e)),t}function O(e,t){var o,s,i,r=(e=D(e)).includes("j")?(o=e,s=t,i=[],function(){return i.length=0,Object.assign(i,arguments),t=s,r=i,(e=o).includes("j")?(n=t,e=y["dynCall_"+e],r&&r.length?e.apply(null,[n].concat(r)):e.call(null,n)):Ne(t).apply(null,r);var e,t,r,n}):Ne(t);return"function"!=typeof r&&F("unknown function pointer with signature "+e+": "+t),r}var He=void 0;function qe(e){var e=wt(e),t=D(e);return x(e),t}function Ve(e,t){var r=[],n={};throw t.forEach(function e(t){n[t]||C[t]||(ge[t]?ge[t].forEach(e):(r.push(t),n[t]=!0))}),new He(e+": "+r.map(qe).join([", "]))}function Ge(e,t){for(var r=[],n=0;n<e;n++)r.push(h[t+4*n>>2]);return r}function Xe(e){for(;e.length;){var t=e.pop();e.pop()(t)}}function Je(e,t){var r;if(e instanceof Function)return(r=ve(e.name||"unknownFunctionName",function(){})).prototype=e.prototype,r=new r,(t=e.apply(r,t))instanceof Object?t:r;throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function")}function Ke(e,t,r,n,o){var s=t.length;s<2&&F("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var r=null!==t[1]&&null!==r,i=!1,a=1;a<t.length;++a)if(null!==t[a]&&void 0===t[a].destructorFunction){i=!0;break}for(var u="void"!==t[0].name,l="",c="",a=0;a<s-2;++a)l+=(0!==a?", ":"")+"arg"+a,c+=(0!==a?", ":"")+"arg"+a+"Wired";var d="return function "+we(e)+"("+l+") {\nif (arguments.length !== "+(s-2)+") {\nthrowBindingError('function "+e+" called with ' + arguments.length + ' arguments, expected "+(s-2)+" args!');\n}\n",f=(i&&(d+="var destructors = [];\n"),i?"destructors":"null"),h=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],p=[F,n,o,Xe,t[0],t[1]];for(r&&(d+="var thisWired = classParam.toWireType("+f+", this);\n"),a=0;a<s-2;++a)d+="var arg"+a+"Wired = argType"+a+".toWireType("+f+", arg"+a+"); // "+t[a+2].name+"\n",h.push("argType"+a),p.push(t[a+2]);if(d+=(u?"var rv = ":"")+"invoker(fn"+(0<(c=r?"thisWired"+(0<c.length?", ":"")+c:c).length?", ":"")+c+");\n",i)d+="runDestructors(destructors);\n";else for(a=r?1:2;a<t.length;++a){var m=1===a?"thisWired":"arg"+(a-2)+"Wired";null!==t[a].destructorFunction&&(d+=m+"_dtor("+m+"); // "+t[a].name+"\n",h.push(m+"_dtor"),p.push(t[a].destructorFunction))}return u&&(d+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),h.push(d+="}\n"),Je(Function,h).apply(null,p)}var Ze=[],$=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function Qe(e){4<e&&0==--$[e].refcount&&($[e]=void 0,Ze.push(e))}var Ye={toValue:e=>(e||F("Cannot use deleted val. handle = "+e),$[e].value),toHandle:e=>{switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var t=Ze.length?Ze.pop():$.length;return $[t]={refcount:1,value:e},t}}};function et(e){var t;return null===e?"null":"object"==(t=typeof e)||"array"==t||"function"==t?e.toString():""+e}var tt="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function rt(e,t){for(var r,n=e>>1,o=n+t/2;!(o<=n)&&J[n];)++n;if(32<(r=n<<1)-e&&tt)return tt.decode(f.subarray(e,r));for(var s="",i=0;!(t/2<=i);++i){var a=u[e+2*i>>1];if(0==a)break;s+=String.fromCharCode(a)}return s}function nt(e,t,r){if((r=void 0===r?2147483647:r)<2)return 0;for(var n=t,o=(r-=2)<2*e.length?r/2:e.length,s=0;s<o;++s){var i=e.charCodeAt(s);u[t>>1]=i,t+=2}return u[t>>1]=0,t-n}function ot(e){return 2*e.length}function st(e,t){for(var r=0,n="";!(t/4<=r);){var o,s=l[e+4*r>>2];if(0==s)break;++r,65536<=s?(o=s-65536,n+=String.fromCharCode(55296|o>>10,56320|1023&o)):n+=String.fromCharCode(s)}return n}function it(e,t,r){if((r=void 0===r?2147483647:r)<4)return 0;for(var n=t,o=n+r-4,s=0;s<e.length;++s){var i=e.charCodeAt(s);if(55296<=i&&i<=57343&&(i=65536+((1023&i)<<10)|1023&e.charCodeAt(++s)),l[t>>2]=i,(t+=4)+4>o)break}return l[t>>2]=0,t-n}function at(e){for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);55296<=n&&n<=57343&&++r,t+=4}return t}var ut={},lt=[],ct=[],dt={};function ft(){if(!ft.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:z||"./this.program"};for(t in dt)void 0===dt[t]?delete e[t]:e[t]=dt[t];var t,r=[];for(t in e)r.push(t+"="+e[t]);ft.strings=r}return ft.strings}function ht(e,t,r,n){this.parent=e=e||this,this.mount=e.mount,this.mounted=null,this.id=k.nextInode++,this.name=t,this.mode=r,this.node_ops={},this.stream_ops={},this.rdev=n}Object.defineProperties(ht.prototype,{read:{get:function(){return 365==(365&this.mode)},set:function(e){e?this.mode|=365:this.mode&=-366}},write:{get:function(){return 146==(146&this.mode)},set:function(e){e?this.mode|=146:this.mode&=-147}},isFolder:{get:function(){return k.isDir(this.mode)}},isDevice:{get:function(){return k.isChrdev(this.mode)}}}),k.FSNode=ht,k.staticInit();for(var pt=new Array(256),mt=0;mt<256;++mt)pt[mt]=String.fromCharCode(mt);ye=pt,P=y.BindingError=be(Error,"BindingError"),Ee=y.InternalError=be(Error,"InternalError"),M.prototype.isAliasOf=function(e){if(!(this instanceof M))return!1;if(!(e instanceof M))return!1;for(var t=this.$$.ptrType.registeredClass,r=this.$$.ptr,n=e.$$.ptrType.registeredClass,o=e.$$.ptr;t.baseClass;)r=t.upcast(r),t=t.baseClass;for(;n.baseClass;)o=n.upcast(o),n=n.baseClass;return t===n&&r===o},M.prototype.clone=function(){var e;return this.$$.ptr||Se(this),this.$$.preservePointerOnDelete?(this.$$.count.value+=1,this):((e=$e(Object.create(Object.getPrototypeOf(this),{$$:{value:{count:(e=this.$$).count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType}}}))).$$.count.value+=1,e.$$.deleteScheduled=!1,e)},M.prototype.delete=function(){this.$$.ptr||Se(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&F("Object already scheduled for deletion"),Ae(this),Ce(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)},M.prototype.isDeleted=function(){return!this.$$.ptr},M.prototype.deleteLater=function(){return this.$$.ptr||Se(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&F("Object already scheduled for deletion"),Fe.push(this),1===Fe.length&&Me&&Me(Te),this.$$.deleteScheduled=!0,this},y.getInheritedInstanceCount=function(){return Object.keys(Re).length},y.getLiveInheritedInstances=function(){var e,t=[];for(e in Re)Re.hasOwnProperty(e)&&t.push(Re[e]);return t},y.flushPendingDeletes=Te,y.setDelayFunction=function(e){Me=e,Fe.length&&Me&&Me(Te)},R.prototype.getPointee=function(e){return e=this.rawGetPointee?this.rawGetPointee(e):e},R.prototype.destructor=function(e){this.rawDestructor&&this.rawDestructor(e)},R.prototype.argPackAdvance=8,R.prototype.readValueFromPointer=Le,R.prototype.deleteObject=function(e){null!==e&&e.delete()},R.prototype.fromWireType=function(e){var t,r,n=this.getPointee(e);return n?(t=function(e,t){for(void 0===t&&F("ptr should not be undefined");e.baseClass;)t=e.upcast(t),e=e.baseClass;return t}(t=this.registeredClass,t=n),void 0!==(t=Re[t])?0===t.$$.count.value?(t.$$.ptr=n,t.$$.smartPtr=e,t.clone()):(t=t.clone(),this.destructor(e),t):(t=this.registeredClass.getActualType(n),!(t=Pe[t])||(t=this.isConst?t.constPointerType:t.pointerType,null===(r=function e(t,r,n){return r===n?t:void 0===n.baseClass||null===(t=e(t,r,n.baseClass))?null:n.downcast(t)}(n,this.registeredClass,t.registeredClass)))?o.call(this):this.isSmartPointer?Oe(t.registeredClass.instancePrototype,{ptrType:t,ptr:r,smartPtrType:this,smartPtr:e}):Oe(t.registeredClass.instancePrototype,{ptrType:t,ptr:r}))):(this.destructor(e),null);function o(){return this.isSmartPointer?Oe(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:n,smartPtrType:this,smartPtr:e}):Oe(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}},He=y.UnboundTypeError=be(Error,"UnboundTypeError"),y.count_emval_handles=function(){for(var e=0,t=5;t<$.length;++t)void 0!==$[t]&&++e;return e},y.get_first_emval=function(){for(var e=5;e<$.length;++e)if(void 0!==$[e])return $[e];return null};var yt={q:function(e){return bt(e+24)+24},p:function(e,t,r){throw new fe(e).init(t,r),e},C:function(e,t,r){S.varargs=r;try{var n=S.getStreamFromFD(e);switch(t){case 0:return(o=S.get())<0?-28:k.createStream(n,o).fd;case 1:case 2:case 6:case 7:return 0;case 3:return n.flags;case 4:var o=S.get();return n.flags|=o,0;case 5:return o=S.get(),u[o+0>>1]=2,0;case 16:case 8:default:return-28;case 9:return l[gt()>>2]=28,-1}}catch(e){if(void 0!==k&&e instanceof k.ErrnoError)return-e.errno;throw e}},w:function(e,t,r,n){S.varargs=n;try{t=S.getStr(t),t=S.calculateAt(e,t);var o=n?S.get():0;return k.open(t,r,o).fd}catch(e){if(void 0!==k&&e instanceof k.ErrnoError)return-e.errno;throw e}},u:function(e,t,r,n,o){},E:function(e,r,n,o,s){var i=me(n);T(e,{name:r=D(r),fromWireType:function(e){return!!e},toWireType:function(e,t){return t?o:s},argPackAdvance:8,readValueFromPointer:function(e){var t;if(1===n)t=d;else if(2===n)t=u;else{if(4!==n)throw new TypeError("Unknown boolean type size: "+r);t=l}return this.fromWireType(t[e>>i])},destructorFunction:null})},t:function(u,e,t,l,r,c,n,d,o,f,h,s,p){h=D(h),c=O(r,c),d=d&&O(n,d),f=f&&O(o,f),p=O(s,p);var i,m=we(h);r=m,n=function(){Ve("Cannot construct "+h+" due to unbound types",[l])},y.hasOwnProperty(r)?(F("Cannot register public name '"+r+"' twice"),xe(y,r,r),y.hasOwnProperty(i)&&F("Cannot register multiple overloads of a function with the same number of arguments ("+i+")!"),y[r].overloadTable[i]=n):y[r]=n,ke([u,e,t],l?[l]:[],function(e){e=e[0],e=l?(i=e.registeredClass).instancePrototype:M.prototype;var t,r,n=ve(m,function(){if(Object.getPrototypeOf(this)!==o)throw new P("Use 'new' to construct "+h);if(void 0===s.constructor_body)throw new P(h+" has no accessible constructor");var e=s.constructor_body[arguments.length];if(void 0===e)throw new P("Tried to invoke ctor of "+h+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(s.constructor_body).toString()+") parameters instead!");return e.apply(this,arguments)}),o=Object.create(e,{constructor:{value:n}}),s=(n.prototype=o,new je(h,n,o,p,i,c,d,f)),e=new R(h,s,!0,!1,!1),i=new R(h+"*",s,!1,!1,!1),a=new R(h+" const*",s,!1,!0,!1);return Pe[u]={pointerType:i,constPointerType:a},t=m,n=n,y.hasOwnProperty(t)||_e("Replacing nonexistant public symbol"),y[t].overloadTable,y[t]=n,y[t].argCount=r,[e,i,a]})},r:function(e,n,t,r,o,s){G(0<n);var i=Ge(n,t);o=O(r,o),ke([],[e],function(t){var r="constructor "+(t=t[0]).name;if(void 0===t.registeredClass.constructor_body&&(t.registeredClass.constructor_body=[]),void 0!==t.registeredClass.constructor_body[n-1])throw new P("Cannot register multiple constructors with identical number of parameters ("+(n-1)+") for class '"+t.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return t.registeredClass.constructor_body[n-1]=()=>{Ve("Cannot construct "+t.name+" due to unbound types",i)},ke([],i,function(e){return e.splice(1,0,null),t.registeredClass.constructor_body[n-1]=Ke(r,e,null,o,s),[]}),[]})},d:function(e,s,i,t,r,a,u,l){var c=Ge(i,t);s=D(s),a=O(r,a),ke([],[e],function(t){var r=(t=t[0]).name+"."+s;function e(){Ve("Cannot call "+r+" due to unbound types",c)}s.startsWith("@@")&&(s=Symbol[s.substring(2)]),l&&t.registeredClass.pureVirtualFunctions.push(s);var n=t.registeredClass.instancePrototype,o=n[s];return void 0===o||void 0===o.overloadTable&&o.className!==t.name&&o.argCount===i-2?(e.argCount=i-2,e.className=t.name,n[s]=e):(xe(n,s,r),n[s].overloadTable[i-2]=e),ke([],c,function(e){e=Ke(r,e,t,a,u);return void 0===n[s].overloadTable?(e.argCount=i-2,n[s]=e):n[s].overloadTable[i-2]=e,[]}),[]})},D:function(e,t){T(e,{name:t=D(t),fromWireType:function(e){var t=Ye.toValue(e);return Qe(e),t},toWireType:function(e,t){return Ye.toHandle(t)},argPackAdvance:8,readValueFromPointer:Le,destructorFunction:null})},n:function(e,t,r){r=me(r);T(e,{name:t=D(t),fromWireType:function(e){return e},toWireType:function(e,t){return t},argPackAdvance:8,readValueFromPointer:function(e,t){switch(t){case 2:return function(e){return this.fromWireType(K[e>>2])};case 3:return function(e){return this.fromWireType(Z[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}(t,r),destructorFunction:null})},c:function(e,t,r,n,o){t=D(t);var s,i=me(r),a=e=>e,r=(0===n&&(s=32-8*r,a=e=>e<<s>>>s),t.includes("unsigned"));T(e,{name:t,fromWireType:a,toWireType:r?function(e,t){return this.name,t>>>0}:function(e,t){return this.name,t},argPackAdvance:8,readValueFromPointer:function(e,t,r){switch(t){case 0:return r?function(e){return d[e]}:function(e){return f[e]};case 1:return r?function(e){return u[e>>1]}:function(e){return J[e>>1]};case 2:return r?function(e){return l[e>>2]}:function(e){return h[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}(t,i,0!==n),destructorFunction:null})},b:function(e,t,r){var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];function o(e){var t=h,r=t[e>>=2],t=t[e+1];return new n(X,t,r)}T(e,{name:r=D(r),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},m:function(e,t){var l="std::string"===(t=D(t));T(e,{name:t,fromWireType:function(e){var t,r=h[e>>2],n=e+4;if(l)for(var o=n,s=0;s<=r;++s){var i,a=n+s;s!=r&&0!=f[a]||(i=ee(o,a-o),void 0===t?t=i:t=t+String.fromCharCode(0)+i,o=a+1)}else{for(var u=new Array(r),s=0;s<r;++s)u[s]=String.fromCharCode(f[n+s]);t=u.join("")}return x(e),t},toWireType:function(e,t){var r,n="string"==typeof(t=t instanceof ArrayBuffer?new Uint8Array(t):t),o=(n||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int8Array||F("Cannot pass non-string to std::string"),r=l&&n?re(t):t.length,bt(4+r+1)),s=o+4;if(h[o>>2]=r,l&&n)te(t,f,s,r+1);else if(n)for(var i=0;i<r;++i){var a=t.charCodeAt(i);255<a&&(x(s),F("String has UTF-16 code units that do not fit in 8 bits")),f[s+i]=a}else for(i=0;i<r;++i)f[s+i]=t[i];return null!==e&&e.push(x,o),o},argPackAdvance:8,readValueFromPointer:Le,destructorFunction:function(e){x(e)}})},h:function(e,u,o){var l,s,c,i,d;o=D(o),2===u?(l=rt,s=nt,i=ot,c=()=>J,d=1):4===u&&(l=st,s=it,i=at,c=()=>h,d=2),T(e,{name:o,fromWireType:function(e){for(var t,r=h[e>>2],n=c(),o=e+4,s=0;s<=r;++s){var i,a=e+4+s*u;s!=r&&0!=n[a>>d]||(i=l(o,a-o),void 0===t?t=i:t=t+String.fromCharCode(0)+i,o=a+u)}return x(e),t},toWireType:function(e,t){"string"!=typeof t&&F("Cannot pass non-string to C++ string type "+o);var r=i(t),n=bt(4+r+u);return h[n>>2]=r>>d,s(t,n+4,r+u),null!==e&&e.push(x,n),n},argPackAdvance:8,readValueFromPointer:Le,destructorFunction:function(e){x(e)}})},o:function(e,t){T(e,{isVoid:!0,name:t=D(t),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,t){}})},f:function(){return Date.now()},g:function(e,t,r,n){(e=lt[e])(t=Ye.toValue(t),r=void 0===(t=ut[e=r])?D(e):t,null,n)},j:Qe,i:function(e,t){var r=function(e,t){for(var r,n,o=new Array(e),s=0;s<e;++s)o[s]=(r=h[t+4*s>>2],void 0===(n=C[r])&&F("parameter "+s+" has unknown type "+qe(r)),n);return o}(e,t),t=r[0],n=t.name+"_$"+r.slice(1).map(function(e){return e.name}).join("_")+"$",o=ct[n];if(void 0===o){for(var s=["retType"],i=[t],a="",u=0;u<e-1;++u)a+=(0!==u?", ":"")+"arg"+u,s.push("argType"+u),i.push(r[1+u]);for(var l="return function "+we("methodCaller_"+n)+"(handle, name, destructors, args) {\n",c=0,u=0;u<e-1;++u)l+="    var arg"+u+" = argType"+u+".readValueFromPointer(args"+(c?"+"+c:"")+");\n",c+=r[u+1].argPackAdvance;for(l+="    var rv = handle[name]("+a+");\n",u=0;u<e-1;++u)r[u+1].deleteObject&&(l+="    argType"+u+".deleteObject(arg"+u+");\n");t.isVoid||(l+="    return retType.toWireType(destructors, rv);\n"),s.push(l+="};\n");var t=Je(Function,s).apply(null,i),d=lt.length;lt.push(t),ct[n]=o=d}return o},a:function(){v("")},A:function(e,t,r){f.copyWithin(e,t,t+r)},v:function(e){f.length,v("OOM")},y:function(i,a){var u=0;return ft().forEach(function(e,t){for(var r=a+u,n=(h[i+4*t>>2]=r,e),o=r,s=0;s<n.length;++s)d[o++>>0]=n.charCodeAt(s);d[o>>0]=0,u+=e.length+1}),0},z:function(e,t){var r=ft(),n=(h[e>>2]=r.length,0);return r.forEach(function(e){n+=e.length+1}),h[t>>2]=n,0},l:function(e){try{var t=S.getStreamFromFD(e);return k.close(t),0}catch(e){if(void 0!==k&&e instanceof k.ErrnoError)return e.errno;throw e}},x:function(e,t){try{var r=S.getStreamFromFD(e),n=r.tty?2:k.isDir(r.mode)?3:k.isLink(r.mode)?7:4;return d[t>>0]=n,0}catch(e){if(void 0!==k&&e instanceof k.ErrnoError)return e.errno;throw e}},B:function(e,t,r,n){try{var o=function(e,t,r){for(var n=0,o=0;o<r;o++){var s=h[t>>2],i=h[t+4>>2],s=(t+=8,k.read(e,d,s,i,void 0));if(s<0)return-1;if(n+=s,s<i)break}return n}(S.getStreamFromFD(e),t,r);return l[n>>2]=o,0}catch(e){if(void 0!==k&&e instanceof k.ErrnoError)return e.errno;throw e}},s:function(e,t,r,n,o){try{var s,i=r+2097152>>>0<4194305-!!t?(t>>>0)+4294967296*r:NaN;return isNaN(i)?61:(s=S.getStreamFromFD(e),k.llseek(s,i,n),w=[s.position>>>0,(g=s.position,1<=+Math.abs(g)?0<g?(0|Math.min(+Math.floor(g/4294967296),4294967295))>>>0:~~+Math.ceil((g-(~~g>>>0))/4294967296)>>>0:0)],l[o>>2]=w[0],l[o+4>>2]=w[1],s.getdents&&0===i&&0===n&&(s.getdents=null),0)}catch(e){if(void 0!==k&&e instanceof k.ErrnoError)return e.errno;throw e}},k:function(e,t,r,n){try{var o=function(e,t,r){for(var n=0,o=0;o<r;o++){var s=h[t>>2],i=h[t+4>>2],s=(t+=8,k.write(e,d,s,i,void 0));if(s<0)return-1;n+=s}return n}(S.getStreamFromFD(e),t,r);return h[n>>2]=o,0}catch(e){if(void 0!==k&&e instanceof k.ErrnoError)return e.errno;throw e}},e:function(e){}},x=(!function(){var t={a:yt};function r(e,t){var e=e.exports;y.asm=e,e=y.asm.F.buffer,X=e,y.HEAP8=d=new Int8Array(e),y.HEAP16=u=new Int16Array(e),y.HEAP32=l=new Int32Array(e),y.HEAPU8=f=new Uint8Array(e),y.HEAPU16=J=new Uint16Array(e),y.HEAPU32=h=new Uint32Array(e),y.HEAPF32=K=new Float32Array(e),y.HEAPF64=Z=new Float64Array(e),Q=y.asm.I,e=y.asm.G,oe.unshift(e),ae()}function n(e){r(e.instance)}function o(e){return function(){if(!s&&(N||c)){if("function"==typeof fetch&&!le(p))return fetch(p,{credentials:"same-origin"}).then(function(e){if(e.ok)return e.arrayBuffer();throw"failed to load wasm binary file at '"+p+"'"}).catch(function(){return ce(p)});if(m)return new Promise(function(t,e){m(p,function(e){t(new Uint8Array(e))},e)})}return Promise.resolve().then(function(){return ce(p)})}().then(function(e){return WebAssembly.instantiate(e,t)}).then(function(e){return e}).then(e,function(e){a("failed to asynchronously prepare wasm: "+e),v(e)})}if(ie(),y.instantiateWasm)try{return y.instantiateWasm(t,r)}catch(t){return a("Module.instantiateWasm callback failed with error: "+t)}s||"function"!=typeof WebAssembly.instantiateStreaming||ue(p)||le(p)||H||"function"!=typeof fetch?o(n):fetch(p,{credentials:"same-origin"}).then(function(e){return WebAssembly.instantiateStreaming(e,t).then(n,function(e){return a("wasm streaming compile failed: "+e),a("falling back to ArrayBuffer instantiation"),o(n)})})}(),y.___wasm_call_ctors=function(){return(y.___wasm_call_ctors=y.asm.G).apply(null,arguments)},y._free=function(){return(x=y._free=y.asm.H).apply(null,arguments)}),gt=y.___errno_location=function(){return(gt=y.___errno_location=y.asm.J).apply(null,arguments)},wt=y.___getTypeName=function(){return(wt=y.___getTypeName=y.asm.K).apply(null,arguments)};y.___embind_register_native_and_builtin_types=function(){return(y.___embind_register_native_and_builtin_types=y.asm.L).apply(null,arguments)};var vt,bt=y._malloc=function(){return(bt=y._malloc=y.asm.M).apply(null,arguments)},Et=y._emscripten_builtin_memalign=function(){return(Et=y._emscripten_builtin_memalign=y.asm.N).apply(null,arguments)},_t=y.___cxa_is_pointer_type=function(){return(_t=y.___cxa_is_pointer_type=y.asm.O).apply(null,arguments)};function kt(){function e(){if(!vt&&(vt=!0,y.calledRun=!0,!V)){if(y.noFSInit||k.init.initialized||k.init(),k.ignorePermissions=!1,de(oe),y.onRuntimeInitialized&&y.onRuntimeInitialized(),y.postRun)for("function"==typeof y.postRun&&(y.postRun=[y.postRun]);y.postRun.length;)e=y.postRun.shift(),se.unshift(e);var e;de(se)}}0<r||(function(){if(y.preRun)for("function"==typeof y.preRun&&(y.preRun=[y.preRun]);y.preRun.length;)e=y.preRun.shift(),ne.unshift(e);var e;de(ne)}(),0<r)||(y.setStatus?(y.setStatus("Running..."),setTimeout(function(){setTimeout(function(){y.setStatus("")},1),e()},1)):e())}if(y.dynCall_viiijj=function(){return(y.dynCall_viiijj=y.asm.P).apply(null,arguments)},y.dynCall_jij=function(){return(y.dynCall_jij=y.asm.Q).apply(null,arguments)},y.dynCall_jii=function(){return(y.dynCall_jii=y.asm.R).apply(null,arguments)},y.dynCall_jiji=function(){return(y.dynCall_jiji=y.asm.S).apply(null,arguments)},n=function e(){vt||kt(),vt||(n=e)},y.preInit)for("function"==typeof y.preInit&&(y.preInit=[y.preInit]);0<y.preInit.length;)y.preInit.pop()();kt(),j.exports=y});const b="fetch",a="debug",u="warn",E={playType:"player",container:"",videoBuffer:1e3,videoBufferDelay:1e3,networkDelay:1e4,isResize:!0,isFullResize:!1,isFlv:!1,isHls:!1,isFmp4:!1,isFmp4Private:!1,isWebrtc:!1,isWebrtcForZLM:!1,isWebrtcForSRS:!1,isWebrtcForOthers:!1,isNakedFlow:!1,isMpeg4:!1,debug:!1,debugLevel:u,debugUuid:"",isMulti:!0,multiIndex:-1,hotKey:!1,loadingTimeout:10,heartTimeout:10,timeout:10,pageVisibilityHiddenTimeout:300,loadingTimeoutReplay:!0,heartTimeoutReplay:!0,loadingTimeoutReplayTimes:3,heartTimeoutReplayTimes:3,heartTimeoutReplayUseLastFrameShow:!1,replayUseLastFrameShow:!1,supportDblclickFullscreen:!1,showBandwidth:!1,showPerformance:!1,mseCorrectTimeDuration:20,keepScreenOn:!0,isNotMute:!1,hasAudio:!0,hasVideo:!0,operateBtns:{fullscreen:!1,screenshot:!1,play:!1,audio:!1,record:!1,ptz:!1,quality:!1,zoom:!1,close:!1,scale:!1,performance:!1,logSave:!1,aiFace:!1,aiObject:!1,fullscreenFn:null,fullscreenExitFn:null,screenshotFn:null,playFn:null,pauseFn:null,recordFn:null,recordStopFn:null},extendOperateBtns:[],contextmenuBtns:[],watermarkConfig:{},controlAutoHide:!1,hasControl:!1,loadingIcon:!0,loadingText:"",background:"",backgroundLoadingShow:!1,loadingBackground:"",loadingBackgroundWidth:0,loadingBackgroundHeight:0,decoder:"decoder-pro.js",decoderAudio:"decoder-pro-audio.js",decoderHard:"decoder-pro-hard.js",wasmMp4RecorderDecoder:"jessibuca-pro-mp4-recorder-decoder.js",decoderWASM:"",isDecoderUseCDN:!1,url:"",rotate:0,mirrorRotate:"none",aspectRatio:"default",playbackConfig:{playList:[],fps:"",showControl:!0,controlType:"normal",duration:0,startTime:"",showRateBtn:!1,rateConfig:[],showPrecision:"",showPrecisionBtn:!0,isCacheBeforeDecodeForFpsRender:!1,uiUsePlaybackPause:!1,isPlaybackPauseClearCache:!0,isUseFpsRender:!1,isUseLocalCalculateTime:!1,localOneFrameTimestamp:40,supportWheel:!1,useWCS:!1,useMSE:!1},qualityConfig:[],defaultStreamQuality:"",scaleConfig:["拉伸","缩放","正常"],forceNoOffscreen:!0,hiddenAutoPause:!1,protocol:2,demuxType:"flv",useWasm:!1,useMSE:!1,useWCS:!1,useSIMD:!0,wcsUseVideoRender:!0,wcsUseWebgl2Render:!0,wasmUseVideoRender:!0,mseUseCanvasRender:!1,hlsUseCanvasRender:!1,webrtcUseCanvasRender:!1,useOffscreen:!1,useWebGPU:!1,mseDecodeErrorReplay:!0,wcsDecodeErrorReplay:!0,wasmDecodeErrorReplay:!0,simdDecodeErrorReplay:!0,autoWasm:!0,webglAlignmentErrorReplay:!0,webglContextLostErrorReplay:!0,openWebglAlignment:!1,syncAudioAndVideo:!1,syncAudioAndVideoDiff:500,playbackDelayTime:1e3,playbackFps:25,playbackForwardMaxRateDecodeIFrame:4,playbackCurrentTimeMove:!0,useVideoRender:!0,useCanvasRender:!1,networkDelayTimeoutReplay:!1,recordType:"mp4",checkFirstIFrame:!0,nakedFlowFps:25,audioEngine:null,isShowRecordingUI:!0,isShowZoomingUI:!0,useFaceDetector:!1,useObjectDetector:!1,ptzClickType:"click",ptzStopEmitDelay:.3,ptzZoomShow:!1,ptzApertureShow:!1,ptzFocusShow:!1,ptzMoreArrowShow:!1,weiXinInAndroidAudioBufferSize:4800,isCrypto:!1,isM7sCrypto:!1,isSm4Crypto:!1,sm4CryptoKey:"",cryptoKey:"",cryptoIV:"",cryptoKeyUrl:"",autoResize:!1,useWebFullScreen:!1,ptsMaxDiff:3600,aiFaceDetectLevel:2,aiFaceDetectWidth:240,aiFaceDetectShowRect:!0,aiFaceDetectInterval:1e3,aiFaceDetectRectConfig:{},aiObjectDetectLevel:2,aiObjectDetectWidth:240,aiObjectDetectShowRect:!0,aiObjectDetectInterval:1e3,aiObjectDetectRectConfig:{},videoRenderSupportScale:!0,mediaSourceTsIsMaxDiffReplay:!0,controlHtml:"",isH265:!1,isWebrtcH265:!1,supportLockScreenPlayAudio:!0,supportHls265:!1,isEmitSEI:!1,pauseAndNextPlayUseLastFrameShow:!1,demuxUseWorker:!1,playFailedAndPausedReplay:!1,videoElementPlayingFailedReplay:!0,mp4RecordUseWasm:!0,mseAutoCleanupSourceBuffer:!0,mseAutoCleanupMaxBackwardDuration:30,mseAutoCleanupMinBackwardDuration:10,isNewMseDecoder:!1},_="playAudio",k="workerFetch",S="streamEnd",D="streamSuccess",A="fetchError",C=10,P="AbortError",l=0,F="idle",T="buffering",M="complete";function R(e){return e[0]>>4===C&&e[1]===l}function O(){return(performance&&"function"==typeof performance.now?performance:Date).now()}function $(e){return!0===e||"true"===e}function x(e){return!0!==e&&"true"!==e}s(function(e){var i,t,a,r,n;i="undefined"!=typeof window&&void 0!==window.document?window.document:{},t=e.exports,a=function(){for(var e,t=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],r=0,n=t.length,o={};r<n;r++)if((e=t[r])&&e[1]in i){for(r=0;r<e.length;r++)o[t[0][r]]=e[r];return o}return!1}(),r={change:a.fullscreenchange,error:a.fullscreenerror},n={request:function(o,s){return new Promise(function(e,t){var r=function(){this.off("change",r),e()}.bind(this),n=(this.on("change",r),(o=o||i.documentElement)[a.requestFullscreen](s));n instanceof Promise&&n.then(r).catch(t)}.bind(this))},exit:function(){return new Promise(function(e,t){var r,n;this.isFullscreen?(r=function(){this.off("change",r),e()}.bind(this),this.on("change",r),(n=i[a.exitFullscreen]())instanceof Promise&&n.then(r).catch(t)):e()}.bind(this))},toggle:function(e,t){return this.isFullscreen?this.exit():this.request(e,t)},onchange:function(e){this.on("change",e)},onerror:function(e){this.on("error",e)},on:function(e,t){e=r[e];e&&i.addEventListener(e,t,!1)},off:function(e,t){e=r[e];e&&i.removeEventListener(e,t,!1)},raw:a},a?(Object.defineProperties(n,{isFullscreen:{get:function(){return Boolean(i[a.fullscreenElement])}},element:{enumerable:!0,get:function(){return i[a.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return Boolean(i[a.fullscreenEnabled])}}}),t?e.exports=n:window.screenfull=n):t?e.exports={isEnabled:!1}:window.screenfull={isEnabled:!1}}).isEnabled;try{if("object"==typeof WebAssembly&&"function"==typeof WebAssembly.instantiate){var c=new WebAssembly.Module(Uint8Array.of(0,97,115,109,1,0,0,0));if(c instanceof WebAssembly.Module)new WebAssembly.Instance(c)instanceof WebAssembly.Instance}}catch(e){}const d=Symbol(32),f=Symbol(16),h=Symbol(8);class j{constructor(e){this.g=e,this.consumed=0,e&&(this.need=e.next().value)}setG(e){this.g=e,this.demand(e.next().value,!0)}consume(){this.buffer&&this.consumed&&(this.buffer.copyWithin(0,this.consumed),this.buffer=this.buffer.subarray(0,this.buffer.length-this.consumed),this.consumed=0)}demand(e,t){return t&&this.consume(),this.need=e,this.flush()}read(r){return e=this,u=function*(){return this.lastReadPromise&&(yield this.lastReadPromise),this.lastReadPromise=new Promise((t,e)=>{this.reject=e,this.resolve=e=>{delete this.lastReadPromise,delete this.resolve,delete this.need,t(e)},this.demand(r,!0)||null==(e=this.pull)||e.call(this,r)})},new(a=(a=i=void 0)||Promise)(function(r,t){function n(e){try{s(u.next(e))}catch(e){t(e)}}function o(e){try{s(u.throw(e))}catch(e){t(e)}}function s(e){var t;e.done?r(e.value):((t=e.value)instanceof a?t:new a(function(e){e(t)})).then(n,o)}s((u=u.apply(e,i||[])).next())});var e,i,a,u}readU32(){return this.read(d)}readU16(){return this.read(f)}readU8(){return this.read(h)}close(){var e;this.g&&this.g.return(),this.buffer&&this.buffer.subarray(0,0),null!=(e=this.reject)&&e.call(this,new Error("EOF")),delete this.lastReadPromise}flush(){if(this.buffer&&this.need){let e=null;const n=this.buffer.subarray(this.consumed);let t=0;var r=e=>n.length<(t=e);if("number"==typeof this.need){if(r(this.need))return;e=n.subarray(0,t)}else if(this.need===d){if(r(4))return;e=n[0]<<24|n[1]<<16|n[2]<<8|n[3]}else if(this.need===f){if(r(2))return;e=n[0]<<8|n[1]}else if(this.need===h){if(r(1))return;e=n[0]}else if("buffer"in this.need){if("byteOffset"in this.need){if(r(this.need.byteLength-this.need.byteOffset))return;new Uint8Array(this.need.buffer,this.need.byteOffset).set(n.subarray(0,t)),e=this.need}else if(this.g)return void this.g.throw(new Error("Unsupported type"))}else{if(r(this.need.byteLength))return;new Uint8Array(this.need).set(n.subarray(0,t)),e=this.need}return this.consumed+=t,this.g?this.demand(this.g.next(e).value,!0):this.resolve&&this.resolve(e),e}}write(e){if(e instanceof Uint8Array?this.malloc(e.length).set(e):"buffer"in e?this.malloc(e.byteLength).set(new Uint8Array(e.buffer,e.byteOffset,e.byteLength)):this.malloc(e.byteLength).set(new Uint8Array(e)),!this.g&&!this.resolve)return new Promise(e=>this.pull=e);this.flush()}writeU32(e){this.malloc(4).set([e>>24&255,e>>16&255,e>>8&255,255&e]),this.flush()}writeU16(e){this.malloc(2).set([e>>8&255,255&e]),this.flush()}writeU8(e){this.malloc(1)[0]=e,this.flush()}malloc(e){if(this.buffer){var t=this.buffer.length,r=t+e;if(r<=this.buffer.buffer.byteLength-this.buffer.byteOffset)this.buffer=new Uint8Array(this.buffer.buffer,this.buffer.byteOffset,r);else{const e=new Uint8Array(r);e.set(this.buffer),this.buffer=e}return this.buffer.subarray(t,r)}return this.buffer=new Uint8Array(e),this.buffer}}j.U32=d,j.U16=f,j.U8=h;class B{constructor(s){this.log=function(e){if(s._opt.debug&&s._opt.debugLevel==a){for(var t=s._opt.debugUuid?`[${s._opt.debugUuid}]`:"",r=arguments.length,n=new Array(1<r?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];console.log(`JbPro${t}[✅✅✅][${e}]`,...n)}},this.warn=function(e){if(s._opt.debug&&(s._opt.debugLevel==a||s._opt.debugLevel==u)){for(var t=s._opt.debugUuid?`[${s._opt.debugUuid}]`:"",r=arguments.length,n=new Array(1<r?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];console.log(`JbPro${t}[❗❗❗][${e}]`,...n)}},this.error=function(e){for(var t=s._opt.debugUuid?`[${s._opt.debugUuid}]`:"",r=arguments.length,n=new Array(1<r?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];console.error(`JbPro${t}[❌❌❌][${e}]`,...n)}}}const p={init:0,findFirstStartCode:1,findSecondStartCode:2};class U extends class{on(e,t,r){var n=this.e||(this.e={});return(n[e]||(n[e]=[])).push({fn:t,ctx:r}),this}once(n,o,s){const i=this;function a(){i.off(n,a);for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];o.apply(s,t)}return a._=o,this.on(n,a,s)}emit(e){for(var t=((this.e||(this.e={}))[e]||[]).slice(),r=arguments.length,n=new Array(1<r?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];for(let e=0;e<t.length;e+=1)t[e].fn.apply(t[e].ctx,n);return this}off(e,r){const t=this.e||(this.e={});if(e){var n=t[e],o=[];if(n&&r)for(let e=0,t=n.length;e<t;e+=1)n[e].fn!==r&&n[e].fn._!==r&&o.push(n[e]);return o.length?t[e]=o:delete t[e],this}Object.keys(t).forEach(e=>{delete t[e]}),delete this.e}}{constructor(e){super(),this.player=e,this.isDestroyed=!1,this.reset()}destroy(){this.isDestroyed=!1,this.off(),this.reset()}reset(){this.stats=p.init,this.tempBuffer=new Uint8Array(0),this.parsedOffset=0,this.versionLayer=0}dispatch(e,t){var r,n=new Uint8Array(this.tempBuffer.length+e.length);for(n.set(this.tempBuffer,0),n.set(e,this.tempBuffer.length),this.tempBuffer=n;!this.isDestroyed;){if(this.state==p.Init){let e=!1;for(;2<=this.tempBuffer.length-this.parsedOffset&&!this.isDestroyed;){if(255==this.tempBuffer[this.parsedOffset]&&!(!1&this.tempBuffer[this.parsedOffset+1])){this.versionLayer=this.tempBuffer[this.parsedOffset+1],this.state=p.findFirstStartCode,this.fisrtStartCodeOffset=this.parsedOffset,this.parsedOffset+=2,e=!0;break}this.parsedOffset++}if(e)continue;break}if(this.state==p.findFirstStartCode){let e=!1;for(;2<=this.tempBuffer.length-this.parsedOffset&&!this.isDestroyed;){if(255==this.tempBuffer[this.parsedOffset]&&this.tempBuffer[this.parsedOffset+1]==this.versionLayer){this.state=p.findSecondStartCode,this.secondStartCodeOffset=this.parsedOffset,this.parsedOffset+=2,e=!0;break}this.parsedOffset++}if(e)continue;break}this.state==p.findSecondStartCode&&(r=this.tempBuffer.slice(this.fisrtStartCodeOffset,this.secondStartCodeOffset),this.emit("data",r,t),this.tempBuffer=this.tempBuffer.slice(this.secondStartCodeOffset),this.fisrtStartCodeOffset=0,this.parsedOffset=2,this.state=p.findFirstStartCode)}}}function m(u){let r=[],l=[],n=new AbortController,o=null,s=null,i=null,a=!1,c=[],d=0,f=0,t=null,h=null,p=!1;const m="audio worker";function y(){if(p=!0,w.fetchStatus!==T||x(w._opt.isChrome)){if(n)try{n.abort(),n=null}catch(e){w.debug.log(m,"abort catch",e)}}else n=null,w.debug.log(m,`abort() and not abortController.abort() _status is ${w.fetchStatus} and _isChrome is `+w._opt.isChrome)}let g=()=>{var e=function(){{var r=E;let t="";if("object"==typeof r)try{t=JSON.stringify(r),t=JSON.parse(t)}catch(e){t=r}else t=r;return t}}();return{debug:e.debug,debugLevel:e.debugLevel,sampleRate:0,audioBufferSize:1024,videoBuffer:e.videoBuffer,isChrome:!1}},w={isDestroyed:!1,fetchStatus:F,_opt:g(),mp3Demuxer:null,init:function(){w.debug.log(m,"init and opt is",w._opt),w.stopId=setInterval(()=>{var e=(new Date).getTime(),e=e-(t=t||e);100<e&&w.debug.warn(m,"loop demux diff time is "+e);{let e=null;if(r.length)if(e=r[0],-1===w.getDelay(e.ts))r.shift(),w.doDecode(e);else for(;r.length;){if(e=r[0],!(w.getDelay(e.ts)>w._opt.videoBuffer)){w.delay<0&&w.debug.warn(m,`loop() do not decode and delay is ${w.delay}, bufferList is `+r.length);break}r.shift(),w.doDecode(e)}else-1!==w.delay&&w.debug.log(m,"loop() bufferList is empty and reset delay"),w.resetAllDelay()}t=(new Date).getTime()},10)},doDecode:function(e){e.decoder.decode(e.payload,e.ts,e.isIFrame,e.cts)},getDelay:function(e){var t,r;return e?(w.preDelayTimestamp&&w.preDelayTimestamp>e?1e3<w.preDelayTimestamp-e&&w.debug.warn(m,`getDelay() and preDelayTimestamp is ${w.preDelayTimestamp} > timestamp is ${e} more than ${w.preDelayTimestamp-e}ms`):w.firstTimestamp?e&&(t=Date.now()-w.startTimestamp,r=e-w.firstTimestamp,w.delay=r<=t?t-r:r-t):(w.firstTimestamp=e,w.startTimestamp=Date.now(),w.delay=-1),w.preDelayTimestamp=e,w.delay):-1},resetAllDelay:function(){w.firstTimestamp=null,w.startTimestamp=null,w.delay=-1,w.preDelayTimestamp=null},close:function(){if(w.debug.log(m,"close"),w.isDestroyed=!0,y(),!o||1!==o.readyState&&2!==o.readyState?o&&w.debug.log(m,"close() and socket.readyState is "+o.readyState):(p=!0,o.close(1e3,"Client disconnecting")),o=null,w.stopId&&(clearInterval(w.stopId),w.stopId=null),w.mp3Demuxer&&(w.mp3Demuxer.destroy(),w.mp3Demuxer=null),w.writableStream&&x(w.writableStream.locked)&&w.writableStream.close().catch(()=>{w.debug.log("worker","close() and writableStream.close() error",e)}),w.writableStream=null,v)try{v.clear&&v.clear(),v=null}catch(e){w.debug.warn(m,"close() and audioDecoder.clear error",e)}s=null,w._opt=g(),r=[],l=[],i=null,w.resetAllDelay(),w.fetchStatus=F,a=!1,c=[],d=0,f=0,t=null,h=null,postMessage({cmd:"closeEnd"})},fetchStream:function(e,t){w.debug.log(m,"fetchStream, url is "+e,"options:",JSON.stringify(t)),s=function(r){let n=0,o=O();return e=>{var t;"[object Number]"===Object.prototype.toString.call(e)&&(n+=e,1e3<=(t=(e=O())-o))&&(r(n/t*1e3),o=e,n=0)}}(e=>{postMessage({cmd:k,type:"streamRate",value:e})}),2===t.protocol?(i=new j(w.demuxFlv()),fetch(e,{signal:n.signal}).then(e=>{if(p)w.debug.log(m,"request abort and run res.body.cancel()"),w.fetchStatus=F,e.body.cancel();else if(e.ok&&200<=e.status&&e.status<=299)if(postMessage({cmd:k,type:D}),"undefined"!=typeof WritableStream)w.writableStream=new WritableStream({write:e=>n&&n.signal&&n.signal.aborted?(w.debug.log(m,"writableStream write() and abortController.signal.aborted is true so return"),void(w.fetchStatus=M)):$(p)?(w.debug.log(m,"writableStream write() and requestAbort is true so return"),void(w.fetchStatus=M)):(w.fetchStatus=T,s(e.byteLength),void i.write(e)),close:()=>{w.fetchStatus=M,i=null,y(),postMessage({cmd:k,type:S,value:b})},abort:e=>{n&&n.signal&&n.signal.aborted?(w.debug.log(m,"writableStream abort() and abortController.signal.aborted is true so return"),w.fetchStatus=M):(i=null,e.name!==P&&(y(),postMessage({cmd:k,type:A,value:e.toString()})))}}),e.body.pipeTo(w.writableStream);else{const t=e.body.getReader(),r=()=>{t.read().then(e=>{var{done:e,value:t}=e;return e?(w.fetchStatus=M,i=null,y(),void postMessage({cmd:k,type:S,value:b})):n&&n.signal&&n.signal.aborted?(w.debug.log(m,"fetchNext().then() and abortController.signal.aborted is true so return"),void(w.fetchStatus=M)):$(p)?(w.debug.log(m,"fetchNext().then() and requestAbort is true so return"),void(w.fetchStatus=M)):(w.fetchStatus=T,s(t.byteLength),i.write(t),void r())}).catch(e=>{n&&n.signal&&n.signal.aborted?(w.debug.log(m,"fetchNext().catch() and abortController.signal.aborted is true so return"),w.fetchStatus=M):(i=null,e.name!==P&&(y(),postMessage({cmd:k,type:A,value:e.toString()})))})};r()}else w.debug.warn(m,`fetch response status is ${e.status} and ok is ${e.ok} and emit error and next abort()`),y(),postMessage({cmd:k,type:A,value:`fetch response status is ${e.status} and ok is `+e.ok})}).catch(e=>{n&&n.signal&&n.signal.aborted?w.debug.log(m,"fetch().catch() and abortController.signal.aborted is true so return"):e.name!==P&&(y(),postMessage({cmd:k,type:A,value:e.toString()}),i=null)})):1===t.protocol&&(t.isFlv&&(i=new j(w.demuxFlv())),(o=new WebSocket(e)).binaryType="arraybuffer",o.onopen=()=>{w.debug.log(m,"fetchStream, WebsocketStream  socket open"),postMessage({cmd:k,type:D}),postMessage({cmd:k,type:"websocketOpen"})},o.onclose=e=>{w.debug.log(m,"fetchStream, WebsocketStream socket close and code is "+e.code),1006===e.code&&w.debug.error(m,"fetchStream, WebsocketStream socket close abnormally and code is "+e.code),$(p)?w.debug.log(m,"fetchStream, WebsocketStream socket close and requestAbort is true so return"):(i=null,postMessage({cmd:k,type:S,value:"websocket"}))},o.onerror=e=>{w.debug.error(m,"fetchStream, WebsocketStream socket error",e),i=null,postMessage({cmd:k,type:"websocketError",value:e.isTrusted?"websocket user aborted":"websocket error"})},o.onmessage=e=>{s(e.data.byteLength),t.isFlv?i.write(e.data):w.demuxM7s(e.data)})},demuxFlv:function*(){yield 9;const e=new ArrayBuffer(4),t=new Uint8Array(e),r=new Uint32Array(e);for(;;){t[3]=0;const e=yield 15,s=e[4];t[0]=e[7],t[1]=e[6],t[2]=e[5];var n=r[0],o=(t[0]=e[10],t[1]=e[9],t[2]=e[8],t[3]=e[11],r[0]),n=(yield n).slice();8===s&&w.decode(n,{type:1,ts:o})}},decode:function(e,t){postMessage({cmd:k,type:"streamAbps",value:e.byteLength}),w.pushBuffer(e,t.ts)},setCodecAudio:function(e,t){const r=e[0]>>4,n=e[0]>>1&1;if(h=r===C?n?16:8:0===n?8:16,v&&v.setCodec)if(R(e)||7==r||8==r||2==r){w.debug.log(m,"setCodecAudio: init audio codec, codeId is "+r);const n=r===C?e.slice(2):new Uint8Array(0);v.setCodec(r,w._opt.sampleRate,n),a=!0,r!==C&&(2==r?(w.mp3Demuxer||(w.mp3Demuxer=new U(w),w.mp3Demuxer.on("data",(e,t)=>{v.decode(e,t)})),w.mp3Demuxer.dispatch(e.slice(1),t)):v.decode(e.slice(1),t))}else w.debug.warn(m,"setCodecAudio: hasInitAudioCodec is false, codecId is ",r);else w.debug.error(m,"setCodecAudio: audioDecoder or audioDecoder.setCodec is null")},pushBuffer:function(e,t){R(e)?w.decodeAudio(e,t):r.push({ts:t,payload:e,decoder:{decode:w.decodeAudio},isIFrame:!1})},decodeAudio:function(e,t){var r=e[0]>>4;a?2==r?w.mp3Demuxer.dispatch(e.slice(1),t):v.decode(r===C?e.slice(2):e.slice(1),t):w.setCodecAudio(e)},demuxM7s:function(e){var t=new DataView(e),r=t.getUint32(1,!1);1===t.getUint8(0)&&w.decode(new Uint8Array(e,5),{type:1,ts:r})},audioInfo:function(e,t,r){postMessage({cmd:"audioCode",code:e}),postMessage({cmd:"initAudio",sampleRate:t,channels:r,depth:h}),f=r},pcmData:function(o,s,i){if(w.isDestroyed)w.debug.log(m,"pcmData, decoder is destroyed and return");else{let t=s,r=[],e=0,n=w._opt.audioBufferSize;for(let e=0;e<2;e++){var a=u.HEAPU32[(o>>2)+e]>>2;r[e]=u.HEAPF32.subarray(a,a+t)}if(d){if(!(t>=(s=n-d)))return d+=t,l[0]=Float32Array.of(...l[0],...r[0]),void(2==f&&(l[1]=Float32Array.of(...l[1],...r[1])));c[0]=Float32Array.of(...l[0],...r[0].subarray(0,s)),2==f&&(c[1]=Float32Array.of(...l[1],...r[1].subarray(0,s))),postMessage({cmd:_,buffer:c,delay:w.delay,ts:i},c.map(e=>e.buffer)),e=s,t-=s}for(d=t;d>=n;d-=n)c[0]=r[0].slice(e,e+=n),2==f&&(c[1]=r[1].slice(e-n,e)),postMessage({cmd:_,buffer:c,delay:w.delay,ts:i},c.map(e=>e.buffer));d&&(l[0]=r[0].slice(e),2==f)&&(l[1]=r[1].slice(e))}},sendWebsocketMessage:function(e){o?1===o.readyState?o.send(e):w.debug.error(m,"socket is not open"):w.debug.error(m,"socket is null")},timeEnd:function(){}},v=(w.debug=new B(w),null);u.AudioDecoder&&(v=new u.AudioDecoder(w)),postMessage({cmd:"init"}),self.onmessage=function(e){var t=e.data;switch(t.cmd){case"init":try{w._opt=Object.assign(w._opt,JSON.parse(t.opt))}catch(e){}w.init();break;case"fetchStream":w.fetchStream(t.url,JSON.parse(t.opt));break;case"close":w.close();break;case"updateConfig":w.debug.log(m,"updateConfig",t.key,t.value),w._opt[t.key]=t.value;break;case"sendWsMessage":w.sendWebsocketMessage(t.message)}}}Date.now||(Date.now=function(){return(new Date).getTime()}),i.postRun=function(){m(i)}});
