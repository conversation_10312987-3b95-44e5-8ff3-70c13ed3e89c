<!-- 平台消息 -->
<template>
  <div>
    <div v-loading="tableLoading">
      <el-scrollbar
        v-if="tablePage.length"
        ref="projectBarRef"
        class="message-content"
      >
        <div
          v-infinite-scroll="loadData"
          :infinite-scroll-disabled="isInfiniteScrollDisabled"
          :infinite-scroll-distance="10"
        >
          <div
            v-for="(item, index) in tablePage"
            :key="index"
            class="content-item"
          >
            <div class="content-item-left">
              <div v-if="!item.hasRead" class="news-status" />
              <div class="message-icon">
                <img src="@/assets/sx_logo_primary.png" class="icon-img" />
              </div>
              <div class="content-item-time">
                <div class="center-title">
                  {{ $t('page.dialog.platformNotification') }}
                </div>
                <div class="left-subtitle center-content">
                  <div class="content-text">{{ item.content }}</div>
                </div>
                <div class="left-subtitle">{{ item.noticeTime }}</div>
              </div>
            </div>
            <div class="content-item-right">
              <el-button
                class="handle-button"
                type="primary"
                @click="handleClick(item)"
              >
                {{ $t('page.check') }}
              </el-button>
            </div>
          </div>
        </div>
      </el-scrollbar>
      <el-empty v-else />
    </div>

    <PopMessageInfo
      ref="popMessageInfoRef"
      :message-type="3"
      :show-cancel="false"
      @updateMessageCount="updateCount"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import i18n from '@/lang';

import PopMessageInfo from '../components/popMessageInfo.vue';
import { getStationMessagePage } from '@/api/messageCenter';

const totalNum = ref('');
const tableLoading = ref('false');
const projectBarRef = ref('projectBarRef');
const isInfiniteScrollDisabled = ref(false);
const viewData = ref({});
const tablePage = ref([]);
const popMessageInfoRef = ref('popMessageInfoRef');
const emit = defineEmits(['changeCount', 'stationMessageCount']);

const tableQueryData = ref({
  pageSize: 10,
  pageNum: 1
});

const handleClick = data => {
  popMessageInfoRef.value.handleOpen({
    ...data,
    title: i18n.global.t('page.dialog.title.platformNotification')
  });
  viewData.value = data;
};

const getTablePage = async () => {
  tableLoading.value = true;
  isInfiniteScrollDisabled.value = true;
  try {
    const res = await getStationMessagePage(tableQueryData.value);
    tablePage.value = Object.assign(tablePage.value, res.data.records);
    totalNum.value = res.data.pages; // 总页码
    emit('changeCount');
    emit('stationMessageCount');

    nextTick(() => {
      if (res.data.total) {
        closeTooltip();
      }
    });
    isInfiniteScrollDisabled.value = false;
  } catch (err) {
    console.log('获取分页信息错误', err);
  }
  tableLoading.value = false;
};

// 滚动加载
const loadData = () => {
  if (tableQueryData.value.pageNum < totalNum.value) {
    tableQueryData.value.pageNum++;
    getTablePage();
  }
};

const closeTooltip = () => {
  projectBarRef.value.wrapRef.onscroll = () => {
    const list = document.getElementsByClassName('el-tooltip__popper');
    if (list.length > 0) {
      list[list.length - 1].style.display = 'none';
    }
  };
};

const updateCount = () => {
  viewData.value.hasRead = 1;
  emit('changeCount');
};

onMounted(() => {
  getTablePage();
});
</script>

<style lang="scss" scoped>
.message-content {
  height: 455px;

  :deep(.el-scrollbar__wrap) {
    overflow-x: auto;
  }
}

.content-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border-bottom: 1px solid #ececec;

  .left-subtitle {
    line-height: 25px;
  }

  .content-text {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .center-content {
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: grid;
    width: 100%;
  }

  .content-item-left {
    display: flex;
    align-items: center;
    width: 100%;

    .message-icon {
      min-width: 64px;
      min-height: 64px;
      width: 64px;
      height: 64px;
      background-color: rgba($color: #097efc, $alpha: 0.1);
      color: #097efc;
      margin: 0 15px;
      padding: 12px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      .icon-img {
        width: 32px;
        height: 19px;
      }
    }

    .content-item-time {
      font-size: 14px;
      color: #999999;
      line-height: 14px;
      font-weight: 400;
      margin-right: 15px;

      .time-title {
        font-size: 16px;
        // line-height: 40px;
        margin-bottom: 10px;
        font-weight: 600;
        color: #333;
      }

      //   .left-subtitle {
      //   }
    }
  }

  .center-title {
    font-size: 16px;
    color: #222222;
    //   line-height: 40px;
    font-weight: 600;
    margin-bottom: 7px;
  }

  .content-item-right {
    width: 70px;
    margin-left: 10px;
  }
}
.news-status {
  width: 10px;
  height: 10px;
  color: red;
  background: red;
  border-radius: 50%;
  min-width: 10px;
}
</style>
