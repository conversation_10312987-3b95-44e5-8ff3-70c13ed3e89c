import * as Cesium from 'cesium';
import { polylinematerial } from './PolylineMaterialAppearance';
import { toCartesian3 } from './common';
import { createFrustum, getFrustum, updateAllFrustum } from '@/views/plan/newplan/kmz/hocks';
import { GlbModel } from './glbModel';
import { setActionComponentVisible } from '@/views/plan/newplan/kmz/hocks/modules/waylineshandle.js';
import { useWayPointStore } from '@/store/modules/wayPointInfo.js';
import { ElMessage } from 'element-plus';
import { isMouseInElementRect } from '@/utils';
import {
  imglayer,
  globalConfigResource,
  cialayer,
  projectTerrainOnline,
  projectCustomTerrainProvider
} from '@/components/Cesium/libs/cesium/index';
import { useEditTrackerStore } from '@/store/modules/dataEditTracker.js';
import { getDeptSysSetting } from '@/api/wayline';
const editTrackerStore = useEditTrackerStore();
class Plan {
  constructor(viewer, eyeViewer) {
    this.viewer = viewer;
    this.eyeViewer = eyeViewer;
    this.customTerrainProvider;
    this.tool = 'pan';
    this.line = null;
    //无人机相关
    this.model = null; //无人机模型对象
    this.curUAVPoint = []; //当前无人机位置
    this.wrjTerrainHeight = null; //无人机所在地形高度
    this.wrjHeight = null; //无人机高度
    this.wrjSpeed = 10; //速度
    //无人机视锥图
    this.eyeFrustum = null;
    this.fov = 30;
    this.near = 0.1;
    this.far = 200;
    //航点相关
    this.flyStartPoint = null; //飞行起飞点 [lon,lat,height]
    this.planPointJson = []; //地图上的航点列表
    this.placemarkJson = []; //提交的航点列表
    this.curPlanPointJson = null; //当前航点
    this.curDictEntity = null; //与前一个航点的距离注记
    this.nextDictEntity = null; //与下一个航点的距离注记
    this.aslHeight = 120; //绝对高度
    this.altHeight = 120; //相对起飞点高度
    this.heightModel = 'EGM96'; //航点高程参考平面,EGM96：使用海拔高编辑;relativeToStartPoint：使用相对点的高度进行编辑
    this.globalHeight = 120; //航线设置中设置的高度值，对应planRouteSet中的heights.value.curHeight
    //this.aglHeight = 120;//相对地形高度
    this.planPointFlow = true; //航点是否跟随航线，主要是调整高度时，航点是否跟随调整变化，不跟随则不变
    this.planAllLength = 0; //航线总长，m为单位
    this.planTime = 0; //预计执行时间
    this.keyTimeout = 0; //防止keyup事件不执行（页面焦点失去后，会不执行keyup）
    this.flags = {
      // 相机位置
      moveForward: false,
      moveBackward: false,
      moveUp: false,
      moveDown: false,
      moveLeft: false,
      moveRight: false,

      // 相机姿态
      twistLeft: false,
      twistRight: false,
      lookLeft: false,
      lookRight: false
    };
    this.speed = 5;
    this.continueRun = true;
    this.deltaRadians = Cesium.Math.toRadians(10); // 旋转角度
    this.position = new Cesium.Cartesian3();
    this.listeners = {}; // 用于保存事件监听器的引用
  }

  /**
   * 初始化地图
   */
  init(isFly) {
    //取消双击
    this.viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);
    this.viewer.imageryLayers.addImageryProvider(imglayer);
    this.viewer.imageryLayers.addImageryProvider(cialayer);
    // 根据是否使用在线地形，进行添加项目地形
    if (projectTerrainOnline) {
      const onlineTerrainProvider = Cesium.createWorldTerrain({
        requestWaterMask: false,
        requestVertexNormals: true
      });
      this.customTerrainProvider = onlineTerrainProvider;
    } else {
      this.customTerrainProvider = projectCustomTerrainProvider || customTerrainProvider;
    }
    // this.customTerrainProvider = projectCustomTerrainProvider;
    this.viewer.terrainProvider = this.customTerrainProvider;
    this.eyeViewer.terrainProvider = this.customTerrainProvider;
    // this.show3DCoordinates();
    this.initEyeViewer();
    this.setAntiAliasing();
    // 定位到厦门
    // const targetPointOption = {
    //   lon: globalConfigResource?.init?.lon || 0,
    //   lat: globalConfigResource?.init?.lat || 0,
    //   viewHeight: globalConfigResource?.init?.viewHeight || 0,
    //   heading: globalConfigResource?.init?.viewHeight || 0,
    //   pitch: globalConfigResource?.init?.viewHeight || 0,
    //   roll: globalConfigResource?.init?.roll || 0,
    //   duration: globalConfigResource?.init?.duration || 2
    // };
    this.initViewerCamera(isFly);
    // // 设置初始位置
    // if (isFly != false) {
    //   this.flyTo(Cesium.Cartesian3.fromDegrees(targetPointOption.lon, targetPointOption.lat, 100), 200, 0, -30, 0, 2);
    // }
    // this.setEyeViewer(Cesium.Cartesian3.fromDegrees(targetPointOption.lon, targetPointOption.lat, 200), 0, 0, 0);
  }

  /**
   * 初始化相机视角
   */
  initViewerCamera = async isFly => {
    await getDeptSysSetting({}).then(res => {
      const { wayline_config = {} } = res;
      // 设置初始位置
      if (isFly != false) {
        this.flyTo(
          Cesium.Cartesian3.fromDegrees(Number(wayline_config.longitude), Number(wayline_config.latitude), 100),
          200,
          0,
          -30,
          0,
          2
        );
      }
      this.setEyeViewer(
        Cesium.Cartesian3.fromDegrees(Number(wayline_config.longitude), Number(wayline_config.latitude), 200),
        0,
        0,
        0
      );
    });
  };
  /**
   * 初始化鹰眼地图
   */
  initEyeViewer() {
    this.eyeViewer.imageryLayers.addImageryProvider(imglayer);
    this.eyeViewer.imageryLayers.addImageryProvider(cialayer);
    // 如果为真，则允许用户旋转相机。如果为假，相机将锁定到当前标题。此标志仅适用于2D和3D。
    this.eyeViewer.scene.screenSpaceCameraController.enableRotate = false;
    // 如果为true，则允许用户平移地图。如果为假，相机将保持锁定在当前位置。此标志仅适用于2D和Columbus视图模式。
    this.eyeViewer.scene.screenSpaceCameraController.enableTranslate = false;
    // 如果为真，允许用户放大和缩小。如果为假，相机将锁定到距离椭圆体的当前距离
    this.eyeViewer.scene.screenSpaceCameraController.enableZoom = false;
    // 如果为真，则允许用户倾斜相机。如果为假，相机将锁定到当前标题。这个标志只适用于3D和哥伦布视图。
    this.eyeViewer.scene.screenSpaceCameraController.enableTilt = false;
    // const terrainProvider = new Cesium.ArcGISTiledElevationTerrainProvider({
    //   url: 'https://elevation3d.arcgis.com/arcgis/rest/services/WorldElevation3D/Terrain3D/ImageServer'
    // });
    // this.eyeViewer.terrainProvider = terrainProvider;
  }
  /**
   * 设置鹰眼地图
   */
  setEyeViewer(position, heading, pitch, roll) {
    this.eyeViewer.camera.setView({
      destination: toCartesian3(position),
      orientation: {
        heading: heading,
        pitch: pitch,
        roll: roll
      }
    });
  }

  /**
   * 更新鹰眼图视图
   * @param {Cesium.Cartesian3} position
   * @param {Number} heading 度
   * @param {Number} pitch 度
   * @param {Number} roll 度
   * @returns
   */
  updateEyeViewer(position, heading, pitch, roll, zoom) {
    if (!this.eyeViewer) {
      return;
    }
    this.eyeViewer?.camera?.setView({
      destination: toCartesian3(position),
      orientation: {
        heading: Cesium.Math.toRadians(heading),
        pitch: Cesium.Math.toRadians(roll + 90),
        roll: Cesium.Math.toRadians(pitch)
      }
    });
    // 根据倍率计算大小
    if (zoom) {
      window.$bus.emit('updateRectDom', {
        zoom: zoom
      });
    }
  }

  /**
   * 初始化鼠标事件
   */
  initMouse(fun) {
    let handler3D = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
    //鼠标在地图中点击时触发的事件
    handler3D.setInputAction(movement => {
      let pick = movement.position;
      if (pick) {
        let cartesian = this.viewer.scene.globe.pick(this.viewer.camera.getPickRay(pick), this.viewer.scene);
        if (cartesian) {
          //世界坐标转地理坐标（弧度）
          let cartographic = this.viewer.scene.globe.ellipsoid.cartesianToCartographic(cartesian);
          if (cartographic) {
            //海拔
            let height = this.viewer.scene.globe.getHeight(cartographic);

            //地理坐标（弧度）转经纬度坐标
            let point = [(cartographic.longitude / Math.PI) * 180, (cartographic.latitude / Math.PI) * 180];
            if (!height) {
              height = 0;
            }
            if (!point) {
              point = [0, 0];
            }
            if (this.tool == 'start') {
              //coordinatesDiv.innerHTML = "<span id='cd_label' style='font-size:13px;text-align:center;font-family:微软雅黑;color:#edffff;'>视角高度:" + (he - he2).toFixed(2) + "米&nbsp;&nbsp;&nbsp;&nbsp;海拔高度:" + height.toFixed(2) + "米&nbsp;&nbsp;&nbsp;&nbsp;经度：" + point[0].toFixed(6) + "&nbsp;&nbsp;纬度：" + point[1].toFixed(6) + "</span>";
              document.body.style.cursor = 'default';

              this.tool = 'pan';
              if (fun != undefined && fun != null) fun('start', { lon: point[0], lat: point[1], height: height });

              // {
              //   index: 0,
              //   type: '起飞点',
              //   lon: lon,
              //   lat: lat,
              //   startHeight: height,//海拔高度
              //   globalHeight: this.globalHeight,
              //   height: this.wrjHeight,
              //   UAVHPR: hpr, //无人机方位
              //   position: this.curUAVPoint, //无人机停留高度点
              //   planLineEntity: planLineEntity, //起飞路线
              //   pointEntity: null,
              //   lineEntity: null,
              //   dPointEntity: pointEntity, //起飞点
              //   length: (this.wrjHeight - height) //无人机与起飞点的距离
              // }
            } else if (this.tool == 'add') {
              const that = this;
              document.body.style.cursor = 'default';
              const position = Cesium.Cartesian3.fromDegrees(point[0], point[1], this.aslHeight);
              this.wrjHeight = this.aslHeight;
              const { hpr } = this.model.getHpr();
              const cartographic = Cesium.Cartographic.fromCartesian(position);
              const promise = Cesium.sampleTerrainMostDetailed(this.customTerrainProvider, [cartographic]);
              promise
                .then(function (updatedPositions) {
                  const terrainHeight = updatedPositions[0].height;
                  const json = that.addPoint(position, terrainHeight);
                  //  {
                  //   index: index,
                  //   type: "航点",
                  //   height: this.curUAVPoint.z, //
                  //   UAVHPR: hpr, //方位
                  //   position: position,//航点位置
                  //   planLineEntity: planLineEntity,//航线对象
                  //   pointEntity: pointEntity,//航点对象
                  //   lineEntity: lineEntity,//航点离地虚线
                  //   dPointEntity: dPointEntity,//航点地面点
                  //   length: length
                  // };
                  that.setModel(position, hpr);
                  that.tool = 'pan';
                  if (fun != undefined && fun != null) fun('add', json);
                })
                .catch(function (reason, data) {
                  const json = that.addPoint(position);
                  that.setModel(position, hpr);
                  that.tool = 'pan';
                  if (fun != undefined && fun != null) fun('add', json);
                  console.log('catch失败执行回调抛出失败原因：', reason);
                });
            } else {
              //选中航点判断
              const pickRay = this.viewer.scene.pick(movement.position);
              if (pickRay != undefined && typeof pickRay.id == 'object') {
                let k = pickRay.id.id.indexOf('航点');
                if (k > -1) {
                  //选中航点
                  let planPointIndex = parseInt(pickRay.id.id.substring(k + 2)) - 1; //航点索引
                  const planPoint = this.selectPlanPoint(planPointIndex, false);
                  if (planPoint) if (fun != undefined && fun != null) fun('sel', planPoint);
                }
              }
            }
          }
        }
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

    //鼠标在地图中移动时触发的事件
    handler3D.setInputAction(movement => {
      const pickRay = this.viewer.scene.pick(movement.endPosition);
      if (pickRay != undefined && typeof pickRay.id == 'object') {
        if (pickRay.id.id.indexOf('航点') > -1) {
          document.body.style.cursor = 'pointer';
        }
      } else {
        document.body.style.cursor = 'default';
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
  }

  /**
   * 选中航点
   * @param {*} planPointIndex 航点索引
   * @returns
   */
  selectPlanPoint(planPointIndex, isFly) {
    try {
      //-1代表取消选中
      if (planPointIndex == -1) {
        if (this.curPlanPointJson != null) {
          this.curPlanPointJson.pointEntity.billboard.image = './resource/images/plan/placemark.png';
        }
        if (this.curDictEntity != null) {
          this.viewer.entities.remove(this.curDictEntity);
        }
        if (this.nextDictEntity != null) {
          this.viewer.entities.remove(this.nextDictEntity);
        }
        this.curPlanPointJson = null;
        return null;
      }

      for (let i = 0; i < this.planPointJson.length; i++) {
        const mark = this.planPointJson[i];
        if (mark.type == '航点' && mark.index == planPointIndex) {
          if (this.curPlanPointJson != null) {
            this.curPlanPointJson.pointEntity.billboard.image = './resource/images/plan/placemark.png';
          }
          this.curPlanPointJson = mark;
          this.curPlanPointJson.pointEntity.billboard.image = './resource/images/plan/placemark_Light.png';

          if (this.curDictEntity != null) {
            this.viewer.entities.remove(this.curDictEntity);
          }
          if (this.nextDictEntity != null) {
            this.viewer.entities.remove(this.nextDictEntity);
          }
          const prePlanPointJson = this.planPointJson[i - 1];
          let nextPlanPointJson = null;
          //显示当前航点和下一航点的距离
          if (this.planPointJson.length > i + 1) {
            nextPlanPointJson = this.planPointJson[i + 1];
            // 计算中点
            const centerPoint2 = Cesium.Cartesian3.midpoint(
              this.curPlanPointJson.position,
              nextPlanPointJson.position,
              new Cesium.Cartesian3()
            );
            //距离格式化
            let lenghtStr2 = this.getLengthStr(nextPlanPointJson.length);
            this.nextDictEntity = this.addLabel(centerPoint2, lenghtStr2);
          }
          //显示当前航点和前一航点的距离
          // 计算中点
          const centerPoint = Cesium.Cartesian3.midpoint(
            prePlanPointJson.position,
            this.curPlanPointJson.position,
            new Cesium.Cartesian3()
          );
          //距离格式化
          let lenghtStr = this.getLengthStr(this.curPlanPointJson.length);
          this.curDictEntity = this.addLabel(centerPoint, lenghtStr);
          const position = this.curPlanPointJson.position;
          const hpr = this.curPlanPointJson.UAVHPR;
          //设置飞机模型的位置
          this.setModel(position, hpr);
          if (isFly)
            //飞行定位过去
            this.flyTo(position, 500, 0, -30, 0, 1);
          return this.curPlanPointJson;
          break;
        }
      }
    } catch (error) {
      console.log('error:', error);
    }
  }

  isOnPlanLine(airPosition) {
    if (this.planPointJson.length === 0) {
      return false;
    }
    return this.planPointJson.some(mark => mark.type === '航点' && mark.position.equals(airPosition));
  }

  /**
   * 键盘按下时开始无人机移动操作
   * @param {*} keyCode
   */
  setClickDown(keyCode) {
    const that = this;
    let flagName = this.getFlagForKeyCode(keyCode);
    if (typeof flagName !== 'underfined') {
      this.flags[flagName] = true;
      //防止失去焦点后，一直移动
      // if (that.keyTimeout != 0) {
      //   clearTimeout(that.keyTimeout);
      // }
      // that.keyTimeout = setTimeout(function () {
      //   that.flags[flagName] = false;
      //   clearTimeout(that.keyTimeout);
      //   that.keyTimeout = 0;
      // }, 3000);
    }
  }

  /**
   * 键盘弹起时停止无人机移动操作
   * @param {*} keyCode
   */
  setClickUp(keyCode) {
    let flagName = this.getFlagForKeyCode(keyCode);
    if (typeof flagName !== 'underfined') {
      this.flags[flagName] = false;
    }
  }

  /**
   * 初始化键盘事件
   */
  initKey(fun) {
    const that = this;
    const mousePosition = { x: 0, y: 0 };
    // 假设你想要在鼠标点击时获取位置
    document.addEventListener('mousemove', function (event) {
      mousePosition.x = event.clientX; // 鼠标相对于浏览器窗口左上角的横坐标
      mousePosition.y = event.clientY; // 鼠标相对于浏览器窗口左上角的纵坐标
    });
    // 定义和保存事件处理程序
    that.listeners.keydownHandler = function (e) {
      const isOver = isMouseInElementRect(document.getElementById('container'), mousePosition.x, mousePosition.y);
      if (!isOver) {
        return;
      }
      let flagName = that.getFlagForKeyCode(e.keyCode);
      if (typeof flagName !== 'undefined') {
        that.flags[flagName] = true;
      }
    };
    that.listeners.keyupHandler = function (e) {
      const isOver = isMouseInElementRect(document.getElementById('container'), mousePosition.x, mousePosition.y);
      if (!isOver) {
        return;
      }
      let flagName = that.getFlagForKeyCode(e.keyCode);
      if (typeof flagName !== 'underfined') {
        that.flags[flagName] = false;
      }

      // 新增航点 space 键
      if (e.keyCode == 32) {
        if (that.model != null && document.activeElement == document.body) {
          //获取经纬度、高程
          const { position } = that.model.getPosition();
          // 如果飞机处于航点上 提示飞机处于航点上，无法新增航点
          let isOnLine = that.isOnPlanLine(position);
          if (isOnLine) {
            ElMessage({
              message: '飞机处于航点上，无法新增航点.',
              type: 'warning'
            });
            return;
          }
          const json = that.addPoint(position, that.wrjTerrainHeight);
          if (fun != undefined && fun != null) {
            let frustun = getFrustum('action');
            if (frustun) {
              frustun.setVisible(false);
            }
            // 设置当前的动作id为空
            const wayPointStore = useWayPointStore();
            wayPointStore.setCurrentActionNull();
            // 设置动画组件为空
            setActionComponentVisible();
            editTrackerStore.dataTracker.markAsModified();
            fun('add', json);
          }
        }
      }
    };

    // 绑定和添加事件监听器
    document.addEventListener('keydown', that.listeners.keydownHandler, false);
    document.addEventListener('keyup', that.listeners.keyupHandler, false);
    document.addEventListener('mouseleave', this.handleMouseLeave);
    document.addEventListener('mouseenter', this.handleMouseEnter);
    // 添加事件监听器
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
    let eyeViewerCamera = this.eyeViewer?.camera || null;
    // 监听不同键码事件，并控制相应的相机事件
    this.viewer.clock.onTick.addEventListener(function (clock) {
      if (that.model == null) {
        return;
      }
      if (!that.continueRun) {
        return;
      }
      let entity = that.model;
      let isSet = false;
      if (that.flags.moveForward) {
        // 更新实体在场景中的位置
        // 1、从实体的当前位置和朝向计算出实体在Y轴的方向向量。
        // 2、将此向量乘以速度得到位移向量。
        // 3、将实体当前位置和位移向量相加,得到新的位置。
        that.position = Cesium.Cartesian3.add(
          entity.position,
          Cesium.Cartesian3.multiplyByScalar(
            Cesium.Matrix3.multiplyByVector(
              Cesium.Matrix3.fromQuaternion(entity.orientation),
              Cesium.Cartesian3.UNIT_Y,
              new Cesium.Cartesian3()
            ),
            that.speed,
            new Cesium.Cartesian3()
          ),
          new Cesium.Cartesian3()
        );
        entity.setPosition(that.position);
        isSet = true;
      }
      if (that.flags.moveBackward) {
        // 计算新的位置
        that.position = Cesium.Cartesian3.add(
          entity.position,
          Cesium.Cartesian3.multiplyByScalar(
            Cesium.Matrix3.multiplyByVector(
              Cesium.Matrix3.fromQuaternion(entity.orientation),
              Cesium.Cartesian3.UNIT_Y,
              new Cesium.Cartesian3()
            ),
            -that.speed,
            new Cesium.Cartesian3()
          ),
          new Cesium.Cartesian3()
        );
        entity.setPosition(that.position);
        isSet = true;
      }
      // 向上调整的是高度
      if (that.flags.moveUp) {
        that.position = Cesium.Cartesian3.add(
          entity.position,
          Cesium.Cartesian3.multiplyByScalar(
            Cesium.Matrix3.multiplyByVector(
              Cesium.Matrix3.fromQuaternion(entity.orientation),
              Cesium.Cartesian3.UNIT_Z,
              new Cesium.Cartesian3()
            ),
            that.speed,
            new Cesium.Cartesian3()
          ),
          new Cesium.Cartesian3()
        );
        entity.setPosition(that.position);
        isSet = true;
      }
      //   向下调整的是高度
      if (that.flags.moveDown) {
        that.position = Cesium.Cartesian3.add(
          entity.position,
          Cesium.Cartesian3.multiplyByScalar(
            Cesium.Matrix3.multiplyByVector(
              Cesium.Matrix3.fromQuaternion(entity.orientation),
              Cesium.Cartesian3.UNIT_Z,
              new Cesium.Cartesian3()
            ),
            -that.speed,
            new Cesium.Cartesian3()
          ),
          new Cesium.Cartesian3()
        );
        entity.setPosition(that.position);
        isSet = true;
      }
      if (that.flags.moveLeft) {
        // 计算新的位置
        that.position = Cesium.Cartesian3.add(
          entity.position,
          Cesium.Cartesian3.multiplyByScalar(
            Cesium.Matrix3.multiplyByVector(
              Cesium.Matrix3.fromQuaternion(entity.orientation),
              Cesium.Cartesian3.UNIT_X,
              new Cesium.Cartesian3()
            ),
            -that.speed,
            new Cesium.Cartesian3()
          ),
          new Cesium.Cartesian3()
        );
        entity.setPosition(that.position);
        isSet = true;
      }
      if (that.flags.moveRight) {
        that.position = Cesium.Cartesian3.add(
          entity.position,
          Cesium.Cartesian3.multiplyByScalar(
            Cesium.Matrix3.multiplyByVector(
              Cesium.Matrix3.fromQuaternion(entity.orientation),
              Cesium.Cartesian3.UNIT_X,
              new Cesium.Cartesian3()
            ),
            that.speed,
            new Cesium.Cartesian3()
          ),
          new Cesium.Cartesian3()
        );
        entity.setPosition(that.position);
        isSet = true;
      }
      if (that.flags.lookLeft) {
        let h = that.model.heading - that.deltaRadians;
        that.model.setHeading(h);
        isSet = true;
      }
      if (that.flags.lookRight) {
        let h = that.model.heading + that.deltaRadians;
        that.model.setHeading(h);
        isSet = true;
      }
      if (that.flags.twistLeft) {
        isSet = true;
      }
      if (that.flags.twistRight) {
        isSet = true;
      }
      if (isSet == true) {
        let { hpr } = that.model.getHpr();
        const { latitude, longitude, height, position } = that.model.getPosition();
        that.wrjHeight = height;
        if (that.heightModel == 'EGM96') {
          that.globalHeight = height;
        } else if (that.heightModel == 'relativeToStartPoint') {
          that.globalHeight = height - that.flyStartPoint[2];
        }
        that.setModel(position, hpr);
        // 这里要构建headingPitchRoll 来设置 鹰眼图的相机
        let eyeFrustum = getFrustum('camera');
        let option = eyeFrustum?.getOptions() ?? null;
        option && that.updateEyeViewer(position, option.heading, option.pitch, option.roll, null);
        // 这里更新鹰眼视图
        if (fun != undefined && fun != null)
          fun('move', {
            position: position,
            lon: longitude,
            lat: latitude,
            startHeight: that.flyStartPoint[2], //起飞点海拔高度
            terrainHeight: that.wrjTerrainHeight,
            UAVHPR: hpr,
            height: that.wrjHeight
          });
      }
    });
  }
  /**
   * 将键盘码绑定在对应相机事件
   * @param {*} keyCode
   * @returns
   */
  getFlagForKeyCode(keyCode) {
    switch (keyCode) {
      case 'W'.charCodeAt(0): //87
        return 'moveForward';
      case 'S'.charCodeAt(0): //83
        return 'moveBackward';
      case 'C'.charCodeAt(0): //67
        return 'moveUp';
      case 'Z'.charCodeAt(0): //90
        return 'moveDown';
      case 'D'.charCodeAt(0): //68
        return 'moveRight';
      case 'A'.charCodeAt(0): //65
        return 'moveLeft';
      case 'Q'.charCodeAt(0): //81
        return 'lookLeft';
      case 'E'.charCodeAt(0): //69
        return 'lookRight';
      case 38:
        return 'lookUp';
      case 40:
        return 'lookDown';
        // case 'E'.charCodeAt(0):
        //   return 'twistRight';
        // case 'Q'.charCodeAt(0):
        //   return 'twistLeft';
        dafault: return undefined;
    }
  }

  /**
   *
   * @param {*} tool 工具标识，start,pan,add
   */
  setTool(tool) {
    this.tool = tool;
    if (tool == 'start') {
      document.body.style.cursor = 'pointer';
    }
  }

  /**
   * 飞行定位，传入笛卡尔C3坐标点
   * @param {*} lon 经度
   * @param {*} lat 纬度
   * @param {*} height 高度
   * @param {*} heading 偏航角
   * @param {*} pitch 俯仰角
   * @param {*} range 范围（米），代表相机距离目标的距离
   * @param {*} duration 飞行时间，秒
   */
  flyTo(position, height, heading, pitch, range, duration) {
    //const center = Cesium.Cartesian3.fromDegrees(lon, lat);
    const h = Cesium.Math.toRadians(heading);
    const p = Cesium.Math.toRadians(pitch);
    const r = range;
    this.viewer.camera.flyToBoundingSphere(new Cesium.BoundingSphere(position, height), {
      offset: new Cesium.HeadingPitchRange(h, p, r),
      duration: duration
    });
  }
  /**
   * 飞行定位,传入经纬度
   * @param {*} lon 经度
   * @param {*} lat 纬度
   * @param {*} height 高度
   * @param {*} heading 偏航角
   * @param {*} pitch 俯仰角
   * @param {*} range 范围（米），代表相机距离目标的距离
   * @param {*} duration 飞行时间，秒
   */
  flyTo2(lon, lat, height, heading, pitch, range, duration) {
    const position = Cesium.Cartesian3.fromDegrees(lon, lat);
    const h = Cesium.Math.toRadians(heading);
    const p = Cesium.Math.toRadians(pitch);
    const r = range;
    this.viewer.camera.flyToBoundingSphere(new Cesium.BoundingSphere(position, height), {
      offset: new Cesium.HeadingPitchRange(h, p, r),
      duration: duration
    });
  }

  /**
   * 飞行定位,传入经纬度
   * @param {*} lon 经度
   * @param {*} lat 纬度
   * @param {*} height 高度
   * @param {*} heading 偏航角
   * @param {*} pitch 俯仰角
   * @param {*} range 范围（米），代表相机距离目标的距离
   * @param {*} duration 飞行时间，秒
   */
  flyTo3(lon, lat, height, heading, pitch, range, duration, completeCallback) {
    // const position = Cesium.Cartesian3.fromDegrees(lon, lat);
    const h = Cesium.Math.toRadians(heading);
    const p = Cesium.Math.toRadians(pitch);
    const r = range;
    this.viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(lon, lat, height),
      orientation: {
        heading: h,
        pitch: p,
        roll: 0 // 如果不需要翻滚，可以设置为0
      },
      // offset: new Cesium.HeadingPitchRange(h, p, r),
      duration: duration || 1000,
      complete: completeCallback() // 添加完成后的回调函数
    });
  }
  /**
   * 设置起飞点
   * @param {*} lon 起飞点经度
   * @param {*} lat 纬度
   * @param {*} height 海拔高度
   * @param {*} waylineMode 安全模式/倾斜模式
   * @param {*} addSecurityHeight 增加的安全高度
   * @returns
   */
  setFlyPoint(lon, lat, height, waylineMode, addSecurityHeight) {
    this.flyStartPoint = [lon, lat, height];
    this.curUAVPoint = Cesium.Cartesian3.fromDegrees(lon, lat, this.aslHeight);
    let startLinePos = Cesium.Cartesian3.fromDegrees(lon, lat, this.aslHeight);
    //存在起飞点对象，先移除相关对象
    if (this.planPointJson.length > 0) {
      this.viewer.scene.primitives.remove(this.planPointJson[0].planLineEntity[0]); //特效线是primitive
      this.viewer.entities.remove(this.planPointJson[0].planLineEntity[1]);
      this.viewer.entities.remove(this.planPointJson[0].pointEntity);
    }
    //飞向第一航点时的高度
    let flyHeight = this.aslHeight;
    //倾斜模式
    if (waylineMode === 'pointToPoint') {
      flyHeight = height + addSecurityHeight;
      startLinePos = Cesium.Cartesian3.fromDegrees(lon, lat, flyHeight);
    }
    // //倾斜模式
    // else {
    //   startLinePos = Cesium.Cartesian3.fromDegrees(lon, lat, height + templateJsons.wpml_missionConfig.wpml_takeOffSecurityHeight);
    // }
    let hpr = new Cesium.HeadingPitchRoll(Cesium.Math.toRadians(0), 0, 0);
    this.wrjHeight = this.aslHeight;
    this.setModel(this.curUAVPoint, hpr);
    //起飞点图标
    let startEntity = this.viewer.entities.add({
      id: '起飞点',
      name: '起飞点',
      position: Cesium.Cartesian3.fromDegrees(lon, lat, height),
      //图标
      billboard: {
        image: './resource/images/plan/起飞点.png',
        width: 32,
        height: 32,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        // disableDepthTestDistance: Number.POSITIVE_INFINITY // 点总是在最上层
      },
      zIndex: 1
    });
    //起飞点离无人机的线
    let color = Cesium.Color.YELLOW;
    let firstPos = Cesium.Cartesian3.fromDegrees(this.flyStartPoint[0], this.flyStartPoint[1], this.flyStartPoint[2]);
    let linePos = [firstPos, startLinePos];
    let colors = [];
    for (let i = 0; i < 40; ++i) {
      colors.push(Cesium.Color.fromRandom({ alpha: 1.0 }));
    }
    // let linePos = [
    //   this.flyStartPoint[0], this.flyStartPoint[1],0,
    //   this.flyStartPoint[0], this.flyStartPoint[1], this.altHeight
    // ];
    //起飞点到安全高度的线，特效线
    let startLinePrimitive = this.viewer.scene.primitives.add(
      new Cesium.Primitive({
        id: '起飞点_line1',
        geometryInstances: new Cesium.GeometryInstance({
          geometry: new Cesium.PolylineGeometry({
            positions: linePos, //Cesium.Cartesian3.fromDegreesArrayHeights(linePos),
            width: 5.0,
            //vertexFormat: Cesium.PolylineColorAppearance.VERTEX_FORMAT,
            colors: colors
            // colorsPerVertex : true
          })
          // attributes: {
          //   color: Cesium.ColorGeometryInstanceAttribute.fromColor(new Cesium.Color(1.0, 0.0, 1.0, 1.0))
          // }
        }),
        //appearance : new Cesium.PolylineColorAppearance()
        appearance: new Cesium.PolylineMaterialAppearance({
          material: polylinematerial.Polylineglowflow(Cesium.Color.YELLOW)
        })
      })
    );
    //起飞点到安全高度的线，白线
    let startLineEntity2 = this.viewer.entities.add({
      id: '起飞点_line2',
      polyline: {
        positions: linePos,
        width: 5,
        material: Cesium.Color.WHITE.withAlpha(0.4)
      }
    });

    let json = {
      index: 0,
      type: '起飞点',
      lon: lon,
      lat: lat,
      flow: this.planPointFlow,
      waylineMode: waylineMode,
      startHeight: height, //起飞点海拔高度
      terrainHeight: height, //地形高度
      globalHeight: this.globalHeight,
      height: flyHeight,
      UAVHPR: hpr, //无人机方位
      position: startLinePos, //无人机往第一个航线点的位置
      planLineEntity: [startLinePrimitive, startLineEntity2], //起飞路线
      pointEntity: startEntity, //起飞点
      lineEntity: null, //离地线
      dPointEntity: null, //航点地面点
      length: 0 //无人机与起飞点的距离
    };
    if (this.planPointJson.length == 0) {
      this.planPointJson.push(json);
    } else {
      this.planPointJson[0] = json;
      if (this.planPointJson.length > 1) {
        const planPoint1 = this.planPointJson[1];

        //需要重新更新与航点1的连线及距离
        //1、先移除掉原来的线
        if (planPoint1.planLineEntity != null) {
          this.viewer.scene.primitives.remove(planPoint1.planLineEntity[0]); //特效线是primitive
          this.viewer.entities.remove(planPoint1.planLineEntity[1]);
        }
        if (planPoint1.position != null) {
          let linePos = [startLinePos, planPoint1.position];

          //以下是如果当前选中的为航点1，则需要重新生成显示距离的对象
          if (this.curPlanPointJson != null && this.curPlanPointJson.index == 0) {
            if (this.curDictEntity != null) {
              this.viewer.entities.remove(this.curDictEntity);
            }
            // 计算中点
            const centerPoint = Cesium.Cartesian3.midpoint(startLinePos, planPoint1.position, new Cesium.Cartesian3());
            //距离格式化
            let lenghtStr = this.getLengthStr(this.curPlanPointJson.length);

            this.curDictEntity = this.addLabel(centerPoint, lenghtStr);
          }

          //2、添加新的线
          const lines = this.addLine(1, linePos);

          //3、计算距离
          length = this.getLength(startLinePos, planPoint1.position);

          //重新计算总长度和时间
          this.getAllLengthAndTime();
          this.planPointJson[1].planLineEntity = lines;
          this.planPointJson[1].length = length;
        }
      }
    }
    return json;
  }

  /**
   * 无人机模型位置设置
   * @param {*} pos Cesium.Cartesian3
   * @param {*} hpr Cesium.HeadingPitchRoll
   * @param {*} orientation Cesium.Quaternion
   */
  async setModel(pos, hpr) {
    if (this.model == null) {
      this.model = new GlbModel(this.viewer, './model/三棱箭头.glb', toCartesian3(pos));
      this.curUAVPoint = pos;
      this.setEyeViewer(pos, hpr.heading, hpr.pitch, hpr.roll);
      let option = {
        viewer: this.viewer,
        position: pos,
        fov: this.fov,
        near: this.near,
        far: this.far,
        heading: Cesium.Math.toDegrees(hpr.heading),
        pitch: 0,
        roll: -90,
        width: 60,
        height: 45
      };
      // 获取功能对应的视锥体的对象参数
      const actionFrustum = getFrustum('action');
      const actionFrustumOption = actionFrustum?.getOptions() || null;
      if (actionFrustumOption) {
        option = {
          viewer: this.viewer,
          position: pos,
          fov: actionFrustumOption.fov || this.fov,
          near: actionFrustumOption.near || this.near,
          far: actionFrustumOption.far || this.far,
          heading: actionFrustumOption.heading || Cesium.Math.toDegrees(hpr.heading),
          pitch: 0,
          roll: -90,
          width: 60,
          height: 45
        };
      } else {
      }
      // 创建视锥体
      this.eyeFrustum = createFrustum(option, 'camera');
    } else {
      let { hpr: HeadingPitchRoll, h } = this.model.getHpr();
      this.curUAVPoint = pos;
      this.model.setPosition(this.curUAVPoint);
      this.setEyeViewer(
        toCartesian3(this.curUAVPoint),
        HeadingPitchRoll.heading,
        HeadingPitchRoll.pitch,
        HeadingPitchRoll.roll
      );
      // 获取要处理的
      // 更新之前需要获取视锥体的配置参数
      const { fov, near, far, heading, pitch, roll, width, height } = this.eyeFrustum?.getOptions() ?? {};
      const curFov = this.fov === fov ? this.fov : fov;
      const curNear = this.near === near ? this.near : near;
      const curFar = this.far === far ? this.far : far;
      const curHeading = this.model.getHeading() || heading;
      const curRoll = roll;
      const curWidth = 60;
      const curHeight = 45;
      // 同步更新
      const option = {
        position: pos,
        fov: curFov,
        near: curNear,
        far: curFar,
        heading: curHeading,
        pitch: 0,
        roll: curRoll,
        width: curWidth,
        height: curHeight
      };
      this.eyeFrustum.update(option);
      updateAllFrustum(option);
    }
    //计算地形的高度
    try {
      const cartographic = Cesium.Cartographic.fromCartesian(pos);
      const updatedPositions = await Cesium.sampleTerrainMostDetailed(this.customTerrainProvider, [cartographic]);
      this.wrjTerrainHeight = updatedPositions[0].height;
    } catch (error) {
      this.wrjTerrainHeight = 0;
    }
  }

  updateFrustumAndEyeViewer(options) {
    try {
      // 这里解构的大
      const {
        position,
        fov = null,
        near = null,
        far = null,
        heading = 0,
        pitch = 0,
        roll = 0,
        width,
        height
      } = options;
      this.fov = fov || this.fov;
      this.near = near || this.near;
      this.far = far || this.far;
      // 需要变换 不然两个坐标不一致
      // this.setEyeViewer(toCartesian3(position), heading, pitch, roll);
      // 飞机 都不起飞 只是视锥体角度调整
      // this.setEyeViewer(toCartesian3(position), hpr.heading, Cesium.Math.toRadians(this.pitch), hpr.roll);
      const hpr = Cesium.HeadingPitchRoll(heading, pitch, roll);
      this.setEyeViewer(toCartesian3(position), hpr.heading, hpr.pitch, hpr.roll);
      // 更新之前需要获取视锥体的配置参数
      const curFov = this.fov;
      const curNear = this.near;
      const curFar = this.far;
      const curHeading = heading;
      const curRoll = roll;
      const curWidth = width;
      const curHeight = height;
      this.eyeFrustum.update({
        position: toCartesian3(position),
        fov: curFov,
        near: curNear,
        far: curFar,
        heading: curHeading,
        pitch: 0,
        roll: curRoll,
        width: curWidth,
        height: curHeight
      });
    } catch (error) {}
  }

  /**
   * 添加航点
   * @param {*} position Cesium.Cartesian3
   */
  addPoint(position, terrainHeight) {
    try {
      if (terrainHeight == undefined || terrainHeight == null) {
        terrainHeight = 0;
      }
      let length = 0;
      //航线
      let lines;
      let index = this.planPointJson.length;
      if (this.planPointJson.length > 0) {
        let lastPointJson = this.planPointJson[this.planPointJson.length - 1];
        let lastPosition = lastPointJson.position;
        let linePos = [lastPosition, position];

        //连接线
        lines = this.addLine(index, linePos);
        length = this.getLength(lastPosition, position);
      }

      //航点
      let pointEntity = this.viewer.entities.add({
        id: '航点' + index,
        position: position,
        label: {
          //   //文字标签
          text: '' + index,
          font: '15px sans-serif',
          fillColor: Cesium.Color.BLACK,
          //  outlineColor: Cesium.Color.YELLOW,
          //  outlineWidth:2,
          style: Cesium.LabelStyle.FILL,
          // 对齐方式(水平和竖直)
          // horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
          verticalOrigin: Cesium.VerticalOrigin.CENTER,
          pixelOffset: new Cesium.Cartesian2(1, 0),
          showBackground: true,
          backgroundColor: Cesium.Color.fromAlpha(Cesium.Color.LIME, 1),
          backgroundPadding: new Cesium.Cartesian2(3, 5),
          disableDepthTestDistance: Number.POSITIVE_INFINITY
        },
        //图标
        billboard: {
          image: './resource/images/plan/placemark.png',
          width: 43,
          height: 43,
          disableDepthTestDistance: Number.POSITIVE_INFINITY
        }
      });
      let cartographic = this.viewer.scene.globe.ellipsoid.cartesianToCartographic(position);
      let lat = Cesium.Math.toDegrees(cartographic.latitude); //经度
      let lng = Cesium.Math.toDegrees(cartographic.longitude); //纬度
      let height = cartographic.height;

      // 计算新位置
      const dPos = new Cesium.Cartesian3.fromDegrees(lng, lat, terrainHeight);

      // 航点离地面的虚线
      let lineEntity = this.viewer.entities.add({
        polyline: {
          positions: [position, dPos],
          width: 3,
          material: new Cesium.PolylineDashMaterialProperty({
            color: Cesium.Color.WHITE,
            dashLength: 10 //短划线长度
          })
          //material: Cesium.Color.WHITE
        }
      });

      // 地面的点
      let dPointEntity = this.viewer.entities.add({
        // 给初始点位设置一定的离地高度，否者会被压盖
        position: dPos,
        point: {
          color: Cesium.Color.WHITE,
          pixelSize: 8
        }
      });
      // 获取当前模型的hpr
      const { hpr } = this.model.getHpr();
      // let heading1 = parseInt(Cesium.Math.toDegrees(hpr.heading));
      // let pitch1 = parseInt(Cesium.Math.toDegrees(hpr.pitch));
      // let roll1 = parseInt(Cesium.Math.toDegrees(hpr.roll));

      let json = {
        index: index - 1,
        type: '航点',
        lon: lng,
        lat: lat,
        flow: this.planPointFlow,
        startHeight: this.flyStartPoint[2], //起飞点海拔高度
        terrainHeight: terrainHeight, //地形高度
        globalHeight: this.globalHeight,
        height: height, //无人机的高度
        UAVHPR: hpr, //方位
        position: position, //航点位置
        planLineEntity: lines, //航线对象
        pointEntity: pointEntity, //航点对象
        lineEntity: lineEntity, //航点离地虚线
        dPointEntity: dPointEntity, //航点地面点
        length: length
      };
      this.planPointJson.push(json);
      //重新计算总长度和时间
      this.getAllLengthAndTime();
      return json;
    } catch (error) {}
  }

  /**
   * 添加航点到航点列表，提交用的航点列表
   * @param {*} json {lon,lat,wpml_ellipsoidHeight,wpml_height,wpml_waypointSpeed,wpml_waypointHeadingMode,wpml_waypointTurnMode}
   */
  addPlacemark(json) {
    const placemark = {
      Point: {
        coordinates: json.lon + ',' + json.lat
      },
      wpml_index: this.placemarkJson.length, //航点序号，* 注：在一条航线内该ID唯一。
      wpml_ellipsoidHeight: json.wpml_ellipsoidHeight, //全局航线高度（椭球高），需要转换
      wpml_height: json.wpml_height, //全局航线高度（EGM96海拔高/相对起飞点高度/AGL相对地面高度）
      wpml_useGlobalHeight: 1, //是否使用全局高度
      wpml_useGlobalSpeed: 1, //是否使用全局飞行速度，* 注：此处的全局飞行速度即“wpml:autoFlightSpeed”
      wpml_waypointSpeed: json.wpml_waypointSpeed, //航点飞行速度，* 注：当且仅当“wpml:useGlobalSpeed”为“0”时必需
      wpml_useGlobalHeadingParam: 1, //是否使用全局偏航角模式参数
      wpml_useGlobalTurnParam: 1, //是否使用全局航点类型（全局航点转弯模式）
      wpml_useStraightLine: 1, //该航段是否贴合直线
      wpml_waypointHeadingParam: {
        wpml_waypointHeadingMode: json.wpml_waypointHeadingMode,
        wpml_waypointHeadingAngle: 0,
        wpml_waypointPoiPoint: '0.000000,0.000000,0.000000', //兴趣点，数据格式为：纬度,经度,高度。当wpml:waypointHeadingMode为towardPOI该字段生效。
        wpml_waypointHeadingPathMode: 'followBadArc', //clockwise：顺时针旋转飞行器偏航角;counterClockwise：逆时针旋转飞行器偏航角;followBadArc：沿最短路径旋转飞行器偏航角
        wpml_waypointHeadingPoiIndex: 0
      },
      wpml_waypointTurnParam: {
        wpml_waypointTurnMode: json.wpml_waypointTurnMode, //航点类型（航点转弯模式）
        wpml_waypointTurnDampingDist: 8 //航点转弯截距
      }
      // wpml_gimbalPitchAngle: 0,//航点云台俯仰角，* 注：当且仅当“wpml:gimbalPitchMode”为“usePointSetting”时必需。
      // //航点动作组
      // wpml_actionGroup: {
      //   wpml_actionGroupId: this.actionGroup,//动作组id。* 注：在一个kmz文件内该ID唯一。建议从0开始单调连续递增。
      //   wpml_actionGroupStartIndex: placemarkIndex,//动作组开始生效的航点
      //   wpml_actionGroupEndIndex: placemarkIndex,//动作组结束生效的航点，* 注：当“动作组结束生效的航点”与“动作组开始生效的航点”一致，则代表该动作组仅在该航点处生效。
      //   wpml_actionGroupMode: "sequence",//动作执行模式，sequence：串行执行。即动作组内的动作依次按顺序执行。
      //   wpml_actionTrigger: {
      //     //动作触发器类型,reachPoint：到达航点时执行；betweenAdjacentPoints：航段触发，均匀转云台；multipleTiming：等时触发；multipleDistance：等距触发；
      //     //* 注：“betweenAdjacentPoints”需配合动作"gimbalEvenlyRotate"使用，“multipleTiming” 配合动作 “takePhoto” 即可实现等时间隔拍照，“multipleDistance” 配合动作 “takePhoto” 即可实现等距离间隔拍照。
      //     wpml_actionTriggerType: "reachPoint",
      //     //wpml:actionTriggerParam: 1, //> 0，* 注：当“actionTriggerType”为“multipleTiming”时，该元素表示间隔时间，单位是s。当“actionTriggerType”为“multipleDistance”时，该元素表示间隔距离，单位是m。
      //   },
      //   //动作列表
      //   wpml_action:{
      //     wpml_actionId:0,//动作id，* 注：在一个动作组内该ID唯一。建议从0开始单调连续递增
      //     //动作类型
      //     // takePhoto：单拍;startRecord：开始录像;stopRecord：结束录像;focus：对焦;zoom：变焦;customDirName：创建新文件夹
      //     // gimbalRotate：旋转云台;rotateYaw：飞行器偏航;hover：悬停等待; gimbalEvenlyRotate：航段间均匀转动云台pitch角;
      //     // orientedShoot：精准复拍动作; panoShot：全景拍照动作（仅支持M30/M30T）; recordPointCloud：点云录制操作
      //     wpml_actionActuatorFunc:"takePhoto",
      //     //动作参数
      //     wpml_actionActuatorFuncParam:{
      //       wpml_payloadPositionIndex:0,
      //       wpml_useGlobalPayloadLensIndex:1,//是否使用全局存储类型,0：不使用全局设置;1：使用全局设置
      //     }
      //   }
      // }
    };
    //console.log('111 add:', placemarkJsons, placemark);
    //添加到航点列表
    this.placemarkJson.push(placemark);
    return placemark;
  }

  // /**
  //  * 设置/更新航点位置
  //  * @param {*} planPointIndex 航线索引
  //  * @param {*} position Cesium.Cartesian3
  //  */
  // setPoint(planPointIndex, position) {
  //   const index = planPointIndex + 1;
  //   if (this.planPointJson.length > index && index != 0) {
  //     const planPoint = this.planPointJson[index];
  //     //先移除相关对象
  //     this.viewer.scene.primitives.remove(planPoint.planLineEntity[0]); //特效线是primitive
  //     this.viewer.entities.remove(planPoint.planLineEntity[1]);
  //     this.viewer.entities.remove(planPoint.pointEntity); //删除航点
  //     this.viewer.entities.remove(planPoint.lineEntity);
  //     this.viewer.entities.remove(planPoint.dPointEntity);

  //     //重新生成当前航点的连接线
  //     const linePos = [this.planPointJson[index - 1].position, position];
  //     const lines = this.addLine(index, linePos);

  //     //航点
  //     let pointEntity = this.viewer.entities.add({
  //       id: '航点' + index,
  //       position: position,
  //       label: {
  //         //   //文字标签
  //         text: '' + index,
  //         font: '15px sans-serif',
  //         fillColor: Cesium.Color.BLACK,
  //         //  outlineColor: Cesium.Color.YELLOW,
  //         //  outlineWidth:2,
  //         style: Cesium.LabelStyle.FILL,
  //         // 对齐方式(水平和竖直)
  //         // horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
  //         verticalOrigin: Cesium.VerticalOrigin.CENTER,
  //         pixelOffset: new Cesium.Cartesian2(1, 0),
  //         showBackground: true,
  //         backgroundColor: Cesium.Color.fromAlpha(Cesium.Color.LIME, 1),
  //         backgroundPadding: new Cesium.Cartesian2(3, 5),
  //         disableDepthTestDistance: Number.POSITIVE_INFINITY
  //       },
  //       //图标
  //       billboard: {
  //         image: './resource/images/plan/placemark.png',
  //         width: 43,
  //         height: 43,
  //         disableDepthTestDistance: Number.POSITIVE_INFINITY
  //       }
  //     });

  //     let cartographic = this.viewer.scene.globe.ellipsoid.cartesianToCartographic(position);
  //     let lat = Cesium.Math.toDegrees(cartographic.latitude); //经度
  //     let lng = Cesium.Math.toDegrees(cartographic.longitude); //纬度
  //     let height = cartographic.height;

  //     //     //计算点与地形的相交点
  //     //     // 指定一个方向向量，例如指向北方的单位向量
  //     //     let direction = new Cesium.Cartesian3(0.0, 0.0, 1.0);
  //     //     // 创建一条射线，起点是相机位置，方向是指定的向量
  //     //     let ray = new Cesium.Ray(position, direction);
  //     //     // 使用globe.pick方法找到射线与地球表面的交点
  //     // let intersection = this.viewer.scene.globe.pick(this.viewer.scene.camera.getPickRay(this.viewer.canvas.width / 2, this.viewer.canvas.height / 2), this.viewer.scene);

  //     // // 如果存在相交点，将其转换为世界坐标
  //     // if (Cesium.defined(intersection)) {
  //     //     intersection = Cesium.Ellipsoid.WGS84.cartesianToCartographic(intersection);
  //     //     console.log("相交点的经纬度：", intersection);
  //     // } else {
  //     //     console.log("没有找到相交点");
  //     // }

  //     const dPos = new Cesium.Cartesian3.fromDegrees(lng, lat, 0);

  //     // 航点离地面的虚线
  //     let linePositions = [];
  //     linePositions.push(position);
  //     linePositions.push(dPos);
  //     let lineEntity = this.viewer.entities.add({
  //       polyline: {
  //         positions: linePositions,
  //         width: 3,
  //         material: new Cesium.PolylineDashMaterialProperty({
  //           color: Cesium.Color.WHITE,
  //           dashLength: 10 //短划线长度
  //         })
  //         //material: Cesium.Color.WHITE
  //       }
  //     });

  //     // 地面的点
  //     let dPointEntity = this.viewer.entities.add({
  //       // 给初始点位设置一定的离地高度，否者会被压盖
  //       position: dPos,
  //       point: {
  //         color: Cesium.Color.WHITE,
  //         pixelSize: 8
  //       }
  //     });

  //     //赋值
  //     planPoint.lon = lng;
  //     planPoint.lat = lat;
  //     planPoint.height = height;
  //     planPoint.position = position;
  //     planPoint.planLineEntity = lines;
  //     planPoint.lineEntity = lineEntity;
  //     planPoint.pointEntity = pointEntity;
  //     planPoint.dPointEntity = dPointEntity;
  //     planPoint.length = this.getLength(linePos[0], linePos[1]);
  //     this.setPlacemark(planPointIndex, {
  //       lon: lng,
  //       lat: lat,
  //       wpml_ellipsoidHeight: height,
  //       wpml_height: height,
  //       wpml_waypointSpeed: null,
  //       wpml_waypointHeadingMode: null,
  //       wpml_waypointTurnMode: null
  //     });

  //     //重新生成与下一个航点的连接线
  //     if (index + 1 < this.planPointJson.length) {
  //       this.viewer.scene.primitives.remove(this.planPointJson[index + 1].planLineEntity[0]); //特效线是primitive
  //       this.viewer.entities.remove(this.planPointJson[index + 1].planLineEntity[1]);

  //       const linePos2 = [position, this.planPointJson[index + 1].position];
  //       const lines2 = this.addLine(index + 1, linePos2);
  //       this.planPointJson[index + 1].planLineEntity = lines2;
  //       this.planPointJson[index + 1].length = this.getLength(linePos2[0], linePos2[1]);
  //     }
  //   }
  // }

  /**
   * 设置航点信息
   * @param {*} index 航点序号
   * @param {*} json {lon,lat,wpml_ellipsoidHeight,wpml_height,wpml_waypointSpeed,wpml_waypointHeadingMode,wpml_waypointTurnMode}
   */
  setPlacemark(index, json) {
    this.placemarkJson[index].wpml_index = index; //航点序号
    if (json.lon != null && json.lat != null) this.placemarkJson[index].Point.coordinates = json.lon + ',' + json.lat; //坐标
    if (json.wpml_ellipsoidHeight != null) this.placemarkJson[index].wpml_ellipsoidHeight = json.wpml_ellipsoidHeight; //全局航线高度（椭球高），需要转换
    if (json.wpml_height != null) this.placemarkJson[index].wpml_height = json.wpml_height; //全局航线高度（EGM96海拔高
    if (json.wpml_waypointSpeed != null) this.placemarkJson[index].wpml_waypointSpeed = json.wpml_waypointSpeed;
    if (json.wpml_waypointHeadingMode != null)
      this.placemarkJson[index].wpml_waypointHeadingParam.wpml_waypointHeadingMode = json.wpml_waypointHeadingMode;
    if (json.wpml_waypointTurnMode != null)
      this.placemarkJson[index].wpml_waypointTurnParam.wpml_waypointTurnMode = json.wpml_waypointTurnMode;
    return this.placemarkJson[index];
  }

  /**
   * 删除航点
   * @param {*} planPointIndex 航点索引
   */
  deletePoint(planPointIndex) {
    const index = planPointIndex + 1;
    if (this.planPointJson.length > index && index != 0) {
      const planPoint = this.planPointJson[index];
      //先移除相关对象
      this.viewer.scene.primitives.remove(planPoint.planLineEntity[0]); //特效线是primitive
      this.viewer.entities.remove(planPoint.planLineEntity[1]);
      this.viewer.entities.remove(planPoint.pointEntity); //删除航点
      this.viewer.entities.remove(planPoint.lineEntity);
      this.viewer.entities.remove(planPoint.dPointEntity);

      //从JSON中移除航点
      this.planPointJson.splice(index, 1);
      this.placemarkJson.splice(planPointIndex, 1);
      //重绘航点
      this.refreshPoint(planPointIndex);
    }
  }

  /**
   * 刷新航点，重新刷新地图上的航点
   * @param {*} refreshIndex 从该航点索引开始刷新后续航点
   * @param {*} addHeight 增加的距离
   * @param {*} isAlladd 是否全部增加，用于切换绝对高度和相对高度时的高度差计算，但不刷新JSON航点的值
   */
  refreshPoint(refreshIndex, addHeight, isAlladd) {
    try {
      let startIndex = 1;
      if (refreshIndex) {
        startIndex = refreshIndex + 1; //第一个点为起飞点。所以实际航点需要加一
      }
      if (refreshIndex == 0) {
        const startFlyPoint = this.planPointJson[0];
        this.setFlyPoint(
          startFlyPoint.lon,
          startFlyPoint.lat,
          startFlyPoint.startHeight,
          startFlyPoint.waylineMode,
          startFlyPoint.height - startFlyPoint.startHeight
        );
      }

      const { hpr } = this.model.getHpr();
      for (let i = startIndex; i < this.planPointJson.length; i++) {
        const planPoint = this.planPointJson[i];
        //先该航点地图上的对象
        if (planPoint.planLineEntity != null) {
          this.viewer.scene.primitives.remove(planPoint.planLineEntity[0]); //特效线是primitive
          this.viewer.entities.remove(planPoint.planLineEntity[1]);
        }
        if (planPoint.pointEntity != null) this.viewer.entities.remove(planPoint.pointEntity); //删除航点
        if (planPoint.lineEntity != null) this.viewer.entities.remove(planPoint.lineEntity);
        if (planPoint.dPointEntity != null) this.viewer.entities.remove(planPoint.dPointEntity);

        let height = planPoint.height;
        let gHeight = planPoint.globalHeight;
        let position = planPoint.position;
        if (addHeight) {
          if (isAlladd == true || planPoint.flow == true) {
            height += addHeight;
            gHeight += addHeight;
          }
        }

        position = new Cesium.Cartesian3.fromDegrees(planPoint.lon, planPoint.lat, height);

        //1、航点对象
        let pointEntity = this.viewer.entities.add({
          id: '航点' + i,
          position: position,
          label: {
            //   //文字标签
            text: '' + i,
            font: '15px sans-serif',
            fillColor: Cesium.Color.BLACK,
            //  outlineColor: Cesium.Color.YELLOW,
            //  outlineWidth:2,
            style: Cesium.LabelStyle.FILL,
            // 对齐方式(水平和竖直)
            // horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
            verticalOrigin: Cesium.VerticalOrigin.CENTER,
            pixelOffset: new Cesium.Cartesian2(1, 0),
            showBackground: true,
            backgroundColor: Cesium.Color.fromAlpha(Cesium.Color.LIME, 1),
            backgroundPadding: new Cesium.Cartesian2(3, 5),
            disableDepthTestDistance: Number.POSITIVE_INFINITY
          },
          //图标
          billboard: {
            image: './resource/images/plan/placemark.png',
            width: 43,
            height: 43,
            disableDepthTestDistance: Number.POSITIVE_INFINITY
          }
        });

        const dPos = new Cesium.Cartesian3.fromDegrees(planPoint.lon, planPoint.lat, planPoint.terrainHeight);

        //航点离地面的虚线
        let lineEntity = this.viewer.entities.add({
          polyline: {
            positions: [position, dPos],
            width: 3,
            material: new Cesium.PolylineDashMaterialProperty({
              color: Cesium.Color.WHITE,
              dashLength: 10 //短划线长度
            })
            //material: Cesium.Color.WHITE
          }
        });

        // 地面的点
        let dPointEntity = this.viewer.entities.add({
          // 给初始点位设置一定的离地高度，否者会被压盖
          position: dPos,
          point: {
            color: Cesium.Color.WHITE,
            pixelSize: 8
          }
        });

        //2、航点连接线
        const linePos = [this.planPointJson[i - 1].position, position];
        const lines = this.addLine(i, linePos);

        //赋值
        planPoint.index = i - 1;
        planPoint.height = height;

        if (planPoint.UAVHPR == null) {
          planPoint.UAVHPR = hpr;
        }
        planPoint.position = position;
        planPoint.planLineEntity = lines;
        planPoint.lineEntity = lineEntity;
        planPoint.pointEntity = pointEntity;
        planPoint.dPointEntity = dPointEntity;
        planPoint.length = this.getLength(linePos[0], linePos[1]);
        //切换绝对高度和相对高度时，不刷新航点
        if (isAlladd == undefined || isAlladd == null || isAlladd == false) {
          planPoint.globalHeight = gHeight;
          //刷新航点信息
          this.setPlacemark(i - 1, {
            lon: planPoint.lon,
            lat: planPoint.lat,
            wpml_ellipsoidHeight: gHeight,
            wpml_height: gHeight,
            wpml_waypointSpeed: null,
            wpml_waypointHeadingMode: null,
            wpml_waypointTurnMode: null
          });
        }
      }
      //重新计算长度和预估时间
      this.getAllLengthAndTime();
      if (this.planPointJson.length > 0) return this.planPointJson[0];
      else return null;
    } catch (error) {
      console.log('err:', error);
    }
  }

  /**
   * 刷新航点，重新刷新地图上的航点
   * @param {*} refreshIndex 从该航点索引开始刷新后续航点
   * @param {*} addHeight 增加的距离
   * @param {*} isAlladd 是否全部增加，用于切换绝对高度和相对高度时的高度差计算，但不刷新JSON航点的值
   */
  refreshPoint2(refreshIndex, addHeight, isAlladd) {
    try {
      let startIndex = 1;
      if (refreshIndex) {
        startIndex = refreshIndex + 1; //第一个点为起飞点。所以实际航点需要加一
      }
      // 获取
      if (refreshIndex == 0) {
        const startFlyPoint = this.planPointJson[0];
        this.setFlyPoint(
          startFlyPoint.lon,
          startFlyPoint.lat,
          startFlyPoint.startHeight,
          startFlyPoint.waylineMode,
          startFlyPoint.height - startFlyPoint.startHeight
        );
      }

      const { hpr } = this.model.getHpr();
      for (let i = startIndex; i < this.planPointJson.length; i++) {
        const planPoint = this.planPointJson[i];
        //先该航点地图上的对象
        if (planPoint.planLineEntity != null) {
          this.viewer.scene.primitives.remove(planPoint.planLineEntity[0]); //特效线是primitive
          this.viewer.entities.remove(planPoint.planLineEntity[1]);
        }
        if (planPoint.pointEntity != null) this.viewer.entities.remove(planPoint.pointEntity); //删除航点
        if (planPoint.lineEntity != null) this.viewer.entities.remove(planPoint.lineEntity);
        if (planPoint.dPointEntity != null) this.viewer.entities.remove(planPoint.dPointEntity);

        let height = planPoint.height;
        let gHeight = planPoint.globalHeight;
        let position = planPoint.position;
        if (addHeight) {
          if (isAlladd == true || planPoint.flow == true) {
            height += addHeight;
            gHeight += addHeight;
          }
        }

        position = new Cesium.Cartesian3.fromDegrees(planPoint.lon, planPoint.lat, height);

        //1、航点对象
        let pointEntity = this.viewer.entities.add({
          id: '航点' + i,
          position: position,
          label: {
            //   //文字标签
            text: '' + i,
            font: '15px sans-serif',
            fillColor: Cesium.Color.BLACK,
            //  outlineColor: Cesium.Color.YELLOW,
            //  outlineWidth:2,
            style: Cesium.LabelStyle.FILL,
            // 对齐方式(水平和竖直)
            // horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
            verticalOrigin: Cesium.VerticalOrigin.CENTER,
            pixelOffset: new Cesium.Cartesian2(1, 0),
            showBackground: true,
            backgroundColor: Cesium.Color.fromAlpha(Cesium.Color.LIME, 1),
            backgroundPadding: new Cesium.Cartesian2(3, 5),
            disableDepthTestDistance: Number.POSITIVE_INFINITY
          },
          //图标
          billboard: {
            image: './resource/images/plan/placemark.png',
            width: 43,
            height: 43,
            disableDepthTestDistance: Number.POSITIVE_INFINITY
          }
        });

        const dPos = new Cesium.Cartesian3.fromDegrees(planPoint.lon, planPoint.lat, planPoint.terrainHeight);

        //航点离地面的虚线
        let lineEntity = this.viewer.entities.add({
          polyline: {
            positions: [position, dPos],
            width: 3,
            material: new Cesium.PolylineDashMaterialProperty({
              color: Cesium.Color.WHITE,
              dashLength: 10 //短划线长度
            })
            //material: Cesium.Color.WHITE
          }
        });

        // 地面的点
        let dPointEntity = this.viewer.entities.add({
          // 给初始点位设置一定的离地高度，否者会被压盖
          position: dPos,
          point: {
            color: Cesium.Color.WHITE,
            pixelSize: 8
          }
        });

        //2、航点连接线
        const linePos = [this.planPointJson[i - 1].position, position];
        const lines = this.addLine(i, linePos);

        //赋值
        planPoint.index = i - 1;
        planPoint.height = height;

        if (planPoint.UAVHPR == null) {
          planPoint.UAVHPR = hpr;
        }
        planPoint.position = position;
        planPoint.planLineEntity = lines;
        planPoint.lineEntity = lineEntity;
        planPoint.pointEntity = pointEntity;
        planPoint.dPointEntity = dPointEntity;
        planPoint.length = this.getLength(linePos[0], linePos[1]);
        //切换绝对高度和相对高度时，不刷新航点
        if (isAlladd == undefined || isAlladd == null || isAlladd == false) {
          planPoint.globalHeight = gHeight;
          //刷新航点信息
          this.setPlacemark(i - 1, {
            lon: planPoint.lon,
            lat: planPoint.lat,
            wpml_ellipsoidHeight: gHeight,
            wpml_height: gHeight,
            wpml_waypointSpeed: null,
            wpml_waypointHeadingMode: null,
            wpml_waypointTurnMode: null
          });
        }
      }
      //重新计算长度和预估时间
      this.getAllLengthAndTime();
      if (this.planPointJson.length > 0) return this.planPointJson[0];
      else return null;
    } catch (error) {
      console.log('err:', error);
    }
  }
  /**
   *
   * @param {*} planPointIndex
   * @param {*} linePos
   * @returns
   */
  addLine(planPointIndex, linePos) {
    //前一个点到后一个点的线，特效
    const planLinePrimitive = this.viewer.scene.primitives.add(
      new Cesium.Primitive({
        id: '航点' + (planPointIndex + 1) + '_line1',
        geometryInstances: new Cesium.GeometryInstance({
          geometry: new Cesium.PolylineGeometry({
            positions: linePos,
            width: 5.0
          })
        }),
        appearance: new Cesium.PolylineMaterialAppearance({
          material: polylinematerial.Polylineglowflow(Cesium.Color.LAWNGREEN)
        })
      })
    );

    //前一个点到后一个点的线，白色
    const planLineEntity2 = this.viewer.entities.add({
      id: '航点' + (planPointIndex + 1) + '_line2',
      polyline: {
        positions: linePos,
        width: 5,
        material: Cesium.Color.WHITE.withAlpha(0.4)
      }
    });

    return [planLinePrimitive, planLineEntity2];
  }

  /**
   *
   */
  getAllLengthAndTime() {
    //重新计算总长度和时间
    this.planAllLength = 0;
    this.planPointJson.forEach(planPoint => {
      this.planAllLength += planPoint.length;
    });
    this.planTime = this.planAllLength / this.wrjSpeed; //预计时间
  }

  /**
   * 计算两点距离
   * @param firstPoint
   * @param secondPoint
   */
  getLength(firstPoint, secondPoint) {
    // 计算距离
    let length = Cesium.Cartesian3.distance(firstPoint, secondPoint);
    return length;
  }

  /**
   * 返回格式化的距离字符
   * @param {*} length
   * @returns
   */
  getLengthStr(length) {
    let lenghtStr = '';
    if (length > 1000) {
      lenghtStr = (length / 1000).toFixed(2) + ' 公里';
    } else {
      lenghtStr = length.toFixed(2) + ' 米';
    }
    return lenghtStr;
  }

  /**
   *
   * @param {*} lon
   * @param {*} lat
   * @param {*} height
   * @returns
   */
  convertToEllipsoidHeight(lon, lat, height) {
    //118.046519622195,24.6106765591627,120   129.782879276146
    // let cartesian3 = Cesium.Cartesian3.fromDegrees(lon, lat, height);
    // let normal = Cesium.Ellipsoid.WGS84.geodeticSurfaceNormal(cartesian3);
    // let magnitude = Cesium.Cartesian3.dot(cartesian3, normal);
    // cartesian3.z = normal.z*magnitude;
    // let cartographic = this.viewer.scene.globe.ellipsoid.cartesianToCartographic(cartesian3);
    // let elec_String = this.viewer.scene.globe.getHeight(cartographic);
    // console.log(elec_String);
    // return Cesium.Cartesian3.fromArray([cartesian3.x,cartesian3.y,cartesian3.z].map(function (value, index, array) {
    //   const h = value - [normal.x,normal.y,normal.z][index] * magnitude;
    //   console.log(h);
    //   return h;
    // }));
  }

  /**
   * 添加标签
   * @param position
   * @param text
   */
  addLabel(centerPoint, text) {
    return this.viewer.entities.add(
      new Cesium.Entity({
        position: centerPoint,
        label: {
          text: text,
          font: '16px sans-serif',
          style: Cesium.LabelStyle.FILL_AND_OUTLINE, //FDDSILL  FILL_AND_OUTLINE OUTLINE
          fillColor: Cesium.Color.WHITE,
          showBackground: true, //指定标签后面背景的可见性
          backgroundColor: new Cesium.Color(0.165, 0.165, 0.165, 0.8), // 背景颜色
          backgroundPadding: new Cesium.Cartesian2(6, 6), //指定以像素为单位的水平和垂直背景填充padding
          pixelOffset: new Cesium.Cartesian2(0, -5),
          disableDepthTestDistance: Number.POSITIVE_INFINITY
        }
      })
    );
  }

  /**
   * 抗锯齿
   */
  setAntiAliasing = function () {
    this.viewer.scene.postProcessStages.fxaa.enabled = false;
    let supportsImageRenderingPixelated = this.viewer.cesiumWidget._supportsImageRenderingPixelated;
    if (supportsImageRenderingPixelated) {
      let vtxf_dpr = window.devicePixelRatio;
      while (vtxf_dpr >= 2.0) {
        vtxf_dpr /= 2.0;
      }
      this.viewer.resolutionScale = vtxf_dpr;
    }
  };

  show3DCoordinates = function () {
    let coordinatesDiv = document.getElementById('map_coordinates');
    if (coordinatesDiv) {
      coordinatesDiv.style.display = 'block';
    } else {
      coordinatesDiv = document.createElement('div');
      coordinatesDiv.id = 'map_coordinates';
      coordinatesDiv.style.zIndex = '50';
      coordinatesDiv.style.bottom = '60px';
      coordinatesDiv.style.height = '29px';
      coordinatesDiv.style.position = 'absolute';
      coordinatesDiv.style.overflow = 'hidden';
      coordinatesDiv.style.textAlign = 'center';
      coordinatesDiv.style.padding = '0 10px';
      coordinatesDiv.style.background = 'rgba(0,0,0,0.5)';
      coordinatesDiv.style.left = '0';
      coordinatesDiv.style.bottom = '60';
      coordinatesDiv.style.lineHeight = '29px';
      coordinatesDiv.innerHTML =
        "<span id='cd_label' style='font-size:13px;text-align:center;font-family:微软雅黑;color:#edffff;'>暂无坐标信息</span>";
      document.getElementById('container').append(coordinatesDiv);
      let handler3D = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
      handler3D.setInputAction(movement => {
        let pick = new Cesium.Cartesian2(movement.endPosition.x, movement.endPosition.y);
        if (pick) {
          let cartesian = this.viewer.scene.globe.pick(this.viewer.camera.getPickRay(pick), this.viewer.scene);
          if (cartesian) {
            //世界坐标转地理坐标（弧度）
            let cartographic = this.viewer.scene.globe.ellipsoid.cartesianToCartographic(cartesian);
            if (cartographic) {
              //海拔
              let height = this.viewer.scene.globe.getHeight(cartographic);
              //视角海拔高度
              let he = Math.sqrt(
                this.viewer.scene.camera.positionWC.x * this.viewer.scene.camera.positionWC.x +
                  this.viewer.scene.camera.positionWC.y * this.viewer.scene.camera.positionWC.y +
                  this.viewer.scene.camera.positionWC.z * this.viewer.scene.camera.positionWC.z
              );
              let he2 = Math.sqrt(cartesian.x * cartesian.x + cartesian.y * cartesian.y + cartesian.z * cartesian.z);
              //地理坐标（弧度）转经纬度坐标
              let point = [(cartographic.longitude / Math.PI) * 180, (cartographic.latitude / Math.PI) * 180];
              if (!height) {
                height = 0;
              }
              if (!he) {
                he = 0;
              }
              if (!he2) {
                he2 = 0;
              }
              if (!point) {
                point = [0, 0];
              }
              coordinatesDiv.innerHTML =
                "<span id='cd_label' style='font-size:13px;text-align:center;font-family:微软雅黑;color:#edffff;'>视角高度:" +
                (he - he2).toFixed(2) +
                '米&nbsp;&nbsp;&nbsp;&nbsp;海拔高度:' +
                height.toFixed(2) +
                '米&nbsp;&nbsp;&nbsp;&nbsp;经度：' +
                point[0].toFixed(6) +
                '&nbsp;&nbsp;纬度：' +
                point[1].toFixed(6) +
                '</span>';
            }
          }
        }
      }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    }
  };

  /**
   * 获取点集合所在位置的地形高度
   * @param {*} positions C3坐标集合
   * @param {*} fun 回调函数，因为异步执行
   */
  getTerrainHeight(lonlats, fun) {
    let cartographics = [];
    lonlats.forEach(point => {
      const cartographic = Cesium.Cartographic.fromDegrees(point[0], point[1]);
      cartographics.push(cartographic);
    });
    const promise = Cesium.sampleTerrainMostDetailed(this.customTerrainProvider, cartographics);
    promise
      .then(function (updatedPositions) {
        if (fun != undefined && fun != null) fun(updatedPositions);
      })
      .catch(function (reason, data) {
        if (fun != undefined && fun != null) fun(null);
        console.log('获取高程数据失败', reason);
      });
  }

  /**
   * 生成不重复的标识
   * @param {*} len
   * @param {*} radix
   * @returns
   */
  uuid(len, radix) {
    let chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
    let uuid = [],
      i;
    radix = radix || chars.length;

    if (len) {
      // Compact form
      for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * radix)];
    } else {
      // rfc4122, version 4 form
      let r;

      // rfc4122 requires these characters
      uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
      uuid[14] = '4';

      // Fill in random data.  At i==19 set the high bits of clock sequence as
      // per rfc4122, sec. 4.1.5
      for (i = 0; i < 36; i++) {
        if (!uuid[i]) {
          r = 0 | (Math.random() * 16);
          uuid[i] = chars[i == 19 ? (r & 0x3) | 0x8 : r];
        }
      }
    }

    return uuid.join('');
  }
  //#region 事件监听方法

  dispose() {
    document.removeEventListener('keydown', this.listeners.keydownHandler, false);
    document.removeEventListener('keyup', this.listeners.keyupHandler, false);

    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    document.removeEventListener('mouseleave', this.handleMouseLeave);
    document.removeEventListener('mouseenter', this.handleMouseEnter);
  }
  handleVisibilityChange() {
    // 检查页面是否失去焦点
    if (document.hidden) {
      // 页面失去焦点时的操作
      this.continueRun = false;
    } else {
      // 页面重新获得焦点时的操作
      this.continueRun = true;
    }
  }

  handleMouseLeave(event) {
    // 检查鼠标是否移出了当前页面
    if (
      event.clientX < 0 ||
      event.clientX > window.innerWidth ||
      event.clientY < 0 ||
      event.clientY > window.innerHeight
    ) {
      // 鼠标移出当前页面时的操作
      this.continueRun = false;
    } else {
      this.continueRun = true;
    }
  }
  handleMouseEnter(event) {
    // 检查鼠标是否移出了当前页面
    if (
      event.clientX < 0 ||
      event.clientX > window.innerWidth ||
      event.clientY < 0 ||
      event.clientY > window.innerHeight
    ) {
      this.continueRun = false;
    } else {
      this.continueRun = true;
    }
  }
  //#endregion
  reSetIndes() {
    setTimeout(() => {
      if (this.model.entity) {
        this.viewer.entities.remove(this.model.entity);
      }
    }, 3000);
    setTimeout(() => {
      if (this.model.entity) {
        this.viewer.entities.add(this.model.entity);
      }
    }, 5000);
  }
}

export default Plan;
