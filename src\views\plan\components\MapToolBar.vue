<template>
  <div class="map-tool-wrapper">
    <div class="item" @click="setFullMap">
      <el-image class="img" style="width: 20px; height: 20px" :src="fullmap" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'MapToolBar'
};
</script>
<script setup>
import { getOrCreateCesiumEngineInstance, setPlanInstance } from '@/components/Cesium/libs/cesium/global.js';
import { positionCameraToViewPoints } from '@/views/plan/newplan/kmz/hocks/modules/waylineshandle.js';
const fullmap = new URL('@/assets/plan/fullMap.png', import.meta.url).href;
let engineInstane = null;
let viewer = null;
const getInstance = () => {
  let engine = getOrCreateCesiumEngineInstance('plan');
  if (!engine) {
    engine = getOrCreateCesiumEngineInstance('fly');
  }
  return engine;
};

const setFullMap = () => {
  if (engineInstane) {
    const { viewer } = engineInstane;
    positionCameraToViewPoints(viewer);
  }
  console.log('setFullMap');
};
onMounted(() => {
  engineInstane = getInstance();
});
</script>

<style scoped>
.map-tool-wrapper {
  position: absolute;
  height: auto;
  right: 360px;
  bottom: 20px;
  margin: 0;
  border-radius: 5px;
  user-select: none;
  z-index: 100;
  background-color: rgba(55, 58, 68, 0.805);
}
.map-tool-wrapper .item {
  display: flex;
  justify-content: center;
  align-items: center;
}

.map-tool-wrapper .item:hover {
  background-color: #6f7279cd;
}
.img {
  /* width: 20px;
  height: 20px; */
}
.item:first-child,
.item:last-child {
  margin-top: 0;
  margin-bottom: 0;
}
</style>
