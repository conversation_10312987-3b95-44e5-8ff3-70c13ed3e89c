<script>
export default {
  name: 'RealTimeFight'
};
</script>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { EBizCode } from '@/utils/constants';
import { useConnectWebSocket } from '@/hooks/useConnectWebSocket';
import { useDeviceStateStore } from '@/store/modules/deviceState.js';
import DroneVideo from './droneVideo.vue';
import dockPlane from './dockPlane.vue';
import navPlane from './navPlane.vue';
import ControlList from './controlList.vue';
import ShareVideo from './shareVideo.vue';
import RealTimeFlyMap from '@/views/map/map-fly-manager/realtimeflight/index.vue';
import { getCameraSelected } from '../../../api/devices';
import NoauthImg from '@/assets/fight_noauth.png';
import { readRemoteJsonFile } from '@/utils/configHelper';
import { getDeptSysSetting } from '@/api/wayline';
import { getDevicesBound } from '@/api/devices';
const router = useRouter();
const deviceStateStore = useDeviceStateStore();
const showControl = ref(false);
const realTimeFlyMapRef = ref(null);
const controlRef = ref(null);
const dockPlaneRef = ref(null);
const navPlaneRef = ref(null);
const airportData = ref([]); //机场视频
const videoData = ref([]); //无人机视频
const status = ref('14');
const showMagnify = ref('map'); //放大
const showLeft = ref('airport'); //左侧
const showLeftBottom = ref('drone'); //左下
const airStatus = ref('0'); //机场状态
const showAI = ref(false);
const shareVisible = ref(false);
const shareTitle = ref('');
const globalConfigResource = ref({});
// 无人机、机场属性定时器
let intervalTimer = null;
const hasAuth = ref(false);
const authOpacity = ref(0.01);
// 当前机场携带信息
let dockInfo = ref(null);

useConnectWebSocket(payload => {
  if (!payload) {
    return;
  }
  switch (payload.biz_code) {
    // 机场信息更新
    case EBizCode.DockOsd: {
      const info = payload.data;
      deviceStateStore.setCurrentDock(info);
      break;
    }
    // 遥控器信息更新
    case EBizCode.GatewayOsd: {
      // console.log('----gateway_osd', payload);
      break;
    }
    // 飞机信息更新
    case EBizCode.DeviceOsd: {
      const info = payload.data;
      deviceStateStore.setCurrentNav(info);
      break;
    }
  }
});

// 获取路由携带参数
function handleRouteParams(query) {
  dockInfo.value = query;
  if (query.type === '1') {
    // 1代 无人机和机场的视频只能有其中一个
    if (status.value != '14') {
      setUavVideo();
    } else {
      setAirportVideo();
    }
  } else {
    setAirportVideo();
    if (status.value != '14') {
      setUavVideo();
     
    }
  }
}

function initAirportList() {
  getDevicesBound({
    domain: '3',
    page: 1,
    page_size: 50
  }).then(data => {
    const { list, pagination } = data;
    if (list.length > 0) {
      hasAuth.value = true;
    } else {
      hasAuth.value = false;
      authOpacity.value = 1;
    }
  });
}

watch(
  () => status.value,
  newVal => {
    if (dockInfo.value.type === '1') {
      // 1代 无人机和机场的视频只能有其中一个
      if (newVal != undefined && newVal != 14) {
        setUavVideo();
        clearAirportVideo();
      } else {
        clearUavVideo();
        setAirportVideo();
      }
    } else {
      if (newVal != undefined && newVal != 14) {
        setUavVideo();
      } else {
        clearUavVideo();
      }
    }

  }
);

watch(
  () => showMagnify.value,
  newVal => {
    setTimeout(() => {
      realTimeFlyMapRef.value.setAlarmIsShow(showMagnify.value);
    }, 500);
  }
);

function hiddenOrShow() {
  if (showControl.value) {
    showControl.value = false;
  } else {
    showControl.value = true;
  }
  window.$bus.emit('hkPlayerResize');
}
onMounted(async () => {
  const currentQuery = router.currentRoute.value.query;
  // await readRemoteJsonFile('').then(res => {
  //   globalConfigResource.value = res;
  // });
  await getDeptSysSetting({}).then(res => {
    const { base_config = {}} = res; 
    globalConfigResource.value['showAI'] = base_config.show_ai;
  });
  handleRouteParams(currentQuery);
  initAirportList();
  // 更新机场和无人机位置、信息等状态
  let flag = 1;
  intervalTimer = setInterval(() => {
    if (dockInfo.value) {
      let dockOptions = { dockInfo: dockInfo.value, osdInfo: deviceStateStore.getDockBySn(dockInfo.value.dock_sn) };
      if (toRaw(toRaw(dockOptions).osdInfo)?.link_osd?.drc_state == 2 && flag == 1) {
        controlRef.value.initPostDrcEnter();
        flag = flag + 1;
      }
      if (toRaw(toRaw(dockOptions).osdInfo)?.link_osd?.drc_state == 0 && flag == 1) {
        controlRef.value.initPostDrcOut();
        flag = flag + 1;
      }
      realTimeFlyMapRef.value?.setDockModel(dockOptions.osdInfo, dockOptions.dockInfo);
      airStatus.value = toRaw(toRaw(dockOptions).osdInfo).basic_osd.mode_code;
      dockPlaneRef.value.setComponentData(dockOptions);
      let navOptions = { navInfo: dockInfo.value, osdInfo: deviceStateStore.getNavBySn(dockInfo.value.device_sn) };
      status.value = toRaw(navOptions).osdInfo && toRaw(navOptions)?.osdInfo?.mode_code;
      realTimeFlyMapRef.value.setNavModel(navOptions.osdInfo, navOptions.navInfo);
      navPlaneRef.value.setComponentData(navOptions);
    }
  }, 1000);
});

onUnmounted(() => {
  clearInterval(intervalTimer);
});

function changePosition(type) {
  if (type == 'airport') {
    if (showMagnify.value == 'airport') {
      // 机场视频切换会左侧
      if (showLeft.value == 'drone') {
        showMagnify.value = 'drone';
        showLeft.value = 'airport';
        showLeftBottom.value = 'map';
        return;
      } else if (showLeft.value == 'map') {
        showMagnify.value = 'map';
        showLeft.value = 'airport';
        showLeftBottom.value = 'drone';
        return;
      }
    } else if (showMagnify.value == 'drone') {
      showMagnify.value = type;
      showLeft.value = 'drone';
      showLeftBottom.value = 'map';
      return;
    } else {
      showLeft.value = showMagnify.value;
      showMagnify.value = type;
      showLeftBottom.value = 'drone';
      return;
    }
  } else if (type == 'drone') {
    if (showMagnify.value == 'drone') {
      showMagnify.value = showLeftBottom.value;
      showLeftBottom.value = type;
      return;
    } else if (showMagnify.value == 'airport') {
      showMagnify.value = type;
      showLeft.value = 'airport';
      showLeftBottom.value = 'map';
      return;
    } else {
      showLeftBottom.value = showMagnify.value;
      showMagnify.value = 'drone';
      showLeft.value = 'airport';
      return;
    }
  } else {
    if (showMagnify.value == 'drone') {
      showMagnify.value = 'map';
      showLeftBottom.value = 'drone';
      showLeft.value = 'airport';
      return;
    } else if (showMagnify.value == 'airport') {
      showMagnify.value = 'map';
      showLeft.value = 'airport';
      showLeftBottom.value = 'drone';
      return;
    } else {
      if(showMagnify.value == 'map'){
        ElMessage.info('请先在左侧选择【无人机】或【机场】窗体，再进行切换');
        return;
      }
      showLeftBottom.value = 'drone';
      showMagnify.value = 'map';
      showLeft.value = 'airport';
      return;
    }
  }
}

function changeShowAi() {
  showAI.value = !showAI.value;
}

function openShare() {
  shareVisible.value = true;
}

function choseShare() {
  shareVisible.value = false;
}

// 清空/关闭机场的视频
function clearAirportVideo() {
  console.log('清空/关闭机场的视频');

  const currentQuery = router.currentRoute.value.query;
  airportData.value = [
    {
      nickname: currentQuery.nickname
    }
  ];
}

// 清空/关闭无人机的视频
function clearUavVideo() {
  const currentQuery = router.currentRoute.value.query;
  let video = {
    nickname: currentQuery.device_nickname,
    device_sn: null,
    droneSelected: null,
    index: null,
    source: '2',
    claritySelected: null,
    cameraId: null
  };
  videoData.value = [video];
}
// 设置机场的视频
function setAirportVideo() {
  console.log('设置机场的视频');
  const query = router.currentRoute.value.query;
  getCameraSelected({
    device_sn: query.dock_sn
  }).then(res => {
    if (!res.source && !res.url_type && !res.index) {
      clearAirportVideo();
      return;
    }
    let params = {
      nickname: query.nickname,
      device_sn: query.dock_sn,
      droneSelected: query.dock_sn,
      url_type: res.url_type,
      source: res.source,
      index: res.index,
      claritySelected: 0,
      cameraId: '417dc9aa-0db3-4cd8-91ea-1387ae59e716'
    };
    airportData.value = [params];
  });
}
// 设置无人机的视频
function setUavVideo() {
  const currentQuery = router.currentRoute.value.query;
  getCameraSelected({
    device_sn: currentQuery.device_sn
  }).then(data => {
    if (!data.source && !data.url_type && !data.index) {
      videoData.value = [];
      // if (dockInfo.value.type === '1') {
      //   // 如果是1代无人机， 当无人机关闭了视频的适合，开启机场的视频
      //   setAirportVideo();
      // }
      return;
    }
    let video = {
      nickname: currentQuery.device_nickname,
      device_sn: currentQuery.device_sn,
      droneSelected: currentQuery.device_sn,
      index: data.index,
      url_type: data.url_type,
      source: data.source,
      claritySelected: 0,
      cameraId: '417dc9aa-0db3-4cd8-91ea-1387ae59e716'
    };
    videoData.value = [video];

    // if (dockInfo.value.type === '1') {
    //   // 如果是1代无人机， 当无人机开启了视频的时候，关闭机场的视频
    //   clearAirportVideo();
    // }
  });
}
</script>

<template>
  <div class="home-container" v-if="hasAuth">
    <!-- 右侧警情列表以及无人机列表 -->
    <div class="info-box">
      <div style="height: 21%">
        <dockPlane ref="dockPlaneRef" />
      </div>
      <div style="height: 21%">
        <navPlane ref="navPlaneRef" />
      </div>

      <div class="airport-box">
        <RealTimeFlyMap
          ref="realTimeFlyMapRef"
          v-if="showLeft == 'map'"
          :showAlarm="false"
          @change="changePosition('map')"
        />
        <drone-video
          :show-refresh="false"
          v-if="showLeft == 'airport'"
          :show-up="true"
          playerId="livePlayerJiChang"
          :show-close="false"
          :visible="true"
          :device="airportData"
          :show-blow-up="true"
          @change="changePosition('airport')"
        />
        <drone-video
          :show-share="true"
          :show-refresh="false"
          :show-AI="globalConfigResource.showAI"
          :show-up="true"
          v-if="showLeft == 'drone'"
          playerId="jichangWu"
          :show-close="false"
          :visible="true"
          :device="videoData"
          :show-blow-up="true"
          @change="changePosition('drone')"
          @showAI="changeShowAi"
          @showShare="openShare"
        />
      </div>

      <div class="airport-box">
        <RealTimeFlyMap
          ref="realTimeFlyMapRef"
          v-if="showLeftBottom == 'map'"
          :showAlarm="false"
          @change="changePosition('map')"
        />
        <drone-video
          :show-refresh="false"
          v-if="showLeftBottom == 'airport'"
          :show-up="true"
          playerId="livePlayerJiChang"
          :show-close="false"
          :visible="true"
          :device="airportData"
          :show-blow-up="true"
          @change="changePosition('airport')"
        />
        <drone-video
          :show-share="true"
          :show-refresh="false"
          :show-AI="globalConfigResource.showAI"
          :show-up="true"
          v-if="showLeftBottom == 'drone'"
          playerId="jichangWu"
          :show-close="false"
          :visible="true"
          :device="videoData"
          :show-blow-up="true"
          @change="changePosition('drone')"
          @showAI="changeShowAi"
          @showShare="openShare"
        />
      </div>
    </div>
    <!-- 地图区域 -->

    <div class="map-container">
      <div class="map-box" :class="{ 'is-active': showControl }">
        <RealTimeFlyMap
          ref="realTimeFlyMapRef"
          v-if="showMagnify == 'map'"
          :isBig="true"
          @change="changePosition('map')"
        />
        <drone-video
          v-if="showMagnify == 'airport'"
          :show-up="true"
          playerId="livePlayerJiChang"
          :isBig="true"
          :show-close="false"
          :visible="true"
          :device="airportData"
          :show-blow-up="true"
          @change="changePosition('airport')"
        />
        <drone-video
          :show-share="true"
          v-if="showMagnify == 'drone'"
          :show-AI="globalConfigResource.showAI"
          :show-up="true"
          playerId="jichangWu"
          :show-close="false"
          :isBig="true"
          :visible="true"
          :device="videoData"
          :show-blow-up="true"
          @change="changePosition('drone')"
          @showAI="changeShowAi"
          @showShare="openShare"
        />
      </div>
      <div class="ai-box" v-if="showAI">
        <iframe
          src="http://10.88.188.216/wsVideoPlayer.html?videoId=12000000002000361223"
          width="480"
          height="320"
        ></iframe>
      </div>
      <div class="control-box">
        <div :class="showControl ? 'hidden-box' : 'show-box'" @click="hiddenOrShow"></div>
        <control-list ref="controlRef" :visible="showControl" :status="status" :airStatus="airStatus" />
      </div>
      <div :class="shareVisible ? 'share-box' : 'share-box-none'">
        <share-video :visible="shareVisible" :shareData="videoData" :title="shareTitle" @closeDialog="choseShare" />
      </div>
    </div>
  </div>
  <div v-else class="empty" :style="{ opacity: authOpacity }">
    <img :src="NoauthImg" alt="" class="empty-img" />
  </div>
</template>

<style lang="scss" scoped>
.empty {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  .empty-img {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
.airport-box {
  width: 100%;
  height: 29.1%;
  overflow: hidden;
}

.home-container {
  position: relative;
  display: flex;
  flex-direction: row;
  height: calc(100vh - 60px);
  width: 100%;
  background: #fff;
  overflow: hidden;
}
.ai-box {
  position: absolute;
  top: 40px;
  right: 0;
  width: 480px;
  height: 320px;
  z-index: 9999;
}
.task-container {
  width: 456px;
  position: absolute;
  right: 336px;
  top: 10px;
}
.video-container {
  width: 416px;
  position: absolute;
  left: 0;
  bottom: 0;
}
.map-container {
  position: relative;
  width: 83%;
  max-width: 1547px;
  height: 100%;
  .share-box {
    position: absolute;
    width: 400px;
    height: 535px;
    top: 45px;
    left: 10px;
    z-index: 100;
  }
  .share-box-none {
    position: absolute;
    width: 0px;
    height: 535px;
    top: 45px;
    left: 10px;
    z-index: 100;
  }
  .control-box {
    width: 100%;
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: 999;
    .hidden-box {
      width: 80px;
      height: 24px;
      top: -24px;
      left: 50%;
      cursor: pointer;
      position: absolute;
      background-image: url('@/assets/hidden-box.png');
      z-index: 9999;
    }
    .show-box {
      width: 80px;
      height: 24px;
      top: -24px;
      left: 50%;
      cursor: pointer;
      position: absolute;
      background-image: url('@/assets/show-box.png');
      z-index: 9999;
    }
  }
  .map-box {
    width: 100%;
    height: 100%;
    padding-bottom: 38px;
    &.is-active {
      padding-bottom: 272px;
    }
  }
}
.info-box {
  width: 20%;
  overflow: hidden;
  height: 100%;
  z-index: 2;
  border-top: 2px solid #001129;
}
</style>
