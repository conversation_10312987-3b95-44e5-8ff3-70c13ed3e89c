<template>
  <div class="container">
    <el-form
      ref="loginFormRef"
      :model="loginData"
      :rules="loginRules"
      class="login-form"
    >
      <el-form-item prop="mobile" class="relative">
        <div class="p-2 text-white">
          <i class="ff-cloud-icon clound-mobile-icon"></i>
        </div>
        <el-input
          class="flex-1"
          ref="phone"
          size="large"
          v-model="loginData.mobile"
          :placeholder="$t('page.login.input.phone')"
          name="phone"
        />
        <div class="smsCodeArea absolute right-2">
          <el-button
            type="primary"
            plain
            :disabled="smsCodeBtnDisabled"
            :loading="smsCodeBtnLoading"
            @click="handClickSmsCode"
          >
            {{ _.isNumber(smsBtnText) ? smsBtnText + 's' : $t(smsBtnText) }}
          </el-button>
        </div>
      </el-form-item>
      <el-form-item prop="verifyCode">
        <div class="p-2 text-white">
          <i class="ff-cloud-icon cloud-valid-code"></i>
        </div>
        <el-input
          class="flex-1"
          v-model="loginData.verifyCode"
          :placeholder="$t('login.getCode')"
          size="large"
          name="smsCode"
          @keyup.enter="handleLogin"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'smdLogin'
};
</script>

<script setup>
import _ from 'lodash';
import router from '@/router';

import i18n from '@/lang';

import { sendNote } from '@/api/auth';
import {
  getLastCountdownEndTime,
  setCountdownTimeDelay,
  getCountdownTimeDelay,
  removeCountdownTimeDelay,
  setLastCountdownEndTime,
  removeLastCountdownEndTime
} from '@/utils/store';
// 状态管理依赖
import { useUserStore } from '@/store/modules/user.js';
import { ElMessage } from 'element-plus';

const userStore = useUserStore();

const smsCodeBtnLoading = ref(false);
const smsCodeBtnDisabled = ref(false);
const smsBtnText = ref('login.getCode');
const popDragVerify = ref('popDragVerify');

/**
 * 按钮loading
 */
const loading = ref(false);

/**
 * 登录表单引用
 */
const loginFormRef = ref(ElForm);

const loginData = ref({
  mobile: '',
  verifyCode: ''
});

const loginRules = {
  mobile: [
    {
      required: true,
      message: i18n.global.t('page.login.input.errorTips.phone')
    }
  ],
  verifyCode: [
    {
      required: true,
      message: i18n.global.t('page.login.input.errorTips.smsCode')
    }
  ]
};

onMounted(() => {
  const lastCountdownEndTime = getLastCountdownEndTime();
  // 如果上次倒计时结束时间存在，说明上次倒计时还未结束，需要继续倒计时
  if (lastCountdownEndTime) {
    monitorCountdown();
  }
  // 监听滑动验证码弹窗关闭事件
  window.$bus.on('dialogCancel', () => {
    loading.value = false;
  });

  // 监听滑动验证码弹窗beforeClose事件
  window.$bus.on('dialogBeforeClose', data => {
    if (!_.isEmpty(data) && data.loginType === 'smsLogin') {
      validSubmit(data);
    }
  });
});

onBeforeUnmount(() => {
  window.$bus.off('dialogCancel');
  window.$bus.off('dialogBeforeClose');
});

onUnmounted(() => {
  window.$bus.off('dialogCancel');
  window.$bus.off('dialogBeforeClose');
});

// 获取手机验证码
async function handClickSmsCode() {
  try {
    loginFormRef.value.validateField('mobile', async valid => {
      if (valid) {
        smsCodeBtnLoading.value = true;
        try {
          const res = await sendCode({ mobile: loginData.value.mobile });
          if (res) {
            countDown();
          }

          smsCodeBtnLoading.value = false;
        } catch (error) {
          smsCodeBtnLoading.value = false;
        }
      }
    });
  } catch (error) {
    smsCodeBtnLoading.value = false;
  }
}

const sendCode = async data => {
  try {
    const res = await sendNote(data);
    return res.code * 1 === 200;
  } catch (err) {
    return false;
  }
};

/**
 * 登录
 */
function handleLogin() {
  loginFormRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      try {
        popDragVerify.value.handleOpen();
      } catch (error) {
        loading.value = false;
      }
    }
  });
}

function validSubmit(validResult) {
  if (validResult) {
    userStore
      .loginBySms(loginData.value)
      .then(() => {
        ElMessage.success(
          i18n.global.t('page.dialog.actionFb.successfullyLogin')
        );
        router.push({ path: '/' });
      })
      .catch(() => {
        loading.value = false;
      })
      .finally(() => {
        loading.value = false;
      });
  }
}

function monitorCountdown() {
  const LocalDelay = getLocalDelay();
  const timeLine = parseInt((new Date().getTime() - LocalDelay.time) / 1000);
  if (timeLine > LocalDelay.delay) {
    console.log('过期');
  } else {
    let _delay = LocalDelay.delay - timeLine;
    smsCodeBtnDisabled.value = true;

    const timer = setInterval(function () {
      if (_delay > 1) {
        _delay--;
        smsBtnText.value = _delay;
        setLocalDelay(_delay);
      } else {
        clearInterval(timer);
        removeLastCountdownEndTime();
        removeCountdownTimeDelay();
        smsBtnText.value = 'login.getCode';
        smsCodeBtnDisabled.value = false;
      }
    }, 1000);
  }
}

function countDown(callback) {
  if (smsBtnText.value == 'login.getCode') {
    const _delay = 60;
    let delay = _delay;
    smsCodeBtnDisabled.value = true;

    const timer = setInterval(function () {
      if (delay > 1) {
        delay--;
        smsBtnText.value = delay;
        setLocalDelay(delay);
      } else {
        clearInterval(timer);
        smsCodeBtnDisabled.value = false;
        removeLastCountdownEndTime();
        removeCountdownTimeDelay();
        smsBtnText.value = 'login.getCode';
      }
    }, 1000);

    callback();
  } else {
    return false;
  }
}

function setLocalDelay(delay) {
  setCountdownTimeDelay(delay);
  setLastCountdownEndTime(new Date().getTime());
}

//getLocalDelay()
function getLocalDelay() {
  const LocalDelay = {};
  LocalDelay.delay = getCountdownTimeDelay();
  LocalDelay.time = getLastCountdownEndTime();

  return LocalDelay;
}

defineExpose({ loginFormRef, handleLogin, loading, loginData });
</script>

<style lang="scss" scoped>
.container {
  width: 520px;
  background-color: #2d3a4b;
  overflow: hidden;

  .login-form {
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    overflow: hidden;
  }
}

.el-form-item {
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.1);
  border-radius: 5px;
}

.el-input {
  background: transparent;

  // 子组件 scoped 无效，使用 :deep
  :deep(.el-input__wrapper) {
    padding: 0;
    background: transparent;
    box-shadow: none;

    .el-input__inner {
      background: transparent;
      border: 0px;
      border-radius: 0px;
      color: #fff;
      caret-color: #fff;

      &:-webkit-autofill {
        box-shadow: 0 0 0 1000px transparent inset !important;
        -webkit-text-fill-color: #fff !important;
      }

      // 设置输入框自动填充的延迟属性
      &:-webkit-autofill,
      &:-webkit-autofill:hover,
      &:-webkit-autofill:focus,
      &:-webkit-autofill:active {
        -webkit-transition-delay: 99999s;
        -webkit-transition: color 99999s ease-out,
          background-color 99999s ease-out;
      }
    }
  }
}

.ff-cloud-icon {
  font-size: 14px;
  color: #fff;
}

:deep(.el-input-group__append) {
  margin-right: 5px;
  border-radius: 4px;
  background-color: var(--el-color-primary);
  box-shadow: none;
  color: #fff;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__nav-wrap) {
  .el-tabs__item {
    color: #fff;
  }
  .is-active {
    color: var(--el-color-primary);
  }
}
</style>
