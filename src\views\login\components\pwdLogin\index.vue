<template>
  <div class="container">
    <div class="login-title">用户登录</div>
    <el-form ref="loginFormRef" :model="loginData" :rules="loginRules" class="login-form" @keyup.enter="submitForm">
      <el-form-item prop="username">
        <div class="p-2 text-white">
          <i class="ff-cloud-icon clound-status-person" />
        </div>
        <el-input
          class="flex-1"
          ref="username"
          size="large"
          v-model="loginData.username"
          placeholder="请输入账号"
          name="username"
        />
      </el-form-item>
      <el-form-item prop="password">
        <span class="p-2 text-white">
          <i class="ff-cloud-icon cloud-lock" />
        </span>
        <el-input
          class="flex-1"
          v-model="loginData.password"
          :placeholder="$t('login.pwd')"
          :type="passwordVisible === false ? 'password' : 'input'"
          size="large"
          name="password"
        />
        <span class="mr-2" @click="passwordVisible = !passwordVisible">
          <svg-icon
            :icon-class="passwordVisible === false ? 'hideEye' : 'eye-open'"
            class="text-white cursor-pointer"
          />
        </span>
      </el-form-item>
      <!-- <el-form-item prop="verifyCode">
        <span class="p-2 text-white">
          <i class="ff-cloud-icon cloud-lock" />
        </span>
        <el-input
          class="flex-1"
          v-model="loginData.verifyCode"
          :placeholder="$t('login.imageCode')"
          size="large"
        />
        <div class="captcha mr-2">
          <el-image
            style="width: 120 /* 120/192 */; height: 40; /* 40/192 */"
            :src="captchaBase64"
            fit="contain"
            @click="getCaptcha"
          ></el-image>
        </div>
      </el-form-item> -->
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'pwdLogin'
};
</script>

<script setup>
import _ from 'lodash';

import router from '@/router/index.js';
import { getCaptchaApi } from '@/api/auth/index.js';

// 状态管理依赖
import { useUserStore } from '@/store/modules/user.js';
import { authorityShow } from '@/utils/authority';

const userStore = useUserStore();

const loading = ref(false);
const passwordVisible = ref(false);
const loginFormRef = ref(ElForm);
const emit = defineEmits(['key']);

const loginData = ref({
  username: '',
  password: '',
  flag: 1
});
/**
 * 验证码图片Base64字符串
 */
const captchaBase64 = ref();

const loginRules = {
  username: [{ required: true, trigger: 'blur', message: '请输入账号' }],
  password: [{ required: true, trigger: 'blur', message: '请输入密码' }],
  verifyCode: [{ required: true, trigger: 'blur' }]
};

/**
 * 获取验证码
 */
function getCaptcha() {
  getCaptchaApi().then(({ data }) => {
    const { image, key } = data;
    loginData.value.verifyCodeKey = key;
    captchaBase64.value = image;
  });
}

onMounted(() => {
  // getCaptcha();
  // 监听滑动验证码弹窗关闭事件
  window.$bus.on('dialogCancel', data => {
    if (data.emitName === 'dragVerify') {
      loading.value = false;
    }
  });

  // 监听滑动验证码弹窗beforeClose事件
  window.$bus.on('dialogBeforeClose', data => {
    if (!_.isEmpty(data) && data.emitData.loginType && data.emitName === 'dragVerify') {
      validSubmit(data.emitData);
    }
  });
});

function submitForm() {
  emit('key');
}

onBeforeUnmount(() => {
  window.$bus.off('dialogCancel');
  window.$bus.off('dialogBeforeClose');
});

onUnmounted(() => {
  window.$bus.off('dialogCancel');
  window.$bus.off('dialogBeforeClose');
});

/**
 * 密码校验器
 */
function passwordValidator(rule, value, callback) {
  if (value.length < 6) {
    callback(new Error('密码不能小于6位'));
  } else {
    callback();
  }
}

function validSubmit(validResult) {
  if (validResult) {
    userStore
      .login(loginData.value)
      .then(() => {
        ElMessage.success('登录成功!');
        router.push({ path: '/' });
      })
      .catch((error) => {
        console.error('登录异常:', error);
        loading.value = false;
      })
      .finally(() => {
        loading.value = false;
      });
  }
}

defineExpose({ loginFormRef, loading, loginData });
</script>

<style lang="scss" scoped>
:deep(.el-input__inner::placeholder) {
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #98a2b3 !important;
  text-align: left;
  line-height: 24px;
  font-weight: 400;
}
.container {
  width: 480px;
  border-radius: 8px;
  padding: 0 40px;
  overflow: hidden;

  .login-form {
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    overflow: hidden;
  }
}
.login-title {
  margin: 40px 0;
  font-family: SourceHanSansSC-Bold;
  font-size: 24px;
  color: #ffffff;
  text-align: center;
  line-height: 32px;
  font-weight: 700;
}
.el-form-item {
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.1);
  border-radius: 5px;
}

.el-input {
  background: transparent;

  // 子组件 scoped 无效，使用 :deep
  :deep(.el-input__wrapper) {
    padding: 0;
    background: transparent;
    box-shadow: none;

    .el-input__inner {
      background: transparent;
      border: 0px;
      border-radius: 0px;
      color: #fff;
      caret-color: #fff;

      &:-webkit-autofill {
        box-shadow: 0 0 0 1000px transparent inset !important;
        -webkit-text-fill-color: #fff !important;
      }

      // 设置输入框自动填充的延迟属性
      &:-webkit-autofill,
      &:-webkit-autofill:hover,
      &:-webkit-autofill:focus,
      &:-webkit-autofill:active {
        -webkit-transition-delay: 99999s;
        -webkit-transition: color 99999s ease-out, background-color 99999s ease-out;
      }
    }
  }
}

.ff-cloud-icon {
  font-size: 16px;
  color: #98a2b3;
}
.captcha {
  display: flex;
  cursor: pointer;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__nav-wrap) {
  .el-tabs__item {
    color: #fff;
  }
  .is-active {
    color: var(--el-color-primary);
  }
}
</style>
