<template>
  <div class="image-comparsion-container">
    <div class="left-contetnt">
      <div id="container"></div>
    </div>
    <div class="right-contetnt"><picture-list /></div>
  </div>
</template>
<script>
export default { name: 'ImageComparison' };
</script>
<script setup>
import { onMounted, onUnmounted } from 'vue';
import { initMap, initPicturesList, timeSelectTypeRect, timerRect } from './imageComparison';
import PictureList from './components/PictureList.vue';
//#region 构建对象

//#endregion

//#region 方法

function init() {
  initMap();
  initPicturesList();
  timerRect.value = [];
  timerRect.visible = false;
  timeSelectTypeRect.value = 'seven';
}

//#endregion
onMounted(() => {
  init();
});

onUnmounted(() => {});
</script>
<style scoped lang="scss">
@import '../../../styles/common/global.scss';
.image-comparsion-container {
  width: 100%;
  height: 100%;
  display: flex;
}
#container {
  width: 100%;
  height: 100%;
  background-color: #171717;
  z-index: 1;
}

.left-contetnt {
  width: calc(100% - 350px);
  height: 100%;
  background-color: #1f2f49;
}

.left-contetnt .title {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 600;
  color: #fff;
}

.left-contetnt .tab-wrap {
  width: 100%;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #a83c3c;
}

.left-contetnt .action-wrap {
  width: 100%;
  height: 38px;
  display: flex;
  justify-content: center;
}
.left-contetnt .action-wrap .action-list {
  width: 100%;
  display: flex;
  justify-content: center; /* 使得子元素在容器中水平居中 */
  align-items: center; /* 如果需要同时垂直居中 */
}

.action-item {
  position: relative; /* 为伪元素定位准备 */
  top: -2px;
  width: 50%;
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #98a2b3;
  line-height: 38px;
  font-weight: 400;
  padding: 0px 5px; /* 添加一些内边距使其更易点击 */
  padding-bottom: 5px;
  display: flex;
  justify-content: center;
  cursor: pointer; /* 鼠标变为手型，表示可点击 */
}

.action-item::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  height: 5px;
  margin-bottom: 0px;
  background-color: #2e90fa; /* 横线的颜色 */
  transition: width 0.3s ease; /* 动画效果 */
}

.action-item.action-active::after {
  width: 100%; /* 选中时横线扩展到整个宽度 */
}
.action-active {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #2e90fa;
  text-align: center;
  font-weight: 400;
}
.fix-blb {
  position: absolute;
  left: 10px;
  top: 20px;
}

.right-contetnt {
  width: 350px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #1f2f49;
}

.right-contetnt .toolbar {
  position: absolute;
  padding: 5px;
  right: 10px;
  top: 20px;
  z-index: 100000;
}
.right-contetnt .GeomInfoDialog {
  position: absolute;
  right: 150px;
  top: 20px;
  z-index: 100000;
}

.action-content-wrap {
  width: 100%;
  height: calc(100% - 38px);
  background-color: #001129 !important;
}
.action-content-wrap .action-content {
  padding: 0px 10px;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  color: #2e90fa;
}

.area-item {
  margin: 12px 0px;
}
// 第一个和最后一个 不做margin
.item:first-child,
.item:last-child {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}
</style>
