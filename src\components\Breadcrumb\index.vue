<template>
  <el-breadcrumb class="h-[60px] flex items-center pl-[10px]">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="item.path">
        <span
          v-if="
            item.redirect === 'noredirect' || index === breadcrumbs.length - 1
          "
          class="lastItem"
        >
          {{ translateRouteTitleI18n(item.meta.title) }}
        </span>

        <span :class="['item', !item.name ? 'noClickItem' : '']" v-else>
          {{ translateRouteTitleI18n(item.meta.title) }}
        </span>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<script setup>
import { useRoute } from 'vue-router';
// import { compile } from 'path-to-regexp';
// import router from '@/router/index.js';
import { translateRouteTitleI18n } from '@/utils/i18n.js';

const currentRoute = useRoute();
// const pathCompile = path => {
//   const { params } = currentRoute;
//   const toPath = compile(path);
//   return toPath(params);
// };

const breadcrumbs = ref([]);

function getBreadcrumb() {
  let matched = currentRoute.matched.filter(
    item => item.meta && item.meta.title
  );
  // const first = matched[0];
  // if (!isDashboard(first)) {
  //   matched = [{ path: '/dashboard', meta: { title: 'dashboard' } }].concat(
  //     matched
  //   );
  // }

  breadcrumbs.value = matched.filter(item => {
    return item.meta && item.meta.title && item.meta.breadcrumb !== false;
  });
}

// function isDashboard(route) {
//   const name = route && route.name;
//   if (!name) {
//     return false;
//   }
//   return (
//     name.toString().trim().toLocaleLowerCase() ===
//     'Dashboard'.toLocaleLowerCase()
//   );
// }

// function handleLink(item) {
//   if (!item.name) {
//     return;
//   }
//   const { redirect, path } = item;
//   if (redirect) {
//     router.push(redirect).catch(err => {
//       console.warn(err);
//     });
//     return;
//   }
//   router.push(pathCompile(path)).catch(err => {
//     console.warn(err);
//   });
// }

watch(
  () => currentRoute.path,
  path => {
    if (path.startsWith('/redirect/')) {
      return;
    }
    getBreadcrumb();
  }
);

onBeforeMount(() => {
  getBreadcrumb();
});
</script>

<style lang="scss" scoped>
.app-breadcrumb.el-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 50px;
  margin-left: 8px;
}

// 覆盖 element-plus 的样式
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.lastItem {
  color: #d1daec;
}

.item {
  color: #fff;
  &:hover {
    // cursor: pointer;
  }
}

.noClickItem {
  color: #d1daec;
  &:hover {
    cursor: unset;
  }
}
</style>
