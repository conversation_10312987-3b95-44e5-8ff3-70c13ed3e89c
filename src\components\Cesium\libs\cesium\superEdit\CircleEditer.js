import * as Cesium from 'cesium';
import {
  addPropertiesToEntity,
  getPropertyByKey,
  calculatePointFromCenter,
  toCartesian3,
  toNumber,
  toDegrees
} from '../common';
import { generateKey } from '@/utils';
import { dialogDataRect } from '@/views/flight-manage/flight-area/flightAreaHandle';

//#region 绘制部分
let viewer = null;
let handler = null;
let labelEntity = null;
const endpointIcon = new URL('@/assets/plan/wrj/endpoint.png', import.meta.url).href;
const midpointIcon = new URL('@/assets/plan/wrj/middlepoint.png', import.meta.url).href;
//#region 编辑部分
export let isCircleEdit = ref(false);

function getOriginalCircleInfo() {
  return {
    radius: 0,
    position: []
  };
}
let originalCircleInfo = null;
export let circleEditEntity = null;
let editObjectGlobal = null;
let editCenterPoint = null;
let editBorderPoint = null;
// 适合多有圆形的编辑方法
export const editCircle = (v, circle, CallBack) => {
  // 检查是否满足绘制条件
  if (!v || !circle || !circle.ellipse || isCircleEdit.value) {
    return;
  }
  try {
    viewer = v;
    editObjectGlobal = null;
    circleEditEntity = circle;
    originalCircleInfo = getOriginalCircleInfo();
    let currentPoint = null;
    let newRadius = 0; // 为了保证 name 唯一 这里使用 generateKey() 生成唯一值
    let uniqueName = generateKey();
    let flightAreaType_ = getPropertyByKey(circle, 'flightAreaType');
    let drawResultObject = {
      geomType: 'circle',
      action: 'edit',
      flightAreaType: flightAreaType_ || 'dfence',
      id: getPropertyByKey(circle, 'id'),
      center: [],
      centerEntity: null,
      radius: 1,
      entity: null,
      startPosition: null,
      endPosition: null,
      title: getPropertyByKey(circle, 'title') || '',
      color: getPropertyByKey(circle, 'color') || '',
      area: 0,
      length: 0
    };
    dialogDataRect.action = 'edit';
    dialogDataRect.geomType = 'circle';
    dialogDataRect.id = drawResultObject.id;
    dialogDataRect.entity = circle;
    // 创建圆心
    let properties = circle.properties ?? null;
    if (!properties) {
      return;
    }
    document.body.style.cursor = 'default';
    drawResultObject.startPosition = toCartesian3(properties.center._value);
    drawResultObject.center = toDegrees(drawResultObject.startPosition);
    //生成圆心编辑点
    editCenterPoint = createPoint(viewer, drawResultObject.startPosition, midpointIcon);
    editObjectGlobal = drawResultObject;
    // 生成边界点
    let radius = toNumber(properties.radius._value, 2);
    let pointBorderC3 = calculatePointFromCenter(toDegrees(drawResultObject.startPosition), radius);
    if (!pointBorderC3) {
      return;
    }
    editBorderPoint = createPoint(viewer, pointBorderC3, endpointIcon);
    editBorderPoint.name = uniqueName;
    drawResultObject.endPosition = pointBorderC3;
    drawResultObject.radius = Cesium.Cartesian3.distance(drawResultObject.startPosition, drawResultObject.endPosition);
    drawResultObject.area = toNumber(drawResultObject.radius * drawResultObject.radius * Math.PI, 2);
    drawResultObject.area = parseArea(drawResultObject.area);
    drawResultObject.length = toNumber(drawResultObject.radius * 2 * Math.PI, 2);
    originalCircleInfo.radius = drawResultObject.radius;
    originalCircleInfo.position = drawResultObject.center;
    // 这里创建label辅助
    createOrUpdateLabelEntity(
      drawResultObject,
      Cesium.Cartesian3.midpoint(drawResultObject.startPosition, drawResultObject.endPosition, new Cesium.Cartesian3())
    );
    isCircleEdit.value = true;
    CallBack &&
      CallBack({
        data: drawResultObject,
        circle: circle
      });
    // 事件监听
    circle.handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    //点击事件
    circle.handler.setInputAction(event => {
      let feature_ = viewer.scene.pick(event.position);
      if (Cesium.defined(feature_)) {
        if (feature_.id.name === uniqueName) {
          currentPoint = feature_.id;
        }
      } else {
        currentPoint = null;
        return;
      }
      isCircleEdit.value = true;
      editObjectGlobal = drawResultObject;
      circleEditEntity = circle;
      dialogDataRect.entity = circle;
      // 这里对外传数据进行窗体变更
      if (isCircleEdit.value) {
        CallBack &&
          CallBack({
            data: drawResultObject,
            circle: circle,
            clickType: 'LEFT_DOWN',
            event: 'update'
          });
      }
    }, Cesium.ScreenSpaceEventType.LEFT_DOWN);

    // 对鼠标移动事件的监听
    circle.handler.setInputAction(event => {
      //获取加载地形后对应的经纬度和高程：地标坐标
      let ray = viewer.camera.getPickRay(event.endPosition);
      let newCartesian = viewer.scene.globe.pick(ray, viewer.scene);
      if (!newCartesian) {
        return;
      }
      if (currentPoint == null) {
        return;
      }
      // 鼠标状态
      let pickedObject = viewer.scene.pick(event.endPosition);
      if (Cesium.defined(pickedObject)) {
        if (pickedObject.id.name === uniqueName) {
          document.body.style.cursor = 'pointer';
        } else {
          document.body.style.cursor = 'default';
        }
      } else {
        document.body.style.cursor = 'default';
      }

      //更新当前点的位置
      currentPoint.position = newCartesian;
      drawResultObject.endPosition = newCartesian;
      drawResultObject.radius = Cesium.Cartesian3.distance(
        drawResultObject.startPosition,
        drawResultObject.endPosition
      );
      drawResultObject.area = toNumber(drawResultObject.radius * drawResultObject.radius * Math.PI, 2);
      drawResultObject.area = parseArea(drawResultObject.area);
      drawResultObject.length = toNumber(drawResultObject.radius * 2 * Math.PI, 2);
      //  计算 cartesian, cartesian2中间点 这里在移动的时添加 一个label 用于展示半径 这里的label 位置在圆心和圆心连线中点
      const midpoint = Cesium.Cartesian3.midpoint(
        drawResultObject.startPosition,
        newCartesian,
        new Cesium.Cartesian3()
      );
      // 标注 如果存在了就直接更新位置和 label值
      createOrUpdateLabelEntity(drawResultObject, midpoint);
      //移动的是半径点，则更新半径
      if (currentPoint.name == uniqueName) {
        viewer.scene.screenSpaceCameraController.enableRotate = false;
        viewer.scene.screenSpaceCameraController.enableZoom = false;
        let centerTemp = viewer.scene.globe.ellipsoid.cartesianToCartographic(
          circle.position.getValue(Cesium.JulianDate.now())
        );
        let radiusTemp = viewer.scene.globe.ellipsoid.cartesianToCartographic(
          currentPoint.position.getValue(Cesium.JulianDate.now())
        );
        let geodesic = new Cesium.EllipsoidGeodesic();
        geodesic.setEndPoints(centerTemp, radiusTemp);
        newRadius = geodesic.surfaceDistance;
        circle.ellipse.semiMinorAxis = new Cesium.CallbackProperty(function (time, result) {
          return newRadius;
        }, false);
        circle.ellipse.semiMajorAxis = new Cesium.CallbackProperty(function (time, result) {
          return newRadius;
        }, false);
        circleEditEntity = circle;
        editObjectGlobal = drawResultObject;
        // 这里对外传数据进行窗体变更
        if (isCircleEdit.value) {
          CallBack &&
            CallBack({
              data: drawResultObject,
              circle: circle,
              clickType: 'LEFT_UP',
              event: 'update'
            });
        }
      }
      //移动的是圆中心，则更新圆中心
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    // 对鼠标抬起事件的监听
    circle.handler.setInputAction(event => {
      currentPoint = null;
      viewer.scene.screenSpaceCameraController.enableRotate = true;
      viewer.scene.screenSpaceCameraController.enableZoom = true;
    }, Cesium.ScreenSpaceEventType.LEFT_UP);
  } catch (error) {}
};
export const closeCircleEdit = circle => {
  if (!circle || !circle.handler) {
    return;
  }
  document.body.style.cursor = 'default';
  //移除地图事件
  circle.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOWN);
  circle.handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
  circle.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_UP);
  if (editObjectGlobal && viewer) {
    viewer.scene.screenSpaceCameraController.enableRotate = true;
    viewer.scene.screenSpaceCameraController.enableZoom = true;
  }
  isCircleEdit.value = false;
  updateProperty(circle);
  clearLabelEntity();
  return circle;
};
export const cancelCircleEdit = () => {
  if (!originalCircleInfo || !circleEditEntity || !viewer) {
    return;
  }
  isCircleEdit.value = false;
  circleEditEntity.properties.radius = originalCircleInfo.radius;
  const r = originalCircleInfo.radius || 0;
  circleEditEntity.ellipse.semiMinorAxis = new Cesium.CallbackProperty(function (time, result) {
    return r || 0;
  }, false);
  circleEditEntity.ellipse.semiMajorAxis = new Cesium.CallbackProperty(function (time, result) {
    return r || 0;
  }, false);
  closeCircleEdit(circleEditEntity);
};
/**
 * 更新属性信息
 * @param {*} circle
 */
const updateProperty = circle => {
  circle.properties.radius = circle.ellipse.semiMinorAxis.getValue();
};

//#endregion

//#region 公用部分
/**
 * 米到公里的转换方法
 * @param {*} num
 * @returns
 */

const parseFloat = num => {
  num = toNumber(num, 3);
  let result =
    Number(num) === 0
      ? ''
      : Number(num) > 1000
      ? (Number(num) / 1000).toFixed(2) + ' km'
      : Number(num).toFixed(2) + ' m';
  return result;
};

/**
 * 通用产生采集点
 * @param {*} cartesian
 * @returns
 */
export function createPoint(viewer, cartesian, iconurl = endpointIcon) {
  let point = viewer.entities.add({
    position: toCartesian3(cartesian),
    billboard: {
      image: iconurl || endpointIcon,
      width: 14,
      height: 14,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      disableDepthTestDistance: Number.POSITIVE_INFINITY // 点总是在最上层
    }
  });
  return point;
}
//#endregion

//#region 创建辅助部分

// 清除辅助部分
export function clearLabelEntity() {
  if (!viewer) {
    return;
  }
  if (labelEntity) {
    viewer.entities.remove(labelEntity);
    labelEntity = null;
  }
  if (editBorderPoint) {
    viewer.entities.remove(editBorderPoint);
    editBorderPoint = null;
  }
  if (editCenterPoint) {
    viewer.entities.remove(editCenterPoint);
    editCenterPoint = null;
  }
}
// 创建辅助部分
function createOrUpdateLabelEntity(drawResultObject = null, midpoint = null) {
  if (!viewer || !midpoint || !drawResultObject) {
    return;
  }
  if (labelEntity) {
    labelEntity.position = midpoint;
    labelEntity.label.text = parseFloat(drawResultObject.radius);
  } else {
    labelEntity = viewer.entities.add({
      position: midpoint,
      label: {
        text: parseFloat(drawResultObject.radius),
        show: true,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        font: '30px monospace', // 字体边框
        outline: true,
        fillColor: Cesium.Color.WHITE,
        outlineWidth: 5,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -30),
        showBackground: true,
        backgroundColor: new Cesium.Color(0.117, 0.117, 0.117, 0.7),
        eyeOffset: new Cesium.Cartesian3(0, 0, 2),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND, //贴地
        disableDepthTestDistance: Number.POSITIVE_INFINITY // 点总是在最上层
      },
      polyline: {
        positions: new Cesium.CallbackProperty(function (time, result) {
          let arr = [drawResultObject.startPosition, drawResultObject.endPosition];
          if (!drawResultObject.startPosition || !drawResultObject.endPosition) {
            return [];
          }
          return arr;
        }, false),
        width: 5,
        material: new Cesium.PolylineOutlineMaterialProperty({
          color: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.CRIMSON,
          outlineWidth: 2
        }),
        clampToGround: true
      }
    });
  }
}

//#endregion
const parseArea = num => {
  return num;
  // if (num >= 10000) {
  //   // 如果面积大于等于 1,000,000 平方米，用平方公里表示
  //   const areaInSquareKilometers = (num / 1000000).toFixed(2);
  //   return areaInSquareKilometers + 'km²';
  // } else {
  //   // 否则用平方米表示
  //   const areaInSquareMeters = num.toFixed(2);
  //   return areaInSquareMeters + 'm²';
  // }
};
