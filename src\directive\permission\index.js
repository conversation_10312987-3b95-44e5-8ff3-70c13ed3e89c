import { useUserStoreHook } from '@/store/modules/user';
import _ from 'lodash';

/**
 * Vue 自定义指令，用于控制按钮权限的显示与隐藏。
 */
export const hasPerms = {
  /**
   * 指令绑定到元素后的处理函数，根据用户的角色权限和指令的参数，决定是否开启或关闭该元素。
   *
   * @param {HTMLElement} el - 被绑定指令的元素。
   * @param {Object} binding - 包含指令的参数。
   * @param {string} binding.value - DOM元素绑定的按钮权限标识。
   * <AUTHOR>
   */
  mounted(el, binding) {
    const { roles } = useUserStoreHook();

    // 按钮权限校验
    const { value } = binding;
    if (value) {
      const requiredPerms = value; // DOM绑定需要的按钮权限标识

      const hasPerm = _.includes(roles, requiredPerms * 1);

      if (!hasPerm) {
        el.parentNode && el.parentNode.removeChild(el);
      }
    } else {
      throw new Error('need perms! Like v-has-perms="73"');
    }
  }
};
