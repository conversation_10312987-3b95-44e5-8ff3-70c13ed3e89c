/**
 * STOMP WebSocket服务
 * 用于与后端STOMP WebSocket服务进行通信
 */
import Stomp from 'stompjs';

class StompWebSocketService {
  constructor() {
    this.stompClient = null;
    this.webSocket = null;
    this.connected = false;
    this.subscriptions = {};
    this.eventHandlers = {
      connect: [],
      disconnect: [],
      error: [],
      message: []
    };
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 3000; // 初始重连延迟3秒
    this.heartbeatTimer = null;
    this.debug = false;
  }

  /**
   * 初始化STOMP客户端
   * @param {string} url - WebSocket连接URL
   * @param {Object} headers - 连接头信息
   * @param {Object} options - 配置选项
   */
  init(url, headers = {}, options = {}) {
    // 如果已有连接，先断开
    if (this.stompClient) {
      this.disconnect();
    }

    this.debug = options.debug || false;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 5;
    this.reconnectDelay = options.reconnectDelay || 3000;

    try {
      this._log('正在连接WebSocket:', url);

      // 创建原生WebSocket
      this.webSocket = new WebSocket(url);

      // 添加WebSocket事件监听器
      this.webSocket.onopen = () => {
        this._log('WebSocket连接已打开');
      };

      this.webSocket.onerror = event => {
        console.error('WebSocket连接错误:', event);
        this._triggerEvent('error', { type: 'websocket', event });
        // WebSocket连接错误，安排重连
        this._scheduleReconnect(url, headers, options);
      };

      this.webSocket.onclose = event => {
        this._log('WebSocket连接已关闭, 代码:', event.code, '原因:', event.reason);

        // 只有在非正常关闭且之前连接成功的情况下尝试重连
        if (event.code !== 1000 && this.connected) {
          this._log('WebSocket异常关闭，尝试重连');
          this._scheduleReconnect(url, headers, options);
        } else if (event.code !== 1000) {
          // 如果连接尚未成功就关闭，也尝试重连
          this._log('WebSocket连接失败，尝试重连');
          this._scheduleReconnect(url, headers, options);
        }
      };

      // 创建STOMP客户端
      this.stompClient = Stomp.over(this.webSocket);

      // 配置心跳，确保使用传入的值或默认值
      this.stompClient.heartbeat.outgoing = options.heartbeatOutgoing || 30000;
      this.stompClient.heartbeat.incoming = options.heartbeatIncoming || 30000;

      // 配置调试日志
      if (!this.debug) {
        this.stompClient.debug = () => {};
      } else {
        this.stompClient.debug = message => {
          this._log('STOMP:', message);
        };
      }

      // 配置超时处理
      const connectTimeout = setTimeout(() => {
        if (!this.connected) {
          console.error('STOMP连接超时');
          // 如果连接尚未成功，触发错误并尝试重连
          this._triggerEvent('error', { type: 'timeout' });
          this._scheduleReconnect(url, headers, options);
        }
      }, 10000); // 10秒超时

      // 连接STOMP服务器
      this.stompClient.connect(
        headers,
        frame => {
          // 清除超时定时器
          clearTimeout(connectTimeout);

          this._log('STOMP连接成功');
          this.connected = true;
          this.reconnectAttempts = 0;
          this._triggerEvent('connect', { connected: true, frame });

          // 启动心跳
          // this._startHeartbeat(options);
        },
        error => {
          // 清除超时定时器
          clearTimeout(connectTimeout);

          console.error('STOMP连接错误:', error);
          this.connected = false;
          this._triggerEvent('error', { type: 'stomp', error });
          this._scheduleReconnect(url, headers, options);
        }
      );
    } catch (error) {
      console.error('初始化STOMP客户端出错:', error);
      this._scheduleReconnect(url, headers, options);
    }
  }

  /**
   * 订阅主题
   * @param {string} topic - 要订阅的主题
   * @param {Function} callback - 消息处理回调函数
   * @returns {string} 订阅ID
   */
  subscribe(topic, callback) {
    if (!this.stompClient || !this.connected) {
      console.warn('STOMP未连接，无法订阅主题:', topic);
      return null;
    }

    try {
      const subscription = this.stompClient.subscribe(topic, message => {
        try {
          // 解析消息体
          const body = message.body;
          const data = body ? JSON.parse(body) : {};

          // 触发全局消息事件
          this._triggerEvent('message', { topic, data });

          // 调用回调函数
          callback(data);
        } catch (error) {
          console.error(`处理主题 ${topic} 消息时出错:`, error);
        }
      });

      // 保存订阅对象
      const subscriptionId = subscription.id;
      this.subscriptions[subscriptionId] = subscription;

      return subscriptionId;
    } catch (error) {
      console.error(`订阅主题 ${topic} 时出错:`, error);
      return null;
    }
  }

  /**
   * 取消订阅
   * @param {string} subscriptionId - 订阅ID
   */
  unsubscribe(subscriptionId) {
    if (this.subscriptions[subscriptionId]) {
      try {
        this.subscriptions[subscriptionId].unsubscribe();
        delete this.subscriptions[subscriptionId];
        return true;
      } catch (error) {
        console.error(`取消订阅 ${subscriptionId} 时出错:`, error);
      }
    }
    return false;
  }

  /**
   * 发送消息
   * @param {string} destination - 目标地址
   * @param {Object} body - 消息体
   * @param {Object} headers - 消息头
   */
  send(destination, body = {}, headers = {}) {
    if (!this.stompClient || !this.connected) {
      console.warn('STOMP未连接，无法发送消息:', destination);
      return false;
    }

    try {
      const bodyStr = typeof body === 'string' ? body : JSON.stringify(body);
      this.stompClient.send(destination, headers, bodyStr);
      return true;
    } catch (error) {
      console.error(`发送消息到 ${destination} 时出错:`, error);
      return false;
    }
  }

  /**
   * 注册事件处理器
   * @param {string} event - 事件名称 (connect, disconnect, error, message)
   * @param {Function} handler - 事件处理函数
   */
  registerHandler(event, handler) {
    if (this.eventHandlers[event]) {
      this.eventHandlers[event].push(handler);
    }
  }

  /**
   * 移除事件处理器
   * @param {string} event - 事件名称
   * @param {Function} handler - 事件处理函数
   */
  removeHandler(event, handler) {
    if (this.eventHandlers[event]) {
      this.eventHandlers[event] = this.eventHandlers[event].filter(h => h !== handler);
    }
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {Object} data - 事件数据
   * @private
   */
  _triggerEvent(event, data) {
    if (this.eventHandlers[event]) {
      this.eventHandlers[event].forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`执行 ${event} 事件处理器时出错:`, error);
        }
      });
    }
  }

  /**
   * 启动心跳检测
   * @private
   */
  _startHeartbeat(options) {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
    }

    this.heartbeatTimer = setInterval(() => {
      if (this.connected && this.stompClient) {
        try {
          // 发送心跳消息
          this.send('/app/user/heartbeat', {
            user_id: options?.user_id || '',
            timestamp: Date.now(),
            deviceType: 'pc'
          });
        } catch (error) {
          console.error('发送心跳消息失败:', error);
        }
      }
    }, 10000); // 15秒发送一次心跳
  }

  /**
   * 安排重连
   * @param {string} url - WebSocket连接URL
   * @param {Object} headers - 连接头信息
   * @param {Object} options - 配置选项
   * @private
   */
  _scheduleReconnect(url, headers, options) {
    this.reconnectAttempts++;

    if (this.reconnectAttempts <= this.maxReconnectAttempts) {
      // 使用指数退避策略，每次重连延迟增加
      const delay = Math.min(
        30000, // 最大延迟30秒
        this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts - 1)
      );

      this._log(`将在 ${delay}ms 后尝试第 ${this.reconnectAttempts}/${this.maxReconnectAttempts} 次重连`);

      // 确保清理旧的连接资源
      this._cleanupConnection();

      setTimeout(() => {
        this._log(`正在尝试第 ${this.reconnectAttempts}/${this.maxReconnectAttempts} 次重连...`);
        this.init(url, headers, options);
      }, delay);
    } else {
      console.error(`已达到最大重连次数 (${this.maxReconnectAttempts})，停止重连`);
      this._triggerEvent('error', { type: 'max_reconnect_attempts' });

      // 清理所有资源
      this._cleanupConnection();
    }
  }

  /**
   * 清理连接资源
   * @private
   */
  _cleanupConnection() {
    // 清理心跳定时器
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }

    // 清理WebSocket连接
    if (this.webSocket) {
      try {
        this.webSocket.onclose = null; // 移除onclose处理器，避免触发额外的重连
        this.webSocket.onerror = null; // 移除onerror处理器
        this.webSocket.close();
      } catch (e) {
        // 忽略关闭错误
      }
      this.webSocket = null;
    }

    // 清理STOMP客户端
    if (this.stompClient) {
      try {
        this.stompClient.disconnect();
      } catch (e) {
        // 忽略断开错误
      }
      this.stompClient = null;
    }

    this.connected = false;
  }

  /**
   * 断开连接
   */
  disconnect() {
    // 取消所有订阅
    Object.keys(this.subscriptions).forEach(id => {
      this.unsubscribe(id);
    });

    // 如果已连接，发送断开事件
    if (this.connected) {
      this._triggerEvent('disconnect', { connected: false });
    }

    // 使用清理函数断开连接
    this._cleanupConnection();
  }

  /**
   * 获取连接状态
   * @returns {boolean} 是否已连接
   */
  isConnected() {
    return this.connected;
  }

  /**
   * 输出日志
   * @private
   */
  _log(...args) {
    if (this.debug) {
      console.log(...args);
    }
  }
}

// 导出单例实例
export default new StompWebSocketService();
