<template>
	<el-dialog
		:title="title"
    v-if="visible"
		:model-value="visible"
		width="812px"
    class="detailDialog"
		align-center
		:close-on-click-modal="false"
		@close="closeDialog"
	>
		<div class="detail-top">
      <el-row>
        <el-col :span="12"><div class="grid-content ep-bg-purple" />
          <div>
            <span class="detail-title">机场名称</span>
            <span class="detail-value" :title="detailData.nickname">{{ detailData.nickname }}</span>
          </div>
          <div>
            <span class="detail-title">设备SN</span>
            <span class="detail-value" :title="detailData.device_sn">{{ detailData.device_sn }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div>
            <span class="detail-title">计划名称</span>
            <span class="detail-value" :title="detailData.plan_name">{{ detailData.plan_name }}</span>
          </div>
          <div>
            <span class="detail-title">计划执行时间</span>
            <span class="detail-value">{{ `${detailData.start_date?.substring(0,10)} 至 ${detailData.end_date?.substring(0,10)}` }}</span>
          </div>
        </el-col>
      </el-row>
    </div>
    <div v-for="item in detailData.tasks" :key="item.id">
      <div class="detail-file">
        <div class="flex">
          <div class="file-top">
            <div class="file-name ellipsis" :title="item.scheme_name">{{ item.scheme_name }}</div>
            <div>
              <span :class="item.status == 1 ? 'status-green' : 'status-red'">{{ item.status_desc }}</span>
              <span class="file-time">计划维保时间：{{ item.due_date?.substring(0,10) }}</span>
            </div>
          </div>
          <div style="margin-top: 17px;">
            <el-button type="primary" v-if="item.status != 0" :icon="Download" @click="downLoadFile(item.attachments)">下载附件</el-button>
          </div>
        </div>
        <el-divider />
        <div class="check-box">
          <div style="margin-bottom: 15px" v-for="res in item.task_item_vos" :key="res.id">
            <span class="item-title" :title="res.item_name">{{ res.item_name }}</span>
            <span :class="res.status == 1 ? 'status-green' : 'status-red'" style="transform: translateY(-7px);">{{ res.status_desc }}</span>
          </div>
        </div>
      </div>
    </div>
	</el-dialog>
</template>
  
  <script setup>
  import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
  import { Download } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus';
  import { getPlanDetail } from '@/api/devices/maintenance';
  
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '查看维保记录'
    },
    formData: {
      type: Object,
      default(rawProps) {
        return {};
      }
    }
  });
  const form = reactive({});
  const detailData = ref({});
  watch(
    () => props.formData,
    (newVal, oldVal) => {
      Object.assign(form, newVal);
      console.log('点击编辑获取详情', form);
      initDetail()
    },
    { deep: true }
  );
  const emit = defineEmits(['update:visible', 'submit']);
  const loading = ref(false);
  // 关闭弹窗
  function closeDialog() {
    resetForm();
    emit('update:visible', false);
  }
  
  /**
   * 重置表单
   */
  function resetForm() {
    loading.value = false;
    Object.keys(form).map(key => {
      delete form[key];
    });
  }

  function downLoadFile (url,name='') {
    const link = document.createElement('a');
    link.download = name;
    link.href = url;
    document.body.appendChild(link);
    const evt = document.createEvent('MouseEvents');
    evt.initEvent('click', false, false);
    link.dispatchEvent(evt);
    document.body.removeChild(link);
  }

  function initDetail() {
    getPlanDetail({
      id: form.id
    }).then(res=>{
      detailData.value = res
    })
  }

  onMounted(() => {
  });
  
  defineExpose({ resetForm });
  </script>
<style lang="scss">
.detailDialog {
  overflow-x: hidden;
  .el-dialog__body{
    background-color: #001129 !important;
  }
}
::-webkit-scrollbar {
  width: 12px;  /* 设置滚动条的宽度 */
  background: #001129;
}
::-webkit-scrollbar-thumb {
  background-color: rgba(46,144,255,0.5);
	border-radius: 2px; /* 设置滚动条滑块的背景色 */
}
</style>
<style scoped lang="scss">
:deep(.el-overlay-dialog .el-dialog .el-dialog__body) {
  overflow-x: hidden;
}
::-webkit-scrollbar {
  width: 8px;  /* 设置滚动条的宽度 */
}
::-webkit-scrollbar-thumb {
  background-color: rgba(46,144,255,0.5);
	border-radius: 2px; /* 设置滚动条滑块的背景色 */
}
:deep(.el-dialog__body) {
  background-color: #001129;
}
.flex {
  display: flex;
  justify-content: space-between;
}
.detail-top {
  width: 768px;
  height: 96px;
  background: #11253E;
  padding: 16px;
  .detail-title {
    min-width: 100px;
    height: 35px;
    line-height: 35px;
    display: inline-block;
    font-family: SourceHanSansSC-Bold;
    font-size: 14px;
    color: #FFFFFF;
    text-align: left;
    line-height: 22px;
    font-weight: 700;
  }
  .detail-value {
    display: inline-block;
    max-width: 230px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;
    color: #FFFFFF;
    text-align: left;
    line-height: 22px;
    font-weight: 400;
    transform: translateY(7px);
  }
}
.detail-file{
  width: 768px;
  background: #11253E;
  padding: 16px;
  margin: 16px 0;
  .status-red,
  .status-green {
    width: 44px;
    height: 24px;
    display: inline-block;
    font-family: SourceHanSansSC-Regular;
    font-size: 12px;
    color: #F97066;
    background-color: #573c4a;
    padding: 0 auto;
    border-radius: 2px;
    text-align: center;
    line-height: 22px;
    font-weight: 400;
    margin-right: 8px;
  }
  .status-green {
    background: rgba(57,191,164,0.30);
    color: #35af99;
  }
  .file-time {
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;
    color: #FFFFFF;
    text-align: right;
    line-height: 22px;
    font-weight: 400;
  }
  .file-name {
    width: 400px;
    font-family: SourceHanSansSC-Bold;
    font-size: 16px;
    color: #FFFFFF;
    text-align: left;
    line-height: 24px;
    font-weight: 700;
    margin-bottom: 12px;
  }
  .item-title {
    min-width: 80px;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;
    color: #FFFFFF;
    text-align: left;
    line-height: 22px;
    font-weight: 400;
  }
  .check-box {
    width: 100%;
    height: 220px;
    overflow-y: auto;
  }
}
</style>
  