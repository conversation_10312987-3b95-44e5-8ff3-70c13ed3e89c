import request from '@/utils/request';
import { MAP_PATH, API_VERSION, APPLICTION_FEATURE } from '../config/index';

// 图元管理API主路径
const BASE_URL = MAP_PATH + API_VERSION + APPLICTION_FEATURE;

/**
 * 创建图元
 * @param {Object} data 图元数据
 * @returns {Promise}
 */
export function createFeature(data) {
  return request({
    url: `${BASE_URL}/create`,
    method: 'POST',
    data
  });
}

/**
 * 更新图元
 * @param {Object} data 图元数据
 * @returns {Promise}
 */
export function updateFeature(data) {
  return request({
    url: `${BASE_URL}/update`,
    method: 'PUT',
    data
  });
}

/**
 * 删除图元
 * @param {string|number} id 图元ID
 * @returns {Promise}
 */
export function deleteFeature(id) {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'DELETE'
  });
}

/**
 * 获取图元详情
 * @param {string|number} id 图元ID
 * @returns {Promise}
 */
export function getFeatureDetail(id) {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'GET'
  });
}

/**
 * 获取图元列表
 * @param {Object} data 查询参数
 * @returns {Promise}
 */
export function getFeatureList(data) {
  return request({
    url: `${BASE_URL}/list`,
    method: 'POST',
    data
  });
}

/**
 * 根据图层ID获取图元列表
 * @param {string|number} layerId 图层ID
 * @returns {Promise}
 */
export function getFeaturesByLayer(layerId) {
  return request({
    url: `${BASE_URL}/layer/${layerId}`,
    method: 'GET'
  });
}
