// 一些全局通用的接口
import request from '@/utils/request';

import { STATISTICS_PATH,  API_VERSION } from '../config/index';
import { useUserStoreHook } from '@/store/modules/user';

// 主路径
const BASE_URL = STATISTICS_PATH + API_VERSION ;




/**
 * 仪表盘统计信息
 *
 * @returns
 */
export function getSatisticsInfo(queryParams) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}`,
    method: 'get',
    params: queryParams,
  });
}