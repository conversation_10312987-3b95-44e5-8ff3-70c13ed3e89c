export const AMAP_KEY = '4f8bca37413d6394c1352a013063d658';
export const AMAP_VERSION = '1.4.15';
export const AMAP_MAP_TYPE = 'TMAP_NORMAL_MAP';
export const AMAP_CENTER = [118.06784, 24.54033];
export const AMAP_CITY_TYPE = '全国';
// 航飞模拟起飞路线
export const flyLineCoordinates = [
  [118.13703826953275, 24.491381285390815, 100],
  [118.13495607623122, 24.492536305205448, 100],
  [118.132566946069, 24.494446216659753, 100],
  [118.13201114148262, 24.495102977927857, 100],
  [118.13200179421773, 24.498502109106255, 100],
  [118.13293931520258, 24.501846984663032, 100],
  [118.1328718446315, 24.504403030741486, 100],
  [118.1325358358618, 24.510594590532477, 100],
  [118.13246128493859, 24.52025388391982, 100],
  [118.13387412422531, 24.521737054071195, 100],
  [118.13523262322042, 24.523467397344362, 100],
  [118.1354803083301, 24.525401207387333, 100],
  [118.13338821954835, 24.52678544336051, 100],
  [118.13072556109921, 24.526587696298122, 100],
  [118.1287693222373, 24.525598956317168, 100],
  [118.1280900726344, 24.52376976683007, 100],
  [118.12942140185794, 24.521446163387296, 100],
  [118.1297390652291, 24.52122392281896, 100],
  [118.13038355576214, 24.521016589156247, 100],
  [118.13066691165358, 24.52083643646246, 100],
  [118.13110020177078, 24.520593911344264, 100],
  [118.13147625955835, 24.520419815095906, 100],
  [118.13203916585246, 24.520183222533717, 100],
  [118.13227020107712, 24.52016078862414, 100],
  [118.13227076704021, 24.520160779326034, 100],
  [118.13227739599608, 24.519376166878175, 100],
  [118.13234860223372, 24.51056941754797, 100],
  [118.13254003324283, 24.50703446742611, 100],
  [118.1327105148033, 24.501967016004528, 100],
  [118.13173658899598, 24.498576115558947, 100],
  [118.13169863752086, 24.495260734564027, 100],
  [118.1321481878806, 24.494637818659726, 100],
  [118.13321436102228, 24.493682262874188, 100],
  [118.13427860177148, 24.492844164053224, 100],
  [118.13489258681801, 24.492359926632773, 100],
  [118.13714989585418, 24.491289213795994, 100],
  [118.13706763251258, 24.491424540536286, 100],
  [118.13703826953275, 24.491381285390815, 100]
];
// 航飞模拟起飞机场起落位置
export const airPortCoordinate = [118.13703826953275, 24.491381285390815, 0];
export const airPortModel = new URL('/resource/models/tjp.glb', import.meta.url)
  .href;
export const airModel = new URL('/resource/models/dajiang.glb', import.meta.url)
  .href;
export const targetPointIcon = new URL('/resource/png/zhd.png', import.meta.url)
  .href;
export const airPortIcon = new URL('/resource/png/ap.png', import.meta.url)
  .href;
export const videoUrl = new URL('/resource/video/1.mp4', import.meta.url).href;
// 本地存储用于保存航飞任务数据key
export const KEY_FLY_DATA_INFO = 'ff_flight_manage_data_key';
export const DRONE_STATUE = {
  FLYING: 'flying',
  PREPARE: 'prepare',
  STOP: 'stop'
};
