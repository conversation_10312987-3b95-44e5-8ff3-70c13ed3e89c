<template>
    <div class="width100 flex-column flex-justify-start flex-align-start" style="background-color: white;">

      <p class="fz16 ml10 mt15 mb10 color-text-title color-font-bold" style="color: #939393">
        在手动开始之前，请选择发布模式和直播类型
      </p>
    <div
      class="mt15 flex-row flex-align-center flex-justify-between"
      style="width: 100%;">
      <p class="ml10 mb0 fz16" style="color: black">
        选择视频发布模式:
      </p>
      <el-select
        style="width: 200px; margin-right: 20px;"
        placeholder="选择模式"
        v-model="publishModeSelected"
        @select="onPublishModeSelect"
      >
        <el-option
          v-for="item in publishModeList"
          :key="item.label"
          :value="item.value"
          :label="item.label"
        >
        </el-option>
      </el-select>
    </div>

    <div class="ml10 mr10" style="width: 96%; margin-top: -10px;">
      <el-divider />
    </div>
    <div
      class="flex-row flex-align-center flex-justify-between"
      style="width: 100%; margin-top: -10px;"
    >
      <p class="ml10 mb0 fz16">选择直播类型:</p>
      <el-select
        style="width: 200px; margin-right: 20px;"
        placeholder="选择直播类型"
        v-model="liveStreamStatus.type"
        @select="onLiveTypeSelect"
      >
        <el-option
          v-for="item in liveTypeList"
          :key="item.label"
          :value="item.value"
          :label="item.label"
        >
        </el-option>
      </el-select>
    </div>
    <div class="ml10 mr10" style="width: 96%; margin-top: -10px;">
      <el-divider />
    </div>
    <div class="width-100" style="margin-top: -10px;">
      <div class="ml10" style="width: 97%;">
        <span class="fz16">Param: </span>
        <span v-if="liveStreamStatus.type === ELiveTypeValue.Agora" style="word-break: break-all; color: #75c5f6;">
          <div class="flex-col flex-justify-center flex-align-center">
            <div>
              <span class="ml10">Token:</span>
              <el-input
                class="ml10"
                v-model:value="agoraParam.token"
                placeholder="Token"
              ></el-input>
            </div>
            <div>
              <span class="ml10">Channel:</span>
              <el-input
                class="ml10"
                v-model:value="agoraParam.channelId"
                placeholder="Channel"
              ></el-input>
            </div>
          </div>
        </span>
        <span v-else-if="liveStreamStatus.type === ELiveTypeValue.RTMP" style="word-break: break-all; color: #75c5f6;">{{ rtmpParam }}</span>
        <span v-else-if="liveStreamStatus.type === ELiveTypeValue.RTSP" style="word-break: break-all; color: #75c5f6;">{{ rtspParam }}</span>
        <span v-else-if="liveStreamStatus.type === ELiveTypeValue.GB28181" style="word-break: break-all; color: #75c5f6;">{{ gb28181Param }}</span>
        <span v-else></span>
      </div>

    </div>
    <div class="ml10 mr10" style="width: 96%; margin-top: -10px;">
      <el-divider />
    </div>
    <div class="mb20 flex-row flex-align-center flex-justify-center"
      style="width: 100%; ">
      <el-button class="flex-column fz20 flex-align-center flex-justify-center" style="width: 100px;" type="ghost" @click="onPlay">开始</el-button>
      <el-button class="flex-column fz20 flex-align-center flex-justify-center ml40" style="width: 100px;" type="ghost" @click="onStop">停止</el-button>
    </div>
    <el-button v-if="playVisiable" class="flex-column flex-align-center" shape="circle" @click="showLivingStatus"
      style="position: fixed; top: 13vh; left: 5vw; opacity: 0.8; background-color: rgb(0,0,0,0)">
      <template #icon><CaretRight style="font-size: 26px; color: " /></template>
    </el-button>

    <el-drawer  v-model="drawerVisible" width="280px" :modal="false" @close="closeDrawer">
      <div class="fz16 width-100">
        <div class="mt20" style=" margin-bottom: -10px;">
          <span class="fz20 flex-row flex-align-center flex-justify-center">
            <span style="color: greenyellow;">{{ liveState }}</span></span>
        </div>
        <el-divider />
        <div style=" margin-top: -10px; margin-bottom: -15px;">
          <span>Frame Rate:</span><span style="float: right; color: #75c5f6;">{{ liveStreamStatus.fps }}<span v-if="liveStreamStatus.fps != -1"> fps</span></span><br/>
        </div>
        <el-divider />
        <div style=" margin-top: -10px; margin-bottom: -10px;">
          <span>Video Bit Rate:</span><span style="float: right; color: #75c5f6;">{{ liveStreamStatus.videoBitRate }}<span v-if="liveStreamStatus.videoBitRate != -1"> kbps</span></span><br/>
        </div>
        <el-divider />
        <div style=" margin-top: -10px; margin-bottom: -10px;">
          <span>Audio Bit Rate:</span><span style="float: right; color: #75c5f6;">{{ liveStreamStatus.audioBitRate }}<span v-if="liveStreamStatus.audioBitRate != -1"> kbps</span></span><br/>
        </div>
        <el-divider />
        <div style=" margin-top: -10px; margin-bottom: -10px;">
          <span>Packet Loss Rate:</span><span style="float: right; color: #75c5f6;">{{ liveStreamStatus.dropRate }}<span v-if="liveStreamStatus.dropRate != -1"> %</span></span><br/>
        </div>
        <el-divider />
        <div style=" margin-top: -10px; margin-bottom: -10px;">
          <span>RTT:</span><span style="float: right; color: #75c5f6;">{{ liveStreamStatus.rtt }}<span v-if="liveStreamStatus.rtt != -1"> ms</span></span><br/>
        </div>
        <el-divider />
        <div style=" margin-top: -10px;">
          <span >Jitter:</span><span style="float: right; color: #75c5f6;">{{ liveStreamStatus.jitter }}</span><br/>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { CURRENT_CONFIG as config, CURRENT_CONFIG } from '@/api/http/config'
import apiPilot from '@/api/pilot-login/pilot-bridge';
import { useRouter } from 'vue-router';
import { ELiveStatusValue, EStatusValue, ELiveTypeName, ELiveTypeValue, EVideoPublishType } from '@/utils/constants'
import { CaretRight } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus'

const router = useRouter();
const publishModeList = [
  {
    value: EVideoPublishType.VideoOnDemand,
    label: EVideoPublishType.VideoOnDemand
  },
  {
    value: EVideoPublishType.VideoByManual,
    label: EVideoPublishType.VideoByManual
  },
  {
    value: EVideoPublishType.VideoDemandAuxManual,
    label: EVideoPublishType.VideoDemandAuxManual
  }
]
const liveTypeList = [
  {
    value: ELiveTypeValue.Agora,
    label: ELiveTypeName.Agora
  },
  {
    value: ELiveTypeValue.RTMP,
    label: ELiveTypeName.RTMP
  },
  {
    value: ELiveTypeValue.RTSP,
    label: ELiveTypeName.RTSP
  },
  {
    value: ELiveTypeValue.GB28181,
    label: ELiveTypeName.GB28181
  }
]
const agoraParam = reactive({
  uid: '2892130292',
  token: config.agoraToken,
  channelId: config.agoraChannel
})
const rtmpParam = {
  url: config.rtmpURL + new Date().getTime()
}
const rtspParam = {
  userName: CURRENT_CONFIG.rtspUserName,
  password: CURRENT_CONFIG.rtspPassword,
  port: CURRENT_CONFIG.rtspPort
}
const gb28181Param = {
  serverIp: CURRENT_CONFIG.gbServerIp,
  serverPort: CURRENT_CONFIG.gbServerPort,
  serverId: CURRENT_CONFIG.gbServerId,
  agentId: CURRENT_CONFIG.gbAgentId,
  password: CURRENT_CONFIG.gbPassword,
  agentPort: CURRENT_CONFIG.gbAgentPort,
  agentChannel: CURRENT_CONFIG.gbAgentChannel,
}

const playVisiable = ref(false)
const drawerVisible = ref(false)
const liveState = ref(EStatusValue.DISCONNECT)
const liveTypeSelected = ref()
const publishModeSelected = ref()
const liveStreamStatus = reactive({
  audioBitRate: -1,
  dropRate: -1,
  fps: -1,
  jitter: -1,
  quality: -1,
  rtt: -1,
  status: -1,
  type: -1,
  videoBitRate: -1
})

onMounted(() => {
  localStorage.setItem('isHome', 'false')
  const config = JSON.parse(apiPilot.getLiveshareConfig())
  liveStreamStatus.type = config.type
  refreshLiveType()

  window.liveStatusCallback = arg => {
    liveStatusCallback(arg)
  }
})

const liveStatusCallback = async (arg) => {
  liveStreamStatus.fps = arg.fps
  liveStreamStatus.audioBitRate = arg.audioBitRate
  liveStreamStatus.dropRate = arg.dropRate
  liveStreamStatus.jitter = arg.jitter
  liveStreamStatus.rtt = arg.rtt
  liveStreamStatus.videoBitRate = arg.videoBitRate
  liveStreamStatus.quality = arg.quality
  liveStreamStatus.type = arg.type
  liveStreamStatus.status = arg.status

  switch (liveStreamStatus.status) {
    case ELiveStatusValue.LIVING:
      liveState.value = EStatusValue.LIVING
      break
    case ELiveStatusValue.CONNECTED:
      liveState.value = EStatusValue.CONNECTED
      break
    default:
      liveState.value = EStatusValue.DISCONNECT
  }
}
function refreshLiveType () {
  switch (liveStreamStatus.type) {
    case ELiveTypeValue.Agora:
      liveTypeSelected.value = ELiveTypeName.Agora
      break
    case ELiveTypeValue.RTMP:
      liveTypeSelected.value = ELiveTypeName.RTMP
      break
    case ELiveTypeValue.RTSP:
      liveTypeSelected.value = ELiveTypeName.RTSP
      break
    case ELiveTypeValue.GB28181:
      liveTypeSelected.value = ELiveTypeName.GB28181
      break
    default:
      liveTypeSelected.value = ELiveTypeName.Unknown
  }
}

function closeDrawer () {

}

const onLiveTypeSelect = (val) => {
  liveStreamStatus.type = val
  refreshLiveType()
}
const onPublishModeSelect = (val) => {
  publishModeSelected.value = val
  apiPilot.setVideoPublishType(publishModeSelected.value)
}
const onPlay = () => {
  console.info('hhhhh',JSON.stringify(agoraParam))
  if (!publishModeSelected.value) {
    ElMessage.warning('Please select publish mode!')
    return
  }
  if (liveTypeSelected.value === ELiveTypeName.Unknown) {
    ElMessage.warning('Please select livestream type!')
    return
  }
  switch (liveStreamStatus.type) {
    case 1: {
      apiPilot.setLiveshareConfig(ELiveTypeValue.Agora, JSON.stringify(agoraParam))
      break
    }
    case 2: {
      apiPilot.setLiveshareConfig(ELiveTypeValue.RTMP, JSON.stringify(rtmpParam))
      break
    }
    case 3: {
      apiPilot.setLiveshareConfig(ELiveTypeValue.RTSP, JSON.stringify(rtspParam))
      break
    }
    case 4: {
      apiPilot.setLiveshareConfig(ELiveTypeValue.GB28181, JSON.stringify(gb28181Param))
      break
    }
  }
  const status = apiPilot.startLiveshare()
  if (status) {
    console.log('status',status)
    playVisiable.value = true
    drawerVisible.value = true
    ElMessage.success('success')
  }
}

const showLivingStatus = () => {
  drawerVisible.value = !drawerVisible.value
}

const onStop = () => {
  const status = apiPilot.stopLiveshare()
  if (status) {
    ElMessage.success('success')
    playVisiable.value = false
    drawerVisible.value = false
    setTimeout(() => {
      let key
      for (key in liveStreamStatus) {
        if (key === 'type') {
          continue
        }
        liveStreamStatus[key] = -1
      }
    }, 2000)
  }
}
</script>

<style lang="scss" scoped>
// @import '/@/styles/index.scss';
.green {
  color: green;
}
.red {
  color: rgb(253, 31, 31);
}
.blue {
  color: rgb(25, 0, 255);
}
</style>
