// 网络类型
export const NetworkStateTypeEnum = {
  FOUR_G: 1, // 4G网
  ETHERNET: 2 // 以太网
};

// 网络情况
export const NetworkStateQualityEnum = {
  NO_SIGNAL: 0,
  BAD: 1,
  POOR: 2,
  FAIR: 3,
  GOOD: 4,
  EXCELLENT: 5
};
// 雨量
// export const RainfallEnum = {
//   NONE: 0,
//   LIGHT_RAIN: 1,
//   MODERATE_RAIN: 2,
//   HEAVY_RAIN: 3
// };
export const RainfallEnum = {
  无雨: 0,
  小雨: 1,
  中雨: 2,
  暴雨: 3
};

export const WindDirectionEnum = {
  正北: 1,
  东北: 2,
  东: 3,
  东南: 4,
  南: 5,
  西南: 6,
  西: 7,
  西北: 8
};

// 机场当前状态 ,TODO 待验证后面的参数
export const EDockModeCode_en = {
  Disconnected: -1,
  Idle: 0,
  Debugging: 1,
  Remote_Debugging: 2,
  Upgrading: 3,
  Working: 4
};


// 机场当前状态 ,TODO 待验证后面的参数
export const EDockModeCode = {
  "离线": -1,
  "空闲中": 0,
  "现场调试": 1,
  "远程调试": 2,
  "固件升级中": 3,
  "作业中": 4
};

// 飞机状态
export const EModeCode_en = {
  Standby: 0,
  Preparing: 1,
  Ready: 2,
  Manual: 3,
  Automatic: 4,
  Waypoint: 5,
  Panoramic: 6,
  Active_Track: 7,
  ADS_B: 8,
  Return_To_Home: 9,
  Landing: 10,
  Forced_Landing: 11,
  Three_Blades_Landing: 12,
  Upgrading: 13,
  Disconnected: 14
};

export const EModeCode = {
  "待机": 0,
  "起飞准备": 1,
  "起飞准备完毕": 2,
  "手动飞行": 3,
  "自动起飞": 4,
  "航线飞行": 5,
  "全景拍照": 6,
  "智能跟随": 7,
  "ADS-B 躲避": 8,
  "自动返航": 9,
  "自动降落": 10,
  "强制降落": 11,
  "三桨叶降落": 12,
  "升级中": 13,
  "离线": 14,
  "APAS": 15,
  "虚拟摇杆状态": 16,
  "指令飞行": 17,
  "空中 RTK 收敛模式": 18,
  "机场选址中": 19
};

export const EGear = {
  A: 0,
  P: 1,
  NAV: 2,
  FPV: 3,
  FARM: 4,
  S: 5,
  F: 6,
  M: 7,
  G: 8,
  T: 9
};

export const MaintainStatus = {
  state: null, //number
  last_maintain_type: null, //number
  last_maintain_time: null, //number
  last_maintain_work_sorties: null //number
};

export const DrcStateEnum = {
  DISCONNECT: 0,
  CONNECTING: 1,
  CONNECTED: 2
};

// 机场存储容量：总容量（单位：KB）、已使用（单位：KB）
export const AirportStorage = {
  total: null, //number // 单位：KB
  used: null //number
};

// 舱盖状态
export const CoverStateEnum = {
  Close: 0, // 关闭
  Open: 1, // 打开
  HalfOpen: 2, // 半打开
  Failed: 3 // 失败
};

// 推杆状态
export const PutterStateEnum = {
  Close: 0, // 关闭
  Open: 1, // 打开
  HalfOpen: 2, // 半打开
  Failed: 3 // 失败
};

// 充电状态
export const ChargeStateEnum = {
  NotCharge: 0, // 空闲
  Charge: 1 // 正在充电
};

export const DroneChargeState = {
  state: ChargeStateEnum,
  capacity_percent: ''
};

// 补光灯状态
export const SupplementLightStateEnum = {
  Close: 0, // 关闭
  Open: 1 // 打开
};

// 机场声光报警状态
export const AlarmModeEnum = {
  CLOSE: 0, // 关闭
  OPEN: 1 // 开启
};

// 电池保养
export const BatteryStoreModeEnum = {
  BATTERY_PLAN_STORE: 1, // 电池计划存储策略
  BATTERY_EMERGENCY_STORE: 2 // 电池应急存储策略
};

// 飞行器电池保养
export const DroneBatteryStateEnum = {
  NoMaintenanceRequired: 0, // 0-无需保养
  MaintenanceRequired: 1, // 1-待保养
  MaintenanceInProgress: 2 // 2-正在保养
};

export const DroneBatteryModeEnum = {
  CLOSE: 0, // 关闭
  OPEN: 1 // 开启
};

// 4g链路连接状态
export const FourGLinkStateEnum = {
  CLOSE: 0, // 断开
  OPEN: 1 // 连接
};

//  Sdr链路连接状态
export const SdrLinkStateEnum = {
  CLOSE: 0, // 断开
  OPEN: 1 // 连接
};

// 机场的图传链路模式
export const LinkWorkModeEnum = {
  SDR: 0, // sdr模式
  FourG_FUSION_MODE: 1 // 4G融合模式
};

export const DockBasicOsd = {
  network_state: {
    type: NetworkStateTypeEnum,
    quality: null, //number
    rate: null //number
  },
  drone_charge_state: {
    state: null, //number
    capacity_percent: null //number
  },
  drone_in_dock: false,
  rainfall: RainfallEnum,
  wind_speed: null, //number
  environment_temperature: null, //number
  temperature: null, //number
  humidity: null, //number
  latitude: null, //number
  longitude: null, //number
  height: null, //number
  alternate_land_point: {
    latitude: null, //number
    longitude: null, //number
    height: null, //number
    safe_land_height: null, //number
    is_configured: null //number
  },
  first_power_on: null, //number
  positionState: {
    gps_number: 0, //number
    is_fixed: 0, //number
    rtk_number: 0, //number
    is_calibration: 0, //number
    quality: 0 //number
  },
  storage: {
    total: null, //number
    used: null //number
  },
  mode_code: -1, //number
  cover_state: null, //number
  supplement_light_state: null, //number
  emergency_stop_state: null, //number
  air_conditioner: {
    air_conditioner_state: null, //number
    switch_time: null //number
  },
  battery_store_mode: BatteryStoreModeEnum, // 电池保养(存储)模式
  alarm_state: AlarmModeEnum, // 机场声光报警状态
  putter_state: null, //number
  sub_device: {
    device_sn: '',
    device_model_key: '',
    device_online_status: null, //number
    device_paired: null //number
  }
  // live_capacity?: LiveCapacity; // 直播能力
  // live_status?: Array<LiveStatus>; // 直播状态
};

export const DockLinkOsd = {
  drc_state: DrcStateEnum,
  flighttask_prepare_capacity: null, //number
  flighttask_step_code: null, //number
  media_file_detail: {
    remain_upload: null //number
  },
  sdr: {
    up_quality: '',
    down_quality: '',
    frequency_band: null //number
  },
  wireless_link: {
    // 图传链路<会包括4G和sdr信息
    dongle_number: null, //number // dongle 数量
    ['4g_link_state']: FourGLinkStateEnum, // 4g_link_state
    sdr_link_state: SdrLinkStateEnum, // sdr链路连接状态
    link_workmode: LinkWorkModeEnum, // 图传链路模式
    sdr_quality: null, //number // sdr信号质量 0-5
    ['4g_quality']: null, //number // 4G信号质量 0-5
    ['4g_freq_band']: null, //number
    ['4g_gnd_quality']: null, //number
    ['4g_uav_quality']: null, //number
    sdr_freq_band: null //number
  }
};

export const DockWorkOsd = {
  job_number: null, //number
  acc_time: null, //number
  activation_time: null, //number
  maintain_status: {
    maintain_status_array: MaintainStatus
  },
  electric_supply_voltage: null, //number
  working_voltage: '',
  working_current: '',
  backup_battery: {
    voltage: null, //number
    temperature: null, //number
    switch: null //number
  },
  drone_battery_maintenance_info: {
    // 飞行器电池保养信息
    maintenance_state: DroneBatteryStateEnum, // 保养状态
    maintenance_time_left: null //number // 电池保养剩余时间(小时)
  }
};

// 设备拓扑osd信息
export const deviceInfo = {
  // 遥控器
  gateway: {
    capacity_percent: '', //string
    transmission_signal_quality: '', //string
    longitude: null, //number,
    latitude: null //number,
  },
  // 机场
  dock: {
    basic_osd: DockBasicOsd,
    link_osd: DockLinkOsd,
    work_osd: DockWorkOsd
  },
  // 飞机
  device: {
    longitude: null, //number,
    latitude: null, //number,
    gear: null, //number,
    mode_code: 14, //number,
    height: '--', //string
    home_distance: '--', //string
    horizontal_speed: '--', //string
    vertical_speed: '--', //string
    wind_speed: '--', //string
    wind_direction: '', //string
    elevation: '--', //string
    position_state: {
      gps_number: '', //string
      is_fixed: null, //number,
      rtk_number: ''
    },
    battery: {
      capacity_percent: '', //string
      landing_power: '', //string
      remain_flight_time: null, //number,
      return_home_power: ''
    }
    //   night_lights_state?: NightLightsStateEnum;// 夜航灯开关
    //   height_limit?: null,//number;// 限高设置
    //   distance_limit_status?: DistanceLimitStatus;// 限远开关
    //   obstacle_avoidance?: ObstacleAvoidance;// 飞行器避障开关设置
    //   cameras?: DeviceOsdCamera[]
  }
};

// OSD 显示设备相关信息
// export const osdVisible = {
//   sn: 'fjc',
//   model: 'M3TD',
//   callsign: '',
//   visible: false,
//   is_dock: true,
//   gateway_sn: '',
//   gateway_callsign: 'fjc',
//   payloads: null
// };
export const osdVisible = {
  visible: false,
  dock_sn: '',
  device_sn: '',
  dock_callsign: '',
  device_callsign: ''
};
