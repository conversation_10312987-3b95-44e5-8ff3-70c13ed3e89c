import { DroneInfo } from './DroneInfo';
import { PayloadInfo } from './PayloadInfo';
import {
  FLYTO_WAY_LINE_MODE,
  FINISH_ACTION,
  EXIT_ON_RCLOST,
  EXECUTE_RCLOST_ACTION
} from '@/views/plan/newplan/kmz/props';
class WaylineMissionConfig {
  constructor() {}
  initMissionConfig(options = {}) {
    // 设置默认值
    this.wpml_flyToWaylineMode = options.flyToWaylineMode || FLYTO_WAY_LINE_MODE.safely;
    this.wpml_finishAction = options.finishAction || FINISH_ACTION.goHome;
    this.wpml_exitOnRCLost = options.exitOnRCLost || EXIT_ON_RCLOST.goContinue;
    this.wpml_executeRCLostAction = options.executeRCLostAction || EXECUTE_RCLOST_ACTION.goBack;
    this.wpml_takeOffSecurityHeight = options.takeOffSecurityHeight || 20;
    this.wpml_globalTransitionalSpeed = options.globalTransitionalSpeed || 10;
    this.wpml_globalRTHHeight = options.globalRTHHeight || 100;
    // 初始化无人机信息，默认为空
    this.wpml_droneInfo = new DroneInfo({
      droneEnumValue: options.droneInfo?.droneEnumValue || null,
      droneSubEnumValue: options.droneInfo?.droneSubEnumValue || null
    });
    this.wpml_payloadInfo = new PayloadInfo({
      payloadEnumValue: options.payloadInfo?.payloadEnumValue || null,
      payloadSubEnumValue: options.payloadInfo?.payloadSubEnumValue || null,
      payloadPositionIndex: options.payloadInfo?.payloadPositionIndex || null
    });
  }

  //#region get set
  setFlyToWaylineMode(value) {
    this.wpml_flyToWaylineMode = value;
  }
  setFinishAction(value) {
    this.wpml_finishAction = value;
  }
  setExitOnRCLost(value) {
    this.wpml_exitOnRCLost = value;
  }

  setExecuteRCLostAction(value) {
    this.wpml_executeRCLostAction = value;
  }

  setTakeOffSecurityHeight(value) {
    this.wpml_takeOffSecurityHeight = value;
  }

  setGlobalTransitionalSpeed(value) {
    this.wpml_globalTransitionalSpeed = value;
  }

  setGlobalRTHHeight(value) {
    this.wpml_globalRTHHeight = value;
  }

  getFlyToWaylineMode() {
    return this.wpml_flyToWaylineMode;
  }

  getFinishAction() {
    return this.wpml_finishAction;
  }

  getExitOnRCLost() {
    return this.wpml_exitOnRCLost;
  }

  getExecuteRCLostAction() {
    return this.wpml_executeRCLostAction;
  }

  getTakeOffSecurityHeight() {
    return this.wpml_takeOffSecurityHeight;
  }

  getGlobalTransitionalSpeed() {
    return this.wpml_globalTransitionalSpeed;
  }

  getGlobalRTHHeight() {
    return this.wpml_globalRTHHeight;
  }
  getDroneInfo() {
    return this.wpml_droneInfo;
  }
  gePayloadInfo() {
    return this.wpml_payloadInfo;
  }

  //#endregion

  setDroneDetails(enumValue, subEnumValue) {
    this.wpml_droneInfo.wpml_droneEnumValue = enumValue;
    this.wpml_droneInfo.wpml_droneSubEnumValue = subEnumValue;
  }

  setPayloadDetails(payloadEnumValue, payloadSubEnumValue, payloadPositionIndex) {
    this.wpml_payloadInfo.wpml_payloadEnumValue = payloadEnumValue;
    this.wpml_payloadInfo.wpml_payloadSubEnumValue = payloadSubEnumValue;
    this.wpml_payloadInfo.wpml_payloadPositionIndex = payloadPositionIndex;
  }
}

export { WaylineMissionConfig };
