@import './sidebar.scss';
@import './reset.scss';
@import './dark.scss';
@import './flexstyle.scss';
@import './ff-cloud/iconfont/iconfont.css';

.app-container {
  padding: 20px;

  .search {
    padding: 18px 0 0 10px;
    margin-bottom: 10px;
    border-radius: 4px;
    // border: 1px solid var(--el-border-color-light);
    box-shadow: var(--el-box-shadow-light);
    background-color: var(--el-bg-color-overlay);
  }
}

.el-table th.el-table__cell {
  background-color: $tableHeaderBg !important;
  color: $tableHeaderColor;
  padding: $tableHeaderPadding;
}
