<template>
  <el-tooltip :content="dockInfo.nickname" placement="top">
    <div :style="{ width: `${calculatedWidth}px` }" class="pop-wrapper">{{ dockInfo.short_nickname }}</div>
  </el-tooltip>
</template>

<script>
export default {
  name: 'airportNamePopup'
};
</script>
<script setup>
import { onMounted, onUnmounted, defineExpose, reactive, ref, toRefs } from 'vue';

let calculatedWidth = ref(100);
let dockInfo = reactive({
  nickname: '', //机场名
  dock_sn: '', //机场SN号
  dock_xyz: '', //机场XYZ
  device_nickname: '', //无人机名
  short_nickname: '', //无人机名省略名
  device_sn: '', //无人机SN号
  device_xyz: ''
});

function calculateWidth() {
  dockInfo.short_nickname = dockInfo.nickname;
  if (dockInfo.short_nickname.length > 10) {
    dockInfo.short_nickname = dockInfo.short_nickname.substring(0, 10) + '...';
  }
  const nickname = dockInfo.short_nickname;
  const charCount = nickname.length;
  if (charCount > 5) {
    calculatedWidth.value = charCount * 18;
  }
}

/**
 * 设置组件数据
 * @param {*} options {donkInfo:{},osdInfo:{}}
 */
const setComponentData = options => {
  if (options && typeof options === 'object') {
    for (const key in options) {
      if (options.hasOwnProperty(key)) {
        dockInfo[key] = options[key];
      }
    }
  }
};
// 对外抛出方法
defineExpose({
  setComponentData
});

onMounted(() => {
  setTimeout(() => {
    calculateWidth();
  }, 50);
});
onUnmounted(() => {});
onBeforeUnmount(() => {});
</script>

<style scoped lang="scss">
.pop-wrapper {
  position: absolute;
  //width: 100px;
  height: 29px;
  user-select: none;
  z-index: 2;
  font-family: SourceHanSansSC-Bold;
  background: rgba(17, 37, 62, 0.75);
  border: 1px solid #2fa5ff;
  color: #fff;
  text-align: center;
  line-height: 26px;
}
</style>
