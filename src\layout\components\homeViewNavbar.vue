<script setup>
import { onMounted, onUnmounted, reactive, computed } from 'vue';
import { refreshToken } from '@/api/devices';
import { useRouter } from 'vue-router';
import { useStorage } from '@vueuse/core';
import { getDeptSysSetting } from '@/api/wayline';
import { constantRoutes } from '@/router/index';
import AvatarArea from './AvatarArea/index.vue';
// import { getWeather } from '@/api/dept';
import { getWeather } from '@/api/system/dept'; 
// 导入所有需要动态加载的组件
import FlightRecord from '@/views/history-record/flight-record/index.vue';
import AirRoute from '@/views/plan/airroute/index.vue';
import MediaLibrary from '@/views/result-manage/media-library/index.vue';
import LiveStream from '@/views/videos/live-stream/index.vue';
import AlarmManager from '@/views/work-order/alarmManager/index.vue';
import { authorityShow } from '@/utils/authority';
import { Message } from '@element-plus/icons-vue'
const defaultLogoIcon = new URL('/resource/images/default-logo.jpeg', import.meta.url).href;;
let authority = localStorage.getItem('menu') && JSON?.parse(localStorage.getItem('menu'));
let timerRef = null;
const router = useRouter();
let timer = null;
const token = useStorage('accessToken', '');
const path = ref('/dashboard');
const menuActive = ref('');
const data = reactive({});
const weatherData = ref({});
const timeData = ref({});
const loading = ref(false);
const weatherLogo = ref('192.168.15.27:3000/src/assets/homeViewImg/weather/多云.png');
const menuList = ref([
  {
    name: '飞行记录',
    url: '/flight-record',
    authority: 'flight-record'
  },
  {
    name: '航线管理',
    url: '/plan',
    authority: 'plan'
  },
  {
    name: '媒体库',
    url: '/media-library',
    authority: 'media-library'
  },
  {
    name: '视频墙',
    url: '/live-stream',
    authority: 'live-stream'
  }
]);

// Add dialog state
const dialogVisible = ref(false);
const currentComponent = ref(null);
const dialogTitle = ref('');
//预警信息
const alarmDialog = reactive({
  visible: false,
  title:''
});

// 过滤权限菜单
const showMenu = ref([]);
function handleMenu() {
  if (authority === null) {
    authority = localStorage.getItem('menu') && JSON?.parse(localStorage.getItem('menu'));
  }
  menuList.value.forEach((item, index) => {
    if (authority.indexOf(item.authority) == -1) {
      item.show = false;
    } else {
      item.show = true;
    }
  });
  showMenu.value = menuList.value.filter(item => item.show == true);
}

onUnmounted(() => {
  clearInterval(timerRef);
  window.$bus.off('updateAlarmInfo');
});

onMounted(() => {
  initWeather();
  initData();
  handleMenu();
  timerRef = setInterval(() => {
    refreshToken({}).then(res => {
      token.value = res.access_token;
    });
  }, 1000 * 60 * 15);
  getTime();
  timer = setInterval(() => {
    getTime();
  }, 1000);
  //监听预警信息更新
  window.$bus.on('updateAlarmInfo', (data)=>{
    console.log("收到预警信息",data);
    //调用更新角标接口
  });
});

function initData() {
  getDeptSysSetting({}).then(res => {
    const { base_config = {} } = res;
    data.name = base_config.sys_name;
    data.logo = base_config.sys_logo_url;
  });
}

function initWeather() {
  loading.value = true;
  getWeather({}).then(res => {
    weatherData.value = res;
    weatherLogo.value = `@/assets/homeViewImg/weather/${res.weather}.png`;
    loading.value = false;
  });
}

// 创建组件映射对象
const componentMap = {
  '/flight-record': FlightRecord,
  '/plan': AirRoute,
  '/media-library': MediaLibrary,
  '/live-stream': LiveStream
};

function menuClick(item) {
  menuActive.value = item.url;
  dialogTitle.value = item.name;
  // 使用组件映射
  currentComponent.value = componentMap[item.url];
  dialogVisible.value = true;
}

// 关闭弹窗回调
function handleDialogClose() {
  currentComponent.value = null;
  menuActive.value = '';
}

function getTime() {
  const weekList = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth() + 1;
  const week = now.getDay();
  const day = now.getDate();
  const hours = now.getHours();
  const minutes = now.getMinutes();
  const seconds = now.getSeconds();
  const formattedTime = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(
    seconds
  ).padStart(2, '0')}`;
  timeData.value = {
    day: `${year}年${month}月${day}日`,
    formattedTime: formattedTime,
    week: weekList[week]
  };
}
// 默认logo图片路径
function handleImageError() {
  data.logo = defaultLogoIcon;
}

// 使用 import.meta.url 动态生成路径
const weatherIcons = {
  多云: new URL('../../assets/homeViewImg/weather/多云.png', import.meta.url).href,
  小雨: new URL('../../assets/homeViewImg/weather/小雨.png', import.meta.url).href,
  中雨: new URL('../../assets/homeViewImg/weather/中雨.png', import.meta.url).href,
  大雨: new URL('../../assets/homeViewImg/weather/大雨.png', import.meta.url).href,
  小雪: new URL('../../assets/homeViewImg/weather/小雪.png', import.meta.url).href,
  中雪: new URL('../../assets/homeViewImg/weather/中雪.png', import.meta.url).href,
  大雪: new URL('../../assets/homeViewImg/weather/大雪.png', import.meta.url).href,
  雾: new URL('../../assets/homeViewImg/weather/雾.png', import.meta.url).href,
  雾霾: new URL('../../assets/homeViewImg/weather/雾霾.png', import.meta.url).href,
  浮尘: new URL('../../assets/homeViewImg/weather/浮尘.png', import.meta.url).href,
  沙尘暴: new URL('../../assets/homeViewImg/weather/沙尘暴.png', import.meta.url).href,
  晴: new URL('../../assets/homeViewImg/weather/晴.png', import.meta.url).href,
  阴: new URL('../../assets/homeViewImg/weather/阴.png', import.meta.url).href
};

// 计算属性获取天气图标
const weatherIconSrc = computed(() => {
  return weatherIcons[weatherData.value.weather] || '';
});

//预警
function showAlarm() {
  alarmDialog.title = '预警管理'
  alarmDialog.visible = true
}
// 关闭弹窗回调
function handleAlarmDialogClose() {
  alarmDialog.visible = false;
}
</script>

<template>
  <!-- 顶部导航栏 -->
  <div class="navbar">
    <!-- 左侧面包屑 -->
    <div class="flex left-menu">
      <div style="display: flex" v-for="(item, index) in showMenu" :key="index">
        <div @click="menuClick(item)" :class="menuActive == item.url ? 'main_top_menu_active' : 'main_top_menu'">
          <div class="h-full w-full flex items-center justify-center">{{ item.name }}</div>
        </div>
        <span v-if="index !== showMenu.length - 1" class="division"></span>
      </div>
    </div>
    <div class="menu-icon">
      <el-image
        style="width: 40px; height: 40px; margin-right: 10px; transform: translateY(8px)"
        :src="data.logo"
        :zoom-rate="1.2"
        :max-scale="7"
        :min-scale="0.2"
        fit="cover"
        @error="handleImageError"
      />
      <span class="menu_name">{{ data.name }}</span>
    </div>
    <!-- 右侧导航设置 -->
    <div class="flex items-center">
      <div style="margin-right: 15px">
        <span class="timer">{{ timeData.day || '' }}</span
        ><br />
        <span class="timer">{{ `${timeData.week} ${timeData.formattedTime}` }}</span>
      </div>
      <div style="margin-right: 15px">
        <span class="timer"> {{ weatherData.weather || '' }}</span
        ><br />
        <span class="timer"> {{ weatherData.temperature ? `${weatherData.temperature}°C` : '' }} </span>
      </div>
      <div style="margin-right: 15px">
        <img v-if="weatherIconSrc" :src="weatherIconSrc" width="40" height="34" alt="weather" />
      </div>
      <div style="margin-right: 15px">
        <span class="timer"
          >{{ weatherData.wind_direction ? `${weatherData.wind_direction}风` : ''
          }}{{ weatherData.wind_power ? `${weatherData.wind_power}级` : '' }}</span
        ><br />
        <span class="timer"
          >{{
            weatherData.min_temperature
              ? `${weatherData.min_temperature || ''}-${weatherData.max_temperature || ''}°C`
              : ''
          }}
        </span>
      </div>
      <el-badge :value="200" :max="99" style="margin-right: 16px; cursor: pointer;" @click="showAlarm">
        <el-icon size="30" color="#fff">
          <Message />
        </el-icon>
      </el-badge>
      <!-- <div class="backstage" @click="toBackstage()">
        <svg-icon icon-class="backstage" style="margin-left: 4" />
        后台管理
      </div> -->
      <AvatarArea :show-avatar="true" :show-dashboard="true" />
    </div>
  </div>

  <!-- Add dialog component -->
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="90%"
    :close-on-click-modal="false"
    :append-to-body="true"
    class="route-dialog"
    @close="handleDialogClose"
  >
    <component :is="currentComponent" :isDialogMode="true" />
  </el-dialog>

  <!-- 预警信息 -->
  <div v-if="alarmDialog.visible">
    <el-dialog
    v-model="alarmDialog.visible"
    :title="alarmDialog.title"
    width="90%"
    :close-on-click-modal="false"
    :append-to-body="true"
    class="route-dialog1"
    @close="handleAlarmDialogClose"
  >
    <AlarmManager />
  </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.division {
  width: 5px;
  height: 12px;
  border-left: 2px solid #acd4f7;
  transform: translateX(10px) translateY(10px);
  // transform: translateY(10px);
}
.navbar {
  height: 106px;
  // line-height: 106px;
  padding-top: 30px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-image: url('../../assets/homeViewImg/img.png');
  background-color: linear-gradient(180deg, rgba(0, 17, 41, 0.9) 38%, rgba(0, 17, 41, 0) 100%);
  z-index: 100;
  .backstage {
    color: #fff;
    cursor: pointer;
  }
  .timer {
    // display: block;
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
  }
  .menu-icon {
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
  }
  .main_top_menu {
    width: 100px;
    height: 33px;
    background-image: url('../../assets/homeViewImg/menu.png');
    margin-left: 20px;
    cursor: pointer;
    font-size: 16px;
    line-height: 33px;
    text-align: center;
    color: #d4d4d4;
    font-weight: bold;
    font-family: SourceHanSansSC-Regular;
  }
  .main_top_menu_active {
    width: 100px;
    height: 33px;
    line-height: 33px;
    background-image: url('../../assets/homeViewImg/menu-active.png');
    margin-left: 20px;
    cursor: pointer;
    font-size: 16px;
    text-align: center;
    color: #fff;
    font-weight: bold;
    font-family: SourceHanSansSC-Regular;
  }
  .flex {
    display: flex;
  }
  .menu_name {
    font-size: 32px;
    height: 106px;
    line-height: 106px;
    color: white;
    font-weight: bold;
  }
}
</style>

<style>
/* 全局样式，不使用scoped，确保能够正确应用到el-dialog */
.el-dialog.route-dialog .el-dialog__body {
  max-height: 90vh !important;
  overflow-y: auto !important;
  padding: 10px !important;
  height: 90vh;
}
.el-dialog.route-dialog1 .el-dialog__body {
  max-height: 90vh !important;
  overflow-y: auto !important;
  padding: 10px !important;
}

/* 滚动条样式 */
.el-dialog.route-dialog .el-dialog__body::-webkit-scrollbar {
  width: 6px !important;
  height: 6px !important;
}
.el-dialog.route-dialog1 .el-dialog__body::-webkit-scrollbar {
  width: 0 !important;
}

.el-dialog.route-dialog .el-dialog__body::-webkit-scrollbar-track {
  background: #11253e !important;
  border-radius: 3px !important;
}

.el-dialog.route-dialog .el-dialog__body::-webkit-scrollbar-thumb {
  background: #175094 !important;
  border-radius: 3px !important;
}

.el-dialog.route-dialog .el-dialog__body::-webkit-scrollbar-thumb:hover {
  background: #2468b1 !important;
}
</style>
