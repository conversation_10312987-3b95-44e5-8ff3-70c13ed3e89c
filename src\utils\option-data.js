import i18n from '@/lang';
import { TASK_TYPE, TARGET_TYPE } from '@/utils/constants';
const LangText = i18n.global.messages.value[i18n.global.locale.value];
const optionData = {
  // 失控动作
  outofControlActionList: [
    {
      label: '退出航线并返航',
      value: 0
    },
    {
      label: '继续执行航线任务',
      value: 1
    },
    {
      label: '降落',
      value: 2
    }
  ],
  statusOptions: [
    {
      label: '在线',
      value: true
    },
    {
      label: '离线',
      value: false
    },
  ],
  maintenanceStatus: [
    {
      label: '完成',
      value: 1
    },
    {
      label: '未完成',
      value: 0
    },
  ],
  lampList: [
    {
      label: '红蓝快闪',
      key: 'rlks',
      value: 2
    },
    {
      label: '红蓝慢闪',
      key: 'hlms',
      value: 1
    },
    {
      label: '红红爆闪',
      key: 'hhbs',
      value: 4
    },
    {
      label: '蓝蓝爆闪',
      key: 'llbs',
      value: 3
    },
  ],
  loadOptions: [
    {
      label: '喊话器',
      value: 1
    },
  ],
  weekOptions: [
    {
      label: '周一',
      value: '周一',
    },
    {
      label: '周二',
      value: '周二',
    },
    {
      label: '周三',
      value: '周三',
    },
    {
      label: '周四',
      value: '周四',
    },
    {
      label: '周五',
      value: '周五',
    },
    {
      label: '周六',
      value: '周六',
    },
    {
      label: '周日',
      value: '周日',
    },
  ],
  timeOptions: [
    {
      label: '天',
      value: 1
    },
    {
      label: '周',
      value: 2
    },
  ],
  typeOptions: [
    {
      label: '立即执行',
      value: TASK_TYPE.Immediate
    },
    {
      label: '定时执行',
      value: TASK_TYPE.Timed
    },
    {
      label: '定期执行',
      value: TASK_TYPE.Condition
    }
  ],
  jobOptions: [
    {
      label: '普通任务',
      value: 0
    },
    {
      label: '接处警任务',
      value: 1
    },
  ],
  targetOptions: [
    {
      label: '悬停',
      value: TARGET_TYPE.hover
    },
    {
      label: '环绕飞行',
      value: TARGET_TYPE.radio
    },
    {
      label: '全景拍摄',
      value: TARGET_TYPE.panorama
    }
  ],
  alarmOptions: [
    { label: '全部', value: 0 },
    { label: '近3天', value: 1 },
    { label: '近7天', value: 2 },
    { label: '近1个月', value: 3 },
  ],
  airStatusList: [
    { label: '开启', value: 0 },
    { label: '暂停', value: 1 },
    { label: '结束', value: 2 },
  ],
  statusList: [
    { label: '待执行', value: 1 },
    { label: '执行中', value: 2 },
    { label: '完成', value: 3 },
    { label: '取消', value: 4 },
    { label: '失败', value: 5 },
    { label: '暂停', value: 6 }
  ],
  jjStatusList: [
    { label: '未执行', value: 1 },
    { label: '执行中', value: 2 },
    { label: '执行成功', value: 3 },
    { label: '取消执行', value: 4 },
    { label: '执行失败', value: 5 }
  ],
  airlineTypeOption: [
    {
      label: '航点航线',
      value: '0',
      imgUrl: new URL('@/assets/plan/type-line.svg', import.meta.url).href,
      desc: '定义多个航点确定飞行路径'
    },
    {
      label: '面状航线',
      value: '1',
      imgUrl: new URL('@/assets/plan/type-area.svg', import.meta.url).href,
      desc: '在多边形区域内自动生成'
    },
    // {
    //   label: '倾斜摄影',
    //   value: '3',
    //   imgUrl: new URL('@/assets/plan/type-tilt.svg', import.meta.url).href,
    //   desc: '5向航线规划飞行路线，适合三维'
    // },
    // {
    //   label: '环形测绘',
    //   value: '4',
    //   imgUrl: new URL('@/assets/plan/type-circle.svg', import.meta.url).href,
    //   desc: '环绕一个点进行拍摄,适合高楼'
    // }
  ],
  clarityList: [
    //直播清晰度
    {
      value: 0,
      label: '自动'
    },
    {
      value: 1,
      label: '流畅'
    },
    {
      value: 2,
      label: '标准'
    },
    {
      value: 3,
      label: '高清'
    },
    {
      value: 4,
      label: '超清'
    }
  ],
  mediaTypeOption: [
    //媒体类型
    {
      value: 1,
      label: '照片'
    },
    {
      value: 2,
      label: '视频'
    },
    {
      value: 3,
      label: '全景图'
    }
  ],
  modelTypeOption: [
    //模型类型
    {
      value: 'QX',
      label: '倾斜摄影'
    },
    {
      value: 'DY',
      label: '点云'
    },
    {
      value: '2D',
      label: '二维地图'
    },
    {
      value: 'QJ',
      label: '全景照片'
    }
  ],
  switchVideoTypes: [
    //无人机摄像头镜头
    {
      value: 'zoom',
      label: '变焦'
    },
    {
      value: 'wide',
      label: '广角'
    }, {
      value: 'ir',
      label: '红外'
    }
  ],
  validityOption: [
    // 有效期下拉框
    {
      value: 1,
      label: '1天'
    },
    {
      value: 3,
      label: '3天'
    },
    {
     value: 7,
      label: '7天'
    },
    {
      value: 30,
      label: '30天'
    }
  ],
  hmsLevelOption: [
    //告警等级
    {
      value: 0,
      label: '通知'
    },
    {
      value: 1,
      label: '注意'
    },
    {
      value: 2,
      label: '警告'
    }
  ],
  //保单状态
  orderStatusOption: [
    {
      value: 0,
      label: '正常'
    },
    {
      value: 1,
      label: '即将逾期'
    },
    {
      value: 2,
      label: '已过期'
    }
  ],
  //电池健康状态
  batteryStatusOption: [
    {
      value: 0,
      label: '故障'
    },
    {
      value: 1,
      label: '健康'
    },
  ],
  //组织状态
  deptStatusOption: [
    {
      value: 0,
      label: '正常'
    },
    {
      value: 1,
      label: '停用'
    },
  ],
  isPilotOption: [
    {
      value: 1,
      label: '是'
    },
    {
      value: 0,
      label: '否'
    },
  ],
  //无人机驾驶证类型
  driveTypeOption: [
    {
      value: 'AOPA',
      label: 'AOPA',
      disabled: false
    },
    {
      value: 'UTC',
      label: 'UTC',
      disabled: false
    },
    {
      value: 'ASFC',
      label: 'ASFC',
      disabled: false
    },
    {
      value: 'CAAC',
      label: 'CAAC',
      disabled: false
    },
  ],
  // GB28181平台
  gbPlatform: [ 
    {
      value: '2',
      label: '海康isc'
    },
    {
      value: '3',
      label: '宇视unisee'
    },
    {
      value: '1',
      label: '视频云平台'
    },
  ],
  // 视频类型
  gbVideoType: [
    {
      value: '0',
      label: '关闭'
    },
    {
      value: '1',
      label: '打开'
    },
  ],
  // 推流方式
  gbUrlType: [
    {
      value: '0',
      label: 'AGORA'
    },
    {
      value: '1',
      label: 'RTMP'
    },
    {
      value: '2',
      label: 'RTSP'
    },
    {
      value: '3',
      label: 'GB28181'
    },
    {
      value: '4',
      label: 'WHIP'
    }
  ],
  // 流传输类型
  streamTransferType: [
    {
      value: 'udp',
      label: 'udp'
    },
    {
      value: 'tcp',
      label: 'tcp'
    }
  ]
};

export default optionData;
