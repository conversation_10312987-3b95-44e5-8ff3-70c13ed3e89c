<script setup>
import { useRoute } from 'vue-router';

import SidebarItem from './SidebarItem.vue';
// import Logo from './Logo.vue';
import Logo from './Logo.vue';
import { constantRoutes } from '@/router/index';

import { useSettingsStore } from '@/store/modules/settings';
import { useAppStore } from '@/store/modules/app';
import { storeToRefs } from 'pinia';
import variables from '@/styles/variables.module.scss';
import { useUserStore } from '@/store/modules/user';
import { computed, onMounted } from 'vue';
const userStore = useUserStore();
const settingsStore = useSettingsStore();
const appStore = useAppStore();

const { sidebarLogo } = storeToRefs(settingsStore);
const route = useRoute();
const newRoutes = ref([]);
let authority = localStorage.getItem('menu') && JSON?.parse(localStorage.getItem('menu'));

const activeMenu = computed(() => {
  const { meta, path } = route;

  if (meta.activeMenu) {
    return meta.activeMenu;
  }
  return path;
});

watch(
  () => authority,
  val => {
    console.log('val', val);
  }
);

const toggleSideBar = () => {
  appStore.toggleSidebar(true);
};

onMounted(()=>{
  handleMenu()
})

function handleMenu() {
  if (authority === null) {
    authority = localStorage.getItem('menu') && JSON?.parse(localStorage.getItem('menu'));
  }
  let arr = [];
  arr = constantRoutes?.filter(item => {
    // 普通权限判断
    const inMenu = authority?.indexOf(item.path?.replace('/', '')) > -1;
    // 超级管理员且路由菜单有admin权限
    const isForSuperAdmin = Array.isArray(item.authority) && item.authority.includes('admin') && userStore.userData.is_super_admin;
    return inMenu || isForSuperAdmin;
  });
  arr.forEach(res => {
    if (res.children && res.children.length) {
      res.children = res.children.filter(resp => {
        const inMenu = authority?.indexOf(resp.path?.replace('/', '')) > -1;
        const isForSuperAdmin = Array.isArray(resp.authority) && resp.authority.includes('admin') && userStore.userData.is_super_admin;
        return inMenu || isForSuperAdmin;
      });
    }
  });
  newRoutes.value = arr;
}
</script>

<template>
  <div :class="{ 'has-logo': sidebarLogo }">
    <Logo v-if="sidebarLogo" :collapse="appStore.sidebar.opened" />
    <div class="sidebar-content h-full">
      <el-scrollbar class="sidebar-scrollbar">
        <el-menu
          :default-active="activeMenu"
          :collapse="appStore.sidebar.opened"
          :background-color="variables.menuBg"
          :text-color="variables.menuText"
          :active-text-color="variables.menuActiveText"
          :unique-opened="true"
          :collapse-transition="false"
          mode="vertical"
        >
          <sidebar-item
            v-for="route in newRoutes"
            :item="route"
            :key="route.path"
            :base-path="route.path"
            :is-collapse="!appStore.sidebar.opened"
          />
        </el-menu>
      </el-scrollbar>
      <!-- <hamburger
        :is-active="appStore.sidebar.opened"
        @toggleClick="toggleSideBar"
      /> -->
    </div>
  </div>
</template>

<style scoped>
/* .sidebar-scrollbar {
  height: calc(100% - 120px) !important;
}

.sidebar-content {
  box-shadow: 0px 0 10px rgba(0, 0, 0, 0.2);
}

.hamburger-shadow {
  box-shadow: 0 -5px 10px rgba(0, 0, 0, 0.2);
} */
</style>
