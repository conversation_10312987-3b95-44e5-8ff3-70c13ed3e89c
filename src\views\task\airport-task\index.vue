<!--应用管理-->
<script>
export default {
  name: 'AirportTask'
};
</script>

<script setup>
import { onBeforeUnmount, reactive } from 'vue';
import DetailDialog from './DetailDialog.vue';
import EditDialog from './EditDialog.vue';
import optionData from '@/utils/option-data';
import { getAirTaskList, deleteAirTaskList, pauseTask } from '@/api/task';
import { COMMON_COLOR, TASK_TYPE, DOMAIN } from '@/utils/constants';
import { getErrorMessage } from '@/utils/errorCode';
import { getDevicesBound } from '@/api/devices';
import { ElMessage } from 'element-plus';
import moment from 'moment';
import { authorityShow } from '@/utils/authority';

const editDialogRef = ref(null);
const exeAirportOptions = ref([]);

const loading = ref(false);
const total = ref(0);

const queryParams = reactive({
  job_types: [0, 1],
  page_num: 1,
  page_size: 10
});

const dataList = ref([]);
const dialog = reactive({
  visible: false
});
const editDialog = reactive({
  visible: false
});

let formData = reactive({});
let detailData = reactive({});

function getDevices() {
  getDevicesBound({
    domain: DOMAIN.DOCK,
    page_num: 1,
    page_size: 50
  }).then(res => {
    const { list = [] } = res;
    exeAirportOptions.value = list;
  });
}

/**
 * 查询
 */
function handleQuery(params) {
  loading.value = true;
  getAirTaskList({
    ...queryParams,
    ...params,
  }).then(data => {
    const { records } = data;
    dataList.value = records || [];
    loading.value = false;
    queryParams.page_num = data.current;
    total.value = data.total;
  });
}

function handleSearchJobType (value) {
  const params = {
    ...queryParams,
    job_types: value!==null && value !== '' ? [value] : [0,1],
    begin_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[0]} 00:00:00` : '',
    end_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[1]} 23:59:59` : '',
    page_num: 1
  };
  delete params.rangTime;
  handleQuery(params);
}

function handleSearch() {
  console.log('queryParams',queryParams)
  const params = {
    ...queryParams,
    job_types: queryParams.hasOwnProperty('job_type') && queryParams.job_type !== null && queryParams.job_type !=='' ? [queryParams.job_type] : [0,1],
    begin_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[0]} 00:00:00` : '',
    end_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[1]} 23:59:59` : '',
    page_num: 1
  };
  delete params.rangTime;
  handleQuery(params);
}
function handleSearchNowPage() {
  const params = {
    ...queryParams,
    begin_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[0]} 00:00:00` : '',
    end_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[1]} 23:59:59` : '',
    page_num: queryParams.page_num,
    job_types: [0, 1]
  };
  delete params.rangTime;
  handleQuery(params);
}

/**
 * 重置查询
 */
function resetQuery() {
  queryParams.name = '';
  queryParams.wayline_file_name = '';
  queryParams.dock_sn = '';
  queryParams.begin_time = '';
  queryParams.end_time = '';
  queryParams.status = '';
  queryParams.task_type = '';
  queryParams.job_type = '';
  queryParams.job_types = [0, 1];
  queryParams.rangTime = '';
  queryParams.page_num = 1;
  queryParams.page_size = 10;
  handleQuery({ ...queryParams });
}

function stopTask(row, type) {
  let title = '';
  let tag=''
  if (type == 'PAUSE') {
    title = '暂停后未超过执行时间可以恢复，超过则无法恢复，是否确定暂停任务？';
    tag = '确认暂停？';
  } else if (type == 'RESUME') {
    title = '是否确认恢复任务？';
    tag = '确认恢复？';
  } else {
    title = '终止后将无法继续执行后续任务，是否确认终止？';
    tag = '确认终止？';
  }
  ElMessageBox.confirm(`${title}`,`${tag}`, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    autofocus: false,
    closeOnClickModal: false,
    type: 'warning'
  }).then(() => {
    pauseTask(
      {
        task_id: toRaw(row).flight_task_id
      },
      {
        status: type
      }
    ).then(data => {
      ElMessage.success('操作成功');
      if (type === 'TERMINATE') {
        handleSearchNowPage();
      } else {
        handleSearch();
      }
    });
  });
}

/**
 *
 * @param dicTypeId 应用ID
 */
function openDialog(row) {
  dialog.visible = true;
  Object.keys(detailData).map(key => {
    delete detailData[key];
  });
  if (row) {
    Object.assign(detailData, { ...row });
  }
}

function openEditDialog(row) {
  editDialog.visible = true;
  Object.keys(formData).map(key => {
    delete formData[key];
  });
  if (row) {
    editDialog.title = '编辑任务';
    Object.assign(formData, { ...row });
  } else {
    editDialog.title = '新增任务';
    nextTick(() => {
      editDialogRef.value.setDefaultValue();
    });
  }
}

/**
 * 删除
 */
function handleDelete(row) {
  ElMessageBox.confirm(`确认后将删除此任务及相关的历史执行记录，且无法进行恢复`, '确认删除所选任务？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    autofocus: false,
    closeOnClickModal: false,
    type: 'warning'
  }).then(() => {
    deleteAirTaskList({
      task_id: toRaw(row).flight_task_id
    }).then(data => {
      ElMessage.success('删除成功');
      handleQuery();
    });
  });
}

onMounted(() => {
  window.addEventListener('setItem', () => {
    handleQuery();
  });
  getDevices();
  handleQuery({ ...queryParams });
});

const handleSizeChange = val => {
  // Calculate the new total pages based on the new page size
  const newTotalPages = Math.ceil(total.value / val);
  
  // If current page is greater than new total pages, adjust to the last page
  if (queryParams.page_num > newTotalPages) {
    queryParams.page_num = newTotalPages || 1;
  }
  
  queryParams.page_size = val;
  const params = {
    ...queryParams,
    job_types: queryParams.hasOwnProperty('job_type') && queryParams.job_type !== null && queryParams.job_type !== '' ? [queryParams.job_type] : [0, 1],
    begin_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[0]} 00:00:00` : '',
    end_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[1]} 23:59:59` : '',
    page_size: val,
    page_num: queryParams.page_num // Use the potentially adjusted page number
  };
  handleQuery({ ...params });
};

const handleCurrentChange = val => {
  queryParams.page_num = val;
  const params = {
    ...queryParams,
    job_types:  queryParams.hasOwnProperty('job_type') && queryParams.job_type != null && queryParams.job_type !=='' ? [queryParams.job_type] : [0,1],
    begin_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[0]} 00:00:00` : '',
    end_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[1]} 23:59:59` : '',
    page_num: val
  };
  handleQuery({ ...params });
};

onBeforeUnmount(() => {
  window.removeEventListener('setItem', () => {});
});
</script>

<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
          <el-form-item label="" prop="name">
            <el-input
              class="input-serach"
              v-model="queryParams.name"
              placeholder="请输入任务名称"
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
            />
          </el-form-item>
          <el-form-item label="" prop="wayline_file_name">
            <el-input
              class="input-serach"
              v-model="queryParams.wayline_file_name"
              placeholder="请输入航线名称"
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
            />
          </el-form-item>
          <el-form-item label="" prop="keyWord">
            <el-date-picker
              class="input-serach"
              v-model="queryParams.rangTime"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @change="handleSearch"
            />
          </el-form-item>
          <el-form-item label="" prop="dock_sn">
            <el-select
              class="input-serach"
              :fit-input-width="true"
              v-model="queryParams.dock_sn"
              placeholder="请选择执行机场"
              clearable
              @change="handleSearch"
            >
              <el-option
                v-for="item in exeAirportOptions"
                :key="item.device_sn"
                :label="item.nickname"
                :value="item.device_sn"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="status">
            <el-select
              class="input-serach"
              v-model="queryParams.status"
              placeholder="请选择任务状态"
              clearable
              @change="handleSearch"
            >
              <el-option
                v-for="(item, index) in optionData.airStatusList"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="task_type">
            <el-select
              class="input-serach"
              v-model="queryParams.task_type"
              placeholder="请选择任务执行方式"
              clearable
              @change="handleSearch"
            >
              <el-option
                v-for="(item, index) in optionData.typeOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="job_type">
            <el-select
              class="input-serach"
              v-model="queryParams.job_type"
              placeholder="请选择任务类型"
              clearable
              @change='(value)=>{handleSearchJobType(value)}'
            >
              <el-option
                v-for="(item, index) in optionData.jobOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="search-btn" style="transform: translateY(15px)">
        <el-button type="primary" @click="handleSearch()"><i-ep-search />搜索</el-button>
        <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
      </div>
    </div>

    <el-card shadow="never">
      <template #header>
        <el-button type="primary" @click="openEditDialog()" v-if="authorityShow('createAirportTask')"><i-ep-plus />新增任务</el-button>
      </template>
      <el-table highlight-current-row :data="dataList" v-loading="loading" stripe height="540">
        <el-table-column label="序号" align="center" width="80">
          <template #default="scope">
            <span>{{ scope.$index + 1 + queryParams.page_size * (queryParams.page_num - 1) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="任务ID" prop="flight_task_id" show-overflow-tooltip />
        <el-table-column label="任务名称" prop="name" show-overflow-tooltip />
        <el-table-column label="航线名称" prop="wayline_file_name" show-overflow-tooltip />
        <el-table-column label="计划执行时间" prop="execute_time" width="200" show-overflow-tooltip />
        <el-table-column label="任务执行方式" show-overflow-tooltip prop="task_type_desc">
        </el-table-column>
        <el-table-column label="任务类型" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ optionData.jobOptions.find(item => item.value === scope.row.job_type)?.label }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建人" prop="create_user_name" show-overflow-tooltip></el-table-column>
        <el-table-column label="执行机场" prop="dock_sn_desc" show-overflow-tooltip> </el-table-column>
        <el-table-column label="任务状态" prop="status_desc" show-overflow-tooltip></el-table-column>
        <el-table-column fixed="right" label="操作" align="center" width="200" v-if="authorityShow('controlAirportTask') || authorityShow('checkAirportTask') || authorityShow('deleteAirportTask')">
          <template #default="scope">
            <el-button type="primary" link @click.stop="openDialog(scope.row)" v-if="authorityShow('checkAirportTask')">详情</el-button>
            <el-button
              type="danger"
              v-if="scope.row.task_type != 0 && scope.row.status == 0 && scope.row.task_type !== 1 && authorityShow('controlAirportTask')" 
              link
              @click.stop="stopTask(scope.row, 'PAUSE')"
              >暂停</el-button
            >
            <el-button type="danger" v-if="scope.row.status == 1 && scope.row.task_type !== 1 && authorityShow('controlAirportTask')" link @click.stop="stopTask(scope.row, 'RESUME')"
              >恢复</el-button
            >
            <el-button
              type="danger"
              v-if="scope.row.task_type != 0 && scope.row.status == 0 && moment(scope.row.execute_time) >= moment() && scope.row.task_type == 1 && authorityShow('controlAirportTask')"
              link
              @click.stop="stopTask(scope.row, 'PAUSE')"
              >暂停</el-button
            >
            <el-button type="danger" v-if="scope.row.status == 1 && moment(scope.row.execute_time) >= moment() && scope.row.task_type == 1 && authorityShow('controlAirportTask')" link @click.stop="stopTask(scope.row, 'RESUME')"
              >恢复</el-button
            >
            <el-button
              type="danger"
              v-if="scope.row.status == 0 || scope.row.status == 1 && authorityShow('controlAirportTask')"
              link
              @click.stop="stopTask(scope.row, 'TERMINATE')"
              >终止</el-button
            >
            <el-button type="danger" link @click.stop="handleDelete(scope.row)" v-if="authorityShow('deleteAirportTask')">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-content">
        <el-pagination
          v-if="total > 0"
          v-model:current-page="queryParams.page_num"
          v-model:page-size="queryParams.page_size"
          :background="true"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <EditDialog
      v-if="editDialog.visible"
      ref="editDialogRef"
      v-model:visible="editDialog.visible"
      :title="editDialog.title"
      :form-data="formData"
      @submit="resetQuery"
    />

    <DetailDialog v-model:visible="dialog.visible" :form-data="detailData" />
  </div>
</template>
<style scoped lang="scss">
.pagination-content {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;

  &.hidden {
    display: none;
  }
}
.input-serach {
  width: 200px;
}
.flex-center {
  display: flex;
  align-items: center;
  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
}
.search {
  display: flex;
  align-items: center;
  padding: 24px;
  .search-form {
    flex: 1;
  }
}
</style>
