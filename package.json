{"name": "ff-vue3-vite-elp-admin", "version": "1.0.0", "repository": "ssh://git@**************:922/ff-fe/ff-vue3-elp-admin-interface.git", "license": "MIT", "author": "四信前端团队", "scripts": {"build": "vite build --mode production", "build:pilot": "vite build --mode pilot", "build-staging": "vite build --mode staging", "build:en": "vite build --mode productionByEn", "dev": "vite serve --mode development", "lint": "npx lint-staged && npx sort-package-json", "prettier": "prettier --write .", "preview": "yarn build && vite preview", "test": "jest"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@element-plus/icons-vue": "^2.3.1", "@googlemaps/markerclusterer": "^2.1.2", "@photo-sphere-viewer/core": "^5.8.1", "@turf/turf": "^7.0.0", "@vueuse/components": "^10.5.0", "@vueuse/core": "^9.1.1", "@wangeditor/editor": "^5.0.0", "@wangeditor/editor-for-vue": "^5.1.10", "agora-rtc-sdk-ng": "^4.23.4", "axios": "^1.3.4", "cesium": "1.102.0", "cesium-navigation-es6": "^3.0.9", "color-gradient-picker-vue3": "^1.0.3", "copy-to-clipboard": "^3.3.3", "crypto-js": "^4.1.1", "echarts": "^5.2.2", "egm96-universal": "^1.1.0", "element-plus": "^2.4.2", "eventemitter3": "^5.0.0", "html2pdf.js": "^0.10.1", "jszip": "^3.10.1", "lodash": "^4.17.21", "moment": "^2.29.4", "mqtt": "^5.5.0", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^6.2.0", "pinia": "^2.0.33", "qrcode": "^1.5.4", "qs": "^6.13.0", "reconnecting-websocket": "^4.4.0", "sm-crypto": "^0.3.12", "sockjs-client": "^1.5.0", "stomp": "^v0.1.1", "stompjs": "^2.3.3", "store": "^2.0.12", "toolcool-color-picker": "^1.0.15", "vconsole": "^3.15.1", "vite-plugin-cesium": "^1.2.22", "vue": "^3.2.45", "vue-cropper": "^1.1.1", "vue-grid-layout": "^3.0.0-beta1", "vue-i18n": "^9.7.1", "vue-lazyload": "^3.0.0", "vue-router": "^4.2.5", "vue3-grid-layout": "^1.0.0", "vue3-grid-layout-next": "^1.0.6"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.23.6", "@babel/preset-env": "^7.23.6", "@commitlint/cli": "^16.2.3", "@commitlint/config-conventional": "^16.2.1", "@iconify-json/ep": "^1.1.8", "@types/nprogress": "^0.2.0", "@types/path-browserify": "^1.0.0", "@vitejs/plugin-vue": "^4.0.0", "@vue/cli-plugin-unit-jest": "^5.0.8", "@vue/test-utils": "^2.4.3", "@vue/vue3-jest": "^27.0.0-alpha.3", "autoprefixer": "^10.4.13", "babel-jest": "^27.0.0", "babel-plugin-transform-vite-meta-env": "^1.0.3", "cesium": "1.102.0", "cross-env": "^7.0.3", "eslint": "^8.34.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^9.9.0", "fast-glob": "^3.2.11", "husky": "^7.0.4", "jest": "^27.0.0", "lint-staged": "^15.2.0", "postcss": "^8.4.20", "prettier": "^2.6.2", "sass": "^1.62.1", "unocss": "^0.50.1", "unplugin-auto-import": "^0.13.0", "unplugin-icons": "^0.15.1", "unplugin-vue-components": "^0.23.0", "vite": "^4.3.8", "vite-plugin-cesium": "^1.2.22", "vite-plugin-svg-icons": "^2.0.1", "vue-eslint-parser": "^9.1.0"}}