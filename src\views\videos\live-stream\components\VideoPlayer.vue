<template>
  <div class="cell">
    <div class="cell-player">
      <div :class="cellClass(i)" v-for="(i, index) in cellCount" :key="index"  :ref="(el) => setItemRef(el, index)">
         <template v-if="shouldRenderCell(i)">
            <ComPlayer 
              :index="index"
              :i="i"
              :device="deviceList?.[index]"
              :style-props="ysStyle[index]"
              :playerId="props.playerId !='' ? props.playerId : 'livePlayer' + i"
              :show-blow-up="showBlowUp"
              :show-title="showTitle"
              :cellCount="cellCount"
              :showClose="showClose"
              ref="ComonPlayers"
            />
          </template>
          <template v-if="shouldRenderCells(i)">
            <div class="cell-player-6-2-cell">
              <ComPlayer 
                :index="index"
                :i="i"
                :device="deviceList?.[index]"
                :style-props="ysStyle[index]"
                :playerId="props.playerId !='' ? props.playerId : 'livePlayer' + i"
                :show-blow-up="showBlowUp"
                :show-title="showTitle"
                :cellCount="cellCount"
                :showClose="showClose"
                ref="ComonPlayers"
              />
              <ComPlayer 
                :index="index"
                :i="i"
                :device="deviceList?.[index +1]"
                :style-props="ysStyle[index]"
                :playerId="props.playerId !='' ? props.playerId :'livePlayer' + i*10"
                :show-blow-up="showBlowUp"
                :show-title="showTitle"
                :cellCount="cellCount"
                :showClose="showClose"
                ref="ComonPlayers"
              />
            </div>
          </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import { nextTick, onMounted, ref, toRaw, watch } from 'vue';
import ComPlayer from './ComPlayer.vue'
const deviceList = ref(null);
const props = defineProps({
  data: {
    type: Array,
    default() {
      return [];
    }
  },
  showClose: {
    type: Boolean,
    default: true
  },
  showTitle: {
    type: Boolean,
    default: true
  },
  showBlowUp: {
    type: Boolean,
    default: true
  },
  playerId: {
    type: String,
		default: ''
  },
  videoData: { //直接传播放路径，则不用去请求
    type: Array,
    default() {
      return [];
    }
  }
});
const cellCount = ref(4);
const itemRefs = ref([]);
const itemVRefs = ref([]);
const ComonPlayers = ref(null)
const ysStyle = ref([//宇视默认宽高 9宫格
  {width:0,height:0},
  {width:0,height:0},
  {width:0,height:0},
  {width:0,height:0},
  {width:0,height:0},
  {width:0,height:0},
  {width:0,height:0},
  {width:0,height:0},
  {width:0,height:0},
])
import { getYSConfig } from '@/api/live'
watch(
  () => props.videoData,
  newVal => {
    if(newVal.length >0) {
      ComonPlayers.value.forEach((element, index) => {
        if (index + 1 > newVal.length) {
          // 停止
          element.onVideoStop && element.onVideoStop();
        } else {
          // 初始化
          const device = newVal[index];
          if (device) {
            element.onH5VideoPlay(device)
          }else{
            element.onVideoStop && element.onVideoStop();
          }
        }
      });
    }
  }
);

watch(
  () => props.data,
  newVal => {
    const arr = toRaw(newVal)?.filter((item,index,self)=>{
      return self.findIndex(t => JSON.stringify(t) === JSON.stringify(item)) === index;
    })
    deviceList.value = arr || [];
    console.log("deviceList.value =",deviceList.value )
    nextTick(()=>{
      refreshPlayer();
    })
  },
  { deep: true, immediate: true }
);

// 监听 cellCount 变化，重新计算
watch(cellCount, (newVal) => {
  itemVRefs.value = []
  nextTick(() => {
    measureItems();
  });
});

// 动态绑定 ref
const setItemRef = (el,index) => {
  if (el) {
    itemVRefs.value[index] = el;
  }
};

const shouldRenderCell = (i) => {
  return cellCount.value !== 6 || (cellCount.value === 6 && i !== 2 && i !== 3);
};

const shouldRenderCell11 = (i) => {
  return (cellCount.value === 6 && i !== 2 && i !== 3);
};
const shouldRenderCells = (i) => {
  return cellCount.value === 6 && i == 2;
};

function refreshPlayer() {
  ComonPlayers.value?.forEach((element, index) => {
    if (index + 1 > deviceList.value.length) {
      // 停止
      element.onVideoStop && element.onVideoStop();
    } else {
      // 初始化
      const device = deviceList.value[index];
      if (device.droneSelected) {
        element.onVideoStart(device)
      }else{
        element.onVideoStop && element.onVideoStop();
        if(ComonPlayers.value.length == 1 && deviceList.value.length >1) {
          let arr = deviceList.value
          arr.shift();
          deviceList.value = arr
          refreshPlayer()
        }
      }
    }
  });
}

function cellClass(index) {
  switch (cellCount.value) {
    case 1:
      return ['cell-player-1'];
    case 4:
      return ['cell-player-4'];
    case 6:
      if (index == 1) return ['cell-player-6-1'];
      if (index == 2) return ['cell-player-6-2'];
      if (index == 3) return ['cell-player-6-none'];
      return ['cell-player-6'];
    case 9:
      return ['cell-player-9'];
    case 16:
      return ['cell-player-16'];
    default:
      break;
  }
}

function getVideoBoxStyle() {

}
const emit = defineEmits(['onClose']);

onMounted(() => {
  window.$bus.on('changeVideoCount', count => {
    cellCount.value = count;
    ComonPlayers.value = null
    setTimeout(() => {
      refreshPlayer()
    }, 500);
  });

  nextTick(measureItems); // 确保 DOM 渲染完成
});
// 计算每个元素的宽高
const measureItems = () => {
  ysStyle.value.forEach(item=>{
    item.width = 0
    item.height = 0
  })
  nextTick(() => { 
    itemVRefs.value.forEach((el, index) => {
    if (el) {
      const width = el.offsetWidth;
      const height = el.offsetHeight;
      ysStyle.value[index].width = width
      ysStyle.value[index].height = height
      console.log(`Item ${index}: width=${width}px, height=${height}px`);
    }
  });
});
  
};
</script>

<style  lang="scss" scoped>
.select-pop {
  .select-view {
    display: flex;
    flex-direction: column;
    .select-item {
      cursor: pointer;
      color: white;
      font-size: 14px;
      padding: 8px;
      border-bottom: 1px solid #000000;
      text-align: center;
    }
    .select-item-active {
      color: var(--el-color-primary);
    }
  }
}
.cell {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
  .cell-tool {
    line-height: 30px;
    padding: 10px;
    text-align: center;
    .grid-view {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      .tool-grid-text {
        color: white;
        font-size: 14px;
        cursor: pointer;
        margin: 0 8px;
      }
    }
  }
  .cell-player {
    flex: 1;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    height: 100% !important;
    justify-content: space-between;
  }
  .cell-player-4 {
    width: 50%;
    height: 50% !important;
    box-sizing: border-box;
  }
  .cell-player-1 {
    width: 100% !important;
    height: 100% !important;
    box-sizing: border-box;
  }
  .cell-player-6-1 {
    width: 66.66%;
    height: 66.66% !important;
    box-sizing: border-box;
  }
  .cell-player-6-2 {
    width: 33.33%;
    height: 66.66% !important;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }
  .cell-player-6-none {
    display: none;
  }
  .cell-player-6-2-cell {
    width: 100%;
    height: 50% !important;
    box-sizing: border-box;
  }
  .cell-player-6 {
    width: 33.33%;
    height: 33.33% !important;
    box-sizing: border-box;
  }
  .cell-player-9 {
    width: 33.33%;
    height: 33.33% !important;
    box-sizing: border-box;
  }
  .cell-player-16 {
    width: 25%;
    height: 25% !important;
    box-sizing: border-box;
  }
}
</style>

