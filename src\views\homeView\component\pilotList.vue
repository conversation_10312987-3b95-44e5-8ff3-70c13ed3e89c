<script>
export default { name: 'PilotList' };
</script>

<script setup>
import { ref, reactive, onMounted, onUnmounted, toRaw, watch } from 'vue';
import { listOnlineUser } from '@/api/system/user';
import { Phone, User, OfficeBuilding, List, Postcard, Microphone, Refresh } from '@element-plus/icons-vue';
import { useUserStoreHook } from '@/store/modules/user'; 
import { ElMessageBox, ElMessage } from 'element-plus';

const userStore = useUserStoreHook();
// 来自父组件的属性
const props = defineProps({
  isConnected: {
    type: Boolean,
    default: false
  },
  websocketMessage: {
    type: Object,
    default: () => ({})
  }
});

// 在线状态文字映射
const STATUS_TEXT_MAP = {
  0: '离线',
  1: 'PC端在线',
  2: '手柄端在线',
  3: '双端在线'
};

const queryParams = reactive({
  pilot: true,
  pageNo: 1,
  pageSize: 10
});
const dataList = ref([]);
const total = ref(0);
const emit = defineEmits(['onClick', 'select', 'reconnect', 'callPilot', 'call-action']);
const loading = ref(false);

// 用户在线状态映射
const userStatusMap = ref({});
// 轮询定时器
let pollingTimer = null;

// 语音通话相关状态 - 保留状态但不再显示组件
const currentCallChannel = ref('');
const currentCallPilot = ref(null);

/**
 * 查询
 */
function handleQuery() {
  loading.value = true;
  listOnlineUser({
    ...queryParams
  }).then(data => {
    const { list } = data;
    dataList.value = list || [];
    total.value = data.total;

    // 获取用户状态
    if (list) {
      // 处理用户状态信息
      const users = list.map(user => ({
        user_id: user.user_id,
        onlineStatus: user.online_status || 0 // 确保有onlineStatus字段: 0-离线, 1-PC端在线, 2-手柄端在线, 3-双端在线
      }));

      updateStatusMap(users);
    }

    setTimeout(() => {
      loading.value = false;
    }, 500);
  });
}

function load() {
  const totalPage = Math.ceil(total.value / 10);
  if (queryParams.pageNo >= totalPage || dataList.value.length < 10) {
    return;
  }
  queryParams.pageNo += 1;
  listOnlineUser({
    ...queryParams
  }).then(data => {
    const { list } = data;
    let arr = toRaw(dataList.value).concat(list);
    dataList.value = arr;

    // 获取新加载用户的状态
    if (list) {
      // 处理用户状态信息
      const users = list.map(user => ({
        user_id: user.user_id,
        onlineStatus: user.online_status || 0 // 确保有onlineStatus字段: 0-离线, 1-PC端在线, 2-手柄端在线, 3-双端在线
      }));

      updateStatusMap(users);
    }
  });
}

// 处理WebSocket消息
function handleWebSocketMessage() {
  if (!props.websocketMessage || !props.websocketMessage.topic) return;

  try {
    const { topic, data } = props.websocketMessage;
    console.log('飞手列表收到WebSocket消息:', topic, data);

    // 处理不同的主题
    if (topic === '/topic/user/status') {
      // 处理用户状态更新
      if (data && data.user_id) {
        console.log('更新用户状态:', data.user_id, data);

        // 计算当前状态
        const currentStatus =
          data.onlineStatus ||
          (data.deviceType ? (data.deviceType === 'pc' ? 1 : data.deviceType === 'mobile' ? 2 : 0) : 0);

        // 获取之前的状态
        const previousStatus = userStatusMap.value[data.user_id]?.onlineStatus || 0;

        // 更新用户状态映射
        userStatusMap.value = {
          ...userStatusMap.value,
          [data.user_id]: {
            ...data,
            onlineStatus: currentStatus,
            lastUpdate: Date.now()
          }
        };

        // 判断是否需要刷新列表
        if (
          (previousStatus > 0 && currentStatus === 0) ||
          (previousStatus === 0 && currentStatus > 0) ||
          !userStatusMap.value[data.user_id]
        ) {
          console.log('用户状态发生重要变化，刷新列表');
          handleQuery();
        } else {
          // 仅更新本地状态，不刷新整个列表
          console.log('仅更新本地状态显示');
          // 通过这种方式触发视图更新
          userStatusMap.value = { ...userStatusMap.value };
        }
      }
    }
    // 处理针对特定用户的通话相关消息
    else if (topic.includes('/topic/') && topic.includes('/call/')) {
      // 提取主题类型
      let messageType = '';
      if (topic.includes('/call/incoming')) messageType = 'incoming';
      else if (topic.includes('/call/answer')) messageType = 'answer';
      else if (topic.includes('/call/reject')) messageType = 'reject';
      else if (topic.includes('/call/end')) messageType = 'end';
      else if (topic.includes('/call/leave')) messageType = 'leave';

      switch (messageType) {
        case 'incoming':
          // 处理个人通话邀请
          if (data && (data.session_id || data.channel_name)) {
            console.log('收到个人通话邀请:', data);
            handleIncomingCall(data);
          }
          break;
        case 'answer':
          // 处理个人通话应答
          if (data && data.session_id) {
            console.log('收到个人通话应答:', data);
            emit('call-action', {
              action: 'accept',
              user_id: data.userId,
              session_id: data.session_id,
              user_name: data.user_name
            });
          }
          break;
        case 'reject':
          // 处理个人通话拒绝
          if (data && data.session_id) {
            console.log('收到个人通话拒绝:', data);
            emit('call-action', {
              action: 'reject',
              user_id: data.user_id,
              session_id: data.session_id,
              user_name: data.user_name
            });
          }
          break;
        case 'end':
          // 处理个人通话结束
          if (data && data.session_id) {
            console.log('收到个人通话结束:', data);
            emit('call-action', {
              action: 'end',
              session_id: data.session_id,
              user_name: data.user_name
            });
          }
          break;
        case 'leave':
          // 处理参与者离开
          if (data && data.session_id && data.user_id) {
            console.log('收到参与者离开:', data);
            emit('call-action', {
              action: 'leave',
              user_id: data.user_id,
              session_id: data.session_id,
              user_name: data.user_name
            });
          }
          break;
      }
    }
  } catch (error) {
    console.error('处理WebSocket消息出错:', error);
  }
}

// 启动轮询
function startPolling() {
  if (pollingTimer) return;

  console.log('启动状态轮询');
  // 立即执行一次
  fetchOnlinePilots();

  // 每30秒轮询一次
  pollingTimer = setInterval(() => {
    fetchOnlinePilots();
  }, 30000);
}

// 停止轮询
function stopPolling() {
  if (pollingTimer) {
    console.log('停止状态轮询');
    clearInterval(pollingTimer);
    pollingTimer = null;
  }
}

// 获取在线飞手列表
async function fetchOnlinePilots() {
  try {
    // 使用listOnlineUser API获取飞手列表
    const response = await listOnlineUser({
      pilot: true,
      pageNo: 1,
      pageSize: 50 // 获取更多数据以确保覆盖所有在线飞手
    });

    if (response && response.list) {
      // 处理用户状态信息
      const users = response.list.map(user => ({
        user_id: user.user_id,
        onlineStatus: user.online_status || 0 // 确保有onlineStatus字段: 0-离线, 1-PC端在线, 2-手柄端在线, 3-双端在线
      }));

      updateStatusMap(users);
    }
  } catch (error) {
    console.error('获取在线飞手列表失败:', error);
  }
}

// 更新状态映射
function updateStatusMap(users) {
  if (!users || !Array.isArray(users)) return;

  const newStatusMap = { ...userStatusMap.value };
  users.forEach(user => {
    if (user.user_id) {
      newStatusMap[user.user_id] = user;
    }
  });
  userStatusMap.value = newStatusMap;
}

// 获取用户状态
function getUserStatus(user_id) {
  return userStatusMap.value[user_id] || { onlineStatus: 0 };
}

// 获取状态文本
function getStatusText(user_id) {
  const status = getUserStatus(user_id);
  return STATUS_TEXT_MAP[status.onlineStatus] || STATUS_TEXT_MAP[0];
}

// 获取状态样式类
function getStatusClass(user_id) {
  const status = getUserStatus(user_id);

  // 根据不同的在线状态返回不同的样式类
  if (status.onlineStatus === 0) {
    return 'status-offline';
  } else {
    return 'status-controller';
  }
}

// 监听连接状态变化并处理WebSocket消息
function watchConnectionStatus() {
  console.log('初始属性值:', {
    isConnected: props.isConnected,
    websocketMessage: props.websocketMessage
  });

  const unwatch = watch(
    () => props.isConnected,
    newValue => {
      console.log('连接状态变化:', newValue);
      if (newValue) {
        console.log('WebSocket已连接，停止轮询...');
        stopPolling();
      } else {
        console.log('WebSocket已断开，启动轮询...');
        startPolling();
      }
    },
    { immediate: true }
  );

  // 监听WebSocket消息
  const unwatchMessage = watch(
    () => props.websocketMessage,
    newValue => {
      console.log('WebSocket消息变化:', newValue);
      handleWebSocketMessage();
    },
    { deep: true }
  );

  return [unwatch, unwatchMessage];
}

let unwatchFunctions = [];

onMounted(() => {
  console.log('飞手列表组件已挂载');
  console.log('属性:', {
    isConnected: props.isConnected,
    websocketMessage: props.websocketMessage
  });

  handleQuery();

  // 监听连接状态变化和WebSocket消息
  unwatchFunctions = watchConnectionStatus();

  // 添加一分钟备用轮询，以防WebSocket连接失败
  setTimeout(() => {
    if (!props.isConnected) {
      console.log('WebSocket未连接，启动轮询模式');
      startPolling();
    }
  }, 60000);
});

// 组件卸载时移除监听
onUnmounted(() => {
  if (unwatchFunctions.length > 0) {
    unwatchFunctions.forEach(unwatch => {
      if (typeof unwatch === 'function') {
        unwatch();
      }
    });
  }
  stopPolling();
});

// 处理呼叫飞手
function handleCall(pilot) {
  console.log('呼叫飞手:', pilot);

  // 使用callService发起通话
  emit('callPilot', { pilot });
}

// 处理收到的呼叫
function handleIncomingCall(callData) {
  const { session_id, caller_name, channel_name } = callData;

  // 通知父组件有关来电
  emit('call-action', {
    action: 'incoming',
    session_id,
    caller_name,
    channel_name
  });
}

// 检查是否为当前登录用户
function isCurrentUser(userId) {
  return userId === userStore.userData.user_id;
}
</script>

<template>
  <!-- 查询按钮 -->
  <div class="query-bar">
    <el-tooltip content="刷新列表" placement="top" :disabled="loading">
      <el-button type="primary" @click="handleQuery" class="query-button">
        <el-icon><Refresh /></el-icon>
      </el-button>
    </el-tooltip>
  </div>
  <div class="alarm-ul" v-infinite-scroll="load" v-if="dataList.length > 0" v-loading="loading">
    <div
      class="alarm-item"
      v-for="(item, index) in dataList"
      :key="index"
      :class="{ 'current-user': isCurrentUser(item.user_id) }"
    >
      <div class="drone-name">
        <div class="pilot-info">
          <div class="load-name">
            <el-avatar :size="36" :class="['user-avatar', isCurrentUser(item.user_id) ? 'current-user-avatar' : '']">
              {{ item.username ? item.username.charAt(0).toUpperCase() : 'U' }}
            </el-avatar>
            <div class="user-info">
              <span class="username">
                {{ item.username }}
                <el-tag v-if="isCurrentUser(item.user_id)" size="small" type="success" class="current-tag">本人</el-tag>
              </span>
              <div class="status-info">
                <el-icon :class="['status-icon', getStatusClass(item.user_id)]">
                  <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" data-v-78e17ca8="">
                    <path
                      fill="currentColor"
                      d="M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768zm0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896z"
                    ></path>
                    <path
                      fill="currentColor"
                      d="M480 256a32 32 0 0 1 32 32v256a32 32 0 0 1-64 0V288a32 32 0 0 1 32-32z"
                    ></path>
                    <path fill="currentColor" d="M480 512h256a32 32 0 0 1 0 64H480a32 32 0 0 1 0-64z"></path>
                  </svg>
                </el-icon>
                <span :class="['status-text', getStatusClass(item.user_id)]">{{ getStatusText(item.user_id) }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="mobile-info" v-if="item.mobile">
          <el-icon class="phone-icon"><Phone /></el-icon>
          <span class="load-name">{{ item.mobile }}</span>
        </div>
      </div>
      <div class="car-name">
        <el-icon class="icon"><Postcard /></el-icon>
        {{ item?.driving_type_list ? item?.driving_type_list.join(',') : '无驾驶类型' }}
      </div>
      <div class="car-name dept-row">
        <div class="dept-info">
          <el-icon class="icon"><OfficeBuilding /></el-icon>
          {{ item.dept_name || '无部门信息' }}
        </div>
        <el-button
          type="primary"
          size="small"
          class="call-button"
          @click="handleCall(item)"
          :disabled="getUserStatus(item.user_id).onlineStatus === 0"
          v-if="!isCurrentUser(item.user_id)"
        >
          <el-icon class="call-icon"><Microphone /></el-icon>
          呼叫
        </el-button>
      </div>
    </div>
  </div>
  <el-empty description="暂无数据" v-else>
    <template #image>
      <img src="../../../assets/empty_home.png" />
    </template>
  </el-empty>
</template>

<style lang="scss" scoped>
:deep(.el-tabs__header) {
  margin-bottom: 0;
}
.right-icon {
  margin-top: 5px;
  display: flex;
}

/* 状态样式 */
.status-controller {
  color: #1890ff !important; /* 手柄端在线 - 蓝色 */
  font-weight: 600;
}
.status-offline {
  color: #98a2b3 !important; /* 离线 - 灰色 */
}

.pointer {
  cursor: pointer;
}
.currentColor {
  background: #175091 !important;
}
:deep(.el-tabs__header) {
  border-bottom: 1px solid #344054;
}
:deep(.el-tabs__nav-wrap) {
  background: #11253e;
  color: #fff;
  height: 38px;
  line-height: 38px;
  font-family: SourceHanSansSC-Bold;
  font-size: 14px;
  text-align: left;
  font-weight: 700;
  border-bottom: 1px solid #344054;
  padding-left: 8px;
}
::-webkit-scrollbar {
  width: 8px; /* 设置滚动条的宽度 */
}
::-webkit-scrollbar-thumb {
  background-color: rgba(46, 144, 255, 0.5);
  border-radius: 2px; /* 设置滚动条滑块的背景色 */
}
.alarm-title {
  height: 38px;
  line-height: 38px;
  background: #11253e;
  color: #fff;
  font-family: SourceHanSansSC-Bold;
  font-size: 14px;
  text-align: left;
  font-weight: 700;
  border-bottom: 1px solid #344054;
  padding-left: 8px;
}
.alarm-ul {
  background: #001129;
  padding: 8px 8px 0 8px;
  height: 99%;
  overflow: auto;

  .connection-status {
    margin-bottom: 10px;
    text-align: center;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;

    .status-connected {
      color: #52c41a;
    }

    .status-disconnected {
      color: #ff4d4f;
    }

    .reconnect-btn {
      margin-left: 10px;
      padding: 2px 8px;
      font-size: 12px;
      height: 24px;
    }
  }

  .alarm-item {
    min-height: 76px;
    background: #11253e;
    margin-bottom: 12px;
    padding: 12px;
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;
    color: #f5f6f8;
    text-align: left;
    line-height: 22px;
    font-weight: 400;
    border-radius: 6px;
    transition: background-color 0.3s;
    border-left: 3px solid transparent;

    &:hover {
      background: #172c46;
    }

    &.current-user {
      background: #193254;
      border-left: 3px solid #52c41a;

      &:hover {
        background: #1e395f;
      }
    }

    .list {
      height: 38px;
      line-height: 38px;
      vertical-align: middle;
    }

    .load-name {
      font-family: SourceHanSansSC-Regular;
      font-size: 14px;
      color: #f5f6f8;
      text-align: left;
      line-height: 20px;
      font-weight: 400;
      display: flex;
      align-items: center;

      .user-avatar {
        margin-right: 12px;
        background-color: #1890ff;
        color: white;
        font-weight: bold;

        &.current-user-avatar {
          background-color: #52c41a;
        }
      }

      .user-info {
        display: flex;
        flex-direction: column;

        .username {
          display: flex;
          align-items: center;
          font-weight: 500;

          .current-tag {
            margin-left: 6px;
            font-size: 10px;
            padding: 0 4px;
            height: 18px;
            line-height: 16px;
          }
        }
      }

      .icon {
        color: #2e90fa;
        margin-right: 8px;
        font-size: 16px;
      }
    }

    .car-name {
      font-family: SourceHanSansSC-Regular;
      font-size: 12px;
      color: #98a2b3;
      line-height: 20px;
      font-weight: 400;
      margin-top: 10px;
      display: flex;
      align-items: center;

      .icon {
        color: #2e90fa;
        margin-right: 8px;
        font-size: 16px;
      }
    }

    .dept-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 10px;

      .dept-info {
        display: flex;
        align-items: center;
      }

      .call-button {
        padding: 6px 12px;
        font-size: 12px;
        height: 28px;
        background-color: #1890ff;
        color: #fff;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
          background-color: #4096ff;
        }

        &:disabled {
          background-color: #98a2b3;
          cursor: not-allowed;
        }

        .call-icon {
          margin-right: 4px;
          font-size: 14px;
        }
      }
    }

    .drone-name {
      display: flex;
      justify-content: space-between;
    }

    .pilot-info {
      display: flex;
      flex-direction: column;
    }

    .mobile-info {
      display: flex;
      align-items: center;

      .phone-icon {
        color: #2e90fa;
        margin-right: 6px;
        font-size: 16px;
      }
    }

    .status-info {
      display: flex;
      align-items: center;
      margin-top: 4px;

      .status-icon {
        margin-right: 4px;
        font-size: 14px;
      }

      .status-text {
        font-size: 12px;
      }
    }
  }
  .alarm-item:last-child {
    margin-bottom: 0;
  }
}
.flex {
  height: 38px;
  line-height: 38px;
  display: flex;
  justify-content: space-between;
}

/* 优化查询按钮样式 */
.query-bar {
  display: flex;
  justify-content: flex-end;
  padding: 10px 12px 0;
  margin-bottom: 12px;

  .query-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    padding: 0;
    border: 1px solid #1890ff;
    background-color: transparent;
    border-radius: 4px;
    color: #1890ff;
    transition: all 0.3s;
    position: relative; 

    .el-icon {
      margin: 0;
      font-size: 16px;
      transition: all 0.2s;
    }

    &:hover:not(.is-loading) {
      background-color: rgba(24, 144, 255, 0.15);
      transform: rotate(30deg);
    }

    &:active:not(.is-loading) {
      transform: scale(0.95) rotate(30deg);
    }
  }
}
</style>

