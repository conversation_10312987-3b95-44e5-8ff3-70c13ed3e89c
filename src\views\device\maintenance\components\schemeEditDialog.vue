<template>
	<el-dialog
		:title="title"
    v-if="visible"
		:model-value="visible"
		width="600px"
		align-center
		:close-on-click-modal="false"
		@close="closeDialog"
	>
		<el-form class="app-form" ref="dataFormRef" :model="form" :rules="rules" label-width="120px">
			<el-form-item label="维保方案名称" prop="scheme_name">
				<el-input
					v-model="form.scheme_name"
					placeholder="请输入维保方案名称"
					maxlength="50"
					clearable
					@blur="form.scheme_name = $event.target.value.trim()"
				/>
			</el-form-item>
			<el-form-item label="选择维保项" required >
				<div class="select-time" v-for="(n, index) in scheme_item_number" :key="n">
					<el-input
						style="width: 250px;"
						v-model="form.scheme_item[n - 1]"
						placeholder="请输入维保项"
						maxlength="50"
						clearable
					/>
          <el-button
            style="margin-left: 20px"
            :icon="Plus"
            type="primary"
            circle
            size="small"
            @click="addTime"
          />
          <el-button
            :icon="Minus"
            type="danger"
            circle
            size="small"
            @click="removeTime(index)"
            :disabled="scheme_item_number === 1"
          />
        </div>
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button type="primary" @click="handleSubmit" v-loading="loading">确 定</el-button>
				<el-button @click="closeDialog">取 消</el-button>
			</div>
		</template>
	</el-dialog>
</template>
  
  <script setup>
  import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
  import { ElMessage } from 'element-plus';
	import { Plus, Minus } from '@element-plus/icons-vue';
  import { addScheme, editScheme, getSchemeDetail } from '@/api/devices/maintenance.js';

	const scheme_item_number = ref(1);
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '标题'
    },
    formData: {
      type: Object,
      default(rawProps) {
        return {};
      }
    }
  });
  const form = reactive({
		scheme_item: []
	});
  const dataFormRef = ref(ElForm);
  
  watch(
    () => props.formData,
    (newVal, oldVal) => {
      Object.assign(form, newVal);
      console.log('点击编辑获取详情', form);
			if(props.title == '编辑维保方案') {
				initDetail();
			}
    },
    { deep: true }
  );
  const emit = defineEmits(['update:visible', 'submit']);
  const rules = reactive({
    scheme_name: [{ required: true, message: '请输入维保方案名称', trigger: 'blur' }],
	});
  const loading = ref(false);
  
  // 关闭弹窗 
  function closeDialog() {
    resetForm();
    emit('update:visible', false);
  }

	function addTime() {
		dataFormRef.value.validateField('scheme_item', valid => {
			if (valid) {
				form.scheme_item.push([]);
				scheme_item_number.value = scheme_item_number.value + 1;
			}
		});
	}

	function removeTime(index) {
		if (scheme_item_number.value === 1) return;
		scheme_item_number.value = scheme_item_number.value - 1;
		form.scheme_item.splice(index, 1);
	}
  
  /**
   * 重置表单
   */
  function resetForm() {
    dataFormRef.value.resetFields();
    dataFormRef.value.clearValidate();
    loading.value = false;
  	form.scheme_item = []
  	form.scheme_name = ''
		scheme_item_number.value = 1
  }
  
  function handleSubmit() {
    dataFormRef.value.validate(isValid => {
      if (isValid) {
        if(form.scheme_item.length <=0) {
          ElMessage.error('请先选择维保项')
          return;
        }
        for(let i=0;i<form.scheme_item.length;i++) {
          if(form.scheme_item[i].length == 0) {
            ElMessage.error(`请选择第${i+1}个维保项`)
            return;
          }
        }
        loading.value = true;
        if (props.title == '新增维保方案') {
					let arr = []
					form.scheme_item?.forEach(res=>{
						let obj = {}
						obj.item_name = res
						arr.push(obj)
					})
					let params = {
						scheme_name: form.scheme_name,
						items: arr
					};
          addScheme(params)
            .then(res => {
              loading.value = false;
              ElMessage.success('新增成功');
              closeDialog();
              emit('submit');
            })
            .catch(e => {
              loading.value = false;
            });
        } else {
          //编辑保单
					let arr = []
					form.scheme_item?.forEach(res=>{
						let obj = {}
						obj.item_name = res
						arr.push(obj)
					})
					let params = {
						id: form.id,
						scheme_name: form.scheme_name,
						items: arr
					};
          editScheme(params)
            .then(res => {
              loading.value = false;
              ElMessage.success('修改成功');
              closeDialog();
              emit('submit');
            })
            .catch(e => {
              loading.value = false;
            });
        }
      } else {
        loading.value = false;
      }
    });
  }

	function initDetail () {
		getSchemeDetail({
			id: form.id
		}).then(res=>{
			form.scheme_item = res.items?.map(item=>item.item_name) || []
			scheme_item_number.value = res.items ? res.items.length : 1
		})
	}

  onMounted(() => {
  });
  
  defineExpose({ resetForm });
  </script>
  <style scoped lang="scss">
  .input-serach {
    width: 200px;
  }
  .fly-bottom {
    margin-top: 40px;
    text-align: center;
  }
  .app-form {
    .select-time {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin: 5px 0;
      margin-top: 0;
    }
    ::v-deep {
      .el-input-number,
      .el-select {
        width: 100%;
      }
      .el-input-number .el-input__inner {
        text-align: left;
      }
      .el-input-number.is-controls-right .el-input__wrapper {
        padding-left: 11px;
      }
      .el-date-editor.el-input,
      .el-date-editor.el-input__wrapper {
        width: 100%;
      }
    }
  }
  </style>
  