// import _ from 'lodash';
import { defineStore } from 'pinia';
// import { constantRoutes, AGREED_ROUTE } from '@/router';
import { store } from '@/store';
// import { useUserStoreHook } from './user';
// import MenuTypeEnum from '@/enums/authTypes';

// const { CATALOG, MENU } = MenuTypeEnum;

// setup
export const usePermissionStore = defineStore('permission', () => {
  // state
  // const routes = ref([]);
  // actions
  // function setRoutes(newRoutes) {
  //   const ENV = import.meta.env;
  //   routes.value = constantRoutes.concat(newRoutes);
  //   // 生产环境下移除菜单管理
  //   if (ENV.MODE === 'production') {
  //     _.remove(routes.value, item => {
  //       return item.name === 'menuManager' || item.name === 'tools';
  //     });
  //   }
  // }
  // // 生成路由
  // function generateRoutes() {
  //   const sysRoutes = [];
  //   // 待加载的约定路由
  //   const userStore = useUserStoreHook();
  //   for (const item of userStore.permsRoutes) {
  //     const routerItem = AGREED_ROUTE[item.id];
  //     // 判断是否存在相应id的路由配置
  //     if (!routerItem) continue;
  //     const childrenRoutes = item.children
  //       .filter(children_menu =>
  //         // 1 目录(CATALOG) 2 菜单(MENU)
  //         // 3 页签(PAGE_TAG) 4 按钮(PAGE_BUTTON)
  //         [CATALOG, MENU].includes(children_menu.type)
  //       )
  //       .map(children_menu => {
  //         return AGREED_ROUTE[children_menu.id];
  //       })
  //       .filter(route => route);
  //     routerItem.children.push(...childrenRoutes);
  //     sysRoutes.push(routerItem);
  //   }
  //   // sysRoutes.unshift(AGREED_ROUTE['dashboard']);
  //   userStore.userAllRoutes.value = [...sysRoutes];
  //   setRoutes(sysRoutes);
  // }
  // return { routes, setRoutes, generateRoutes };
});

// 非setup
export function usePermissionStoreHook() {
  return usePermissionStore(store);
}
