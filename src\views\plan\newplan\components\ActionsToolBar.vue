<template>
  <div class="action-tool-wrapper" draggable="false">
    <div v-show="actionList.length > 0">
      <div v-for="act in actionList" :key="act" @click="onClick(act)">
        <div v-if="act.active" class="block">
          <el-tooltip popper-class="popperClass" :content="act.title" placement="left" effect="light">
            <el-image style="width: 25px; height: 25px" :src="act.url" />
          </el-tooltip>
        </div>
      </div>
    </div>
    <div class="warning-block" v-show="actionList.length === 0">
      <span class="icon">
        <el-icon><Warning /></el-icon>
      </span>

      <el-tooltip
        popper-class="popperClass"
        content="请选择设备类型并重新进入该页面，以完成动作配置"
        placement="top"
        effect="light"
      >
        未配置相关动作！
      </el-tooltip>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ActionsToolBar'
};
</script>
<script setup>
import { ElMessage } from 'element-plus';
import 'element-plus/dist/index.css';
import { Warning } from '@element-plus/icons-vue';
import {
  ACTION_ACTUATOR_FUNC,
  ACTION_ACTUATOR_FUNC_ICON,
  ACTION_ACTUATOR_FUNC_NAME,
  OPERATION_TYPE
} from '@/utils/constants';
import { useWayPointStore } from '@/store/modules/wayPointInfo.js';
import {
  getCurrentPlaceMark,
  createActionGroup,
  addActionGroupToPlaceMark,
  getMaxActionIndexFromActionGroup,
  insertActionIntoActionGroup,
  reBuidPlacemarkAndActionGroup,
  getMaxIndexNormalActionGroup,
  getAllActions
} from '../kmz/hocks/modules/waylineshandle';
import { actionCreateFuncMap } from '../kmz/hocks/actions';
import { ActionComponentMap } from '../actionComponents';
import { setStopMultipAction } from '../kmz/hocks/modules/multipleActionHandle';
import { getPlanInstance } from '@/components/Cesium/libs/cesium';
import { createFrustum, getFrustum } from '../kmz/hocks';
import { getLastActionInfoByPlacemarkIndex } from '../kmz/hocks/modules/actionHandle';
import { getDroneInfoByKey } from '../../common/devConfigHelper';
import { useDeviceStore } from '@/store/modules/device.js';
import { useEditTrackerStore } from '@/store/modules/dataEditTracker.js';
const editTrackerStore = useEditTrackerStore();
const deviceStore = useDeviceStore();
const wayPointStore = useWayPointStore();
const actionList = ref([]);

//#region 初始化
const init = () => {
  // 初始化info
  const devInfo = deviceStore.getCurrentDevice();
  let droneModelKey = devInfo.droneModelKey ?? '';
  if (droneModelKey === '') {
    ElMessage.error('获取设备信息出错！');
    return;
  }
  const baseDroneInfo = getDroneInfoByKey(droneModelKey);
  if (!baseDroneInfo) {
    ElMessage.error('未找到基础设备信息！');
    return;
  }
  let actions = baseDroneInfo.actions || [];
  if (actions.length === '') {
    ElMessage.error('当前未配置动作项，请核实！');
    return;
  }
  actionList.value = actions.map(actionName => {
    return {
      title: ACTION_ACTUATOR_FUNC_NAME[actionName],
      url: ACTION_ACTUATOR_FUNC_ICON[actionName],
      key: actionName,
      active: ActionComponentMap[actionName] || false
    };
  });
};
//#endregion

//#region 方法
const onClick = item => {
  const { key, url, title } = item;
  // 检查是否当前功能开放
  const hasComponent = ActionComponentMap[key];
  if (!hasComponent) {
    ElMessage.error('该功能未开放');
    return;
  }

  // 获取构建函数
  const actionFuncObj = actionCreateFuncMap[key];
  if (!actionFuncObj) {
    ElMessage.error('暂未开放此功能！');
    return null;
  }
  // 执行相关添加动作组数据和展示对应的动作操作面板
  // 根据功能 key
  const { currentPlaceMark, index: placemarkIndex } = getCurrentPlaceMark();
  if (!currentPlaceMark) {
    ElMessage.error('请先选择航点后，再添加动作！');
    return;
  }

  //#region  这里要区分动作类型是否是等间隔类型如等距离间距拍照和等时间间隔拍照还是普通动作类型
  // 1、如果是等距离间距拍照和等时间间隔拍照 那么都要创建工作组对象
  // 2、如果是其他动作则检查是否存在普通工作组如果存在则直接追加到普通动作组中
  //#endregion
  let curActionGroup = null;
  let triggerOptions = null;
  let curAction = null;
  if (checkMultipleAction(item)) {
    if (key === ACTION_ACTUATOR_FUNC.timeIntervalTakePhoto) {
      triggerOptions = {
        actionTriggerType: 'multipleTiming',
        actionTriggerParam: 3
      };
    } else if (key === ACTION_ACTUATOR_FUNC.distanceIntervalTakePhoto) {
      triggerOptions = {
        actionTriggerType: 'multipleDistance',
        actionTriggerParam: 10
      };
    } else if (key === ACTION_ACTUATOR_FUNC.stopIntervalTakePhoto) {
      triggerOptions = {
        actionTriggerType: null,
        actionTriggerParam: null
      };
    }
    // 这里设置动当前动作组的相关参数
    const actionFuncObj = actionCreateFuncMap[key];
    const { actionGroup = null, action = null } = actionFuncObj?.createFunc({
      placemark: currentPlaceMark
    });
    // 动作赋值
    curAction = action;
    // 将动作添加到航点对象中
    const newActionGroup = addActionGroupToPlaceMark(currentPlaceMark, actionGroup);
    // 重建航点和动作组序列
    reBuidPlacemarkAndActionGroup(0);

    //#region 1 右边动作参数面板
    // 获取动作组默认参数 根据动作类型  action组件 右边组件内容面板数据更新
    window.$bus.emit('setAction', {
      key: key,
      title: title,
      url: url,
      actionUuid: action.uuid,
      actionFuncParam: action.wpml_actionActuatorFuncParam,
      action: action,
      actionGroup: newActionGroup || actionGroup
    });
    //#endregion

    //#region 2 左侧列表所属动作参数
    /*********** tmj 20240420**************/
    // 左侧列表所属动作参数
    const actionOption = {
      type: key,
      url: url, // 图标url,
      title: title,
      actionUuid: action.uuid,
      actionTriggerType: triggerOptions.actionTriggerType,
      actionId: getMaxActionIndexFromActionGroup(actionGroup),
      actionActuatorFunc: ACTION_ACTUATOR_FUNC[key],
      actionActuatorFuncParamOptions: action.wpml_actionActuatorFuncParam || null
    };
    //添加动作到当前航点
    wayPointStore.addAction(actionOption, actionGroup?.wpml_actionGroupId);
    //#endregion
    //#region 3 这里如果是停止间隔动作需要进行特别处理
    // 这里执行停止间隔动作相关联的操作
    if (key === ACTION_ACTUATOR_FUNC.stopIntervalTakePhoto) {
      // 停止间隔动作将改变上一个间隔动作的结束索引
      setStopMultipAction(action, actionGroup);
    }
    //#endregion
  } else {
    triggerOptions = {
      actionTriggerType: 'reachPoint',
      actionTriggerParam: ''
    };
    // 如果是普通动作组,获取索引最大的普通组 如果不是则直接获取最大索引的动作组
    // 检查该Placcemark是否已经存在ActionGroup 如果存在则向里面添加动作 如果不存在则先创建动作组再向里添加动作
    curActionGroup = getMaxIndexNormalActionGroup(currentPlaceMark, OPERATION_TYPE.normal);
    if (!curActionGroup) {
      const newActionGroup = createActionGroup(currentPlaceMark);
      curActionGroup = addActionGroupToPlaceMark(currentPlaceMark, newActionGroup);
    }
    if (!curActionGroup) {
      throw new Error('”curActionGroup“ is null');
    }
    // 重建序列
    reBuidPlacemarkAndActionGroup();
    //#region 获取动作默认参数 根据动作类型
    const actionCreateFuncObj = actionCreateFuncMap[key];
    let actionOption = null;
    // 这里有几个参数要从外面传入

    if (ACTION_ACTUATOR_FUNC.zoom === key) {
      const option = getLastActionInfoByPlacemarkIndex(placemarkIndex);

      actionOption = {
        type: key,
        url: url, // 图标url,
        title: title,
        actionUuid: '',
        actionTriggerType: triggerOptions.actionTriggerType,
        actionId: getMaxActionIndexFromActionGroup(curActionGroup),
        actionActuatorFunc: ACTION_ACTUATOR_FUNC[key],
        actionActuatorFuncParamOptions: actionCreateFuncObj ? actionCreateFuncObj.defaultParamFunc(option) : null,
        action: null
      };
    } else {
      actionOption = {
        type: key,
        url: url, // 图标url,
        title: title,
        actionUuid: '',
        actionTriggerType: triggerOptions.actionTriggerType,
        actionId: getMaxActionIndexFromActionGroup(curActionGroup),
        actionActuatorFunc: ACTION_ACTUATOR_FUNC[key],
        actionActuatorFuncParamOptions: actionCreateFuncObj ? actionCreateFuncObj.defaultParamFunc() : null,
        action: null
      };
    }

    // 向动作组中插入动作
    curAction = insertActionIntoActionGroup(curActionGroup, actionOption);
    if (!curAction) {
      return;
    }
    // 设置 actionUuid 和动作对象
    actionOption.actionUuid = curAction.uuid;
    actionOption.action = curAction;
    // actionOption action组件 右边组件内容面板数据更新
    const options = {
      key: key, // 如'takePhoto',
      title: title,
      url: url, // 图标url
      actionUuid: actionOption.actionUuid, // 动作id
      actionFuncParam: curAction.wpml_actionActuatorFuncParam, // 动作参数
      action: curAction, // 动作
      actionGroup: null
    };
    //  如果是缩放动作那么动作参数要线获取上一个动作的参数的值 缩放值
    if (key === ACTION_ACTUATOR_FUNC.zoom) {
      let allActions = getAllActions() ?? [];
      // 遍历 并且获取动作类型为ACTION_ACTUATOR_FUNC.zoom的动作和动作id在 actionUuid 之前的 动作
      const zoomActions = allActions.filter(item => item.wpml_actionActuatorFunc === ACTION_ACTUATOR_FUNC.zoom);
      const targetIndex = zoomActions.findIndex(item => item.uuid === actionOption.actionUuid);
      // If the target action is found, get the previous action's wpml_actionActuatorFuncParam
      if (targetIndex > 0) {
        const previousAction = zoomActions[targetIndex - 1];
        let result = Object.assign({}, previousAction.wpml_actionActuatorFuncParam);
        options.actionFuncParam = result;
        curAction.wpml_actionActuatorFuncParam = result;
        actionOption.action.wpml_actionActuatorFuncParam = result;
      }
    }

    window.$bus.emit('setAction', options);
    //#endregion

    /*********** tmj 20240420**************/
    //添加动作到当前航点
    const actionGroupId = curActionGroup?.getActionGroupId();
    wayPointStore.addAction(actionOption, actionGroupId);
  }
  const plan = getPlanInstance();
  // 设置地图第一个点被选中
  const pointjson = plan.selectPlanPoint(placemarkIndex, false);
  if (pointjson) {
    const { lon = null, lat = null, height = null, UAVHPR: hpr = null } = pointjson;
    let cameraFrustun = getFrustum('camera');
    const op = cameraFrustun?.getOptions() ?? {};
    let frustun = getFrustum('action');
    if (frustun) {
      frustun.update({
        ...op,
        position: [lon, lat, height]
      });
    } else {
      createFrustum(
        {
          ...op,
          colorType: 'action',
          curAction
        },
        'action'
      );
    }
  }
  // 标记编辑
  editTrackerStore.dataTracker.markAsModified();
};

/**
 * 检查该动作项是否是等间隔执行动作
 * @param {bool} 是否是等间隔执行动作
 */
const checkMultipleAction = item => {
  try {
    const { key, url, title } = item;
    if (
      key === ACTION_ACTUATOR_FUNC.timeIntervalTakePhoto ||
      key === ACTION_ACTUATOR_FUNC.distanceIntervalTakePhoto ||
      key === ACTION_ACTUATOR_FUNC.stopIntervalTakePhoto
    ) {
      return true;
    }
    return false;
  } catch (error) {
    return false;
  }
};

//#endregion
onMounted(() => {
  init();
});
onUnmounted(() => {});
</script>

<style>
.popperClass {
  color: #ffffff;
}
</style>
<style lang="scss" scoped>
.action-tool-wrapper {
  position: absolute;
  width: auto;
  height: 500px;
  top: 55px;
  right: 360px;
  margin: 0;
  padding: 0;
  user-select: none;
  z-index: 100;
  .block {
    // background-color: rgba(0, 0, 0, 0.612);
    margin: 5px 0px;
    padding: 5px;
    display: flex;
    justify-content: flex-end;
    align-content: center;
    background-color: rgba(55, 58, 68, 0.805);
    .title {
      color: aliceblue;
      margin-right: 5px;
      pointer-events: none;
    }
    :hover {
      background-color: rgba(55, 58, 68, 0.805);
      cursor: pointer;
    }
  }
  :hover {
    // background-color: rgba(55, 58, 68, 0.805);
    cursor: pointer;
  }
}
.warning-block {
  margin: 5px 0px;
  padding: 5px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background-color: #ff9900;
  .title {
    color: aliceblue;
    margin-right: 5px;
    pointer-events: none;
  }
  .icon {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
