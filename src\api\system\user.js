import request from '@/utils/request';

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/system/user/page',
    method: 'get',
    params: query
  })
}
// 获取用户分页列表（含在线状态）
export function listOnlineUser(query) {
  return request({
    url: '/system/user/online-page',
    method: 'get',
    params: query
  })
}

// 查询成员详细
export function getUser(UserId) {
  return request({
    url: '/system/user/get?id=' + UserId,
    method: 'get'
  })
}

// 新增成员
export function addUser(data) {
  return request({
    url: '/system/user/create',
    method: 'post',
    data: data
  })
}

// 修改成员
export function updateUser(data) {
  return request({
    url: '/system/user/update',
    method: 'put',
    data: data
  })
}

// 删除成员
export function delUser(UserId) {
  return request({
    url: '/system/user/delete?id=' + UserId,
    method: 'delete'
  })
}


// 查询驾驶证列表
export function listDriver(userId) {
  return request({
    url: '/manage/api/v1/users/drive/'+userId,
    method: 'get',
  })
}
// 新增修改驾驶证列表
export function updateDriver(data,userId) {
  return request({
    url: '/manage/api/v1/users/drive/'+userId,
    method: 'post',
    data: data
  })
}
// 删除驾驶证
export function delDriver(id) {
  return request({
    url: '/manage/api/v1/users/drive/'+id,
    method: 'delete'
  })
}