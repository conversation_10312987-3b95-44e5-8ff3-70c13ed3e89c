import i18n from '@/lang';
import { fmtTime } from '@/utils/helper';

/**
 * 自定义 Hook 用于处理表单相关操作。
 */
export default function useForm() {}

/**
 * 日期选择器快捷选项的配置。
 * 包含了几个预定义的日期范围选项，每个选项包括文本描述和值的生成函数。
 */
export const DATE_PICKER_OPTIONS_SHORTCUTS = [
  {
    text: i18n.global.t('page.lastDay'), // 文本描述，表示最近一天
    value: () => {
      let end = new Date();
      let start = new Date();

      // 获取开始和结束时间，以包括整个最近一天的范围
      start = fmtTime(start, 'yyyy-MM-dd 00:00:00');
      end = fmtTime(end, 'yyyy-MM-dd 23:59:59');

      return [start, end]; // 返回日期范围数组
    }
  },
  {
    text: i18n.global.t('page.lastThreeDay'), // 文本描述，表示最近三天
    value: () => {
      let end = new Date();
      let start = new Date();

      // 计算三天前的日期并获取开始和结束时间
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
      start = fmtTime(start, 'yyyy-MM-dd 00:00:00');
      end = fmtTime(end, 'yyyy-MM-dd 23:59:59');

      return [start, end]; // 返回日期范围数组
    }
  },
  {
    text: i18n.global.t('page.lastWeek'), // 文本描述，表示最近一周
    value: () => {
      let end = new Date();
      let start = new Date();

      // 计算一周前的日期并获取开始和结束时间
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      start = fmtTime(start, 'yyyy-MM-dd 00:00:00');
      end = fmtTime(end, 'yyyy-MM-dd 23:59:59');

      return [start, end]; // 返回日期范围数组
    }
  }
];

/**
 * 默认的日期时间范围配置。
 * 包括了一个起始时间和结束时间，用于初始化日期选择器的默认值。
 */
export const DATE_DEFAULT_TIME = [
  new Date(2000, 1, 0, 0, 0, 0), // 起始时间，默认为 2000-01-01 00:00:00
  new Date(2000, 1, 1, 23, 59, 59) // 结束时间，默认为 2000-01-01 23:59:59
];

// export const DATE_DEFAULT_TIME = [
//   new Date(new Date().getTime() - 3600 * 1000 * 24 * 7), // 起始时间，默认为 七天前的 00:00:00
//   new Date(new Date().setHours(23, 59, 59, 999)) // 结束时间，默认为 当天的 23:59:59
// ];
