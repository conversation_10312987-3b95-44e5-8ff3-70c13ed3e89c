!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("crypto")):"function"==typeof define&&define.amd?define(["crypto"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).crypto)}(this,(function(e){"use strict";function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var r=t(e),s=1e-6,a="undefined"!=typeof Float32Array?Float32Array:Array;function n(){var e=new a(16);return a!=Float32Array&&(e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[11]=0,e[12]=0,e[13]=0,e[14]=0),e[0]=1,e[5]=1,e[10]=1,e[15]=1,e}function o(e){return e[0]=1,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=1,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=1,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,e}Math.hypot||(Math.hypot=function(){for(var e=0,t=arguments.length;t--;)e+=arguments[t]*arguments[t];return Math.sqrt(e)});var d,l=function(e,t,i,r,s,a,n){var o=1/(t-i),d=1/(r-s),l=1/(a-n);return e[0]=-2*o,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=-2*d,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=2*l,e[11]=0,e[12]=(t+i)*o,e[13]=(s+r)*d,e[14]=(n+a)*l,e[15]=1,e};function c(e,t,i){var r=new a(3);return r[0]=e,r[1]=t,r[2]=i,r}d=new a(3),a!=Float32Array&&(d[0]=0,d[1]=0,d[2]=0);var u=(e,t)=>{t&&e.pixelStorei(e.UNPACK_ALIGNMENT,1);const i=function(){const t=_(e.VERTEX_SHADER,"\n            attribute vec4 aVertexPosition;\n            attribute vec2 aTexturePosition;\n            uniform mat4 uModelMatrix;\n            uniform mat4 uViewMatrix;\n            uniform mat4 uProjectionMatrix;\n            varying lowp vec2 vTexturePosition;\n            void main(void) {\n              gl_Position = uProjectionMatrix * uViewMatrix * uModelMatrix * aVertexPosition;\n              vTexturePosition = aTexturePosition;\n            }\n        "),i=_(e.FRAGMENT_SHADER,"\n            precision highp float;\n            varying highp vec2 vTexturePosition;\n            uniform int isyuv;\n            uniform sampler2D rgbaTexture;\n            uniform sampler2D yTexture;\n            uniform sampler2D uTexture;\n            uniform sampler2D vTexture;\n\n            const mat4 YUV2RGB = mat4( 1.1643828125, 0, 1.59602734375, -.87078515625,\n                                       1.1643828125, -.39176171875, -.81296875, .52959375,\n                                       1.1643828125, 2.017234375, 0, -1.081390625,\n                                       0, 0, 0, 1);\n\n\n            void main(void) {\n\n                if (isyuv>0) {\n\n                    highp float y = texture2D(yTexture,  vTexturePosition).r;\n                    highp float u = texture2D(uTexture,  vTexturePosition).r;\n                    highp float v = texture2D(vTexture,  vTexturePosition).r;\n                    gl_FragColor = vec4(y, u, v, 1) * YUV2RGB;\n\n                } else {\n                    gl_FragColor =  texture2D(rgbaTexture, vTexturePosition);\n                }\n            }\n        "),r=e.createProgram();if(e.attachShader(r,t),e.attachShader(r,i),e.linkProgram(r),!e.getProgramParameter(r,e.LINK_STATUS))return console.log("Unable to initialize the shader program: "+e.getProgramInfoLog(r)),null;return r}();let r={program:i,attribLocations:{vertexPosition:e.getAttribLocation(i,"aVertexPosition"),texturePosition:e.getAttribLocation(i,"aTexturePosition")},uniformLocations:{projectionMatrix:e.getUniformLocation(i,"uProjectionMatrix"),modelMatrix:e.getUniformLocation(i,"uModelMatrix"),viewMatrix:e.getUniformLocation(i,"uViewMatrix"),rgbatexture:e.getUniformLocation(i,"rgbaTexture"),ytexture:e.getUniformLocation(i,"yTexture"),utexture:e.getUniformLocation(i,"uTexture"),vtexture:e.getUniformLocation(i,"vTexture"),isyuv:e.getUniformLocation(i,"isyuv")}},a=function(){const t=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,t);e.bufferData(e.ARRAY_BUFFER,new Float32Array([-1,-1,-1,1,-1,-1,1,1,-1,-1,1,-1]),e.STATIC_DRAW);var i=[];i=i.concat([0,1],[1,1],[1,0],[0,0]);const r=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,r),e.bufferData(e.ARRAY_BUFFER,new Float32Array(i),e.STATIC_DRAW);const s=e.createBuffer();e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,s);return e.bufferData(e.ELEMENT_ARRAY_BUFFER,new Uint16Array([0,1,2,0,2,3]),e.STATIC_DRAW),{position:t,texPosition:r,indices:s}}(),d=p(),u=p(),h=p(),f=p();function p(){let t=e.createTexture();return e.bindTexture(e.TEXTURE_2D,t),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),t}function _(t,i){const r=e.createShader(t);return e.shaderSource(r,i),e.compileShader(r),e.getShaderParameter(r,e.COMPILE_STATUS)?r:(console.log("An error occurred compiling the shaders: "+e.getShaderInfoLog(r)),e.deleteShader(r),null)}function m(t,i){e.viewport(0,0,t,i),e.clearColor(0,0,0,0),e.clearDepth(1),e.enable(e.DEPTH_TEST),e.depthFunc(e.LEQUAL),e.clear(e.COLOR_BUFFER_BIT|e.DEPTH_BUFFER_BIT);const d=n();l(d,-1,1,-1,1,.1,100);const p=n();o(p);const _=n();!function(e,t,i,r){var a,n,d,l,c,u,h,f,p,_,m=t[0],g=t[1],y=t[2],b=r[0],v=r[1],S=r[2],w=i[0],A=i[1],B=i[2];Math.abs(m-w)<s&&Math.abs(g-A)<s&&Math.abs(y-B)<s?o(e):(h=m-w,f=g-A,p=y-B,a=v*(p*=_=1/Math.hypot(h,f,p))-S*(f*=_),n=S*(h*=_)-b*p,d=b*f-v*h,(_=Math.hypot(a,n,d))?(a*=_=1/_,n*=_,d*=_):(a=0,n=0,d=0),l=f*d-p*n,c=p*a-h*d,u=h*n-f*a,(_=Math.hypot(l,c,u))?(l*=_=1/_,c*=_,u*=_):(l=0,c=0,u=0),e[0]=a,e[1]=l,e[2]=h,e[3]=0,e[4]=n,e[5]=c,e[6]=f,e[7]=0,e[8]=d,e[9]=u,e[10]=p,e[11]=0,e[12]=-(a*m+n*g+d*y),e[13]=-(l*m+c*g+u*y),e[14]=-(h*m+f*g+p*y),e[15]=1)}(_,c(0,0,0),c(0,0,-1),c(0,1,0));{const t=3,i=e.FLOAT,s=!1,n=0,o=0;e.bindBuffer(e.ARRAY_BUFFER,a.position),e.vertexAttribPointer(r.attribLocations.vertexPosition,t,i,s,n,o),e.enableVertexAttribArray(r.attribLocations.vertexPosition)}{const t=2,i=e.FLOAT,s=!1,n=0,o=0;e.bindBuffer(e.ARRAY_BUFFER,a.texPosition),e.vertexAttribPointer(r.attribLocations.texturePosition,t,i,s,n,o),e.enableVertexAttribArray(r.attribLocations.texturePosition)}e.activeTexture(e.TEXTURE0+3),e.bindTexture(e.TEXTURE_2D,u),e.activeTexture(e.TEXTURE0+4),e.bindTexture(e.TEXTURE_2D,h),e.activeTexture(e.TEXTURE0+5),e.bindTexture(e.TEXTURE_2D,f),e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,a.indices),e.useProgram(r.program),e.uniformMatrix4fv(r.uniformLocations.projectionMatrix,!1,d),e.uniformMatrix4fv(r.uniformLocations.modelMatrix,!1,p),e.uniformMatrix4fv(r.uniformLocations.viewMatrix,!1,_),e.uniform1i(r.uniformLocations.rgbatexture,2),e.uniform1i(r.uniformLocations.ytexture,3),e.uniform1i(r.uniformLocations.utexture,4),e.uniform1i(r.uniformLocations.vtexture,5),e.uniform1i(r.uniformLocations.isyuv,1);{const t=6,i=e.UNSIGNED_SHORT,r=0;e.drawElements(e.TRIANGLES,t,i,r)}}return{render:function(t,i,r,s,a){e.activeTexture(e.TEXTURE0),e.bindTexture(e.TEXTURE_2D,u),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t,i,0,e.LUMINANCE,e.UNSIGNED_BYTE,r),e.activeTexture(e.TEXTURE1),e.bindTexture(e.TEXTURE_2D,h),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t/2,i/2,0,e.LUMINANCE,e.UNSIGNED_BYTE,s),e.activeTexture(e.TEXTURE2),e.bindTexture(e.TEXTURE_2D,f),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t/2,i/2,0,e.LUMINANCE,e.UNSIGNED_BYTE,a),m(t,i)},renderYUV:function(t,i,r){let s=r.slice(0,t*i),a=r.slice(t*i,t*i*5/4),n=r.slice(t*i*5/4,t*i*3/2);e.activeTexture(e.TEXTURE0),e.bindTexture(e.TEXTURE_2D,u),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t,i,0,e.LUMINANCE,e.UNSIGNED_BYTE,s),e.activeTexture(e.TEXTURE1),e.bindTexture(e.TEXTURE_2D,h),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t/2,i/2,0,e.LUMINANCE,e.UNSIGNED_BYTE,a),e.activeTexture(e.TEXTURE2),e.bindTexture(e.TEXTURE_2D,f),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t/2,i/2,0,e.LUMINANCE,e.UNSIGNED_BYTE,n),m(t,i)},destroy:function(){e.deleteProgram(r.program),e.deleteBuffer(a.position),e.deleteBuffer(a.texPosition),e.deleteBuffer(a.indices),e.deleteTexture(d),e.deleteTexture(u),e.deleteTexture(h),e.deleteTexture(f),r=null,a=null,d=null,u=null,h=null,f=null}}};const h=1,f=2,p="fetch",_="websocket",m="player",g="playbackTF",y="mp4",b="debug",v="warn",S=36e5,w=4080,A=12,B={playType:m,container:"",videoBuffer:1e3,videoBufferDelay:1e3,networkDelay:1e4,isResize:!0,isFullResize:!1,isFlv:!1,isHls:!1,isFmp4:!1,isFmp4Private:!1,isWebrtc:!1,isWebrtcForZLM:!1,isWebrtcForSRS:!1,isWebrtcForOthers:!1,isNakedFlow:!1,isMpeg4:!1,isAliyunRtc:!1,isTs:!1,debug:!1,debugLevel:v,debugUuid:"",isMulti:!0,multiIndex:-1,hotKey:!1,loadingTimeout:10,heartTimeout:10,timeout:10,pageVisibilityHiddenTimeout:300,loadingTimeoutReplay:!0,heartTimeoutReplay:!0,loadingTimeoutReplayTimes:3,heartTimeoutReplayTimes:3,heartTimeoutReplayUseLastFrameShow:!0,replayUseLastFrameShow:!0,replayShowLoadingIcon:!1,supportDblclickFullscreen:!1,showBandwidth:!1,showPerformance:!1,mseCorrectTimeDuration:20,mseCorrectAudioTimeDuration:20,keepScreenOn:!0,isNotMute:!1,muted:!0,hasAudio:!0,hasVideo:!0,operateBtns:{fullscreen:!1,screenshot:!1,play:!1,audio:!1,record:!1,ptz:!1,quality:!1,zoom:!1,close:!1,scale:!1,performance:!1,logSave:!1,aiFace:!1,aiObject:!1,aiOcclusion:!1,fullscreenFn:null,fullscreenExitFn:null,screenshotFn:null,playFn:null,pauseFn:null,recordFn:null,recordStopFn:null},extendOperateBtns:[],contextmenuBtns:[],watermarkConfig:{},controlAutoHide:!1,hasControl:!1,loadingIcon:!0,loadingIconStyle:{},loadingText:"",background:"",poster:"",backgroundLoadingShow:!0,loadingBackground:"",loadingBackgroundWidth:0,loadingBackgroundHeight:0,decoder:"decoder-pro.js",decoderAudio:"decoder-pro-audio.js",decoderHard:"decoder-pro-hard.js",decoderHardNotWasm:"decoder-pro-hard-not-wasm.js",wasmMp4RecorderDecoder:"jessibuca-pro-mp4-recorder-decoder.js",decoderWASM:"",isDecoderUseCDN:!1,url:"",rotate:0,mirrorRotate:"none",aspectRatio:"default",playbackConfig:{playList:[],fps:"",showControl:!0,controlType:"normal",duration:0,startTime:"",showRateBtn:!1,rateConfig:[],showPrecision:"",showPrecisionBtn:!0,isCacheBeforeDecodeForFpsRender:!1,uiUsePlaybackPause:!1,isPlaybackPauseClearCache:!0,isUseFpsRender:!1,isUseLocalCalculateTime:!1,localOneFrameTimestamp:40,supportWheel:!1,useWCS:!1,useMSE:!1},qualityConfig:[],defaultStreamQuality:"",scaleConfig:["拉伸","缩放","正常"],forceNoOffscreen:!0,hiddenAutoPause:!1,protocol:f,demuxType:"flv",useWasm:!1,useMSE:!1,useWCS:!1,useSIMD:!0,useMThreading:!1,wcsUseVideoRender:!0,wcsUseWebgl2Render:!0,wasmUseVideoRender:!0,mseUseCanvasRender:!1,hlsUseCanvasRender:!1,webrtcUseCanvasRender:!1,useOffscreen:!1,useWebGPU:!1,mseDecodeErrorReplay:!0,wcsDecodeErrorReplay:!0,wasmDecodeErrorReplay:!0,simdDecodeErrorReplay:!0,simdDecodeErrorReplayType:"wasm",autoWasm:!0,decoderErrorAutoWasm:!0,hardDecodingNotSupportAutoWasm:!0,webglAlignmentErrorReplay:!0,webglContextLostErrorReplay:!0,openWebglAlignment:!1,syncAudioAndVideo:!1,syncAudioAndVideoDiff:500,playbackDelayTime:1e3,playbackFps:25,playbackForwardMaxRateDecodeIFrame:4,playbackCurrentTimeMove:!0,useVideoRender:!0,useCanvasRender:!1,networkDelayTimeoutReplay:!1,recordType:y,checkFirstIFrame:!0,nakedFlowFps:25,audioEngine:null,isShowRecordingUI:!0,isShowZoomingUI:!0,useFaceDetector:!1,useObjectDetector:!1,useImageDetector:!1,useOcclusionDetector:!1,ptzPositionConfig:{},ptzShowType:"vertical",ptzClickType:"click",ptzStopEmitDelay:.3,ptzZoomShow:!1,ptzApertureShow:!1,ptzFocusShow:!1,ptzMoreArrowShow:!1,ptzCruiseShow:!1,ptzFogShow:!1,ptzWiperShow:!1,ptzSupportDraggable:!1,weiXinInAndroidAudioBufferSize:4800,isM7sCrypto:!1,m7sCryptoAudio:!1,isSm4Crypto:!1,isXorCrypto:!1,sm4CryptoKey:"",m7sCryptoKey:"",xorCryptoKey:"",cryptoKey:"",cryptoIV:"",cryptoKeyUrl:"",autoResize:!1,useWebFullScreen:!1,ptsMaxDiff:3600,aiFaceDetectLevel:2,aiFaceDetectWidth:240,aiFaceDetectShowRect:!0,aiFaceDetectInterval:1e3,aiFaceDetectRectConfig:{},aiObjectDetectLevel:2,aiObjectDetectWidth:240,aiObjectDetectShowRect:!0,aiObjectDetectInterval:1e3,aiObjectDetectRectConfig:{},aiOcclusionDetectInterval:1e3,aiImageDetectDrop:!1,aiImageDetectActive:!1,videoRenderSupportScale:!0,mediaSourceTsIsMaxDiffReplay:!0,controlHtml:"",isH265:!1,isWebrtcH265:!1,supportLockScreenPlayAudio:!0,supportHls265:!1,isEmitSEI:!1,pauseAndNextPlayUseLastFrameShow:!1,demuxUseWorker:!0,playFailedAndReplay:!0,showMessageConfig:{webglAlignmentError:"Webgl 渲染失败",webglContextLostError:"webgl 上下文丢失",mediaSourceH265NotSupport:"不支持硬解码H265",mediaSourceFull:"缓冲区已满",mediaSourceAppendBufferError:"初始化解码器失败",mseSourceBufferError:"解码失败",mseAddSourceBufferError:"初始化解码器失败",mediaSourceDecoderConfigurationError:"初始化解码器失败",mediaSourceTsIsMaxDiff:"流异常",mseWidthOrHeightChange:"流异常",mediaSourceAudioG711NotSupport:"硬解码不支持G711a/u音频格式",mediaSourceUseCanvasRenderPlayFailed:"MediaSource解码使用canvas渲染失败",webcodecsH265NotSupport:"不支持硬解码H265",webcodecsUnsupportedConfigurationError:"初始化解码器失败",webcodecsDecodeConfigureError:"初始化解码器失败",webcodecsDecodeError:"解码失败",wcsWidthOrHeightChange:"解码失败",wasmDecodeError:"解码失败",simdDecodeError:"解码失败",wasmWidthOrHeightChange:"流异常",wasmUseVideoRenderError:"video自动渲染失败",videoElementPlayingFailed:"video自动渲染失败",simdH264DecodeVideoWidthIsTooLarge:"不支持该分辨率的视频",networkDelayTimeout:"网络超时重播失败",fetchError:"请求失败",streamEnd:"请求结束",websocketError:"请求失败",webrtcError:"请求失败",hlsError:"请求失败",decoderWorkerInitError:"初始化worker失败",videoElementPlayingFailedForWebrtc:"video自动渲染失败",videoInfoError:"解析视频分辨率失败",webrtcStreamH265:"webrtc不支持H265",delayTimeout:"播放超时重播失败",loadingTimeout:"加载超时重播失败",loadingTimeoutRetryEnd:"加载超时重播失败",delayTimeoutRetryEnd:"播放超时重播失败"},videoElementPlayingFailedReplay:!0,mp4RecordUseWasm:!0,mseAutoCleanupSourceBuffer:!0,mseAutoCleanupMaxBackwardDuration:30,mseAutoCleanupMinBackwardDuration:10,widthOrHeightChangeReplay:!0,simdH264DecodeVideoWidthIsTooLargeReplay:!0,mediaSourceAudioG711NotSupportReplay:!0,mediaSourceAudioInitTimeoutReplay:!0,mediaSourceUseCanvasRenderPlayFailedReplay:!0,mediaSourceUseCanvasRenderPlayFailedReplayType:"video",widthOrHeightChangeReplayDelayTime:0,ghostWatermarkConfig:{on:5,off:5,content:"",fontSize:12,color:"white",opacity:.15,speed:.2},dynamicWatermarkConfig:{content:"",speed:.2,fontSize:12,color:"white",opacity:.15},isDropSameTimestampGop:!1,mseDecodeAudio:!1,nakedFlowH265DemuxUseNew:!0,extendDomConfig:{html:"",showBeforePlay:!1,showAfterLoading:!0},disableContextmenu:!1,mseDecoderUseWorker:!1,openMemoryLog:!1,mainThreadFetchUseWorker:!0,playFailedAndPausedShowPlayBtn:!0,mseCorrectionTimestamp:!0,flvDemuxBufferSizeTooLargeReplay:!1,flvDemuxBufferSizeTooLargeEmitFailed:!1,flvDemuxBufferSizeMaxLarge:1048576,isCheckInView:!1,hiddenControl:!1},x="init",U="initVideo",E="render",T="playAudio",k="initAudio",C="audioCode",D="audioNalu",I="audioAACSequenceHeader",L="videoCode",F="videoCodec",P="videoNalu",M="videoPayload",z="audioPayload",R="workerFetch",N="iframeIntervalTs",G="isDropping",O="playbackStreamVideoFps",H="wasmWidthOrHeightChange",V="simdDecodeError",$="simdH264DecodeVideoWidthIsTooLarge",W="closeEnd",Y="tempStream",q="videoSEI",j="flvScriptData",K="aacSequenceHeader",X="videoSequenceHeader",Z="flvBufferData",J="checkFirstIFrame",Q="mseHandle",ee="mseFirstRenderTime",te="mseError",ie=1,re=2,se=8,ae=9,ne=18,oe="init",de="decode",le="audioDecode",ce="videoDecode",ue="close",he="updateConfig",fe="clearBuffer",pe="fetchStream",_e="sendWsMessage",me="mseUpdateVideoTimestamp",ge="delayTimeout",ye="loadingTimeout",be="streamEnd",ve="streamRate",Se="streamAbps",we="streamVbps",Ae="streamDts",Be="streamSuccess",xe="streamStats",Ue="networkDelayTimeout",Ee="websocketOpen",Te={playError:"playIsNotPauseOrUrlIsNull",fetchError:"fetchError",websocketError:"websocketError",webcodecsH265NotSupport:"webcodecsH265NotSupport",webcodecsDecodeError:"webcodecsDecodeError",webcodecsUnsupportedConfigurationError:"webcodecsUnsupportedConfigurationError",webcodecsDecodeConfigureError:"webcodecsDecodeConfigureError",mediaSourceH265NotSupport:"mediaSourceH265NotSupport",mediaSourceAudioG711NotSupport:"mediaSourceAudioG711NotSupport",mediaSourceAudioInitTimeout:"mediaSourceAudioInitTimeout",mediaSourceAudioNoDataTimeout:"mediaSourceAudioNoDataTimeout",mediaSourceDecoderConfigurationError:"mediaSourceDecoderConfigurationError",mediaSourceFull:"mseSourceBufferFull",mseSourceBufferError:"mseSourceBufferError",mseAddSourceBufferError:"mseAddSourceBufferError",mediaSourceAppendBufferError:"mediaSourceAppendBufferError",mediaSourceTsIsMaxDiff:"mediaSourceTsIsMaxDiff",mediaSourceUseCanvasRenderPlayFailed:"mediaSourceUseCanvasRenderPlayFailed",mediaSourceBufferedIsZeroError:"mediaSourceBufferedIsZeroError",wasmDecodeError:"wasmDecodeError",wasmUseVideoRenderError:"wasmUseVideoRenderError",hlsError:"hlsError",webrtcError:"webrtcError",webrtcClosed:"webrtcClosed",webrtcIceCandidateError:"webrtcIceCandidateError",webglAlignmentError:"webglAlignmentError",wasmWidthOrHeightChange:"wasmWidthOrHeightChange",mseWidthOrHeightChange:"mseWidthOrHeightChange",wcsWidthOrHeightChange:"wcsWidthOrHeightChange",widthOrHeightChange:"widthOrHeightChange",tallWebsocketClosedByError:"tallWebsocketClosedByError",flvDemuxBufferSizeTooLarge:"flvDemuxBufferSizeTooLarge",wasmDecodeVideoNoResponseError:"wasmDecodeVideoNoResponseError",audioChannelError:"audioChannelError",simdH264DecodeVideoWidthIsTooLarge:"simdH264DecodeVideoWidthIsTooLarge",simdDecodeError:"simdDecodeError",webglContextLostError:"webglContextLostError",videoElementPlayingFailed:"videoElementPlayingFailed",videoElementPlayingFailedForWebrtc:"videoElementPlayingFailedForWebrtc",decoderWorkerInitError:"decoderWorkerInitError",videoInfoError:"videoInfoError",videoCodecIdError:"videoCodecIdError",streamEnd:be,websocket1006Error:"websocket1006Error",delayTimeout:ge,loadingTimeout:ye,networkDelayTimeout:Ue,aliyunRtcError:"aliyunRtcError",...{talkStreamError:"talkStreamError",talkStreamClose:"talkStreamClose"}},ke=1,Ce=7,De=12,Ie=99,Le="H264(AVC)",Fe="H265(HEVC)",Pe=10,Me=7,ze=8,Re=2,Ne=7,Ge=8,Oe=5,He=1,Ve=5,$e=6,We=7,Ye=8,qe=14,je=19,Ke=20,Xe=21,Ze=32,Je=32,Qe=33,et=33,tt=34,it=34,rt=39,st=39,at=40,nt="key",ot="delta",dt='video/mp4; codecs="avc1.64002A"',lt='video/mp4; codecs="hev1.1.6.L123.b0"',ct='video/mp4;codecs="hev1.1.6.L120.90"',ut='video/mp4;codecs="hev1.2.4.L120.90"',ht='video/mp4;codecs="hev1.3.E.L120.90"',ft='video/mp4;codecs="hev1.4.10.L120.90"',pt="ended",_t="open",mt="closed",gt="sourceclose",yt="sourceopen",bt="sourceended",vt="avc",St="hevc",wt="AbortError",At=0,Bt=1,xt=1,Ut="idle",Et="buffering",Tt="complete",kt=1,Ct=2,Dt=128,It=0,Lt=1,Ft=3,Pt=16;var Mt="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function zt(e,t){return e(t={exports:{}},t.exports),t.exports}zt((function(e){!function(){var t="undefined"!=typeof window&&void 0!==window.document?window.document:{},i=e.exports,r=function(){for(var e,i=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],r=0,s=i.length,a={};r<s;r++)if((e=i[r])&&e[1]in t){for(r=0;r<e.length;r++)a[i[0][r]]=e[r];return a}return!1}(),s={change:r.fullscreenchange,error:r.fullscreenerror},a={request:function(e,i){return new Promise(function(s,a){var n=function(){this.off("change",n),s()}.bind(this);this.on("change",n);var o=(e=e||t.documentElement)[r.requestFullscreen](i);o instanceof Promise&&o.then(n).catch(a)}.bind(this))},exit:function(){return new Promise(function(e,i){if(this.isFullscreen){var s=function(){this.off("change",s),e()}.bind(this);this.on("change",s);var a=t[r.exitFullscreen]();a instanceof Promise&&a.then(s).catch(i)}else e()}.bind(this))},toggle:function(e,t){return this.isFullscreen?this.exit():this.request(e,t)},onchange:function(e){this.on("change",e)},onerror:function(e){this.on("error",e)},on:function(e,i){var r=s[e];r&&t.addEventListener(r,i,!1)},off:function(e,i){var r=s[e];r&&t.removeEventListener(r,i,!1)},raw:r};r?(Object.defineProperties(a,{isFullscreen:{get:function(){return Boolean(t[r.fullscreenElement])}},element:{enumerable:!0,get:function(){return t[r.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return Boolean(t[r.fullscreenEnabled])}}}),i?e.exports=a:window.screenfull=a):i?e.exports={isEnabled:!1}:window.screenfull={isEnabled:!1}}()})).isEnabled;class Rt{constructor(e){this._buffer=e,this._buffer_index=0,this._total_bytes=e.byteLength,this._total_bits=8*e.byteLength,this._current_word=0,this._current_word_bits_left=0}destroy(){this._buffer=null}_fillCurrentWord(){let e=this._total_bytes-this._buffer_index;if(e<=0)return void console.error("ExpGolomb: _fillCurrentWord() but no bytes available",this._total_bytes,this._buffer_index);let t=Math.min(4,e),i=new Uint8Array(4);i.set(this._buffer.subarray(this._buffer_index,this._buffer_index+t)),this._current_word=new DataView(i.buffer).getUint32(0,!1),this._buffer_index+=t,this._current_word_bits_left=8*t}readBits(e){if(e>32&&console.error("ExpGolomb: readBits() bits exceeded max 32bits!"),e<=this._current_word_bits_left){let t=this._current_word>>>32-e;return this._current_word<<=e,this._current_word_bits_left-=e,t}let t=this._current_word_bits_left?this._current_word:0;t>>>=32-this._current_word_bits_left;let i=e-this._current_word_bits_left;this._fillCurrentWord();let r=Math.min(i,this._current_word_bits_left),s=this._current_word>>>32-r;return this._current_word<<=r,this._current_word_bits_left-=r,t=t<<r|s,t}readBool(){return 1===this.readBits(1)}readByte(){return this.readBits(8)}_skipLeadingZero(){let e;for(e=0;e<this._current_word_bits_left;e++)if(0!=(this._current_word&2147483648>>>e))return this._current_word<<=e,this._current_word_bits_left-=e,e;return this._fillCurrentWord(),e+this._skipLeadingZero()}readUEG(){let e=this._skipLeadingZero();return this.readBits(e+1)-1}readSEG(){let e=this.readUEG();return 1&e?e+1>>>1:-1*(e>>>1)}}const Nt=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350,-1,-1,-1],Gt=Nt,Ot=Nt;function Ht(e){let{profile:t,sampleRate:i,channel:r}=e;return new Uint8Array([175,0,t<<3|(14&i)>>1,(1&i)<<7|r<<3])}function Vt(e){return $t(e)&&e[1]===At}function $t(e){return e[0]>>4===Pe}const Wt=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350];function Yt(e){let t=new Uint8Array(e),i=null,r=0,s=0,a=0,n=null;if(r=s=t[0]>>>3,a=(7&t[0])<<1|t[1]>>>7,a<0||a>=Wt.length)return void console.error("Flv: AAC invalid sampling frequency index!");let o=Wt[a],d=(120&t[1])>>>3;if(d<0||d>=8)return void console.log("Flv: AAC invalid channel configuration");5===r&&(n=(7&t[1])<<1|t[2]>>>7,t[2]);let l=self.navigator.userAgent.toLowerCase();return-1!==l.indexOf("firefox")?a>=6?(r=5,i=new Array(4),n=a-3):(r=2,i=new Array(2),n=a):-1!==l.indexOf("android")?(r=2,i=new Array(2),n=a):(r=5,n=a,i=new Array(4),a>=6?n=a-3:1===d&&(r=2,i=new Array(2),n=a)),i[0]=r<<3,i[0]|=(15&a)>>>1,i[1]=(15&a)<<7,i[1]|=(15&d)<<3,5===r&&(i[1]|=(15&n)>>>1,i[2]=(1&n)<<7,i[2]|=8,i[3]=0),{audioType:"aac",config:i,sampleRate:o,channelCount:d,objectType:r,codec:"mp4a.40."+r,originalCodec:"mp4a.40."+s}}class qt{constructor(e){this.data_=e,this.eof_flag_=!1,this.current_syncword_offset_=this.findNextSyncwordOffset(0),this.eof_flag_&&console.error("Could not found ADTS syncword until payload end")}findNextSyncwordOffset(e){let t=e,i=this.data_;for(;;){if(t+7>=i.byteLength)return this.eof_flag_=!0,i.byteLength;if(4095===(i[t+0]<<8|i[t+1])>>>4)return t;t++}}readNextAACFrame(){let e=this.data_,t=null;for(;null==t&&!this.eof_flag_;){let i=this.current_syncword_offset_,r=(8&e[i+1])>>>3,s=(6&e[i+1])>>>1,a=1&e[i+1],n=(192&e[i+2])>>>6,o=(60&e[i+2])>>>2,d=(1&e[i+2])<<2|(192&e[i+3])>>>6,l=(3&e[i+3])<<11|e[i+4]<<3|(224&e[i+5])>>>5;if(e[i+6],i+l>this.data_.byteLength){this.eof_flag_=!0,this.has_last_incomplete_data=!0;break}let c=1===a?7:9,u=l-c;i+=c;let h=this.findNextSyncwordOffset(i+u);if(this.current_syncword_offset_=h,0!==r&&1!==r||0!==s)continue;let f=e.subarray(i,i+u);t={},t.audio_object_type=n+1,t.sampling_freq_index=o,t.sampling_frequency=Gt[o],t.channel_config=d,t.data=f}return t}hasIncompleteData(){return this.has_last_incomplete_data}getIncompleteData(){return this.has_last_incomplete_data?this.data_.subarray(this.current_syncword_offset_):null}}class jt{constructor(e){this.data_=e,this.eof_flag_=!1,this.current_syncword_offset_=this.findNextSyncwordOffset(0),this.eof_flag_&&console.error("Could not found ADTS syncword until payload end")}findNextSyncwordOffset(e){let t=e,i=this.data_;for(;;){if(t+1>=i.byteLength)return this.eof_flag_=!0,i.byteLength;if(695===(i[t+0]<<3|i[t+1]>>>5))return t;t++}}getLATMValue(e){let t=e.readBits(2),i=0;for(let r=0;r<=t;r++)i<<=8,i|=e.readByte();return i}readNextAACFrame(e){let t=this.data_,i=null;for(;null==i&&!this.eof_flag_;){let r=this.current_syncword_offset_,s=(31&t[r+1])<<8|t[r+2];if(r+3+s>=this.data_.byteLength){this.eof_flag_=!0,this.has_last_incomplete_data=!0;break}let a=new Rt(t.subarray(r+3,r+3+s)),n=null;if(a.readBool()){if(null==e){console.warn("StreamMuxConfig Missing"),this.current_syncword_offset_=this.findNextSyncwordOffset(r+3+s),a.destroy();continue}n=e}else{let e=a.readBool();if(e&&a.readBool()){console.error("audioMuxVersionA is Not Supported"),a.destroy();break}if(e&&this.getLATMValue(a),!a.readBool()){console.error("allStreamsSameTimeFraming zero is Not Supported"),a.destroy();break}if(0!==a.readBits(6)){console.error("more than 2 numSubFrames Not Supported"),a.destroy();break}if(0!==a.readBits(4)){console.error("more than 2 numProgram Not Supported"),a.destroy();break}if(0!==a.readBits(3)){console.error("more than 2 numLayer Not Supported"),a.destroy();break}let t=e?this.getLATMValue(a):0,i=a.readBits(5);t-=5;let r=a.readBits(4);t-=4;let s=a.readBits(4);t-=4,a.readBits(3),t-=3,t>0&&a.readBits(t);let o=a.readBits(3);if(0!==o){console.error(`frameLengthType = ${o}. Only frameLengthType = 0 Supported`),a.destroy();break}a.readByte();let d=a.readBool();if(d)if(e)this.getLATMValue(a);else{let e=0;for(;;){e<<=8;let t=a.readBool();if(e+=a.readByte(),!t)break}console.log(e)}a.readBool()&&a.readByte(),n={},n.audio_object_type=i,n.sampling_freq_index=r,n.sampling_frequency=Gt[n.sampling_freq_index],n.channel_config=s,n.other_data_present=d}let o=0;for(;;){let e=a.readByte();if(o+=e,255!==e)break}let d=new Uint8Array(o);for(let e=0;e<o;e++)d[e]=a.readByte();i={},i.audio_object_type=n.audio_object_type,i.sampling_freq_index=n.sampling_freq_index,i.sampling_frequency=Gt[n.sampling_freq_index],i.channel_config=n.channel_config,i.other_data_present=n.other_data_present,i.data=d,this.current_syncword_offset_=this.findNextSyncwordOffset(r+3+s)}return i}hasIncompleteData(){return this.has_last_incomplete_data}getIncompleteData(){return this.has_last_incomplete_data?this.data_.subarray(this.current_syncword_offset_):null}}function Kt(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(e[t]<<24>>>0)+(e[t+1]<<16)+(e[t+2]<<8)+(e[t+3]||0)}function Xt(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4;if(e.length<4)return;const i=e.length,r=[];let s,a=0;for(;a+t<i;)if(s=Kt(e,a),3===t&&(s>>>=8),a+=t,s){if(a+s>i)break;r.push(e.subarray(a,a+s)),a+=s}return r}function Zt(e){const t=e.byteLength,i=new Uint8Array(4);i[0]=t>>>24&255,i[1]=t>>>16&255,i[2]=t>>>8&255,i[3]=255&t;const r=new Uint8Array(t+4);return r.set(i,0),r.set(e,4),r}function Jt(e,t){let i=null;return t?e.length>=28&&(i=1+(3&e[26])):e.length>=12&&(i=1+(3&e[9])),i}function Qt(){return(new Date).getTime()}function ei(e,t,i){return Math.max(Math.min(e,Math.max(t,i)),Math.min(t,i))}function ti(){return performance&&"function"==typeof performance.now?performance.now():Date.now()}function ii(e){let t=0,i=ti();return r=>{if(s=r,"[object Number]"!==Object.prototype.toString.call(s))return;var s;t+=r;const a=ti(),n=a-i;n>=1e3&&(e(t/n*1e3),i=a,t=0)}}function ri(){const e=window.navigator.userAgent.toLowerCase();return/firefox/i.test(e)}function si(){let e=!1;return"MediaSource"in self&&(self.MediaSource.isTypeSupported(lt)||self.MediaSource.isTypeSupported(ct)||self.MediaSource.isTypeSupported(ut)||self.MediaSource.isTypeSupported(ht)||self.MediaSource.isTypeSupported(ft))&&(e=!0),e}function ai(e){return null==e}function ni(e){return!ai(e)}function oi(e){return"function"==typeof e}function di(e){let t=null,i=31&e[0];return i!==Ne&&i!==Ge||(t=Le),t||(i=(126&e[0])>>1,i!==Ze&&i!==Qe&&i!==tt||(t=Fe)),t}function li(){return"undefined"!=typeof WritableStream}function ci(e){e.close()}function ui(e,t){t&&(e=e.filter((e=>e.type&&e.type===t)));let i=e[0],r=null,s=1;if(e.length>0){let t=e[1];t&&t.ts-i.ts>1e5&&(i=t,s=2)}if(i)for(let a=s;a<e.length;a++){let s=e[a];if(t&&s.type&&s.type!==t&&(s=null),s){if(s.ts-i.ts>=1e3){e[a-1].ts-i.ts<1e3&&(r=a+1)}}}return r}function hi(e){return e.ok&&e.status>=200&&e.status<=299}function fi(){return function(e){let t="";if("object"==typeof e)try{t=JSON.stringify(e),t=JSON.parse(t)}catch(i){t=e}else t=e;return t}(B)}function pi(e){return e[0]>>4===Bt&&e[1]===At}function _i(e){return!0===e||"true"===e}function mi(e){return!0!==e&&"true"!==e}function gi(){return!!(self.Worker&&self.MediaSource&&"canConstructInDedicatedWorker"in self.MediaSource&&!0===self.MediaSource.canConstructInDedicatedWorker)}(()=>{try{if("object"==typeof WebAssembly&&"function"==typeof WebAssembly.instantiate){const e=new WebAssembly.Module(Uint8Array.of(0,97,115,109,1,0,0,0));if(e instanceof WebAssembly.Module)return new WebAssembly.Instance(e)instanceof WebAssembly.Instance}}catch(e){}})();var yi=function(e,t,i,r){return new(i||(i=Promise))((function(s,a){function n(e){try{d(r.next(e))}catch(e){a(e)}}function o(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(n,o)}d((r=r.apply(e,t||[])).next())}))};const bi=Symbol(32),vi=Symbol(16),Si=Symbol(8);class wi{constructor(e){this.g=e,this.consumed=0,e&&(this.need=e.next().value)}setG(e){this.g=e,this.demand(e.next().value,!0)}consume(){this.buffer&&this.consumed&&(this.buffer.copyWithin(0,this.consumed),this.buffer=this.buffer.subarray(0,this.buffer.length-this.consumed),this.consumed=0)}demand(e,t){return t&&this.consume(),this.need=e,this.flush()}read(e){return yi(this,void 0,void 0,(function*(){return this.lastReadPromise&&(yield this.lastReadPromise),this.lastReadPromise=new Promise(((t,i)=>{var r;this.reject=i,this.resolve=e=>{delete this.lastReadPromise,delete this.resolve,delete this.need,t(e)};this.demand(e,!0)||null===(r=this.pull)||void 0===r||r.call(this,e)}))}))}readU32(){return this.read(bi)}readU16(){return this.read(vi)}readU8(){return this.read(Si)}close(){var e;this.g&&this.g.return(),this.buffer&&this.buffer.subarray(0,0),null===(e=this.reject)||void 0===e||e.call(this,new Error("EOF")),delete this.lastReadPromise}flush(){if(!this.buffer||!this.need)return;let e=null;const t=this.buffer.subarray(this.consumed);let i=0;const r=e=>t.length<(i=e);if("number"==typeof this.need){if(r(this.need))return;e=t.subarray(0,i)}else if(this.need===bi){if(r(4))return;e=t[0]<<24|t[1]<<16|t[2]<<8|t[3]}else if(this.need===vi){if(r(2))return;e=t[0]<<8|t[1]}else if(this.need===Si){if(r(1))return;e=t[0]}else if("buffer"in this.need){if("byteOffset"in this.need){if(r(this.need.byteLength-this.need.byteOffset))return;new Uint8Array(this.need.buffer,this.need.byteOffset).set(t.subarray(0,i)),e=this.need}else if(this.g)return void this.g.throw(new Error("Unsupported type"))}else{if(r(this.need.byteLength))return;new Uint8Array(this.need).set(t.subarray(0,i)),e=this.need}return this.consumed+=i,this.g?this.demand(this.g.next(e).value,!0):this.resolve&&this.resolve(e),e}write(e){if(e instanceof Uint8Array?this.malloc(e.length).set(e):"buffer"in e?this.malloc(e.byteLength).set(new Uint8Array(e.buffer,e.byteOffset,e.byteLength)):this.malloc(e.byteLength).set(new Uint8Array(e)),!this.g&&!this.resolve)return new Promise((e=>this.pull=e));this.flush()}writeU32(e){this.malloc(4).set([e>>24&255,e>>16&255,e>>8&255,255&e]),this.flush()}writeU16(e){this.malloc(2).set([e>>8&255,255&e]),this.flush()}writeU8(e){this.malloc(1)[0]=e,this.flush()}malloc(e){if(this.buffer){const t=this.buffer.length,i=t+e;if(i<=this.buffer.buffer.byteLength-this.buffer.byteOffset)this.buffer=new Uint8Array(this.buffer.buffer,this.buffer.byteOffset,i);else{const e=new Uint8Array(i);e.set(this.buffer),this.buffer=e}return this.buffer.subarray(t,i)}return this.buffer=new Uint8Array(e),this.buffer}}wi.U32=bi,wi.U16=vi,wi.U8=Si;class Ai{constructor(e){this.log=function(t){if(e._opt.debug&&e._opt.debugLevel==b){const a=e._opt.debugUuid?`[${e._opt.debugUuid}]`:"";for(var i=arguments.length,r=new Array(i>1?i-1:0),s=1;s<i;s++)r[s-1]=arguments[s];console.log(`JbPro${a}[✅✅✅][${t}]`,...r)}},this.warn=function(t){if(e._opt.debug&&(e._opt.debugLevel==b||e._opt.debugLevel==v)){const a=e._opt.debugUuid?`[${e._opt.debugUuid}]`:"";for(var i=arguments.length,r=new Array(i>1?i-1:0),s=1;s<i;s++)r[s-1]=arguments[s];console.log(`JbPro${a}[❗❗❗][${t}]`,...r)}},this.error=function(t){const i=e._opt.debugUuid?`[${e._opt.debugUuid}]`:"";for(var r=arguments.length,s=new Array(r>1?r-1:0),a=1;a<r;a++)s[a-1]=arguments[a];console.error(`JbPro${i}[❌❌❌][${t}]`,...s)}}}class Bi{static _ebsp2rbsp(e){let t=e,i=t.byteLength,r=new Uint8Array(i),s=0;for(let e=0;e<i;e++)e>=2&&3===t[e]&&0===t[e-1]&&0===t[e-2]||(r[s]=t[e],s++);return new Uint8Array(r.buffer,0,s)}static parseSPS(e){let t=Bi._ebsp2rbsp(e),i=new Rt(t);i.readByte();let r=i.readByte();i.readByte();let s=i.readByte();i.readUEG();let a=Bi.getProfileString(r),n=Bi.getLevelString(s),o=1,d=420,l=[0,420,422,444],c=8;if((100===r||110===r||122===r||244===r||44===r||83===r||86===r||118===r||128===r||138===r||144===r)&&(o=i.readUEG(),3===o&&i.readBits(1),o<=3&&(d=l[o]),c=i.readUEG()+8,i.readUEG(),i.readBits(1),i.readBool())){let e=3!==o?8:12;for(let t=0;t<e;t++)i.readBool()&&(t<6?Bi._skipScalingList(i,16):Bi._skipScalingList(i,64))}i.readUEG();let u=i.readUEG();if(0===u)i.readUEG();else if(1===u){i.readBits(1),i.readSEG(),i.readSEG();let e=i.readUEG();for(let t=0;t<e;t++)i.readSEG()}let h=i.readUEG();i.readBits(1);let f=i.readUEG(),p=i.readUEG(),_=i.readBits(1);0===_&&i.readBits(1),i.readBits(1);let m=0,g=0,y=0,b=0;i.readBool()&&(m=i.readUEG(),g=i.readUEG(),y=i.readUEG(),b=i.readUEG());let v=1,S=1,w=0,A=!0,B=0,x=0;if(i.readBool()){if(i.readBool()){let e=i.readByte(),t=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2],r=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1];e>0&&e<16?(v=t[e-1],S=r[e-1]):255===e&&(v=i.readByte()<<8|i.readByte(),S=i.readByte()<<8|i.readByte())}if(i.readBool()&&i.readBool(),i.readBool()&&(i.readBits(4),i.readBool()&&i.readBits(24)),i.readBool()&&(i.readUEG(),i.readUEG()),i.readBool()){let e=i.readBits(32),t=i.readBits(32);A=i.readBool(),B=t,x=2*e,w=B/x}}let U=1;1===v&&1===S||(U=v/S);let E=0,T=0;if(0===o)E=1,T=2-_;else{E=3===o?1:2,T=(1===o?2:1)*(2-_)}let k=16*(f+1),C=16*(p+1)*(2-_);k-=(m+g)*E,C-=(y+b)*T;let D=Math.ceil(k*U);return i.destroy(),i=null,{profile_string:a,level_string:n,bit_depth:c,ref_frames:h,chroma_format:d,chroma_format_string:Bi.getChromaFormatString(d),frame_rate:{fixed:A,fps:w,fps_den:x,fps_num:B},sar_ratio:{width:v,height:S},codec_size:{width:k,height:C},present_size:{width:D,height:C}}}static parseSPS$2(e){let t=e.subarray(1,4),i="avc1.";for(let e=0;e<3;e++){let r=t[e].toString(16);r.length<2&&(r="0"+r),i+=r}let r=Bi._ebsp2rbsp(e),s=new Rt(r);s.readByte();let a=s.readByte();s.readByte();let n=s.readByte();s.readUEG();let o=Bi.getProfileString(a),d=Bi.getLevelString(n),l=1,c=420,u=[0,420,422,444],h=8,f=8;if((100===a||110===a||122===a||244===a||44===a||83===a||86===a||118===a||128===a||138===a||144===a)&&(l=s.readUEG(),3===l&&s.readBits(1),l<=3&&(c=u[l]),h=s.readUEG()+8,f=s.readUEG()+8,s.readBits(1),s.readBool())){let e=3!==l?8:12;for(let t=0;t<e;t++)s.readBool()&&(t<6?Bi._skipScalingList(s,16):Bi._skipScalingList(s,64))}s.readUEG();let p=s.readUEG();if(0===p)s.readUEG();else if(1===p){s.readBits(1),s.readSEG(),s.readSEG();let e=s.readUEG();for(let t=0;t<e;t++)s.readSEG()}let _=s.readUEG();s.readBits(1);let m=s.readUEG(),g=s.readUEG(),y=s.readBits(1);0===y&&s.readBits(1),s.readBits(1);let b=0,v=0,S=0,w=0;s.readBool()&&(b=s.readUEG(),v=s.readUEG(),S=s.readUEG(),w=s.readUEG());let A=1,B=1,x=0,U=!0,E=0,T=0;if(s.readBool()){if(s.readBool()){let e=s.readByte(),t=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2],i=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1];e>0&&e<16?(A=t[e-1],B=i[e-1]):255===e&&(A=s.readByte()<<8|s.readByte(),B=s.readByte()<<8|s.readByte())}if(s.readBool()&&s.readBool(),s.readBool()&&(s.readBits(4),s.readBool()&&s.readBits(24)),s.readBool()&&(s.readUEG(),s.readUEG()),s.readBool()){let e=s.readBits(32),t=s.readBits(32);U=s.readBool(),E=t,T=2*e,x=E/T}}let k=1;1===A&&1===B||(k=A/B);let C=0,D=0;if(0===l)C=1,D=2-y;else{C=3===l?1:2,D=(1===l?2:1)*(2-y)}let I=16*(m+1),L=16*(g+1)*(2-y);I-=(b+v)*C,L-=(S+w)*D;let F=Math.ceil(I*k);return s.destroy(),s=null,{codec_mimetype:i,profile_idc:a,level_idc:n,profile_string:o,level_string:d,chroma_format_idc:l,bit_depth:h,bit_depth_luma:h,bit_depth_chroma:f,ref_frames:_,chroma_format:c,chroma_format_string:Bi.getChromaFormatString(c),frame_rate:{fixed:U,fps:x,fps_den:T,fps_num:E},sar_ratio:{width:A,height:B},codec_size:{width:I,height:L},present_size:{width:F,height:L}}}static _skipScalingList(e,t){let i=8,r=8,s=0;for(let a=0;a<t;a++)0!==r&&(s=e.readSEG(),r=(i+s+256)%256),i=0===r?i:r}static getProfileString(e){switch(e){case 66:return"Baseline";case 77:return"Main";case 88:return"Extended";case 100:return"High";case 110:return"High10";case 122:return"High422";case 244:return"High444";default:return"Unknown"}}static getLevelString(e){return(e/10).toFixed(1)}static getChromaFormatString(e){switch(e){case 420:return"4:2:0";case 422:return"4:2:2";case 444:return"4:4:4";default:return"Unknown"}}}class xi{constructor(e){this.buffer=e,this.buflen=e.length,this.bufpos=0,this.bufoff=0,this.iserro=!1}read(e){let t=0,i=0;for(;e;){if(e<0||this.bufpos>=this.buflen)return this.iserro=!0,0;this.iserro=!1,i=this.bufoff+e>8?8-this.bufoff:e,t<<=i,t+=this.buffer[this.bufpos]>>8-this.bufoff-i&255>>8-i,this.bufoff+=i,e-=i,8==this.bufoff&&(this.bufpos++,this.bufoff=0)}return t}look(e){let t=this.bufpos,i=this.bufoff,r=this.read(e);return this.bufpos=t,this.bufoff=i,r}read_golomb(){let e;for(e=0;0===this.read(1)&&!this.iserro;e++);return(1<<e)+this.read(e)-1}}function Ui(e){const t={};let i=function(){let e=new ArrayBuffer(2);return new DataView(e).setInt16(0,256,!0),256===new Int16Array(e)[0]}();const r=new DataView(e.buffer);let s=r.getUint8(0),a=r.getUint8(1);if(r.getUint8(2),r.getUint8(3),1!==s||0===a)return{};const n=1+(3&r.getUint8(4));if(3!==n&&4!==n)return{};let o=31&r.getUint8(5);if(0===o)return{};let d=6;for(let s=0;s<o;s++){let a=r.getUint16(d,!i);if(d+=2,0===a)continue;let n=new Uint8Array(e.buffer,d,a);d+=a;let o=Bi.parseSPS(n);if(0!==s)continue;t.sps=n,t.timescale=1e3,t.codecWidth=o.codec_size.width,t.codecHeight=o.codec_size.height,t.presentWidth=o.present_size.width,t.presentHeight=o.present_size.height,t.profile=o.profile_string,t.level=o.level_string,t.bitDepth=o.bit_depth,t.chromaFormat=o.chroma_format,t.sarRatio=o.sar_ratio,t.frameRate=o.frame_rate,!1!==o.frame_rate.fixed&&0!==o.frame_rate.fps_num&&0!==o.frame_rate.fps_den||(t.frameRate={fixed:!0,fps:25,fps_num:25e3,fps_den:1e3});let l=t.frameRate.fps_den,c=t.frameRate.fps_num;t.refSampleDuration=t.timescale*(l/c);let u=n.subarray(1,4),h="avc1.";for(let e=0;e<3;e++){let t=u[e].toString(16);t.length<2&&(t="0"+t),h+=t}t.codec=h}let l=r.getUint8(d);if(0===l)return{};d++;for(let s=0;s<l;s++){let s=r.getUint16(d,!i);if(d+=2,0===s)continue;let a=new Uint8Array(e.buffer,d,s);d+=s,t.pps=a}if(t.videoType=vt,t.sps){const e=t.sps.byteLength,i=new Uint8Array([e>>>24&255,e>>>16&255,e>>>8&255,255&e]),r=new Uint8Array(e+4);r.set(i,0),r.set(t.sps,4),t.sps=r}if(t.pps){const e=t.pps.byteLength,i=new Uint8Array([e>>>24&255,e>>>16&255,e>>>8&255,255&e]),r=new Uint8Array(e+4);r.set(i,0),r.set(t.pps,4),t.pps=r}return t}function Ei(e){let{sps:t,pps:i}=e;const r=[23,0,0,0,0,1,66,0,30,255];r[0]=23,r[6]=t[1],r[7]=t[2],r[8]=t[3],r[10]=225,r[11]=t.byteLength>>8&255,r[12]=255&t.byteLength,r.push(...t,1,i.byteLength>>8&255,255&i.byteLength,...i);return new Uint8Array(r)}function Ti(e){let{sps:t,pps:i}=e,r=8+t.byteLength+1+2+i.byteLength,s=!1;const a=Bi.parseSPS$2(t);66!==t[3]&&77!==t[3]&&88!==t[3]&&(s=!0,r+=4);let n=new Uint8Array(r);n[0]=1,n[1]=t[1],n[2]=t[2],n[3]=t[3],n[4]=255,n[5]=225;let o=t.byteLength;n[6]=o>>>8,n[7]=255&o;let d=8;n.set(t,8),d+=o,n[d]=1;let l=i.byteLength;n[d+1]=l>>>8,n[d+2]=255&l,n.set(i,d+3),d+=3+l,s&&(n[d]=252|a.chroma_format_idc,n[d+1]=248|a.bit_depth_luma-8,n[d+2]=248|a.bit_depth_chroma-8,n[d+3]=0,d+=4);const c=[23,0,0,0,0],u=new Uint8Array(c.length+n.byteLength);return u.set(c,0),u.set(n,c.length),u}function ki(e,t){let i=[];i[0]=t?23:39,i[1]=1,i[2]=0,i[3]=0,i[4]=0,i[5]=e.byteLength>>24&255,i[6]=e.byteLength>>16&255,i[7]=e.byteLength>>8&255,i[8]=255&e.byteLength;const r=new Uint8Array(i.length+e.byteLength);return r.set(i,0),r.set(e,i.length),r}function Ci(e,t){let i=[];i[0]=t?23:39,i[1]=1,i[2]=0,i[3]=0,i[4]=0;const r=new Uint8Array(i.length+e.byteLength);return r.set(i,0),r.set(e,i.length),r}function Di(e){return 31&e[0]}function Ii(e){return e===$e}function Li(e){return!function(e){return e===Ne||e===Ge}(e)&&!Ii(e)}function Fi(e){return e===Oe}function Pi(e){if(0===e.length)return!1;const t=Di(e[0]);for(let i=1;i<e.length;i++)if(t!==Di(e[i]))return!1;return!0}class Mi{constructor(e){this.data=e,this.eofFlag=!1,this.currentStartcodeOffset=this.findNextStartCodeOffset(0),this.eofFlag&&console.error("Could not find H264 startcode until payload end!")}findNextStartCodeOffset(e){let t=e,i=this.data;for(;;){if(t+3>=i.byteLength)return this.eofFlag=!0,i.byteLength;let e=i[t+0]<<24|i[t+1]<<16|i[t+2]<<8|i[t+3],r=i[t+0]<<16|i[t+1]<<8|i[t+2];if(1===e||1===r)return t;t++}}readNextNaluPayload(){let e=this.data,t=null;for(;null==t&&!this.eofFlag;){let i=this.currentStartcodeOffset;i+=1===(e[i]<<24|e[i+1]<<16|e[i+2]<<8|e[i+3])?4:3;let r=31&e[i],s=(128&e[i])>>>7,a=this.findNextStartCodeOffset(i);this.currentStartcodeOffset=a,r>=qe||0===s&&(t={type:r,data:e.subarray(i,a)})}return t}}class zi{constructor(e){let t=e.data.byteLength;this.type=e.type,this.data=new Uint8Array(4+t),new DataView(this.data.buffer).setUint32(0,t),this.data.set(e.data,4)}}const Ri=e=>{let t=e,i=t.byteLength,r=new Uint8Array(i),s=0;for(let e=0;e<i;e++)e>=2&&3===t[e]&&0===t[e-1]&&0===t[e-2]||(r[s]=t[e],s++);return new Uint8Array(r.buffer,0,s)},Ni=e=>{switch(e){case 0:return"4:0:0";case 1:return"4:2:0";case 2:return"4:2:2";case 3:return"4:4:4";default:return"Unknown"}};class Gi{static _ebsp2rbsp(e){let t=e,i=t.byteLength,r=new Uint8Array(i),s=0;for(let e=0;e<i;e++)e>=2&&3===t[e]&&0===t[e-1]&&0===t[e-2]||(r[s]=t[e],s++);return new Uint8Array(r.buffer,0,s)}static parseVPS(e){let t=Gi._ebsp2rbsp(e),i=new Rt(t);return i.readByte(),i.readByte(),i.readBits(4),i.readBits(2),i.readBits(6),{num_temporal_layers:i.readBits(3)+1,temporal_id_nested:i.readBool()}}static parseSPS(e){let t=Gi._ebsp2rbsp(e),i=new Rt(t);i.readByte(),i.readByte();let r=0,s=0,a=0,n=0;i.readBits(4);let o=i.readBits(3);i.readBool();let d=i.readBits(2),l=i.readBool(),c=i.readBits(5),u=i.readByte(),h=i.readByte(),f=i.readByte(),p=i.readByte(),_=i.readByte(),m=i.readByte(),g=i.readByte(),y=i.readByte(),b=i.readByte(),v=i.readByte(),S=i.readByte(),w=[],A=[];for(let e=0;e<o;e++)w.push(i.readBool()),A.push(i.readBool());if(o>0)for(let e=o;e<8;e++)i.readBits(2);for(let e=0;e<o;e++)w[e]&&(i.readByte(),i.readByte(),i.readByte(),i.readByte(),i.readByte(),i.readByte(),i.readByte(),i.readByte(),i.readByte(),i.readByte(),i.readByte()),A[e]&&i.readByte();i.readUEG();let B=i.readUEG();3==B&&i.readBits(1);let x=i.readUEG(),U=i.readUEG();i.readBool()&&(r+=i.readUEG(),s+=i.readUEG(),a+=i.readUEG(),n+=i.readUEG());let E=i.readUEG(),T=i.readUEG(),k=i.readUEG();for(let e=i.readBool()?0:o;e<=o;e++)i.readUEG(),i.readUEG(),i.readUEG();if(i.readUEG(),i.readUEG(),i.readUEG(),i.readUEG(),i.readUEG(),i.readUEG(),i.readBool()){if(i.readBool())for(let e=0;e<4;e++)for(let t=0;t<(3===e?2:6);t++){if(i.readBool()){let t=Math.min(64,1<<4+(e<<1));e>1&&i.readSEG();for(let e=0;e<t;e++)i.readSEG()}else i.readUEG()}}i.readBool(),i.readBool(),i.readBool()&&(i.readByte(),i.readUEG(),i.readUEG(),i.readBool());let C=i.readUEG(),D=0;for(let e=0;e<C;e++){let t=!1;if(0!==e&&(t=i.readBool()),t){e===C&&i.readUEG(),i.readBool(),i.readUEG();let t=0;for(let e=0;e<=D;e++){let e=i.readBool(),r=!1;e||(r=i.readBool()),(e||r)&&t++}D=t}else{let e=i.readUEG(),t=i.readUEG();D=e+t;for(let t=0;t<e;t++)i.readUEG(),i.readBool();for(let e=0;e<t;e++)i.readUEG(),i.readBool()}}if(i.readBool()){let e=i.readUEG();for(let t=0;t<e;t++){for(let e=0;e<k+4;e++)i.readBits(1);i.readBits(1)}}let I=!1,L=0,F=1,P=1,M=!1,z=1,R=1;if(i.readBool(),i.readBool(),i.readBool()){if(i.readBool()){let e=i.readByte(),t=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2],r=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1];e>0&&e<=16?(F=t[e-1],P=r[e-1]):255===e&&(F=i.readBits(16),P=i.readBits(16))}if(i.readBool()&&i.readBool(),i.readBool()){i.readBits(3),i.readBool(),i.readBool()&&(i.readByte(),i.readByte(),i.readByte())}if(i.readBool()&&(i.readUEG(),i.readUEG()),i.readBool(),i.readBool(),i.readBool(),I=i.readBool(),I&&(i.readUEG(),i.readUEG(),i.readUEG(),i.readUEG()),i.readBool()){if(z=i.readBits(32),R=i.readBits(32),i.readBool()&&i.readUEG(),i.readBool()){let e=!1,t=!1,r=!1;e=i.readBool(),t=i.readBool(),(e||t)&&(r=i.readBool(),r&&(i.readByte(),i.readBits(5),i.readBool(),i.readBits(5)),i.readBits(4),i.readBits(4),r&&i.readBits(4),i.readBits(5),i.readBits(5),i.readBits(5));for(let s=0;s<=o;s++){let s=i.readBool();M=s;let a=!0,n=1;s||(a=i.readBool());let o=!1;if(a?i.readUEG():o=i.readBool(),o||(n=i.readUEG()+1),e){for(let e=0;e<n;e++)i.readUEG(),i.readUEG(),r&&(i.readUEG(),i.readUEG());i.readBool()}if(t){for(let e=0;e<n;e++)i.readUEG(),i.readUEG(),r&&(i.readUEG(),i.readUEG());i.readBool()}}}}i.readBool()&&(i.readBool(),i.readBool(),i.readBool(),L=i.readUEG(),i.readUEG(),i.readUEG(),i.readUEG(),i.readUEG())}i.readBool();let N=`hvc1.${c}.1.L${S}.B0`,G=x-(r+s)*(1===B||2===B?2:1),O=U-(a+n)*(1===B?2:1),H=1;return 1!==F&&1!==P&&(H=F/P),i.destroy(),i=null,{codec_mimetype:N,profile_string:Gi.getProfileString(c),level_string:Gi.getLevelString(S),profile_idc:c,bit_depth:E+8,ref_frames:1,chroma_format:B,chroma_format_string:Gi.getChromaFormatString(B),general_level_idc:S,general_profile_space:d,general_tier_flag:l,general_profile_idc:c,general_profile_compatibility_flags_1:u,general_profile_compatibility_flags_2:h,general_profile_compatibility_flags_3:f,general_profile_compatibility_flags_4:p,general_constraint_indicator_flags_1:_,general_constraint_indicator_flags_2:m,general_constraint_indicator_flags_3:g,general_constraint_indicator_flags_4:y,general_constraint_indicator_flags_5:b,general_constraint_indicator_flags_6:v,min_spatial_segmentation_idc:L,constant_frame_rate:0,chroma_format_idc:B,bit_depth_luma_minus8:E,bit_depth_chroma_minus8:T,frame_rate:{fixed:M,fps:R/z,fps_den:z,fps_num:R},sar_ratio:{width:F,height:P},codec_size:{width:G,height:O},present_size:{width:G*H,height:O}}}static parsePPS(e){let t=Gi._ebsp2rbsp(e),i=new Rt(t);i.readByte(),i.readByte(),i.readUEG(),i.readUEG(),i.readBool(),i.readBool(),i.readBits(3),i.readBool(),i.readBool(),i.readUEG(),i.readUEG(),i.readSEG(),i.readBool(),i.readBool(),i.readBool()&&i.readUEG(),i.readSEG(),i.readSEG(),i.readBool(),i.readBool(),i.readBool(),i.readBool();let r=i.readBool(),s=i.readBool(),a=1;return s&&r?a=0:s?a=3:r&&(a=2),{parallelismType:a}}static getChromaFormatString(e){switch(e){case 0:return"4:0:0";case 1:return"4:2:0";case 2:return"4:2:2";case 3:return"4:4:4";default:return"Unknown"}}static getProfileString(e){switch(e){case 1:return"Main";case 2:return"Main10";case 3:return"MainSP";case 4:return"Rext";case 9:return"SCC";default:return"Unknown"}}static getLevelString(e){return(e/30).toFixed(1)}}function Oi(e){let t={codecWidth:0,codecHeight:0,videoType:St,width:0,height:0,profile:0,level:0};e=e.slice(5);do{let i={};if(e.length<23){console.warn("parseHEVCDecoderConfigurationRecord$2",`arrayBuffer.length ${e.length} < 23`);break}if(i.configurationVersion=e[0],1!=i.configurationVersion)break;i.general_profile_space=e[1]>>6&3,i.general_tier_flag=e[1]>>5&1,i.general_profile_idc=31&e[1],i.general_profile_compatibility_flags=e[2]<<24|e[3]<<16|e[4]<<8|e[5],i.general_constraint_indicator_flags=e[6]<<24|e[7]<<16|e[8]<<8|e[9],i.general_constraint_indicator_flags=i.general_constraint_indicator_flags<<16|e[10]<<8|e[11],i.general_level_idc=e[12],i.min_spatial_segmentation_idc=(15&e[13])<<8|e[14],i.parallelismType=3&e[15],i.chromaFormat=3&e[16],i.bitDepthLumaMinus8=7&e[17],i.bitDepthChromaMinus8=7&e[18],i.avgFrameRate=e[19]<<8|e[20],i.constantFrameRate=e[21]>>6&3,i.numTemporalLayers=e[21]>>3&7,i.temporalIdNested=e[21]>>2&1,i.lengthSizeMinusOne=3&e[21];let r=e[22],s=e.slice(23);for(let e=0;e<r&&!(s.length<3);e++){let e=63&s[0],r=s[1]<<8|s[2];s=s.slice(3);for(let a=0;a<r&&!(s.length<2);a++){let r=s[0]<<8|s[1];if(s.length<2+r)break;if(s=s.slice(2),33==e){let e=new Uint8Array(r);e.set(s.slice(0,r),0),i.psps=Vi(e,i),t.profile=i.general_profile_idc,t.level=i.general_level_idc/30,t.width=i.psps.pic_width_in_luma_samples-(i.psps.conf_win_left_offset+i.psps.conf_win_right_offset),t.height=i.psps.pic_height_in_luma_samples-(i.psps.conf_win_top_offset+i.psps.conf_win_bottom_offset)}s=s.slice(r)}}}while(0);return t.codecWidth=t.width||1920,t.codecHeight=t.height||1080,t.presentHeight=t.codecHeight,t.presentWidth=t.codecWidth,t.timescale=1e3,t.refSampleDuration=1e3/23976*1e3,t}function Hi(e){const t=e;if(t.length<22)return console.error(`Invalid HEVCDecoderConfigurationRecord, lack of data! ${t.length} < 22`),{};let i={codecWidth:0,codecHeight:0,videoType:St},r=function(){let e=new ArrayBuffer(2);return new DataView(e).setInt16(0,256,!0),256===new Int16Array(e)[0]}(),s=new DataView(t.buffer),a=s.getUint8(0),n=31&s.getUint8(1);if(1!==a||0===n)return console.error(`Invalid HEVCDecoderConfigurationRecord,version is ${a}, hevcProfile is ${n}`),{};let o=1+(3&s.getUint8(21));if(3!==o&&4!==o)return console.error("Invalid HEVCDecoderConfigurationRecord, Strange NaluLengthSizeMinusOne: "+(o-1)),{};let d=s.getUint8(22);for(let e=0,a=23;e<d;e++){let e=63&s.getUint8(a+0),n=s.getUint16(a+1,!r);a+=3;for(let o=0;o<n;o++){let n=s.getUint16(a+0,!r);if(0===o)if(33===e){a+=2;let e=new Uint8Array(t.buffer,a,n),r=Gi.parseSPS(e);i.codecWidth=r.codec_size.width,i.codecHeight=r.codec_size.height,i.presentWidth=r.present_size.width,i.presentHeight=r.present_size.height,i.profile=r.profile_string,i.level=r.level_string,i.bitDepth=r.bit_depth,i.chromaFormat=r.chroma_format,i.sarRatio=r.sar_ratio,i.frameRate=r.frame_rate,!1!==r.frame_rate.fixed&&0!==r.frame_rate.fps_num&&0!==r.frame_rate.fps_den||(i.frameRate={fixed:!0,fps:23.976,fps_num:23976,fps_den:1e3}),i.frameRate.fps_den,i.frameRate.fps_num,i.codec=r.codec_mimetype,a+=n}else a+=2+n;else a+=2+n}}return i.hvcc=new Uint8Array(t),i}function Vi(e,t){let i={},r=e.length,s=[],a=new xi(e);a.read(1),a.read(6),a.read(6),a.read(3);for(let e=2;e<r;e++)e+2<r&&3==a.look(24)?(s.push(a.read(8)),s.push(a.read(8)),e+=2,a.read(8)):s.push(a.read(8));let n=new Uint8Array(s),o=new xi(n);if(i.sps_video_parameter_set_id=o.read(4),i.sps_max_sub_layers_minus1=o.read(3),i.sps_temporal_id_nesting_flag=o.read(1),i.profile_tier_level=function(e,t,i){let r={};r.profile_space=e.read(2),r.tier_flag=e.read(1),r.profile_idc=e.read(5),r.profile_compatibility_flags=e.read(32),r.general_progressive_source_flag=e.read(1),r.general_interlaced_source_flag=e.read(1),r.general_non_packed_constraint_flag=e.read(1),r.general_frame_only_constraint_flag=e.read(1),e.read(32),e.read(12),r.level_idc=e.read(8),r.sub_layer_profile_present_flag=[],r.sub_layer_level_present_flag=[];for(let t=0;t<i;t++)r.sub_layer_profile_present_flag[t]=e.read(1),r.sub_layer_level_present_flag[t]=e.read(1);if(i>0)for(let t=i;t<8;t++)e.read(2);r.sub_layer_profile_space=[],r.sub_layer_tier_flag=[],r.sub_layer_profile_idc=[],r.sub_layer_profile_compatibility_flag=[],r.sub_layer_progressive_source_flag=[],r.sub_layer_interlaced_source_flag=[],r.sub_layer_non_packed_constraint_flag=[],r.sub_layer_frame_only_constraint_flag=[],r.sub_layer_level_idc=[];for(let t=0;t<i;t++)r.sub_layer_profile_present_flag[t]&&(r.sub_layer_profile_space[t]=e.read(2),r.sub_layer_tier_flag[t]=e.read(1),r.sub_layer_profile_idc[t]=e.read(5),r.sub_layer_profile_compatibility_flag[t]=e.read(32),r.sub_layer_progressive_source_flag[t]=e.read(1),r.sub_layer_interlaced_source_flag[t]=e.read(1),r.sub_layer_non_packed_constraint_flag[t]=e.read(1),r.sub_layer_frame_only_constraint_flag[t]=e.read(1),e.read(32),e.read(12)),r.sub_layer_level_present_flag[t]?r.sub_layer_level_idc[t]=e.read(8):r.sub_layer_level_idc[t]=1;return r}(o,0,i.sps_max_sub_layers_minus1),i.sps_seq_parameter_set_id=o.read_golomb(),i.chroma_format_idc=o.read_golomb(),3==i.chroma_format_idc?i.separate_colour_plane_flag=o.read(1):i.separate_colour_plane_flag=0,i.pic_width_in_luma_samples=o.read_golomb(),i.pic_height_in_luma_samples=o.read_golomb(),i.conformance_window_flag=o.read(1),i.conformance_window_flag){let e=1+(i.chroma_format_idc<2),t=1+(i.chroma_format_idc<3);i.conf_win_left_offset=o.read_golomb()*t,i.conf_win_right_offset=o.read_golomb()*t,i.conf_win_top_offset=o.read_golomb()*e,i.conf_win_bottom_offset=o.read_golomb()*e}else i.conf_win_left_offset=0,i.conf_win_right_offset=0,i.conf_win_top_offset=0,i.conf_win_bottom_offset=0;return i}function $i(e){let{vps:t,pps:i,sps:r}=e,s={configurationVersion:1};const a=(e=>{let t=Ri(e),i=new Rt(t);return i.readByte(),i.readByte(),i.readBits(4),i.readBits(2),i.readBits(6),{num_temporal_layers:i.readBits(3)+1,temporal_id_nested:i.readBool()}})(t),n=(e=>{let t=Ri(e),i=new Rt(t);i.readByte(),i.readByte();let r=0,s=0,a=0,n=0;i.readBits(4);let o=i.readBits(3);i.readBool();let d=i.readBits(2),l=i.readBool(),c=i.readBits(5),u=i.readByte(),h=i.readByte(),f=i.readByte(),p=i.readByte(),_=i.readByte(),m=i.readByte(),g=i.readByte(),y=i.readByte(),b=i.readByte(),v=i.readByte(),S=i.readByte(),w=[],A=[];for(let e=0;e<o;e++)w.push(i.readBool()),A.push(i.readBool());if(o>0)for(let e=o;e<8;e++)i.readBits(2);for(let e=0;e<o;e++)w[e]&&(i.readByte(),i.readByte(),i.readByte(),i.readByte(),i.readByte(),i.readByte(),i.readByte(),i.readByte(),i.readByte(),i.readByte(),i.readByte()),w[e]&&i.readByte();i.readUEG();let B=i.readUEG();3==B&&i.readBits(1);let x=i.readUEG(),U=i.readUEG();i.readBool()&&(r+=i.readUEG(),s+=i.readUEG(),a+=i.readUEG(),n+=i.readUEG());let E=i.readUEG(),T=i.readUEG(),k=i.readUEG();for(let e=i.readBool()?0:o;e<=o;e++)i.readUEG(),i.readUEG(),i.readUEG();if(i.readUEG(),i.readUEG(),i.readUEG(),i.readUEG(),i.readUEG(),i.readUEG(),i.readBool()&&i.readBool())for(let e=0;e<4;e++)for(let t=0;t<(3===e?2:6);t++)if(i.readBool()){let t=Math.min(64,1<<4+(e<<1));e>1&&i.readSEG();for(let e=0;e<t;e++)i.readSEG()}else i.readUEG();i.readBool(),i.readBool(),i.readBool()&&(i.readByte(),i.readUEG(),i.readUEG(),i.readBool());let C=i.readUEG(),D=0;for(let e=0;e<C;e++){let t=!1;if(0!==e&&(t=i.readBool()),t){e===C&&i.readUEG(),i.readBool(),i.readUEG();let t=0;for(let e=0;e<=D;e++){let e=i.readBool(),r=!1;e||(r=i.readBool()),(e||r)&&t++}D=t}else{let e=i.readUEG(),t=i.readUEG();D=e+t;for(let t=0;t<e;t++)i.readUEG(),i.readBool();for(let e=0;e<t;e++)i.readUEG(),i.readBool()}}if(i.readBool()){let e=i.readUEG();for(let t=0;t<e;t++){for(let e=0;e<k+4;e++)i.readBits(1);i.readBits(1)}}let I=!1,L=0,F=1,P=1,M=!1,z=1,R=1;if(i.readBool(),i.readBool(),i.readBool()){if(i.readBool()){let e=i.readByte(),t=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2],r=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1];e>0&&e<16?(F=t[e-1],P=r[e-1]):255===e&&(F=i.readBits(16),P=i.readBits(16))}if(i.readBool()&&i.readBool(),i.readBool()&&(i.readBits(3),i.readBool(),i.readBool()&&(i.readByte(),i.readByte(),i.readByte())),i.readBool()&&(i.readUEG(),i.readUEG()),i.readBool(),i.readBool(),i.readBool(),I=i.readBool(),I&&(r+=i.readUEG(),s+=i.readUEG(),a+=i.readUEG(),n+=i.readUEG()),i.readBool()&&(z=i.readBits(32),R=i.readBits(32),i.readBool()&&(i.readUEG(),i.readBool()))){let e=!1,t=!1,r=!1;e=i.readBool(),t=i.readBool(),(e||t)&&(r=i.readBool(),r&&(i.readByte(),i.readBits(5),i.readBool(),i.readBits(5)),i.readBits(4),i.readBits(4),r&&i.readBits(4),i.readBits(5),i.readBits(5),i.readBits(5));for(let s=0;s<=o;s++){let s=i.readBool();M=s;let a=!1,n=1;s||(a=i.readBool());let o=!1;if(a?i.readSEG():o=i.readBool(),o||(cpbcnt=i.readUEG()+1),e)for(let e=0;e<n;e++)i.readUEG(),i.readUEG(),r&&(i.readUEG(),i.readUEG());if(t)for(let e=0;e<n;e++)i.readUEG(),i.readUEG(),r&&(i.readUEG(),i.readUEG())}}i.readBool()&&(i.readBool(),i.readBool(),i.readBool(),L=i.readUEG(),i.readUEG(),i.readUEG(),i.readUEG(),i.readUEG())}i.readBool();let N=`hvc1.${c}.1.L${S}.B0`,G=x,O=U,H=1;return 1!==F&&1!==P&&(H=F/P),i.destroy(),i=null,{codec_mimetype:N,level_string:(V=S,(V/30).toFixed(1)),profile_idc:c,bit_depth:E+8,ref_frames:1,chroma_format:B,chroma_format_string:Ni(B),general_level_idc:S,general_profile_space:d,general_tier_flag:l,general_profile_idc:c,general_profile_compatibility_flags_1:u,general_profile_compatibility_flags_2:h,general_profile_compatibility_flags_3:f,general_profile_compatibility_flags_4:p,general_constraint_indicator_flags_1:_,general_constraint_indicator_flags_2:m,general_constraint_indicator_flags_3:g,general_constraint_indicator_flags_4:y,general_constraint_indicator_flags_5:b,general_constraint_indicator_flags_6:v,min_spatial_segmentation_idc:L,constant_frame_rate:0,chroma_format_idc:B,bit_depth_luma_minus8:E,bit_depth_chroma_minus8:T,frame_rate:{fixed:M,fps:R/z,fps_den:z,fps_num:R},sar_ratio:{width:F,height:P},codec_size:{width:G,height:O},present_size:{width:G*H,height:O}};var V})(r),o=(e=>{let t=Ri(e),i=new Rt(t);i.readByte(),i.readByte(),i.readUEG(),i.readUEG(),i.readBool(),i.readBool(),i.readBits(3),i.readBool(),i.readBool(),i.readUEG(),i.readUEG(),i.readSEG(),i.readBool(),i.readBool(),i.readBool()&&i.readUEG(),i.readSEG(),i.readSEG(),i.readBool(),i.readBool(),i.readBool(),i.readBool();let r=i.readBool(),s=i.readBool(),a=1;return s&&r?a=0:s?a=3:r&&(a=2),{parallelismType:a}})(i);s=Object.assign(s,a,n,o);let d=23+(5+t.byteLength)+(5+r.byteLength)+(5+i.byteLength),l=new Uint8Array(d);l[0]=1,l[1]=(3&s.general_profile_space)<<6|(s.general_tier_flag?1:0)<<5|31&s.general_profile_idc,l[2]=s.general_profile_compatibility_flags_1||0,l[3]=s.general_profile_compatibility_flags_2||0,l[4]=s.general_profile_compatibility_flags_3||0,l[5]=s.general_profile_compatibility_flags_4||0,l[6]=s.general_constraint_indicator_flags_1||0,l[7]=s.general_constraint_indicator_flags_2||0,l[8]=s.general_constraint_indicator_flags_3||0,l[9]=s.general_constraint_indicator_flags_4||0,l[10]=s.general_constraint_indicator_flags_5||0,l[11]=s.general_constraint_indicator_flags_6||0,l[12]=60,l[13]=240|(3840&s.min_spatial_segmentation_idc)>>8,l[14]=255&s.min_spatial_segmentation_idc,l[15]=252|3&s.parallelismType,l[16]=252|3&s.chroma_format_idc,l[17]=248|7&s.bit_depth_luma_minus8,l[18]=248|7&s.bit_depth_chroma_minus8,l[19]=0,l[20]=0,l[21]=(3&s.constant_frame_rate)<<6|(7&s.num_temporal_layers)<<3|(s.temporal_id_nested?1:0)<<2|3,l[22]=3,l[23]=128|Ze,l[24]=0,l[25]=1,l[26]=(65280&t.byteLength)>>8,l[27]=(255&t.byteLength)>>0,l.set(t,28),l[23+(5+t.byteLength)+0]=128|Qe,l[23+(5+t.byteLength)+1]=0,l[23+(5+t.byteLength)+2]=1,l[23+(5+t.byteLength)+3]=(65280&r.byteLength)>>8,l[23+(5+t.byteLength)+4]=(255&r.byteLength)>>0,l.set(r,23+(5+t.byteLength)+5),l[23+(5+t.byteLength+5+r.byteLength)+0]=128|tt,l[23+(5+t.byteLength+5+r.byteLength)+1]=0,l[23+(5+t.byteLength+5+r.byteLength)+2]=1,l[23+(5+t.byteLength+5+r.byteLength)+3]=(65280&i.byteLength)>>8,l[23+(5+t.byteLength+5+r.byteLength)+4]=(255&i.byteLength)>>0,l.set(i,23+(5+t.byteLength+5+r.byteLength)+5);const c=[28,0,0,0,0],u=new Uint8Array(c.length+l.byteLength);return u.set(c,0),u.set(l,c.length),u}function Wi(e,t){let i=[];i[0]=t?28:44,i[1]=1,i[2]=0,i[3]=0,i[4]=0,i[5]=e.byteLength>>24&255,i[6]=e.byteLength>>16&255,i[7]=e.byteLength>>8&255,i[8]=255&e.byteLength;const r=new Uint8Array(i.length+e.byteLength);return r.set(i,0),r.set(e,i.length),r}function Yi(e,t){let i=[];i[0]=t?28:44,i[1]=1,i[2]=0,i[3]=0,i[4]=0;const r=new Uint8Array(i.length+e.byteLength);return r.set(i,0),r.set(e,i.length),r}function qi(e){return(126&e[0])>>1}function ji(e){return e===rt}function Ki(e){return!function(e){return e>=32&&e<=40}(e)}function Xi(e){return e>=16&&e<=21}function Zi(e){if(0===e.length)return!1;const t=qi(e[0]);for(let i=1;i<e.length;i++)if(t!==qi(e[i]))return!1;return!0}class Ji{constructor(e){this.data=e,this.eofFlag=!1,this.currentStartcodeOffset=this.findNextStartCodeOffset(0),this.eofFlag&&console.error("Could not find H265 startcode until payload end!")}findNextStartCodeOffset(e){let t=e,i=this.data;for(;;){if(t+3>=i.byteLength)return this.eofFlag=!0,i.byteLength;let e=i[t+0]<<24|i[t+1]<<16|i[t+2]<<8|i[t+3],r=i[t+0]<<16|i[t+1]<<8|i[t+2];if(1===e||1===r)return t;t++}}readNextNaluPayload(){let e=this.data,t=null;for(;null==t&&!this.eofFlag;){let i=this.currentStartcodeOffset;i+=1===(e[i]<<24|e[i+1]<<16|e[i+2]<<8|e[i+3])?4:3;let r=e[i]>>1&63,s=(128&e[i])>>>7,a=this.findNextStartCodeOffset(i);this.currentStartcodeOffset=a,0===s&&(t={type:r,data:e.subarray(i,a)})}return t}}class Qi{constructor(e){let t=e.data.byteLength;this.type=e.type,this.data=new Uint8Array(4+t),new DataView(this.data.buffer).setUint32(0,t),this.data.set(e.data,4)}}function er(e){return parseInt(e)===e}function tr(e){if(!er(e.length))return!1;for(var t=0;t<e.length;t++)if(!er(e[t])||e[t]<0||e[t]>255)return!1;return!0}function ir(e,t){if(e.buffer&&"Uint8Array"===e.name)return t&&(e=e.slice?e.slice():Array.prototype.slice.call(e)),e;if(Array.isArray(e)){if(!tr(e))throw new Error("Array contains invalid value: "+e);return new Uint8Array(e)}if(er(e.length)&&tr(e))return new Uint8Array(e);throw new Error("unsupported array-like object")}function rr(e){return new Uint8Array(e)}function sr(e,t,i,r,s){null==r&&null==s||(e=e.slice?e.slice(r,s):Array.prototype.slice.call(e,r,s)),t.set(e,i)}var ar,nr={toBytes:function(e){var t=[],i=0;for(e=encodeURI(e);i<e.length;){var r=e.charCodeAt(i++);37===r?(t.push(parseInt(e.substr(i,2),16)),i+=2):t.push(r)}return ir(t)},fromBytes:function(e){for(var t=[],i=0;i<e.length;){var r=e[i];r<128?(t.push(String.fromCharCode(r)),i++):r>191&&r<224?(t.push(String.fromCharCode((31&r)<<6|63&e[i+1])),i+=2):(t.push(String.fromCharCode((15&r)<<12|(63&e[i+1])<<6|63&e[i+2])),i+=3)}return t.join("")}},or=(ar="0123456789abcdef",{toBytes:function(e){for(var t=[],i=0;i<e.length;i+=2)t.push(parseInt(e.substr(i,2),16));return t},fromBytes:function(e){for(var t=[],i=0;i<e.length;i++){var r=e[i];t.push(ar[(240&r)>>4]+ar[15&r])}return t.join("")}}),dr={16:10,24:12,32:14},lr=[1,2,4,8,16,32,64,128,27,54,108,216,171,77,154,47,94,188,99,198,151,53,106,212,179,125,250,239,197,145],cr=[99,124,119,123,242,107,111,197,48,1,103,43,254,215,171,118,202,130,201,125,250,89,71,240,173,212,162,175,156,164,114,192,183,253,147,38,54,63,247,204,52,165,229,241,113,216,49,21,4,199,35,195,24,150,5,154,7,18,128,226,235,39,178,117,9,131,44,26,27,110,90,160,82,59,214,179,41,227,47,132,83,209,0,237,32,252,177,91,106,203,190,57,74,76,88,207,208,239,170,251,67,77,51,133,69,249,2,127,80,60,159,168,81,163,64,143,146,157,56,245,188,182,218,33,16,255,243,210,205,12,19,236,95,151,68,23,196,167,126,61,100,93,25,115,96,129,79,220,34,42,144,136,70,238,184,20,222,94,11,219,224,50,58,10,73,6,36,92,194,211,172,98,145,149,228,121,231,200,55,109,141,213,78,169,108,86,244,234,101,122,174,8,186,120,37,46,28,166,180,198,232,221,116,31,75,189,139,138,112,62,181,102,72,3,246,14,97,53,87,185,134,193,29,158,225,248,152,17,105,217,142,148,155,30,135,233,206,85,40,223,140,161,137,13,191,230,66,104,65,153,45,15,176,84,187,22],ur=[82,9,106,213,48,54,165,56,191,64,163,158,129,243,215,251,124,227,57,130,155,47,255,135,52,142,67,68,196,222,233,203,84,123,148,50,166,194,35,61,238,76,149,11,66,250,195,78,8,46,161,102,40,217,36,178,118,91,162,73,109,139,209,37,114,248,246,100,134,104,152,22,212,164,92,204,93,101,182,146,108,112,72,80,253,237,185,218,94,21,70,87,167,141,157,132,144,216,171,0,140,188,211,10,247,228,88,5,184,179,69,6,208,44,30,143,202,63,15,2,193,175,189,3,1,19,138,107,58,145,17,65,79,103,220,234,151,242,207,206,240,180,230,115,150,172,116,34,231,173,53,133,226,249,55,232,28,117,223,110,71,241,26,113,29,41,197,137,111,183,98,14,170,24,190,27,252,86,62,75,198,210,121,32,154,219,192,254,120,205,90,244,31,221,168,51,136,7,199,49,177,18,16,89,39,128,236,95,96,81,127,169,25,181,74,13,45,229,122,159,147,201,156,239,160,224,59,77,174,42,245,176,200,235,187,60,131,83,153,97,23,43,4,126,186,119,214,38,225,105,20,99,85,33,12,125],hr=[3328402341,4168907908,4000806809,4135287693,4294111757,3597364157,3731845041,2445657428,1613770832,33620227,3462883241,1445669757,3892248089,3050821474,1303096294,3967186586,2412431941,528646813,2311702848,4202528135,4026202645,2992200171,2387036105,4226871307,1101901292,3017069671,1604494077,1169141738,597466303,1403299063,3832705686,2613100635,1974974402,3791519004,1033081774,1277568618,1815492186,2118074177,4126668546,2211236943,1748251740,1369810420,3521504564,4193382664,3799085459,2883115123,1647391059,706024767,134480908,2512897874,1176707941,2646852446,806885416,932615841,168101135,798661301,235341577,605164086,461406363,3756188221,3454790438,1311188841,2142417613,3933566367,302582043,495158174,1479289972,874125870,907746093,3698224818,3025820398,1537253627,2756858614,1983593293,3084310113,2108928974,1378429307,3722699582,1580150641,327451799,2790478837,3117535592,0,3253595436,1075847264,3825007647,2041688520,3059440621,3563743934,2378943302,1740553945,1916352843,2487896798,2555137236,2958579944,2244988746,3151024235,3320835882,1336584933,3992714006,2252555205,2588757463,1714631509,293963156,2319795663,3925473552,67240454,4269768577,2689618160,2017213508,631218106,1269344483,2723238387,1571005438,2151694528,93294474,1066570413,563977660,1882732616,4059428100,1673313503,2008463041,2950355573,1109467491,537923632,3858759450,4260623118,3218264685,2177748300,403442708,638784309,3287084079,3193921505,899127202,2286175436,773265209,2479146071,1437050866,4236148354,2050833735,3362022572,3126681063,840505643,3866325909,3227541664,427917720,2655997905,2749160575,1143087718,1412049534,999329963,193497219,2353415882,3354324521,1807268051,672404540,2816401017,3160301282,369822493,2916866934,3688947771,1681011286,1949973070,336202270,2454276571,201721354,1210328172,3093060836,2680341085,3184776046,1135389935,3294782118,965841320,831886756,3554993207,4068047243,3588745010,2345191491,1849112409,3664604599,26054028,2983581028,2622377682,1235855840,3630984372,2891339514,4092916743,3488279077,3395642799,4101667470,1202630377,268961816,1874508501,4034427016,1243948399,1546530418,941366308,1470539505,1941222599,2546386513,3421038627,2715671932,3899946140,1042226977,2521517021,1639824860,227249030,260737669,3765465232,2084453954,1907733956,3429263018,2420656344,100860677,4160157185,470683154,3261161891,1781871967,2924959737,1773779408,394692241,2579611992,974986535,664706745,3655459128,3958962195,731420851,571543859,3530123707,2849626480,126783113,865375399,765172662,1008606754,361203602,3387549984,2278477385,2857719295,1344809080,2782912378,59542671,1503764984,160008576,437062935,1707065306,3622233649,2218934982,3496503480,2185314755,697932208,1512910199,504303377,2075177163,2824099068,1841019862,739644986],fr=[2781242211,2230877308,2582542199,2381740923,234877682,3184946027,2984144751,1418839493,1348481072,50462977,2848876391,2102799147,434634494,1656084439,3863849899,2599188086,1167051466,2636087938,1082771913,2281340285,368048890,3954334041,3381544775,201060592,3963727277,1739838676,4250903202,3930435503,3206782108,4149453988,2531553906,1536934080,3262494647,484572669,2923271059,1783375398,1517041206,1098792767,49674231,1334037708,1550332980,4098991525,886171109,150598129,2481090929,1940642008,1398944049,1059722517,201851908,1385547719,1699095331,1587397571,674240536,2704774806,252314885,3039795866,151914247,908333586,2602270848,1038082786,651029483,1766729511,3447698098,2682942837,454166793,2652734339,1951935532,775166490,758520603,3000790638,4004797018,4217086112,4137964114,1299594043,1639438038,3464344499,2068982057,1054729187,1901997871,2534638724,4121318227,1757008337,0,750906861,1614815264,535035132,3363418545,3988151131,3201591914,1183697867,3647454910,1265776953,3734260298,3566750796,3903871064,1250283471,1807470800,717615087,3847203498,384695291,3313910595,3617213773,1432761139,2484176261,3481945413,283769337,100925954,2180939647,4037038160,1148730428,3123027871,3813386408,4087501137,4267549603,3229630528,2315620239,2906624658,3156319645,1215313976,82966005,3747855548,3245848246,1974459098,1665278241,807407632,451280895,251524083,1841287890,1283575245,337120268,891687699,801369324,3787349855,2721421207,3431482436,959321879,1469301956,4065699751,2197585534,1199193405,2898814052,3887750493,724703513,2514908019,2696962144,2551808385,3516813135,2141445340,1715741218,2119445034,2872807568,2198571144,3398190662,700968686,3547052216,1009259540,2041044702,3803995742,487983883,1991105499,1004265696,1449407026,1316239930,504629770,3683797321,168560134,1816667172,3837287516,1570751170,1857934291,4014189740,2797888098,2822345105,2754712981,936633572,2347923833,852879335,1133234376,1500395319,3084545389,2348912013,1689376213,3533459022,3762923945,3034082412,4205598294,133428468,634383082,2949277029,2398386810,3913789102,403703816,3580869306,2297460856,1867130149,1918643758,607656988,4049053350,3346248884,1368901318,600565992,2090982877,2632479860,557719327,3717614411,3697393085,2249034635,2232388234,2430627952,1115438654,3295786421,2865522278,3633334344,84280067,33027830,303828494,2747425121,1600795957,4188952407,3496589753,2434238086,1486471617,658119965,3106381470,953803233,334231800,3005978776,857870609,3151128937,1890179545,2298973838,2805175444,3056442267,574365214,2450884487,550103529,1233637070,4289353045,2018519080,2057691103,2399374476,4166623649,2148108681,387583245,3664101311,836232934,3330556482,3100665960,3280093505,2955516313,2002398509,287182607,3413881008,4238890068,3597515707,975967766],pr=[1671808611,2089089148,2006576759,2072901243,4061003762,1807603307,1873927791,3310653893,810573872,16974337,1739181671,729634347,4263110654,3613570519,2883997099,1989864566,3393556426,2191335298,3376449993,2106063485,4195741690,1508618841,1204391495,4027317232,2917941677,3563566036,2734514082,2951366063,2629772188,2767672228,1922491506,3227229120,3082974647,4246528509,2477669779,644500518,911895606,1061256767,4144166391,3427763148,878471220,2784252325,3845444069,4043897329,1905517169,3631459288,827548209,356461077,67897348,3344078279,593839651,3277757891,405286936,2527147926,84871685,2595565466,118033927,305538066,2157648768,3795705826,3945188843,661212711,2999812018,1973414517,152769033,2208177539,745822252,439235610,455947803,1857215598,1525593178,2700827552,1391895634,994932283,3596728278,3016654259,695947817,3812548067,795958831,2224493444,1408607827,3513301457,0,3979133421,543178784,4229948412,2982705585,1542305371,1790891114,3410398667,3201918910,961245753,1256100938,1289001036,1491644504,3477767631,3496721360,4012557807,2867154858,4212583931,1137018435,1305975373,861234739,2241073541,1171229253,4178635257,33948674,2139225727,1357946960,1011120188,2679776671,2833468328,1374921297,2751356323,1086357568,2408187279,2460827538,2646352285,944271416,4110742005,3168756668,3066132406,3665145818,560153121,271589392,4279952895,4077846003,3530407890,3444343245,202643468,322250259,3962553324,1608629855,2543990167,1154254916,389623319,3294073796,2817676711,2122513534,1028094525,1689045092,1575467613,422261273,1939203699,1621147744,2174228865,1339137615,3699352540,577127458,712922154,2427141008,2290289544,1187679302,3995715566,3100863416,339486740,3732514782,1591917662,186455563,3681988059,3762019296,844522546,978220090,169743370,1239126601,101321734,611076132,1558493276,3260915650,3547250131,2901361580,1655096418,2443721105,2510565781,3828863972,2039214713,3878868455,3359869896,928607799,1840765549,2374762893,3580146133,1322425422,2850048425,1823791212,1459268694,4094161908,3928346602,1706019429,2056189050,2934523822,135794696,3134549946,2022240376,628050469,779246638,472135708,2800834470,3032970164,3327236038,3894660072,3715932637,1956440180,522272287,1272813131,3185336765,2340818315,2323976074,1888542832,1044544574,3049550261,1722469478,1222152264,50660867,4127324150,236067854,1638122081,895445557,1475980887,3117443513,2257655686,3243809217,489110045,2662934430,3778599393,4162055160,2561878936,288563729,1773916777,3648039385,2391345038,2493985684,2612407707,505560094,2274497927,3911240169,3460925390,1442818645,678973480,3749357023,2358182796,2717407649,2306869641,219617805,3218761151,3862026214,1120306242,1756942440,1103331905,2578459033,762796589,252780047,2966125488,1425844308,3151392187,372911126],_r=[1667474886,2088535288,2004326894,2071694838,4075949567,1802223062,1869591006,3318043793,808472672,16843522,1734846926,724270422,4278065639,3621216949,2880169549,1987484396,3402253711,2189597983,3385409673,2105378810,4210693615,1499065266,1195886990,4042263547,2913856577,3570689971,2728590687,2947541573,2627518243,2762274643,1920112356,3233831835,3082273397,4261223649,2475929149,640051788,909531756,1061110142,4160160501,3435941763,875846760,2779116625,3857003729,4059105529,1903268834,3638064043,825316194,353713962,67374088,3351728789,589522246,3284360861,404236336,2526454071,84217610,2593830191,117901582,303183396,2155911963,3806477791,3958056653,656894286,2998062463,1970642922,151591698,2206440989,741110872,437923380,454765878,1852748508,1515908788,2694904667,1381168804,993742198,3604373943,3014905469,690584402,3823320797,791638366,2223281939,1398011302,3520161977,0,3991743681,538992704,4244381667,2981218425,1532751286,1785380564,3419096717,3200178535,960056178,1246420628,1280103576,1482221744,3486468741,3503319995,4025428677,2863326543,4227536621,1128514950,1296947098,859002214,2240123921,1162203018,4193849577,33687044,2139062782,1347481760,1010582648,2678045221,2829640523,1364325282,2745433693,1077985408,2408548869,2459086143,2644360225,943212656,4126475505,3166494563,3065430391,3671750063,555836226,269496352,4294908645,4092792573,3537006015,3452783745,202118168,320025894,3974901699,1600119230,2543297077,1145359496,387397934,3301201811,2812801621,2122220284,1027426170,1684319432,1566435258,421079858,1936954854,1616945344,2172753945,1330631070,3705438115,572679748,707427924,2425400123,2290647819,1179044492,4008585671,3099120491,336870440,3739122087,1583276732,185277718,3688593069,3772791771,842159716,976899700,168435220,1229577106,101059084,606366792,1549591736,3267517855,3553849021,2897014595,1650632388,2442242105,2509612081,3840161747,2038008818,3890688725,3368567691,926374254,1835907034,2374863873,3587531953,1313788572,2846482505,1819063512,1448540844,4109633523,3941213647,1701162954,2054852340,2930698567,134748176,3132806511,2021165296,623210314,774795868,471606328,2795958615,3031746419,3334885783,3907527627,3722280097,1953799400,522133822,1263263126,3183336545,2341176845,2324333839,1886425312,1044267644,3048588401,1718004428,1212733584,50529542,4143317495,235803164,1633788866,892690282,1465383342,3115962473,2256965911,3250673817,488449850,2661202215,3789633753,4177007595,2560144171,286339874,1768537042,3654906025,2391705863,2492770099,2610673197,505291324,2273808917,3924369609,3469625735,1431699370,673740880,3755965093,2358021891,2711746649,2307489801,218961690,3217021541,3873845719,1111672452,1751693520,1094828930,2576986153,757954394,252645662,2964376443,1414855848,3149649517,370555436],mr=[1374988112,2118214995,437757123,975658646,1001089995,530400753,2902087851,1273168787,540080725,2910219766,2295101073,4110568485,1340463100,3307916247,641025152,3043140495,3736164937,632953703,1172967064,1576976609,3274667266,2169303058,2370213795,1809054150,59727847,361929877,3211623147,2505202138,3569255213,1484005843,1239443753,2395588676,1975683434,4102977912,2572697195,666464733,3202437046,4035489047,3374361702,2110667444,1675577880,3843699074,2538681184,1649639237,2976151520,3144396420,4269907996,4178062228,1883793496,2403728665,2497604743,1383856311,2876494627,1917518562,3810496343,1716890410,3001755655,800440835,2261089178,3543599269,807962610,599762354,33778362,3977675356,2328828971,2809771154,4077384432,1315562145,1708848333,101039829,3509871135,3299278474,875451293,2733856160,92987698,2767645557,193195065,1080094634,1584504582,3178106961,1042385657,2531067453,3711829422,1306967366,2438237621,1908694277,67556463,1615861247,429456164,3602770327,2302690252,1742315127,2968011453,126454664,3877198648,2043211483,2709260871,2084704233,4169408201,0,159417987,841739592,504459436,1817866830,4245618683,260388950,1034867998,908933415,168810852,1750902305,2606453969,607530554,202008497,2472011535,3035535058,463180190,2160117071,1641816226,1517767529,470948374,3801332234,3231722213,1008918595,303765277,235474187,4069246893,766945465,337553864,1475418501,2943682380,4003061179,2743034109,4144047775,1551037884,1147550661,1543208500,2336434550,3408119516,3069049960,3102011747,3610369226,1113818384,328671808,2227573024,2236228733,3535486456,2935566865,3341394285,496906059,3702665459,226906860,2009195472,733156972,2842737049,294930682,1206477858,2835123396,2700099354,1451044056,573804783,2269728455,3644379585,2362090238,2564033334,2801107407,2776292904,3669462566,1068351396,742039012,1350078989,1784663195,1417561698,4136440770,2430122216,775550814,2193862645,2673705150,1775276924,1876241833,3475313331,3366754619,270040487,3902563182,3678124923,3441850377,1851332852,3969562369,2203032232,3868552805,2868897406,566021896,4011190502,3135740889,1248802510,3936291284,699432150,832877231,708780849,3332740144,899835584,1951317047,4236429990,3767586992,866637845,4043610186,1106041591,2144161806,395441711,1984812685,1139781709,3433712980,3835036895,2664543715,1282050075,3240894392,1181045119,2640243204,25965917,4203181171,4211818798,3009879386,2463879762,3910161971,1842759443,2597806476,933301370,1509430414,3943906441,3467192302,3076639029,3776767469,2051518780,2631065433,1441952575,404016761,1942435775,1408749034,1610459739,3745345300,2017778566,3400528769,3110650942,941896748,3265478751,371049330,3168937228,675039627,4279080257,967311729,135050206,3635733660,1683407248,2076935265,3576870512,1215061108,3501741890],gr=[1347548327,1400783205,3273267108,2520393566,3409685355,4045380933,2880240216,2471224067,1428173050,4138563181,2441661558,636813900,4233094615,3620022987,2149987652,2411029155,1239331162,1730525723,2554718734,3781033664,46346101,310463728,2743944855,3328955385,3875770207,2501218972,3955191162,3667219033,768917123,3545789473,692707433,1150208456,1786102409,2029293177,1805211710,3710368113,3065962831,401639597,1724457132,3028143674,409198410,2196052529,1620529459,1164071807,3769721975,2226875310,486441376,2499348523,1483753576,428819965,2274680428,3075636216,598438867,3799141122,1474502543,711349675,129166120,53458370,2592523643,2782082824,4063242375,2988687269,3120694122,1559041666,730517276,2460449204,4042459122,2706270690,3446004468,3573941694,533804130,2328143614,2637442643,2695033685,839224033,1973745387,957055980,2856345839,106852767,1371368976,4181598602,1033297158,2933734917,1179510461,3046200461,91341917,1862534868,4284502037,605657339,2547432937,3431546947,2003294622,3182487618,2282195339,954669403,3682191598,1201765386,3917234703,3388507166,0,2198438022,1211247597,2887651696,1315723890,4227665663,1443857720,507358933,657861945,1678381017,560487590,3516619604,975451694,2970356327,261314535,3535072918,2652609425,1333838021,2724322336,1767536459,370938394,182621114,3854606378,1128014560,487725847,185469197,2918353863,3106780840,3356761769,2237133081,1286567175,3152976349,4255350624,2683765030,3160175349,3309594171,878443390,1988838185,3704300486,1756818940,1673061617,3403100636,272786309,1075025698,545572369,2105887268,4174560061,296679730,1841768865,1260232239,4091327024,3960309330,3497509347,1814803222,2578018489,4195456072,575138148,3299409036,446754879,3629546796,4011996048,3347532110,3252238545,4270639778,915985419,3483825537,681933534,651868046,2755636671,3828103837,223377554,2607439820,1649704518,3270937875,3901806776,1580087799,4118987695,3198115200,2087309459,2842678573,3016697106,1003007129,2802849917,1860738147,2077965243,164439672,4100872472,32283319,2827177882,1709610350,2125135846,136428751,3874428392,3652904859,3460984630,3572145929,3593056380,2939266226,824852259,818324884,3224740454,930369212,2801566410,2967507152,355706840,1257309336,4148292826,243256656,790073846,2373340630,1296297904,1422699085,3756299780,3818836405,457992840,3099667487,2135319889,77422314,1560382517,1945798516,788204353,1521706781,1385356242,870912086,325965383,2358957921,2050466060,2388260884,2313884476,4006521127,901210569,3990953189,1014646705,1503449823,1062597235,2031621326,3212035895,3931371469,1533017514,350174575,2256028891,2177544179,1052338372,741876788,1606591296,1914052035,213705253,2334669897,1107234197,1899603969,3725069491,2631447780,2422494913,1635502980,1893020342,1950903388,1120974935],yr=[2807058932,1699970625,2764249623,1586903591,1808481195,1173430173,1487645946,59984867,4199882800,1844882806,1989249228,1277555970,3623636965,3419915562,1149249077,2744104290,1514790577,459744698,244860394,3235995134,1963115311,4027744588,2544078150,4190530515,1608975247,2627016082,2062270317,1507497298,2200818878,567498868,1764313568,3359936201,2305455554,2037970062,1047239e3,1910319033,1337376481,2904027272,2892417312,984907214,1243112415,830661914,861968209,2135253587,2011214180,2927934315,2686254721,731183368,1750626376,4246310725,1820824798,4172763771,3542330227,48394827,2404901663,2871682645,671593195,3254988725,2073724613,145085239,2280796200,2779915199,1790575107,2187128086,472615631,3029510009,4075877127,3802222185,4107101658,3201631749,1646252340,4270507174,1402811438,1436590835,3778151818,3950355702,3963161475,4020912224,2667994737,273792366,2331590177,104699613,95345982,3175501286,2377486676,1560637892,3564045318,369057872,4213447064,3919042237,1137477952,2658625497,1119727848,2340947849,1530455833,4007360968,172466556,266959938,516552836,0,2256734592,3980931627,1890328081,1917742170,4294704398,945164165,3575528878,958871085,3647212047,2787207260,1423022939,775562294,1739656202,3876557655,2530391278,2443058075,3310321856,547512796,1265195639,437656594,3121275539,719700128,3762502690,387781147,218828297,3350065803,2830708150,2848461854,428169201,122466165,3720081049,1627235199,648017665,4122762354,1002783846,2117360635,695634755,3336358691,4234721005,4049844452,3704280881,2232435299,574624663,287343814,612205898,1039717051,840019705,2708326185,793451934,821288114,1391201670,3822090177,376187827,3113855344,1224348052,1679968233,2361698556,1058709744,752375421,2431590963,1321699145,3519142200,2734591178,188127444,2177869557,3727205754,2384911031,3215212461,2648976442,2450346104,3432737375,1180849278,331544205,3102249176,4150144569,2952102595,2159976285,2474404304,766078933,313773861,2570832044,2108100632,1668212892,3145456443,2013908262,418672217,3070356634,2594734927,1852171925,3867060991,3473416636,3907448597,2614737639,919489135,164948639,2094410160,2997825956,590424639,2486224549,1723872674,3157750862,3399941250,3501252752,3625268135,2555048196,3673637356,1343127501,4130281361,3599595085,2957853679,1297403050,81781910,3051593425,2283490410,532201772,1367295589,3926170974,895287692,1953757831,1093597963,492483431,3528626907,1446242576,1192455638,1636604631,209336225,344873464,1015671571,669961897,3375740769,3857572124,2973530695,3747192018,1933530610,3464042516,935293895,3454686199,2858115069,1863638845,3683022916,4085369519,3292445032,875313188,1080017571,3279033885,621591778,1233856572,2504130317,24197544,3017672716,3835484340,3247465558,2220981195,3060847922,1551124588,1463996600],br=[4104605777,1097159550,396673818,660510266,2875968315,2638606623,4200115116,3808662347,821712160,1986918061,3430322568,38544885,3856137295,718002117,893681702,1654886325,2975484382,3122358053,3926825029,4274053469,796197571,1290801793,1184342925,3556361835,2405426947,2459735317,1836772287,1381620373,3196267988,1948373848,3764988233,3385345166,3263785589,2390325492,1480485785,3111247143,3780097726,2293045232,548169417,3459953789,3746175075,439452389,1362321559,1400849762,1685577905,1806599355,2174754046,137073913,1214797936,1174215055,3731654548,2079897426,1943217067,1258480242,529487843,1437280870,3945269170,3049390895,3313212038,923313619,679998e3,3215307299,57326082,377642221,3474729866,2041877159,133361907,1776460110,3673476453,96392454,878845905,2801699524,777231668,4082475170,2330014213,4142626212,2213296395,1626319424,1906247262,1846563261,562755902,3708173718,1040559837,3871163981,1418573201,3294430577,114585348,1343618912,2566595609,3186202582,1078185097,3651041127,3896688048,2307622919,425408743,3371096953,2081048481,1108339068,2216610296,0,2156299017,736970802,292596766,1517440620,251657213,2235061775,2933202493,758720310,265905162,1554391400,1532285339,908999204,174567692,1474760595,4002861748,2610011675,3234156416,3693126241,2001430874,303699484,2478443234,2687165888,585122620,454499602,151849742,2345119218,3064510765,514443284,4044981591,1963412655,2581445614,2137062819,19308535,1928707164,1715193156,4219352155,1126790795,600235211,3992742070,3841024952,836553431,1669664834,2535604243,3323011204,1243905413,3141400786,4180808110,698445255,2653899549,2989552604,2253581325,3252932727,3004591147,1891211689,2487810577,3915653703,4237083816,4030667424,2100090966,865136418,1229899655,953270745,3399679628,3557504664,4118925222,2061379749,3079546586,2915017791,983426092,2022837584,1607244650,2118541908,2366882550,3635996816,972512814,3283088770,1568718495,3499326569,3576539503,621982671,2895723464,410887952,2623762152,1002142683,645401037,1494807662,2595684844,1335535747,2507040230,4293295786,3167684641,367585007,3885750714,1865862730,2668221674,2960971305,2763173681,1059270954,2777952454,2724642869,1320957812,2194319100,2429595872,2815956275,77089521,3973773121,3444575871,2448830231,1305906550,4021308739,2857194700,2516901860,3518358430,1787304780,740276417,1699839814,1592394909,2352307457,2272556026,188821243,1729977011,3687994002,274084841,3594982253,3613494426,2701949495,4162096729,322734571,2837966542,1640576439,484830689,1202797690,3537852828,4067639125,349075736,3342319475,4157467219,4255800159,1030690015,1155237496,2951971274,1757691577,607398968,2738905026,499347990,3794078908,1011452712,227885567,2818666809,213114376,3034881240,1455525988,3414450555,850817237,1817998408,3092726480],vr=[0,235474187,470948374,303765277,941896748,908933415,607530554,708780849,1883793496,2118214995,1817866830,1649639237,1215061108,1181045119,1417561698,1517767529,3767586992,4003061179,4236429990,4069246893,3635733660,3602770327,3299278474,3400528769,2430122216,2664543715,2362090238,2193862645,2835123396,2801107407,3035535058,3135740889,3678124923,3576870512,3341394285,3374361702,3810496343,3977675356,4279080257,4043610186,2876494627,2776292904,3076639029,3110650942,2472011535,2640243204,2403728665,2169303058,1001089995,899835584,666464733,699432150,59727847,226906860,530400753,294930682,1273168787,1172967064,1475418501,1509430414,1942435775,2110667444,1876241833,1641816226,2910219766,2743034109,2976151520,3211623147,2505202138,2606453969,2302690252,2269728455,3711829422,3543599269,3240894392,3475313331,3843699074,3943906441,4178062228,4144047775,1306967366,1139781709,1374988112,1610459739,1975683434,2076935265,1775276924,1742315127,1034867998,866637845,566021896,800440835,92987698,193195065,429456164,395441711,1984812685,2017778566,1784663195,1683407248,1315562145,1080094634,1383856311,1551037884,101039829,135050206,437757123,337553864,1042385657,807962610,573804783,742039012,2531067453,2564033334,2328828971,2227573024,2935566865,2700099354,3001755655,3168937228,3868552805,3902563182,4203181171,4102977912,3736164937,3501741890,3265478751,3433712980,1106041591,1340463100,1576976609,1408749034,2043211483,2009195472,1708848333,1809054150,832877231,1068351396,766945465,599762354,159417987,126454664,361929877,463180190,2709260871,2943682380,3178106961,3009879386,2572697195,2538681184,2236228733,2336434550,3509871135,3745345300,3441850377,3274667266,3910161971,3877198648,4110568485,4211818798,2597806476,2497604743,2261089178,2295101073,2733856160,2902087851,3202437046,2968011453,3936291284,3835036895,4136440770,4169408201,3535486456,3702665459,3467192302,3231722213,2051518780,1951317047,1716890410,1750902305,1113818384,1282050075,1584504582,1350078989,168810852,67556463,371049330,404016761,841739592,1008918595,775550814,540080725,3969562369,3801332234,4035489047,4269907996,3569255213,3669462566,3366754619,3332740144,2631065433,2463879762,2160117071,2395588676,2767645557,2868897406,3102011747,3069049960,202008497,33778362,270040487,504459436,875451293,975658646,675039627,641025152,2084704233,1917518562,1615861247,1851332852,1147550661,1248802510,1484005843,1451044056,933301370,967311729,733156972,632953703,260388950,25965917,328671808,496906059,1206477858,1239443753,1543208500,1441952575,2144161806,1908694277,1675577880,1842759443,3610369226,3644379585,3408119516,3307916247,4011190502,3776767469,4077384432,4245618683,2809771154,2842737049,3144396420,3043140495,2673705150,2438237621,2203032232,2370213795],Sr=[0,185469197,370938394,487725847,741876788,657861945,975451694,824852259,1483753576,1400783205,1315723890,1164071807,1950903388,2135319889,1649704518,1767536459,2967507152,3152976349,2801566410,2918353863,2631447780,2547432937,2328143614,2177544179,3901806776,3818836405,4270639778,4118987695,3299409036,3483825537,3535072918,3652904859,2077965243,1893020342,1841768865,1724457132,1474502543,1559041666,1107234197,1257309336,598438867,681933534,901210569,1052338372,261314535,77422314,428819965,310463728,3409685355,3224740454,3710368113,3593056380,3875770207,3960309330,4045380933,4195456072,2471224067,2554718734,2237133081,2388260884,3212035895,3028143674,2842678573,2724322336,4138563181,4255350624,3769721975,3955191162,3667219033,3516619604,3431546947,3347532110,2933734917,2782082824,3099667487,3016697106,2196052529,2313884476,2499348523,2683765030,1179510461,1296297904,1347548327,1533017514,1786102409,1635502980,2087309459,2003294622,507358933,355706840,136428751,53458370,839224033,957055980,605657339,790073846,2373340630,2256028891,2607439820,2422494913,2706270690,2856345839,3075636216,3160175349,3573941694,3725069491,3273267108,3356761769,4181598602,4063242375,4011996048,3828103837,1033297158,915985419,730517276,545572369,296679730,446754879,129166120,213705253,1709610350,1860738147,1945798516,2029293177,1239331162,1120974935,1606591296,1422699085,4148292826,4233094615,3781033664,3931371469,3682191598,3497509347,3446004468,3328955385,2939266226,2755636671,3106780840,2988687269,2198438022,2282195339,2501218972,2652609425,1201765386,1286567175,1371368976,1521706781,1805211710,1620529459,2105887268,1988838185,533804130,350174575,164439672,46346101,870912086,954669403,636813900,788204353,2358957921,2274680428,2592523643,2441661558,2695033685,2880240216,3065962831,3182487618,3572145929,3756299780,3270937875,3388507166,4174560061,4091327024,4006521127,3854606378,1014646705,930369212,711349675,560487590,272786309,457992840,106852767,223377554,1678381017,1862534868,1914052035,2031621326,1211247597,1128014560,1580087799,1428173050,32283319,182621114,401639597,486441376,768917123,651868046,1003007129,818324884,1503449823,1385356242,1333838021,1150208456,1973745387,2125135846,1673061617,1756818940,2970356327,3120694122,2802849917,2887651696,2637442643,2520393566,2334669897,2149987652,3917234703,3799141122,4284502037,4100872472,3309594171,3460984630,3545789473,3629546796,2050466060,1899603969,1814803222,1730525723,1443857720,1560382517,1075025698,1260232239,575138148,692707433,878443390,1062597235,243256656,91341917,409198410,325965383,3403100636,3252238545,3704300486,3620022987,3874428392,3990953189,4042459122,4227665663,2460449204,2578018489,2226875310,2411029155,3198115200,3046200461,2827177882,2743944855],wr=[0,218828297,437656594,387781147,875313188,958871085,775562294,590424639,1750626376,1699970625,1917742170,2135253587,1551124588,1367295589,1180849278,1265195639,3501252752,3720081049,3399941250,3350065803,3835484340,3919042237,4270507174,4085369519,3102249176,3051593425,2734591178,2952102595,2361698556,2177869557,2530391278,2614737639,3145456443,3060847922,2708326185,2892417312,2404901663,2187128086,2504130317,2555048196,3542330227,3727205754,3375740769,3292445032,3876557655,3926170974,4246310725,4027744588,1808481195,1723872674,1910319033,2094410160,1608975247,1391201670,1173430173,1224348052,59984867,244860394,428169201,344873464,935293895,984907214,766078933,547512796,1844882806,1627235199,2011214180,2062270317,1507497298,1423022939,1137477952,1321699145,95345982,145085239,532201772,313773861,830661914,1015671571,731183368,648017665,3175501286,2957853679,2807058932,2858115069,2305455554,2220981195,2474404304,2658625497,3575528878,3625268135,3473416636,3254988725,3778151818,3963161475,4213447064,4130281361,3599595085,3683022916,3432737375,3247465558,3802222185,4020912224,4172763771,4122762354,3201631749,3017672716,2764249623,2848461854,2331590177,2280796200,2431590963,2648976442,104699613,188127444,472615631,287343814,840019705,1058709744,671593195,621591778,1852171925,1668212892,1953757831,2037970062,1514790577,1463996600,1080017571,1297403050,3673637356,3623636965,3235995134,3454686199,4007360968,3822090177,4107101658,4190530515,2997825956,3215212461,2830708150,2779915199,2256734592,2340947849,2627016082,2443058075,172466556,122466165,273792366,492483431,1047239e3,861968209,612205898,695634755,1646252340,1863638845,2013908262,1963115311,1446242576,1530455833,1277555970,1093597963,1636604631,1820824798,2073724613,1989249228,1436590835,1487645946,1337376481,1119727848,164948639,81781910,331544205,516552836,1039717051,821288114,669961897,719700128,2973530695,3157750862,2871682645,2787207260,2232435299,2283490410,2667994737,2450346104,3647212047,3564045318,3279033885,3464042516,3980931627,3762502690,4150144569,4199882800,3070356634,3121275539,2904027272,2686254721,2200818878,2384911031,2570832044,2486224549,3747192018,3528626907,3310321856,3359936201,3950355702,3867060991,4049844452,4234721005,1739656202,1790575107,2108100632,1890328081,1402811438,1586903591,1233856572,1149249077,266959938,48394827,369057872,418672217,1002783846,919489135,567498868,752375421,209336225,24197544,376187827,459744698,945164165,895287692,574624663,793451934,1679968233,1764313568,2117360635,1933530610,1343127501,1560637892,1243112415,1192455638,3704280881,3519142200,3336358691,3419915562,3907448597,3857572124,4075877127,4294704398,3029510009,3113855344,2927934315,2744104290,2159976285,2377486676,2594734927,2544078150],Ar=[0,151849742,303699484,454499602,607398968,758720310,908999204,1059270954,1214797936,1097159550,1517440620,1400849762,1817998408,1699839814,2118541908,2001430874,2429595872,2581445614,2194319100,2345119218,3034881240,3186202582,2801699524,2951971274,3635996816,3518358430,3399679628,3283088770,4237083816,4118925222,4002861748,3885750714,1002142683,850817237,698445255,548169417,529487843,377642221,227885567,77089521,1943217067,2061379749,1640576439,1757691577,1474760595,1592394909,1174215055,1290801793,2875968315,2724642869,3111247143,2960971305,2405426947,2253581325,2638606623,2487810577,3808662347,3926825029,4044981591,4162096729,3342319475,3459953789,3576539503,3693126241,1986918061,2137062819,1685577905,1836772287,1381620373,1532285339,1078185097,1229899655,1040559837,923313619,740276417,621982671,439452389,322734571,137073913,19308535,3871163981,4021308739,4104605777,4255800159,3263785589,3414450555,3499326569,3651041127,2933202493,2815956275,3167684641,3049390895,2330014213,2213296395,2566595609,2448830231,1305906550,1155237496,1607244650,1455525988,1776460110,1626319424,2079897426,1928707164,96392454,213114376,396673818,514443284,562755902,679998e3,865136418,983426092,3708173718,3557504664,3474729866,3323011204,4180808110,4030667424,3945269170,3794078908,2507040230,2623762152,2272556026,2390325492,2975484382,3092726480,2738905026,2857194700,3973773121,3856137295,4274053469,4157467219,3371096953,3252932727,3673476453,3556361835,2763173681,2915017791,3064510765,3215307299,2156299017,2307622919,2459735317,2610011675,2081048481,1963412655,1846563261,1729977011,1480485785,1362321559,1243905413,1126790795,878845905,1030690015,645401037,796197571,274084841,425408743,38544885,188821243,3613494426,3731654548,3313212038,3430322568,4082475170,4200115116,3780097726,3896688048,2668221674,2516901860,2366882550,2216610296,3141400786,2989552604,2837966542,2687165888,1202797690,1320957812,1437280870,1554391400,1669664834,1787304780,1906247262,2022837584,265905162,114585348,499347990,349075736,736970802,585122620,972512814,821712160,2595684844,2478443234,2293045232,2174754046,3196267988,3079546586,2895723464,2777952454,3537852828,3687994002,3234156416,3385345166,4142626212,4293295786,3841024952,3992742070,174567692,57326082,410887952,292596766,777231668,660510266,1011452712,893681702,1108339068,1258480242,1343618912,1494807662,1715193156,1865862730,1948373848,2100090966,2701949495,2818666809,3004591147,3122358053,2235061775,2352307457,2535604243,2653899549,3915653703,3764988233,4219352155,4067639125,3444575871,3294430577,3746175075,3594982253,836553431,953270745,600235211,718002117,367585007,484830689,133361907,251657213,2041877159,1891211689,1806599355,1654886325,1568718495,1418573201,1335535747,1184342925];function Br(e){for(var t=[],i=0;i<e.length;i+=4)t.push(e[i]<<24|e[i+1]<<16|e[i+2]<<8|e[i+3]);return t}var xr=function(e){if(!(this instanceof xr))throw Error("AES must be instanitated with `new`");Object.defineProperty(this,"key",{value:ir(e,!0)}),this._prepare()};xr.prototype._prepare=function(){var e=dr[this.key.length];if(null==e)throw new Error("invalid key size (must be 16, 24 or 32 bytes)");this._Ke=[],this._Kd=[];for(var t=0;t<=e;t++)this._Ke.push([0,0,0,0]),this._Kd.push([0,0,0,0]);var i,r=4*(e+1),s=this.key.length/4,a=Br(this.key);for(t=0;t<s;t++)i=t>>2,this._Ke[i][t%4]=a[t],this._Kd[e-i][t%4]=a[t];for(var n,o=0,d=s;d<r;){if(n=a[s-1],a[0]^=cr[n>>16&255]<<24^cr[n>>8&255]<<16^cr[255&n]<<8^cr[n>>24&255]^lr[o]<<24,o+=1,8!=s)for(t=1;t<s;t++)a[t]^=a[t-1];else{for(t=1;t<s/2;t++)a[t]^=a[t-1];n=a[s/2-1],a[s/2]^=cr[255&n]^cr[n>>8&255]<<8^cr[n>>16&255]<<16^cr[n>>24&255]<<24;for(t=s/2+1;t<s;t++)a[t]^=a[t-1]}for(t=0;t<s&&d<r;)l=d>>2,c=d%4,this._Ke[l][c]=a[t],this._Kd[e-l][c]=a[t++],d++}for(var l=1;l<e;l++)for(var c=0;c<4;c++)n=this._Kd[l][c],this._Kd[l][c]=vr[n>>24&255]^Sr[n>>16&255]^wr[n>>8&255]^Ar[255&n]},xr.prototype.encrypt=function(e){if(16!=e.length)throw new Error("invalid plaintext size (must be 16 bytes)");for(var t=this._Ke.length-1,i=[0,0,0,0],r=Br(e),s=0;s<4;s++)r[s]^=this._Ke[0][s];for(var a=1;a<t;a++){for(s=0;s<4;s++)i[s]=hr[r[s]>>24&255]^fr[r[(s+1)%4]>>16&255]^pr[r[(s+2)%4]>>8&255]^_r[255&r[(s+3)%4]]^this._Ke[a][s];r=i.slice()}var n,o=rr(16);for(s=0;s<4;s++)n=this._Ke[t][s],o[4*s]=255&(cr[r[s]>>24&255]^n>>24),o[4*s+1]=255&(cr[r[(s+1)%4]>>16&255]^n>>16),o[4*s+2]=255&(cr[r[(s+2)%4]>>8&255]^n>>8),o[4*s+3]=255&(cr[255&r[(s+3)%4]]^n);return o},xr.prototype.decrypt=function(e){if(16!=e.length)throw new Error("invalid ciphertext size (must be 16 bytes)");for(var t=this._Kd.length-1,i=[0,0,0,0],r=Br(e),s=0;s<4;s++)r[s]^=this._Kd[0][s];for(var a=1;a<t;a++){for(s=0;s<4;s++)i[s]=mr[r[s]>>24&255]^gr[r[(s+3)%4]>>16&255]^yr[r[(s+2)%4]>>8&255]^br[255&r[(s+1)%4]]^this._Kd[a][s];r=i.slice()}var n,o=rr(16);for(s=0;s<4;s++)n=this._Kd[t][s],o[4*s]=255&(ur[r[s]>>24&255]^n>>24),o[4*s+1]=255&(ur[r[(s+3)%4]>>16&255]^n>>16),o[4*s+2]=255&(ur[r[(s+2)%4]>>8&255]^n>>8),o[4*s+3]=255&(ur[255&r[(s+1)%4]]^n);return o};var Ur=function(e){if(!(this instanceof Ur))throw Error("AES must be instanitated with `new`");this.description="Electronic Code Block",this.name="ecb",this._aes=new xr(e)};Ur.prototype.encrypt=function(e){if((e=ir(e)).length%16!=0)throw new Error("invalid plaintext size (must be multiple of 16 bytes)");for(var t=rr(e.length),i=rr(16),r=0;r<e.length;r+=16)sr(e,i,0,r,r+16),sr(i=this._aes.encrypt(i),t,r);return t},Ur.prototype.decrypt=function(e){if((e=ir(e)).length%16!=0)throw new Error("invalid ciphertext size (must be multiple of 16 bytes)");for(var t=rr(e.length),i=rr(16),r=0;r<e.length;r+=16)sr(e,i,0,r,r+16),sr(i=this._aes.decrypt(i),t,r);return t};var Er=function(e,t){if(!(this instanceof Er))throw Error("AES must be instanitated with `new`");if(this.description="Cipher Block Chaining",this.name="cbc",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 bytes)")}else t=rr(16);this._lastCipherblock=ir(t,!0),this._aes=new xr(e)};Er.prototype.encrypt=function(e){if((e=ir(e)).length%16!=0)throw new Error("invalid plaintext size (must be multiple of 16 bytes)");for(var t=rr(e.length),i=rr(16),r=0;r<e.length;r+=16){sr(e,i,0,r,r+16);for(var s=0;s<16;s++)i[s]^=this._lastCipherblock[s];this._lastCipherblock=this._aes.encrypt(i),sr(this._lastCipherblock,t,r)}return t},Er.prototype.decrypt=function(e){if((e=ir(e)).length%16!=0)throw new Error("invalid ciphertext size (must be multiple of 16 bytes)");for(var t=rr(e.length),i=rr(16),r=0;r<e.length;r+=16){sr(e,i,0,r,r+16),i=this._aes.decrypt(i);for(var s=0;s<16;s++)t[r+s]=i[s]^this._lastCipherblock[s];sr(e,this._lastCipherblock,0,r,r+16)}return t};var Tr=function(e,t,i){if(!(this instanceof Tr))throw Error("AES must be instanitated with `new`");if(this.description="Cipher Feedback",this.name="cfb",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 size)")}else t=rr(16);i||(i=1),this.segmentSize=i,this._shiftRegister=ir(t,!0),this._aes=new xr(e)};Tr.prototype.encrypt=function(e){if(e.length%this.segmentSize!=0)throw new Error("invalid plaintext size (must be segmentSize bytes)");for(var t,i=ir(e,!0),r=0;r<i.length;r+=this.segmentSize){t=this._aes.encrypt(this._shiftRegister);for(var s=0;s<this.segmentSize;s++)i[r+s]^=t[s];sr(this._shiftRegister,this._shiftRegister,0,this.segmentSize),sr(i,this._shiftRegister,16-this.segmentSize,r,r+this.segmentSize)}return i},Tr.prototype.decrypt=function(e){if(e.length%this.segmentSize!=0)throw new Error("invalid ciphertext size (must be segmentSize bytes)");for(var t,i=ir(e,!0),r=0;r<i.length;r+=this.segmentSize){t=this._aes.encrypt(this._shiftRegister);for(var s=0;s<this.segmentSize;s++)i[r+s]^=t[s];sr(this._shiftRegister,this._shiftRegister,0,this.segmentSize),sr(e,this._shiftRegister,16-this.segmentSize,r,r+this.segmentSize)}return i};var kr=function(e,t){if(!(this instanceof kr))throw Error("AES must be instanitated with `new`");if(this.description="Output Feedback",this.name="ofb",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 bytes)")}else t=rr(16);this._lastPrecipher=ir(t,!0),this._lastPrecipherIndex=16,this._aes=new xr(e)};kr.prototype.encrypt=function(e){for(var t=ir(e,!0),i=0;i<t.length;i++)16===this._lastPrecipherIndex&&(this._lastPrecipher=this._aes.encrypt(this._lastPrecipher),this._lastPrecipherIndex=0),t[i]^=this._lastPrecipher[this._lastPrecipherIndex++];return t},kr.prototype.decrypt=kr.prototype.encrypt;var Cr=function(e){if(!(this instanceof Cr))throw Error("Counter must be instanitated with `new`");0===e||e||(e=1),"number"==typeof e?(this._counter=rr(16),this.setValue(e)):this.setBytes(e)};Cr.prototype.setValue=function(e){if("number"!=typeof e||parseInt(e)!=e)throw new Error("invalid counter value (must be an integer)");if(e>Number.MAX_SAFE_INTEGER)throw new Error("integer value out of safe range");for(var t=15;t>=0;--t)this._counter[t]=e%256,e=parseInt(e/256)},Cr.prototype.setBytes=function(e){if(16!=(e=ir(e,!0)).length)throw new Error("invalid counter bytes size (must be 16 bytes)");this._counter=e},Cr.prototype.increment=function(){for(var e=15;e>=0;e--){if(255!==this._counter[e]){this._counter[e]++;break}this._counter[e]=0}};var Dr=function(e,t){if(!(this instanceof Dr))throw Error("AES must be instanitated with `new`");this.description="Counter",this.name="ctr",t instanceof Cr||(t=new Cr(t)),this._counter=t,this._remainingCounter=null,this._remainingCounterIndex=16,this._aes=new xr(e)};Dr.prototype.encrypt=function(e){for(var t=ir(e,!0),i=0;i<t.length;i++)16===this._remainingCounterIndex&&(this._remainingCounter=this._aes.encrypt(this._counter._counter),this._remainingCounterIndex=0,this._counter.increment()),t[i]^=this._remainingCounter[this._remainingCounterIndex++];return t},Dr.prototype.decrypt=Dr.prototype.encrypt;const Ir={AES:xr,Counter:Cr,ModeOfOperation:{ecb:Ur,cbc:Er,cfb:Tr,ofb:kr,ctr:Dr},utils:{hex:or,utf8:nr},padding:{pkcs7:{pad:function(e){var t=16-(e=ir(e,!0)).length%16,i=rr(e.length+t);sr(e,i);for(var r=e.length;r<i.length;r++)i[r]=t;return i},strip:function(e){if((e=ir(e,!0)).length<16)throw new Error("PKCS#7 invalid length");var t=e[e.length-1];if(t>16)throw new Error("PKCS#7 padding byte out of range");for(var i=e.length-t,r=0;r<t;r++)if(e[i+r]!==t)throw new Error("PKCS#7 invalid padding byte");var s=rr(i);return sr(e,s,0,0,i),s}}},_arrayTest:{coerceArray:ir,createArray:rr,copyArray:sr}};var Lr=zt((function(e,t){var i;e.exports=(i=i||function(e,t){var i;if("undefined"!=typeof window&&window.crypto&&(i=window.crypto),"undefined"!=typeof self&&self.crypto&&(i=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(i=globalThis.crypto),!i&&"undefined"!=typeof window&&window.msCrypto&&(i=window.msCrypto),!i&&void 0!==Mt&&Mt.crypto&&(i=Mt.crypto),!i)try{i=r.default}catch(e){}var s=function(){if(i){if("function"==typeof i.getRandomValues)try{return i.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof i.randomBytes)try{return i.randomBytes(4).readInt32LE()}catch(e){}}throw new Error("Native crypto module could not be used to get secure random number.")},a=Object.create||function(){function e(){}return function(t){var i;return e.prototype=t,i=new e,e.prototype=null,i}}(),n={},o=n.lib={},d=o.Base={extend:function(e){var t=a(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},l=o.WordArray=d.extend({init:function(e,i){e=this.words=e||[],this.sigBytes=i!=t?i:4*e.length},toString:function(e){return(e||u).stringify(this)},concat:function(e){var t=this.words,i=e.words,r=this.sigBytes,s=e.sigBytes;if(this.clamp(),r%4)for(var a=0;a<s;a++){var n=i[a>>>2]>>>24-a%4*8&255;t[r+a>>>2]|=n<<24-(r+a)%4*8}else for(var o=0;o<s;o+=4)t[r+o>>>2]=i[o>>>2];return this.sigBytes+=s,this},clamp:function(){var t=this.words,i=this.sigBytes;t[i>>>2]&=4294967295<<32-i%4*8,t.length=e.ceil(i/4)},clone:function(){var e=d.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],i=0;i<e;i+=4)t.push(s());return new l.init(t,e)}}),c=n.enc={},u=c.Hex={stringify:function(e){for(var t=e.words,i=e.sigBytes,r=[],s=0;s<i;s++){var a=t[s>>>2]>>>24-s%4*8&255;r.push((a>>>4).toString(16)),r.push((15&a).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,i=[],r=0;r<t;r+=2)i[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new l.init(i,t/2)}},h=c.Latin1={stringify:function(e){for(var t=e.words,i=e.sigBytes,r=[],s=0;s<i;s++){var a=t[s>>>2]>>>24-s%4*8&255;r.push(String.fromCharCode(a))}return r.join("")},parse:function(e){for(var t=e.length,i=[],r=0;r<t;r++)i[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new l.init(i,t)}},f=c.Utf8={stringify:function(e){try{return decodeURIComponent(escape(h.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return h.parse(unescape(encodeURIComponent(e)))}},p=o.BufferedBlockAlgorithm=d.extend({reset:function(){this._data=new l.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=f.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var i,r=this._data,s=r.words,a=r.sigBytes,n=this.blockSize,o=a/(4*n),d=(o=t?e.ceil(o):e.max((0|o)-this._minBufferSize,0))*n,c=e.min(4*d,a);if(d){for(var u=0;u<d;u+=n)this._doProcessBlock(s,u);i=s.splice(0,d),r.sigBytes-=c}return new l.init(i,c)},clone:function(){var e=d.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});o.Hasher=p.extend({cfg:d.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,i){return new e.init(i).finalize(t)}},_createHmacHelper:function(e){return function(t,i){return new _.HMAC.init(e,i).finalize(t)}}});var _=n.algo={};return n}(Math),i)}));zt((function(e,t){var i,r,s,a,n,o,d;e.exports=(s=(r=d=Lr).lib,a=s.Base,n=s.WordArray,(o=r.x64={}).Word=a.extend({init:function(e,t){this.high=e,this.low=t}}),o.WordArray=a.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=t!=i?t:8*e.length},toX32:function(){for(var e=this.words,t=e.length,i=[],r=0;r<t;r++){var s=e[r];i.push(s.high),i.push(s.low)}return n.create(i,this.sigBytes)},clone:function(){for(var e=a.clone.call(this),t=e.words=this.words.slice(0),i=t.length,r=0;r<i;r++)t[r]=t[r].clone();return e}}),d)})),zt((function(e,t){var i;e.exports=(i=Lr,function(){if("function"==typeof ArrayBuffer){var e=i.lib.WordArray,t=e.init,r=e.init=function(e){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){for(var i=e.byteLength,r=[],s=0;s<i;s++)r[s>>>2]|=e[s]<<24-s%4*8;t.call(this,r,i)}else t.apply(this,arguments)};r.prototype=e}}(),i.lib.WordArray)})),zt((function(e,t){var i;e.exports=(i=Lr,function(){var e=i,t=e.lib.WordArray,r=e.enc;function s(e){return e<<8&4278255360|e>>>8&16711935}r.Utf16=r.Utf16BE={stringify:function(e){for(var t=e.words,i=e.sigBytes,r=[],s=0;s<i;s+=2){var a=t[s>>>2]>>>16-s%4*8&65535;r.push(String.fromCharCode(a))}return r.join("")},parse:function(e){for(var i=e.length,r=[],s=0;s<i;s++)r[s>>>1]|=e.charCodeAt(s)<<16-s%2*16;return t.create(r,2*i)}},r.Utf16LE={stringify:function(e){for(var t=e.words,i=e.sigBytes,r=[],a=0;a<i;a+=2){var n=s(t[a>>>2]>>>16-a%4*8&65535);r.push(String.fromCharCode(n))}return r.join("")},parse:function(e){for(var i=e.length,r=[],a=0;a<i;a++)r[a>>>1]|=s(e.charCodeAt(a)<<16-a%2*16);return t.create(r,2*i)}}}(),i.enc.Utf16)})),zt((function(e,t){var i;e.exports=(i=Lr,function(){var e=i,t=e.lib.WordArray;function r(e,i,r){for(var s=[],a=0,n=0;n<i;n++)if(n%4){var o=r[e.charCodeAt(n-1)]<<n%4*2|r[e.charCodeAt(n)]>>>6-n%4*2;s[a>>>2]|=o<<24-a%4*8,a++}return t.create(s,a)}e.enc.Base64={stringify:function(e){var t=e.words,i=e.sigBytes,r=this._map;e.clamp();for(var s=[],a=0;a<i;a+=3)for(var n=(t[a>>>2]>>>24-a%4*8&255)<<16|(t[a+1>>>2]>>>24-(a+1)%4*8&255)<<8|t[a+2>>>2]>>>24-(a+2)%4*8&255,o=0;o<4&&a+.75*o<i;o++)s.push(r.charAt(n>>>6*(3-o)&63));var d=r.charAt(64);if(d)for(;s.length%4;)s.push(d);return s.join("")},parse:function(e){var t=e.length,i=this._map,s=this._reverseMap;if(!s){s=this._reverseMap=[];for(var a=0;a<i.length;a++)s[i.charCodeAt(a)]=a}var n=i.charAt(64);if(n){var o=e.indexOf(n);-1!==o&&(t=o)}return r(e,t,s)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),i.enc.Base64)})),zt((function(e,t){var i;e.exports=(i=Lr,function(){var e=i,t=e.lib.WordArray;function r(e,i,r){for(var s=[],a=0,n=0;n<i;n++)if(n%4){var o=r[e.charCodeAt(n-1)]<<n%4*2|r[e.charCodeAt(n)]>>>6-n%4*2;s[a>>>2]|=o<<24-a%4*8,a++}return t.create(s,a)}e.enc.Base64url={stringify:function(e,t){void 0===t&&(t=!0);var i=e.words,r=e.sigBytes,s=t?this._safe_map:this._map;e.clamp();for(var a=[],n=0;n<r;n+=3)for(var o=(i[n>>>2]>>>24-n%4*8&255)<<16|(i[n+1>>>2]>>>24-(n+1)%4*8&255)<<8|i[n+2>>>2]>>>24-(n+2)%4*8&255,d=0;d<4&&n+.75*d<r;d++)a.push(s.charAt(o>>>6*(3-d)&63));var l=s.charAt(64);if(l)for(;a.length%4;)a.push(l);return a.join("")},parse:function(e,t){void 0===t&&(t=!0);var i=e.length,s=t?this._safe_map:this._map,a=this._reverseMap;if(!a){a=this._reverseMap=[];for(var n=0;n<s.length;n++)a[s.charCodeAt(n)]=n}var o=s.charAt(64);if(o){var d=e.indexOf(o);-1!==d&&(i=d)}return r(e,i,a)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"}}(),i.enc.Base64url)})),zt((function(e,t){var i;e.exports=(i=Lr,function(e){var t=i,r=t.lib,s=r.WordArray,a=r.Hasher,n=t.algo,o=[];!function(){for(var t=0;t<64;t++)o[t]=4294967296*e.abs(e.sin(t+1))|0}();var d=n.MD5=a.extend({_doReset:function(){this._hash=new s.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var i=0;i<16;i++){var r=t+i,s=e[r];e[r]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8)}var a=this._hash.words,n=e[t+0],d=e[t+1],f=e[t+2],p=e[t+3],_=e[t+4],m=e[t+5],g=e[t+6],y=e[t+7],b=e[t+8],v=e[t+9],S=e[t+10],w=e[t+11],A=e[t+12],B=e[t+13],x=e[t+14],U=e[t+15],E=a[0],T=a[1],k=a[2],C=a[3];E=l(E,T,k,C,n,7,o[0]),C=l(C,E,T,k,d,12,o[1]),k=l(k,C,E,T,f,17,o[2]),T=l(T,k,C,E,p,22,o[3]),E=l(E,T,k,C,_,7,o[4]),C=l(C,E,T,k,m,12,o[5]),k=l(k,C,E,T,g,17,o[6]),T=l(T,k,C,E,y,22,o[7]),E=l(E,T,k,C,b,7,o[8]),C=l(C,E,T,k,v,12,o[9]),k=l(k,C,E,T,S,17,o[10]),T=l(T,k,C,E,w,22,o[11]),E=l(E,T,k,C,A,7,o[12]),C=l(C,E,T,k,B,12,o[13]),k=l(k,C,E,T,x,17,o[14]),E=c(E,T=l(T,k,C,E,U,22,o[15]),k,C,d,5,o[16]),C=c(C,E,T,k,g,9,o[17]),k=c(k,C,E,T,w,14,o[18]),T=c(T,k,C,E,n,20,o[19]),E=c(E,T,k,C,m,5,o[20]),C=c(C,E,T,k,S,9,o[21]),k=c(k,C,E,T,U,14,o[22]),T=c(T,k,C,E,_,20,o[23]),E=c(E,T,k,C,v,5,o[24]),C=c(C,E,T,k,x,9,o[25]),k=c(k,C,E,T,p,14,o[26]),T=c(T,k,C,E,b,20,o[27]),E=c(E,T,k,C,B,5,o[28]),C=c(C,E,T,k,f,9,o[29]),k=c(k,C,E,T,y,14,o[30]),E=u(E,T=c(T,k,C,E,A,20,o[31]),k,C,m,4,o[32]),C=u(C,E,T,k,b,11,o[33]),k=u(k,C,E,T,w,16,o[34]),T=u(T,k,C,E,x,23,o[35]),E=u(E,T,k,C,d,4,o[36]),C=u(C,E,T,k,_,11,o[37]),k=u(k,C,E,T,y,16,o[38]),T=u(T,k,C,E,S,23,o[39]),E=u(E,T,k,C,B,4,o[40]),C=u(C,E,T,k,n,11,o[41]),k=u(k,C,E,T,p,16,o[42]),T=u(T,k,C,E,g,23,o[43]),E=u(E,T,k,C,v,4,o[44]),C=u(C,E,T,k,A,11,o[45]),k=u(k,C,E,T,U,16,o[46]),E=h(E,T=u(T,k,C,E,f,23,o[47]),k,C,n,6,o[48]),C=h(C,E,T,k,y,10,o[49]),k=h(k,C,E,T,x,15,o[50]),T=h(T,k,C,E,m,21,o[51]),E=h(E,T,k,C,A,6,o[52]),C=h(C,E,T,k,p,10,o[53]),k=h(k,C,E,T,S,15,o[54]),T=h(T,k,C,E,d,21,o[55]),E=h(E,T,k,C,b,6,o[56]),C=h(C,E,T,k,U,10,o[57]),k=h(k,C,E,T,g,15,o[58]),T=h(T,k,C,E,B,21,o[59]),E=h(E,T,k,C,_,6,o[60]),C=h(C,E,T,k,w,10,o[61]),k=h(k,C,E,T,f,15,o[62]),T=h(T,k,C,E,v,21,o[63]),a[0]=a[0]+E|0,a[1]=a[1]+T|0,a[2]=a[2]+k|0,a[3]=a[3]+C|0},_doFinalize:function(){var t=this._data,i=t.words,r=8*this._nDataBytes,s=8*t.sigBytes;i[s>>>5]|=128<<24-s%32;var a=e.floor(r/4294967296),n=r;i[15+(s+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),i[14+(s+64>>>9<<4)]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8),t.sigBytes=4*(i.length+1),this._process();for(var o=this._hash,d=o.words,l=0;l<4;l++){var c=d[l];d[l]=16711935&(c<<8|c>>>24)|4278255360&(c<<24|c>>>8)}return o},clone:function(){var e=a.clone.call(this);return e._hash=this._hash.clone(),e}});function l(e,t,i,r,s,a,n){var o=e+(t&i|~t&r)+s+n;return(o<<a|o>>>32-a)+t}function c(e,t,i,r,s,a,n){var o=e+(t&r|i&~r)+s+n;return(o<<a|o>>>32-a)+t}function u(e,t,i,r,s,a,n){var o=e+(t^i^r)+s+n;return(o<<a|o>>>32-a)+t}function h(e,t,i,r,s,a,n){var o=e+(i^(t|~r))+s+n;return(o<<a|o>>>32-a)+t}t.MD5=a._createHelper(d),t.HmacMD5=a._createHmacHelper(d)}(Math),i.MD5)})),zt((function(e,t){var i,r,s,a,n,o,d,l;e.exports=(r=(i=l=Lr).lib,s=r.WordArray,a=r.Hasher,n=i.algo,o=[],d=n.SHA1=a.extend({_doReset:function(){this._hash=new s.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var i=this._hash.words,r=i[0],s=i[1],a=i[2],n=i[3],d=i[4],l=0;l<80;l++){if(l<16)o[l]=0|e[t+l];else{var c=o[l-3]^o[l-8]^o[l-14]^o[l-16];o[l]=c<<1|c>>>31}var u=(r<<5|r>>>27)+d+o[l];u+=l<20?1518500249+(s&a|~s&n):l<40?1859775393+(s^a^n):l<60?(s&a|s&n|a&n)-1894007588:(s^a^n)-899497514,d=n,n=a,a=s<<30|s>>>2,s=r,r=u}i[0]=i[0]+r|0,i[1]=i[1]+s|0,i[2]=i[2]+a|0,i[3]=i[3]+n|0,i[4]=i[4]+d|0},_doFinalize:function(){var e=this._data,t=e.words,i=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[14+(r+64>>>9<<4)]=Math.floor(i/4294967296),t[15+(r+64>>>9<<4)]=i,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=a.clone.call(this);return e._hash=this._hash.clone(),e}}),i.SHA1=a._createHelper(d),i.HmacSHA1=a._createHmacHelper(d),l.SHA1)})),zt((function(e,t){var i;e.exports=(i=Lr,function(e){var t=i,r=t.lib,s=r.WordArray,a=r.Hasher,n=t.algo,o=[],d=[];!function(){function t(t){for(var i=e.sqrt(t),r=2;r<=i;r++)if(!(t%r))return!1;return!0}function i(e){return 4294967296*(e-(0|e))|0}for(var r=2,s=0;s<64;)t(r)&&(s<8&&(o[s]=i(e.pow(r,.5))),d[s]=i(e.pow(r,1/3)),s++),r++}();var l=[],c=n.SHA256=a.extend({_doReset:function(){this._hash=new s.init(o.slice(0))},_doProcessBlock:function(e,t){for(var i=this._hash.words,r=i[0],s=i[1],a=i[2],n=i[3],o=i[4],c=i[5],u=i[6],h=i[7],f=0;f<64;f++){if(f<16)l[f]=0|e[t+f];else{var p=l[f-15],_=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,m=l[f-2],g=(m<<15|m>>>17)^(m<<13|m>>>19)^m>>>10;l[f]=_+l[f-7]+g+l[f-16]}var y=r&s^r&a^s&a,b=(r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22),v=h+((o<<26|o>>>6)^(o<<21|o>>>11)^(o<<7|o>>>25))+(o&c^~o&u)+d[f]+l[f];h=u,u=c,c=o,o=n+v|0,n=a,a=s,s=r,r=v+(b+y)|0}i[0]=i[0]+r|0,i[1]=i[1]+s|0,i[2]=i[2]+a|0,i[3]=i[3]+n|0,i[4]=i[4]+o|0,i[5]=i[5]+c|0,i[6]=i[6]+u|0,i[7]=i[7]+h|0},_doFinalize:function(){var t=this._data,i=t.words,r=8*this._nDataBytes,s=8*t.sigBytes;return i[s>>>5]|=128<<24-s%32,i[14+(s+64>>>9<<4)]=e.floor(r/4294967296),i[15+(s+64>>>9<<4)]=r,t.sigBytes=4*i.length,this._process(),this._hash},clone:function(){var e=a.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=a._createHelper(c),t.HmacSHA256=a._createHmacHelper(c)}(Math),i.SHA256)})),zt((function(e,t){var i,r,s,a,n,o;e.exports=(r=(i=o=Lr).lib.WordArray,s=i.algo,a=s.SHA256,n=s.SHA224=a.extend({_doReset:function(){this._hash=new r.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var e=a._doFinalize.call(this);return e.sigBytes-=4,e}}),i.SHA224=a._createHelper(n),i.HmacSHA224=a._createHmacHelper(n),o.SHA224)})),zt((function(e,t){var i;e.exports=(i=Lr,function(){var e=i,t=e.lib.Hasher,r=e.x64,s=r.Word,a=r.WordArray,n=e.algo;function o(){return s.create.apply(s,arguments)}var d=[o(1116352408,3609767458),o(1899447441,602891725),o(3049323471,3964484399),o(3921009573,2173295548),o(961987163,4081628472),o(1508970993,3053834265),o(2453635748,2937671579),o(2870763221,3664609560),o(3624381080,2734883394),o(310598401,1164996542),o(607225278,1323610764),o(1426881987,3590304994),o(1925078388,4068182383),o(2162078206,991336113),o(2614888103,633803317),o(3248222580,3479774868),o(3835390401,2666613458),o(4022224774,944711139),o(264347078,2341262773),o(604807628,2007800933),o(770255983,1495990901),o(1249150122,1856431235),o(1555081692,3175218132),o(1996064986,2198950837),o(2554220882,3999719339),o(2821834349,766784016),o(2952996808,2566594879),o(3210313671,3203337956),o(3336571891,1034457026),o(3584528711,2466948901),o(113926993,3758326383),o(338241895,168717936),o(666307205,1188179964),o(773529912,1546045734),o(1294757372,1522805485),o(1396182291,2643833823),o(1695183700,2343527390),o(1986661051,1014477480),o(2177026350,1206759142),o(2456956037,344077627),o(2730485921,1290863460),o(2820302411,3158454273),o(3259730800,3505952657),o(3345764771,106217008),o(3516065817,3606008344),o(3600352804,1432725776),o(4094571909,1467031594),o(275423344,851169720),o(430227734,3100823752),o(506948616,1363258195),o(659060556,3750685593),o(883997877,3785050280),o(958139571,3318307427),o(1322822218,3812723403),o(1537002063,2003034995),o(1747873779,3602036899),o(1955562222,1575990012),o(2024104815,1125592928),o(2227730452,2716904306),o(2361852424,442776044),o(2428436474,593698344),o(2756734187,3733110249),o(3204031479,2999351573),o(3329325298,3815920427),o(3391569614,3928383900),o(3515267271,566280711),o(3940187606,3454069534),o(4118630271,4000239992),o(116418474,1914138554),o(174292421,2731055270),o(289380356,3203993006),o(460393269,320620315),o(685471733,587496836),o(852142971,1086792851),o(1017036298,365543100),o(1126000580,2618297676),o(1288033470,3409855158),o(1501505948,4234509866),o(1607167915,987167468),o(1816402316,1246189591)],l=[];!function(){for(var e=0;e<80;e++)l[e]=o()}();var c=n.SHA512=t.extend({_doReset:function(){this._hash=new a.init([new s.init(1779033703,4089235720),new s.init(3144134277,2227873595),new s.init(1013904242,4271175723),new s.init(2773480762,1595750129),new s.init(1359893119,2917565137),new s.init(2600822924,725511199),new s.init(528734635,4215389547),new s.init(1541459225,327033209)])},_doProcessBlock:function(e,t){for(var i=this._hash.words,r=i[0],s=i[1],a=i[2],n=i[3],o=i[4],c=i[5],u=i[6],h=i[7],f=r.high,p=r.low,_=s.high,m=s.low,g=a.high,y=a.low,b=n.high,v=n.low,S=o.high,w=o.low,A=c.high,B=c.low,x=u.high,U=u.low,E=h.high,T=h.low,k=f,C=p,D=_,I=m,L=g,F=y,P=b,M=v,z=S,R=w,N=A,G=B,O=x,H=U,V=E,$=T,W=0;W<80;W++){var Y,q,j=l[W];if(W<16)q=j.high=0|e[t+2*W],Y=j.low=0|e[t+2*W+1];else{var K=l[W-15],X=K.high,Z=K.low,J=(X>>>1|Z<<31)^(X>>>8|Z<<24)^X>>>7,Q=(Z>>>1|X<<31)^(Z>>>8|X<<24)^(Z>>>7|X<<25),ee=l[W-2],te=ee.high,ie=ee.low,re=(te>>>19|ie<<13)^(te<<3|ie>>>29)^te>>>6,se=(ie>>>19|te<<13)^(ie<<3|te>>>29)^(ie>>>6|te<<26),ae=l[W-7],ne=ae.high,oe=ae.low,de=l[W-16],le=de.high,ce=de.low;q=(q=(q=J+ne+((Y=Q+oe)>>>0<Q>>>0?1:0))+re+((Y+=se)>>>0<se>>>0?1:0))+le+((Y+=ce)>>>0<ce>>>0?1:0),j.high=q,j.low=Y}var ue,he=z&N^~z&O,fe=R&G^~R&H,pe=k&D^k&L^D&L,_e=C&I^C&F^I&F,me=(k>>>28|C<<4)^(k<<30|C>>>2)^(k<<25|C>>>7),ge=(C>>>28|k<<4)^(C<<30|k>>>2)^(C<<25|k>>>7),ye=(z>>>14|R<<18)^(z>>>18|R<<14)^(z<<23|R>>>9),be=(R>>>14|z<<18)^(R>>>18|z<<14)^(R<<23|z>>>9),ve=d[W],Se=ve.high,we=ve.low,Ae=V+ye+((ue=$+be)>>>0<$>>>0?1:0),Be=ge+_e;V=O,$=H,O=N,H=G,N=z,G=R,z=P+(Ae=(Ae=(Ae=Ae+he+((ue+=fe)>>>0<fe>>>0?1:0))+Se+((ue+=we)>>>0<we>>>0?1:0))+q+((ue+=Y)>>>0<Y>>>0?1:0))+((R=M+ue|0)>>>0<M>>>0?1:0)|0,P=L,M=F,L=D,F=I,D=k,I=C,k=Ae+(me+pe+(Be>>>0<ge>>>0?1:0))+((C=ue+Be|0)>>>0<ue>>>0?1:0)|0}p=r.low=p+C,r.high=f+k+(p>>>0<C>>>0?1:0),m=s.low=m+I,s.high=_+D+(m>>>0<I>>>0?1:0),y=a.low=y+F,a.high=g+L+(y>>>0<F>>>0?1:0),v=n.low=v+M,n.high=b+P+(v>>>0<M>>>0?1:0),w=o.low=w+R,o.high=S+z+(w>>>0<R>>>0?1:0),B=c.low=B+G,c.high=A+N+(B>>>0<G>>>0?1:0),U=u.low=U+H,u.high=x+O+(U>>>0<H>>>0?1:0),T=h.low=T+$,h.high=E+V+(T>>>0<$>>>0?1:0)},_doFinalize:function(){var e=this._data,t=e.words,i=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[30+(r+128>>>10<<5)]=Math.floor(i/4294967296),t[31+(r+128>>>10<<5)]=i,e.sigBytes=4*t.length,this._process(),this._hash.toX32()},clone:function(){var e=t.clone.call(this);return e._hash=this._hash.clone(),e},blockSize:32});e.SHA512=t._createHelper(c),e.HmacSHA512=t._createHmacHelper(c)}(),i.SHA512)})),zt((function(e,t){var i,r,s,a,n,o,d,l;e.exports=(r=(i=l=Lr).x64,s=r.Word,a=r.WordArray,n=i.algo,o=n.SHA512,d=n.SHA384=o.extend({_doReset:function(){this._hash=new a.init([new s.init(3418070365,3238371032),new s.init(1654270250,914150663),new s.init(2438529370,812702999),new s.init(355462360,4144912697),new s.init(1731405415,4290775857),new s.init(2394180231,1750603025),new s.init(3675008525,1694076839),new s.init(1203062813,3204075428)])},_doFinalize:function(){var e=o._doFinalize.call(this);return e.sigBytes-=16,e}}),i.SHA384=o._createHelper(d),i.HmacSHA384=o._createHmacHelper(d),l.SHA384)})),zt((function(e,t){var i;e.exports=(i=Lr,function(e){var t=i,r=t.lib,s=r.WordArray,a=r.Hasher,n=t.x64.Word,o=t.algo,d=[],l=[],c=[];!function(){for(var e=1,t=0,i=0;i<24;i++){d[e+5*t]=(i+1)*(i+2)/2%64;var r=(2*e+3*t)%5;e=t%5,t=r}for(e=0;e<5;e++)for(t=0;t<5;t++)l[e+5*t]=t+(2*e+3*t)%5*5;for(var s=1,a=0;a<24;a++){for(var o=0,u=0,h=0;h<7;h++){if(1&s){var f=(1<<h)-1;f<32?u^=1<<f:o^=1<<f-32}128&s?s=s<<1^113:s<<=1}c[a]=n.create(o,u)}}();var u=[];!function(){for(var e=0;e<25;e++)u[e]=n.create()}();var h=o.SHA3=a.extend({cfg:a.cfg.extend({outputLength:512}),_doReset:function(){for(var e=this._state=[],t=0;t<25;t++)e[t]=new n.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(e,t){for(var i=this._state,r=this.blockSize/2,s=0;s<r;s++){var a=e[t+2*s],n=e[t+2*s+1];a=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),n=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8),(T=i[s]).high^=n,T.low^=a}for(var o=0;o<24;o++){for(var h=0;h<5;h++){for(var f=0,p=0,_=0;_<5;_++)f^=(T=i[h+5*_]).high,p^=T.low;var m=u[h];m.high=f,m.low=p}for(h=0;h<5;h++){var g=u[(h+4)%5],y=u[(h+1)%5],b=y.high,v=y.low;for(f=g.high^(b<<1|v>>>31),p=g.low^(v<<1|b>>>31),_=0;_<5;_++)(T=i[h+5*_]).high^=f,T.low^=p}for(var S=1;S<25;S++){var w=(T=i[S]).high,A=T.low,B=d[S];B<32?(f=w<<B|A>>>32-B,p=A<<B|w>>>32-B):(f=A<<B-32|w>>>64-B,p=w<<B-32|A>>>64-B);var x=u[l[S]];x.high=f,x.low=p}var U=u[0],E=i[0];for(U.high=E.high,U.low=E.low,h=0;h<5;h++)for(_=0;_<5;_++){var T=i[S=h+5*_],k=u[S],C=u[(h+1)%5+5*_],D=u[(h+2)%5+5*_];T.high=k.high^~C.high&D.high,T.low=k.low^~C.low&D.low}T=i[0];var I=c[o];T.high^=I.high,T.low^=I.low}},_doFinalize:function(){var t=this._data,i=t.words;this._nDataBytes;var r=8*t.sigBytes,a=32*this.blockSize;i[r>>>5]|=1<<24-r%32,i[(e.ceil((r+1)/a)*a>>>5)-1]|=128,t.sigBytes=4*i.length,this._process();for(var n=this._state,o=this.cfg.outputLength/8,d=o/8,l=[],c=0;c<d;c++){var u=n[c],h=u.high,f=u.low;h=16711935&(h<<8|h>>>24)|4278255360&(h<<24|h>>>8),f=16711935&(f<<8|f>>>24)|4278255360&(f<<24|f>>>8),l.push(f),l.push(h)}return new s.init(l,o)},clone:function(){for(var e=a.clone.call(this),t=e._state=this._state.slice(0),i=0;i<25;i++)t[i]=t[i].clone();return e}});t.SHA3=a._createHelper(h),t.HmacSHA3=a._createHmacHelper(h)}(Math),i.SHA3)})),zt((function(e,t){var i;e.exports=(i=Lr,function(e){var t=i,r=t.lib,s=r.WordArray,a=r.Hasher,n=t.algo,o=s.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),d=s.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),l=s.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),c=s.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),u=s.create([0,1518500249,1859775393,2400959708,2840853838]),h=s.create([1352829926,1548603684,1836072691,2053994217,0]),f=n.RIPEMD160=a.extend({_doReset:function(){this._hash=s.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var i=0;i<16;i++){var r=t+i,s=e[r];e[r]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8)}var a,n,f,v,S,w,A,B,x,U,E,T=this._hash.words,k=u.words,C=h.words,D=o.words,I=d.words,L=l.words,F=c.words;for(w=a=T[0],A=n=T[1],B=f=T[2],x=v=T[3],U=S=T[4],i=0;i<80;i+=1)E=a+e[t+D[i]]|0,E+=i<16?p(n,f,v)+k[0]:i<32?_(n,f,v)+k[1]:i<48?m(n,f,v)+k[2]:i<64?g(n,f,v)+k[3]:y(n,f,v)+k[4],E=(E=b(E|=0,L[i]))+S|0,a=S,S=v,v=b(f,10),f=n,n=E,E=w+e[t+I[i]]|0,E+=i<16?y(A,B,x)+C[0]:i<32?g(A,B,x)+C[1]:i<48?m(A,B,x)+C[2]:i<64?_(A,B,x)+C[3]:p(A,B,x)+C[4],E=(E=b(E|=0,F[i]))+U|0,w=U,U=x,x=b(B,10),B=A,A=E;E=T[1]+f+x|0,T[1]=T[2]+v+U|0,T[2]=T[3]+S+w|0,T[3]=T[4]+a+A|0,T[4]=T[0]+n+B|0,T[0]=E},_doFinalize:function(){var e=this._data,t=e.words,i=8*this._nDataBytes,r=8*e.sigBytes;t[r>>>5]|=128<<24-r%32,t[14+(r+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),e.sigBytes=4*(t.length+1),this._process();for(var s=this._hash,a=s.words,n=0;n<5;n++){var o=a[n];a[n]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}return s},clone:function(){var e=a.clone.call(this);return e._hash=this._hash.clone(),e}});function p(e,t,i){return e^t^i}function _(e,t,i){return e&t|~e&i}function m(e,t,i){return(e|~t)^i}function g(e,t,i){return e&i|t&~i}function y(e,t,i){return e^(t|~i)}function b(e,t){return e<<t|e>>>32-t}t.RIPEMD160=a._createHelper(f),t.HmacRIPEMD160=a._createHmacHelper(f)}(),i.RIPEMD160)})),zt((function(e,t){var i,r,s;e.exports=(r=(i=Lr).lib.Base,s=i.enc.Utf8,void(i.algo.HMAC=r.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=s.parse(t));var i=e.blockSize,r=4*i;t.sigBytes>r&&(t=e.finalize(t)),t.clamp();for(var a=this._oKey=t.clone(),n=this._iKey=t.clone(),o=a.words,d=n.words,l=0;l<i;l++)o[l]^=1549556828,d[l]^=909522486;a.sigBytes=n.sigBytes=r,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,i=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(i))}})))})),zt((function(e,t){var i,r,s,a,n,o,d,l,c;e.exports=(r=(i=c=Lr).lib,s=r.Base,a=r.WordArray,n=i.algo,o=n.SHA256,d=n.HMAC,l=n.PBKDF2=s.extend({cfg:s.extend({keySize:4,hasher:o,iterations:25e4}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var i=this.cfg,r=d.create(i.hasher,e),s=a.create(),n=a.create([1]),o=s.words,l=n.words,c=i.keySize,u=i.iterations;o.length<c;){var h=r.update(t).finalize(n);r.reset();for(var f=h.words,p=f.length,_=h,m=1;m<u;m++){_=r.finalize(_),r.reset();for(var g=_.words,y=0;y<p;y++)f[y]^=g[y]}s.concat(h),l[0]++}return s.sigBytes=4*c,s}}),i.PBKDF2=function(e,t,i){return l.create(i).compute(e,t)},c.PBKDF2)})),zt((function(e,t){var i,r,s,a,n,o,d,l;e.exports=(r=(i=l=Lr).lib,s=r.Base,a=r.WordArray,n=i.algo,o=n.MD5,d=n.EvpKDF=s.extend({cfg:s.extend({keySize:4,hasher:o,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var i,r=this.cfg,s=r.hasher.create(),n=a.create(),o=n.words,d=r.keySize,l=r.iterations;o.length<d;){i&&s.update(i),i=s.update(e).finalize(t),s.reset();for(var c=1;c<l;c++)i=s.finalize(i),s.reset();n.concat(i)}return n.sigBytes=4*d,n}}),i.EvpKDF=function(e,t,i){return d.create(i).compute(e,t)},l.EvpKDF)})),zt((function(e,t){var i;e.exports=void((i=Lr).lib.Cipher||function(e){var t=i,r=t.lib,s=r.Base,a=r.WordArray,n=r.BufferedBlockAlgorithm,o=t.enc;o.Utf8;var d=o.Base64,l=t.algo.EvpKDF,c=r.Cipher=n.extend({cfg:s.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,i){this.cfg=this.cfg.extend(i),this._xformMode=e,this._key=t,this.reset()},reset:function(){n.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function e(e){return"string"==typeof e?b:g}return function(t){return{encrypt:function(i,r,s){return e(r).encrypt(t,i,r,s)},decrypt:function(i,r,s){return e(r).decrypt(t,i,r,s)}}}}()});r.StreamCipher=c.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var u=t.mode={},h=r.BlockCipherMode=s.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),f=u.CBC=function(){var t=h.extend();function i(t,i,r){var s,a=this._iv;a?(s=a,this._iv=e):s=this._prevBlock;for(var n=0;n<r;n++)t[i+n]^=s[n]}return t.Encryptor=t.extend({processBlock:function(e,t){var r=this._cipher,s=r.blockSize;i.call(this,e,t,s),r.encryptBlock(e,t),this._prevBlock=e.slice(t,t+s)}}),t.Decryptor=t.extend({processBlock:function(e,t){var r=this._cipher,s=r.blockSize,a=e.slice(t,t+s);r.decryptBlock(e,t),i.call(this,e,t,s),this._prevBlock=a}}),t}(),p=(t.pad={}).Pkcs7={pad:function(e,t){for(var i=4*t,r=i-e.sigBytes%i,s=r<<24|r<<16|r<<8|r,n=[],o=0;o<r;o+=4)n.push(s);var d=a.create(n,r);e.concat(d)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}};r.BlockCipher=c.extend({cfg:c.cfg.extend({mode:f,padding:p}),reset:function(){var e;c.reset.call(this);var t=this.cfg,i=t.iv,r=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=r.createEncryptor:(e=r.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,i&&i.words):(this._mode=e.call(r,this,i&&i.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4});var _=r.CipherParams=s.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),m=(t.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext,i=e.salt;return(i?a.create([1398893684,1701076831]).concat(i).concat(t):t).toString(d)},parse:function(e){var t,i=d.parse(e),r=i.words;return 1398893684==r[0]&&1701076831==r[1]&&(t=a.create(r.slice(2,4)),r.splice(0,4),i.sigBytes-=16),_.create({ciphertext:i,salt:t})}},g=r.SerializableCipher=s.extend({cfg:s.extend({format:m}),encrypt:function(e,t,i,r){r=this.cfg.extend(r);var s=e.createEncryptor(i,r),a=s.finalize(t),n=s.cfg;return _.create({ciphertext:a,key:i,iv:n.iv,algorithm:e,mode:n.mode,padding:n.padding,blockSize:e.blockSize,formatter:r.format})},decrypt:function(e,t,i,r){return r=this.cfg.extend(r),t=this._parse(t,r.format),e.createDecryptor(i,r).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),y=(t.kdf={}).OpenSSL={execute:function(e,t,i,r,s){if(r||(r=a.random(8)),s)n=l.create({keySize:t+i,hasher:s}).compute(e,r);else var n=l.create({keySize:t+i}).compute(e,r);var o=a.create(n.words.slice(t),4*i);return n.sigBytes=4*t,_.create({key:n,iv:o,salt:r})}},b=r.PasswordBasedCipher=g.extend({cfg:g.cfg.extend({kdf:y}),encrypt:function(e,t,i,r){var s=(r=this.cfg.extend(r)).kdf.execute(i,e.keySize,e.ivSize,r.salt,r.hasher);r.iv=s.iv;var a=g.encrypt.call(this,e,t,s.key,r);return a.mixIn(s),a},decrypt:function(e,t,i,r){r=this.cfg.extend(r),t=this._parse(t,r.format);var s=r.kdf.execute(i,e.keySize,e.ivSize,t.salt,r.hasher);return r.iv=s.iv,g.decrypt.call(this,e,t,s.key,r)}})}())})),zt((function(e,t){var i;e.exports=((i=Lr).mode.CFB=function(){var e=i.lib.BlockCipherMode.extend();function t(e,t,i,r){var s,a=this._iv;a?(s=a.slice(0),this._iv=void 0):s=this._prevBlock,r.encryptBlock(s,0);for(var n=0;n<i;n++)e[t+n]^=s[n]}return e.Encryptor=e.extend({processBlock:function(e,i){var r=this._cipher,s=r.blockSize;t.call(this,e,i,s,r),this._prevBlock=e.slice(i,i+s)}}),e.Decryptor=e.extend({processBlock:function(e,i){var r=this._cipher,s=r.blockSize,a=e.slice(i,i+s);t.call(this,e,i,s,r),this._prevBlock=a}}),e}(),i.mode.CFB)})),zt((function(e,t){var i,r,s;e.exports=((s=Lr).mode.CTR=(i=s.lib.BlockCipherMode.extend(),r=i.Encryptor=i.extend({processBlock:function(e,t){var i=this._cipher,r=i.blockSize,s=this._iv,a=this._counter;s&&(a=this._counter=s.slice(0),this._iv=void 0);var n=a.slice(0);i.encryptBlock(n,0),a[r-1]=a[r-1]+1|0;for(var o=0;o<r;o++)e[t+o]^=n[o]}}),i.Decryptor=r,i),s.mode.CTR)})),zt((function(e,t){var i;e.exports=((i=Lr).mode.CTRGladman=function(){var e=i.lib.BlockCipherMode.extend();function t(e){if(255==(e>>24&255)){var t=e>>16&255,i=e>>8&255,r=255&e;255===t?(t=0,255===i?(i=0,255===r?r=0:++r):++i):++t,e=0,e+=t<<16,e+=i<<8,e+=r}else e+=1<<24;return e}function r(e){return 0===(e[0]=t(e[0]))&&(e[1]=t(e[1])),e}var s=e.Encryptor=e.extend({processBlock:function(e,t){var i=this._cipher,s=i.blockSize,a=this._iv,n=this._counter;a&&(n=this._counter=a.slice(0),this._iv=void 0),r(n);var o=n.slice(0);i.encryptBlock(o,0);for(var d=0;d<s;d++)e[t+d]^=o[d]}});return e.Decryptor=s,e}(),i.mode.CTRGladman)})),zt((function(e,t){var i,r,s;e.exports=((s=Lr).mode.OFB=(i=s.lib.BlockCipherMode.extend(),r=i.Encryptor=i.extend({processBlock:function(e,t){var i=this._cipher,r=i.blockSize,s=this._iv,a=this._keystream;s&&(a=this._keystream=s.slice(0),this._iv=void 0),i.encryptBlock(a,0);for(var n=0;n<r;n++)e[t+n]^=a[n]}}),i.Decryptor=r,i),s.mode.OFB)})),zt((function(e,t){var i,r;e.exports=((r=Lr).mode.ECB=((i=r.lib.BlockCipherMode.extend()).Encryptor=i.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),i.Decryptor=i.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),i),r.mode.ECB)})),zt((function(e,t){var i;e.exports=((i=Lr).pad.AnsiX923={pad:function(e,t){var i=e.sigBytes,r=4*t,s=r-i%r,a=i+s-1;e.clamp(),e.words[a>>>2]|=s<<24-a%4*8,e.sigBytes+=s},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},i.pad.Ansix923)})),zt((function(e,t){var i;e.exports=((i=Lr).pad.Iso10126={pad:function(e,t){var r=4*t,s=r-e.sigBytes%r;e.concat(i.lib.WordArray.random(s-1)).concat(i.lib.WordArray.create([s<<24],1))},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},i.pad.Iso10126)})),zt((function(e,t){var i;e.exports=((i=Lr).pad.Iso97971={pad:function(e,t){e.concat(i.lib.WordArray.create([2147483648],1)),i.pad.ZeroPadding.pad(e,t)},unpad:function(e){i.pad.ZeroPadding.unpad(e),e.sigBytes--}},i.pad.Iso97971)})),zt((function(e,t){var i;e.exports=((i=Lr).pad.ZeroPadding={pad:function(e,t){var i=4*t;e.clamp(),e.sigBytes+=i-(e.sigBytes%i||i)},unpad:function(e){var t=e.words,i=e.sigBytes-1;for(i=e.sigBytes-1;i>=0;i--)if(t[i>>>2]>>>24-i%4*8&255){e.sigBytes=i+1;break}}},i.pad.ZeroPadding)})),zt((function(e,t){var i;e.exports=((i=Lr).pad.NoPadding={pad:function(){},unpad:function(){}},i.pad.NoPadding)})),zt((function(e,t){var i;e.exports=(i=Lr,function(e){var t=i,r=t.lib.CipherParams,s=t.enc.Hex;t.format.Hex={stringify:function(e){return e.ciphertext.toString(s)},parse:function(e){var t=s.parse(e);return r.create({ciphertext:t})}}}(),i.format.Hex)})),zt((function(e,t){var i;e.exports=(i=Lr,function(){var e=i,t=e.lib.BlockCipher,r=e.algo,s=[],a=[],n=[],o=[],d=[],l=[],c=[],u=[],h=[],f=[];!function(){for(var e=[],t=0;t<256;t++)e[t]=t<128?t<<1:t<<1^283;var i=0,r=0;for(t=0;t<256;t++){var p=r^r<<1^r<<2^r<<3^r<<4;p=p>>>8^255&p^99,s[i]=p,a[p]=i;var _=e[i],m=e[_],g=e[m],y=257*e[p]^16843008*p;n[i]=y<<24|y>>>8,o[i]=y<<16|y>>>16,d[i]=y<<8|y>>>24,l[i]=y,y=16843009*g^65537*m^257*_^16843008*i,c[p]=y<<24|y>>>8,u[p]=y<<16|y>>>16,h[p]=y<<8|y>>>24,f[p]=y,i?(i=_^e[e[e[g^_]]],r^=e[e[r]]):i=r=1}}();var p=[0,1,2,4,8,16,32,64,128,27,54],_=r.AES=t.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e=this._keyPriorReset=this._key,t=e.words,i=e.sigBytes/4,r=4*((this._nRounds=i+6)+1),a=this._keySchedule=[],n=0;n<r;n++)n<i?a[n]=t[n]:(l=a[n-1],n%i?i>6&&n%i==4&&(l=s[l>>>24]<<24|s[l>>>16&255]<<16|s[l>>>8&255]<<8|s[255&l]):(l=s[(l=l<<8|l>>>24)>>>24]<<24|s[l>>>16&255]<<16|s[l>>>8&255]<<8|s[255&l],l^=p[n/i|0]<<24),a[n]=a[n-i]^l);for(var o=this._invKeySchedule=[],d=0;d<r;d++){if(n=r-d,d%4)var l=a[n];else l=a[n-4];o[d]=d<4||n<=4?l:c[s[l>>>24]]^u[s[l>>>16&255]]^h[s[l>>>8&255]]^f[s[255&l]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,n,o,d,l,s)},decryptBlock:function(e,t){var i=e[t+1];e[t+1]=e[t+3],e[t+3]=i,this._doCryptBlock(e,t,this._invKeySchedule,c,u,h,f,a),i=e[t+1],e[t+1]=e[t+3],e[t+3]=i},_doCryptBlock:function(e,t,i,r,s,a,n,o){for(var d=this._nRounds,l=e[t]^i[0],c=e[t+1]^i[1],u=e[t+2]^i[2],h=e[t+3]^i[3],f=4,p=1;p<d;p++){var _=r[l>>>24]^s[c>>>16&255]^a[u>>>8&255]^n[255&h]^i[f++],m=r[c>>>24]^s[u>>>16&255]^a[h>>>8&255]^n[255&l]^i[f++],g=r[u>>>24]^s[h>>>16&255]^a[l>>>8&255]^n[255&c]^i[f++],y=r[h>>>24]^s[l>>>16&255]^a[c>>>8&255]^n[255&u]^i[f++];l=_,c=m,u=g,h=y}_=(o[l>>>24]<<24|o[c>>>16&255]<<16|o[u>>>8&255]<<8|o[255&h])^i[f++],m=(o[c>>>24]<<24|o[u>>>16&255]<<16|o[h>>>8&255]<<8|o[255&l])^i[f++],g=(o[u>>>24]<<24|o[h>>>16&255]<<16|o[l>>>8&255]<<8|o[255&c])^i[f++],y=(o[h>>>24]<<24|o[l>>>16&255]<<16|o[c>>>8&255]<<8|o[255&u])^i[f++],e[t]=_,e[t+1]=m,e[t+2]=g,e[t+3]=y},keySize:8});e.AES=t._createHelper(_)}(),i.AES)})),zt((function(e,t){var i;e.exports=(i=Lr,function(){var e=i,t=e.lib,r=t.WordArray,s=t.BlockCipher,a=e.algo,n=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],o=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],d=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],l=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],c=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],u=a.DES=s.extend({_doReset:function(){for(var e=this._key.words,t=[],i=0;i<56;i++){var r=n[i]-1;t[i]=e[r>>>5]>>>31-r%32&1}for(var s=this._subKeys=[],a=0;a<16;a++){var l=s[a]=[],c=d[a];for(i=0;i<24;i++)l[i/6|0]|=t[(o[i]-1+c)%28]<<31-i%6,l[4+(i/6|0)]|=t[28+(o[i+24]-1+c)%28]<<31-i%6;for(l[0]=l[0]<<1|l[0]>>>31,i=1;i<7;i++)l[i]=l[i]>>>4*(i-1)+3;l[7]=l[7]<<5|l[7]>>>27}var u=this._invSubKeys=[];for(i=0;i<16;i++)u[i]=s[15-i]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._subKeys)},decryptBlock:function(e,t){this._doCryptBlock(e,t,this._invSubKeys)},_doCryptBlock:function(e,t,i){this._lBlock=e[t],this._rBlock=e[t+1],h.call(this,4,252645135),h.call(this,16,65535),f.call(this,2,858993459),f.call(this,8,16711935),h.call(this,1,1431655765);for(var r=0;r<16;r++){for(var s=i[r],a=this._lBlock,n=this._rBlock,o=0,d=0;d<8;d++)o|=l[d][((n^s[d])&c[d])>>>0];this._lBlock=n,this._rBlock=a^o}var u=this._lBlock;this._lBlock=this._rBlock,this._rBlock=u,h.call(this,1,1431655765),f.call(this,8,16711935),f.call(this,2,858993459),h.call(this,16,65535),h.call(this,4,252645135),e[t]=this._lBlock,e[t+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function h(e,t){var i=(this._lBlock>>>e^this._rBlock)&t;this._rBlock^=i,this._lBlock^=i<<e}function f(e,t){var i=(this._rBlock>>>e^this._lBlock)&t;this._lBlock^=i,this._rBlock^=i<<e}e.DES=s._createHelper(u);var p=a.TripleDES=s.extend({_doReset:function(){var e=this._key.words;if(2!==e.length&&4!==e.length&&e.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var t=e.slice(0,2),i=e.length<4?e.slice(0,2):e.slice(2,4),s=e.length<6?e.slice(0,2):e.slice(4,6);this._des1=u.createEncryptor(r.create(t)),this._des2=u.createEncryptor(r.create(i)),this._des3=u.createEncryptor(r.create(s))},encryptBlock:function(e,t){this._des1.encryptBlock(e,t),this._des2.decryptBlock(e,t),this._des3.encryptBlock(e,t)},decryptBlock:function(e,t){this._des3.decryptBlock(e,t),this._des2.encryptBlock(e,t),this._des1.decryptBlock(e,t)},keySize:6,ivSize:2,blockSize:2});e.TripleDES=s._createHelper(p)}(),i.TripleDES)})),zt((function(e,t){var i;e.exports=(i=Lr,function(){var e=i,t=e.lib.StreamCipher,r=e.algo,s=r.RC4=t.extend({_doReset:function(){for(var e=this._key,t=e.words,i=e.sigBytes,r=this._S=[],s=0;s<256;s++)r[s]=s;s=0;for(var a=0;s<256;s++){var n=s%i,o=t[n>>>2]>>>24-n%4*8&255;a=(a+r[s]+o)%256;var d=r[s];r[s]=r[a],r[a]=d}this._i=this._j=0},_doProcessBlock:function(e,t){e[t]^=a.call(this)},keySize:8,ivSize:0});function a(){for(var e=this._S,t=this._i,i=this._j,r=0,s=0;s<4;s++){i=(i+e[t=(t+1)%256])%256;var a=e[t];e[t]=e[i],e[i]=a,r|=e[(e[t]+e[i])%256]<<24-8*s}return this._i=t,this._j=i,r}e.RC4=t._createHelper(s);var n=r.RC4Drop=s.extend({cfg:s.cfg.extend({drop:192}),_doReset:function(){s._doReset.call(this);for(var e=this.cfg.drop;e>0;e--)a.call(this)}});e.RC4Drop=t._createHelper(n)}(),i.RC4)})),zt((function(e,t){var i;e.exports=(i=Lr,function(){var e=i,t=e.lib.StreamCipher,r=e.algo,s=[],a=[],n=[],o=r.Rabbit=t.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,i=0;i<4;i++)e[i]=16711935&(e[i]<<8|e[i]>>>24)|4278255360&(e[i]<<24|e[i]>>>8);var r=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],s=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];for(this._b=0,i=0;i<4;i++)d.call(this);for(i=0;i<8;i++)s[i]^=r[i+4&7];if(t){var a=t.words,n=a[0],o=a[1],l=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8),c=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),u=l>>>16|4294901760&c,h=c<<16|65535&l;for(s[0]^=l,s[1]^=u,s[2]^=c,s[3]^=h,s[4]^=l,s[5]^=u,s[6]^=c,s[7]^=h,i=0;i<4;i++)d.call(this)}},_doProcessBlock:function(e,t){var i=this._X;d.call(this),s[0]=i[0]^i[5]>>>16^i[3]<<16,s[1]=i[2]^i[7]>>>16^i[5]<<16,s[2]=i[4]^i[1]>>>16^i[7]<<16,s[3]=i[6]^i[3]>>>16^i[1]<<16;for(var r=0;r<4;r++)s[r]=16711935&(s[r]<<8|s[r]>>>24)|4278255360&(s[r]<<24|s[r]>>>8),e[t+r]^=s[r]},blockSize:4,ivSize:2});function d(){for(var e=this._X,t=this._C,i=0;i<8;i++)a[i]=t[i];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<a[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<a[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<a[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<a[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<a[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<a[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<a[6]>>>0?1:0)|0,this._b=t[7]>>>0<a[7]>>>0?1:0,i=0;i<8;i++){var r=e[i]+t[i],s=65535&r,o=r>>>16,d=((s*s>>>17)+s*o>>>15)+o*o,l=((4294901760&r)*r|0)+((65535&r)*r|0);n[i]=d^l}e[0]=n[0]+(n[7]<<16|n[7]>>>16)+(n[6]<<16|n[6]>>>16)|0,e[1]=n[1]+(n[0]<<8|n[0]>>>24)+n[7]|0,e[2]=n[2]+(n[1]<<16|n[1]>>>16)+(n[0]<<16|n[0]>>>16)|0,e[3]=n[3]+(n[2]<<8|n[2]>>>24)+n[1]|0,e[4]=n[4]+(n[3]<<16|n[3]>>>16)+(n[2]<<16|n[2]>>>16)|0,e[5]=n[5]+(n[4]<<8|n[4]>>>24)+n[3]|0,e[6]=n[6]+(n[5]<<16|n[5]>>>16)+(n[4]<<16|n[4]>>>16)|0,e[7]=n[7]+(n[6]<<8|n[6]>>>24)+n[5]|0}e.Rabbit=t._createHelper(o)}(),i.Rabbit)})),zt((function(e,t){var i;e.exports=(i=Lr,function(){var e=i,t=e.lib.StreamCipher,r=e.algo,s=[],a=[],n=[],o=r.RabbitLegacy=t.extend({_doReset:function(){var e=this._key.words,t=this.cfg.iv,i=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],r=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];this._b=0;for(var s=0;s<4;s++)d.call(this);for(s=0;s<8;s++)r[s]^=i[s+4&7];if(t){var a=t.words,n=a[0],o=a[1],l=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8),c=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),u=l>>>16|4294901760&c,h=c<<16|65535&l;for(r[0]^=l,r[1]^=u,r[2]^=c,r[3]^=h,r[4]^=l,r[5]^=u,r[6]^=c,r[7]^=h,s=0;s<4;s++)d.call(this)}},_doProcessBlock:function(e,t){var i=this._X;d.call(this),s[0]=i[0]^i[5]>>>16^i[3]<<16,s[1]=i[2]^i[7]>>>16^i[5]<<16,s[2]=i[4]^i[1]>>>16^i[7]<<16,s[3]=i[6]^i[3]>>>16^i[1]<<16;for(var r=0;r<4;r++)s[r]=16711935&(s[r]<<8|s[r]>>>24)|4278255360&(s[r]<<24|s[r]>>>8),e[t+r]^=s[r]},blockSize:4,ivSize:2});function d(){for(var e=this._X,t=this._C,i=0;i<8;i++)a[i]=t[i];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<a[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<a[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<a[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<a[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<a[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<a[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<a[6]>>>0?1:0)|0,this._b=t[7]>>>0<a[7]>>>0?1:0,i=0;i<8;i++){var r=e[i]+t[i],s=65535&r,o=r>>>16,d=((s*s>>>17)+s*o>>>15)+o*o,l=((4294901760&r)*r|0)+((65535&r)*r|0);n[i]=d^l}e[0]=n[0]+(n[7]<<16|n[7]>>>16)+(n[6]<<16|n[6]>>>16)|0,e[1]=n[1]+(n[0]<<8|n[0]>>>24)+n[7]|0,e[2]=n[2]+(n[1]<<16|n[1]>>>16)+(n[0]<<16|n[0]>>>16)|0,e[3]=n[3]+(n[2]<<8|n[2]>>>24)+n[1]|0,e[4]=n[4]+(n[3]<<16|n[3]>>>16)+(n[2]<<16|n[2]>>>16)|0,e[5]=n[5]+(n[4]<<8|n[4]>>>24)+n[3]|0,e[6]=n[6]+(n[5]<<16|n[5]>>>16)+(n[4]<<16|n[4]>>>16)|0,e[7]=n[7]+(n[6]<<8|n[6]>>>24)+n[5]|0}e.RabbitLegacy=t._createHelper(o)}(),i.RabbitLegacy)})),zt((function(e,t){var i;e.exports=(i=Lr,function(){var e=i,t=e.lib.BlockCipher,r=e.algo;const s=16,a=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731],n=[[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946],[1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055],[3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504],[976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462]];var o={pbox:[],sbox:[]};function d(e,t){let i=t>>24&255,r=t>>16&255,s=t>>8&255,a=255&t,n=e.sbox[0][i]+e.sbox[1][r];return n^=e.sbox[2][s],n+=e.sbox[3][a],n}function l(e,t,i){let r,a=t,n=i;for(let t=0;t<s;++t)a^=e.pbox[t],n=d(e,a)^n,r=a,a=n,n=r;return r=a,a=n,n=r,n^=e.pbox[s],a^=e.pbox[s+1],{left:a,right:n}}function c(e,t,i){let r,a=t,n=i;for(let t=s+1;t>1;--t)a^=e.pbox[t],n=d(e,a)^n,r=a,a=n,n=r;return r=a,a=n,n=r,n^=e.pbox[1],a^=e.pbox[0],{left:a,right:n}}function u(e,t,i){for(let t=0;t<4;t++){e.sbox[t]=[];for(let i=0;i<256;i++)e.sbox[t][i]=n[t][i]}let r=0;for(let n=0;n<s+2;n++)e.pbox[n]=a[n]^t[r],r++,r>=i&&(r=0);let o=0,d=0,c=0;for(let t=0;t<s+2;t+=2)c=l(e,o,d),o=c.left,d=c.right,e.pbox[t]=o,e.pbox[t+1]=d;for(let t=0;t<4;t++)for(let i=0;i<256;i+=2)c=l(e,o,d),o=c.left,d=c.right,e.sbox[t][i]=o,e.sbox[t][i+1]=d;return!0}var h=r.Blowfish=t.extend({_doReset:function(){if(this._keyPriorReset!==this._key){var e=this._keyPriorReset=this._key,t=e.words,i=e.sigBytes/4;u(o,t,i)}},encryptBlock:function(e,t){var i=l(o,e[t],e[t+1]);e[t]=i.left,e[t+1]=i.right},decryptBlock:function(e,t){var i=c(o,e[t],e[t+1]);e[t]=i.left,e[t+1]=i.right},blockSize:2,keySize:4,ivSize:2});e.Blowfish=t._createHelper(h)}(),i.Blowfish)}));function Fr(e){return e[3]|e[2]<<8|e[1]<<16|e[0]<<24}function Pr(e,t,i){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];t=new Uint8Array(t),i=new Uint8Array(i);const s=e.byteLength;let a=5;for(;a<s;){let n=Fr(e.slice(a,a+4));if(n>s)break;let o=e[a+4],d=!1;if(r?(o=o>>>1&63,d=[0,1,2,3,4,5,6,7,8,9,16,17,18,19,20,21].includes(o)):(o&=31,d=1===o||5===o),d){const r=e.slice(a+4+2,a+4+n);let s=new Ir.ModeOfOperation.ctr(t,new Ir.Counter(i));const o=s.decrypt(r);s=null,e.set(o,a+4+2)}a=a+4+n}return e}function Mr(e,t,i){if(e.byteLength<=30)return e;const r=e.slice(32);let s=new Ir.ModeOfOperation.ctr(t,new Ir.Counter(i));const a=s.decrypt(r);return s=null,e.set(a,32),e}zt((function(e,t){e.exports=Lr}));var zr=zt((function(e,t){var r,s,a,n=(r=new Date,s=4,a={setLogLevel:function(e){s=e==this.debug?1:e==this.info?2:e==this.warn?3:(this.error,4)},debug:function(e,t){void 0===console.debug&&(console.debug=console.log),1>=s&&console.debug("["+n.getDurationString(new Date-r,1e3)+"]","["+e+"]",t)},log:function(e,t){this.debug(e.msg)},info:function(e,t){2>=s&&console.info("["+n.getDurationString(new Date-r,1e3)+"]","["+e+"]",t)},warn:function(e,t){3>=s&&console.warn("["+n.getDurationString(new Date-r,1e3)+"]","["+e+"]",t)},error:function(e,t){4>=s&&console.error("["+n.getDurationString(new Date-r,1e3)+"]","["+e+"]",t)}},a);n.getDurationString=function(e,t){var i;function r(e,t){for(var i=(""+e).split(".");i[0].length<t;)i[0]="0"+i[0];return i.join(".")}e<0?(i=!0,e=-e):i=!1;var s=e/(t||1),a=Math.floor(s/3600);s-=3600*a;var n=Math.floor(s/60),o=1e3*(s-=60*n);return o-=1e3*(s=Math.floor(s)),o=Math.floor(o),(i?"-":"")+a+":"+r(n,2)+":"+r(s,2)+"."+r(o,3)},n.printRanges=function(e){var t=e.length;if(t>0){for(var i="",r=0;r<t;r++)r>0&&(i+=","),i+="["+n.getDurationString(e.start(r))+","+n.getDurationString(e.end(r))+"]";return i}return"(empty)"},t.Log=n;var o=function(e){if(!(e instanceof ArrayBuffer))throw"Needs an array buffer";this.buffer=e,this.dataview=new DataView(e),this.position=0};o.prototype.getPosition=function(){return this.position},o.prototype.getEndPosition=function(){return this.buffer.byteLength},o.prototype.getLength=function(){return this.buffer.byteLength},o.prototype.seek=function(e){var t=Math.max(0,Math.min(this.buffer.byteLength,e));return this.position=isNaN(t)||!isFinite(t)?0:t,!0},o.prototype.isEos=function(){return this.getPosition()>=this.getEndPosition()},o.prototype.readAnyInt=function(e,t){var i=0;if(this.position+e<=this.buffer.byteLength){switch(e){case 1:i=t?this.dataview.getInt8(this.position):this.dataview.getUint8(this.position);break;case 2:i=t?this.dataview.getInt16(this.position):this.dataview.getUint16(this.position);break;case 3:if(t)throw"No method for reading signed 24 bits values";i=this.dataview.getUint8(this.position)<<16,i|=this.dataview.getUint8(this.position+1)<<8,i|=this.dataview.getUint8(this.position+2);break;case 4:i=t?this.dataview.getInt32(this.position):this.dataview.getUint32(this.position);break;case 8:if(t)throw"No method for reading signed 64 bits values";i=this.dataview.getUint32(this.position)<<32,i|=this.dataview.getUint32(this.position+4);break;default:throw"readInt method not implemented for size: "+e}return this.position+=e,i}throw"Not enough bytes in buffer"},o.prototype.readUint8=function(){return this.readAnyInt(1,!1)},o.prototype.readUint16=function(){return this.readAnyInt(2,!1)},o.prototype.readUint24=function(){return this.readAnyInt(3,!1)},o.prototype.readUint32=function(){return this.readAnyInt(4,!1)},o.prototype.readUint64=function(){return this.readAnyInt(8,!1)},o.prototype.readString=function(e){if(this.position+e<=this.buffer.byteLength){for(var t="",i=0;i<e;i++)t+=String.fromCharCode(this.readUint8());return t}throw"Not enough bytes in buffer"},o.prototype.readCString=function(){for(var e=[];;){var t=this.readUint8();if(0===t)break;e.push(t)}return String.fromCharCode.apply(null,e)},o.prototype.readInt8=function(){return this.readAnyInt(1,!0)},o.prototype.readInt16=function(){return this.readAnyInt(2,!0)},o.prototype.readInt32=function(){return this.readAnyInt(4,!0)},o.prototype.readInt64=function(){return this.readAnyInt(8,!1)},o.prototype.readUint8Array=function(e){for(var t=new Uint8Array(e),i=0;i<e;i++)t[i]=this.readUint8();return t},o.prototype.readInt16Array=function(e){for(var t=new Int16Array(e),i=0;i<e;i++)t[i]=this.readInt16();return t},o.prototype.readUint16Array=function(e){for(var t=new Int16Array(e),i=0;i<e;i++)t[i]=this.readUint16();return t},o.prototype.readUint32Array=function(e){for(var t=new Uint32Array(e),i=0;i<e;i++)t[i]=this.readUint32();return t},o.prototype.readInt32Array=function(e){for(var t=new Int32Array(e),i=0;i<e;i++)t[i]=this.readInt32();return t},t.MP4BoxStream=o;var d=function(e,t,i){this._byteOffset=t||0,e instanceof ArrayBuffer?this.buffer=e:"object"==typeof e?(this.dataView=e,t&&(this._byteOffset+=t)):this.buffer=new ArrayBuffer(e||0),this.position=0,this.endianness=null==i?d.LITTLE_ENDIAN:i};d.prototype={},d.prototype.getPosition=function(){return this.position},d.prototype._realloc=function(e){if(this._dynamicSize){var t=this._byteOffset+this.position+e,i=this._buffer.byteLength;if(t<=i)t>this._byteLength&&(this._byteLength=t);else{for(i<1&&(i=1);t>i;)i*=2;var r=new ArrayBuffer(i),s=new Uint8Array(this._buffer);new Uint8Array(r,0,s.length).set(s),this.buffer=r,this._byteLength=t}}},d.prototype._trimAlloc=function(){if(this._byteLength!=this._buffer.byteLength){var e=new ArrayBuffer(this._byteLength),t=new Uint8Array(e),i=new Uint8Array(this._buffer,0,t.length);t.set(i),this.buffer=e}},d.BIG_ENDIAN=!1,d.LITTLE_ENDIAN=!0,d.prototype._byteLength=0,Object.defineProperty(d.prototype,"byteLength",{get:function(){return this._byteLength-this._byteOffset}}),Object.defineProperty(d.prototype,"buffer",{get:function(){return this._trimAlloc(),this._buffer},set:function(e){this._buffer=e,this._dataView=new DataView(this._buffer,this._byteOffset),this._byteLength=this._buffer.byteLength}}),Object.defineProperty(d.prototype,"byteOffset",{get:function(){return this._byteOffset},set:function(e){this._byteOffset=e,this._dataView=new DataView(this._buffer,this._byteOffset),this._byteLength=this._buffer.byteLength}}),Object.defineProperty(d.prototype,"dataView",{get:function(){return this._dataView},set:function(e){this._byteOffset=e.byteOffset,this._buffer=e.buffer,this._dataView=new DataView(this._buffer,this._byteOffset),this._byteLength=this._byteOffset+e.byteLength}}),d.prototype.seek=function(e){var t=Math.max(0,Math.min(this.byteLength,e));this.position=isNaN(t)||!isFinite(t)?0:t},d.prototype.isEof=function(){return this.position>=this._byteLength},d.prototype.mapUint8Array=function(e){this._realloc(1*e);var t=new Uint8Array(this._buffer,this.byteOffset+this.position,e);return this.position+=1*e,t},d.prototype.readInt32Array=function(e,t){e=null==e?this.byteLength-this.position/4:e;var i=new Int32Array(e);return d.memcpy(i.buffer,0,this.buffer,this.byteOffset+this.position,e*i.BYTES_PER_ELEMENT),d.arrayToNative(i,null==t?this.endianness:t),this.position+=i.byteLength,i},d.prototype.readInt16Array=function(e,t){e=null==e?this.byteLength-this.position/2:e;var i=new Int16Array(e);return d.memcpy(i.buffer,0,this.buffer,this.byteOffset+this.position,e*i.BYTES_PER_ELEMENT),d.arrayToNative(i,null==t?this.endianness:t),this.position+=i.byteLength,i},d.prototype.readInt8Array=function(e){e=null==e?this.byteLength-this.position:e;var t=new Int8Array(e);return d.memcpy(t.buffer,0,this.buffer,this.byteOffset+this.position,e*t.BYTES_PER_ELEMENT),this.position+=t.byteLength,t},d.prototype.readUint32Array=function(e,t){e=null==e?this.byteLength-this.position/4:e;var i=new Uint32Array(e);return d.memcpy(i.buffer,0,this.buffer,this.byteOffset+this.position,e*i.BYTES_PER_ELEMENT),d.arrayToNative(i,null==t?this.endianness:t),this.position+=i.byteLength,i},d.prototype.readUint16Array=function(e,t){e=null==e?this.byteLength-this.position/2:e;var i=new Uint16Array(e);return d.memcpy(i.buffer,0,this.buffer,this.byteOffset+this.position,e*i.BYTES_PER_ELEMENT),d.arrayToNative(i,null==t?this.endianness:t),this.position+=i.byteLength,i},d.prototype.readUint8Array=function(e){e=null==e?this.byteLength-this.position:e;var t=new Uint8Array(e);return d.memcpy(t.buffer,0,this.buffer,this.byteOffset+this.position,e*t.BYTES_PER_ELEMENT),this.position+=t.byteLength,t},d.prototype.readFloat64Array=function(e,t){e=null==e?this.byteLength-this.position/8:e;var i=new Float64Array(e);return d.memcpy(i.buffer,0,this.buffer,this.byteOffset+this.position,e*i.BYTES_PER_ELEMENT),d.arrayToNative(i,null==t?this.endianness:t),this.position+=i.byteLength,i},d.prototype.readFloat32Array=function(e,t){e=null==e?this.byteLength-this.position/4:e;var i=new Float32Array(e);return d.memcpy(i.buffer,0,this.buffer,this.byteOffset+this.position,e*i.BYTES_PER_ELEMENT),d.arrayToNative(i,null==t?this.endianness:t),this.position+=i.byteLength,i},d.prototype.readInt32=function(e){var t=this._dataView.getInt32(this.position,null==e?this.endianness:e);return this.position+=4,t},d.prototype.readInt16=function(e){var t=this._dataView.getInt16(this.position,null==e?this.endianness:e);return this.position+=2,t},d.prototype.readInt8=function(){var e=this._dataView.getInt8(this.position);return this.position+=1,e},d.prototype.readUint32=function(e){var t=this._dataView.getUint32(this.position,null==e?this.endianness:e);return this.position+=4,t},d.prototype.readUint16=function(e){var t=this._dataView.getUint16(this.position,null==e?this.endianness:e);return this.position+=2,t},d.prototype.readUint8=function(){var e=this._dataView.getUint8(this.position);return this.position+=1,e},d.prototype.readFloat32=function(e){var t=this._dataView.getFloat32(this.position,null==e?this.endianness:e);return this.position+=4,t},d.prototype.readFloat64=function(e){var t=this._dataView.getFloat64(this.position,null==e?this.endianness:e);return this.position+=8,t},d.endianness=new Int8Array(new Int16Array([1]).buffer)[0]>0,d.memcpy=function(e,t,i,r,s){var a=new Uint8Array(e,t,s),n=new Uint8Array(i,r,s);a.set(n)},d.arrayToNative=function(e,t){return t==this.endianness?e:this.flipArrayEndianness(e)},d.nativeToEndian=function(e,t){return this.endianness==t?e:this.flipArrayEndianness(e)},d.flipArrayEndianness=function(e){for(var t=new Uint8Array(e.buffer,e.byteOffset,e.byteLength),i=0;i<e.byteLength;i+=e.BYTES_PER_ELEMENT)for(var r=i+e.BYTES_PER_ELEMENT-1,s=i;r>s;r--,s++){var a=t[s];t[s]=t[r],t[r]=a}return e},d.prototype.failurePosition=0,String.fromCharCodeUint8=function(e){for(var t=[],i=0;i<e.length;i++)t[i]=e[i];return String.fromCharCode.apply(null,t)},d.prototype.readString=function(e,t){return null==t||"ASCII"==t?String.fromCharCodeUint8.apply(null,[this.mapUint8Array(null==e?this.byteLength-this.position:e)]):new TextDecoder(t).decode(this.mapUint8Array(e))},d.prototype.readCString=function(e){var t=this.byteLength-this.position,i=new Uint8Array(this._buffer,this._byteOffset+this.position),r=t;null!=e&&(r=Math.min(e,t));for(var s=0;s<r&&0!==i[s];s++);var a=String.fromCharCodeUint8.apply(null,[this.mapUint8Array(s)]);return null!=e?this.position+=r-s:s!=t&&(this.position+=1),a};var l=Math.pow(2,32);d.prototype.readInt64=function(){return this.readInt32()*l+this.readUint32()},d.prototype.readUint64=function(){return this.readUint32()*l+this.readUint32()},d.prototype.readInt64=function(){return this.readUint32()*l+this.readUint32()},d.prototype.readUint24=function(){return(this.readUint8()<<16)+(this.readUint8()<<8)+this.readUint8()},t.DataStream=d,d.prototype.save=function(e){var t=new Blob([this.buffer]);if(!window.URL||!URL.createObjectURL)throw"DataStream.save: Can't create object URL.";var i=window.URL.createObjectURL(t),r=document.createElement("a");document.body.appendChild(r),r.setAttribute("href",i),r.setAttribute("download",e),r.setAttribute("target","_self"),r.click(),window.URL.revokeObjectURL(i)},d.prototype._dynamicSize=!0,Object.defineProperty(d.prototype,"dynamicSize",{get:function(){return this._dynamicSize},set:function(e){e||this._trimAlloc(),this._dynamicSize=e}}),d.prototype.shift=function(e){var t=new ArrayBuffer(this._byteLength-e),i=new Uint8Array(t),r=new Uint8Array(this._buffer,e,i.length);i.set(r),this.buffer=t,this.position-=e},d.prototype.writeInt32Array=function(e,t){if(this._realloc(4*e.length),e instanceof Int32Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapInt32Array(e.length,t);else for(var i=0;i<e.length;i++)this.writeInt32(e[i],t)},d.prototype.writeInt16Array=function(e,t){if(this._realloc(2*e.length),e instanceof Int16Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapInt16Array(e.length,t);else for(var i=0;i<e.length;i++)this.writeInt16(e[i],t)},d.prototype.writeInt8Array=function(e){if(this._realloc(1*e.length),e instanceof Int8Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapInt8Array(e.length);else for(var t=0;t<e.length;t++)this.writeInt8(e[t])},d.prototype.writeUint32Array=function(e,t){if(this._realloc(4*e.length),e instanceof Uint32Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapUint32Array(e.length,t);else for(var i=0;i<e.length;i++)this.writeUint32(e[i],t)},d.prototype.writeUint16Array=function(e,t){if(this._realloc(2*e.length),e instanceof Uint16Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapUint16Array(e.length,t);else for(var i=0;i<e.length;i++)this.writeUint16(e[i],t)},d.prototype.writeUint8Array=function(e){if(this._realloc(1*e.length),e instanceof Uint8Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapUint8Array(e.length);else for(var t=0;t<e.length;t++)this.writeUint8(e[t])},d.prototype.writeFloat64Array=function(e,t){if(this._realloc(8*e.length),e instanceof Float64Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapFloat64Array(e.length,t);else for(var i=0;i<e.length;i++)this.writeFloat64(e[i],t)},d.prototype.writeFloat32Array=function(e,t){if(this._realloc(4*e.length),e instanceof Float32Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)d.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapFloat32Array(e.length,t);else for(var i=0;i<e.length;i++)this.writeFloat32(e[i],t)},d.prototype.writeInt32=function(e,t){this._realloc(4),this._dataView.setInt32(this.position,e,null==t?this.endianness:t),this.position+=4},d.prototype.writeInt16=function(e,t){this._realloc(2),this._dataView.setInt16(this.position,e,null==t?this.endianness:t),this.position+=2},d.prototype.writeInt8=function(e){this._realloc(1),this._dataView.setInt8(this.position,e),this.position+=1},d.prototype.writeUint32=function(e,t){this._realloc(4),this._dataView.setUint32(this.position,e,null==t?this.endianness:t),this.position+=4},d.prototype.writeUint16=function(e,t){this._realloc(2),this._dataView.setUint16(this.position,e,null==t?this.endianness:t),this.position+=2},d.prototype.writeUint8=function(e){this._realloc(1),this._dataView.setUint8(this.position,e),this.position+=1},d.prototype.writeFloat32=function(e,t){this._realloc(4),this._dataView.setFloat32(this.position,e,null==t?this.endianness:t),this.position+=4},d.prototype.writeFloat64=function(e,t){this._realloc(8),this._dataView.setFloat64(this.position,e,null==t?this.endianness:t),this.position+=8},d.prototype.writeUCS2String=function(e,t,i){null==i&&(i=e.length);for(var r=0;r<e.length&&r<i;r++)this.writeUint16(e.charCodeAt(r),t);for(;r<i;r++)this.writeUint16(0)},d.prototype.writeString=function(e,t,i){var r=0;if(null==t||"ASCII"==t)if(null!=i){var s=Math.min(e.length,i);for(r=0;r<s;r++)this.writeUint8(e.charCodeAt(r));for(;r<i;r++)this.writeUint8(0)}else for(r=0;r<e.length;r++)this.writeUint8(e.charCodeAt(r));else this.writeUint8Array(new TextEncoder(t).encode(e.substring(0,i)))},d.prototype.writeCString=function(e,t){var i=0;if(null!=t){var r=Math.min(e.length,t);for(i=0;i<r;i++)this.writeUint8(e.charCodeAt(i));for(;i<t;i++)this.writeUint8(0)}else{for(i=0;i<e.length;i++)this.writeUint8(e.charCodeAt(i));this.writeUint8(0)}},d.prototype.writeStruct=function(e,t){for(var i=0;i<e.length;i+=2){var r=e[i+1];this.writeType(r,t[e[i]],t)}},d.prototype.writeType=function(e,t,i){var r;if("function"==typeof e)return e(this,t);if("object"==typeof e&&!(e instanceof Array))return e.set(this,t,i);var s=null,a="ASCII",n=this.position;switch("string"==typeof e&&/:/.test(e)&&(r=e.split(":"),e=r[0],s=parseInt(r[1])),"string"==typeof e&&/,/.test(e)&&(r=e.split(","),e=r[0],a=parseInt(r[1])),e){case"uint8":this.writeUint8(t);break;case"int8":this.writeInt8(t);break;case"uint16":this.writeUint16(t,this.endianness);break;case"int16":this.writeInt16(t,this.endianness);break;case"uint32":this.writeUint32(t,this.endianness);break;case"int32":this.writeInt32(t,this.endianness);break;case"float32":this.writeFloat32(t,this.endianness);break;case"float64":this.writeFloat64(t,this.endianness);break;case"uint16be":this.writeUint16(t,d.BIG_ENDIAN);break;case"int16be":this.writeInt16(t,d.BIG_ENDIAN);break;case"uint32be":this.writeUint32(t,d.BIG_ENDIAN);break;case"int32be":this.writeInt32(t,d.BIG_ENDIAN);break;case"float32be":this.writeFloat32(t,d.BIG_ENDIAN);break;case"float64be":this.writeFloat64(t,d.BIG_ENDIAN);break;case"uint16le":this.writeUint16(t,d.LITTLE_ENDIAN);break;case"int16le":this.writeInt16(t,d.LITTLE_ENDIAN);break;case"uint32le":this.writeUint32(t,d.LITTLE_ENDIAN);break;case"int32le":this.writeInt32(t,d.LITTLE_ENDIAN);break;case"float32le":this.writeFloat32(t,d.LITTLE_ENDIAN);break;case"float64le":this.writeFloat64(t,d.LITTLE_ENDIAN);break;case"cstring":this.writeCString(t,s);break;case"string":this.writeString(t,a,s);break;case"u16string":this.writeUCS2String(t,this.endianness,s);break;case"u16stringle":this.writeUCS2String(t,d.LITTLE_ENDIAN,s);break;case"u16stringbe":this.writeUCS2String(t,d.BIG_ENDIAN,s);break;default:if(3==e.length){for(var o=e[1],l=0;l<t.length;l++)this.writeType(o,t[l]);break}this.writeStruct(e,t)}null!=s&&(this.position=n,this._realloc(s),this.position=n+s)},d.prototype.writeUint64=function(e){var t=Math.floor(e/l);this.writeUint32(t),this.writeUint32(4294967295&e)},d.prototype.writeUint24=function(e){this.writeUint8((16711680&e)>>16),this.writeUint8((65280&e)>>8),this.writeUint8(255&e)},d.prototype.adjustUint32=function(e,t){var i=this.position;this.seek(e),this.writeUint32(t),this.seek(i)},d.prototype.mapInt32Array=function(e,t){this._realloc(4*e);var i=new Int32Array(this._buffer,this.byteOffset+this.position,e);return d.arrayToNative(i,null==t?this.endianness:t),this.position+=4*e,i},d.prototype.mapInt16Array=function(e,t){this._realloc(2*e);var i=new Int16Array(this._buffer,this.byteOffset+this.position,e);return d.arrayToNative(i,null==t?this.endianness:t),this.position+=2*e,i},d.prototype.mapInt8Array=function(e){this._realloc(1*e);var t=new Int8Array(this._buffer,this.byteOffset+this.position,e);return this.position+=1*e,t},d.prototype.mapUint32Array=function(e,t){this._realloc(4*e);var i=new Uint32Array(this._buffer,this.byteOffset+this.position,e);return d.arrayToNative(i,null==t?this.endianness:t),this.position+=4*e,i},d.prototype.mapUint16Array=function(e,t){this._realloc(2*e);var i=new Uint16Array(this._buffer,this.byteOffset+this.position,e);return d.arrayToNative(i,null==t?this.endianness:t),this.position+=2*e,i},d.prototype.mapFloat64Array=function(e,t){this._realloc(8*e);var i=new Float64Array(this._buffer,this.byteOffset+this.position,e);return d.arrayToNative(i,null==t?this.endianness:t),this.position+=8*e,i},d.prototype.mapFloat32Array=function(e,t){this._realloc(4*e);var i=new Float32Array(this._buffer,this.byteOffset+this.position,e);return d.arrayToNative(i,null==t?this.endianness:t),this.position+=4*e,i};var c=function(e){this.buffers=[],this.bufferIndex=-1,e&&(this.insertBuffer(e),this.bufferIndex=0)};(c.prototype=new d(new ArrayBuffer,0,d.BIG_ENDIAN)).initialized=function(){var e;return this.bufferIndex>-1||(this.buffers.length>0?0===(e=this.buffers[0]).fileStart?(this.buffer=e,this.bufferIndex=0,n.debug("MultiBufferStream","Stream ready for parsing"),!0):(n.warn("MultiBufferStream","The first buffer should have a fileStart of 0"),this.logBufferLevel(),!1):(n.warn("MultiBufferStream","No buffer to start parsing from"),this.logBufferLevel(),!1))},ArrayBuffer.concat=function(e,t){n.debug("ArrayBuffer","Trying to create a new buffer of size: "+(e.byteLength+t.byteLength));var i=new Uint8Array(e.byteLength+t.byteLength);return i.set(new Uint8Array(e),0),i.set(new Uint8Array(t),e.byteLength),i.buffer},c.prototype.reduceBuffer=function(e,t,i){var r;return(r=new Uint8Array(i)).set(new Uint8Array(e,t,i)),r.buffer.fileStart=e.fileStart+t,r.buffer.usedBytes=0,r.buffer},c.prototype.insertBuffer=function(e){for(var t=!0,i=0;i<this.buffers.length;i++){var r=this.buffers[i];if(e.fileStart<=r.fileStart){if(e.fileStart===r.fileStart){if(e.byteLength>r.byteLength){this.buffers.splice(i,1),i--;continue}n.warn("MultiBufferStream","Buffer (fileStart: "+e.fileStart+" - Length: "+e.byteLength+") already appended, ignoring")}else e.fileStart+e.byteLength<=r.fileStart||(e=this.reduceBuffer(e,0,r.fileStart-e.fileStart)),n.debug("MultiBufferStream","Appending new buffer (fileStart: "+e.fileStart+" - Length: "+e.byteLength+")"),this.buffers.splice(i,0,e),0===i&&(this.buffer=e);t=!1;break}if(e.fileStart<r.fileStart+r.byteLength){var s=r.fileStart+r.byteLength-e.fileStart,a=e.byteLength-s;if(!(a>0)){t=!1;break}e=this.reduceBuffer(e,s,a)}}t&&(n.debug("MultiBufferStream","Appending new buffer (fileStart: "+e.fileStart+" - Length: "+e.byteLength+")"),this.buffers.push(e),0===i&&(this.buffer=e))},c.prototype.logBufferLevel=function(e){var t,i,r,s,a,o=[],d="";for(r=0,s=0,t=0;t<this.buffers.length;t++)i=this.buffers[t],0===t?(a={},o.push(a),a.start=i.fileStart,a.end=i.fileStart+i.byteLength,d+="["+a.start+"-"):a.end===i.fileStart?a.end=i.fileStart+i.byteLength:((a={}).start=i.fileStart,d+=o[o.length-1].end-1+"], ["+a.start+"-",a.end=i.fileStart+i.byteLength,o.push(a)),r+=i.usedBytes,s+=i.byteLength;o.length>0&&(d+=a.end-1+"]");var l=e?n.info:n.debug;0===this.buffers.length?l("MultiBufferStream","No more buffer in memory"):l("MultiBufferStream",this.buffers.length+" stored buffer(s) ("+r+"/"+s+" bytes), continuous ranges: "+d)},c.prototype.cleanBuffers=function(){var e,t;for(e=0;e<this.buffers.length;e++)(t=this.buffers[e]).usedBytes===t.byteLength&&(n.debug("MultiBufferStream","Removing buffer #"+e),this.buffers.splice(e,1),e--)},c.prototype.mergeNextBuffer=function(){var e;if(this.bufferIndex+1<this.buffers.length){if((e=this.buffers[this.bufferIndex+1]).fileStart===this.buffer.fileStart+this.buffer.byteLength){var t=this.buffer.byteLength,i=this.buffer.usedBytes,r=this.buffer.fileStart;return this.buffers[this.bufferIndex]=ArrayBuffer.concat(this.buffer,e),this.buffer=this.buffers[this.bufferIndex],this.buffers.splice(this.bufferIndex+1,1),this.buffer.usedBytes=i,this.buffer.fileStart=r,n.debug("ISOFile","Concatenating buffer for box parsing (length: "+t+"->"+this.buffer.byteLength+")"),!0}return!1}return!1},c.prototype.findPosition=function(e,t,i){var r,s=null,a=-1;for(r=!0===e?0:this.bufferIndex;r<this.buffers.length&&(s=this.buffers[r]).fileStart<=t;)a=r,i&&(s.fileStart+s.byteLength<=t?s.usedBytes=s.byteLength:s.usedBytes=t-s.fileStart,this.logBufferLevel()),r++;return-1!==a&&(s=this.buffers[a]).fileStart+s.byteLength>=t?(n.debug("MultiBufferStream","Found position in existing buffer #"+a),a):-1},c.prototype.findEndContiguousBuf=function(e){var t,i,r,s=void 0!==e?e:this.bufferIndex;if(i=this.buffers[s],this.buffers.length>s+1)for(t=s+1;t<this.buffers.length&&(r=this.buffers[t]).fileStart===i.fileStart+i.byteLength;t++)i=r;return i.fileStart+i.byteLength},c.prototype.getEndFilePositionAfter=function(e){var t=this.findPosition(!0,e,!1);return-1!==t?this.findEndContiguousBuf(t):e},c.prototype.addUsedBytes=function(e){this.buffer.usedBytes+=e,this.logBufferLevel()},c.prototype.setAllUsedBytes=function(){this.buffer.usedBytes=this.buffer.byteLength,this.logBufferLevel()},c.prototype.seek=function(e,t,i){var r;return-1!==(r=this.findPosition(t,e,i))?(this.buffer=this.buffers[r],this.bufferIndex=r,this.position=e-this.buffer.fileStart,n.debug("MultiBufferStream","Repositioning parser at buffer position: "+this.position),!0):(n.debug("MultiBufferStream","Position "+e+" not found in buffered data"),!1)},c.prototype.getPosition=function(){if(-1===this.bufferIndex||null===this.buffers[this.bufferIndex])throw"Error accessing position in the MultiBufferStream";return this.buffers[this.bufferIndex].fileStart+this.position},c.prototype.getLength=function(){return this.byteLength},c.prototype.getEndPosition=function(){if(-1===this.bufferIndex||null===this.buffers[this.bufferIndex])throw"Error accessing position in the MultiBufferStream";return this.buffers[this.bufferIndex].fileStart+this.byteLength},c.prototype.destroy=function(){this.buffers=[],this.bufferIndex},t.MultiBufferStream=c;var u=function(){var e=[];e[3]="ES_Descriptor",e[4]="DecoderConfigDescriptor",e[5]="DecoderSpecificInfo",e[6]="SLConfigDescriptor",this.getDescriptorName=function(t){return e[t]};var t=this,i={};return this.parseOneDescriptor=function(t){var r,s,a,o=0;for(r=t.readUint8(),a=t.readUint8();128&a;)o=(127&a)<<7,a=t.readUint8();return o+=127&a,n.debug("MPEG4DescriptorParser","Found "+(e[r]||"Descriptor "+r)+", size "+o+" at position "+t.getPosition()),(s=e[r]?new i[e[r]](o):new i.Descriptor(o)).parse(t),s},i.Descriptor=function(e,t){this.tag=e,this.size=t,this.descs=[]},i.Descriptor.prototype.parse=function(e){this.data=e.readUint8Array(this.size)},i.Descriptor.prototype.findDescriptor=function(e){for(var t=0;t<this.descs.length;t++)if(this.descs[t].tag==e)return this.descs[t];return null},i.Descriptor.prototype.parseRemainingDescriptors=function(e){for(var i=e.position;e.position<i+this.size;){var r=t.parseOneDescriptor(e);this.descs.push(r)}},i.ES_Descriptor=function(e){i.Descriptor.call(this,3,e)},i.ES_Descriptor.prototype=new i.Descriptor,i.ES_Descriptor.prototype.parse=function(e){if(this.ES_ID=e.readUint16(),this.flags=e.readUint8(),this.size-=3,128&this.flags?(this.dependsOn_ES_ID=e.readUint16(),this.size-=2):this.dependsOn_ES_ID=0,64&this.flags){var t=e.readUint8();this.URL=e.readString(t),this.size-=t+1}else this.URL="";32&this.flags?(this.OCR_ES_ID=e.readUint16(),this.size-=2):this.OCR_ES_ID=0,this.parseRemainingDescriptors(e)},i.ES_Descriptor.prototype.getOTI=function(e){var t=this.findDescriptor(4);return t?t.oti:0},i.ES_Descriptor.prototype.getAudioConfig=function(e){var t=this.findDescriptor(4);if(!t)return null;var i=t.findDescriptor(5);if(i&&i.data){var r=(248&i.data[0])>>3;return 31===r&&i.data.length>=2&&(r=32+((7&i.data[0])<<3)+((224&i.data[1])>>5)),r}return null},i.DecoderConfigDescriptor=function(e){i.Descriptor.call(this,4,e)},i.DecoderConfigDescriptor.prototype=new i.Descriptor,i.DecoderConfigDescriptor.prototype.parse=function(e){this.oti=e.readUint8(),this.streamType=e.readUint8(),this.upStream=0!=(this.streamType>>1&1),this.streamType=this.streamType>>>2,this.bufferSize=e.readUint24(),this.maxBitrate=e.readUint32(),this.avgBitrate=e.readUint32(),this.size-=13,this.parseRemainingDescriptors(e)},i.DecoderSpecificInfo=function(e){i.Descriptor.call(this,5,e)},i.DecoderSpecificInfo.prototype=new i.Descriptor,i.SLConfigDescriptor=function(e){i.Descriptor.call(this,6,e)},i.SLConfigDescriptor.prototype=new i.Descriptor,this};t.MPEG4DescriptorParser=u;var h={ERR_INVALID_DATA:-1,ERR_NOT_ENOUGH_DATA:0,OK:1,BASIC_BOXES:["mdat","idat","free","skip","meco","strk"],FULL_BOXES:["hmhd","nmhd","iods","xml ","bxml","ipro","mere"],CONTAINER_BOXES:[["moov",["trak","pssh"]],["trak"],["edts"],["mdia"],["minf"],["dinf"],["stbl",["sgpd","sbgp"]],["mvex",["trex"]],["moof",["traf"]],["traf",["trun","sgpd","sbgp"]],["vttc"],["tref"],["iref"],["mfra",["tfra"]],["meco"],["hnti"],["hinf"],["strk"],["strd"],["sinf"],["rinf"],["schi"],["trgr"],["udta",["kind"]],["iprp",["ipma"]],["ipco"],["grpl"],["j2kH"],["etyp",["tyco"]]],boxCodes:[],fullBoxCodes:[],containerBoxCodes:[],sampleEntryCodes:{},sampleGroupEntryCodes:[],trackGroupTypes:[],UUIDBoxes:{},UUIDs:[],initialize:function(){h.FullBox.prototype=new h.Box,h.ContainerBox.prototype=new h.Box,h.SampleEntry.prototype=new h.Box,h.TrackGroupTypeBox.prototype=new h.FullBox,h.BASIC_BOXES.forEach((function(e){h.createBoxCtor(e)})),h.FULL_BOXES.forEach((function(e){h.createFullBoxCtor(e)})),h.CONTAINER_BOXES.forEach((function(e){h.createContainerBoxCtor(e[0],null,e[1])}))},Box:function(e,t,i){this.type=e,this.size=t,this.uuid=i},FullBox:function(e,t,i){h.Box.call(this,e,t,i),this.flags=0,this.version=0},ContainerBox:function(e,t,i){h.Box.call(this,e,t,i),this.boxes=[]},SampleEntry:function(e,t,i,r){h.ContainerBox.call(this,e,t),this.hdr_size=i,this.start=r},SampleGroupEntry:function(e){this.grouping_type=e},TrackGroupTypeBox:function(e,t){h.FullBox.call(this,e,t)},createBoxCtor:function(e,t){h.boxCodes.push(e),h[e+"Box"]=function(t){h.Box.call(this,e,t)},h[e+"Box"].prototype=new h.Box,t&&(h[e+"Box"].prototype.parse=t)},createFullBoxCtor:function(e,t){h[e+"Box"]=function(t){h.FullBox.call(this,e,t)},h[e+"Box"].prototype=new h.FullBox,h[e+"Box"].prototype.parse=function(e){this.parseFullHeader(e),t&&t.call(this,e)}},addSubBoxArrays:function(e){if(e){this.subBoxNames=e;for(var t=e.length,i=0;i<t;i++)this[e[i]+"s"]=[]}},createContainerBoxCtor:function(e,t,i){h[e+"Box"]=function(t){h.ContainerBox.call(this,e,t),h.addSubBoxArrays.call(this,i)},h[e+"Box"].prototype=new h.ContainerBox,t&&(h[e+"Box"].prototype.parse=t)},createMediaSampleEntryCtor:function(e,t,i){h.sampleEntryCodes[e]=[],h[e+"SampleEntry"]=function(e,t){h.SampleEntry.call(this,e,t),h.addSubBoxArrays.call(this,i)},h[e+"SampleEntry"].prototype=new h.SampleEntry,t&&(h[e+"SampleEntry"].prototype.parse=t)},createSampleEntryCtor:function(e,t,i,r){h.sampleEntryCodes[e].push(t),h[t+"SampleEntry"]=function(i){h[e+"SampleEntry"].call(this,t,i),h.addSubBoxArrays.call(this,r)},h[t+"SampleEntry"].prototype=new h[e+"SampleEntry"],i&&(h[t+"SampleEntry"].prototype.parse=i)},createEncryptedSampleEntryCtor:function(e,t,i){h.createSampleEntryCtor.call(this,e,t,i,["sinf"])},createSampleGroupCtor:function(e,t){h[e+"SampleGroupEntry"]=function(t){h.SampleGroupEntry.call(this,e,t)},h[e+"SampleGroupEntry"].prototype=new h.SampleGroupEntry,t&&(h[e+"SampleGroupEntry"].prototype.parse=t)},createTrackGroupCtor:function(e,t){h[e+"TrackGroupTypeBox"]=function(t){h.TrackGroupTypeBox.call(this,e,t)},h[e+"TrackGroupTypeBox"].prototype=new h.TrackGroupTypeBox,t&&(h[e+"TrackGroupTypeBox"].prototype.parse=t)},createUUIDBox:function(e,t,i,r){h.UUIDs.push(e),h.UUIDBoxes[e]=function(r){t?h.FullBox.call(this,"uuid",r,e):i?h.ContainerBox.call(this,"uuid",r,e):h.Box.call(this,"uuid",r,e)},h.UUIDBoxes[e].prototype=t?new h.FullBox:i?new h.ContainerBox:new h.Box,r&&(h.UUIDBoxes[e].prototype.parse=t?function(e){this.parseFullHeader(e),r&&r.call(this,e)}:r)}};function f(e,t){this.x=e,this.y=t}function p(e,t){this.bad_pixel_row=e,this.bad_pixel_column=t}h.initialize(),h.TKHD_FLAG_ENABLED=1,h.TKHD_FLAG_IN_MOVIE=2,h.TKHD_FLAG_IN_PREVIEW=4,h.TFHD_FLAG_BASE_DATA_OFFSET=1,h.TFHD_FLAG_SAMPLE_DESC=2,h.TFHD_FLAG_SAMPLE_DUR=8,h.TFHD_FLAG_SAMPLE_SIZE=16,h.TFHD_FLAG_SAMPLE_FLAGS=32,h.TFHD_FLAG_DUR_EMPTY=65536,h.TFHD_FLAG_DEFAULT_BASE_IS_MOOF=131072,h.TRUN_FLAGS_DATA_OFFSET=1,h.TRUN_FLAGS_FIRST_FLAG=4,h.TRUN_FLAGS_DURATION=256,h.TRUN_FLAGS_SIZE=512,h.TRUN_FLAGS_FLAGS=1024,h.TRUN_FLAGS_CTS_OFFSET=2048,h.Box.prototype.add=function(e){return this.addBox(new h[e+"Box"])},h.Box.prototype.addBox=function(e){return this.boxes.push(e),this[e.type+"s"]?this[e.type+"s"].push(e):this[e.type]=e,e},h.Box.prototype.set=function(e,t){return this[e]=t,this},h.Box.prototype.addEntry=function(e,t){var i=t||"entries";return this[i]||(this[i]=[]),this[i].push(e),this},t.BoxParser=h,h.parseUUID=function(e){return h.parseHex16(e)},h.parseHex16=function(e){for(var t="",i=0;i<16;i++){var r=e.readUint8().toString(16);t+=1===r.length?"0"+r:r}return t},h.parseOneBox=function(e,t,i){var r,s,a,o=e.getPosition(),d=0;if(e.getEndPosition()-o<8)return n.debug("BoxParser","Not enough data in stream to parse the type and size of the box"),{code:h.ERR_NOT_ENOUGH_DATA};if(i&&i<8)return n.debug("BoxParser","Not enough bytes left in the parent box to parse a new box"),{code:h.ERR_NOT_ENOUGH_DATA};var l=e.readUint32(),c=e.readString(4),u=c;if(n.debug("BoxParser","Found box of type '"+c+"' and size "+l+" at position "+o),d=8,"uuid"==c){if(e.getEndPosition()-e.getPosition()<16||i-d<16)return e.seek(o),n.debug("BoxParser","Not enough bytes left in the parent box to parse a UUID box"),{code:h.ERR_NOT_ENOUGH_DATA};d+=16,u=a=h.parseUUID(e)}if(1==l){if(e.getEndPosition()-e.getPosition()<8||i&&i-d<8)return e.seek(o),n.warn("BoxParser",'Not enough data in stream to parse the extended size of the "'+c+'" box'),{code:h.ERR_NOT_ENOUGH_DATA};l=e.readUint64(),d+=8}else if(0===l)if(i)l=i;else if("mdat"!==c)return n.error("BoxParser","Unlimited box size not supported for type: '"+c+"'"),r=new h.Box(c,l),{code:h.OK,box:r,size:r.size};return 0!==l&&l<d?(n.error("BoxParser","Box of type "+c+" has an invalid size "+l+" (too small to be a box)"),{code:h.ERR_NOT_ENOUGH_DATA,type:c,size:l,hdr_size:d,start:o}):0!==l&&i&&l>i?(n.error("BoxParser","Box of type '"+c+"' has a size "+l+" greater than its container size "+i),{code:h.ERR_NOT_ENOUGH_DATA,type:c,size:l,hdr_size:d,start:o}):0!==l&&o+l>e.getEndPosition()?(e.seek(o),n.info("BoxParser","Not enough data in stream to parse the entire '"+c+"' box"),{code:h.ERR_NOT_ENOUGH_DATA,type:c,size:l,hdr_size:d,start:o}):t?{code:h.OK,type:c,size:l,hdr_size:d,start:o}:(h[c+"Box"]?r=new h[c+"Box"](l):"uuid"!==c?(n.warn("BoxParser","Unknown box type: '"+c+"'"),(r=new h.Box(c,l)).has_unparsed_data=!0):h.UUIDBoxes[a]?r=new h.UUIDBoxes[a](l):(n.warn("BoxParser","Unknown uuid type: '"+a+"'"),(r=new h.Box(c,l)).uuid=a,r.has_unparsed_data=!0),r.hdr_size=d,r.start=o,r.write===h.Box.prototype.write&&"mdat"!==r.type&&(n.info("BoxParser","'"+u+"' box writing not yet implemented, keeping unparsed data in memory for later write"),r.parseDataAndRewind(e)),r.parse(e),(s=e.getPosition()-(r.start+r.size))<0?(n.warn("BoxParser","Parsing of box '"+u+"' did not read the entire indicated box data size (missing "+-s+" bytes), seeking forward"),e.seek(r.start+r.size)):s>0&&(n.error("BoxParser","Parsing of box '"+u+"' read "+s+" more bytes than the indicated box data size, seeking backwards"),0!==r.size&&e.seek(r.start+r.size)),{code:h.OK,box:r,size:r.size})},h.Box.prototype.parse=function(e){"mdat"!=this.type?this.data=e.readUint8Array(this.size-this.hdr_size):0===this.size?e.seek(e.getEndPosition()):e.seek(this.start+this.size)},h.Box.prototype.parseDataAndRewind=function(e){this.data=e.readUint8Array(this.size-this.hdr_size),e.position-=this.size-this.hdr_size},h.FullBox.prototype.parseDataAndRewind=function(e){this.parseFullHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size),this.hdr_size-=4,e.position-=this.size-this.hdr_size},h.FullBox.prototype.parseFullHeader=function(e){this.version=e.readUint8(),this.flags=e.readUint24(),this.hdr_size+=4},h.FullBox.prototype.parse=function(e){this.parseFullHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size)},h.ContainerBox.prototype.parse=function(e){for(var t,i;e.getPosition()<this.start+this.size;){if((t=h.parseOneBox(e,!1,this.size-(e.getPosition()-this.start))).code!==h.OK)return;if(i=t.box,this.boxes.push(i),this.subBoxNames&&-1!=this.subBoxNames.indexOf(i.type))this[this.subBoxNames[this.subBoxNames.indexOf(i.type)]+"s"].push(i);else{var r="uuid"!==i.type?i.type:i.uuid;this[r]?n.warn("Box of type "+r+" already stored in field of this type"):this[r]=i}}},h.Box.prototype.parseLanguage=function(e){this.language=e.readUint16();var t=[];t[0]=this.language>>10&31,t[1]=this.language>>5&31,t[2]=31&this.language,this.languageString=String.fromCharCode(t[0]+96,t[1]+96,t[2]+96)},h.SAMPLE_ENTRY_TYPE_VISUAL="Visual",h.SAMPLE_ENTRY_TYPE_AUDIO="Audio",h.SAMPLE_ENTRY_TYPE_HINT="Hint",h.SAMPLE_ENTRY_TYPE_METADATA="Metadata",h.SAMPLE_ENTRY_TYPE_SUBTITLE="Subtitle",h.SAMPLE_ENTRY_TYPE_SYSTEM="System",h.SAMPLE_ENTRY_TYPE_TEXT="Text",h.SampleEntry.prototype.parseHeader=function(e){e.readUint8Array(6),this.data_reference_index=e.readUint16(),this.hdr_size+=8},h.SampleEntry.prototype.parse=function(e){this.parseHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size)},h.SampleEntry.prototype.parseDataAndRewind=function(e){this.parseHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size),this.hdr_size-=8,e.position-=this.size-this.hdr_size},h.SampleEntry.prototype.parseFooter=function(e){h.ContainerBox.prototype.parse.call(this,e)},h.createMediaSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_HINT),h.createMediaSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_METADATA),h.createMediaSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_SUBTITLE),h.createMediaSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_SYSTEM),h.createMediaSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_TEXT),h.createMediaSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,(function(e){var t;this.parseHeader(e),e.readUint16(),e.readUint16(),e.readUint32Array(3),this.width=e.readUint16(),this.height=e.readUint16(),this.horizresolution=e.readUint32(),this.vertresolution=e.readUint32(),e.readUint32(),this.frame_count=e.readUint16(),t=Math.min(31,e.readUint8()),this.compressorname=e.readString(t),t<31&&e.readString(31-t),this.depth=e.readUint16(),e.readUint16(),this.parseFooter(e)})),h.createMediaSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_AUDIO,(function(e){this.parseHeader(e),e.readUint32Array(2),this.channel_count=e.readUint16(),this.samplesize=e.readUint16(),e.readUint16(),e.readUint16(),this.samplerate=e.readUint32()/65536,this.parseFooter(e)})),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"avc1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"avc2"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"avc3"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"avc4"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"av01"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"dav1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"hvc1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"hev1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"hvt1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"lhe1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"dvh1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"dvhe"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"vvc1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"vvi1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"vvs1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"vvcN"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"vp08"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"vp09"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"avs3"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"j2ki"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"mjp2"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"mjpg"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"uncv"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_AUDIO,"mp4a"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_AUDIO,"ac-3"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_AUDIO,"ac-4"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_AUDIO,"ec-3"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_AUDIO,"Opus"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_AUDIO,"mha1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_AUDIO,"mha2"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_AUDIO,"mhm1"),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_AUDIO,"mhm2"),h.createEncryptedSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_VISUAL,"encv"),h.createEncryptedSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_AUDIO,"enca"),h.createEncryptedSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_SUBTITLE,"encu"),h.createEncryptedSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_SYSTEM,"encs"),h.createEncryptedSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_TEXT,"enct"),h.createEncryptedSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_METADATA,"encm"),h.createBoxCtor("a1lx",(function(e){var t=16*(1+(1&(1&e.readUint8())));this.layer_size=[];for(var i=0;i<3;i++)this.layer_size[i]=16==t?e.readUint16():e.readUint32()})),h.createBoxCtor("a1op",(function(e){this.op_index=e.readUint8()})),h.createFullBoxCtor("auxC",(function(e){this.aux_type=e.readCString();var t=this.size-this.hdr_size-(this.aux_type.length+1);this.aux_subtype=e.readUint8Array(t)})),h.createBoxCtor("av1C",(function(e){var t=e.readUint8();if(t>>7&!1)n.error("av1C marker problem");else if(this.version=127&t,1===this.version)if(t=e.readUint8(),this.seq_profile=t>>5&7,this.seq_level_idx_0=31&t,t=e.readUint8(),this.seq_tier_0=t>>7&1,this.high_bitdepth=t>>6&1,this.twelve_bit=t>>5&1,this.monochrome=t>>4&1,this.chroma_subsampling_x=t>>3&1,this.chroma_subsampling_y=t>>2&1,this.chroma_sample_position=3&t,t=e.readUint8(),this.reserved_1=t>>5&7,0===this.reserved_1){if(this.initial_presentation_delay_present=t>>4&1,1===this.initial_presentation_delay_present)this.initial_presentation_delay_minus_one=15&t;else if(this.reserved_2=15&t,0!==this.reserved_2)return void n.error("av1C reserved_2 parsing problem");var i=this.size-this.hdr_size-4;this.configOBUs=e.readUint8Array(i)}else n.error("av1C reserved_1 parsing problem");else n.error("av1C version "+this.version+" not supported")})),h.createBoxCtor("avcC",(function(e){var t,i;for(this.configurationVersion=e.readUint8(),this.AVCProfileIndication=e.readUint8(),this.profile_compatibility=e.readUint8(),this.AVCLevelIndication=e.readUint8(),this.lengthSizeMinusOne=3&e.readUint8(),this.nb_SPS_nalus=31&e.readUint8(),i=this.size-this.hdr_size-6,this.SPS=[],t=0;t<this.nb_SPS_nalus;t++)this.SPS[t]={},this.SPS[t].length=e.readUint16(),this.SPS[t].nalu=e.readUint8Array(this.SPS[t].length),i-=2+this.SPS[t].length;for(this.nb_PPS_nalus=e.readUint8(),i--,this.PPS=[],t=0;t<this.nb_PPS_nalus;t++)this.PPS[t]={},this.PPS[t].length=e.readUint16(),this.PPS[t].nalu=e.readUint8Array(this.PPS[t].length),i-=2+this.PPS[t].length;i>0&&(this.ext=e.readUint8Array(i))})),h.createBoxCtor("btrt",(function(e){this.bufferSizeDB=e.readUint32(),this.maxBitrate=e.readUint32(),this.avgBitrate=e.readUint32()})),h.createFullBoxCtor("ccst",(function(e){var t=e.readUint8();this.all_ref_pics_intra=128==(128&t),this.intra_pred_used=64==(64&t),this.max_ref_per_pic=(63&t)>>2,e.readUint24()})),h.createBoxCtor("cdef",(function(e){var t;for(this.channel_count=e.readUint16(),this.channel_indexes=[],this.channel_types=[],this.channel_associations=[],t=0;t<this.channel_count;t++)this.channel_indexes.push(e.readUint16()),this.channel_types.push(e.readUint16()),this.channel_associations.push(e.readUint16())})),h.createBoxCtor("clap",(function(e){this.cleanApertureWidthN=e.readUint32(),this.cleanApertureWidthD=e.readUint32(),this.cleanApertureHeightN=e.readUint32(),this.cleanApertureHeightD=e.readUint32(),this.horizOffN=e.readUint32(),this.horizOffD=e.readUint32(),this.vertOffN=e.readUint32(),this.vertOffD=e.readUint32()})),h.createBoxCtor("clli",(function(e){this.max_content_light_level=e.readUint16(),this.max_pic_average_light_level=e.readUint16()})),h.createFullBoxCtor("cmex",(function(e){1&this.flags&&(this.pos_x=e.readInt32()),2&this.flags&&(this.pos_y=e.readInt32()),4&this.flags&&(this.pos_z=e.readInt32()),8&this.flags&&(0==this.version?16&this.flags?(this.quat_x=e.readInt32(),this.quat_y=e.readInt32(),this.quat_z=e.readInt32()):(this.quat_x=e.readInt16(),this.quat_y=e.readInt16(),this.quat_z=e.readInt16()):this.version),32&this.flags&&(this.id=e.readUint32())})),h.createFullBoxCtor("cmin",(function(e){this.focal_length_x=e.readInt32(),this.principal_point_x=e.readInt32(),this.principal_point_y=e.readInt32(),1&this.flags&&(this.focal_length_y=e.readInt32(),this.skew_factor=e.readInt32())})),h.createBoxCtor("cmpd",(function(e){for(this.component_count=e.readUint32(),this.component_types=[],this.component_type_urls=[],i=0;i<this.component_count;i++){var t=e.readUint16();this.component_types.push(t),t>=32768&&this.component_type_urls.push(e.readCString())}})),h.createFullBoxCtor("co64",(function(e){var t,i;if(t=e.readUint32(),this.chunk_offsets=[],0===this.version)for(i=0;i<t;i++)this.chunk_offsets.push(e.readUint64())})),h.createFullBoxCtor("CoLL",(function(e){this.maxCLL=e.readUint16(),this.maxFALL=e.readUint16()})),h.createBoxCtor("colr",(function(e){if(this.colour_type=e.readString(4),"nclx"===this.colour_type){this.colour_primaries=e.readUint16(),this.transfer_characteristics=e.readUint16(),this.matrix_coefficients=e.readUint16();var t=e.readUint8();this.full_range_flag=t>>7}else("rICC"===this.colour_type||"prof"===this.colour_type)&&(this.ICC_profile=e.readUint8Array(this.size-4))})),h.createFullBoxCtor("cprt",(function(e){this.parseLanguage(e),this.notice=e.readCString()})),h.createFullBoxCtor("cslg",(function(e){0===this.version&&(this.compositionToDTSShift=e.readInt32(),this.leastDecodeToDisplayDelta=e.readInt32(),this.greatestDecodeToDisplayDelta=e.readInt32(),this.compositionStartTime=e.readInt32(),this.compositionEndTime=e.readInt32())})),h.createFullBoxCtor("ctts",(function(e){var t,i;if(t=e.readUint32(),this.sample_counts=[],this.sample_offsets=[],0===this.version)for(i=0;i<t;i++){this.sample_counts.push(e.readUint32());var r=e.readInt32();r<0&&n.warn("BoxParser","ctts box uses negative values without using version 1"),this.sample_offsets.push(r)}else if(1==this.version)for(i=0;i<t;i++)this.sample_counts.push(e.readUint32()),this.sample_offsets.push(e.readInt32())})),h.createBoxCtor("dac3",(function(e){var t=e.readUint8(),i=e.readUint8(),r=e.readUint8();this.fscod=t>>6,this.bsid=t>>1&31,this.bsmod=(1&t)<<2|i>>6&3,this.acmod=i>>3&7,this.lfeon=i>>2&1,this.bit_rate_code=3&i|r>>5&7})),h.createBoxCtor("dec3",(function(e){var t=e.readUint16();this.data_rate=t>>3,this.num_ind_sub=7&t,this.ind_subs=[];for(var i=0;i<this.num_ind_sub+1;i++){var r={};this.ind_subs.push(r);var s=e.readUint8(),a=e.readUint8(),n=e.readUint8();r.fscod=s>>6,r.bsid=s>>1&31,r.bsmod=(1&s)<<4|a>>4&15,r.acmod=a>>1&7,r.lfeon=1&a,r.num_dep_sub=n>>1&15,r.num_dep_sub>0&&(r.chan_loc=(1&n)<<8|e.readUint8())}})),h.createFullBoxCtor("dfLa",(function(e){var t=[],i=["STREAMINFO","PADDING","APPLICATION","SEEKTABLE","VORBIS_COMMENT","CUESHEET","PICTURE","RESERVED"];for(this.parseFullHeader(e);;){var r=e.readUint8(),s=Math.min(127&r,i.length-1);if(s?e.readUint8Array(e.readUint24()):(e.readUint8Array(13),this.samplerate=e.readUint32()>>12,e.readUint8Array(20)),t.push(i[s]),128&r)break}this.numMetadataBlocks=t.length+" ("+t.join(", ")+")"})),h.createBoxCtor("dimm",(function(e){this.bytessent=e.readUint64()})),h.createBoxCtor("dmax",(function(e){this.time=e.readUint32()})),h.createBoxCtor("dmed",(function(e){this.bytessent=e.readUint64()})),h.createBoxCtor("dOps",(function(e){if(this.Version=e.readUint8(),this.OutputChannelCount=e.readUint8(),this.PreSkip=e.readUint16(),this.InputSampleRate=e.readUint32(),this.OutputGain=e.readInt16(),this.ChannelMappingFamily=e.readUint8(),0!==this.ChannelMappingFamily){this.StreamCount=e.readUint8(),this.CoupledCount=e.readUint8(),this.ChannelMapping=[];for(var t=0;t<this.OutputChannelCount;t++)this.ChannelMapping[t]=e.readUint8()}})),h.createFullBoxCtor("dref",(function(e){var t,i;this.entries=[];for(var r=e.readUint32(),s=0;s<r;s++){if((t=h.parseOneBox(e,!1,this.size-(e.getPosition()-this.start))).code!==h.OK)return;i=t.box,this.entries.push(i)}})),h.createBoxCtor("drep",(function(e){this.bytessent=e.readUint64()})),h.createFullBoxCtor("elng",(function(e){this.extended_language=e.readString(this.size-this.hdr_size)})),h.createFullBoxCtor("elst",(function(e){this.entries=[];for(var t=e.readUint32(),i=0;i<t;i++){var r={};this.entries.push(r),1===this.version?(r.segment_duration=e.readUint64(),r.media_time=e.readInt64()):(r.segment_duration=e.readUint32(),r.media_time=e.readInt32()),r.media_rate_integer=e.readInt16(),r.media_rate_fraction=e.readInt16()}})),h.createFullBoxCtor("emsg",(function(e){1==this.version?(this.timescale=e.readUint32(),this.presentation_time=e.readUint64(),this.event_duration=e.readUint32(),this.id=e.readUint32(),this.scheme_id_uri=e.readCString(),this.value=e.readCString()):(this.scheme_id_uri=e.readCString(),this.value=e.readCString(),this.timescale=e.readUint32(),this.presentation_time_delta=e.readUint32(),this.event_duration=e.readUint32(),this.id=e.readUint32());var t=this.size-this.hdr_size-(16+(this.scheme_id_uri.length+1)+(this.value.length+1));1==this.version&&(t-=4),this.message_data=e.readUint8Array(t)})),h.createEntityToGroupCtor=function(e,t){h[e+"Box"]=function(t){h.FullBox.call(this,e,t)},h[e+"Box"].prototype=new h.FullBox,h[e+"Box"].prototype.parse=function(e){if(this.parseFullHeader(e),t)t.call(this,e);else for(this.group_id=e.readUint32(),this.num_entities_in_group=e.readUint32(),this.entity_ids=[],i=0;i<this.num_entities_in_group;i++){var r=e.readUint32();this.entity_ids.push(r)}}},h.createEntityToGroupCtor("aebr"),h.createEntityToGroupCtor("afbr"),h.createEntityToGroupCtor("albc"),h.createEntityToGroupCtor("altr"),h.createEntityToGroupCtor("brst"),h.createEntityToGroupCtor("dobr"),h.createEntityToGroupCtor("eqiv"),h.createEntityToGroupCtor("favc"),h.createEntityToGroupCtor("fobr"),h.createEntityToGroupCtor("iaug"),h.createEntityToGroupCtor("pano"),h.createEntityToGroupCtor("slid"),h.createEntityToGroupCtor("ster"),h.createEntityToGroupCtor("tsyn"),h.createEntityToGroupCtor("wbbr"),h.createEntityToGroupCtor("prgr"),h.createFullBoxCtor("esds",(function(e){var t=e.readUint8Array(this.size-this.hdr_size),i=new u;this.esd=i.parseOneDescriptor(new d(t.buffer,0,d.BIG_ENDIAN))})),h.createBoxCtor("fiel",(function(e){this.fieldCount=e.readUint8(),this.fieldOrdering=e.readUint8()})),h.createBoxCtor("frma",(function(e){this.data_format=e.readString(4)})),h.createBoxCtor("ftyp",(function(e){var t=this.size-this.hdr_size;this.major_brand=e.readString(4),this.minor_version=e.readUint32(),t-=8,this.compatible_brands=[];for(var i=0;t>=4;)this.compatible_brands[i]=e.readString(4),t-=4,i++})),h.createFullBoxCtor("hdlr",(function(e){0===this.version&&(e.readUint32(),this.handler=e.readString(4),e.readUint32Array(3),this.name=e.readString(this.size-this.hdr_size-20),"\0"===this.name[this.name.length-1]&&(this.name=this.name.slice(0,-1)))})),h.createBoxCtor("hvcC",(function(e){var t,i,r,s;this.configurationVersion=e.readUint8(),s=e.readUint8(),this.general_profile_space=s>>6,this.general_tier_flag=(32&s)>>5,this.general_profile_idc=31&s,this.general_profile_compatibility=e.readUint32(),this.general_constraint_indicator=e.readUint8Array(6),this.general_level_idc=e.readUint8(),this.min_spatial_segmentation_idc=4095&e.readUint16(),this.parallelismType=3&e.readUint8(),this.chroma_format_idc=3&e.readUint8(),this.bit_depth_luma_minus8=7&e.readUint8(),this.bit_depth_chroma_minus8=7&e.readUint8(),this.avgFrameRate=e.readUint16(),s=e.readUint8(),this.constantFrameRate=s>>6,this.numTemporalLayers=(13&s)>>3,this.temporalIdNested=(4&s)>>2,this.lengthSizeMinusOne=3&s,this.nalu_arrays=[];var a=e.readUint8();for(t=0;t<a;t++){var n=[];this.nalu_arrays.push(n),s=e.readUint8(),n.completeness=(128&s)>>7,n.nalu_type=63&s;var o=e.readUint16();for(i=0;i<o;i++){var d={};n.push(d),r=e.readUint16(),d.data=e.readUint8Array(r)}}})),h.createFullBoxCtor("iinf",(function(e){var t;0===this.version?this.entry_count=e.readUint16():this.entry_count=e.readUint32(),this.item_infos=[];for(var i=0;i<this.entry_count;i++){if((t=h.parseOneBox(e,!1,this.size-(e.getPosition()-this.start))).code!==h.OK)return;"infe"!==t.box.type&&n.error("BoxParser","Expected 'infe' box, got "+t.box.type),this.item_infos[i]=t.box}})),h.createFullBoxCtor("iloc",(function(e){var t;t=e.readUint8(),this.offset_size=t>>4&15,this.length_size=15&t,t=e.readUint8(),this.base_offset_size=t>>4&15,1===this.version||2===this.version?this.index_size=15&t:this.index_size=0,this.items=[];var i=0;if(this.version<2)i=e.readUint16();else{if(2!==this.version)throw"version of iloc box not supported";i=e.readUint32()}for(var r=0;r<i;r++){var s={};if(this.items.push(s),this.version<2)s.item_ID=e.readUint16();else{if(2!==this.version)throw"version of iloc box not supported";s.item_ID=e.readUint32()}switch(1===this.version||2===this.version?s.construction_method=15&e.readUint16():s.construction_method=0,s.data_reference_index=e.readUint16(),this.base_offset_size){case 0:s.base_offset=0;break;case 4:s.base_offset=e.readUint32();break;case 8:s.base_offset=e.readUint64();break;default:throw"Error reading base offset size"}var a=e.readUint16();s.extents=[];for(var n=0;n<a;n++){var o={};if(s.extents.push(o),1===this.version||2===this.version)switch(this.index_size){case 0:o.extent_index=0;break;case 4:o.extent_index=e.readUint32();break;case 8:o.extent_index=e.readUint64();break;default:throw"Error reading extent index"}switch(this.offset_size){case 0:o.extent_offset=0;break;case 4:o.extent_offset=e.readUint32();break;case 8:o.extent_offset=e.readUint64();break;default:throw"Error reading extent index"}switch(this.length_size){case 0:o.extent_length=0;break;case 4:o.extent_length=e.readUint32();break;case 8:o.extent_length=e.readUint64();break;default:throw"Error reading extent index"}}}})),h.createBoxCtor("imir",(function(e){var t=e.readUint8();this.reserved=t>>7,this.axis=1&t})),h.createFullBoxCtor("infe",(function(e){if(0!==this.version&&1!==this.version||(this.item_ID=e.readUint16(),this.item_protection_index=e.readUint16(),this.item_name=e.readCString(),this.content_type=e.readCString(),this.content_encoding=e.readCString()),1===this.version)return this.extension_type=e.readString(4),n.warn("BoxParser","Cannot parse extension type"),void e.seek(this.start+this.size);this.version>=2&&(2===this.version?this.item_ID=e.readUint16():3===this.version&&(this.item_ID=e.readUint32()),this.item_protection_index=e.readUint16(),this.item_type=e.readString(4),this.item_name=e.readCString(),"mime"===this.item_type?(this.content_type=e.readCString(),this.content_encoding=e.readCString()):"uri "===this.item_type&&(this.item_uri_type=e.readCString()))})),h.createFullBoxCtor("ipma",(function(e){var t,i;for(entry_count=e.readUint32(),this.associations=[],t=0;t<entry_count;t++){var r={};this.associations.push(r),this.version<1?r.id=e.readUint16():r.id=e.readUint32();var s=e.readUint8();for(r.props=[],i=0;i<s;i++){var a=e.readUint8(),n={};r.props.push(n),n.essential=(128&a)>>7==1,1&this.flags?n.property_index=(127&a)<<8|e.readUint8():n.property_index=127&a}}})),h.createFullBoxCtor("iref",(function(e){var t,i;for(this.references=[];e.getPosition()<this.start+this.size;){if((t=h.parseOneBox(e,!0,this.size-(e.getPosition()-this.start))).code!==h.OK)return;(i=0===this.version?new h.SingleItemTypeReferenceBox(t.type,t.size,t.hdr_size,t.start):new h.SingleItemTypeReferenceBoxLarge(t.type,t.size,t.hdr_size,t.start)).write===h.Box.prototype.write&&"mdat"!==i.type&&(n.warn("BoxParser",i.type+" box writing not yet implemented, keeping unparsed data in memory for later write"),i.parseDataAndRewind(e)),i.parse(e),this.references.push(i)}})),h.createBoxCtor("irot",(function(e){this.angle=3&e.readUint8()})),h.createFullBoxCtor("ispe",(function(e){this.image_width=e.readUint32(),this.image_height=e.readUint32()})),h.createFullBoxCtor("kind",(function(e){this.schemeURI=e.readCString(),this.value=e.readCString()})),h.createFullBoxCtor("leva",(function(e){var t=e.readUint8();this.levels=[];for(var i=0;i<t;i++){var r={};this.levels[i]=r,r.track_ID=e.readUint32();var s=e.readUint8();switch(r.padding_flag=s>>7,r.assignment_type=127&s,r.assignment_type){case 0:r.grouping_type=e.readString(4);break;case 1:r.grouping_type=e.readString(4),r.grouping_type_parameter=e.readUint32();break;case 2:case 3:break;case 4:r.sub_track_id=e.readUint32();break;default:n.warn("BoxParser","Unknown leva assignement type")}}})),h.createBoxCtor("lsel",(function(e){this.layer_id=e.readUint16()})),h.createBoxCtor("maxr",(function(e){this.period=e.readUint32(),this.bytes=e.readUint32()})),f.prototype.toString=function(){return"("+this.x+","+this.y+")"},h.createBoxCtor("mdcv",(function(e){this.display_primaries=[],this.display_primaries[0]=new f(e.readUint16(),e.readUint16()),this.display_primaries[1]=new f(e.readUint16(),e.readUint16()),this.display_primaries[2]=new f(e.readUint16(),e.readUint16()),this.white_point=new f(e.readUint16(),e.readUint16()),this.max_display_mastering_luminance=e.readUint32(),this.min_display_mastering_luminance=e.readUint32()})),h.createFullBoxCtor("mdhd",(function(e){1==this.version?(this.creation_time=e.readUint64(),this.modification_time=e.readUint64(),this.timescale=e.readUint32(),this.duration=e.readUint64()):(this.creation_time=e.readUint32(),this.modification_time=e.readUint32(),this.timescale=e.readUint32(),this.duration=e.readUint32()),this.parseLanguage(e),e.readUint16()})),h.createFullBoxCtor("mehd",(function(e){1&this.flags&&(n.warn("BoxParser","mehd box incorrectly uses flags set to 1, converting version to 1"),this.version=1),1==this.version?this.fragment_duration=e.readUint64():this.fragment_duration=e.readUint32()})),h.createFullBoxCtor("meta",(function(e){this.boxes=[],h.ContainerBox.prototype.parse.call(this,e)})),h.createFullBoxCtor("mfhd",(function(e){this.sequence_number=e.readUint32()})),h.createFullBoxCtor("mfro",(function(e){this._size=e.readUint32()})),h.createFullBoxCtor("mskC",(function(e){this.bits_per_pixel=e.readUint8()})),h.createFullBoxCtor("mvhd",(function(e){1==this.version?(this.creation_time=e.readUint64(),this.modification_time=e.readUint64(),this.timescale=e.readUint32(),this.duration=e.readUint64()):(this.creation_time=e.readUint32(),this.modification_time=e.readUint32(),this.timescale=e.readUint32(),this.duration=e.readUint32()),this.rate=e.readUint32(),this.volume=e.readUint16()>>8,e.readUint16(),e.readUint32Array(2),this.matrix=e.readUint32Array(9),e.readUint32Array(6),this.next_track_id=e.readUint32()})),h.createBoxCtor("npck",(function(e){this.packetssent=e.readUint32()})),h.createBoxCtor("nump",(function(e){this.packetssent=e.readUint64()})),h.createFullBoxCtor("padb",(function(e){var t=e.readUint32();this.padbits=[];for(var i=0;i<Math.floor((t+1)/2);i++)this.padbits=e.readUint8()})),h.createBoxCtor("pasp",(function(e){this.hSpacing=e.readUint32(),this.vSpacing=e.readUint32()})),h.createBoxCtor("payl",(function(e){this.text=e.readString(this.size-this.hdr_size)})),h.createBoxCtor("payt",(function(e){this.payloadID=e.readUint32();var t=e.readUint8();this.rtpmap_string=e.readString(t)})),h.createFullBoxCtor("pdin",(function(e){var t=(this.size-this.hdr_size)/8;this.rate=[],this.initial_delay=[];for(var i=0;i<t;i++)this.rate[i]=e.readUint32(),this.initial_delay[i]=e.readUint32()})),h.createFullBoxCtor("pitm",(function(e){0===this.version?this.item_id=e.readUint16():this.item_id=e.readUint32()})),h.createFullBoxCtor("pixi",(function(e){var t;for(this.num_channels=e.readUint8(),this.bits_per_channels=[],t=0;t<this.num_channels;t++)this.bits_per_channels[t]=e.readUint8()})),h.createBoxCtor("pmax",(function(e){this.bytes=e.readUint32()})),h.createFullBoxCtor("prdi",(function(e){if(this.step_count=e.readUint16(),this.item_count=[],2&this.flags)for(var t=0;t<this.step_count;t++)this.item_count[t]=e.readUint16()})),h.createFullBoxCtor("prft",(function(e){this.ref_track_id=e.readUint32(),this.ntp_timestamp=e.readUint64(),0===this.version?this.media_time=e.readUint32():this.media_time=e.readUint64()})),h.createFullBoxCtor("pssh",(function(e){if(this.system_id=h.parseHex16(e),this.version>0){var t=e.readUint32();this.kid=[];for(var i=0;i<t;i++)this.kid[i]=h.parseHex16(e)}var r=e.readUint32();r>0&&(this.data=e.readUint8Array(r))})),h.createFullBoxCtor("clef",(function(e){this.width=e.readUint32(),this.height=e.readUint32()})),h.createFullBoxCtor("enof",(function(e){this.width=e.readUint32(),this.height=e.readUint32()})),h.createFullBoxCtor("prof",(function(e){this.width=e.readUint32(),this.height=e.readUint32()})),h.createContainerBoxCtor("tapt",null,["clef","prof","enof"]),h.createBoxCtor("rtp ",(function(e){this.descriptionformat=e.readString(4),this.sdptext=e.readString(this.size-this.hdr_size-4)})),h.createFullBoxCtor("saio",(function(e){1&this.flags&&(this.aux_info_type=e.readUint32(),this.aux_info_type_parameter=e.readUint32());var t=e.readUint32();this.offset=[];for(var i=0;i<t;i++)0===this.version?this.offset[i]=e.readUint32():this.offset[i]=e.readUint64()})),h.createFullBoxCtor("saiz",(function(e){1&this.flags&&(this.aux_info_type=e.readUint32(),this.aux_info_type_parameter=e.readUint32()),this.default_sample_info_size=e.readUint8();var t=e.readUint32();if(this.sample_info_size=[],0===this.default_sample_info_size)for(var i=0;i<t;i++)this.sample_info_size[i]=e.readUint8()})),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_METADATA,"mett",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.mime_format=e.readCString(),this.parseFooter(e)})),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_METADATA,"metx",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.namespace=e.readCString(),this.schema_location=e.readCString(),this.parseFooter(e)})),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_SUBTITLE,"sbtt",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.mime_format=e.readCString(),this.parseFooter(e)})),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_SUBTITLE,"stpp",(function(e){this.parseHeader(e),this.namespace=e.readCString(),this.schema_location=e.readCString(),this.auxiliary_mime_types=e.readCString(),this.parseFooter(e)})),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_SUBTITLE,"stxt",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.mime_format=e.readCString(),this.parseFooter(e)})),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_SUBTITLE,"tx3g",(function(e){this.parseHeader(e),this.displayFlags=e.readUint32(),this.horizontal_justification=e.readInt8(),this.vertical_justification=e.readInt8(),this.bg_color_rgba=e.readUint8Array(4),this.box_record=e.readInt16Array(4),this.style_record=e.readUint8Array(12),this.parseFooter(e)})),h.createSampleEntryCtor(h.SAMPLE_ENTRY_TYPE_METADATA,"wvtt",(function(e){this.parseHeader(e),this.parseFooter(e)})),h.createSampleGroupCtor("alst",(function(e){var t,i=e.readUint16();for(this.first_output_sample=e.readUint16(),this.sample_offset=[],t=0;t<i;t++)this.sample_offset[t]=e.readUint32();var r=this.description_length-4-4*i;for(this.num_output_samples=[],this.num_total_samples=[],t=0;t<r/4;t++)this.num_output_samples[t]=e.readUint16(),this.num_total_samples[t]=e.readUint16()})),h.createSampleGroupCtor("avll",(function(e){this.layerNumber=e.readUint8(),this.accurateStatisticsFlag=e.readUint8(),this.avgBitRate=e.readUint16(),this.avgFrameRate=e.readUint16()})),h.createSampleGroupCtor("avss",(function(e){this.subSequenceIdentifier=e.readUint16(),this.layerNumber=e.readUint8();var t=e.readUint8();this.durationFlag=t>>7,this.avgRateFlag=t>>6&1,this.durationFlag&&(this.duration=e.readUint32()),this.avgRateFlag&&(this.accurateStatisticsFlag=e.readUint8(),this.avgBitRate=e.readUint16(),this.avgFrameRate=e.readUint16()),this.dependency=[];for(var i=e.readUint8(),r=0;r<i;r++){var s={};this.dependency.push(s),s.subSeqDirectionFlag=e.readUint8(),s.layerNumber=e.readUint8(),s.subSequenceIdentifier=e.readUint16()}})),h.createSampleGroupCtor("dtrt",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),h.createSampleGroupCtor("mvif",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),h.createSampleGroupCtor("prol",(function(e){this.roll_distance=e.readInt16()})),h.createSampleGroupCtor("rap ",(function(e){var t=e.readUint8();this.num_leading_samples_known=t>>7,this.num_leading_samples=127&t})),h.createSampleGroupCtor("rash",(function(e){if(this.operation_point_count=e.readUint16(),this.description_length!==2+(1===this.operation_point_count?2:6*this.operation_point_count)+9)n.warn("BoxParser","Mismatch in "+this.grouping_type+" sample group length"),this.data=e.readUint8Array(this.description_length-2);else{if(1===this.operation_point_count)this.target_rate_share=e.readUint16();else{this.target_rate_share=[],this.available_bitrate=[];for(var t=0;t<this.operation_point_count;t++)this.available_bitrate[t]=e.readUint32(),this.target_rate_share[t]=e.readUint16()}this.maximum_bitrate=e.readUint32(),this.minimum_bitrate=e.readUint32(),this.discard_priority=e.readUint8()}})),h.createSampleGroupCtor("roll",(function(e){this.roll_distance=e.readInt16()})),h.SampleGroupEntry.prototype.parse=function(e){n.warn("BoxParser","Unknown Sample Group type: "+this.grouping_type),this.data=e.readUint8Array(this.description_length)},h.createSampleGroupCtor("scif",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),h.createSampleGroupCtor("scnm",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),h.createSampleGroupCtor("seig",(function(e){this.reserved=e.readUint8();var t=e.readUint8();this.crypt_byte_block=t>>4,this.skip_byte_block=15&t,this.isProtected=e.readUint8(),this.Per_Sample_IV_Size=e.readUint8(),this.KID=h.parseHex16(e),this.constant_IV_size=0,this.constant_IV=0,1===this.isProtected&&0===this.Per_Sample_IV_Size&&(this.constant_IV_size=e.readUint8(),this.constant_IV=e.readUint8Array(this.constant_IV_size))})),h.createSampleGroupCtor("stsa",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),h.createSampleGroupCtor("sync",(function(e){var t=e.readUint8();this.NAL_unit_type=63&t})),h.createSampleGroupCtor("tele",(function(e){var t=e.readUint8();this.level_independently_decodable=t>>7})),h.createSampleGroupCtor("tsas",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),h.createSampleGroupCtor("tscl",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),h.createSampleGroupCtor("vipr",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),h.createFullBoxCtor("sbgp",(function(e){this.grouping_type=e.readString(4),1===this.version?this.grouping_type_parameter=e.readUint32():this.grouping_type_parameter=0,this.entries=[];for(var t=e.readUint32(),i=0;i<t;i++){var r={};this.entries.push(r),r.sample_count=e.readInt32(),r.group_description_index=e.readInt32()}})),p.prototype.toString=function(){return"[row: "+this.bad_pixel_row+", column: "+this.bad_pixel_column+"]"},h.createFullBoxCtor("sbpm",(function(e){var t;for(this.component_count=e.readUint16(),this.component_index=[],t=0;t<this.component_count;t++)this.component_index.push(e.readUint16());var i=e.readUint8();for(this.correction_applied=128==(128&i),this.num_bad_rows=e.readUint32(),this.num_bad_cols=e.readUint32(),this.num_bad_pixels=e.readUint32(),this.bad_rows=[],this.bad_columns=[],this.bad_pixels=[],t=0;t<this.num_bad_rows;t++)this.bad_rows.push(e.readUint32());for(t=0;t<this.num_bad_cols;t++)this.bad_columns.push(e.readUint32());for(t=0;t<this.num_bad_pixels;t++){var r=e.readUint32(),s=e.readUint32();this.bad_pixels.push(new p(r,s))}})),h.createFullBoxCtor("schm",(function(e){this.scheme_type=e.readString(4),this.scheme_version=e.readUint32(),1&this.flags&&(this.scheme_uri=e.readString(this.size-this.hdr_size-8))})),h.createBoxCtor("sdp ",(function(e){this.sdptext=e.readString(this.size-this.hdr_size)})),h.createFullBoxCtor("sdtp",(function(e){var t,i=this.size-this.hdr_size;this.is_leading=[],this.sample_depends_on=[],this.sample_is_depended_on=[],this.sample_has_redundancy=[];for(var r=0;r<i;r++)t=e.readUint8(),this.is_leading[r]=t>>6,this.sample_depends_on[r]=t>>4&3,this.sample_is_depended_on[r]=t>>2&3,this.sample_has_redundancy[r]=3&t})),h.createFullBoxCtor("senc"),h.createFullBoxCtor("sgpd",(function(e){this.grouping_type=e.readString(4),n.debug("BoxParser","Found Sample Groups of type "+this.grouping_type),1===this.version?this.default_length=e.readUint32():this.default_length=0,this.version>=2&&(this.default_group_description_index=e.readUint32()),this.entries=[];for(var t=e.readUint32(),i=0;i<t;i++){var r;r=h[this.grouping_type+"SampleGroupEntry"]?new h[this.grouping_type+"SampleGroupEntry"](this.grouping_type):new h.SampleGroupEntry(this.grouping_type),this.entries.push(r),1===this.version&&0===this.default_length?r.description_length=e.readUint32():r.description_length=this.default_length,r.write===h.SampleGroupEntry.prototype.write&&(n.info("BoxParser","SampleGroup for type "+this.grouping_type+" writing not yet implemented, keeping unparsed data in memory for later write"),r.data=e.readUint8Array(r.description_length),e.position-=r.description_length),r.parse(e)}})),h.createFullBoxCtor("sidx",(function(e){this.reference_ID=e.readUint32(),this.timescale=e.readUint32(),0===this.version?(this.earliest_presentation_time=e.readUint32(),this.first_offset=e.readUint32()):(this.earliest_presentation_time=e.readUint64(),this.first_offset=e.readUint64()),e.readUint16(),this.references=[];for(var t=e.readUint16(),i=0;i<t;i++){var r={};this.references.push(r);var s=e.readUint32();r.reference_type=s>>31&1,r.referenced_size=2147483647&s,r.subsegment_duration=e.readUint32(),s=e.readUint32(),r.starts_with_SAP=s>>31&1,r.SAP_type=s>>28&7,r.SAP_delta_time=268435455&s}})),h.SingleItemTypeReferenceBox=function(e,t,i,r){h.Box.call(this,e,t),this.hdr_size=i,this.start=r},h.SingleItemTypeReferenceBox.prototype=new h.Box,h.SingleItemTypeReferenceBox.prototype.parse=function(e){this.from_item_ID=e.readUint16();var t=e.readUint16();this.references=[];for(var i=0;i<t;i++)this.references[i]={},this.references[i].to_item_ID=e.readUint16()},h.SingleItemTypeReferenceBoxLarge=function(e,t,i,r){h.Box.call(this,e,t),this.hdr_size=i,this.start=r},h.SingleItemTypeReferenceBoxLarge.prototype=new h.Box,h.SingleItemTypeReferenceBoxLarge.prototype.parse=function(e){this.from_item_ID=e.readUint32();var t=e.readUint16();this.references=[];for(var i=0;i<t;i++)this.references[i]={},this.references[i].to_item_ID=e.readUint32()},h.createFullBoxCtor("SmDm",(function(e){this.primaryRChromaticity_x=e.readUint16(),this.primaryRChromaticity_y=e.readUint16(),this.primaryGChromaticity_x=e.readUint16(),this.primaryGChromaticity_y=e.readUint16(),this.primaryBChromaticity_x=e.readUint16(),this.primaryBChromaticity_y=e.readUint16(),this.whitePointChromaticity_x=e.readUint16(),this.whitePointChromaticity_y=e.readUint16(),this.luminanceMax=e.readUint32(),this.luminanceMin=e.readUint32()})),h.createFullBoxCtor("smhd",(function(e){this.balance=e.readUint16(),e.readUint16()})),h.createFullBoxCtor("ssix",(function(e){this.subsegments=[];for(var t=e.readUint32(),i=0;i<t;i++){var r={};this.subsegments.push(r),r.ranges=[];for(var s=e.readUint32(),a=0;a<s;a++){var n={};r.ranges.push(n),n.level=e.readUint8(),n.range_size=e.readUint24()}}})),h.createFullBoxCtor("stco",(function(e){var t;if(t=e.readUint32(),this.chunk_offsets=[],0===this.version)for(var i=0;i<t;i++)this.chunk_offsets.push(e.readUint32())})),h.createFullBoxCtor("stdp",(function(e){var t=(this.size-this.hdr_size)/2;this.priority=[];for(var i=0;i<t;i++)this.priority[i]=e.readUint16()})),h.createFullBoxCtor("sthd"),h.createFullBoxCtor("stri",(function(e){this.switch_group=e.readUint16(),this.alternate_group=e.readUint16(),this.sub_track_id=e.readUint32();var t=(this.size-this.hdr_size-8)/4;this.attribute_list=[];for(var i=0;i<t;i++)this.attribute_list[i]=e.readUint32()})),h.createFullBoxCtor("stsc",(function(e){var t,i;if(t=e.readUint32(),this.first_chunk=[],this.samples_per_chunk=[],this.sample_description_index=[],0===this.version)for(i=0;i<t;i++)this.first_chunk.push(e.readUint32()),this.samples_per_chunk.push(e.readUint32()),this.sample_description_index.push(e.readUint32())})),h.createFullBoxCtor("stsd",(function(e){var t,i,r,s;for(this.entries=[],r=e.readUint32(),t=1;t<=r;t++){if((i=h.parseOneBox(e,!0,this.size-(e.getPosition()-this.start))).code!==h.OK)return;h[i.type+"SampleEntry"]?((s=new h[i.type+"SampleEntry"](i.size)).hdr_size=i.hdr_size,s.start=i.start):(n.warn("BoxParser","Unknown sample entry type: "+i.type),s=new h.SampleEntry(i.type,i.size,i.hdr_size,i.start)),s.write===h.SampleEntry.prototype.write&&(n.info("BoxParser","SampleEntry "+s.type+" box writing not yet implemented, keeping unparsed data in memory for later write"),s.parseDataAndRewind(e)),s.parse(e),this.entries.push(s)}})),h.createFullBoxCtor("stsg",(function(e){this.grouping_type=e.readUint32();var t=e.readUint16();this.group_description_index=[];for(var i=0;i<t;i++)this.group_description_index[i]=e.readUint32()})),h.createFullBoxCtor("stsh",(function(e){var t,i;if(t=e.readUint32(),this.shadowed_sample_numbers=[],this.sync_sample_numbers=[],0===this.version)for(i=0;i<t;i++)this.shadowed_sample_numbers.push(e.readUint32()),this.sync_sample_numbers.push(e.readUint32())})),h.createFullBoxCtor("stss",(function(e){var t,i;if(i=e.readUint32(),0===this.version)for(this.sample_numbers=[],t=0;t<i;t++)this.sample_numbers.push(e.readUint32())})),h.createFullBoxCtor("stsz",(function(e){var t;if(this.sample_sizes=[],0===this.version)for(this.sample_size=e.readUint32(),this.sample_count=e.readUint32(),t=0;t<this.sample_count;t++)0===this.sample_size?this.sample_sizes.push(e.readUint32()):this.sample_sizes[t]=this.sample_size})),h.createFullBoxCtor("stts",(function(e){var t,i,r;if(t=e.readUint32(),this.sample_counts=[],this.sample_deltas=[],0===this.version)for(i=0;i<t;i++)this.sample_counts.push(e.readUint32()),(r=e.readInt32())<0&&(n.warn("BoxParser","File uses negative stts sample delta, using value 1 instead, sync may be lost!"),r=1),this.sample_deltas.push(r)})),h.createFullBoxCtor("stvi",(function(e){var t=e.readUint32();this.single_view_allowed=3&t,this.stereo_scheme=e.readUint32();var i,r,s=e.readUint32();for(this.stereo_indication_type=e.readString(s),this.boxes=[];e.getPosition()<this.start+this.size;){if((i=h.parseOneBox(e,!1,this.size-(e.getPosition()-this.start))).code!==h.OK)return;r=i.box,this.boxes.push(r),this[r.type]=r}})),h.createBoxCtor("styp",(function(e){h.ftypBox.prototype.parse.call(this,e)})),h.createFullBoxCtor("stz2",(function(e){var t,i;if(this.sample_sizes=[],0===this.version)if(this.reserved=e.readUint24(),this.field_size=e.readUint8(),i=e.readUint32(),4===this.field_size)for(t=0;t<i;t+=2){var r=e.readUint8();this.sample_sizes[t]=r>>4&15,this.sample_sizes[t+1]=15&r}else if(8===this.field_size)for(t=0;t<i;t++)this.sample_sizes[t]=e.readUint8();else if(16===this.field_size)for(t=0;t<i;t++)this.sample_sizes[t]=e.readUint16();else n.error("BoxParser","Error in length field in stz2 box")})),h.createFullBoxCtor("subs",(function(e){var t,i,r,s;for(r=e.readUint32(),this.entries=[],t=0;t<r;t++){var a={};if(this.entries[t]=a,a.sample_delta=e.readUint32(),a.subsamples=[],(s=e.readUint16())>0)for(i=0;i<s;i++){var n={};a.subsamples.push(n),1==this.version?n.size=e.readUint32():n.size=e.readUint16(),n.priority=e.readUint8(),n.discardable=e.readUint8(),n.codec_specific_parameters=e.readUint32()}}})),h.createFullBoxCtor("tenc",(function(e){if(e.readUint8(),0===this.version)e.readUint8();else{var t=e.readUint8();this.default_crypt_byte_block=t>>4&15,this.default_skip_byte_block=15&t}this.default_isProtected=e.readUint8(),this.default_Per_Sample_IV_Size=e.readUint8(),this.default_KID=h.parseHex16(e),1===this.default_isProtected&&0===this.default_Per_Sample_IV_Size&&(this.default_constant_IV_size=e.readUint8(),this.default_constant_IV=e.readUint8Array(this.default_constant_IV_size))})),h.createFullBoxCtor("tfdt",(function(e){1==this.version?this.baseMediaDecodeTime=e.readUint64():this.baseMediaDecodeTime=e.readUint32()})),h.createFullBoxCtor("tfhd",(function(e){var t=0;this.track_id=e.readUint32(),this.size-this.hdr_size>t&&this.flags&h.TFHD_FLAG_BASE_DATA_OFFSET?(this.base_data_offset=e.readUint64(),t+=8):this.base_data_offset=0,this.size-this.hdr_size>t&&this.flags&h.TFHD_FLAG_SAMPLE_DESC?(this.default_sample_description_index=e.readUint32(),t+=4):this.default_sample_description_index=0,this.size-this.hdr_size>t&&this.flags&h.TFHD_FLAG_SAMPLE_DUR?(this.default_sample_duration=e.readUint32(),t+=4):this.default_sample_duration=0,this.size-this.hdr_size>t&&this.flags&h.TFHD_FLAG_SAMPLE_SIZE?(this.default_sample_size=e.readUint32(),t+=4):this.default_sample_size=0,this.size-this.hdr_size>t&&this.flags&h.TFHD_FLAG_SAMPLE_FLAGS?(this.default_sample_flags=e.readUint32(),t+=4):this.default_sample_flags=0})),h.createFullBoxCtor("tfra",(function(e){this.track_ID=e.readUint32(),e.readUint24();var t=e.readUint8();this.length_size_of_traf_num=t>>4&3,this.length_size_of_trun_num=t>>2&3,this.length_size_of_sample_num=3&t,this.entries=[];for(var i=e.readUint32(),r=0;r<i;r++)1===this.version?(this.time=e.readUint64(),this.moof_offset=e.readUint64()):(this.time=e.readUint32(),this.moof_offset=e.readUint32()),this.traf_number=e["readUint"+8*(this.length_size_of_traf_num+1)](),this.trun_number=e["readUint"+8*(this.length_size_of_trun_num+1)](),this.sample_number=e["readUint"+8*(this.length_size_of_sample_num+1)]()})),h.createFullBoxCtor("tkhd",(function(e){1==this.version?(this.creation_time=e.readUint64(),this.modification_time=e.readUint64(),this.track_id=e.readUint32(),e.readUint32(),this.duration=e.readUint64()):(this.creation_time=e.readUint32(),this.modification_time=e.readUint32(),this.track_id=e.readUint32(),e.readUint32(),this.duration=e.readUint32()),e.readUint32Array(2),this.layer=e.readInt16(),this.alternate_group=e.readInt16(),this.volume=e.readInt16()>>8,e.readUint16(),this.matrix=e.readInt32Array(9),this.width=e.readUint32(),this.height=e.readUint32()})),h.createBoxCtor("tmax",(function(e){this.time=e.readUint32()})),h.createBoxCtor("tmin",(function(e){this.time=e.readUint32()})),h.createBoxCtor("totl",(function(e){this.bytessent=e.readUint32()})),h.createBoxCtor("tpay",(function(e){this.bytessent=e.readUint32()})),h.createBoxCtor("tpyl",(function(e){this.bytessent=e.readUint64()})),h.TrackGroupTypeBox.prototype.parse=function(e){this.parseFullHeader(e),this.track_group_id=e.readUint32()},h.createTrackGroupCtor("msrc"),h.TrackReferenceTypeBox=function(e,t,i,r){h.Box.call(this,e,t),this.hdr_size=i,this.start=r},h.TrackReferenceTypeBox.prototype=new h.Box,h.TrackReferenceTypeBox.prototype.parse=function(e){this.track_ids=e.readUint32Array((this.size-this.hdr_size)/4)},h.trefBox.prototype.parse=function(e){for(var t,i;e.getPosition()<this.start+this.size;){if((t=h.parseOneBox(e,!0,this.size-(e.getPosition()-this.start))).code!==h.OK)return;(i=new h.TrackReferenceTypeBox(t.type,t.size,t.hdr_size,t.start)).write===h.Box.prototype.write&&"mdat"!==i.type&&(n.info("BoxParser","TrackReference "+i.type+" box writing not yet implemented, keeping unparsed data in memory for later write"),i.parseDataAndRewind(e)),i.parse(e),this.boxes.push(i)}},h.createFullBoxCtor("trep",(function(e){for(this.track_ID=e.readUint32(),this.boxes=[];e.getPosition()<this.start+this.size;){if(ret=h.parseOneBox(e,!1,this.size-(e.getPosition()-this.start)),ret.code!==h.OK)return;box=ret.box,this.boxes.push(box)}})),h.createFullBoxCtor("trex",(function(e){this.track_id=e.readUint32(),this.default_sample_description_index=e.readUint32(),this.default_sample_duration=e.readUint32(),this.default_sample_size=e.readUint32(),this.default_sample_flags=e.readUint32()})),h.createBoxCtor("trpy",(function(e){this.bytessent=e.readUint64()})),h.createFullBoxCtor("trun",(function(e){var t=0;if(this.sample_count=e.readUint32(),t+=4,this.size-this.hdr_size>t&&this.flags&h.TRUN_FLAGS_DATA_OFFSET?(this.data_offset=e.readInt32(),t+=4):this.data_offset=0,this.size-this.hdr_size>t&&this.flags&h.TRUN_FLAGS_FIRST_FLAG?(this.first_sample_flags=e.readUint32(),t+=4):this.first_sample_flags=0,this.sample_duration=[],this.sample_size=[],this.sample_flags=[],this.sample_composition_time_offset=[],this.size-this.hdr_size>t)for(var i=0;i<this.sample_count;i++)this.flags&h.TRUN_FLAGS_DURATION&&(this.sample_duration[i]=e.readUint32()),this.flags&h.TRUN_FLAGS_SIZE&&(this.sample_size[i]=e.readUint32()),this.flags&h.TRUN_FLAGS_FLAGS&&(this.sample_flags[i]=e.readUint32()),this.flags&h.TRUN_FLAGS_CTS_OFFSET&&(0===this.version?this.sample_composition_time_offset[i]=e.readUint32():this.sample_composition_time_offset[i]=e.readInt32())})),h.createFullBoxCtor("tsel",(function(e){this.switch_group=e.readUint32();var t=(this.size-this.hdr_size-4)/4;this.attribute_list=[];for(var i=0;i<t;i++)this.attribute_list[i]=e.readUint32()})),h.createFullBoxCtor("txtC",(function(e){this.config=e.readCString()})),h.createBoxCtor("tyco",(function(e){var t=(this.size-this.hdr_size)/4;this.compatible_brands=[];for(var i=0;i<t;i++)this.compatible_brands[i]=e.readString(4)})),h.createFullBoxCtor("udes",(function(e){this.lang=e.readCString(),this.name=e.readCString(),this.description=e.readCString(),this.tags=e.readCString()})),h.createFullBoxCtor("uncC",(function(e){var t;if(this.profile=e.readUint32(),1==this.version);else if(0==this.version){for(this.component_count=e.readUint32(),this.component_index=[],this.component_bit_depth_minus_one=[],this.component_format=[],this.component_align_size=[],t=0;t<this.component_count;t++)this.component_index.push(e.readUint16()),this.component_bit_depth_minus_one.push(e.readUint8()),this.component_format.push(e.readUint8()),this.component_align_size.push(e.readUint8());this.sampling_type=e.readUint8(),this.interleave_type=e.readUint8(),this.block_size=e.readUint8();var i=e.readUint8();this.component_little_endian=i>>7&1,this.block_pad_lsb=i>>6&1,this.block_little_endian=i>>5&1,this.block_reversed=i>>4&1,this.pad_unknown=i>>3&1,this.pixel_size=e.readUint32(),this.row_align_size=e.readUint32(),this.tile_align_size=e.readUint32(),this.num_tile_cols_minus_one=e.readUint32(),this.num_tile_rows_minus_one=e.readUint32()}})),h.createFullBoxCtor("url ",(function(e){1!==this.flags&&(this.location=e.readCString())})),h.createFullBoxCtor("urn ",(function(e){this.name=e.readCString(),this.size-this.hdr_size-this.name.length-1>0&&(this.location=e.readCString())})),h.createUUIDBox("********************************",!0,!1,(function(e){this.LiveServerManifest=e.readString(this.size-this.hdr_size).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")})),h.createUUIDBox("********************************",!0,!1,(function(e){this.system_id=h.parseHex16(e);var t=e.readUint32();t>0&&(this.data=e.readUint8Array(t))})),h.createUUIDBox("********************************",!0,!1),h.createUUIDBox("********************************",!0,!1,(function(e){this.default_AlgorithmID=e.readUint24(),this.default_IV_size=e.readUint8(),this.default_KID=h.parseHex16(e)})),h.createUUIDBox("********************************",!0,!1,(function(e){this.fragment_count=e.readUint8(),this.entries=[];for(var t=0;t<this.fragment_count;t++){var i={},r=0,s=0;1===this.version?(r=e.readUint64(),s=e.readUint64()):(r=e.readUint32(),s=e.readUint32()),i.absolute_time=r,i.absolute_duration=s,this.entries.push(i)}})),h.createUUIDBox("********************************",!0,!1,(function(e){1===this.version?(this.absolute_time=e.readUint64(),this.duration=e.readUint64()):(this.absolute_time=e.readUint32(),this.duration=e.readUint32())})),h.createFullBoxCtor("vmhd",(function(e){this.graphicsmode=e.readUint16(),this.opcolor=e.readUint16Array(3)})),h.createFullBoxCtor("vpcC",(function(e){var t;1===this.version?(this.profile=e.readUint8(),this.level=e.readUint8(),t=e.readUint8(),this.bitDepth=t>>4,this.chromaSubsampling=t>>1&7,this.videoFullRangeFlag=1&t,this.colourPrimaries=e.readUint8(),this.transferCharacteristics=e.readUint8(),this.matrixCoefficients=e.readUint8(),this.codecIntializationDataSize=e.readUint16(),this.codecIntializationData=e.readUint8Array(this.codecIntializationDataSize)):(this.profile=e.readUint8(),this.level=e.readUint8(),t=e.readUint8(),this.bitDepth=t>>4&15,this.colorSpace=15&t,t=e.readUint8(),this.chromaSubsampling=t>>4&15,this.transferFunction=t>>1&7,this.videoFullRangeFlag=1&t,this.codecIntializationDataSize=e.readUint16(),this.codecIntializationData=e.readUint8Array(this.codecIntializationDataSize))})),h.createBoxCtor("vttC",(function(e){this.text=e.readString(this.size-this.hdr_size)})),h.createFullBoxCtor("vvcC",(function(e){var t,i,r={held_bits:void 0,num_held_bits:0,stream_read_1_bytes:function(e){this.held_bits=e.readUint8(),this.num_held_bits=8},stream_read_2_bytes:function(e){this.held_bits=e.readUint16(),this.num_held_bits=16},extract_bits:function(e){var t=this.held_bits>>this.num_held_bits-e&(1<<e)-1;return this.num_held_bits-=e,t}};if(r.stream_read_1_bytes(e),r.extract_bits(5),this.lengthSizeMinusOne=r.extract_bits(2),this.ptl_present_flag=r.extract_bits(1),this.ptl_present_flag){if(r.stream_read_2_bytes(e),this.ols_idx=r.extract_bits(9),this.num_sublayers=r.extract_bits(3),this.constant_frame_rate=r.extract_bits(2),this.chroma_format_idc=r.extract_bits(2),r.stream_read_1_bytes(e),this.bit_depth_minus8=r.extract_bits(3),r.extract_bits(5),r.stream_read_2_bytes(e),r.extract_bits(2),this.num_bytes_constraint_info=r.extract_bits(6),this.general_profile_idc=r.extract_bits(7),this.general_tier_flag=r.extract_bits(1),this.general_level_idc=e.readUint8(),r.stream_read_1_bytes(e),this.ptl_frame_only_constraint_flag=r.extract_bits(1),this.ptl_multilayer_enabled_flag=r.extract_bits(1),this.general_constraint_info=new Uint8Array(this.num_bytes_constraint_info),this.num_bytes_constraint_info){for(t=0;t<this.num_bytes_constraint_info-1;t++){var s=r.extract_bits(6);r.stream_read_1_bytes(e);var a=r.extract_bits(2);this.general_constraint_info[t]=s<<2|a}this.general_constraint_info[this.num_bytes_constraint_info-1]=r.extract_bits(6)}else r.extract_bits(6);if(this.num_sublayers>1){for(r.stream_read_1_bytes(e),this.ptl_sublayer_present_mask=0,i=this.num_sublayers-2;i>=0;--i){var n=r.extract_bits(1);this.ptl_sublayer_present_mask|=n<<i}for(i=this.num_sublayers;i<=8&&this.num_sublayers>1;++i)r.extract_bits(1);for(this.sublayer_level_idc=[],i=this.num_sublayers-2;i>=0;--i)this.ptl_sublayer_present_mask&1<<i&&(this.sublayer_level_idc[i]=e.readUint8())}if(this.ptl_num_sub_profiles=e.readUint8(),this.general_sub_profile_idc=[],this.ptl_num_sub_profiles)for(t=0;t<this.ptl_num_sub_profiles;t++)this.general_sub_profile_idc.push(e.readUint32());this.max_picture_width=e.readUint16(),this.max_picture_height=e.readUint16(),this.avg_frame_rate=e.readUint16()}this.nalu_arrays=[];var o=e.readUint8();for(t=0;t<o;t++){var d=[];this.nalu_arrays.push(d),r.stream_read_1_bytes(e),d.completeness=r.extract_bits(1),r.extract_bits(2),d.nalu_type=r.extract_bits(5);var l=1;for(13!=d.nalu_type&&12!=d.nalu_type&&(l=e.readUint16()),i=0;i<l;i++){var c=e.readUint16();d.push({data:e.readUint8Array(c),length:c})}}})),h.createFullBoxCtor("vvnC",(function(e){var t=strm.readUint8();this.lengthSizeMinusOne=3&t})),h.SampleEntry.prototype.isVideo=function(){return!1},h.SampleEntry.prototype.isAudio=function(){return!1},h.SampleEntry.prototype.isSubtitle=function(){return!1},h.SampleEntry.prototype.isMetadata=function(){return!1},h.SampleEntry.prototype.isHint=function(){return!1},h.SampleEntry.prototype.getCodec=function(){return this.type.replace(".","")},h.SampleEntry.prototype.getWidth=function(){return""},h.SampleEntry.prototype.getHeight=function(){return""},h.SampleEntry.prototype.getChannelCount=function(){return""},h.SampleEntry.prototype.getSampleRate=function(){return""},h.SampleEntry.prototype.getSampleSize=function(){return""},h.VisualSampleEntry.prototype.isVideo=function(){return!0},h.VisualSampleEntry.prototype.getWidth=function(){return this.width},h.VisualSampleEntry.prototype.getHeight=function(){return this.height},h.AudioSampleEntry.prototype.isAudio=function(){return!0},h.AudioSampleEntry.prototype.getChannelCount=function(){return this.channel_count},h.AudioSampleEntry.prototype.getSampleRate=function(){return this.samplerate},h.AudioSampleEntry.prototype.getSampleSize=function(){return this.samplesize},h.SubtitleSampleEntry.prototype.isSubtitle=function(){return!0},h.MetadataSampleEntry.prototype.isMetadata=function(){return!0},h.decimalToHex=function(e,t){var i=Number(e).toString(16);for(t=null==t?t=2:t;i.length<t;)i="0"+i;return i},h.avc1SampleEntry.prototype.getCodec=h.avc2SampleEntry.prototype.getCodec=h.avc3SampleEntry.prototype.getCodec=h.avc4SampleEntry.prototype.getCodec=function(){var e=h.SampleEntry.prototype.getCodec.call(this);return this.avcC?e+"."+h.decimalToHex(this.avcC.AVCProfileIndication)+h.decimalToHex(this.avcC.profile_compatibility)+h.decimalToHex(this.avcC.AVCLevelIndication):e},h.hev1SampleEntry.prototype.getCodec=h.hvc1SampleEntry.prototype.getCodec=function(){var e,t=h.SampleEntry.prototype.getCodec.call(this);if(this.hvcC){switch(t+=".",this.hvcC.general_profile_space){case 0:t+="";break;case 1:t+="A";break;case 2:t+="B";break;case 3:t+="C"}t+=this.hvcC.general_profile_idc,t+=".";var i=this.hvcC.general_profile_compatibility,r=0;for(e=0;e<32&&(r|=1&i,31!=e);e++)r<<=1,i>>=1;t+=h.decimalToHex(r,0),t+=".",0===this.hvcC.general_tier_flag?t+="L":t+="H",t+=this.hvcC.general_level_idc;var s=!1,a="";for(e=5;e>=0;e--)(this.hvcC.general_constraint_indicator[e]||s)&&(a="."+h.decimalToHex(this.hvcC.general_constraint_indicator[e],0)+a,s=!0);t+=a}return t},h.vvc1SampleEntry.prototype.getCodec=h.vvi1SampleEntry.prototype.getCodec=function(){var e,t=h.SampleEntry.prototype.getCodec.call(this);if(this.vvcC){t+="."+this.vvcC.general_profile_idc,this.vvcC.general_tier_flag?t+=".H":t+=".L",t+=this.vvcC.general_level_idc;var i="";if(this.vvcC.general_constraint_info){var r,s=[],a=0;for(a|=this.vvcC.ptl_frame_only_constraint<<7,a|=this.vvcC.ptl_multilayer_enabled<<6,e=0;e<this.vvcC.general_constraint_info.length;++e)a|=this.vvcC.general_constraint_info[e]>>2&63,s.push(a),a&&(r=e),a=this.vvcC.general_constraint_info[e]>>2&3;if(void 0===r)i=".CA";else{i=".C";var n="ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",o=0,d=0;for(e=0;e<=r;++e)for(o=o<<8|s[e],d+=8;d>=5;){i+=n[o>>d-5&31],o&=(1<<(d-=5))-1}d&&(i+=n[31&(o<<=5-d)])}}t+=i}return t},h.mp4aSampleEntry.prototype.getCodec=function(){var e=h.SampleEntry.prototype.getCodec.call(this);if(this.esds&&this.esds.esd){var t=this.esds.esd.getOTI(),i=this.esds.esd.getAudioConfig();return e+"."+h.decimalToHex(t)+(i?"."+i:"")}return e},h.stxtSampleEntry.prototype.getCodec=function(){var e=h.SampleEntry.prototype.getCodec.call(this);return this.mime_format?e+"."+this.mime_format:e},h.vp08SampleEntry.prototype.getCodec=h.vp09SampleEntry.prototype.getCodec=function(){var e=h.SampleEntry.prototype.getCodec.call(this),t=this.vpcC.level;0==t&&(t="00");var i=this.vpcC.bitDepth;return 8==i&&(i="08"),e+".0"+this.vpcC.profile+"."+t+"."+i},h.av01SampleEntry.prototype.getCodec=function(){var e,t=h.SampleEntry.prototype.getCodec.call(this),i=this.av1C.seq_level_idx_0;return i<10&&(i="0"+i),2===this.av1C.seq_profile&&1===this.av1C.high_bitdepth?e=1===this.av1C.twelve_bit?"12":"10":this.av1C.seq_profile<=2&&(e=1===this.av1C.high_bitdepth?"10":"08"),t+"."+this.av1C.seq_profile+"."+i+(this.av1C.seq_tier_0?"H":"M")+"."+e},h.Box.prototype.writeHeader=function(e,t){this.size+=8,this.size>l&&(this.size+=8),"uuid"===this.type&&(this.size+=16),n.debug("BoxWriter","Writing box "+this.type+" of size: "+this.size+" at position "+e.getPosition()+(t||"")),this.size>l?e.writeUint32(1):(this.sizePosition=e.getPosition(),e.writeUint32(this.size)),e.writeString(this.type,null,4),"uuid"===this.type&&e.writeUint8Array(this.uuid),this.size>l&&e.writeUint64(this.size)},h.FullBox.prototype.writeHeader=function(e){this.size+=4,h.Box.prototype.writeHeader.call(this,e," v="+this.version+" f="+this.flags),e.writeUint8(this.version),e.writeUint24(this.flags)},h.Box.prototype.write=function(e){"mdat"===this.type?this.data&&(this.size=this.data.length,this.writeHeader(e),e.writeUint8Array(this.data)):(this.size=this.data?this.data.length:0,this.writeHeader(e),this.data&&e.writeUint8Array(this.data))},h.ContainerBox.prototype.write=function(e){this.size=0,this.writeHeader(e);for(var t=0;t<this.boxes.length;t++)this.boxes[t]&&(this.boxes[t].write(e),this.size+=this.boxes[t].size);n.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},h.TrackReferenceTypeBox.prototype.write=function(e){this.size=4*this.track_ids.length,this.writeHeader(e),e.writeUint32Array(this.track_ids)},h.avcCBox.prototype.write=function(e){var t;for(this.size=7,t=0;t<this.SPS.length;t++)this.size+=2+this.SPS[t].length;for(t=0;t<this.PPS.length;t++)this.size+=2+this.PPS[t].length;for(this.ext&&(this.size+=this.ext.length),this.writeHeader(e),e.writeUint8(this.configurationVersion),e.writeUint8(this.AVCProfileIndication),e.writeUint8(this.profile_compatibility),e.writeUint8(this.AVCLevelIndication),e.writeUint8(this.lengthSizeMinusOne+252),e.writeUint8(this.SPS.length+224),t=0;t<this.SPS.length;t++)e.writeUint16(this.SPS[t].length),e.writeUint8Array(this.SPS[t].nalu);for(e.writeUint8(this.PPS.length),t=0;t<this.PPS.length;t++)e.writeUint16(this.PPS[t].length),e.writeUint8Array(this.PPS[t].nalu);this.ext&&e.writeUint8Array(this.ext)},h.co64Box.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.chunk_offsets.length,this.writeHeader(e),e.writeUint32(this.chunk_offsets.length),t=0;t<this.chunk_offsets.length;t++)e.writeUint64(this.chunk_offsets[t])},h.cslgBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=20,this.writeHeader(e),e.writeInt32(this.compositionToDTSShift),e.writeInt32(this.leastDecodeToDisplayDelta),e.writeInt32(this.greatestDecodeToDisplayDelta),e.writeInt32(this.compositionStartTime),e.writeInt32(this.compositionEndTime)},h.cttsBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.sample_counts.length,this.writeHeader(e),e.writeUint32(this.sample_counts.length),t=0;t<this.sample_counts.length;t++)e.writeUint32(this.sample_counts[t]),1===this.version?e.writeInt32(this.sample_offsets[t]):e.writeUint32(this.sample_offsets[t])},h.drefBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4,this.writeHeader(e),e.writeUint32(this.entries.length);for(var t=0;t<this.entries.length;t++)this.entries[t].write(e),this.size+=this.entries[t].size;n.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},h.elngBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=this.extended_language.length,this.writeHeader(e),e.writeString(this.extended_language)},h.elstBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4+12*this.entries.length,this.writeHeader(e),e.writeUint32(this.entries.length);for(var t=0;t<this.entries.length;t++){var i=this.entries[t];e.writeUint32(i.segment_duration),e.writeInt32(i.media_time),e.writeInt16(i.media_rate_integer),e.writeInt16(i.media_rate_fraction)}},h.emsgBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=16+this.message_data.length+(this.scheme_id_uri.length+1)+(this.value.length+1),this.writeHeader(e),e.writeCString(this.scheme_id_uri),e.writeCString(this.value),e.writeUint32(this.timescale),e.writeUint32(this.presentation_time_delta),e.writeUint32(this.event_duration),e.writeUint32(this.id),e.writeUint8Array(this.message_data)},h.ftypBox.prototype.write=function(e){this.size=8+4*this.compatible_brands.length,this.writeHeader(e),e.writeString(this.major_brand,null,4),e.writeUint32(this.minor_version);for(var t=0;t<this.compatible_brands.length;t++)e.writeString(this.compatible_brands[t],null,4)},h.hdlrBox.prototype.write=function(e){this.size=20+this.name.length+1,this.version=0,this.flags=0,this.writeHeader(e),e.writeUint32(0),e.writeString(this.handler,null,4),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeCString(this.name)},h.hvcCBox.prototype.write=function(e){var t,i;for(this.size=23,t=0;t<this.nalu_arrays.length;t++)for(this.size+=3,i=0;i<this.nalu_arrays[t].length;i++)this.size+=2+this.nalu_arrays[t][i].data.length;for(this.writeHeader(e),e.writeUint8(this.configurationVersion),e.writeUint8(this.general_profile_space<<6+this.general_tier_flag<<5+this.general_profile_idc),e.writeUint32(this.general_profile_compatibility),e.writeUint8Array(this.general_constraint_indicator),e.writeUint8(this.general_level_idc),e.writeUint16(this.min_spatial_segmentation_idc+(15<<24)),e.writeUint8(this.parallelismType+252),e.writeUint8(this.chroma_format_idc+252),e.writeUint8(this.bit_depth_luma_minus8+248),e.writeUint8(this.bit_depth_chroma_minus8+248),e.writeUint16(this.avgFrameRate),e.writeUint8((this.constantFrameRate<<6)+(this.numTemporalLayers<<3)+(this.temporalIdNested<<2)+this.lengthSizeMinusOne),e.writeUint8(this.nalu_arrays.length),t=0;t<this.nalu_arrays.length;t++)for(e.writeUint8((this.nalu_arrays[t].completeness<<7)+this.nalu_arrays[t].nalu_type),e.writeUint16(this.nalu_arrays[t].length),i=0;i<this.nalu_arrays[t].length;i++)e.writeUint16(this.nalu_arrays[t][i].data.length),e.writeUint8Array(this.nalu_arrays[t][i].data)},h.kindBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=this.schemeURI.length+1+(this.value.length+1),this.writeHeader(e),e.writeCString(this.schemeURI),e.writeCString(this.value)},h.mdhdBox.prototype.write=function(e){this.size=20,this.flags=0,this.version=0,this.writeHeader(e),e.writeUint32(this.creation_time),e.writeUint32(this.modification_time),e.writeUint32(this.timescale),e.writeUint32(this.duration),e.writeUint16(this.language),e.writeUint16(0)},h.mehdBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4,this.writeHeader(e),e.writeUint32(this.fragment_duration)},h.mfhdBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4,this.writeHeader(e),e.writeUint32(this.sequence_number)},h.mvhdBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=96,this.writeHeader(e),e.writeUint32(this.creation_time),e.writeUint32(this.modification_time),e.writeUint32(this.timescale),e.writeUint32(this.duration),e.writeUint32(this.rate),e.writeUint16(this.volume<<8),e.writeUint16(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32Array(this.matrix),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(this.next_track_id)},h.SampleEntry.prototype.writeHeader=function(e){this.size=8,h.Box.prototype.writeHeader.call(this,e),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint16(this.data_reference_index)},h.SampleEntry.prototype.writeFooter=function(e){for(var t=0;t<this.boxes.length;t++)this.boxes[t].write(e),this.size+=this.boxes[t].size;n.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},h.SampleEntry.prototype.write=function(e){this.writeHeader(e),e.writeUint8Array(this.data),this.size+=this.data.length,n.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},h.VisualSampleEntry.prototype.write=function(e){this.writeHeader(e),this.size+=70,e.writeUint16(0),e.writeUint16(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint16(this.width),e.writeUint16(this.height),e.writeUint32(this.horizresolution),e.writeUint32(this.vertresolution),e.writeUint32(0),e.writeUint16(this.frame_count),e.writeUint8(Math.min(31,this.compressorname.length)),e.writeString(this.compressorname,null,31),e.writeUint16(this.depth),e.writeInt16(-1),this.writeFooter(e)},h.AudioSampleEntry.prototype.write=function(e){this.writeHeader(e),this.size+=20,e.writeUint32(0),e.writeUint32(0),e.writeUint16(this.channel_count),e.writeUint16(this.samplesize),e.writeUint16(0),e.writeUint16(0),e.writeUint32(this.samplerate<<16),this.writeFooter(e)},h.stppSampleEntry.prototype.write=function(e){this.writeHeader(e),this.size+=this.namespace.length+1+this.schema_location.length+1+this.auxiliary_mime_types.length+1,e.writeCString(this.namespace),e.writeCString(this.schema_location),e.writeCString(this.auxiliary_mime_types),this.writeFooter(e)},h.SampleGroupEntry.prototype.write=function(e){e.writeUint8Array(this.data)},h.sbgpBox.prototype.write=function(e){this.version=1,this.flags=0,this.size=12+8*this.entries.length,this.writeHeader(e),e.writeString(this.grouping_type,null,4),e.writeUint32(this.grouping_type_parameter),e.writeUint32(this.entries.length);for(var t=0;t<this.entries.length;t++){var i=this.entries[t];e.writeInt32(i.sample_count),e.writeInt32(i.group_description_index)}},h.sgpdBox.prototype.write=function(e){var t,i;for(this.flags=0,this.size=12,t=0;t<this.entries.length;t++)i=this.entries[t],1===this.version&&(0===this.default_length&&(this.size+=4),this.size+=i.data.length);for(this.writeHeader(e),e.writeString(this.grouping_type,null,4),1===this.version&&e.writeUint32(this.default_length),this.version>=2&&e.writeUint32(this.default_sample_description_index),e.writeUint32(this.entries.length),t=0;t<this.entries.length;t++)i=this.entries[t],1===this.version&&0===this.default_length&&e.writeUint32(i.description_length),i.write(e)},h.sidxBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=20+12*this.references.length,this.writeHeader(e),e.writeUint32(this.reference_ID),e.writeUint32(this.timescale),e.writeUint32(this.earliest_presentation_time),e.writeUint32(this.first_offset),e.writeUint16(0),e.writeUint16(this.references.length);for(var t=0;t<this.references.length;t++){var i=this.references[t];e.writeUint32(i.reference_type<<31|i.referenced_size),e.writeUint32(i.subsegment_duration),e.writeUint32(i.starts_with_SAP<<31|i.SAP_type<<28|i.SAP_delta_time)}},h.smhdBox.prototype.write=function(e){this.version=0,this.flags=1,this.size=4,this.writeHeader(e),e.writeUint16(this.balance),e.writeUint16(0)},h.stcoBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4+4*this.chunk_offsets.length,this.writeHeader(e),e.writeUint32(this.chunk_offsets.length),e.writeUint32Array(this.chunk_offsets)},h.stscBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+12*this.first_chunk.length,this.writeHeader(e),e.writeUint32(this.first_chunk.length),t=0;t<this.first_chunk.length;t++)e.writeUint32(this.first_chunk[t]),e.writeUint32(this.samples_per_chunk[t]),e.writeUint32(this.sample_description_index[t])},h.stsdBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=0,this.writeHeader(e),e.writeUint32(this.entries.length),this.size+=4,t=0;t<this.entries.length;t++)this.entries[t].write(e),this.size+=this.entries[t].size;n.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},h.stshBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.shadowed_sample_numbers.length,this.writeHeader(e),e.writeUint32(this.shadowed_sample_numbers.length),t=0;t<this.shadowed_sample_numbers.length;t++)e.writeUint32(this.shadowed_sample_numbers[t]),e.writeUint32(this.sync_sample_numbers[t])},h.stssBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4+4*this.sample_numbers.length,this.writeHeader(e),e.writeUint32(this.sample_numbers.length),e.writeUint32Array(this.sample_numbers)},h.stszBox.prototype.write=function(e){var t,i=!0;if(this.version=0,this.flags=0,this.sample_sizes.length>0)for(t=0;t+1<this.sample_sizes.length;){if(this.sample_sizes[t+1]!==this.sample_sizes[0]){i=!1;break}t++}else i=!1;this.size=8,i||(this.size+=4*this.sample_sizes.length),this.writeHeader(e),i?e.writeUint32(this.sample_sizes[0]):e.writeUint32(0),e.writeUint32(this.sample_sizes.length),i||e.writeUint32Array(this.sample_sizes)},h.sttsBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.sample_counts.length,this.writeHeader(e),e.writeUint32(this.sample_counts.length),t=0;t<this.sample_counts.length;t++)e.writeUint32(this.sample_counts[t]),e.writeUint32(this.sample_deltas[t])},h.tfdtBox.prototype.write=function(e){var t=Math.pow(2,32)-1;this.version=this.baseMediaDecodeTime>t?1:0,this.flags=0,this.size=4,1===this.version&&(this.size+=4),this.writeHeader(e),1===this.version?e.writeUint64(this.baseMediaDecodeTime):e.writeUint32(this.baseMediaDecodeTime)},h.tfhdBox.prototype.write=function(e){this.version=0,this.size=4,this.flags&h.TFHD_FLAG_BASE_DATA_OFFSET&&(this.size+=8),this.flags&h.TFHD_FLAG_SAMPLE_DESC&&(this.size+=4),this.flags&h.TFHD_FLAG_SAMPLE_DUR&&(this.size+=4),this.flags&h.TFHD_FLAG_SAMPLE_SIZE&&(this.size+=4),this.flags&h.TFHD_FLAG_SAMPLE_FLAGS&&(this.size+=4),this.writeHeader(e),e.writeUint32(this.track_id),this.flags&h.TFHD_FLAG_BASE_DATA_OFFSET&&e.writeUint64(this.base_data_offset),this.flags&h.TFHD_FLAG_SAMPLE_DESC&&e.writeUint32(this.default_sample_description_index),this.flags&h.TFHD_FLAG_SAMPLE_DUR&&e.writeUint32(this.default_sample_duration),this.flags&h.TFHD_FLAG_SAMPLE_SIZE&&e.writeUint32(this.default_sample_size),this.flags&h.TFHD_FLAG_SAMPLE_FLAGS&&e.writeUint32(this.default_sample_flags)},h.tkhdBox.prototype.write=function(e){this.version=0,this.size=80,this.writeHeader(e),e.writeUint32(this.creation_time),e.writeUint32(this.modification_time),e.writeUint32(this.track_id),e.writeUint32(0),e.writeUint32(this.duration),e.writeUint32(0),e.writeUint32(0),e.writeInt16(this.layer),e.writeInt16(this.alternate_group),e.writeInt16(this.volume<<8),e.writeUint16(0),e.writeInt32Array(this.matrix),e.writeUint32(this.width),e.writeUint32(this.height)},h.trexBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=20,this.writeHeader(e),e.writeUint32(this.track_id),e.writeUint32(this.default_sample_description_index),e.writeUint32(this.default_sample_duration),e.writeUint32(this.default_sample_size),e.writeUint32(this.default_sample_flags)},h.trunBox.prototype.write=function(e){this.version=0,this.size=4,this.flags&h.TRUN_FLAGS_DATA_OFFSET&&(this.size+=4),this.flags&h.TRUN_FLAGS_FIRST_FLAG&&(this.size+=4),this.flags&h.TRUN_FLAGS_DURATION&&(this.size+=4*this.sample_duration.length),this.flags&h.TRUN_FLAGS_SIZE&&(this.size+=4*this.sample_size.length),this.flags&h.TRUN_FLAGS_FLAGS&&(this.size+=4*this.sample_flags.length),this.flags&h.TRUN_FLAGS_CTS_OFFSET&&(this.size+=4*this.sample_composition_time_offset.length),this.writeHeader(e),e.writeUint32(this.sample_count),this.flags&h.TRUN_FLAGS_DATA_OFFSET&&(this.data_offset_position=e.getPosition(),e.writeInt32(this.data_offset)),this.flags&h.TRUN_FLAGS_FIRST_FLAG&&e.writeUint32(this.first_sample_flags);for(var t=0;t<this.sample_count;t++)this.flags&h.TRUN_FLAGS_DURATION&&e.writeUint32(this.sample_duration[t]),this.flags&h.TRUN_FLAGS_SIZE&&e.writeUint32(this.sample_size[t]),this.flags&h.TRUN_FLAGS_FLAGS&&e.writeUint32(this.sample_flags[t]),this.flags&h.TRUN_FLAGS_CTS_OFFSET&&(0===this.version?e.writeUint32(this.sample_composition_time_offset[t]):e.writeInt32(this.sample_composition_time_offset[t]))},h["url Box"].prototype.write=function(e){this.version=0,this.location?(this.flags=0,this.size=this.location.length+1):(this.flags=1,this.size=0),this.writeHeader(e),this.location&&e.writeCString(this.location)},h["urn Box"].prototype.write=function(e){this.version=0,this.flags=0,this.size=this.name.length+1+(this.location?this.location.length+1:0),this.writeHeader(e),e.writeCString(this.name),this.location&&e.writeCString(this.location)},h.vmhdBox.prototype.write=function(e){this.version=0,this.flags=1,this.size=8,this.writeHeader(e),e.writeUint16(this.graphicsmode),e.writeUint16Array(this.opcolor)},h.cttsBox.prototype.unpack=function(e){var t,i,r;for(r=0,t=0;t<this.sample_counts.length;t++)for(i=0;i<this.sample_counts[t];i++)e[r].pts=e[r].dts+this.sample_offsets[t],r++},h.sttsBox.prototype.unpack=function(e){var t,i,r;for(r=0,t=0;t<this.sample_counts.length;t++)for(i=0;i<this.sample_counts[t];i++)e[r].dts=0===r?0:e[r-1].dts+this.sample_deltas[t],r++},h.stcoBox.prototype.unpack=function(e){var t;for(t=0;t<this.chunk_offsets.length;t++)e[t].offset=this.chunk_offsets[t]},h.stscBox.prototype.unpack=function(e){var t,i,r,s,a;for(s=0,a=0,t=0;t<this.first_chunk.length;t++)for(i=0;i<(t+1<this.first_chunk.length?this.first_chunk[t+1]:1/0);i++)for(a++,r=0;r<this.samples_per_chunk[t];r++){if(!e[s])return;e[s].description_index=this.sample_description_index[t],e[s].chunk_index=a,s++}},h.stszBox.prototype.unpack=function(e){var t;for(t=0;t<this.sample_sizes.length;t++)e[t].size=this.sample_sizes[t]},h.DIFF_BOXES_PROP_NAMES=["boxes","entries","references","subsamples","items","item_infos","extents","associations","subsegments","ranges","seekLists","seekPoints","esd","levels"],h.DIFF_PRIMITIVE_ARRAY_PROP_NAMES=["compatible_brands","matrix","opcolor","sample_counts","sample_counts","sample_deltas","first_chunk","samples_per_chunk","sample_sizes","chunk_offsets","sample_offsets","sample_description_index","sample_duration"],h.boxEqualFields=function(e,t){if(e&&!t)return!1;var i;for(i in e)if(!(h.DIFF_BOXES_PROP_NAMES.indexOf(i)>-1||e[i]instanceof h.Box||t[i]instanceof h.Box||void 0===e[i]||void 0===t[i]||"function"==typeof e[i]||"function"==typeof t[i]||e.subBoxNames&&e.subBoxNames.indexOf(i.slice(0,4))>-1||t.subBoxNames&&t.subBoxNames.indexOf(i.slice(0,4))>-1||"data"===i||"start"===i||"size"===i||"creation_time"===i||"modification_time"===i||h.DIFF_PRIMITIVE_ARRAY_PROP_NAMES.indexOf(i)>-1||e[i]===t[i]))return!1;return!0},h.boxEqual=function(e,t){if(!h.boxEqualFields(e,t))return!1;for(var i=0;i<h.DIFF_BOXES_PROP_NAMES.length;i++){var r=h.DIFF_BOXES_PROP_NAMES[i];if(e[r]&&t[r]&&!h.boxEqual(e[r],t[r]))return!1}return!0};var _=function(){};_.prototype.parseSample=function(e){var t,i={};i.resources=[];var r=new o(e.data.buffer);if(e.subsamples&&0!==e.subsamples.length){if(i.documentString=r.readString(e.subsamples[0].size),e.subsamples.length>1)for(t=1;t<e.subsamples.length;t++)i.resources[t]=r.readUint8Array(e.subsamples[t].size)}else i.documentString=r.readString(e.data.length);return"undefined"!=typeof DOMParser&&(i.document=(new DOMParser).parseFromString(i.documentString,"application/xml")),i};var m=function(){};m.prototype.parseSample=function(e){return new o(e.data.buffer).readString(e.data.length)},m.prototype.parseConfig=function(e){var t=new o(e.buffer);return t.readUint32(),t.readCString()},t.XMLSubtitlein4Parser=_,t.Textin4Parser=m;var g=function(e){this.stream=e||new c,this.boxes=[],this.mdats=[],this.moofs=[],this.isProgressive=!1,this.moovStartFound=!1,this.onMoovStart=null,this.moovStartSent=!1,this.onReady=null,this.readySent=!1,this.onSegment=null,this.onSamples=null,this.onError=null,this.sampleListBuilt=!1,this.fragmentedTracks=[],this.extractedTracks=[],this.isFragmentationInitialized=!1,this.sampleProcessingStarted=!1,this.nextMoofNumber=0,this.itemListBuilt=!1,this.onSidx=null,this.sidxSent=!1};g.prototype.destroy=function(){this.stream&&(this.stream.destroy(),this.stream=null),this.boxes=[],this.mdats=[],this.moofs=[],this.isProgressive=!1,this.moovStartFound=!1,this.onMoovStart=null,this.moovStartSent=!1,this.onReady=null,this.readySent=!1,this.onSegment=null,this.onSamples=null,this.onError=null,this.sampleListBuilt=!1,this.fragmentedTracks=[],this.extractedTracks=[],this.isFragmentationInitialized=!1,this.sampleProcessingStarted=!1,this.nextMoofNumber=0,this.itemListBuilt=!1,this.onSidx=null,this.sidxSent=!1},g.prototype.setSegmentOptions=function(e,t,i){var r=this.getTrackById(e);if(r){var s={};this.fragmentedTracks.push(s),s.id=e,s.user=t,s.trak=r,r.nextSample=0,s.segmentStream=null,s.nb_samples=1e3,s.rapAlignement=!0,i&&(i.nbSamples&&(s.nb_samples=i.nbSamples),i.rapAlignement&&(s.rapAlignement=i.rapAlignement))}},g.prototype.unsetSegmentOptions=function(e){for(var t=-1,i=0;i<this.fragmentedTracks.length;i++){this.fragmentedTracks[i].id==e&&(t=i)}t>-1&&this.fragmentedTracks.splice(t,1)},g.prototype.setExtractionOptions=function(e,t,i){var r=this.getTrackById(e);if(r){var s={};this.extractedTracks.push(s),s.id=e,s.user=t,s.trak=r,r.nextSample=0,s.nb_samples=1e3,s.samples=[],i&&i.nbSamples&&(s.nb_samples=i.nbSamples)}},g.prototype.unsetExtractionOptions=function(e){for(var t=-1,i=0;i<this.extractedTracks.length;i++){this.extractedTracks[i].id==e&&(t=i)}t>-1&&this.extractedTracks.splice(t,1)},g.prototype.parse=function(){var e,t;if(!this.restoreParsePosition||this.restoreParsePosition())for(;;){if(this.hasIncompleteMdat&&this.hasIncompleteMdat()){if(this.processIncompleteMdat())continue;return}if(this.saveParsePosition&&this.saveParsePosition(),(e=h.parseOneBox(this.stream,false)).code===h.ERR_NOT_ENOUGH_DATA){if(this.processIncompleteBox){if(this.processIncompleteBox(e))continue;return}return}var i;switch(i="uuid"!==(t=e.box).type?t.type:t.uuid,this.boxes.push(t),i){case"mdat":this.mdats.push(t);break;case"moof":this.moofs.push(t);break;case"moov":this.moovStartFound=!0,0===this.mdats.length&&(this.isProgressive=!0);default:void 0!==this[i]&&n.warn("ISOFile","Duplicate Box of type: "+i+", overriding previous occurrence"),this[i]=t}this.updateUsedBytes&&this.updateUsedBytes(t,e)}},g.prototype.checkBuffer=function(e){if(null==e)throw"Buffer must be defined and non empty";if(void 0===e.fileStart)throw"Buffer must have a fileStart property";return 0===e.byteLength?(n.warn("ISOFile","Ignoring empty buffer (fileStart: "+e.fileStart+")"),this.stream.logBufferLevel(),!1):(n.info("ISOFile","Processing buffer (fileStart: "+e.fileStart+")"),e.usedBytes=0,this.stream.insertBuffer(e),this.stream.logBufferLevel(),!!this.stream.initialized()||(n.warn("ISOFile","Not ready to start parsing"),!1))},g.prototype.appendBuffer=function(e,t){var i;if(this.checkBuffer(e))return this.parse(),this.moovStartFound&&!this.moovStartSent&&(this.moovStartSent=!0,this.onMoovStart&&this.onMoovStart()),this.moov?(this.sampleListBuilt||(this.buildSampleLists(),this.sampleListBuilt=!0),this.updateSampleLists(),this.onReady&&!this.readySent&&(this.readySent=!0,this.onReady(this.getInfo())),this.processSamples(t),this.nextSeekPosition?(i=this.nextSeekPosition,this.nextSeekPosition=void 0):i=this.nextParsePosition,this.stream.getEndFilePositionAfter&&(i=this.stream.getEndFilePositionAfter(i))):i=this.nextParsePosition?this.nextParsePosition:0,this.sidx&&this.onSidx&&!this.sidxSent&&(this.onSidx(this.sidx),this.sidxSent=!0),this.meta&&(this.flattenItemInfo&&!this.itemListBuilt&&(this.flattenItemInfo(),this.itemListBuilt=!0),this.processItems&&this.processItems(this.onItem)),this.stream.cleanBuffers&&(n.info("ISOFile","Done processing buffer (fileStart: "+e.fileStart+") - next buffer to fetch should have a fileStart position of "+i),this.stream.logBufferLevel(),this.stream.cleanBuffers(),this.stream.logBufferLevel(!0),n.info("ISOFile","Sample data size in memory: "+this.getAllocatedSampleDataSize())),i},g.prototype.getInfo=function(){var e,t,i,r,s,a,n={},o=new Date("1904-01-01T00:00:00Z").getTime();if(this.moov)for(n.hasMoov=!0,n.duration=this.moov.mvhd.duration,n.timescale=this.moov.mvhd.timescale,n.isFragmented=null!=this.moov.mvex,n.isFragmented&&this.moov.mvex.mehd&&(n.fragment_duration=this.moov.mvex.mehd.fragment_duration),n.isProgressive=this.isProgressive,n.hasIOD=null!=this.moov.iods,n.brands=[],n.brands.push(this.ftyp.major_brand),n.brands=n.brands.concat(this.ftyp.compatible_brands),n.created=new Date(o+1e3*this.moov.mvhd.creation_time),n.modified=new Date(o+1e3*this.moov.mvhd.modification_time),n.tracks=[],n.audioTracks=[],n.videoTracks=[],n.subtitleTracks=[],n.metadataTracks=[],n.hintTracks=[],n.otherTracks=[],e=0;e<this.moov.traks.length;e++){if(a=(i=this.moov.traks[e]).mdia.minf.stbl.stsd.entries[0],r={},n.tracks.push(r),r.id=i.tkhd.track_id,r.name=i.mdia.hdlr.name,r.references=[],i.tref)for(t=0;t<i.tref.boxes.length;t++)s={},r.references.push(s),s.type=i.tref.boxes[t].type,s.track_ids=i.tref.boxes[t].track_ids;i.edts&&(r.edits=i.edts.elst.entries),r.created=new Date(o+1e3*i.tkhd.creation_time),r.modified=new Date(o+1e3*i.tkhd.modification_time),r.movie_duration=i.tkhd.duration,r.movie_timescale=n.timescale,r.layer=i.tkhd.layer,r.alternate_group=i.tkhd.alternate_group,r.volume=i.tkhd.volume,r.matrix=i.tkhd.matrix,r.track_width=i.tkhd.width/65536,r.track_height=i.tkhd.height/65536,r.timescale=i.mdia.mdhd.timescale,r.cts_shift=i.mdia.minf.stbl.cslg,r.duration=i.mdia.mdhd.duration,r.samples_duration=i.samples_duration,r.codec=a.getCodec(),r.kind=i.udta&&i.udta.kinds.length?i.udta.kinds[0]:{schemeURI:"",value:""},r.language=i.mdia.elng?i.mdia.elng.extended_language:i.mdia.mdhd.languageString,r.nb_samples=i.samples.length,r.size=i.samples_size,r.bitrate=8*r.size*r.timescale/r.samples_duration,a.isAudio()?(r.type="audio",n.audioTracks.push(r),r.audio={},r.audio.sample_rate=a.getSampleRate(),r.audio.channel_count=a.getChannelCount(),r.audio.sample_size=a.getSampleSize()):a.isVideo()?(r.type="video",n.videoTracks.push(r),r.video={},r.video.width=a.getWidth(),r.video.height=a.getHeight()):a.isSubtitle()?(r.type="subtitles",n.subtitleTracks.push(r)):a.isHint()?(r.type="metadata",n.hintTracks.push(r)):a.isMetadata()?(r.type="metadata",n.metadataTracks.push(r)):(r.type="metadata",n.otherTracks.push(r))}else n.hasMoov=!1;if(n.mime="",n.hasMoov&&n.tracks){for(n.videoTracks&&n.videoTracks.length>0?n.mime+='video/mp4; codecs="':n.audioTracks&&n.audioTracks.length>0?n.mime+='audio/mp4; codecs="':n.mime+='application/mp4; codecs="',e=0;e<n.tracks.length;e++)0!==e&&(n.mime+=","),n.mime+=n.tracks[e].codec;n.mime+='"; profiles="',n.mime+=this.ftyp.compatible_brands.join(),n.mime+='"'}return n},g.prototype.setNextSeekPositionFromSample=function(e){e&&(this.nextSeekPosition?this.nextSeekPosition=Math.min(e.offset+e.alreadyRead,this.nextSeekPosition):this.nextSeekPosition=e.offset+e.alreadyRead)},g.prototype.processSamples=function(e){var t,i;if(this.sampleProcessingStarted){if(this.isFragmentationInitialized&&null!==this.onSegment)for(t=0;t<this.fragmentedTracks.length;t++){var r=this.fragmentedTracks[t];for(i=r.trak;i.nextSample<i.samples.length&&this.sampleProcessingStarted;){n.debug("ISOFile","Creating media fragment on track #"+r.id+" for sample "+i.nextSample);var s=this.createFragment(r.id,i.nextSample,r.segmentStream);if(!s)break;if(r.segmentStream=s,i.nextSample++,(i.nextSample%r.nb_samples==0||e||i.nextSample>=i.samples.length)&&(n.info("ISOFile","Sending fragmented data on track #"+r.id+" for samples ["+Math.max(0,i.nextSample-r.nb_samples)+","+(i.nextSample-1)+"]"),n.info("ISOFile","Sample data size in memory: "+this.getAllocatedSampleDataSize()),this.onSegment&&this.onSegment(r.id,r.user,r.segmentStream.buffer,i.nextSample,e||i.nextSample>=i.samples.length),r.segmentStream=null,r!==this.fragmentedTracks[t]))break}}if(null!==this.onSamples)for(t=0;t<this.extractedTracks.length;t++){var a=this.extractedTracks[t];for(i=a.trak;i.nextSample<i.samples.length&&this.sampleProcessingStarted;){n.debug("ISOFile","Exporting on track #"+a.id+" sample #"+i.nextSample);var o=this.getSample(i,i.nextSample);if(!o){this.setNextSeekPositionFromSample(i.samples[i.nextSample]);break}if(i.nextSample++,a.samples.push(o),(i.nextSample%a.nb_samples==0||i.nextSample>=i.samples.length)&&(n.debug("ISOFile","Sending samples on track #"+a.id+" for sample "+i.nextSample),this.onSamples&&this.onSamples(a.id,a.user,a.samples),a.samples=[],a!==this.extractedTracks[t]))break}}}},g.prototype.getBox=function(e){var t=this.getBoxes(e,!0);return t.length?t[0]:null},g.prototype.getBoxes=function(e,t){var i=[];return g._sweep.call(this,e,i,t),i},g._sweep=function(e,t,i){for(var r in this.type&&this.type==e&&t.push(this),this.boxes){if(t.length&&i)return;g._sweep.call(this.boxes[r],e,t,i)}},g.prototype.getTrackSamplesInfo=function(e){var t=this.getTrackById(e);return t?t.samples:void 0},g.prototype.getTrackSample=function(e,t){var i=this.getTrackById(e);return this.getSample(i,t)},g.prototype.releaseUsedSamples=function(e,t){var i=0,r=this.getTrackById(e);r.lastValidSample||(r.lastValidSample=0);for(var s=r.lastValidSample;s<t;s++)i+=this.releaseSample(r,s);n.info("ISOFile","Track #"+e+" released samples up to "+t+" (released size: "+i+", remaining: "+this.samplesDataSize+")"),r.lastValidSample=t},g.prototype.start=function(){this.sampleProcessingStarted=!0,this.processSamples(!1)},g.prototype.stop=function(){this.sampleProcessingStarted=!1},g.prototype.flush=function(){n.info("ISOFile","Flushing remaining samples"),this.updateSampleLists(),this.processSamples(!0),this.stream.cleanBuffers(),this.stream.logBufferLevel(!0)},g.prototype.seekTrack=function(e,t,i){var r,s,a,o,d=0,l=0;if(0===i.samples.length)return n.info("ISOFile","No sample in track, cannot seek! Using time "+n.getDurationString(0,1)+" and offset: 0"),{offset:0,time:0};for(r=0;r<i.samples.length;r++){if(s=i.samples[r],0===r)l=0,o=s.timescale;else if(s.cts>e*s.timescale){l=r-1;break}t&&s.is_sync&&(d=r)}for(t&&(l=d),e=i.samples[l].cts,i.nextSample=l;i.samples[l].alreadyRead===i.samples[l].size&&i.samples[l+1];)l++;return a=i.samples[l].offset+i.samples[l].alreadyRead,n.info("ISOFile","Seeking to "+(t?"RAP":"")+" sample #"+i.nextSample+" on track "+i.tkhd.track_id+", time "+n.getDurationString(e,o)+" and offset: "+a),{offset:a,time:e/o}},g.prototype.getTrackDuration=function(e){var t;return e.samples?((t=e.samples[e.samples.length-1]).cts+t.duration)/t.timescale:1/0},g.prototype.seek=function(e,t){var i,r,s,a=this.moov,o={offset:1/0,time:1/0};if(this.moov){for(s=0;s<a.traks.length;s++)i=a.traks[s],e>this.getTrackDuration(i)||((r=this.seekTrack(e,t,i)).offset<o.offset&&(o.offset=r.offset),r.time<o.time&&(o.time=r.time));return n.info("ISOFile","Seeking at time "+n.getDurationString(o.time,1)+" needs a buffer with a fileStart position of "+o.offset),o.offset===1/0?o={offset:this.nextParsePosition,time:0}:o.offset=this.stream.getEndFilePositionAfter(o.offset),n.info("ISOFile","Adjusted seek position (after checking data already in buffer): "+o.offset),o}throw"Cannot seek: moov not received!"},g.prototype.equal=function(e){for(var t=0;t<this.boxes.length&&t<e.boxes.length;){var i=this.boxes[t],r=e.boxes[t];if(!h.boxEqual(i,r))return!1;t++}return!0},t.ISOFile=g,g.prototype.lastBoxStartPosition=0,g.prototype.parsingMdat=null,g.prototype.nextParsePosition=0,g.prototype.discardMdatData=!1,g.prototype.processIncompleteBox=function(e){var t;return"mdat"===e.type?(t=new h[e.type+"Box"](e.size),this.parsingMdat=t,this.boxes.push(t),this.mdats.push(t),t.start=e.start,t.hdr_size=e.hdr_size,this.stream.addUsedBytes(t.hdr_size),this.lastBoxStartPosition=t.start+t.size,this.stream.seek(t.start+t.size,!1,this.discardMdatData)?(this.parsingMdat=null,!0):(this.moovStartFound?this.nextParsePosition=this.stream.findEndContiguousBuf():this.nextParsePosition=t.start+t.size,!1)):("moov"===e.type&&(this.moovStartFound=!0,0===this.mdats.length&&(this.isProgressive=!0)),!!this.stream.mergeNextBuffer&&this.stream.mergeNextBuffer()?(this.nextParsePosition=this.stream.getEndPosition(),!0):(e.type?this.moovStartFound?this.nextParsePosition=this.stream.getEndPosition():this.nextParsePosition=this.stream.getPosition()+e.size:this.nextParsePosition=this.stream.getEndPosition(),!1))},g.prototype.hasIncompleteMdat=function(){return null!==this.parsingMdat},g.prototype.processIncompleteMdat=function(){var e;return e=this.parsingMdat,this.stream.seek(e.start+e.size,!1,this.discardMdatData)?(n.debug("ISOFile","Found 'mdat' end in buffered data"),this.parsingMdat=null,!0):(this.nextParsePosition=this.stream.findEndContiguousBuf(),!1)},g.prototype.restoreParsePosition=function(){return this.stream.seek(this.lastBoxStartPosition,!0,this.discardMdatData)},g.prototype.saveParsePosition=function(){this.lastBoxStartPosition=this.stream.getPosition()},g.prototype.updateUsedBytes=function(e,t){this.stream.addUsedBytes&&("mdat"===e.type?(this.stream.addUsedBytes(e.hdr_size),this.discardMdatData&&this.stream.addUsedBytes(e.size-e.hdr_size)):this.stream.addUsedBytes(e.size))},g.prototype.add=h.Box.prototype.add,g.prototype.addBox=h.Box.prototype.addBox,g.prototype.init=function(e){var t=e||{};this.add("ftyp").set("major_brand",t.brands&&t.brands[0]||"iso4").set("minor_version",0).set("compatible_brands",t.brands||["iso4"]);var i=this.add("moov");return i.add("mvhd").set("timescale",t.timescale||600).set("rate",t.rate||65536).set("creation_time",0).set("modification_time",0).set("duration",t.duration||0).set("volume",t.width?0:256).set("matrix",[65536,0,0,0,65536,0,0,0,1073741824]).set("next_track_id",1),i.add("mvex"),this},g.prototype.addTrack=function(e){this.moov||this.init(e);var t=e||{};t.width=t.width||320,t.height=t.height||320,t.id=t.id||this.moov.mvhd.next_track_id,t.type=t.type||"avc1";var i=this.moov.add("trak");this.moov.mvhd.next_track_id=t.id+1,i.add("tkhd").set("flags",h.TKHD_FLAG_ENABLED|h.TKHD_FLAG_IN_MOVIE|h.TKHD_FLAG_IN_PREVIEW).set("creation_time",0).set("modification_time",0).set("track_id",t.id).set("duration",t.duration||0).set("layer",t.layer||0).set("alternate_group",0).set("volume",1).set("matrix",[0,0,0,0,0,0,0,0,0]).set("width",t.width<<16).set("height",t.height<<16);var r=i.add("mdia");r.add("mdhd").set("creation_time",0).set("modification_time",0).set("timescale",t.timescale||1).set("duration",t.media_duration||0).set("language",t.language||"und"),r.add("hdlr").set("handler",t.hdlr||"vide").set("name",t.name||"Track created with MP4Box.js"),r.add("elng").set("extended_language",t.language||"fr-FR");var s=r.add("minf");if(void 0!==h[t.type+"SampleEntry"]){var a=new h[t.type+"SampleEntry"];a.data_reference_index=1;var n="";for(var d in h.sampleEntryCodes)for(var l=h.sampleEntryCodes[d],c=0;c<l.length;c++)if(l.indexOf(t.type)>-1){n=d;break}switch(n){case"Visual":if(s.add("vmhd").set("graphicsmode",0).set("opcolor",[0,0,0]),a.set("width",t.width).set("height",t.height).set("horizresolution",72<<16).set("vertresolution",72<<16).set("frame_count",1).set("compressorname",t.type+" Compressor").set("depth",24),t.avcDecoderConfigRecord){var u=new h.avcCBox;u.parse(new o(t.avcDecoderConfigRecord)),a.addBox(u)}else if(t.hevcDecoderConfigRecord){var f=new h.hvcCBox;f.parse(new o(t.hevcDecoderConfigRecord)),a.addBox(f)}break;case"Audio":s.add("smhd").set("balance",t.balance||0),a.set("channel_count",t.channel_count||2).set("samplesize",t.samplesize||16).set("samplerate",t.samplerate||65536);break;case"Hint":s.add("hmhd");break;case"Subtitle":if(s.add("sthd"),"stpp"===t.type)a.set("namespace",t.namespace||"nonamespace").set("schema_location",t.schema_location||"").set("auxiliary_mime_types",t.auxiliary_mime_types||"");break;default:s.add("nmhd")}t.description&&a.addBox(t.description),t.description_boxes&&t.description_boxes.forEach((function(e){a.addBox(e)})),s.add("dinf").add("dref").addEntry((new h["url Box"]).set("flags",1));var p=s.add("stbl");return p.add("stsd").addEntry(a),p.add("stts").set("sample_counts",[]).set("sample_deltas",[]),p.add("stsc").set("first_chunk",[]).set("samples_per_chunk",[]).set("sample_description_index",[]),p.add("stco").set("chunk_offsets",[]),p.add("stsz").set("sample_sizes",[]),this.moov.mvex.add("trex").set("track_id",t.id).set("default_sample_description_index",t.default_sample_description_index||1).set("default_sample_duration",t.default_sample_duration||0).set("default_sample_size",t.default_sample_size||0).set("default_sample_flags",t.default_sample_flags||0),this.buildTrakSampleLists(i),t.id}},h.Box.prototype.computeSize=function(e){var t=e||new d;t.endianness=d.BIG_ENDIAN,this.write(t)},g.prototype.addSample=function(e,t,i){var r=i||{},s={},a=this.getTrackById(e);if(null!==a){s.number=a.samples.length,s.track_id=a.tkhd.track_id,s.timescale=a.mdia.mdhd.timescale,s.description_index=r.sample_description_index?r.sample_description_index-1:0,s.description=a.mdia.minf.stbl.stsd.entries[s.description_index],s.data=t,s.size=t.byteLength,s.alreadyRead=s.size,s.duration=r.duration||1,s.cts=r.cts||0,s.dts=r.dts||0,s.is_sync=r.is_sync||!1,s.is_leading=r.is_leading||0,s.depends_on=r.depends_on||0,s.is_depended_on=r.is_depended_on||0,s.has_redundancy=r.has_redundancy||0,s.degradation_priority=r.degradation_priority||0,s.offset=0,s.subsamples=r.subsamples,a.samples.push(s),a.samples_size+=s.size,a.samples_duration+=s.duration,void 0===a.first_dts&&(a.first_dts=r.dts),this.processSamples();var n=this.createSingleSampleMoof(s);return this.addBox(n),n.computeSize(),n.trafs[0].truns[0].data_offset=n.size+8,this.add("mdat").data=new Uint8Array(t),s}},g.prototype.createSingleSampleMoof=function(e){var t=0;t=e.is_sync?1<<25:65536;var i=new h.moofBox;i.add("mfhd").set("sequence_number",this.nextMoofNumber),this.nextMoofNumber++;var r=i.add("traf"),s=this.getTrackById(e.track_id);return r.add("tfhd").set("track_id",e.track_id).set("flags",h.TFHD_FLAG_DEFAULT_BASE_IS_MOOF),r.add("tfdt").set("baseMediaDecodeTime",e.dts-(s.first_dts||0)),r.add("trun").set("flags",h.TRUN_FLAGS_DATA_OFFSET|h.TRUN_FLAGS_DURATION|h.TRUN_FLAGS_SIZE|h.TRUN_FLAGS_FLAGS|h.TRUN_FLAGS_CTS_OFFSET).set("data_offset",0).set("first_sample_flags",0).set("sample_count",1).set("sample_duration",[e.duration]).set("sample_size",[e.size]).set("sample_flags",[t]).set("sample_composition_time_offset",[e.cts-e.dts]),i},g.prototype.lastMoofIndex=0,g.prototype.samplesDataSize=0,g.prototype.resetTables=function(){var e,t,i,r,s,a;for(this.initial_duration=this.moov.mvhd.duration,this.moov.mvhd.duration=0,e=0;e<this.moov.traks.length;e++){(t=this.moov.traks[e]).tkhd.duration=0,t.mdia.mdhd.duration=0,(t.mdia.minf.stbl.stco||t.mdia.minf.stbl.co64).chunk_offsets=[],(i=t.mdia.minf.stbl.stsc).first_chunk=[],i.samples_per_chunk=[],i.sample_description_index=[],(t.mdia.minf.stbl.stsz||t.mdia.minf.stbl.stz2).sample_sizes=[],(r=t.mdia.minf.stbl.stts).sample_counts=[],r.sample_deltas=[],(s=t.mdia.minf.stbl.ctts)&&(s.sample_counts=[],s.sample_offsets=[]),a=t.mdia.minf.stbl.stss;var n=t.mdia.minf.stbl.boxes.indexOf(a);-1!=n&&(t.mdia.minf.stbl.boxes[n]=null)}},g.initSampleGroups=function(e,t,i,r,s){var a,n,o,d;function l(e,t,i){this.grouping_type=e,this.grouping_type_parameter=t,this.sbgp=i,this.last_sample_in_run=-1,this.entry_index=-1}for(t&&(t.sample_groups_info=[]),e.sample_groups_info||(e.sample_groups_info=[]),n=0;n<i.length;n++){for(d=i[n].grouping_type+"/"+i[n].grouping_type_parameter,o=new l(i[n].grouping_type,i[n].grouping_type_parameter,i[n]),t&&(t.sample_groups_info[d]=o),e.sample_groups_info[d]||(e.sample_groups_info[d]=o),a=0;a<r.length;a++)r[a].grouping_type===i[n].grouping_type&&(o.description=r[a],o.description.used=!0);if(s)for(a=0;a<s.length;a++)s[a].grouping_type===i[n].grouping_type&&(o.fragment_description=s[a],o.fragment_description.used=!0,o.is_fragment=!0)}if(t){if(s)for(n=0;n<s.length;n++)!s[n].used&&s[n].version>=2&&(d=s[n].grouping_type+"/0",(o=new l(s[n].grouping_type,0)).is_fragment=!0,t.sample_groups_info[d]||(t.sample_groups_info[d]=o))}else for(n=0;n<r.length;n++)!r[n].used&&r[n].version>=2&&(d=r[n].grouping_type+"/0",o=new l(r[n].grouping_type,0),e.sample_groups_info[d]||(e.sample_groups_info[d]=o))},g.setSampleGroupProperties=function(e,t,i,r){var s,a;for(s in t.sample_groups=[],r){var n;if(t.sample_groups[s]={},t.sample_groups[s].grouping_type=r[s].grouping_type,t.sample_groups[s].grouping_type_parameter=r[s].grouping_type_parameter,i>=r[s].last_sample_in_run&&(r[s].last_sample_in_run<0&&(r[s].last_sample_in_run=0),r[s].entry_index++,r[s].entry_index<=r[s].sbgp.entries.length-1&&(r[s].last_sample_in_run+=r[s].sbgp.entries[r[s].entry_index].sample_count)),r[s].entry_index<=r[s].sbgp.entries.length-1?t.sample_groups[s].group_description_index=r[s].sbgp.entries[r[s].entry_index].group_description_index:t.sample_groups[s].group_description_index=-1,0!==t.sample_groups[s].group_description_index)n=r[s].fragment_description?r[s].fragment_description:r[s].description,t.sample_groups[s].group_description_index>0?(a=t.sample_groups[s].group_description_index>65535?(t.sample_groups[s].group_description_index>>16)-1:t.sample_groups[s].group_description_index-1,n&&a>=0&&(t.sample_groups[s].description=n.entries[a])):n&&n.version>=2&&n.default_group_description_index>0&&(t.sample_groups[s].description=n.entries[n.default_group_description_index-1])}},g.process_sdtp=function(e,t,i){t&&(e?(t.is_leading=e.is_leading[i],t.depends_on=e.sample_depends_on[i],t.is_depended_on=e.sample_is_depended_on[i],t.has_redundancy=e.sample_has_redundancy[i]):(t.is_leading=0,t.depends_on=0,t.is_depended_on=0,t.has_redundancy=0))},g.prototype.buildSampleLists=function(){var e,t;for(e=0;e<this.moov.traks.length;e++)t=this.moov.traks[e],this.buildTrakSampleLists(t)},g.prototype.buildTrakSampleLists=function(e){var t,i,r,s,a,n,o,d,l,c,u,h,f,p,_,m,y,b,v,S,w,A,B,x;if(e.samples=[],e.samples_duration=0,e.samples_size=0,i=e.mdia.minf.stbl.stco||e.mdia.minf.stbl.co64,r=e.mdia.minf.stbl.stsc,s=e.mdia.minf.stbl.stsz||e.mdia.minf.stbl.stz2,a=e.mdia.minf.stbl.stts,n=e.mdia.minf.stbl.ctts,o=e.mdia.minf.stbl.stss,d=e.mdia.minf.stbl.stsd,l=e.mdia.minf.stbl.subs,h=e.mdia.minf.stbl.stdp,c=e.mdia.minf.stbl.sbgps,u=e.mdia.minf.stbl.sgpds,b=-1,v=-1,S=-1,w=-1,A=0,B=0,x=0,g.initSampleGroups(e,null,c,u),void 0!==s){for(t=0;t<s.sample_sizes.length;t++){var U={};U.number=t,U.track_id=e.tkhd.track_id,U.timescale=e.mdia.mdhd.timescale,U.alreadyRead=0,e.samples[t]=U,U.size=s.sample_sizes[t],e.samples_size+=U.size,0===t?(p=1,f=0,U.chunk_index=p,U.chunk_run_index=f,y=r.samples_per_chunk[f],m=0,_=f+1<r.first_chunk.length?r.first_chunk[f+1]-1:1/0):t<y?(U.chunk_index=p,U.chunk_run_index=f):(p++,U.chunk_index=p,m=0,p<=_||(_=++f+1<r.first_chunk.length?r.first_chunk[f+1]-1:1/0),U.chunk_run_index=f,y+=r.samples_per_chunk[f]),U.description_index=r.sample_description_index[U.chunk_run_index]-1,U.description=d.entries[U.description_index],U.offset=i.chunk_offsets[U.chunk_index-1]+m,m+=U.size,t>b&&(v++,b<0&&(b=0),b+=a.sample_counts[v]),t>0?(e.samples[t-1].duration=a.sample_deltas[v],e.samples_duration+=e.samples[t-1].duration,U.dts=e.samples[t-1].dts+e.samples[t-1].duration):U.dts=0,n?(t>=S&&(w++,S<0&&(S=0),S+=n.sample_counts[w]),U.cts=e.samples[t].dts+n.sample_offsets[w]):U.cts=U.dts,o?(t==o.sample_numbers[A]-1?(U.is_sync=!0,A++):(U.is_sync=!1,U.degradation_priority=0),l&&l.entries[B].sample_delta+x==t+1&&(U.subsamples=l.entries[B].subsamples,x+=l.entries[B].sample_delta,B++)):U.is_sync=!0,g.process_sdtp(e.mdia.minf.stbl.sdtp,U,U.number),U.degradation_priority=h?h.priority[t]:0,l&&l.entries[B].sample_delta+x==t&&(U.subsamples=l.entries[B].subsamples,x+=l.entries[B].sample_delta),(c.length>0||u.length>0)&&g.setSampleGroupProperties(e,U,t,e.sample_groups_info)}t>0&&(e.samples[t-1].duration=Math.max(e.mdia.mdhd.duration-e.samples[t-1].dts,0),e.samples_duration+=e.samples[t-1].duration)}},g.prototype.updateSampleLists=function(){var e,t,i,r,s,a,n,o,d,l,c,u,f,p,_;if(void 0!==this.moov)for(;this.lastMoofIndex<this.moofs.length;)if(d=this.moofs[this.lastMoofIndex],this.lastMoofIndex++,"moof"==d.type)for(l=d,e=0;e<l.trafs.length;e++){for(c=l.trafs[e],u=this.getTrackById(c.tfhd.track_id),f=this.getTrexById(c.tfhd.track_id),r=c.tfhd.flags&h.TFHD_FLAG_SAMPLE_DESC?c.tfhd.default_sample_description_index:f?f.default_sample_description_index:1,s=c.tfhd.flags&h.TFHD_FLAG_SAMPLE_DUR?c.tfhd.default_sample_duration:f?f.default_sample_duration:0,a=c.tfhd.flags&h.TFHD_FLAG_SAMPLE_SIZE?c.tfhd.default_sample_size:f?f.default_sample_size:0,n=c.tfhd.flags&h.TFHD_FLAG_SAMPLE_FLAGS?c.tfhd.default_sample_flags:f?f.default_sample_flags:0,c.sample_number=0,c.sbgps.length>0&&g.initSampleGroups(u,c,c.sbgps,u.mdia.minf.stbl.sgpds,c.sgpds),t=0;t<c.truns.length;t++){var m=c.truns[t];for(i=0;i<m.sample_count;i++){(p={}).moof_number=this.lastMoofIndex,p.number_in_traf=c.sample_number,c.sample_number++,p.number=u.samples.length,c.first_sample_index=u.samples.length,u.samples.push(p),p.track_id=u.tkhd.track_id,p.timescale=u.mdia.mdhd.timescale,p.description_index=r-1,p.description=u.mdia.minf.stbl.stsd.entries[p.description_index],p.size=a,m.flags&h.TRUN_FLAGS_SIZE&&(p.size=m.sample_size[i]),u.samples_size+=p.size,p.duration=s,m.flags&h.TRUN_FLAGS_DURATION&&(p.duration=m.sample_duration[i]),u.samples_duration+=p.duration,u.first_traf_merged||i>0?p.dts=u.samples[u.samples.length-2].dts+u.samples[u.samples.length-2].duration:(c.tfdt?p.dts=c.tfdt.baseMediaDecodeTime:p.dts=0,u.first_traf_merged=!0),p.cts=p.dts,m.flags&h.TRUN_FLAGS_CTS_OFFSET&&(p.cts=p.dts+m.sample_composition_time_offset[i]),_=n,m.flags&h.TRUN_FLAGS_FLAGS?_=m.sample_flags[i]:0===i&&m.flags&h.TRUN_FLAGS_FIRST_FLAG&&(_=m.first_sample_flags),p.is_sync=!(_>>16&1),p.is_leading=_>>26&3,p.depends_on=_>>24&3,p.is_depended_on=_>>22&3,p.has_redundancy=_>>20&3,p.degradation_priority=65535&_;var y=!!(c.tfhd.flags&h.TFHD_FLAG_BASE_DATA_OFFSET),b=!!(c.tfhd.flags&h.TFHD_FLAG_DEFAULT_BASE_IS_MOOF),v=!!(m.flags&h.TRUN_FLAGS_DATA_OFFSET),S=0;S=y?c.tfhd.base_data_offset:b||0===t?l.start:o,p.offset=0===t&&0===i?v?S+m.data_offset:S:o,o=p.offset+p.size,(c.sbgps.length>0||c.sgpds.length>0||u.mdia.minf.stbl.sbgps.length>0||u.mdia.minf.stbl.sgpds.length>0)&&g.setSampleGroupProperties(u,p,p.number_in_traf,c.sample_groups_info)}}if(c.subs){u.has_fragment_subsamples=!0;var w=c.first_sample_index;for(t=0;t<c.subs.entries.length;t++)w+=c.subs.entries[t].sample_delta,(p=u.samples[w-1]).subsamples=c.subs.entries[t].subsamples}}},g.prototype.getSample=function(e,t){var i,r=e.samples[t];if(!this.moov)return null;if(r.data){if(r.alreadyRead==r.size)return r}else r.data=new Uint8Array(r.size),r.alreadyRead=0,this.samplesDataSize+=r.size,n.debug("ISOFile","Allocating sample #"+t+" on track #"+e.tkhd.track_id+" of size "+r.size+" (total: "+this.samplesDataSize+")");for(;;){var s=this.stream.findPosition(!0,r.offset+r.alreadyRead,!1);if(!(s>-1))return null;var a=(i=this.stream.buffers[s]).byteLength-(r.offset+r.alreadyRead-i.fileStart);if(r.size-r.alreadyRead<=a)return n.debug("ISOFile","Getting sample #"+t+" data (alreadyRead: "+r.alreadyRead+" offset: "+(r.offset+r.alreadyRead-i.fileStart)+" read size: "+(r.size-r.alreadyRead)+" full size: "+r.size+")"),d.memcpy(r.data.buffer,r.alreadyRead,i,r.offset+r.alreadyRead-i.fileStart,r.size-r.alreadyRead),i.usedBytes+=r.size-r.alreadyRead,this.stream.logBufferLevel(),r.alreadyRead=r.size,r;if(0===a)return null;n.debug("ISOFile","Getting sample #"+t+" partial data (alreadyRead: "+r.alreadyRead+" offset: "+(r.offset+r.alreadyRead-i.fileStart)+" read size: "+a+" full size: "+r.size+")"),d.memcpy(r.data.buffer,r.alreadyRead,i,r.offset+r.alreadyRead-i.fileStart,a),r.alreadyRead+=a,i.usedBytes+=a,this.stream.logBufferLevel()}},g.prototype.releaseSample=function(e,t){var i=e.samples[t];return i.data?(this.samplesDataSize-=i.size,i.data=null,i.alreadyRead=0,i.size):0},g.prototype.getAllocatedSampleDataSize=function(){return this.samplesDataSize},g.prototype.getCodecs=function(){var e,t="";for(e=0;e<this.moov.traks.length;e++){e>0&&(t+=","),t+=this.moov.traks[e].mdia.minf.stbl.stsd.entries[0].getCodec()}return t},g.prototype.getTrexById=function(e){var t;if(!this.moov||!this.moov.mvex)return null;for(t=0;t<this.moov.mvex.trexs.length;t++){var i=this.moov.mvex.trexs[t];if(i.track_id==e)return i}return null},g.prototype.getTrackById=function(e){if(void 0===this.moov)return null;for(var t=0;t<this.moov.traks.length;t++){var i=this.moov.traks[t];if(i.tkhd.track_id==e)return i}return null},g.prototype.items=[],g.prototype.entity_groups=[],g.prototype.itemsDataSize=0,g.prototype.flattenItemInfo=function(){var e,t,i,r=this.items,s=this.entity_groups,a=this.meta;if(null!=a&&void 0!==a.hdlr&&void 0!==a.iinf){for(e=0;e<a.iinf.item_infos.length;e++)(i={}).id=a.iinf.item_infos[e].item_ID,r[i.id]=i,i.ref_to=[],i.name=a.iinf.item_infos[e].item_name,a.iinf.item_infos[e].protection_index>0&&(i.protection=a.ipro.protections[a.iinf.item_infos[e].protection_index-1]),a.iinf.item_infos[e].item_type?i.type=a.iinf.item_infos[e].item_type:i.type="mime",i.content_type=a.iinf.item_infos[e].content_type,i.content_encoding=a.iinf.item_infos[e].content_encoding;if(a.grpl)for(e=0;e<a.grpl.boxes.length;e++)entity_group={},entity_group.id=a.grpl.boxes[e].group_id,entity_group.entity_ids=a.grpl.boxes[e].entity_ids,entity_group.type=a.grpl.boxes[e].type,s[entity_group.id]=entity_group;if(a.iloc)for(e=0;e<a.iloc.items.length;e++){var o=a.iloc.items[e];switch(i=r[o.item_ID],0!==o.data_reference_index&&(n.warn("Item storage with reference to other files: not supported"),i.source=a.dinf.boxes[o.data_reference_index-1]),o.construction_method){case 0:break;case 1:case 2:n.warn("Item storage with construction_method : not supported")}for(i.extents=[],i.size=0,t=0;t<o.extents.length;t++)i.extents[t]={},i.extents[t].offset=o.extents[t].extent_offset+o.base_offset,i.extents[t].length=o.extents[t].extent_length,i.extents[t].alreadyRead=0,i.size+=i.extents[t].length}if(a.pitm&&(r[a.pitm.item_id].primary=!0),a.iref)for(e=0;e<a.iref.references.length;e++){var d=a.iref.references[e];for(t=0;t<d.references.length;t++)r[d.from_item_ID].ref_to.push({type:d.type,id:d.references[t]})}if(a.iprp)for(var l=0;l<a.iprp.ipmas.length;l++){var c=a.iprp.ipmas[l];for(e=0;e<c.associations.length;e++){var u=c.associations[e];if((i=r[u.id])||(i=s[u.id]),i)for(void 0===i.properties&&(i.properties={},i.properties.boxes=[]),t=0;t<u.props.length;t++){var h=u.props[t];if(h.property_index>0&&h.property_index-1<a.iprp.ipco.boxes.length){var f=a.iprp.ipco.boxes[h.property_index-1];i.properties[f.type]=f,i.properties.boxes.push(f)}}}}}},g.prototype.getItem=function(e){var t,i;if(!this.meta)return null;if(!(i=this.items[e]).data&&i.size)i.data=new Uint8Array(i.size),i.alreadyRead=0,this.itemsDataSize+=i.size,n.debug("ISOFile","Allocating item #"+e+" of size "+i.size+" (total: "+this.itemsDataSize+")");else if(i.alreadyRead===i.size)return i;for(var r=0;r<i.extents.length;r++){var s=i.extents[r];if(s.alreadyRead!==s.length){var a=this.stream.findPosition(!0,s.offset+s.alreadyRead,!1);if(!(a>-1))return null;var o=(t=this.stream.buffers[a]).byteLength-(s.offset+s.alreadyRead-t.fileStart);if(!(s.length-s.alreadyRead<=o))return n.debug("ISOFile","Getting item #"+e+" extent #"+r+" partial data (alreadyRead: "+s.alreadyRead+" offset: "+(s.offset+s.alreadyRead-t.fileStart)+" read size: "+o+" full extent size: "+s.length+" full item size: "+i.size+")"),d.memcpy(i.data.buffer,i.alreadyRead,t,s.offset+s.alreadyRead-t.fileStart,o),s.alreadyRead+=o,i.alreadyRead+=o,t.usedBytes+=o,this.stream.logBufferLevel(),null;n.debug("ISOFile","Getting item #"+e+" extent #"+r+" data (alreadyRead: "+s.alreadyRead+" offset: "+(s.offset+s.alreadyRead-t.fileStart)+" read size: "+(s.length-s.alreadyRead)+" full extent size: "+s.length+" full item size: "+i.size+")"),d.memcpy(i.data.buffer,i.alreadyRead,t,s.offset+s.alreadyRead-t.fileStart,s.length-s.alreadyRead),t.usedBytes+=s.length-s.alreadyRead,this.stream.logBufferLevel(),i.alreadyRead+=s.length-s.alreadyRead,s.alreadyRead=s.length}}return i.alreadyRead===i.size?i:null},g.prototype.releaseItem=function(e){var t=this.items[e];if(t.data){this.itemsDataSize-=t.size,t.data=null,t.alreadyRead=0;for(var i=0;i<t.extents.length;i++){t.extents[i].alreadyRead=0}return t.size}return 0},g.prototype.processItems=function(e){for(var t in this.items){var i=this.items[t];this.getItem(i.id),e&&!i.sent&&(e(i),i.sent=!0,i.data=null)}},g.prototype.hasItem=function(e){for(var t in this.items){var i=this.items[t];if(i.name===e)return i.id}return-1},g.prototype.getMetaHandler=function(){return this.meta?this.meta.hdlr.handler:null},g.prototype.getPrimaryItem=function(){return this.meta&&this.meta.pitm?this.getItem(this.meta.pitm.item_id):null},g.prototype.itemToFragmentedTrackFile=function(e){var t=e||{},i=null;if(null==(i=t.itemId?this.getItem(t.itemId):this.getPrimaryItem()))return null;var r=new g;r.discardMdatData=!1;var s={type:i.type,description_boxes:i.properties.boxes};i.properties.ispe&&(s.width=i.properties.ispe.image_width,s.height=i.properties.ispe.image_height);var a=r.addTrack(s);return a?(r.addSample(a,i.data),r):null},g.prototype.write=function(e){for(var t=0;t<this.boxes.length;t++)this.boxes[t].write(e)},g.prototype.createFragment=function(e,t,i){var r=this.getTrackById(e),s=this.getSample(r,t);if(null==s)return this.setNextSeekPositionFromSample(r.samples[t]),null;var a=i||new d;a.endianness=d.BIG_ENDIAN;var o=this.createSingleSampleMoof(s);o.write(a),o.trafs[0].truns[0].data_offset=o.size+8,n.debug("MP4Box","Adjusting data_offset with new value "+o.trafs[0].truns[0].data_offset),a.adjustUint32(o.trafs[0].truns[0].data_offset_position,o.trafs[0].truns[0].data_offset);var l=new h.mdatBox;return l.data=s.data,l.write(a),a},g.writeInitializationSegment=function(e,t,i,r){var s;n.debug("ISOFile","Generating initialization segment");var a=new d;a.endianness=d.BIG_ENDIAN,e.write(a);var o=t.add("mvex");for(i&&o.add("mehd").set("fragment_duration",i),s=0;s<t.traks.length;s++)o.add("trex").set("track_id",t.traks[s].tkhd.track_id).set("default_sample_description_index",1).set("default_sample_duration",r).set("default_sample_size",0).set("default_sample_flags",65536);return t.write(a),a.buffer},g.prototype.save=function(e){var t=new d;t.endianness=d.BIG_ENDIAN,this.write(t),t.save(e)},g.prototype.getBuffer=function(){var e=new d;return e.endianness=d.BIG_ENDIAN,this.write(e),e.buffer},g.prototype.initializeSegmentation=function(){var e,t,i,r;for(null===this.onSegment&&n.warn("MP4Box","No segmentation callback set!"),this.isFragmentationInitialized||(this.isFragmentationInitialized=!0,this.nextMoofNumber=0,this.resetTables()),t=[],e=0;e<this.fragmentedTracks.length;e++){var s=new h.moovBox;s.mvhd=this.moov.mvhd,s.boxes.push(s.mvhd),i=this.getTrackById(this.fragmentedTracks[e].id),s.boxes.push(i),s.traks.push(i),(r={}).id=i.tkhd.track_id,r.user=this.fragmentedTracks[e].user,r.buffer=g.writeInitializationSegment(this.ftyp,s,this.moov.mvex&&this.moov.mvex.mehd?this.moov.mvex.mehd.fragment_duration:void 0,this.moov.traks[e].samples.length>0?this.moov.traks[e].samples[0].duration:0),t.push(r)}return t},h.Box.prototype.printHeader=function(e){this.size+=8,this.size>l&&(this.size+=8),"uuid"===this.type&&(this.size+=16),e.log(e.indent+"size:"+this.size),e.log(e.indent+"type:"+this.type)},h.FullBox.prototype.printHeader=function(e){this.size+=4,h.Box.prototype.printHeader.call(this,e),e.log(e.indent+"version:"+this.version),e.log(e.indent+"flags:"+this.flags)},h.Box.prototype.print=function(e){this.printHeader(e)},h.ContainerBox.prototype.print=function(e){this.printHeader(e);for(var t=0;t<this.boxes.length;t++)if(this.boxes[t]){var i=e.indent;e.indent+=" ",this.boxes[t].print(e),e.indent=i}},g.prototype.print=function(e){e.indent="";for(var t=0;t<this.boxes.length;t++)this.boxes[t]&&this.boxes[t].print(e)},h.mvhdBox.prototype.print=function(e){h.FullBox.prototype.printHeader.call(this,e),e.log(e.indent+"creation_time: "+this.creation_time),e.log(e.indent+"modification_time: "+this.modification_time),e.log(e.indent+"timescale: "+this.timescale),e.log(e.indent+"duration: "+this.duration),e.log(e.indent+"rate: "+this.rate),e.log(e.indent+"volume: "+(this.volume>>8)),e.log(e.indent+"matrix: "+this.matrix.join(", ")),e.log(e.indent+"next_track_id: "+this.next_track_id)},h.tkhdBox.prototype.print=function(e){h.FullBox.prototype.printHeader.call(this,e),e.log(e.indent+"creation_time: "+this.creation_time),e.log(e.indent+"modification_time: "+this.modification_time),e.log(e.indent+"track_id: "+this.track_id),e.log(e.indent+"duration: "+this.duration),e.log(e.indent+"volume: "+(this.volume>>8)),e.log(e.indent+"matrix: "+this.matrix.join(", ")),e.log(e.indent+"layer: "+this.layer),e.log(e.indent+"alternate_group: "+this.alternate_group),e.log(e.indent+"width: "+this.width),e.log(e.indent+"height: "+this.height)};var y={createFile:function(e,t){var i=void 0===e||e,r=new g(t);return r.discardMdatData=!i,r}};t.createFile=y.createFile}));function Rr(e){return e.reduce(((e,t)=>256*e+t))}function Nr(e){const t=[101,103,119,99],i=e.length-28,r=e.slice(i,i+t.length);return t.every(((e,t)=>e===r[t]))}zr.Log,zr.MP4BoxStream,zr.DataStream,zr.MultiBufferStream,zr.MPEG4DescriptorParser,zr.BoxParser,zr.XMLSubtitlein4Parser,zr.Textin4Parser,zr.ISOFile,zr.createFile;class Gr{constructor(){this.s=null,this.a=null,this.l=0,this.c=0,this.u=1/0,this.A=!1,this.d=!1,this.r=4194304,this.n=new Uint8Array([30,158,90,33,244,57,83,165,2,70,35,87,215,231,226,108]),this.t=this.n.slice().reverse()}destroy(){this.s=null,this.a=null,this.l=0,this.c=0,this.u=1/0,this.A=!1,this.d=!1,this.r=4194304,this.n=null,this.t=null}transport(e){if(!this.s&&this.l>50)return e;if(this.l++,this.d)return e;const t=new Uint8Array(e);if(this.A){if(!(this.c<this.u))return this.a&&this.s?(this.a.set(t,this.r),this.s.parse(null,this.r,t.byteLength),this.a.slice(this.r,this.r+t.byteLength)):(console.error("video_error_2"),this.d=!0,e);Nr(t)&&this.c++}else{const i=function(e,t){const i=function(e,t){for(let i=0;i<e.byteLength-t.length;i++)for(let r=0;r<t.length&&e[i+r]===t[r];r++)if(r===t.length-1)return i;return null}(e,t);if(i){const t=Rr(e.slice(i+16,i+16+8));return[t,Rr(e.slice(i+24,i+24+8)),function(e){return e.map((e=>~e))}(e.slice(i+32,i+32+t))]}return null}(t,this.t);if(!i)return e;const r=function(e){try{if("object"!=typeof WebAssembly||"function"!=typeof WebAssembly.instantiate)throw null;{const e=new WebAssembly.Module(Uint8Array.of(0,97,115,109,1,0,0,0));if(!(e instanceof WebAssembly.Module&&new WebAssembly.Instance(e)instanceof WebAssembly.Instance))throw null}}catch(e){return new Error("video_error_4")}let t;try{t={env:{__handle_stack_overflow:()=>e(new Error("video_error_1")),memory:new WebAssembly.Memory({initial:256,maximum:256})}}}catch(e){return new Error("video_error_5")}return t}(e);if(r instanceof Error)return console.error(r.message),this.d=!0,e;this.A=!0,this.u=i[1],Nr(t)&&this.c++,WebAssembly.instantiate(i[2],r).then((e=>{if("function"!=typeof(t=e.instance.exports).parse||"object"!=typeof t.memory)return this.d=!0,void console.error("video_error_3");var t;this.s=e.instance.exports,this.a=new Uint8Array(this.s.memory.buffer)})).catch((e=>{this.d=!0,console.error("video_error_6")}))}return e}}const Or=16,Hr=[214,144,233,254,204,225,61,183,22,182,20,194,40,251,44,5,43,103,154,118,42,190,4,195,170,68,19,38,73,134,6,153,156,66,80,244,145,239,152,122,51,84,11,67,237,207,172,98,228,179,28,169,201,8,232,149,128,223,148,250,117,143,63,166,71,7,167,252,243,115,23,186,131,89,60,25,230,133,79,168,104,107,129,178,113,100,218,139,248,235,15,75,112,86,157,53,30,36,14,94,99,88,209,162,37,34,124,59,1,33,120,135,212,0,70,87,159,211,39,82,76,54,2,231,160,196,200,158,234,191,138,210,64,199,56,181,163,247,242,206,249,97,21,161,224,174,93,164,155,52,26,85,173,147,50,48,245,140,177,227,29,246,226,46,130,102,202,96,192,41,35,171,13,83,78,111,213,219,55,69,222,253,142,47,3,255,106,114,109,108,91,81,141,27,175,146,187,221,188,127,17,217,92,65,31,16,90,216,10,193,49,136,165,205,123,189,45,116,208,18,184,229,180,176,137,105,151,74,12,150,119,126,101,185,241,9,197,110,198,132,24,240,125,236,58,220,77,32,121,238,95,62,215,203,57,72],Vr=[462357,472066609,943670861,1415275113,1886879365,2358483617,2830087869,3301692121,3773296373,4228057617,404694573,876298825,1347903077,1819507329,2291111581,2762715833,3234320085,3705924337,4177462797,337322537,808926789,1280531041,1752135293,2223739545,2695343797,3166948049,3638552301,4110090761,269950501,741554753,1213159005,1684763257];function $r(e){const t=[];for(let i=0,r=e.length;i<r;i+=2)t.push(parseInt(e.substr(i,2),16));return t}function Wr(e){return e.map((e=>1===(e=e.toString(16)).length?"0"+e:e)).join("")}function Yr(e){const t=[];for(let i=0,r=e.length;i<r;i++){const r=e.codePointAt(i);if(r<=127)t.push(r);else if(r<=2047)t.push(192|r>>>6),t.push(128|63&r);else if(r<=55295||r>=57344&&r<=65535)t.push(224|r>>>12),t.push(128|r>>>6&63),t.push(128|63&r);else{if(!(r>=65536&&r<=1114111))throw t.push(r),new Error("input is not supported");i++,t.push(240|r>>>18&28),t.push(128|r>>>12&63),t.push(128|r>>>6&63),t.push(128|63&r)}}return t}function qr(e){const t=[];for(let i=0,r=e.length;i<r;i++)e[i]>=240&&e[i]<=247?(t.push(String.fromCodePoint(((7&e[i])<<18)+((63&e[i+1])<<12)+((63&e[i+2])<<6)+(63&e[i+3]))),i+=3):e[i]>=224&&e[i]<=239?(t.push(String.fromCodePoint(((15&e[i])<<12)+((63&e[i+1])<<6)+(63&e[i+2]))),i+=2):e[i]>=192&&e[i]<=223?(t.push(String.fromCodePoint(((31&e[i])<<6)+(63&e[i+1]))),i++):t.push(String.fromCodePoint(e[i]));return t.join("")}function jr(e,t){const i=31&t;return e<<i|e>>>32-i}function Kr(e){return(255&Hr[e>>>24&255])<<24|(255&Hr[e>>>16&255])<<16|(255&Hr[e>>>8&255])<<8|255&Hr[255&e]}function Xr(e){return e^jr(e,2)^jr(e,10)^jr(e,18)^jr(e,24)}function Zr(e){return e^jr(e,13)^jr(e,23)}function Jr(e,t,i){const r=new Array(4),s=new Array(4);for(let t=0;t<4;t++)s[0]=255&e[4*t],s[1]=255&e[4*t+1],s[2]=255&e[4*t+2],s[3]=255&e[4*t+3],r[t]=s[0]<<24|s[1]<<16|s[2]<<8|s[3];for(let e,t=0;t<32;t+=4)e=r[1]^r[2]^r[3]^i[t+0],r[0]^=Xr(Kr(e)),e=r[2]^r[3]^r[0]^i[t+1],r[1]^=Xr(Kr(e)),e=r[3]^r[0]^r[1]^i[t+2],r[2]^=Xr(Kr(e)),e=r[0]^r[1]^r[2]^i[t+3],r[3]^=Xr(Kr(e));for(let e=0;e<16;e+=4)t[e]=r[3-e/4]>>>24&255,t[e+1]=r[3-e/4]>>>16&255,t[e+2]=r[3-e/4]>>>8&255,t[e+3]=255&r[3-e/4]}function Qr(e,t,i){const r=new Array(4),s=new Array(4);for(let t=0;t<4;t++)s[0]=255&e[0+4*t],s[1]=255&e[1+4*t],s[2]=255&e[2+4*t],s[3]=255&e[3+4*t],r[t]=s[0]<<24|s[1]<<16|s[2]<<8|s[3];r[0]^=2746333894,r[1]^=1453994832,r[2]^=1736282519,r[3]^=2993693404;for(let e,i=0;i<32;i+=4)e=r[1]^r[2]^r[3]^Vr[i+0],t[i+0]=r[0]^=Zr(Kr(e)),e=r[2]^r[3]^r[0]^Vr[i+1],t[i+1]=r[1]^=Zr(Kr(e)),e=r[3]^r[0]^r[1]^Vr[i+2],t[i+2]=r[2]^=Zr(Kr(e)),e=r[0]^r[1]^r[2]^Vr[i+3],t[i+3]=r[3]^=Zr(Kr(e));if(0===i)for(let e,i=0;i<16;i++)e=t[i],t[i]=t[31-i],t[31-i]=e}function es(e,t,i){let{padding:r="pkcs#7",mode:s,iv:a=[],output:n="string"}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if("cbc"===s&&("string"==typeof a&&(a=$r(a)),16!==a.length))throw new Error("iv is invalid");if("string"==typeof t&&(t=$r(t)),16!==t.length)throw new Error("key is invalid");if(e="string"==typeof e?0!==i?Yr(e):$r(e):[...e],("pkcs#5"===r||"pkcs#7"===r)&&0!==i){const t=Or-e.length%Or;for(let i=0;i<t;i++)e.push(t)}const o=new Array(32);Qr(t,o,i);const d=[];let l=a,c=e.length,u=0;for(;c>=Or;){const t=e.slice(u,u+16),r=new Array(16);if("cbc"===s)for(let e=0;e<Or;e++)0!==i&&(t[e]^=l[e]);Jr(t,r,o);for(let e=0;e<Or;e++)"cbc"===s&&0===i&&(r[e]^=l[e]),d[u+e]=r[e];"cbc"===s&&(l=0!==i?r:t),c-=Or,u+=Or}if(("pkcs#5"===r||"pkcs#7"===r)&&0===i){const e=d.length,t=d[e-1];for(let i=1;i<=t;i++)if(d[e-i]!==t)throw new Error("padding is invalid");d.splice(e-t,t)}return"array"!==n?0!==i?Wr(d):qr(d):d}function ts(e){return e[3]|e[2]<<8|e[1]<<16|e[0]<<24}function is(e,t){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const r=e.byteLength;let s=5;for(;s<r;){let a=ts(e.slice(s,s+4));if(a>r)break;let n=e[s+4],o=!1;if(i?(n=n>>>1&63,o=[0,1,2,3,4,5,6,7,8,9,16,17,18,19,20,21].includes(n)):(n&=31,o=1===n||5===n),o){const i=es(e.slice(s+4+2,s+4+a),t,0,{padding:"none",output:"array"});e.set(i,s+4+2)}s=s+4+a}return e}class rs{on(e,t,i){const r=this.e||(this.e={});return(r[e]||(r[e]=[])).push({fn:t,ctx:i}),this}once(e,t,i){const r=this;function s(){r.off(e,s);for(var a=arguments.length,n=new Array(a),o=0;o<a;o++)n[o]=arguments[o];t.apply(i,n)}return s._=t,this.on(e,s,i)}emit(e){const t=((this.e||(this.e={}))[e]||[]).slice();for(var i=arguments.length,r=new Array(i>1?i-1:0),s=1;s<i;s++)r[s-1]=arguments[s];for(let e=0;e<t.length;e+=1)t[e].fn.apply(t[e].ctx,r);return this}off(e,t){const i=this.e||(this.e={});if(!e)return Object.keys(i).forEach((e=>{delete i[e]})),void delete this.e;const r=i[e],s=[];if(r&&t)for(let e=0,i=r.length;e<i;e+=1)r[e].fn!==t&&r[e].fn._!==t&&s.push(r[e]);return s.length?i[e]=s:delete i[e],this}}const ss={init:0,findFirstStartCode:1,findSecondStartCode:2};class as extends rs{constructor(e){super(),this.player=e,this.isDestroyed=!1,this.reset()}destroy(){this.isDestroyed=!1,this.off(),this.reset()}reset(){this.stats=ss.init,this.tempBuffer=new Uint8Array(0),this.parsedOffset=0,this.versionLayer=0}dispatch(e,t){let i=new Uint8Array(this.tempBuffer.length+e.length);for(i.set(this.tempBuffer,0),i.set(e,this.tempBuffer.length),this.tempBuffer=i;!this.isDestroyed;){if(this.state==ss.Init){let e=!1;for(;this.tempBuffer.length-this.parsedOffset>=2&&!this.isDestroyed;)if(255==this.tempBuffer[this.parsedOffset]){if(!(!1&this.tempBuffer[this.parsedOffset+1])){this.versionLayer=this.tempBuffer[this.parsedOffset+1],this.state=ss.findFirstStartCode,this.fisrtStartCodeOffset=this.parsedOffset,this.parsedOffset+=2,e=!0;break}this.parsedOffset++}else this.parsedOffset++;if(e)continue;break}if(this.state==ss.findFirstStartCode){let e=!1;for(;this.tempBuffer.length-this.parsedOffset>=2&&!this.isDestroyed;)if(255==this.tempBuffer[this.parsedOffset]){if(this.tempBuffer[this.parsedOffset+1]==this.versionLayer){this.state=ss.findSecondStartCode,this.secondStartCodeOffset=this.parsedOffset,this.parsedOffset+=2,e=!0;break}this.parsedOffset++}else this.parsedOffset++;if(e)continue;break}if(this.state==ss.findSecondStartCode){let e=this.tempBuffer.slice(this.fisrtStartCodeOffset,this.secondStartCodeOffset);this.emit("data",e,t),this.tempBuffer=this.tempBuffer.slice(this.secondStartCodeOffset),this.fisrtStartCodeOffset=0,this.parsedOffset=2,this.state=ss.findFirstStartCode}}}}function ns(e,t,i){for(let r=2;r<e.length;++r){const s=r-2,a=t[s%t.length],n=i[s%i.length];e[r]=e[r]^a^n}return e}function os(e){return e[3]|e[2]<<8|e[1]<<16|e[0]<<24}function ds(e,t,i){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];const s=e.byteLength;let a=5;for(;a<s;){let n=os(e.slice(a,a+4));if(n>s)break;let o=e[a+4],d=!1;if(r?(o=o>>>1&63,d=[0,1,2,3,4,5,6,7,8,9,16,17,18,19,20,21].includes(o)):(o&=31,d=1===o||5===o),d){const r=ns(e.slice(a+4,a+4+n),t,i);e.set(r,a+4)}a=a+4+n}return e}function ls(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];if((t=t.filter(Boolean)).length<2)return t[0];const r=new Uint8Array(t.reduce(((e,t)=>e+t.byteLength),0));let s=0;return t.forEach((e=>{r.set(e,s),s+=e.byteLength})),r}class cs{constructor(e){this.destroys=[],this.proxy=this.proxy.bind(this),this.master=e}proxy(e,t,i){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if(!e)return;if(Array.isArray(t))return t.map((t=>this.proxy(e,t,i,r)));e.addEventListener(t,i,r);const s=()=>{oi(e.removeEventListener)&&e.removeEventListener(t,i,r)};return this.destroys.push(s),s}destroy(){this.master.debug&&this.master.debug.log("Events","destroy"),this.destroys.forEach((e=>e())),this.destroys=[]}}class us{static init(){us.types={avc1:[],avcC:[],hvc1:[],hvcC:[],av01:[],av1C:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[],".mp3":[],Opus:[],dOps:[],"ac-3":[],dac3:[],"ec-3":[],dec3:[]};for(let e in us.types)us.types.hasOwnProperty(e)&&(us.types[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]);let e=us.constants={};e.FTYP=new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,97,118,99,49]),e.STSD_PREFIX=new Uint8Array([0,0,0,0,0,0,0,1]),e.STTS=new Uint8Array([0,0,0,0,0,0,0,0]),e.STSC=e.STCO=e.STTS,e.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),e.HDLR_VIDEO=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),e.HDLR_AUDIO=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),e.DREF=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),e.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),e.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])}static box(e){let t=8,i=null,r=Array.prototype.slice.call(arguments,1),s=r.length;for(let e=0;e<s;e++)t+=r[e].byteLength;i=new Uint8Array(t),i[0]=t>>>24&255,i[1]=t>>>16&255,i[2]=t>>>8&255,i[3]=255&t,i.set(e,4);let a=8;for(let e=0;e<s;e++)i.set(r[e],a),a+=r[e].byteLength;return i}static generateInitSegment(e){let t=us.box(us.types.ftyp,us.constants.FTYP),i=us.moov(e),r=new Uint8Array(t.byteLength+i.byteLength);return r.set(t,0),r.set(i,t.byteLength),r}static moov(e){let t=us.mvhd(e.timescale,e.duration),i=us.trak(e),r=us.mvex(e);return us.box(us.types.moov,t,i,r)}static mvhd(e,t){return us.box(us.types.mvhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,e>>>24&255,e>>>16&255,e>>>8&255,255&e,t>>>24&255,t>>>16&255,t>>>8&255,255&t,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]))}static trak(e){return us.box(us.types.trak,us.tkhd(e),us.mdia(e))}static tkhd(e){let t=e.id,i=e.duration,r=e.presentWidth,s=e.presentHeight;return us.box(us.types.tkhd,new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t,0,0,0,0,i>>>24&255,i>>>16&255,i>>>8&255,255&i,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,r>>>8&255,255&r,0,0,s>>>8&255,255&s,0,0]))}static mdia(e){return us.box(us.types.mdia,us.mdhd(e),us.hdlr(e),us.minf(e))}static mdhd(e){let t=e.timescale,i=e.duration;return us.box(us.types.mdhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t,i>>>24&255,i>>>16&255,i>>>8&255,255&i,85,196,0,0]))}static hdlr(e){let t=null;return t="audio"===e.type?us.constants.HDLR_AUDIO:us.constants.HDLR_VIDEO,us.box(us.types.hdlr,t)}static minf(e){let t=null;return t="audio"===e.type?us.box(us.types.smhd,us.constants.SMHD):us.box(us.types.vmhd,us.constants.VMHD),us.box(us.types.minf,t,us.dinf(),us.stbl(e))}static dinf(){return us.box(us.types.dinf,us.box(us.types.dref,us.constants.DREF))}static stbl(e){return us.box(us.types.stbl,us.stsd(e),us.box(us.types.stts,us.constants.STTS),us.box(us.types.stsc,us.constants.STSC),us.box(us.types.stsz,us.constants.STSZ),us.box(us.types.stco,us.constants.STCO))}static stsd(e){return"audio"===e.type?"mp3"===e.audioType?us.box(us.types.stsd,us.constants.STSD_PREFIX,us.mp3(e)):us.box(us.types.stsd,us.constants.STSD_PREFIX,us.mp4a(e)):"avc"===e.videoType?us.box(us.types.stsd,us.constants.STSD_PREFIX,us.avc1(e)):us.box(us.types.stsd,us.constants.STSD_PREFIX,us.hvc1(e))}static mp3(e){let t=e.channelCount,i=e.audioSampleRate,r=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,t,0,16,0,0,0,0,i>>>8&255,255&i,0,0]);return us.box(us.types[".mp3"],r)}static mp4a(e){let t=e.channelCount,i=e.audioSampleRate,r=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,t,0,16,0,0,0,0,i>>>8&255,255&i,0,0]);return us.box(us.types.mp4a,r,us.esds(e))}static esds(e){let t=e.config||[],i=t.length,r=new Uint8Array([0,0,0,0,3,23+i,0,1,0,4,15+i,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([i]).concat(t).concat([6,1,2]));return us.box(us.types.esds,r)}static avc1(e){let t=e.avcc;const i=e.codecWidth,r=e.codecHeight;let s=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,i>>>8&255,255&i,r>>>8&255,255&r,0,72,0,0,0,72,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return us.box(us.types.avc1,s,us.box(us.types.avcC,t))}static hvc1(e){let t=e.avcc;const i=e.codecWidth,r=e.codecHeight;let s=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,i>>>8&255,255&i,r>>>8&255,255&r,0,72,0,0,0,72,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return us.box(us.types.hvc1,s,us.box(us.types.hvcC,t))}static mvex(e){return us.box(us.types.mvex,us.trex(e))}static trex(e){let t=e.id,i=new Uint8Array([0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return us.box(us.types.trex,i)}static moof(e,t){return us.box(us.types.moof,us.mfhd(e.sequenceNumber),us.traf(e,t))}static mfhd(e){let t=new Uint8Array([0,0,0,0,e>>>24&255,e>>>16&255,e>>>8&255,255&e]);return us.box(us.types.mfhd,t)}static traf(e,t){let i=e.id,r=us.box(us.types.tfhd,new Uint8Array([0,0,0,0,i>>>24&255,i>>>16&255,i>>>8&255,255&i])),s=us.box(us.types.tfdt,new Uint8Array([0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t])),a=us.sdtp(e),n=us.trun(e,a.byteLength+16+16+8+16+8+8);return us.box(us.types.traf,r,s,n,a)}static sdtp(e){let t=new Uint8Array(5),i=e.flags;return t[4]=i.isLeading<<6|i.dependsOn<<4|i.isDependedOn<<2|i.hasRedundancy,us.box(us.types.sdtp,t)}static trun(e,t){let i=new Uint8Array(28);t+=36,i.set([0,0,15,1,0,0,0,1,t>>>24&255,t>>>16&255,t>>>8&255,255&t],0);let r=e.duration,s=e.size,a=e.flags,n=e.cts;return i.set([r>>>24&255,r>>>16&255,r>>>8&255,255&r,s>>>24&255,s>>>16&255,s>>>8&255,255&s,a.isLeading<<2|a.dependsOn,a.isDependedOn<<6|a.hasRedundancy<<4|a.isNonSync,0,0,n>>>24&255,n>>>16&255,n>>>8&255,255&n],12),us.box(us.types.trun,i)}static mdat(e){return us.box(us.types.mdat,e)}}us.init();var hs,fs=zt((function(e){e.exports=function(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e},e.exports.__esModule=!0,e.exports.default=e.exports}));(hs=fs)&&hs.__esModule&&Object.prototype.hasOwnProperty.call(hs,"default")&&hs.default;const ps=[44100,48e3,32e3,0],_s=[22050,24e3,16e3,0],ms=[11025,12e3,8e3,0],gs=[0,32,64,96,128,160,192,224,256,288,320,352,384,416,448,-1],ys=[0,32,48,56,64,80,96,112,128,160,192,224,256,320,384,-1],bs=[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320,-1];function vs(e){if(e.length<4)return void console.error("Invalid MP3 packet, header missing!");let t=new Uint8Array(e.buffer),i=null;if(255!==t[0])return void console.error("Invalid MP3 packet, first byte != 0xFF ");let r=t[1]>>>3&3,s=(6&t[1])>>1,a=(240&t[2])>>>4,n=(12&t[2])>>>2,o=3!==(t[3]>>>6&3)?2:1,d=0,l=0;switch(r){case 0:d=ms[n];break;case 2:d=_s[n];break;case 3:d=ps[n]}switch(s){case 1:a<bs.length&&(l=bs[a]);break;case 2:a<ys.length&&(l=ys[a]);break;case 3:a<gs.length&&(l=gs[a])}return i={bitRate:l,samplingRate:d,channelCount:o,codec:"mp3",originalCodec:"mp3",audioType:"mp3"},i}const Ss=3,ws=4,As=6,Bs=15,xs=17,Us=129,Es=135,Ts=21,ks=134,Cs=27,Ds=36;class Is{constructor(){this.slices=[],this.total_length=0,this.expected_length=0,this.random_access_indicator=0}}class Ls{constructor(){this.pid=null,this.data=null,this.stream_type=null,this.random_access_indicator=null}}class Fs{constructor(){this.pid=null,this.stream_id=null,this.len=null,this.data=null,this.pts=null,this.nearest_pts=null,this.dts=null}}const Ps=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350];class Ms{constructor(){this.mimeType=null,this.duration=null,this.hasAudio=null,this.hasVideo=null,this.audioCodec=null,this.videoCodec=null,this.audioDataRate=null,this.videoDataRate=null,this.audioSampleRate=null,this.audioChannelCount=null,this.width=null,this.height=null,this.fps=null,this.profile=null,this.level=null,this.refFrames=null,this.chromaFormat=null,this.sarNum=null,this.sarDen=null,this.metadata=null,this.segments=null,this.segmentCount=null,this.hasKeyframesIndex=null,this.keyframesIndex=null}isComplete(){let e=!1===this.hasAudio||!0===this.hasAudio&&null!=this.audioCodec&&null!=this.audioSampleRate&&null!=this.audioChannelCount,t=!1===this.hasVideo||!0===this.hasVideo&&null!=this.videoCodec&&null!=this.width&&null!=this.height&&null!=this.fps&&null!=this.profile&&null!=this.level&&null!=this.refFrames&&null!=this.chromaFormat&&null!=this.sarNum&&null!=this.sarDen;return null!=this.mimeType&&e&&t}isSeekable(){return!0===this.hasKeyframesIndex}getNearestKeyframe(e){if(null==this.keyframesIndex)return null;let t=this.keyframesIndex,i=this._search(t.times,e);return{index:i,milliseconds:t.times[i],fileposition:t.filepositions[i]}}_search(e,t){let i=0,r=e.length-1,s=0,a=0,n=r;for(t<e[0]&&(i=0,a=n+1);a<=n;){if(s=a+Math.floor((n-a)/2),s===r||t>=e[s]&&t<e[s+1]){i=s;break}e[s]<t?a=s+1:n=s-1}return i}}class zs{constructor(e){let t=null,i=e.audio_object_type,r=e.audio_object_type,s=e.sampling_freq_index,a=e.channel_config,n=0,o=navigator.userAgent.toLowerCase();-1!==o.indexOf("firefox")?s>=6?(r=5,t=new Array(4),n=s-3):(r=2,t=new Array(2),n=s):-1!==o.indexOf("android")?(r=2,t=new Array(2),n=s):(r=5,n=s,t=new Array(4),s>=6?n=s-3:1===a&&(r=2,t=new Array(2),n=s)),t[0]=r<<3,t[0]|=(15&s)>>>1,t[1]=(15&s)<<7,t[1]|=(15&a)<<3,5===r&&(t[1]|=(15&n)>>>1,t[2]=(1&n)<<7,t[2]|=8,t[3]=0),this.config=t,this.sampling_rate=Ps[s],this.sampling_index=s,this.channel_count=a,this.object_type=r,this.original_object_type=i,this.codec_mimetype="mp4a.40."+r,this.original_codec_mimetype="mp4a.40."+i}}Date.now||(Date.now=function(){return(new Date).getTime()}),function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=[],s=[],a={},n=new AbortController,o=null,d=null,l=null,c=null,b=!1,v=null,B=null,ge=!1,ye=!1,Oe=!!_i(i),qe=!1,rt=null,ct=null,ut=null,ht=[],ft=null,vt=null,St=0,Bt=0,Mt=null,zt=null,Rt=0,Nt=0,Gt=!1,Wt=!1,Kt=!1,ti=null,yi=null,bi=null,vi=!1,Si=!0,xi=()=>{const e=fi();return{debug:e.debug,debugLevel:e.debugLevel,debugUuid:e.debugUuid,useOffscreen:e.useOffscreen,useWCS:e.useWCS,useMSE:e.useMSE,videoBuffer:e.videoBuffer,videoBufferDelay:e.videoBufferDelay,openWebglAlignment:e.openWebglAlignment,playType:e.playType,hasAudio:e.hasAudio,hasVideo:e.hasVideo,playbackRate:1,playbackForwardMaxRateDecodeIFrame:e.playbackForwardMaxRateDecodeIFrame,playbackIsCacheBeforeDecodeForFpsRender:e.playbackConfig.isCacheBeforeDecodeForFpsRender,sampleRate:0,networkDelay:e.networkDelay,visibility:!0,useSIMD:e.useSIMD,isRecording:!1,recordType:e.recordType,isNakedFlow:e.isNakedFlow,checkFirstIFrame:e.checkFirstIFrame,audioBufferSize:1024,isM7sCrypto:e.isM7sCrypto,m7sCryptoAudio:e.m7sCryptoAudio,cryptoKey:e.cryptoKey,cryptoIV:e.cryptoIV,isSm4Crypto:e.isSm4Crypto,sm4CryptoKey:e.sm4CryptoKey,isXorCrypto:e.isXorCrypto,isHls265:!1,isFlv:e.isFlv,isFmp4:e.isFmp4,isMpeg4:e.isMpeg4,isTs:e.isTs,isFmp4Private:e.isFmp4Private,isEmitSEI:e.isEmitSEI,isRecordTypeFlv:!1,isWasmMp4:!1,isChrome:!1,isDropSameTimestampGop:e.isDropSameTimestampGop,mseDecodeAudio:e.mseDecodeAudio,nakedFlowH265DemuxUseNew:e.nakedFlowH265DemuxUseNew,mseDecoderUseWorker:e.mseDecoderUseWorker,mseAutoCleanupSourceBuffer:e.mseAutoCleanupSourceBuffer,mseAutoCleanupMaxBackwardDuration:e.mseAutoCleanupMaxBackwardDuration,mseAutoCleanupMinBackwardDuration:e.mseAutoCleanupMinBackwardDuration,mseCorrectTimeDuration:e.mseCorrectTimeDuration,mseCorrectAudioTimeDuration:e.mseCorrectAudioTimeDuration}};"VideoEncoder"in self&&(a={hasInit:!1,isEmitInfo:!1,offscreenCanvas:null,offscreenCanvasCtx:null,decoder:new VideoDecoder({output:function(e){if(a.isEmitInfo||(rr.debug.log("worker","Webcodecs Video Decoder initSize"),postMessage({cmd:U,w:e.codedWidth,h:e.codedHeight}),a.isEmitInfo=!0,a.offscreenCanvas=new OffscreenCanvas(e.codedWidth,e.codedHeight),a.offscreenCanvasCtx=a.offscreenCanvas.getContext("2d")),oi(e.createImageBitmap))e.createImageBitmap().then((t=>{a.offscreenCanvasCtx.drawImage(t,0,0,e.codedWidth,e.codedHeight);let i=a.offscreenCanvas.transferToImageBitmap();postMessage({cmd:E,buffer:i,delay:rr.delay,ts:0},[i]),ci(e)}));else{a.offscreenCanvasCtx.drawImage(e,0,0,e.codedWidth,e.codedHeight);let t=a.offscreenCanvas.transferToImageBitmap();postMessage({cmd:E,buffer:t,delay:rr.delay,ts:0},[t]),ci(e)}},error:function(e){rr.debug.error("worker","VideoDecoder error",e)}}),decode:function(e,t,i){const r=e[0]>>4==1;if(a.hasInit){const i=new EncodedVideoChunk({data:e.slice(5),timestamp:t,type:r?nt:ot});a.decoder.decode(i)}else if(r&&0===e[1]){const t=15&e[0];postMessage({cmd:L,code:t});const i=new Uint8Array(e);postMessage({cmd:F,buffer:i,codecId:t},[i.buffer]);let r=null,s=null;const n=e.slice(5);t===Ce?(s=Ui(n),r={codec:s.codec,description:n}):t===De&&(s=Hi(n),r={codec:s.codec,description:n}),s&&s.codecWidth&&s.codecHeight&&(r.codedHeight=s.codecHeight,r.codedWidth=s.codecWidth);try{a.decoder.configure(r),a.hasInit=!0}catch(e){rr.debug.log("worker","VideoDecoder configure error",e.code,e)}}},reset(){a.hasInit=!1,a.isEmitInfo=!1,a.offscreenCanvas=null,a.offscreenCanvasCtx=null}});let Ri=function(){if(vi=!0,rr.fetchStatus!==Et||mi(rr._opt.isChrome)){if(n)try{n.abort(),n=null}catch(e){rr.debug.log("worker","abort catch",e)}}else n=null,rr.debug.log("worker",`abort() and not abortController.abort() _status is ${rr.fetchStatus} and _isChrome is ${rr._opt.isChrome}`)},Ni={init(){Ni.lastBuf=null,Ni.vps=null,Ni.sps=null,Ni.pps=null,Ni.streamType=null,Ni.localDts=0,Ni.isSendSeqHeader=!1},destroy(){Ni.lastBuf=null,Ni.vps=null,Ni.sps=null,Ni.pps=null,Ni.streamType=null,Ni.localDts=0,Ni.isSendSeqHeader=!1},dispatch(e){const t=new Uint8Array(e);Ni.extractNALu$2(t)},getNaluDts(){let e=Ni.localDts;return Ni.localDts=Ni.localDts+40,e},getNaluAudioDts(){const e=rr._opt.sampleRate,t=rr._opt.audioBufferSize;return Ni.localDts+parseInt(t/e*1e3)},extractNALu(e){let t,i,r=0,s=e.byteLength,a=0,n=[];for(;r<s;)switch(t=e[r++],a){case 0:0===t&&(a=1);break;case 1:a=0===t?2:0;break;case 2:case 3:0===t?a=3:1===t&&r<s?(i&&n.push(e.subarray(i,r-a-1)),i=r,a=0):a=0}return i&&n.push(e.subarray(i,s)),n},extractNALu$2(e){let t=null;if(!e||e.byteLength<1)return;Ni.lastBuf?(t=new Uint8Array(e.byteLength+Ni.lastBuf.length),t.set(Ni.lastBuf),t.set(new Uint8Array(e),Ni.lastBuf.length)):t=new Uint8Array(e);let i=0,r=-1,s=-2;const a=new Array;for(let e=0;e<t.length;e+=2){const i=t[e],n=t[e+1];0==r&&0==i&&0==n?a.push(e-1):1==n&&0==i&&0==r&&0==s&&a.push(e-2),s=i,r=n}if(a.length>1)for(let e=0;e<a.length-1;++e){const r=t.subarray(a[e],a[e+1]+1);Ni.handleNALu(r),i=a[e+1]}else i=a[0];if(0!=i&&i<t.length)Ni.lastBuf=t.subarray(i);else{Ni.lastBuf||(Ni.lastBuf=t);const i=new Uint8Array(Ni.lastBuf.length+e.byteLength);i.set(Ni.lastBuf),i.set(new Uint8Array(e),Ni.lastBuf.length),Ni.lastBuf=i}},handleNALu(e){e.byteLength<=4?rr.debug.warn("worker",`handleNALu nalu byteLength is ${e.byteLength} <= 4`):(e=e.slice(4),Ni.handleVideoNalu(e))},handleVideoNalu(e){if(Ni.streamType||(Ni.streamType=di(e),ti=Ni.streamType===Fe),Ni.streamType===Le){const t=Ni.handleAddNaluStartCode(e),i=Ni.extractNALu(t);if(0===i.length)return void rr.debug.warn("worker","handleVideoNalu","h264 naluList.length === 0");const r=[];if(i.forEach((e=>{const t=Di(e);t===Ge||t===Ne?Ni.handleVideoH264Nalu(e):Li(t)&&r.push(e)})),1===r.length)Ni.handleVideoH264Nalu(r[0]);else{if(Pi(r)){const e=Di(r[0]),t=Fi(e);Ni.handleVideoH264NaluList(r,t,e)}else r.forEach((e=>{Ni.handleVideoH264Nalu(e)}))}}else if(Ni.streamType===Fe)if(rr._opt.nakedFlowH265DemuxUseNew){const t=Ni.handleAddNaluStartCode(e),i=Ni.extractNALu(t);if(0===i.length)return void rr.debug.warn("worker","handleVideoNalu","h265 naluList.length === 0");const r=[];if(i.forEach((e=>{const t=qi(e);t===tt||t===Qe||t===Ze?Ni.handleVideoH265Nalu(e):Ki(t)&&r.push(e)})),1===r.length)Ni.handleVideoH265Nalu(r[0]);else{if(Zi(r)){const e=qi(r[0]),t=Xi(e);Ni.handleVideoH265NaluList(r,t,e)}else r.forEach((e=>{Ni.handleVideoH265Nalu(e)}))}}else{qi(e)===tt?Ni.extractH265PPS(e):Ni.handleVideoH265Nalu(e)}},extractH264PPS(e){const t=Ni.handleAddNaluStartCode(e);Ni.extractNALu(t).forEach((e=>{Ii(Di(e))?Ni.extractH264SEI(e):Ni.handleVideoH264Nalu(e)}))},extractH265PPS(e){const t=Ni.handleAddNaluStartCode(e);Ni.extractNALu(t).forEach((e=>{ji(qi(e))?Ni.extractH265SEI(e):Ni.handleVideoH265Nalu(e)}))},extractH264SEI(e){const t=Ni.handleAddNaluStartCode(e);Ni.extractNALu(t).forEach((e=>{Ni.handleVideoH264Nalu(e)}))},extractH265SEI(e){const t=Ni.handleAddNaluStartCode(e);Ni.extractNALu(t).forEach((e=>{Ni.handleVideoH265Nalu(e)}))},handleAddNaluStartCode(e){const t=[0,0,0,1],i=new Uint8Array(e.length+t.length);return i.set(t),i.set(e,t.length),i},handleVideoH264Nalu(e){const t=Di(e);switch(t){case Ne:Ni.sps=e;break;case Ge:Ni.pps=e}if(Ni.isSendSeqHeader){if(Ni.sps&&Ni.pps){const e=Ei({sps:Ni.sps,pps:Ni.pps}),t=Ni.getNaluDts();rr.decode(e,{type:re,ts:t,isIFrame:!0,cts:0}),Ni.sps=null,Ni.pps=null}if(Li(t)){const i=Fi(t),r=Ni.getNaluDts(),s=ki(e,i);Ni.doDecode(s,{type:re,ts:r,isIFrame:i,cts:0})}else rr.debug.warn("work",`handleVideoH264Nalu Avc Seq Head is ${t}`)}else if(Ni.sps&&Ni.pps){Ni.isSendSeqHeader=!0;const e=Ei({sps:Ni.sps,pps:Ni.pps});rr.decode(e,{type:re,ts:0,isIFrame:!0,cts:0}),Ni.sps=null,Ni.pps=null}},handleVideoH264NaluList(e,t,i){if(Ni.isSendSeqHeader){const r=Ni.getNaluDts(),s=Ci(e.reduce(((e,t)=>{const i=Zt(e),r=Zt(t),s=new Uint8Array(i.byteLength+r.byteLength);return s.set(i,0),s.set(r,i.byteLength),s})),t);Ni.doDecode(s,{type:re,ts:r,isIFrame:t,cts:0}),rr.debug.log("worker",`handleVideoH264NaluList list size is ${e.length} package length is ${s.byteLength} isIFrame is ${t},nalu type is ${i}, dts is ${r}`)}else rr.debug.warn("worker","handleVideoH264NaluList isSendSeqHeader is false")},handleVideoH265Nalu(e){const t=qi(e);switch(t){case Ze:Ni.vps=e;break;case Qe:Ni.sps=e;break;case tt:Ni.pps=e}if(Ni.isSendSeqHeader){if(Ni.vps&&Ni.sps&&Ni.pps){const e=$i({vps:Ni.vps,sps:Ni.sps,pps:Ni.pps}),t=Ni.getNaluDts();rr.decode(e,{type:re,ts:t,isIFrame:!0,cts:0}),Ni.vps=null,Ni.sps=null,Ni.pps=null}if(Ki(t)){const i=Xi(t),r=Ni.getNaluDts(),s=Wi(e,i);Ni.doDecode(s,{type:re,ts:r,isIFrame:i,cts:0})}else rr.debug.warn("work",`handleVideoH265Nalu HevcSeqHead is ${t}`)}else if(Ni.vps&&Ni.sps&&Ni.pps){Ni.isSendSeqHeader=!0;const e=$i({vps:Ni.vps,sps:Ni.sps,pps:Ni.pps});rr.decode(e,{type:re,ts:0,isIFrame:!0,cts:0}),Ni.vps=null,Ni.sps=null,Ni.pps=null}},handleVideoH265NaluList(e,t,i){if(Ni.isSendSeqHeader){const r=Ni.getNaluDts(),s=Yi(e.reduce(((e,t)=>{const i=Zt(e),r=Zt(t),s=new Uint8Array(i.byteLength+r.byteLength);return s.set(i,0),s.set(r,i.byteLength),s})),t);Ni.doDecode(s,{type:re,ts:r,isIFrame:t,cts:0}),rr.debug.log("worker",`handleVideoH265NaluList list size is ${e.length} package length is ${s.byteLength} isIFrame is ${t},nalu type is ${i}, dts is ${r}`)}else rr.debug.warn("worker","handleVideoH265NaluList isSendSeqHeader is false")},doDecode(e,t){rr.calcNetworkDelay(t.ts),t.isIFrame&&rr.calcIframeIntervalTimestamp(t.ts),rr.decode(e,t)}},Vi={LOG_NAME:"worker fmp4Demuxer",mp4Box:null,offset:0,videoTrackId:null,audioTrackId:null,isHevc:!1,listenMp4Box(){Vi.mp4Box=zr.createFile(),Vi.mp4Box.onReady=Vi.onReady,Vi.mp4Box.onError=Vi.onError,Vi.mp4Box.onSamples=Vi.onSamples},initTransportDescarmber(){Vi.transportDescarmber=new Gr},_getSeqHeader(e){const t=Vi.mp4Box.getTrackById(e.id);for(const e of t.mdia.minf.stbl.stsd.entries)if(e.avcC||e.hvcC){const t=new zr.DataStream(void 0,0,zr.DataStream.BIG_ENDIAN);let i=[];e.avcC?(e.avcC.write(t),i=[23,0,0,0,0]):(Vi.isHevc=!0,ti=!0,e.hvcC.write(t),i=[28,0,0,0,0]);const r=new Uint8Array(t.buffer,8),s=new Uint8Array(i.length+r.length);return s.set(i,0),s.set(r,i.length),s}return null},onReady(e){rr.debug.log(Vi.LOG_NAME,"onReady()");const t=e.videoTracks[0],i=e.audioTracks[0];if(t){Vi.videoTrackId=t.id;const e=Vi._getSeqHeader(t);e&&(rr.debug.log(Vi.LOG_NAME,"seqHeader"),rr.decodeVideo(e,0,!0,0)),Vi.mp4Box.setExtractionOptions(t.id)}if(i&&rr._opt.hasAudio){Vi.audioTrackId=i.id;const e=i.audio||{},t=Ot.indexOf(e.sample_rate),r=i.codec.replace("mp4a.40.","");Vi.mp4Box.setExtractionOptions(i.id);const s=Ht({profile:parseInt(r,10),sampleRate:t,channel:e.channel_count});rr.debug.log(Vi.LOG_NAME,"aacADTSHeader"),rr.decodeAudio(s,0)}Vi.mp4Box.start()},onError(e){rr.debug.error(Vi.LOG_NAME,"mp4Box onError",e)},onSamples(e,t,i){if(e===Vi.videoTrackId)for(const t of i){const i=t.data,r=t.is_sync,s=1e3*t.cts/t.timescale;t.duration,t.timescale,r&&rr.calcIframeIntervalTimestamp(s);let a=null;a=Vi.isHevc?Yi(i,r):Ci(i,r),rr.decode(a,{type:re,ts:s,isIFrame:r,cts:0}),Vi.mp4Box.releaseUsedSamples(e,t.number)}else if(e===Vi.audioTrackId){if(rr._opt.hasAudio)for(const t of i){const i=t.data,r=1e3*t.cts/t.timescale;t.duration,t.timescale;const s=new Uint8Array(i.byteLength+2);s.set([175,1],0),s.set(i,2),rr.decode(s,{type:ie,ts:r,isIFrame:!1,cts:0}),Vi.mp4Box.releaseUsedSamples(e,t.number)}}else rr.debug.warn(Vi.LOG_NAME,"onSamples() trackId error",e)},dispatch(e){let t=new Uint8Array(e);Vi.transportDescarmber&&(t=Vi.transportDescarmber.transport(t)),t.buffer.fileStart=Vi.offset,Vi.offset+=t.byteLength,Vi.mp4Box.appendBuffer(t.buffer)},destroy(){Vi.mp4Box&&(Vi.mp4Box.stop(),Vi.mp4Box.flush(),Vi.mp4Box.destroy(),Vi.mp4Box=null),Vi.transportDescarmber&&(Vi.transportDescarmber.destroy(),Vi.transportDescarmber=null),Vi.offset=0,Vi.videoTrackId=null,Vi.audioTrackId=null,Vi.isHevc=!1}},er={LOG_NAME:"worker mpeg4Demuxer",lastBuffer:new Uint8Array(0),parsedOffset:0,firstStartCodeOffset:0,secondStartCodeOffset:0,state:"init",hasInitVideoCodec:!1,localDts:0,dispatch(e){const t=new Uint8Array(e);er.extractNALu(t)},destroy(){er.lastBuffer=new Uint8Array(0),er.parsedOffset=0,er.firstStartCodeOffset=0,er.secondStartCodeOffset=0,er.state="init",er.hasInitVideoCodec=!1,er.localDts=0},extractNALu(e){if(!e||e.byteLength<1)return void rr.debug.warn(er.LOG_NAME,"extractNALu() buffer error",e);const t=new Uint8Array(er.lastBuffer.length+e.length);for(t.set(er.lastBuffer,0),t.set(new Uint8Array(e),er.lastBuffer.length),er.lastBuffer=t;;){if("init"===er.state){let e=!1;for(;er.lastBuffer.length-er.parsedOffset>=4;)if(0===er.lastBuffer[er.parsedOffset])if(0===er.lastBuffer[er.parsedOffset+1])if(1===er.lastBuffer[er.parsedOffset+2]){if(182===er.lastBuffer[er.parsedOffset+3]){er.state="findFirstStartCode",er.firstStartCodeOffset=er.parsedOffset,er.parsedOffset+=4,e=!0;break}er.parsedOffset++}else er.parsedOffset++;else er.parsedOffset++;else er.parsedOffset++;if(e)continue;break}if("findFirstStartCode"===er.state){let e=!1;for(;er.lastBuffer.length-er.parsedOffset>=4;)if(0===er.lastBuffer[er.parsedOffset])if(0===er.lastBuffer[er.parsedOffset+1])if(1===er.lastBuffer[er.parsedOffset+2]){if(182===er.lastBuffer[er.parsedOffset+3]){er.state="findSecondStartCode",er.secondStartCodeOffset=er.parsedOffset,er.parsedOffset+=4,e=!0;break}er.parsedOffset++}else er.parsedOffset++;else er.parsedOffset++;else er.parsedOffset++;if(e)continue;break}if("findSecondStartCode"===er.state){if(!(er.lastBuffer.length-er.parsedOffset>0))break;{let e,t,i=192&er.lastBuffer[er.parsedOffset];e=0==i?er.secondStartCodeOffset-14:er.secondStartCodeOffset;let r=0==(192&er.lastBuffer[er.firstStartCodeOffset+4]);if(r){if(er.firstStartCodeOffset-14<0)return void rr.debug.warn(er.LOG_NAME,"firstStartCodeOffset -14 is",er.firstStartCodeOffset-14);er.hasInitVideoCodec||(er.hasInitVideoCodec=!0,rr.debug.log(er.LOG_NAME,"setCodec"),ar.setCodec(Ie,"")),t=er.lastBuffer.subarray(er.firstStartCodeOffset-14,e)}else t=er.lastBuffer.subarray(er.firstStartCodeOffset,e);let s=er.getNaluDts();er.hasInitVideoCodec?(postMessage({cmd:R,type:we,value:t.byteLength}),postMessage({cmd:R,type:Ae,value:s}),ar.decode(t,r?1:0,s)):rr.debug.warn(er.LOG_NAME,"has not init video codec"),er.lastBuffer=er.lastBuffer.subarray(e),er.firstStartCodeOffset=0==i?14:0,er.parsedOffset=er.firstStartCodeOffset+4,er.state="findFirstStartCode"}}}},getNaluDts(){let e=er.localDts;return er.localDts=er.localDts+40,e}},tr={TAG_NAME:"worker TsLoaderV2",first_parse_:!0,tsPacketSize:0,syncOffset:0,pmt_:null,config_:null,media_info_:new Ms,timescale_:90,duration_:0,pat_:{version_number:0,network_pid:0,program_map_pid:{}},current_program_:null,current_pmt_pid_:-1,program_pmt_map_:{},pes_slice_queues_:{},section_slice_queues_:{},video_metadata_:{vps:null,sps:null,pps:null,details:null},audio_metadata_:{codec:null,audio_object_type:null,sampling_freq_index:null,sampling_frequency:null,channel_config:null},last_pcr_:null,audio_last_sample_pts_:void 0,aac_last_incomplete_data_:null,has_video_:!1,has_audio_:!1,video_init_segment_dispatched_:!1,audio_init_segment_dispatched_:!1,video_metadata_changed_:!1,audio_metadata_changed_:!1,loas_previous_frame:null,video_track_:{type:"video",id:1,sequenceNumber:0,samples:[],length:0},audio_track_:{type:"audio",id:2,sequenceNumber:0,samples:[],length:0},_remainingPacketData:null,init(){},destroy(){tr.media_info_=null,tr.pes_slice_queues_=null,tr.section_slice_queues_=null,tr.video_metadata_=null,tr.audio_metadata_=null,tr.aac_last_incomplete_data_=null,tr.video_track_=null,tr.audio_track_=null,tr._remainingPacketData=null},probe(e){let t=new Uint8Array(e),i=-1,r=188;if(t.byteLength<=3*r)return{needMoreData:!0};for(;-1===i;){let e=Math.min(1e3,t.byteLength-3*r);for(let s=0;s<e;){if(71===t[s]&&71===t[s+r]&&71===t[s+2*r]){i=s;break}s++}if(-1===i)if(188===r)r=192;else{if(192!==r)break;r=204}}return-1===i?{match:!1}:(192===r&&i>=4&&(i-=4),{match:!0,consumed:0,ts_packet_size:r,sync_offset:i})},_initPmt:()=>({program_number:0,version_number:0,pcr_pid:0,pid_stream_type:{},common_pids:{h264:void 0,h265:void 0,adts_aac:void 0,loas_aac:void 0,opus:void 0,ac3:void 0,eac3:void 0,mp3:void 0},pes_private_data_pids:{},timed_id3_pids:{},synchronous_klv_pids:{},asynchronous_klv_pids:{},scte_35_pids:{},smpte2038_pids:{}}),dispatch(e){tr._remainingPacketData&&(e=ls(tr._remainingPacketData,e),tr._remainingPacketData=null);let t=e.buffer;const i=tr.parseChunks(t);i?tr._remainingPacketData=e.subarray(i):e.length<this.tsPacketSize&&(tr._remainingPacketData=e)},parseChunks(e){let t=0;if(tr.first_parse_){tr.first_parse_=!1;const i=tr.probe(e);i.match&&(tr.tsPacketSize=i.ts_packet_size,tr.syncOffset=i.sync_offset),t=tr.syncOffset,rr.debug.log(tr.TAG_NAME,`isFirstDispatch and tsPacketSize = ${tr.tsPacketSize}, syncOffset = ${tr.syncOffset}`)}for(;t+tr.tsPacketSize<=e.byteLength;){192===tr.tsPacketSize&&(t+=4);const i=new Uint8Array(e,t,188);let r=i[0];if(71!==r){rr.debug.warn(tr.TAG_NAME,`sync_byte = ${r}, not 0x47`);break}let s=(64&i[1])>>>6;i[1];let a=(31&i[1])<<8|i[2],n=(48&i[3])>>>4,o=15&i[3],d=!(!tr.pmt_||tr.pmt_.pcr_pid!==a),l={},c=4;if(2==n||3==n){let e=i[4];if(e>0&&(d||3==n)){if(l.discontinuity_indicator=(128&i[5])>>>7,l.random_access_indicator=(64&i[5])>>>6,l.elementary_stream_priority_indicator=(32&i[5])>>>5,(16&i[5])>>>4){let e=300*(i[6]<<25|i[7]<<17|i[8]<<9|i[9]<<1|i[10]>>>7)+((1&i[10])<<8|i[11]);tr.last_pcr_=e}}if(2==n||5+e===188){t+=188,204===tr.tsPacketSize&&(t+=16);continue}c=5+e}if(1==n||3==n)if(0===a||a===tr.current_pmt_pid_||null!=tr.pmt_&&tr.pmt_.pid_stream_type[a]===ks){let i=188-c;tr.handleSectionSlice(e,t+c,i,{pid:a,payload_unit_start_indicator:s,continuity_conunter:o,random_access_indicator:l.random_access_indicator})}else if(null!=tr.pmt_&&null!=tr.pmt_.pid_stream_type[a]){let i=188-c,r=tr.pmt_.pid_stream_type[a];a!==tr.pmt_.common_pids.h264&&a!==tr.pmt_.common_pids.h265&&a!==tr.pmt_.common_pids.adts_aac&&a!==tr.pmt_.common_pids.loas_aac&&a!==tr.pmt_.common_pids.ac3&&a!==tr.pmt_.common_pids.eac3&&a!==tr.pmt_.common_pids.opus&&a!==tr.pmt_.common_pids.mp3&&!0!==tr.pmt_.pes_private_data_pids[a]&&!0!==tr.pmt_.timed_id3_pids[a]&&!0!==tr.pmt_.synchronous_klv_pids[a]&&!0!==tr.pmt_.asynchronous_klv_pids[a]||tr.handlePESSlice(e,t+c,i,{pid:a,stream_type:r,payload_unit_start_indicator:s,continuity_conunter:o,random_access_indicator:l.random_access_indicator})}t+=188,204===tr.tsPacketSize&&(t+=16)}return tr.dispatchAudioVideoMediaSegment(),t},handleSectionSlice(e,t,i,r){let s=new Uint8Array(e,t,i),a=tr.section_slice_queues_[r.pid];if(r.payload_unit_start_indicator){let n=s[0];if(null!=a&&0!==a.total_length){let s=new Uint8Array(e,t+1,Math.min(i,n));a.slices.push(s),a.total_length+=s.byteLength,a.total_length===a.expected_length?tr.emitSectionSlices(a,r):tr.clearSlices(a,r)}for(let o=1+n;o<s.byteLength;){if(255===s[o+0])break;let n=(15&s[o+1])<<8|s[o+2];tr.section_slice_queues_[r.pid]=new Is,a=tr.section_slice_queues_[r.pid],a.expected_length=n+3,a.random_access_indicator=r.random_access_indicator;let d=new Uint8Array(e,t+o,Math.min(i-o,a.expected_length-a.total_length));a.slices.push(d),a.total_length+=d.byteLength,a.total_length===a.expected_length?tr.emitSectionSlices(a,r):a.total_length>=a.expected_length&&tr.clearSlices(a,r),o+=d.byteLength}}else if(null!=a&&0!==a.total_length){let s=new Uint8Array(e,t,Math.min(i,a.expected_length-a.total_length));a.slices.push(s),a.total_length+=s.byteLength,a.total_length===a.expected_length?tr.emitSectionSlices(a,r):a.total_length>=a.expected_length&&tr.clearSlices(a,r)}},handlePESSlice(e,t,i,r){let s=new Uint8Array(e,t,i),a=s[0]<<16|s[1]<<8|s[2];s[3];let n=s[4]<<8|s[5];if(r.payload_unit_start_indicator){if(1!==a)return void rr.debug.warn(tr.TAG_NAME,`handlePESSlice: packet_start_code_prefix should be 1 but with value ${a}`);let e=tr.pes_slice_queues_[r.pid];e&&(0===e.expected_length||e.expected_length===e.total_length?tr.emitPESSlices(e,r):tr.clearSlices(e,r)),tr.pes_slice_queues_[r.pid]=new Is,tr.pes_slice_queues_[r.pid].random_access_indicator=r.random_access_indicator}if(null==tr.pes_slice_queues_[r.pid])return;let o=tr.pes_slice_queues_[r.pid];o.slices.push(s),r.payload_unit_start_indicator&&(o.expected_length=0===n?0:n+6),o.total_length+=s.byteLength,o.expected_length>0&&o.expected_length===o.total_length?tr.emitPESSlices(o,r):o.expected_length>0&&o.expected_length<o.total_length&&tr.clearSlices(o,r)},emitSectionSlices(e,t){let i=new Uint8Array(e.total_length);for(let t=0,r=0;t<e.slices.length;t++){let s=e.slices[t];i.set(s,r),r+=s.byteLength}e.slices=[],e.expected_length=-1,e.total_length=0;let r={};r.pid=t.pid,r.data=i,r.file_position=e.file_position,r.random_access_indicator=e.random_access_indicator,tr.parseSection(r)},emitPESSlices(e,t){let i=new Uint8Array(e.total_length);for(let t=0,r=0;t<e.slices.length;t++){let s=e.slices[t];i.set(s,r),r+=s.byteLength}e.slices=[],e.expected_length=-1,e.total_length=0;let r=new Ls;r.pid=t.pid,r.data=i,r.stream_type=t.stream_type,r.random_access_indicator=e.random_access_indicator,tr.parsePES(r)},clearSlices(e){e.slices=[],e.expected_length=-1,e.total_length=0},parseSection(e){let t=e.data,i=e.pid;0===i?tr.parsePAT(t):i===tr.current_pmt_pid_?tr.parsePMT(t):null!=tr.pmt_&&tr.pmt_.scte_35_pids[i]},parsePES(e){let t=e.data,i=t[0]<<16|t[1]<<8|t[2],r=t[3],s=t[4]<<8|t[5];if(1===i){if(188!==r&&190!==r&&191!==r&&240!==r&&241!==r&&255!==r&&242!==r&&248!==r){t[6];let i,a,n=(192&t[7])>>>6,o=t[8];2!==n&&3!==n||(i=536870912*(14&t[9])+4194304*(255&t[10])+16384*(254&t[11])+128*(255&t[12])+(254&t[13])/2,a=3===n?536870912*(14&t[14])+4194304*(255&t[15])+16384*(254&t[16])+128*(255&t[17])+(254&t[18])/2:i);let d,l=9+o;if(0!==s){if(s<3+o)return void rr.debug.warn(tr.TAG_NAME,"Malformed PES: PES_packet_length < 3 + PES_header_data_length");d=s-3-o}else d=t.byteLength-l;let c=t.subarray(l,l+d);switch(e.stream_type){case Ss:case ws:tr.parseMP3Payload(c,i);break;case As:tr.pmt_.common_pids.opus===e.pid||tr.pmt_.common_pids.ac3===e.pid||tr.pmt_.common_pids.eac3===e.pid||(tr.pmt_.asynchronous_klv_pids[e.pid]?tr.parseAsynchronousKLVMetadataPayload(c,e.pid,r):tr.pmt_.smpte2038_pids[e.pid]?tr.parseSMPTE2038MetadataPayload(c,i,a,e.pid,r):tr.parsePESPrivateDataPayload(c,i,a,e.pid,r));break;case Bs:tr.parseADTSAACPayload(c,i);break;case xs:tr.parseLOASAACPayload(c,i);break;case Us:case Es:break;case Ts:tr.pmt_.timed_id3_pids[e.pid]?tr.parseTimedID3MetadataPayload(c,i,a,e.pid,r):tr.pmt_.synchronous_klv_pids[e.pid]&&tr.parseSynchronousKLVMetadataPayload(c,i,a,e.pid,r);break;case Cs:tr.parseH264Payload(c,i,a,e.random_access_indicator);break;case Ds:tr.parseH265Payload(c,i,a,e.random_access_indicator)}}else if((188===r||191===r||240===r||241===r||255===r||242===r||248===r)&&e.stream_type===As){let i,a=6;i=0!==s?s:t.byteLength-a;let n=t.subarray(a,a+i);tr.parsePESPrivateDataPayload(n,void 0,void 0,e.pid,r)}}else rr.debug.error(tr.TAG_NAME,`parsePES: packet_start_code_prefix should be 1 but with value ${i}`)},parsePAT(e){let t=e[0];if(0!==t)return void Log.e(tr.TAG,`parsePAT: table_id ${t} is not corresponded to PAT!`);let i=(15&e[1])<<8|e[2];e[3],e[4];let r=(62&e[5])>>>1,s=1&e[5],a=e[6];e[7];let n=null;if(1===s&&0===a)n={version_number:0,network_pid:0,program_pmt_pid:{}},n.version_number=r;else if(n=tr.pat_,null==n)return;let o=i-5-4,d=-1,l=-1;for(let t=8;t<8+o;t+=4){let i=e[t]<<8|e[t+1],r=(31&e[t+2])<<8|e[t+3];0===i?n.network_pid=r:(n.program_pmt_pid[i]=r,-1===d&&(d=i),-1===l&&(l=r))}1===s&&0===a&&(null==tr.pat_&&rr.debug.log(tr.TAG_NAME,`Parsed first PAT: ${JSON.stringify(n)}`),tr.pat_=n,tr.current_program_=d,tr.current_pmt_pid_=l)},parsePMT(e){let t=e[0];if(2!==t)return void rr.debug.error(tr.TAG_NAME,`parsePMT: table_id ${t} is not corresponded to PMT!`);let i,r=(15&e[1])<<8|e[2],s=e[3]<<8|e[4],a=(62&e[5])>>>1,n=1&e[5],o=e[6];if(e[7],1===n&&0===o)i=tr._initPmt(),i.program_number=s,i.version_number=a,tr.program_pmt_map_[s]=i;else if(i=tr.program_pmt_map_[s],null==i)return;i.pcr_pid=(31&e[8])<<8|e[9];let d=(15&e[10])<<8|e[11],l=12+d,c=r-9-d-4;for(let t=l;t<l+c;){let r=e[t],s=(31&e[t+1])<<8|e[t+2],a=(15&e[t+3])<<8|e[t+4];i.pid_stream_type[s]=r;let n=i.common_pids.h264||i.common_pids.h265,o=i.common_pids.adts_aac||i.common_pids.loas_aac||i.common_pids.ac3||i.common_pids.eac3||i.common_pids.opus||i.common_pids.mp3;if(r!==Cs||n)if(r!==Ds||n)if(r!==Bs||o)if(r!==xs||o)if(r!==Us||o)if(r!==Es||o)if(r!==Ss&&r!==ws||o)if(r===As){if(i.pes_private_data_pids[s]=!0,a>0){for(let r=t+5;r<t+5+a;){let t=e[r+0],a=e[r+1];if(5===t){let t=String.fromCharCode(...Array.from(e.subarray(r+2,r+2+a)));"VANC"===t?i.smpte2038_pids[s]=!0:"Opus"===t?i.common_pids.opus=s:"KLVA"===t&&(i.asynchronous_klv_pids[s]=!0)}else if(127===t&&s===i.common_pids.opus){let t=null;if(128===e[r+2]&&(t=e[r+3]),null==t){Log.e(tr.TAG,"Not Supported Opus channel count.");continue}const i={codec:"opus",channel_count:0==(15&t)?2:15&t,channel_config_code:t,sample_rate:48e3},s={codec:"opus",meta:i};0==tr.audio_init_segment_dispatched_?(tr.audio_metadata_=i,tr.dispatchAudioInitSegment(s)):tr.detectAudioMetadataChange(s)&&(tr.dispatchAudioMediaSegment(),tr.dispatchAudioInitSegment(s))}r+=2+a}e.subarray(t+5,t+5+a)}}else if(r===Ts){if(a>0)for(let r=t+5;r<t+5+a;){let t=e[r+0],a=e[r+1];if(38===t){let t=e[r+2]<<8|e[r+3]<<0,a=null;65535===t&&(a=String.fromCharCode(...Array.from(e.subarray(r+4,r+4+4))));let n=null;if(255===e[r+4+(65535===t?4:0)]){let i=4+(65535===t?4:0)+1;n=String.fromCharCode(...Array.from(e.subarray(r+i,r+i+4)))}"ID3 "===a&&"ID3 "===n?i.timed_id3_pids[s]=!0:"KLVA"===n&&(i.synchronous_klv_pids[s]=!0)}r+=2+a}}else r===ks&&(i.scte_35_pids[s]=!0);else i.common_pids.mp3=s;else i.common_pids.eac3=s;else i.common_pids.ac3=s;else i.common_pids.loas_aac=s;else i.common_pids.adts_aac=s;else i.common_pids.h265=s;else i.common_pids.h264=s;t+=5+a}s===tr.current_program_&&(null==tr.pmt_&&rr.debug.log(tr.TAG_NAME,`Parsed first PMT: ${JSON.stringify(i)}`),tr.pmt_=i,(i.common_pids.h264||i.common_pids.h265)&&(tr.has_video_=!0),(i.common_pids.adts_aac||i.common_pids.loas_aac||i.common_pids.ac3||i.common_pids.opus||i.common_pids.mp3)&&(tr.has_audio_=!0))},parseSCTE35(e){},parseH264Payload(e,t,i,r){let s=new Mi(e),a=null,n=null,o=[],d=0,l=!1;for(;null!=(a=s.readNextNaluPayload());){let e=new zi(a);if(e.type===We){let t=Bi.parseSPS$2(a.data);tr.video_init_segment_dispatched_?!0===tr.detectVideoMetadataChange(e,t)&&(rr.debug.log(tr.TAG_NAME,"H264: Critical h264 metadata has been changed, attempt to re-generate InitSegment"),tr.video_metadata_changed_=!0,tr.video_metadata_={vps:void 0,sps:e,pps:void 0,details:t}):(tr.video_metadata_.sps=e,tr.video_metadata_.details=t)}else e.type===Ye?tr.video_init_segment_dispatched_&&!tr.video_metadata_changed_||(tr.video_metadata_.pps=e,tr.video_metadata_.sps&&tr.video_metadata_.pps&&(tr.video_metadata_changed_&&tr.dispatchVideoMediaSegment(),tr.dispatchVideoInitSegment())):(e.type===Ve||e.type===He&&1===r)&&(l=!0);tr.video_init_segment_dispatched_&&(o.push(e),d+=e.data.byteLength)}let c=Math.floor(t/tr.timescale_),u=Math.floor(i/tr.timescale_);if(o.length){let e=tr.video_track_;for(let e=0;e<o.length;e++){let t=o[e];if(null==n)n=t.data;else{let e=new Uint8Array(n.byteLength+t.data.byteLength);e.set(n,0),e.set(t.data,n.byteLength),n=e}}let t={length:d,isIFrame:l,dts:u,pts:c,cts:c-u,payload:n,type:re,isHevc:!1};e.samples.push(t),e.length=n.byteLength}},parseH265Payload(e,t,i,r){let s=new Ji(e),a=null,n=null,o=[],d=0,l=!1;for(;null!=(a=s.readNextNaluPayload());){let e=new Qi(a);if(e.type===Je){if(!tr.video_init_segment_dispatched_){let t=Gi.parseVPS(a.data);tr.video_metadata_.vps=e,tr.video_metadata_.details={...tr.video_metadata_.details,...t}}}else if(e.type===et){let t=Gi.parseSPS(a.data);tr.video_init_segment_dispatched_?!0===tr.detectVideoMetadataChange(e,t)&&(rr.debug.log(tr.TAG_NAME,"H265: Critical h265 metadata has been changed, attempt to re-generate InitSegment"),tr.video_metadata_changed_=!0,tr.video_metadata_={vps:void 0,sps:e,pps:void 0,details:t}):(tr.video_metadata_.sps=e,tr.video_metadata_.details={...tr.video_metadata_.details,...t})}else if(e.type===it){if(!tr.video_init_segment_dispatched_||tr.video_metadata_changed_){let t=Gi.parsePPS(a.data);tr.video_metadata_.pps=e,tr.video_metadata_.details={...tr.video_metadata_.details,...t},tr.video_metadata_.vps&&tr.video_metadata_.sps&&tr.video_metadata_.pps&&(tr.video_metadata_changed_&&tr.dispatchVideoMediaSegment(),tr.dispatchVideoInitSegment())}}else e.type!==je&&e.type!==Ke&&e.type!==Xe||(l=!0);tr.video_init_segment_dispatched_&&(o.push(e),d+=e.data.byteLength)}let c=Math.floor(t/tr.timescale_),u=Math.floor(i/tr.timescale_);if(o.length){let e=tr.video_track_;for(let e=0;e<o.length;e++){let t=o[e];if(null==n)n=t.data;else{let e=new Uint8Array(n.byteLength+t.data.byteLength);e.set(n,0),e.set(t.data,n.byteLength),n=e}}let t={type:re,length:d,isIFrame:l,dts:u,pts:c,cts:c-u,payload:n,isHevc:!0};e.samples.push(t),e.length=n.byteLength}},detectVideoMetadataChange(e,t){if(t.codec_mimetype!==tr.video_metadata_.details.codec_mimetype)return rr.debug.log(tr.TAG_NAME,`Video: Codec mimeType changed from ${tr.video_metadata_.details.codec_mimetype} to ${t.codec_mimetype}`),!0;if(t.codec_size.width!==tr.video_metadata_.details.codec_size.width||t.codec_size.height!==tr.video_metadata_.details.codec_size.height){let e=tr.video_metadata_.details.codec_size,i=t.codec_size;return rr.debug.log(tr.TAG_NAME,`Video: Coded Resolution changed from ${e.width}x${e.height} to ${i.width}x${i.height}`),!0}return t.present_size.width!==tr.video_metadata_.details.present_size.width&&(rr.debug.log(tr.TAG_NAME,`Video: Present resolution width changed from ${tr.video_metadata_.details.present_size.width} to ${t.present_size.width}`),!0)},isInitSegmentDispatched:()=>tr.has_video_&&tr.has_audio_?tr.video_init_segment_dispatched_&&tr.audio_init_segment_dispatched_:tr.has_video_&&!tr.has_audio_?tr.video_init_segment_dispatched_:!(tr.has_video_||!tr.has_audio_)&&tr.audio_init_segment_dispatched_,dispatchVideoInitSegment(){let e=tr.video_metadata_.details,t={type:"video"};t.id=tr.video_track_.id,t.timescale=1e3,t.duration=tr.duration_,t.codecWidth=e.codec_size.width,t.codecHeight=e.codec_size.height,t.presentWidth=e.present_size.width,t.presentHeight=e.present_size.height,t.profile=e.profile_string,t.level=e.level_string,t.bitDepth=e.bit_depth,t.chromaFormat=e.chroma_format,t.sarRatio=e.sar_ratio,t.frameRate=e.frame_rate;let i=t.frameRate.fps_den,r=t.frameRate.fps_num;if(t.refSampleDuration=i/r*1e3,t.codec=e.codec_mimetype,tr.video_metadata_.vps){let e=tr.video_metadata_.vps.data.subarray(4),i=tr.video_metadata_.sps.data.subarray(4),r=tr.video_metadata_.pps.data.subarray(4);t.hvcc=$i({vps:e,sps:i,pps:r}),0==tr.video_init_segment_dispatched_&&rr.debug.log(tr.TAG_NAME,`Generated first HEVCDecoderConfigurationRecord for mimeType: ${t.codec}`),t.hvcc&&rr.decodeVideo(t.hvcc,0,!0,0)}else{let e=tr.video_metadata_.sps.data.subarray(4),i=tr.video_metadata_.pps.data.subarray(4);t.avcc=Ti({sps:e,pps:i}),0==tr.video_init_segment_dispatched_&&rr.debug.log(tr.TAG_NAME,`Generated first AVCDecoderConfigurationRecord for mimeType: ${t.codec}`),t.avcc&&rr.decodeVideo(t.avcc,0,!0,0)}tr.video_init_segment_dispatched_=!0,tr.video_metadata_changed_=!1;let s=tr.media_info_;s.hasVideo=!0,s.width=t.codecWidth,s.height=t.codecHeight,s.fps=t.frameRate.fps,s.profile=t.profile,s.level=t.level,s.refFrames=e.ref_frames,s.chromaFormat=e.chroma_format_string,s.sarNum=t.sarRatio.width,s.sarDen=t.sarRatio.height,s.videoCodec=t.codec,s.hasAudio&&s.audioCodec?s.mimeType=`video/mp2t; codecs="${s.videoCodec},${s.audioCodec}"`:s.mimeType=`video/mp2t; codecs="${s.videoCodec}"`},dispatchVideoMediaSegment(){tr.isInitSegmentDispatched()&&tr.video_track_.length&&tr._preDoDecode()},dispatchAudioMediaSegment(){tr.isInitSegmentDispatched()&&tr.audio_track_.length&&tr._preDoDecode()},dispatchAudioVideoMediaSegment(){tr.isInitSegmentDispatched()&&(tr.audio_track_.length||tr.video_track_.length)&&tr._preDoDecode()},parseADTSAACPayload(e,t){if(tr.has_video_&&!tr.video_init_segment_dispatched_)return;if(tr.aac_last_incomplete_data_){let t=new Uint8Array(e.byteLength+tr.aac_last_incomplete_data_.byteLength);t.set(tr.aac_last_incomplete_data_,0),t.set(e,tr.aac_last_incomplete_data_.byteLength),e=t}let i,r;if(null!=t&&(r=t/tr.timescale_),"aac"===tr.audio_metadata_.codec){if(null==t&&null!=tr.audio_last_sample_pts_)i=1024/tr.audio_metadata_.sampling_frequency*1e3,r=tr.audio_last_sample_pts_+i;else if(null==t)return void rr.debug.warn(tr.TAG_NAME,"AAC: Unknown pts");if(tr.aac_last_incomplete_data_&&tr.audio_last_sample_pts_){i=1024/tr.audio_metadata_.sampling_frequency*1e3;let e=tr.audio_last_sample_pts_+i;Math.abs(e-r)>1&&(rr.debug.warn(tr.TAG_NAME,`AAC: Detected pts overlapped, expected: ${e}ms, PES pts: ${r}ms`),r=e)}}let s,a=new qt(e),n=null,o=r;for(;null!=(n=a.readNextAACFrame());){i=1024/n.sampling_frequency*1e3;const e={codec:"aac",data:n};0==tr.audio_init_segment_dispatched_?(tr.audio_metadata_={codec:"aac",audio_object_type:n.audio_object_type,sampling_freq_index:n.sampling_freq_index,sampling_frequency:n.sampling_frequency,channel_config:n.channel_config},tr.dispatchAudioInitSegment(e)):tr.detectAudioMetadataChange(e)&&(tr.dispatchAudioMediaSegment(),tr.dispatchAudioInitSegment(e)),s=o;let t=Math.floor(o);const r=new Uint8Array(n.data.length+2);r.set([175,1],0),r.set(n.data,2);let a={payload:r,length:r.byteLength,pts:t,dts:t,type:ie};tr.audio_track_.samples.push(a),tr.audio_track_.length+=r.byteLength,o+=i}a.hasIncompleteData()&&(tr.aac_last_incomplete_data_=a.getIncompleteData()),s&&(tr.audio_last_sample_pts_=s)},parseLOASAACPayload(e,t){if(tr.has_video_&&!tr.video_init_segment_dispatched_)return;if(tr.aac_last_incomplete_data_){let t=new Uint8Array(e.byteLength+tr.aac_last_incomplete_data_.byteLength);t.set(tr.aac_last_incomplete_data_,0),t.set(e,tr.aac_last_incomplete_data_.byteLength),e=t}let i,r;if(null!=t&&(r=t/tr.timescale_),"aac"===tr.audio_metadata_.codec){if(null==t&&null!=tr.audio_last_sample_pts_)i=1024/tr.audio_metadata_.sampling_frequency*1e3,r=tr.audio_last_sample_pts_+i;else if(null==t)return void rr.debug.warn(tr.TAG_NAME,"AAC: Unknown pts");if(tr.aac_last_incomplete_data_&&tr.audio_last_sample_pts_){i=1024/tr.audio_metadata_.sampling_frequency*1e3;let e=tr.audio_last_sample_pts_+i;Math.abs(e-r)>1&&(rr.debug.warn(tr.TAG,`AAC: Detected pts overlapped, expected: ${e}ms, PES pts: ${r}ms`),r=e)}}let s,a=new jt(e),n=null,o=r;for(;null!=(n=a.readNextAACFrame(ai(this.loas_previous_frame)?void 0:this.loas_previous_frame));){tr.loas_previous_frame=n,i=1024/n.sampling_frequency*1e3;const e={codec:"aac",data:n};0==tr.audio_init_segment_dispatched_?(tr.audio_metadata_={codec:"aac",audio_object_type:n.audio_object_type,sampling_freq_index:n.sampling_freq_index,sampling_frequency:n.sampling_frequency,channel_config:n.channel_config},tr.dispatchAudioInitSegment(e)):tr.detectAudioMetadataChange(e)&&(tr.dispatchAudioMediaSegment(),tr.dispatchAudioInitSegment(e)),s=o;let t=Math.floor(o);const r=new Uint8Array(n.data.length+2);r.set([175,1],0),r.set(n.data,2);let a={payload:r,length:r.byteLength,pts:t,dts:t,type:ie};tr.audio_track_.samples.push(a),tr.audio_track_.length+=r.byteLength,o+=i}a.hasIncompleteData()&&(tr.aac_last_incomplete_data_=a.getIncompleteData()),s&&(tr.audio_last_sample_pts_=s)},parseAC3Payload(e,t){},parseEAC3Payload(e,t){},parseOpusPayload(e,t){},parseMP3Payload(e,t){if(tr.has_video_&&!tr.video_init_segment_dispatched_)return;let i=[44100,48e3,32e3,0],r=[22050,24e3,16e3,0],s=[11025,12e3,8e3,0],a=e[1]>>>3&3,n=(6&e[1])>>1;e[2];let o=(12&e[2])>>>2,d=3!==(e[3]>>>6&3)?2:1,l=0,c=34;switch(a){case 0:l=s[o];break;case 2:l=r[o];break;case 3:l=i[o]}switch(n){case 1:c=34;break;case 2:c=33;break;case 3:c=32}const u={};u.object_type=c,u.sample_rate=l,u.channel_count=d,u.data=e;const h={codec:"mp3",data:u};0==tr.audio_init_segment_dispatched_?(tr.audio_metadata_={codec:"mp3",object_type:c,sample_rate:l,channel_count:d},tr.dispatchAudioInitSegment(h)):tr.detectAudioMetadataChange(h)&&(tr.dispatchAudioMediaSegment(),tr.dispatchAudioInitSegment(h));let f={payload:e,length:e.byteLength,pts:t/tr.timescale_,dts:t/tr.timescale_,type:ie};tr.audio_track_.samples.push(f),tr.audio_track_.length+=e.byteLength},detectAudioMetadataChange(e){if(e.codec!==tr.audio_metadata_.codec)return rr.debug.log(tr.TAG_NAME,`Audio: Audio Codecs changed from ${tr.audio_metadata_.codec} to ${e.codec}`),!0;if("aac"===e.codec&&"aac"===tr.audio_metadata_.codec){const t=e.data;if(t.audio_object_type!==tr.audio_metadata_.audio_object_type)return rr.debug.log(tr.TAG_NAME,`AAC: AudioObjectType changed from ${tr.audio_metadata_.audio_object_type} to ${t.audio_object_type}`),!0;if(t.sampling_freq_index!==tr.audio_metadata_.sampling_freq_index)return rr.debug.log(tr.TAG_NAME,`AAC: SamplingFrequencyIndex changed from ${tr.audio_metadata_.sampling_freq_index} to ${t.sampling_freq_index}`),!0;if(t.channel_config!==tr.audio_metadata_.channel_config)return rr.debug.log(tr.TAG_NAME,`AAC: Channel configuration changed from ${tr.audio_metadata_.channel_config} to ${t.channel_config}`),!0}else if("ac-3"===e.codec&&"ac-3"===tr.audio_metadata_.codec){const t=e.data;if(t.sampling_frequency!==tr.audio_metadata_.sampling_frequency)return rr.debug.log(tr.TAG_NAME,`AC3: Sampling Frequency changed from ${tr.audio_metadata_.sampling_frequency} to ${t.sampling_frequency}`),!0;if(t.bit_stream_identification!==tr.audio_metadata_.bit_stream_identification)return rr.debug.log(tr.TAG_NAME,`AC3: Bit Stream Identification changed from ${tr.audio_metadata_.bit_stream_identification} to ${t.bit_stream_identification}`),!0;if(t.bit_stream_mode!==tr.audio_metadata_.bit_stream_mode)return rr.debug.log(tr.TAG_NAME,`AC3: BitStream Mode changed from ${tr.audio_metadata_.bit_stream_mode} to ${t.bit_stream_mode}`),!0;if(t.channel_mode!==tr.audio_metadata_.channel_mode)return rr.debug.log(tr.TAG_NAME,`AC3: Channel Mode changed from ${tr.audio_metadata_.channel_mode} to ${t.channel_mode}`),!0;if(t.low_frequency_effects_channel_on!==tr.audio_metadata_.low_frequency_effects_channel_on)return rr.debug.log(tr.TAG_NAME,`AC3: Low Frequency Effects Channel On changed from ${tr.audio_metadata_.low_frequency_effects_channel_on} to ${t.low_frequency_effects_channel_on}`),!0}else if("opus"===e.codec&&"opus"===tr.audio_metadata_.codec){const t=e.meta;if(t.sample_rate!==tr.audio_metadata_.sample_rate)return rr.debug.log(tr.TAG_NAME,`Opus: SamplingFrequencyIndex changed from ${tr.audio_metadata_.sample_rate} to ${t.sample_rate}`),!0;if(t.channel_count!==tr.audio_metadata_.channel_count)return rr.debug.log(tr.TAG_NAME,`Opus: Channel count changed from ${tr.audio_metadata_.channel_count} to ${t.channel_count}`),!0}else if("mp3"===e.codec&&"mp3"===tr.audio_metadata_.codec){const t=e.data;if(t.object_type!==tr.audio_metadata_.object_type)return rr.debug.log(tr.TAG_NAME,`MP3: AudioObjectType changed from ${tr.audio_metadata_.object_type} to ${t.object_type}`),!0;if(t.sample_rate!==tr.audio_metadata_.sample_rate)return rr.debug.log(tr.TAG_NAME,`MP3: SamplingFrequencyIndex changed from ${tr.audio_metadata_.sample_rate} to ${t.sample_rate}`),!0;if(t.channel_count!==tr.audio_metadata_.channel_count)return rr.debug.log(tr.TAG_NAME,`MP3: Channel count changed from ${tr.audio_metadata_.channel_count} to ${t.channel_count}`),!0}return!1},dispatchAudioInitSegment(e){let t={type:"audio"};if(t.id=tr.audio_track_.id,t.timescale=1e3,t.duration=tr.duration_,"aac"===tr.audio_metadata_.codec){let i="aac"===e.codec?e.data:null,r=new zs(i);t.audioSampleRate=r.sampling_rate,t.audioSampleRateIndex=r.sampling_index,t.channelCount=r.channel_count,t.codec=r.codec_mimetype,t.originalCodec=r.original_codec_mimetype,t.config=r.config,t.refSampleDuration=1024/t.audioSampleRate*t.timescale;const s=Ht({profile:rr._opt.mseDecodeAudio?r.object_type:r.original_object_type,sampleRate:t.audioSampleRateIndex,channel:t.channelCount});rr.decodeAudio(s,0)}else"ac-3"===tr.audio_metadata_.codec||"ec-3"===tr.audio_metadata_.codec||"opus"===tr.audio_metadata_.codec||"mp3"===tr.audio_metadata_.codec&&(t.audioSampleRate=tr.audio_metadata_.sample_rate,t.channelCount=tr.audio_metadata_.channel_count,t.codec="mp3",t.originalCodec="mp3",t.config=void 0);0==tr.audio_init_segment_dispatched_&&rr.debug.log(tr.TAG_NAME,`Generated first AudioSpecificConfig for mimeType: ${t.codec}`),tr.audio_init_segment_dispatched_=!0,tr.video_metadata_changed_=!1;let i=tr.media_info_;i.hasAudio=!0,i.audioCodec=t.originalCodec,i.audioSampleRate=t.audioSampleRate,i.audioChannelCount=t.channelCount,i.hasVideo&&i.videoCodec?i.mimeType=`video/mp2t; codecs="${i.videoCodec},${i.audioCodec}"`:i.mimeType=`video/mp2t; codecs="${i.audioCodec}"`},dispatchPESPrivateDataDescriptor(e,t,i){},parsePESPrivateDataPayload(e,t,i,r,s){let a=new Fs;if(a.pid=r,a.stream_id=s,a.len=e.byteLength,a.data=e,null!=t){let e=Math.floor(t/tr.timescale_);a.pts=e}else a.nearest_pts=tr.getNearestTimestampMilliseconds();if(null!=i){let e=Math.floor(i/tr.timescale_);a.dts=e}},parseTimedID3MetadataPayload(e,t,i,r,s){rr.debug.log(tr.TAG_NAME,`Timed ID3 Metadata: pid=${r}, pts=${t}, dts=${i}, stream_id=${s}`)},parseSynchronousKLVMetadataPayload(e,t,i,r,s){rr.debug.log(tr.TAG_NAME,`Synchronous KLV Metadata: pid=${r}, pts=${t}, dts=${i}, stream_id=${s}`)},parseAsynchronousKLVMetadataPayload(e,t,i){rr.debug.log(tr.TAG_NAME,`Asynchronous KLV Metadata: pid=${t}, stream_id=${i}`)},parseSMPTE2038MetadataPayload(e,t,i,r,s){rr.debug.log(tr.TAG_NAME,`SMPTE 2038 Metadata: pid=${r}, pts=${t}, dts=${i}, stream_id=${s}`)},getNearestTimestampMilliseconds(){if(null!=tr.audio_last_sample_pts_)return Math.floor(tr.audio_last_sample_pts_);if(null!=tr.last_pcr_){return Math.floor(tr.last_pcr_/300/tr.timescale_)}},_preDoDecode(){const e=tr.video_track_,t=tr.audio_track_;let i=e.samples;t.samples.length>0&&(i=e.samples.concat(t.samples),i=i.sort(((e,t)=>e.dts-t.dts))),i.forEach((e=>{const t=new Uint8Array(e.payload);delete e.payload,e.type===re?tr._doDecodeVideo({...e,payload:t}):e.type===ie&&tr._doDecodeAudio({...e,payload:t})})),e.samples=[],e.length=0,t.samples=[],t.length=0},_doDecodeVideo(e){const t=new Uint8Array(e.payload);let i=null;i=e.isHevc?Yi(t,e.isIFrame):Ci(t,e.isIFrame),e.isIFrame&&rr.calcIframeIntervalTimestamp(e.dts);let r=rr.cryptoPayload(i,e.isIFrame);rr.decode(r,{type:re,ts:e.dts,isIFrame:e.isIFrame,cts:e.cts})},_doDecodeAudio(e){const t=new Uint8Array(e.payload);let i=t;_i(rr._opt.m7sCryptoAudio)&&(i=rr.cryptoPayloadAudio(t)),rr.decode(i,{type:ie,ts:e.dts,isIFrame:!1,cts:0})}},ir=null;gi()&&(ir={TAG_NAME:"worker MediaSource",_resetInIt(){ir.isAvc=null,ir.isAAC=null,ir.videoInfo={},ir.videoMeta={},ir.audioMeta={},ir.sourceBuffer=null,ir.audioSourceBuffer=null,ir.hasInit=!1,ir.hasAudioInit=!1,ir.isAudioInitInfo=!1,ir.videoMimeType="",ir.audioMimeType="",ir.cacheTrack={},ir.cacheAudioTrack={},ir.timeInit=!1,ir.sequenceNumber=0,ir.audioSequenceNumber=0,ir.firstRenderTime=null,ir.firstAudioTime=null,ir.mediaSourceAppendBufferFull=!1,ir.mediaSourceAppendBufferError=!1,ir.mediaSourceAddSourceBufferError=!1,ir.mediaSourceBufferError=!1,ir.mediaSourceError=!1,ir.prevTimestamp=null,ir.decodeDiffTimestamp=null,ir.prevDts=null,ir.prevAudioDts=null,ir.prevPayloadBufferSize=0,ir.isWidthOrHeightChanged=!1,ir.prevTs=null,ir.prevAudioTs=null,ir.eventListenList=[],ir.pendingRemoveRanges=[],ir.pendingSegments=[],ir.pendingAudioRemoveRanges=[],ir.pendingAudioSegments=[],ir.supportVideoFrameCallbackHandle=null,ir.audioSourceBufferCheckTimeout=null,ir.audioSourceNoDataCheckTimeout=null,ir.hasPendingEos=!1,ir.$video={currentTime:0,readyState:0}},init(){ir.events=new cs,ir._resetInIt(),ir.mediaSource=new self.MediaSource,ir.isDecodeFirstIIframe=!!mi(rr._opt.checkFirstIFrame),ir._bindMediaSourceEvents()},destroy(){ir.stop(),ir._clearAudioSourceBufferCheckTimeout(),ir.eventListenList&&ir.eventListenList.length&&(ir.eventListenList.forEach((e=>e())),ir.eventListenList=[]),ir._resetInIt(),ir.mediaSource=null},getState:()=>ir.mediaSource&&ir.mediaSource.readyState,isStateOpen:()=>ir.getState()===_t,isStateClosed:()=>ir.getState()===mt,isStateEnded:()=>ir.getState()===pt,_bindMediaSourceEvents(){const{proxy:e}=ir.events,t=e(ir.mediaSource,yt,(()=>{rr.debug.log(ir.TAG_NAME,"sourceOpen"),ir._onMediaSourceSourceOpen()})),i=e(ir.mediaSource,gt,(()=>{rr.debug.log(ir.TAG_NAME,"sourceClose")})),r=e(ir.mediaSource,bt,(()=>{rr.debug.log(ir.TAG_NAME,"sourceended")}));ir.eventListenList.push(t,i,r)},_onMediaSourceSourceOpen(){ir.sourceBuffer||(rr.debug.log(ir.TAG_NAME,"onMediaSourceSourceOpen() sourceBuffer is null and next init"),ir._initSourceBuffer()),ir.audioSourceBuffer||(rr.debug.log(ir.TAG_NAME,"onMediaSourceSourceOpen() audioSourceBuffer is null and next init"),ir._initAudioSourceBuffer()),ir._hasPendingSegments()&&ir._doAppendSegments()},decodeVideo(e,t,i,r){if(rr.isDestroyed)rr.debug.warn(ir.TAG_NAME,"decodeVideo() and decoder is destroyed");else if(mi(ir.hasInit))if(i&&e[1]===At){const r=15&e[0];if(r===De&&mi(si()))return void ir.emitError(Te.mediaSourceH265NotSupport);ir.videoInfo.codec=r,postMessage({cmd:L,code:r});const s=new Uint8Array(e);postMessage({cmd:F,buffer:s,codecId:r},[s.buffer]),ir.hasInit=ir._decodeConfigurationRecord(e,t,i,r)}else rr.debug.warn(ir.TAG_NAME,`decodeVideo has not init , isIframe is ${i} , payload is ${e[1]}`);else if(!ir.isDecodeFirstIIframe&&i&&(ir.isDecodeFirstIIframe=!0),ir.isDecodeFirstIIframe){if(i&&0===e[1]){const t=15&e[0];let i={};if(t===Ce){i=Ui(e.slice(5))}else t===De&&(i=Oi(e));const r=ir.videoInfo;r&&r.codecWidth&&r.codecWidth&&i&&i.codecWidth&&i.codecHeight&&(i.codecWidth!==r.codecWidth||i.codecHeight!==r.codecWidth)&&(rr.debug.warn(ir.TAG_NAME,`\n                                decodeVideo: video width or height is changed,\n                                old width is ${r.codecWidth}, old height is ${r.codecWidth},\n                                new width is ${i.codecWidth}, new height is ${i.codecHeight},\n                                and emit change event`),ir.isWidthOrHeightChanged=!0,ir.emitError(Te.mseWidthOrHeightChange))}if(ir.isWidthOrHeightChanged)return void rr.debug.warn(ir.TAG_NAME,"decodeVideo: video width or height is changed, and return");if(pi(e))return void rr.debug.warn(ir.TAG_NAME,"decodeVideo and payload is video sequence header so drop this frame");if(e.byteLength<A)return void rr.debug.warn(ir.TAG_NAME,`decodeVideo and payload is too small , payload length is ${e.byteLength}`);let s=t;if(rr.isPlayer){if(null===ir.firstRenderTime&&(ir.firstRenderTime=t,postMessage({cmd:ee,value:ir.firstRenderTime})),s=t-ir.firstRenderTime,s<0&&(rr.debug.warn(ir.TAG_NAME,`decodeVideo\n                                 local dts is < 0 , ts is ${t} and prevTs is ${ir.prevTs},\n                                 firstRenderTime is ${ir.firstRenderTime} and mseCorrectTimeDuration is ${rr._opt.mseCorrectTimeDuration}`),s=null===ir.prevDts?0:ir.prevDts+rr._opt.mseCorrectTimeDuration,ir._checkTsIsMaxDiff(t)))return rr.debug.warn(ir.TAG_NAME,`decodeVideo is max diff , ts is ${t} and prevTs is ${ir.prevTs}, diff is ${ir.prevTs-t}`),void ir.emitError(Te.mediaSourceTsIsMaxDiff);if(null!==ir.prevDts&&s<=ir.prevDts){if(rr.debug.warn(ir.TAG_NAME,`\n                                decodeVideo dts is less than(or equal) prev dts ,\n                                dts is ${s} and prev dts is ${ir.prevDts} ，\n                                and now ts is ${t} and prev ts is ${ir.prevTs} ，\n                                and diff is ${t-ir.prevTs} and firstRenderTime is ${ir.firstRenderTime} and isIframe is ${i}，\n                                and mseCorrectTimeDuration is ${rr._opt.mseCorrectTimeDuration},\n                                and prevPayloadBufferSize is ${ir.prevPayloadBufferSize} and payload size is ${e.byteLength}`),s===ir.prevDts&&ir.prevPayloadBufferSize===e.byteLength)return void rr.debug.warn(ir.TAG_NAME,"decodeVideo dts is equal to prev dts and payload size is equal to prev payload size so drop this frame");if(s=ir.prevDts+rr._opt.mseCorrectTimeDuration,ir._checkTsIsMaxDiff(t))return rr.debug.warn(ir.TAG_NAME,`decodeVideo is max diff , ts is ${t} and prevTs is ${ir.prevTs}, diff is ${ir.prevTs-t} and emit replay`),void ir.emitError(Te.mediaSourceTsIsMaxDiff)}}rr.isPlayer?ir._decodeVideo(e,s,i,r,t):rr.isPlayback,ir.prevDts=s,ir.prevPayloadBufferSize=e.byteLength,ir.prevTs=t}else rr.debug.log(ir.TAG_NAME,"decodeVideo first frame is not iFrame")},decodeAudio(e,t){if(rr.isDestroyed)rr.debug.warn(ir.TAG_NAME,"decodeAudio() and decoder is destroyed");else if(mi(ir.hasAudioInit))ir.hasAudioInit=ir._decodeAudioConfigurationRecord(e,t);else{let i=t;if(Vt(e))return void rr.debug.log(ir.TAG_NAME,"decodeAudio and has already initialized and payload is aac codec packet so drop this frame");if(ir._clearAudioNoDataCheckTimeout(),ir.isDecodeFirstIIframe){if(rr.isPlayer){if(null===ir.firstAudioTime&&(ir.firstAudioTime=t,null!==ir.firstRenderTime&&null!==ir.prevTs)){const e=Math.abs(ir.firstRenderTime-ir.prevTs);e>300&&(ir.firstAudioTime-=e,rr.debug.warn(ir.TAG_NAME,`video\n                                    firstAudioTime is ${ir.firstRenderTime} and current time is ${ir.prevTs}\n                                    play time is ${e} and firstAudioTime ${t} - ${e} = ${ir.firstAudioTime}`))}i=t-ir.firstAudioTime,i<0&&(rr.debug.warn(ir.TAG_NAME,`decodeAudio\n                             local dts is < 0 , ts is ${t} and prevTs is ${ir.prevAudioTs},\n                             firstAudioTime is ${ir.firstAudioTime}`),i=null===ir.prevAudioDts?0:ir.prevAudioDts+rr._opt.mseCorrectAudioTimeDuration),null!==ir.prevAudioTs&&i<=ir.prevAudioDts&&(rr.debug.warn(ir.TAG_NAME,`\n                            decodeAudio dts is less than(or equal) prev dts ,\n                            dts is ${i} and prev dts is ${ir.prevAudioDts} ，\n                            and now ts is ${t} and prev ts is ${ir.prevAudioTs} ，\n                            and diff is ${t-ir.prevAudioTs}`),i=ir.prevAudioDts+rr._opt.mseCorrectAudioTimeDuration)}rr.isPlayer?ir._decodeAudio(e,i,t):rr.isPlayback,ir.prevAudioTs=t,ir.prevAudioDts=i}else rr.debug.log(ir.TAG_NAME,"decodeAudio first frame is not iFrame")}},_checkTsIsMaxDiff:e=>ir.prevTs>0&&e<ir.prevTs&&ir.prevTs-e>S,_decodeConfigurationRecord(e,t,i,r){let s=e.slice(5),a={};if(r===Ce?a=Ui(s):r===De&&(a=Hi(s)),ir.videoInfo.width=a.codecWidth,ir.videoInfo.height=a.codecHeight,0===a.codecWidth&&0===a.codecHeight)return rr.debug.warn(ir.TAG_NAME,"_decodeConfigurationRecord error",JSON.stringify(a)),ir.emitError(Te.mediaSourceDecoderConfigurationError),!1;const n={id:kt,type:"video",timescale:1e3,duration:0,avcc:s,codecWidth:a.codecWidth,codecHeight:a.codecHeight,videoType:a.videoType},o=us.generateInitSegment(n);ir.isAvc=r===Ce;let d=a.codec;return ir.videoMimeType=d?`video/mp4; codecs="${a.codec}"`:ir.isAvc?dt:lt,postMessage({cmd:U,w:a.codecWidth,h:a.codecHeight}),ir._initSourceBuffer(),ir.appendBuffer(o.buffer),ir.sequenceNumber=0,ir.cacheTrack={},ir.timeInit=!1,!0},_decodeAudioConfigurationRecord(e,t){const i=e[0]>>4,r=e[0]>>1&1,s=i===Re,a=i===Pe;if(mi(a||s))return rr.debug.warn(ir.TAG_NAME,`_decodeAudioConfigurationRecord audio codec is not support , codecId is ${i} ant auto wasm decode`),ir.emitError(Te.mediaSourceAudioG711NotSupport),!1;const n={id:Ct,type:"audio",timescale:1e3};let o={};if(Vt(e)){if(o=Yt(e.slice(2)),!o)return!1;n.audioSampleRate=o.sampleRate,n.channelCount=o.channelCount,n.config=o.config,n.refSampleDuration=1024/n.audioSampleRate*n.timescale}else{if(!s)return!1;if(o=vs(e),!o)return!1;n.audioSampleRate=o.samplingRate,n.channelCount=o.channelCount,n.refSampleDuration=1152/n.audioSampleRate*n.timescale}n.codec=o.codec,n.duration=0;let d="mp4",l=o.codec,c=null;s&&mi(ri())?(d="mpeg",l="",c=new Uint8Array):c=us.generateInitSegment(n);let u=`${n.type}/${d}`;return l&&l.length>0&&(u+=`;codecs=${l}`),mi(ir.isAudioInitInfo)&&(bi=i===Pe?r?16:8:0===r?8:16,postMessage({cmd:C,code:i}),postMessage({cmd:k,sampleRate:n.audioSampleRate,channels:n.channelCount,depth:bi}),ir.isAudioInitInfo=!0),ir.audioMimeType=u,ir.isAAC=a,ir._initAudioSourceBuffer(),ir.appendAudioBuffer(c.buffer),!0},_initSourceBuffer(){const{proxy:e}=ir.events;if(null===ir.sourceBuffer&&null!==ir.mediaSource&&ir.isStateOpen()&&ir.videoMimeType){try{ir.sourceBuffer=ir.mediaSource.addSourceBuffer(ir.videoMimeType),rr.debug.log(ir.TAG_NAME,"_initSourceBuffer() mseDecoder.mediaSource.addSourceBuffer()",ir.videoMimeType)}catch(e){return rr.debug.error(ir.TAG_NAME,"appendBuffer() mseDecoder.mediaSource.addSourceBuffer()",e.code,e),ir.emitError(Te.mseAddSourceBufferError,e.code),void(ir.mediaSourceAddSourceBufferError=!0)}if(ir.sourceBuffer){const t=e(ir.sourceBuffer,"error",(e=>{ir.mediaSourceBufferError=!0,rr.debug.error(ir.TAG_NAME,"mseSourceBufferError mseDecoder.sourceBuffer",e),ir.emitError(Te.mseSourceBufferError,e.code)})),i=e(ir.sourceBuffer,"updateend",(()=>{ir._hasPendingRemoveRanges()?ir._doRemoveRanges():ir._hasPendingSegments()?ir._doAppendSegments():ir.hasPendingEos&&(rr.debug.log(ir.TAG_NAME,"videoSourceBuffer updateend and hasPendingEos is true, next endOfStream()"),ir.endOfStream())}));ir.eventListenList.push(t,i)}}else rr.debug.log(ir.TAG_NAME,`_initSourceBuffer and mseDecoder.isStateOpen is ${ir.isStateOpen()} and mseDecoder.isAvc === null is ${null===ir.isAvc}`)},_initAudioSourceBuffer(){const{proxy:e}=ir.events;if(null===ir.audioSourceBuffer&&null!==ir.mediaSource&&ir.isStateOpen()&&ir.audioMimeType){try{ir.audioSourceBuffer=ir.mediaSource.addSourceBuffer(ir.audioMimeType),ir._clearAudioSourceBufferCheckTimeout(),rr.debug.log(ir.TAG_NAME,"_initAudioSourceBuffer() mseDecoder.mediaSource.addSourceBuffer()",ir.audioMimeType)}catch(e){return rr.debug.error(ir.TAG_NAME,"appendAudioBuffer() mseDecoder.mediaSource.addSourceBuffer()",e.code,e),ir.emitError(Te.mseAddSourceBufferError,e.code),void(ir.mediaSourceAddSourceBufferError=!0)}if(ir.audioSourceBuffer){const t=e(ir.audioSourceBuffer,"error",(e=>{ir.mediaSourceBufferError=!0,rr.debug.error(ir.TAG_NAME,"mseSourceBufferError mseDecoder.audioSourceBuffer",e),ir.emitError(Te.mseSourceBufferError,e.code)})),i=e(ir.audioSourceBuffer,"updateend",(()=>{ir._hasPendingRemoveRanges()?ir._doRemoveRanges():ir._hasPendingSegments()?ir._doAppendSegments():ir.hasPendingEos&&(rr.debug.log(ir.TAG_NAME,"audioSourceBuffer updateend and hasPendingEos is true, next endOfStream()"),ir.endOfStream())}));ir.eventListenList.push(t,i),null===ir.audioSourceNoDataCheckTimeout&&(ir.audioSourceNoDataCheckTimeout=setTimeout((()=>{ir._clearAudioNoDataCheckTimeout(),ir.emitError(Te.mediaSourceAudioNoDataTimeout)}),1e3))}}else rr.debug.log(ir.TAG_NAME,`_initAudioSourceBuffer and mseDecoder.isStateOpen is ${ir.isStateOpen()} and mseDecoder.audioMimeType is ${ir.audioMimeType}`)},_decodeVideo(e,t,i,r,s){let a=e.slice(5),n=a.byteLength;if(0===n)return void rr.debug.warn(ir.TAG_NAME,"_decodeVideo payload bytes is 0 and return");let o=(new Date).getTime(),d=!1;ir.prevTimestamp||(ir.prevTimestamp=o,d=!0);const l=o-ir.prevTimestamp;if(ir.decodeDiffTimestamp=l,l>500&&!d&&rr.isPlayer&&rr.debug.warn(ir.TAG_NAME,`_decodeVideo now time is ${o} and prev time is ${ir.prevTimestamp}, diff time is ${l} ms`),ir.cacheTrack.id&&t>=ir.cacheTrack.dts){let e=8+ir.cacheTrack.size,i=new Uint8Array(e);i[0]=e>>>24&255,i[1]=e>>>16&255,i[2]=e>>>8&255,i[3]=255&e,i.set(us.types.mdat,4),i.set(ir.cacheTrack.data,8),ir.cacheTrack.duration=t-ir.cacheTrack.dts;let r=us.moof(ir.cacheTrack,ir.cacheTrack.dts);ir.cacheTrack={};let s=new Uint8Array(r.byteLength+i.byteLength);s.set(r,0),s.set(i,r.byteLength),ir.appendBuffer(s.buffer)}else rr.debug.log(ir.TAG_NAME,`timeInit set false , cacheTrack = {} now dts is ${t}, and ts is ${s} cacheTrack dts is ${ir.cacheTrack&&ir.cacheTrack.dts}`),ir.timeInit=!1,ir.cacheTrack={};ir.cacheTrack||(ir.cacheTrack={}),ir.cacheTrack.id=kt,ir.cacheTrack.sequenceNumber=++ir.sequenceNumber,ir.cacheTrack.size=n,ir.cacheTrack.dts=t,ir.cacheTrack.cts=r,ir.cacheTrack.isKeyframe=i,ir.cacheTrack.data=a,ir.cacheTrack.flags={isLeading:0,dependsOn:i?2:1,isDependedOn:i?1:0,hasRedundancy:0,isNonSync:i?0:1},ir.prevTimestamp=(new Date).getTime()},_decodeAudio(e,t,i){let r=ir.isAAC?e.slice(2):e.slice(1),s=r.byteLength;if(ir.cacheAudioTrack.id&&t>=ir.cacheAudioTrack.dts){let e=8+ir.cacheAudioTrack.size,i=new Uint8Array(e);i[0]=e>>>24&255,i[1]=e>>>16&255,i[2]=e>>>8&255,i[3]=255&e,i.set(us.types.mdat,4),i.set(ir.cacheAudioTrack.data,8),ir.cacheAudioTrack.duration=t-ir.cacheAudioTrack.dts;let r=us.moof(ir.cacheAudioTrack,ir.cacheAudioTrack.dts);ir.cacheAudioTrack={};let s=new Uint8Array(r.byteLength+i.byteLength);s.set(r,0),s.set(i,r.byteLength),ir.appendAudioBuffer(s.buffer)}else ir.cacheAudioTrack={};ir.cacheAudioTrack||(ir.cacheAudioTrack={}),ir.cacheAudioTrack.id=Ct,ir.cacheAudioTrack.sequenceNumber=++ir.audioSequenceNumber,ir.cacheAudioTrack.size=s,ir.cacheAudioTrack.dts=t,ir.cacheAudioTrack.cts=0,ir.cacheAudioTrack.data=r,ir.cacheAudioTrack.flags={isLeading:0,dependsOn:1,isDependedOn:0,hasRedundancy:0}},appendBuffer(e){rr.isDestroyed?rr.debug.warn(ir.TAG_NAME,"appendBuffer() player is destroyed"):ir.mediaSourceAddSourceBufferError?rr.debug.warn(ir.TAG_NAME,"mseDecoder.mediaSourceAddSourceBufferError is true"):ir.mediaSourceAppendBufferFull?rr.debug.warn(ir.TAG_NAME,"mseDecoder.mediaSourceAppendBufferFull is true"):ir.mediaSourceAppendBufferError?rr.debug.warn(ir.TAG_NAME,"mseDecoder.mediaSourceAppendBufferError is true"):ir.mediaSourceBufferError?rr.debug.warn(ir.TAG_NAME,"mseDecoder.mediaSourceBufferError is true"):(ir.pendingSegments.push(e),ir.sourceBuffer&&(rr._opt.mseAutoCleanupSourceBuffer&&ir._needCleanupSourceBuffer()&&ir._doCleanUpSourceBuffer(),mi(ir.getSourceBufferUpdating())&&ir.isStateOpen()&&mi(ir._hasPendingRemoveRanges()))?ir._doAppendSegments():ir.isStateClosed()?(ir.mediaSourceBufferError=!0,ir.emitError(Te.mseSourceBufferError,"mediaSource is not attached to video or mediaSource is closed")):ir.isStateEnded()?(ir.mediaSourceBufferError=!0,ir.emitError(Te.mseSourceBufferError,"mediaSource is end")):ir._hasPendingRemoveRanges()&&rr.debug.log(ir.TAG_NAME,`video has pending remove ranges and video length is ${ir.pendingRemoveRanges.length}, audio length is ${ir.pendingAudioRemoveRanges.length}`))},appendAudioBuffer(e){rr.isDestroyed?rr.debug.warn(ir.TAG_NAME,"appendAudioBuffer() player is destroyed"):ir.mediaSourceAddSourceBufferError?rr.debug.warn(ir.TAG_NAME,"mseDecoder.mediaSourceAddSourceBufferError is true"):ir.mediaSourceAppendBufferFull?rr.debug.warn(ir.TAG_NAME,"mseDecoder.mediaSourceAppendBufferFull is true"):ir.mediaSourceAppendBufferError?rr.debug.warn(ir.TAG_NAME,"mseDecoder.mediaSourceAppendBufferError is true"):ir.mediaSourceBufferError?rr.debug.warn(ir.TAG_NAME,"mseDecoder.mediaSourceBufferError is true"):(ir.pendingAudioSegments.push(e),ir.audioSourceBuffer&&(rr._opt.mseAutoCleanupSourceBuffer&&ir._needCleanupSourceBuffer()&&ir._doCleanUpSourceBuffer(),mi(ir.getAudioSourceBufferUpdating())&&ir.isStateOpen()&&mi(ir._hasPendingRemoveRanges()))?ir._doAppendSegments():ir.isStateClosed()?(ir.mediaSourceBufferError=!0,ir.emitError(Te.mseSourceBufferError,"mediaSource is not attached to video or mediaSource is closed")):ir.isStateEnded()?(ir.mediaSourceBufferError=!0,ir.emitError(Te.mseSourceBufferError,"mediaSource is end")):ir._hasPendingRemoveRanges()&&rr.debug.log(ir.TAG_NAME,`audio has pending remove ranges and video length is ${ir.pendingRemoveRanges.length}, audio length is ${ir.pendingAudioRemoveRanges.length}`))},getSourceBufferUpdating:()=>ir.sourceBuffer&&ir.sourceBuffer.updating,getAudioSourceBufferUpdating:()=>ir.audioSourceBuffer&&ir.audioSourceBuffer.updating,stop(){ir.abortSourceBuffer(),ir.removeSourceBuffer(),ir.endOfStream()},clearUpAllSourceBuffer(){if(ir.sourceBuffer){const e=ir.sourceBuffer.buffered;for(let t=0;t<e.length;t++){let i=e.start(t),r=e.end(t);ir.pendingRemoveRanges.push({start:i,end:r})}mi(ir.getSourceBufferUpdating())&&ir._doRemoveRanges()}if(ir.audioSourceBuffer){const e=ir.audioSourceBuffer.buffered;for(let t=0;t<e.length;t++){let i=e.start(t),r=e.end(t);ir.pendingAudioRemoveRanges.push({start:i,end:r})}mi(ir.getAudioSourceBufferUpdating())&&ir._doRemoveRanges()}},endOfStream(){if(ir.isStateOpen()&&Si)if(ir.getSourceBufferUpdating()||ir.getAudioSourceBufferUpdating())rr.debug.log(ir.TAG_NAME,"endOfStream() has pending eos"),ir.hasPendingEos=!0;else{ir.hasPendingEos=!1;try{rr.debug.log(ir.TAG_NAME,"endOfStream()"),ir.mediaSource.endOfStream()}catch(e){rr.debug.warn(ir.TAG_NAME,"endOfStream() error",e)}}},abortSourceBuffer(){if(ir.isStateOpen){if(ir.sourceBuffer){try{rr.debug.log(ir.TAG_NAME,"abortSourceBuffer() abort sourceBuffer"),ir.sourceBuffer.abort()}catch(e){}mi(ir.getSourceBufferUpdating())&&ir._doRemoveRanges()}if(ir.audioSourceBuffer){try{rr.debug.log(ir.TAG_NAME,"abortSourceBuffer() abort audioSourceBuffer"),ir.audioSourceBuffer.abort()}catch(e){}mi(ir.getAudioSourceBufferUpdating())&&ir._doRemoveRanges()}}ir.sourceBuffer=null,ir.audioSourceBuffer=null},removeSourceBuffer(){if(!ir.isStateClosed()&&ir.mediaSource){if(ir.sourceBuffer)try{rr.debug.log(ir.TAG_NAME,"removeSourceBuffer() sourceBuffer"),ir.mediaSource.removeSourceBuffer(ir.sourceBuffer)}catch(e){rr.debug.warn(ir.TAG_NAME,"removeSourceBuffer() sourceBuffer error",e)}if(ir.audioSourceBuffer)try{rr.debug.log(ir.TAG_NAME,"removeSourceBuffer() audioSourceBuffer"),ir.mediaSource.removeSourceBuffer(ir.audioSourceBuffer)}catch(e){rr.debug.warn(ir.TAG_NAME,"removeSourceBuffer() audioSourceBuffer error",e)}}},_hasPendingSegments:()=>ir.pendingSegments.length>0||ir.pendingAudioSegments.length>0,getPendingSegmentsLength:()=>ir.pendingSegments.length,_handleUpdatePlaybackRate(){},_doAppendSegments(){if(ir.isStateClosed()||ir.isStateEnded())rr.debug.log(ir.TAG_NAME,"_doAppendSegments() mediaSource is closed or ended and return");else if(null!==ir.sourceBuffer){if(ir.needInitAudio()&&null===ir.audioSourceBuffer)return rr.debug.log(ir.TAG_NAME,"_doAppendSegments() audioSourceBuffer is null and need init audio source buffer"),void(null===ir.audioSourceBufferCheckTimeout&&(ir.audioSourceBufferCheckTimeout=setTimeout((()=>{ir._clearAudioSourceBufferCheckTimeout(),ir.emitError(Te.mediaSourceAudioInitTimeout)}),1e3)));if(mi(ir.getSourceBufferUpdating())&&ir.pendingSegments.length>0){const e=ir.pendingSegments.shift();try{ir.sourceBuffer.appendBuffer(e)}catch(e){rr.debug.error(ir.TAG_NAME,"mseDecoder.sourceBuffer.appendBuffer()",e.code,e),22===e.code?(ir.stop(),ir.mediaSourceAppendBufferFull=!0,ir.emitError(Te.mediaSourceFull)):11===e.code?(ir.stop(),ir.mediaSourceAppendBufferError=!0,ir.emitError(Te.mediaSourceAppendBufferError)):(ir.stop(),ir.mediaSourceBufferError=!0,ir.emitError(Te.mseSourceBufferError,e.code))}}if(mi(ir.getAudioSourceBufferUpdating())&&ir.pendingAudioSegments.length>0){const e=ir.pendingAudioSegments.shift();try{ir.audioSourceBuffer.appendBuffer(e)}catch(e){rr.debug.error(ir.TAG_NAME,"mseDecoder.audioSourceBuffer.appendBuffer()",e.code,e),22===e.code?(ir.stop(),ir.mediaSourceAppendBufferFull=!0,ir.emitError(Te.mediaSourceFull)):11===e.code?(ir.stop(),ir.mediaSourceAppendBufferError=!0,ir.emitError(Te.mediaSourceAppendBufferError)):(ir.stop(),ir.mediaSourceBufferError=!0,ir.emitError(Te.mseSourceBufferError,e.code))}}}else rr.debug.log(ir.TAG_NAME,"_doAppendSegments() sourceBuffer is null and wait init and return")},_doCleanUpSourceBuffer(){const e=ir.$video.currentTime;if(ir.sourceBuffer){const t=ir.sourceBuffer.buffered;let i=!1;for(let r=0;r<t.length;r++){let s=t.start(r),a=t.end(r);if(s<=e&&e<a+3){if(e-s>=rr._opt.mseAutoCleanupMaxBackwardDuration){i=!0;let t=e-rr._opt.mseAutoCleanupMinBackwardDuration;ir.pendingRemoveRanges.push({start:s,end:t})}}else a<e&&(i=!0,ir.pendingRemoveRanges.push({start:s,end:a}))}i&&mi(ir.getSourceBufferUpdating())&&ir._doRemoveRanges()}if(ir.audioSourceBuffer){const t=ir.audioSourceBuffer.buffered;let i=!1;for(let r=0;r<t.length;r++){let s=t.start(r),a=t.end(r);if(s<=e&&e<a+3){if(e-s>=rr._opt.mseAutoCleanupMaxBackwardDuration){i=!0;let t=e-rr._opt.mseAutoCleanupMinBackwardDuration;ir.pendingAudioRemoveRanges.push({start:s,end:t})}}else a<e&&(i=!0,ir.pendingAudioRemoveRanges.push({start:s,end:a}))}i&&mi(ir.getAudioSourceBufferUpdating())&&ir._doRemoveRanges()}},_hasPendingRemoveRanges:()=>ir.pendingRemoveRanges.length>0||ir.pendingAudioRemoveRanges.length>0,needInitAudio:()=>rr._opt.hasAudio&&rr._opt.mseDecodeAudio,_doRemoveRanges(){if(ir.sourceBuffer&&mi(ir.getSourceBufferUpdating())){let e=ir.pendingRemoveRanges;for(;e.length&&mi(ir.getSourceBufferUpdating());){let t=e.shift();try{ir.sourceBuffer.remove(t.start,t.end)}catch(e){rr.debug.warn(ir.TAG_NAME,"_doRemoveRanges() sourceBuffer error",e)}}}if(ir.audioSourceBuffer&&mi(ir.getAudioSourceBufferUpdating())){let e=ir.pendingAudioRemoveRanges;for(;e.length&&mi(ir.getAudioSourceBufferUpdating());){let t=e.shift();try{ir.audioSourceBuffer.remove(t.start,t.end)}catch(e){rr.debug.warn(ir.TAG_NAME,"_doRemoveRanges() audioSourceBuffer error",e)}}}},_getPlaybackRate(){},_needCleanupSourceBuffer(){if(mi(rr._opt.mseAutoCleanupSourceBuffer))return!1;const e=ir.$video.currentTime;if(ir.sourceBuffer){let t=ir.sourceBuffer.buffered;if(t.length>=1&&e-t.start(0)>=rr._opt.mseAutoCleanupMaxBackwardDuration)return!0}if(ir.audioSourceBuffer){let t=ir.audioSourceBuffer.buffered;if(t.length>=1&&e-t.start(0)>=rr._opt.mseAutoCleanupMaxBackwardDuration)return!0}return!1},_clearAudioSourceBufferCheckTimeout(){ir.audioSourceBufferCheckTimeout&&(clearTimeout(ir.audioSourceBufferCheckTimeout),ir.audioSourceBufferCheckTimeout=null)},_clearAudioNoDataCheckTimeout(){ir.audioSourceNoDataCheckTimeout&&(clearTimeout(ir.audioSourceNoDataCheckTimeout),ir.audioSourceNoDataCheckTimeout=null)},getHandle:()=>ir.mediaSource.handle,emitError(e){postMessage({cmd:te,value:e,msg:arguments.length>1&&void 0!==arguments[1]?arguments[1]:""})}});let rr={isPlayer:!0,isPlayback:!1,dropping:!1,isPushDropping:!1,isWorkerFetch:!1,isDestroyed:!1,fetchStatus:Ut,_opt:xi(),mp3Demuxer:null,delay:-1,pushLatestDelay:-1,firstTimestamp:null,startTimestamp:null,preDelayTimestamp:null,stopId:null,streamFps:null,streamAudioFps:null,streamVideoFps:null,writableStream:null,networkDelay:0,webglObj:null,startStreamRateAndStatsInterval:function(){rr.stopStreamRateAndStatsInterval(),l=setInterval((()=>{d&&d(0);const e=JSON.stringify({demuxBufferDelay:rr.getVideoBufferLength(),audioDemuxBufferDelay:rr.getAudioBufferLength(),streamBufferByteLength:rr.getStreamBufferLength(),netBuf:rr.networkDelay||0,pushLatestDelay:rr.pushLatestDelay||0,latestDelay:rr.delay,isStreamTsMoreThanLocal:qe});postMessage({cmd:R,type:xe,value:e})}),1e3)},stopStreamRateAndStatsInterval:function(){l&&(clearInterval(l),l=null)},useOffscreen:function(){return rr._opt.useOffscreen&&"undefined"!=typeof OffscreenCanvas},getDelay:function(e,t){if(!e||rr._opt.hasVideo&&!Oe)return-1;if(t===ie)return rr.delay;if(rr.preDelayTimestamp&&rr.preDelayTimestamp>e)return rr.preDelayTimestamp-e>1e3&&rr.debug.warn("worker",`getDelay() and preDelayTimestamp is ${rr.preDelayTimestamp} > timestamp is ${e} more than ${rr.preDelayTimestamp-e}ms and return ${rr.delay}`),rr.preDelayTimestamp=e,rr.delay;if(rr.firstTimestamp){if(e){const t=Date.now()-rr.startTimestamp,i=e-rr.firstTimestamp;t>=i?(qe=!1,rr.delay=t-i):(qe=!0,rr.delay=i-t)}}else rr.firstTimestamp=e,rr.startTimestamp=Date.now(),rr.delay=-1;return rr.preDelayTimestamp=e,rr.delay},getDelayNotUpdateDelay:function(e,t){if(!e||rr._opt.hasVideo&&!Oe)return-1;if(t===ie)return rr.pushLatestDelay;if(rr.preDelayTimestamp&&rr.preDelayTimestamp-e>1e3)return rr.debug.warn("worker",`getDelayNotUpdateDelay() and preDelayTimestamp is ${rr.preDelayTimestamp} > timestamp is ${e} more than ${rr.preDelayTimestamp-e}ms and return -1`),-1;if(rr.firstTimestamp){let t=-1;if(e){const i=Date.now()-rr.startTimestamp,r=e-rr.firstTimestamp;i>=r?(qe=!1,t=i-r):(qe=!0,t=r-i)}return t}return-1},resetDelay:function(){rr.firstTimestamp=null,rr.startTimestamp=null,rr.delay=-1,rr.dropping=!1},resetAllDelay:function(){rr.resetDelay(),rr.preDelayTimestamp=null},doDecode:function(e){rr._opt.isEmitSEI&&e.type===re&&rr.isWorkerFetch&&rr.findSei(e.payload,e.ts),rr.isPlayUseMSEAndDecoderInWorker()?e.type===ie?rr._opt.mseDecodeAudio?ir.decodeAudio(e.payload,e.ts):e.decoder.decode(e.payload,e.ts):e.type===re&&ir.decodeVideo(e.payload,e.ts,e.isIFrame,e.cts):rr._opt.useWCS&&rr.useOffscreen()&&e.type===re&&a.decode?a.decode(e.payload,e.ts,e.cts):e.decoder.decode(e.payload,e.ts,e.isIFrame,e.cts)},decodeNext(e){if(0===r.length)return;const t=e.ts,s=r[0],a=e.type===re&&pi(e.payload);if(mi(i))a&&(rr.debug.log("worker",`decode data type is ${e.type} and\n                ts is ${t} next data type is ${s.type} ts is ${s.ts}\n                isVideoSqeHeader is ${a}`),r.shift(),rr.doDecode(s));else{const i=s.ts-t,n=s.type===ie&&e.type===re;(i<=20||n||a)&&(rr.debug.log("worker",`decode data type is ${e.type} and\n                ts is ${t} next data type is ${s.type} ts is ${s.ts}\n                diff is ${i} and isVideoAndNextAudio is ${n} and isVideoSqeHeader is ${a}`),r.shift(),rr.doDecode(s))}},init:function(){rr.debug.log("worker","init and opt is",JSON.stringify(rr._opt));const e=rr._opt.playType===m,t=rr._opt.playType===g;if(Ni.init(),rr.isPlayer=e,rr.isPlayback=t,rr.isPlayUseMSEAndDecoderInWorker()&&ir&&ir.init(),rr.isPlaybackCacheBeforeDecodeForFpsRender())rr.debug.log("worker","playback and playbackIsCacheBeforeDecodeForFpsRender is true");else{rr.debug.log("worker","setInterval()");const t=rr._opt.videoBuffer+rr._opt.videoBufferDelay,i=()=>{let i=null;if(r.length){if(rr.isPushDropping)return void rr.debug.warn("worker",`loop() isPushDropping is true and bufferList length is ${r.length}`);if(rr.dropping){for(i=r.shift(),rr.debug.warn("worker",`loop() dropBuffer is dropping and isIFrame ${i.isIFrame} and delay is ${rr.delay} and bufferlist is ${r.length}`);!i.isIFrame&&r.length;)i=r.shift();const e=rr.getDelayNotUpdateDelay(i.ts,i.type);i.isIFrame&&e<=rr.getNotDroppingDelayTs()&&(rr.debug.log("worker","loop() is dropping = false, is iFrame"),rr.dropping=!1,rr.doDecode(i),rr.decodeNext(i))}else if(rr.isPlayback||rr.isPlayUseMSE()||0===rr._opt.videoBuffer)for(;r.length;)i=r.shift(),rr.doDecode(i);else if(i=r[0],-1===rr.getDelay(i.ts,i.type))rr.debug.log("worker","loop() common dumex delay is -1 ,data.ts is",i.ts),r.shift(),rr.doDecode(i),rr.decodeNext(i);else if(rr.delay>t&&e)rr.hasIframeInBufferList()?(rr.debug.log("worker",`delay is ${rr.delay} > maxDelay ${t}, set dropping is true`),rr.resetAllDelay(),rr.dropping=!0,postMessage({cmd:G})):(r.shift(),rr.doDecode(i),rr.decodeNext(i));else for(;r.length;){if(i=r[0],!(rr.getDelay(i.ts,i.type)>rr._opt.videoBuffer)){rr.delay<0&&rr.debug.warn("worker",`loop() do not decode and delay is ${rr.delay}, bufferList is ${r.length}`);break}r.shift(),rr.doDecode(i)}}else-1!==rr.delay&&rr.debug.log("worker","loop() bufferList is empty and reset delay"),rr.resetAllDelay()};rr.stopId=setInterval((()=>{let e=(new Date).getTime();rt||(rt=e);const t=e-rt;t>100&&rr.debug.warn("worker",`loop demux diff time is ${t}`),i(),rt=(new Date).getTime()}),20)}if(mi(rr._opt.checkFirstIFrame)&&(Oe=!0),rr.isPlayUseMSEAndDecoderInWorker()&&ir){const e=ir.getHandle();e&&postMessage({cmd:Q,mseHandle:e},[e])}},playbackCacheLoop:function(){rr.stopId&&(clearInterval(rr.stopId),rr.stopId=null);const e=()=>{let e=null;r.length&&(e=r.shift(),rr.doDecode(e))};e();const t=Math.ceil(1e3/(rr.streamFps*rr._opt.playbackRate));rr.debug.log("worker",`playbackCacheLoop fragDuration is ${t}, streamFps is ${rr.streamFps}, streamAudioFps is ${rr.streamAudioFps} ,streamVideoFps is ${rr.streamVideoFps} playbackRate is ${rr._opt.playbackRate}`),rr.stopId=setInterval(e,t)},close:function(){if(rr.debug.log("worker","close"),rr.isDestroyed=!0,Ri(),!o||1!==o.readyState&&2!==o.readyState?o&&rr.debug.log("worker",`close() and socket.readyState is ${o.readyState}`):(vi=!0,o.close(1e3,"Client disconnecting")),o=null,rr.stopStreamRateAndStatsInterval(),rr.stopId&&(clearInterval(rr.stopId),rr.stopId=null),rr.mp3Demuxer&&(rr.mp3Demuxer.destroy(),rr.mp3Demuxer=null),rr.writableStream&&mi(rr.writableStream.locked)&&rr.writableStream.close().catch((e=>{rr.debug.log("worker","close() and writableStream.close() error",e)})),rr.writableStream=null,sr)try{sr.clear&&sr.clear(),sr=null}catch(e){rr.debug.warn("worker","close() and audioDecoder.clear error",e)}if(ar)try{ar.clear&&ar.clear(),ar=null}catch(e){rr.debug.warn("worker","close() and videoDecoder.clear error",e)}d=null,rt=null,qe=!1,a&&(a.reset&&a.reset(),a=null),ir&&(ir.destroy(),ir=null),rr.firstTimestamp=null,rr.startTimestamp=null,rr.networkDelay=0,rr.streamFps=null,rr.streamAudioFps=null,rr.streamVideoFps=null,rr.delay=-1,rr.pushLatestDelay=-1,rr.preDelayTimestamp=null,rr.dropping=!1,rr.isPushDropping=!1,rr.isPlayer=!0,rr.isPlayback=!1,rr.isWorkerFetch=!1,rr._opt=xi(),rr.webglObj&&(rr.webglObj.destroy(),rr.offscreenCanvas.removeEventListener("webglcontextlost",rr.onOffscreenCanvasWebglContextLost),rr.offscreenCanvas.removeEventListener("webglcontextrestored",rr.onOffscreenCanvasWebglContextRestored),rr.offscreenCanvas=null,rr.offscreenCanvasGL=null,rr.offscreenCanvasCtx=null),r=[],s=[],c&&(c.buffer=null,c=null),v=null,B=null,ge=!1,ye=!1,Oe=!1,Gt=!1,Wt=!1,Kt=!1,ti=null,yi=null,ht=[],St=0,Bt=0,ct=null,ut=null,Mt=null,zt=null,bi=null,Rt=0,Nt=0,ft=null,vt=null,rr.fetchStatus=Ut,Si=!0,Ni.destroy(),Vi.destroy(),er.destroy(),tr.destroy(),postMessage({cmd:W})},pushBuffer:function(e,t){if(t.type===ie&&Vt(e)){if(rr.debug.log("worker",`pushBuffer audio ts is ${t.ts}, isAacCodecPacket is true`),rr._opt.isRecordTypeFlv){const t=new Uint8Array(e);postMessage({cmd:K,buffer:t},[t.buffer])}rr.decodeAudio(e,t.ts)}else if(t.type===re&&t.isIFrame&&pi(e)){if(rr.debug.log("worker",`pushBuffer video ts is ${t.ts}, isVideoSequenceHeader is true`),rr._opt.isRecordTypeFlv){const t=new Uint8Array(e);postMessage({cmd:X,buffer:t},[t.buffer])}rr.decodeVideo(e,t.ts,t.isIFrame,t.cts)}else{if(rr._opt.isRecording)if(rr._opt.isRecordTypeFlv){const i=new Uint8Array(e);postMessage({cmd:Z,type:t.type,buffer:i,ts:t.ts},[i.buffer])}else if(rr._opt.recordType===y)if(t.type===re){const i=new Uint8Array(e).slice(5);postMessage({cmd:P,buffer:i,isIFrame:t.isIFrame,ts:t.ts,cts:t.cts},[i.buffer])}else if(t.type===ie&&rr._opt.isWasmMp4){const i=new Uint8Array(e),r=$t(i)?i.slice(2):i.slice(1);postMessage({cmd:D,buffer:r,ts:t.ts},[r.buffer])}if(rr.isPlayer&&Rt>0&&zt>0&&t.type===re){const e=t.ts-zt,i=Rt+Rt/2;e>i&&rr.debug.log("worker",`pushBuffer video\n                    ts is ${t.ts}, preTimestamp is ${zt},\n                    diff is ${e} and preTimestampDuration is ${Rt} and maxDiff is ${i}\n                    maybe trigger black screen or flower screen\n                    `)}if(rr.isPlayer&&zt>0&&t.type===re&&t.ts<zt&&zt-t.ts>S&&(rr.debug.warn("worker",`pushBuffer,\n                preTimestamp is ${zt}, options.ts is ${t.ts},\n                diff is ${zt-t.ts} more than 3600000,\n                and resetAllDelay`),rr.resetAllDelay(),zt=null,Rt=0),rr.isPlayer&&zt>0&&t.ts<=zt&&t.type===re&&(rr.debug.warn("worker",`pushBuffer() and isIFrame is ${t.isIFrame} and,\n                options.ts is ${t.ts} less than (or equal) preTimestamp is ${zt} and\n                payloadBufferSize is ${e.byteLength} and prevPayloadBufferSize is ${Nt}`),rr._opt.isDropSameTimestampGop&&mi(t.isIFrame)&&Oe)){const e=rr.hasIframeInBufferList(),t=mi(rr.isPushDropping);return rr.debug.log("worker",`pushBuffer, isDropSameTimestampGop is true and\n                    hasIframe is ${e} and isNotPushDropping is ${t} and next dropBuffer`),void(e&&t?rr.dropBuffer$2():(rr.clearBuffer(!0),_i(rr._opt.checkFirstIFrame)&&_i(i)&&(rr.isPlayUseMSEAndDecoderInWorker()?ir.isDecodeFirstIIframe=!1:postMessage({cmd:J}))))}if(rr.isPlayer&&Oe){const e=rr._opt.videoBuffer+rr._opt.videoBufferDelay,i=rr.getDelayNotUpdateDelay(t.ts,t.type);rr.pushLatestDelay=i,i>e&&rr.delay<e&&rr.delay>0&&rr.hasIframeInBufferList()&&!1===rr.isPushDropping&&(rr.debug.log("worker",`pushBuffer(), pushLatestDelay is ${i} more than ${e} and decoder.delay is ${rr.delay} and has iIframe and next decoder.dropBuffer$2()`),rr.dropBuffer$2())}if(rr.isPlayer&&t.type===re&&(zt>0&&(Rt=t.ts-zt),Nt=e.byteLength,zt=t.ts),t.type===ie?r.push({ts:t.ts,payload:e,decoder:{decode:rr.decodeAudio},type:ie,isIFrame:!1}):t.type===re&&r.push({ts:t.ts,cts:t.cts,payload:e,decoder:{decode:rr.decodeVideo},type:re,isIFrame:t.isIFrame}),rr.isPlaybackCacheBeforeDecodeForFpsRender()&&(ai(rr.streamVideoFps)||ai(rr.streamAudioFps))){let e=rr.streamVideoFps,t=rr.streamAudioFps;if(ai(rr.streamVideoFps)&&(e=ui(r,re),e&&(rr.streamVideoFps=e,postMessage({cmd:O,value:rr.streamVideoFps}),rr.streamFps=t?e+t:e,mi(rr._opt.hasAudio)&&(rr.debug.log("worker","playbackCacheBeforeDecodeForFpsRender, _opt.hasAudio is false and set streamAudioFps is 0"),rr.streamAudioFps=0),rr.playbackCacheLoop())),ai(rr.streamAudioFps)&&(t=ui(r,ie),t&&(rr.streamAudioFps=t,rr.streamFps=e?e+t:t,rr.playbackCacheLoop())),ai(rr.streamVideoFps)&&ai(rr.streamAudioFps)){const i=r.map((e=>({type:e.type,ts:e.ts})));rr.debug.log("worker",`playbackCacheBeforeDecodeForFpsRender, calc streamAudioFps is ${t}, streamVideoFps is ${e}, bufferListLength  is ${r.length}, and ts list is ${JSON.stringify(i)}`)}const i=rr.getAudioBufferLength()>0,s=i?60:40;r.length>=s&&(rr.debug.warn("worker",`playbackCacheBeforeDecodeForFpsRender, bufferListLength  is ${r.length} more than ${s}, and hasAudio is ${i} an set streamFps is 25`),rr.streamVideoFps=25,postMessage({cmd:O,value:rr.streamVideoFps}),i?(rr.streamAudioFps=25,rr.streamFps=rr.streamVideoFps+rr.streamAudioFps):rr.streamFps=rr.streamVideoFps,rr.playbackCacheLoop())}}},getVideoBufferLength(){let e=0;return r.forEach((t=>{t.type===re&&(e+=1)})),e},hasIframeInBufferList:()=>r.some((e=>e.type===re&&e.isIFrame)),isAllIframeInBufferList(){const e=rr.getVideoBufferLength();let t=0;return r.forEach((e=>{e.type===re&&e.isIFrame&&(t+=1)})),e===t},getNotDroppingDelayTs:()=>rr._opt.videoBuffer+rr._opt.videoBufferDelay/2,getAudioBufferLength(){let e=0;return r.forEach((t=>{t.type===ie&&(e+=1)})),e},getStreamBufferLength(){let e=0;return c&&c.buffer&&(e=c.buffer.byteLength),rr._opt.isNakedFlow?Ni.lastBuf&&(e=Ni.lastBuf.byteLength):rr._opt.isTs?tr._remainingPacketData&&(e=tr._remainingPacketData.byteLength):rr._opt.isFmp4&&Vi.mp4Box&&(e=Vi.mp4Box.getAllocatedSampleDataSize()),e},fetchStream:function(e,t){rr.debug.log("worker","fetchStream, url is "+e,"options:",JSON.stringify(t)),rr.isWorkerFetch=!0,t.isFlv?rr._opt.isFlv=!0:t.isFmp4?rr._opt.isFmp4=!0:t.isMpeg4?rr._opt.isMpeg4=!0:t.isNakedFlow?rr._opt.isNakedFlow=!0:t.isTs&&(rr._opt.isTs=!0),d=ii((e=>{postMessage({cmd:R,type:ve,value:e})})),rr.startStreamRateAndStatsInterval(),t.isFmp4&&(Vi.listenMp4Box(),rr._opt.isFmp4Private&&Vi.initTransportDescarmber()),t.protocol===f?(c=new wi(rr.demuxFlv()),fetch(e,{signal:n.signal}).then((e=>{if(_i(vi))return rr.debug.log("worker","request abort and run res.body.cancel()"),rr.fetchStatus=Ut,void e.body.cancel();if(!hi(e))return rr.debug.warn("worker",`fetch response status is ${e.status} and ok is ${e.ok} and emit error and next abort()`),Ri(),void postMessage({cmd:R,type:Te.fetchError,value:`fetch response status is ${e.status} and ok is ${e.ok}`});if(postMessage({cmd:R,type:Be}),li())rr.writableStream=new WritableStream({write:e=>n&&n.signal&&n.signal.aborted?(rr.debug.log("worker","writableStream write() and abortController.signal.aborted is true so return"),void(rr.fetchStatus=Tt)):_i(vi)?(rr.debug.log("worker","writableStream write() and requestAbort is true so return"),void(rr.fetchStatus=Tt)):void("string"!=typeof e?(rr.fetchStatus=Et,d(e.byteLength),t.isFlv?c.write(e):t.isFmp4?rr.demuxFmp4(e):t.isMpeg4?rr.demuxMpeg4(e):t.isTs&&rr.demuxTs(e)):rr.debug.warn("worker",`writableStream write() and value is "${e}" string so return`)),close:()=>{rr.debug.log("worker","writableStream close()"),rr.fetchStatus=Tt,c=null,Ri(),postMessage({cmd:R,type:be,value:p,msg:"fetch done"})},abort:e=>{if(n&&n.signal&&n.signal.aborted)return rr.debug.log("worker","writableStream abort() and abortController.signal.aborted is true so return"),void(rr.fetchStatus=Tt);c=null,e.name!==wt?(rr.debug.log("worker",`writableStream abort() and e is ${e.toString()}`),Ri(),postMessage({cmd:R,type:Te.fetchError,value:e.toString()})):rr.debug.log("worker","writableStream abort() and e.name is AbortError so return")}}),e.body.pipeTo(rr.writableStream);else{const i=e.body.getReader(),r=()=>{i.read().then((e=>{let{done:i,value:s}=e;return i?(rr.debug.log("worker","fetchNext().then() and done is true"),rr.fetchStatus=Tt,c=null,Ri(),void postMessage({cmd:R,type:be,value:p,msg:"fetch done"})):n&&n.signal&&n.signal.aborted?(rr.debug.log("worker","fetchNext().then() and abortController.signal.aborted is true so return"),void(rr.fetchStatus=Tt)):_i(vi)?(rr.debug.log("worker","fetchNext().then() and requestAbort is true so return"),void(rr.fetchStatus=Tt)):void("string"!=typeof s?(rr.fetchStatus=Et,d(s.byteLength),t.isFlv?c.write(s):t.isFmp4?rr.demuxFmp4(s):t.isMpeg4&&rr.demuxMpeg4(s),r()):rr.debug.warn("worker",`fetchNext().then() and value "${s}" is string so return`))})).catch((e=>{if(n&&n.signal&&n.signal.aborted)return rr.debug.log("worker","fetchNext().catch() and abortController.signal.aborted is true so return"),void(rr.fetchStatus=Tt);c=null,e.name!==wt?(rr.debug.log("worker",`fetchNext().catch() and e is ${e.toString()}`),Ri(),postMessage({cmd:R,type:Te.fetchError,value:e.toString()})):rr.debug.log("worker","fetchNext().catch() and e.name is AbortError so return")}))};r()}})).catch((e=>{n&&n.signal&&n.signal.aborted?rr.debug.log("worker","fetch().catch() and abortController.signal.aborted is true so return"):e.name!==wt?(rr.debug.log("worker",`fetch().catch() and e is ${e.toString()}`),Ri(),postMessage({cmd:R,type:Te.fetchError,value:e.toString()}),c=null):rr.debug.log("worker","fetch().catch() and e.name is AbortError so return")}))):t.protocol===h&&(t.isFlv&&(c=new wi(rr.demuxFlv())),o=new WebSocket(e),o.binaryType="arraybuffer",o.onopen=()=>{rr.debug.log("worker","fetchStream, WebsocketStream  socket open"),postMessage({cmd:R,type:Be}),postMessage({cmd:R,type:Ee})},o.onclose=e=>{b?rr.debug.log("worker","fetchStream, WebsocketStream socket close and isSocketError is true , so return"):(rr.debug.log("worker",`fetchStream, WebsocketStream socket close and code is ${e.code}`),1006===e.code&&rr.debug.error("worker",`fetchStream, WebsocketStream socket close abnormally and code is ${e.code}`),_i(vi)?rr.debug.log("worker","fetchStream, WebsocketStream socket close and requestAbort is true so return"):(c=null,postMessage({cmd:R,type:be,value:_,msg:e.code})))},o.onerror=e=>{rr.debug.error("worker","fetchStream, WebsocketStream socket error",e),b=!0,c=null,postMessage({cmd:R,type:Te.websocketError,value:e.isTrusted?"websocket user aborted":"websocket error"})},o.onmessage=e=>{"string"!=typeof e.data?(d(e.data.byteLength),t.isFlv?c.write(e.data):t.isFmp4?rr.demuxFmp4(e.data):t.isMpeg4?rr.demuxMpeg4(e.data):rr._opt.isNakedFlow?rr.demuxNakedFlow(e.data):rr.demuxM7s(e.data)):rr.debug.warn("worker",`socket on message is string "${e.data}" and return`)})},demuxFlv:function*(){yield 9;const e=new ArrayBuffer(4),t=new Uint8Array(e),i=new Uint32Array(e);for(;;){t[3]=0;const e=yield 15,r=e[4];t[0]=e[7],t[1]=e[6],t[2]=e[5];const s=i[0];t[0]=e[10],t[1]=e[9],t[2]=e[8],t[3]=e[11];let a=i[0];const n=(yield s).slice();switch(r){case se:if(n.byteLength>0){let e=n;_i(rr._opt.m7sCryptoAudio)&&(e=rr.cryptoPayloadAudio(n)),rr.decode(e,{type:ie,ts:a})}else rr.debug.warn("worker",`demuxFlv() type is audio and payload.byteLength is ${n.byteLength} and return`);break;case ae:if(n.byteLength>=6){const e=n[0];if(rr._isEnhancedH265Header(e))rr._decodeEnhancedH265Video(n,a);else{n[0];let e=n[0]>>4===xt;if(e&&pi(n)&&null===ti){const e=15&n[0];ti=e===De,yi=Jt(n,ti),rr.debug.log("worker",`demuxFlv() isVideoSequenceHeader is true and isHevc is ${ti} and nalUnitSize is ${yi}`)}e&&rr.calcIframeIntervalTimestamp(a),rr.isPlayer&&rr.calcNetworkDelay(a),i[0]=n[4],i[1]=n[3],i[2]=n[2],i[3]=0;let t=i[0],r=rr.cryptoPayload(n,e);rr.decode(r,{type:re,ts:a,isIFrame:e,cts:t})}}else rr.debug.warn("worker",`demuxFlv() type is video and payload.byteLength is ${n.byteLength} and return`);break;case ne:postMessage({cmd:j,buffer:n},[n.buffer]);break;default:rr.debug.log("worker",`demuxFlv() type is ${r}`)}}},decode:function(e,t){t.type===ie?rr._opt.hasAudio&&(postMessage({cmd:R,type:Se,value:e.byteLength}),rr.isPlayer?rr.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts}):rr.isPlayback&&(rr.isPlaybackOnlyDecodeIFrame()||(rr.isPlaybackCacheBeforeDecodeForFpsRender(),rr.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts})))):t.type===re&&rr._opt.hasVideo&&(postMessage({cmd:R,type:we,value:e.byteLength}),postMessage({cmd:R,type:Ae,value:t.ts}),rr.isPlayer?rr.pushBuffer(e,{type:t.type,ts:t.ts,isIFrame:t.isIFrame,cts:t.cts}):rr.isPlayback&&(rr.isPlaybackOnlyDecodeIFrame()?t.isIFrame&&rr.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts,isIFrame:t.isIFrame}):(rr.isPlaybackCacheBeforeDecodeForFpsRender(),rr.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts,isIFrame:t.isIFrame}))))},cryptoPayload:function(e,t){let i=e;return rr._opt.isM7sCrypto?rr._opt.cryptoIV&&rr._opt.cryptoIV.byteLength>0&&rr._opt.cryptoKey&&rr._opt.cryptoKey.byteLength>0?i=Pr(e,rr._opt.cryptoKey,rr._opt.cryptoIV,ti):rr.debug.error("worker",`isM7sCrypto cryptoKey.length is ${rr._opt.cryptoKey&&rr._opt.cryptoKey.byteLength} or cryptoIV.length is ${rr._opt.cryptoIV&&rr._opt.cryptoIV.byteLength} null`):rr._opt.isSm4Crypto?rr._opt.sm4CryptoKey&&t?i=is(e,rr._opt.sm4CryptoKey):rr._opt.sm4CryptoKey||rr.debug.error("worker","isSm4Crypto opt.sm4CryptoKey is null"):rr._opt.isXorCrypto&&(rr._opt.cryptoIV&&rr._opt.cryptoIV.byteLength>0&&rr._opt.cryptoKey&&rr._opt.cryptoKey.byteLength>0?i=ds(e,rr._opt.cryptoKey,rr._opt.cryptoIV,ti):rr.debug.error("worker",`isXorCrypto cryptoKey.length is ${rr._opt.cryptoKey&&rr._opt.cryptoKey.byteLength} or cryptoIV.length is ${rr._opt.cryptoIV&&rr._opt.cryptoIV.byteLength} null`)),i},cryptoPayloadAudio:function(e){let t=e;if(rr._opt.isM7sCrypto)if(rr._opt.cryptoIV&&rr._opt.cryptoIV.byteLength>0&&rr._opt.cryptoKey&&rr._opt.cryptoKey.byteLength>0){e[0]>>4===Pe&&(t=Mr(e,rr._opt.cryptoKey,rr._opt.cryptoIV))}else rr.debug.error("worker",`isM7sCrypto cryptoKey.length is ${rr._opt.cryptoKey&&rr._opt.cryptoKey.byteLength} or cryptoIV.length is ${rr._opt.cryptoIV&&rr._opt.cryptoIV.byteLength} null`);return t},setCodecAudio:function(e,t){const i=e[0]>>4,r=e[0]>>1&1;if(bi=i===Pe?r?16:8:0===r?8:16,sr&&sr.setCodec)if(Vt(e)||i===Me||i===ze||i===Re){rr.debug.log("worker",`setCodecAudio: init audio codec, codeId is ${i}`);const r=i===Pe?e.slice(2):new Uint8Array(0);sr.setCodec(i,rr._opt.sampleRate,r),i===Pe&&postMessage({cmd:I,buffer:r},[r.buffer]),ye=!0,i!==Pe&&(i===Re?(rr.mp3Demuxer||(rr.mp3Demuxer=new as(rr),rr.mp3Demuxer.on("data",((e,t)=>{sr.decode(e,t)}))),rr.mp3Demuxer.dispatch(e.slice(1),t)):sr.decode(e.slice(1),t))}else rr.debug.warn("worker","setCodecAudio: hasInitAudioCodec is false, codecId is ",i);else rr.debug.error("worker","setCodecAudio: audioDecoder or audioDecoder.setCodec is null")},decodeAudio:function(e,t){if(rr.isDestroyed)rr.debug.log("worker","decodeAudio, decoder is destroyed and return");else if(rr.isPlayUseMSEAndDecoderInWorkerAndMseDecodeAudio())ir.decodeAudio(e,t);else if(_i(i)&&_i(rr._opt.mseDecodeAudio))postMessage({cmd:z,payload:e,ts:t,cts:t},[e.buffer]);else{const i=e[0]>>4;if(ye){if(Vt(e))return void rr.debug.log("worker","decodeAudio and has already initialized and payload is aac codec packet so drop this frame");i===Re?rr.mp3Demuxer.dispatch(e.slice(1),t):sr.decode(i===Pe?e.slice(2):e.slice(1),t)}else rr.setCodecAudio(e,t)}},setCodecVideo:function(e){const t=15&e[0];if(ar&&ar.setCodec)if(pi(e))if(t===Ce||t===De){rr.debug.log("worker",`setCodecVideo: init video codec , codecId is ${t}`);const i=e.slice(5);if(t===Ce&&rr._opt.useSIMD){const e=Ui(i);if(e.codecWidth>w||e.codecHeight>w)return postMessage({cmd:$}),void rr.debug.warn("worker",`setCodecVideo: SIMD H264 decode video width is too large, width is ${e.codecWidth}, height is ${e.codecHeight}`)}const r=new Uint8Array(e);ge=!0,ar.setCodec(t,i),postMessage({cmd:L,code:t}),postMessage({cmd:F,buffer:r,codecId:t},[r.buffer])}else rr.debug.warn("worker",`setCodecVideo: hasInitVideoCodec is false, codecId is ${t} is not H264 or H265`);else rr.debug.warn("worker",`decodeVideo: hasInitVideoCodec is false, codecId is ${t} and frameType is ${e[0]>>4} and packetType is ${e[1]}`);else rr.debug.error("worker","setCodecVideo: videoDecoder or videoDecoder.setCodec is null")},decodeVideo:function(e,t,r){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;if(rr.isDestroyed)rr.debug.log("worker","decodeVideo, decoder is destroyed and return");else if(rr.isPlayUseMSEAndDecoderInWorker())ir.decodeVideo(e,t,r,s);else if(_i(i))postMessage({cmd:M,payload:e,isIFrame:r,ts:t,cts:s,delay:rr.delay},[e.buffer]);else if(ge)if(!Oe&&r&&(Oe=!0),Oe){if(r&&pi(e)){const t=15&e[0];let i={};if(t===Ce){i=Ui(e.slice(5))}else t===De&&(i=Oi(e));i.codecWidth&&i.codecHeight&&v&&B&&(i.codecWidth!==v||i.codecHeight!==B)&&(rr.debug.warn("worker",`\n                            decodeVideo: video width or height is changed,\n                            old width is ${v}, old height is ${B},\n                            new width is ${i.codecWidth}, new height is ${i.codecHeight},\n                            and emit change event`),Wt=!0,postMessage({cmd:H}))}if(Wt)return void rr.debug.warn("worker","decodeVideo: video width or height is changed, and return");if(Kt)return void rr.debug.warn("worker","decodeVideo: simd decode error, and return");if(pi(e))return void rr.debug.warn("worker","decodeVideo and payload is video sequence header so drop this frame");if(e.byteLength<A)return void rr.debug.warn("worker",`decodeVideo and payload is too small , payload length is ${e.byteLength}`);const i=e.slice(5);ar.decode(i,r?1:0,t)}else rr.debug.log("worker","decodeVideo first frame is not iFrame");else rr.setCodecVideo(e)},clearBuffer:function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];rr.debug.log("worker",`clearBuffer,bufferList length is ${r.length}, need clear is ${e}`),e&&(r=[]),rr.isPlayer&&(rr.resetAllDelay(),_i(rr._opt.checkFirstIFrame)&&(rr.dropping=!0,postMessage({cmd:G}))),_i(rr._opt.checkFirstIFrame)&&mi(i)&&(Oe=!1)},dropBuffer$2:function(){if(r.length>0){let e=r.findIndex((e=>_i(e.isIFrame)&&e.type===re));if(rr.isAllIframeInBufferList())for(let t=0;t<r.length;t++){const i=r[t],s=rr.getDelayNotUpdateDelay(i.ts,i.type);if(s>=rr.getNotDroppingDelayTs()){rr.debug.log("worker",`dropBuffer$2() isAllIframeInBufferList() is true, and index is ${t} and tempDelay is ${s} and notDroppingDelayTs is ${rr.getNotDroppingDelayTs()}`),e=t;break}}if(e>=0){rr.isPushDropping=!0,postMessage({cmd:G});const t=r.length;r=r.slice(e);const i=r.shift();rr.resetAllDelay(),rr.getDelay(i.ts,i.type),rr.doDecode(i),rr.isPushDropping=!1,rr.debug.log("worker",`dropBuffer$2() iFrameIndex is ${e},and old bufferList length is ${t} ,new bufferList is ${r.length} and new delay is ${rr.delay} `)}else rr.isPushDropping=!1}0===r.length&&(rr.isPushDropping=!1)},demuxM7s:function(e){const t=new DataView(e),i=t.getUint32(1,!1),r=t.getUint8(0),s=new ArrayBuffer(4),a=new Uint32Array(s);switch(r){case ie:rr.decode(new Uint8Array(e,5),{type:ie,ts:i});break;case re:if(t.byteLength>=11){const r=new Uint8Array(e,5),s=r[0];if(rr._isEnhancedH265Header(s))rr._decodeEnhancedH265Video(r,i);else{const e=t.getUint8(5)>>4==1;if(e&&(rr.calcIframeIntervalTimestamp(i),pi(r)&&null===ti)){const e=15&r[0];ti=e===De}rr.isPlayer&&rr.calcNetworkDelay(i),a[0]=r[4],a[1]=r[3],a[2]=r[2],a[3]=0;let s=a[0],n=rr.cryptoPayload(r,e);rr.decode(n,{type:re,ts:i,isIFrame:e,cts:s})}}else rr.debug.warn("worker",`demuxM7s() type is video and arrayBuffer length is ${e.byteLength} and return`)}},demuxNakedFlow:function(e){Ni.dispatch(e)},demuxFmp4:function(e){Vi.dispatch(e)},demuxMpeg4:function(e){er.dispatch(e)},demuxTs:function(e){tr.dispatch(e)},_decodeEnhancedH265Video:function(e,t){const i=e[0],r=48&i,s=15&i,a=e.slice(1,5),n=new ArrayBuffer(4),o=new Uint32Array(n),d="a"==String.fromCharCode(a[0]);if(ti=mi(d),s===It){if(r===Pt){const i=e.slice(5);if(d);else{const r=new Uint8Array(5+i.length);r.set([28,0,0,0,0],0),r.set(i,5),yi=Jt(e,ti),rr.debug.log("worker",`demuxFlv() isVideoSequenceHeader(enhancedH265) is true and isHevc is ${ti} and nalUnitSize is ${yi}`),rr.decode(r,{type:re,ts:t,isIFrame:!0,cts:0})}}}else if(s===Lt){let i=e,s=0;const a=r===Pt;if(a&&rr.calcIframeIntervalTimestamp(t),d);else{o[0]=e[4],o[1]=e[3],o[2]=e[2],o[3]=0,s=o[0];i=Yi(e.slice(8),a),i=rr.cryptoPayload(i,a),rr.decode(i,{type:re,ts:t,isIFrame:a,cts:s})}}else if(s===Ft){const i=r===Pt;i&&rr.calcIframeIntervalTimestamp(t);let s=Yi(e.slice(5),i);s=rr.cryptoPayload(s,i),rr.decode(s,{type:re,ts:t,isIFrame:i,cts:0})}},_isEnhancedH265Header:function(e){return(e&Dt)===Dt},findSei:function(e,t){let i=4;ni(yi)&&(i=yi);Xt(e.slice(5),i).forEach((e=>{const i=ti?e[0]>>>1&63:31&e[0];(ti&&(i===at||i===st)||mi(ti)&&i===$e)&&postMessage({cmd:q,buffer:e,ts:t},[e.buffer])}))},calcNetworkDelay:function(e){if(!(Oe&&e>0))return;null===ct?(ct=e,ut=Qt()):e<ct&&(rr.debug.warn("worker",`calcNetworkDelay, dts is ${e} less than bufferStartDts is ${ct}`),ct=e,ut=Qt());const t=e-ct,i=Qt()-ut,r=i>t?i-t:0;rr.networkDelay=r,r>rr._opt.networkDelay&&rr._opt.playType===m&&(rr.debug.warn("worker",`calcNetworkDelay now dts:${e}, start dts is ${ct} vs start is ${t},local diff is ${i} ,delay is ${r}`),postMessage({cmd:R,type:Ue,value:r}))},calcIframeIntervalTimestamp:function(e){null===Mt?Mt=e:Mt<e&&(vt=e-Mt,postMessage({cmd:N,value:vt}),Mt=e)},canVisibilityDecodeNotDrop:function(){return rr._opt.visibility&&v*B<=2073600},isPlaybackCacheBeforeDecodeForFpsRender:function(){return rr.isPlayback&&rr._opt.playbackIsCacheBeforeDecodeForFpsRender},isPlaybackOnlyDecodeIFrame:function(){return rr._opt.playbackRate>=rr._opt.playbackForwardMaxRateDecodeIFrame},isPlayUseMSE:function(){return rr.isPlayer&&rr._opt.useMSE&&_i(i)},isPlayUseMSEAndDecoderInWorker:function(){return rr.isPlayUseMSE()&&rr._opt.mseDecoderUseWorker},isPlayUseMSEAndDecoderInWorkerAndMseDecodeAudio:function(){return rr.isPlayUseMSEAndDecoderInWorker()&&rr._opt.mseDecodeAudio},playbackUpdatePlaybackRate:function(){rr.clearBuffer(!0)},onOffscreenCanvasWebglContextLost:function(e){rr.debug.error("worker","handleOffscreenCanvasWebglContextLost and next try to create webgl"),e.preventDefault(),Gt=!0,rr.webglObj.destroy(),rr.webglObj=null,rr.offscreenCanvasGL=null,setTimeout((()=>{rr.offscreenCanvasGL=rr.offscreenCanvas.getContext("webgl"),rr.offscreenCanvasGL&&rr.offscreenCanvasGL.getContextAttributes().stencil?(rr.webglObj=u(rr.offscreenCanvasGL,rr._opt.openWebglAlignment),Gt=!1):rr.debug.error("worker","handleOffscreenCanvasWebglContextLost, stencil is false")}),500)},onOffscreenCanvasWebglContextRestored:function(e){rr.debug.log("worker","handleOffscreenCanvasWebglContextRestored"),e.preventDefault()},videoInfo:function(e,t,i){postMessage({cmd:L,code:e}),postMessage({cmd:U,w:t,h:i}),v=t,B=i,rr.useOffscreen()&&(rr.offscreenCanvas=new OffscreenCanvas(t,i),rr.offscreenCanvasGL=rr.offscreenCanvas.getContext("webgl"),rr.webglObj=u(rr.offscreenCanvasGL,rr._opt.openWebglAlignment),rr.offscreenCanvas.addEventListener("webglcontextlost",rr.onOffscreenCanvasWebglContextLost,!1),rr.offscreenCanvas.addEventListener("webglcontextrestored",rr.onOffscreenCanvasWebglContextRestored,!1))},audioInfo:function(e,t,i){postMessage({cmd:C,code:e}),postMessage({cmd:k,sampleRate:t,channels:i,depth:bi}),Bt=i},yuvData:function(t,i){if(rr.isDestroyed)return void rr.debug.log("worker","yuvData, decoder is destroyed and return");const r=v*B*3/2;let s=e.HEAPU8.subarray(t,t+r),a=new Uint8Array(s);if(ft=null,rr.useOffscreen())try{if(Gt)return;rr.webglObj.renderYUV(v,B,a);let e=rr.offscreenCanvas.transferToImageBitmap();postMessage({cmd:E,buffer:e,delay:rr.delay,ts:i},[e])}catch(e){rr.debug.error("worker","yuvData, transferToImageBitmap error is",e)}else postMessage({cmd:E,output:a,delay:rr.delay,ts:i},[a.buffer])},pcmData:function(e,i,r){if(rr.isDestroyed)return void rr.debug.log("worker","pcmData, decoder is destroyed and return");let a=i,n=[],o=0,d=rr._opt.audioBufferSize;for(let i=0;i<2;i++){let r=t.HEAPU32[(e>>2)+i]>>2;n[i]=t.HEAPF32.subarray(r,r+a)}if(St){if(!(a>=(i=d-St)))return St+=a,s[0]=Float32Array.of(...s[0],...n[0]),void(2==Bt&&(s[1]=Float32Array.of(...s[1],...n[1])));ht[0]=Float32Array.of(...s[0],...n[0].subarray(0,i)),2==Bt&&(ht[1]=Float32Array.of(...s[1],...n[1].subarray(0,i))),postMessage({cmd:T,buffer:ht,ts:r},ht.map((e=>e.buffer))),o=i,a-=i}for(St=a;St>=d;St-=d)ht[0]=n[0].slice(o,o+=d),2==Bt&&(ht[1]=n[1].slice(o-d,o)),postMessage({cmd:T,buffer:ht,ts:r},ht.map((e=>e.buffer)));St&&(s[0]=n[0].slice(o),2==Bt&&(s[1]=n[1].slice(o))),n=[]},errorInfo:function(e){null===ft&&(ft=Qt());const t=Qt(),i=ei(vt>0?2*vt:5e3,1e3,5e3),r=t-ft;r>i&&(rr.debug.warn("worker",`errorInfo() emit simdDecodeError and\n                iframeIntervalTimestamp is ${vt} and diff is ${r} and maxDiff is ${i}\n                and replay`),Kt=!0,postMessage({cmd:V}))},sendWebsocketMessage:function(e){o?o.readyState===ke?o.send(e):rr.debug.error("worker","socket is not open"):rr.debug.error("worker","socket is null")},timeEnd:function(){},postStreamToMain(e,t){postMessage({cmd:Y,type:t,buffer:e},[e.buffer])}};rr.debug=new Ai(rr);let sr=null;t.AudioDecoder&&(sr=new t.AudioDecoder(rr));let ar=null;e.VideoDecoder&&(ar=new e.VideoDecoder(rr)),postMessage({cmd:x}),self.onmessage=function(e){let t=e.data;switch(t.cmd){case oe:try{rr._opt=Object.assign(rr._opt,JSON.parse(t.opt))}catch(e){}rr.init();break;case de:rr.pushBuffer(t.buffer,t.options);break;case le:rr.decodeAudio(t.buffer,t.ts);break;case ce:rr.decodeVideo(t.buffer,t.ts,t.isIFrame);break;case fe:rr.clearBuffer(t.needClear);break;case pe:rr.fetchStream(t.url,JSON.parse(t.opt));break;case ue:rr.debug.log("worker","close",JSON.stringify(t.options)),t.options&&mi(t.options.isVideoInited)&&(Si=t.options.isVideoInited),rr.close();break;case he:rr.debug.log("worker","updateConfig",t.key,t.value),rr._opt[t.key]=t.value,"playbackRate"===t.key&&(rr.playbackUpdatePlaybackRate(),rr.isPlaybackCacheBeforeDecodeForFpsRender()&&rr.playbackCacheLoop());break;case _e:rr.sendWebsocketMessage(t.message);break;case me:ir.$video.currentTime=Number(t.message)}}}({},{},!0)}));
