<template>
  <el-dialog
    :title="title"
    v-if="visible"
    :model-value="visible"
    width="600px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form class="app-form" ref="dataFormRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="负载品牌" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入负载品牌"
          maxlength="64"
          @blur="form.name = $event.target.value.trim()"
        />
      </el-form-item>
      <el-form-item label="负载名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入负载名称"
          maxlength="64"
          @blur="form.name = $event.target.value.trim()"
        />
      </el-form-item>
      <el-form-item label="负载ID" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入负载ID"
          maxlength="64"
          @blur="form.name = $event.target.value.trim()"
        />
      </el-form-item>
      <el-form-item label="负载型号" prop="task_type">
        <el-select v-model="form.task_type" placeholder="请选择负载型号">
          <el-option v-for="item in optionData.typeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="重量" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入重量"
          maxlength="64"
          @blur="form.name = $event.target.value.trim()"
        />
      </el-form-item>
      <el-form-item label="负载类型" prop="task_type">
        <el-select v-model="form.task_type" placeholder="请选择负载类型">
          <el-option v-for="item in optionData.typeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item  label="所属组织" prop="orgId" class="mt-[16px]">
        <el-tree-select
          v-model="form.orgId"
          placeholder="请选择组织"
          :data="deptList"
          filterable
          check-strictly
          :props="{
            children: 'children',
            label: 'orgName',
            value: 'id',
            disabled: ''
          }"
          :render-after-expand="false"
        />
      </el-form-item>

    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-loading="loading">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import optionData from '@/utils/option-data';
import moment from 'moment';
import { getWaylines } from '@/api/wayline';
import { getDevicesBound } from '@/api/devices';
import { createFlightTask } from '@/api/task';
import { DOMAIN, TASK_TYPE } from '@/utils/constants';
import { Plus, Minus } from '@element-plus/icons-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  formData: {
    type: Object,
    default(rawProps) {
      return {};
    }
  }
});
const form = reactive({});
const dataFormRef = ref(ElForm);
const airLineOptions = ref([]);
const exeAirportOptions = ref([]);
const select_time_number = ref(1);
const select_execute_ref = ref();
const wayline_type = ref(null);
watch(
  () => props.formData,
  (newVal, oldVal) => {
    Object.assign(form, newVal);
  },
  { deep: true }
);
const emit = defineEmits(['update:visible', 'submit']);
const rules = reactive({
  name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  task_type: [{ required: true, message: '请选择任务类型', trigger: 'blur' }],
  select_execute_date: [{ required: true, message: '请选择日期', trigger: 'blur' }],
  select_time: [
    {
      validator: async (rule, value, callback) => {
        validEndTime(callback);
        validStartTime(callback);
        if (form.select_time.length < select_time_number.value) {
          callback(new Error('选择时间'));
        }
        validOverlapped(callback);
      }
    }
  ],
  select_execute_rang_time: [{ required: true, message: '请选择周期时间', trigger: 'blur' }],
  dock_sn: [{ required: true, message: '请选择执行机场', trigger: 'blur' }],
  file_id: [{ required: true, message: '请选择航线', trigger: 'blur' }],
  rth_altitude: [{ required: true, message: '请输入返航高度', trigger: 'blur' }],
  out_of_control_action: [
    {
      required: true,
      message: '请选择航线失控动作',
      trigger: ['blur']
    }
  ]
});

defineExpose({ setDefaultValue });
function timeChange() {
  select_execute_ref.value.validate();
}
function disabledDate(time) {
  return moment(time) < moment().subtract(1, 'days');
}
function validStartTime(callback) {
  for (let i = 0; i < form.select_time.length; i++) {
    if (!form.select_time[i][0]) {
      callback(new Error('请选择开始时间'));
    }
  }
}

function validEndTime(callback) {
  if (TASK_TYPE.Condition !== form.task_type) return;
  for (let i = 0; i < form.select_time.length; i++) {
    if (!form.select_time[i][1]) {
      callback(new Error('请选择结束时间'));
    }
    if (form.select_time[i][0] && moment(form.select_time[i][1]).isSameOrBefore(moment(form.select_time[i][0]))) {
      callback(new Error('结束时间必须大于开始时间'));
    }
  }
}
function validOverlapped(callback) {
  if (TASK_TYPE.Condition !== form.task_type) return;
  const arr = form.select_time.slice();
  arr.sort((a, b) => moment(a[0]).unix() - moment(b[0]).unix());
  arr.forEach((v, i, arr) => {
    if (i > 0 && v[0] < arr[i - 1][1]) {
      throw new Error('Overlapping time periods.');
    }
  });
}
// 设置默认值
function setDefaultValue() {
  if (!form.task_type && optionData.typeOptions.length > 0) {
    form.task_type = optionData.typeOptions[0].value;
  }
  if (!form.rth_altitude) {
    form.rth_altitude = 100;
  }
  if (!form.out_of_control_action && optionData.outofControlActionList.length > 0) {
    form.out_of_control_action = optionData.outofControlActionList[0].value;
  }
  if (!form.select_time) {
    form.select_time = [[]];
  }
}
const loading = ref(false);

// 关闭弹窗
function closeDialog() {
  resetForm();
  emit('update:visible', false);
}
/**
 * 重置表单
 */
function resetForm() {
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  loading.value = false;
  Object.keys(form).map(key => {
    delete form[key];
  });
}

function handleSubmit() {
  dataFormRef.value.validate(isValid => {
    if (isValid) {
      const task_days = []; // 日期
      if (form.task_type === TASK_TYPE.Timed || form.task_type === TASK_TYPE.Condition) {
        if (form.select_execute_date.length === 2) {
          for (
            let i = moment(form.select_execute_date[0]);
            i.isSameOrBefore(moment(form.select_execute_date[1]));
            i.add(1, 'days')
          ) {
            task_days.push(i.unix());
          }
        }
      }
      const task_periods = [];
      if (form.task_type === TASK_TYPE.Timed || form.task_type === TASK_TYPE.Condition) {
        // 定时任务时间
        for (let i = 0; i < form.select_time.length; i++) {
          const result = [];
          result.push(moment(form.select_time[i][0]).unix());
          if (form.task_type === TASK_TYPE.Condition) {
            result.push(moment(form.select_time[i][1]).unix());
          }
          task_periods.push(result);
        }
      }
      let wayline_type = '';
      if (form.file_id) {
        const findObj = airLineOptions.value.find(item => item.id === form.file_id);
        if (findObj && findObj.template_types && findObj.template_types.length > 0) {
          wayline_type = findObj.template_types[0];
        }
      }
      let params = { ...form, task_days, task_periods, wayline_type };

      loading.value = true;
      createFlightTask(params)
        .then(res => {
          loading.value = false;

          ElMessage.success('新增成功');

          closeDialog();
          emit('submit');
        })
        .catch(e => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}
function addTime() {
  dataFormRef.value.validateField('select_time', valid => {
    if (valid) {
      form.select_time.push([]);
      select_time_number.value = select_time_number.value + 1;
    }
  });
}
function removeTime(index) {
  if (select_time_number.value === 1) return;
  select_time_number.value = select_time_number.value - 1;
  form.select_time.splice(index, 1);
}
function getLines() {
  getWaylines({
    order_by: 'update_time desc',
    page: 1,
    page_size: 50
  }).then(res => {
    const { list = [] } = res;
    airLineOptions.value = list;
  });
}
function getDevices() {
  getDevicesBound({
    domain: DOMAIN.DOCK,
    page: 1,
    page_size: 50
  }).then(res => {
    const { list = [] } = res;
    exeAirportOptions.value = list;
  });
}
onMounted(() => {
  getLines();
  getDevices();
});
</script>
<style scoped lang="scss">
.input-serach {
  width: 200px;
}
.app-form {
  .select-time {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 5px 0;
  }
  ::v-deep {
    .el-input-number,
    .el-select {
      width: 100%;
    }
    .el-input-number .el-input__inner {
      text-align: left;
    }
    .el-input-number.is-controls-right .el-input__wrapper {
      padding-left: 11px;
    }
    .el-date-editor.el-input,
    .el-date-editor.el-input__wrapper {
      width: 100%;
    }
  }
}
</style>
