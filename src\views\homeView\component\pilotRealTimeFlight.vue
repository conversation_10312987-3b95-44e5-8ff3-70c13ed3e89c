<script>
export default {
  name: 'RealTimeFight'
};
</script>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { EBizCode } from '@/utils/constants';
import { useConnectWebSocket } from '@/hooks/useConnectWebSocket';
import { useDeviceStateStore } from '@/store/modules/deviceState.js';
import PilotVideo from './pilotVideo.vue';
import navPlane from './navPlane.vue';
import RealTimeFlyMap from '@/views/map/map-fly-manager/realtimeflight/pilotIndex.vue';
import { getCameraSelected } from '../../../api/devices';
import shareVideo from '@/views/homePage/component/shareVideo.vue';

const router = useRouter();
const deviceStateStore = useDeviceStateStore();
const realTimeFlyMapRef = ref(null);
const navPlaneRef = ref(null);
const videoData = ref([]); //无人机视频
const status = ref('14');
const shareVisible = ref(false);
const shareData = ref([]);
// 无人机、机场属性定时器
let intervalTimer = null;
// 当前机场携带信息
let dockInfo = ref(null);

useConnectWebSocket(payload => {
  if (!payload) {
    return;
  }
  switch (payload.biz_code) {
    // 机场信息更新
    case EBizCode.DockOsd: {
      const info = payload.data;
      deviceStateStore.setCurrentDock(info);
      break;
    }
    // 遥控器信息更新
    case EBizCode.GatewayOsd: {
      // console.log('----gateway_osd', payload);
      break;
    }
    // 飞机信息更新
    case EBizCode.DeviceOsd: {
      const info = payload.data;
      deviceStateStore.setCurrentNav(info);
      break;
    }
  }
});

// 获取路由携带参数
function handleRouteParams(query) {
  dockInfo.value = query;
  getCameraSelected({
    device_sn: query.device_sn
  }).then(data => {
    if (!data.source && !data.url_type && !data.index) {
      videoData.value = [];
      return;
    }
    let video = {
      nickname: query.device_nickname,
      device_sn: query.device_sn,
      droneSelected: query.device_sn,
      url_type: data.url_type,
      index: data.index,
      source: data.source,
      claritySelected: 0,
      cameraId: '417dc9aa-0db3-4cd8-91ea-1387ae59e716'
    };
    videoData.value = [video];
  });
}

watch(
  () => status.value,
  newVal => {
    if (newVal != undefined && newVal != 14) {
      const currentQuery = router.currentRoute.value.query;
      getCameraSelected({
        device_sn: currentQuery.device_sn
      }).then(data => {
        if (!data.source && !data.url_type && !data.index) {
          videoData.value = [];
          return;
        }
        let video = {
          nickname: currentQuery.device_nickname,
          device_sn: currentQuery.device_sn,
          droneSelected: currentQuery.device_sn,
          index: data.index,
          url_type: data.url_type,
          source: data.source,
          claritySelected: 0,
          cameraId: '417dc9aa-0db3-4cd8-91ea-1387ae59e716'
        };
        videoData.value = [video];
      });
    } else {
      const currentQuery = router.currentRoute.value.query;
      let video = {
        nickname: currentQuery.device_nickname,
        device_sn: null,
        droneSelected: null,
        index: null,
        source: '2',
        claritySelected: null,
        cameraId: null
      };
      videoData.value = [video];
    }
  }
);

onMounted(() => {
  const currentQuery = router.currentRoute.value.query;
  handleRouteParams(currentQuery);
  // 更新飞手无人机位置、信息等状态
  intervalTimer = setInterval(() => {
    if (dockInfo.value) {
      // let dockOptions = { dockInfo: dockInfo.value, osdInfo: deviceStateStore.getDockBySn(dockInfo.value.dock_sn) };
      // realTimeFlyMapRef.value?.setDockModel(dockOptions.osdInfo, dockOptions.dockInfo);
      // airStatus.value = toRaw(toRaw(dockOptions).osdInfo).basic_osd.mode_code;
      // dockPlaneRef.value.setComponentData(dockOptions);
      let navOptions = { navInfo: dockInfo.value, osdInfo: deviceStateStore.getNavBySn(dockInfo.value.device_sn) };
      status.value = toRaw(navOptions).osdInfo && toRaw(navOptions)?.osdInfo?.mode_code;
      realTimeFlyMapRef.value.setNavModel(navOptions.osdInfo, navOptions.navInfo);
      navPlaneRef.value.setComponentData(navOptions);
    }
  }, 1000);
});

function openShare() {
  shareVisible.value = true;
}

function choseShare() {
  shareVisible.value = false;
}

onUnmounted(() => {
  clearInterval(intervalTimer);
});
</script>

<template>
  <div class="home-container">
    <!-- 右侧警情列表以及无人机列表 -->
    <div class="info-box">
      <div style="height: 32%">
        <!-- 通过url is_carrier判断是否为运载机 -->
        <navPlane ref="navPlaneRef" :isCarrier="router.currentRoute.value.query.is_carrier === 'true' ? true : false" />
      </div>
      <div class="airport-box">
        <pilot-video
          :show-share="true"
          :show-refresh="false"
          :show-up="true"
          playerId="livePlayerJiChang"
          :show-close="false"
          :visible="true"
          :device="videoData"
          @showShare="openShare"
          :show-blow-up="false"
        />
      </div>
    </div>
    <!-- 地图区域 -->
    <div class="map-container">
      <div class="map-box">
        <div class="alarm-title">
          <svg-icon icon-class="title" style="margin-right: 4" />
          <span>{{ '地图' }}</span>
        </div>
        <RealTimeFlyMap ref="realTimeFlyMapRef" />
      </div>
      <div :class="shareVisible ? 'share-box' : 'share-box-none'">
        <share-video :visible="shareVisible" :shareData="videoData" @closeDialog="choseShare" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.airport-box {
  width: 100%;
  height: 68%;
  overflow: hidden;
}
.alarm-title {
  height: 40px;
  line-height: 38px;
  background: #11253e;
  color: #fff;
  font-family: SourceHanSansSC-Bold;
  font-size: 14px;
  text-align: left;
  font-weight: 700;
  padding-left: 8px;
  position: absolute;
  top: 0px;
  width: 100%;
  z-index: 2;
  border-top: 2px solid #001129;
  border-bottom: 1px solid #344054;
  .nick {
    font-size: 14px;
    color: #f5f6f8;
    text-align: left;
    font-weight: 400;
  }
  .status {
    margin-top: 7px;
    background: rgba(42, 139, 125, 0.5);
    border-radius: 2px;
    width: 110px;
    height: 24px;
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;
    color: #39bfa4;
    text-align: center;
    line-height: 22px;
    font-weight: 400;
    &.is-active {
      color: #98a2b3;
      background: rgba(44, 62, 86, 0.5);
    }
  }
}
.home-container {
  position: relative;
  display: flex;
  flex-direction: row;
  height: calc(100vh - 60px);
  width: 100%;
  background: #fff;
  overflow: hidden;
}
.ai-box {
  position: absolute;
  top: 40px;
  right: 0;
  width: 480px;
  height: 320px;
  z-index: 9999;
}
.task-container {
  width: 456px;
  position: absolute;
  right: 336px;
  top: 10px;
}
.video-container {
  width: 416px;
  position: absolute;
  left: 0;
  bottom: 0;
}
.map-container {
  position: relative;
  width: 46.6%;
  height: 100%;
  .share-box {
    position: absolute;
    width: 400px;
    height: 535px;
    top: 270px;
    left: 10px;
    z-index: 100;
  }
  .share-box-none {
    position: absolute;
    width: 0px;
    height: 535px;
    top: 45px;
    left: 10px;
    z-index: 100;
  }
  .control-box {
    width: 100%;
    position: absolute;
    bottom: 0;
    right: 0;
    .hidden-box {
      width: 80px;
      height: 24px;
      top: -24px;
      left: 50%;
      cursor: pointer;
      position: absolute;
      background-image: url('@/assets/hidden-box.png');
      z-index: 9999;
    }
    .show-box {
      width: 80px;
      height: 24px;
      top: -24px;
      left: 50%;
      cursor: pointer;
      position: absolute;
      background-image: url('@/assets/show-box.png');
      z-index: 9999;
    }
  }
  .map-box {
    width: 100%;
    height: 100%;
  }
}
.info-box {
  width: 53.4%;
  height: 100%;
  z-index: 2;
  border-top: 2px solid #001129;
}
</style>
