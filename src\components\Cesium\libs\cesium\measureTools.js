import * as Cesium from 'cesium';

/***
 * 坐标转换 84转笛卡尔
 *
 * @param {Object} {lng,lat,alt} 地理坐标
 *
 * @return {Object} Cartesian3 三维位置坐标
 */
export function transformWGS84ToCartesian(position, alt) {
  return position
    ? Cesium.Cartesian3.fromDegrees(
        position.lng || position.lon,
        position.lat,
        (position.alt = alt || position.alt),
        Cesium.Ellipsoid.WGS84
      )
    : Cesium.Cartesian3.ZERO;
}
/***
 * 坐标数组转换 笛卡尔转84
 *
 * @param {Array} WSG84Arr {lng,lat,alt} 地理坐标数组
 * @param {Number} alt 拔高
 * @return {Array} Cartesian3 三维位置坐标数组
 */
export function transformWGS84ArrayToCartesianArray(WSG84Arr, alt) {
  if (WSG84Arr) {
    return WSG84Arr
      ? WSG84Arr.map(function (item) {
          return transformWGS84ToCartesian(item, alt);
        })
      : [];
  }
}
/***
 * 坐标转换 笛卡尔转84
 *
 * @param {Object} Cartesian3 三维位置坐标
 *
 * @return {Object} {lng,lat,alt} 地理坐标
 */
export function transformCartesianToWGS84(cartesian) {
  if (cartesian) {
    const ellipsoid = Cesium.Ellipsoid.WGS84;
    const cartographic = ellipsoid.cartesianToCartographic(cartesian);
    return {
      lng: Cesium.Math.toDegrees(cartographic.longitude),
      lat: Cesium.Math.toDegrees(cartographic.latitude),
      alt: cartographic.height
    };
  }
}
/***
 * 坐标数组转换 笛卡尔转86
 *
 * @param {Array} cartesianArr 三维位置坐标数组
 *
 * @return {Array} {lng,lat,alt} 地理坐标数组
 */
export function transformCartesianArrayToWGS84Array(cartesianArr) {
  return cartesianArr && cartesianArr.length
    ? cartesianArr.map(function (item) {
        return transformCartesianToWGS84(item);
      })
    : [];
}
/**
 * 84坐标转弧度坐标
 * @param {Object} position wgs84
 * @return {Object} Cartographic 弧度坐标
 *
 */
export function transformWGS84ToCartographic(position) {
  return position
    ? Cesium.Cartographic.fromDegrees(position.lng || position.lon, position.lat, position.alt)
    : Cesium.Cartographic.ZERO;
}
/**
 * 获取84坐标的距离
 * @param {*} positions
 */
export function getPositionDistance(positions) {
  let distance = 0;
  for (let i = 0; i < positions.length - 1; i++) {
    const point1cartographic = transformWGS84ToCartographic(positions[i]);
    const point2cartographic = transformWGS84ToCartographic(positions[i + 1]);
    const geodesic = new Cesium.EllipsoidGeodesic();
    geodesic.setEndPoints(point1cartographic, point2cartographic);
    let s = geodesic.surfaceDistance;
    s = Math.sqrt(Math.pow(s, 2) + Math.pow(point2cartographic.height - point1cartographic.height, 2));
    distance = distance + s;
  }
  return distance.toFixed(3);
}
/**
 * 计算一组坐标组成多边形的面积
 * @param {*} positions
 */
export function getPositionsArea(positions) {
  let result = 0;
  if (positions) {
    let h = 0;
    const ellipsoid = Cesium.Ellipsoid.WGS84;
    positions.push(positions[0]);
    for (let i = 1; i < positions.length; i++) {
      const oel = ellipsoid.cartographicToCartesian(transformWGS84ToCartographic(positions[i - 1]));
      const el = ellipsoid.cartographicToCartesian(transformWGS84ToCartographic(positions[i]));
      h += oel.x * el.y - el.x * oel.y;
    }
    result = Number(Math.abs(h).toFixed(2));
  }
  return result;
}
/**
 * 拾取位置点
 *
 * @param {Object} px 屏幕坐标
 *
 * @return {Object} Cartesian3 三维坐标
 */
export function getCatesian3FromPX(px, viewer) {
  if (!px) {
    return false;
  }

  let isOn3dtiles = false;
  let isOnTerrain = false;

  for (const pick of viewer.scene.drillPick(px)) {
    if (
      pick &&
      (pick.primitive instanceof Cesium.Cesium3DTileFeature ||
        pick.primitive instanceof Cesium.Cesium3DTileset ||
        pick.primitive instanceof Cesium.Model)
    ) {
      isOn3dtiles = true;
    }

    if (isOn3dtiles) {
      viewer.scene.pick(px);
      const cartesian = viewer.scene.pickPosition(px);

      if (cartesian) {
        const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
        if (cartographic.height < 0) {
          cartographic.height = 0;
        }
        const position = transformWGS84ToCartesian(
          {
            lng: Cesium.Math.toDegrees(cartographic.longitude),
            lat: Cesium.Math.toDegrees(cartographic.latitude),
            alt: cartographic.height
          },
          null
        );
        return position;
      }
    }
  }

  if (!isOn3dtiles && !(viewer.terrainProvider instanceof Cesium.EllipsoidTerrainProvider)) {
    const ray = viewer.scene.camera.getPickRay(px);

    if (!ray) {
      return false;
    }

    const cartesian = viewer.scene.globe.pick(ray, viewer.scene);

    if (cartesian) {
      isOnTerrain = true;
      return cartesian;
    }
  }

  if (!isOn3dtiles && !isOnTerrain) {
    const cartesian = viewer.scene.camera.pickEllipsoid(px, viewer.scene.globe.ellipsoid);

    if (cartesian) {
      const position = transformCartesianToWGS84(cartesian);

      if (position.alt < 0) {
        return transformWGS84ToCartesian(position, 0.1);
      }

      return cartesian;
    }
  }

  return false;
}
// 获取点样式
export function getPointStyle() {
  return {
    pixelSize: 4,
    outlineColor: Cesium.Color.BLUE,
    outlineWidth: 2
  };
}
// 获取线样式
export function getLineStyle(value) {
  return {
    width: 4,
    material: Cesium.Color.BLUE,
    clampToGround: value
  };
}
/**
 * 获取测距label样式
 * @param {Number} num
 */
export function getDrawLineMeasureLabel(num) {
  return {
    text:
      Number(num) === 0
        ? '起点'
        : Number(num) > 1000
        ? (Number(num) / 1000).toFixed(4) + ' km'
        : Number(num).toFixed(2) + ' m',
    show: true,
    showBackground: true,
    font: '14px monospace',
    horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
    disableDepthTestDistance: Number.POSITIVE_INFINITY,
    pixelOffset: new Cesium.Cartesian2(0, -20) //left top
  };
}
/**
 * 测距
 */
export function drawLineMeasureGraphics(viewer, drawLayer, handlers) {
  if (viewer) {
    const positions = [];
    const lineEntity = new Cesium.Entity();
    const movePointEntity = new Cesium.Entity();
    const moveLineEntityL = new Cesium.Entity();
    // 添加线段
    lineEntity.polyline = getLineStyle(false);
    lineEntity.polyline.positions = new Cesium.CallbackProperty(() => {
      return positions;
    }, false);
    drawLayer.entities.add(lineEntity);
    // 注册场景点击事件
    // let handlers = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    // left
    handlers.setInputAction(function (movement) {
      const cartesian = getCatesian3FromPX(movement.position, viewer);
      if (cartesian && cartesian.x) {
        if (positions.length == 0) {
          drawLayer.entities.add(movePointEntity);
          drawLayer.entities.add(moveLineEntityL);
        } else {
          const len = positions.length - 1;
          if (cartesian.x === positions[len].x && cartesian.y === positions[len].y && cartesian.z === positions[len].z)
            return;
        }
        // 添加量测信息点
        positions.push(cartesian);
        addInfoPoint(cartesian);
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

    handlers.setInputAction(function (movement) {
      const cartesian = getCatesian3FromPX(movement.endPosition, viewer);
      if (positions.length >= 1) {
        if (cartesian && cartesian.x) {
          moveLineEntityL.polyline = getLineStyle(false);
          moveLineEntityL.polyline.positions = new Cesium.CallbackProperty(() => {
            return [positions[positions.length - 1], cartesian];
          }, false);
          // 实时计算长度
          movePointEntity.position = cartesian.clone();
          movePointEntity.point = getPointStyle();
          const change84 = transformCartesianArrayToWGS84Array(positions.concat([cartesian.clone()]));
          const result84 = getPositionDistance(change84);
          movePointEntity.label = getDrawLineMeasureLabel(result84);
        }
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    // right
    handlers.setInputAction(function () {
      handlers.destroy();
      handlers = null;
      drawLayer.entities.remove(movePointEntity);
      drawLayer.entities.remove(moveLineEntityL);
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);

    //添加坐标点
    function addInfoPoint(position) {
      const labelEntity = new Cesium.Entity();
      labelEntity.position = position;
      labelEntity.point = getPointStyle();
      const change84 = transformCartesianArrayToWGS84Array(positions);
      const result84 = getPositionDistance(change84);
      labelEntity.label = getDrawLineMeasureLabel(result84);
      drawLayer.entities.add(labelEntity);
    }
  }
}
/**
 * 测面积
 */
export function drawAreaMeasureGraphics(viewer, drawLayer, handler) {
  if (viewer) {
    const positions = [];
    let movePositions = [];
    const polygonEntity = new Cesium.Entity();
    polygonEntity.polyline = getLineStyle(true);
    polygonEntity.polyline.positions = new Cesium.CallbackProperty(() => {
      return movePositions.length > 2 ? movePositions.concat([positions[0]]) : movePositions;
    }, false);

    drawLayer.entities.add(polygonEntity);
    // const customLabel = "";
    // const handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    // left
    handler.setInputAction(function (movement) {
      const cartesian = getCatesian3FromPX(movement.position, viewer);
      if (cartesian && cartesian.x) {
        if (positions.length > 0) {
          const len = positions.length - 1;
          if (
            cartesian.x === positions[len].x &&
            cartesian.y === positions[len].y &&
            cartesian.z === positions[len].z
          ) {
            return;
          }
        }
        positions.push(cartesian);
        // hierarchy.positions.push(cartesian);
        addPoint(cartesian);
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    // mouse
    handler.setInputAction(function (movement) {
      const cartesian = getCatesian3FromPX(movement.endPosition, viewer);
      if (positions.length >= 1) {
        if (cartesian && cartesian.x) {
          if (positions.length >= 2) {
            polygonEntity.label = getLabelStyle(positions.concat([cartesian]));
          }
          polygonEntity.position = cartesian;
          movePositions = positions.concat([cartesian]);
          // hierarchy.positions = movePositions;
        }
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

    // right
    handler.setInputAction(function () {
      movePositions = positions;
      polygonEntity.label = null;
      handler.destroy();
      // 添加信息点
      addInfoPoint(positions[positions.length - 1]);
      // viewer.zoomTo(polygonEntity);
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);

    //添加坐标点
    function addPoint(position) {
      const labelEntity = new Cesium.Entity();
      labelEntity.position = position;
      labelEntity.point = getPointStyle();
      drawLayer.entities.add(labelEntity);
    }

    function addInfoPoint(position) {
      const customLabelEntity = new Cesium.Entity();
      customLabelEntity.position = position;
      customLabelEntity.label = getLabelStyle(positions);
      drawLayer.entities.add(customLabelEntity);
    }
    // 获取面积的label
    function getLabelStyle(positions) {
      const area = getPositionsArea(transformCartesianArrayToWGS84Array(positions));
      return {
        text: area > 1000000.0 ? (area / 1000000.0).toFixed(4) + '平方公里' : area + '平方米',
        show: true,
        showBackground: true,
        font: '14px monospace',
        horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        pixelOffset: new Cesium.Cartesian2(-20, -50) //left top
      };
    }
  }
}
/**
 * 画三角量测
 * @param {*} options
 */
export function drawTrianglesMeasureGraphics(options, viewer) {
  options.style = options.style || {
    width: 3,
    material: Cesium.Color.BLUE.withAlpha(0.5)
  };
  if (viewer && options) {
    const trianglesEntity = new Cesium.Entity(),
      tempLineEntity = new Cesium.Entity(),
      tempLineEntity2 = new Cesium.Entity(),
      positions = [],
      tempPoints = [],
      tempPoints2 = [],
      handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    // 高度
    function _getHeading(startPosition, endPosition) {
      if (!startPosition && !endPosition) return 0;
      if (Cesium.Cartesian3.equals(startPosition, endPosition)) return 0;
      const cartographic = Cesium.Cartographic.fromCartesian(startPosition);
      const cartographic2 = Cesium.Cartographic.fromCartesian(endPosition);
      return (cartographic2.height - cartographic.height).toFixed(2);
    }
    // 偏移点
    function computesHorizontalLine(positions) {
      const cartographic = Cesium.Cartographic.fromCartesian(positions[0]);
      const cartographic2 = Cesium.Cartographic.fromCartesian(positions[1]);
      return Cesium.Cartesian3.fromDegrees(
        Cesium.Math.toDegrees(cartographic.longitude),
        Cesium.Math.toDegrees(cartographic.latitude),
        cartographic2.height
      );
    }
    // left
    handler.setInputAction(function (movement) {
      const position = getCatesian3FromPX(movement.position, viewer);
      if (!position && !position.z) {
        return false;
      }
      if (positions.length == 0) {
        positions.push(position.clone());
        positions.push(position.clone());
        tempPoints.push(position.clone());
        tempPoints.push(position.clone());
      } else {
        handler.destroy();
        if (typeof options.callback === 'function') {
          options.callback({
            e: trianglesEntity,
            e2: tempLineEntity,
            e3: tempLineEntity2
          });
        }
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    // mouse
    handler.setInputAction(function (movement) {
      const position = getCatesian3FromPX(movement.endPosition, viewer);
      if (position && positions.length > 0) {
        //直线
        positions.pop();
        positions.push(position.clone());
        const horizontalPosition = computesHorizontalLine(positions);
        //高度
        tempPoints.pop();
        tempPoints.push(horizontalPosition.clone());
        //水平线
        tempPoints2.pop(), tempPoints2.pop();
        tempPoints2.push(position.clone());
        tempPoints2.push(horizontalPosition.clone());
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

    // create entity

    //直线
    trianglesEntity.polyline = {
      positions: new Cesium.CallbackProperty(function () {
        return positions;
      }, false),
      ...options.style
    };
    trianglesEntity.position = new Cesium.CallbackProperty(function () {
      return positions[0];
    }, false);
    trianglesEntity.point = {
      pixelSize: 0.001,
      outlineColor: Cesium.Color.BLUE,
      outlineWidth: 5
    };
    trianglesEntity.label = {
      text: new Cesium.CallbackProperty(function () {
        return '直线:' + getPositionDistance(transformCartesianArrayToWGS84Array(positions)) + '米';
      }, false),
      show: true,
      showBackground: true,
      font: '14px monospace',
      horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      pixelOffset: new Cesium.Cartesian2(50, -100) //left top
    };
    //高度
    tempLineEntity.polyline = {
      positions: new Cesium.CallbackProperty(function () {
        return tempPoints;
      }, false),
      ...options.style
    };
    tempLineEntity.position = new Cesium.CallbackProperty(function () {
      return tempPoints2[1];
    }, false);
    tempLineEntity.point = {
      pixelSize: 0.001,
      outlineColor: Cesium.Color.BLUE,
      outlineWidth: 5
    };
    tempLineEntity.label = {
      text: new Cesium.CallbackProperty(function () {
        return '高度:' + _getHeading(tempPoints[0], tempPoints[1]) + '米';
      }, false),
      show: true,
      showBackground: true,
      font: '14px monospace',
      horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      pixelOffset: new Cesium.Cartesian2(-20, 100) //left top
    };
    //水平
    tempLineEntity2.polyline = {
      positions: new Cesium.CallbackProperty(function () {
        return tempPoints2;
      }, false),
      ...options.style
    };
    tempLineEntity2.position = new Cesium.CallbackProperty(function () {
      return positions[1];
    }, false);
    tempLineEntity2.point = {
      pixelSize: 0.001,
      outlineColor: Cesium.Color.BLUE,
      outlineWidth: 5
    };
    tempLineEntity2.label = {
      text: new Cesium.CallbackProperty(function () {
        return '水平距离:' + getPositionDistance(transformCartesianArrayToWGS84Array(tempPoints2)) + '米';
      }, false),
      show: true,
      showBackground: true,
      font: '14px monospace',
      horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      pixelOffset: new Cesium.Cartesian2(-150, -20) //left top
    };
    drawLayer.entities.add(tempLineEntity2);
    drawLayer.entities.add(tempLineEntity);
    drawLayer.entities.add(trianglesEntity);
  }
}
