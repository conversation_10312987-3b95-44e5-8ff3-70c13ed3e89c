import { DRONE_ENUM, DRONE_TYPE, CAMERA_TYPE_ENUM } from '@/config';
import {
  EXECUTE_HEIGHT_MODE,
  FINISH_ACTION,
  HEIGHT_MODE_ENUM,
  SHOOTTYPE_ENUM,
  TEMPLATE_TYPE_ENUM
} from '../../newplan/kmz/props';

export const flightDoneOptions = [
  {
    value: FINISH_ACTION.goHome,
    label: '自动返航'
  },
  {
    value: FINISH_ACTION.gotoFirstWaypoint,
    label: '返回航线起始点悬停'
  },
  {
    value: FINISH_ACTION.noAction,
    label: '退出航线模式'
  },
  {
    value: FINISH_ACTION.autoLand,
    label: '原地降落'
  }
];
export const shootTypeOptions = [
  {
    value: SHOOTTYPE_ENUM.time, // '等时间隔拍照',
    label: '等时间隔拍照'
  },
  {
    value: SHOOTTYPE_ENUM.distance, //'等距间隔拍照',
    label: '等距间隔拍照'
  }
];

export const cameraTypeEnum = {
  visible: 'visible',
  ir: 'ir',
  wide: 'wide'
};

export const cameraTypeOptions = [
  // {
  //   id: 1,
  //   value: CAMERA_TYPE_ENUM.visable, // wide,ir...
  //   label: '可见光', // 中文名称
  //   active: true // 是否激活
  // },
  // {
  //   id: 2,
  //   value: cameraTypeEnum.ir, // wide,ir...
  //   label: '红外', // 中文名称
  //   active: false // 是否激活
  // }
];

export const templateTypeOptions = [
  {
    id: 1,
    value: TEMPLATE_TYPE_ENUM.mapping2d,
    label: '正射影像',
    active: true
  },
  {
    id: 2,
    value: TEMPLATE_TYPE_ENUM.mapping3d,
    label: '倾斜影像',
    active: false
  }
];

// 航点高程参考平面
export const heightModeOptions = [
  {
    id: 1,
    value: HEIGHT_MODE_ENUM.EGM96, // 使用海拔高编辑 = 绝对高度,
    label: '绝对高度'
  },
  {
    id: 2,
    value: HEIGHT_MODE_ENUM.relativeToStartPoint, //'相对起飞点高度',
    label: '相对起飞点高度'
  }
];
export const HEIGHT_MODE_IMAGE_ENUM = {
  [HEIGHT_MODE_ENUM.EGM96]: new URL('@/assets/plan/wrj/绝对高度.png', import.meta.url).href,
  [HEIGHT_MODE_ENUM.relativeToStartPoint]: new URL('@/assets/plan/wrj/相对起飞点高度.png', import.meta.url).href
};

// 这里预设大疆无人机的等时间隔拍照动作时间间隔大约是2秒
export const DJ_WRJ_SPEED = 2; // 大约2m/s
export const DJ_WRJ_MAX_SPEED = 15; // 大约2m/s
