<script setup>
import { computed, watchEffect, onMounted, onUnmounted, ref } from 'vue';
import { useWindowSize } from '@vueuse/core';
import {
  AppMain,
  Navbar,
  //  Settings,
  TagsView
} from './components/index';
import Sidebar from './components/Sidebar/index.vue';

import { useAppStore } from '@/store/modules/app';
import { useSettingsStore } from '@/store/modules/settings';

import JJSocketService from '@/utils/websocket/JJSocketService';
import router from '@/router';
import { resetSetItem } from '@/utils/helper';
import moment from 'moment';
import { fetchTaskReport, getJJConfig, executeAlarm } from '@/api/task';
import { useConnectWebSocket } from '@/hooks/useConnectWebSocket';
import { EBizCode } from '@/utils/constants';
const showTips = ref(false);
useConnectWebSocket(payload => {
  if (!payload) {
    return;
  }
  switch (payload.biz_code) {
    // 任务下达直播
    case EBizCode.DeviceVideoLive: {
      // 显示直播
      showTips.value = true;
      break;
    }
  }
});
const { width } = useWindowSize();

/**
 * 响应式布局容器固定宽度
 *
 * 大屏（>=1200px）
 * 中屏（>=992px）
 * 小屏（>=768px）
 */
const SMALL_WIDTH = 768;
const LG_WIDTH = 1200;

const appStore = useAppStore();
const settingsStore = useSettingsStore();

const fixedHeader = computed(() => settingsStore.fixedHeader);
const showTagsView = computed(() => settingsStore.tagsView);
// const showSettings = computed(() => settingsStore.showSettings);

const classObj = computed(() => ({
  // hideSidebar: !appStore.sidebar.opened,
  openSidebar: appStore.sidebar.opened,
  withoutAnimation: appStore.sidebar.withoutAnimation,
  mobile: appStore.device === 'mobile'
}));

const dialogVisible = ref(false);
const alarmData = ref({});
const defaultTriggerEnable = ref(true); //是否自动出警 为true：显示确认按钮 false:显示立即出警
onMounted(() => {
  // const wsIns = JJSocketService.Instance;
  // wsIns.connect(() => {
  //   // 连接成功可以执行发送订阅
  //   wsIns.subscribe('/topic/alarmReceiveFinishFull', response => {
  //     // 执行订阅成功后事件
  //     const data = JSON.parse(response.body);
  //     console.log('接收警情', data);
  //     if (data?.alarmStatus == '02') {
  //       //上报警情
  //       getJJConfigAPI(data);
  //     }
  //   });
  // });

  // const data = {
  //   phone: '96017388',
  //   alarmTime: '2023-10-31 10:59:41',
  //   alarmId: '1745015169689325570',
  //   lon: 118.04600747,
  //   content: '一级火灾，软三A测试，天黑了',
  //   lat: 24.60974979
  // };
  // openDialog(data);
});
/**
 * 接处警任务上报
 */
function taskReport(data) {
  openDialog(data);

  fetchTaskReport({
    alarm_id: data?.alarmId,
    alarm_name: data?.content,
    latitude: data?.lat,
    longitude: data?.lon,
    remark: JSON.stringify(data)
  }).then(res => {
  }).finally(()=>{
    window.$bus.emit('refreshJJTask');
  })

  
}
/**
 * 获取配置信息
 */
function getJJConfigAPI(data) {
  getJJConfig({}).then(res => {
    defaultTriggerEnable.value = res.default_trigger_enable;
    taskReport(data);
  });
}
function openDialog(data) {
  dialogVisible.value = true;
  alarmData.value = data;
}

function toReceive() {
  //是否自动出警 为true：显示确认按钮 false:显示立即出警
  if (defaultTriggerEnable.value) {
    dialogVisible.value = false;
  }
  // let alarmInfo = alarmData.value;
  // const params = {
  //   id: new Date().getTime().toString(),
  //   taskName: alarmInfo.content,
  //   taskType: '1', // 立即执行的任务
  //   taskStatus: '3', // 默认开启
  //   planExeTime: moment().format('YYYY-MM-DD HH:mm:ss'),
  //   airLineId: '4567', //选择航线
  //   backHeight: 300, //返航高度
  //   outofControlActionId: '44', //航线失控动作

  //   recordList: [
  //     {
  //       recordId: new Date().getTime().toString(),
  //       exeTime: moment().format('YYYY-MM-DD HH:mm:ss'),
  //       exeStatus: '执行成功',
  //       desc: '-'
  //     }
  //   ]
  // };
  // let taskList = [];
  // let taskData = sessionStorage.getItem('taskData') || '';
  // if (taskData) {
  //   taskList = JSON.parse(taskData);
  // }
  // taskList.push(params);
  // resetSetItem('taskData', JSON.stringify(taskList));
  // dialogVisible.value = false;
  // router.push('/map-fly');
}

function toLiveStream() {
  router.push({
    path: '/live-stream',
    query: {
      tab: 'second'
    }
  });
  showTips.value = false;
}

// watchEffect(() => {
//   if (width.value < SMALL_WIDTH) {
//     // appStore.toggleDevice('mobile');
//     appStore.closeSideBar(true);
//   } else {
//     appStore.toggleDevice('desktop');

//     if (width.value >= LG_WIDTH) {
//       //大屏
//       appStore.openSideBar(true);
//     } else {
//       appStore.closeSideBar(true);
//     }
//   }
// });

function handleOutsideClick() {
  appStore.closeSideBar(false);
}
function toCancel() {
  dialogVisible.value = false;
}
function toExe() {
  let alarmInfo = alarmData.value;
  executeAlarm(alarmInfo.alarmId).then(data => {
    ElMessage.success('出警成功');
    dialogVisible.value = false;
  });
}
</script>

<template>
  <div :class="classObj" class="app-wrapper">
    <!-- 手机设备侧边栏打开遮罩层 -->
    <div v-if="classObj.mobile && classObj.openSidebar" class="drawer-bg" @click="handleOutsideClick" />
    <Sidebar class="sidebar-container" />
    <div :class="{ hasTagsView: showTagsView }" class="main-container">
      <div :class="{ 'fixed-header': fixedHeader }">
        <navbar />
        <tags-view v-if="showTagsView" />
      </div>
      <app-main />
    </div>
  </div>

  <el-dialog v-model="dialogVisible" title="您有一条新的警情" width="30%" :close-on-click-modal="false">
    <div class="content"><span class="label-color">报警描述：</span>{{ alarmData.content }}</div>
    <div class="content"><span class="label-color">报警时间：</span>{{ alarmData.alarmTime }}</div>
    <div class="content"><span class="label-color">报警电话：</span>{{ alarmData.phone }}</div>
    <template #footer>
      <span class="dialog-footer" style="margin-right: 10px;" v-if="!defaultTriggerEnable">
        <el-button  @click="toCancel"> 取消 </el-button>
      </span>
      <span class="dialog-footer" v-if="!defaultTriggerEnable">
        <el-button type="primary" @click="toExe"> 立即出警 </el-button>
      </span>
      <span class="dialog-footer" v-else>
        <el-button type="primary" @click="toReceive"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 任务下达通知 -->
  <div v-if="showTips" class="tip-view">您有新的任务下发，<a @click="toLiveStream">前往查看实时视频</a></div>
</template>

<style lang="scss" scoped>
.app-wrapper {
  &:after {
    content: '';
    display: table;
    clear: both;
  }

  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
}
.hideSidebar .fixed-header {
  width: calc(100% - #{$miniSideBarWidth});
}
.mobile .fixed-header {
  width: 100%;
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.tip-view {
  position: fixed;
  bottom: 50px;
  right: 30px;
  background: var(--el-color-primary);
  color: white;
  border-radius: 50px;
  padding: 20px;

  animation-name: scaleAnimation; // 动画名
  animation-duration: 2s; // 动画时长
  animation-iteration-count: infinite; // 永久动画
  transition-timing-function: ease-in-out; // 动画过渡
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); // 元素阴影

  a {
    text-decoration: underline;
  }
}
@keyframes scaleAnimation {
  // 动画设置
  0% {
    transform: scale(1);
  }

  25% {
    transform: scale(1.08);
  }

  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(1.08);
  }
}
</style>
