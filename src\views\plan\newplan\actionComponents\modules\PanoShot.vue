<template>
  <div class="panoShot-wrapper"></div>
</template>
<script>
export default {
  name: 'PanoShot'
};
</script>
<script setup>
import { onMounted, onUnmounted } from 'vue';
import 'element-plus/dist/index.css';

//#region 对外暴露方法
const setComponentData = options => {};
const getComponentData = () => {
  return null;
};
defineExpose({
  setComponentData,
  getComponentData
});
//#endregion
onMounted(() => {});
onUnmounted(() => {});
</script>
<style lang="scss" scoped>
.panoShot-wrapper {
}
</style>
