<!--应用管理-->
<script>
export default {
  name: 'AirportTask'
};
</script>

<script setup>
import { reactive } from 'vue';
import { getFlightRecordList, deleteFlightRecordList, lockFlightRecord } from '@/api/task';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
// 直接引入历史记录详情组件
import HistoryDetailComponent from '@/views/map/map-fly-manager/history/index.vue';

// 接收isDialogMode参数，默认为false
const props = defineProps({
  isDialogMode: {
    type: Boolean,
    default: false
  }
});

const tableRef = ref();
const router = useRouter();
const loading = ref(false);
const total = ref(0);
const queryParams = reactive({
  page_num: 1,
  page_size: 10
});
const dataList = ref([]);

// 嵌套弹窗相关状态
const nestedDialogVisible = ref(false);
const nestedDialogTitle = ref('飞行记录详情');
const nestedDialogParams = ref({});

/**
 * 查询
 */
function handleQuery(params) {
  loading.value = true;
  getFlightRecordList({
    ...params
  }).then(data => {
    const { list } = data;
    dataList.value = list || [];
    loading.value = false;
    queryParams.page_num = data.pagination.page;
    total.value = data.pagination.total;
  });
}

function handleSearch() {
  const params = {
    ...queryParams,
    begin_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[0]} 00:00:00` : '',
    end_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[1]} 23:59:59` : '',
    page_num: 1
  };
  delete params.rangTime;
  handleQuery(params);
}

/**
 * 重置查询
 */
function resetQuery() {
  queryParams.name = '';
  queryParams.job_id = '';
  queryParams.drone_name = '';
  queryParams.begin_time = '';
  queryParams.end_time = '';
  queryParams.rangTime = '';
  queryParams.page_num = 1;
  queryParams.page_size = 10;
  handleQuery({ ...queryParams });
}

/**
 *
 * @param dicTypeId 应用ID
 */
function openDialog(item) {
  // 如果是弹窗模式，可以选择不同的处理方式
  if (props.isDialogMode) {
    // 在弹窗中处理，使用无菜单栏路由跳转,并新开一个页面  
    // const baseUrl = window.location.origin;  
    // const url = `${baseUrl}/mapfly-manager-history-no-header?job_id=${item.job_id}&flight_id=${item.job_id}&drone_name=${encodeURIComponent(item.drone_name)}`;
    // window.open(url, '_blank');
    router.push({
      path: '/mapfly-manager-history-no-header',
      query: {
        job_id: item.job_id,
        flight_id: item.job_id,
        drone_name: item.drone_name,
      }
    });
  } else {
    // 路由跳转
    router.push({
      path: '/mapfly-manager-history',
      query: {
        job_id: item.job_id,
        flight_id: item.job_id,
        drone_name: item.drone_name,
        // wayline_id: item.wayline_id,
        // dock_sn: item.dock_sn
      }
    });
  }
}

/**
 * 删除
 */
function handleDelete(row) {
  if(toRaw(row).locked) {
    ElMessage.error('该记录已被锁定，无法删除！')
    return;
  }
  ElMessageBox.confirm(`删除飞行记录将同时删除该记录下的成果数据，是否确定删除该记录？`,'确认删除所选历史记录？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    autofocus: false,
    closeOnClickModal: false,
    type: 'warning'
  }).then(() => {
    deleteFlightRecordList({
      job_id: toRaw(row).job_id
    }).then(data => {
      ElMessage.success('删除成功');
      handleQuery();
    });
  });
}

const changeLockType = (val,row) => {
  const data = toRaw(row)
  lockFlightRecord({
    id: row.job_id
  },{
    locked: val
  }).then(data=>{
    ElMessage.success('操作成功');
    handleQuery({...queryParams});
  })
}

onMounted(() => {
  handleQuery({ ...queryParams });
});

const handleSizeChange = val => {
  // Calculate the new total pages based on the new page size
  const newTotalPages = Math.ceil(total.value / val);
  
  // If current page is greater than new total pages, adjust to the last page
  if (queryParams.page_num > newTotalPages) {
    queryParams.page_num = newTotalPages || 1;
  }
  queryParams.page_size = val;
  const params = {
    ...queryParams,
    begin_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[0]} 00:00:00` : '',
    end_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[1]} 23:59:59` : '',
    page_size: val
  };
  delete params.rangTime;
  handleQuery({ ...params });
};

const handleCurrentChange = val => {
  tableRef.value.setScrollTop(0)
  queryParams.page_num = val;
  const params = {
    ...queryParams,
    begin_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[0]} 00:00:00` : '',
    end_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[1]} 23:59:59` : '',
    page_num: val
  };
  delete params.rangTime;
  handleQuery({ ...params });
};
</script>

<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
          <el-form-item label="" prop="job_id">
            <el-input
              class="input-serach"
              v-model="queryParams.job_id"
              placeholder="请输入任务ID"
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
            />
          </el-form-item>
          <el-form-item label="" prop="name">
            <el-input
              class="input-serach"
              v-model="queryParams.name"
              placeholder="请输入任务名称"
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
            />
          </el-form-item>
          
          <el-form-item label="" prop="keyWord">
            <el-date-picker
              class="input-serach"
              v-model="queryParams.rangTime"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @change="handleSearch"
            />
          </el-form-item>
          <el-form-item label="" prop="drone_name">
            <el-input
              class="input-serach"
              v-model="queryParams.drone_name"
              placeholder="请输入无人机名称"
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
            />
          </el-form-item>
        </el-form>
      </div>
      <div class="search-btn">
        <el-button type="primary" @click="handleSearch()"><i-ep-search />搜索</el-button>
        <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
      </div>
    </div>
    <el-card shadow="never">
      <el-table highlight-current-row :data="dataList" v-loading="loading" stripe height="630" ref="tableRef">
        <el-table-column label="序号" align="center" width="80">
          <template #default="scope">
            <span :title="scope.$index">{{ scope.$index + 1 + queryParams.page_size * (queryParams.page_num - 1) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="任务ID" width="250" prop="job_id" show-overflow-tooltip />
        <el-table-column label="任务名称" prop="name" show-overflow-tooltip />
        <el-table-column label="飞行距离" prop="distance_show" show-overflow-tooltip />

        <el-table-column label="飞行时长" prop="duration_show" show-overflow-tooltip />
        <el-table-column label="任务开始时间" prop="start_time" width="180" show-overflow-tooltip />
        <el-table-column label="任务结束时间" prop="end_time" width="180" show-overflow-tooltip />

        <el-table-column label="无人机名称" width="260" prop="drone_name" show-overflow-tooltip />

        <el-table-column label="创建人" prop="creator" show-overflow-tooltip> </el-table-column>

        <el-table-column label="锁定飞行记录"  width="120" prop="status_desc" show-overflow-tooltip>
          <template #default="scope">
            <el-switch v-model="scope.row.locked" @change="(val)=>{changeLockType(val,scope.row)}"/>
          </template>
        </el-table-column>

        <el-table-column fixed="right" label="操作" align="center" width="200">
          <template #default="scope">
            <el-button type="primary" link @click.stop="openDialog(scope.row)">详情</el-button>
            <!-- <el-button type="danger" link @click.stop="handleDelete(scope.row)">删除</el-button> -->
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-content">
        <el-pagination
         v-if="total > 0"
          v-model:current-page="queryParams.page_num"
          v-model:page-size="queryParams.page_size"
          :background="true"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 嵌套弹窗 -->
    <el-dialog
      v-model="nestedDialogVisible"
      :title="nestedDialogTitle"
      width="95%"
      :close-on-click-modal="false"
      :append-to-body="true"
      :destroy-on-close="true"
      class="nested-dialog"
    >
      <component :is="HistoryDetailComponent" v-bind="nestedDialogParams" :isDialogMode="true" />
    </el-dialog>
  </div>
</template>
<style scoped lang="scss">
.pagination-content {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;

  &.hidden {
    display: none;
  }
}
.input-serach {
  width: 200px;
}
.flex-center {
  display: flex;
  align-items: center;
  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
}
.search {
  display: flex;
  align-items: center;
  padding: 24px;
  height: 64px;
  .search-form {
    flex: 1;
    padding-top: 16px;
  }
}

/* 嵌套弹窗样式 */
:deep(.nested-dialog) {
  .el-dialog__body {
    max-height: 90vh;
    overflow-y: auto;
    padding: 10px;
  }
}
</style>
