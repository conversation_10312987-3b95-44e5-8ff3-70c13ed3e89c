<template>
  <div class="">
    <el-form ref="loginFormRef" :model="loginFormData" :rules="loginRules">
      <template v-if="props.accountType === 0">
        <el-form-item prop="account">
          <div class="login-form-input">
            <span class="ff-cloud-icon clound-status-person custom-icon" />
            <el-input
              ref="account"
              v-model="loginFormData.account"
              :readonly="readonly"
              :placeholder="$t('login.usernamePhoneOrEmail')"
              name="account"
              type="text"
              tabindex="1"
              autocomplete="new-password"
              @focus="readonly = false"
              @keyup.enter="handleLogin"
            />
          </div>
        </el-form-item>
      </template>
      <template v-if="props.accountType === 1">
        <el-form-item prop="tenantId">
          <div class="login-form-input">
            <span class="ff-cloud-icon cloud-tenant custom-icon" />
            <el-input
              ref="tenantId"
              v-model="loginFormData.tenantId"
              :readonly="readonly"
              :placeholder="$t('login.tenantID')"
              name="tenantId"
              type="text"
              tabindex="1"
              autocomplete="new-password"
              @focus="readonly = false"
              @keyup.enter="handleLogin"
            />
          </div>
        </el-form-item>
        <el-form-item prop="username">
          <div class="login-form-input">
            <span class="ff-cloud-icon clound-status-person custom-icon" />
            <el-input
              ref="username"
              v-model="loginFormData.username"
              :readonly="readonly"
              :placeholder="$t('login.username')"
              type="text"
              tabindex="2"
              autocomplete="new-password"
              @focus="readonly = false"
              @keyup.enter="handleLogin"
            />
          </div>
        </el-form-item>
      </template>
      <el-form-item prop="password">
        <div class="login-form-input">
          <span class="ff-cloud-icon cloud-lock custom-icon" />
          <el-input
            ref="passwordRef"
            v-model="loginFormData.password"
            :readonly="readonly"
            :placeholder="$t('login.pwd')"
            :type="passwordType"
            tabindex="3"
            autocomplete="new-password"
            @focus="readonly = false"
            @keyup.enter="handleLogin"
          />
          <span class="show-pwd cursor-pointer" @click="showPwd">
            <i
              class="ff-cloud-icon"
              :class="
                passwordType === 'password'
                  ? 'clound-open-eye'
                  : 'clound-close-eye'
              "
            />
          </span>
        </div>
      </el-form-item>
      <div class="flex items-center login-box-foot">
        <div class="flex-1">
          <el-checkbox v-model="remember">
            {{ $t('login.rememberMe') }}
          </el-checkbox>
        </div>
        <div>
          <el-link
            v-if="!isExclusive"
            :underline="false"
            type="primary"
            @click="handleRegister"
          >
            {{ $t('login.rapidRegistration') }}
          </el-link>
          <el-link
            v-if="props.accountType === 0"
            class="ml-2"
            :underline="false"
            type="primary"
            @click="toResetPwd"
          >
            {{ $t('login.retrievePwd') }}
          </el-link>
        </div>
      </div>
      <el-button
        :loading="submitLoading"
        type="primary"
        class="login-form-btn"
        @click.prevent="handleLogin"
      >
        {{ $t('login.login') }}
      </el-button>
    </el-form>
  </div>
</template>

<script setup>
import _ from 'lodash';
import i18n from '@/lang';
//
import { useRoute } from 'vue-router';
import { ref, reactive, watch, onMounted, onUnmounted } from 'vue';
import router from '@/router';

import { useUserStore } from '@/store/modules/user.js';
import { getTenantRememberPwd, setTenantRememberPwd } from '@/utils/store';

const userStore = useUserStore();

const props = defineProps({
  // 账号类型 0主账号登录 1子账号登录
  accountType: {
    type: Number,
    default: 0
  },
  loginAction: {
    type: Function,
    default: () => {}
  }
});
const loginFormRef = ref('loginFormRef');
const passwordRef = ref('passwordRef');
const remember = ref(false);
const readonly = ref(true);
const isExclusive = ref(false);
const route = useRoute();
const loginFormData = reactive({
  username: '',
  password: '',
  account: '',
  tenantId: ''
});

const loginRules = {
  username: [
    {
      required: true,
      trigger: ['blur', 'change'],
      message: `${i18n.global.t('login.enterUserName')}`
    }
  ],
  password: [
    {
      required: true,
      trigger: ['blur', 'change'],
      message: i18n.global.t('login.enterPwd')
    }
  ],
  account: [
    {
      required: true,
      trigger: ['blur', 'change'],
      message: `${i18n.global.t('login.enterUserName')}`
    }
  ],
  tenantId: [
    {
      required: true,
      trigger: ['blur', 'change'],
      message: i18n.global.t('login.enterTenantID')
    }
  ]
};

const submitLoading = ref(false);
const passwordType = ref('password');
const rememberConfig = reactive({});

const toResetPwd = () => {
  router.push({ name: 'ResetPassword', query: route.query });
};

const showPwd = () => {
  passwordType.value = passwordType.value === 'password' ? '' : 'password';
  nextTick(() => {
    passwordRef.value.focus();
  });
};

const handleRegister = () => {
  router.push({ name: 'Register' });
};

const handleLogin = () => {
  submitLoading.value = true;
  loginFormRef.value.validate(async valid => {
    if (valid) {
      props.loginAction();
    } else {
      submitLoading.value = false;
      return false;
    }
  });
};

const validSubmit = async validResult => {
  if (validResult) {
    try {
      if (props.accountType === 0) {
        if (isExclusive.value) {
          const form = _.cloneDeep(loginFormData);
          await userStore
            .exclusiveLogin({ data: form, tenantId: route.query.tenant })
            .then(() => {
              ElMessage.success(
                i18n.global.t('page.dialog.actionFb.successfullyLogin')
              );
              router.replace({ name: 'dashboard' });
            })
            .catch(() => {
              submitLoading.value = false;
            })
            .finally(() => {
              submitLoading.value = false;
            });
          rememberAccount();
        } else {
          userStore
            .login(loginFormData)
            .then(() => {
              rememberAccount();
              ElMessage.success(
                i18n.global.t('page.dialog.actionFb.successfullyLogin')
              );
              router.replace({ name: 'dashboard' });
            })
            .catch(() => {
              submitLoading.value = false;
            })
            .finally(() => {
              submitLoading.value = false;
            });
        }
      } else {
        userStore
          .subAccountLogin(loginFormData)
          .then(() => {
            ElMessage.success(
              i18n.global.t('page.dialog.actionFb.successfullyLogin')
            );
            router.replace({ name: 'dashboard' });
          })
          .catch(() => {
            submitLoading.value = false;
          })
          .finally(() => {
            submitLoading.value = false;
          });
        rememberAccount();
      }
    } catch {
      console.log('login error');
    }
  }
  submitLoading.value = false;
};

const setRememberConfig = () => {
  const config =
    props.accountType === 0
      ? isExclusive.value
        ? rememberConfig.exclusive
        : rememberConfig.mainAccound
      : rememberConfig.subAccound;

  remember.value = config ? true : false;

  Object.assign(loginFormData, config ? config[1] : {});
};

const rememberAccount = () => {
  const config =
    props.accountType === 0
      ? isExclusive.value
        ? 'exclusive'
        : 'mainAccound'
      : 'subAccound';

  rememberConfig[config] = [
    remember.value,
    remember.value ? loginFormData : {}
  ];

  setTenantRememberPwd(rememberConfig);
};

watch(
  () => props.accountType,
  newVal => {
    // accountType 0 为主账号 1 为子账号
    if (newVal >= 0) {
      Object.assign(loginFormData, {
        username: '',
        password: '',
        account: '',
        tenantId: ''
      });

      setRememberConfig();

      setTimeout(() => {
        loginFormRef?.value.clearValidate();
      }, 50);
    }
  }
);

const handleSubmit = data => {
  if (
    !_.isEmpty(data) &&
    data.emitData.loginType === 'pwdLogin' &&
    data.emitName === 'dragVerify'
  ) {
    validSubmit(data.emitData);
  }
};

const handleCancle = data => {
  if (data.emitName === 'dragVerify') {
    submitLoading.value = false;
  }
};

onMounted(() => {
  Object.assign(rememberConfig, _.cloneDeep(getTenantRememberPwd()));

  if (JSON.stringify(route.query) !== '{}' && route.query.tenant) {
    isExclusive.value = true;
  }

  setRememberConfig();

  window.$bus.on('dialogCancel', handleCancle);
  // 监听滑动验证码弹窗beforeClose事件
  window.$bus.on('dialogBeforeClose', handleSubmit);
});

onUnmounted(() => {
  window.$bus.off('dialogCancel');
  window.$bus.off('dialogBeforeClose');
});
</script>

<style lang="scss" scoped>
:deep() {
  .el-form-item.is-error .el-input__wrapper {
    box-shadow: none;
    &:hover {
      box-shadow: none;
    }
  }

  .el-form-item.is-error .el-input__wrapper.is-focus {
    box-shadow: none !important;
  }
}
.login-form-input {
  width: 100%;
  display: flex;
  padding: 12px 20px;
  border-radius: 4px;
  background: #f8fafe;
  line-height: 1;

  align-items: center;
  :deep() {
    .el-input__wrapper {
      box-shadow: none;
      background-color: #f8fafe;
    }
    .el-input__inner {
      height: unset;
      border: none;
      background: transparent;
      line-height: 1;
      flex: 1;

      -webkit-appearance: none;
      &:-webkit-autofill {
        box-shadow: 0 0 0 1000px #f8fafe inset !important;

        -webkit-text-fill-color: #000 !important;
      }
    }
    .el-input__prefix {
      position: relative;
    }
    .custom-icon {
      color: #097efc;
      font-size: 18px;
    }
  }
}
:deep(.login-box-foot) {
  .el-checkbox {
    color: #a8bdd3;
    .el-checkbox__inner {
      border-color: #119ffb;
      background-color: transparent;
    }
  }
  .el-link--primary {
    color: #fff;
  }
}
.login-form-input-icon {
  width: 20px;
}
.login-form-btn {
  margin-top: 25px;
  padding: 15px;
  width: 100%;
  font-weight: 500;
  font-size: 14px;
  background: linear-gradient(90deg, #4057fb, #2fb7ff);
  border: none;

  &:hover,
  &:focus {
    background: #2fb7ff;
    border: none;
  }
}
</style>
