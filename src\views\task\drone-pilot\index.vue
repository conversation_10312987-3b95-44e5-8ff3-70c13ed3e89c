<!--应用管理-->
<script>
export default {
  name: 'AirportTask'
};
</script>

<script setup>
import { onBeforeUnmount, reactive } from 'vue';
import { getAirTaskList, deleteAirTaskList, pauseTask } from '@/api/task';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import EditDialog from './EditDialog.vue';
import { authorityShow } from '@/utils/authority';

const editDialogRef = ref(null);
const loading = ref(false);
const total = ref(0);
const router = useRouter();
const queryParams = reactive({
  job_types: [2], // 飞手任务
  page_num: 1,
  page_size: 10
});
const dataList = ref([]);
const dialog = reactive({
  visible: false
});
const editDialog = reactive({
  visible: false
});
let formData = reactive({});
let detailData = reactive({});

/**
 * 查询
 */
function handleQuery(params) {
  loading.value = true;
  getAirTaskList({
    ...queryParams,
    ...params
  }).then(data => {
    const { records } = data;
    dataList.value = records || [];
    loading.value = false;
    queryParams.page_num = data.current;
    total.value = data.total;
  });
}

function handleSearch() {
  const params = {
    ...queryParams,
    begin_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[0]} 00:00:00` : '',
    end_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[1]} 23:59:59` : '',
    page_num: 1
  };
  delete params.rangTime;
  handleQuery(params);
}

/**
 * 重置查询
 */
function resetQuery() {
  queryParams.name = '';
  queryParams.begin_time = '';
  queryParams.end_time = '';
  queryParams.rangTime = '';
  queryParams.page_num = 1;
  queryParams.page_size = 10;
  handleQuery({ ...queryParams });
}

/**
 *
 * @param dicTypeId 应用ID
 */
function openDialog(row) {
  dialog.visible = true;
  Object.keys(detailData).map(key => {
    delete detailData[key];
  });
  if (row) {
    Object.assign(detailData, { ...row });
  }
}

function openEditDialog(row) {
  editDialog.visible = true;
  Object.keys(formData).map(key => {
    delete formData[key];
  });
  if (row) {
    editDialog.title = '编辑飞手任务';
    Object.assign(formData, { ...row });
  } else {
    editDialog.title = '新增任务';
    nextTick(() => {
      editDialogRef.value.setDefaultValue();
    });
  }
}

/**
 * 删除
 */
function handleDelete(row) {
  ElMessageBox.confirm(`确认后将删除此任务及相关的历史执行记录，且无法进行恢复`, '确认删除所选任务？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    autofocus: false,
    closeOnClickModal: false,
    type: 'warning'
  }).then(() => {
    deleteAirTaskList({
      task_id: toRaw(row).flight_task_id
    }).then(data => {
      ElMessage.success('删除成功');
      handleQuery({ ...queryParams });
    });
  });
}

/**
 *
 * @param dicTypeId 应用ID
 */
function gotoHistroy(item) {
  // 路由跳转
  router.push({
    path: '/mapfly-manager-history',
    query: {
      job_id: item.flight_task_id,
      flight_id: item.flight_task_id,
      drone_name: item.drone_sn_desc
      // wayline_id: item.wayline_id,
      // dock_sn: item.dock_sn
    }
  });
}

onMounted(() => {
  handleQuery({ ...queryParams });
});

const handleSizeChange = val => {
  // Calculate the new total pages based on the new page size
  const newTotalPages = Math.ceil(total.value / val);

  // If current page is greater than new total pages, adjust to the last page
  if (queryParams.page_num > newTotalPages) {
    queryParams.page_num = newTotalPages || 1;
  }
  queryParams.page_size = val;
  handleQuery({ ...queryParams });
};

const handleCurrentChange = val => {
  queryParams.page_num = val;
  const params = {
    ...queryParams,
    begin_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[0]} 00:00:00` : '',
    end_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[1]} 23:59:59` : '',
    page_num: val
  };
  handleQuery({ ...params });
};
</script>

<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
          <el-form-item label="" prop="name">
            <el-input
              class="input-serach"
              v-model="queryParams.name"
              placeholder="请输入任务名称"
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
            />
          </el-form-item>
          <el-form-item label="" prop="keyWord">
            <el-date-picker
              class="input-serach"
              v-model="queryParams.rangTime"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @change="handleSearch"
            />
          </el-form-item>
        </el-form>
      </div>
      <div class="search-btn">
        <el-button type="primary" @click="handleSearch()"><i-ep-search />搜索</el-button>
        <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
      </div>
    </div>
    <el-card shadow="never">
      <template #header></template>
      <el-table highlight-current-row :data="dataList" v-loading="loading" stripe height="540">
        <el-table-column label="序号" align="center" width="80">
          <template #default="scope">
            <span>{{ scope.$index + 1 + queryParams.page_size * (queryParams.page_num - 1) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="任务ID" prop="flight_task_id" show-overflow-tooltip />
        <el-table-column label="任务名称" prop="name" show-overflow-tooltip />
        <!-- <el-table-column label="航线名称" prop="wayline_file_name" show-overflow-tooltip /> -->
        <el-table-column label="任务执行时间" prop="execute_time" width="200" show-overflow-tooltip />
        <el-table-column label="创建人" prop="create_user_name" show-overflow-tooltip />
        <el-table-column label="任务状态" prop="status_desc" show-overflow-tooltip />

        <el-table-column
          fixed="right"
          label="操作"
          align="center"
          width="200"
          v-if="
            authorityShow('editDronePilot') || authorityShow('recordDronePilot') || authorityShow('deleteDronePilot')
          "
        >
          <template #default="scope">
            <el-button
              v-if="authorityShow('editDronePilot')"
              type="primary"
              link
              @click.stop="openEditDialog(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="primary"
              v-if="scope.row.status_desc == '结束' && authorityShow('recordDronePilot')"
              link
              @click.stop="gotoHistroy(scope.row)"
              >飞行记录</el-button
            >
            <el-button v-if="authorityShow('deleteDronePilot')" type="danger" link @click.stop="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-content">
        <el-pagination
          v-if="total > 0"
          v-model:current-page="queryParams.page_num"
          v-model:page-size="queryParams.page_size"
          :background="true"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    <EditDialog
      v-model:visible="editDialog.visible"
      :title="editDialog.title"
      :form-data="formData"
      @submit="resetQuery"
    />
  </div>
</template>
<style scoped lang="scss">
.pagination-content {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;

  &.hidden {
    display: none;
  }
}
.input-serach {
  width: 200px;
}
.flex-center {
  display: flex;
  align-items: center;
  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
}
.search {
  display: flex;
  align-items: center;
  padding: 24px;
  height: 64px;
  .search-form {
    flex: 1;
    padding-top: 16px;
  }
}
</style>
