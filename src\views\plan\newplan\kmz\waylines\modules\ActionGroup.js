import { generateKey } from '@/utils';
import { ACTION_TRIGGER_TYPE, OPERATION_TYPE } from '@/utils/constants';
class ActionGroup {
  wpml_actionTrigger = {
    wpml_actionTriggerType: 'sequence',
    wpml_actionTriggerParam: null
  };

  /**
   * Constructs an ActionGroup instance with the provided options.
   *
   * @param {object} options - The options object for creating the ActionGroup.
   * @param {number} [options.placemarkIndex=0] - The index of the placemark associated with this ActionGroup.
   * @param {number} [options.actionGroupId=0] - The unique identifier for this ActionGroup.
   * @param {number} [options.actionGroupStartIndex=0] - The starting index of the actions in this ActionGroup.
   * @param {number} [options.actionGroupEndIndex=0] - The ending index of the actions in this ActionGroup.
   * @param {string} [options.actionGroupMode='sequence'] - The mode of execution for the actions in this ActionGroup. Default is 'sequence'.
   * @param {string} [options.actionTriggerType='reachPoint'] - The type of trigger for the actions in this ActionGroup. Default is 'reachPoint'.
   * @param {any} [options.actionTriggerParam=null] - The parameter for the action trigger.
   * @param {Array} [options.actions=[]] - The array of actions in this ActionGroup.
   */
  constructor(options) {
    this.uuid = options.uuid || generateKey();
    this.placemarkIndex = options.placemarkIndex || 0;
    this.wpml_actionGroupId = options.actionGroupId || 0;
    this.wpml_actionGroupStartIndex = options.actionGroupStartIndex || 0;
    this.wpml_actionGroupEndIndex = options.actionGroupEndIndex || 0;
    this.wpml_actionGroupMode = options.actionGroupMode || 'sequence'; // 默认按顺序执行
    // 一下两个方法在外部构建完后传入
    this.wpml_actionTrigger.wpml_actionTriggerType = options.actionTriggerType || ACTION_TRIGGER_TYPE.reachPoint; //：到达航点时执行
    this.wpml_actionTrigger.wpml_actionTriggerParam = options.actionTriggerParam || null; //
    // 动画组
    this.wpml_action = options.actions || [];
    this.type = options.type || OPERATION_TYPE.normal;
  }
  getType() {
    return this.type;
  }
  getActions() {
    return this.wpml_action ?? [];
  }
  getActionCount() {
    return this?.wpml_action?.length ?? 0;
  }
  // 获取当前组的id
  getActionGroupId() {
    return this.wpml_actionGroupId;
  }
  getActionGroupTriggerType() {
    return this.wpml_actionTrigger.wpml_actionTriggerType || null;
  }
  getMaxActionIndex() {
    return this.wpml_action.length;
  }

  // 设置动画动作组合这边可以是外部排序后进行设置
  setActions(actions) {
    this.wpml_action = actions;
  }

  // 添加动作
  addAction(action) {
    this.wpml_action.push(action);
  }

  // 移除动作
  removeAction(index) {
    this.wpml_action.splice(index, 1);
  }

  // 移除动作 这里的 wpml_actionId 是按照动作顺序的
  removeActionByActionId(wpml_actionId) {
    const i = this.wpml_action.findIndex(action => {
      return action.getActionId() == wpml_actionId;
    });
    if (i > -1) {
      this.wpml_action.splice(i, 1);
    }
  }

  // 移除动作 这里的 wpml_actionId 是按照动作顺序的
  removeActionByActionUuid(uuid) {
    const i = this.wpml_action.findIndex(action => {
      return action.uuid == uuid;
    });
    if (i > -1) {
      this.wpml_action.splice(i, 1);
    } // 重新设置动作序号
    this.resetAction();
  }

  // 获取动作
  getAction(index) {
    return this.wpml_action[index];
  }

  // 动作排序 需要根据实际来写
  sortActions() {
    this.wpml_action.sort((a, b) => a.wpml_actionId - b.wpml_actionId);
  }

  // 重新设置动作序号
  resetAction() {
    this.wpml_action.forEach((action, index) => {
      action.wpml_actionId = index;
    });
  }

  toJSON() {
    return {
      wpml_actionGroupId: this.wpml_actionGroupId,
      wpml_actionGroupStartIndex: this.wpml_actionGroupStartIndex,
      wpml_actionGroupEndIndex: this.wpml_actionGroupEndIndex,
      wpml_actionGroupMode: this.wpml_actionGroupMode,
      wpml_actionTrigger: this.wpml_actionTrigger,
      wpml_action: this.wpml_action
    };
  }

  static fromJSON(json) {
    return new ActionGroup(json);
  }
}

export { ActionGroup };
