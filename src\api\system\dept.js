import request from '@/utils/request';

// 查询部门列表
export function listDept(query) {
  return request({
    url: '/system/dept/list',
    method: 'get',
    params: query
  })
}
// // 查询部门列表（排除节点）
// export function listDeptExcludeChild(deptId) {
//   return request({
//     url: '/system/dept/list/exclude/' + deptId,
//     method: 'get'
//   })
// }

// 查询部门详细
export function getDept(deptId) {
  return request({
    url: '/system/dept/get?id=' + deptId,
    method: 'get'
  })
}

// 新增部门
export function addDept(data) {
  return request({
    url: '/system/dept/create',
    method: 'post',
    data: data
  })
}

// 修改部门
export function updateDept(data) {
  return request({
    url: '/system/dept/update',
    method: 'put',
    data: data
  })
}

// 删除部门
export function delDept(deptId) {
  return request({
    url: '/system/dept/delete?id=' + deptId,
    method: 'delete'
  })
}
// 角色列表
export function getRolePage(query) {
  return request({
    url: '/system/role/page',
    method: 'GET',
    params: query
  })
}

// 添加角色
export function addRole(data) {
  return request({
    url: '/system/role/create',
    method: 'post',
    data: data
  })
}

// 获取角色信息
export function getRoleInfo(query) {
  return request({
    url: '/system/role/get',
    method: 'GET',
    params: query
  })
}

// 删除角色信息
export function deleteUserList(query) {
  return request({
    url: '/system/role/delete',
    method: 'delete',
    params: query
  })
}

// 编辑角色信息
export function editRoleInfo(data) {
  return request({
    url: '/system/role/update',
    method: 'put',
    data: data
  })
}


// 获取菜单树
export function getMenuTree(data) {
  return request({
    url: '/system/menu/tree',
    method: 'POST',
    data: data
  })
}


// 获取角色的菜单信息
export function getUserMenuInfo(query) {
  return request({
    url: '/system/permission/list-role-menus',
    method: 'GET',
    params: query
  })
}

// 赋予菜单
export function saveMenu(data) {
  return request({
    url: '/system/permission/assign-role-menu',
    method: 'POST',
    data: data
  })
}

export function getWeather(query) {
  return request({
    url: '/infra/api/v1/weather',
    method: 'GET',
    params: query
  })
}
