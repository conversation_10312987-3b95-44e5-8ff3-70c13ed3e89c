
html, body, #app, #my-app {
	height: 100%;
	overflow: hidden;
}

body {
	background-color: #f7f9fa;
	-webkit-font-smoothing: antialiased;
	// Prevent font enlargement in horizontal screen
	text-size-adjust: 100%;

	font-family: sans-serif, Roboto, sans-serif-medium, Arial;
	font-feature-settings: normal;
	color: #000;
	font-size: 14px;

	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	::-webkit-scrollbar {
		width: 8px;
		height: 8px;
		background: transparent;
	}
	
	::-webkit-scrollbar-thumb {
		border-radius: 4px;
		border: none;
		background: rgb(89, 89, 89);
	}
}
.flex-display {
  display: flex;
}

.flex-column {
  @extend .flex-display;
  flex-direction: column;
}

.flex-row {
  @extend .flex-display;
  flex-direction: row;
}

.flex-align-start {
  align-items: flex-start;
}
.flex-align-end {
  align-items: flex-end;
}
.flex-align-baseline {
  align-items: baseline;
}
.flex-align-stretch {
  align-items: stretch;
}
.flex-align-center {
  align-items: center;
}

.flex-justify-start {
  justify-content: flex-start;
}
.flex-justify-end {
  justify-content: flex-end;
}
.flex-justify-center {
  justify-content: center;
}
.flex-justify-between {
  justify-content: space-between;
}
.flex-justify-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.flex-shrink-1 {
  flex-shrink: 1;
}

//width
.width-100vw {
  width: 100vw;
}
.width-100 {
  width: 100%;
}

//height
.height-100vh {
  height: 100vh;
}
.height-100 {
  height: 100%;
}

//margin
m-5 {
  margin: -5px !important;
}
.mt-5 {
  margin-top: -5px !important;
}

.mt100 {
  margin-top: 100px !important;
}

.mt110 {
  margin-top: 110px !important;
}

.mb-5 {
  margin-bottom: -5px !important;
}
.ml-5 {
  margin-left: -5px !important;
}
.mr-5 {
  margin-right: -5px !important;
}
.m0 {
  margin: 0px !important;
}
.mt0 {
  margin-top: 0px !important;
}
.mb0 {
  margin-bottom: 0px !important;
}
.ml0 {
  margin-left: 0px !important;
}
.mr0 {
  margin-right: 0px !important;
}
.m5 {
  margin: 5px !important;
}
.mt5 {
  margin-top: 5px !important;
}
.mb5 {
  margin-bottom: 5px !important;
}
.ml5 {
  margin-left: 5px !important;
}
.mr5 {
  margin-right: 5px !important;
}
.m10 {
  margin: 10px !important;
}
.mt10 {
  margin-top: 10px !important;
}
.mb10 {
  margin-bottom: 10px !important;
}
.ml10 {
  margin-left: 10px !important;
}
.mr10 {
  margin-right: 10px !important;
}
.m15 {
  margin: 15px !important;
}
.mt15 {
  margin-top: 15px !important;
}
.mb15 {
  margin-bottom: 15px !important;
}
.ml15 {
  margin-left: 15px !important;
}
.mr15 {
  margin-right: 15px !important;
}
.m20 {
  margin: 20px !important;
}
.mt20 {
  margin-top: 20px !important;
}
.mb20 {
  margin-bottom: 20px !important;
}
.ml20 {
  margin-left: 20px !important;
}
.mr20 {
  margin-right: 20px !important;
}
.m25 {
  margin: 25px !important;
}
.mt25 {
  margin-top: 25px !important;
}
.mb25 {
  margin-bottom: 25px !important;
}
.ml25 {
  margin-left: 25px !important;
}
.mr25 {
  margin-right: 25px !important;
}
.m30 {
  margin: 30px !important;
}
.mt30 {
  margin-top: 30px !important;
}
.mb30 {
  margin-bottom: 30px !important;
}
.ml30 {
  margin-left: 30px !important;
}
.ml40 {
  margin-left: 40px !important;
}
.mr30 {
  margin-right: 30px !important;
}
.m50 {
  margin: 50px !important;
}
.mt50 {
  margin-top: 50px !important;
}
.mb50 {
  margin-bottom: 50px !important;
}
.ml50 {
  margin-left: 50px !important;
}
.mr50 {
  margin-right: 50px !important;
}
// padding值
.p0 {
  padding: 0 !important;
}
.pt0 {
  padding-top: 0 !important;
}
.pr0 {
  padding-right: 0 !important;
}
.pb0 {
  padding-bottom: 0 !important;
}
.pl0 {
  padding-left: 0 !important;
}
.p5 {
  padding: 5px;
}
.pt5 {
  padding-top: 5px;
}
.pr5 {
  padding-right: 5px;
}
.pb5 {
  padding-bottom: 5px;
}
.pl5 {
  padding-left: 5px;
}
.p10 {
  padding: 10px;
}
.pt10 {
  padding-top: 10px;
}
.pr10 {
  padding-right: 10px;
}
.pb10 {
  padding-bottom: 10px;
}
.pl10 {
  padding-left: 10px;
}
.p15 {
  padding: 15px;
}
.pt15 {
  padding-top: 15px;
}
.pr15 {
  padding-right: 15px;
}
.pb15 {
  padding-bottom: 15px;
}
.pl15 {
  padding-left: 15px;
}
.p20 {
  padding: 20px;
  box-sizing: border-box;
}
.pt20 {
  padding-top: 20px;
}
.pr20 {
  padding-right: 20px;
}
.pb20 {
  padding-bottom: 20px;
}
.pl20 {
  padding-left: 20px;
}
.p30 {
  padding: 30px;
  box-sizing: border-box;
}
.pt30 {
  padding-top: 30px;
}
.pr30 {
  padding-right: 30px;
}
.pb30 {
  padding-bottom: 30px;
}
.pl30 {
  padding-left: 30px;
}
.pb50 {
  padding-bottom: 50px;
}
.pl50 {
  padding-left: 50px;
}
.pl120 {
  padding-left: 120px;
}
.pl150 {
  padding-left: 150px;
}
.pt50 {
  padding-top: 50px;
}
$font-family-sans-serif: 'Open Sans', BlinkMacSystemFont, 'Segoe UI', Roboto,
  'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
  'Microsoft YaHei', SimSun, sans-serif;

$line-heights: (
  12: 20px,
  14: 22px,
  16: 24px,
  18: 26px
);

// 用法: @include text(12)
@mixin text($size) {
  font-size: #{$size}px;
  line-height: map-get($line-heights, $size);
}

// 常规体
@mixin text-regular {
  font-weight: 400;
}

// 中粗体
@mixin text-semibold {
  font-weight: 600;
}

@mixin ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.fz10 {
  font-size: 10px;
}
.fz12 {
  font-size: 12px;
}
.fz14 {
  font-size: 14px;
}
.fz16 {
  font-size: 16px;
}
.fz18 {
  font-size: 18px;
}
.fz20 {
  font-size: 20px;
}
.fz22 {
  font-size: 22px;
}
.fz24 {
  font-size: 24px;
}
.fz26 {
  font-size: 26px;
}
.fz28 {
  font-size: 28px;
}
.fz30 {
  font-size: 30px;
}
.fz32 {
  font-size: 32px;
}
.fz35 {
  font-size: 35px;
}
