<script>
export default { name: 'AlarmList' };
</script>

<script setup>
import optionData from '@/utils/option-data';
import { onMounted, ref, toRaw } from 'vue';
import { getJJTaskPage } from '@/api/task';
import { EBizCode } from '@/utils/constants';
import { useConnectWebSocket } from '@/hooks/useConnectWebSocket';
import { ElMessage } from 'element-plus';
const emit = defineEmits(['OneClickDelivery','LocationJump']);
const dataList = ref([]);
const total = ref(0);
const alarmId = ref('')
const queryParams = reactive({
  pageNum: 1,
  pageSize: 20,
	time_type: 1
});
const loading = ref(false);

defineExpose({
	updateStatus,
});

function updateStatus (id) {
	console.log('id',id)
	dataList.value?.forEach(res=>{
		if(res.alarm_id === id){
			res.current_status_name ='下发中'
			res.current_status = 2
		}
	})
}

useConnectWebSocket(payload => {
  if (!payload) {
    return;
  }
  switch (payload.biz_code) {
    // 刷新警情列表
    case EBizCode.AlarmTaskReport: {
      // 刷新警情列表
			ElMessage.success(`接收到新的警情，${payload.data.alarm_name}`)
			queryParams.pageNum = 1
      handleQuery();
      break;
    }
		// 一键下发-更新列表
		case EBizCode.AlarmTaskUpdate: {
			console.log('payload',payload);
			updatedList(payload.data)
      break;
    }
  }
});

function oneClickDelibery (item) {
	emit('OneClickDelivery',item);
}

function locationJump (item) {
	alarmId.value = toRaw(item).alarm_id
	emit('LocationJump',item)
}

onMounted(()=>{
	handleQuery();
})

function updatedList (obj) {
	console.log('obj',obj)
	dataList.value?.forEach(res=>{
		if(res.alarm_id === obj.alarm_id){
			if(obj.current_status == 5) {
				obj.current_status_name = '下发失败'
			}else if(obj.current_status == 4) {
				obj.current_status_name = '下发取消'
			}else if(obj.current_status == 3) {
				obj.current_status_name = '下发成功'
			}else if(obj.current_status == 2) {
				obj.current_status_name = '下发中'
			}else if(obj.current_status == 1) {
				obj.current_status_name = '未下发'
			}else {
				obj.current_status_name = '未知'
			}
			res.current_status_name = obj.current_status_name
			res.current_status = obj.current_status
		}
	})
}

/**
 * 查询
 */
 function handleQuery() {
	loading.value = true
  getJJTaskPage({
		time_type: queryParams.time_type,
    page: queryParams.pageNum,
    page_size: queryParams.pageSize
  }).then(data => {
    const { list, pagination } = data;
		const container = document.querySelector(".alarm-ul-scroll");
		container?.scrollTo(0,0)
    dataList.value = list || [];
		setTimeout(()=>{
			loading.value = false
		},500)
    total.value = pagination.total;
  });
}

function load () {
	const totalPage = Math.ceil(total.value / 20)
	if(queryParams.pageNum >= totalPage || dataList.value.length < 20) {
		return;
	}
	queryParams.pageNum += 1
	
	getJJTaskPage({
		time_type: queryParams.time_type,
    page: queryParams.pageNum,
    page_size: queryParams.pageSize
  }).then(data => {
    const { list } = data;
		let arr = toRaw(dataList.value).concat(list)
		dataList.value = arr
  });
}

function changeTimeType (val) {
	queryParams.pageNum = 1
	queryParams.time_type = val
	handleQuery();
}
</script>

<template>
  <div class="alarm-title flex">
		<div style="width: 100%">
			<el-select
				v-model="queryParams.time_type"
				placeholder="请选择"
				style="width: 98%"
				@change="changeTimeType"
			>
				<el-option
					v-for="item in optionData.alarmOptions"
					:key="item.value"
					:label="item.label"
					:value="item.value"
				/>
			</el-select>
		</div>
	</div>
	<div class="alarm-ul-scroll" v-infinite-scroll="load" v-if="dataList.length > 0" v-loading="loading">
		<div :class="alarmId == item.alarm_id ? ['alarm-item','currentColor'] : 'alarm-item'" v-for="item in dataList" :key="item.id" @click="locationJump(item)">
			<div class="flex">
				<div class="flex">
					<div class="danger" style="margin-right: 8px;">{{ item.alarm_level_name || ''}}</div>
					<div class="danger alarm-type" style="margin-right: 8px;" v-if="item.alarm_type" :title="item.alarm_type">{{ item.alarm_type }}</div>
					<div :class="item.current_status_name == '下发成功' || item.current_status_name == '下发中' ? 'green' : item.current_status_name == '下发失败' ? 'danger' : 'orange'">{{ item.current_status_name || ''}}</div>
				</div>
			</div>
			<div class="alarm-address">
				{{ item.alarm_name || '福建省厦门市集美区新兵街道时代焦点厦门影视传媒-拍摄基地拍摄记得拍摄基地拍摄记得拍摄基地拍摄记得' }}
			</div>
			<div class="flex">
				<el-button type="primary" size="small" @click="oneClickDelibery(item)">一键下发</el-button>
				<div class="alarm-time">
					{{ item.create_time || '' }}
				</div>
			</div>
		</div>
	</div>
	<el-empty description="暂无数据" v-else>
		<template #image>
			<img src="../../../assets/empty_home.png">
		</template>
	</el-empty>
</template>

<style lang="scss" scoped>
::-webkit-scrollbar {
  width: 8px;  /* 设置滚动条的宽度 */
}
::-webkit-scrollbar-thumb {
  background-color: rgba(46,144,255,0.5);
	border-radius: 2px; /* 设置滚动条滑块的背景色 */
}
:deep(.el-input__wrapper) {
	// background-color: #11253e;
	border: none;
	color: #fff;
}
.alarm-title {
	height: 38px;
	color: #fff;
	font-family: SourceHanSansSC-Bold;
	font-size: 14px;
	text-align: left;
	font-weight: 700;
	padding: 5px 8px;
	margin-bottom: 10px;
}
:deep(.el-input__wrapper) {
	background-color: #11253E;
	border: 1px solid #475467;
	border-color: #475467 !important;
	box-shadow: none;
	color: #fff;
}
:deep(.el-input__inner) {
	color: #98A2B3;
}
:deep(.el-input-number__decrease)  {
	background-color: #11253E;
	color: #fff;
	box-shadow: none;
	border-left: 1px solid #475467;
}
:deep(.el-input.is-disabled .el-input__wrapper) {
	background-color: #11253E;
	box-shadow: none;
	color: #606266;
}
:deep(.el-select) {
	--el-select-border-color-hover: none;
	--el-select-disabled-border:none;
}
:deep(.el-input-number.is-controls-right .el-input-number__increase) {
	border-bottom: 1px solid #475467;
	border-left: 1px solid #475467;
	box-shadow: none;
}
:deep(.el-input-number.is-controls-right .el-input-number__decrease) {
	border-left: 1px solid #475467;
	box-shadow: none;
}
:deep(.el-input-number__increase) {
	color: #fff;
	box-shadow: none;
	border-left: 1px solid #475467;
	background-color: #11253E;
}
.alarm-type {
	max-width: 85px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.danger,
.green,
.orange {
	font-family: SourceHanSansSC-Regular;
	font-size: 12px;
	color: #F97066;
	text-align: center;
	line-height: 24px;
	height: 24px;
	font-weight: 400;
	background:  rgba($color: #912828, $alpha: 0.5);
	border-radius: 2px;
	padding: 0 4px;
	cursor: default;
}
.green {
	background: rgba(42,139,125,0.30);
	color: #39BFA4;
}
.orange {
	background: rgb(253, 176, 34,0.30);
	color: #FDB022;
}
.currentColor {
	background: #175091 !important;
}
.alarm-ul-scroll {
	background: #001129;
	padding: 0px 8px 0 8px;
	height: 94%;
	overflow: auto;
	.alarm-item {
		min-height: 106px;
		background: #11253E;
		color: #fff;
		margin-bottom: 12px;
		padding: 8px;
		.alarm-time {
			font-family: SourceHanSansSC-Regular;
			font-size: 12px;
			color: #98A2B3;
			text-align: right;
			line-height: 24px;
			font-weight: 400;
		}
		.alarm-address{
			font-family: SourceHanSansSC-Regular;
			font-size: 14px;
			color: #FFFFFF;
			text-align: justify;
			line-height: 22px;
			font-weight: 400;
			margin-top: 8px;
			overflow: hidden;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			text-overflow: ellipsis;
			white-space: normal;
			margin-bottom: 8px;
		}
	}
}
.flex {
	display: flex;
	justify-content: space-between;
}
</style>
