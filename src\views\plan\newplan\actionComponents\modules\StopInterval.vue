<template>
  <div class="stop-wrapper">
    <div class="header"></div>
  </div>
</template>
<script>
export default {
  name: 'StopInterval'
};
</script>
<script setup>
import { onMounted, onUnmounted } from 'vue';
import 'element-plus/dist/index.css';
//#region 对外暴露方法
const setComponentData = options => {};
const getComponentData = () => {
  return null;
};
defineExpose({
  setComponentData,
  getComponentData
});
//#endregion
onMounted(() => {});
onUnmounted(() => {});
</script>
<style lang="scss" scoped>
.stop-wrapper {
  position: absolute;
  width: 100%;
  height: 100px;
  color: white;
  padding: 5px;
  margin-top: 10px;
}
</style>
