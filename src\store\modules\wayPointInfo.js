import { defineStore } from 'pinia';
import { reactive } from 'vue';
import { ruleCheck } from '@/views/plan/newplan/kmz/hocks/modules/rules';
import { getFrustum, createFrustum, getActionFrustumOptions, updateAllFrustum } from '@/views/plan/newplan/kmz/hocks';
import DataTracker from '@/views/plan/libs/DataTracker';
// setup 航点列表信息
export const useWayPointStore = defineStore('wayPointInfo', () => {
  // state
  const currentPointId = ref(null); // 当前选中航点id
  const currentPointIndex = ref(0); // 当前选中航点下标
  const currentActionId = ref(null); // 当前选中航点的动作id
  const pointList = ref([]); // 航点列表数据
  const dataTracker = new DataTracker();
  const currentModelInfo = reactive({
    latitude: 0,
    longitude: 0,
    height: 0,
    heading: 0,
    pitch: 0,
    roll: -90
  });

  //  通过传入数据对 pointlist进行数据处理 如果动作中存在冲突则添加告警
  function checkActionsRule(actionList) {
    // 初始化航点告警 默认不存在告警
    let hasWarnning = false;
    // 对actionList 按航点进行分组 按照航点唯一id分组 id:[] 的方式
    const groupedData = (actionList || []).reduce((groups, item) => {
      const { placemarkUuid } = item;
      if (!groups[placemarkUuid]) {
        groups[placemarkUuid] = [];
      }
      groups[placemarkUuid].push(item);
      return groups;
    }, {});
    const results = Object.values(groupedData) || [];
    const warningPlacemarkList = [];
    // 输出的是2维数组 [[],[{},{}]]
    results.forEach(resultList => {
      let warningCount = 0;
      resultList.forEach(item => {
        let {
          action = null,
          placemark = null,
          actionUuid = null,
          placemarkUuid = null,
          warningMsg = null,
          warning = false
        } = item;
        if (!placemarkUuid) {
          placemarkUuid = placemark?.uuid;
        }
        if (!actionUuid) {
          actionUuid = action?.uuid;
        }
        // 设置航点告警 -图标 i变色
        const warningPoint = pointList.value.find(point => {
          return point.uuid === placemarkUuid;
        });
        // 列表中的动作信息
        (warningPoint?.action || []).forEach(action => {
          if (actionUuid === action.actionUuid) {
            action.warning = warning;
            action.warningMsg = warningMsg || '';
            if (warning) {
              warningCount++;
              // 重复的index 要去除
              if (placemark && !warningPlacemarkList.includes(placemark.wpml_index)) {
                warningPlacemarkList.push(placemark.wpml_index);
              }
            }
          }
        });
        // 这里如果warningCount 大于0 那么就设置航点告警  反之 设置为false
        if (warningPoint) {
          warningPoint.warning = warningCount > 0;
        }
        // 标记当前航点是否存在告警
        if (!hasWarnning) {
          hasWarnning = warningCount > 0;
        }
      });
    });
    return {
      hasWarnning: hasWarnning,
      warningPlacemarkList: warningPlacemarkList
    };
  }

  // actions
  // 设置/初始化航点列表数据
  function setPointList(data) {
    try {
      pointList.value = data || [];
      if (pointList.value.length === 0) {
        currentPointId.value = null;
        currentPointIndex.value = 0;
        currentActionId.value = null;
        setCurrentAction(null);
      }
    } catch (error) {
    } finally {
      ruleCheck();
    }
  }

  /**
   * 初始化时 系统默认选中的第一个点和第一个点的动作
   */
  function initFirstSelect() {
    try {
      if (pointList.value.length === 0) {
        currentPointId.value = null;
        currentPointIndex.value = 0;
        currentActionId.value = null;
        setCurrentAction(null);
      } else {
        const firstPoint = pointList?.value[0] || null;
        const actionObj = firstPoint?.action[0] || null; // 拿到动作对象
        const firstActionUuid = actionObj?.actionUuid || '';
        const firstAction = actionObj?.action || null;
        if (firstPoint && actionObj && firstAction && firstActionUuid && firstActionUuid.length > 0) {
          setCurrentPoint(firstPoint, false);
          setCurrentAction(firstActionUuid);
          //   如果存在第一个动作那么就新增一个动作视锥体
          let frustun = getFrustum('action');
          let options = null;
          if (frustun) {
            const opt = frustun?.getOptions() ?? {};
            options = {
              ...opt
            };
            frustun.update(options);
          } else {
            // 这里获取相机创建的视锥体参数构建第一个动作视锥体参数
            const cameraFrustum = getFrustum('camera');
            let cameraOpt = cameraFrustum?.getOptions() ?? {};
            options = {
              ...cameraOpt,
              colorType: 'action'
            };
            let actionParms = getActionFrustumOptions(firstAction);
            options = {
              ...options,
              ...actionParms
            };
            frustun = createFrustum(options, 'action');
            updateAllFrustum(options);
          }
        }
      }
      return {
        currentPointId: currentPointId.value,
        currentActionId: currentActionId.value
      };
    } catch (error) {
      return {
        currentPointId: null,
        currentActionId: null
      };
    } finally {
      ruleCheck();
    }
  }

  /**
   * 新增一个航点
   * @params 经纬度等航点数据
   */
  function addPoint(params) {
    try {
      pointList.value.push({
        ...params,
        pointId: new Date().getTime()
      });

      // 新增就选中最新航点
      if (pointList.value.length > 0) {
        setCurrentPoint(pointList.value[pointList.value.length - 1], false);
      }
      // 同时设置选中动作为null
      currentActionId.value = null;
    } catch (error) {
    } finally {
      ruleCheck();
    }
  }

  /**  新增动作 
   * @param {*} actionOptions actionType 动作类型
   * {
     key: key,
      title: title,
      url: url, 
      actionTriggerType:"",// 动作触发方式
      actionUuid: actionUuid,
      actionFuncParam: actionFuncParam,
      actionActuatorFunc: actionOptions.type,
      action: null, // 动作对象
      actionGroup: null // 动作组对象 
   * }
   * @param {*} actionGroupId  当前组ID
   */
  function addAction(actionOptions, actionGroupId = 0) {
    try {
      let pIndex = pointList.value.findIndex(item => item.uuid === currentPointId.value);
      if (pIndex !== -1) {
        let actionList = pointList.value[pIndex]?.action || [];
        // 这里的actionId是实际的动画索引
        // 获取最大的动作索引
        let actionId = actionList.length;
        const actionUuid = actionOptions.actionUuid;
        actionList.push({
          action: actionOptions?.action ?? {},
          key: actionUuid,
          type: actionOptions.type,
          title: actionOptions.title || '',
          url: actionOptions.url || '',
          actionTriggerType: actionOptions.actionTriggerType || 'reachPoint',
          actionIndex: actionId,
          actionId: actionId,
          actionActuatorFunc: actionOptions.type,
          actionUuid: actionUuid,
          actionGroupId: actionGroupId
        });
        pointList.value[pIndex]['action'] = actionList;
        setCurrentAction(actionUuid);
      }
    } catch (error) {
    } finally {
      ruleCheck();
    }
  }

  // 设置当前动作id
  function setCurrentAction(id) {
    currentActionId.value = id;
    // 当切换或者修改航点和动作后触发
    window.$bus.emit('changeAction', {
      currentPointId: currentPointId.value,
      currentActionId: currentActionId.value,
      pointList: pointList.value
    });
  }

  /**
   * 设置当前航点id
   * @param {*} point
   * @param {*} setAction 是否要在这里设置的第一个点为高亮 默认是要 外部可以传入不进行设置(外部设置后)
   */
  function setCurrentPoint(point, setAction = true) {
    if (!point) {
      currentPointId.value = null;
      currentPointIndex.value = 0;
      setCurrentAction(null);
      return;
    }
    if (currentPointId.value === point.uuid) {
      return;
    }
    currentPointId.value = point.uuid;
    if (!setAction) {
      return;
    }
    // 直接查找对象，后续逻辑依赖于这个查找结果，无需提前检查setAction
    const findObj = pointList.value.find(item => item.uuid === point.uuid);
    if (!findObj) {
      setCurrentAction(null); // 如果找不到对应的对象，直接设置为空并返回
      return;
    }
    // 解构actions，简化后续访问
    const { action: actions = [], index = 0 } = findObj;
    currentPointIndex.value = index;
    // 直接使用三元运算符设置currentAction，减少if判断
    setCurrentAction(actions.length > 0 ? actions[0].actionUuid : null);
  }

  function checkZero(value) {
    return value !== 0 ? value : 0;
  }

  /**
   * 删除动作 删除动作 将当前点重至到前一个点id
   * @param {*} options
   * @param {*} options.pointIndex 航点下标
   * @param {*} options.action 动作
   */
  function deleteAction(options) {
    try {
      const { pointIndex, action } = options;
      const point = pointList.value[pointIndex];
      if (!point) {
        console.error('无法找到指定 pointIndex 的点');
        return;
      }
      let actionList = point.action || [];
      const actionUuidToDelete = action.actionUuid;
      // 被移除的动作id
      let curActionIndex = actionList.findIndex(item => item.actionUuid === actionUuidToDelete);
      // 这里如果选中第一个进行移除那么将选中项设置为后面一项  如果有进行actionList列表移除那么就是0 项 这里要注意
      // 设置后一个动作id
      let prevActionIndex = curActionIndex === 0 ? 1 : curActionIndex - 1;
      // 前一个动作
      const prevAction = actionList.find(item => {
        return item.actionIndex === prevActionIndex;
      });
      // 设置动作id
      currentActionId.value = prevAction ? prevAction.actionUuid : null;
      point.action = actionList;
      setCurrentPoint(pointList.value[pointIndex], false);
      pointList.value[pointIndex] = point;
    } catch (error) {
    } finally {
      ruleCheck();
    }
  }

  /**
   * 删除航点  后续这个方法将不会被使用 后续主要是全部列表重新绘制
   * @param {object} param
   *  @param {object} param.pointIndex 点索引
   */
  function deletePoint(param) {
    try {
      const { pointIndex } = param;
      let newValue = checkZero(pointIndex);
      // 增加了对newValue范围的校验，确保其不会小于0或超出列表长度
      if (newValue >= 0 && newValue < pointList.value.length) {
        //#region 找到后一个航点信息
        // pointList.value.splice(newValue, 1);// 这里不需要进行移除 所以这里是 newValue === 0 ? 1 是1 不是0
        let hightLightPointIndex = newValue === 0 ? 1 : newValue - 1;
        const prevPoint = pointList.value.find(item => {
          return item.index === hightLightPointIndex;
        });
        currentPointId.value = prevPoint ? prevPoint.uuid : '';
        let actions = prevPoint?.action || [];
        currentActionId.value = actions.length > 0 ? actions[0].actionUuid : null;
        return true;
        //#endregion
      } else {
        // 如果newValue不在有效范围内，则将currentPointId设置为null
        currentPointId.value = null;
        currentPointIndex.value = 0;
        currentActionId.value = null;
        return false;
      }
    } catch (error) {
      console.error('删除航点时发生错误：', error);
      currentPointId.value = null;
      currentPointIndex.value = 0;
      currentActionId.value = null;
      return false;
    } finally {
      // 删除成功后，更新currentPointId和currentActionId的值
      ruleCheck();
    }
  }

  // 清空航点和动作
  function clearAll() {
    pointList.value = [];
    currentPointId.value = null;
    currentPointIndex.value = 0;
    currentActionId.value = null;
  }
  function setCurrentActionNull() {
    currentActionId.value = null;
    window.$bus.emit('setAction', null);
  }
  function getCurrentPointAndAction() {
    return {
      currentPointId: currentPointId.value,
      currentActionId: currentActionId.value
    };
  }

  //#region 设置当前模型的相关信息
  function setCurrentModelInfo(options) {
    if (!options) {
      return;
    }
    const { latitude = 0, longitude = 0, height = 0, hpr = null } = options;
    // const position = toCartesian3(longitude, latitude, height);
    currentModelInfo.hpr = hpr;
    currentModelInfo.height = height;
    currentModelInfo.longitude = longitude;
    currentModelInfo.latitude = latitude;
    return currentModelInfo;
  }
  function getCurrentModelInfo() {
    return currentModelInfo;
  }
  //#endregion

  return {
    currentPointId,
    currentActionId,
    currentPointIndex,
    pointList,
    dataTracker,
    currentModelInfo,
    setCurrentAction,
    setCurrentPoint,
    setPointList,
    deleteAction,
    deletePoint,
    addPoint,
    addAction,
    clearAll,
    initFirstSelect,
    getCurrentPointAndAction,
    checkActionsRule,
    setCurrentActionNull,
    setCurrentModelInfo,
    getCurrentModelInfo
  };
});
