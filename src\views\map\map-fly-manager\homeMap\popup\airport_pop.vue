<template>
  <div class="pop-wrapper">
    <div class="pw-title">
      <el-row>
        <el-col :span="2" class="nick">
          <svg-icon icon-class="airfield" style="width: 20px; height: 20px; margin-left: 4px; margin-top: 9px" />
        </el-col>
        <el-col :span="16" class="nick"> {{ dockInfo.nickname }}</el-col>
        <!-- <el-col :span="6"></el-col> -->
        <el-col :span="6">
          <div class="status" :class="{ 'is-active': osdInfo.mode_code === -1 }">
            {{ getEnumKey(EDockModeCode, osdInfo.mode_code) }}
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="pw-content">
      <el-row class="c-row">
        <el-col :span="4" class="c-title">网速</el-col>
        <el-col :span="8" class="c-text">{{ osdInfo.network_state + ' kb/s' }} </el-col>
        <el-col :span="4" class="c-title">电压</el-col>
        <el-col :span="8" class="c-text">{{ osdInfo.working_voltage + ' mV' }}</el-col>
      </el-row>
      <el-row class="c-row">
        <el-col :span="4" class="c-title">电流</el-col>
        <el-col :span="8" class="c-text">{{ osdInfo.working_current + ' mA' }}</el-col>
        <el-col :span="4" class="c-title">风速</el-col>
        <el-col :span="8" class="c-text">{{ (osdInfo.wind_speed / 10).toFixed(2) + ' m/s' }}</el-col>
      </el-row>
      <el-row class="c-row">
        <el-col :span="4" class="c-title">雨量</el-col>
        <el-col :span="8" class="c-text">{{ getEnumKey(RainfallEnum, osdInfo.rainfall) }}</el-col>
        <el-col :span="4" class="c-title">环境</el-col>
        <el-col :span="8" class="c-text">{{ osdInfo.environment_temperature + ' °C' }}</el-col>
      </el-row>
      <el-row class="c-row">
        <el-col :span="4" class="c-title">机场</el-col>
        <el-col :span="8" class="c-text">{{ osdInfo.temperature + ' °C' }}</el-col>
        <el-col :span="4" class="c-title">无人机</el-col>
        <el-col :span="8" class="c-text">
          <el-link style="color: #409eff">
            <span @click="openNavPopup()" class="truncate-text"> {{ dockInfo.device_nickname }} </span>
          </el-link>
        </el-col>
      </el-row>
      <div class="moreInfo" @click="moreInfo()" :class="{ 'is-active': osdInfo.mode_code === -1 }">查看详情</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'airportPop'
};
</script>
<script setup>
import { onMounted, onUnmounted, defineExpose, reactive, ref, toRefs } from 'vue';
import { RainfallEnum, EDockModeCode } from '@/views/map/map-fly-manager/components/osdInfo';
import { getCesiumEngineInstance, flyTo } from '@/components/Cesium/libs/cesium';
import { createNavInfoPopup } from './popup';
import router from '@/router';

let dockInfo = reactive({
  nickname: '', //机场名
  dock_sn: '', //机场SN号
  dock_xyz: '', //机场XYZ
  device_nickname: '', //无人机名
  device_sn: '', //无人机SN号
  device_xyz: '' //无人机XYZ
  // alarm_id: '', //警情ID
  // flight_id: '', //飞行任务ID
  // wayline_id: '' //航线文件ID
});

let osdInfo = reactive({
  mode_code: -1,
  network_state: '--',
  working_voltage: '--',
  working_current: '--',
  wind_speed: 0,
  rainfall: '--',
  environment_temperature: '--',
  temperature: '--'
});

// 枚举获取值
const getEnumKey = (enumObject, value) => {
  return Object.keys(enumObject).find(key => enumObject[key] === value);
};

// 跳转实时飞行页面
const moreInfo = () => {
  if (osdInfo.mode_code === -1) {
    ElMessage.warning('该机场处于离线状态');
    return;
  }
  if (dockInfo.dock_sn === '') {
    ElMessage.warning('无机场相关SN信息');
    return;
  }
  router.push({
    path: '/real-time-flight',
    query: {
      dock_sn: dockInfo.dock_sn, //机场序列号
      nickname: dockInfo.nickname, //机场名
      device_sn: dockInfo.device_sn, //飞机序列号
      device_nickname: dockInfo.device_nickname, //无人机名
      // alarm_id: dockInfo.alarm_id, //警情ID
      // flight_id: dockInfo.flight_id, //飞行任务ID
      // wayline_id: dockInfo.wayline_id, //航线文件ID
      lon: dockInfo.dock_xyz.x, // 经纬度
      lat: dockInfo.dock_xyz.y, // 经纬度
      altitude: dockInfo.dock_xyz.z, // 高度
      camera_selected: '',
      type: dockInfo.type
    }
  });
};

// 跳转无人机弹窗
const openNavPopup = () => {
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  let message = createNavInfoPopup(flyView, dockInfo, true);
  if (message.indexOf('未上线') > 0 && message !== undefined) {
    ElMessage.warning(message);
  }
};

/**
 * 设置组件数据
 * @param {*} options {donkInfo:{},osdInfo:{}}
 */
const setComponentData = options => {
  if (options && typeof options === 'object') {
    const nowDockInfo = options.dockInfo;
    for (const key in dockInfo) {
      if (nowDockInfo.hasOwnProperty(key)) {
        dockInfo[key] = nowDockInfo[key];
      }
    }
    const str = '--';
    const nowOsdInfo = options.osdInfo;
    if (nowOsdInfo !== null && nowOsdInfo !== undefined) {
      osdInfo.mode_code = nowOsdInfo.basic_osd?.mode_code;
      osdInfo.network_state = nowOsdInfo.basic_osd?.network_state?.rate;
      osdInfo.working_voltage = nowOsdInfo.work_osd?.working_voltage ?? str;
      osdInfo.working_current = nowOsdInfo.work_osd?.working_current ?? str;
      osdInfo.wind_speed = nowOsdInfo.basic_osd?.wind_speed;
      osdInfo.rainfall = nowOsdInfo.basic_osd?.rainfall;
      osdInfo.environment_temperature = nowOsdInfo.basic_osd?.environment_temperature;
      osdInfo.temperature = nowOsdInfo.basic_osd?.temperature;
    }
  }
};
// 对外抛出方法
defineExpose({
  setComponentData
});

onMounted(() => {});
onUnmounted(() => {});
onBeforeUnmount(() => {});
</script>

<style scoped lang="scss">
.pop-wrapper {
  position: absolute;
  width: 300px;
  height: 206px;
  background: #001129;
  user-select: none;
  z-index: 2;
  font-family: SourceHanSansSC-Bold;
}
.pw-title {
  width: 300px;
  height: 38px;
  line-height: 38px;
  background-color: #11253e;
  font-family: SourceHanSansSC-Regular;
  .nick {
    font-size: 14px;
    color: #f5f6f8;
    text-align: left;
    font-weight: 400;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .status {
    margin-top: 7px;
    background: rgba(42, 139, 125, 0.5);
    border-radius: 2px;
    width: 68px;
    height: 24px;
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;
    color: #39bfa4;
    text-align: center;
    line-height: 22px;
    font-weight: 400;
    &.is-active {
      color: #98a2b3;
      background: rgba(44, 62, 86, 0.5);
    }
  }
}
.pw-content {
  width: 300px;
  height: 168px;
  .c-row {
    font-size: 14px;
    color: #e4e7ec;
    line-height: 22px;
    font-weight: 500;
    padding: 5px;
  }
  .c-title {
    text-align: center;
  }
  .c-text {
    text-align: center;
  }
  .moreInfo {
    width: 272px;
    height: 32px;
    border-radius: 2px;
    color: #fff;
    background-color: #2e90fa;
    margin-left: 14px;
    text-align: center;
    font-size: 14px;
    line-height: 32px;
    &.is-active {
      color: #98a2b4;
      background: rgba(44, 62, 86, 0.5);
    }
  }
}
.truncate-text {
  display: inline-block;
  max-width: 90px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-top: -4px;
}
</style>
