//#region actionActuatorFuncParam

//#region 1 takePhoto

export const PAYLOAD_LENS_INDEX = {
  zoom: 'zoom', // 存储变焦镜头拍摄照片
  wide: 'wide', // 存储广角镜头拍摄照片
  narrow_band: 'narrow_band', // 存储窄带镜头拍摄照片
  visable: 'visable', // 存储可见光照片
  ir: 'ir' // 存储红外镜头拍摄照片
};

export const PAYLOAD_LENS_INDEX_CN_NAME = {
  [PAYLOAD_LENS_INDEX.zoom]: '变焦照片',
  [PAYLOAD_LENS_INDEX.wide]: '广角照片',
  [PAYLOAD_LENS_INDEX.ir]: '红外照片',
  [PAYLOAD_LENS_INDEX.narrow_band]: '窄带照片',
  [PAYLOAD_LENS_INDEX.visable]: '可见光照片'
};

export const PAYLOAD_LENS_INDEX_BY_DEVICE_TYPE = {
  'Matrice 3TD': {
    visable: 'visable', // 存储可见光照片}
    ir: 'ir' // 存储红外镜头拍摄照片
  },
  Matrice_3TD: {
    visable: 'visable', // 存储可见光照片}
    ir: 'ir' // 存储红外镜头拍摄照片
  },
  Default: PAYLOAD_LENS_INDEX
};

export const getPayloadLensIndex = deviceType => {
  return PAYLOAD_LENS_INDEX_BY_DEVICE_TYPE[deviceType] || PAYLOAD_LENS_INDEX_BY_DEVICE_TYPE.Default;
};

export function getPayloadLensIndexValue(lenses) {
  return lenses.map(lens => PayloadLensIndex[lens]).join(',');
}
// const lensesUsed = ['wide', 'ir', 'narrow_band'];
// const payloadLensIndex = getPayloadLensIndexValue(lensesUsed);
// console.log(payloadLensIndex); // "wide,ir,narrow_band"

export const USE_GLOBAL_PAYLOAD_LENS_INDEX = {
  0: 0, // 不使用全局设置
  1: 1 // 使用全局设置
};

//#endregion

//#region focus
export const IS_POINT_FOCUS = {
  0: 0, // 区域对焦
  1: 1 // 点对焦
};
//#endregion

//#region gimbalRotate
//云台偏航角转动坐标系
export const GIMBAL_HEADING_YAW_BASE = {
  north: 'north' // 相对地理北
};
//云台转动模式
export const GIMBAL_ROTATE_MODE = {
  absoluteAngle: 'absoluteAngle' // 绝对角度，相对于正北方的角度
};
//#endregion

//#region rotateYaw
//飞行器偏航角转动模式
export const AIR_CRAFT_PATH_MODE = {
  clockwise: 'clockwise', // 顺时针旋转
  counterClockwise: 'counterClockwise' // 逆时针旋转
};
//#endregion

//#region accurateShoot
//是否框选精准复拍目标
export const ACCURATE_FRAME_VALID = {
  1: 1, // 已框选目标物
  0: 0 // 未框选目标物
};
// *注：该值设置为1，复拍时飞行器会自主寻找目标进行拍摄。该值设置为0，复拍时飞行器只会按照飞行器姿态和负载姿态进行动作重复，不会自主寻找目标
//#endregion

//#region orientedShoot
//相机类型
export const ORIENTED_CAMERA_TYPE = {
  52: 52, // 52（机型：M30双光相机）,
  53: 53, //53（机型：M30T三光相机）
  66: 66, //  66（机型：Mavic 3E 相机）
  67: 67, // 67（机型：Mavic 3T 相机）
  80: 80, // 80（机型：Matrice 3D 相机）
  81: 81 //  81（机型：Matrice 3TD 相机）
};

// 拍照模式
export const ORIENTED_PHOTO_MODE = {
  normalPhoto: 'normalPhoto', // 普通拍照
  lowLightSmartShooting: 'lowLightSmartShooting' // 低光智能拍照
};
//#endregion

//#region recordPointCloud
// 点云操作
export const RECORD_POINT_CLOUD_OPERATE = {
  startRecord: 'startRecord', // 开始点云录制
  pauseRecord: 'pauseRecord', // 暂停点云录制
  resumeRecord: 'resumeRecord', //继续点云录制
  stopRecord: 'stopRecord' //结束点云录制
};

//#endregion

//#endregion
