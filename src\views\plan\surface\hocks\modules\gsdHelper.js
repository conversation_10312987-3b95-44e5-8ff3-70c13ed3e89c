import * as Cesium from 'cesium';

import { usePlanInfoStore } from '@/store/modules/planInfo.js';
import { parmsInfoRect } from './generateWayLineHandle';
import { cameraTypeEnum } from '../../props/config';
import { getCmosData } from './cameraHeleper';
import { CAMERA_TYPE_ENUM } from '@/config';
const planInfoStore = usePlanInfoStore();

// 在摄影测量与遥感中，影像地面分辨率（GSD）是指数字影像的单个像素大小所对应的实际地面的距离，单位：厘米/像素。
// GSD 需要如下参数才能计算： 相机分辨率，传感器尺寸，焦距（真实焦距），以及对地高度。
// 可以通过这个进行计算：GSD=航高*像元尺寸/焦距

/**
 * 计算无人机飞行高度
 *
 * @param {number} imW - 图像宽度（像素）
 * @param {number} GSD - 地面分辨率
 * @param {number} Fr - 相机焦距（mm）
 * @param {number} Sw - 相机传感器宽度（mm）
 * @returns {number} - 飞行高度（米）
 */
function calculateFlightHeight(imW, GSD, Fr, Sw) {
  if (imW <= 0 || GSD <= 0 || Fr <= 0 || Sw <= 0) {
    throw new Error('所有输入参数必须为正数');
  }
  const height = (imW * GSD * Fr) / (Sw * 100);
  return height;
}

/**
 * 计算地面分辨率 (GSD)
 * calculateGSD(5472, 200, 8.8, 12.8333); // 算法验证 5.33
 * @param {number} imW - 图像宽度（像素）
 * @param {number} height - 飞行高度（米）
 * @param {number} Fr - 相机焦距（mm）
 * @param {number} Sw - 相机传感器宽度（mm）
 * @returns {number} - 地面分辨率（GSD）
 */
export function calculateGSD(imW, height, Fr, Sw) {
  if (imW <= 0 || height <= 0 || Fr <= 0 || Sw <= 0) {
    // throw new Error('所有输入参数必须为正数');
    return 0;
  }
  const GSD = (Sw * height * 100) / (imW * Fr);
  return GSD;
}

/**
 * 通过地面分辨率 (GSD) 反推飞行高度
 * @param {number} imW - 图像宽度（像素）
 * @param {number} GSD - 地面分辨率
 * @param {number} Fr - 相机焦距（mm）
 * @param {number} Sw - 相机传感器宽度（mm）
 * @returns {number} - 飞行高度（米）
 */
export function calculateHeight(imW, GSD, Fr, Sw) {
  if (imW <= 0 || GSD <= 0 || Fr <= 0 || Sw <= 0) {
    // throw new Error('所有输入参数必须为正数');
    return 0;
  }
  const height = (imW * GSD * Fr) / (Sw * 100);
  return height;
}

//#region 根据高度计算gsd
export function getIrGSD(value) {}
export function getVisibleGSD(value) {}
//#endregion

//#region 根据高度值计算gsd

export function caculateGSDByHeight(type = CAMERA_TYPE_ENUM.visable, h = 100) {
  let cameraInfo = getCmosData({
    camera: type
  });
  if (!cameraInfo) {
    return 0.5;
  }
  let opt = {
    focalLength: cameraInfo.focalLength,
    pixel: cameraInfo.pixel,
    pixels: cameraInfo.pixels,
    flightheight: parmsInfoRect.realFlightHight,
    pixels_width: cameraInfo.pixels_width,
    width: cameraInfo.width
  };
  let gsd = calculateGSD(opt.pixels_width, h, opt.focalLength, opt.width);
  // 这里检查是否为 NAN
  if (isNaN(gsd)) {
    gsd = 0;
  }
  return gsd;
}

export function caculateVisibleGSDByHeight(h = 100) {
  let cameraInfo = getCmosData({
    camera: CAMERA_TYPE_ENUM.visable
  });
  if (!cameraInfo) {
    return 0.5;
  }
  let opt = {
    focalLength: cameraInfo.focalLength,
    pixel: cameraInfo.pixel,
    pixels: cameraInfo.pixels,
    flightheight: parmsInfoRect.realFlightHight,
    pixels_width: cameraInfo.pixels_width,
    width: cameraInfo.width
  };
  let gsd = calculateGSD(opt.pixels_width, h, opt.focalLength, opt.width);
  console.log('7777777777777', gsd);
  return gsd;
}

//#endregion

//#region 根据gsd计算高度

export function caculateHeightByGSD(type = CAMERA_TYPE_ENUM.visable, gsdValue = 2) {
  let cameraInfo = getCmosData({
    camera: type
  });
  if (!cameraInfo) {
    throw new Error('未查询到相机信息');
  }
  let opt = {
    focalLength: cameraInfo.focalLength,
    pixel: cameraInfo.pixel,
    pixels: cameraInfo.pixels,
    flightheight: parmsInfoRect.realFlightHight,
    pixels_width: cameraInfo.pixels_width,
    width: cameraInfo.width
  };
  let h = calculateHeight(opt.pixels_width, gsdValue, opt.focalLength, opt.width);
  return h;
}

// export function caculateHeightByVisibleGSD(gsd_visable = 2) {
//   let cameraInfo = getCmosData({
//     camera: CAMERA_TYPE_ENUM.visable
//   });
//   if (!cameraInfo) {
//     throw new Error('未查询到相机信息');
//     return;
//   }
//   let opt = {
//     focalLength: cameraInfo.focalLength,
//     pixel: cameraInfo.pixel,
//     pixels: cameraInfo.pixels,
//     flightheight: parmsInfoRect.realFlightHight,
//     pixels_width: cameraInfo.pixels_width,
//     width: cameraInfo.width
//   };
//   let h = calculateHeight(opt.pixels_width, gsd_visable, opt.focalLength, opt.width);
//   return h;
// }

// export function caculateHeightByIrGSD(gsd_ir = 2) {
//   let cameraInfo = getCmosData({
//     camera: CAMERA_TYPE_ENUM.visable
//   });
//   if (!cameraInfo) {
//     throw new Error('未查询到相机信息');
//   }
//   let opt = {
//     focalLength: cameraInfo.focalLength,
//     pixel: cameraInfo.pixel,
//     pixels: cameraInfo.pixels,
//     flightheight: parmsInfoRect.realFlightHight,
//     pixels_width: cameraInfo.pixels_width,
//     width: cameraInfo.width
//   };
//   let h = calculateHeight(opt.pixels_width, gsd_ir, opt.focalLength, opt.width);
//   return h;
// }
//#endregion
