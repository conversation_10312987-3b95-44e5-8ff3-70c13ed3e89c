<template>
  <el-dialog
    :model-value="visible"
    title="图元详情"
    width="700px"
    :close-on-click-modal="false"
    @update:model-value="handleClose"
  >
    <div class="feature-detail">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="图元名称">
          {{ featureData.featureName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="所属图层">
          {{ featureData.layerName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="图元地址" :span="2">
          {{ featureData.featureAddr || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="经度">
          {{ featureData.longitude || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="纬度">
          {{ featureData.latitude || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="联系人">
          {{ featureData.contact || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="联系电话">
          {{ featureData.telephone || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="创建者">
          {{ featureData.creator || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatDateTime(featureData.createTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="更新者">
          {{ featureData.updater || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ formatDateTime(featureData.updateTime) }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 坐标集合 -->
      <div v-if="featureData.featureCoords" class="detail-section">
        <h4>坐标集合</h4>
        <el-input
          :model-value="featureData.featureCoords"
          type="textarea"
          :rows="6"
          readonly
        />
      </div>

      <!-- 扩展属性 -->
      <div v-if="featureData.extraInfo" class="detail-section">
        <h4>扩展属性</h4>
        <el-input
          :model-value="formatJson(featureData.extraInfo)"
          type="textarea"
          :rows="6"
          readonly
        />
      </div>

      <!-- 备注 -->
      <div v-if="featureData.remark" class="detail-section">
        <h4>备注</h4>
        <el-input
          :model-value="featureData.remark"
          type="textarea"
          :rows="3"
          readonly
        />
      </div>

      <!-- 地图显示 -->
      <div class="detail-section">
        <h4>位置信息</h4>
        <div class="map-container">
          <div class="coordinate-display">
            <span class="coordinate-label">坐标：</span>
            <span class="coordinate-value">
              {{ featureData.longitude }}, {{ featureData.latitude }}
            </span>
            <el-button 
              type="primary" 
              size="small" 
              @click="copyCoordinate"
              style="margin-left: 10px"
            >
              复制坐标
            </el-button>
          </div>
          <!-- 这里可以集成地图组件显示位置 -->
          <div class="map-placeholder">
            <i class="el-icon-location" style="font-size: 48px; color: #409eff;"></i>
            <p>地图显示功能待集成</p>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  featureData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible']);

/**
 * 格式化日期时间
 */
function formatDateTime(dateTime) {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

/**
 * 格式化JSON
 */
function formatJson(jsonStr) {
  if (!jsonStr) return '';
  try {
    const obj = JSON.parse(jsonStr);
    return JSON.stringify(obj, null, 2);
  } catch (error) {
    return jsonStr;
  }
}

/**
 * 复制坐标
 */
function copyCoordinate() {
  const coordinate = `${props.featureData.longitude}, ${props.featureData.latitude}`;
  if (navigator.clipboard) {
    navigator.clipboard.writeText(coordinate).then(() => {
      ElMessage.success('坐标已复制到剪贴板');
    }).catch(() => {
      ElMessage.error('复制失败');
    });
  } else {
    // 兼容旧浏览器
    const textArea = document.createElement('textarea');
    textArea.value = coordinate;
    document.body.appendChild(textArea);
    textArea.select();
    try {
      document.execCommand('copy');
      ElMessage.success('坐标已复制到剪贴板');
    } catch (error) {
      ElMessage.error('复制失败');
    }
    document.body.removeChild(textArea);
  }
}

/**
 * 关闭弹窗
 */
function handleClose() {
  emit('update:visible', false);
}
</script>

<style lang="scss" scoped>
.feature-detail {
  .detail-section {
    margin-top: 20px;
    
    h4 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 14px;
      font-weight: 600;
    }
  }

  .map-container {
    .coordinate-display {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      padding: 10px;
      background-color: #f5f7fa;
      border-radius: 4px;

      .coordinate-label {
        font-weight: 600;
        color: #606266;
      }

      .coordinate-value {
        font-family: 'Courier New', monospace;
        color: #409eff;
        font-weight: 600;
      }
    }

    .map-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      border: 2px dashed #dcdfe6;
      border-radius: 4px;
      color: #909399;

      p {
        margin: 10px 0 0 0;
        font-size: 14px;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
