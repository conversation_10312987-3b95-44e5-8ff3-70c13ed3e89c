import request from '@/utils/request';

/**
 * 创建字典
 * @param {Object} data - 字典数据
 */
export function createDict(data) {
  return request({
    url: '/system/dict/create',
    method: 'post',
    data
  });
}

/**
 * 更新字典
 * @param {Object} data - 字典数据
 */
export function updateDict(data) {
  return request({
    url: '/system/dict/update',
    method: 'put',
    data
  });
}

/**
 * 删除字典
 * @param {Number} id - 字典ID
 */
export function deleteDict(id) {
  return request({
    url: '/system/dict/delete',
    method: 'delete',
    params: { id }
  });
}

/**
 * 获取字典详情
 * @param {Number} id - 字典ID
 */
export function getDict(id) {
  return request({
    url: '/system/dict/get',
    method: 'get',
    params: { id }
  });
}

/**
 * 获取字典树列表
 * @param {Object} params - 查询参数，包含keyword（搜索关键字）
 */
export function getDictList(params) {
  return request({
    url: '/system/dict/list',
    method: 'get',
    params
  });
}

/**
 * 获取指定编码的子字典
 * @param {String} code - 字典编码
 */
export function getDictChildren(code) {
  return request({
    url: `/system/dict/children/${code}`,
    method: 'get'
  });
}

/**
 * 根据字典编码获取字典信息
 * @param {String} dictCode - 字典编码
 */
export function getDictByCode(dictCode) {
  return request({
    url: `/system/dict/code/${dictCode}`,
    method: 'get'
  });
} 