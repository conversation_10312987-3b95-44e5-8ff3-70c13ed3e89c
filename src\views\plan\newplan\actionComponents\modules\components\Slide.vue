<template>
  <div class="wrapper">
    <div class="top">
      <span class="title">{{ data.title }}</span>
      <span class="value">
        <el-input-number
          v-show="showInput.input"
          ref="inputNumber"
          :controls="false"
          class="input-number"
          type="number"
          size="small"
          :step="data.step || 1"
          v-model="data.value"
          :max="data.max"
          :min="data.min"
          :precision="1"
          @blur="onBlur"
          @keyup.enter="handleEnter"
        ></el-input-number>
        <span class="input-number-span" @click="setInputNumberVisible(true)" v-show="!showInput.input">
          {{ data.value }}
        </span>
        <span class="unit"> {{ data.unit }} </span>
      </span>
    </div>
    <div class="header">
      <div class="left">
        <span
          class="uranus-btn"
          :class="{
            uranusBtnDisabled: decreaseVisible,
            notAllowed: decreaseVisible
          }"
          @click="onDecrease(data.step || 0.1)"
        >
          <el-icon>
            <Minus />
          </el-icon>
        </span>
      </div>
      <div class="middle">
        <el-slider
          v-model="data.value"
          :max="data.max"
          :min="data.min"
          placement="bottom"
          :step="1"
          @input="onChange"
        />
      </div>
      <div class="right">
        <span
          class="uranus-btn"
          :class="{
            uranusBtnDisabled: addVisible,
            notAllowed: addVisible
          }"
          @click="onAdd(data.step || 0.1)"
        >
          <el-icon> <Plus /> </el-icon
        ></span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'Slide'
};
</script>
<script setup>
import { onMounted, onUnmounted, computed } from 'vue';
import 'element-plus/dist/index.css';
import { Minus, Plus } from '@element-plus/icons-vue';
//#region 数据双向绑定

// 传入组件数据结构
// const dataRef = reactive({
//   title: '云台偏航角',
//   unit: '°',
//   acttionType: 'RotateYaw',
//   min: -180,
//   max: 180,
//   value: 0.0 // 默认最小单位值
// });
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
const data = computed({
  get: () => props.modelValue,
  set: value => {
    emits('update:modelValue', value);
  }
});

const inputNumber = ref(null);
const showInput = reactive({
  input: false,
  span: true
});
// 对外定义事件
const emits = defineEmits(['update:modelValue', 'changeHandle']); // 触发事件
const onDecrease = v => {
  try {
    // 这里设置默认值
    const { min = data.value?.min || -180, max = data.value?.max || 180 } = props.modelValue;
    let dataValue = data.value?.value;
    dataValue -= v;
    if (dataValue <= min) {
      dataValue = min;
    }
    if (dataValue >= max) {
      dataValue = max;
    }
    data.value.value = Number(dataValue.toFixed(1));
    emits('changeHandle', data.value.value);
  } catch (error) {}
};
const onAdd = v => {
  try {
    const { min = data.value?.min || -180, max = data.value?.max || 180 } = props.modelValue;
    let dataValue = data.value?.value;
    dataValue += v;
    if (dataValue <= min) {
      dataValue = min;
    }
    if (dataValue >= max) {
      dataValue = max;
    }
    data.value.value = Number(dataValue.toFixed(1));
    console.log('onAdd:', data.value.value);
    emits('changeHandle', data.value.value);
  } catch (error) {}
};
const onChange = v => {
  try {
    data.value.value = Number(v.toFixed(1));
    emits('changeHandle', Number(v.toFixed(1)));
  } catch (error) {
    console.log('error:', error);
  }
};

const decreaseVisible = computed(() => {
  return data.value.value <= data.value.min;
});
const addVisible = computed(() => {
  return data.value.value >= data.value.max;
});

const setInputNumberVisible = v => {
  showInput.input = v;
  showInput.span = !v;
  if (inputNumber.value) {
    inputNumber.value.focus();
    inputNumber.value.select();
  }
};
const onBlur = () => {
  showInput.input = false;
  showInput.span = true;
};
const handleEnter = () => {
  if (showInput.input) {
    showInput.input = false;
    showInput.span = true;
  }
};
//#endregion
onMounted(() => {});
onUnmounted(() => {});
</script>
<style lang="scss" scoped>
::v-deep.el-input {
  background-color: #313131;
  color: white;
  border: 0px !important;
}
::v-deep.el-input-number .el-input__inner {
  background-color: #313131;
  color: white;
  font-size: 20px !important;
  height: 28px !important;
  text-align: center;
  color: #2d8cf0 !important;
  font-weight: 600;
  border-color: transparent !important; /* 使边框也变透明 */
}

::v-deep .el-slider .el-slider__runway {
  background-color: #001229 !important;
}

::v-deep.el-input-number .el-input__inner:focus {
  border-color: aqua !important;
}
::v-deep.el-input-number .is-disabled .el-input__wrapper {
  background-color: #313131;
  border: 0px !important;
}
::v-deep.el-input-number .el-input__wrapper {
  background-color: #313131;
}
::v-deep.el-input-number__decrease,
::v-deep.el-input-number__increase,
::v-deep.el-input-number__input {
  border-color: transparent !important;
  border: 0px !important;
}
.wrapper {
  // background-color: rgb(49, 49, 49);
  background-color: #11253e !important;
  color: white;
  padding: 5px;
  width: 100%;
  user-select: none;
  .top {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    // margin-top: 5px;
    height: 28px !important;
    padding: 2px 2px;
  }
  .header {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
    .left {
      width: auto;
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }
    .right {
      width: auto;
      display: flex;
      align-items: center;
    }
    .middle {
      display: flex;
      align-items: center;
      width: 100%;
      padding: 0px 10px;
    }
  }

  .header-input {
    margin: 5px 0px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .btn {
      display: flex;
      align-items: center;
      margin-left: 10px;
    }
  }
}

.input-number-span {
  cursor: text;
  height: 28px !important;
  font-size: 20px;
  line-height: 28px;
  text-align: center;
  color: #2d8cf0;
  font-weight: 600;
  flex: 0;
}
.unit {
  width: 10px;
  margin-left: 5px;
}
.round {
  border: 5px;
  color: white;
}
.item {
  width: auto;
  padding: 2px 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  background: rgba(45, 140, 240, 0.35);
  color: #ffffff40;
  margin-left: 5px;
  user-select: none;
  &:hover {
    cursor: pointer;
  }
}
.active {
  cursor: pointer;
  text-decoration: none !important;
  background: #5d5f61;
}

.uranus-btn {
  background: #505254;
  color: #fff;
  width: 20px;
  height: 20px;
  margin: 2px 2px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: auto auto;
  user-select: none;
  cursor: pointer;
}

.uranus-btn:hover {
  cursor: pointer;
  text-decoration: none !important;
  background: #5d5f61;
}

.uranusBtnDisabled {
  cursor: not-allowed !important;
  color: hsla(0, 0%, 100%, 0.25);
}

.notAllowed {
  cursor: not-allowed !important;
  color: hsla(0, 0%, 100%, 0.25);
  //   cursor: pointer;
  background: #505254 !important;
}

.color-blue {
  background: #2d8cf0;
  color: white;
}
</style>
