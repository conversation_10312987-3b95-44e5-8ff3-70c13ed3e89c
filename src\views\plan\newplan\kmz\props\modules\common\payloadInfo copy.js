//#region payloadEnumValue 相关信息
export const payloadEnumValueOptions = {
  42: {
    deviceTypes: ['H20'],
    label: 'H20'
  },
  43: {
    deviceTypes: ['H20T'],
    label: 'H20T'
  },
  52: {
    deviceTypes: ['M30'],
    label: 'M30双光相机'
  },
  53: {
    deviceTypes: ['M30T'],
    label: 'M30T三光相机'
  },
  61: {
    deviceTypes: ['H20N'],
    label: 'H20N'
  },
  66: {
    deviceTypes: ['M3E'],
    label: 'Mavic 3E 相机'
  },
  67: {
    deviceTypes: ['M3T'],
    label: 'Mavic 3T 相机'
  },
  68: {
    deviceTypes: ['M3M'],
    label: 'Mavic 3M 相机'
  },
  80: {
    deviceTypes: ['M3D'],
    label: 'Matrice 3D 相机'
  },
  81: {
    deviceTypes: ['M3TD'],
    label: 'Matrice 3TD 相机'
  },
  65534: {
    deviceTypes: ['PSDK'],
    label: 'PSDK'
  }
};

/**
 * 根据给定的Payload枚举值获取其配置信息。
 *
 * @param {string} payloadEnumValue - Payload枚举值，用于索引配置对象。
 * @return {Object} 返回与给定Payload枚举值对应配置对象。
 */
export function getPayloadEnumValueConfig(payloadEnumValue) {
  // 通过枚举值从配置对象中获取对应的配置信息
  return payloadEnumValueOptions[payloadEnumValue];
}

/**
 * 根据设备类型获取枚举值。
 * @param {string} deviceType 设备类型。
 * @returns {number|null} 对应的枚举值，如果未找到则返回null。
 */
export function getPayloadEnumValueByDeviceType(deviceType) {
  // 使用 Object.entries 来遍历 payloadEnumValueOptions 对象，
  // 这样可以确保只遍历对象本身的属性，而不是原型链上的属性。
  Object.entries(payloadEnumValueOptions).forEach(
    ([payloadEnumValue, { deviceTypes }]) => {
      // 检查 deviceTypes 是否包含指定的 deviceType。
      if (deviceTypes.includes(deviceType)) {
        // 对 payloadEnumValue 进行解析，并检查解析结果是否为数字。
        const enumValue = parseInt(payloadEnumValue, 10);
        if (!isNaN(enumValue)) {
          return enumValue;
        } else {
          // 如果 payloadEnumValue 不能被正确解析为数字，记录错误并返回 null。
          console.error(
            `payloadEnumValue "${payloadEnumValue}" is not a valid number.`
          );
          return null;
        }
      }
    }
  );

  // 如果没有找到匹配的枚举值，返回 null。
  return null;
}

// 使用示例
// console.log(getPayloadEnumValueConfig(42));
// // {
// //   deviceTypes: ['H20'],
// //   label: 'H20'
// // }
// console.log(getPayloadEnumValueByDeviceType('M3E')); // 66
// console.log(getPayloadEnumValueByDeviceType('DJI Mavic')); // null
//#endregion

//#region payloadPositionIndex 相关信息
export const payloadPositionIndexConfig = {
  0: {
    deviceTypes: ['M300 RTK', 'M350 RTK'],
    label: '飞行器1号挂载位置',
    value: 0,
    description:
      'M300 RTK，M350 RTK机型，对应机身左前方。其它机型，对应主云台。'
  },
  1: {
    deviceTypes: ['M300 RTK', 'M350 RTK'],
    label: '飞行器2号挂载位置',
    value: 1,
    description: 'M300 RTK，M350 RTK机型，对应机身右前方。'
  },
  2: {
    deviceTypes: ['M300 RTK', 'M350 RTK'],
    label: '飞行器3号挂载位置',
    value: 2,
    description: 'M300 RTK，M350 RTK机型，对应机身上方。'
  }
};

/**
 * 根据给定的位置索引从配置中获取相应的payload配置信息。
 * @param {number} positionIndex - 位置索引，用于查找对应的payload配置。
 * @return {Object} 返回与给定位置索引对应的具体payload配置对象。
 */
export function payloadPositionIndexConfig(positionIndex) {
  // 通过位置索引从配置对象中获取相应的payload配置
  return payloadPositionIndexConfig[positionIndex];
}

/**
 * 根据设备类型获取payload位置索引。
 * @param {string} deviceType - 设备类型。
 * @return {number|null} 对应的payload位置索引，如果未找到则返回null。
 */
export function getPayloadPositionIndexByDeviceType(deviceType) {
  // 遍历payloadPositionIndexConfig对象，确保只处理自有属性
  for (let positionIndex in payloadPositionIndexConfig) {
    if (!payloadPositionIndexConfig.hasOwnProperty(positionIndex)) {
      continue;
    }
    const { deviceTypes } = payloadPositionIndexConfig[positionIndex];
    // 检查设备类型是否包含在配置中
    if (deviceTypes.includes(deviceType)) {
      // 使用parseInt并明确指定基数为10
      return parseInt(positionIndex, 10);
    }
  }
  // 如果未找到匹配的设备类型，返回null
  return null;
}
//#endregion

//#region payloadParam 相关信息
export const payloadParamConfig = {
  // 负载挂载位置
  payloadParam: payloadPositionIndexConfig,
  // 负载对焦模式
  focusMode: {
    firstPoint: {
      value: 'firstPoint',
      label: '首个航点自动对焦'
    },
    custom: {
      value: 'custom',
      label: '标定对焦值对焦'
    }
  },
  // 负载测光模式
  meteringMode: {
    average: {
      value: 'average',
      label: '全局测光'
    },
    spot: {
      value: 'spot',
      label: '点测光'
    }
  },
  // 是否开启畸变矫正
  dewarpingEnable: {
    0: {
      value: 0,
      label: '0：不开启'
    },
    1: {
      value: 1,
      label: '1：开启'
    }
  },
  // 激光雷达回波模式
  meteringMode: {
    singleReturnStrongest: {
      value: 'singleReturnStrongest',
      label: '单回波'
    },
    dualReturn: {
      value: 'dualReturn',
      label: '双回波'
    },
    tripleReturn: {
      value: 'tripleReturn',
      label: '三回波'
    }
  },
  // 负载采样率
  samplingRate: {
    60000: {
      value: 60000,
      label: '60000 Hz'
    },
    80000: {
      value: 80000,
      label: '80000 Hz'
    },
    120000: {
      value: 120000,
      label: '120000 Hz'
    },
    160000: {
      value: 160000,
      label: '160000 Hz'
    },
    180000: {
      value: 180000,
      label: '180000 Hz'
    },
    240000: {
      value: 240000,
      label: '240000 Hz'
    }
  },
  // 负载扫描模式
  scanningMode: {
    epetitive: {
      value: 'epetitive',
      label: '重复扫描'
    },
    nonRepetitive: {
      value: 'nonRepetitive',
      label: '重复扫描'
    }
  },
  modelColoringEnable: {
    0: {
      value: 0,
      label: '不上色'
    },
    1: {
      value: 1,
      label: '真彩上色'
    }
  },
  //  注：存储多个镜头照片，格式如“<wpml:imageFormat>wide,ir</wpml:imageFormat>”
  imageFormat: {
    wide: {
      value: 'wide',
      description: '存储广角镜头照片',
      label: '广角镜头照片'
    },
    zoom: {
      value: 'zoom',
      description: '存储变焦镜头照片',
      label: '变焦照片'
    },
    ir: {
      value: 'ir',
      description: '存储红外镜头照片',
      label: '红外照片'
    },
    narrow_Band: {
      value: 'narrow_band',
      description: '存储窄带镜头拍摄照片',
      label: '窄带照片'
    },
    visible: {
      value: 'visable',
      description: '可见光照片',
      label: '可见光照片'
    }
  }
};

//#endregion
