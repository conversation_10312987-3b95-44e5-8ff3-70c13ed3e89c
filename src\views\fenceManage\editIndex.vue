<template>
  <div class="fence-edit-container">
    <!-- 全局加载遮罩 -->
    <div v-if="pageLoading" class="page-loading-mask">
      <div class="loading-spinner">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <div class="loading-text">加载中...</div>
      </div>
    </div>

    <!-- 加载错误提示 -->
    <div v-if="loadError" class="load-error-mask">
      <div class="error-container">
        <el-icon class="error-icon"><WarningFilled /></el-icon>
        <div class="error-title">加载失败</div>
        <div class="error-message">{{ errorMessage }}</div>
        <el-button type="primary" @click="handleRetry">重试</el-button>
      </div>
    </div>

    <div id="cesium-container"></div>
    <div class="left-wrap">
      <div class="left-content-panel bg-dark-blue">
        <div class="panel-header">
          <div class="title">{{ pageTitle }}</div>
          <div class="actions">
            <el-button v-if="!isDetail" type="primary" @click="handleSubmit" :loading="submitLoading">保存</el-button>
            <el-button @click="handleCancel">返回</el-button>
          </div>
        </div>
        <div class="panel-content">
          <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-width="100px"
            v-loading="loading"
            :disabled="isDetail"
            :class="{ 'detail-form': isDetail }"
          >
            <el-form-item prop="fenceName" label="围栏名称">
              <el-input v-model="form.fenceName" placeholder="请输入围栏名称" maxlength="16" show-word-limit />
            </el-form-item>

            <el-form-item prop="fenceAddr" label="围栏地址">
              <el-input v-model="form.fenceAddr" placeholder="请输入围栏地址" maxlength="64" show-word-limit />
            </el-form-item>

            <el-form-item prop="fenceType" label="围栏类型">
              <el-select v-model="form.fenceType" placeholder="请选择围栏类型" style="width: 100%">
                <el-option v-for="item in fenceTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>

            <el-form-item prop="fenceTarget" label="识别目标">
              <el-select v-model="form.fenceTarget" placeholder="请选择识别目标" style="width: 100%">
                <el-option v-for="item in targetOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>

            <el-form-item prop="businessType" label="业务类型">
              <el-select v-model="form.businessType" placeholder="请选择业务类型" style="width: 100%">
                <el-option
                  v-for="item in businessTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item prop="preProcess" label="处置措施">
              <el-select v-model="form.preProcess" placeholder="请选择前期处置措施" style="width: 100%">
                <el-option
                  v-for="item in preProcessOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item prop="contact" label="联系人">
              <el-input v-model="form.contact" placeholder="请输入联系人" maxlength="16" show-word-limit />
            </el-form-item>

            <el-form-item prop="telephone" label="联系电话">
              <el-input v-model="form.telephone" placeholder="请输入联系电话" maxlength="32" />
            </el-form-item>

            <el-form-item prop="fenceStatus" label="围栏状态">
              <el-radio-group v-model="form.fenceStatus">
                <el-radio label="active">启用</el-radio>
                <el-radio label="inactive">停用</el-radio>
              </el-radio-group>
            </el-form-item>

            <!-- <el-form-item prop="layerId" label="所属图层">
              <el-input-number v-model="form.layerId" :min="0" :precision="0" style="width: 100%" />
            </el-form-item> -->

            <el-form-item prop="remark" label="备注">
              <el-input v-model="form.remark" placeholder="请输入备注" type="textarea" maxlength="256" />
            </el-form-item>
            <el-form-item prop="fenceCoords" label="围栏区域">
              <el-input
                v-model="form.fenceCoords"
                placeholder="请输入备注"
                type="textarea"
                disabled
                class="coords-textarea"
              />
            </el-form-item>
            <el-form-item label="围栏统计">
              <div class="fence-info">
                <div class="info-item">
                  <span class="label">面积:</span>
                  <span class="value">{{ formatArea(currentFenceData.area) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">周长:</span>
                  <span class="value">{{ formatLength(currentFenceData.perimeter) }}</span>
                </div>
              </div>
            </el-form-item>
            <el-form-item>
              <div v-if="form.fenceCoords && form.fenceCoords.length === 0" class="coords-display empty">
                请在右侧地图上绘制围栏区域
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div class="toolbar" v-if="!isDetail">
      <div v-if="!isDetail" class="tool-item" @click="handleDrawPolygon">
        <el-tooltip content="绘制围栏" placement="left">
          <div class="tool-icon polygon-icon"></div>
        </el-tooltip>
      </div>
      <div v-if="!isDetail" class="tool-item" @click="handleClearMap">
        <el-tooltip content="清除围栏" placement="left">
          <el-icon class="tool-delete-icon"><Delete /></el-icon>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Delete, Loading, WarningFilled } from '@element-plus/icons-vue';
import { getFence, createFence, updateFence } from '@/api/fence';
import {
  initMap,
  startDrawPolygon,
  clearMap,
  drawExistingFence,
  destroyMap,
  currentFenceData,
  FENCE_TYPE
} from './fenceHandle';

const route = useRoute();
const router = useRouter();
const formRef = ref(null);
const loading = ref(false);
const submitLoading = ref(false);
// 页面整体加载状态
const pageLoading = ref(true);
const mapLoaded = ref(false);
const dataLoaded = ref(true); // 默认为true，如果不需要加载数据则直接认为已加载完成

// 错误处理
const loadError = ref(false);
const errorMessage = ref('');

// 判断页面模式
const isAdd = computed(() => route.query.mode === 'add');
const isDetail = computed(() => route.query.mode === 'detail');

// 页面标题
const pageTitle = computed(() => {
  if (isAdd.value) return '新增围栏';
  if (isDetail.value) return '围栏详情';
  return '编辑围栏';
});

// 监听地图和数据加载状态，当都加载完成时，隐藏加载遮罩
watch(
  [mapLoaded, dataLoaded],
  ([mapLoadedVal, dataLoadedVal]) => {
    if (mapLoadedVal && dataLoadedVal) {
      // 添加短暂延迟确保UI渲染完成
      setTimeout(() => {
        pageLoading.value = false;
      }, 300);
    }
  },
  { immediate: true }
);

// 围栏类型选项
const fenceTypeOptions = [
  { label: '入侵检测', value: 'intrusion' },
  { label: '区域监控', value: 'monitor' }
];

// 识别目标选项
const targetOptions = [
  { label: '人员', value: 'person' },
  { label: '车辆', value: 'vehicle' },
  { label: '全部', value: 'all' }
];

// 业务类型选项
const businessTypeOptions = [
  { label: '消防通道', value: 'fire_access' },
  { label: '危险区域', value: 'danger_area' },
  { label: '重点区域', value: 'important_area' }
];

// 前期处置措施选项
const preProcessOptions = [
  { label: '语音提示', value: 'speaker' },
  { label: '预警通知', value: 'alert' },
  { label: '无措施', value: 'none' }
];

// 表单数据
const form = reactive({
  id: undefined,
  fenceName: '',
  fenceAddr: '',
  fenceType: 'intrusion',
  fenceTarget: 'vehicle',
  businessType: 'fire_access',
  preProcess: 'speaker',
  contact: '',
  telephone: '',
  fenceStatus: 'active',
  fenceCoords: [],
  layerId: 0,
  remark: ''
});

// 表单验证规则
const rules = reactive({
  fenceName: [{ required: true, message: '围栏名称不能为空', trigger: 'blur' }],
  fenceType: [{ required: true, message: '围栏类型不能为空', trigger: 'change' }],
  fenceTarget: [{ required: true, message: '识别目标不能为空', trigger: 'change' }],
  businessType: [{ required: true, message: '业务类型不能为空', trigger: 'change' }],
  preProcess: [{ required: true, message: '前期处置措施不能为空', trigger: 'change' }],
  fenceStatus: [{ required: true, message: '围栏状态不能为空', trigger: 'change' }],
  fenceCoords: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (!value || value.length < 3) {
          callback(new Error('请至少绘制3个坐标点的围栏区域'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ],
  telephone: [
    {
      validator: (_, value, callback) => {
        if (!value || value.trim() === '') {
          callback();
          return;
        }

        // 手机号码正则：1开头，第二位为3-9，总共11位数字
        const mobileRegex = /^1[3-9]\d{9}$/;
        // 固定电话正则：区号-号码 或 区号号码，支持分机号
        const landlineRegex = /^(0\d{2,3}-?\d{7,8}(-\d{1,6})?)$/;

        if (!mobileRegex.test(value) && !landlineRegex.test(value)) {
          callback(new Error('请输入正确的手机号码或固定电话'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
});

// 监听当前围栏数据变化，更新表单
watch(
  () => currentFenceData.coordinates,
  newVal => {
    if (newVal && newVal.length > 0) {
      form.fenceCoords = newVal;
    }
  },
  { deep: true }
);

// 格式化面积
function formatArea(area) {
  if (!area || area <= 0) return '0 m²';
  if (area < 10000) {
    return `${area.toFixed(2)} m²`;
  } else {
    return `${(area / 10000).toFixed(2)} km²`;
  }
}

// 格式化长度
function formatLength(length) {
  if (!length || length <= 0) return '0 m';
  if (length < 1000) {
    return `${length.toFixed(2)} m`;
  } else {
    return `${(length / 1000).toFixed(2)} km`;
  }
}

// 绘制多边形
function handleDrawPolygon() {
  startDrawPolygon(data => {
    // 存储坐标数组，将在提交时转换为字符串
    form.fenceCoords = data.coordinates;
    currentFenceData.area = data.area;
    currentFenceData.perimeter = data.perimeter;
  });
}

// 清除地图
function handleClearMap() {
  clearMap();
  form.fenceCoords = [];
}

// 重试加载
async function handleRetry() {
  loadError.value = false;
  pageLoading.value = true;
  mapLoaded.value = false;
  dataLoaded.value = !isAdd.value && route.query.id;

  try {
    await initMap();
    mapLoaded.value = true;

    // 重新加载围栏数据
    if (!isAdd.value && route.query.id) {
      loadFenceData();
    }
  } catch (error) {
    handleLoadError('地图初始化失败，请检查网络连接后重试');
  }
}

// 处理加载错误
function handleLoadError(message) {
  pageLoading.value = false;
  loadError.value = true;
  errorMessage.value = message || '加载失败，请稍后重试';
}

// 加载围栏数据
function loadFenceData() {
  if (!isAdd.value && route.query.id) {
    loading.value = true;
    dataLoaded.value = false;
    getFence(route.query.id)
      .then(res => {
        // 转换字段名
        Object.keys(res).forEach(key => {
          // 转换服务端返回的下划线格式字段名为驼峰格式
          const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
          form[camelKey] = res[key];
        });

        // 特殊处理围栏坐标
        if (res.fence_coords) {
          form.fenceCoords = res.fence_coords;
        }

        // 如果有围栏坐标，在地图上绘制出来
        if (form.fenceCoords && form.fenceCoords.length > 0) {
          if (typeof res.fence_coords === 'string') {
            drawExistingFence(JSON.parse(form.fenceCoords));
          }
        }
      })
      .catch(err => {
        console.error(err);
        handleLoadError('获取围栏详情失败，请稍后重试');
      })
      .finally(() => {
        loading.value = false;
        dataLoaded.value = true;
      });
  }
}

// 提交表单
function handleSubmit() {
  formRef.value.validate(valid => {
    if (valid) {
      submitLoading.value = true;

      // 构造请求参数
      const params = { ...form };

      // 确保坐标数据是字符串格式
      if (Array.isArray(params.fenceCoords)) {
        params.fenceCoords = JSON.stringify(params.fenceCoords);
      } else if (params.fenceCoords === undefined || params.fenceCoords === null) {
        params.fenceCoords = '[]';
      }

      // 转换驼峰命名为下划线命名
      const snakeCaseParams = {};
      Object.keys(params).forEach(key => {
        // 驼峰转下划线: fenceName -> fence_name
        const snakeKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
        snakeCaseParams[snakeKey] = params[key];
      });

      const request = form.id ? updateFence : createFence;
      request(snakeCaseParams)
        .then(res => {
          ElMessage.success('保存成功');
          router.push('/fence-manage');
        })
        .catch(err => {
          console.error('保存失败', err);
          ElMessage.error('保存失败');
        })
        .finally(() => {
          submitLoading.value = false;
        });
    }
  });
}

// 取消编辑
function handleCancel() {
  router.push('/fence-manage');
}

// 生命周期钩子
onMounted(async () => {
  // 初始化地图
  try {
    await initMap();
    mapLoaded.value = true;
  } catch (error) {
    console.error('地图初始化失败', error);
    handleLoadError('地图初始化失败，请检查网络连接后重试');
  }

  // 加载围栏数据
  loadFenceData();
});

onUnmounted(() => {
  // 销毁地图
  destroyMap();
});
</script>

<style lang="scss" scoped>
.fence-edit-container {
  width: 100%;
  height: 100%;
  background-color: #171717;
  user-select: none;
  position: relative;
}

/* 全局加载遮罩样式 */
.page-loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 17, 41, 0.8);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;

  .loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .loading-icon {
    font-size: 40px;
    color: #fff;
    margin-bottom: 10px;
    animation: rotate-loading 1.5s linear infinite;
  }

  .loading-text {
    font-size: 16px;
    color: #fff;
    font-weight: 500;
    letter-spacing: 1px;
  }
}

/* 加载错误遮罩样式 */
.load-error-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 17, 41, 0.9);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;

  .error-container {
    background-color: #001129;
    border-radius: 8px;
    padding: 30px;
    width: 400px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
    border: 1px solid #1a2c47;
  }

  .error-icon {
    font-size: 48px;
    color: #ff4d4f;
    margin-bottom: 16px;
  }

  .error-title {
    font-size: 20px;
    color: #fff;
    font-weight: 600;
    margin-bottom: 12px;
  }

  .error-message {
    font-size: 14px;
    color: #a5b7d1;
    margin-bottom: 24px;
    line-height: 1.5;
  }
}

@keyframes rotate-loading {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}

#cesium-container {
  width: 100%;
  height: 100%;
  background-color: #171717;
  z-index: 1;
  position: absolute;
  left: 0px;
  top: 0px;
  right: 0px;
  bottom: 0px;
}

.left-wrap {
  width: 400px;
  height: 100%;
  position: absolute;
  left: 0px;
  transition: left 0.3s ease-in-out; /* 添加过渡效果 */
  top: 0px;
  z-index: 999;
}

.left-content-panel {
  width: 100%;
  height: 100%;
  background-color: #001129;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
}

.panel-header {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #1a2c47;
}

.panel-header .title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;

  /* 详情模式下的表单样式优化 */
  :deep(.el-form-item.is-disabled) {
    .el-input__wrapper {
      background-color: rgba(21, 41, 66, 0.8) !important;
      box-shadow: 0 0 0 1px #2c4a6a inset !important;
    }

    .el-input__inner,
    .el-textarea__inner {
      color: #e0e0e0 !important;
      -webkit-text-fill-color: #e0e0e0 !important;
    }

    .el-radio__label {
      color: #e0e0e0 !important;
    }

    .el-radio__input.is-disabled.is-checked .el-radio__inner {
      background-color: #1989fa !important;
      border-color: #1989fa !important;
    }

    .el-select .el-input.is-disabled .el-input__wrapper {
      background-color: rgba(21, 41, 66, 0.8) !important;
    }

    .el-input-number.is-disabled .el-input__wrapper {
      background-color: rgba(21, 41, 66, 0.8) !important;
    }

    .el-form-item__label {
      color: #8899b0 !important;
    }
  }
}

.toolbar {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 48px;
  background-color: rgba(0, 17, 41, 0.8);
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
  z-index: 999;
}

.toolbar-content {
  padding: 10px 0;
}

.tool-item {
  width: 40px;
  height: 40px;
  margin: 10px auto;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s;
}

.tool-item:hover {
  background-color: rgba(46, 144, 250, 0.3);
}

.tool-icon {
  width: 24px;
  height: 24px;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.tool-delete-icon {
  font-size: 20px;
  color: #fff;
}

.polygon-icon {
  background-image: url('@/assets/flightArea/svg/polygon.svg');
}

.circle-icon {
  background-image: url('@/assets/flightArea/svg/circle.svg');
}

.coords-display {
  padding: 8px 12px;
  background-color: #1a2c47;
  border-radius: 4px;
  color: #2e90fa;
  border: 1px solid #2c4a6a;
}

.coords-display.empty {
  color: #98a2b3;
  font-style: italic;
}

.fence-info {
  width: 100%;
  margin-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
  background-color: rgba(21, 41, 66, 0.5);
  border-radius: 4px;
  padding: 10px;
  border: 1px solid #2c4a6a;
}

.info-item {
  width: 50%;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.info-item .label {
  color: #98a2b3;
  margin-right: 8px;
}

.info-item .value {
  color: #2e90fa;
  font-weight: 500;
}

/* 坐标集合textarea样式 */
.coords-textarea {
  :deep(.el-textarea__inner) {
    &::-webkit-scrollbar {
      width: 8px !important;
    }

    &::-webkit-scrollbar-track {
      background-color: #001129;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 2px;
      width: 8px !important;
      background: #175192 !important;
    }
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px !important;
}

::-webkit-scrollbar-track {
  background-color: #001129;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb {
  border-radius: 2px;
  width: 8px !important;
  background: #175192 !important;
}

.bg-dark-blue {
  background-color: #001129;
}
</style>
