<template>
  <div class="map-100">
    <CesiumWidget />
    <Video :src="src" :width="width" :height="height" :autoplay="autoplay" />
    <Disaster />
    <DjInfo />
  </div>
</template>
<script>
export default {
  name: 'Mapfly'
};
</script>
<script setup>
import { onMounted, onUnmounted, onBeforeUpdate, onBeforeUnmount } from 'vue';
import * as Cesium from 'cesium';
import CesiumWidget from '@/components/Cesium/Cesium.vue';
import Disaster from '@/views/map/map-fly/component/Disaster.vue';
import DjInfo from '@/views/map/map-fly/component/DjInfo.vue';
import Video from '@/views/map/map-fly/component/Video.vue';
import {
  getOrCreateCesiumEngineInstance,
  CesiumLayerManager,
  imglayer,
  cialayer,
  flyTo,
  CesiumGLBLoader,
  startAnimation,
  stopAnimation,
  createPointEntity,
  CesiumFlight,
  interpolatePoints,
  addImageIcon,
  toCartesian3,
  delCesiumEngineInstance,
  globalConfigResource
} from '@/components/Cesium/libs/cesium';
import localCacheListener from '@/utils/localCacheListener';
import {
  airPortCoordinate,
  flyLineCoordinates,
  airModel,
  airPortModel,
  targetPointIcon,
  airPortIcon,
  KEY_FLY_DATA_INFO,
  videoUrl
} from '@/config/Map';
let engine = null;
let fly = null;
//#region 视频属性
let src = ref(videoUrl);
let width = ref(580);
let height = ref(327);
let start = ref(false);
let autoplay = ref(true);

//#endregion
const initCesium = () => {
  engine = getOrCreateCesiumEngineInstance('fly');
  const layerManager = new CesiumLayerManager(engine.viewer);
  layerManager.addLayer(imglayer);
  layerManager.addLayer(cialayer); // 定位到厦门
  flyTo(engine.viewer, 118.1276, 24.4838, 1000, 0, -30, 0, 2);
};

// 监听处理方法
const subscribeEvent = data => {
  setTimeout(() => {
    // 修改浏览器的本地缓存（可以在其他窗口或标签中模拟）
    const data = {
      lon: 118.1381748529617,
      lat: 24.49589577445994,
      jobInfo: '灾害点巡检',
      deviceName: 'FF-*********',
      flyInfo: '100米|2分15秒',
      alarmTime: '2024-01-10 12:39:23',
      flytime: '2024-01-10 12:39:23',
      flyplace: '湖里区-莲岳里',
      driver: '沐风',
      belong: '厦门四信',
      content: '地震'
    };
    localStorage.setItem(KEY_FLY_DATA_INFO, JSON.stringify(data));
  }, 10);

  // 获取数据
  const obj = data;
  // 获取目标点位置
  const targetPoi = [obj.lon, obj.lat, 0];
  // 给目标点加图标
  addImageIcon(engine.viewer, targetPoi, targetPointIcon);
  addImageIcon(engine.viewer, airPortCoordinate, airPortIcon);
  // 开始时马上拉到起飞点
  flyTo(engine.viewer, airPortCoordinate[0], airPortCoordinate[1], 100, 0, -30, 0, 2);

  //#region 设置无人机位置
  let airOrigin = toCartesian3([airPortCoordinate[0], airPortCoordinate[1], 1]);
  let airHpr = new Cesium.HeadingPitchRoll(Cesium.Math.toRadians(0), -90, 0);
  let airOrientation = Cesium.Transforms.headingPitchRollQuaternion(airOrigin, airHpr);
  const airModelLoader = new CesiumGLBLoader(engine.viewer, airModel, airOrigin, airOrientation);
  const model = airModelLoader.loadModel();
  model.position = airOrigin;
  //#endregion

  //#region 开始起飞
  fly = CesiumFlight.getInstance(engine.viewer);
  // 直接起飞
  fly.setAirInfo(model, airPortCoordinate, targetPoi);
  fly.setFlightLineAndPoints();
  // 设置航飞点
  setTimeout(() => {
    fly.startFlight();
  }, 0);
  //#endregion
};

const subscribe = () => {
  // 监听本地缓存
  const cacheListener = new localCacheListener(KEY_FLY_DATA_INFO);
  // 添加监听器
  cacheListener.subscribe(subscribeEvent);
  setTimeout(() => {
    // 修改浏览器的本地缓存（可以在其他窗口或标签中模拟）
    const data = {
      lon: 118.1381748529617,
      lat: 24.49589577445994,
      jobInfo: '灾害点巡检',
      deviceName: 'FF-*********',
      flyInfo: '100米|2分15秒',
      alarmTime: '2024-01-10 12:39:23',
      flytime: '2024-01-10 12:39:23',
      flyplace: '湖里区-莲岳里',
      driver: '沐风',
      belong: '厦门四信-公告研发',
      content: '地震'
    };
    localStorage.setItem(KEY_FLY_DATA_INFO, JSON.stringify(data));
  }, 100);
};
onMounted(() => {
  initCesium();
  // subscribe();
  const data = {
    phone: '454564988',
    alarmTime: '2024-01-10 12:39:23',
    alarmId: '地震灾害',
    startTime: '2024-01-10 12:40:23',
    lon: 118.1381748529617,
    lat: 24.49589577445994,
    content: '地震'
  };
  subscribeEvent(data);

  setTimeout(() => {
    autoplay = true;
  }, 4000);
});
onUnmounted(() => {});
onBeforeUnmount(() => {
  fly.stopFlight();
  fly = null;
});
</script>

<style lang="scss" scoped>
.map-100 {
  height: 100%;
  width: 100%;
  position: relative;

  .tool {
    position: absolute;
    top: 50px;
    color: red;
    background-color: antiquewhite;
    z-index: 100;
  }
}
</style>
