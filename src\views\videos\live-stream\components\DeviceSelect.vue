<template>
  <div class="select-contanier">
    <el-select class="count-select" v-model="cellCount" @change="handleCountChange">
      <el-option v-for="item in countData" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="无人机" name="first"> </el-tab-pane>
      <el-tab-pane label="机场" name="second"> </el-tab-pane>
    </el-tabs>
    <el-tree
      v-show="activeName === 'first' && droneData.length > 0"
      ref="droneRef"
      style="max-width: 600px"
      :data="droneData"
      show-checkbox
      node-key="id"
      :props="defaultProps"
      default-expand-all
      @check="handleCheckedDeviceChange"
    >
      <template #default="{ node }">
        <span class="custom-tree-node showname" :title="node.label" v-text="node.label"></span>
      </template>
    </el-tree>
    <el-empty v-if="activeName === 'first' && droneData.length === 0" description="暂无在线设备" >
      <template #image>
        <svg-icon icon-class="empty" style="width: 100px;height: 100px;"/>
      </template>
    </el-empty>

    <el-tree
      ref="dockRef"
      v-show="activeName === 'second' && dockData.length > 0"
      style="max-width: 600px"
      :data="dockData"
      show-checkbox
      node-key="id"
      :props="defaultProps"
      default-expand-all
      @check="handleCheckedDeviceChange"
    >
      <template #default="{ node }">
        <span class="custom-tree-node showname" :title="node.label" v-text="node.label"></span>
      </template>
    </el-tree>
    <el-empty v-if="activeName === 'second' && dockData.length === 0" description="暂无在线设备" >
      <template #image>
        <svg-icon icon-class="empty" style="width: 100px;height: 100px;"/>
      </template>
    </el-empty>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref } from 'vue';
import { getLiveCapacity } from '@/api/live';
import { DOMAIN } from '@/utils/constants';
import optionData from '@/utils/option-data';
import { useRouter } from 'vue-router';
import { settings } from 'nprogress';
import { useConnectWebSocket } from '@/hooks/useConnectWebSocket';
import { EBizCode } from '@/utils/constants';

useConnectWebSocket(payload => {
  if (!payload) {
    return;
  }
  switch (payload.biz_code) {
    // 在线设备变更
    case EBizCode.CoverOpen:
    case EBizCode.DeviceOnline:
    case EBizCode.DeviceOffline: {
      handleQuery(true);
      break;
    }
  }
});
const router = useRouter();

const emit = defineEmits(['onChange']);
const cellCount = ref(4);
const countData = ref([
  {
    label: '1宫格',
    value: 1
  },

  {
    label: '4宫格',
    value: 4
  },
  {
    label: '6宫格',
    value: 6
  },
  {
    label: '9宫格',
    value: 9
  }
]);
const defaultProps = {
  children: 'children',
  label: 'name',
  class: 'device-tree'
};
const props = defineProps({
  title: {
    type: String,
    default: ''
  }
});
const dockRef = ref(null);
const droneRef = ref(null);
const activeName = ref('first');
const dockData = ref([]);
const droneData = ref([]);
const checkedNodes = ref([]);
function handleClick() {}
function setCheckedNode(data, select) {
  if (data.cameras_list) {
    // 非摄像头
    let checkedKeys = select.checkedKeys || [];

    let cameras_list = data.cameras_list || [];
    cameras_list.forEach(element => {
      if (checkedKeys.length == 0) {
        // 取消全选
        let findIndex = checkedNodes.value.findIndex(item => item.id == element.id);
        if (findIndex !== -1) {
          // 删除操作改为置空操作
          checkedNodes.value[findIndex] = {};
        }
      } else {
        // 全选
        let emptyIndex = checkedNodes.value.findIndex(item => !item.id);
        if (emptyIndex !== -1) {
          // 删除操作改为置空操作
          checkedNodes.value[emptyIndex] = element;
        } else {
          checkedNodes.value.push(element);
        }
      }
    });
  } else {
    // 摄像头
    let findIndex = checkedNodes.value.findIndex(item => item.id == data.id);
    if (findIndex !== -1) {
      checkedNodes.value[findIndex] = {};
    } else {
      let emptyIndex = checkedNodes.value.findIndex(item => !item.id);
      if (emptyIndex !== -1) {
        checkedNodes.value[emptyIndex] = data;
      } else {
        checkedNodes.value.push(data);
      }
    }
  }
}
function resetDeviceData() {
  const list = checkedNodes.value;
  let checkedDevice = [];
  list.forEach(element => {
    if (!element.cameras_list) {
      // 摄像头
      let videoList = element.videos_list || [];
      if (!element.url_type && videoList.length === 0 && element.source != '1' && !element.droneSelected) {
        checkedDevice.push({
          droneSelected: null,
          cameraSelected: null,
          cameraName: null,
          cameraId: null,
          videoSelected: null, // 选择当前摄像头的第一个视频index
          claritySelected: optionData.clarityList[0].value,
          lensSelected: null,
          isDockLive: false, // videos_list的switch_video_types数量>0则为true
          isExternal: false
        });
      } else {
        let switch_video_types = videoList[0]?.switch_video_types || [];
        checkedDevice.push({
          droneSelected: element.droneSelected,
          channel:element.channel,
          cameraSelected: element.index || '',
          cameraName: element.name,
          cameraId: element.id,
          source: element.source,
          url_type: element.url_type,
          videoSelected: videoList[0]?.index, // 选择当前摄像头的第一个视频index
          claritySelected: optionData.clarityList[3].value,
          lensSelected: 'normal',
          isDockLive: switch_video_types.length > 0 ? true : false, // videos_list的switch_video_types数量>0则为true
          isExternal: element.source === '1' ? true : false // type=external为外部视频
        });
      }
    }
  });

  emit('onChange', checkedDevice);
}
function handleCheckedDeviceChange(data, select) {
  // 处理勾选的数据
  setCheckedNode(data, select);
  // 重置勾选数据
  resetDeviceData();
}
/**
 * 查询
 */
function handleQuery(refresh) {
  getLiveCapacity({}).then(data => {
    const list = data || [];
    let dockList = []; // 机场
    let droneList = []; // 无人机
    list.forEach(element => {
      element.id = element.sn;
      const cameras_list = element.cameras_list || [];
      cameras_list.forEach(c => {
        c.droneSelected = element.sn;
      });
      element.children = cameras_list || [];

      if (element.domain + '' === DOMAIN.DOCK) {
        dockList.push(element);
      } else if (element.domain + '' === DOMAIN.DRONE) {
        droneList.push(element);
      }
    });
    dockData.value = dockList || [];
    droneData.value = droneList || [];

    if (refresh) {
      // 重置选中状态
      resetCheckKeys();
    }
  });
}
onUnmounted(() => {
  window.$bus.off('liveStreamClose');
  window.$bus.off('changeVideoCount');
});
onMounted(() => {
  const query = router.currentRoute.value.query;
  if (query.tab) {
    activeName.value = query.tab;
  }

  handleQuery();
  window.$bus.on('liveStreamClose', dronePara => {
    // 删除选中的节点
    let findIndex = checkedNodes.value.findIndex(item => item.id == dronePara.cameraId);
    if (findIndex !== -1) {
      // checkedNodes.value.splice(findIndex, 1);
      // 删除操作改为置空操作
      checkedNodes.value[findIndex] = {};
    }
    resetCheckKeys();
  });
});

function resetCheckKeys() {
  let checkKeys = [];
  checkedNodes.value.forEach(element => {
    checkKeys.push(element.id);
  });
  dockRef.value?.setCheckedKeys(checkKeys);
  droneRef.value?.setCheckedKeys(checkKeys);
  resetDeviceData();
}
function handleCountChange(count) {
  checkedNodes.value = checkedNodes.value.filter(item=>{
     return Object.keys(item).length !== 0
  })
  window.$bus.emit('changeVideoCount', count);
}
</script>

<style  lang="scss" scoped>
:deep(.el-tree-node__label) {
  overflow: hidden;
  max-width: 160px;
  text-overflow: ellipsis;
  white-space: nowrap;
}
  .showname{
    width: 170px; // 定宽
    text-align: left;
    overflow: hidden !important;// 溢出部分隐藏
    white-space: nowrap !important;//禁止自动换行
    text-overflow:ellipsis !important;// 使溢出部分以省略号显示
    display: block !important;
  }
.select-contanier {
  background-color: #001129;
  height: 100%;
  border: 1px solid #001129;
  color: white;
  text-align: center;
  .count-select {
    margin: 10px 0;
  }
  .device-item {
    border-radius: 8px;
    font-size: 14px;
    padding: 5px 10px;
    background: #262c33;
    margin: 5px;
    .device-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
  }
  :deep(.el-tabs__item) {
    color: white;
    width: 120px;
  }
  :deep(.el-tabs__item.is-active) {
    color: var(--el-color-primary);
  }
  :deep(.el-checkbox-group) {
    display: flex;
    flex-direction: column;
    margin: 0 10px;
  }
  :deep(.el-tree) {
    background: transparent;
    color: white;
    --el-tree-node-hover-bg-color: #262c33;
  }
  :deep(.el-tree .is-current) {
    background-color:#001129;
  }
  :deep(.el-tree-node__content:hover) {
    background: #262c33;
  }
}
</style>
