<template>
  <div class="section-wrapper">
    <div class="section-header">
      <div class="title">{{ title }}</div>
    </div>
    <div class="section-content" v-if="cameraDataRef.length > 0">
      <div
        v-for="item in cameraDataRef"
        :key="item.id"
        class="section-content-item"
        :disabled="item.active"
        :class="{ active: item.active }"
        @click="onCameraTypeChangeHandle(item)"
      >
        {{ item.label }}
      </div>
    </div>
    <div class="section-content" v-else>
      <div class="camera-none">未配置相机数据请核实</div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'CameraSelect'
};
</script>
<script setup>
import { ref, toRaw } from 'vue';
import { cloneDeep } from 'lodash';
import { useDeviceStore } from '@/store/modules/device.js';
import { useWayPointStore } from '@/store/modules/wayPointInfo.js';
import { usePlanInfoStore } from '@/store/modules/planInfo.js';
import { useEditTrackerStore } from '@/store/modules/dataEditTracker.js';
const editTrackerStore = useEditTrackerStore();
const planInfoStore = usePlanInfoStore();
const deviceStore = useDeviceStore();
const isInitialized = ref(false);
const title = ref('镜头类型选择');
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
const data = computed({
  get: () => props.modelValue,
  set: value => {
    emits('update:modelValue', value);
  }
}); // 对外定义事件
const emits = defineEmits(['update:modelValue', 'changeHandle']); // 触发事件
// 通过传入数据计算得出
const cameraDataRef = ref([]);
/**
 * 根据给定对象在选项列表中更新活动状态，并返回活动状态列表
 * 主要用于检查和更新一组选项的活动状态，并判断是否有选项被选中
 * @param {Object} obj - 需要检查和更新活动状态的目标对象
 * @param {Array} options - 选项列表，包含多个选项对象
 * @returns {boolean} - 返回一个数组
 */
const getCheckActiveList = (obj, options) => {
  let activeList = [];
  // 根据当前设置状态，判断是否需要选中
  (options || [])?.forEach(item => {
    if (item.value === obj.value) {
      item.active = !obj.active;
    }
  });
  // 收集被选中的相机类型
  options.forEach(item => {
    if (item.active) {
      activeList.push(item.value);
    }
  });
  // 返回被选中的相机类型数组
  return activeList;
};
const onCameraTypeChangeHandle = obj => {
  const list = getCheckActiveList(obj, cloneDeep(cameraDataRef.value));
  // 如果只有一个那么就不再进行后续操作
  if (list.length < 1) {
    return;
  } else {
    (cameraDataRef.value || [])?.forEach(item => {
      if (item.value === obj.value) {
        item.active = !obj.active;
      }
    });
  }

  let result = [];
  (cameraDataRef.value || [])?.forEach(item => {
    if (item.active) {
      // 这里收集被选中的相机
      result.push(item.value);
    }
  });
  // 这里设置跟随航线的相机类型数据
  deviceStore?.setCameraSelect(result ?? []); // 使用可选链和空值合并操作符
  deviceStore?.setWayLineCameraType(result ?? []); // 使用可选链和空值合并操作符
  editTrackerStore.dataTracker.markAsModified();
  emits('changeHandle', result); // 抛出
};

// 监听变化并且执行初始化
const init = () => {
  let adp = deviceStore.deviceAdapter;
  // 检查 adp 中是否存在 getCmosValue 方法
  if (!adp || !adp.getCmosValue) {
    throw new Error('adp or adp.getCmosValue is not defined');
  }
  let cmosList = toRaw(adp?.getCmosValue() ?? []);
  if (!cmosList) {
    cameraDataRef.value = [];
    deviceStore.setCameraSelect([]);
    deviceStore.setWayLineCameraType([]);
    return;
  }
  cameraDataRef.value = [];
  // 渲染相机
  cmosList.forEach((cameraInfo, i) => {
    try {
      cameraDataRef.value.push({
        id: i,
        value: cameraInfo.cameraType,
        label: cameraInfo.cameraLabel,
        active: true
      });
    } catch (error) {
      console.error('Error in forEach loop:', error);
    }
  });
  reSetCameraType();
};

// 数据回显的时候进行监听并且设置默认选中值
const reSetCameraType = () => {
  // 这里获取一次当前设备支持的镜头类型
  let cmosList = toRaw(deviceStore.deviceAdapter?.getCmosValue() ?? null);
  if (!cmosList) {
    cameraDataRef.value = [];
    deviceStore.setCameraSelect([]);
    deviceStore.setWayLineCameraType([]);
    return;
  }
  // cameraSelected 这里相机如果不在 cmosList 中的 要被剔除
  let cameraSelected = deviceStore.cameraSelected.filter(camera => {
    return cmosList.some(cmos => {
      return cmos.cameraType === camera;
    });
  });

  (cameraDataRef.value || []).forEach((c, i) => {
    c.active = false;
  });

  // 循环结束后，设置第一个元素的active为true
  cameraSelected.forEach(camera => {
    let camertItem = cameraDataRef.value.find(item => {
      return item.value === camera;
    });
    camertItem && (camertItem.active = true);
  });
};

// 监听 deviceAdapter 的变化
const onAdapterReady = () => {
  if (deviceStore.deviceAdapter && !isInitialized.value) {
    isInitialized.value = true;
    init();
  }
};

//
const restCamera = () => {
  let adp = deviceStore.deviceAdapter;
  // 检查 adp 中是否存在 getCmosValue 方法
  if (!adp || !adp.getCmosValue) {
    throw new Error('adp or adp.getCmosValue is not defined');
  }
  let cmosList = toRaw(adp?.getCmosValue() ?? []);
  cameraDataRef.value = [];
  // 渲染相机
  cmosList.forEach((cameraInfo, i) => {
    try {
      cameraDataRef.value.push({
        id: i,
        value: cameraInfo.cameraType,
        label: cameraInfo.cameraLabel,
        active: true
      });
    } catch (error) {
      console.error('Error in forEach loop:', error);
    }
  });
};
onMounted(() => {
  deviceStore.$subscribe(onAdapterReady);
  init();
  restCamera();
  setTimeout(() => {
    editTrackerStore.dataTracker.reset();
  }, 500);
});
onUnmounted(() => {
  cameraDataRef.value = [];
});
</script>

<style scoped>
.section-wrapper {
  color: white;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  user-select: none;
  padding: 0px 0px;
}
.section-wrapper .section-header .title {
  font-size: 16px;
}
.section-wrapper .section-content {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 10px;
}
.section-wrapper .section-content .section-content-item {
  height: 30px;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  background-color: #555658;
}
.section-content-item:not(:first-child) {
  margin-left: 10px;
}
.section-content-item.active {
  background-color: #2d8cf0 !important;
  color: white;
}

.camera-none {
  width: 100%;
  text-align: center;
  background-color: #404040 !important;
  padding: 1px 10px;
  border-radius: 5px;
  color: white;
  cursor: not-allowed;
}
</style>
