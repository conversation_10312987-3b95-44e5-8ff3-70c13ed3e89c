import { generateKey } from '@/utils';
import { SHOOTTYPE_ENUM } from '@/views/plan/newplan/kmz/props';
// SHOOTTYPE_ENUM

class TemplatePlacemark {
  /**
   * @param {object} options
   */
  constructor(options) {
    this.uuid = options.uuid || generateKey();
    // 是否开启标定飞行
    this.wpml_caliFlightEnable = options.caliFlightEnable || 0;
    // 是否开启高程优化
    this.wpml_elevationOptimizeEnable = options.elevationOptimizeEnable || 1;
    // 是否开启智能摆拍
    this.wpml_smartObliqueEnable = options.smartObliqueEnable || 0;
    // 拍照模式
    this.wpml_shootType = options.shootType || SHOOTTYPE_ENUM.time;
    // 航线方向
    this.wpml_direction = options.direction || 0;
    // ellipsoidHeight 全局航线高度（椭球高）* 注：
    // 如果 wpml:height 选用相对起飞点高度，则 wpml:ellipsoidHeight 和 wpml:height 相同；
    // 如果 wpml:height 选用 EGM96 海拔高度或 AGL 相对地面高度，则 wpml:wpml:ellipsoidHeight 由 wpml:height 做相应转换得到。
    this.wpml_ellipsoidHeight = options.ellipsoidHeight || 0;
    // 全局航线高度（EGM96海拔高/相对起飞点高度/AGL相对地面高度）
    // * 注：该元素与 wpml:ellipsoidHeight 配合使用，二者是同一位置不同高程参考平面的表达。
    this.wpml_height = options.height || 0;
    // 测区外扩距离
    this.wpml_margin = options.margin || 0;
    this.wpml_overlap = {
      wpml_orthoCameraOverlapH: options?.overlap?.orthoCameraOverlapH || 80,
      wpml_orthoCameraOverlapW: options?.overlap?.orthoCameraOverlapW || 80,
      wpml_inclinedCameraOverlapH: options?.overlap?.inclinedCameraOverlapH || 80,
      wpml_inclinedCameraOverlapW: options?.overlap?.inclinedCameraOverlapW || 70
    };
    this.Polygon = {
      outerBoundaryIs: {
        LinearRing: {
          coordinates: options.coordinates
        }
      }
    };
  }
}

export { TemplatePlacemark };
