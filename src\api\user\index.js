import request from '@/utils/request';
import { MAIN_PATH, APPLICTION_USERS, API_VERSION } from '../config/index';
import { useUserStoreHook } from '@/store/modules/user';

// 主路径
const BASE_URL = MAIN_PATH + API_VERSION + APPLICTION_USERS;

/**
 * 登录成功后获取用户信息（昵称、头像、权限集合和角色集合）
 */
export function getUserInfo() {
  return request({
    url: `${BASE_URL}/current`,
    method: 'get'
  });
}

/**
 * 获取当前工作空间用户分页信息
 *
 * @param queryParams
 */
export function getUserPage(queryParams) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;

  return request({
    url: `${BASE_URL}/${workspace_id}/users`,
    method: 'get',
    params: queryParams
  });
}

export function uploadProfile(data) {
  return request({
    url: `${BASE_URL}upload`,
    method: 'post',
    responseType: 'upload',
    data
  });
}

export function downloadProfile(data) {
  return request({
    url: `${BASE_URL}download`,
    method: 'get',
    responseType: 'blob',
    params: data
  });
}

export function uploadOneFile(data) {
  return request({
    url: `${BASE_URL}uploadOneFile`,
    method: 'post',
    responseType: 'json',
    data
  });
}

/**
 * 导出用户
 *
 * @param queryParams
 * @returns
 */
export function exportUser(queryParams) {
  return request({
    url: '/api/v1/users/_export',
    method: 'get',
    params: queryParams,
    responseType: 'arraybuffer'
  });
}
