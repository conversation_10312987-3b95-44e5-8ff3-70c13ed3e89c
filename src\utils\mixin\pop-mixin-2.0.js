export const POPMIXIN = {
  data() {
    return {
      open: false
    };
  },
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    show: {
      handler: function (val) {
        if (val === this.open) {
          return;
        }
        if (this.beforeOpen) {
          this.beforeOpen();
        } else {
          this.dialogOpen();
        }
      },
      immediate: true
    }
  },
  methods: {
    dialogOpen() {
      this.open = true;
    },
    beforeClose(done) {
      this.$emit('update:show', false);
      this.open = false;
      if (done) {
        done();
      }
    },
    dialogClose(data) {
      this.$emit('afterClose', data);
      this.$emit('update:show', false);
      this.open = false;
    },
    dialogCancel() {
      this.$emit('update:show', false);
      this.open = false;
    }
  }
};
