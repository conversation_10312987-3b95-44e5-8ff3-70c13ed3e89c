<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-input
          v-model="queryParams.layerName"
          placeholder="请输入图层名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleSearch"
        />
        <el-select
          v-model="queryParams.layerType"
          placeholder="图层类型"
          clearable
          style="width: 150px; margin-left: 10px"
        >
          <el-option
            v-for="item in layerTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-select
          v-model="queryParams.isVisible"
          placeholder="显示状态"
          clearable
          style="width: 120px; margin-left: 10px"
        >
          <el-option label="显示" :value="true" />
          <el-option label="隐藏" :value="false" />
        </el-select>
      </div>
      <div class="search-btn">
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button v-if="authorityShow('createLayer')" type="primary" @click="handleAdd">新增</el-button>
      </div>
    </div>
    <el-card shadow="never">
      <el-table
        :data="dataList"
        v-loading="loading"
        stripe
        height="630"
        style="width: 100%; margin-top: 10px"
        row-key="id"
      >
        <el-table-column prop="layerName" label="图层名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="layerType" label="图层类型" min-width="100">
          <template #default="scope">
            {{ getLayerTypeLabel(scope.row.layerType) }}
          </template>
        </el-table-column>
        <el-table-column prop="minLevel" label="最小级别" width="100" align="center" />
        <el-table-column prop="maxLevel" label="最大级别" width="100" align="center" />
        <el-table-column prop="isVisible" label="是否显示" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.isVisible ? 'success' : 'danger'">
              {{ scope.row.isVisible ? '显示' : '隐藏' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sortOrder" label="排序" width="80" align="center" />
        <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" width="160" align="center">
          <template #default="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" v-if="authorityShow('editLayer') || authorityShow('deleteLayer')" width="200" align="center" fixed="right">
          <template #default="scope">
            <el-button v-if="authorityShow('editLayer')" type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button v-if="authorityShow('deleteLayer')" type="danger" link @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-content">
        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="handleSearch"
        />
      </div>
    </el-card>

    <!-- 新增/编辑弹窗 -->
    <LayerDialog
      v-model:visible="dialogVisible"
      :form-data="currentLayer"
      :is-edit="isEdit"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getLayerList, deleteLayer } from '@/api/map/layer';
import LayerDialog from './components/LayerDialog.vue';
import Pagination from '@/components/Pagination/index.vue';
import { authorityShow } from '@/utils/authority';

const loading = ref(false);
const dataList = ref([]);
const total = ref(0);
const dialogVisible = ref(false);
const isEdit = ref(false);
const currentLayer = ref({});

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  layerName: '',
  layerType: '',
  isVisible: null
});

// 图层类型选项
const layerTypeOptions = [
  { label: '点图层', value: 'point' },
  { label: '线图层', value: 'line' },
  { label: '面图层', value: 'polygon' },
  { label: '路网图层', value: 'roadnetwork' },
  { label: '栅格图层', value: 'tile' }
];

/**
 * 获取图层类型标签
 */
function getLayerTypeLabel(type) {
  const option = layerTypeOptions.find(item => item.value === type);
  return option ? option.label : type;
}

/**
 * 格式化日期时间
 */
function formatDateTime(dateTime) {
  if (!dateTime) return '';
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

/**
 * 字段名转换：下划线转驼峰
 */
function toCamelCase(obj) {
  if (Array.isArray(obj)) {
    return obj.map(item => toCamelCase(item));
  } else if (obj !== null && typeof obj === 'object') {
    const newObj = {};
    Object.keys(obj).forEach(key => {
      const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
      newObj[camelKey] = toCamelCase(obj[key]);
    });
    return newObj;
  }
  return obj;
}

/**
 * 字段名转换：驼峰转下划线
 */
function toSnakeCase(obj) {
  if (Array.isArray(obj)) {
    return obj.map(item => toSnakeCase(item));
  } else if (obj !== null && typeof obj === 'object') {
    const newObj = {};
    Object.keys(obj).forEach(key => {
      const snakeKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
      newObj[snakeKey] = toSnakeCase(obj[key]);
    });
    return newObj;
  }
  return obj;
}

/**
 * 查询图层列表
 */
async function handleQuery() {
  loading.value = true;
  try {
    const params = {
      page_num: queryParams.pageNum,
      page_size: queryParams.pageSize
    };

    // 添加查询条件
    if (queryParams.layerName) {
      params.layer_name = queryParams.layerName.trim();
    }
    if (queryParams.layerType) {
      params.layer_type = queryParams.layerType;
    }
    if (queryParams.isVisible !== null) {
      params.is_visible = queryParams.isVisible;
    }

    const result = await getLayerList(params);
    if (result && result.data && Array.isArray(result.data)) {
      // 转换字段名为驼峰格式
      dataList.value = toCamelCase(result.data);
      total.value = result.total || result.data.length;
    } else if (result && Array.isArray(result)) {
      // 兼容直接返回数组的情况
      dataList.value = toCamelCase(result);
      total.value = result.length;
    } else {
      dataList.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('获取图层列表失败:', error);
    ElMessage.error('获取图层列表失败');
    dataList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
}

/**
 * 搜索
 */
function handleSearch() {
  queryParams.pageNum = 1;
  handleQuery();
}

/**
 * 重置
 */
function handleReset() {
  queryParams.layerName = '';
  queryParams.layerType = '';
  queryParams.isVisible = null;
  handleSearch();
}

/**
 * 新增
 */
function handleAdd() {
  currentLayer.value = {};
  isEdit.value = false;
  dialogVisible.value = true;
}

/**
 * 编辑
 */
function handleEdit(row) {
  currentLayer.value = { ...row };
  isEdit.value = true;
  dialogVisible.value = true;
}

/**
 * 删除
 */
async function handleDelete(row) {
  try {
    await ElMessageBox.confirm(
      `确定要删除图层"${row.layerName}"吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    await deleteLayer(row.id);
    ElMessage.success('删除成功');
    handleQuery();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除图层失败:', error);
      ElMessage.error('删除失败');
    }
  }
}

/**
 * 弹窗成功回调
 */
function handleDialogSuccess() {
  dialogVisible.value = false;
  handleQuery();
}

// 初始化
onMounted(() => {
  handleQuery();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 16px 20px;
  .search {
    display: flex;
    align-items: center;
    padding: 24px;
    height: 64px;
    .search-form {
      flex: 1;
      color: #fff;
    }
    .search-btn {
      margin-left: 16px;
    }
  }
  .app-content {
    width: 100%;
    max-height: calc(100vh - 154px);
    padding: 16px 24px;
    background: #fff;
    overflow: auto;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
    border-radius: 4px;
    &::-webkit-scrollbar {
      width: 10px !important;
      height: 10px !important;
      background: #e4e7ec;
      border-radius: 4px;
    }
    &::-webkit-scrollbar-thumb {
      width: 10px !important;
      min-height: 20px !important;
      background: #b7d9fd !important;
      border-radius: 4px !important;
    }
    .btn-box {
      margin-bottom: 16px;
    }
    .textHidden {
      width: 180px;
      height: 20px;
      line-height: 20px;
      text-align: left;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
}
.pagination-content {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;

  &.hidden {
    display: none;
  }
}
</style>
