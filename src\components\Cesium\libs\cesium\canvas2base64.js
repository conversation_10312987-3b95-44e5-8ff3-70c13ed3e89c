import { Canvas2Image } from '@/utils/canvas2Image';
const { convertToBase64 } = Canvas2Image();
export const canvas2base64 = (viewer, width = 480, height = 270, type = 'jpg') => {
  if (!viewer) {
    return;
  }
  // 这一步必须要执行
  viewer.render();
  // 获取canvas元素
  // let png = convertToImage(viewer.scene.canvas, null, null, type);
  let bs64 = convertToBase64(viewer.scene.canvas, width, height);
  return bs64;
};

function saveToFile(scene) {
  let canvas = scene.canvas;
  let image = canvas.toDataURL('image/png').replace('image/png', 'image/octet-stream');
  let link = document.createElement('a');
  let blob = dataURLtoBlob(image);
  let objurl = URL.createObjectURL(blob);
  link.download = 'scene.png';
  link.href = objurl;
  link.click();
}
function dataURLtoBlob(dataurl) {
  let arr = dataurl.split(','),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], { type: mime });
}
