<script>
export default {
  name: 'user'
};
</script>

<script setup>
import { ElMessage } from 'element-plus';
import { toRaw } from 'vue';
import { listDept } from '@/api/system/dept';
import { listUser, delUser } from '@/api/system/user';
import optionData from '@/utils/option-data';
import handleTree from '@/utils/handleTree';
import EditDialog from './EditDialog.vue';
import EditDriverDialog from './EditDriverDialog.vue';
import { nextTick } from 'vue';
import { authorityShow } from '@/utils/authority';

const deptTreeRef = ref(ElTree); // 组织树
const userFormRef = ref(ElForm); // 用户表单
const resetForms = ref(ElForm); // 重置密码表单
const dataFrom = ref(ElForm); // 数据源表单
const editDialogRef = ref(null);
const editDialog = reactive({
  visible: false
});
const editDriverDialogRef = ref(null);
const editDriverDialog = reactive({
  visible: false
});
const auth = reactive({
  edit: false,
  delete: false
});

const loading = ref(false);
const total = ref(0);
const passwordVisible = ref(false); // 重置密码-显隐
const dataVisible = ref(false); // 新增数据源-显隐
const resetValue = reactive({
  userId: '',
  password: ''
}); // 重置密码-值
const dialog = reactive({
  visible: false
});
const queryParams = reactive({
  deptId: '',
  pageNo: 1,
  pageSize: 10,
  username: '',
  status: ''
});
const userList = ref();
const removeVisible = ref(false);
const removeId = ref(undefined);
let formData = reactive({});
let addFormData = reactive({});
const searchDeptName = ref('');
const deptList = ref();
const roleList = ref();
const idsWithChildren = ref([]);

watchEffect(
  () => {
    deptTreeRef.value?.filter(searchDeptName.value);
  },
  {
    flush: 'post' // watchEffect会在DOM挂载或者更新之前就会触发，此属性控制在DOM元素更新后运行
  }
);

/** 查询组织列表 */
function getDeptList(searchParam) {
  listDept(searchParam)
    .then(response => {
      deptList.value = handleTree(response, 'id');
      // 用于存储包含 children 节点的 id
      idsWithChildren.value = [];
      // 遍历整个树形结构数组
      deptList.value.forEach(node => traverseTree(node));
      nextTick(() => {
        deptTreeRef.value.setExpandedKeys(idsWithChildren.value);
      });
    })
    .catch(error => {
      deptList.value = [];
    });
}
// 递归函数来遍历树形结构
function traverseTree(node) {
  if (node.children && node.children.length > 0) {
    idsWithChildren.value.push(node.id); // 如果节点有 children，将其 id 添加到数组中
    node.children.forEach(child => traverseTree(child)); // 递归遍历子节点
  }
}
/**
 * 组织筛选
 */
function handleDeptFilter(value, data) {
  if (!value) {
    return true;
  }
  return data.name?.indexOf(value) != -1;
}

/**
 * 组织树节点
 */
function handleDeptNodeClick(data) {
  queryParams.deptId = toRaw(data).id;
  handleQuery();
}

// 删除文件夹
function deleteFolder(id) {
  ElMessageBox.confirm('删除该组织将同时解除组织与用户之间的关联关系，是否确认？', '确认删除该组织', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    autofocus: false,
    closeOnClickModal: false,
    type: 'warning'
  })
    .then(function () {
      // deleteUserTree(id)
      //   .then(() => {
      //     ElMessage.success('删除成功');
      //     getDeptOptions();
      //     resetQuery();
      //   })
      //   .catch(error => {
      //     ElMessage.error(error.message || '组织删除失败');
      //   });
    })
    .catch(error => {});
}

/**
 * 获取角色下拉列表
 */
async function getRoleOptions() {}

/**
 * 修改成员状态
 */
function handleStatusChange(row) {
  const data = toRaw(row);
}

function handleSizeChange(value) {
  queryParams.pageSize = value;
  handleQuery();
}

function handleCurrentChange(value) {
  queryParams.pageNo = value;
  handleQuery();
}
function handleSearch() {
  queryParams.username = queryParams.username.trim();
  queryParams.pageNo = 1;
  handleQuery();
}
/**
 * 查询
 */
function handleQuery() {
  // Calculate the new total pages based on the new page size
  const newTotalPages = Math.ceil(total.value / queryParams.pageSize);
  if (queryParams.pageNo > newTotalPages) {
    queryParams.pageNo = newTotalPages || 1;
  }
  const params = {
    ...queryParams,
    startDateTime: queryParams.rangTime?.length === 2 ? queryParams.rangTime[0] : '',
    endDateTime: queryParams.rangTime?.length === 2 ? queryParams.rangTime[1] : ''
  };
  delete params.rangTime;
  loading.value = true;
  listUser(queryParams)
    .then(data => {
      userList.value = data.list || [];
      total.value = data.total;
      loading.value = false;
    })
    .catch(error => {
      userList.value = [];
      loading.value = false;
      console.error('There was a problem with the fetch operation:', error); // 捕获处理失败的错误
    });
}

function closeDriver() {
  handleQuery();
}

/**
 * 重置查询
 */
function resetQuery() {
  queryParams.pageNo = 1;
  queryParams.pageSize = 10;
  queryParams.username = '';
  queryParams.status = '';
  handleQuery();
}

/**
 * 重置密码
 */
function resetPassword(row) {
  passwordVisible.value = true;
  resetValue.userId = toRaw(row).userId;
  randerPassword();
}

//打开用户信息弹窗
function openEditDialog(row) {
  editDialog.visible = true;
  Object.keys(formData).map(key => {
    delete formData[key];
  });
  if (row) {
    editDialog.title = '编辑用户';
    Object.assign(formData, { ...row });
  } else {
    editDialog.title = '新增用户';
    formData.value = {};
  }
  // //新增的时候清空表单验证和表单数据
  if (!row) {
    // editDialogRef.value.resetForm();
  }
}

//打开驾驶证弹窗
function openDriverDialog(row) {
  editDriverDialog.visible = true;
  Object.keys(addFormData).map(key => {
    delete addFormData[key];
  });
  Object.assign(addFormData, { ...row });
  editDriverDialog.title = '驾驶证列表';
}

function closeReset() {
  passwordVisible.value = false;
  resetForms.value.resetFields();
  resetForms.value.clearValidate();
}

// 关闭重置按钮弹框
function closePassword() {
  passwordVisible.value = false;
}

const randerPassword = () => {};

/**
 * 删除用户
 */
function handleDelete(row) {
  ElMessageBox.confirm(`删除用户将同时删除用户相关的飞行记录、维保任务等信息。是否确认删除！`, '确认删除所选用户？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    autofocus: false,
    closeOnClickModal: false,
    type: 'warning'
  }).then(() => {
    delUser(row.id).then(data => {
      ElMessage.success('删除成功');
      handleQuery();
    });
  });
}
function changeUserList(value) {
  searchDeptName.value = value;
  getDeptList({ name: value });
}

onMounted(() => {
  getDeptList(null);
  handleQuery();
});
</script>

<template>
  <div class="app-container column-container">
    <div class="app-aside">
      <div class="app-aside-container">
        <el-input v-model="searchDeptName" placeholder="搜索组织名称" clearable @input="changeUserList">
          <template #prefix>
            <i-ep-search class="base-search-icon" />
          </template>
        </el-input>
        <div class="app-aside-content common-tree tree-limit">
          <el-tree-V2
            class="mt-2"
            ref="deptTreeRef"
            :data="deptList"
            :height="715"
            :current-node-key="queryParams.deptId"
            :props="{
              children: 'children',
              label: 'name',
              value: 'id',
              class: 'user-treeNode'
            }"
            :expand-on-click-node="false"
            :filter-node-method="handleDeptFilter"
            default-expand-all
            @node-click="handleDeptNodeClick"
          >
            <template #default="{ data }">
              <div class="common-tree-title user-tree-title">
                <el-tooltip class="box-item" effect="dark" :content="data.name" placement="top">
                  <div class="flex-1 text-overflow-ellipse">
                    {{ data.name }}
                  </div>
                </el-tooltip>
              </div>
            </template>
          </el-tree-V2>
        </div>
      </div>
    </div>
    <div class="app-main-content">
      <div class="search">
        <div class="search-form">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="" prop="username">
              <el-input
                class="input-serach"
                v-model="queryParams.username"
                placeholder="请输入用户名称或账号名称"
                clearable
                @keyup.enter="handleSearch"
                maxlength="32"
                @blur="queryParams.username = $event.target.value.trim()"
              />
            </el-form-item>
            <el-form-item label="" prop="status">
              <el-select
                class="input-serach"
                v-model="queryParams.status"
                placeholder="请选择状态"
                clearable
                @change="handleSearch"
              >
                <el-option
                  v-for="(item, index) in optionData.deptStatusOption"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div class="search-btn">
          <el-button type="primary" @click="handleSearch()"><i-ep-search />搜索</el-button>
          <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
        </div>
      </div>
      <div class="app-content">
        <div class="btn-box" v-if="authorityShow('createUser')">
          <el-button type="primary" @click="openEditDialog()">
            <i-ep-plus class="mr-[8px]" />
            新增用户
          </el-button>
        </div>
        <div>
          <el-table
            :data="userList"
            v-loading="loading"
            header-cell-class-name="default-table-header-cell"
            table-layout="auto"
            stripe
            height="540"
          >
            <el-table-column label="序号" width="80">
              <template #default="scope">
                <span>{{ scope.$index + 1 + queryParams.pageSize * (queryParams.pageNo - 1) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="用户名称" prop="nickname" show-overflow-tooltip />
            <el-table-column label="账号名称" prop="username" show-overflow-tooltip />
            <el-table-column label="所属组织" prop="dept_name" show-overflow-tooltip />
            <el-table-column label="手机号" prop="mobile" show-overflow-tooltip>
              <template #default="scope">
                <span>{{ scope.row.mobile || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="驾驶证" prop="driving_type_list" show-overflow-tooltip>
              <template #default="scope">
                <span>{{ scope.row.driving_type_list ? scope.row.driving_type_list.join('、') : '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" show-overflow-tooltip>
              <template #default="scope">
                <span>{{ optionData.deptStatusOption.find(item => item.value == scope.row.status).label || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="pilot" label="是否飞手" show-overflow-tooltip>
              <template #default="scope">
                <span>{{ optionData.isPilotOption.find(item => item.value == scope.row.pilot).label || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              min-width="220px"
              fixed="right"
              v-if="authorityShow('editUser') || authorityShow('manageDrive') || authorityShow('deleteUser')"
            >
              <template #default="scope">
                <el-button type="primary" link @click="openEditDialog(scope.row)" v-if="authorityShow('editUser')">
                  编辑
                </el-button>
                <!-- <el-button  type="primary" link @click="resetPassword(scope.row)"
                  >重置密码</el-button
                > -->
                <el-button type="primary" link @click="openDriverDialog(scope.row)" v-if="authorityShow('manageDrive')">
                  驾驶证管理
                </el-button>
                <el-button
                  :type="!scope.row.enabled ? 'danger' : ''"
                  link
                  @click.stop="handleDelete(scope.row)"
                  v-if="authorityShow('deleteUser')"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.pageNo"
            v-model:limit="queryParams.pageSize"
            @pagination="handleQuery"
          />
        </div>
      </div>
    </div>

    <!-- 重置密码弹窗 -->
    <el-dialog
      title="重置密码"
      v-if="passwordVisible"
      v-model="passwordVisible"
      width="800px"
      append-to-body
      @close="closePassword"
      class="common-dialog"
      :close-on-click-modal="false"
    >
      <template #header>
        <div class="common-title">重置密码</div>
      </template>
      <el-form style="width: 100%" ref="resetForms" :model="resetValue" label-width="80px" label-position="top">
        <div class="common-title-text new-password">新密码</div>
        <div class="reset-from pt-[16px]">
          <el-form-item prop="username" style="width: 296px">
            <el-input disabled="true" v-model="resetValue.password" placeholder="请输入" />
          </el-form-item>
          <el-button type="primary" @click="randerPassword" class="reset-btn"><i-ep-refresh-left />随机生成</el-button>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="copyAndSave"
            ><svg-icon icon-class="save" color="#fff" />保存并复制</el-button
          >
          <el-button @click="closeReset">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <EditDialog
      ref="editDialogRef"
      v-model:visible="editDialog.visible"
      :title="editDialog.title"
      :form-data="formData"
      @submit="closeDriver"
    />
    <EditDriverDialog
      ref="editDriverDialogRef"
      v-model:visible="editDriverDialog.visible"
      :title="editDriverDialog.title"
      :add-form-data="addFormData"
      @driveSubmit="closeDriver"
    />
  </div>
</template>

<style lang="scss" scoped>
.role-name {
  font-size: 14px;
  color: #2a8b7d;
  text-align: left;
  line-height: 22px;
  font-weight: 400;
}

.user-tree-title {
  padding-right: 88px;
  .user-tree-icon {
    display: none;
  }
  &:hover {
    .user-tree-icon {
      display: block;
    }
  }
}
.tree-limit {
  :deep(.el-tree) {
    .el-tree-node.is-current > .el-tree-node__content {
      .user-tree-title {
        color: var(--el-color-primary) !important;
      }
    }
    .el-tree-node__content {
      max-width: 100%;
      height: 40px;
      line-height: 40px;
      overflow: hidden;
    }
  }
}
.app-container {
  padding: 16px 20px;
  .search {
    padding: 18px 0 0 10px;
    margin-bottom: 10px;
    border-radius: 4px;
    border: 1px solid var(--el-border-color-light);
    box-shadow: var(--el-box-shadow-light);
    background-color: var(--el-bg-color-overlay);
  }
  &.column-container {
    display: flex;
    flex-direction: row;
    width: 100%;
    height: calc(100vh - 82px);
    .app-aside {
      width: 19.2%;
      height: 100%;
      padding: 16px 20px;
      padding-bottom: 0;
      background: #11253e;
      box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
      border-radius: 4px;
      .app-aside-container {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        padding: 0 8px 20px 8px;
        .app-aside-content {
          //flex: 1;
          width: 100%;
          height: 100%;
          // overflow: auto;
          &::-webkit-scrollbar {
            width: 10px !important;
            height: 10px !important;
            background: #e4e7ec;
            border-radius: 4px;
          }
          &::-webkit-scrollbar-thumb {
            width: 10px !important;
            min-height: 20px !important;
            background: #b7d9fd !important;
            border-radius: 4px !important;
          }
        }
      }
    }
    .app-main-content {
      flex: 1;
      width: 0;
      margin-left: 16px;
    }
  }
  .custom-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 48px;
    line-height: 48px;
    margin-bottom: 16px;
    background: #fff;
    vertical-align: middle;
  }

  .custom-preview {
    width: 100%;
    height: 435px;
    overflow-y: hidden;
    padding: 16px 0;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
  }
  .custom-search-box {
    width: 100%;
    padding: 16px 24px;
    margin-bottom: 16px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
    position: relative;
    .custom-search {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      background: #fff;
      border-radius: 4px;
      box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
      margin-bottom: 12px;
      .custom-screen-search {
        position: absolute;
        right: 0;
        top: 0;
      }
      .search-content {
        display: flex;
        flex-direction: row;
        align-items: center;
      }
      .custom-library {
        width: 31%;
        margin-right: 8px;
      }
      .search-param {
        display: flex;
        align-items: center;
        white-space: nowrap;
        font-size: 14px;
        color: #344054;
        line-height: 22px;
        font-weight: 400;
        margin-right: 5px;
      }
      + .app-content {
        max-height: calc(100vh - 202px);
      }
    }
    .custom-content {
      width: 100%;
      max-height: calc(100vh - 154px);
      padding: 16px 24px;
      background: #fff;
      overflow: auto;
      box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
      border-radius: 4px;
      &::-webkit-scrollbar {
        width: 6px !important;
        height: 6px !important;
      }
      &::-webkit-scrollbar-thumb {
        width: 6px !important;
        min-height: 40px !important;
        background: hsla(0, 0%, 76.9%, 0.7) !important;
        border-radius: 62px !important;
      }
      .btn-box {
        margin-bottom: 16px;
      }
      .textHidden {
        width: 180px;
        height: 20px;
        line-height: 20px;
        text-align: left;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
  .search-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 16px 16px 16px 8px;
    margin-bottom: 16px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
    .search-content {
      display: flex;
      flex-direction: row;
      align-items: center;
    }
    .search-param {
      display: flex;
      align-items: center;
      white-space: nowrap;
      font-size: 14px;
      color: #475467;
      line-height: 22px;
      font-weight: 400;
      min-width: 56px;
      margin-left: 8px;
      margin-right: 8px;
    }
    + .app-content {
      max-height: calc(100vh - 202px);
    }
  }
  .app-content {
    width: 100%;
    max-height: calc(100vh - 154px);
    height: 720px;
    background-color: #11253e;
    padding: 16px;
    overflow: auto;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
    border-radius: 4px;
    &::-webkit-scrollbar {
      width: 10px !important;
      height: 10px !important;
      background: #e4e7ec;
      border-radius: 4px;
    }
    &::-webkit-scrollbar-thumb {
      width: 10px !important;
      min-height: 20px !important;
      background: #b7d9fd !important;
      border-radius: 4px !important;
    }
    .btn-box {
      margin-bottom: 16px;
    }
    .textHidden {
      width: 180px;
      height: 20px;
      line-height: 20px;
      text-align: left;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
  .page-title {
    font-size: 16px;
    color: #101828;
    text-align: left;
    line-height: 16px;
    font-weight: 600;
  }
  .input-serach {
    width: 200px;
  }
  .flex-center {
    display: flex;
    align-items: center;
    .status {
      margin-right: 4px;
      width: 6px;
      height: 6px;
      border-radius: 50%;
    }
  }
  .search {
    display: flex;
    align-items: center;
    padding: 0 24px;
    .search-form {
      padding-top: 16px;
      flex: 1;
    }
  }
}
</style>
