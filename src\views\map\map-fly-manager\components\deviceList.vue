<template>
  <!-- 设备列表 -->
  <div class="deviceContainer">
    <el-collapse v-model="activeName" accordion>
      <el-collapse-item v-for="(item, index) in deviceDataList" :key="index" :title="getItemTitle(item)">
        <el-card style="max-width: 400px">
          <el-form label-width="60px" :disabled="true">
            <el-form-item label="机场">
              <el-col :span="20">{{ item.device_name }}</el-col>
              <el-col :span="4">
                <span
                  title="点击查看机场相关信息"
                  class="ff-cloud-icon clound-open-eye right-aligned"
                  @click="showOSD(item)"
                />
              </el-col>
            </el-form-item>
            <el-form-item
              label="状态"
              :style="
                deviceState.dock[item.device_sn] === undefined ||
                deviceState.dock[item.device_sn].basic_osd?.mode_code === EDockModeCode.离线
                  ? 'color: red;'
                  : 'color: rgb(25,190,107)'
              "
            >
              {{
                deviceState.dock[item.device_sn] === undefined
                  ? 'Disconnected'
                  : getEnumKey(EDockModeCode, deviceState.dock[item.device_sn]?.basic_osd?.mode_code)
              }}
            </el-form-item>
            <el-form-item label="SN">
              {{ item.device_sn }}
            </el-form-item>
            <el-form-item label="无人机">
              <el-col :span="8">{{ item.children.device_name }}</el-col>
              <el-col :span="8"> 
                <el-dropdown @command="handleCommand" style="display: block">
                  <div style="display: flex; align-items: center">
                    {{ currentSwitchType }}<el-icon class="el-icon--right"><arrow-down /></el-icon>
                  </div>

                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        v-for="item in optionData.switchVideoTypes"
                        :key="item.value"
                        :command="item.value"
                        >{{ item.label }}</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-col>

              <el-col :span="8">
                <span style="color: blue; cursor: pointer" @click="showLiveStream(item)">直播</span>
              </el-col>
            </el-form-item>
            <!-- {{ deviceState.device[item.children.device_sn] }} -->
            <el-form-item
              label="状态"
              :style="
                deviceState.device[item.children.device_sn] === undefined ||
                deviceState.device[item.children.device_sn]?.mode_code === EModeCode.离线
                  ? 'color: red;'
                  : 'color: rgb(25,190,107)'
              "
            >
              {{
                deviceState.device[item.children.device_sn] === undefined
                  ? '离线'
                  : getEnumKey(EModeCode, deviceState.device[item.children.device_sn]?.mode_code)
              }}
            </el-form-item>
            <el-form-item label="SN">
              {{ item.children.device_sn }}
            </el-form-item>
          </el-form>
        </el-card>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>
<script>
export default {
  name: 'deviceList'
};
</script>
<script setup>
import { onMounted, onUnmounted, onBeforeUnmount, reactive } from 'vue';
import { EModeCode, EDockModeCode } from './osdInfo';
import { useConnectWebSocket } from '@/hooks/useConnectWebSocket';
import { EBizCode, DOMAIN } from '@/utils/constants';
import { getDevicesBound } from '@/api/devices';
import { changeLivestreamLens } from '@/api/live';
import optionData from '@/utils/option-data';
import { useDeviceStateStore } from '@/store/modules/deviceState.js';
import { ArrowDown } from '@element-plus/icons-vue';
const deviceStateStore = useDeviceStateStore();
const activeName = ref('1');
const deviceDataList = ref([]);
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  queryType: 2,
  keyWord: ''
});

// 多设备列表绑定
const deviceState = deviceStateStore.deviceState;

// 当前展示的设备信息
const nowOsdVisible = deviceStateStore.nowOsdVisible;
// 当前展示无人机信息
const nowUavVisible = deviceStateStore.nowUavVisible;

useConnectWebSocket(payload => {
  if (!payload) {
    return;
  }
  switch (payload.biz_code) {
    // 机场信息更新
    case EBizCode.DockOsd: {
      const info = payload.data;
      deviceStateStore.setCurrentDock(info);
      console.log('----dock_osd', payload);
      break;
    }
    // 遥控器信息更新
    case EBizCode.GatewayOsd: {
      console.log('----gateway_osd', payload);
      break;
    }
    // 飞机信息更新
    case EBizCode.DeviceOsd: {
      const info = payload.data;
      deviceStateStore.setCurrentNav(info);
      console.log('----nav_osd', payload);
      break;
    }
  }
});
// 枚举获取值
const getEnumKey = (enumObject, value) => {
  return Object.keys(enumObject).find(key => enumObject[key] === value);
};

/**
 * 每组绑定的机场设备
 */
function getItemTitle(item) {
  return `${item.nickname} - ${item.children.nickname}`;
}

/**
 * 查看关闭OSD实时面板信息
 */
function showOSD(item) {
  const osdinfo = {};
  osdinfo.visible = !nowOsdVisible.visible;
  osdinfo.dock_sn = item?.device_sn;
  osdinfo.device_sn = item?.children.device_sn;
  osdinfo.dock_callsign = item?.nickname;
  osdinfo.device_callsign = item?.children.nickname;
  deviceStateStore.setNowOsdVisible(osdinfo);
}

const uavInfo = {
  droneSelected: '1581F6Q8D236L0010113',
  cameraSelected: '81-0-0',
  cameraName: 'M3TD Camera',
  cameraId: '40495644-ad03-4a3c-8941-cb62fdafea2c',
  videoSelected: 'normal-0',
  claritySelected: 0,
  lensSelected: 'normal',
  isDockLive: true
};
/**
 * 查看大疆无人机直播
 */
function showLiveStream(item) {
  //test
  const dockInfo = {
    droneSelected: '6QCDL5L0000086',
    cameraSelected: '165-0-7',
    cameraName: 'DJI Dock Camera',
    cameraId: '494e33c7-6821-497e-bd34-206115de3477',
    videoSelected: 'normal-0',
    claritySelected: 0,
    lensSelected: 'normal',
    isDockLive: true
  };

  uavInfo.visible = !nowUavVisible.visible;
  uavInfo.cameraName = uavInfo.cameraName;
  uavInfo.droneSelected = uavInfo.droneSelected;
  uavInfo.cameraSelected = uavInfo.cameraSelected;
  uavInfo.videoSelected = uavInfo.videoSelected;
  uavInfo.claritySelected = uavInfo.claritySelected;
  uavInfo.cameraId = uavInfo.cameraId;
  deviceStateStore.setUavVisible(uavInfo);
}
const currentSwitchType = ref( optionData.switchVideoTypes[0].label);
/***
 * 切换无人机直播镜头
 * */
function handleCommand(command) {
  const currentObj = optionData.switchVideoTypes.find(item => item.value === command);

  if (currentObj) {
    currentSwitchType.value = currentObj.label;
    const nonSwitchable = 'normal';

    const videoId =
      uavInfo.droneSelected + '/' + uavInfo.cameraSelected + '/' + (uavInfo.videoSelected || nonSwitchable + '-0');

    changeLivestreamLens({
      video_id: videoId,
      video_type: command
    }).then(() => {
      ElMessage.success('切换镜头成功');
    });
  }
}
/**
 * 查询机场以及附属无人机
 */
function handleQuery() {
  getDevicesBound({
    domain: DOMAIN.DOCK,
    page: queryParams.pageNum,
    page_size: queryParams.pageSize
  }).then(data => {
    const { list, pagination } = data;
    deviceDataList.value = list || [];
    // total.value = pagination.total;
  });
}

onMounted(() => {
  window.$bus.on('closeVideoView', () => {
    uavInfo.visible = false;
    deviceStateStore.setUavVisible(uavInfo);
  });

  handleQuery();
});
onUnmounted(() => {
  window.$bus.off('closeVideoView');
});
onBeforeUnmount(() => {});
</script>

<style lang="scss" scoped>
.deviceContainer {
  // height: 400px;
  width: 260px;
  position: absolute;
  margin-left: 10px;
  left: 0;
  top: 10px;
  // background: #fff;
  // color: #000;
  border-radius: 5px;
}
.deviceCard-header {
  display: flex;
  font-weight: bold;
}
::v-deep .el-collapse-item__header {
  font-size: 18px;
  color: #333;
  background-color: #f2f6fc;
  padding: 12px 20px;
  cursor: pointer;
}
::v-deep .el-collapse-item__content {
  padding-bottom: 0px !important;
}

.right-aligned {
  // margin-left: auto;
  color: #0a7dfc;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
}
</style>
