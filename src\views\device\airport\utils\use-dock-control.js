import { ref } from 'vue'
import { postSendCmd } from '@/api/devices'
import { DeviceCmd } from '@/types/device-cmd'

export function useDockControl () {
  const dockControlPanelVisible = ref(false)

  function setDockControlPanelVisible (visible) {
    dockControlPanelVisible.value = visible
  }

  // 远程调试开关
  async function dockDebugOnOff (sn, on) {
    const result = await sendDockControlCmd({
      sn: sn,
      cmd: on ? DeviceCmd.DebugModeOpen : DeviceCmd.DebugModeClose
    }, false)
    return result
  }

  // 发送指令
  async function sendDockControlCmd (params, tip = true) {
    try {
      let body = undefined 
      if (params.action !== undefined) {
        body = {
          action: params.action
        }
      }
      await postSendCmd({ dock_sn: params.sn, device_cmd: params.cmd }, body)
      tip && ElMessage.success('指令发送成功')
      return true
      throw (msg)
    } catch (e) {
      tip && ElMessage.error('指令发送失败')
      return false
    }
  }

  // 控制面板关闭
  async function onCloseControlPanel (sn, debugging) {
    if (debugging) {
      await dockDebugOnOff(sn, false)
    }
    setDockControlPanelVisible(false)
  }

  return {
    dockControlPanelVisible,
    setDockControlPanelVisible,
    sendDockControlCmd,
    dockDebugOnOff,
    onCloseControlPanel,
  }
}
