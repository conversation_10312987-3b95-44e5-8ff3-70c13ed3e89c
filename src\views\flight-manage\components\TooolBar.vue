<template>
  <div class="tool-bar-wrap">
    <div class="tool-bar-right">
      <el-tooltip popper-class="tooltips" content="圆形作业区" raw-content="true" placement="left">
        <div class="item" @click="onClickHandle(GEOMTYPE.CIRCLE, FLIGHTAREATYPE.DFENCE)">
          <img class="svgStyle" style="color: aqua" :src="iconSource.circle" alt="圆形作业区" />
        </div>
      </el-tooltip>
      <el-tooltip popper-class="tooltips" content="多边形作业区" raw-content="true" placement="left">
        <div class="item" @click="onClickHandle(GEOMTYPE.POLYGON, FLIGHTAREATYPE.DFENCE)">
          <img class="svgStyle" :src="iconSource.polygon" alt="多边形作业区" />
        </div>
      </el-tooltip>
      <el-tooltip popper-class="tooltips" content="圆形限飞区" raw-content="true" placement="left">
        <div class="item" @click="onClickHandle(GEOMTYPE.CIRCLE, FLIGHTAREATYPE.NFZ)">
          <img class="svgStyle" :src="iconSource.circle_limit" alt="圆形限飞区" />
        </div>
      </el-tooltip>
      <el-tooltip popper-class="tooltips" content="多边形限飞区" raw-content="true" placement="left">
        <div class="item" @click="onClickHandle(GEOMTYPE.POLYGON, FLIGHTAREATYPE.NFZ)">
          <img class="svgStyle" :src="iconSource.polygon_limit" alt="多边形限飞区" />
        </div>
      </el-tooltip>
    </div>
  </div>
</template>
<script>
export default { name: 'AreaItem' };
</script>
<script setup>
import { ElMessage } from 'element-plus';
import 'element-plus/dist/index.css';
import { Delete, MapLocation } from '@element-plus/icons-vue';
import { defineProps, computed, defineEmits, onMounted, onUnmounted } from 'vue';
import {
  iconSource,
  curDarwPolygonDataRect,
  GEOMTYPE,
  viewer,
  drawHandle,
  FLIGHTAREATYPE,
  COLORTYPE,
  dialogDataRect,
  editStatue
} from '../../flight-manage/flight-area/flightAreaHandle';

import {
  cancelCircleEdit,
  isCircleEdit,
  editCircle,
  closeCircleEdit
} from '@/components/Cesium/libs/cesium/superEdit/CircleEditer';
import { drawCircle, isCircleCreate, circleDispose } from '@/components/Cesium/libs/cesium/superEdit/CircleCreater';
import {
  editPolygon,
  cancelPolygonEdit,
  isPolygonEdit,
  closePolygonEdit
} from '@/components/Cesium/libs/cesium/superEdit/PolygonEditer';
import { drawPolygon, isPolygonCreate, polygonDispose } from '@/components/Cesium/libs/cesium/superEdit/PolygonCreater';
const value1 = ref(true);
const isShowDialog = ref(true);
let drawing = null;
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
const data = computed({
  get: () => props.modelValue,
  set: value => {
    emits('update:modelValue', value);
  }
});
const emits = defineEmits(['update:modelValue', 'onDeleteHandle', 'onZoomToAreaHandle', 'onSetAreaVisibleHandle']);

//#region

//#endregion

//#region 方法

const onClickHandle = (geomType, flightAreaType) => {
  if (!geomType || !flightAreaType) {
    return;
  }

  if (dialogDataRect.visible) {
    ElMessage.warning('请先完成当前编辑');
    return;
  }

  curDarwPolygonDataRect.geomType = geomType;
  curDarwPolygonDataRect.flightAreaType = flightAreaType;
  closeCircleEdit();
  closePolygonEdit();
  polygonDispose();
  circleDispose();
  drawHandle(
    {
      geomType: curDarwPolygonDataRect.geomType,
      flightAreaType: curDarwPolygonDataRect.flightAreaType,
      color:
        curDarwPolygonDataRect.flightAreaType === FLIGHTAREATYPE.DFENCE
          ? COLORTYPE[FLIGHTAREATYPE.DFENCE]
          : COLORTYPE[FLIGHTAREATYPE.NFZ],
      title: '',
      id: ''
    },
    data => {
      console.log('33331', data);
    }
  );
};

//#endregion

onMounted(() => {});

onUnmounted(() => {});
</script>
<style lang="scss">
.tool-bar-wrap {
  position: relative;
}

.tool-bar-left {
  position: absolute;
  top: 0px;
  right: 40px;
  z-index: 0;
  background: #c8cfa5;
  .condition-dialog {
    width: 200px;
    height: 320px;
  }
  .title {
    width: 200px;
    padding: 2px 8px;
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;
    color: #ffffff;
    text-align: justify;
    line-height: 22px;
    font-weight: 400;
    background-color: #11253e;
  }
  .content {
    width: 200px;
    height: 120px;
  }
}
.tool-bar-right {
  position: absolute;
  top: 0px;
  right: 0px;
  width: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #11253e;
}
.item {
  width: 35px;
  height: 35px;
  padding: 6px 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #4c5562;
  background-color: #11253e;
}
.item.is-selected {
  border-radius: 2px;
  background-color: #4c5562 !important;
}

// 设置 item border 的底线样式 最后一个不显示
.tooltips {
  background: #11253e !important;
  border: #11253e !important;
  color: #ffffff !important;
}
// 三角形
.tooltips.el-popper .el-popper__arrow::before {
  background: #11253e !important;
  background-color: #11253e !important;
  border: #11253e !important;
}

.item:last-child {
  border-bottom: 1px solid #4c556200;
}
.svgStyle {
  width: 20px;
  height: 20px;
  cursor: pointer;
  fill: blue;
  // filter: hue-rotate(240deg) saturate(100%) brightness(120%);
}
</style>
