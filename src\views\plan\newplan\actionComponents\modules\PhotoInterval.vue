<template>
  <div class="camera-wrapper">
    <Numbers v-model="numDataRect" @changeHandle="onChangeHandle" />
    <camera-type-select v-model="cameraSelectDataRect" @cameraTypeChange="onCameraTypeChangeHandle" />
  </div>
</template>
<script>
export default {
  name: 'PhotoInterval'
};
</script>
<script setup>
import { onMounted, onUnmounted, defineExpose, reactive, ref } from 'vue';
import Numbers from './/components/Numbers.vue';
import CameraTypeSelect from './components/CameraTypeSelect.vue';
import 'element-plus/dist/index.css';
import { useEditTrackerStore } from '@/store/modules/dataEditTracker.js';
const editTrackerStore = useEditTrackerStore();

// 组件要使用以下方式导出后 才可以被其他组件识别
//#region 初始化
const deviceType = ref('Matrice_3TD');
// 记录当前航线信息
let curDeviceInfo = null;
// 记录当前的动作对象数据 动作组对象
let curActionGroup = null;
let curAction = null;
// 设置数字组件数据
const numDataRect = reactive({
  title: '间隔时间',
  unit: 's',
  acttionType: '',
  min: 1,
  max: 30,
  value: 3 // 默认最小单位值
});
const cameraSelectDataRect = reactive({
  deviceType: deviceType.value,
  wpml_payloadLensIndex: 'visible,ir', // 默认传入是 ”aaa,bbb,ccc“，
  wpml_useGlobalPayloadLensIndex: 1 // 跟随航线 1 自定义为 0
});
const param = reactive({
  wpml_fileSuffix: '',
  wpml_payloadLensIndex: 'visible,ir', // 默认传入是 ”aaa,bbb,ccc“，
  wpml_useGlobalPayloadLensIndex: 1, // 跟随航线 1 自定义为 0
  wpml_payloadPositionIndex: 0 // 暂时默认为0 一般不在组件内变更该值
});

const config = {
  multipleTiming: {
    min: 1,
    max: 30,
    unit: 's',
    title: '间隔时间拍照',
    value: 3 // 默认3
  },
  multipleDistance: {
    min: 1,
    max: 100,
    unit: 'm',
    title: '间隔距离拍照',
    value: 10 // 默认10
  }
};

//#endregion

//#region 对外暴露方法
/**
 * 设置组件数据
 * @param {*} options
 * @param {*} options.actionFuncParam // 动作参数
 * @param {*} options.deviceInfo // 设备信息
 * @param {*} options.action // 动作对象
 */
const setComponentData = options => {
  console.log('setComponentData', options);
  // 设置数据前先初始组件数据及界面
  const { actionFuncParam = null, action = null, actionGroup = null, deviceInfo = null } = options;
  //  获取设备信息
  const { droneSubEnumLabel } = deviceInfo;
  // 设备信息及型号
  curDeviceInfo = deviceInfo;
  curAction = action;
  curActionGroup = actionGroup;

  deviceType.value = droneSubEnumLabel;
  const {
    wpml_fileSuffix = '',
    wpml_payloadLensIndex = '',
    wpml_payloadPositionIndex = 0,
    wpml_useGlobalPayloadLensIndex = 1
  } = actionFuncParam;

  // 设置当前的组件数据
  param.wpml_fileSuffix = wpml_fileSuffix;
  param.wpml_payloadLensIndex = wpml_payloadLensIndex;
  param.wpml_payloadPositionIndex = wpml_payloadPositionIndex;
  param.wpml_useGlobalPayloadLensIndex = wpml_useGlobalPayloadLensIndex;
  cameraSelectDataRect.wpml_payloadLensIndex = wpml_payloadLensIndex;
  cameraSelectDataRect.wpml_useGlobalPayloadLensIndex = wpml_useGlobalPayloadLensIndex;
  if (!actionGroup) {
    return;
  }
  // 设置数字对象
  const actionTriggerType = curActionGroup.wpml_actionTrigger.wpml_actionTriggerType;
  numDataRect.value = curActionGroup.wpml_actionTrigger.wpml_actionTriggerParam;
  numDataRect.min = config[actionTriggerType].min || 1;
  numDataRect.max = config[actionTriggerType].max;
  numDataRect.unit = config[actionTriggerType].unit;
  numDataRect.title = config[actionTriggerType].title;
};

/**
 * 获取组件数据
 * return {Object} options
 * options.wpml_fileSuffix // 导出字符串 ""
 * options.wpml_payloadLensIndex // 导出字符串用逗号隔开 "aaa,bbb,ccc"
 */
const getComponentData = () => {
  return {};
};

//#endregion

//#region 方法
// 数据变更后进行修改
const onChangeHandle = v => {
  // 获取动作中的参数进行涉资
  if (curActionGroup) {
    curActionGroup.wpml_actionTrigger.wpml_actionTriggerParam = v;
    editTrackerStore.dataTracker.markAsModified();
  }
};

const onCameraTypeChangeHandle = () => {
  param.wpml_payloadLensIndex = cameraSelectDataRect.wpml_payloadLensIndex;
  param.wpml_useGlobalPayloadLensIndex = cameraSelectDataRect.wpml_useGlobalPayloadLensIndex;
  setActionFuncParms();
};

// 取消选中跟随航点 设置其他类型
const setActionFuncParms = () => {
  if (curAction) {
    curAction.wpml_actionActuatorFuncParam.wpml_fileSuffix = param.wpml_fileSuffix;
    curAction.wpml_actionActuatorFuncParam.wpml_payloadLensIndex = param.wpml_payloadLensIndex;
    curAction.wpml_actionActuatorFuncParam.wpml_useGlobalPayloadLensIndex = param.wpml_useGlobalPayloadLensIndex;
    curAction.wpml_actionActuatorFuncParam.wpml_payloadPositionIndex = param.wpml_payloadPositionIndex;
    editTrackerStore.dataTracker.markAsModified();
  }
};

//#endregion

//#region 对外抛出方法
defineExpose({
  setComponentData,
  getComponentData
});
//#endregion

//#region 生命周期
onMounted(() => {});
onUnmounted(() => {});
//#endregion
</script>
<style lang="scss" scoped>
::v-deep.el-input .el-input__inner {
  background-color: #313131;
  color: white;
  font-size: 16px;
  width: 130px;
  height: 36px;
  line-height: 36px;
}
::v-deep .el-input.is-disabled .el-input__wrapper {
  background-color: #313131;
}
::v-deep.el-input .el-input__wrapper {
  background-color: #313131;
}

.camera-wrapper {
  position: absolute;
  width: 100%;
  background-color: #11253e !important;
  color: white;
  // padding: 5px 5px;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 27px;
    margin-top: 2px;
    .edit {
      display: flex;
      align-items: center;
    }
  }
  .camera-select {
    // padding: 10px;
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }
    .right {
      width: 80px;
      height: 30px;
      display: flex;
      align-items: center;
    }
  }
  .header-input {
    margin: 5px 0px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .btn {
      display: flex;
      align-items: center;
      margin-left: 10px;
    }
  }
}

.round {
  border: 5px;
  color: white;
}
.item {
  width: auto;
  padding: 2px 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  background: rgba(45, 140, 240, 0.35);
  color: #ffffff40;
  margin-left: 5px;
  user-select: none;
  &:hover {
    cursor: pointer;
  }
}
.active {
  color: #fff;
  background: #2d8cf0;
}
.notAllowed {
  cursor: not-allowed !important;
}

.color-blue {
  background: #2d8cf0;
  color: white;
}
</style>
