import { defineStore } from 'pinia';
import DataTracker from '@/views/plan/libs/DataTracker';
export const usePlanInfoStore = defineStore('planInfo', () => {
  const curPlanData = ref(null);
  const curPlanJobType = ref(0); // job_type ：工作类型（0：普通任务，1：接处警任务）
  const curDroneData = ref(null);
  const dataTracker = new DataTracker();
  function setCurPlanData(data) {
    curPlanData.value = data;
  }

  function getCurPlanData() {
    return curPlanData.value;
  }

  function setCurDroneInfo(data) {
    curDroneData.value = data;
  }

  function getCurDroneInfo() {
    return curDroneData.value;
  }
  function getCurPlanJobType() {
    return curPlanJobType.value;
  }
  function setCurPlanJobType(v = 0) {
    curPlanJobType.value = v;
  }
  return {
    curPlanData,
    setCurPlanData,
    getCurPlanData,
    setCurDroneInfo,
    getCurDroneInfo,
    dataTracker,
    curPlanJobType,
    getCurPlanJobType,
    setCurPlanJobType
  };
});
