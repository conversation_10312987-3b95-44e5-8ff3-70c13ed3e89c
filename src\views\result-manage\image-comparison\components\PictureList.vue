<template>
  <div class="picture-list-wrapper">
    <div class="search-wrapper" v-loading="loading">
      <div class="selector search-wrapper-item">
        <el-select
          v-model="timeSelectTypeRect.value"
          placeholder="请选择"
          style="width: 100%"
          :clearable="false"
          @change="onQuickerTimeChangeHandle"
        >
          <el-option v-for="item in timeSelectTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="timer search-wrapper-item" v-show="showTimer">
        <el-row>
          <el-date-picker
            v-model="timerRect.value"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :size="size"
            value-format="YYYY-MM-DD"
            @change="onTimeChangeHandle"
          />
        </el-row>
      </div>
    </div>
    <div class="picture-wrapper" v-show="pictureListRect.visible">
      <div class="picture-list-content" ref="contentRef">
        <div class="picture-list-item" v-for="item in pictureListRect.list" :key="item.id">
          <div class="picture-list-item-content" @click="showImg(item)">
            <div class="picture-list-item-img">
              <el-image style="width: 100%" :src="item.file_url" fit="fill" />
            </div>
            <div class="picture-list-item-info">
              <div class="picture-list-item-info-title ellipsis">
                <el-tooltip :content="item.file_name" placement="top" effect="dark">
                  <span class="fonts">{{ item.file_name || '未命名' }}</span>
                </el-tooltip>
              </div>
              <div class="picture-list-item-info-time">
                <span class="fonts">{{ item.create_time }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <ImgDetail v-model:visible="imgDetail.visible" :title="imgDetail.title" :imgUrl="imgDetail.imgUrl" />
  </div>
</template>
<script>
export default { name: 'PictureList' };
</script>
<script setup>
import { defineProps, computed, defineEmits, onMounted, onUnmounted } from 'vue';
import {
  timeSelectTypeOptions,
  timeSelectTypeRect,
  onQuickerTimeChangeHandle,
  onTimeChangeHandle,
  timerRect,
  pictureListRect,
  comparePicturesRect,
  loading
} from '../imageComparison';
import ImgDetail from './ImgDetail.vue';
const copy = new URL('@/assets/compare/copy.png', import.meta.url).href;
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
const data = computed({
  get: () => props.modelValue,
  set: value => {
    emits('update:modelValue', value);
  }
});
const emits = defineEmits(['update:modelValue']);
//#region 定义变量
const showTimer = computed(() => {
  return timerRect.visible;
});

watch(
  () => timerRect.visible,
  () => {
    updateContentHeight();
  }
);

const imgDetail = reactive({
  visible: false,
  title: '图片详情',
  imgUrl: ''
});
//#region 动态计算高度
const contentRef = ref(null);
const updateContentHeight = () => {
  if (contentRef.value) {
    const searchWrapperHeight = document.querySelector('.search-wrapper').offsetHeight;
    const titleHeight = document.querySelector('.picture-list-title')?.offsetHeight ?? 0;
    const wrapperHeight = document.querySelector('.picture-list-wrapper').offsetHeight;
    let remainingHeight = wrapperHeight - searchWrapperHeight - titleHeight;
    if (showTimer.value) {
      remainingHeight -= 30;
    }
    contentRef.value.style.height = `${remainingHeight}px`;
  }
};
//#endregion

function showImg(data) {
  imgDetail.visible = true;
  imgDetail.title = data.name;
  imgDetail.imgUrl = data.file_url || data.url;
  comparePicturesRect.leftPicture = data; // 设置主照片
}

//#endregion
onMounted(() => {
  setTimeout(() => {
    updateContentHeight();
    window.addEventListener('resize', updateContentHeight);
  }, 100);
});
onUnmounted(() => {
  window.removeEventListener('resize', updateContentHeight);
});
</script>
<style lang="scss" scoped>
@import '../../../../styles/common/global.scss';
::v-deep .el-loading-mask {
  background-color: #a3a3a357 !important;
}
.picture-list-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-wrapper {
  width: 100%;
  padding: 10px 5px;
  .search-wrapper-item {
    margin: 10px 2px;
  }
}
.picture-wrapper {
  flex: 1;
  background-color: #001229;
  height: 100%;
  flex-direction: column;
  .picture-list-title {
    height: auto;
    width: 100%;
    padding: 8px 8px;
    color: aliceblue;
    background-color: #1f2f49;
    border-bottom: 1px solid #85858571;
    display: flex;
    align-items: center;
    .icon {
      display: flex;
      align-items: center;
      margin-right: 15px;
    }
  }
  .picture-list-content {
    flex: 1;
    width: 100%;
    color: aliceblue;
    overflow-y: auto;
    user-select: none;

    .picture-list-item {
      margin: 15px 0px;
      border-radius: 5px;
      .picture-list-item-content {
        background-color: rgb(24, 41, 56);
        padding: 0px 8px;
        cursor: pointer;
        .picture-list-item-img {
          height: auto;
          width: 100%;
        }
        .picture-list-item-info {
          padding: 5px;
          cursor: pointer !important;
          .picture-list-item-info-title {
            .fonts {
              font-size: 16px;
              font-weight: 600;
              color: rgb(255, 255, 255);
            }
          }
          .picture-list-item-info-time {
            .fonts {
              font-size: 14px;
              color: rgba(255, 255, 255, 0.654);
            }
          }
        }
      }
    }
  }
}

/* 滚动条样式 -----*/
/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 8px !important;
}
/* 滚动槽 */
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(190, 92, 92, 0.2);
  color: #175192;
  border-radius: 2px;
}

// 上箭头
// ::-webkit-scrollbar-button:start {
//   background-image: url('../../../../assets/up-arrow.png');
//   background-size: 14px !important;
//   background-repeat: no-repeat;
//   background-position: center center;
// }
// ::-webkit-scrollbar-button:end {
//   background-image: url('../../../../assets/down-arrow.png');
//   background-repeat: no-repeat;
//   background-size: 14px !important;
//   background-position: center center;
// }
/* 滚动条滑块（里面小方块） */
::-webkit-scrollbar-thumb {
  border-radius: 2px;
  width: 12px !important;
  background: #175192 !important;
  -webkit-box-shadow: inset 0 0 6px #175192 !important;
}
/* ---------*/
</style>
