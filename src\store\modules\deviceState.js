import { defineStore } from 'pinia';
import { EDeviceTypeName } from '@/utils/constants';
import { reactive } from 'vue';

// setup 设备状态信息（机场/无人机）
export const useDeviceStateStore = defineStore('deviceState', () => {
  // state
  // 设备状态数据
  const deviceState = reactive({
    dock: {}, // 机场状态数据
    device: {}, // 设备状态数据
    // 以下为指令飞行用到数据
    deviceInfo: {},
    dockInfo: {},
    gatewayInfo: {}, //remote controller, dock
    currentSn: '',
    currentType: -1
  });

  // osd面板
  const nowOsdVisible = reactive({
    visible: false, // 是否显示当前osd面板
    dock_sn: '', // 机场sn
    device_sn: '', // 无人机sn
    dock_callsign: '', // 机场 callsign
    device_callsign: '' // 无人机 callsign
  });

  //无人机直播
  const nowUavVisible = reactive({
    visible: false, //无人机 camera
    cameraName: '',
    droneSelected: '',
    cameraSelected: '',
    videoSelected: '',
    claritySelected: '',
    cameraId: ''
  });

  const osdVisible = {
    // 远程控制中用到的osd 显示设备相关信息
    sn: '',
    callsign: '',
    model: '',
    visible: true,
    gateway_sn: '',
    is_dock: false,
    payloads: null
  };

  // 机场指令执行状态信息
  const devicesCmdExecuteInfo = reactive({});

  // actions
  // 设置机场状态数据
  // latitude、longitude
  function setCurrentDock(info) {
    if (Object.keys(info.host).length === 0) {
      return;
    }
    if (!deviceState.dock.hasOwnProperty(info.sn)) {
      deviceState.dock[info.sn] = {};
      deviceState.dockInfo[info.sn] = {};
    }
    if (info.host.mode_code !== undefined) {
      deviceState.dock[info.sn].basic_osd = info.host;
      deviceState.dockInfo[info.sn].basic_osd = info.host;
    }
    if (info.host.wireless_link) {
      deviceState.dock[info.sn].link_osd = info.host;
      deviceState.dockInfo[info.sn].link_osd = info.host;
    }
    if (info.host.job_number !== undefined) {
      deviceState.dock[info.sn].work_osd = info.host;
      deviceState.dockInfo[info.sn].work_osd = info.host;
    }

    // 以下为指令飞行用到数据

    deviceState.currentSn = info.sn;
    deviceState.currentType = EDeviceTypeName.Dock;
  }

  // 获取指定 sn 的机场状态数据
  function getDockBySn(sn) {
    return deviceState.dock[sn];
  }

  // 设置无人机状态数据
  function setCurrentNav(info) {
    // console.log("设置无人机状态数据",info)
    if (!deviceState.device.hasOwnProperty(info.sn)) {
      deviceState.device[info.sn] = {};
    }
    deviceState.device[info.sn] = info.host;

    // 以下为指令飞行用到数据
    deviceState.deviceInfo[info.sn] = info.host;
    deviceState.currentSn = info.sn;
    deviceState.currentType = EDeviceTypeName.Aircraft;
  }

  // 获取指定 sn 的无人机状态数据
  function getNavBySn(sn) {
    return deviceState.device[sn];
  }

  // 设置当前osd面板
  function setNowOsdVisible(info) {
    nowOsdVisible.visible = info.visible;
    nowOsdVisible.dock_sn = info.dock_sn;
    nowOsdVisible.device_sn = info.device_sn;
    nowOsdVisible.dock_callsign = info.dock_callsign;
    nowOsdVisible.device_callsign = info.device_callsign;
  }

  // 设置当前无人机信息
  function setUavVisible(info) {
    nowUavVisible.visible = info.visible;
    nowUavVisible.cameraName = info.cameraName;
    nowUavVisible.droneSelected = info.droneSelected;
    nowUavVisible.cameraSelected = info.cameraSelected;
    nowUavVisible.videoSelected = info.videoSelected;
    nowUavVisible.claritySelected = info.claritySelected;
    nowUavVisible.cameraId = info.cameraId;
  }

  //保存设备指令ws消息推送
  function setDeviceCmdExecuteInfo(info) {
    console.log('保存设备指令ws消息推送');
    if (!info.sn) {
      return;
    }
    if (devicesCmdExecuteInfo[info.sn]) {
      const index = devicesCmdExecuteInfo[info.sn].findIndex(
        cmdExecuteInfo => cmdExecuteInfo.biz_code === info.biz_code
      );
      if (index >= 0) {
        // 丢弃前面的消息
        if (devicesCmdExecuteInfo[info.sn][index].timestamp > info.timestamp) {
          return;
        }
        devicesCmdExecuteInfo[info.sn][index] = info;
      } else {
        devicesCmdExecuteInfo[info.sn].push(info);
      }
    } else {
      devicesCmdExecuteInfo[info.sn] = [info];
    }
  }

  // 设置远程控制中用到的osd 显示设备相关信息
  function setOsdVisible(device) {
    osdVisible.sn = device.sn;
    osdVisible.callsign = device.callsign;
    osdVisible.model = device.model;
    osdVisible.visible = true;
    osdVisible.gateway_sn = device.gateway.sn;
    osdVisible.is_dock = true;
    osdVisible.gateway_callsign = device.gateway.callsign;
    osdVisible.payloads = device.payload;
  }

  function setGatewayInfo(info) {
    deviceState.gatewayInfo[info.sn] = info.host;
    deviceState.currentSn = info.sn;
    deviceState.currentType = EDeviceTypeName.Gateway;
  }

  function setDeviceOffline(info) {
    // state.deviceStatusEvent.deviceOffline = info
    delete deviceState.gatewayInfo[info.sn];
    delete deviceState.deviceInfo[info.sn];
    delete deviceState.dockInfo[info.sn];
    // delete state.hmsInfo[info.sn]
    // delete state.markerInfo.coverMap[info.sn]
    // delete state.markerInfo.pathMap[info.sn]
  }

  return {
    deviceState,
    nowOsdVisible,
    osdVisible,
    nowUavVisible,
    setCurrentDock,
    setCurrentNav,
    getDockBySn,
    getNavBySn,
    setNowOsdVisible,
    setDeviceCmdExecuteInfo,
    setOsdVisible,
    setGatewayInfo,
    setDeviceOffline,
    setUavVisible
  };
});
