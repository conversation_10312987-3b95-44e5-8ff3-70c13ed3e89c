<template>
  <el-dialog
    :title="title"
    :model-value="visible"
    v-if="visible"
    width="1056px"
    height="689px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <!--全景图-->
    <div id="viewerID" class="content"></div>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { Viewer } from '@photo-sphere-viewer/core';
import '@photo-sphere-viewer/core/index.css'; // 引入CSS样式

let panoramaViewer;
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  imgUrl: {
    type: String,
    default: '图片路径'
  }
});
const emit = defineEmits(['update:visible']);
defineExpose({});

// 关闭弹窗
function closeDialog() {
  if (panoramaViewer) {
    panoramaViewer.destroy();
    panoramaViewer = null;
  }
  emit('update:visible', false);
}

// 初始化全景图
function initPanoramaViewer() {
  if (panoramaViewer) {
    panoramaViewer.destroy();
    panoramaViewer = null;
  }
  if (props.visible && props.imgUrl) {
    nextTick(() => {
      panoramaViewer = new Viewer({
        container: '#viewerID',
        panorama: props.imgUrl
      });
    });
  }
}

onMounted(() => {
  initPanoramaViewer();
});

watch([() => props.visible, () => props.imgUrl], ([newVisible, newImgUrl]) => {
  if (newVisible && newImgUrl) {
    initPanoramaViewer();
  }
});
</script>

<style scoped lang="scss">
.content {
  height: 567px;
}
</style>
