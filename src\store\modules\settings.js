import { defineStore } from 'pinia';
// import { useStorage } from '@vueuse/core';

import defaultSettings from '@/settings';

import { getTagView, setTagView } from '@/utils/store';

export const useSettingsStore = defineStore('setting', () => {
  // state
  const showSettings = ref(defaultSettings.showSettings);
  const fixedHeader = ref(defaultSettings.fixedHeader);
  const sidebarLogo = ref(defaultSettings.sidebarLogo);
  const tagsView = ref(getTagView() || defaultSettings.tagsView || false);
  // const layout = useStorage('layout', defaultSettings.layout);
  // actions
  function changeSetting(param) {
    const { key, value } = param;
    switch (key) {
      case 'showSettings':
        showSettings.value = value;
        break;
      case 'fixedHeader':
        fixedHeader.value = value;
        break;
      case 'tagsView':
        tagsView.value = value;
        setTagView(tagsView.value);
        break;
      case 'sidevarLogo':
        sidebarLogo.value = value;
        break;
      // case 'layout':
      //   layout.value = value;
      //   break;
      default:
        break;
    }
  }

  return {
    showSettings,
    tagsView,
    fixedHeader,
    sidebarLogo,
    // layout,
    changeSetting
  };
});
