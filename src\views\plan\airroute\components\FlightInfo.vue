<template>
  <el-form
    ref="ruleFormRef"
    :model="waylineBaseInfoRect"
    label-position="top"
    class="wayline-base-info-wrapper bg-light-blue"
    :rules="rules"
  >
    <el-form-item label="航线名称" prop="planName">
      <el-input v-model="waylineBaseInfoRect.planName" placeholder="请输入航线名" :maxlength="32"></el-input>
    </el-form-item>
    <el-form-item label="航线类型">
      <el-select
        style="width: 100%"
        v-model="waylineBaseInfoRect.waylineType"
        placeholder="请选择"
        @change="onWayLineTypeChangeHandle"
      >
        <el-option
          v-for="item in waylineTypeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="设备型号">
      <el-select
        style="width: 100%"
        v-model="waylineBaseInfoRect.currentDevLabel"
        placeholder="请选择"
        @change="onDeviceChangeHandle"
      >
        <el-option
          v-for="item in waylineBaseInfoRect.droneChildrenListOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item>
      <div class="btn-wrap">
        <el-button type="primary" :disabled="!gotoCreate" @click="onCreateHandle">创建航线</el-button>
      </div>
    </el-form-item>
  </el-form>
</template>
<script>
export default {
  name: 'FlightInfo'
};
</script>
<script setup>
import { ElInput, ElButton } from 'element-plus';
import { onMounted, onUnmounted } from 'vue';
import { useDeviceStore } from '@/store/modules/device.js';
import { getDeviceModelList } from '@/api/wayline';
import { getDroneInfoByKey, deviceConfigs, setDeviceConfigsToList } from '@/views/plan/common/devConfigHelper.js';

const ruleFormRef = ref(null);
const deviceStore = useDeviceStore();
const emits = defineEmits(['changeHandle']); // 触发事件
const DRONE_LIST = ref([]);
// 这里是一个对象数组
const DRONE_CONFIG_LIST = ref({});
const gotoCreate = ref(true);
//#region 默认初始数据
const WAYLINETYPE = {
  polyline: 'polyline',
  polygon: 'polygon'
};
const waylineBaseInfoRect = reactive({
  planId: '',
  planName: '新建航线',
  series: '',
  droneEnumVal: 91, //飞行器
  droneEnumLabel: 'Matrice 3TD', //
  droneSubEnumVal: '',
  droneSubEnumLabel: '', //'
  payloadEnumVal: 81, //负载
  payloadSubEnumVal: 0,
  droneModelKey: '',
  payloadModelKey: '',
  droneChildrenListOptions: [],
  waylineType: WAYLINETYPE.polyline,
  currentDevValue: '',
  currentDevLabel: ''
});
const waylineTypeOptions = [
  {
    value: WAYLINETYPE.polyline,
    label: '航点航线'
  },
  {
    value: WAYLINETYPE.polygon,
    label: '面状航线'
  }
];

//#region 表单和校验
const validatePlanName = (rule, value, callback) => {
  const forbiddenChars = ['<', '>', ':', '"', '/', '|', '?', '*', '.', '_', '\\'];
  if (value === '') {
    callback(new Error('航线名称不能为空'));
  } else if (value && forbiddenChars.some(char => value.includes(char))) {
    callback(new Error('航线名称请勿包含以下字符：< > : " / | ? * . _ \\'));
  } else if (value.length > 32) {
    callback(new Error('航线名称长度不能超过32个字符'));
  } else {
    callback();
  }
};

const rules = {
  planName: [
    { required: true, message: '请输入航线名称', trigger: 'blur' },
    { min: 1, max: 32, message: '航线名称长度要在 1 - 32 之间', trigger: 'blur' },
    { validator: validatePlanName, trigger: 'blur' }
  ]
};

//#endregion

const onWayLineTypeChangeHandle = value => {
  waylineBaseInfoRect.waylineType = value;
};
//#endregion

const onDeviceChangeHandle = value => {
  waylineBaseInfoRect.droneModelKey = value;
  getDeviceOptions();
};

const init = () => {
  // 用于存储结果的对象
  DRONE_CONFIG_LIST.value = setDeviceConfigsToList(deviceConfigs);
  if (!DRONE_LIST.value || DRONE_LIST.value.length === 0) {
    throw new Error('获取设备列表出错，请检查网络！');
  }
  waylineBaseInfoRect.droneChildrenListOptions.length = 0;
  DRONE_LIST.value.forEach(drone => {
    waylineBaseInfoRect.droneChildrenListOptions.push({ value: drone.device_model, label: drone.device_name });
  });
  // 获取 key
  waylineBaseInfoRect.droneModelKey = waylineBaseInfoRect.droneChildrenListOptions[0].value;
  deviceStore.getDeviceInfoByType(waylineBaseInfoRect.droneModelKey);
  waylineBaseInfoRect.currentDevValue = waylineBaseInfoRect.droneChildrenListOptions[0].value;
  waylineBaseInfoRect.currentDevLabel = waylineBaseInfoRect.droneChildrenListOptions[0].label;
  getDeviceOptions();
};

/**
 * 获取设备选项
 */
const getDeviceOptions = () => {
  if (!waylineBaseInfoRect.droneModelKey) {
    return;
  }
  // 获取信息
  const baseInfo = getDroneInfoByKey(waylineBaseInfoRect.droneModelKey);
  if (!baseInfo) {
    gotoCreate.value = false;
    // ElMessage.error('未找到基础设备信息！');
    return;
  }
  gotoCreate.value = true;
  let deviceInfo = baseInfo?.device ?? null;
  if (!deviceInfo) {
    ElMessage.error('配置信息中未找到设备信息！');
    return;
  }

  waylineBaseInfoRect.series = deviceInfo.series;
  waylineBaseInfoRect.droneEnumVal = deviceInfo.droneEnumVal;
  waylineBaseInfoRect.droneEnumLabel = deviceInfo.droneEnumLabel ?? '';
  waylineBaseInfoRect.droneSubEnumVal = deviceInfo.droneSubEnumVal;
  waylineBaseInfoRect.droneSubEnumLabel = deviceInfo.droneSubEnumLabel;
  waylineBaseInfoRect.payloadEnumVal = deviceInfo.payloadEnumVal;
  waylineBaseInfoRect.payloadSubEnumVal = deviceInfo.payloadSubEnumVal;
  waylineBaseInfoRect.payloadModelKey = deviceInfo.payloadModelKey;
};

/**
 * 创建航线
 * @param options
 */
const onCreateHandle = () => {
  ruleFormRef.value.validate(valid => {
    if (valid) {
      deviceStore.getDeviceInfoByType(waylineBaseInfoRect.droneModelKey) ?? null;
      const respone = {
        checked: true,
        type: '0'
      };
      if (waylineBaseInfoRect.waylineType === WAYLINETYPE.polyline) {
        respone.type = '0';
      } else if (waylineBaseInfoRect.waylineType === WAYLINETYPE.polygon) {
        respone.type = '1';
      }
      // 将当前设备信息保存到全局变量中
      deviceStore.setCurrentDevice(waylineBaseInfoRect);
      // 跳转
      emits('changeHandle', respone);
    } else {
      return false;
    }
  });
};

onMounted(() => {
  getDeviceModelList()
    .then(data => {
      DRONE_LIST.value = data ?? [];
      init();
    })
    .catch(error => {
      DRONE_LIST.value = [];
      throw new Error('Error: 获取设备列表出错: ' + error.message + '.');
    });
});
onUnmounted(() => {});
</script>
<style lang="scss" scoped>
::v-deep .el-input .el-input__inner {
  font-size: 16px;
  color: #dadada;
}
::v-deep .el-input-numbert.is-disabled .el-input__wrapper {
  background-color: #11253e;
}
::v-deep .el-input .el-input__wrapper {
  background-color: #11253e;
  box-shadow: 0 0 0 1px #cfcfcf8f inset;
}

::v-deep .el-input .el-input__wrapper.is-focus {
  box-shadow: 0 0 0 1px #cfcfcf8f inset;
}

::v-deep .el-select .el-input {
  border: 1px solid #cfcfcf8f;
}
::v-deep .el-select .el-input__inner {
  border: 1px solid transparent;
  color: #dadada;
}
::v-deep .el-select .el-input__wrapper {
  background-color: #11253e;
  color: #dadada;
}
::v-deep .el-select .el-input .el-select__caret {
  color: #fff;
}
.bg-light-blue {
  background-color: #1f2f49 !important;
}
.bg-dark-blue {
  background-color: #1f2f49 !important;
}

.wayline-base-info-wrapper {
  background-color: #232323;
  color: white;
  padding: 15px 5px;
  width: 100%;
  user-select: none;
  .row {
    margin: 5px;
    padding: 3px 12px;
  }

  .row-label {
    padding: 3px 0px;
  }

  // row 第一个和最后一个 不进行 margin
  .row:first-child {
    margin-top: 0px;
    padding: 0px 12px;
  }
  .row:last-child {
    margin-bottom: 0px;
    padding: 0px 12px;
  }
}
.btn-wrap {
  width: 100%;
  display: flex;
  justify-content: center;
}
.round {
  border: 5px;
  color: white;
}
.item {
  width: auto;
  padding: 2px 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  background: rgba(45, 140, 240, 0.35);
  color: #ffffff40;
  margin-left: 5px;
  user-select: none;
  &:hover {
    cursor: pointer;
  }
}
.active {
  cursor: pointer;
  text-decoration: none !important;
  background: #5d5f61;
}

.uranus-btn {
  background: #505254;
  color: #fff;
  margin: 2px 2px;
  padding: 2px 5px;
  user-select: none;
  cursor: pointer;
}

.uranus-btn:hover {
  cursor: pointer;
  text-decoration: none !important;
  background: #5d5f61;
}

.uranusBtnDisabled {
  cursor: not-allowed !important;
  color: hsla(0, 0%, 100%, 0.25);
}

.notAllowed {
  cursor: not-allowed !important;
  color: hsla(0, 0%, 100%, 0.25);
  //cursor: pointer;
  background: #505254 !important;
}

.color-blue {
  background: #2d8cf0;
  color: white;
}
</style>
