<template>
  <el-dialog
    :title="title"
    v-if="visible"
    :model-value="visible"
    width="800px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form label-width="100px">
      <div class="user-add-from">
        <div class="addFrom-left">
          <el-form-item label="驾驶证信息" prop="password" class="mt-[16px]">
            <el-button type="primary" @click="handleAdd('add')">添加驾驶证</el-button>
          </el-form-item>
          <div>
            <el-table :data="driverList" header-cell-class-name="default-table-header-cell" table-layout="auto" stripe height="540">
              <el-table-column label="序号" type="index" width="62" />
              <el-table-column label="驾驶证类型" width="100" prop="type" show-overflow-tooltip />
              <el-table-column label="驾驶证号" width="150" prop="number" show-overflow-tooltip />
              <el-table-column label="过期日期" width="150" prop="limit_time" show-overflow-tooltip>
                <template #default="scope">
                  <span>{{ moment(scope.row.limit_time).format('YYYY-MM-DD') }}</span>
                </template>
              </el-table-column>
              <el-table-column label="驾驶证照片" width="120" prop="image">
                <template #default="scope">
                  <img style="width: 100px; height: 100px" :src="scope.row.image" />
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button type="primary" link @click="handleAdd('edit', scope.row)"> 编辑 </el-button>
                  <el-button :type="!scope.row.enabled ? 'danger' : ''" link @click.stop="handleDelete(scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">关闭</el-button>
      </div>
    </template>
    <!--嵌套添加驾驶证弹窗-->
    <el-dialog
      title="驾驶证信息"
      v-if="addVisible"
      :model-value="addVisible"
      destroy-on-close
      width="500px"
      align-center
      :close-on-click-modal="false"
      @close="closeAddDialog"
    >
      <el-form ref="addFormRef" :model="addForm" :rules="rules" label-width="100px">
        <div class="user-add-from">
          <div class="addFrom-left">
            <el-row>
              <el-col :span="24">
                <el-form-item label="驾驶证类型" prop="type" class="mt-[8px]">
                  <el-select v-model="addForm.type" placeholder="请选择驾驶证类型" style="width: 100%" clearable>
                    <el-option
                      v-for="item in canChooseList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      :disabled="item.disabled"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="驾驶证号" prop="number" class="mt-[8px]">
                  <el-input
                    v-model="addForm.number"
                    type="text"
                    placeholder="请输入驾驶证号"
                    maxlength="30"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="过期日期" prop="limit_time" class="mt-[8px]">
                  <el-date-picker
                    v-model="addForm.limit_time"
                    type="date"
                    placeholder="请选择过期日期"
                    :value-format="'YYYY-MM-DD 00:00:00'"
                    :format="'YYYY-MM-DD'"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="驾驶证照片" class="mt-[8px]" required>
                  <div>
                    <el-button class="uploadBtn" type="primary"
                      >上传照片<input
                        class="uploadImg"
                        ref="fileInput"
                        type="file"
                        accept="image/*"
                        @change="handleFileChange"
                        title=""
                    /></el-button>
                  </div>
                  <div v-if="addForm.image" class="mt-[16px]" style="min-width: 300px">
                    <el-image
                      style="max-width: 300px; max-height: 300px"
                      :src="addForm.image"
                      :previewSrcList="[addForm.image]"
                      :zoom-rate="1.2"
                      :max-scale="7"
                      :min-scale="0.2"
                      fit="cover"
                    />
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmit" v-loading="loading">确 定</el-button>
          <el-button @click="closeAddDialog">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import optionData from '@/utils/option-data';
import moment from 'moment';
import { listDriver, updateDriver, delDriver } from '@/api/system/user';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  addFormData: {
    type: Object,
    default(rawProps) {
      return {};
    }
  }
});
const form = reactive({});
const canChooseList = ref([]);
const driverList = ref([]);
let addForm = reactive({});
const addVisible = ref(false);
const addFormRef = ref(null);
const fileInput = ref(null);
watch(
  () => props.addFormData,
  (newVal, oldVal) => {
    Object.assign(form, newVal); //form.id 父组件传入成员id
    getList();
  },
  { deep: true }
);
const emit = defineEmits(['update:visible', 'driveSubmit']);
const rules = reactive({
  type: [{ required: true, message: '请选择驾驶证类型', trigger: 'change' }],
  number: [{ required: true, message: '请输入驾驶证号', trigger: ['blur'] },{
      required: true,
      pattern: /^[a-zA-Z0-9]+$/,
      message: '驾驶证号只能输入英文和数字',
      trigger: 'blur'
    }],
  limit_time: [{ required: true, message: '请选择过期日期', trigger: ['blur', 'change'] }],
  image: [{ required: true, message: '请上传驾驶证照片', trigger: 'change' }]
});

defineExpose({});

const loading = ref(false);

/** 查询驾驶证列表 */
function getList() {
  loading.value = true;
  listDriver(form.id)
    .then(response => {
      driverList.value = response.list || [];
      loading.value = false;
      //驾驶证类型需要重新赋值避免过滤出错
      canChooseList.value = JSON.parse(JSON.stringify(optionData.driveTypeOption));
      console.log('获取列表', driverList.value, optionData.driveTypeOption);
    })
    .catch(error => {
      driverList.value = [];
      loading.value = false;
      console.error('There was a problem with the fetch operation:', error); // 捕获处理失败的错误
    });
}
//过滤可选驾驶证类型
function filterType() {
  // 遍历 driverList 中的每个对象
  driverList.value.forEach(dataItem => {
    // 在 driveTypeOption 中找到匹配的选项
    let optionItem = canChooseList.value.find(option => option.value == dataItem.type);
    // 如果找到匹配项，则将其 disabled 置为 true
    if (optionItem) {
      optionItem.disabled = true;
    }
  });
}
// 打开添加弹窗
function handleAdd(type, row) {
  addVisible.value = true;
  resetAddForm();
  filterType();
  nextTick(() => {
    if (type == 'edit') {
      //编辑打开
      addForm.id = row.id
      addForm.type = row.type;
      addForm.number = row.number;
      addForm.limit_time = moment(row.limit_time).format('YYYY-MM-DD 00:00:00');
      addForm.image = row.image;
    }
  });
}
// 关闭添加弹窗
function closeAddDialog() {
  addVisible.value = false;
}
/**
 * 重置添加表单
 */
function resetAddForm() {
  nextTick(() => {
    addFormRef.value.resetFields();
    setTimeout(()=>{
      addFormRef.value.clearValidate();
    },100)
    Object.keys(addForm).map(key => {
      delete addForm[key];
    });
    //清除上传文件的图片路径
    fileInput.value.value = '';
  });
}
// 关闭列表弹窗
function closeDialog() {
  emit('update:visible', false);
  emit('driveSubmit');
}
//原生上传图片
const handleFileChange = event => {
  const file = event.target.files[0];
  if (!file) {
    return;
  }
  if (file.size > 2 * 1024 * 1024) {
    ElMessage.error('上传的图片大小不能超过2MB');
    return;
  }
  const reader = new FileReader();
  reader.onload = () => {
    addForm.image = reader.result;
  };
  reader.readAsDataURL(file);
};
//提交添加表单弹窗
function handleSubmit() {
  addFormRef.value.validate(isValid => {
    if (isValid) {
      if(!addForm.image) {
        ElMessage.error('请上传驾驶证照片')
        return;
      }
      let params = { ...addForm };
      loading.value = true;
      updateDriver(params, form.id)
        .then(res => {
          loading.value = false;
          ElMessage.success('操作成功');
          getList();
          closeAddDialog();
        })
        .catch(e => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}
//删除驾驶证
function handleDelete(row) {
  ElMessageBox.confirm(`确认后将删除此驾驶证，且无法进行恢复`, '确认删除所选驾驶证？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    autofocus: false,
    closeOnClickModal: false,
    type: 'warning'
  }).then(() => {
    delDriver(row.id).then(data => {
      ElMessage.success('删除成功');
      getList();
    });
  });
}
onMounted(() => {
  //驾驶证可选列表初始化等于字典里配置的类型
  canChooseList.value = JSON.parse(JSON.stringify(optionData.driveTypeOption));
});
</script>
<style scoped lang="scss">
.input-serach {
  width: 200px;
}
.uploadImg {
  opacity: 0;
  position: absolute;
  left: 0;
  top: 0;
  width: 140px;
  height: 32px;
  cursor: pointer;
}
.uploadBtn {
  position: relative;
  width: 140px;
}
.app-form {
  .select-time {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 5px 0;
  }
  ::v-deep {
    .el-input-number,
    .el-select {
      width: 100%;
    }
    .el-input-number .el-input__inner {
      text-align: left;
    }
    .el-input-number.is-controls-right .el-input__wrapper {
      padding-left: 11px;
    }
    .el-date-editor.el-input,
    .el-date-editor.el-input__wrapper {
      width: 100%;
    }
  }
}
</style>
