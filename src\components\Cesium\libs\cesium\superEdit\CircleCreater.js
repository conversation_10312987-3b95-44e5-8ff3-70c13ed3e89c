import * as Cesium from 'cesium';
import {
  addPropertiesToEntity,
  getPropertyByKey,
  calculatePointFromCenter,
  toCartesian3,
  toNumber,
  toDegrees
} from '../common';
import { generateKey } from '@/utils';
import { ElMessage, ElMessageBox } from 'element-plus';
import { dialogDataRect } from '@/views/flight-manage/flight-area/flightAreaHandle';

//#region 绘制部分
let viewer = null;
let handler = null;
let labelEntity = null;
export let createObjectGlobal = null;
export let isCircleCreate = ref(false);
const endpointIcon = new URL('@/assets/plan/wrj/endpoint.png', import.meta.url).href;
const midpointIcon = new URL('@/assets/plan/wrj/middlepoint.png', import.meta.url).href;
/**
 * 绘制圆
 * @param {*} viewer viewer 对象
 * @param {*} CallBack 完成后的回调
 */
export const drawCircle = (v, options, CallBack) => {
  if (!v) {
    throw new Error('viewer is required');
  }
  circleDispose();
  viewer = v;
  let drawResultObject = {
    geomType: options.geomType || 'circle',
    flightAreaType: options.flightAreaType || 'dfence',
    id: null,
    center: [],
    centerEntity: null,
    radius: 1,
    entity: null,
    startPosition: null,
    endPosition: null,
    title: options?.title || '',
    action: 'add',
    color: options?.color || '',
    area: 0,
    length: 0
  };
  document.body.style.cursor = 'crosshair'; //
  try {
    handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas); // 选中的经纬度
    // 最后
    handler.setInputAction(click => {
      drawResultObject.id = generateKey();
      let cartesian = viewer.camera.pickEllipsoid(click.position, viewer.scene.globe.ellipsoid);
      drawResultObject.startPosition = cartesian;
      let cartographic = Cesium.Cartographic.fromCartesian(
        cartesian,
        viewer.scene.globe.ellipsoid,
        new Cesium.Cartographic()
      );
      let lng = Cesium.Math.toDegrees(cartographic.longitude);
      let lat = Cesium.Math.toDegrees(cartographic.latitude);
      drawResultObject.center = [lng, lat];
      // 创建一个新的 CallbackProperty 实例
      const semiMinorAxisCb = new Cesium.CallbackProperty(function () {
        return drawResultObject.radius || 0;
      }, false);

      drawResultObject.centerEntity = createPoint(viewer, drawResultObject.center, endpointIcon);
      drawResultObject.entity = viewer.entities.add({
        position: new Cesium.Cartesian3.fromDegrees(...drawResultObject.center, 0),
        name: 'circle',
        id: drawResultObject.id,
        label: {
          text: options?.label || '',
          show: true,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: Cesium.VerticalOrigin.CENTER,
          font: '20px monospace', // 字体边框
          outline: true,
          fillColor: Cesium.Color.WHITE,
          outlineWidth: 5,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new Cesium.Cartesian2(0, -30),
          showBackground: true,
          backgroundColor: new Cesium.Color(0.117, 0.117, 0.117, 0.7),
          eyeOffset: new Cesium.Cartesian3(0, 0, 2),
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND //贴地
        },
        ellipse: {
          material: options?.color || Cesium.Color.WHITE.withAlpha(0.4),
          outline: true,
          outlineColor: Cesium.Color.RED,
          outlineWidth: 1, // 是否被提供的材质填充
          fill: true,
          semiMajorAxis: semiMinorAxisCb,
          semiMinorAxis: semiMinorAxisCb,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND //贴地
        }
      });
      createObjectGlobal = drawResultObject.entity;
      // 这里移除左键监听
      if (drawResultObject.center) {
        handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
      }
      // 移动则动态绘制
      handler.setInputAction(move => {
        let newCartesian = viewer.camera.pickEllipsoid(move.endPosition, viewer.scene.globe.ellipsoid);
        if (!newCartesian) {
          return;
        }
        drawResultObject.endPosition = newCartesian;
        drawResultObject.radius = Cesium.Cartesian3.distance(cartesian, newCartesian);
        //  计算 cartesian, cartesian2中间点 这里在移动的时添加 一个label 用于展示半径 这里的label 位置在圆心和圆心连线中点
        const midpoint = Cesium.Cartesian3.midpoint(cartesian, newCartesian, new Cesium.Cartesian3());
        // 标注 如果存在了就直接更新位置和 label值
        createOrUpdateLabelEntity(drawResultObject, midpoint);
      }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      dialogDataRect.action = 'add';
      dialogDataRect.entity = drawResultObject.entity;
      isCircleCreate.value = true;
      createObjectGlobal = drawResultObject.entity;
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

    handler.setInputAction(() => {
      if (!drawResultObject.startPosition || drawResultObject.radius === 0) {
        ElMessage.warning('请先绘制圆心点');
        return;
      }
      handler?.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      handler?.destroy();
      document.body.style.cursor = 'default';
      drawResultObject.area = toNumber(drawResultObject.radius * drawResultObject.radius * Math.PI, 2);
      // drawResultObject.area = parseArea(drawResultObject.area);
      drawResultObject.length = toNumber(drawResultObject.radius * 2 * Math.PI, 2);

      addPropertiesToEntity(drawResultObject.entity, {
        type: 'circle',
        title: drawResultObject.title || '',
        id: drawResultObject.id,
        center: drawResultObject.center || [],
        flightAreaType: drawResultObject.flightAreaType || 'dfence',
        radius: toNumber(drawResultObject.radius, 2),
        area: toNumber(drawResultObject.radius * drawResultObject.radius * Math.PI, 2),
        length: toNumber(drawResultObject.radius * 2 * Math.PI, 2),
        color: options?.color || Cesium.Color.WHITE.withAlpha(0.4)
      });
      clearLabelEntity();
      createObjectGlobal = drawResultObject.entity;
      isCircleCreate.value = true;
      drawResultObject.centerEntity && viewer.entities.remove(drawResultObject.centerEntity);
      CallBack && CallBack(drawResultObject);
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  } catch (error) {}
};

export const circleDispose = () => {
  if (handler && handler.isDestroyed() === false) {
    handler?.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    handler?.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
    handler?.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    handler.destroy();
  }
  isCircleCreate.value = false;
  if (viewer && createObjectGlobal) {
    viewer.entities.remove(createObjectGlobal);
  }
  createObjectGlobal = null;
  document.body.style.cursor = 'default';
};
export const cancelCircleAdd = () => {
  if (viewer && createObjectGlobal) {
    viewer.entities.remove(createObjectGlobal);
  }
  createObjectGlobal = null;
  circleDispose();
};
//#endregion

//#region 公用部分
/**
 * 米到公里的转换方法
 * @param {*} num
 * @returns
 */

const parseFloat = num => {
  num = toNumber(num, 3);
  let result =
    Number(num) === 0
      ? ''
      : Number(num) > 1000
      ? (Number(num) / 1000).toFixed(2) + ' km'
      : Number(num).toFixed(2) + ' m';
  return result;
};

const parseArea = num => {
  if (num >= 10000) {
    // 如果面积大于等于 1,000,000 平方米，用平方公里表示
    const areaInSquareKilometers = (num / 1000000).toFixed(2);
    return areaInSquareKilometers + ' km²';
  } else {
    // 否则用平方米表示
    const areaInSquareMeters = num.toFixed(2);
    return areaInSquareMeters + ' m²';
  }
};

/**
 * 通用产生采集点
 * @param {*} cartesian
 * @returns
 */
export function createPoint(viewer, cartesian, iconurl = endpointIcon) {
  let point = viewer.entities.add({
    position: toCartesian3(cartesian),
    billboard: {
      image: iconurl || endpointIcon,
      width: 14,
      height: 14,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      disableDepthTestDistance: Number.POSITIVE_INFINITY // 点总是在最上层
    }
  });
  return point;
}
//#endregion

//#region 创建辅助部分

// 清除辅助部分
export function clearLabelEntity() {
  if (!viewer) {
    return;
  }
  if (labelEntity) {
    viewer.entities.remove(labelEntity);
    labelEntity = null;
  }
}
// 创建辅助部分
function createOrUpdateLabelEntity(drawResultObject = null, midpoint = null) {
  if (!viewer || !midpoint || !drawResultObject) {
    return;
  }
  if (labelEntity) {
    labelEntity.position = midpoint;
    labelEntity.label.text = parseFloat(drawResultObject.radius);
  } else {
    labelEntity = viewer.entities.add({
      position: midpoint,
      label: {
        text: parseFloat(drawResultObject.radius),
        show: true,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        font: '30px monospace', // 字体边框
        outline: true,
        fillColor: Cesium.Color.WHITE,
        outlineWidth: 5,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -30),
        showBackground: true,
        backgroundColor: new Cesium.Color(0.117, 0.117, 0.117, 0.7),
        eyeOffset: new Cesium.Cartesian3(0, 0, 2),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND, //贴地
        disableDepthTestDistance: Number.POSITIVE_INFINITY // 点总是在最上层
      },

      polyline: {
        positions: new Cesium.CallbackProperty(function (time, result) {
          let arr = [drawResultObject.startPosition, drawResultObject.endPosition];
          if (!drawResultObject.startPosition || !drawResultObject.endPosition) {
            return [];
          }
          return arr;
        }, false),
        width: 5,
        material: new Cesium.PolylineOutlineMaterialProperty({
          color: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.CRIMSON,
          outlineWidth: 2
        }),
        clampToGround: true
      }
    });
  }
}

//#endregion
