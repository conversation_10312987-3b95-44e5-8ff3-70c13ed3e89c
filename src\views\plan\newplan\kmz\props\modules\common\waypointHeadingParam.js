//#region waypointHeadingParam
//飞行器偏航角模式
export const WAY_POINT_HEADING_MODE = {
  followWayline: 'followWayline', // '沿航线方向。飞行器机头沿着航线方向飞至下一航点',
  manually: 'manually', //'手动控制。飞行器在飞至下一航点的过程中,用户可以手动控制飞行器机头朝向',
  fixed: 'fixed', //'锁定当前偏航角。飞行器机头保持执行完航点动作后的飞行器偏航角飞至下一航点',
  smoothTransition: 'smoothTransition' // '自定义。通过"wpml:waypointHeadingAngle"给定某航点的目标偏航角,并在航段飞行过程中均匀过渡至下一航点的目标偏航角',
  // towardPOI: 'towardPOI' //'朝向兴趣点'
};
export const WAY_POINT_HEADING_MODEOptions = [
  // {
  //   value: WAY_POINT_HEADING_MODE.followWayline,
  //   label: '沿航线方向。飞行器机头沿着航线方向飞至下一航点'
  // },
  // {
  //   value: WAY_POINT_HEADING_MODE.manually,
  //   label: '手动控制。飞行器在飞至下一航点的过程中,用户可以手动控制飞行器机头朝向'
  // },
  // {
  //   value: WAY_POINT_HEADING_MODE.fixed,
  //   label: '锁定当前偏航角。飞行器机头保持执行完航点动作后的飞行器偏航角飞至下一航点'
  // },
  // {
  //   value: WAY_POINT_HEADING_MODE.smoothTransition,
  //   label:
  //     '自定义。通过"wpml:waypointHeadingAngle"给定某航点的目标偏航角,并在航段飞行过程中均匀过渡至下一航点的目标偏航角'
  // },
  // { value: WAY_POINT_HEADING_MODE.towardPOI, label: '朝向兴趣点' }
];

//飞行器偏航角转动方向
export const WAY_POINT_HEADING_PATH_MODE = {
  clockwise: 'clockwise', // 顺时针旋转飞行器偏航角
  counterClockwise: 'counterClockwise', // 逆时针旋转飞行器偏航角
  followBadArc: 'followBadArc' // 沿最短路径旋转飞行器偏航角
};
export const WAY_POINT_HEADING_PATH_MODEOptions = [
  {
    value: WAY_POINT_HEADING_PATH_MODE.clockwise,
    label: '顺时针旋转飞行器偏航角'
  },
  {
    value: WAY_POINT_HEADING_PATH_MODE.counterClockwise,
    label: '逆时针旋转飞行器偏航角'
  },
  {
    value: WAY_POINT_HEADING_PATH_MODE.followBadArc,
    label: '沿最短路径旋转飞行器偏航角'
  }
];
//#endregion

//#region 代码块
//#endregion

//#region 代码块
//#endregion
