// 云台俯仰角相关
// kml 录像默认结构如下
// clockwise：顺时针旋转
// counterClockwise：逆时针旋转
{
  //   <wpml:action>
  //   <wpml:actionId>1</wpml:actionId>
  //   <wpml:actionActuatorFunc>gimbalRotate</wpml:actionActuatorFunc>
  //   <wpml:actionActuatorFuncParam>
  //     <wpml:gimbalHeadingYawBase>north</wpml:gimbalHeadingYawBase>
  //     <wpml:gimbalRotateMode>absoluteAngle</wpml:gimbalRotateMode>
  //     <wpml:gimbalPitchRotateEnable>0</wpml:gimbalPitchRotateEnable>
  //     <wpml:gimbalPitchRotateAngle>0</wpml:gimbalPitchRotateAngle>
  //     <wpml:gimbalRollRotateEnable>0</wpml:gimbalRollRotateEnable>
  //     <wpml:gimbalRollRotateAngle>0</wpml:gimbalRollRotateAngle>
  //     <wpml:gimbalYawRotateEnable>1</wpml:gimbalYawRotateEnable>
  //     <wpml:gimbalYawRotateAngle>24.8</wpml:gimbalYawRotateAngle>
  //     <wpml:gimbalRotateTimeEnable>0</wpml:gimbalRotateTimeEnable>
  //     <wpml:gimbalRotateTime>0</wpml:gimbalRotateTime>
  //     <wpml:payloadPositionIndex>0</wpml:payloadPositionIndex>
  //   </wpml:actionActuatorFuncParam>
  // </wpml:action>
}
import { Action } from '../../waylines';
import { ACTION_TRIGGER_TYPE, ACTION_ACTUATOR_FUNC } from '@/utils/constants';
import { generateKey } from '@/utils';
//#region 云台俯仰角相关
/**
 * 创建云台俯仰角相关动作
 * @param {Object} options 动作配置项，应包含actionId、actionActuatorFunc
 * @param {Object|null} actionActuatorFuncParamOptions 动作执行器参数配置，可选
 * @returns {Action|null} 返回Action实例或在配置不正确时返回null
 */
export function createGimbalRotateAction(options, actionActuatorFuncParamOptions = null) {
  try {
    // 创建动作
    if (!options) {
      return null;
    }
    return new Action({
      actionId: options.actionId || 0,
      actionActuatorFunc: ACTION_ACTUATOR_FUNC.gimbalRotate,
      actionActuatorFuncParam: actionActuatorFuncParamOptions || getGimbalRotateActionDefaultParam(),
      uuid: options.actionUuid || generateKey(), // 动作id
      trigger: ACTION_TRIGGER_TYPE.reachPoint
    });
  } catch (error) {
    console.error('创建 Action 实例失败:', error);
    return null;
  }
}
// 获取悬停默认参数
export function getGimbalRotateActionDefaultParam() {
  return {
    wpml_gimbalHeadingYawBase: 'north', // 北面为基准方面
    wpml_gimbalRotateMode: 'absoluteAngle',
    wpml_gimbalPitchRotateEnable: 0,
    wpml_gimbalPitchRotateAngle: 0,
    wpml_gimbalRollRotateEnable: 0,
    wpml_gimbalRollRotateAngle: 0,
    wpml_gimbalYawRotateEnable: 0,
    wpml_gimbalYawRotateAngle: 0,
    wpml_gimbalRotateTimeEnable: 0,
    wpml_gimbalRotateTime: 0,
    wpml_payloadPositionIndex: 0
  };
}
//#endregion
