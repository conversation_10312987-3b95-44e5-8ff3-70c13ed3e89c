<script>
export default { name: 'Drone<PERSON>ist' };
</script>

<script setup>
import { ref, onMounted, watch } from 'vue';
import VideoPlayer from '../../videos/live-stream/components/VideoPlayer.vue';
import { refreshStreams } from '@/api/devices'
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
	showClose: {
		type: Boolean,
		default: true, //是否显示关闭按钮
	},
	showBlowUp: {
		type: Boolean,
		default: false, //是否显示与地图切换按钮
	},
	isBig: {
		type: Boolean,
		default: false, //是否为实时飞行右侧大屏展示
	},
	heigth: {
		type: Number,
    default: 328
	},
	device: {
		type: Array,
		default() {
      return [];
    }
	},
	playerId: {
		type: String,
		default: ''
	},
	showUp: {
		type: Boolean,
		default: true, //是否显示全屏按钮
	},
	showRefresh: {
		type: Boolean,
		default: false, //是否显示刷新按钮
	},
	showAI: {
		type: Boolean,
		default: false, //是否显示AI烟火识别
	},
	showShare: {
		type: <PERSON>olean,
		default: false, //是否显示AI烟火识别
	},
});
const emit = defineEmits(['onClose','showAI']);
const checkedDevice=ref([])

//宇视
const ysVideoRef = ref(null)
const playerYsId = ref(`playerBox_${Date.now()}`)
const deviceNo = ref('11010900011321200021')//test


function handleCountChange() {
  window.$bus.emit('changeVideoCount', 1);
}

watch(
  () => props.device,
  newVal => {
    checkedDevice.value = [];
		let params = {
			...toRaw(newVal)[0],
			droneSelected: toRaw(newVal)[0]?.device_sn,
			channel: toRaw(newVal)[0]?.channel,
			cameraSelected: toRaw(newVal)[0]?.index,
			cameraName: toRaw(newVal)[0]?.nickname,
			url_type: toRaw(newVal)[0]?.url_type,
			source: toRaw(newVal)[0]?.source,
			claritySelected: 3,
			cameraId: toRaw(newVal)[0]?.cameraId || '417dc9aa-0db3-4cd8-91ea-1387ae59e716'
		}
    checkedDevice.value = [params] || [];
  },
  { deep: true, immediate: true }
);

onMounted(()=>{
	handleCountChange();
})

function closeVideo () {
	if(ysVideoRef.value){
		ysVideoRef.value.onStop()
		ysVideoRef.value = null
	}
	emit('onClose')
}

function refreshVideo () {
	refreshStreams({
		url: '',
		video_id: `${checkedDevice.value[0]?.droneSelected}/${checkedDevice.value[0]?.cameraSelected}/normal-0`,
		url_type: 3,
		video_quality: checkedDevice.value[0]?.claritySelected
	}).then(res=>{
    window.$bus.emit('refreshVideo',res.url);
	})
}

function blowUp () {
  window.$bus.emit('changeVideoCount', 1);
	emit('change')
}

function changeAI () {
	emit('showAI')
}

function openShare () {
	emit('showShare')
}

</script>

<template>
	<div v-show="visible" style="height: 100%;z-index: 9999;">
		<div :class="isBig ? ['big-title','flex'] : ['alarm-title','flex']">
			<div>	
				<svg-icon icon-class="title" style="margin-right: 4;transform: translateY(-13px)" />
				<span class="ellipsis title">{{ checkedDevice[0]?.nickname || '' }}</span>
			</div>
			<div class="flex">
				<div v-if="showShare" style="margin-right: 10px;cursor: pointer" @click="openShare">分享</div>
				<div v-if="showAI" style="margin-right: 10px;cursor: pointer" @click="changeAI">烟火识别</div>
				<div v-if="showRefresh" @click="refreshVideo"><svg-icon icon-class="refresh" style="width:16px;height:16px;margin-right: 6;cursor: pointer;" /></div>
				<div v-if="showClose" @click="closeVideo"><svg-icon icon-class="close" style="width:16px;height:16px;margin-right: 4;cursor: pointer;" /></div>
				<div v-if="showBlowUp" @click="blowUp"><svg-icon icon-class="blow-up" style="width:16px;height:16px;margin-right: 12;color: #fff;cursor: pointer;" /></div>
			</div>
		</div>
		<div :class="isBig ? 'bigBox' : 'alarm-ul'">
			<YsPlayer 
			:width="400"
			:height="229"
			:playerId="playerYsId"
			:deviceNo="checkedDevice[0].channel"
			:deviceData="checkedDevice[0]"
			ref="ysVideoRef"
			v-if="checkedDevice[0].source == '3'"/>
			<VideoPlayer :playerId="props.playerId" :data="checkedDevice" :show-close="false" :show-title="false" :show-blow-up= "showUp" v-else/>
		</div>
	</div>
</template>

<style lang="scss" scoped>
.title {
	display: inline-block;
	width: 180px;
}
.bigBox{
	height: 95.1%;
}
.big-title {
	height: 4%;
	max-height: 40px;
	background: #11253E;
	color: #fff;
	font-family: SourceHanSansSC-Bold;
	font-size: 14px;
	text-align: left;
	font-weight: 700;
	border-bottom: 1px solid #344054;
	padding-left: 8px;
	z-index: 999;
}
.alarm-title {
	height: 15.2%;
	line-height: 38px;
	max-height: 45px;
	background: #11253E;
	color: #fff;
	font-family: SourceHanSansSC-Bold;
	font-size: 14px;
	text-align: left;
	font-weight: 700;
	border-bottom: 1px solid #344054;
	padding-left: 8px;
	z-index: 999;
}
.alarm-ul {
	background: #001129;
	height: 84.8%;
	.alarm-item {
		min-height: 76px;
		background: #11253E;
		margin-bottom: 12px;
		padding: 8px;
		font-family: SourceHanSansSC-Regular;
		font-size: 14px;
		color: #F5F6F8;
		text-align: left;
		line-height: 22px;
		font-weight: 400;
		.alarm-time {
			font-family: SourceHanSansSC-Regular;
			font-size: 12px;
			color: #98A2B3;
			text-align: right;
			line-height: 20px;
			font-weight: 400;
		}
		.alarm-address{
			font-family: SourceHanSansSC-Regular;
			font-size: 14px;
			color: #FFFFFF;
			text-align: justify;
			line-height: 22px;
			font-weight: 400;
			margin-top: 8px;
			overflow: hidden;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			text-overflow: ellipsis;
			white-space: normal;
		}
	}
}
.flex {
	height: 15.2%;
	line-height: 38px;
	display: flex;
	justify-content: space-between;
}
</style>
