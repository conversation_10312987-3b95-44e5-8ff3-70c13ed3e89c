import * as Cesium from 'cesium';
import { polylinematerial } from '@/components/Cesium/libs/cesium//PolylineMaterialAppearance';

class Wayline {
  constructor(viewer, planJson) {
    this.viewer = viewer;
    this.planJson = planJson;
    this.globalHeight = 0;
    this.planPointJson = []; //地图上的航点列表
    this.waylineLyr = null; //航线图层
    //this.primitivesGroup = new Cesium.PrimitiveCollection(); // waylines的图元集合
  }
  createWayLine2() {}
  createCustomData() {
    const waylineLyrDataSource = this.viewer.dataSources.getByName('waylineLyr');
    if (waylineLyrDataSource.length === 0) {
      this.waylineLyr = new Cesium.CustomDataSource('waylineLyr');
      this.viewer.dataSources.add(this.waylineLyr);
    } else {
      this.waylineLyr = waylineLyrDataSource[0];
    }
  }
  removeCustomData() {
    if (this.waylineLyr !== null) {
      this.waylineLyr.entities.removeAll();
    }
    // if (this.primitivesGroup !== null) {
    this.planPointJson.forEach(item => {
      this.viewer.scene.primitives.remove(item.planLineEntity[0]);
    });
    // }
  }
  createWayLine() {
    this.removeCustomData();
    this.createCustomData();
    //1.创建起飞点
    const flyStr = this.planJson.template.wpml_missionConfig.wpml_takeOffRefPoint;
    const flyParm = flyStr.split(',');
    const lat = parseFloat(flyParm[0]);
    const lon = parseFloat(flyParm[1]);
    const height = parseFloat(flyParm[2]);
    this.globalHeight = parseFloat(this.planJson.template.Folder.wpml_globalHeight);

    this.setFlyPoint(
      lon,
      lat,
      height,
      this.planJson.template.wpml_missionConfig.wpml_flyToWaylineMode,
      this.planJson.template.wpml_missionConfig.wpml_takeOffSecurityHeight
    );

    //2.创建航点列表
    const placemarkArr = this.planJson.template.Folder.Placemark;
    const lonlats = [];
    for (let i = 0; i < placemarkArr.length; i++) {
      const placemark = placemarkArr[i];
      const point = placemark.Point.coordinates.split(',');
      const lon = parseFloat(point[0]);
      const lat = parseFloat(point[1]);
      let aslHeight = parseInt(placemark.wpml_height);
      if (this.planJson.template.Folder.wpml_waylineCoordinateSysParam.wpml_heightMode == 'EGM96') {
        aslHeight = parseInt(placemark.wpml_height);
      } else if (
        this.planJson.template.Folder.wpml_waylineCoordinateSysParam.wpml_heightMode == 'relativeToStartPoint'
      ) {
        aslHeight = parseInt(placemark.wpml_height) + parseFloat(flyParm[2]);
      }
      const pJson = {
        index: i,
        type: '航点',
        lon: lon,
        lat: lat,
        flow: true,
        startHeight: parseFloat(flyParm[2]), //起飞点海拔高度
        terrainHeight: 0, //地形高度
        globalHeight: parseInt(placemark.wpml_height),
        height: aslHeight, //航点的高度,
        UAVHPR: null, //方位
        position: null, //航点位置
        planLineEntity: null, //航线对象
        pointEntity: null, //航点对象
        lineEntity: null, //航点离地虚线
        dPointEntity: null, //航点地面点
        length: 0
      };
      this.planPointJson.push(pJson);
      lonlats.push([lon, lat]);
    }
    const that = this;
    this.getTerrainHeight(lonlats, function (updatedPositions) {
      if (updatedPositions != null) {
        for (let i = 0; i < updatedPositions.length; i++) {
          const position = updatedPositions[i];
          this.planPointJson[i + 1].terrainHeight = position.height;
        }
      }

      // 3.创建航点 航线到地图上
      that.setWayLineToMap();
    });
  }
  /**
   * 1.设置起飞点
   * @param {*} lon 起飞点经度
   * @param {*} lat 纬度
   * @param {*} height 海拔高度
   * @param {*} waylineMode 安全模式/倾斜模式
   * @param {*} addSecurityHeight 增加的安全高度
   * @returns
   */
  setFlyPoint(lon, lat, height, waylineMode, addSecurityHeight) {
    this.flyStartPoint = [lon, lat, height];

    //飞向第一航点时的高度
    let flyHeight = this.globalHeight;
    if (waylineMode === 'pointToPoint') {
      flyHeight = height + addSecurityHeight;
    } else {
    }
    let startLinePos = Cesium.Cartesian3.fromDegrees(lon, lat, flyHeight);
    var hpr = new Cesium.HeadingPitchRoll(Cesium.Math.toRadians(0), 0, 0);
    //起飞点图标
    // let startEntity = this.viewer.entities.add({
    let startEntity = this.waylineLyr.entities.add({
      id: '起飞点',
      name: '起飞点',
      position: Cesium.Cartesian3.fromDegrees(lon, lat, height),
      //图标
      billboard: {
        image: './resource/images/plan/起飞点.png',
        width: 32,
        height: 32
      }
    });
    //起飞点离无人机的线
    let color = Cesium.Color.YELLOW;
    let firstPos = Cesium.Cartesian3.fromDegrees(this.flyStartPoint[0], this.flyStartPoint[1], this.flyStartPoint[2]);
    let linePos = [firstPos, startLinePos];
    var colors = [];
    for (var i = 0; i < 40; ++i) {
      colors.push(Cesium.Color.fromRandom({ alpha: 1.0 }));
    }
    //起飞点到安全高度的线，特效线
    // let startLinePrimitive = this.viewer.scene.primitives.add(
    //   new Cesium.Primitive({
    //     id: '起飞点_line1',
    //     geometryInstances: new Cesium.GeometryInstance({
    //       geometry: new Cesium.PolylineGeometry({
    //         positions: linePos, //Cesium.Cartesian3.fromDegreesArrayHeights(linePos),
    //         width: 5.0,
    //         colors: colors
    //       })
    //     }),
    //     appearance: new Cesium.PolylineMaterialAppearance({
    //       material: polylinematerial.Polylineglowflow(Cesium.Color.YELLOW)
    //     })
    //   })
    // );
    //起飞点到安全高度的线，白线
    // let startLineEntity2 = this.viewer.entities.add({
    let startLineEntity2 = this.waylineLyr.entities.add({
      id: '起飞点_line2',
      polyline: {
        positions: linePos,
        width: 5,
        material: new Cesium.PolylineDashMaterialProperty({
          color: Cesium.Color.fromCssColorString('#40DBEF').withAlpha(0.6),
          dashLength: 20 //短划线长度
        })
      }
    });

    let json = {
      index: 0,
      type: '起飞点',
      lon: lon,
      lat: lat,
      flow: this.planPointFlow,
      waylineMode: waylineMode,
      startHeight: height, //起飞点海拔高度
      terrainHeight: height, //地形高度
      globalHeight: this.globalHeight,
      height: flyHeight,
      UAVHPR: hpr, //无人机方位
      position: startLinePos, //无人机往第一个航线点的位置
      planLineEntity: [null, startLineEntity2], //起飞路线
      pointEntity: startEntity, //起飞点
      lineEntity: null, //离地线
      dPointEntity: null, //航点地面点
      length: 0 //无人机与起飞点的距离
    };
    if (this.planPointJson.length == 0) {
      this.planPointJson.push(json);
    }
  }

  setWayLineToMap() {
    // let planPointOne = this.planPointJson[0];
    // planPointOne.position = new Cesium.Cartesian3.fromDegrees(planPointOne.lon, planPointOne.lat, planPointOne.height);
    // this.planPointJson[0].position = this.startLinePos;
    for (let i = 1; i < this.planPointJson.length; i++) {
      const planPoint = this.planPointJson[i];
      //先移除航点地图上的对象
      // if (planPoint.planLineEntity != null) {
      //   this.viewer.scene.primitives.remove(planPoint.planLineEntity[0]); //特效线是primitive
      //   this.viewer.entities.remove(planPoint.planLineEntity[1]);
      // }
      // if (planPoint.pointEntity != null) this.viewer.entities.remove(planPoint.pointEntity); //删除航点
      // if (planPoint.lineEntity != null) this.viewer.entities.remove(planPoint.lineEntity);
      // if (planPoint.dPointEntity != null) this.viewer.entities.remove(planPoint.dPointEntity);

      let height = planPoint.height;
      let gHeight = planPoint.globalHeight;
      let position = planPoint.position;
      // if (addHeight) {
      //   if (isAlladd == true || planPoint.flow == true) {
      //     height += addHeight;
      //     gHeight += addHeight;
      //   }
      // }

      position = new Cesium.Cartesian3.fromDegrees(planPoint.lon, planPoint.lat, height);

      //1、航点对象
      // let pointEntity = this.viewer.entities.add({
      let pointEntity = this.waylineLyr.entities.add({
        id: '航点' + i,
        position: position,
        label: {
          //   //文字标签
          text: '' + i,
          font: '12px sans-serif',
          fillColor: Cesium.Color.BLACK,
          style: Cesium.LabelStyle.FILL,
          verticalOrigin: Cesium.VerticalOrigin.CENTER,
          pixelOffset: new Cesium.Cartesian2(1, 0),
          showBackground: true,
          // backgroundColor: Cesium.Color.fromAlpha(Cesium.Color.LIME, 1),
          backgroundColor: Cesium.Color.fromCssColorString('#40DBEF').withAlpha(1),
          backgroundPadding: new Cesium.Cartesian2(3, 5),
          // disableDepthTestDistance: Number.POSITIVE_INFINITY
          scaleByDistance: new Cesium.NearFarScalar(300, 1.0, 1000, 0.5)
        },
        //图标
        billboard: {
          image: './resource/images/plan/placemark1.png',
          width: 32,
          height: 32,
          scaleByDistance: new Cesium.NearFarScalar(300, 1.0, 1000, 0.5)
          // disableDepthTestDistance: Number.POSITIVE_INFINITY
        }
      });

      const dPos = new Cesium.Cartesian3.fromDegrees(planPoint.lon, planPoint.lat, planPoint.terrainHeight);

      //航点离地面的虚线
      // let lineEntity = this.viewer.entities.add({
      let lineEntity = this.waylineLyr.entities.add({
        polyline: {
          positions: [position, dPos],
          width: 3,
          material: new Cesium.PolylineDashMaterialProperty({
            color: Cesium.Color.WHITE,
            dashLength: 10 //短划线长度
          })
        }
      });

      // 地面的点
      // let dPointEntity = this.viewer.entities.add({
      let dPointEntity = this.waylineLyr.entities.add({
        // 给初始点位设置一定的离地高度，否者会被压盖
        position: dPos,
        point: {
          color: Cesium.Color.WHITE,
          pixelSize: 8
        }
      });

      //2、航点连接线
      const linePos = [this.planPointJson[i - 1].position, position];
      const lines = this.addLine(i, linePos);

      //赋值
      planPoint.index = i - 1;

      planPoint.position = position;
      planPoint.planLineEntity = lines;
      planPoint.lineEntity = lineEntity;
      planPoint.pointEntity = pointEntity;
      planPoint.dPointEntity = dPointEntity;
    }
  }

  /**
   *
   * @param {*} planPointIndex
   * @param {*} linePos
   * @returns
   */
  addLine(planPointIndex, linePos) {
    //前一个点到后一个点的线，特效
    // const planLinePrimitive = this.viewer.scene.primitives.add(
    //   new Cesium.Primitive({
    //     id: '航点' + (planPointIndex + 1) + '_line1',
    //     geometryInstances: new Cesium.GeometryInstance({
    //       geometry: new Cesium.PolylineGeometry({
    //         positions: linePos,
    //         width: 5.0
    //       })
    //     }),
    //     appearance: new Cesium.PolylineMaterialAppearance({
    //       material: polylinematerial.Polylineglowflow(Cesium.Color.LAWNGREEN)
    //     })
    //   })
    // );
    // 添加该图元到组
    // this.primitivesGroup.add(planLinePrimitive);

    //前一个点到后一个点的线，白色
    // const planLineEntity2 = this.viewer.entities.add({
    const planLineEntity2 = this.waylineLyr.entities.add({
      id: '航点' + (planPointIndex + 1) + '_line2',
      polyline: {
        positions: linePos,
        width: 5,
        material: new Cesium.PolylineDashMaterialProperty({
          color: Cesium.Color.fromCssColorString('#40DBEF').withAlpha(0.7),
          dashLength: 20 //短划线长度
        })
      }
    });

    // return [planLinePrimitive, planLineEntity2];
    return [null, planLineEntity2];
  }

  /**
   * 获取点集合所在位置的地形高度
   * @param {*} positions C3坐标集合
   * @param {*} fun 回调函数，因为异步执行
   */
  getTerrainHeight(lonlats, fun) {
    let cartographics = [];
    lonlats.forEach(point => {
      const cartographic = Cesium.Cartographic.fromDegrees(point[0], point[1]);
      cartographics.push(cartographic);
    });
    try {
      const promise = Cesium.sampleTerrainMostDetailed(this.viewer.terrainProvider, cartographics);
      promise
        .then(function (updatedPositions) {
          if (fun != undefined && fun != null) fun(updatedPositions);
        })
        .catch(function (reason, data) {
          if (fun != undefined && fun != null) fun(null);
          console.log('获取高程数据失败', reason);
        });
    } catch (e) {
      console.log('获取高程数据失败', e);
      if (fun != undefined && fun != null) fun(null);
    }
  }
}
export default Wayline;
