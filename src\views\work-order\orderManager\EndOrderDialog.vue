<template>
  <el-dialog
    :title="title"
    v-if="visible"
    :model-value="visible"
    width="800px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form class="app-form" ref="dataFormRef" :model="form" :rules="rules" label-width="120px" >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="结束原因" prop="reason" required>
            <el-select v-model="form.reason" placeholder="请选择结束原因">
              <el-option
                v-for="item in userOptinos"
                :key="item.user_id"
                :label="item?.username"
                :value="item.user_id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-loading="loading">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { listUser } from '@/api/system/user';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  formData: {
    type: Object,
    default(rawProps) {
      return {};
    }
  }
});
const form = reactive({});
const dataFormRef = ref(ElForm);
const userOptinos = ref([]);

watch(
  () => props.formData,
  (newVal, oldVal) => {
    Object.assign(form, newVal);
  },
  { deep: true,immediate:true }
);
const emit = defineEmits(['update:visible', 'submit']);
const rules = reactive({
  reason: [{ required: true, message: '请选择结束原因', trigger: ['change','blur']}],
});

defineExpose({ setDefaultValue });

// 设置默认值
function setDefaultValue() {
  
}
const loading = ref(false);

// 关闭弹窗
function closeDialog() {
  resetForm();
  emit('update:visible', false);
}
/**
 * 重置表单
 */
function resetForm() {
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  loading.value = false;
  Object.keys(form).map(key => {
    delete form[key];
  });
}

function handleSubmit() {
   console.log('3=====',form)
    dataFormRef.value.validate(isValid => {
      if (isValid) {
      console.log('3=====',form)
      //   let params = {
      //     ...form,
        
      //   };
      //   loading.value = true;
      //   addAirTaskList(params)
      //     .then(res => {
      //       loading.value = false;
      //       ElMessage.success('新增成功');
      //       closeDialog();
      //       emit('submit');
      //     })
      //     .catch(e => {
      //       loading.value = false;
      //     });
      } else {
        loading.value = false;
      }
    });
}
//处置人
function getUser() {
  listUser({
    pageNo: 1,
    pageSize: 999
  }).then(res => {
    const { list = [] } = res;
    userOptinos.value = list;
  });
}

onMounted(() => {
  getUser();
});
</script>
<style scoped lang="scss">

:global(.el-loading-mask) {
  transform: opacity 0.9 !important;
  background-color: rgb(255 255 255 / 0.3);
}
.app-form {
  ::v-deep {
    .el-select {
      width: 100%;
    }
  }
}
.fileList {
  ::v-deep {
    // .el-upload {
    //   display: none !important;
    // }
    .el-upload-list__item:hover .el-icon--close {
      display: none !important;
    }
    .el-icon--close-tip {
      display: none !important
    }
  }
}
</style>
