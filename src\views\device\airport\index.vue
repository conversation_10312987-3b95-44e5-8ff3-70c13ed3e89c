<!--机场管理-->
<script>
export default {
  name: 'Airport'
};
</script>

<script setup>
import { reactive } from 'vue';
import EditDialog from './EditDialog.vue';
import DetailDialog from './DetailDialog.vue';
import BoundInfo from './BoundInfo.vue';
import DebuggerDialog from './DebuggerDialog.vue';
import AlarmListDailog from './components/AlarmListDailog.vue';
import optionData from '@/utils/option-data';
import { getDevicesBound, getDevicesBySn, deleteDevices, getDeviceModal } from '@/api/devices';
import { DOMAIN } from '@/utils/constants';
import { authorityShow } from '@/utils/authority';

const editDialogRef = ref(null);

const loading = ref(false);
const total = ref(0);
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  queryType: 2,
  keyWord: ''
});
const modalList = ref([]); //型号列表

const dataList = ref([]);

const dialog = reactive({
  visible: false
});
const editDialog = reactive({
  visible: false
});
const alarmDialog = reactive({
  visible: false
});

const debugDialog = reactive({
  visible: false
});
const boundDialog = reactive({
  visible: false
});

let formData = reactive({});
let debuggerFormData = reactive({});
/**
 * 查询
 */
function handleQuery() {
  getDevicesBound({
    ...queryParams,
    domain: DOMAIN.DOCK,
    page: queryParams.pageNum,
    page_size: queryParams.pageSize
  }).then(data => {
    const { list, pagination } = data;
    dataList.value = list || [];
    total.value = pagination.total;
  });
}
function handleSearch() {
  queryParams.pageNum = 1;
  handleQuery();
}
/**
 * 重置查询
 */
function resetQuery() {
  queryParams.nickname = '';
  (queryParams.device_name = ''), (queryParams.status = null);
  (queryParams.pageNum = 1), (queryParams.pageSize = 10), handleQuery();
}

/**
 * 打开表单弹窗
 */
function openDialog(row) {
  dialog.visible = true;
  Object.keys(formData).map(key => {
    delete formData[key];
  });
  if (row) {
    getDevicesBySn(row.device_sn).then(data => {
      dialog.title = '机场详情';
      Object.assign(formData, { ...data });
    });
  } else {
    dialog.title = '新增机场';
    nextTick(() => {
      editDialogRef.value.setDefaultValue();
    });
  }
}
/**
 * 打开表单弹窗
 */
function openEditDialog(row) {
  editDialog.visible = true;
  Object.keys(formData).map(key => {
    delete formData[key];
  });
  if (row) {
    editDialog.title = '编辑机场信息';
    Object.assign(formData, { ...row });
  }
}
// 打开告警信息
function openAlarmDialog(row) {
  alarmDialog.visible = true;
  Object.keys(formData).map(key => {
    delete formData[key];
  });
  if (row) {
    alarmDialog.title = '编辑信息';
    Object.assign(formData, { ...row });
  }
}

/**
 * 打开调试弹窗

 */
function openDebugDialog(row) {
  debugDialog.visible = true;
  Object.keys(debuggerFormData).map(key => {
    delete debuggerFormData[key];
  });
  if (row) {
    Object.assign(debuggerFormData, { ...row });
  }
}

function openBoundInfo() {
  boundDialog.visible = true;
}

/**
 * 删除
 */
function handleDelete(row) {
  ElMessageBox.confirm(`确定后将解绑【${row.nickname}】`, '确定解绑？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    autofocus: false,
    closeOnClickModal: false,
    type: 'warning'
  }).then(() => {
    deleteDevices(row.device_sn).then(data => {
      ElMessage.success('解绑成功');
      handleQuery();
    });
  });
}

function initModal() {
  getDeviceModal({
    domain: '3'
  }).then(res => {
    modalList.value = res;
  });
}

const handleSizeChange = val => {
  // Calculate the new total pages based on the new page size
  const newTotalPages = Math.ceil(total.value / val);
  
  // If current page is greater than new total pages, adjust to the last page
  if (queryParams.pageNum > newTotalPages) {
    queryParams.pageNum = newTotalPages || 1;
  }
  queryParams.pageSize = val;
  handleQuery({ ...queryParams });
};

const handleCurrentChange = val => {
  queryParams.pageNum = val;
  handleQuery({ ...queryParams });
};

onMounted(() => {
  initModal();
  handleQuery();
});
</script>
<style lang="scss" scoped>
.input-serach {
  width: 200px;
}
</style>
<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
          <el-form-item label="" prop="nickname">
            <el-input
              class="input-serach"
              v-model="queryParams.nickname"
              placeholder="请输入机场名称"
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
              @blur="queryParams.nickname = $event.target.value.trim()"
            />
          </el-form-item>
          <el-form-item label="" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择机场状态" clearable @change="handleSearch">
              <el-option
                v-for="item in optionData.statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="device_name">
            <el-select v-model="queryParams.device_name" placeholder="请选择机场型号" clearable @change="handleSearch">
              <el-option
                v-for="item in modalList"
                :key="item.device_name"
                :label="item.device_name"
                :value="item.device_name"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="search-btn">
        <el-button type="primary" @click="handleSearch()"><i-ep-search />搜索</el-button>
        <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
      </div>
    </div>
    <el-card shadow="never">
      <div style="margin: 15px 0" v-if="authorityShow('bindAirport')">
        <el-button type="primary" @click="openBoundInfo()">设备绑定码</el-button>
      </div>
      <el-table highlight-current-row :data="dataList" v-loading="loading" stripe height="580">
        <el-table-column label="序号" width="80">
          <template #default="scope">
            <span>{{ scope.$index + 1 + queryParams.pageSize * (queryParams.pageNum - 1) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="组织名称" prop="dept_name" show-overflow-tooltip />
        <el-table-column label="机场型号" prop="device_name" show-overflow-tooltip />
        <el-table-column label="设备SN" prop="device_sn" show-overflow-tooltip />

        <el-table-column label="机场名称" prop="nickname" show-overflow-tooltip />
        <el-table-column label="固件版本" prop="firmware_version" show-overflow-tooltip />

        <el-table-column prop="status" label="无人机名称" width="300" show-overflow-tooltip>
          <template #default="scope">
            <div class="flex-center">
              <span>{{ scope.row.children ? scope.row.children?.nickname : '无' }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="无人机型号" width="130px" show-overflow-tooltip>
          <template #default="scope">
            <div class="flex-center">
              <span>{{ scope.row.children ? scope.row.children.device_name : '无' }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="机场状态" show-overflow-tooltip>
          <template #default="scope">
            <span :class="scope.row.status ? 'online' : 'outline'">{{ scope.row.status ? '在线' : '离线' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="机场覆盖范围" prop="max_flight_distance" show-overflow-tooltip>
          <template #default="scope">
            <div>{{ scope.row.max_flight_distance }}米</div>
          </template>
        </el-table-column>
        <el-table-column
          label="备降点经纬度"
          width="180"
          prop="alternate_land_point_coordinate"
          show-overflow-tooltip
        />

        <!-- <el-table-column label="机场地址" prop="address" width="200" show-overflow-tooltip /> -->

        <el-table-column
          fixed="right"
          label="操作"
          width="250"
          v-if="
            authorityShow('editAirport') ||
            authorityShow('hmsAirport') ||
            authorityShow('debugAirport') ||
            authorityShow('unLockAirport')
          "
        >
          <template #default="scope">
            <el-button type="primary" link @click.stop="openEditDialog(scope.row)" v-if="authorityShow('editAirport')"
              >编辑</el-button
            >
            <!-- <el-button type="primary" link @click.stop="openDialog(scope.row)">详情</el-button> -->
            <el-button type="primary" link @click.stop="openAlarmDialog(scope.row)" v-if="authorityShow('hmsAirport')"
              >告警</el-button
            >
            <el-button type="primary" link @click.stop="openDebugDialog(scope.row)" v-if="authorityShow('debugAirport')"
              >调试</el-button
            >
            <el-button type="danger" link @click.stop="handleDelete(scope.row)" v-if="authorityShow('unLockAirport')"
              >解绑</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-content">
        <el-pagination
          v-if="total > 0"
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :background="true"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <EditDialog
      v-model:visible="editDialog.visible"
      :title="editDialog.title"
      :form-data="formData"
      @submit="resetQuery"
    />
    <DetailDialog v-model:visible="dialog.visible" :form-data="formData" />
    <BoundInfo v-if="boundDialog.visible" v-model:visible="boundDialog.visible" :form-data="formData" />
    <DebuggerDialog v-model:visible="debugDialog.visible" :form-data="debuggerFormData" />
    <AlarmListDailog v-model:visible="alarmDialog.visible" :form-data="formData" :domain="DOMAIN.DOCK" />
  </div>
</template>
<style scoped lang="scss">
.pagination-content {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;

  &.hidden {
    display: none;
  }
}
.flex-center {
  display: flex;
  align-items: center;
  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #4caf51;
  }
  .unstatus {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: red;
  }
}
.online,
.outline {
  display: inline-block;
  width: 45px;
  height: 24px;
  line-height: 24px;
  border-radius: 2px;
  font-family: SourceHanSansSC-Regular;
  font-size: 12px;
  color: rgb(57, 191, 164);
  text-align: center;
  font-weight: 400;
  background: rgba(42, 139, 125, 0.3);
  outline-style: none;
}
.outline {
  color: rgb(152, 162, 179);
  background: rgba(152, 162, 179, 0.3);
}
.search {
  display: flex;
  align-items: center;
  padding: 24px;
  height: 64px;
  .search-form {
    flex: 1;
    padding-top: 16px;
  }
}
</style>
