import { createRouter, createWebHistory } from 'vue-router';

export const Layout = () => import('@/layout/index.vue');
export const homePageLayout = () => import('@/layout/homePage.vue');
export const backstageLayout = () => import('@/layout/backstagePage.vue');
export const homeViewLayout = () => import('@/layout/homeViewPage.vue');

// 静态路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    meta: { hidden: true },
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue')
      }
    ]
  },
  {
    path: '/',
    redirect: '/pilot-login',
    children: [
      {
        path: 'homePage',
        component: () => import('@/views/device/pilot-login/index.vue'),
        name: 'home',
        meta: { hidden: true }
      },
    ]
  },
  {
    path: '/pilot-login',
    component: () => import('@/views/device/pilot-login/index.vue'),
    meta: { hidden: true }
  },
  {
    path: '/pilot-media',
    component: () => import('@/views/device/pilot-login/components/pilot-media.vue'),
    meta: { hidden: true }
  },
  {
    path: '/pilot-home',
    component: () => import('@/views/device/pilot-login/components/pilot-home.vue'),
    meta: { hidden: true }
  },
  {
    path: '/pilot-bind',
    component: () => import('@/views/device/pilot-login/components/pilot-bind.vue'),
    meta: { hidden: true }
  },
  {
    path: '/pilot-liveshare',
    component: () => import('@/views/device/pilot-login/components/pilot-liveshare.vue'),
    meta: { hidden: true }
  },
];

/**
 * 创建路由
 */
const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  // 刷新时，滚动条位置还原
  scrollBehavior: () => ({ left: 0, top: 0 })
});

/**
 * 重置路由
 */
export function resetRouter() {
  router.replace({ path: '/pilot-login' });
  window.location.reload();
}

export default router;
