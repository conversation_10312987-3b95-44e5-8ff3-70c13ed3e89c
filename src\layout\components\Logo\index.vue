<script setup>
import logo from '@/assets/sx_logo2.png';

import { useSettingsStore } from '@/store/modules/settings.js';

const settingsStore = useSettingsStore();

const isProductionByEn = computed(() => {
  return import.meta.env.VITE_APP_NODE_ENV === 'productionByEn';
});

defineProps({
  collapse: {
    type: Boolean,
    required: true
  }
});
</script>

<template>
  <div class="logo-area w-full h-[60px]">
    <transition name="sidebarLogoFade">
      <router-link
        v-if="collapse"
        key="collapse"
        class="h-full w-full flex items-center justify-center"
        to="/"
      >
        <img
          v-if="settingsStore.sidebarLogo"
          iconClass="logo"
          class="icon-collapse"
          :src="logo"
        />
      </router-link>
      <router-link
        v-else
        key="expand"
        class="h-full w-full flex items-center justify-center"
        to="/"
      >
        <img
          v-if="settingsStore.sidebarLogo"
          iconClass="logo"
          class="icon-expand"
          :class="{
            'en-icon-expand': $i18n.locale !== 'zh' || isProductionByEn
          }"
          :src="logo"
        />
        <span class="plateform ml-3 text-white text-2xl text-sm">
          信鸽无人机实战平台
        </span>
      </router-link>
    </transition>
  </div>
</template>

<style lang="scss" scoped>
.logo-area {
  background-color: #3f5bfa;
}
.sidebarLogoFade-enter-active {
  transition: opacity 2s;
}

.sidebarLogoFade-leave-active,
.sidebarLogoFade-enter-from,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.icon-vertical {
  width: 3rem !important;
  height: auto !important;
}

.icon-expand {
  width: 3.3rem !important;
  height: auto !important;
}

.icon-horizon {
  width: 8rem !important;
  height: auto !important;
}

.en-icon-expand {
  width: 2.6rem !important;
  height: auto !important;
}

.icon-collapse {
  width: 2.5rem !important;
  height: auto !important;
}

.plateform {
  font-family: AliMedium;
}
</style>
