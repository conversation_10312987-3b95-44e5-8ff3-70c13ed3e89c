<template>
  <div>
    <div class="upload-top mb-4">
      <div class="el-upload__tip inline-flex ml-3">
        {{ props.uploadTips }}
      </div>
    </div>
    <el-upload
      class="upload-area"
      ref="uploadImgRef"
      action="#"
      list-type="picture-card"
      accept="image/*"
      v-bind="$attrs"
      :drag="true"
    >
      <el-button class="upload-btn" type="primary" :icon="Upload">
        {{ props.btnText }}
      </el-button>
      <template #trigger>
        <div class="upload-tip-box">
          <i-ep-uploadFilled class="el-icon-upload" />
          <div class="el-upload__text">
            {{ props.uploadText }}
          </div>
        </div>
      </template>
    </el-upload>
    <ImgPreview v-model="_showImg" :imgs="[previewImg]" />
  </div>
</template>
<script>
export default {
  name: 'img-upload'
};
</script>

<script setup>
import { Upload } from '@element-plus/icons-vue';
import { watch, toRefs } from 'vue';

const uploadImgRef = ref('uploadImgRef');
const props = defineProps({
  uploadTips: {
    type: String,
    default: '上传提示'
  },
  uploadText: {
    type: String,
    default: '将图片拖到此处'
  },
  btnText: {
    type: String,
    default: '上传图片'
  },
  previewImg: {
    type: String,
    default: ''
  }
});

const _showImg = ref(false);

const { previewImg } = toRefs(props);
watch(previewImg, newVal => {
  if (newVal) {
    _showImg.value = true;
  }
});

defineExpose({
  uploadImgRef
});
</script>

<style lang="scss" scoped>
.upload-area {
  width: 400px;
  position: relative;
}

.upload-top {
  display: flex;
}

.upload-btn {
  position: absolute;
  padding: 8px 8px;
  top: -40px;
  left: 0;
}

:deep(.el-upload--picture-card) {
  --el-upload-picture-card-size: 94px !important;
}

:deep(.el-upload-list--picture-card) {
  --el-upload-list-picture-card-size: 94px !important;
}

:deep(.el-upload-list__item) {
  &:nth-child(4n) {
    margin-right: 0;
  }
}

:deep(.el-upload__text) {
  font-size: 12px;
}

:deep(.el-upload-dragger) {
  padding: 6px 4px;
}

.el-icon-upload {
  color: #b7d9fd;
  font-size: 40px;
}

.el-upload__tip {
  padding-left: 90px;
}
</style>
