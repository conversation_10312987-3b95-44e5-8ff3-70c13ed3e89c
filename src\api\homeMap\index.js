import request from '@/utils/request';
import { LINE_PATH, MAP_PATH, API_VERSION, APPLICTION_WORKSPACES, APPLICTION_LAYER } from '../config/index';
import { useUserStoreHook } from '@/store/modules/user';

// 警情相关图层的主路径
const BASE_LAYER_URL = MAP_PATH + API_VERSION + APPLICTION_LAYER;

// 获取正在执行的任务的主路径
const BASE_JOB_URL = LINE_PATH + API_VERSION + APPLICTION_WORKSPACES;

/**
 * 根据警情ID获取警情相关信息
 */
export function getInfoByAlarmId(data) {
  return request({
    url: `${BASE_LAYER_URL}/getInfoByAlarmId`,
    method: 'post',
    data
  });
}

/**
 * 消防车定位
 */
export function getCarLocation(data) {
  return request({
    url: `${BASE_LAYER_URL}/carLocation`,
    method: 'post',
    data
  });
}

/**
 * 获取正在执行的任务

 *
 * @param queryParams 查询参数 机场SN码
 * @returns 
 * job_id 任务ID
 * job_name 任务名称
 * alarm_id 警情ID
 * alarm_name 警情内容
 * file_id 航线ID
 * file_name 航线名称
 * airline_json 航线JSON
 * wayline_type 航线类型
 */
export function getRunningJob(dock_sn) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_JOB_URL}/${workspace_id}/running/jobs/${dock_sn}`,
    method: 'get'
  });
}
