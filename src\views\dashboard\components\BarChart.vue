<!-- 柱图 -->
<template>
  <div class="pd16">
    <el-empty v-if="top5X.length == 0" description="暂无数据">
      <template #image>
        <img src="../../../assets/empty_home.png">
      </template>
    </el-empty>
    <div v-else :id="id" :type="type" :class="className" :style="{ height, width }" />
  </div>
</template>

<script setup>
import * as echarts from 'echarts';
import { onMounted } from 'vue';
import { getPilotTop5, getPilotDistanceTop5 } from '@/api/dashboard';

const props = defineProps({
  id: {
    type: String,
    default: 'barChart'
  },
  type: {
    type: String,
    default: 'Num' //Num展示飞行次数排行  Distance展示距离排行
  },
  className: {
    type: String,
    default: ''
  },
  width: {
    type: String,
    default: '200px',
    required: true
  },
  height: {
    type: String,
    default: '200px',
    required: true
  }
});
const top5X= ref([]);
const top5Y= ref([]);
const name = ref('')
const options = {
  grid: {
    left: '5%', // 左侧留白
    right: '3%', // 右侧留白
    top: '8%', // 顶部留白
    bottom: '20%' // 底部留白
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    }
  },
  xAxis: [
    {
      type: 'value',
      min: 0,
      offset: 10,
      splitLine: {
        lineStyle: {
          type: 'dashed', // 将 Y 轴的分割线设置为虚线
          color: '#2E90FA'
        },
      },
      axisLabel: {
        formatter: '{value} '
      },
    }
  ],
  axisLabel: {
    color: '#B7D9FD', //坐标轴刻度文字的颜色
    fontSize: 12, //坐标轴刻度文字的大小         (用数字表示)
    fontWeight: 'lighter', //坐标轴刻度文字的加粗程度    (可选bold   bolder  lighter  normal)
    fontstyle: 'normal' //坐标轴刻度文字的样式          (可选normal  italic   oblique)
  },
  yAxis: [
    {
      type: 'category',
      data: top5X.value,
      boundaryGap: false,
      axisLabel: {
        padding: [0, 0, 20, 0] // 调整数值与纵轴的距离
      }
    }
  ],
  series: [
    {
      name: name,
      type: 'bar',
      data: top5Y.value,
      barWidth: 16,
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#2E90FA' },
          { offset: 1, color: '#2E90FA' }
        ])
      }
    }
  ]
};

// 监听 props 的变化
watch(
  () => props.type,
  (newVal, oldVal) => {
    if(newVal == 'Distance') {
      initDistanceTop();
    }else {
      initTop();
    }
    console.log('props.message changed from', oldVal, 'to', newVal);
  }
);
       
function initDistanceTop() {
  getPilotDistanceTop5({}).then(res=>{
    res.sort((a,b)=>a.total_distance_km-b.total_distance_km)
    top5X.value = res.map(item=>item.nickname)
    top5Y.value = res.map(item=>item.total_distance_km)
    options.series[0].data = top5Y.value ? top5Y.value : [];
    options.series[0].name = '飞行距离';
    options.yAxis[0].data = top5X.value ? top5X.value : []
    if(res.length >0 ) {
      setTimeout(()=>{
        const chart = echarts.init(document.getElementById(props.id));
        chart.setOption(options);
      },500)
    }
  })
};

function initTop () {
  getPilotTop5({}).then(res=>{
    res.sort((a,b)=>a.flight_count-b.flight_count)
    top5X.value = res.map(item=>item.nickname)
    top5Y.value = res.map(item=>item.flight_count)
    options.series[0].data = top5Y.value ? top5Y.value : [];
    options.series[0].name = '飞行次数';
    options.yAxis[0].data = top5X.value ? top5X.value : []
    if(res.length >0 ) {
      setTimeout(()=>{
        const chart = echarts.init(document.getElementById(props.id));
        chart.setOption(options);
      },500)
    }
  })
}
onMounted(() => {
  // 图表初始化
  initTop();
  // 大小自适应
  // window.addEventListener('resize', () => {
  //   chart.resize();
  // });
});
</script>
<style scoped>
.pd16 {
  padding: 16px;
}
</style>