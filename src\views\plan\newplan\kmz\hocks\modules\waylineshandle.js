import { getWayPointTemplateData } from '../../props';
import { Action, ActionGroup, PlaceMark, Wayline } from '../../waylines';
import * as Cesium from 'cesium';
import { useWayPointStore } from '@/store/modules/wayPointInfo.js';
import { usePlanInfoStore } from '@/store/modules/planInfo.js';
import {
  ACTION_ACTUATOR_FUNC,
  ACTION_TRIGGER_TYPE,
  ACTION_ACTUATOR_FUNC_ICON,
  ACTION_ACTUATOR_FUNC_NAME,
  ACTION_CONVERT_TRIGGER_TYPE_TO_FUNC,
  OPERATION_TYPE
} from '@/utils/constants';
import { actionCreateFuncMap } from '../actions';
import { ElMessage } from 'element-plus';
import { generateKey } from '@/utils';
import { getActionInfo } from './actionHandle';
import { isMultpleAction, reBuildMultipActionEndIndex } from './multipleActionHandle';
import { getPlanInstance, zoomtoLonglats } from '@/components/Cesium/libs/cesium';
import { calculateFOV, createFrustum, getFrustum } from './actionFrustumHandle';
// 一条航线一个对象
let waylinesXpmlObj = null;
// 存储placemark 集合
let wayPointsPlacemarkMap = null; // PlaceMark 的map 集合
// 当前被选中的 placemark 选项
let currentPlaceMark = null;
// 存储 placemark 转后的数据给到组件使用
let wayPointsMap = null;
// 需要被补充的动作及动作组
let stopMultipleActionList = [];
//#region 设置地图plan对象用于控制动作参数与地图交互
// 计划对象 控制与地图交互方案
let planObject = null;
/**
 * 这里设置plan给到组件使用
 * @param {*} options
 */
export function wayLineHandleSetPlan(plan) {
  planObject = plan || null;
}
/**
 * 获取plan对象供给给其他vue组件使用
 * @returns
 */
export function wayLineHandleGetPlan() {
  return planObject;
}

//#endregion

//#region 航线相关方法
/**
 * 初始化这里触发是在，点击参考起点触发在点击地图后触发以下方法
 */
export function inttWayline() {
  try {
    waylinesXpmlObj = new Wayline();
  } catch (error) {
    throw new Error('初始化 waylinesXpmlObj 失败');
  }
  // 初始化 Map 对象，用于存储 WayPoint 列表和 placemark 数据
  wayPointsMap = new Map();
  wayPointsPlacemarkMap = new Map();
}

/**
 * 通过地图设置点后执行的方法 执行内容
 * 1、转换 Placemark 数据
 * 2、给组件添加当前点数据
 * @param {*} placemarkJosn
 * @returns
 */
export function addWayPoint(placemarkJosn) {
  // 创建给展示组件使用数据
  const data = convertToWayPointList(placemarkJosn);
  const wayPointStore = useWayPointStore();
  // 数组列表添加测点信息
  wayPointStore.addPoint(data);
  wayPointsMap.set(data.pointId, data);
  // 创建 placemark 对象
  const placemarkOptions = {
    coordinates: `${data.longitude},${data.latitude}`,
    index: data.uuid, //data.index,
    executeHeight: data.executeHeight,
    waypointSpeed: data.waypointSpeed,
    waypointHeadingParam: data.waypointHeadingParam,
    waypointTurnParam: data.waypointTurnParam,
    useStraightLine: data.useStraightLine,
    uuid: data.uuid
  };
  const placemark = new PlaceMark(placemarkOptions);
  const folder = waylinesXpmlObj.getFolder(0);
  folder?.addPlacemark(placemark);
  currentPlaceMark = placemark;
  return data;
}

/********tmj 20240425 *********/
/**
 * 基于航线文件JSON添加航点，用于初始化
 * @param {*} placemark
 * @returns
 */
export function addPlacemark(placemark) {
  if (!waylinesXpmlObj) {
    return;
  }
  const folder = waylinesXpmlObj.getFolder(0) || null;
  if (!folder) {
    return;
  }
  // 创建 placemark 对象
  const placemarkOptions = {
    coordinates: placemark.Point.coordinates,
    index: placemark.wpml_index,
    executeHeight: placemark.wpml_executeHeight,
    waypointSpeed: placemark.wpml_waypointSpeed,
    waypointHeadingParam: placemark.wpml_waypointHeadingParam,
    waypointTurnParam: placemark.wpml_waypointTurnParam,
    useStraightLine: placemark.wpml_useStraightLine,
    uuid: placemark.uuid || generateKey()
  };
  const pk = new PlaceMark(placemarkOptions);
  folder?.addPlacemark(pk);
  // 设置动作组和动作 这里需要注意间隔动作构建方式与普通动作和动作组不同
  (placemark.wpml_actionGroup || []).forEach(actiongroup => {
    const groupOptions = {
      wpml_actionGroupId: actiongroup.wpml_actionGroupId,
      wpml_actionGroupStartIndex: actiongroup.wpml_actionGroupStartIndex,
      wpml_actionGroupEndIndex: actiongroup.wpml_actionGroupEndIndex,
      wpml_actionGroupMode: actiongroup.wpml_actionGroupMode,
      wpml_actionTriggerType: actiongroup.wpml_actionTrigger.wpml_actionTriggerType,
      wpml_actionTriggerParam: actiongroup.wpml_actionTrigger.wpml_actionTriggerParam
    };
    // 这里根据 动作组信息构建不同的动作组
    const curActionGroup = createActionGroup(placemark, groupOptions);
    let curWpml_ActionTriggerType = curActionGroup.wpml_actionTrigger.wpml_actionTriggerType;
    // 检查是否是间隔动作 如果是间隔动作需要记录当下的 actiongroup.wpml_actionGroupEndIndex
    //  作为  placemark.wpml_index 查找的一个索引 在尾巴地方插入 停止间隔动作 （在动作组最后插入）
    let isMultple =
      curWpml_ActionTriggerType === ACTION_TRIGGER_TYPE.multipleTiming ||
      curWpml_ActionTriggerType === ACTION_TRIGGER_TYPE.multipleDistance;
    (actiongroup?.wpml_action || []).forEach((action, index) => {
      let actionOptions = null;
      let actionUuid = generateKey();
      // 根据组的触发方式构建不同的动作 比如 间隔动作
      if (action.wpml_actionActuatorFunc === ACTION_ACTUATOR_FUNC.takePhoto && isMultple) {
        // 创建特殊动作 间隔动作
        actionOptions = {
          actionId: index,
          actionActuatorFunc: ACTION_CONVERT_TRIGGER_TYPE_TO_FUNC[curWpml_ActionTriggerType],
          actionActuatorFuncParam: action.wpml_actionActuatorFuncParam,
          uuid: actionUuid,
          type: curWpml_ActionTriggerType
        };
        addStopActinIndex(groupOptions.wpml_actionGroupEndIndex);
      } else {
        actionOptions = {
          actionId: index,
          actionActuatorFunc: action.wpml_actionActuatorFunc,
          actionActuatorFuncParam: action.wpml_actionActuatorFuncParam,
          uuid: actionUuid
        };
      }
      actionOptions && curActionGroup.addAction(new Action(actionOptions));
    });

    // 将组添加到航点中
    pk.addActionGroup(curActionGroup);
  });
  return pk;
}
function addStopActinIndex(i) {
  // 先检查是否存在 i 不存在才添加
  if (stopMultipleActionList.indexOf(i) === -1) {
    stopMultipleActionList.push(i);
  }
}
// 补充动作组和动作
export function complementActionGroup() {
  const planInfoStore = usePlanInfoStore();
  let json = planInfoStore.getCurPlanData();
  // import_type： 创建类型（0：平台创建，1：其他平台导入，2：遥控器同步）   0 都为创建，1、2为导入
  if (json && json.import_type === 0) {
    console.log('当前平台创建 不做处理');
    return;
  }
  const folder = waylinesXpmlObj.getFolder(0) || null;
  if (!folder) {
    return;
  }
  // 这里导入的也有自己平台导出的再导入 需要区分
  (stopMultipleActionList || []).forEach(stopIndex => {
    // 检查 placemarks 数组 是否存在 该索引
    const currentPlaceMark = (folder.placemark || []).find(placemark => placemark.wpml_index === stopIndex);
    if (!currentPlaceMark) {
      return;
    }
    // 检查该 currentPlaceMark 中的动作组中是否存在间隔停止动作
    const curActionGroups = currentPlaceMark?.getActionGroup() ?? [];
    const hasG = curActionGroups.find(actiongroup => {
      return (actiongroup.wpml_action || []).find(action => {
        return action.wpml_actionActuatorFunc === ACTION_ACTUATOR_FUNC.stopIntervalTakePhoto;
      });
    });

    if (hasG) {
      return;
    }
    const actionFuncObj = actionCreateFuncMap[ACTION_ACTUATOR_FUNC.stopIntervalTakePhoto];
    const { actionGroup = null } = actionFuncObj?.createFunc({
      placemark: currentPlaceMark
    });
    currentPlaceMark?.addActionGroup(actionGroup);
  });
  // 清空
  stopMultipleActionList = [];
}

/**
 * placemark数据转换为waypointlist组件展示数据
 * @param {*} placemark
 * @returns
 */
export function convertToWayPointList(placemark) {
  // 创建给展示组件使用数据
  let data = getWayPointTemplateData();
  data.placemarkIndex = placemark.wpml_index;
  data.pointId = placemark.wpml_index;
  data.index = placemark.wpml_index;
  const [longitude, latitude] = placemark.Point.coordinates.split(',');
  data.longitude = longitude;
  data.latitude = latitude;
  data.actionGroup = [];
  data.uuid = placemark.uuid || generateKey();
  return data;
}

/**
 * 将placemark数据转换为展示组件需要的数据
 * @param {*} placemarkArr
 * @returns
 */
export function convertPlacemarksToWayPointList(placemarkArr) {
  const list = [];
  if (!placemarkArr) {
    return list;
  }
  placemarkArr.forEach(placemark => {
    let data = {
      ...getWayPointTemplateData(),
      placemarkIndex: placemark.wpml_index,
      pointId: placemark.wpml_index,
      index: placemark.wpml_index,
      uuid: placemark.uuid || generateKey(),
      longitude: placemark.Point.coordinates.split(',')[0],
      latitude: placemark.Point.coordinates.split(',')[1],
      actionGroup: [],
      action: []
    };
    // 循环组对组进行重新构建  如果是普通组
    if (placemark.wpml_actionGroup && placemark.wpml_actionGroup.length > 0) {
      //actiongroups 执行循环遍历获取各组内的动作
      (placemark?.wpml_actionGroup ?? []).forEach(actiongroup => {
        (actiongroup?.wpml_action ?? []).forEach((action, i) => {
          let actionActuatorFunc = action.wpml_actionActuatorFunc;
          // 过滤掉最新的动作组
          if (actionActuatorFunc === ACTION_ACTUATOR_FUNC.focus) {
            return;
          }
          let acUuid = action.uuid || generateKey();
          let actiongroupUuid = actiongroup.uuid || generateKey();
          const wpml_actionTriggerType =
            actiongroup.wpml_actionTrigger.wpml_actionTriggerType || ACTION_TRIGGER_TYPE.default;
          // 根据 wpml_actionTriggerType 和 动作名称 获取具体的动作名称
          const { title = null, icon = null } = getActionInfo(wpml_actionTriggerType, action.wpml_actionActuatorFunc);
          // 添加个触发类型 用于区分是否是特殊的动作类型
          data.action.push({
            key: acUuid,
            title: title,
            url: icon,
            actionGroupId: actiongroup.wpml_actionGroupId,
            actionIndex: i,
            actionTriggerType: wpml_actionTriggerType,
            actionActuatorFunc: action.wpml_actionActuatorFunc,
            actionId: acUuid || i,
            // 新增属性
            actionGroupUuid: actiongroupUuid,
            actionUuid: acUuid,
            action
          });
        });
      });
    }
    list.push(data);
  });
  return list;
}

/**
 * 将placemark数据转换为展示组件需要的数据
 * @param {*} placemarkArr
 * @returns
 */
export function convertPlacemarksToWayPointList_2(placemarkArr) {
  const list = [];
  if (!placemarkArr) {
    return list;
  }
  placemarkArr.forEach(placemark => {
    let data = getWayPointTemplateData();
    data.placemarkIndex = placemark.wpml_index;
    data.pointId = placemark.wpml_index;
    data.index = placemark.wpml_index; // 航点索引 placemark.wpml_index;
    // 新增属性
    data.uuid = placemark.uuid || generateKey();
    const [longitude, latitude] = placemark.Point.coordinates.split(',');
    data.longitude = longitude;
    data.latitude = latitude;
    data.actionGroup = [];
    data.action = [];
    if (placemark.wpml_actionGroup && placemark.wpml_actionGroup.length > 0) {
      const actiongroups = placemark.wpml_actionGroup || [];
      //actiongroups 执行循环遍历获取各组内的动作
      actiongroups.forEach(actiongroup => {
        actiongroup?.wpml_action?.forEach((action, i) => {
          let acUuid = action.uuid || generateKey();
          let actiongroupUuid = actiongroup.uuid || generateKey();
          data.action.push({
            actionGroupId: actiongroup.wpml_actionGroupId,
            actionIndex: i,
            actionActuatorFunc: action.wpml_actionActuatorFunc,
            actionId: acUuid || i,
            // 新增属性
            actionGroupUuid: actiongroupUuid,
            actionUuid: acUuid
          });
        });
      });
    }
    list.push(data);
  });
  return list;
}

// /**
//  * 创建wayline.wpml文件所需对象
//  * @returns
//  */
// function createWaylinesWpmlObject() {
//   return new Wayline();
// }

/**
 * 获取wayline.wpml文件对象
 * @returns
 */
export function getWaylinesWpmlObject() {
  if (!waylinesXpmlObj) {
    return new Wayline();
  } else {
    return waylinesXpmlObj;
  }
}

/**
 * 销毁wayline.wpml文件对象
 */
export function destoryWayline() {
  waylinesXpmlObj && waylinesXpmlObj.destroy();
  waylinesXpmlObj = null;
  stopMultipleActionList = [];
}

//#region 航点相关信息

/**
 * 获取航点的map集合
 * @returns
 */
export function getWayPointsPlacemarkMap() {
  const folder = waylinesXpmlObj.getFolder(0);
  return folder?.getPlacemarkMap();
}

/**
 * 设置当前选中WayPointList航点列表中
 * @param {*} pointOptions
 * @param {number} pointOptions.index 点索引号
 * 如果是0 pointOptions 可以不传参
 * @returns
 */
export function setCurrentPlaceMark(pointOptions) {
  const folder = waylinesXpmlObj.getFolder(0);
  if (!pointOptions) {
    const { placemark } = folder?.getMinPlacemark();
    currentPlaceMark = placemark;
    return currentPlaceMark;
  }
  const { index = 0 } = pointOptions;
  if (index === 0) {
    const { placemark } = folder?.getMinPlacemark();
    currentPlaceMark = placemark;
  } else {
    currentPlaceMark = folder?.findPlacemark({ wpml_index: index });
  }
  return currentPlaceMark || null;
}

/**
 * 获取当前选中WayPointList航点列表中的航电对应的 PlaceMark 对象
 * @returns PlaceMark
 */
// export function getCurrentPlaceMark() {
//   if (!currentPlaceMark) {
//     currentPlaceMark = setCurrentPlaceMark({ index: 0 });
//   }
//   return currentPlaceMark || null;
// }

export function getCurrentPlaceMark() {
  if (!currentPlaceMark) {
    currentPlaceMark = setCurrentPlaceMark({ index: 0 });
    return {
      currentPlaceMark,
      index: 0
    };
  }
  const folder = waylinesXpmlObj.getFolder(0);
  const placemarks = folder.placemark;
  for (let i = 0; i < placemarks.length; i++) {
    const placeMark = placemarks[i];
    if (placeMark.uuid === currentPlaceMark.uuid) {
      return {
        currentPlaceMark,
        index: i
      };
    }
  }
}

export function getPointJsonPlaceMark(placemark = null) {
  if (!placemark) {
    return null;
  }
  //
  return currentPlaceMark || null;
}

/**
 * 移除placemark
 * @param {*} deleteOptions  移除参数
 * @returns
 */
export function deletePlaceMark(deleteOptions) {
  try {
    if (!deleteOptions) {
      return null;
    }
    const { point } = deleteOptions;
    const placemarkIndex = point.placemarkIndex; //所属的placemark
    if (placemarkIndex === null || placemarkIndex === undefined || !point) {
      return null;
    }
    const folder = waylinesXpmlObj.getFolder(0);
    // 执行移除
    folder?.deletePlacemarkByIndex(placemarkIndex);
    // 获取最新的map集合
    wayPointsPlacemarkMap = folder?.getPlacemarkMap();
    // 创建组
    reBuidPlacemarkAndActionGroup();
    const data = convertPlacemarksToWayPointList(folder.placemark);
    const wayPointStore = useWayPointStore();
    wayPointStore.setPointList(data);
    return true;
  } catch (error) {
    console.log('error:', error);
    return null;
  }
}

/**
 * 获取最大的航点索引
 * @param {*} options
 * @param {string} options.type  min / max 默认取得最小索引
 * @returns 最大的航点索引 和 航点信息
 */
export function getMinMaxPlacemarkInfo(options) {
  const { type = 'min' } = options;
  const response = {
    placemark: null,
    index: 0
  };
  try {
    const folder = waylinesXpmlObj.getFolder(0);
    if (folder) {
      const { placemark, index } = type === 'min' ? folder.getMinPlacemark() : folder.getMaxPlacemark();
      response.placemark = placemark;
      response.index = index;
    }
    return response;
  } catch (error) {}
}

//#endregion

//#region 动作组管理部分

/** 创建动作组
 * @param {*} placemark 航点
 * @param {*} options 动作组配置
 * @param {number} options.wpml_actionGroupId 组id
 * @param {number} options.wpml_actionGroupStartIndex 开始索引
 * @param {number} options.wpml_actionGroupEndIndex 结束索引
 * @param {string} options.wpml_actionGroupMode 组模式
 * @param {string} options.wpml_actionTriggerType 触发方式
 * @param {string} options.wpml_actionTriggerParam 触发参数
 * @returns
 */
export function createActionGroup(placemark, options = null) {
  if (!placemark) {
    return;
  }
  const placemarkIndex = placemark.wpml_index;
  let maxActionGroupId = getMaxActionGroupIndex();
  if (!maxActionGroupId) {
    maxActionGroupId = 0;
  }
  // 创建动作组
  return new ActionGroup({
    placemarkIndex: placemarkIndex,
    actionGroupId: options?.wpml_actionGroupId || maxActionGroupId,
    actionGroupStartIndex: options?.wpml_actionGroupStartIndex || placemarkIndex,
    actionGroupEndIndex: options?.wpml_actionGroupEndIndex || placemarkIndex,
    actionGroupMode: options?.wpml_actionGroupMode || 'sequence',
    actionTriggerType: options?.wpml_actionTriggerType || 'reachPoint',
    actionTriggerParam: options?.wpml_actionTriggerParam || null,
    actions: [],
    uuid: generateKey(),
    type: options?.type || OPERATION_TYPE.normal
  });
}

/**
 * 将动作组加入到 placemark
 * @param {Placemark} placemark
 * @param {ActionGroup} actionGroup
 */
export function addActionGroupToPlaceMark(placemark, actionGroup) {
  // 将动作组加到 placemark
  if (!placemark) {
    return null;
  }
  return placemark.addActionGroup(actionGroup);
}

/**
 *  在 placemark中是获取最大的普通动作组
 *  @param {Placemark} placemark
 */
export function getMaxIndexNormalActionGroup(placemark, type = OPERATION_TYPE.normal) {
  // 将动作组加到 placemark
  if (!placemark) {
    return null;
  }
  // 获取所有动作组
  const groups = placemark.getActionGroup() || [];
  let curGroup = null;
  switch (type) {
    case OPERATION_TYPE.normal:
      for (let i = 0; i < groups.length; i++) {
        const group = groups[i];
        const gType = group.getType();
        const trigger = group.getActionGroupTriggerType();
        const isNormal = gType === OPERATION_TYPE.normal;
        const isMultple =
          trigger === ACTION_TRIGGER_TYPE.multipleDistance || trigger === ACTION_TRIGGER_TYPE.multipleTiming;
        if (!isMultple) {
          curGroup = group;
        }
      }
      break;
    case OPERATION_TYPE.interval:
      break;
    case OPERATION_TYPE.stopInterval:
      break;

    default:
      break;
  }
  return curGroup;
}

export function getMaxActionGroupIndex() {
  // 遍历wayPointsPlacemarkMap
  let tempActionGroupId = -1;
  // 获取最新的map集合
  wayPointsPlacemarkMap = getWayPointsPlacemarkMap();
  for (const placeMark of wayPointsPlacemarkMap.values()) {
    // 取到最大的
    const actionGroups = placeMark.wpml_actionGroup;
    if (actionGroups) {
      for (const actionGroup of actionGroups) {
        if (tempActionGroupId < actionGroup.wpml_actionGroupId) {
          tempActionGroupId = actionGroup.wpml_actionGroupId;
        }
      }
    }
  }
  return tempActionGroupId + 1 || 0;
}

/**
 * 通过相关参数获取动作对象的参数
 * @param {object} actionOptions
 * @param {object} actionOptions.point 选中的点
 * @param {object} actionOptions.actionUuid 选中该点的动作
 * @returns
 */
export function getActionGroupByActionUUID(actionOptions) {
  try {
    if (!waylinesXpmlObj) {
      return null;
    }
    const { point, actionUuid = null } = actionOptions;
    const placemarkIndex = point.placemarkIndex; //所属的placemarkIndex
    if (!point || !actionUuid) {
      return null;
    }
    const folder = waylinesXpmlObj.getFolder(0);
    // 获取最新的map集合
    const folderPlacemark = folder?.placemark;
    const i = folderPlacemark.findIndex(item => item.wpml_index === placemarkIndex);
    if (i === -1) {
      return null;
    }
    // 获取航点对象
    const curPlacemark = folderPlacemark[i] ?? null;
    if (!curPlacemark) {
      return null;
    }
    // 获取航点动作组
    const actionGroups = curPlacemark.getActionGroup() ?? null;
    if (!actionGroups) {
      return null;
    }
    // 遍历 actionGroups 数组,找到包含目标 actionUuid 的动作组
    const targetActionGroup = actionGroups.find(group => group.wpml_action.some(action => action.uuid === actionUuid));
    if (targetActionGroup) {
      // 在目标动作组中找到目标动作
      const currentAction = targetActionGroup.wpml_action.find(action => action.uuid === actionUuid);
      if (currentAction) {
        return {
          action: currentAction,
          actionGroup: targetActionGroup // 返回动作所属的组
        };
      }
    }
    return null;
  } catch (error) {
    return null;
  }
}
//#endregion

//#region 动作相关
/**
 * 从动作组中获取动作的最大索引
 * @param {*} actionGroup
 * @returns
 */
export function getMaxActionIndexFromActionGroup(actionGroup) {
  if (!actionGroup) {
    return null;
  }
  // 目前只是取得第一组的动作组
  const group = actionGroup[0];
  if (!group) {
    return null;
  }
  let maxActionId = group.getMaxActionIndex();
  return maxActionId;
}

/**
 * 向动作组中插入动作
 * @param {*} actionGroup 动作组对象
 * @param {*} actionOptions 动作参数
 * @returns
 */
export function insertActionIntoActionGroup(actionGroup, actionOptions) {
  try {
    if (!actionGroup || !actionOptions) {
      return null;
    }
    const type = actionOptions.type || null;
    if (!type) {
      return null;
    }
    // 获取构建函数
    const actionFuncObj = actionCreateFuncMap[type];
    if (!actionFuncObj) {
      ElMessage.error('暂未开放此功能！');
      return null;
    }
    const newAction = actionFuncObj.createFunc(actionOptions);
    actionGroup.addAction(newAction);
    // 执行构建函数并返回
    return newAction;
  } catch (error) {
    return null;
  }
}

/**
 * 移除动作
 * @param {*} deleteOptions 移除动作参数
 * actionOptions = {placemarkIndex,groupid,actionnid,type }
 * @returns bool
 */
export function deleteActionFromGroup(deleteOptions) {
  try {
    if (!deleteOptions) {
      return null;
    }
    const { point, action: deleteAction } = deleteOptions;
    const placemarkIndex = point.placemarkIndex; //所属的placemark
    const actions = point.action; // 动作组
    if (!point || !actions || !deleteAction) {
      return null;
    }
    const folder = waylinesXpmlObj.getFolder(0);
    // 获取最新的map集合
    const folderPlacemark = folder?.placemark;
    const i = folderPlacemark.findIndex(item => item.wpml_index === placemarkIndex);
    if (i === -1) {
      return null;
    }
    // 获取航点对象
    const curPlacemark = folderPlacemark[i];
    if (!curPlacemark) {
      return null;
    }
    // 获取航点动作组
    let actionGroups = curPlacemark.getActionGroup();
    if (!actionGroups) {
      return null;
    }
    // 这里获取动作组的唯一值 actionUuid
    const beDeleteActionUuid = deleteAction.actionUuid || 0;
    const beDeleteActionGroupList = [];
    // 这里需要进行循环动作组查找动作并且删除
    actionGroups.forEach(actionGroup => {
      // 通过id判断动作组中是否存在该动作
      const indexToRemove = actionGroup.wpml_action.findIndex(action => action.uuid === beDeleteActionUuid);
      if (indexToRemove !== -1) {
        // 根据唯一索引移除动作
        actionGroup?.removeActionByActionUuid(beDeleteActionUuid);
        // 判断动作数组长度是否为0，如果此时动作组为空，那么移除本动作组
        if (actionGroup.getActionCount() === 0) {
          beDeleteActionGroupList.push(actionGroup);
        }
      }
    });

    // 移除动作组
    // beDeleteActionGroupList.forEach(ag => {
    //   const indexToRemove = actionGroups.findIndex(actionGroup => {
    //     return actionGroup.uuid === ag.uuid;
    //   });
    //   if (indexToRemove !== -1) {
    //     actionGroups.splice(indexToRemove, 1);
    //   }
    // });
    // 移除动作组
    actionGroups = actionGroups.filter(
      actionGroup => !beDeleteActionGroupList.some(ag => ag.uuid === actionGroup.uuid)
    );
    reBuidPlacemarkAndActionGroup();
    // 重新填充列表
    const data = convertPlacemarksToWayPointList(folder.placemark);
    const wayPointStore = useWayPointStore();
    wayPointStore.setPointList(data);
    return true;
  } catch (error) {
    return null;
  }
}

// 获取之前的所有动作
/**
 * 通过相关参数获取动作对象的参数
 * @param {object} actionOptions
 * @param {object} actionOptions.actionUuid 选中该点的动作
 * @returns
 */
export function getAllPrevActions(actionOptions) {
  try {
    if (!waylinesXpmlObj) {
      return [];
    }
    const { actionUuid = null } = actionOptions;
    if (!actionUuid) {
      return [];
    }
    const targetActions = [];
    const folder = waylinesXpmlObj.getFolder(0);
    // 获取所有的动作集合
    const placemarks = folder?.placemark ?? [];
    placemarks.forEach(placemark => {
      (placemark.getActionGroup() ?? []).forEach(actionGroup => {
        const actions = actionGroup.getActions() ?? [];
        // 这里将placemark的索引值 placemarkIndex 存储到动作中去
        actions.forEach(action => {
          action.placemarkIndex = placemark.wpml_index;
        });
        targetActions.push(...actions);
      });
    });
    // 判断这里数组的动作下标，取出包括当前下标的所有动作
    let endIndex = targetActions.findIndex(item => item.uuid === actionUuid);
    if (endIndex !== -1) {
      const resultActions = targetActions.slice(0, Math.min(endIndex + 1, targetActions.length));
      return resultActions;
    }
    return [];
  } catch (error) {
    return [];
  }
}

// 获取之前的所有动作
/**
 * 通过相关参数获取动作对象的参数
 * @returns
 */
export function getAllActions() {
  try {
    if (!waylinesXpmlObj) {
      return [];
    }
    const targetActions = [];
    const folder = waylinesXpmlObj.getFolder(0);
    // 获取所有的动作集合
    const placemarks = folder?.placemark ?? [];
    placemarks.forEach(placemark => {
      (placemark.getActionGroup() ?? []).forEach(actionGroup => {
        const actions = actionGroup.getActions() ?? [];
        // 这里将placemark的索引值 placemarkIndex 存储到动作中去
        actions.forEach(action => {
          action.placemarkIndex = placemark.wpml_index;
        });
        targetActions.push(...actions);
      });
    });
    return targetActions ?? [];
  } catch (error) {
    return [];
  }
}

/**
 * 重新构建动作序列
 * 1、移除动作组 2、移除动作 3、重新对动作排序
 * 注意：
 * 如果动作组里面只有一个动作，当该动作被移除时动作组也需要被移除
 * 如果动作组中有两个动作，当删除第一个动作时需要将第二个动作序列重构为1即重新排序
 * 有动作组1、2、3 如果移除第2个那么剩余动作组的id为1、2 动作组序列递增
 *
 * ----重要----
 * 1、时间和距离的间隔操作的 结束索引 wpml_actionGroupEndIndex 是根据下一个停止间隔操作动作按钮所在的索引
 * 比如开始间隔拍照在第一个点，下一个停止间隔操作动作是在第5个点
 * 那么wpml_actionGroupStartIndex=0, wpml_actionGroupEndIndex=4
 * 2、如果动作组中只有开始等时间拍照或者等距离拍照那么 结束间隔拍照 这个动作系统会加载最后一个点的最后一个动作中
 * @param {*} folderIndex
 */
export function reBuidPlacemarkAndActionGroup(folderIndex = 0) {
  try {
    const folder = waylinesXpmlObj.getFolder(folderIndex);
    let tempActionGroupId = 0; // 记录当下的组ID
    let tempActionId = 0; // 当前动作ID
    // 重新排序 按照 wpml_index 排序
    let sortedPlacemarks = folder.placemark?.sort((a, b) => a.wpml_index - b.wpml_index);
    // 顺序遍历
    sortedPlacemarks?.forEach(placemark => {
      let wpml_index = placemark.wpml_index;
      //  获取当前的动作组
      if (placemark.hasOwnProperty('wpml_actionGroup')) {
        for (const actionGroup of placemark.wpml_actionGroup) {
          actionGroup.wpml_actionGroupId = tempActionGroupId;
          actionGroup.wpml_actionGroupStartIndex = wpml_index;
          actionGroup.wpml_actionGroupEndIndex = wpml_index;
          // 这里执行对动作数据的索引重构
          for (const action of actionGroup.wpml_action) {
            action.wpml_actionId = tempActionId;
            tempActionId++;
          }
          tempActionGroupId++;
          tempActionId = 0; // 重置动作ID，每个动作组独立编号
        }
      }
    });
    // 间隔动作索引重建
    reBuildMultipActionEndIndex();
  } catch (error) {}
}

// 清理多余的动作组
export function clearExtraActionGroup(folderIndex = 0) {
  if (!waylinesXpmlObj) {
    return null;
  }
  const folder = waylinesXpmlObj.getFolder(folderIndex);
  if (!folder) {
    return null;
  }
  // wpml_actionGroup 数组中的 wpml_action 数组是空的 则移除该动作组
  folder.placemark?.forEach(placemark => {
    if (placemark.hasOwnProperty('wpml_actionGroup')) {
      placemark.wpml_actionGroup = placemark.wpml_actionGroup.filter(actionGroup => actionGroup.wpml_action.length > 0);
      // 如果这里placemark.wpml_actionGroup 为空数组 则移除这个对象
      if (placemark.wpml_actionGroup.length === 0) {
        delete placemark.wpml_actionGroup;
      }
    }
  });
  return folder;
}

/**
 * 通过相关参数获取动作对象的参数
 * @param {object} actionOptions
 * @param {object} actionOptions.point 选中的点
 * @param {object} actionOptions.action 选中该点的动作
 * @returns
 */
export function getActionActuatorFuncParam(actionOptions) {
  try {
    if (!actionOptions) {
      return null;
    }
    const {
      point,
      action: { actionUuid = null }
    } = actionOptions;
    // const pointUuid = point.uuid || null;
    const placemarkIndex = point.placemarkIndex; //所属的placemarkIndex
    // const actions = point.action; // 动作组
    if (!point || !actionUuid) {
      return null;
    }
    const folder = waylinesXpmlObj.getFolder(0);
    // 获取最新的map集合
    const folderPlacemark = folder?.placemark;
    const i = folderPlacemark.findIndex(item => item.wpml_index === placemarkIndex);
    if (i === -1) {
      return null;
    }
    // 获取航点对象
    const curPlacemark = folderPlacemark[i] ?? null;
    if (!curPlacemark) {
      return null;
    }
    // 获取航点动作组
    const actionGroups = curPlacemark.getActionGroup() ?? null;
    if (!actionGroups) {
      return null;
    }
    // 遍历 actionGroups 数组,找到包含目标 actionUuid 的动作组
    const targetActionGroup = actionGroups.find(group => group.wpml_action.some(action => action.uuid === actionUuid));
    if (targetActionGroup) {
      // 在目标动作组中找到目标动作
      const currentAction = targetActionGroup.wpml_action.find(action => action.uuid === actionUuid);
      if (currentAction) {
        return {
          action: currentAction,
          wpml_actionActuatorFuncParam: currentAction.wpml_actionActuatorFuncParam,
          actionGroup: targetActionGroup // 返回动作所属的组
        };
      }
    }
    return { actionGroup: null, action: null, wpml_actionActuatorFuncParam: null };
  } catch (error) {
    return {
      action: null,
      actionGroup: null,
      wpml_actionActuatorFuncParam: null
    };
  }
}

/**
 * 通过动作ID获取动作所属的点和当前动作所在的所言位置
 * @param {Object} actionOptions
 * @returns {Object} {placemarkIndex, actionIndex}
 */
export function getActionIndex(actionOptions) {
  const { actionUuid = '' } = actionOptions;
  if (actionUuid === '') {
    return {
      pointIndex: null,
      actionIndex: null
    };
  }
  // 获取最新的map集合
  const folder = waylinesXpmlObj.getFolder(0);
  const placemarkArr = folder.placemark;
  let placemarkIndex = null;
  let curAction = null; // 当前动作
  let curActionGroup = null; // 当前动作组
  let count = null; // 当前所在的动作索引位置
  (placemarkArr || []).forEach(pmk => {
    // 获取航点动作组
    const actionGroups = pmk.getActionGroup();
    if (!actionGroups || actionGroups.length === 0) {
      return;
    }

    //#region 这里分为两步执行获取动作和动作组
    // 1、 将动作抽取出来作为数组
    const wpml_actions = [];
    actionGroups.forEach(actiongroup => {
      const acList = actiongroup?.getActions();
      wpml_actions.push(...acList);
    });
    wpml_actions?.forEach((action, i) => {
      // 动作对象这里使用uuid 于 actionUuid对比
      if (action.uuid === actionUuid) {
        curAction = action;
        count = i;
        placemarkIndex = pmk.wpml_index;
      }
    });
    // 2、获取所属动作组
    actionGroups.forEach(actiongroup => {
      (actiongroup?.getActions() || []).forEach((action, i) => {
        if (action.uuid === actionUuid) {
          curActionGroup = actiongroup;
        }
      });
    });
    //#endregion
  });
  return {
    pointIndex: placemarkIndex + 1,
    actionIndex: count + 1,
    action: curAction || null,
    actionGroup: curActionGroup || null
  };
}

/**
 * 从动作组里面获取和视锥体有关的heading roll zoom 这三个相关参数
 * @param {*} actionArr 动作对象数组
 * @param {*} actionObj 被点击的动作数组
 * @returns
 */
export function getFrustParmsFromActions(actionArr = [], actionObj = null) {
  let responeObject = {
    heading: null,
    roll: -90,
    fov: null
  };
  if (!actionArr || actionArr.length === 0 || !actionObj.action || !actionObj.actionUuid) {
    return responeObject;
  }
  //#region 获取要统计的列表 包括本身动作
  const currentActionUuid = actionObj.actionUuid;
  const index = actionArr.findIndex(action => action.actionUuid === currentActionUuid);
  if (index === -1) {
    return responeObject;
  }
  let actionlist = actionArr.slice(0, index + 1);
  if (actionlist.length === 0) {
    return responeObject;
  }
  //#endregion

  //#region 参数构建
  (actionlist || []).forEach(actObj => {
    if (!actObj || !actObj.action) {
      return;
    }
    // 动作
    const { action = null } = actObj;
    // 功能名称
    let actionActuatorFunc = action?.wpml_actionActuatorFunc ?? null;
    // 功能参数
    let actionActuatorFuncParam = action?.wpml_actionActuatorFuncParam ?? null;
    if (!action || !actionActuatorFunc || !actionActuatorFuncParam) {
      return null;
    }
    switch (actionActuatorFunc) {
      case ACTION_ACTUATOR_FUNC.rotateYaw:
        responeObject.heading = actionActuatorFuncParam.wpml_aircraftHeading;
        break;
      case ACTION_ACTUATOR_FUNC.zoom:
        let fov = calculateFOV(actionActuatorFuncParam.wpml_focalLength);
        responeObject.fov = fov;
        break;
      case ACTION_ACTUATOR_FUNC.gimbalRotate:
        responeObject.roll = actionActuatorFuncParam.wpml_gimbalPitchRotateAngle - 90;
      default:
        break;
    }
  });
  //#endregion
  return Object.fromEntries(Object.entries(responeObject).filter(([_, value]) => value !== null));
}

export function getFrustParms(actionlist = []) {
  let responeObject = {
    heading: null,
    roll: -90,
    zoom: null
  };

  if (actionlist.length === 0) {
    return null;
  }
  //#region 参数构建
  (actionlist || []).forEach(actObj => {
    if (!actObj || !actObj.action) {
      return;
    }
    // 动作
    const { action = null } = actObj;
    // 功能名称
    let actionActuatorFunc = action?.wpml_actionActuatorFunc ?? null;
    // 功能参数
    let actionActuatorFuncParam = action?.wpml_actionActuatorFuncParam ?? null;
    if (!action || !actionActuatorFunc || !actionActuatorFuncParam) {
      return;
    }
    switch (actionActuatorFunc) {
      case ACTION_ACTUATOR_FUNC.rotateYaw:
        responeObject.heading = actionActuatorFuncParam.wpml_aircraftHeading;
        break;
      case ACTION_ACTUATOR_FUNC.zoom:
        responeObject.zoom = actionActuatorFuncParam.wpml_focalLength * 24;
        break;
      case ACTION_ACTUATOR_FUNC.gimbalRotate:
        responeObject.roll = actionActuatorFuncParam.wpml_gimbalPitchRotateAngle - 90;
      default:
        break;
    }
  });
  //#endregion
  return Object.fromEntries(Object.entries(responeObject).filter(([_, value]) => value !== null));
  // return responeObject;
}
// 设置当前动作组件可见
export function setActionComponentVisible() {
  const wayPointStore = useWayPointStore();
  //  获取当前被选中的点和id的 唯一ID值
  const { currentActionId } = wayPointStore.getCurrentPointAndAction();
  if (!currentActionId) {
    window.$bus.emit('setAction', null);
    return;
  }
  // 根据当前动作id 返回当前动作对象 这里对象 唯一id为 uuid
  const { action = null, actionGroup = null } = getActionIndex({ actionUuid: currentActionId });
  if (!action) {
    window.$bus.emit('setAction', null);
    return;
  }
  const options = {
    key: action.wpml_actionActuatorFunc,
    title: ACTION_ACTUATOR_FUNC_NAME[action.wpml_actionActuatorFunc],
    url: ACTION_ACTUATOR_FUNC_ICON[action.wpml_actionActuatorFunc],
    actionUuid: action.uuid,
    actionFuncParam: action.wpml_actionActuatorFuncParam,
    action: action,
    actionGroup: actionGroup
  };
  window.$bus.emit('setAction', options);
}

export function getFirstActionInPlaceMark(placemark = null) {
  if (!placemark) {
    return null;
  }
  const actionGroups = placemark.getActionGroup();
  const actionGroup = actionGroups.length > 0 ? actionGroups[0] : null;
  const actions = actionGroup ? actionGroup.getActions() : null;
  if (actions && actions.length > 0) {
    return actions[0];
  }
  return null;
}

//#endregion

// 在航点上检查是否存在动作存在则添加视锥体
export function setFrustInWayPoint(placemark = null) {
  if (!placemark) {
    return null;
  }
  const firstAction = getFirstActionInPlaceMark(placemark);

  if (firstAction) {
    const plan = getPlanInstance();
    const pointJson = plan.selectPlanPoint(0, true);
    if (!pointJson) {
      return;
    }
    const { lon = null, lat = null, height = null } = pointJson;
    let frustun = getFrustum('action');
    if (frustun) {
      const op = frustun?.getOptions() ?? {};
      const options = {
        ...op,
        position: [lon, lat, height]
      };
      frustun.update(options);
    }
  }
}

//#region

//#endregion

//#reginon

export function getAllWayLinePoints() {
  const list = [];
  if (!planObject) {
    return [];
  }
  // 提取经纬度
  (planObject.planPointJson || []).forEach(pointJson => {
    let poi = [pointJson.lon, pointJson.lat, pointJson.height || pointJson.globalHight];
    list.push(poi);
  });
  return list;
}

export function positionCameraToViewPoints(viewer) {
  const longlatArr = getAllWayLinePoints();
  zoomtoLonglats(viewer, longlatArr);
}
//#endregion

//#region  判断航点航线是否有绘制一个航点
/**
 * 获取该动作之前的所有动作的相关参数
 * @param {*} actionOption
 * @param {string} actionOption.actionUuid
 * @returns 动画数组
 */
export const hasWayPoint = () => {
  try {
    if (!waylinesXpmlObj) {
      return [];
    }
    const waypoints = [];
    const folder = waylinesXpmlObj.getFolder(0);
    // 获取所有的动作集合
    const placemarks = folder?.placemark ?? [];
    placemarks.forEach(placemark => {
      waypoints.push(placemark);
    });
    // 输出 如果航点数组大于0  则返回true
    return waypoints.length > 0;
  } catch (error) {
    return false;
  }
};

//#endregion
