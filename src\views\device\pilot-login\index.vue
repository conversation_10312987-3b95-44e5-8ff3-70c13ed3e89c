<template>
  <div class="login flex-column flex-justify-center flex-align-center m0 b0" style="background-color: #f7f9fa">
    <el-image
      style="width: 17vw; height: 10vw; margin-bottom: 50px"
      :src="djiLogo"
    />
    <p class="logo pb50">信鸽无人机实战平台</p>
    <el-form
      layout="inline"
      :model="formState"
      class="flex-row flex-justify-center flex-align-center"
    >
      <el-form-item>
        <el-input v-model="formState.username" placeholder="用户名">
          <template #prefix>
            <el-icon :size="20">
              <User style="color: rgba(0, 0, 0, 0.25)" />
            </el-icon>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item style="margin: 0 20px;">
        <el-input
          v-model="formState.password"
          type="password"
          placeholder="密码"
        >
          <template #prefix>
            <el-icon :size="20">
              <Lock style="color: rgba(0, 0, 0, 0.25)" />
            </el-icon>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button
          class="m0"
          type="primary"
          html-type="submit"
          :disabled="formState.username === '' || formState.password === ''"
          @click="onSubmit"
        >
          登录
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import { onMounted, reactive, ref } from 'vue'
import { CURRENT_CONFIG } from '@/api/http/config'
import { refreshToken } from '@/api/devices/index.js'
import apiPilot from '@/api/pilot-login/pilot-bridge'
import { useRouter } from 'vue-router';
import { useStorage } from '@vueuse/core';
import {
  loginApi,
} from '@/api/auth';
import { EComponentName, ELocalStorageKey, EUserType } from '@/types/airport-tsa'
import { Lock, User } from '@element-plus/icons-vue';
import djiLogo from '@/assets/pilot-login.png'
import '@/styles/index.scss';
const router = useRouter();
const token = useStorage('accessToken', '');

const formState = reactive({
  username: '',
  password: '',
  flag: EUserType.Pilot,
})
const isVerified = ref(false)
onMounted(async () => {
  verifyLicense()
  if (!isVerified.value) {
    return
  }

  apiPilot.setPlatformMessage('信鸽无人机实战平台', '', '')
  const token = localStorage.getItem(ELocalStorageKey.Token)
  if (token) {
    await refreshToken({})
      .then(res => {
        apiPilot.setComponentParam(EComponentName.Api, {
          host: CURRENT_CONFIG.baseURL,
          token: res.access_token
        })
        const jsres = apiPilot.loadComponent(EComponentName.Api, apiPilot.getComponentParam(EComponentName.Api))
        if (!jsres) {
          ElMessage.error('Failed to load api module.')
          return
        }
        apiPilot.setToken(res.access_token)
        localStorage.setItem(ELocalStorageKey.Token, res.access_token)
        router.push({path: '/pilot-home'})
      })
      .catch(err => {
        // ElMessage.error('错误1')
      })
  }
})
const onSubmit = () => {
  loginApi({
    ...formState,
    rc_sn: apiPilot.getRemoteControllerSN()
  }).then(res => {
    if (!isVerified.value) {
      ElMessage.error('Please verify the license firstly.')
      return
    }
    apiPilot.setComponentParam(EComponentName.Api, {
      host: CURRENT_CONFIG.baseURL,
      token: res?.access_token
    })
    const jsres = apiPilot.loadComponent(
      EComponentName.Api,
      apiPilot.getComponentParam(EComponentName.Api)
    )
    apiPilot.setToken(res.access_token)
    // localStorage.setItem('accessToken', res.access_token)
    // localStorage.setItem(ELocalStorageKey.Token, res.access_token)
    token.value = res.access_token;
    localStorage.setItem(ELocalStorageKey.WorkspaceId, res.workspace_id)
    localStorage.setItem(ELocalStorageKey.UserId, res.user_id)
    localStorage.setItem(ELocalStorageKey.Username, res.username)
    localStorage.setItem(ELocalStorageKey.Flag, EUserType.Pilot.toString())
    ElMessage.success('Login Success')
    router.push({path: '/pilot-home'})
  })
  .catch(err => {
    ElMessage.error('错误')
  })
}

function verifyLicense () {
  isVerified.value = apiPilot.platformVerifyLicense(CURRENT_CONFIG.appId, CURRENT_CONFIG.appKey, CURRENT_CONFIG.appLicense) &&
    apiPilot.isPlatformVerifySuccess()
  if (isVerified.value) {
    ElMessage.success('The license verification is successful.')
  } else {
    ElMessage.error('Filed to verify the license. Please check license whether the license is correct, or apply again.')
  }
}
</script>

<style lang="scss" scoped>
.login {
  width: 100%;
  // background-color: $dark-highlight;
  height: 100vh;
  overflow: hidden;
}
.logo {
  color: #2d8cf0;
}
.pb50{
  padding-bottom: 100px;
  font-size: 35px;
}
.flex-display {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-justify-center {
  justify-content: center;
}
.flex-align-center {
  align-items: center;
}
.m0 {
  margin: 0px !important;
}

</style>
