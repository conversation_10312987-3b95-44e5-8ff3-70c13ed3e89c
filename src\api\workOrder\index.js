import request from '@/utils/request';

/**
 * 获取AI事件管理分页
 *
 * @param queryParams
 */
export function getAIEventList(data) {
  return request({
    url: `/ai/event/page`,
    method: 'post',
    data: data
  });
}

/**
 * 获取AI事件详情
 *
 * @param queryParams
 */
export function getAIEventDetail(id) {
  return request({
    url: `/ai/event/${id}`,
    method: 'get',
  });
}

/**
 * 获取预警分页列表
 *
 * @param queryParams
 */
export function getAlarmPage(data) {
  return request({
    url: `/ai/alarm/page`,
    method: 'post',
    data: data
  });
}

/**
 * 获取预警详情
 *
 * @param queryParams
 */
export function getAlarmDetail(id) {
  return request({
    url: `/ai/alarm/${id}`,
    method: 'get',
  });
}

/**
 * 处理预警
 *
 * @param queryParams
 */
export function alarmProcess(data) {
  return request({
    url: `/ai/alarm/process`,
    method: 'post',
    data: data
  });
}

/**
 * 删除预警
 *
 * @param queryParams
 */
export function deleteAlarm(id) {
  return request({
    url: `/ai/alarm/${id}`,
    method: 'delete',
  });
}
