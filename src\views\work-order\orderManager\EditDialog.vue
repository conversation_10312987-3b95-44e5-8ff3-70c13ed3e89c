<template>
  <el-dialog
    :title="title"
    v-if="visible"
    :model-value="visible"
    width="800px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form class="app-form" ref="dataFormRef" :model="form" :rules="rules" label-width="120px" >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="工单类型" prop="work_type" required>
            <el-select v-model="form.work_type" placeholder="请选择工单类型">
              <el-option
                v-for="item in userOptinos"
                :key="item.user_id"
                :label="item?.username"
                :value="item.user_id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工单来源" prop="work_source" required>
            <el-select v-model="form.work_source" placeholder="请选择工单来源">
              <el-option
                v-for="item in userOptinos"
                :key="item.user_id"
                :label="item?.username"
                :value="item.user_id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="优先级" prop="work_level" required>
            <el-select v-model="form.work_level" placeholder="请选择优先级">
              <el-option
                v-for="item in userOptinos"
                :key="item.user_id"
                :label="item?.username"
                :value="item.user_id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工单处置人" prop="processor" required>
            <el-select v-model="form.processor" placeholder="请选择工单处置人">
              <el-option
                v-for="item in userOptinos"
                :key="item.user_id"
                :label="item?.username"
                :value="item.user_id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="地址" prop="work_addr">
            <div style="display: flex;align-items: center;">
              <el-input
                v-model="form.work_addr"
                placeholder=""
                maxlength="250"
                style="width: 550px;margin-right: 10px;"
              />
              <el-button type="primary">地图获取</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="经度" prop="longitude">
            <el-input
              v-model="form.longitude"
              placeholder="请输入经度"
              maxlength="32"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="维度" prop="latitude">
            <el-input
              v-model="form.latitude"
              placeholder="请输入维度"
              maxlength="32"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="工单内容" prop="work_desc">
            <el-input
              v-model="form.work_desc"
              placeholder="请输入工单内容"
              maxlength="500"
              type="textarea"
              :rows="5"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="工单附件" prop="fileList">
            <multi-upload 
            v-model="form.fileList" 
            limit="99"
            accept=".png,.jpg,.jpeg,.bmp,.mp4,.doc,.docx,.xls,.xlsx,.pdf"
            >

            </multi-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-loading="loading">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { listUser } from '@/api/system/user';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  formData: {
    type: Object,
    default(rawProps) {
      return {};
    }
  }
});
const form = reactive({});
const dataFormRef = ref(ElForm);
const userOptinos = ref([]);

watch(
  () => props.formData,
  (newVal, oldVal) => {
    Object.assign(form, newVal);
  },
  { deep: true,immediate:true }
);
const emit = defineEmits(['update:visible', 'submit']);
const rules = reactive({
  work_type: [{ required: true, message: '请选择工单类型', trigger: ['change','blur'] }],
  work_source: [{ required: true, message: '请选择工单来源', trigger: ['change','blur'] }],
  work_level: [{ required: true, message: '请选择优先级', trigger: ['change','blur'] }],
  processor: [{ required: true, message: '请选择工单处置人', trigger: ['change','blur'] }],
  longitude: [
    { required: true, message: '请输入经度', trigger: ['blur'] },
    { pattern: /^(\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,6})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180)$/, message: '纬度：0～90，至多6位小数', trigger: 'blur' }
  ],
  latitude: [
    { required: true, message: '请输入纬度', trigger: ['blur'] },
    { pattern: /^(\+)?([0-8]?\d{1}\.\d{0,6}|90\.0{0,6}|[0-8]?\d{1}|90)$/, message: '经度：0～180，至多6位小数', trigger: 'blur' }
  ],
  work_desc: [{ required: true, message: '请输入工单内容', trigger: ['blur']}],
  fileList: [{ required: true, message: '请上传工单附件', trigger: ['change','blur']}],
});

defineExpose({ setDefaultValue });

// 设置默认值
function setDefaultValue() {
  
}
const loading = ref(false);

// 关闭弹窗
function closeDialog() {
  resetForm();
  emit('update:visible', false);
}
/**
 * 重置表单
 */
function resetForm() {
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  loading.value = false;
  Object.keys(form).map(key => {
    delete form[key];
  });
}

function handleSubmit() {
   console.log('3=====',form)
    dataFormRef.value.validate(isValid => {
      if (isValid) {
      console.log('3=====',form)
      //   let params = {
      //     ...form,
        
      //   };
      //   loading.value = true;
      //   addAirTaskList(params)
      //     .then(res => {
      //       loading.value = false;
      //       ElMessage.success('新增成功');
      //       closeDialog();
      //       emit('submit');
      //     })
      //     .catch(e => {
      //       loading.value = false;
      //     });
      } else {
        loading.value = false;
      }
    });
}
//处置人
function getUser() {
  listUser({
    pageNo: 1,
    pageSize: 999
  }).then(res => {
    const { list = [] } = res;
    userOptinos.value = list;
  });
}

onMounted(() => {
  getUser();
});
</script>
<style scoped lang="scss">

:global(.el-loading-mask) {
  transform: opacity 0.9 !important;
  background-color: rgb(255 255 255 / 0.3);
}
.app-form {
  ::v-deep {
    .el-select {
      width: 100%;
    }
  }
}
.fileList {
  ::v-deep {
    // .el-upload {
    //   display: none !important;
    // }
    .el-upload-list__item:hover .el-icon--close {
      display: none !important;
    }
    .el-icon--close-tip {
      display: none !important
    }
  }
}
</style>
