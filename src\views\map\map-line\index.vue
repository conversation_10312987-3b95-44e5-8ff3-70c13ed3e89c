<template>
  <div class="map-100">
    <div class="map-100" id="cesium-line"></div>
  </div>
</template>
<script>
export default {
  name: 'Mapline'
};
</script>
<script setup>
import { onMounted } from 'vue';
import CesiumEngine from '../../../components/Cesium/libs/cesium/engine';

onMounted(() => {
  const engine = new CesiumEngine();
  engine?.init('cesium-line');
});
</script>

<style lang="scss" scoped>
.map-100 {
  height: 100%;
  width: 100%;
}
</style>
