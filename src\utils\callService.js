import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import {
  joinChannel,
  leaveChannel,
  requestMicrophonePermission,
  forcePlayRemoteAudio,
  troubleshootAudio
} from '@/utils/agoraService';
import { useUserStoreHook } from '@/store/modules/user';
import {
  createCallSession,
  getCallSession,
  answerCallSession,
  rejectCallSession,
  endCallSession,
  leaveCallSession,
  getActiveCallSession as fetchActiveCallSession,
  addParticipant
} from '@/api/call';

// 通话状态常量
const CALL_STATES = {
  IDLE: 'idle', // 空闲状态
  OUTGOING: 'outgoing', // 呼出状态
  INCOMING: 'incoming', // 来电状态
  CONNECTED: 'connected', // 已连接状态
  ENDED: 'ended', // 已结束状态
  ERROR: 'error' // 错误状态
};

// 响应式状态变量
const callState = ref(CALL_STATES.IDLE); // 当前通话状态
const currentSession = ref(null); // 当前会话信息
const currentChannel = ref(''); // 当前通话频道
const remoteUser = ref(null); // 远程用户信息
const errorMessage = ref(''); // 错误信息
const callStartTime = ref(null); // 通话开始时间
const callDuration = ref(0); // 通话时长（秒）
const durationTimer = ref(null); // 计时器引用
const isCallActive = ref(false); // 是否有活动通话
const participants = ref([]); // 通话参与者列表

// 获取用户存储
const userStore = useUserStoreHook();

/**
 * 检查麦克风权限
 * @returns {Promise<boolean>} - 是否有麦克风权限
 */
const checkMicrophonePermission = async () => {
  return await requestMicrophonePermission();
};

/**
 * 初始化新的呼出通话
 * @param {Object} targetUser - 目标用户
 * @returns {Promise<Object>} - 创建的通话会话
 */
const initiateCall = async targetUser => {
  try {
    if (callState.value !== CALL_STATES.IDLE) {
      throw new Error('无法发起通话：当前已有通话进行中');
    }

    // 先请求麦克风权限，但即使没有权限也继续（用户可以只听不说）
    await checkMicrophonePermission();

    callState.value = CALL_STATES.OUTGOING;
    remoteUser.value = targetUser;

    // 通过API创建通话会话
    const response = await createCallSession({
      creator_id: userStore.userData.user_id,
      creator_type: 'pc',
      callees: [
        {
          user_id: targetUser.user_id,
          user_type: targetUser.deviceType || 'pilot'
        }
      ]
    });

    if (response) {
      console.log('通话会话创建成功:', response);
      currentSession.value = response;
      currentChannel.value = response.agora_channel;
      isCallActive.value = true;

      // 初始化参与者列表
      participants.value = [
        {
          user_id: userStore.userData.user_id,
          user_name: userStore.userData.username,
          isCreator: true
        },
        {
          user_id: targetUser.user_id,
          user_name: targetUser.username,
          isCreator: false
        }
      ];

      // 创建者也加入频道
      const joined = await joinChannel(currentChannel.value, userStore.userData.uid);
      if (!joined) {
        throw new Error('创建者加入频道失败');
      }

      return response;
    } else {
      throw new Error('创建通话会话失败');
    }
  } catch (error) {
    callState.value = CALL_STATES.IDLE;
    errorMessage.value = error.message || '发起通话失败';
    console.error('通话发起错误:', error);
    ElMessage.error(errorMessage.value);
    return null;
  }
};

/**
 * 处理来电
 * @param {Object} callData - 来电数据
 */
const handleIncomingCall = callData => {
  if (callState.value !== CALL_STATES.IDLE) {
    console.warn('忽略来电：当前已有通话进行中');
    return;
  }

  callState.value = CALL_STATES.INCOMING;
  currentChannel.value = callData.channel_name;
  currentSession.value = {
    sessionId: callData.session_id
  };
  remoteUser.value = {
    user_id: callData.caller_id,
    username: callData.caller_name
  };
  // 获取会话详情
  fetchSessionDetails(callData.session_id);
};

/**
 * 获取通话会话详情
 * @param {string} sessionId - 会话ID
 */
const fetchSessionDetails = async sessionId => {
  try {
    const response = await getCallSession(sessionId);
    if (response.session_id) {
      currentSession.value = response;

      // 更新参与者列表
      if (response.participants && Array.isArray(response.participants)) {
        participants.value = response.participants;
      }
    }
  } catch (error) {
    console.error('获取会话详情失败:', error);
  }
};

/**
 * 接听来电
 * @returns {Promise<boolean>} - 是否成功接听
 */
const answerCall = async () => {
  try {
    if (callState.value !== CALL_STATES.INCOMING || !currentSession.value) {
      throw new Error('没有可接听的来电');
    }

    // 先请求麦克风权限，但即使没有权限也继续（用户可以只听不说）
    await checkMicrophonePermission();

    // 加入声网频道
    const joined = await joinChannel(currentChannel.value, userStore.userData.uid);

    if (joined) {
      // 通过API接听通话
      const response = await answerCallSession({
        session_id: currentSession.value.session_id,
        user_id: userStore.userData.user_id
      });

      if (response) {
        // 设置为已连接状态
        callState.value = CALL_STATES.CONNECTED;
        isCallActive.value = true;
        startCallTimer();

        // 更新参与者列表
        if (!participants.value.some(p => p.user_id === userStore.userData.user_id)) {
          participants.value.push({
            user_id: userStore.userData.user_id,
            username: userStore.userData.username,
            isCreator: false
          });
        }

        // 延迟检查并强制播放音频
        setTimeout(async () => {
          await forcePlayRemoteAudio();
          console.log('通话连接成功，已尝试播放远程音频');
        }, 2000);

        return true;
      } else {
        throw new Error('加入语音频道失败');
      }
    } else {
      throw new Error('接听通话失败');
    }
  } catch (error) {
    console.error('接听通话错误:', error);
    callState.value = CALL_STATES.ERROR;
    errorMessage.value = error.message || '接听通话失败';
    ElMessage.error(errorMessage.value);
    return false;
  }
};

/**
 * 拒绝来电
 * @returns {Promise<boolean>} - 是否成功拒绝
 */
const rejectCall = async () => {
  try {
    if (callState.value !== CALL_STATES.INCOMING || !currentSession.value) {
      throw new Error('没有可拒绝的来电');
    }

    // 通过API拒绝通话
    const response = await rejectCallSession({
      session_id: currentSession.value.sessionId,
      user_id: userStore.userData.user_id
    });

    if (response) {
      resetCallState();
      return true;
    } else {
      throw new Error('拒绝通话失败');
    }
  } catch (error) {
    console.error('拒绝通话错误:', error);
    resetCallState();
    return false;
  }
};

/**
 * 结束当前通话
 * @returns {Promise<boolean>} - 是否成功结束
 */
const endCall = async () => {
  try {
    if (
      !currentSession.value ||
      (callState.value !== CALL_STATES.CONNECTED && callState.value !== CALL_STATES.OUTGOING)
    ) {
      throw new Error('没有可结束的通话');
    }

    console.log('结束通话...', currentSession.value);
    // 如果是创建者，结束会话
    if (currentSession.value.creator_id === userStore.userData.user_id) {
      await endCallSession({
        session_id: currentSession.value.session_id,
        user_id: userStore.userData.user_id
      });
    } else {
      // 否则只是离开通话
      await leaveCallSession({
        session_id: currentSession.value.session_id,
        user_id: userStore.userData.user_id
      });
    }

    // 离开声网频道
    await leaveChannel();

    // 重置通话状态
    resetCallState();
    return true;
  } catch (error) {
    console.error('结束通话错误:', error);
    // 仍然尝试清理本地状态
    await leaveChannel();
    resetCallState();
    return false;
  }
};

/**
 * 处理远程用户结束通话
 */
const handleCallEnded = () => {
  // 离开声网频道
  leaveChannel();

  // 重置通话状态
  resetCallState();

  // 显示通知
  ElMessage.info('对方已结束通话');
};

/**
 * 启动通话计时器
 */
const startCallTimer = () => {
  callStartTime.value = Date.now();
  durationTimer.value = setInterval(() => {
    callDuration.value = Math.floor((Date.now() - callStartTime.value) / 1000);
  }, 1000);
};

/**
 * 重置通话状态
 */
const resetCallState = () => {
  callState.value = CALL_STATES.IDLE;
  currentSession.value = null;
  currentChannel.value = '';
  remoteUser.value = null;
  errorMessage.value = '';
  isCallActive.value = false;
  participants.value = [];

  if (durationTimer.value) {
    clearInterval(durationTimer.value);
    durationTimer.value = null;
  }
  callDuration.value = 0;
  callStartTime.value = null;
};

/**
 * 获取当前用户的活动通话会话
 * @returns {Promise<Object|null>} - 活动通话会话或null
 */
const getActiveCallSession = async () => {
  try {
    const response = await fetchActiveCallSession(userStore.userData.user_id);

    if (response) {
      currentSession.value = response;
      isCallActive.value = true;
      callState.value = CALL_STATES.CONNECTED;

      // 更新参与者列表
      if (response.participants && Array.isArray(response.participants)) {
        participants.value = response.participants;
      }

      return response;
    }
    return null;
  } catch (error) {
    console.error('获取活动通话失败:', error);
    return null;
  }
};

/**
 * 邀请用户加入通话
 * @param {Object} user - 要邀请的用户
 * @returns {Promise<boolean>} - 是否成功邀请
 */
const inviteToCall = async user => {
  try {
    if (!currentSession.value || callState.value !== CALL_STATES.CONNECTED) {
      throw new Error('没有活动的通话可以邀请用户');
    }
    const response = await addParticipant({
      session_id: currentSession.value.session_id,
      user_id: userStore.userData.user_id,
      target_user_ids: [user.user_id]
    });

    if (response) {
      // 添加到参与者列表
      if (!participants.value.some(p => p.user_id === user.user_id)) {
        participants.value.push({
          user_id: user.user_id,
          user_name: user.user_name,
          isCreator: false,
          status: 'invited'
        });
      }

      ElMessage.success(`已邀请 ${user.user_name} 加入通话`);
      return true;
    } else {
      throw new Error('邀请用户失败');
    }
  } catch (error) {
    console.error('邀请用户错误:', error);
    ElMessage.error(`邀请用户失败: ${error.message}`);
    return false;
  }
};

/**
 * 格式化通话时长为MM:SS
 * @returns {string} - 格式化后的时长
 */
const getFormattedDuration = () => {
  const minutes = Math.floor(callDuration.value / 60);
  const seconds = callDuration.value % 60;
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

export {
  callState,
  currentSession,
  currentChannel,
  remoteUser,
  errorMessage,
  callDuration,
  isCallActive,
  participants,
  CALL_STATES,
  initiateCall,
  handleIncomingCall,
  answerCall,
  rejectCall,
  endCall,
  handleCallEnded,
  getActiveCallSession,
  getFormattedDuration,
  startCallTimer,
  inviteToCall,
  forcePlayRemoteAudio,
  troubleshootAudio
};
