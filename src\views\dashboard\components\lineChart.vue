<!--  线图 -->
<template>
  <div class="pd16">
    <el-empty v-if="uavTotalDistance.length == 0" description="暂无数据">
      <template #image>
        <img src="../../../assets/empty_home.png">
      </template>
    </el-empty>
    <div v-else :id="id" :class="className" :style="{ height, width }" />
  </div>
</template>

<script setup>
import * as echarts from 'echarts';
import { getFlyMileage } from '@/api/dashboard';
import { onMounted } from 'vue';
import moment from 'moment';
const uavTotalDistance = ref([]);
const dateList = ref([]);
const props = defineProps({
  id: {
    type: String,
    default: 'barChart'
  },
  className: {
    type: String,
    default: ''
  },
  width: {
    type: String,
    default: '200px',
    required: true
  },
  height: {
    type: String,
    default: '200px',
    required: true
  }
});

const options = {
  grid: {
    left: '2%',
    right: '3%',
    top: '14%',
    bottom: '14%',
    containLabel: true
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999'
      }
    }
  },
  xAxis: [
    {
      type: 'category',
      boundaryGap: false,
      offset: 10,
      data: dateList.value
    }
  ],
  axisTick: {
    //坐标轴刻度设置
    show: true, //是否显示坐标轴刻度
    inside: true, //坐标轴刻度指向        (true表示向上   false表示向下)
    alignWithLabel: true, //刻度线是否和标签对齐
    length: 1, //坐标轴刻度长度
    lineStyle: {
      //坐标轴刻度的样式
      color: '#B7D9FD', //坐标轴刻度的颜色
      width: 2, //坐标轴刻度的粗细程度    (用数字表示)
      type: 'solid' //坐标轴刻度的类型        (可选solid  dotted  dashed)
    }
  },
  axisLabel: {
    color: '#B7D9FD', //坐标轴刻度文字的颜色
    fontSize: 12, //坐标轴刻度文字的大小         (用数字表示)
    fontWeight: 'lighter', //坐标轴刻度文字的加粗程度    (可选bold   bolder  lighter  normal)
    fontstyle: 'normal' //坐标轴刻度文字的样式          (可选normal  italic   oblique)
  },

  yAxis: [
    {
      type: 'value',
      min: 0,
      splitLine: {
        lineStyle: {
          type: 'dashed', // 将 Y 轴的分割线设置为虚线
          color: '#2E90FA'
        }
      },
      name: '千米',
      nameTextStyle: {
        //坐标轴名称的文字样式
        color: '#B7D9FD',
        fontSize: '12',
        padding: [5, 45, 5, 0]
      },
      axisLabel: {
        formatter: '{value} ',
        color: '#B7D9FD',
        fontSize: '12'
      }
    }
  ],
  series: [
    {
      name: '飞行里程',
      type: 'line',
      yAxisIndex: 0,
      data: uavTotalDistance,
      itemStyle: {
        color: '#409EFF'
      }
    }
  ]
};
//使用moment.js 获取今天及七天前的日期 类似['2024/5/1', '2024/5/2', '2024/5/3', '2024/5/4', '2024/5/5', '2024/5/6', '2024/5/7']
function getLastSevenDays() {
  const dates = [];
  const today = moment(); // 获取今天的日期
  for (let i = 0; i < 7; i++) {
    dates.push(today.subtract(i === 0 ? 0 : 1, 'days').format('YYYY/M/D'));
  }
  options.xAxis[0].data = dates.reverse(); // 反转数组，使日期按顺序排列
}

//获取总的飞行里程
async function getUavTotalDistance() {
  //设为异步函数 数据整理完以后再渲染echart
  try {
    const res = await getFlyMileage();
    console.log('res',res)
    uavTotalDistance.value = res.map(item=>item.total_distance_km);
    dateList.value = res.map(item=>item.flight_date);
    options.series[0].data = uavTotalDistance.value ? uavTotalDistance.value : [];
    options.xAxis[0].data = dateList.value ? dateList.value : []
    if(res.length >0) {
      setTimeout(()=>{
        const chart = echarts.init(document.getElementById(props.id));
        chart.setOption(options);
      },500)
    }
  } catch (error) {
    options.series[0].data = [];
  }


  // window.addEventListener('resize', () => {
  //   chart.resize();
  // });
}

onMounted(() => {
  getLastSevenDays();
  getUavTotalDistance();
});
</script>
<style scoped>
.pd16 {
  padding: 16px;
}
</style>
