import * as Cesium from 'cesium';
import { arrayToCartesian3, c3toDegress, toCartesian3, toNumber } from './common';
import * as egm96 from 'egm96-universal';
import * as turf from '@turf/turf';
/**
 *
 * @param {*} viewer
 * @param {*} positions c3 坐标数组
 */
export const creatPolygon = (viewer, positions) => {
  let zIndex = 10;
  return viewer.entities.add({
    polygon: {
      hierarchy: new Cesium.CallbackProperty(function (time, result) {
        let hierarchyTemp = new Cesium.PolygonHierarchy(positions, []);
        return hierarchyTemp;
      }, false),
      clampToGround: true,
      show: true,
      fill: true,
      material: Cesium.Color.ORANGE.withAlpha(0.5),
      zIndex: zIndex,
      height: zIndex * 1, //多层次
      outline: true,
      outlineColor: Cesium.Color.ALICEBLUE,
      outlineWidth: 2,
      width: 20
    },
    clampToGround: true
  });
};

/**
 *
 * @param {*} viewer
 * @param {*} point 点对象
 * point.position c3 坐标数组
 * point.id id
 */
export const createPoint = (viewer, point) => {
  return viewer.entities.add({
    name: point.id,
    label: {
      text: point.id,
      font: '20px "微软雅黑", Arial, Helvetica, sans-serif', // 调整字体大小和字体
      fillColor: Cesium.Color.WHITE, // 文字填充颜色
      outlineColor: Cesium.Color.fromCssColorString('#2E86C1'), // 文字描边颜色
      outlineWidth: 2, // 文字描边宽度
      showBackground: true, // 显示背景
      backgroundColor: Cesium.Color.fromCssColorString('#2E86C1').withAlpha(0.7), // 背景颜色和透明度
      backgroundPadding: new Cesium.Cartesian2(7, 5), // 背景内边距
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE, //设置描边的风格，上面的参数才会起效果
      horizontalOrigin: Cesium.HorizontalOrigin.RIGHT,
      verticalOrigin: Cesium.VerticalOrigin.TOP
    },
    point: {
      color: Cesium.Color.fromCssColorString('#0072C6'), // 蓝色
      pixelSize: 10, // 大小
      outlineColor: Cesium.Color.WHITE, // 白色边框
      outlineWidth: 2, // 边框宽度
      disableDepthTestDistance: Number.POSITIVE_INFINITY // 点总是在最上层
      // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND, // 贴地显示
    },
    position: point.position,
    disableDepthTestDistance: Number.POSITIVE_INFINITY //永远可视，不被地形压盖
  });
};

/**
 * 设置起点
 * @param {*} viewer
 * @param {*} point
 * @returns
 */
export const createStartPoint = (viewer, point) => {
  return viewer.entities.add({
    name: point.id,
    billboard: {
      image: new URL('../../../../assets/plan/wrj/起点.png', import.meta.url).href,
      width: 30,
      height: 30,
      disableDepthTestDistance: Number.POSITIVE_INFINITY, // 点总是在最上层
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 设置垂直方向的原点为顶部
      heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND, // 相对于地形高度
      pixelOffset: new Cesium.Cartesian2(0, -1) // 向上偏移 12 个像素
    },
    position: point.position,
    disableDepthTestDistance: Number.POSITIVE_INFINITY //永远可视，不被地形压盖
  });
};

/**
 * 设置终点
 * @param {*} viewer
 * @param {*} point
 * @returns
 */
export const createEndtPoint = (viewer, point) => {
  return viewer.entities.add({
    name: point.id,
    billboard: {
      image: new URL('../../../../assets/plan/wrj/终点.png', import.meta.url).href,
      width: 30,
      height: 30,
      disableDepthTestDistance: Number.POSITIVE_INFINITY, // 点总是在最上层
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 设置垂直方向的原点为顶部
      heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND, // 相对于地形高度
      pixelOffset: new Cesium.Cartesian2(0, -1) // 向上偏移 12 个像素
    },
    position: point.position,
    disableDepthTestDistance: Number.POSITIVE_INFINITY //永远可视，不被地形压盖
  });
};

/**
 * 设置机场
 * @param {*} viewer
 * @param {*} point
 * @returns
 */
export const createAirPortPoint = (viewer, point) => {
  return viewer.entities.add({
    name: point.id,
    billboard: {
      image: new URL('../../../../assets/plan/wrj/无人机起降场.png', import.meta.url).href,
      width: 48,
      height: 48,
      disableDepthTestDistance: Number.POSITIVE_INFINITY // 点总是在最上层
    },
    position: point.position,
    disableDepthTestDistance: Number.POSITIVE_INFINITY //永远可视，不被地形压盖
  });
};

// 生成圆形背景
function generateCircleCanvas(radius, color, borderColor) {
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  canvas.width = radius * 2;
  canvas.height = radius * 2;
  // 绘制圆形背景
  context.beginPath();
  context.arc(radius, radius, radius, 0, 2 * Math.PI);
  context.fillStyle = color.toCssColorString();
  context.fill();
  // 绘制白色边框
  context.beginPath();
  context.arc(radius, radius, radius, 0, 2 * Math.PI);
  context.strokeStyle = borderColor.toCssColorString();
  context.lineWidth = 2;
  context.stroke();
  return canvas.toDataURL();
}

/**
 *
 * @param {*} viewer
 * @param {*} positions 经纬度数组 二维数组 [[x,y,z],[]...]
 * @returns
 */
export const createPolyline = (viewer, positions) => {
  // 检查是否是经纬度数组 一维数组和二维数组 直接抓暖为 c3 对象
  const c3Pois = arrayToCartesian3(positions);
  return viewer.entities.add({
    polyline: {
      positions: c3Pois,
      width: 2,
      arcType: Cesium.ArcType.NONE,
      disableDepthTestDistance: Number.POSITIVE_INFINITY, //永远可视，不被地形压盖
      material: Cesium.Color.fromCssColorString('#00FF00')
    },
    clampToGround: true
  });
};

/**
 * 计算凸包
 * @param {*} points 经纬度坐标 
 *[[118.029683530222, 24.5746352438717, 100], 
  [118.029683530222, 24.5746352438717, 100]]
 * @returns
 */
export const convexHull = points => {
  // 按照 x 坐标升序排列
  points.sort((a, b) => a[0] - b[0]);
  const n = points.length;
  const hull = [];

  // 计算下凸包
  for (let i = 0; i < n; i++) {
    while (hull.length >= 2 && crossProduct(hull[hull.length - 2], hull[hull.length - 1], points[i]) <= 0) {
      hull.pop();
    }
    hull.push(points[i]);
  }

  // 计算上凸包
  let t = hull.length + 1;
  for (let i = n - 2; i >= 0; i--) {
    while (hull.length >= t && crossProduct(hull[hull.length - 2], hull[hull.length - 1], points[i]) <= 0) {
      hull.pop();
    }
    hull.push(points[i]);
  }

  // 去掉重复的点
  hull.pop();

  return hull;
};
// 计算三个点的叉乘
function crossProduct(p1, p2, p3) {
  return (p2[0] - p1[0]) * (p3[1] - p1[1]) - (p2[1] - p1[1]) * (p3[0] - p1[0]);
}

export function generateUUID() {
  let d = new Date().getTime();
  if (typeof performance !== 'undefined' && typeof performance.now === 'function') {
    d += performance.now(); // use high-precision timer if available
  }
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (d + Math.random() * 16) % 16 | 0;
    d = Math.floor(d / 16);
    return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
  });
}

export function generateNumberId() {
  const maxId = 10000;
  let id = Math.floor(Math.random() * maxId);
  return id.toString();
}

/**
 * 获取高度值
 * getRelativeHeight 方法获取的高度就是相对于地形的高度（AGL，Above Ground Level）
 * 这个高度表示航点相对于地形表面的高度，考虑了地形起伏。
 * 如果没有地形数据，则会使用默认的椭球高。
 * @param {*} viewer
 * @param {*} position c3 对象
 * @returns 返回绝对高度和相对高度 
 *  {
          absoluteHeight: absoluteHeight,
          terrainHeight: terrainHeight
    }
 */
export function getHeights(viewer, position) {
  position = toCartesian3(position);
  const cartographicPosition = Cesium.Cartographic.fromCartesian(position);
  return new Promise((resolve, reject) => {
    let absoluteHeight = toNumber(cartographicPosition.height, 3);
    Cesium.sampleTerrainMostDetailed(viewer.terrainProvider, [cartographicPosition])
      .then(function (updatedPositions) {
        let terrainHeight = toNumber(updatedPositions[0].height, 3);
        // 返回绝对高度 和 地形高度
        resolve({
          absoluteHeight: absoluteHeight,
          terrainHeight: terrainHeight
        });
      })
      .catch(function (error) {
        reject(error);
      });
  });
}

/**
 * 获取相对高度值
 * getRelativeHeight 方法获取的高度就是相对于地形的高度（AGL，Above Ground Level）
 * 这个高度表示航点相对于地形表面的高度，考虑了地形起伏。
 * 如果没有地形数据，则会使用默认的椭球高。
 * @param {*} viewer
 * @param {*} cartesianPosition c3 对象
 * @returns
 */
export function getRelativeHeight(viewer, cartesianPosition) {
  cartesianPosition = toCartesian3(cartesianPosition);
  const cartographicPosition = Cesium.Cartographic.fromCartesian(cartesianPosition);
  // 检查地形提供器是否为默认的 EllipsoidTerrainProvider
  if (viewer.terrainProvider instanceof Cesium.EllipsoidTerrainProvider) {
    // 没有地形数据，返回椭球高
    return Promise.resolve(cartographicPosition.height);
  } else {
    const absoluteHeight = toNumber(cartographicPosition.height, 3);
    // 获取地形高
    return Cesium.sampleTerrainMostDetailed(viewer.terrainProvider, [cartographicPosition]).then(function (
      updatedPositions
    ) {
      // 获取地形高度 获取绝对高度
      const terrainHeight = toNumber(updatedPositions[0].height, 3);
      return absoluteHeight - terrainHeight;
      //   return {
      //     absoluteHeight: absoluteHeight,
      //     terrainHeight: terrainHeight
      //   };
    });
  }
}

export function getTerrainHeight(viewer, cartesianPosition) {
  cartesianPosition = toCartesian3(cartesianPosition);
  const cartographicPosition = Cesium.Cartographic.fromCartesian(cartesianPosition);
  // 检查地形提供器是否为默认的 EllipsoidTerrainProvider
  if (viewer.terrainProvider instanceof Cesium.EllipsoidTerrainProvider) {
    const absoluteHeight = toNumber(cartographicPosition.height, 3);
    // 没有地形数据，返回椭球高
    return Promise.resolve(absoluteHeight);
  } else {
    // 获取地形高
    return Cesium.sampleTerrainMostDetailed(viewer.terrainProvider, [cartographicPosition]).then(function (
      updatedPositions
    ) {
      // const terrainHeight = toNumber(updatedPositions[0].height, 3);
      if (updatedPositions && updatedPositions.length === 0) {
        return null;
      }
      let cartographic = updatedPositions[0];
      // 地理坐标（弧度制）转化为经纬度坐标
      let longitude = toNumber(Cesium.Math.toDegrees(cartographic.longitude), 7);
      let latitude = toNumber(Cesium.Math.toDegrees(cartographic.latitude), 7);
      let height = toNumber(cartographic.height, 3);
      return {
        terrainHeight: Number(height), // 获取地形高度 获取绝对高度
        position: toCartesian3([longitude, latitude, height]) // 地形上的点
      };
    });
  }
}

/**
 * 获取海平面的高度（也称为椭球高度或绝对高度）。这个高度值是相对于WGS84参考椭球的高度
 * @param {*} cartesianPosition  c3 对象
 * @returns
 */
export function getAbsoluteHeight(cartesianPosition) {
  const cartographicPosition = Cesium.Cartographic.fromCartesian(cartesianPosition);
  return toNumber(cartographicPosition.height, 3);
}

/**
 * 通过c3坐标计算egm96 大地高  // 定义函数，计算EGM96大地水准面的高度
 * @param {*} cartesianPosition  c3 对象
 */
export function ellipsoidToEgm96(longitude, latitude, height) {
  // 转换为相对于 EGM96 的高度
  const egm96Height = egm96.ellipsoidToEgm96(latitude, longitude, height);
  let egm96C3 = toCartesian3([longitude, latitude, egm96Height]);
  return {
    ellipsoidHeight: toNumber(height, 3),
    egm96Height: toNumber(egm96Height, 3),
    egm96Cartesian: egm96C3
  };
}

/**
 * egm96 坐标转椭球高
 * @param {*} longitude 118.000
 * @param {*} latitude 24.111
 * @param {*} height 254.2
 * @returns
 */
export function egm96ToEllipsoid(longitude, latitude, height) {
  // 转换为相对于 EGM96 的高度
  const ellipsoidAlt = egm96.egm96ToEllipsoid(latitude, longitude, height);
  let c3 = toCartesian3([longitude, latitude, ellipsoidAlt]);
  return {
    ellipsoidHeight: toNumber(ellipsoidAlt, 3),
    cartesian3: c3
  };
}

/**
 * //经纬度转墨卡托 算法转换 ok
 * @param {*} long
 * @param {*} lat
 * @param {*} height
 * @returns
 */
export const wgs84ToMkt = (long, lat, height = 0) => {
  const mercator = {
    x: 0,
    y: 0,
    z: height
  };
  const earthRad = 6378137.0;
  mercator.x = ((long * Math.PI) / 180) * earthRad;
  const a = (lat * Math.PI) / 180;
  mercator.y = (earthRad / 2) * Math.log((1.0 + Math.sin(a)) / (1.0 - Math.sin(a)));
  console.log('mercator', mercator);
  return mercator; //[12727039.383734727, 3579066.6894065146,z]
};

//墨卡托转经纬度
export const mktToWgs84 = (x, y) => {
  const lnglat = {
    long: 0,
    lat: 0
  };
  lnglat.long = (x / mercatorMax) * 180;
  const mmy = (y / mercatorMax) * 180;
  lnglat.lat = (180 / Math.PI) * (2 * Math.atan(Math.exp((mmy * Math.PI) / 180)) - Math.PI / 2);
  return lnglat;
};

/**
 * 像素转经纬度 Cartesian2
 * @param {*} px [0,1] 或 Cartesian2
 * @returns
 */
export function px2LongitudeAndLatitude(viewer, px) {
  // 判断px是 Cartesian2 还是数组
  let pick1 = null;
  if (px instanceof Cesium.Cartesian2) {
    pick1 = px;
  } else if (Array.isArray(px) && px.length === 2) {
    pick1 = new Cesium.Cartesian2(px[0], px[1]);
  } else {
    throw new Error('Invalid input format. px must be a Cesium.Cartesian2 or an array of [x, y].');
  }
  const cartesian3 = viewer.scene.globe.pick(viewer.camera.getPickRay(pick1), viewer.scene);
  // 这里转为经纬度
  const cartographic = Cesium.Cartographic.fromCartesian(cartesian3);
  const x = Cesium.Math.toDegrees(cartographic.longitude);
  const y = Cesium.Math.toDegrees(cartographic.latitude);
  const z = cartographic.height;
  return {
    lng: x,
    lat: y,
    height: z
  };
}

export function getBoundingSphere(positions) {
  // 判断px是 Cartesian2 还是数组
  let positionsC3 = positions.map(p => {
    return toCartesian3(p);
  });
  return Cesium.BoundingSphere.fromPoints(positionsC3);
}

/**
 * 根据经纬度数组,计算出包围矩形
 * @param {Array<{lng: number, lat: number}>} positions - 经纬度数组
 * @returns {Cesium.Rectangle} - 包围矩形
 */
export function getBoundingRectangle(positions, offset = 0.01) {
  let west = Infinity,
    south = Infinity,
    east = -Infinity,
    north = -Infinity;
  positions.forEach(pos => {
    west = Math.min(west, pos.lng);
    south = Math.min(south, pos.lat);
    east = Math.max(east, pos.lng);
    north = Math.max(north, pos.lat);
  });
  return Cesium.Rectangle.fromDegrees(west - offset, south - offset, east + offset, north + offset);
}
