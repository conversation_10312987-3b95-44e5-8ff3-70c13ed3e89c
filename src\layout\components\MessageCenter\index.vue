<template>
  <el-badge
    class="icon-basdge setting-container cursor-pointer"
    :value="messageCount"
    :hidden="!messageCount"
    :max="99"
    @click="messageDialogRef.handleOpen()"
  >
    <i-ep-bell class="el-icon-bell mx-3" />
  </el-badge>
  <!-- <MessageDialog ref="messageDialogRef" :getMessageCount="getMessageCount" /> -->
  <!-- <PopVesionMessage ref="popVesionMessageRef" /> -->
</template>

<script>
export default {
  name: 'settings'
};
</script>

<script setup>
import { onUnmounted, onMounted } from 'vue';

// import _ from 'lodash';
// import MessageDialog from './messageDialog.vue';
// import {
//   getMessageCount as _getMessageCount,
//   getLastUnreadVersion
// } from '@/api/messageCenter';

// const popVesionMessageRef = ref('popVesionMessageRef');
const messageCount = ref('');
const compareInterval = ref(null);
const messageDialogRef = ref('messageDialogRef');

// const lastVersion = ref({});

// const clientVersion = computed(() => {
//   return import.meta.env.VITE_CLIENT_VERSION || '';
// });
// const getMessageCount = async () => {
//   const res = await _getMessageCount();
//   const messageArr = [];
//   for (const i in res.data) {
//     messageArr.push(res.data[i]);
//   }
//   messageCount.value = messageArr.reduce((pre, cur) => {
//     return pre + cur;
//   });
//   return res.data || {};
// };

// const checkVersionContent = async () => {
//   try {
//     const res = await getLastUnreadVersion();
//     lastVersion.value = res.data || {};
//     const comparisonResult = compareVersions(
//       clientVersion.value,
//       _.isEmpty(res.data) ? '' : res.data.version
//     );
//     if (comparisonResult < 0) {
//       popVesionMessageRef.value.handleOpen(res.data);
//     }
//   } catch (error) {
//     console.log('error: ', error);
//   }
// };
// 版本对比
// const compareVersions = (currentVersion, latestVersion) => {
//   if (!currentVersion || !latestVersion) return 0;
//   const currentParts = currentVersion.split('.'); // 将当前版本拆分为部分
//   const latestParts = latestVersion.split('.'); // 将最新版本拆分为部分

//   // 循环比较每个部分的大小
//   for (let i = 0; i < Math.max(currentParts.length, currentParts.length); i++) {
//     const currentPart = parseInt(currentParts[i] || 0, 10); // 将部分转换为整数，默认为 0
//     const latestPart = parseInt(latestParts[i] || 0, 10); // 将部分转换为整数，默认为 0

//     if (currentPart < latestPart) {
//       return -1; // 当前版本小于最新版本
//     } else if (currentPart > latestPart) {
//       return 1; // 当前版本大于最新版本
//     }
//   }

//   return 0; // 当前版本等于最新版本
// };

onMounted(() => {
  // getMessageCount();
  // checkVersionContent();
  // compareInterval.value = setInterval(() => {
  //   getMessageCount();
  //   checkVersionContent();
  // }, 300000);
});

onUnmounted(() => {
  clearInterval(compareInterval.value);
});
</script>

<style lang="scss" scoped>
:deep(.el-badge__content) {
  transform: translateY(-50%) translateX(40%);
}

.el-icon-bell {
  color: #fff;
  font-size: 18px;
}
</style>
