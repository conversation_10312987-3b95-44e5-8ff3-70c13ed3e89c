<script>
export default { name: 'TaskInformationList' };
</script>

<script setup>
import { ref, reactive, toRaw } from 'vue';
import { getAirTaskList } from '@/api/task';
import DetailDialog from './DetailDialog.vue';
const queryParams = reactive({
  page_num: 1,
  page_size: 20,
});
const loading = ref(false);
const dataList = ref([]);
const total = ref(0);
const deviceSn = ref('')
const emit = defineEmits(['onClick','select']);
const dialog = reactive({
  visible: false
});
let detailData = reactive({});

/**
 * 查询
 */
 function handleQuery(params={}) {
	loading.value = true
	getAirTaskList({
    ...params,
		...queryParams,
  }).then(data => {
    const { records } = data;
    dataList.value = records || [];
    queryParams.page_num = data.current;
		setTimeout(()=>{
			loading.value = false
		},500)
    total.value = data.total;
  });
}

function load () {
	const totalPage = Math.ceil(total.value / 20)
	if(queryParams.page_num >= totalPage || dataList.value.length < 20) {
		return;
	}
	queryParams.page_num += 1
	
	getAirTaskList({
		...queryParams
  }).then(data => {
		const { records } = data;
		let arr = toRaw(dataList.value).concat(records)
		dataList.value = arr
  });
}

onMounted(() => {
  handleQuery();
});

function locationAirport (item) {
	deviceSn.value = toRaw(item).device_sn
	emit('select',item)
}

/**
 *
 * @param dicTypeId 应用ID
 */
 function openDialog(row) {
  dialog.visible = true;
  Object.keys(detailData).map(key => {
    delete detailData[key];
  });
  if (row) {
    Object.assign(detailData, { ...row });
  }
}
</script>

<template>
	<div class="alarm-ul" v-infinite-scroll="load" v-if="dataList.length > 0" v-loading="loading">
		<div class="alarm-item" v-for="(item,index) in dataList" :key="index">
			<div class="task-name" @click="locationAirport(item)">
				{{ item.name }}
			</div>
			<div class="line-name" v-if="item.wayline_file_name">
				{{ `${item.wayline_file_name}/${item.flight_task_id}` }}
			</div>
			<div class="line-name" v-if="!item.wayline_file_name">
				{{ `${item.flight_task_id}` }}
			</div>
			<div class="drone-name" @click="locationAirport(item?.children)">
				<div class="line-name ellipsis" v-if="item.job_type != 2" style="width: 260px;" :title="item.dock_sn_desc">{{ item.dock_sn_desc }}</div>
				<div class="line-name ellipsis" v-if="item.job_type == 2" style="width: 260px;" :title="item.drone_sn_desc">{{ item.drone_sn_desc }}</div>
				<div class="line-name">{{ item.task_type_desc }}</div>
			</div>
			<div class="flex">
				<div>
					<el-button type="primary" size="small" class="mr8" @click="openDialog(item)">详情</el-button>
				</div>
				<div>
					<span v-if="item.status == 0" class="open-status">开启</span>
					<span v-else-if="item.status == 2" class="off-status">结束</span>
					<span v-else class="stop-status">暂停</span>
				</div>
			</div>
		</div>
    <DetailDialog v-model:visible="dialog.visible" :form-data="detailData" :isDialogMode="true" />
	</div>
	<el-empty description="暂无数据" v-else>
		<template #image>
			<img src="../../../assets/empty_home.png">
		</template>
	</el-empty>
</template>

<style lang="scss" scoped>
:deep(.el-tabs__header) {
	margin-bottom: 0;
}

.right-icon{
	margin-top: 5px;
	display: flex;
}
.open-status {
	display: inline-block;
	text-align: center;
	width: 32px;
	height: 24px;
	background: rgba(42,139,125,0.30);
	border-radius: 2px;
	font-family: SourceHanSansSC-Regular;
	font-size: 12px;
	color: #39BFA4;
	line-height: 24px;
	font-weight: 400;
}
.stop-status {
	display: inline-block;
	text-align: center;
	width: 32px;
	height: 24px;
	background: rgba(145, 124, 40,0.30);
	border-radius: 2px;
	font-family: SourceHanSansSC-Regular;
	font-size: 12px;
	color: rgb(145, 124, 40);
	line-height: 24px;
	font-weight: 400;
}
.off-status {
	display: inline-block;
	text-align: center;
	width: 32px;
	height: 24px;
	background: rgba(152, 162, 179,0.2);
	border-radius: 2px;
	font-family: SourceHanSansSC-Regular;
	font-size: 12px;
	color: rgb(152, 162, 179);
	line-height: 24px;
	font-weight: 400;
}
.grey {
	display: inline-block;
	background: rgba($color: #98A2B3 , $alpha: 0.2);
	border-radius: 2px;
	text-align: center;
	line-height: 24px;
	height: 24px;
	font-weight: 400;
	padding: 0 4px;
	font-family: SourceHanSansSC-Regular;
	font-size: 12px;
	color: #98A2B3;
	text-align: center;
}

.pointer {
	cursor: pointer;
}
.currentColor {
	background: #175091 !important;
}
:deep(.el-tabs__header) {
	border-bottom: 1px solid #344054;
}
:deep(.el-tabs__nav-wrap) {
	background: #11253E;
	color: #fff;
	height: 38px;
	line-height: 38px;
	font-family: SourceHanSansSC-Bold;
	font-size: 14px;
	text-align: left;
	font-weight: 700;
	border-bottom: 1px solid #344054;
	padding-left: 8px;
}
::-webkit-scrollbar {
  width: 8px;  /* 设置滚动条的宽度 */
}
::-webkit-scrollbar-thumb {
  background-color: rgba(46,144,255,0.5);
	border-radius: 2px; /* 设置滚动条滑块的背景色 */
}
.alarm-title {
	height: 38px;
	line-height: 38px;
	background: #11253E;
	color: #fff;
	font-family: SourceHanSansSC-Bold;
	font-size: 14px;
	text-align: left;
	font-weight: 700;
	border-bottom: 1px solid #344054;
	padding-left: 8px;
}
.alarm-ul {
	background: #001129;
	padding: 8px 8px 0 8px;
	height: 99%;
	overflow: auto;
	.alarm-item {
		min-height: 76px;
		background: #11253E;
		margin-bottom: 12px;
		padding: 8px;
		font-family: SourceHanSansSC-Regular;
		font-size: 14px;
		color: #F5F6F8;
		text-align: left;
		line-height: 22px;
		font-weight: 400;
		.list {
			height: 38px;
			line-height: 38px;
			vertical-align: middle
		}
		.drone-name {
			margin-top: 8px;
			display: flex;
			justify-content: space-between;
		}
		.alarm-time {
			font-family: SourceHanSansSC-Regular;
			font-size: 12px;
			color: #98A2B3;
			text-align: right;
			line-height: 20px;
			font-weight: 400;
		}
		.alarm-address{
			font-family: SourceHanSansSC-Regular;
			font-size: 14px;
			color: #FFFFFF;
			text-align: justify;
			line-height: 22px;
			font-weight: 400;
			margin-top: 8px;
			overflow: hidden;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			text-overflow: ellipsis;
			white-space: normal;
		}
		.task-name {
			font-family: SourceHanSansSC-Regular;
			font-size: 14px;
			color: #FFFFFF;
			text-align: justify;
			line-height: 22px;
			font-weight: 400;
			margin-bottom: 8px;
		}
		.line-name {
			font-family: SourceHanSansSC-Regular;
			font-size: 12px;
			color: #98A2B3;
			line-height: 18px;
			font-weight: 400;
		}
	}
}
.flex {
	height: 38px;
	line-height: 38px;
	display: flex;
	justify-content: space-between;
}
</style>
