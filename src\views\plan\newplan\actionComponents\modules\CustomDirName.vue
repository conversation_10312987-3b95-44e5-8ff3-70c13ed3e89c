<template>
  <div class="photo-wrapper bg-light-blue">
    <!-- <span class="title">{{ titleCn }}</span> -->
    <div class="header">
      <span class="title">{{ title }}</span>
      <SuffixEdit v-model="param.wpml_fileSuffix" @changeHandle="onSuffixChangeHandle" />
    </div>
  </div>
</template>
<script>
export default {
  name: 'CustomDirName'
};
</script>
<script setup>
import '../../../style/common.css';
import { onMounted, onUnmounted, defineExpose, reactive, ref, toRefs } from 'vue';
import { ElInput } from 'element-plus';
import 'element-plus/dist/index.css';
import { Edit, Check, Close } from '@element-plus/icons-vue';
import { useEditTrackerStore } from '@/store/modules/dataEditTracker.js';
import SuffixEdit from './components/SuffixEdit.vue';
const editTrackerStore = useEditTrackerStore();
// 组件要使用以下方式导出后 才可以被其他组件识别
//#region 初始化
const titleCn = ref('新文件夹名称');
const title = ref('DJI_YYYYMMDDhhmm_XXX_');
const deviceType = ref('Matrice_3TD');
const cameraSelectDataRect = reactive({
  deviceType: deviceType.value,
  wpml_payloadLensIndex: 'visible,ir', // 默认传入是 ”aaa,bbb,ccc“，
  wpml_useGlobalPayloadLensIndex: 1 // 跟随航线 1 自定义为 0
});
let curDeviceInfo = {};
let curAction = null;
const param = reactive({
  wpml_fileSuffix: '',
  wpml_payloadLensIndex: '', // 默认传入是 ”aaa,bbb,ccc“，
  wpml_useGlobalPayloadLensIndex: 1, // 跟随航线 1 自定义为 0
  wpml_payloadPositionIndex: 0 // 暂时默认为0 一般不在组件内变更该值
});
const btnCtrlRect = reactive({
  isEdit: true,
  isSave: false,
  isClose: false
});
//#endregion

//#region 对外暴露方法
/**
 * 设置组件数据
 * @param {*} options
 * @param {*} options.actionFuncParam // 动作参数
 * @param {*} options.deviceInfo // 设备信息
 * @param {*} options.action // 动作对象
 */
const setComponentData = options => {
  // 设置数据前先初始组件数据及界面
  const { actionFuncParam, action, deviceInfo } = options;
  //  获取设备信息
  const { droneSubEnumLabel } = deviceInfo;
  // 设备信息及型号
  curDeviceInfo = deviceInfo;
  curAction = action;
  deviceType.value = droneSubEnumLabel;
  const {
    wpml_fileSuffix = '',
    wpml_payloadLensIndex = '',
    wpml_payloadPositionIndex = 0,
    wpml_useGlobalPayloadLensIndex = 1
  } = actionFuncParam;
  // 设置当前的组件数据
  param.wpml_fileSuffix = wpml_fileSuffix;
  param.wpml_payloadLensIndex = wpml_payloadLensIndex;
  param.wpml_payloadPositionIndex = wpml_payloadPositionIndex;
  param.wpml_useGlobalPayloadLensIndex = wpml_useGlobalPayloadLensIndex;
  cameraSelectDataRect.deviceType = droneSubEnumLabel;
  cameraSelectDataRect.wpml_payloadLensIndex = wpml_payloadLensIndex;
  cameraSelectDataRect.wpml_useGlobalPayloadLensIndex = wpml_useGlobalPayloadLensIndex;
};

/**
 * 获取组件数据
 * return {Object} options
 * options.wpml_fileSuffix // 导出字符串 ""
 * options.wpml_payloadLensIndex // 导出字符串用逗号隔开 "aaa,bbb,ccc"
 */
const getComponentData = () => {
  return {
    ...param
  };
};

//#endregion

//#region 方法
// 相机类型选项改变后执行保存
// const onCameraTypeChangeHandle = data => {
//   param.wpml_payloadLensIndex = cameraSelectDataRect.wpml_payloadLensIndex;
//   param.wpml_useGlobalPayloadLensIndex = cameraSelectDataRect.wpml_useGlobalPayloadLensIndex;
//   setActionFuncParms();
// };

// 取消选中跟随航点 设置其他类型
const setActionFuncParms = () => {
  if (curAction) {
    curAction.wpml_actionActuatorFuncParam.wpml_fileSuffix = param.wpml_fileSuffix;
    curAction.wpml_actionActuatorFuncParam.wpml_payloadLensIndex = param.wpml_payloadLensIndex;
    curAction.wpml_actionActuatorFuncParam.wpml_useGlobalPayloadLensIndex = param.wpml_useGlobalPayloadLensIndex;
    curAction.wpml_actionActuatorFuncParam.wpml_payloadPositionIndex = param.wpml_payloadPositionIndex;
    editTrackerStore.dataTracker.markAsModified();
  }
};
const onSuffixChangeHandle = data => {
  if (data) {
    curAction.wpml_actionActuatorFuncParam.wpml_fileSuffix = param.wpml_fileSuffix;
    editTrackerStore.dataTracker.markAsModified();
  }
};

//#region 编辑相关属性和方法
const handleBtnCtrl = type => {
  switch (type) {
    case 'save':
      btnCtrlRect.isEdit = true;
      btnCtrlRect.isSave = false;
      btnCtrlRect.isClose = false;
      // 这里对象进行赋值
      setActionFuncParms();
      break;
    case 'edit':
      btnCtrlRect.isEdit = false;
      btnCtrlRect.isSave = true;
      btnCtrlRect.isClose = true;
      break;
    case 'close':
      param.wpml_fileSuffix = '';
      btnCtrlRect.isEdit = true;
      btnCtrlRect.isSave = false;
      btnCtrlRect.isClose = false;
      break;
    default:
      break;
  }
};
//#endregion

//#endregion

//#region 对外抛出方法
defineExpose({
  setComponentData,
  getComponentData
});
//#endregion

//#region 生命周期
onMounted(() => {});
onUnmounted(() => {});
//#endregion
</script>
<style lang="scss" scoped>
::v-deep .el-input .el-input__inner {
  font-size: 16px;
  color: #dadada;
}
::v-deep .el-input-numbert.is-disabled .el-input__wrapper {
  background-color: #11253e;
}
::v-deep .el-input .el-input__wrapper {
  background-color: #11253e;
  box-shadow: 0 0 0 1px #cfcfcf8f inset;
}
::v-deep .el-input .el-input__wrapper.is-focus {
  box-shadow: 0 0 0 1px #cfcfcf8f inset;
}

.photo-wrapper {
  position: absolute;
  width: 100%;
  background-color: rgb(49, 49, 49);
  color: white;
  padding: 10px 5px;
  .header {
  }
  .camera-select {
    margin-top: 5px;
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }
    .right {
      width: 80px;
      height: 30px;
      display: flex;
      align-items: center;
    }
  }
  .header-input {
    margin: 5px 0px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .btn {
      display: flex;
      align-items: center;
      margin-left: 10px;
    }
  }
}

.round {
  border: 5px;
  color: white;
}
.item {
  width: auto;
  padding: 2px 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  background: rgba(45, 140, 240, 0.35);
  color: #ffffff40;
  margin-left: 5px;
  user-select: none;
  &:hover {
    cursor: pointer;
  }
}
.active {
  color: #fff;
  background: #2d8cf0;
}
.notAllowed {
  cursor: not-allowed !important;
}

.color-blue {
  background: #2d8cf0;
  color: white;
}
</style>
