import request from '@/utils/request';
import { LINE_PATH, APPLICTION_WORKSPACES, API_VERSION,ALARM_PATH } from '../config/index';
import { useUserStoreHook } from '@/store/modules/user';

// 主路径
const BASE_URL = LINE_PATH + API_VERSION + APPLICTION_WORKSPACES;
//告警路径
const ALARM_URL = ALARM_PATH + API_VERSION;

// 创建航线任务
export function createFlightTask(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/flight-tasks`,
    method: 'post',
    data
  });
}

/**
 * 获取任务分页
 *
 * @param queryParams
 */
export function getTaskList(queryParams) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;

  return request({
    url: `${BASE_URL}/${workspace_id}/jobs`,
    method: 'get',
    params: queryParams
  });
}

/**
 * 获取任务分页
 *
 * @param queryParams
 */
export function getAirTaskList(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;

  return request({
    url: `/flightTask/api/v1/${workspace_id}/page-tasks`,
    method: 'post',
    data: data
  });
}

export function getFlightRecordList(data) {
  return request({
    url: `/wayline/api/v1/flight/record/page`,
    method: 'post',
    data: data
  });
}

export function deleteFlightRecordList(data) {
  return request({
    url: `/wayline/api/v1/flight/record/${data.job_id}`,
    method: 'delete',
  });
}

export function lockFlightRecord(params,data) {
  return request({
    url: `/wayline/api/v1/flight/record/${params.id}`,
    method: 'PUT',
    data: data
  });
}


/**
 * 获取机场任务-新增
 *
 * @param queryParams
 */
export function addAirTaskList(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;

  return request({
    url: `/flightTask/api/v1/${workspace_id}/tasks`,
    method: 'post',
    data: data
  });
}

/**
 * 获取机场任务-编辑
 *
 * @param queryParams
 */
export function editAirTaskList(data) {
  return request({
    url: `/flightTask/api/v1/task/name`,
    method: 'PUT',
    data: data
  });
}

/**
 * 获取机场任务-删除
 *
 * @param queryParams
 */
export function deleteAirTaskList(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `/flightTask/api/v1/${workspace_id}/tasks/${data.task_id}`,
    method: 'DELETE',
  });
}

/**
 * 接处警获取-查询机场任务列表
 *
 * @param queryParams
 */
export function searchRelevanceList(params,data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  console.log('data',data)
  return request({
    url: `/flightTask/api/v1/${workspace_id}/list-tasks/${params.alarm_id}`,
    method: 'POST',
    data: data
  });
}

/**
 * 接处警获取-查询警情关联机场任务
 *
 * @param queryParams
 */
export function searchAlreadyRelevance(params,data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `/alarm/api/v1/task/${workspace_id}/page-tasks/${params.alarm_id}`,
    method: 'POST',
    data: data
  });
}

/**
 * 机场任务，终止任务
 *
 * @param queryParams
 */
export function pauseTask(params,data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `/flightTask/api/v1/${workspace_id}/tasks/${params.task_id}`,
    method: 'PUT',
    data: data
  });
}

/**
 * 实时飞行-一键起飞
 *
 * @param queryParams
 */
export function takeoffToPoint(params,data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `/control/api/v1/devices/${params.sn}/jobs/takeoff-to-point`,
    method: 'POST',
    data: data
  });
}

/**
 * 实时飞行-飞行控制
 *
 * @param queryParams
 */
export function postDrcEnter(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `/control/api/v1/workspaces/${workspace_id}/drc/enter`,
    method: 'POST',
    data: data
  });
}

// 设置喊话器音量
export function setVolume(params,data) {
  return request({
    url: `/control/api/v1/psdk/${params.sn}/speaker/play/volume/set`,
    method: 'POST',
    data: data
  });
}

// 喊话音频列表
export function audioList(params,data) {
  return request({
    url: `/media/api/v1/audio/list`,
    method: 'GET',
    data: data
  });
}

export function audioPlay(params,data) {
  return request({
    url: `/control/api/v1/psdk/${params.sn}/speaker/audio/play/start`,
    method: 'POST',
    data: data
  });
}

export function stopPlay(params,data) {
  return request({
    url: `/control/api/v1/psdk/${params.sn}/speaker/play/stop`,
    method: 'POST',
    data: data
  });
}

export function setLoopType(params,data) {
  return request({
    url: `/control/api/v1/psdk/${params.sn}/speaker/play/mode/set`,
    method: 'POST',
    data: data
  });
}

export function alarmLampChange(params,data) {
  return request({
    url: `/control/api/v1/psdk/${params.sn}/psdk/widget/value/set`,
    method: 'POST',
    data: data
  });
}

/**
 * 实时飞行-退出飞行控制
 *
 * @param queryParams
 */
export function postDrcExit(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `/control/api/v1/workspaces/${workspace_id}/drc/exit`,
    method: 'POST',
    data: data
  });
}

/**
 * 实时飞行-退出飞行控制
 *
 * @param queryParams
 */
export function courseReversal(params,data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `/control/api/v1/devices/${params.sn}/jobs/${params.service_identifier}`,
    method: 'POST',
    data: data
  });
}


/**
 * 实时飞行-获取控制权
 *
 * @param queryParams
 */
export function postFlightAuth(params,data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `/control/api/v1/devices/${params.sn}/authority/flight`,
    method: 'POST',
    data: data
  });
}

/**
 * 云台控制
 *
 * @param queryParams
 */
export function postPayloadCommands(params,data) {
  return request({
    url: `/control/api/v1/devices/${params}/payload/commands`,
    method: 'POST',
    data: data
  });
}

/**
 * 文本转语音
 *
 * @param data
 */
export function textToVoice(params,data) {
  return request({
    url: `/control/api/v1/psdk/${params.sn}/speaker/tts/play/start`,
    method: 'POST',
    data: data
  });
}

/**
 * 获取负载控制权
 *
 * @param queryParams
 */
export function getAuthority(params,data) {
  return request({
    url: `/control/api/v1/devices/${params.sn}/authority/payload`,
    method: 'POST',
    data: data
  });
}

/**
 * 接处警获取-查询警情关联机场任务
 *
 * @param queryParams
 */
export function linkAlarmTask(params,data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `/alarm/api/v1/task/${workspace_id}/link-alarm-task/${params.alarm_id}`,
    method: 'POST',
    data: data
  });
}

/**
 * 删除设备的绑定状态
 *
 * @param ids
 */
export function deleteTask(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/jobs`,
    method: 'delete',
    params: data
  });
}

/**
 * 获取接处警配置
 *
 * @param queryParams
 */
export function getJJConfig(queryParams) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;

  return request({
    url: `${ALARM_URL}${APPLICTION_WORKSPACES}/config/${workspace_id}`,
    method: 'get',
    params: queryParams
  });
}

/**
 * 更新接处警配置
 *
 * @param data
 */
export function updateJJConfig(data) {
  return request({
    url: `${ALARM_URL}${APPLICTION_WORKSPACES}/config`,
    method: 'put',
    data: data
  });
}

/**
 * 接处警任务
 *
 * @param data
 */
export function getJJTaskPage(data) {
  return request({
    url: `${ALARM_URL}/task/page`,
    method: 'post',
    data: data,
  });
}

/**
 * 接处警任务-删除
 *
 * @param data
 */
export function deleteJJTask(id) {
  return request({
    url: `${ALARM_URL}/task/delete/${id}`,
    method: 'delete'
  });
}

/**
 * 接处警任务上报
 *
 * @param data
 */
export function fetchTaskReport(data) {
  return request({
    url: `${ALARM_URL}/task/report`,
    method: 'post',
    data: data,
    hideErrorMsg:true
  });
}

/**
 * 执行接处警任务
 *
 */
export function executeAlarm(alarmId) {
  return request({
    url: `${ALARM_URL}/task/execute/${alarmId}`,
    method: 'post',
    data: {}
  });
}

// 任务下发
export function submitTask(res,data) {
  return request({
    url: `${ALARM_URL}/task/execute/${res.alarmId}`,
    method: 'post',
    data: data
  });
}

// 获取指定任务ID的飞行记录
export function getRecordByid(jobId) {
  return request({
    url: `/wayline/api/v1/flight/record/${jobId}`,
    method: 'get'
  });
}

