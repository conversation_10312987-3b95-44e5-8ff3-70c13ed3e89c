<template>
  <el-dialog
    :title="title"
    :model-value="visible"
    v-if="visible"
    width="1056px"
    height="689px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <!--图片类型-->
    <div class="content" v-if="imgUrl.indexOf('.mp4') == -1 && imgUrl.indexOf('.MP4') == -1">
      <el-image
        class="imgContent"
        :src="imgUrl"
        :zoom-rate="1.2"
        :max-scale="7"
        :min-scale="0.2"
        :preview-src-list="[imgUrl]"
        fit="contain"
      />
    </div>
    <!--视频类型-->
    <div class="content" v-else>
      <!--视频组件 video 的 src 属性不会自动重新加载新的视频内容,需要加key来处理刷新-->
      <video class="videoContent" controls autoplay :key="imgUrl">
        <source :src="imgUrl" type="video/mp4" />
      </video>
    </div>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  imgUrl: {
    type: String,
    default: '图片路径'
  }
});
const emit = defineEmits(['update:visible']);
defineExpose({});

// 关闭弹窗
function closeDialog() {
  emit('update:visible', false);
}
onMounted(() => {});
</script>
<style scoped lang="scss">
.imgContent {
  width: 100%;
  height: 100%;
  -moz-user-select: none; /*火狐*/
 
 -webkit-user-select: none; /*webkit浏览器*/

 -ms-user-select: none; /*IE10*/

 -khtml-user-select: none; /*早期浏览器*/

 user-select: none;
}
.imgContent::v-deep .el-image__preview {
  cursor: zoom-in;
}
.videoContent {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.content {
  height: 567px;
}
</style>
