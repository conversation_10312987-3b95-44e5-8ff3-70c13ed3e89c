import { readRemoteJsonFile } from '@/utils/configHelper';
const globalConfigResource = ref({});

await readRemoteJsonFile('').then(res => {
  globalConfigResource.value = res;
});

export const CURRENT_CONFIG = {

  // // license
  // appId: globalConfigResource.value.CURRENT_CONFIG['appId'], // You need to go to the development website to apply.
  // appKey: globalConfigResource.value.CURRENT_CONFIG['appKey'], // You need to go to the development website to apply.
  // appLicense: globalConfigResource.value.CURRENT_CONFIG['appLicense'], // You need to go to the development website to apply.

  // // http
  // baseURL: globalConfigResource.value.CURRENT_CONFIG['baseURL'], // This url must end with "/". Example: 'http://***********:6789/'
  // websocketURL: globalConfigResource.value.CURRENT_CONFIG['websocketURL'], // Example: 'ws://***********:6789/api/v1/ws'
  VITE_APP_BASE_URL: globalConfigResource.value['VITE_APP_BASE_URL'],
  VITE_WS_BASE_URL: globalConfigResource.value['VITE_WS_BASE_URL'],
  // livestreaming
  // RTMP  Note: This IP is the address of the streaming server. If you want to see livestream on web page, you need to convert the RTMP stream to WebRTC stream.
  rtmpURL: 'Please enter the rtmp access address.', // Example: 'rtmp://***********/live/'
  // GB28181 Note:If you don't know what these parameters mean, you can go to Pilot2 and select the GB28181 page in the cloud platform. Where the parameters same as these parameters.
  gbServerIp: '**************',
  gbServerPort: '1180',
  gbServerId: '34020000002000000001',
  gbAgentId: '34020000001314200031',
  gbPassword: '12345678',
  gbAgentPort: '5060',
  gbAgentChannel: '34020000001314200031',
  // RTSP
  rtspUserName: 'Please enter the username.',
  rtspPassword: 'Please enter the password.',
  rtspPort: '8554',
  // Agora
  agoraAPPID: '846a839dbb1547e1a3f0b2944b7f28b4',
  agoraToken: '007eJxTYBCfsHG25QID3wsXQz9yztGPOBrl6dMwV2r318Dkufd/evUoMFiYmCVaGFumJCUZmpqYpxomGqcZJBlZmpgkmacZWSSZ3D6smtYQyMjQ8kqDkZEBAkF8ZobyoiwGBgA69x+J',
  agoraChannel: 'wrj',

  // map
  // You can apply on the AMap website.
  amapKey: '********************************',


}
