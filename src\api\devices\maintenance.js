import request from '@/utils/request';
import { MAIN_PATH ,APPLICTION_ORDER, API_VERSION } from '../config/index';
import { useUserStoreHook } from '@/store/modules/user';

/**
 * 获取维保计划列表
 *
 * @param queryParams
 */
export function getPlanList(data) {
	const userStore = useUserStoreHook();
	const { userData } = userStore;
	const { workspace_id } = userData;
	return request({
			url: `/api/v1/deviceMaintenancePlan/${workspace_id}/page`,
			method: 'POST',
			data: data
	});
}

/**
 * 删除维保计划
 *
 * @param ids
 */
export function deletePlan(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `/api/v1/deviceMaintenancePlan/${workspace_id}/removeById`,
    method: 'POST',
		data
  });
}

// 新增维保计划
export function addPlan(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `/api/v1/deviceMaintenancePlan/${workspace_id}/save`,
    method: 'POST',
		data
  });
}

// 编辑维保计划
export function editPlan(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `/api/v1/deviceMaintenancePlan/${workspace_id}/updateById`,
    method: 'POST',
		data
  });
}

// 维保计划详情
export function getPlanDetail(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `/api/v1/deviceMaintenancePlan/${workspace_id}/detail`,
    method: 'POST',
		data
  });
}

// 维保方案列表
export function getSchemeList(data) {
	const userStore = useUserStoreHook();
	const { userData } = userStore;
	const { workspace_id } = userData;
	return request({
			url: `/api/v1/deviceMaintenanceScheme/${workspace_id}/page`,
			method: 'POST',
			data: data
	});
}

// 维保方案详情
export function getSchemeDetail(data) {
	const userStore = useUserStoreHook();
	const { userData } = userStore;
	const { workspace_id } = userData;
	return request({
			url: `/api/v1/deviceMaintenanceScheme/${workspace_id}/detail`,
			method: 'POST',
			data: data
	});
}

// 编辑维保方案
export function editScheme(data) {
	const userStore = useUserStoreHook();
	const { userData } = userStore;
	const { workspace_id } = userData;
	return request({
			url: `/api/v1/deviceMaintenanceScheme/${workspace_id}/updateById`,
			method: 'POST',
			data: data
	});
}

// 编辑维保方案
export function addScheme(data) {
	const userStore = useUserStoreHook();
	const { userData } = userStore;
	const { workspace_id } = userData;
	return request({
			url: `/api/v1/deviceMaintenanceScheme/${workspace_id}/save`,
			method: 'POST',
			data: data
	});
}

// 维保任务分页列表
export function getTaskList(data) {
	const userStore = useUserStoreHook();
	const { userData } = userStore;
	const { workspace_id } = userData;
	return request({
			url: `/api/v1/deviceMaintenanceTask/${workspace_id}/page`,
			method: 'POST',
			data: data
	});
}

// 维保任务-新增
export function addTask(data) {
	const userStore = useUserStoreHook();
	const { userData } = userStore;
	const { workspace_id } = userData;
	return request({
			url: `/api/v1/deviceMaintenanceTask/${workspace_id}/save`,
			method: 'POST',
			data: data
	});
}

//  维保任务-编辑
export function editTask(data) {
	const userStore = useUserStoreHook();
	const { userData } = userStore;
	const { workspace_id } = userData;
	return request({
			url: `/api/v1/deviceMaintenanceTask/${workspace_id}/updateById`,
			method: 'POST',
			data: data
	});
}

//  维保任务-删除
export function deleteTask(data) {
	const userStore = useUserStoreHook();
	const { userData } = userStore;
	const { workspace_id } = userData;
	return request({
			url: `/api/v1/deviceMaintenanceTask/${workspace_id}/removeById`,
			method: 'POST',
			data: data
	});
}

// 维保任务-详情
export function getTaskDetail(data) {
	const userStore = useUserStoreHook();
	const { userData } = userStore;
	const { workspace_id } = userData;
	return request({
			url: `/api/v1/deviceMaintenanceTask/${workspace_id}/detail`,
			method: 'POST',
			data: data
	});
}

// 维保任务-维保
export function maintenanceTask(data) {
	const userStore = useUserStoreHook();
	const { userData } = userStore;
	const { workspace_id } = userData;
	return request({
			url: `/api/v1/deviceMaintenanceTask/${workspace_id}/maintenance`,
			method: 'POST',
			data: data
	});
}

// 维保任务-计划列表
export function getMaintenancePlan(data) {
	const userStore = useUserStoreHook();
	const { userData } = userStore;
	const { workspace_id } = userData;
	return request({
			url: `/api/v1/deviceMaintenancePlan/${workspace_id}/list`,
			method: 'GET',
			params: data
	});
}

// 维保任务-方案列表
export function getMaintenanceScheme(data) {
	const userStore = useUserStoreHook();
	const { userData } = userStore;
	const { workspace_id } = userData;
	return request({
			url: `/api/v1/deviceMaintenanceScheme/${workspace_id}/list`,
			method: 'GET',
			params: data
	});
}

// 维保任务-用户列表
export function getUserList(data) {
	return request({
			url: `/system/user/simple-list`,
			method: 'GET',
			params: data
	});
}

export function uploadTask(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `/api/v1/deviceMaintenanceTask/${workspace_id}/uploadFile`,
    method: 'post',
    responseType: 'upload',
    data
  });
}

// 维保任务-无人机和机场列表
export function getAirportAndDrone(data) {
	const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
	return request({
		url: `/manage/api/v1/devices/${workspace_id}/getDeviceSimpleList?domain=0&domain=3`,
		method: 'GET',
		params: {}
	});
}

// 生成分享链接
export function crateShareUrl(data) {
	const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
	return request({
		url: `/api/v1/deviceVideoShare/${workspace_id}/crateShareUrl`,
		method: 'POST',
		data: data
	});
}

// 生成分享链接
export function getRealVideoUrl(data) {
	return request({
		url: `/api/v1/deviceVideoShare/getRealVideoUrl`,
		method: 'GET',
		params: data
	});
}

// 生成分享链接
export function checkShareUrl(data) {
	return request({
		url: `/api/v1/deviceVideoShare/checkShareUrl`,
		method: 'GET',
		params: data
	});
}