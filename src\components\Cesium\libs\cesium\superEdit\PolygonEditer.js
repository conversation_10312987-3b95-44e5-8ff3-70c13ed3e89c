import * as Cesium from 'cesium';
import {
  addPropertiesToEntity,
  getPropertyByKey,
  calculatePointFromCenter,
  toCartesian3,
  toNumber,
  toDegrees,
  px2Catesian3,
  arrayToCartesian3,
  c3ArrToDegress,
  calculatArea,
  calculatPerimeter,
  getPointsFromMap
} from '../common';
import { generateKey } from '@/utils';
import { getCenter } from '../turfUtils';
import { toRaw } from 'vue';
import { ElMessageBox } from 'element-plus';
import { dialogDataRect } from '@/views/flight-manage/flight-area/flightAreaHandle';
const endpointIcon = new URL('@/assets/plan/wrj/endpoint.png', import.meta.url).href;
const midpointIcon = new URL('@/assets/plan/wrj/middlepoint.png', import.meta.url).href;
let viewer = null;
let handler = null;
let labelEntity = null;
export let originalPositions = [];
export let polygonEditEntity = null;
export let isPolygonEdit = ref(false);
let editObjectGlobal = null;
//#region 编辑部分
// 适合多有圆形的编辑方法
export const editPolygon = (v, polygonEntity, CallBack) => {
  // 检查是否满足绘制条件
  if (!v || !polygonEntity || isPolygonEdit.value) {
    return; // 如果任何条件不满足，则不执行
  }
  try {
    viewer = v;
    editObjectGlobal = null;
    polygonEditEntity = polygonEntity;
    originalPositions = [];

    dialogDataRect.entity = polygonEntity;
    let curSelectedEntity = null;
    let flightAreaType_ = getPropertyByKey(polygonEntity, 'flightAreaType');
    let id_ = getPropertyByKey(polygonEntity, 'id');
    let drawResultObject = {
      geomType: 'polygon',
      action: 'edit',
      flightAreaType: flightAreaType_ || 'dfence',
      id: id_,
      endEntityPoints: [],
      midEntityPoints: [],
      polygon: null,
      positions: [],
      _endPointPositionMap: new Map(),
      title: getPropertyByKey(polygonEntity, 'title') || '',
      color: getPropertyByKey(polygonEntity, 'color') || '',
      area: 0,
      length: 0
    };
    dialogDataRect.action = 'edit';
    dialogDataRect.geomType = 'polygon';
    dialogDataRect.id = drawResultObject.id;
    // 创建圆心
    let properties = polygonEntity.properties ?? null;
    if (!properties) {
      return;
    }
    document.body.style.cursor = 'default';
    // 从实体自定义属性中获取 经纬度 坐标集合
    let c3Positions = getPropertyByKey(polygonEntity, 'positions') ?? [];
    drawResultObject.positions = c3Positions;
    originalPositions = c3Positions;
    // 先初步根据drawResultObject.positions 创建端点
    (drawResultObject.positions || []).forEach(c3p => {
      let p = createPoint(viewer, c3p, { pixelSize: 10, outlineWidth: 2, id: generateKey() });
      drawResultObject.endEntityPoints.push(p);
    });
    // 构建端点和中点
    const { endEntityPoints, midEntityPoints } = renderPoints(viewer, drawResultObject.endEntityPoints);
    drawResultObject.endEntityPoints = endEntityPoints;
    drawResultObject.midEntityPoints = midEntityPoints;
    editObjectGlobal = drawResultObject;
    // 定义事件监听
    polygonEntity.handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    // 点击事件
    isPolygonEdit.value = true;
    let baseInfo = getPolygonBaseInfoFromPositions(drawResultObject.endEntityPoints);
    drawResultObject.area = baseInfo.area;
    drawResultObject.length = baseInfo.length;
    CallBack &&
      CallBack({
        data: drawResultObject,
        polygon: polygonEntity
      });
    polygonEntity.handler.setInputAction(event => {
      isPolygonEdit.value = true;
      let pickedObject = viewer.scene.pick(event.position);
      if (Cesium.defined(pickedObject)) {
        if (pickedObject.id.name === 'ENDPOINT') {
          curSelectedEntity = pickedObject.id;
        } else if (pickedObject.id.name === 'MIDPOINT') {
          curSelectedEntity = pickedObject.id;
          // 将当前的点转换为 端点 索引位置插入 然后清楚重新创建中间点
          clearMidPoints(viewer, drawResultObject, curSelectedEntity);
          const { endEntityPoints, midEntityPoints } = renderPoints(viewer, drawResultObject.endEntityPoints);
          drawResultObject.endEntityPoints = endEntityPoints;
          drawResultObject.midEntityPoints = midEntityPoints;
          if (!curSelectedEntity) {
            throw new Error('转换失败');
          }
        }
        let baseInfo = getPolygonBaseInfoFromPositions(drawResultObject.endEntityPoints);
        drawResultObject.area = baseInfo.area;
        drawResultObject.length = baseInfo.length;
        editObjectGlobal = drawResultObject;
        if (isPolygonEdit.value) {
          CallBack &&
            CallBack({
              data: drawResultObject,
              polygon: polygonEntity,
              clickType: 'LEFT_DOWN',
              event: 'update'
            });
        }
      }
      // 选中的如果是
    }, Cesium.ScreenSpaceEventType.LEFT_DOWN);

    // 对鼠标移动事件的监听
    polygonEntity.handler.setInputAction(event => {
      //获取加载地形后对应的经纬度和高程：地标坐标
      let ray = viewer.camera.getPickRay(event.endPosition);
      let newCartesian = viewer.scene.globe.pick(ray, viewer.scene);
      if (!newCartesian) {
        return;
      }
      if (curSelectedEntity == null) {
        document.body.style.cursor = 'default';
        return;
      }
      // 选中的实体位置变更
      curSelectedEntity.position = newCartesian;
      //移动的是半径点，则更新半径
      if (curSelectedEntity.name === 'ENDPOINT') {
        document.body.style.cursor = 'pointer';
        updateMidPoints(drawResultObject);
      }
      polygonEntity.polygon.hierarchy = getPolygonHierarchyProperty(drawResultObject);
      viewer.scene.screenSpaceCameraController.enableRotate = false;
      viewer.scene.screenSpaceCameraController.enableZoom = false;
      let baseInfo = getPolygonBaseInfoFromPositions(drawResultObject.endEntityPoints);
      drawResultObject.area = baseInfo.area;
      drawResultObject.length = baseInfo.length;
      editObjectGlobal = drawResultObject;
      if (isPolygonEdit.value) {
        CallBack &&
          CallBack({
            data: drawResultObject,
            polygon: polygonEntity,
            clickType: 'MOUSE_MOVE',
            event: 'update'
          });
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

    // 对鼠标抬起事件的监听
    polygonEntity.handler.setInputAction(event => {
      curSelectedEntity = null;
      viewer.scene.screenSpaceCameraController.enableRotate = true;
      viewer.scene.screenSpaceCameraController.enableZoom = true;
    }, Cesium.ScreenSpaceEventType.LEFT_UP);
  } catch (error) {}
};

export const cancelPolygonEdit = () => {
  if (!originalPositions || !polygonEditEntity || !viewer) {
    return;
  }
  isPolygonEdit.value = false;
  polygonEditEntity.polygon.hierarchy = getPolygonHierarchyProperty2(originalPositions);
  closePolygonEdit(polygonEditEntity);
};
/**
 * 第一次初始化点到图上
 * @param {*} viewer
 * @param {*} drawResultObject
 * @returns
 */
export const renderPoints = (viewer, endEntitys = []) => {
  let result = {
    endEntityPoints_: endEntitys,
    midEntityPoints_: []
  };
  // 通过端点构建 中点 插入到对应的索引位
  for (let i = 0; i < result.endEntityPoints_.length; i++) {
    let oneTemp = null;
    let twoTemp = null;
    let midEntity = null;
    let objMidPoint = {
      entity: null,
      prevId: '',
      nextId: ''
    };
    if (i === result.endEntityPoints_.length - 1) {
      oneTemp = result.endEntityPoints_[i];
      twoTemp = result.endEntityPoints_[0];
      // 创建中间点
      midEntity = createMidPoint(
        viewer,
        Cesium.Cartesian3.midpoint(oneTemp.position._value, twoTemp.position._value, new Cesium.Cartesian3()),
        { pixelSize: 10, outlineWidth: 2 }
      );
      objMidPoint.entity = midEntity;
      objMidPoint.prevId = oneTemp.id;
      objMidPoint.nextId = twoTemp.id;
    } else {
      oneTemp = result.endEntityPoints_[i];
      twoTemp = result.endEntityPoints_[i + 1];
      // 创建中间点
      midEntity = createMidPoint(
        viewer,
        Cesium.Cartesian3.midpoint(oneTemp.position._value, twoTemp.position._value, new Cesium.Cartesian3()),
        { pixelSize: 10, outlineWidth: 2 }
      );
      objMidPoint.entity = midEntity;
      objMidPoint.prevId = oneTemp.id;
      objMidPoint.nextId = twoTemp.id;
    }
    result.midEntityPoints_.push(objMidPoint);
  }

  return {
    endEntityPoints: result.endEntityPoints_,
    midEntityPoints: result.midEntityPoints_
  };
};

// 更新中间点位置
export const updateMidPoints = drawResultObject => {
  // 创建完当前的数组 端点
  (drawResultObject.midEntityPoints || []).forEach(tempEntityPoint => {
    const { entity = null, prevId = '', nextId = '' } = tempEntityPoint;
    let p1 = drawResultObject.endEntityPoints.find(p => p.id === prevId);
    let p2 = drawResultObject.endEntityPoints.find(p => p.id === nextId);
    entity.position = Cesium.Cartesian3.midpoint(p1.position._value, p2.position._value, new Cesium.Cartesian3());
  });
};

// 获取动态变更的坐标值positionArr
export const getPolygonHierarchyProperty = (drawResultObject, positionArr2) => {
  return new Cesium.CallbackProperty(() => {
    let positionArr = [];
    drawResultObject.endEntityPoints.forEach(entity => {
      if (entity.name === 'MIDPOINT') {
        return;
      }
      positionArr.push(entity.position._value);
    });
    return new Cesium.PolygonHierarchy(positionArr, null);
  }, false);
};

export const getPolygonHierarchyProperty2 = positionArr2 => {
  return new Cesium.CallbackProperty(() => {
    let positionArr = [];
    positionArr2.forEach(p => {
      positionArr.push(p);
    });
    return new Cesium.PolygonHierarchy(positionArr, null);
  }, false);
};
export const closePolygonEdit = polygon => {
  if (!polygon || !polygon.handler) {
    return;
  }
  document.body.style.cursor = 'default';
  // 移除地图事件
  polygon.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOWN);
  polygon.handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
  polygon.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_UP);
  isPolygonEdit.value = false;
  if (editObjectGlobal && viewer) {
    editObjectGlobal.endEntityPoints.forEach(p => {
      viewer.entities.remove(p);
    });
    editObjectGlobal.midEntityPoints.forEach(p => {
      viewer.entities.remove(p.entity);
    });
    editObjectGlobal.midEntityPoints = [];
    viewer.scene.screenSpaceCameraController.enableRotate = true;
    viewer.scene.screenSpaceCameraController.enableZoom = true;
  }

  clearLabelEntity();
  return polygon;
};

// 根据实际的点位置信息计算面积 长度等
const getPolygonBaseInfoFromPositions = (endEntityPoints = []) => {
  if (endEntityPoints.length === 0) {
    throw new Error('坐标组合不能为空');
  }
  let entityPoints = [];
  endEntityPoints.forEach(entity => {
    entityPoints.push(entity.position._value);
  });
  let degList = c3ArrToDegress(entityPoints);
  let center = getCenter(degList);
  // 计算面积
  let area = calculatArea(entityPoints);
  // 计算周长
  let length = calculatPerimeter(entityPoints);
  return { center: toCartesian3(center), length, area };
};

/**
 * 更新属性信息 entity
 */
const updateProperty = entity => {
  entity.polygon.hierarchy = entity.polygon.hierarchy.getValue();
};

//#endregion

//#region 公用部分

/**
 * 通用产生采集点
 * @param {*} cartesian
 * @returns
 */
export function createPoint(viewer, cartesian, options) {
  let point = viewer.entities.add({
    id: options.id || generateKey(),
    name: 'ENDPOINT',
    position: toCartesian3(cartesian),
    billboard: {
      image: endpointIcon,
      width: 14,
      height: 14,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      disableDepthTestDistance: Number.POSITIVE_INFINITY // 点总是在最上层
    }
  });
  return point;
}

export function createMidPoint(viewer, cartesian, options) {
  let point = viewer.entities.add({
    id: options.id || generateKey(),
    name: 'MIDPOINT',
    position: toCartesian3(cartesian),
    billboard: {
      image: midpointIcon,
      width: 10,
      height: 10,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      disableDepthTestDistance: Number.POSITIVE_INFINITY // 点总是在最上层
    }
  });
  return point;
}

export function clearMidPoints(viewer, drawResultObject, midEntity) {
  if (!midEntity || !drawResultObject) {
    return null;
  }
  let midEntityPointEntity = (drawResultObject.midEntityPoints || []).find(
    mepObj => mepObj.entity.id === midEntity?.id
  );
  if (!midEntityPointEntity) {
    return null;
  }
  // 获取当前点实体
  const { entity = null, prevId = '' } = midEntityPointEntity;
  entity.name = 'ENDPOINT';
  entity.billboard.image = endpointIcon;
  entity.billboard.width = 14;
  entity.billboard.height = 14;
  // 根据这里的 prevId 和 nextId  在 result.entityPoints_ 中找到对应的 prevId 和 nextId 位置,然后将 entity 插入到 该索引中间
  let index = drawResultObject.endEntityPoints.findIndex(p => p.id === prevId);
  index > -1 && drawResultObject.endEntityPoints.splice(index + 1, 0, entity);
  drawResultObject.midEntityPoints.forEach(midEntityPoint => {
    const { entity = null } = midEntityPoint;
    if (entity.name === 'ENDPOINT') {
      return;
    }
    viewer.entities.remove(entity);
  });
  drawResultObject.midEntityPoints = [];
}

/**
 * 通用产生采集点
 * @param {*} cartesian
 * @returns
 */
export function createPolygon(viewer, positions, options) {
  try {
    if (!viewer || !positions) {
      return;
    }
    return viewer.entities.add({
      id: options?.id || generateKey(),
      name: options?.name || 'Polygon_Edit_' + generateKey(),
      polygon: {
        hierarchy: new Cesium.CallbackProperty(() => {
          let hierarchyTemp = new Cesium.PolygonHierarchy(positions, []);
          return hierarchyTemp;
        }, false),
        show: true,
        material: options?.color || Cesium.Color.WHITE.withAlpha(0.4),
        outline: true,
        outlineColor: Cesium.Color.ALICEBLUE,
        outlineWidth: 2,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    });
  } catch (error) {}
}
export function forbidWorld(isForbid) {
  if (!viewer) {
    return;
  }
  viewer.scene.screenSpaceCameraController.enableRotate = !isForbid;
  viewer.scene.screenSpaceCameraController.enableTilt = !isForbid;
  viewer.scene.screenSpaceCameraController.enableTranslate = !isForbid;
  viewer.scene.screenSpaceCameraController.enableInputs = !isForbid;
}

//#endregion

//#region 创建辅助部分

// 清除辅助部分
export function clearLabelEntity() {
  document.body.style.cursor = 'default';
  if (!viewer) {
    return;
  }
  if (labelEntity) {
    viewer.entities.remove(labelEntity);
    labelEntity = null;
  }
}
// 创建辅助部分
function createOrUpdateLabelEntity(drawResultObject = null) {
  if (!viewer || !drawResultObject) {
    return;
  }
  // 更新标注功能
  let options = getPolygonBaseInfoFromPositions(drawResultObject);
  const { center, area = 0 } = options;
  let midPoint = toCartesian3(center);
  if (!midPoint || !area) {
    return;
  }
  if (labelEntity) {
    labelEntity.position = midPoint;
    labelEntity.label.text = area || 0;
  } else {
    labelEntity = viewer.entities.add({
      position: toCartesian3(midPoint),
      point: {
        // 点的大小（像素）
        pixelSize: 15,
        // 点位颜色，fromCssColorString 可以直接使用CSS颜色
        color: Cesium.Color.RED,
        // 边框颜色
        outlineColor: Cesium.Color.fromCssColorString('#fff'),
        // 边框宽度(像素)
        outlineWidth: 2,
        // 是否显示
        show: true,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND //贴地
      },
      label: {
        text: area || 0,
        show: true,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        font: '30px monospace', // 字体边框
        outline: true,
        fillColor: Cesium.Color.WHITE,
        outlineWidth: 5,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -30),
        showBackground: true,
        backgroundColor: new Cesium.Color(0.117, 0.117, 0.117, 0.7),
        eyeOffset: new Cesium.Cartesian3(0, 0, 2),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND //贴地
      }
    });
  }
}

//#endregion
