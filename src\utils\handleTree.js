/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parent_id 父节点字段 默认 'parent_id'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export function handleTree(data, id, parent_id, children) {
  let config = {
    id: id || 'id',
    parent_id: parent_id || 'parent_id',
    childrenList: children || 'children'
  };

  var childrenListMap = {};
  var nodeIds = {};
  var tree = [];

  for (let d of data) {
    let parent_id = d[config.parent_id];
    if (childrenListMap[parent_id] == null) {
      childrenListMap[parent_id] = [];
    }
    nodeIds[d[config.id]] = d;
    childrenListMap[parent_id].push(d);
  }

  for (let d of data) {
    let parent_id = d[config.parent_id];
    if (nodeIds[parent_id] == null) {
      tree.push(d);
    }
  }

  for (let t of tree) {
    adaptToChildrenList(t);
  }

  function adaptToChildrenList(o) {
    if (childrenListMap[o[config.id]] !== null) {
      o[config.childrenList] = childrenListMap[o[config.id]];
    }
    if (o[config.childrenList]) {
      for (let c of o[config.childrenList]) {
        adaptToChildrenList(c);
      }
    }
  }
  return tree;
}
export default handleTree;