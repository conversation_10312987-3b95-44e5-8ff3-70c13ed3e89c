{
  "compilerOptions": {
    "strict": true,
    "jsx": "preserve",
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "baseUrl": ".",
    "allowJs": true,
    "paths": {
      "@/*": ["src/*"]
    },
    "types": ["vite/client", "element-plus/global", "unplugin-icons/types/vue"],
    "skipLibCheck": true /* Skip type checking all .d.ts files. */,
    "allowSyntheticDefaultImports": true /* 允许默认导入 */,
    "forceConsistentCasingInFileNames": true /* Ensure that casing is correct in imports. */
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.vue",
    "src/types/**/*.d.ts",
    "src/**/*.js",
    "src/views/plan/newplan/kmz/mock"
  ],
  "exclude": ["node_modules", "dist"]
}
