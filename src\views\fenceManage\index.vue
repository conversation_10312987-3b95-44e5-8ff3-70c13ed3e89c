<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-input
          v-model="queryParams.keyword"
          placeholder="请输入围栏名称/地址"
          clearable
          style="width: 200px"
          @keyup.enter="handleSearch"
        />
      </div>
      <div class="search-btn">
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
        <el-button v-if="authorityShow('createFence')" type="primary" @click="handleAdd">新增</el-button>
      </div>
    </div>
    <el-card shadow="never">
      <el-table
        :data="dataList"
        v-loading="loading"
        stripe
        height="630"
        style="width: 100%; margin-top: 10px"
        row-key="id"
      >
        <el-table-column prop="fence_name" label="围栏名称" min-width="120" />
        <el-table-column prop="fence_addr" label="围栏地址" min-width="150" show-overflow-tooltip />
        <el-table-column prop="fence_type" label="围栏类型" min-width="100">
          <template #default="scope">
            {{ fenceTypeOptions.find(item => item.value === scope.row.fence_type)?.label || scope.row.fence_type }}
          </template>
        </el-table-column>
        <el-table-column prop="fence_target" label="识别目标" min-width="100">
          <template #default="scope">
            {{ targetOptions.find(item => item.value === scope.row.fence_target)?.label || scope.row.fence_target }}
          </template>
        </el-table-column>
        <el-table-column prop="business_type" label="业务类型" min-width="100">
          <template #default="scope">
            {{
              businessTypeOptions.find(item => item.value === scope.row.business_type)?.label || scope.row.business_type
            }}
          </template>
        </el-table-column>
        <el-table-column prop="contact" label="联系人" min-width="100" />
        <el-table-column prop="telephone" label="联系电话" min-width="120" />
        <el-table-column prop="fence_status" label="状态" width="80" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.fence_status === 'active' ? 'success' : 'danger'">
              {{ scope.row.fence_status === 'active' ? '启用' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          v-if="
            authorityShow('editFence') ||
            authorityShow('deleteFence') ||
            authorityShow('checkFence') ||
            authorityShow('stopFence')
          "
          label="操作"
          width="240"
          align="center"
          fixed="right"
        >
          <template #default="scope">
            <el-button v-if="authorityShow('editFence')" type="primary" link @click="handleUpdate(scope.row)"
              >编辑</el-button
            >
            <el-button v-if="authorityShow('checkFence')" type="primary" link @click="handleDetail(scope.row)"
              >详情</el-button
            >
            <el-button v-if="authorityShow('stopFence')" type="warning" link @click="handleStatusChange(scope.row)">
              {{ scope.row.fence_status === 'active' ? '停用' : '启用' }}
            </el-button>
            <el-popconfirm
              v-if="authorityShow('deleteFence')"
              title="确认删除该围栏吗？"
              @confirm="handleDelete(scope.row)"
            >
              <template #reference>
                <el-button type="danger" link>删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-content">
        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="handleSearch"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import { getFenceList, getFence, deleteFence, updateFenceStatus } from '@/api/fence';
import { authorityShow } from '@/utils/authority';
const router = useRouter();
const loading = ref(false);
const dataList = ref([]);
const total = ref(0);

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  keyword: ''
});

// 围栏类型选项
const fenceTypeOptions = [
  { label: '入侵检测', value: 'intrusion' },
  { label: '区域监控', value: 'monitor' }
];

// 识别目标选项
const targetOptions = [
  { label: '人员', value: 'person' },
  { label: '车辆', value: 'vehicle' },
  { label: '全部', value: 'all' }
];

// 业务类型选项
const businessTypeOptions = [
  { label: '消防通道', value: 'fire_access' },
  { label: '危险区域', value: 'danger_area' },
  { label: '重点区域', value: 'important_area' }
];

// 前期处置措施选项
const preProcessOptions = [
  { label: '语音提示', value: 'speaker' },
  { label: '预警通知', value: 'alert' },
  { label: '无措施', value: 'none' }
];

/**
 * 查询围栏列表
 */
function handleQuery() {
  loading.value = true;

  // 构建查询参数
  const params = {
    pageNo: queryParams.pageNo,
    pageSize: queryParams.pageSize
  };

  // 只有当keyword有值时才添加到查询参数
  if (queryParams.keyword) {
    params.keyword = queryParams.keyword.trim();
  }

  getFenceList(params)
    .then(res => {
      dataList.value = res || [];
      total.value = dataList.value.length;
    })
    .catch(err => {
      console.error(err);
      ElMessage.error('获取围栏列表失败');
      dataList.value = [];
      total.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
}

/**
 * 搜索
 */
function handleSearch() {
  queryParams.pageNo = 1;
  handleQuery();
}

/**
 * 重置查询条件
 */
function resetQuery() {
  queryParams.keyword = '';
  queryParams.pageNo = 1;
  handleQuery();
}

/**
 * 新增围栏
 */
function handleAdd() {
  router.push({
    path: '/fence-edit',
    query: { mode: 'add' }
  });
}

/**
 * 修改围栏
 */
function handleUpdate(row) {
  router.push({
    path: '/fence-edit',
    query: { mode: 'edit', id: row.id }
  });
}

/**
 * 查看围栏详情
 */
function handleDetail(row) {
  router.push({
    path: '/fence-edit',
    query: { mode: 'detail', id: row.id }
  });
}

/**
 * 修改围栏状态
 */
function handleStatusChange(row) {
  const newStatus = row.fence_status === 'active' ? 'inactive' : 'active';
  const statusText = newStatus === 'active' ? '启用' : '停用';

  updateFenceStatus(row.id, newStatus)
    .then(res => {
      if (res) {
        ElMessage.success(`${statusText}成功`);
        handleSearch();
      } else {
        ElMessage.error(res.msg || `${statusText}失败`);
      }
    })
    .catch(err => {
      console.error(err);
      ElMessage.error(`${statusText}失败`);
    });
}

/**
 * 删除围栏
 */
function handleDelete(row) {
  deleteFence(row.id)
    .then(res => {
      if (res) {
        ElMessage.success('删除成功');
        handleSearch();
      } else {
        ElMessage.error(res.msg || '删除失败');
      }
    })
    .catch(err => {
      console.error(err);
      ElMessage.error('删除失败');
    });
}

//  初始化
onMounted(() => {
  handleQuery();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 16px 20px;
  .search {
    display: flex;
    align-items: center;
    padding: 24px;
    height: 64px;
    .search-form {
      flex: 1;
      color: #fff;
    }
    .search-btn {
      margin-left: 16px;
    }
  }
  .app-content {
    width: 100%;
    max-height: calc(100vh - 154px);
    padding: 16px 24px;
    background: #fff;
    overflow: auto;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
    border-radius: 4px;
    &::-webkit-scrollbar {
      width: 10px !important;
      height: 10px !important;
      background: #e4e7ec;
      border-radius: 4px;
    }
    &::-webkit-scrollbar-thumb {
      width: 10px !important;
      min-height: 20px !important;
      background: #b7d9fd !important;
      border-radius: 4px !important;
    }
    .btn-box {
      margin-bottom: 16px;
    }
    .textHidden {
      width: 180px;
      height: 20px;
      line-height: 20px;
      text-align: left;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
}
.pagination-content {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;

  &.hidden {
    display: none;
  }
}
</style>
