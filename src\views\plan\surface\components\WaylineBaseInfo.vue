<template>
  <div class="wayline-base-info-wrapper bg-light-blue">
    <!-- {{ waylineBaseInfoRect }} -->
    <el-row :gutter="16" class="row">
      <el-col :span="24">
        <el-input v-model="waylineBaseInfoRect.planName" style="width: 100%" :maxlength="32" placeholder="请输入航线名"
      /></el-col>
    </el-row>
    <el-row :gutter="10" class="row">
      <el-col :span="24">
        <el-input readonly="true" v-model="waylineBaseInfoRect.droneSubEnumLabel" style="width: 100%" placeholder="" />
      </el-col>
    </el-row>
  </div>
</template>
<script>
export default {
  name: 'WaylineBaseInfo'
};
</script>
<script setup>
import '../style/common.css';
import { ElInput } from 'element-plus';
import { onMounted, onUnmounted, watch } from 'vue';
import { useDeviceStore } from '@/store/modules/device.js';
import { useEditTrackerStore } from '@/store/modules/dataEditTracker.js';
const editTrackerStore = useEditTrackerStore();
const deviceStore = useDeviceStore();
const isInitialized = ref(false);
const uk = 'WAYLINEBASEINFOKEY';
const m = computed(() => {
  return editTrackerStore.dataTracker.getDataStatus();
});

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
const data = computed({
  get: () => props.modelValue,
  set: value => {
    emits('update:modelValue', value);
  }
});

// 对外定义事件
const emits = defineEmits(['update:data']); // 触发事件
//#region 数据双向绑定
const WAYLINETYPE = {
  polyline: 'polyline',
  polygon: 'polygon'
};

const waylineBaseInfoRect = reactive({
  planId: '',
  planName: '',
  series: '',
  droneEnumVal: 91, //飞行器
  droneSubEnumVal: 1,
  droneSubEnumLabel: '', //'Matrice 3TD',
  payloadEnumVal: 81, //负载
  payloadSubEnumVal: 0,
  droneModelKey: '',
  deviceOptions: [],
  waylineType: WAYLINETYPE.polyline
});

/**
 * 初始化设备选项
 */
const init = () => {
  // 加载的时候从设备的全局缓存中抽取对象
  let devInfo = deviceStore.getCurrentDevice();
  if (devInfo.planName === undefined || devInfo.planName === null) {
    let dInfo = localStorage.getItem(uk) ?? null;
    if (!dInfo) {
      return;
    }
    devInfo = JSON.parse(dInfo);
  }
  waylineBaseInfoRect.planName = devInfo.planName;
  waylineBaseInfoRect.series = devInfo.series;
  data.value.planName = waylineBaseInfoRect.planName;
  waylineBaseInfoRect.droneEnumVal = devInfo.droneEnumVal;
  waylineBaseInfoRect.droneSubEnumVal = devInfo.droneSubEnumVal;
  waylineBaseInfoRect.droneSubEnumLabel = devInfo.droneSubEnumLabel;
  waylineBaseInfoRect.payloadEnumVal = devInfo.payloadEnumVal;
  waylineBaseInfoRect.payloadSubEnumVal = devInfo.payloadSubEnumVal;
  waylineBaseInfoRect.droneModelKey = devInfo.droneModelKey;
  waylineBaseInfoRect.deviceOptions = devInfo.deviceOptions;
  waylineBaseInfoRect.waylineType = devInfo.waylineType;
  //  保存到 localStorage 本地缓存中
  setTimeout(() => {
    localStorage.setItem(uk, JSON.stringify(waylineBaseInfoRect));
  }, 1);
};

watch(
  () => waylineBaseInfoRect.planName,
  (newValue, oldValue) => {
    data.value.planName = newValue;
    if (newValue !== oldValue && newValue !== '' && oldValue !== '') {
      editTrackerStore.dataTracker.markAsModified();
    }
  },
  {
    deep: true,
    immediate: true
  }
);
// 监听 deviceAdapter 的变化
const onAdapterReady = () => {
  if (deviceStore.deviceAdapter && !isInitialized.value) {
    isInitialized.value = true;
    init();
  }
};
//#endregion
onMounted(() => {
  init();
  deviceStore.$subscribe(onAdapterReady);
  editTrackerStore.dataTracker.reset();
});
onUnmounted(() => {});
</script>
<style lang="scss" scoped>
::v-deep .el-input .el-input__inner {
  font-size: 16px;
  color: #dadada;
}
::v-deep .el-input-numbert.is-disabled .el-input__wrapper {
  background-color: #11253e;
}
::v-deep .el-input .el-input__wrapper {
  background-color: #11253e;
  box-shadow: 0 0 0 1px #cfcfcf8f inset;
}

::v-deep .el-input .el-input__wrapper.is-focus {
  box-shadow: 0 0 0 1px #cfcfcf8f inset;
}

::v-deep .el-select .el-input {
  border: 1px solid #cfcfcf8f;
}
::v-deep .el-select .el-input__inner {
  border: 1px solid transparent;
  color: #dadada;
}
::v-deep .el-select .el-input__wrapper {
  background-color: #11253e;
  color: #dadada;
}
::v-deep .el-select .el-input .el-select__caret {
  color: #fff;
}

.wayline-base-info-wrapper {
  background-color: #232323;
  color: white;
  padding: 5px;
  width: 100%;
  height: 100px;
  user-select: none;
  .row {
    user-select: none;
    margin: 8px;
    padding: 0px 12px;
  }
}

.round {
  border: 5px;
  color: white;
}
.item {
  width: auto;
  padding: 2px 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  background: rgba(45, 140, 240, 0.35);
  color: #ffffff40;
  margin-left: 5px;
  user-select: none;
  &:hover {
    cursor: pointer;
  }
}
.active {
  cursor: pointer;
  text-decoration: none !important;
  background: #5d5f61;
}

.uranus-btn {
  background: #505254;
  color: #fff;
  margin: 2px 2px;
  padding: 2px 5px;
  user-select: none;
  cursor: pointer;
}

.uranus-btn:hover {
  cursor: pointer;
  text-decoration: none !important;
  background: #5d5f61;
}

.uranusBtnDisabled {
  cursor: not-allowed !important;
  color: hsla(0, 0%, 100%, 0.25);
}

.notAllowed {
  cursor: not-allowed !important;
  color: hsla(0, 0%, 100%, 0.25);
  //   cursor: pointer;
  background: #505254 !important;
}

.color-blue {
  background: #2d8cf0;
  color: white;
}
</style>
