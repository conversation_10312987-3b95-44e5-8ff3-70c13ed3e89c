//#region Folder
// 执行高度模式（该元素仅在waylines.wpml中使用。）
export const EXECUTE_HEIGHT_MODE = {
  WGS84: 'WGS84', // 椭球高模式
  relativeToStartPoint: 'relativeToStartPoint', // 相对起飞点高度模式
  realTimeFollowSurface: 'realTimeFollowSurface' // 使用实时仿地模式,仅支持M3E/M3T/M3M,M3D/M3TD
};
//#endregion

/**
 * 模板类型枚举
 * @enum {number}
 * @property {number} waypoint - 航点飞行
 * @property {number} mapping2d - 建图航拍
 * @property {number} mapping3d - 倾斜摄影
 * @property {number} mappingStrip - 航带飞行
 */
export const TEMPLATE_TYPE_ENUM = Object.freeze({
  waypoint: 'waypoint',
  mapping2d: 'mapping2d',
  mapping3d: 'mapping3d',
  mappingStrip: 'mappingStrip'
});

export const TEMPLATE_TYPE_NUM_ENUM = Object.freeze({
  waypoint: 0,
  mapping2d: 1,
  mapping3d: 2,
  mappingStrip: 3
});

// 创建一个反向映射函数
export function getEnumKeyByValue(enumObj, value) {
  return Object.keys(enumObj).find(key => enumObj[key] === value);
}

export const TEMPLATE_TYPE = Object.freeze({
  waypoint: '航点飞行',
  mapping2d: '建图航拍',
  mapping3d: '倾斜摄影',
  mappingStrip: '航带飞行'
});

// GPS：位置数据采集来源为GPS/BDS/GLONASS/GALILEO等
// RTKBaseStation：采集位置数据时，使用RTK基站进行差分定位
// QianXun：采集位置数据时，使用千寻网络RTK进行差分定位
// Custom：采集位置数据时，使用自定义网络RTK进行差分定位
export const POSITIONING_TYPE_ENUM = Object.freeze({
  GPS: 'GPS',
  RTKBaseStation: 'RTKBaseStation',
  QianXun: 'QianXun',
  Custom: 'Custom'
});

// EGM96：使用海拔高编辑
// relativeToStartPoint：使用相对点的高度进行编辑
// aboveGroundLevel：使用地形数据，AGL下编辑(仅支持司空2平台)
// realTimeFollowSurface: 使用实时仿地模式（仅用于建图航拍模版），仅支持M3E/M3T/M3M机型
export const HEIGHT_MODE_ENUM = Object.freeze({
  EGM96: 'EGM96',
  relativeToStartPoint: 'relativeToStartPoint',
  aboveGroundLevel: 'aboveGroundLevel',
  realTimeFollowSurface: 'realTimeFollowSurface'
});
