<template>
  <div class="wrp">
    <div class="action-wrapper bg-dark-blue">
      <div class="action-header bg-dark-blue" v-if="currentActionObj.actionIndex">
        <div class="left">
          <span class="action-icon">
            <el-image style="width: 25px; height: 25px" :src="titleRect.iconUrl" fit="fill" class="" />
          </span>
          <span class="action-name">{{ titleRect.title }}</span>
          <span class="action-name">
            {{ currentActionObj.pointIndex }} <span class="mid"> - </span> {{ currentActionObj.actionIndex }}</span
          >
        </div>
        <!-- {{ currentActionObj.actionUuid }} -->
        <!-- <div class="right">
        <div
          class="delete-icon"
          @click="onClickHandle(currentActionObj)"
          @mouseover="onMouseover"
          @mouseleave="onMouseleave"
        >
          <el-icon :color="colorIcon"><Delete /></el-icon>
        </div>
      </div> -->
      </div>
      <div class="action-content bg-dark-blue" ref="actionContainerRef"></div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'Actions'
};
</script>
<script setup>
import '../../style/common.css';
import { onMounted, onUnmounted } from 'vue';
import { ElMessage, ElInput, ElButton } from 'element-plus';
import 'element-plus/dist/index.css';
import { Delete } from '@element-plus/icons-vue';
import { ActionComponentMap } from '../actionComponents/index.js';
import {
  ACTION_ACTUATOR_FUNC,
  ACTION_TRIGGER_TYPE,
  ACTION_ACTUATOR_FUNC_ICON,
  ACTION_ACTUATOR_FUNC_NAME,
  DELETE_ACTION_TYPE
} from '@/utils/constants';
import { getActionIndex } from '../kmz/hocks/modules/waylineshandle';
import {
  clearActionComponentMap,
  addNewActionToComponentMap,
  getActionComponent,
  setActionComponentData,
  getActionComponentData,
  vueComponentToElement
} from './hocks/componentControl';
import { usePlanInfoStore } from '@/store/modules/planInfo.js';
import { getActionInfo } from '../kmz/hocks';

const planInfoStore = usePlanInfoStore();
const titleRect = reactive({
  iconUrl: '',
  title: ''
});
// 动作容器
const actionContainerRef = ref();
// 当前动作组件相关参数
const currentActionObj = ref({
  actionUuid: '',
  iconUrl: '',
  title: '动作名称',
  actionType: '',
  actionFuncParam: null,
  pointIndex: null,
  actionIndex: null
});
const colorIcon = ref({ color: 'red' }); // 图标颜色
let currentElement = null; // 标记当前显示的组件元素
/**
 * 这里要注意：区分当前的动作来自于哪里
 * 1、来之于wayPointList 组件 这里可能会带有数据 这里一般是创建过组件数据 已经存在
 * 2、来之于 ActionsToolBar 组件 这里不对组件设置数据 需要根据新的动作类型和uuid 创建新的组件对象
 * 点击动作展示相关动作组件
 * @param {object} options
 * @param {string} options.url
 * @param {string} options.title
 * @param {string} options.key
 * @param {object} options.actionFuncParam 动作参数
 * @param {object} options.action 动作对象
 * @param {object} options.actionUuid 动作ID
 * @param {object} options.actionGroup 动作组对象
 */
const setActionComponentVisibleHandle = options => {
  if (!options) {
    removeElement(currentElement);
    currentActionObj.value.actionUuid = null;
    currentActionObj.value.pointIndex = null;
    currentActionObj.value.actionIndex = null;
    currentActionObj.value.iconUrl = null;
    currentActionObj.value.title = null;
    currentActionObj.value.actionType = null;
    currentActionObj.value.actionFuncParam = null;
    return;
  }
  // 设置前一个动作的数据
  let {
    url = '',
    title = '',
    key = '',
    actionUuid = '',
    actionFuncParam = null,
    action = null,
    actionGroup = null
  } = options;

  // 这里要根据动作组判断下是否是间隔动作的动作组
  if (actionGroup) {
    let triggerType = actionGroup.wpml_actionTrigger.wpml_actionTriggerType ?? '';
    const { title: _title = '', icon: iconUrl } = getActionInfo(triggerType, action.wpml_actionActuatorFunc);
    title = _title;
    url = iconUrl;
  }
  titleRect.title = title;
  titleRect.iconUrl = url;
  if (!actionFuncParam) {
    actionFuncParam = action?.action?.wpml_actionActuatorFuncParam ?? null;
  }

  if (!key) {
    removeElement(currentElement);
    currentActionObj.value.actionUuid = null;
    currentActionObj.value.pointIndex = null;
    currentActionObj.value.actionIndex = null;
    currentActionObj.value.iconUrl = null;
    currentActionObj.value.title = null;
    currentActionObj.value.actionType = null;
    currentActionObj.value.actionFuncParam = null;
    return;
  }
  const { pointIndex, actionIndex } = getActionIndex({ actionUuid });
  currentActionObj.value.actionUuid = actionUuid;
  currentActionObj.value.pointIndex = pointIndex || 0;
  currentActionObj.value.actionIndex = actionIndex || 0;
  currentActionObj.value.iconUrl = titleRect.iconUrl || url || '';
  currentActionObj.value.title = titleRect.title || title || '';
  currentActionObj.value.actionType = key || '';
  currentActionObj.value.actionFuncParam = actionFuncParam || null;
  const componentObject = getActionComponent(actionUuid) ?? null;
  currentElement && removeElement(currentElement);
  if (componentObject) {
    // 如果存在组件对象 表明已经创建
    const { element = null, instance = null } = componentObject;
    if (instance) {
      // 将返回的元素添加到 actionContainerRef 的子节点中
      element && actionContainerRef.value.appendChild(element);
      currentElement = element;
      // 构建组件传递参数
      const actionComponentDataOptions = {
        actionUuid,
        actionFuncParam,
        action, // currentActionObj,
        actionGroup
      };
      setActionComponentData(actionComponentDataOptions);
      return;
    }
  } else {
    // 不存在组件对象 表明还未创建  检查数据是否为空
    if (!actionFuncParam) {
      const { element = null } = addNewActionToComponentMap({ actionName: key, actionUuid: actionUuid, data: null });
      // 将返回的元素添加到 actionContainerRef 的子节点中
      element && actionContainerRef.value.appendChild(element);
      currentElement = element;
      // 构建组件传递参数
      const actionComponentDataOptions = {
        actionUuid,
        actionFuncParam,
        action, // currentActionObj,
        actionGroup
      };
      setActionComponentData(actionComponentDataOptions);
      return;
    }
    const { element = null } = addNewActionToComponentMap({
      actionName: key,
      actionUuid: actionUuid,
      data: actionFuncParam
    });
    // 将返 回的元素添加到 actionContainerRef 的子节点中
    element && actionContainerRef.value.appendChild(element);
    currentElement = element;
    // 构建组件传递参数
    setActionComponentData({ actionUuid, actionFuncParam, action, actionGroup });
  }
};

const removeElement = element => {
  if (element && element.parentNode) {
    element.parentNode.removeChild(element);
  }
};

const onMouseover = e => {
  colorIcon.value = '#ff0000';
};

const onMouseleave = e => {
  colorIcon.value = '#ffffff';
};

const onClickHandle = e => {
  // 删除当前动作
  console.log('currentActionObj,', e);
};

onMounted(() => {
  window.$bus.on('setAction', setActionComponentVisibleHandle);
});

onUnmounted(() => {
  window.$bus.off('setAction', setActionComponentVisibleHandle);
});
</script>
<style lang="scss" scoped>
.wrp {
  position: absolute;
  width: 350px;
  height: calc(100% - 40px);
  top: 40px;
  right: 0px;
  margin: 0;
  padding: 0;
}
.action-wrapper {
  width: 350px;
  height: 100%;
  background-color: #11253e;
  color: white;
  user-select: none;
  z-index: 10;
  .action-header {
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #fff;
    border-bottom: 1px solid #4f4f4f;
    background-color: #232323;

    .left {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      .action-icon {
        display: flex;
        align-items: center;
      }
      .action-name {
        display: flex;
        justify-content: center;
        align-items: center;
        vertical-align: middle;
        margin-left: 10px;
        .mid {
          margin: 0px 5px;
          position: relative;
          top: -2px;
        }
      }
    }
    .right {
      background-color: #232323;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .delete-icon {
        display: flex;
        align-items: center;
        .el-icon {
          cursor: pointer;
        }
      }
    }
  }
}
</style>
