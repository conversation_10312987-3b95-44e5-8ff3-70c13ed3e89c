{"14 days": "14 Days", "1_3_0": {"7-day alarm trend": "7-Day Alarm Trend", "Alarm Trend": "Alarm Trend", "Alarm count": "Number of Alarms", "Alarm handler": "Alarm Handler", "Alarm notification": "Alarm Notification", "Alarm switch": "Alarm Switch", "AlarmLogsText": "The alarm logs is used to monitor whether there is a timely push notification to the alarm handler after the device alarms. From this page, you can see what type of historical records the system pushes to the user.", "AlarmRecordText": "The alarms triggered by all devices are recorded. As long as the device is configured with an alarm threshold, the alarms are recorded regardless of whether the alarm handler is configured.", "AlarmStatusText": "The alarm status are used to count whether the user has processed the alarm processing work order after receiving it, and the processing method. Through the processing statistics, the user's record completion rate for device fault processing can be analyzed.", "All group": "All Group", "All total": "All Total", "Batch Settings": "<PERSON><PERSON> Settings", "Card Description": "Card Description", "Card code": "Card Code", "Card name": "Card Name", "Communication signal strength": "Communication Signal Strength", "Confirm disabling the current alarm notification": "Confirm disabling the current alarm notification", "Confirm enabling current alarm notification": "Confirm enabling current alarm notification", "Daily traffic alarm": "Daily Traffic Alarm", "Date": "Date", "Device": "<PERSON><PERSON>", "Device model": "Device Model", "Device offline time": "Device Offline Time", "Device traffic": "Device Traffic", "Device traffic statistics": "Device Traffic Statistics", "Downlink traffic": "Down Flow", "Device Name or Code": "Device Name or Code", "Device alarm statistics": "Device Alarm Statistics", "Last": "Last", "Last month": "Last Month", "Last three months": "Last Three Months", "Last week": "Last Week", "Message Description": "Message Description", "Message center": "Message Center", "Message time": "Message Time", "Minutes": "Minutes", "Monthly traffic alarm": "Monthly Traffic Alarm", "Monthly traffic device": "Monthly Flow Device", "Network": "Network", "Network type distribution": "Network Type Distribution", "New Device Trends": "New Device Trends", "Notification SettingText": "The alarm notification setting assigns personnel to handle alarms promptly. Select the device and personnel to ensure timely response and prevent accidents.", "Notification Type": "Notification Type", "Notification message": "Notification Message", "Offline alarm": "Offline Alarm", "Online rate trend": "Online Rate Trend", "Password": "Password", "Phone number": "Phone number", "Please Tenant ID": "Tenant ID", "Please enter a processing description": "Please enter a processing description", "Please enter the card code or name": "Please enter the card code or name", "Please select a date": "Please select a date", "Please select month": "Please select month", "Please select the alarm handler": "Please select the alarm handler", "Please select year": "Please select year", "Processed successfully": "Processed successfully", "Processing information error Please return and try again": "Processing information error , Please return and try again", "Select product model": "Select Product Model", "Selected device": "Selected Device", "Set alarm handler": "Set Alarm Handler", "Set alarm threshold": "Set Alarm Threshold", "Signal alarm": "Signal Alarm", "System setting": "System Setting", "The upgrade mode cannot be terminated": "This upgrade method cannot be terminated, please confirm whether to force the termination", "Threshold SettingText": "Setting the alarm threshold, and the device can issue a data alarm when the threshold is triggered. Users can set the threshold for a single device or a batch of devices according to actual needs.", "To": "To", "Today": "Today", "Today alarm device": "Alarm Today", "Traffic aggregation": "Traffic", "Trend": "Trend", "Type": "Type", "Uplink flow": "Up Flow", "User guide": "User Guide", "Username": "Username", "Username phone number email": "Username, Phone number, Email", "Web side": "Web Side", "alarm handling": "Alarm Handling", "batching": "Batching", "day": "Day", "device alarm": "<PERSON><PERSON>", "flow": "Data Usage", "give an alarm": "Alarm", "handle": "<PERSON><PERSON>", "login address": "Login Address", "mail": "Email", "operate successfully": "Operate successfully", "order": "Order", "please handle": "Please Handle", "processed": "Processed", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "total flow": "Total Flow", "unit": "Unit", "untreated": "Untreated"}, "1_3_2": {"Monthly alarm device": "Monthly Alarm Device"}, "1_4_0": {"Alarm Message": "Warning Message", "Current Version": "Current Version", "Preupdate Time": "Pre-update time", "Updated Content": "Update Content", "Version Message": "Version Message", "Version Number": "Version Number", "Version Update": "Version Update"}, "1_4_1": {"Config Method": "Config Method", "Config Instructions": "Config Directives", "Event": "Event", "Event Content": "Event Content", "Event Record": "Event Record", "No product model selected or does not support config instructions": "No product model is selected or the product model does not support config commands", "No product model selected or the product model does not support config files": "No product model selected or the product model does not support config files", "Please select a config file or config directive": "Please select a config file or config directive", "Please select a config instruction": "Please select a config command", "Please Please select the product model first": "Please Please select the product model first", "This product model does not support remote config": "This product model does not support remote config"}, "1_5_0": {"Applicant": "Applicant", "Application": "Application", "Application Expiration Date": "Expiration Date", "Application Information": "Application Information", "Application Record": "Application Record", "Application Requirements": "Application Requirements", "Application Requirements Description": "Requirements Description", "Application for extension of validity period": "Expiration date extension application", "Application letter template": "Application Letter Template", "Apply for a formal account": "Apply for official account", "Audit Details": "Audit Details", "Audit Information": "Audit Information", "Audit not passed": "<PERSON><PERSON> not passed", "Authenticate Now": "Authenticate Now", "Authentication method": "Verification Method", "Certification rights": "Certification Interest", "Certified": "Verified", "Click to download the template": "Click to download the template", "Contact number": "Contact Number", "Failure Reason": "Failure Reason", "File Upload": "File Upload", "Formal certification": "Formal Certification", "Formal certification application letter": "Formal certification application letter", "Four-Faith Cloud Enterprise Formal Certification Application Letter": "Four-Faith Cloud Enterprise Formal Certification Application Letter", "If you encounter any problems during the formal certification or extension application process": "If you encounter problems during the formal certification or extension application process, please contact the operation staff for consultation", "Instruction Manual": "Instruction Manual", "Number of applications": "Number of Applications", "Official accounts will gain more functional experience on the platform and exclusive rights of official accounts": "The official account will get more functional experience of the platform and the exclusive rights of the official account", "One Click Login": "One-click Login", "Platform Notification": "Platform News", "Please bind email": "Please bind email", "Please bind mobile phone number": "Please bind mobile phone number", "Please fill in the remarks": "Please fill in the remarks", "Please select an application effective date": "Please select an application effective date", "Remaining until the end of the probationary period": "Remaining until the end of the trial period", "Resubmit": "Resubmit", "Review failed": "Audit Failed", "Tenant Center": "Tenant Center", "The tenant name or contact person has been modified": "The tenant name/contact person has been modified, please re-upload the authorization file! \nModified information will take effect after approval", "To Handle": "To Deal With", "To extend": "To Extend", "User Authentication Tip": "Upload the photo of the company's official certification application letter, the contact person holding the ID card, and the business license of the company. We will review the information you uploaded. If you pass the review, you can pass the certification", "View Application History": "View Application History", "Wired to": "<PERSON>id <PERSON>", "You are": "You Are", "application effective date": "Application Effective Date", "application status": "Application Status", "apply for extension": "Apply For Extension", "apply upload file tip": "Please upload your documents to assist in the review, which must include but not limited to the photo of the official letter of the company's formal certification application, the contact person holding the ID card, and the business license of the company", "change details": "Change Details", "changed to": "Changed To", "delete device tip": "Deleting a device will simultaneously delete all the device data in the system, please confirm whether to continue to delete", "drag files here": "Drag Files Here", "extension application": "Extension Application", "files number": "Files", "in order to avoid": "In order to avoid the impact on yourself if the application is not approved, users are requested to apply on demand in consideration of the current account validity period and needs", "supported format": "Spported Format", "tenant": "Tenant", "the user initiates": "The user initiates an application for extending the use period according to the demand, and we will review your application, and the use of the platform can be extended after the review is passed"}, "1_6_0": {"Access Time": "Access Time", "AddressTest": "Jimei District, Xiamen City, Fujian Province", "Allocation Template": "Allocation Template", "Analysis And Suggestions": "Analysis And Suggestions", "Base Info": "Base Info", "Basic Information": "Basic Information", "Basic Normal": "Basic Normal", "CPU Usage": "CPU Usage", "Card Preview": "Card Preview", "Check Progress": "Check Progress", "Check whether the template is enabled": "Check whether the template is enabled", "China Mobile": "China Mobile", "China Telecom": "China Telecom", "China Unicom": "China Unicom", "Clear": "Clear", "Click the Edit button and configure the required parameters under this option": "Click the Edit button and configure the required parameters under this option", "Clone Template": "<PERSON><PERSON> Template", "Config at least one display item": "Config at least one display item", "Confirm whether to disable the template": "Confirm whether to disable the template", "Connected": "Connected", "Connected Devices": "Connected Devices", "Connecting Subdevice": "Connecting Subdevice", "Connection Mode": "Connection Mode", "Connection Platform": "Connection Platform", "Connection Rate": "Connection Rate", "Daily Flow Alarm": "Daily Flow Alarm", "Data Closed": "Data Closed", "Data Enabled": "Data Enabled", "Detection": "Detection", "Detection Over Time Analysis": "The test result cannot be obtained normally. You are advised to test again", "Detection Over Time Tips": "The device timed out and the detection failed\t", "Detection Result": "Detection Result", "Device Alarm Report": "Device Alarm Report", "Device Flow Report": "Device Flow Report", "Device Picture": "Device Picture", "Device Type": "Device Type", "Device Unlink Analysis": "If the device is offline, check whether the power supply is normal", "Device Unlink Tips": "If the device is offline, check whether the power supply is normal.", "Device image loading failed": "Device image loading failed", "Devices": "Devices", "Disabled": "Disabled", "Display Style": "Display Style", "Download Speed": "Download Speed", "Exception": "Exception", "Firmware Version": "Firmware Version", "Free": "Free", "Gateway": "Gateway", "Gateway device": "Gateway device", "Gateway sub-device": "Gateway sub-device", "Gentle Reminder": "Gen<PERSON> Reminder", "Honeycomb": "Honeycomb", "IP Address": "IP Address", "IP Conflict": "The ip address of the connected sub-device conflicts. You are advised to change the ip address of the sub-device", "Installation Map": "Installation Map", "Intelligent Detection": "Intelligent Detection", "Intelligent detection": "Intelligent Detection", "LAN": "LAN", "LAN Edit": "LAN Edit", "MAC Address": "MAC Address", "Memory Usage": "Memory Usage", "Monthly Flow Alarm": "Monthly Flow Alarm", "More": "More", "More Info": "More Info", "Network": "Network", "Network Standard": "Network Standard", "No device picture is available": "No device picture is available", "No more cards available": "No more cards available", "Normal": "Normal", "Normal status": "Normal status", "Not Open": "Unopened", "Number of Connected Devices": "Number of Connected Devices", "Number of LAN Ports": "Number of LAN Ports", "Number of SIM Card Slots": "Number of SIM Card Slots", "Number of SIM Cards": "Number of SIM Cards", "Number of WAN Ports": "Number of WAN Ports", "Open Status": "On State", "Opening": "Turned On", "Operator": "Operator", "PDF Report Download": "PDF Report Downloa", "Please enter the number of LAN ports": "Please enter the number of LAN ports", "Please enter the number of SIM cards": "Please enter the number of SIM cards", "Please enter the number of WAN ports": "Please enter the number of WAN ports", "Please select at least one detection item": "Please select at least one test item", "Please select at least one test item": "Please select at least one test item", "Please select the options you want to display": "Please select the options you want to display", "Poor": "Difference", "Port Condition": "Port Status", "Product Selection": "Product Selection", "Real Time Downlink Rate": "Real Time Downlink Rate", "Real Time Uplink Rate": "Real Time Uplink Rate", "Recognized Card": "Card Recognized", "Response Timeout": "Response Timeout", "Running Time": "Running Time", "SIM Card": "SIM Card", "SIM Card Edit": "SIM Card Edit", "SIM Card Status": "Card Status", "SIM Status": "SIM Status", "Select Card": "Select Card", "Signal strength too low": "Signal strength too low", "Signals": "Signal Strength", "Sim Error TIps": "You are advised to check whether the antenna is correctly connected. Antenna position as far as possible in the unenclosed environment, the signal is weak can adjust the antenna position; If the adjustment is still too low, determine whether the carrier signal coverage problem, the verification method can be changed card comparison test", "Small Program": "Small Program", "Small Program Template Edit": "Small Program Template Edit", "Software Version": "Software Version", "State Inspection": "Base Info", "Subclose Mask": "Subclose Mask", "Subnet Mask": "Subnet Mask", "Template View": "Template View", "Test Item": "Detection Items", "The card was not recognized": "The card was not recognized", "The current template is empty": "The current template is empty", "The port is properly connected": "The port is properly connected", "Turn On": "Turn On", "Unconnected": "Unconnected", "Undo": "Undo", "Undo Prompt": "Retention of up to twenty records can be revoked, the current irreversible operation records!", "Unknown Carrier": "Unknown Carrier", "Unrecognized": "Unrecognized", "Upload Speed": "Upload Speed", "Very Good": "Very Good", "WAN": "WAN", "WAN 1": "WAN 1", "WAN Port Config": "WAN Port Config", "WEB": "WEB", "WIFI Link": "WIFI Link", "Way Encryption": "Encryption", "Wired Device": "Wired <PERSON>ce", "Wireless": "Wireless", "Wireless Connection Edit": "Wireless Connection Edit", "Wireless Devices": "Wireless Devices", "Wireless connection normal": "Wireless connection normal", "Work": "Work", "reconnectionErrMsg": "The current network is abnormal. Please try again later", "websocket Error": "The service is abnormal. Please try again later"}, "30 days": "30 Days", "7 days": "7 Days", "AND": "And", "Abort task": "Terminate", "Abort the upgrade": "Terminate", "Academic Research": "Academic Research", "Access address": "Access Address", "Account Name": "Account", "Activate Device": "Activate Device", "Activate Device Statistics": "Activate Device Statistics", "Activate Device Trend": "Activate Device Trend", "Activate Quantity": "Activate Quantity", "Activate Time": "Activate Time", "Agriculture Forestry Animal Husbandry and Fishery": "Agriculture, Forestry, Animal Husbandry & Fishery", "Alarm Logs": "Alarm Logs", "Alarm Manage": "Alarm Manage", "Alarm Record": "Alarm Record", "Alarm Statistics": "Alarm Statistics", "Alarm Status": "Alarm Status", "Alarm Type": "Alarm Type", "Alarm data": "Alarm Data", "Alarm threshold": "Alarm Threshold", "Alarm time": "Alarm Time", "Alarm type statistics": "Alarm type statistics", "Alarms Number": "Alarms Number", "All device": "All Device", "Applicable product model": "Product Model", "Application Succeeded": "Application Succeeded", "Application Time": "Application Time", "Application Type": "Application Type", "Application progress": "Application Progress", "Approved": "Approved", "Are you sure to terminate the current upgrade task": "Confirm to terminate the current upgrade task", "Are you sure you want to terminate the task": "confirm to terminate the task", "Assigning a User": "Assigning a User", "Audit Type": "Audit Type", "Audit failed": "Audit Failed", "Audit succeeded": "<PERSON>t Succeeded", "Audit time": "Audit Time", "Average Handling Time": "Average Handling Time", "Basic information": "Basic Information", "Batch": "<PERSON><PERSON>", "Bind email": "Bind <PERSON><PERSON>", "Bind phone": "Bind Phone", "Binding success": "Binding Success", "Building Materials": "Building Materials", "Cancel": "Cancel", "Cancel administration": "Cancel Administration", "Cancelled authorization": "Cancelled Authorization", "Card Manage": "Card Manage", "Certification specification": "Four Faith Certification Standards", "Change Data": "Change Data", "Change user nickname": "Change User Nickname", "Checked": "Checked", "Click Create Associated Role": "Click to create associated role", "Click Feedback": "Click for feedback", "Click Upload": "Click to Upload", "Click to apply for an official account": "Click to apply for an official account!", "Click to view the progress": "Click to view the progress", "Close": "Close", "Clothing Textile": "Clothing Textile", "Completion time": "Completion Time", "Config File Manage": "Config File Manage", "Config Panel": "Config panel", "Config Task": "Config Task", "Config Tips": "Remote config: 1. Modify config items, 2. <PERSON> Settings, 3. <PERSON>pp<PERSON>, 4. <PERSON><PERSON>. The modified config takes effect only after the restart.", "Config complete": "Config Complete", "Config content": "Config Content", "Config failure": "Config failure", "Config file": "Config File", "Config file size": "Config File Size", "Config in progress": "Config In Progress", "Config product model": "Product Model", "Config the device model": "Config the Device Model", "Config the Device Quantity": "Device Quantity", "Confirm Password": "Confirm Password", "Confirm cancellation": "confirm cancellation", "Confirm the cancellation of authorization": "Confirm the cancellation of authorization", "Confirm to delete the record": "Confirm to delete the record", "Confirm to retry the config task for the device": "Confirm to retry the config task for the device", "Connect your device to the cloud platform through": "Connect your device to the cloud platform through", "Contact Email": "Contact Email", "Contact Mobile Number": "Phone Number", "Contact Us": "Contact Us", "Contact_Email": "Contact_Email", "Contacts": "Contacts", "Copy config file": "Copy Config File", "Corporate name": "Company Name", "Create Now": "Create Now", "Create for users and devices": "Create", "Creating a firmware upgrade task": "Creating New Firmware Upgrade Task", "Current Version": "Current Version", "Customize data permissions": "Customize data permissions", "Customized Firmware": "Customized Firmware", "DashBoard": "DashBoard", "Dashboard Settings": "Dashboard settings", "Data": "Data", "Data Detail": "Data Detail", "Data permission rules": "Data Permission Rules", "Deactivate the currently selected config file": "Deactivate the currently selected config file", "Deleted successfully": "Deleted successfully", "Deliver your device to specialized personnel for operation and maintenance": "and assign data permissions to users.", "Description": "Description", "Device Code": "Device Code", "Device Code or Device Name": "Device Code or Device Name", "Device Details": "<PERSON>ce Det<PERSON>", "Device Grouping": "Device Grouping", "Device ID": "Device ID", "Device ID or Name": "Device ID or Name", "Device Manage": "<PERSON><PERSON>", "Device Name": "Device Name", "Device Name or ID": "Device Name or ID", "Device Trends": "<PERSON><PERSON>", "Device Upgrade Trends": "Device Upgrade Trends", "Device distribution statistics": "Device distribution statistics", "Device list": "Device List", "Device log": "<PERSON><PERSON>", "Device status": "Device Status", "Device upgrade progress": "Upgrade Progress", "Display sort": "Display Sort", "Distribution failed": "Distribution Failed", "Do you want to delete": "Confirm to delete", "Do you want to delete the firmware package": "Do you want to delete the firmware package", "Document type requirements": "Document type requirements", "Download failed please try again later": "Download failed, please try again later", "Download the config file": "Download the config file", "Download the upgrade package": "Download the upgrade package", "Downloading": "Downloading", "E-mail": "E-Mail", "Edit Information": "Edit Information", "Edit completed": "Edit Completed", "Edit config file": "Edit Config File", "Edit succeeded": "Edit Succeeded", "Edit upgrade package": "Edit Upgrade Package", "Education and Training": "Education & Training", "Email Error": "<PERSON><PERSON>", "Enable": "Enable", "Enable the currently selected config file": "Enable the currently selected config file", "Enabled successfully": "Enabled successfully", "End of task": "End", "Energy Device and Services": "Energy Device & Services", "Enlarge": "Enlarge", "Enter the experience": "Enter the Experience", "Enter the node name": "Please fill in the node name", "Enterprise": "Enterprise", "Enterprise Tenant": "Enterprise Tenant", "Enterprise registration": "Enterprise Registration", "Environmental Protection and Greening": "Environmental Protection & Greening", "Device Alarm Processing Statistics": "Device Alarm Processing Statistics", "Escalation State": "Escalation State", "Execution result": "Execution Result", "Existing account": "Existing Account", "FUJIAN": "Fujian", "Factor Report": "Factor Report", "Factor Report Record": "Factor Report Record", "Fail": "Fail", "Fail Times": "Fail Times", "Feedback": "<PERSON><PERSON><PERSON>", "Feedback placeholder": "You can submit any requirements or optimization suggestions to us! Please fill in", "File Status": "File Status", "File Upload Time": "File Upload Time", "File name is too long": "File name is too long", "File service exception  please contact the administrator": "File service exception  please contact the administrator", "Financial Industry": "Financial Industry", "Firmware Manage": "Firmware Manage", "Firmware Name": "Firmware Name", "Firmware Status": "Firmware Status", "Firmware Type": "Firmware Type", "Firmware Upgrade Package": "Firmware Upgrade Package", "Firmware Version": "Firmware Version", "FlowTrend": "Traffic Trend", "For the security of your account  please verify and proceed to the next step after passing the verification": "For the security of your account  please verify and proceed to the next step after passing the verification", "Forced termination": "Forced Termination", "Formal": "Formal", "Four Faith Cloud Platform": "Four Faith Cloud Platform", "Full data permissions": "Full data permissions", "GUANGDONG": "Guangdong", "Gentle Reminder": "Kind tips", "Get Code": "Get Code", "Go online": "Go Online", "Go to Card Settings": "Go to card settings", "Government Organizations": "Government Organizations", "Group Manage": "Group Manage", "Group Notes": "Group Notes", "Group name": "Group Name", "Grouping": "Grouping", "IP address": "IP Address", "IT Industry": "IT Industry", "Identify and copy": "Identify And Copy", "Image upload failed please delete and upload again": "Image upload failed, please delete and upload again", "Image upload failed please try again later": "Image upload failed please try again later", "Improving regulatory efforts through Grouping": "For users and devices.", "In Progress": "In Progress", "Inactive Device": "Inactive Device", "Individual registration": "Individual Registration", "Individual tenant": "Individual Tenant", "Industry": "Industry", "Information change progress": "Information change progress", "Installation Address": "Installation Address", "Instruction sent": "Instruction Sent", "Instrument Panel Config": "Dashboard Config", "Interface request error": "Interface request error", "Keep the value within 40 digits": "Please control it within 40 digits", "Language": "Language", "Language Setting": "Language Settings", "Last start": "Last Start", "Latest Config Status": "Latest Config Status", "Latest Update Time": "Latest Update Time", "Latest config progress": "Latest Progress", "Length Data": "Strip Data", "Local DNS": "Local DNS", "Log Manage": "Log Manage", "Log content": "Log Content", "Log time": "Log Time", "Login account": "<PERSON><PERSON> Account", "Login account including only letters numbers and underscores": "Login account including only letters numbers and underscores", "Login password including letters numbers and special characters": "Login password including letters, numbers and special characters", "Login status error please login again": "Login status error, please log in again", "Longitude and latitude query address failed": "Longitude and latitude query address failed", "Machinery Manufacturing": "Machinery Manufacturing", "Mailbox": "Mailbox", "Maintenance": "Maintenance", "Master account login": "Master Account <PERSON>", "Medical and Health": "Medical & Health", "Menu": "<PERSON><PERSON>", "Menu Manage": "<PERSON><PERSON>", "Menu Permissions": "Menu Permission", "Metallurgical Minerals": "Metallurgical Minerals", "Mini program please search for Four Faith Cloud on WeChat": "(For small programs, please search Four Faith Cloud on WeChat)", "Mobile Number ErroR": "Mobile Number Error", "Mobile Number Error": "Wrong mobile phone number", "Mobile Phone": "Phone", "Modify avatar": "Modify Avatar", "More": "More", "New Config File": "New Config File", "New Config Task": "New Config Task", "New Device": "New Device", "New Upgrade Task": "New Upgrade Task", "New completion": "New Completion", "New contact email": "New Contact Email", "New phone number": "New Phone Number", "New upgrade package": "New Upgrade Package", "Not Started": "Not Started", "Notification Setting": "Notification Setting", "Notification Time": "Notification Time", "Novice guidance": "<PERSON>ce Guidance", "Number of Processed Items": "Number of Processed Items", "Number of Unprocessed Items": "Number of Unprocessed Items", "Number of upgraded devices": "Number of Upgraded Devices", "Official Operation": "Official Operation", "Official account has been applied": "Official account has been applied!", "Offline": "Offline", "Offline devices": "Offline Device", "Oil and Gas and Consumer Fuels": "Oil & Gas & Consumer Fuels", "One Day": "last day", "Online devices": "Online Device", "Online rate": "Online Rate", "Operating IP": "Operating IP", "Operating time": "Operating Time", "Operation log details": "Operation Log Details", "Operation module": "Operation Module", "Operations such as daily operations and maintenance": "Your devices through the Operations and Management module.", "Or WeChat mini program for quick code scanning and addition": ".", "Original Version": "Original Version", "Other Industries": "Other Industries", "Owning Group": "Group", "Owning organizational structure permission": "Owning organizational structure permission", "Package Size": "Package Size", "Parent Group Name": "Parent Group Name", "Parent group": "Parent Group", "Pass the verification": "Verification Passed", "Password": "Login Password", "Password login": "Password Login", "Password reset succeeded": "Password Reset Succeeded", "Personal": "Personal", "Personal Information": "Personal Information", "Personal data permission only": "Only personal data permission", "Phone Number": "Phone Number", "Phone number": "Phone Number", "Place name query failed": "Place name query failed", "Platform addition": "Platform Addition", "Please check and upload": "Please check and upload", "Please check that the latitude and longitude are correct": "Please check whether the latitude and longitude are correct, longitude: -180～180, latitude: -90～90, up to 6 decimal places", "Please confirm whether to delete the group ": "Please confirm whether to delete the group", "Please enter": "Please enter", "Please enter a 6-20 digit login account which can only contain letters numbers and underscores": "Please enter a 6-20 digit login account which can only contain letters numbers and underscores!", "Please enter a 6-20 digit password consisting of numbers letters and special characters": "Please enter a 6-20 digit password consisting of numbers + letters + special characters", "Please enter a contact": "Please enter a contact", "Please enter a correct email address": "Please enter a correct email address", "Please enter a group name": "Please enter a group name", "Please enter a name": "Please enter a name", "Please enter a new password": "Please enter a new password", "Please enter a requirement Description": "Please enter a requirement Description", "Please enter a role name": "Please enter a role name", "Please enter a structure name": "Please enter a structure name", "Please enter a task name": "Please enter a task name", "Please enter a user nickname": "Please enter a user nickname", "Please enter contact phone number ": "Please enter contact phone number", "Please enter display sort": "Please enter display sort", "Please enter email address": "Please enter email address", "Please enter keywords": "Please enter keywords", "Please enter operator": "Please enter operator", "Please enter the account name": "Please enter the account name", "Please enter the confirmation password": "Please enter the confirmation password", "Please enter the correct email address": "Please enter the correct email address", "Please enter the correct mobile phone number": "Please enter the correct phone number", "Please enter the device ID": "Please enter the device ID", "Please enter the device ID or name": "Please enter the device ID or name", "Please enter the device code": "Please enter the device code", "Please enter the device name": "Please enter the device name", "Please enter the email address": "Please enter the email address", "Please enter the firmware version of the upgrade package": "Please enter the firmware version of the upgrade package", "Please enter the group to which you belong": "Please enter the group to which you belong", "Please enter the name of the config file": "Please enter the name of the config file", "Please enter the old password": "Please enter the old password", "Please enter the password again": "Please enter the password again", "Please enter the system module": "Please enter the system module", "Please enter the task number": "Please enter the task number", "Please enter the tenant ID": "Please enter the tenant ID", "Please enter the tenant name": "Please enter the tenant name", "Please enter the upgrade package name": "Please enter the upgrade package name", "Please enter the upgrade task name": "Please enter the upgrade task name", "Please enter the user name": "Please enter the user name", "Please enter the user password": "Please enter the user password", "Please enter the userName or mobile phone or email": "Please enter the username or phone number or email", "Please enter the verification code": "Please enter the correct verification code", "Please enter user name": "Please enter user name", "Please enter your account number": "Please enter your account number", "Please enter your email": "Please enter your email", "Please enter your login account": "Please enter your login account", "Please enter your login password": "Please enter your login password", "Please enter your mobile phone number": "Please enter your phone number", "Please enter your password": "Please enter your password", "Please enter your phone": "Please enter your phone", "Please fill in the Description": "Please fill in the description", "Please fill in the account name": "Please fill in the account name", "Please fill in the company name": "Please fill in the company name", "Please fill in the correct company name to avoid invalid audit": "Please fill in the correct company name to avoid audit failure", "Please fill in the feedback": "Please fill in your feedback", "Please fill in the group name": "Please fill in the group name", "Please fill in the login account": "Please fill in the login account", "Please hold down the slider and drag": "Please hold down the slider and drag", "Please select": "Please select", "Please select a device first": "Please select the device first", "Please select a notification time": "Please select a notification time", "Please select a notification type": "Please select notification type", "Please select a product model that supports the upgrade": "Please select a product model that supports the upgrade", "Please select a status": "Please select a status", "Please select a time range": "Please select a time range", "Please select a user role": "Please select a user role", "Please select a valid period": "Please select a valid period", "Please select an audit type": "Please select an audit type", "Please select at least one notification type": "Please select at least one notification type", "Please select device": "Please select device", "Please select the alarm type": "Please select an alert type", "Please select the applicable product model": "Please select the applicable product model", "Please select the audit result": "Please select the audit result", "Please select the device status": "Please select the device status", "Please select the handler": "Please select a handler", "Please select the industry of the company": "Please select the industry of the company", "Please select the latest config status": "Please select the latest config status", "Please select the message that needs to be processed": "Please select the message to be processed", "Please select the processing status": "Please select a processing status", "Please Please select the product model": "Please Please select the product model", "Please select the province where the company is located": "Please select the province where the company is located", "Please select the reason for failure": "Please select the reason for failure", "Please select the task status": "Please select the task status", "Please select the tenant level": "Please select the tenant level", "Please upload attachments smaller than 5M": "Please upload attachments smaller than 5M", "Please upload the attachment": "Please upload the attachment!", "Please upload the file": "Please upload the file", "Please upload your file to assist in the review The enterprise account application must upload the authorization file with the company seal which supports the extension": "Please upload your file to assist in the review, the enterprise account application must upload the authorization file with the company seal file, support extension", "Power Industry": "Power Industry", "Previous step": "Previous Step", "Privacy Terms": "Four Faith Privacy Policy", "Process Description": "Processing Description", "Processed Alarms Number": "Processed Alarms Number", "Processed by": "Handler", "Processing status": "Processing Status", "Product Model": "Product Model", "Product Model & Firmware Version": "Product Model & Firmware Version", "Product Model statistics": "Product Model Statistics", "Product Models that can be upgraded": "Product Models", "Product type statistics": "Product Type Statistics", "Profile Name": "Profile Name", "Profile details": "Profile Details", "Profile status": "Profile Status", "Province": "Province", "Quantity": "Quantity", "Rapid registration": "Register", "Raw Data Analysis": "Original data analysis", "Raw Data Format": "Original data format", "Re apply": "Reapply", "Read and agree": "Read and Agree", "Real time data": "Real Time Data", "Reason for failure": "Reason for failure", "Reduce": "Reduce", "Refresh later to check the corresponding progress": "Please refresh later to check the corresponding progress", "Registration succeeded please wait patiently for approval": "Registration succeeded, please wait patiently for approval", "Related Products": "Related Products", "Related devices in the group cannot be deleted": "There are related devices under this group and are not allowed to be deleted.", "Relates Information": "Relates Information", "Remaining until the end of the trial": "Remaining until the end of the trial", "Remember me": "Remember Me", "Report Time": "Report Time", "Requirement": "Requirement", "Retrieve by SMS": "Retrieve by SMS", "Retrieve by email": "Retrieve by email", "Return content": "Return Content", "Return to login": "Return to login", "Review reason": "Review reason", "Role": "Role", "Role Manage": "Role Manage", "Role Name": "Role Name", "Rotate Left": "Rotate Left", "Rotate Right": "Rotate Right", "SMS login": "SMS Login", "Safety Protection": "Safety Protection", "Save": "Save", "Save and assign": "Save and assign", "Save successfully": "Save successfully", "Search for the device name or ID": "Search for the device name or ID", "Select Picture": "Select Picture", "Select a config file": "Please select a config file", "Select a config product model": "Please select a product model", "Select a firmware upgrade package": "Upgrade Package", "Select a tenant type": "Please select a tenant type", "Select a user": "Select a User", "Select date": "Select Date", "Select device": "Select Device", "Select device group": "Please select the group you belong to", "Select the firmware upgrade package": "Please select the firmware upgrade package", "Please select the product model": "Please Please select the product model", "Select the upgrade package status": "Please select the Firmware Status", "Select the upgrade package type": "Please select the firmware type", "Selected Data": "Selected Data", "Selecting a Tenant": "Selecting a Tenant", "Service Terms of Four Faith Cloud": "Four Faith Service Terms", "Set a new password": "Set a new password", "Seven Days": "last seven days", "Sign in": "Sign In", "Sign in now": "Sign In Now", "Status change successful": "Status change successful", "Status changed successfully": "Status changed successfully", "StatusCardManager": "Status Card Manager", "Subaccount Login": "Subaccount Login", "Submit Successfully": "Submitted successfully", "Submit application": "Submit Application", "Submit application time": "Submission time", "Success": "success", "Success Times": "Success Times", "Successful deactivation": "Successful Deactivation", "Successfully distributed": "Successfully Distributed", "Successfully upgraded device": "Successfully upgraded device", "System Manage": "System Manage", "System module": "System Module", "Task Creation Time": "Created Time", "Task End Time": "End Time", "Task Name": "Task Name", "Task Progress": "Task Progress", "Task Status": "Task Status", "Task number": "Task Number", "Telephone": "Telephone", "Tenant": "Tenant", "Tenant ID": "Tenant ID", "Tenant Information": "Tenant Information", "Tenant Manage": "Tenant Manage", "Tenant Manager": "Tenant Manager", "Tenant Name": "Tenant Name", "Tenant Type": "Tenant Type", "Tenant level": "Tenant Level", "The 6-20 digit login account can only contain letters numbers and underscores": "The 6-20 digit login account can only contain letters, numbers and underscores!", "The current limit is 10 files": "The current limit is 10 files", "The current logged in user currently does not have permission for this page": "The current logged in user currently does not have permission for this page", "The current verification has expired": {" Please wait for the second reading to end and resend the verification code": " Please wait for the second reading to end and resend the verification code"}, "The device is currently offline": "The device is currently offline", "The device is offline and debugging is not supported": "The device is offline and debugging is not supported", "The device list cannot be empty": "The device list cannot be empty", "The firmware package is disabled successfully": "The firmware package is disabled successfully", "The firmware package is enabled successfully": "The firmware package is enabled successfully", "The format of the attachment is incorrect": {" Please delete it and upload it again": "The format of the attachment is wrong, please upload it again!"}, "The format of the upgrade package may be incorrect": "The format of the upgrade package may be incorrect", "The interface request failed please try again later": "An error occurred in the interface request, please try again later.", "The login account cannot start with a number or underscore": "The login account cannot start with a number or underscore", "The maximum length is 50 characters": "The maximum length is 50 characters", "The organizational structure and the following permissions": "The group to which it belongs and the following permissions", "The passwords entered twice are inconsistent": "The passwords entered twice are inconsistent", "The restart command has been delivered": "The restart command has been delivered", "The size of the uploaded file cannot exceed 100MB": "The size of the uploaded file cannot exceed 100MB", "The size of the uploaded file cannot exceed 50MB": "The size of the uploaded file cannot exceed 50MB", "The task also has": "The task also has", "Three Days": "last three days", "Three days": "Three Days", "Threshold Setting": "<PERSON><PERSON><PERSON><PERSON>", "Total Alarms Number": "Total Alarms Number", "Total alarm": "Total alarm", "Total device quantity": "All Device", "Total device": "Total device", "Tourism and Entertainment": "Tourism & Entertainment", "Traffic trend statistics": "Traffic Trend Statistics", "Transportation": "Transportation", "Trigger Value": "Trigger Value", "Type of operation": "Type of Operation", "Under review": "Under Review", "Units": "Units", "Universal Firmware": "Universal Firmware", "Unprocessed Alarms Number": "Unprocessed Alarms Number", "Unselect All": "Unselect All", "Update succeeded": "Update Succeeded", "Upgrade Frequency Trend": "Upgrade Frequency Trend", "Upgrade Package Exclusive Tenant": "Upgrade Package Exclusive Tenant", "Upgrade Task": "Upgrade Task", "Upgrade failed device": "Upgrade failed device", "Upgrade failure": "Upgrade failure", "Upgrade file": "Upgrade File", "Upgrade package file": "Upgrade Package File", "Upgrade succeeded": "Upgrade Succeeded", "Upgrade success rate statistics": "Upgrade success rate statistics", "Upgrade task name": "Upgrade Task Name", "Upgrade terminated": "Upgrade terminated", "Upgrade the firmware version": "Firmware Version", "Upgrade the package firmware version": "Firmware Version", "Upgraded Device Count": "Upgraded Device Count", "Upgraded version": "Upgraded Version", "Upload files": "Upload Files", "Upload the config file": "Please upload the config file", "Upload the upgrade package file": "Please upload the upgrade package file", "Upload time": "Upload Time", "Upper level node": "Upper Level Node", "User Manage": "User Manage", "User log": "User Log", "User name": "User Name", "User nickname": "Name", "Validation failed": "Validation failed", "Verification Code": "Verification Code", "Verification method": "Verification Method", "Verify email": "<PERSON><PERSON><PERSON>", "Verify phone": "Verify Phone", "View Config Task Details": "View Config Task Details", "View Upgrade Package": "View Upgrade Package", "View Upgrade Task Details": "View Upgrade Task Details", "View details": "View", "View information change review progress": "View information change review progress", "View task": "View Task", "View the complete progress": "Complete Progress", "Waiting for upgrade": "Waiting for upgrade", "Waiting to download": "Waiting to download", "Water Conservancy Industry": "Water Conservancy Industry", "Welcome to login": "Welcome to login", "Whether it is used for ZTP": "Whether it is used for ZTP", "Whether to delete the selected config file": "Whether to delete the currently selected config file", "Whether to disable the currently selected firmware package": "Whether to disable the currently selected firmware package", "Whether to enable the currently selected firmware package": "Whether to enable the currently selected firmware package", "Whether to extend the trial": "Whether to extend the trial", "You can remotely manage your device in the operation and maintenance Manage module": "Support Remotely", "Your account application status": "Your account application status:", "ZTP set successfully": "ZTP set successfully", "ZTP setting": "ZTP Setting", "a week": "A Week", "account type": "Account Type", "add": "Add", "adopt": "Adopted", "alarmManager": {"alarmDataStatistics": {"prompt": {"text": "The alarms triggered by all devices are recorded. As long as the device is configured with an alarm threshold, the alarms are recorded regardless of whether the alarm handler is configured.", "title": "Alarm Record"}}, "notificationLog": {"prompt": {"text": "The alarm logs is used to monitor whether there is a timely push notification to the alarm handler after the device alarms. From this page, you can see what type of historical records the system pushes to the user.", "title": "Alarm logs"}}, "processingStatistics": {"prompt": {"text": "The alarm status are used to count whether the user has processed the alarm processing work order after receiving it, and the processing method. Through the processing statistics, the user's record completion rate for device fault processing can be analyzed.", "title": "Alarm Status"}}, "thresholdSetting": {"dialog": {"communicationSignalStrength": "Communication Signal Strength", "dailyTrafficAlarm": "Daily Traffic Alarm", "deviceOfflineTime": "Offline Time", "minutes": "Minutes", "monthlyTrafficAlarm": "Monthly Traffic Alarm", "offlineAlarm": "Offline Alarm", "selectDevice": "Select Device", "selectProductModel": "Select Product Model", "setAlarmThreshold": "Set Alarm Threshold", "signalAlarm": "Signal Alarm", "title": "Alarm Threshold Config"}, "prompt": {"text": "Setting the alarm threshold, and the device can issue a data alarm when the threshold is triggered. Users can set the threshold for a single device or a batch of devices according to actual needs.", "title": "<PERSON><PERSON><PERSON><PERSON>"}}}, "all": "All", "annual statement": "Annual Report", "binding": "Binding", "bypass account": "Bypass Account", "cancel authorization": "Cancel authorization", "cardManager": {"page": {"cardCode": "Card Code", "cardDescription": "Card Description", "cardName": "Card Name", "thumbnail": "Card Style"}, "search": {"cardNameOrCardCode": "Card Name or Code"}, "table": {"thumbnail": "Card Style"}}, "cardName or cardCode": "Card Name or Code", "change password": "Change Password", "check all": "Select All", "client": "client", "closing date": "Closing Date", "complete": "Complete", "config": "Config", "confirm": "Confirm", "contacts": "Contacts", "content": "Content", "copy": "Copy", "create": "Add", "create time": "Create Time", "cue": {"Config Task": "The remote config task refers to the function of updating device config files on the Four Faith Cloud Platform. This function allows you to batch configure devices, including ZTP config files and other config files.", "Config file": "A config file stores device or application settings. In Four Faith Cloud Platform, config files manage network, parameter, and security configs, simplifying management and deployment.", "Device log": "Device logs record the running status of devices on the Four Faith Cloud Platform. With this function, you can view device running records, including the online and offline status, upgrade, and config, facilitating the tracing of historical device running records.", "Firmware Upgrade Package": "A firmware upgrade package is a file used to update a device's operating system or software. It includes security fixes, performance improvements, and new features. Upload the package for remote firmware upgrades on the platform.", "Group Manage": "Group manage is a hierarchical structure that centrally manages users and devices. Sub-groups are created under groups and bound to different users and devices to control data rights of different users.", "Role Manage": "Role manage allows administrators to assign user permissions. Create roles, assign menu and data access rights, and assign roles to users for proper access control.", "Upgrade Task": "The upgrade task feature on the Four Faith Cloud Platform enables batch device firmware updates. It sends upgrade instructions to devices, monitors the upgrade process for accuracy and stability, and tracks task completion and firmware upgrade status.", "User Manage": "User manage allows you to manage user information in a unified manner, including creating, editing, and deleting users. By managing user groups and assigned user roles, administrators can control device data permissions and visual menu permissions of login users.", "User log": "The user operation log function enables the administrator to view user operation records on the device, including operation time, operator, and operation content, so as to comprehensively monitor and manage operation records."}, "custom": "Customize", "daily statement": "Daily Report", "dashboardCard": {"allDevice": "All Devices", "connectYourDeviceToTheCloudPlatformThrough": "Connect your device to the cloud platform through", "createForUsersAndDevices": "create for users and devices", "groupManage": "Group Management", "improvingRegulatoryEffortsThroughGrouping": "improving regulatory efforts through grouping.", "inactiveDevice": "Inactive Devices", "miniProgramPleaseSearchForFourFaithCloudOnWeChat": "(For the Mini Program, please search for Four Faith Cloud on WeChat)", "noPermission": "The current logged-in user does not have permission to access this page", "noviceGuidance": "<PERSON>ce Guidance", "offlineDevices": "Offline Devices", "onlineDevice": "Online Device", "onlineDevices": "Online Devices", "orWeChatMiniProgramForQuickCodeScanningAndAddition": "or WeChat Mini Program for quick code scanning and addition.", "platformAddition": "Platform Addition", "todayAlarmDevice": "Today's Alarm Devices"}, "dashboardcard": {"14days": "14days", "30days": "30days", "7days": "7days", "Date": "Date", "Number of upgraded devices": "Number of upgraded devices", "advancedSearch": "Advanced Search", "alarmCount": "Alarm Count", "alarmStatistics": "Alarm Statistics", "alarmStatus": "Alarm Status", "annualStatement": "Annual Statement", "custom": "Custom", "dailyStatement": "Daily Statement", "dailyTrafficAlarm": "Daily Traffic Alarm", "deviceUpgradeTrends": "Device Upgrade Trends", "inactiveDevice": "Inactive Device", "monthlyStatement": "Monthly Statement", "monthlyTrafficAlarm": "Monthly Traffic Alarm", "newDevice": "New Device", "number": "number", "offlineAlarm": "Offline Alarm", "order": "Order", "processed": "Processed", "processedAlarmsNumber": "Processed AlarmsNumber", "signalAlarm": "Signal Alarm", "totalAlarmsNumber": "Total Alarms Number", "unit": "Unit", "units": "Units", "unprocessedAlarmsNumber": "Unprocessed Alarms Number", "untreated": "Untreated"}, "data permission": "Data Permission", "days": "Days", "delete": "Delete", "department": "Department", "detailDetail": {"action": {"confirmDeleteDevice": "Confirm deletion of device"}}, "details": "Details", "determine": "Determine", "device": "<PERSON><PERSON>", "deviceDetail": {"actionFb": {"upgradeTaskCreatedSuccessfully": "Upgrade task created successfully"}, "dialog": {"title": {"createUpgradeTask": "Create Upgrade Task"}}, "firmwareName": "Firmware Package Name", "input": {"errorTips": {"inputUpgradeTask": "Please enter the upgrade task name"}, "inputUpgradeTask": "Please enter the upgrade task name", "upgradeTaskName": "Upgrade Task Name"}, "latestUpdateTime": "Latest Update Time", "newUpgradePkg": "New Upgrade Package", "upgradeFirmwarePkg": "Upgrade Firmware Package", "upgradeFirmwareVersion": "Firmware Version", "upgradeTask": "Upgrade Task", "Run Log": "Run Log", "Real Time Log": "Real Time Log", "History Log": "History Log", "Log Detail": "Log Detail", "Download Log": "Download Log", "Get History Log": "Get History Log", "Help": "Help", "Please select QA type": "Please select QA type", "FAQ": "FAQ", "Procedure": "Procedure", "Scenario": "<PERSON><PERSON><PERSON>", "Topology": "Topology", "V": "V", "Scene Empty": " No scene data available, please try again later", "Device networking scenarios": "Device networking scenarios"}, "deviceManager": {"belongingGroup": "Device Grouping", "deviceCodeorDeviceName": "Device Code or Device Name", "deviceHandover": "<PERSON><PERSON>"}, "deviceModel": {"All logs": "All Logs", "Cancel Execution": "Cancel Execution", "Click or drag the file here to upload": "Click or drag the file here to upload", "Config details": "Config Details", "Confirm delete device": "Confirm delete device", "Confirm restarting the device": "Confirm restarting the device", "Connecting device": "Connecting Device", "Create Upgrade Task": "Create Upgrade Task", "Delete device": "Delete", "Deleted label": "Deleted Tag", "Device Information": "Device Information", "Device Selected": "<PERSON><PERSON>", "Device Upgrade": "Device Upgrade", "Device addition": "Device Addition", "Device deleted": "<PERSON><PERSON> Deleted", "Device handover": "<PERSON><PERSON>", "Device panel disabled": "Device Panel disabled", "Device template": "<PERSON><PERSON> Template", "Device transfer": "Device Transfer", "Device transfer completed": "Device Transfer Completed", "Distribution failed": "Distribution Failed", "Download Template": "Download Template", "Duplicate label already exists": "Duplicate tag already exists", "Enable ZTP": "Enable ZTP", "Error in importing parameter format": "Error in importing parameter format", "Execution failed": "Execution Failed", "Execution succeeded": "Execution Succeeded", "Import was successful": "Import was Successful", "Importing parameters": "Importing Parameters", "Input": "Input", "Issuing instructions": "Issuing Instructions", "Load panel": "Load Panel", "New Label Complete": "New Label Complete", "New Tags": "New Tags", "No device information currently available": "No device information currently available", "No information found on this device": "No information found on this device", "Offline time": "Offline Time", "One click read": "Read", "One click settings": "Setting", "Online and offline records": "Online & Offline", "Online duration": "Online Duration", "Only supports file formats": "Only supports file formats", "Parameter config successful": "Parameter config successful", "Parameter distribution failed": "Parameter distribution failed", "Please enter a label": "Please enter a Tag", "Please enter the complete lat and lng": "Please enter the complete lat and lng", "Please enter the device code or device name": "Please enter the device code or device name", "Please fill in the Description": "Please fill in the description", "Please fill in the device code": "Please fill in the device code", "Please fill in the device label": "Please fill in the device tag", "Please fill in the device name": "Please fill in the device name", "Please fill in the installation address": "Please fill in the installation address", "Please fill in the name of the upgrade task": "Please fill in the name of the upgrade task", "Please select a group": "Please select a group", "Please select a label": "Please select a tag", "Please select a log type": "Please select a log type", "Please select a product series": "Please select a product series", "Please select a product type": "Please select a product type", "Please select an associated product": "Please select an associated product", "Product Series": "Product Series", "Reading device parameters": "Reading device parameters", "Response timeout": "Response Timeout", "Restart": "<PERSON><PERSON>", "Select Group": "Select Group", "Select Label": "Select Label", "Send": "Send", "Software Version": "Software Version", "Successfully distributed": "Successfully distributed", "Successfully issued restart command": "Successfully issued restart command", "Support extension": "Support Extension", "Template Import Add": "Template Import Add", "The current browser does not support webSocket": "The current browser does not support webSocket", "The size of the uploaded file cannot exceed": "The size of the uploaded file cannot exceed", "There are currently no commonly used commands": "There are currently no commonly used commands", "There is currently no information about this device": "There is currently no information about this device", "Upgrade Details": "Upgrade Details", "Upgrade Record": "Upgrade Record", "Upgrade Task": "Upgrade Task", "Upgrade firmware package": "Upgrade Firmware Package", "Upgrade task created successfully": "Upgrade task created successfully", "Waiting for execution": "Waiting for Execution", "address": "Address", "advanced search": "Advanced Search", "command": "Command", "commands": "Commands", "config record": "Config Record", "connection exception": "Connection Exception", "debugger": "Debugger", "delete label": "Delete Tag", "device offline": "Device Offline", "device offline Debugger": "Device Offline Debugger", "executing": "Executing", "export log": "Export Log", "fail in send": "Fail In Send", "implementation": "Implementation", "lat and lng": "Lat and Lng", "latitude": "Latitude", "log": "Log", "log information": "Log Information", "log type": "Log Type", "longitude": "Longitude", "overview": "Overview", "product type": "Product Type", "reboot device": "Reboot", "reconnect": "Reconnect", "remote Upgrade": "Upgrade", "remote config": "Config", "remote debugging": "Debugging", "uptime": "Uptime"}, "devices": "Devices", "devices that can terminate the upgrade": "devices that can terminate the upgrade", "dialog": {"Config Timeout tip": "Select an appropriate config duration based on the number of devices. If the config expires, the config task is terminated", "Please enter the upgrade duration": "Please enter the upgrade duration", "Upgrade Duration": "Upgrade Duration", "Upgrade Duration Tip": "Select an appropriate upgrade duration based on the number of devices. If the upgrade expires, the upgrade will be terminated", "detailUpgradePacket": {"title": "View Upgrade Packet"}, "details": "Details", "deviceLog": {"In Progress": "In Progress", "abortUpgrade": "Terminate", "close": "Close", "completionTime": "Completion Time", "configComplete": "Config Complete", "configContent": "Config Content", "configFailure": "Config Failure", "executionResult": "Execution Result", "forcedTermination": "Forced Termination", "lastStart": "Last Start", "operatingIP": "Operating IP", "returnContent": "Return Content", "startTime": "Start Time", "upgradeFailure": "Upgrade Failure", "upgradeFirmwareVersion": "Upgrade Firmware Version", "upgradeSucceeded": "Upgrade Succeeded", "upgrading": "Upgrading", "waitingToDownload": "Waiting to Download"}, "devicelog": {"close": "close"}, "nextStep": "Next Step", "previousStep": "Previous Step", "roleManager": {"checkAll": "Check All", "customizeDataPermissions": "Customize Data Permissions", "dataPermission": "Data Permission", "dataPermissionRules": "Data Permission Rules", "displaySort": "Display Sort", "fillDisplaySort": "Please enter display sort", "fillRoleName": "Please enter a role name", "fold": "Fold", "fullDataPermissions": "Full Data Permissions", "menuPermissions": "Menu Permissions", "orgStructureAndFollowingPermissions": "Org Structure and Following Permissions", "owningOrganizationalStructurePermission": "Owning Organizational Structure Permission", "roleName": "Role Name", "unfold": "Unfold", "unselectAll": "Unselect All"}, "selectProductModel": "Select Product Model", "submit": "Submit", "upgradeTask": {"customizedFirmware": "Customized Upgrade Packet", "deviceGrouping": "Device Grouping", "deviceListCannotBeEmpty": "Device List Cannot Be Empty", "deviceStatus": "Device Status", "enterUpgradeTaskName": "Please enter the upgrade task name", "deviceNameorCode": "Device Name or Code", "firmwareUpgradePkg": "Upgrade Packet", "firmwareVersion": "Firmware Version", "offLine": "Offline", "online": "Online", "pleaseSelectDevice": "Please Select Device", "productModel": "Product Model", "selectDevice": "Select Device", "selectFirmwareUpgradePkg": "Please select the firmware upgrade package", "title": "Create New Firmware Upgrade Task", "unactivated": "Unactivated", "universalFirmware": "Universal Upgrade Packet", "upgradePkgFirmwareVersion": "Upgrade Packet Firmware Version", "upgradeTaskName": "Upgrade Task Name", "upgradedDeviceCount": "Upgraded Device Count"}}, "disable": "Disable", "distribution": "Distribution", "do not have permission for this page": "Do not have permission for this page", "download": "Download", "edit": "Edit", "email": "Email", "empty": "Empty", "end time": "EndTime", "endDate": "EndDate", "examine": "Examine", "export": "Export", "file": "File", "findings of audit": "Audit Results", "firmwareUpgradePkg": {"btn": {"newUpgradePkg": "新增升级包"}, "card": {"text": "固件升级包是包含新版本固件的文件，用于更新设备的操作系统或其他系统软件。固件是设备的低级软件，控制硬件设备的操作和功能。固件升级包通常包含修复漏洞、改进性能、添加新功能等更新内容，在平台上对设备进行远程固件升级时需要提前上传固件升级包。", "title": "固件升级包"}, "firmwareType": "升级包类型", "input": {"firmwareName": "固件升级包名称"}, "page": {"applicableProductModel": "适用产品型号", "customizedFirmware": "定制升级包", "firmwareName": "固件升级包名称", "firmwareStatus": "升级包状态", "firmwareType": "升级包类型", "founder": "创建人", "latestUpdateTime": "最新更新时间", "pkgSize": "升级包大小", "platform": "平台", "universalFirmware": "通用升级包"}, "select": {"customizedFirmware": "定制升级包", "firmwareStatus": "升级包状态", "firmwareType": "升级包类型", "universalFirmware": "通用升级包"}}, "fold": "Fold", "founder": "Founder", "general": {"collapse": "Collapse", "expand": "Expand"}, "goBack": "Back", "gridding": "Grid", "import": "Import", "index": "Index", "initPassword": {"enterPassword": "Please enter the confirmation password", "enterPwdAgain": "Please enter the password again", "enterYourLoginPassword": "Please enter your login password", "enteredTwiceAreInconsistent": "The two entered passwords are inconsistent", "pwdTips": "Please enter a password of 6-20 characters consisting of numbers, letters, and special characters", "resetSucceeded": "Password reset succeeded", "setNewPassword": "Set a new password"}, "is verification": "Iis Verification", "length limit": "Length Limit", "log out": "Log Out", "logManager": {"devicelog": {"promptContent": {"text": "Device logs record the running status of devices on the Four Faith Cloud Platform. With this function, you can view device running records, including the online and offline status, upgrade, and config, facilitating the tracing of historical device running records.", "title": "<PERSON><PERSON> Logs"}, "title": "<PERSON><PERSON> Logs"}}, "login": {"SMS login": "SMS Login", "account": "Username", "dragVerify": {"failTip": "Verification failed, drag the slider to merge the floating images correctly", "successText": "Verification Passed", "successTip": "Verification passed, speed exceeds 80% of users", "text": "Please hold down the slider and drag it to the right", "title": "Safety verification"}, "enterPhone": "Please enter your phone number", "enterPwd": "Please enter your password", "enterTenantID": "Tenant ID", "enterUserName": "Username", "enterVerificationCode": "Verification Code", "getCode": "Get Code", "login": "<PERSON><PERSON>", "masterAccountLogin": "Master Account <PERSON>", "password": "password", "phoneNumber": "Please enter your phone number", "pwd": "Please enter your password", "pwdLogin": "Password Login", "rapidRegistration": "Register", "registration": "Register", "rememberMe": "Remember Me", "rememberPwd": "Remember Me", "retrievePassword": "Retrieve password", "retrievePwd": "Retrieve password", "subaccountLogin": "Subaccount Login", "tenantID": "Tenant ID", "username": "Username", "usernamePhoneOrEmail": "Username", "verificationCode": "Verification Code", "welcome": "Welcome to login"}, "login was successful": "<PERSON><PERSON> was Successful", "logout": {"title": "Determine the cancellation and exit system?"}, "menuManager": {"button": "<PERSON><PERSON>", "catalog": "Catalog", "menu": "<PERSON><PERSON>", "menuType": "Menu Type", "page": "Page", "pageTag": "Page Tag", "root": "Root Directory"}, "model": "Model", "modify successfully": "Modify successfully", "monthly statement": "Monthly Report", "name": "Name", "navbar": {"changePwd": "Change Password", "closeTagView": "Close Tab Mode", "dashboard": "Dashboard", "dashboardSettings": "Dashboard Settings", "document": "Project Documents", "instrumentPanelConfig": "Instrument Panel Config", "logout": "Logout", "openTagView": "Open Tab Mode", "switchLanguageSuccess": "Switching language successfully!", "tagViewSettings": "<PERSON><PERSON><PERSON>", "tenantCenter": "Tenant Center", "tenantInfo": "Tenant Information", "toggleTheme": "Toggle Theme", "userCenter": "User Center"}, "new password": "New Password", "next step": "Next Step", "no": "No", "normal": "Normal", "not card": "No card yet, please go to add", "number": "Number", "off-line": "Offline", "old password": "Old Password", "on trial": "On Trial", "one month": "One Month", "online": "Online", "operate": "Operation", "operationManager": {"configFileManager": {"detail": {"title": "Config File Details"}, "dialog": {"configContent": "Config Content", "configFile": "Config File", "configFileSize": "Config File Size", "detail": {"title": "Config File Details"}, "downloadConfigFile": "Download Config File", "profileStatus": "Config File Status", "uploadTime": "Upload Time"}}, "pkgUpgrade": {"deleteTip": "Do you want to delete the firmware package '{pkg}'?", "disable": "Do you want to disable the currently selected firmware package", "disableSuccess": "Firmware package disabled successfully", "enable": "Do you want to enable the currently selected firmware package", "enableSuccess": "Firmware package enabled successfully"}, "pkgUpgradeTask": {"abortSuccess": "Command has been issued, please refresh later to view the corresponding progress", "abortTip": "There are still {notStartedNum} devices in this task that can be aborted. Are you sure you want to abort the task?", "stopTip1": "This upgrade method cannot be stopped. Please confirm whether to force termination.", "stopTip2": "Confirm termination of the current upgrade task"}, "profileManager": {"addNewConfigFile": "New Config File", "dialog": {"enterNameOfTheConfigFile": "Please enter the name of the config file", "maximumLength50": "Maximum length is 50 characters", "pleaseEnter": "Please enter", "pleaseEnterNameOfConfigFile": "Please enter the name of the config file", "profileName": "Profile Name", "selectProductModel": "Please select the product model", "selectTheProductModel": "Please select the product model", "title1": "Edit Config File", "title2": "Copy Config File", "title3": "New Config File", "uploadFiles": "Upload Files", "uploadTheConfigFile": "Please upload the config file", "uploadTips": "Uploaded file size cannot exceed 50MB"}, "disableTip": "Disable the currently selected config file", "enableTip": "Enable the currently selected config file", "promptContent": {"text": "A config file stores device or application settings. In Four Faith Cloud Platform, config files manage network, parameter, and security configs, simplifying management and deployment.", "title": "Config File"}}, "remoteConfigTask": {"dialog": {"collocationMethod": "Config Method", "configContent": "Config Content", "configFile": "Config File", "configTaskDetail": {"title": "View Config Task Details"}, "configurationInstructions": "Config Instructions", "configureMethod": "Config Method", "configuringDeviceNum": "Config Device Number", "deviceListCannotBeEmpty": "Device list cannot be empty", "deviceNameOrCode": "Device Name or Code", "newConfigTask": {"title": "New Config Task"}, "noCommonlyCommands": "No commonly used commands", "notChooseProductModelOrproductModelNotSupportCommand": "Not choose product model or the product model does not support config instructions", "notChooseProductModelOrproductModelNotSupportConfig": "Not choose product model or the product model does not support config files", "pleaseSelectDevice": "Please select device", "productModelNotSupportConfig": "The product model does not support remote config", "selectConfigFile": "Select Config File", "selectConfigurationFileOrConfigurationDirective": "Select config file or config directive", "selectConfigurationInstruction": "Select config instruction", "selectDevice": "Select Device", "selectProductModel": "Select Product Model", "selectProductModelFirst": "Please select product model first", "taskNumber": "Task Number", "taskStatus": "Task Status", "title": "View Config Task Details"}, "instructionSent": "Instruction has been issued", "retryTip": "Are you sure you want to retry the remote config task for this device?"}, "remoteConfigTaskManager": {"promptContent": {"text": "The remote config task refers to the function of updating device config files on the Four Faith Cloud Platform. This function allows you to batch configure devices, including ZTP config files and other config files.", "title": "Config Task"}}, "upgradePacketManager": {"customizedFirmware": "Customized Firmware", "firmwareName": "Firmware Name", "firmwareStatus": "Firmware Status", "firmwareType": "Firmware Type", "productModel": "Product Model", "promptContent": {"text": "A firmware upgrade package is a file used to update a device's operating system or software. It includes security fixes, performance improvements, and new features. Upload the package for remote firmware upgrades on the platform.", "title": "Firmware Upgrade Packages"}, "universalFirmware": "Universal Firmware"}, "upgradeTaskManager": {"promptContent": {"text": "The upgrade task feature on the Four Faith Cloud Platform enables batch device firmware updates. It sends upgrade instructions to devices, monitors the upgrade process for accuracy and stability, and tracks task completion and firmware upgrade status.", "title": "Upgrade Task"}}}, "operations": {"upgradeTask": {"detail": {"title": "View Upgrade Task Details"}, "dialog": {"abortTheUpgrade": "Terminate", "detail": {"title": "View Upgrade Task Details"}, "devices": "{totalNum} devices", "downloading": "Downloading", "endOfTask": "End", "firmwareName": "Firmware Name", "firmwareVersion": "Firmware Version", "inProgress": "In Progress", "notStarted": "Not Started", "taskCreationTime": "Created Time", "taskEndTime": "End Time", "taskStatus": "Task Status", "upgradeFailure": "Upgrade Failure", "upgradeSucceeded": "Upgrade Succeeded", "upgradeTaskName": "Upgrade Task Name", "upgradedDeviceCount": "Upgraded Device Count", "upgrading": "Upgrading", "waitingTodownload": "Waiting to Download"}}}, "operator": "Operator", "paeg": {"deviceName": "deviceName"}, "page": {"Config Duration": "Config Duration", "OnorOfflineRecords": "Online & Offline", "Task Termination": "Termination Config", "Units": "Unit", "UpgradeFirmwareVersion": "Upgrade Firmware Version", "abortTask": "Abort Task", "abortTheUpgrade": "Abort the Upgrade", "abortUpgrade": "Terminate", "accountName": "Account", "accountType": "Account Type", "actorReport": "Actor Report", "add": "Add", "addNewConfigTask": "New Config Task", "addNewTags": "Add New Tags", "addUpgradePkg": "New Upgrade Package", "advancedSearch": "Advanced Search", "alarmThreshold": "Alarm Threshold", "alarmTime": "Alarm Time", "alarmType": "Alarm Type", "all": "All", "allGroup": "All Groups", "allLog": "All Logs", "applicableProductModels": "Product Model", "batchSettings": "<PERSON><PERSON> Settings", "belongingGroup": "Device Grouping", "binding": "Binding", "bypassAccount": "Bypass Account", "Cancel": "Cancel", "catalog": "Catalog", "check": "View", "checkCompleteProgress": "Complete Progress", "checkDetails": "View", "checkTask": "Check Task", "collocationMethod": "Config Method", "complete": "Complete", "completionTime": "Completion Time", "config": "Config", "configComplete": "Config Complete", "configContent": "Config Content", "configFailure": "Config Failure", "configFile": "Config File", "configInProgress": "Config In Progress", "configInprogress": "Config In Progress", "configLog": "Config Log", "configProductModel": "Product Model", "configurationInstructions": "Config Instructions", "confirm": "Confirm", "confirmCancellation": "Confirm Cancellation", "contactName": "Contact Name", "contactPhone": "Contact Phone", "contactUs": "Contact Us", "content": "Content", "copy": "Copy", "createTime": "Create Time", "currentVersion": "Current Version", "customizedFirmware": "Customized Firmware", "data": "Data", "dataDetail": "Data Detail", "days": "Days", "debug": "Debug", "delete": "Delete", "deleteLabel": "Delete Label", "description": "Description", "descriptionOfRequirement": "Description of Requirement", "detailDevice": {"deviceStatus": "Device Status:"}, "details": "Details", "deviceCancelExecution": "Device Cancel Execution", "deviceCode": "Device Code", "deviceCommand": "Device Command", "deviceCommands": "Device Commands", "deviceDetail": {"allDeviceFlow": "All Device Flow", "belongsGroup": "Belongs Group:", "config": "Config", "debugger": "Debugger", "deviceAlarm": "<PERSON><PERSON>", "deviceCode": "Device Code:", "deviceFlow": "Device Flow", "deviceInformation": "Device Information", "deviceName": "Device Name:", "deviceName ": "Device Name:", "deviceStatics": "Device Alarm Statistics", "deviceStaticsTraffc": "Device Traffic Statistics", "deviceStatus": "Device Status:", "deviceTraffic": "Device Traffic", "executing": "Executing", "information": "Device Information:", "installationAddress": "Installation Address:", "log": "Log", "overview": "Overview", "productModel": "Product Model:", "productType": "Product Type:", "select": {"logType": "Select Log Type"}, "softwareVersion": "Software Version:", "upgrade": "Upgrade", "waitingForExecution": "Waiting for Execution"}, "deviceExecutionFailed": "Device Execution Failed", "deviceExecutionSucceeded": "Device Execution Succeeded", "deviceGrouping": "Device Grouping", "deviceID": "Device ID", "deviceImplementation": "Device Implementation", "deviceInformation": "Device Information", "deviceList": "Device List", "deviceLogInformation": "Device Log Information", "deviceModel": "Device Model", "deviceName": "Device Name", "deviceOfflineDebugger": "<PERSON><PERSON> Offline, Debugger Not Supported", "deviceOfflinetime": "Device Offline Time", "deviceOnlineDuration": "Device Online Duration", "deviceOnlineTime": "Device Online Time", "deviceReconnect": "Device Reconnect", "deviceStatus": "Device Status", "deviceTemplate": "<PERSON><PERSON> Template", "deviceUpgradeDetails": "Device Upgrade Details", "deviceUpgradeProgress": "Upgrade Progress", "devices": "Devices", "dialog": {"ImageUploadFaile": "Image Upload Failed, please try again later", "action": {"confirmAndCopy": "Confirm and Copy", "downloadTemplate": "Download Template", "extendsName": "Supports Extensions", "forSave": "For the security of your account, please verify. After verification, proceed to the next step.", "uploadTemplate": "Click or drag files here to upload"}, "actionFb": {"activatedSuccessfully": "Activated Successfully", "applicationSucceeded": "Application Successful", "cancleDelete": "Deletion Cancelled", "connectingDevice": "Connecting Device", "deactivationSuccessful": "Deactivation Successful", "deviceIssuingInstructions": "Issuing Instructions", "deviceOffline": "<PERSON>ce Offline, Config Not Supported", "devicePanelDisabled": "Device Panel Disabled", "deviceResponsnTimeout": "Response Timeout", "deviceTemplate": "<PERSON><PERSON> Template", "failedAdd": "Addition Failed!", "failedChange": "Change Failed!", "failedDeleted": "Deletion Failed!", "failedEdit": "Edit Failed!", "failedSave": "Save Failed!", "failedUpdate": "Update Failed!", "fileBameTooLong": "Filename Too Long", "fileError": "File Service Exception, Please Contact Administrator!", "labelAlreadyExists": "Device Label Already Exists", "lngandlatQueryFailed": "Longitude and Latitude Query Address Failed", "mobileNumberError": "Mobile Number Error", "onlySupportsFormats": "Only Supports Formats", "parameterDistributionFailed": "Parameter Distribution Failed", "pleaseEnterUserNickname": "Please enter user nickname", "readingDeviceParameters": "Reading Device Parameters", "resetPwdSuccessfully": "Password Reset Successful", "restartCommandSuccessfullySent": "Restart Command Successfully Sent", "statusChangedSuccessfully": "Status Change Successful", "successFullyApplyed": "Application Successful", "successFullyCreateLabel": "Label Added Successfully", "successFullyDeleteLabel": "Label Deleted Successfully", "successFullyHandOver": "<PERSON>ce Handover Completed", "successfullyAdd": "Addition Successful!", "successfullyAddNewDevice": "New Device Added Successfully", "successfullyChange": "Change Successful!", "successfullyDeleted": "Deletion Successful!", "successfullyEdit": "Edit Successful!", "successfullyModifyDevice": "<PERSON>ce Edit Successful", "successfullySave": "Save Successful!", "successfullyUpdate": "Update Successful!", "successfullyUploaded": "Upload Successful", "uploadedFileMustNotExceed": "Uploaded file size must not exceed", "within40Digits": "Within 40 digits"}, "actionTip": {"deleteSelectedConfigFile": "Are you sure you want to delete the currently selected config file", "sureToDelete": "Sure to delete", "tipText": "Tip"}, "alarmHandling": "Alarm Handling", "alarmMessage": "Alarm Message", "alarmThreshold": "Alarm Threshold", "alarmType": "Alarm Type", "applicant": "Applicant", "applicationEffectiveDate": "Application Effective Date", "applicationExpirationDate": "Application Expiration Date", "applicationInformation": "Application Information", "applicationLetterTemplate": "Application Letter Template", "applicationRequirements": "Application Requirements", "applicationRequirementsDescription": "Application Requirements Description", "applicationStatus": "Your account application status:", "applicationTime": "Application Time", "applyFormal": {"formalCertificationApplicationLetter": "Enterprise Formal Certification Application Letter"}, "approved": "Approved", "auditDetails": "Audit Details", "auditFailed": "Audit Failed", "auditInformation": "Audit Information", "auditNotPassed": "Audit Not Passed", "auditTime": "Audit Time", "authorization": {"alert": "Please upload your files for auxiliary review, including but not limited to photos of the enterprise's formal certification application letter, the contact person holding the ID card, and the business license of the enterprise.", "title": "Certify Now"}, "batching": "batching", "cancel": "Cancel", "changeDetails": "Change Details", "changedTo": "Changed To", "chooseDevice": "Selected Devices", "clickUpload": "Click to Upload", "config": {"title": "Config"}, "confirmPassword": "Confirm Password", "contactEmail": "Contact Email", "contactMobileNumber": "Contact Mobile Number", "contacts": "Contacts", "createNewLabel": "Create New Label", "currentLimit10Files": "Currently limited to selecting 10 files", "currentVersion": "current version", "dashboardSettings": "Dashboard Settings", "debugger": {"title": "Debugger"}, "determine": "Determine", "deviceCode": "Device Code", "deviceReconnect": "Reconnect Device", "downloadTemplate": "Click to Download Template", "dragFilesHere": "Drag files here", "emailError": "<PERSON><PERSON>", "enlarge": "Enlarge", "enterConfirmationPassword": "Please enter the confirmation password", "enterCorrectEmailAddress": "Please enter the correct email address", "enterCorrectPhoneNumber": "Please enter the correct phone number", "enterEmailAddress": "Please enter the email address", "enterNewPassword": "Please enter a new password", "enterOldPassword": "Please enter the old password", "enterPasswordAgain": "Please enter the password again", "enterProcessingDescription": "Please enter a processing description", "enterYourEmail": "Please enter your email", "enterYourPhone": "Please enter your phone number", "failureReason": "Failure Reason", "ffFormalCertificationApplicationLetter": "Four-Faith Cloud Formal Certification Application Letter", "fileUpload": "File Upload", "filesNumber": "Files", "fillInTheRemarks": "Please Fill in the Remarks", "getCode": "Get Verification Code", "handle": "<PERSON><PERSON>", "imageUploadFailed": "Image upload failed. Please try again later", "input": {"UpgradePkgFirmwareVersion": "Firmware Version", "contactEmail": "Contact Email", "contactName": "Please enter the contact name", "contactPhone": "Contact Phone", "description": "Please enter the description", "descriptionOfRequirement": "Please enter a requirement description", "deviceCode": "Please enter the device code", "deviceName": "Please enter the device name", "enterLabelName": "Fill in the Device Label", "enterPkgName": "Please enter upgrade package name", "enterUpgradePkgFirmwareVersion": "Please enter the firmware version of the upgrade package", "errorTips": {"checkLngAndLat": "Check if Longitude and Latitude are correct. Longitude: -180~+180, Latitude: -90~+90, up to 6 decimal places.", "deviceCode": "Please fill in the device code", "deviceName": "Please fill in the device name", "email": "Please enter the email address", "enterFirmwareVersion": "Please enter the firmware version of the upgrade package", "enterPkgName": "Please enter the upgrade package name", "fillName": "Please enter a name", "fillReasonableLngAndLat": "Please fill in complete longitude and latitude", "installationAddress": "Please fill in the installation address", "uploadPkgFile": "Please upload the upgrade file"}, "fillRemark": "Please fill in the remark", "firmwareName": "Firmware Name", "input": "Enter", "installationAddress": "Enter Installation Address", "productCanBeUpgraded": "Product Models", "productModel": "Enter Product Model", "pwdLimit": "Password length limit", "rightEmail": "Please enter the correct email address", "rightPhoneNumber": "Please enter the correct phone number", "upgradeFile": "Upgrade File", "userPwd": "Please fill in the user password"}, "inputVerificationCode": "Enter verification code", "instructionManual": "Remark Instructions", "loadingPanel": "Loading Panel", "mailbox": "Mailbox", "messageDescription": "Message Description", "messageTime": "Message Time", "needVerification": "For your account security, please verify, and proceed to the next step after passing the verification.", "newContactEmail": "New Contact Email", "newPassword": "Set New Password", "newPhoneNumber": "New Phone Number", "oldPassword": "Old Password", "oneClickRead": "Read", "oneClickSettings": "Settings", "phoneNumber": "Phone Number", "platformNotification": "Platform Notification", "pleaseEnterCorrectEmailAddress": "Please enter the correct email address", "pleaseEnterCorrectMobilePhoneNumber": "Please enter the correct mobile phone number", "pleaseEnterEmailAddress": "Please enter the email address", "pleaseEnterVerificationCode": "Please enter the correct verification code", "pleaseEnterYourPhone": "Please enter your phone number", "pleaseHandle": "请处理", "proccessErrorTips": "Processing information error, please return and try again", "processedSuccessfully": "Processed Successfully", "pwdAreInconsistent": "Passwords entered twice do not match", "pwdsettingTips": "Please enter a password of 6-20 characters consisting of numbers + letters + special characters", "reduce": "Reduce", "reset": "Reset", "resubmit": "Resubmit", "rotateLeft": "Rotate Left", "rotateRight": "Rotate Right", "select": {"belongingGroup": "Please select the Device Grouping", "errorTips": {"belongingGroup": "Please select the Device Grouping", "deviceModel": "Please Please select the product model", "productSupportsUpgrade": "Please select a product model that supports upgrade", "type": "Please select the type", "upgradePackageType": "Please select the firmware type", "userRole": "Please select the user role"}, "group": "Please select the group", "label": "Please select the label", "productCanBeUpgraded": "Product Models", "productModel": "Please Please select the product model", "relatedProducts": "Please select related products"}, "selectApplicationEffectiveDate": "Please select the application effective date", "selectDate": "Please select date", "selectPictrue": "Select Picture", "submitApplication": "Submit Application", "submitApplicationTime": "Submit Application Time", "supportedFormat": "Supported Format", "tenantName": "Tenant Name", "theFormatOfTheAttachmentIsIncorrect": "The attachment format is incorrect, please upload again", "title": {"applicationProgress": "Application Progress", "applicationRecord": "Application Record", "bindEmail": "Bind <PERSON><PERSON>", "bindPhone": "Bind Phone", "changeName": "Change Name", "changePassword": "Change Password", "createDeivce": "Create Device", "deleteDevice": "Delete Device", "deleteLabel": "Delete Label", "deviceDetails": "<PERSON>ce Det<PERSON>", "deviceUpgrade": "Device Upgrade", "editUpgradePkg": "Edit Upgrade Package", "extensionApplication": "Extension Application", "handOver": "Hand Over Device", "importTemplateDevice": "Import Template Device", "informationChangeProgress": "Information Change Progress", "newTags": "New Tags", "newUpgradePkg": "New Upgrade Package", "platformNotification": "Platform News", "verifyEmail": "<PERSON><PERSON><PERSON>", "verifyPhone": "Verify Phone"}, "toHandle": "Go to Handle", "triggerValue": "Trigger Value", "underReview": "Under Review", "updatedContent": "Updated Content", "uploadAttachmentsSmallerThan5M": "Please upload files smaller than 5M", "uploadFiles": "Upload Files", "uploadTheFile": "Please upload the file", "userName": "Name", "verificationCode": "Verification Code", "versionMessage": "Version Message", "versionNumber": "Version Number", "versionUpdate": "Version Update", "waitingForSMScodeRefresh": "The current verification has expired, please wait for the countdown to end and resend the verification code", "warningTips": {"pkgFormatIncorrect": "The upgrade package format may be incorrect", "uploadAfterCheck": "Please double-check before uploading"}}, "disabled": "Disabled", "displaySort": "Display Sort", "download": "Download", "downloadUpgradePkg": "Download Upgrade Package", "edit": "Edit", "email": "Email", "enable": "Enable", "endOfTask": "End", "enterTheExperience": "Enter the Experience", "escalationState": "Escalation State", "export": "Export", "exportLog": "Export Log", "factorReport": "Factor Report", "factorReportRecord": "Factor Report Record", "father": "Father", "ffcp": "Four-Faith Cloud Platform", "fileStatus": "File Status", "fileUploadTime": "File Upload Time", "firmwareName": "Firmware Name", "firmwareStatus": "Firmware Status", "firmwareType": "Firmware Type", "founder": "Founder", "goBack": "Go Back", "goOnline": "Go Online", "grouping": "Grouping", "handle": "<PERSON><PERSON>", "implementation": "Implementation", "inProgress": "In Progress", "index": "Index", "input": {"endTime": "End Time", "select": "Select", "startTime": "Start Time", "text": "Enter"}, "inputArea": "Input Area", "installationAddress": "Installation Address", "isEnableZTP": "Is Enable ZTP", "langAndLat": "Lat and Lng", "last7Days": "Last 7 Days", "lastDay": "Last Day", "lastThreeDay": "Last 3 Days", "lastWeek": "Last Week", "latestConfigProgress": "Latest Progress", "latestConfigStatus": "Latest Config Status", "latestUpdateTime": "Latest Update Time", "latitude": "Latitude", "logContent": "Log Content", "logTime": "Log Time", "logType": "Log Type", "login": {"input": {"errorTips": {"phone": "Please enter the phone number", "smsCode": "Please enter the verification code"}, "phone": "Please enter the phone number"}, "pwdLogin": "Password Login"}, "longitude": "Longitude", "mail": "Mail", "menu": "<PERSON><PERSON>", "mobileNumber": "Mobile Number", "mobilePhone": "Phone Number", "more": "More", "name": "Name", "newPwd": "New Password", "newUpgradeTask": "New Upgrade Task", "nextStep": "Next Step", "noCommonlyUsedCommands": "No Commonly Used Commands", "noDeviceData": "No device information", "noDeviceInformation": "No Device Information", "noPagePermission": "No page permission", "notStarted": "Not Started", "notificationMessage": "Notification Message", "notificationTime": "Notification Time", "notificationType": "Notification Type", "offLine": "Offline", "online": "Online", "operate": "Operate", "operator": "Operator", "originalVersion": "Original Version", "owningGroup": "Owning Group", "packageSize": "Package Size", "permission": {"rules1": "The current logged-in user has no permission for this page"}, "pkgSize": "Package Size", "platform": "Platform", "pleaseEnter": "Please enter", "pleaseHandle": "Please Handle", "pleaseSelectDeviceFirst": "Please select the device first", "preview": "Preview", "previousStep": "Previous Step", "primaryAccount": "Primary Account", "processDescription": "Process Description", "processed": "Processed", "processedBy": "Processed By", "processingStatus": "Processing Status", "processingTime": "Processing Time", "productModel": "Product Model", "productModelFirmwareVersion": "Product Model & Firmware Version", "productSeries": "Product Series", "productType": "Product Type", "profileName": "Profile Name", "rawDataAnalysis": "Raw Data Analysis", "rawDataFormat": "Raw Data Format", "reboot": "Reboot", "remark": "Remark", "replace": "Replace", "reportTime": "Report Time", "reset": "Reset", "resetPwd": "Reset Password", "retry": "Retry", "role": "Role", "roleName": "Role Name", "save": "Save", "search": "Search", "searchData": {"deviceIDorName": "Device ID or Name", "disabled": "Disabled", "enable": "Enable", "endOfTask": "End", "fileStatus": "File Status", "inProgress": "In Progress", "notStarted": "Not Started", "productModel": "Product Model", "profileName": "Profile Name", "remark": "Remark", "taskName": "Task Name", "taskStatus": "Upgrade Task Status"}, "selectDevice": "Select Device", "selected": "Selected", "selectedDevice": "Selected Device", "send": "Send", "softwareVersion": "Software Version", "sort": "Sort", "status": "Status", "submit": "Submit", "successfullyDistributed": "Successfully Distributed", "successfullySendCmd": "Successfully Sent Command", "tags": "Tags", "taskCreationTime": "Created Time", "taskEndTime": "End Time", "taskNumber": "Task Number", "taskProgress": "Task Progress", "taskStatus": "Task Status", "tips": "Tips", "to": "To", "today": "Today", "totalDeviceQuantity": "All Device", "triggerValue": "Trigger Value", "type": "Type", "unactivated": "Unactivated", "universalFirmware": "Universal Upgrade Package", "universalfirmware": "Universal Upgrade Package", "untreated": "Untreated", "upgrade": "Upgrade", "upgradeFailure": "Upgrade Failure", "upgradeFirmwareVersion": "Upgrade Firmware Version", "upgradePackageFile": "Upgrade Package File", "upgradePkgFirmwareVersion": "Firmware Version", "upgradeRecord": "Upgrade Record", "upgradeSucceeded": "Upgrade Succeeded", "upgradeTaskName": "Upgrade Task Name", "upgradedVersion": "Upgraded Version", "upgrading": "Upgrading", "userManage": {"text": "User manage allows you to manage user information in a unified manner, including creating, editing, and deleting users. By managing user groups and assigned user roles, administrators can control device data permissions and visual menu permissions of login users.", "title": "User Manage"}, "userName": "User Name", "userPwd": "User Password", "verification": "Verification", "waitingToDownload": "Waiting to Download", "webSide": "Web Side"}, "personal center": "Personal Center", "personalCenter": {"accountName": "Account", "contactEmail": "Contact Email", "createTime": "Creation Time", "personalInformation": "Personal Information", "phoneNumber": "Phone Number", "role": "Role", "tenant": "Tenant", "tenantID": "Tenant ID", "userNickname": "Name"}, "platform": "Platform", "platforms": {"Four Faith Cloud Platform": "Four-Faith Cloud Platform"}, "preview": "Preview", "primary account": "Primary Account", "processing time": "Processing Time", "raw data": "Raw Data", "reason": "Reason", "register": {"enterPwdAgain": "Please enter the password again", "fillLoginName": "Please fill in the login account", "fillLoginNameTips": "The login account cannot start with a number or underscore", "getCode": "Get Verification Code", "loginNameRules": "6-20 characters for the login account, can only contain letters, numbers, and underscores!", "pwdNotSame": "Passwords entered twice do not match", "pwdTips": "Please enter a password of 6-20 characters consisting of numbers, letters, and special characters", "register": "Register"}, "remark": "Remark", "remember the password": "Remember Me", "renewal": "Redo", "replace": "Replace", "request": {"confirmText": "Confirm", "loginError": "Login state error. Please log in again.", "serverError": "Interface request failed. Please try again later."}, "reset": "Reset", "reset passwords": "Reset Passwords", "retrieve password": "Retrieve Password", "retry": "Retry", "revocation": "Revocation", "role": "Role", "route": {"alarmDataStatistics": "Alarm Record", "alarmHandlingStatistics": "Alarm Status", "alarmManage": "Alarm Manage", "alarmNotificationRecord": "Alarm Logs", "cardManager": "Card Manage", "configFileManager": "Config File Manage", "configTask": "Config Task", "dashboard": "Dashboard", "deviceDetails": "<PERSON>ce Det<PERSON>", "deviceList": "Device list", "deviceLog": "<PERSON><PERSON>", "deviceManager": "<PERSON><PERSON>", "groupManage": "Group Manage", "logManage": "Log Manage", "maintenance": "Maintenance", "menuManager": "Menu Manager", "notificationSetting": "Notification Setting", "personalCenter": "Personal center", "roleManager": "Role Manage", "scaffoldTool": "Scaffold Tool", "systemManage": "System Manage", "tenantInformation": "Tenant Information", "thresholdSetting": "<PERSON><PERSON><PERSON><PERSON>", "upgradePacketManager": "Firmware Manage", "upgradeTask": "Upgrade Task", "userLog": "User Log", "userManager": "User Manage", "taskManager": "Task Manager"}, "safety verification": "Safety Verification", "save": "Save", "search": "Query", "see": "View", "selected": "Selected", "start time": "Start Time", "startDate": "StartDate", "status": "Status", "statusCard": {"Access Time": "Access Time", "AddressTest": "Jimei District, Xiamen City, Fujian Province", "Allocation Template": "Allocation Template", "Analysis And Suggestions": "Analysis And Suggestions", "Base Info": "Base Info", "Basic Information": "Basic Information", "Basic Information Normal": "Basic Information Normal", "Basic Normal": "Basic Normal", "CPU Usage": "CPU Usage", "Card Preview": "Card Preview", "Check Progress": "Check Progress", "Check whether the template is enabled": "Check whether the template is enabled", "China Mobile": "China Mobile", "China Telecom": "China Telecom", "China Unicom": "China Unicom", "Clear": "Clear", "Click the Edit button and configure the required parameters under this option": "Click the Edit button and configure the required parameters under this option", "Clone Template": "<PERSON><PERSON> Template", "Config at least one display item": "Config at least one display item", "Confirm whether to disable the template": "Confirm whether to disable the template", "Connected": "Connected", "Connected Devices": "Connected Devices", "Connecting Subdevice": "Connecting Subdevice", "Connection Mode": "Connection Mode", "Connection Platform": "Connection Platform", "Connection Rate": "Connection Rate", "Daily Flow Alarm": "Daily Flow Alarm", "Data Closed": "Data Closed", "Data Enabled": "Data Enabled", "Detection": "Detection", "Detection Over Time Analysis": "The test result cannot be obtained normally. You are advised to test again", "Detection Over Time Tips": "The device timed out and the detection failed\t", "Detection Result": "Detection Result", "Device Alarm Report": "Device Alarm Report", "Device Flow Report": "Device Flow Report", "Device Picture": "Device Picture", "Device Type": "Device Type", "Device Unlink Analysis": "If the device is offline, check whether the power supply is normal", "Device Unlink Tips": "If the device is offline, check whether the power supply is normal.", "Device image loading failed": "Device image loading failed", "Devices": "Devices", "Disabled": "Disabled", "Display Style": "Display Style", "Download Speed": "Download Speed", "Device alarm statistics": "Device Alarm Statistics", "Exception": "Exception", "Firmware Version": "Firmware Version", "Free": "Free", "Gateway": "Gateway", "Gateway device": "Gateway device", "Gateway sub-device": "Gateway sub-device", "Gentle Reminder": "Gen<PERSON> Reminder", "Honeycomb": "Honeycomb", "IP Address": "IP Address", "IP Conflict": "The ip address of the connected sub-device conflicts. You are advised to change the ip address of the sub-device", "Installation Map": "Installation Map", "Intelligent Detection": "Intelligent Detection", "Intelligent detection": "Intelligent Detection", "LAN": "LAN", "LAN Edit": "LAN Edit", "Last SevenDays": "Last 7 Days", "MAC Address": "MAC Address", "Memory Usage": "Memory Usage", "Monthly Flow Alarm": "Monthly Flow Alarm", "More": "More", "More Info": "More Info", "Network": "Network", "Network Standard": "Network Standard", "No device picture is available": "No device picture is available", "No more cards available": "No more cards available", "Normal": "Normal", "Normal status": "Normal status", "Not Open": "Unopened", "Number of Connected Devices": "Number of Connected Devices", "Number of LAN Ports": "Number of LAN Ports", "Number of SIM Card Slots": "Number of SIM Card Slots", "Number of SIM Cards": "Number of SIM Cards", "Number of WAN Ports": "Number of WAN Ports", "Open Status": "On State", "Opening": "Turned On", "Operator": "Operator", "PDF Report Download": "PDF Report Downloa", "Password": "Password", "Please enter the number of LAN ports": "Please enter the number of LAN ports", "Please enter the number of SIM cards": "Please enter the number of SIM cards", "Please enter the number of WAN ports": "Please enter the number of WAN ports", "Please select at least one detection item": "Please select at least one test item", "Please select at least one test item": "Please select at least one test item", "Please select the options you want to display": "Please select the options you want to display", "Poor": "Difference", "Port Condition": "Port Status", "Product Selection": "Product Selection", "Real Time Downlink Rate": "Real Time Downlink Rate", "Real Time Uplink Rate": "Real Time Uplink Rate", "Recognized Card": "Card Recognized", "Response Timeout": "Response Timeout", "Running Time": "Running Time", "SIM Card": "SIM Card", "SIM Card Edit": "SIM Card Edit", "SIM Card Status": "Card Status", "SIM Status": "SIM Status", "Select Card": "Select Card", "Signal strength too low": "Signal strength too low", "Signals": "Signal Strength", "Sim Error TIps": "You are advised to check whether the antenna is correctly connected. Antenna position as far as possible in the unenclosed environment, the signal is weak can adjust the antenna position; If the adjustment is still too low, determine whether the carrier signal coverage problem, the verification method can be changed card comparison test", "Small Program": "Small Program", "Small Program Template Edit": "Small Program Template Edit", "Software Version": "Software Version", "State Inspection": "Base Info", "Subclose Mask": "Subclose Mask", "Subnet Mask": "Subnet Mask", "Template View": "Template View", "Test Item": "Detection Items", "The card was not recognized": "The card was not recognized", "The current template is empty": "The current template is empty", "The port is properly connected": "The port is properly connected", "Today": "Today", "Turn On": "Turn On", "Unconnected": "Unconnected", "Undo": "Undo", "Undo Prompt": "Retention of up to twenty records can be revoked, the current irreversible operation records!", "Unknown Carrier": "Unknown Carrier", "Unrecognized": "Unrecognized", "Upload Speed": "Upload Speed", "Very Good": "Very Good", "WAN": "WAN", "WAN 1": "WAN 1", "WAN Port Config": "WAN Port Config", "WEB": "WEB", "WIFI Link": "WIFI Link", "Way Encryption": "Encryption", "Wired Device": "Wired <PERSON>ce", "Wireless": "Wireless", "Wireless Connection Edit": "Wireless Connection Edit", "Wireless Devices": "Wireless Devices", "Wireless connection normal": "Wireless connection normal", "Work": "Work", "reconnectionErrMsg": "The current network is abnormal. Please try again later", "websocket Error": "The service is abnormal. Please try again later"}, "strip": "item", "submit": "Submit", "successfully delete": "Delete Successfully!", "systemManager": {"roleManager": {"promptContent": {"text": "Role manage allows administrators to assign user permissions. Create roles, assign menu and data access rights, and assign roles to users for proper access control.", "title": "Role manage"}, "search": {"disable": "Disable", "normal": "Normal", "roleName": "Role Name", "status": "Status"}}, "structrueManager": {"basicInfo": "Basic Information", "device": "<PERSON><PERSON>", "dialog": {"actionTip": {"sureToDelete": "Please confirm whether to delete this group"}, "enterGroupName": "Please enter group name", "fillTheDescription": "Please fill in the description", "fillTheGroupName": "Please fill in the group name", "groupName": "Group Name", "parentGroupName": "Parent Group Name"}, "founder": "Founder", "groupManager": {"text": "Group manage is a hierarchical structure that centrally manages users and devices. Sub-groups are created under groups and bound to different users and devices to control data rights of different users.", "title": "Group Manage"}, "groupName": "Group Name", "groupNotes": "Group Notes", "relatesInformation": "Related Information", "users": "Users"}, "userManager": {"accountName": "Account", "accountNameTip": "Please enter the account", "accountNameTip1": "Please fill in the account", "accountType": "Account Type", "belongingGroup": "Device Grouping", "createRole": "Click to create associated roles", "disabled": "Disabled", "endDate": "End Date", "inputUserName": "Please enter the username", "mobilePhoneTip": "Please enter the mobile phone number", "normal": "Normal", "pwdTip": "Please fill in the user password", "resetPwd": "Reset Password", "roleTip": "Please select the user role", "startDate": "Start Date", "status": "Status", "username": "Username", "usernameTip1": "Please fill in the username"}}, "tags": "Tags", "tenantInformation": {"Certified": "Certified", "Contacts": "Contacts", "ITIndustry": "Information Industry", "academicResearch": "Academic Research", "agricultureForestryAnimalHusbandryAndFishery": "Agriculture, Forestry, Animal Husbandry, and Fishery", "alertTips": "Tenant name/contact person has been modified. Please re-upload the authorization file! The modified information will take effect after approval.", "application": "Application", "applicationTime": "Application Time", "applicationValidityPeriod": "Apply for Extension of Validity Period", "applyForExtension": "Apply for Extension", "applyFormalAccount": "Apply for Formal Account", "authenticateNow": "Authenticate Now", "authenticationMethod": "Authentication Method", "authenticationTip": "Upload the formal certification application letter of the enterprise, photo of the contact person holding the ID card, and the business license of the enterprise. We will review the information you uploaded, and upon approval, certification will be granted.", "bindEmail": "Please bind your email", "bindPhoneNumber": "Contact Number", "buildingMaterials": "Building Materials", "certificationRights": "Certification Rights", "certified": "Certified", "checkProgress": "Check Information Change Approval Progress", "clickUpload": "Click to Upload", "client": "Client", "clothingTextile": "Clothing and Textile", "contactEmail": "Contact Email", "contactNumber": "Contact Phone Number", "contacts": "Contacts", "currentLimit10Files": "Currently limited to selecting 10 files", "dragFilesHere": "Drag files here", "editInformation": "Edit Information", "educationAndTraining": "Education and Training", "energyDeviceAndServices": "Energy Devices and Services", "enterContact": "Enter Contact", "enterCorrectEmailAddress": "Enter a correct email address", "enterCorrectPhoneNumber": "Enter a correct phone number", "enterEmailAddress": "Enter Email Address", "enterTenantName": "Enter Tenant Name", "enterYourPhone": "Enter Your Phone Number", "environmentalProtectionAndGreening": "Environmental Protection and Greening", "feedbackTip": "If you encounter any problems during the formal certification or extension application, please contact the operation personnel for consultation.", "file": "File", "financialIndustry": "Financial Industry", "formal": "Formal", "formalCertification": "Formal Certification", "governmentOrganizations": "Government Organizations", "inOrderToAvoid": "To avoid the impact on yourself in case the application is not approved, users should consider applying according to the current account validity period and needs.", "industry": "Industry", "language": "Language", "loginAddress": "Login Address", "machineryManufacturing": "Machinery Manufacturing", "mailbox": "Mailbox", "medicalAndHealth": "Medical and Health", "metallurgicalMinerals": "Metallurgical Minerals", "numberOfApplications": "Number of Applications", "officialAccountsTip": "Formal accounts will provide more functional experiences on the platform and exclusive benefits for formal accounts.", "oilAndGasAndConsumerFuels": "Oil and Gas, Consumer Fuels", "onTrial": "On Trial", "otherIndustries": "Other Industries", "personal": "Personal", "powerIndustry": "Power Industry", "province": "Province", "remainTime": "Remaining time until the end of the trial period", "reviewFailed": "Review Failed", "safetyProtection": "Safety Protection", "selectIndustryOfCompany": "Select the industry of the company", "selectProvinceOfCompany": "Select the province of the company", "supportedFormat": "Supported Format", "tenant": "Tenant", "tenantID": "Tenant ID", "tenantInformation": "Tenant Information", "tenantName": "Tenant Name", "tenantType": "Select Tenant Type", "theFormatOfTheAttachmentIsIncorrect": "The format of the attachment is incorrect. Please re-upload!", "toExtend": "To Extend", "tourismAndEntertainment": "Tourism and Entertainment", "transportation": "Transportation", "underReview": "Under Review", "uploadAttachmentsSmallerThan5M": "Upload attachments smaller than 5MB", "uploadFailedDeleteUploadAgain": "Image upload failed. Please delete and upload again.", "uploadFiles": "Upload Files", "uploadTheFile": "Upload the File", "userInitiates": "Users initiate an extension application for the trial period. We will review your application, and upon approval, the platform usage will be extended.", "validPeriod": "Valid Period", "viewApplicationHistory": "View Application History", "waterConservancyIndustry": "Water Conservancy Industry", "wiredTo": "Valid period until", "youAra": "You are"}, "test_i18n": "Internationalization Test", "tips": "Tips", "type": "Type", "unactivated": "Unactivated", "undelete": "Undelete", "unfold": "Expand", "upgrade": "Upgrade", "upgrade time": "Upgrade time", "upgrading": "Upgrading", "uptateTime": "UptateTime", "user password": "Password", "user role": "Role", "users": "Users", "valid period": "Valid Period", "yes": "Yes", "Resume": "Resume", "Pause": "Pause", "sceneDetails": "Scene details", "Please enter search keywords": "Please enter search keywords"}