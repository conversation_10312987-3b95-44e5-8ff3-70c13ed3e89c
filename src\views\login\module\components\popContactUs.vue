<template>
  <el-dialog
    :title="$t('page.contactUs')"
    v-model="dialogVisible"
    v-if="dialogVisible"
    width="850px"
    :append-to-body="true"
    :close-on-click-modal="false"
    :before-close="dialogCancel"
  >
    <el-form
      ref="formRef"
      :disabled="submitLoading"
      :model="formData"
      label-width="auto"
      :rules="rules"
    >
      <el-form-item :label="$t('page.contactName')" prop="linkMan">
        <el-input
          v-model="formData.linkMan"
          :placeholder="$t('page.dialog.input.input')"
          :maxlength="20"
          show-word-limit
        />
      </el-form-item>
      <el-form-item :label="$t('page.contactPhone')" prop="linkPhone">
        <el-input
          v-model="formData.linkPhone"
          :placeholder="$t('page.dialog.input.input')"
          :maxlength="11"
          show-word-limit
        />
      </el-form-item>
      <el-form-item :label="$t('page.email')" prop="linkEmail">
        <el-input
          v-model="formData.linkEmail"
          :placeholder="$t('page.dialog.input.input')"
          :maxlength="200"
          show-word-limit
        />
      </el-form-item>
      <el-form-item
        :label="$t('page.descriptionOfRequirement')"
        prop="consultationMsg"
      >
        <el-input
          v-model="formData.consultationMsg"
          type="textarea"
          :rows="3"
          :maxlength="1000"
          show-word-limit
          :placeholder="$t('page.dialog.input.input')"
        />
      </el-form-item>
    </el-form>
    <div class="contact-box text-xl font-bold">
      <i-ep-phone class="mr-2" />{{ $t('page.contactUs') }} :
      <span>{{ formData.productPhone }}</span>
    </div>
    <template #footer>
      <div>
        <el-button type="primary" :loading="submitLoading" @click="save">
          {{ $t('page.confirm') }}
        </el-button>
        <el-button @click="dialogCancel">{{ $t('page.Cancel') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'PopContactUs'
};
</script>
<script setup>
import { useI18n } from 'vue-i18n';

import i18n from '@/lang';
import useDialog from '@/hooks/useDialog';
import { addConsultation } from '@/api/tenantApp';
import { ElMessage } from 'element-plus';

const formRef = ref('formRef');

const { dialogClose, dialogOpen, dialogVisible, dialogCancel, submitLoading } =
  useDialog(formRef);
const formData = ref({
  linkMan: '',
  linkPhone: '',
  linkEmail: '',
  consultationMsg: ''
});
const { locale } = useI18n();

const rules = ref({
  linkMan: [
    {
      required: true,
      message: i18n.global.t('page.dialog.input.contactName'),
      trigger: ['change', 'blur']
    }
  ],
  linkPhone: [
    {
      required: locale.value === 'zh',
      trigger: ['change', 'blur'],
      message: i18n.global.t('page.dialog.input.contactPhone')
    }
  ],
  linkEmail: [
    {
      required: locale.value === 'en',
      message: i18n.global.t('page.dialog.input.contactEmail'),
      trigger: ['change', 'blur']
    },
    {
      type: 'email',
      message: i18n.global.t('page.dialog.input.rightEmail'),
      trigger: ['change', 'blur']
    }
  ],
  consultationMsg: [
    {
      required: true,
      message: i18n.global.t('page.dialog.input.descriptionOfRequirement'),
      trigger: ['change', 'blur']
    }
  ]
});

const handleOpen = data => {
  nextTick(() => {
    formData.value = Object.assign(
      {
        linkMan: '', // 联系人
        linkPhone: '', // 联系电话
        linkEmail: '', // 电子邮箱
        consultationMsg: '' // 需求描述
      },
      {
        applicationId: data.applicationId,
        productPhone: data.productPhone
      }
    );
  });

  dialogOpen();
};

const save = async () => {
  await formRef.value.validate(async valid => {
    if (valid) {
      submitLoading.value = true;
      try {
        await addConsultation(formData.value);
        submitLoading.value = false;

        ElMessage.success(
          i18n.global.t('page.dialog.actionFb.successfullySave')
        );
        dialogClose();
      } catch (err) {
        submitLoading.value = false;
        console.log(err);
      }
    }
  });
};

defineExpose({ handleOpen });
</script>
