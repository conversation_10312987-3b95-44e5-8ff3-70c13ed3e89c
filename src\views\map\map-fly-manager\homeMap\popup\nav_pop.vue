<template>
  <div class="pop-wrapper">
    <div class="pw-title">
      <el-row>
        <el-col :span="2" class="nick">
          <svg-icon icon-class="drone" style="width: 14px; height: 14px; margin-left: 5px; margin-top: 12px" />
        </el-col>
        <el-col :span="16" class="nick"> {{ dockInfo.device_nickname }}</el-col>
        <!-- <el-col :span="6"></el-col> -->
        <el-col :span="6">
          <div class="status" :class="{ 'is-active': osdInfo.mode_code === 14 }">
            {{ getEnumKey(EModeCode, osdInfo.mode_code) }}
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="pw-content">
      <el-row class="c-row">
        <el-col :span="6" class="c-title">电量</el-col>
        <el-col :span="6" class="c-text">{{ osdInfo.capacity_percent + ' %' }} </el-col>
        <el-col :span="6" class="c-title">绝对高度</el-col>
        <el-col :span="6" class="c-text">{{ osdInfo.height + ' m' }}</el-col>
      </el-row>
      <el-row class="c-row">
        <el-col :span="6" class="c-title">水平速度</el-col>
        <el-col :span="6" class="c-text">{{ osdInfo.horizontal_speed + ' m/s' }}</el-col>
        <el-col :span="6" class="c-title">卫星数量</el-col>
        <el-col :span="6" class="c-text">{{ osdInfo.gps_number }}</el-col>
      </el-row>
      <el-row class="c-row">
        <el-col :span="6" class="c-title">RTK</el-col>
        <el-col :span="6" class="c-text">{{ osdInfo.rtk_number }}</el-col>
        <el-col :span="6" class="c-title">距离返航点</el-col>
        <el-col :span="6" class="c-text">{{ osdInfo.home_distance + ' m' }}</el-col>
      </el-row>
      <div class="moreInfo" @click="moreInfo()" :class="{ 'is-active': osdInfo.mode_code === 14 }">查看详情</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'navPop'
};
</script>
<script setup>
import { onMounted, onUnmounted, defineExpose, reactive, ref, toRefs } from 'vue';
import { EModeCode } from '@/views/map/map-fly-manager/components/osdInfo';
import router from '@/router';

let dockInfo = reactive({
  nickname: '', //机场名
  dock_sn: '', //机场SN号
  dock_xyz: '', //机场XYZ
  device_nickname: '', //无人机名
  device_sn: '', //无人机SN号
  device_xyz: '', //无人机XYZ
  rc_sn: '', //遥控器SN号
  rc_nickname: '' //遥控器名
  // alarm_id: '', //警情ID
  // flight_id: '', //飞行任务ID
  // wayline_id: '' //航线文件ID
});

let osdInfo = reactive({
  mode_code: 14, // 状态
  capacity_percent: '--', //电量
  height: '--', //飞行高度
  horizontal_speed: '--', // 水平速度
  gps_number: '--', //GPS数量
  rtk_number: '--', //RTK数量
  home_distance: '--' // 离机场距离
});

// 枚举获取值
const getEnumKey = (enumObject, value) => {
  return Object.keys(enumObject).find(key => enumObject[key] === value);
};

// 跳转实时飞行页面
const moreInfo = () => {
  if (dockInfo.dock_sn === '') {
    ElMessage.warning('该无人机处于离线状态');
    return;
  }
  if (dockInfo.rc_sn !== '') {
    // 跳转飞手无飞机的实时飞行页面
    router.push({
      path: '/pilot-real-time-flight',
      query: {
        device_sn: dockInfo.device_sn, //飞机序列号
        device_nickname: dockInfo.device_nickname //无人机名
      }
    });
  } else {
    // 跳转机场有携带飞机的实时飞行页面
    router.push({
      path: '/real-time-flight',
      query: {
        dock_sn: dockInfo.dock_sn, //机场序列号
        nickname: dockInfo.nickname, //机场名
        device_sn: dockInfo.device_sn, //飞机序列号
        device_nickname: dockInfo.device_nickname, //无人机名
        dock_xyz: dockInfo.dock_xyz, // 经纬度
        lon: dockInfo.dock_xyz.x, // 经纬度
        lat: dockInfo.dock_xyz.y, // 经纬度
        type: dockInfo.type
      }
    });
  }
};
/**
 * 设置组件数据
 * @param {*} options {navInfo:{},osdInfo:{}}
 */
const setComponentData = options => {
  if (options && typeof options === 'object') {
    const nowNavInfo = options.navInfo;
    for (const key in dockInfo) {
      if (nowNavInfo.hasOwnProperty(key)) {
        dockInfo[key] = nowNavInfo[key];
      }
    }
    const str = '--';
    const nowOsdInfo = options.osdInfo;
    if (nowOsdInfo !== null && nowOsdInfo !== undefined) {
      osdInfo.mode_code = nowOsdInfo?.mode_code;
      osdInfo.capacity_percent = nowOsdInfo?.battery.capacity_percent ?? str;
      osdInfo.height = nowOsdInfo?.height.toFixed(2) ?? str;
      osdInfo.horizontal_speed = nowOsdInfo?.horizontal_speed.toFixed(2) ?? str;
      osdInfo.gps_number = nowOsdInfo?.position_state.gps_number ?? str;
      osdInfo.rtk_number = nowOsdInfo?.position_state.rtk_number ?? str;
      osdInfo.home_distance = nowOsdInfo?.home_distance.toFixed(2) ?? str;
    }
  }
};
// 对外抛出方法
defineExpose({
  setComponentData
});

onMounted(() => {});
onUnmounted(() => {});
onBeforeUnmount(() => {});
</script>

<style scoped lang="scss">
.pop-wrapper {
  position: absolute;
  width: 300px;
  height: 170px;
  background: #001129;
  user-select: none;
  z-index: 2;
  font-family: SourceHanSansSC-Bold;
}
.pw-title {
  width: 300px;
  height: 38px;
  line-height: 38px;
  background-color: #11253e;
  font-family: SourceHanSansSC-Regular;
  .nick {
    font-size: 14px;
    color: #f5f6f8;
    text-align: left;
    font-weight: 400;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .status {
    margin-top: 7px;
    background: rgba(42, 139, 125, 0.5);
    border-radius: 2px;
    width: 68px;
    height: 24px;
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;
    color: #39bfa4;
    text-align: center;
    line-height: 22px;
    font-weight: 400;
    &.is-active {
      color: #98a2b3;
      background: rgba(44, 62, 86, 0.5);
    }
  }
}
.pw-content {
  width: 300px;
  height: 132px;
  .c-row {
    font-size: 14px;
    color: #e4e7ec;
    line-height: 22px;
    font-weight: 500;
    padding: 5px;
  }
  .c-title {
    text-align: center;
    font-family: SourceHanSansSC-Bold;
    font-size: 14px;
    color: #E4E7EC;
    // text-align: right;
    line-height: 22px;
    font-weight: 700;
  }
  .c-text {
    text-align: center;
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;
    color: #E4E7EC;
    // text-align: left;
    line-height: 22px;
    font-weight: 400;
  }
  .moreInfo {
    width: 272px;
    height: 32px;
    border-radius: 2px;
    color: #fff;
    background-color: #2e90fa;
    margin-left: 14px;
    text-align: center;
    font-size: 14px;
    line-height: 32px;
    &.is-active {
      color: #98a2b3;
      background: rgba(44, 62, 86, 0.5);
    }
  }
}
</style>
