<template>
  <div class="palyer-container">
    <div class="player" :id="playerId"></div>

    <div class="title-name">{{ dronePara.cameraName || '无视频' }}</div>
    <div class="close-icon" v-if="dronePara.droneSelected" @click="closeVideo">
      <i-ep-close />
    </div>
  </div>
</template>

<script setup>
import AgoraRTC, {
  IAgoraRTCClient,
  IAgoraRTCRemoteUser
} from 'agora-rtc-sdk-ng';
import { onMounted, onUnmounted, reactive, watch } from 'vue';
import optionData from '@/utils/option-data';
import { getLiveCapacity, startLivestream, stopLivestream } from '@/api/live';

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  playerId: {
    type: String,
    default: ''
  }
});
let agoraClient = {};
const agoraPara = reactive({
  appid: import.meta.env.VITE_AGORA_APPID,
  token: import.meta.env.VITE_AGORA_TOKEN,
  channel: import.meta.env.VITE_AGORA_CHANNEL,
  uid: 123456,
  stream: {}
});

const livePara = reactive({
  url: '',
  webrtc: {},
  videoId: '',
  liveState: false
});
const dronePara = reactive({
  // droneSelected: '6QCDL5L0000086', //设备sn
  // cameraSelected: '165-0-7', // 摄像的index值
  // cameraName:'',// 摄像头名称
  // videoSelected: 'normal-0', // 选择当前摄像头的第一个视频index
  // claritySelected: optionData.clarityList[0].value,
  // lensSelected: 'normal',
  // isDockLive: true // videos_list的switch_video_types数量>0则为true
});
const nonSwitchable = 'normal';

onMounted(() => {
  initData();
});
function initData(data) {
  console.log('111111初始化', agoraPara, dronePara);
  // return;

  if (!props.playerId) {
    return;
  }
  agoraClient = AgoraRTC.createClient({ mode: 'live', codec: 'vp8' });
  agoraClient.setClientRole('audience', { level: 2 });
  if (agoraClient.connectionState === 'DISCONNECTED') {
    agoraClient.join(agoraPara.appid, agoraPara.channel, agoraPara.token)
  }

  agoraClient.on('user-joined', async user => {
    console.log('user[' + user.uid + '] join');
    // onStart(data);
  });
  // mediaType : 'audio' | 'video'
  agoraClient.on('user-published', async (user, mediaType) => {
    await agoraClient.subscribe(user, mediaType);
    if (mediaType === 'video' && dronePara.droneSelected) {
      console.log('subscribe success');
      // Get `RemoteVideoTrack` in the `user` object.
      const remoteVideoTrack = user?.videoTrack;
      // Dynamically create a container in the form of a DIV element for playing the remote video track.
      remoteVideoTrack.play(document.getElementById(props.playerId));
    }
  });
  agoraClient.on('user-unpublished', async user => {
    // 停止播放后离开频道

    // ElMessage.info('unpublish live');
    window.$bus.emit('liveStreamClose',{...dronePara});
    agoraClient.unsubscribe(user);
    clearDronePara();

  });
  agoraClient.on('exception', async e => {
    console.log(e);
    ElMessage.error(e.msg);
  });
}
function clearDronePara() {
  dronePara.droneSelected = null;
  dronePara.cameraSelected = null;
  dronePara.cameraName = null;
  dronePara.cameraId = null;
  dronePara.videoSelected = null;
  dronePara.claritySelected = optionData.clarityList[0].value;
  dronePara.lensSelected = null;
  dronePara.isDockLive = false;
}

async function onStart(data) {
  Object.assign(dronePara, data);
  console.log('111111开始', dronePara);

  const timestamp = new Date().getTime().toString();
  const liveTimestamp = timestamp;
  if (
    data?.droneSelected == null ||
    data?.cameraSelected == null ||
    data?.claritySelected == null
  ) {
   console.log("waring: not select live para!!!",)
    return;
  }
  // agoraClient.setClientRole('audience', { level: 2 });
  // if (agoraClient.connectionState === 'DISCONNECTED') {
  //   await agoraClient.join(agoraPara.appid, agoraPara.channel, agoraPara.token);
  // }
  livePara.videoId =
    data?.droneSelected +
    '/' +
    data?.cameraSelected +
    '/' +
    (data?.videoSelected || nonSwitchable + '-0');

  livePara.url =
    'channel=' +
    agoraPara.channel +
    '&sn=' +
    data?.droneSelected +
    '&token=' +
    encodeURIComponent(agoraPara.token) +
    '&uid=' +
    agoraPara.uid;
  console.log('11111111', data);

  startLivestream({
    url: livePara.url,
    video_id: livePara.videoId,
    url_type: 0,
    video_quality: data?.claritySelected
  })
    .then(res => {
      if (res.code !== 0) {
        return;
      }
      livePara.liveState = true;
    })
    .catch(err => {
      console.error(err);
    });
}
const emit = defineEmits(['onClose']);

function closeVideo() {
  onStop();
}
async function onStop() {
  console.log('111111停止', dronePara);

  if (
    dronePara?.droneSelected == null ||
    dronePara?.cameraSelected == null ||
    dronePara?.claritySelected == null
  ) {
   console.log("waring: not select live para!!!",)

    return;
  }
  livePara.videoId =
    dronePara?.droneSelected +
    '/' +
    dronePara?.cameraSelected +
    '/' +
    (dronePara?.videoSelected || nonSwitchable + '-0');

  stopLivestream({
    video_id: livePara.videoId
  }).then(res => {
    if (res.code === 0) {
      ElMessage.success(res.message);
      livePara.liveState = false;

      console.log('stop play livestream', dronePara);
    }
  });
}
onUnmounted(() => {
  onStop();
});

defineExpose({
  initData,
  onStart,
  onStop
});
</script>

<style scoped lang="scss">
.palyer-container {
  position: relative;
  background-color: #262c33;
  height: 100%;
  border: 1px solid #101010;
  .player {
    width: 100%;
    height: 100%;
  }

  .title-name {
    position: absolute;
    color: white;
    font-size: 14px;
    top: 10px;
    left: 10px;
  }
  .close-icon {
    background-color: #262c33;
    position: absolute;
    color: white;
    font-size: 14px;
    top: 10px;
    right: 10px;
    cursor: pointer;
    border-radius: 50%;
    width: 22px;
    height: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
