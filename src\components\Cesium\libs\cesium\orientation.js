import * as Cesium from 'cesium';

/**
 * 获取向量AB的heading（朝向） getHeading
 * https://blog.csdn.net/u010447508/article/details/105562542
 * @param {*} pointA  Cesium.Cartesian3
 * @param {*} pointB  Cesium.Cartesian3
 * @returns number
 */
export function getHeading(pointA, pointB) {
  //建立以点A为原点，X轴为east,Y轴为north,Z轴朝上的坐标系
  const transform = Cesium.Transforms.eastNorthUpToFixedFrame(pointA);
  //向量AB
  const positionvector = Cesium.Cartesian3.subtract(pointB, pointA, new Cesium.Cartesian3());
  //因transform是将A为原点的eastNorthUp坐标系中的点转换到世界坐标系的矩阵
  //AB为世界坐标中的向量
  //因此将AB向量转换为A原点坐标系中的向量，需乘以transform的逆矩阵。
  const vector = Cesium.Matrix4.multiplyByPointAsVector(
    Cesium.Matrix4.inverse(transform, new Cesium.Matrix4()),
    positionvector,
    new Cesium.Cartesian3()
  );
  //归一化
  const direction = Cesium.Cartesian3.normalize(vector, new Cesium.Cartesian3());
  //heading
  const heading = Math.atan2(direction.y, direction.x) - Cesium.Math.PI_OVER_TWO;
  return Cesium.Math.TWO_PI - Cesium.Math.zeroToTwoPi(heading);
}

export function getHeading2(pointA, pointB) {
  //建立以点A为原点，X轴为east,Y轴为north,Z轴朝上的坐标系
  const transform = Cesium.Transforms.eastNorthUpToFixedFrame(pointA);
  //向量AB
  const positionvector = Cesium.Cartesian3.subtract(pointB, pointA, new Cesium.Cartesian3());
  //因transform是将A为原点的eastNorthUp坐标系中的点转换到世界坐标系的矩阵
  //AB为世界坐标中的向量
  //因此将AB向量转换为A原点坐标系中的向量，需乘以transform的逆矩阵。
  const vector = Cesium.Matrix4.multiplyByPointAsVector(
    Cesium.Matrix4.inverse(transform, new Cesium.Matrix4()),
    positionvector,
    new Cesium.Cartesian3()
  );
  //归一化
  const direction = Cesium.Cartesian3.normalize(vector, new Cesium.Cartesian3());
  //heading
  const heading = Math.atan2(direction.y, direction.x) - Cesium.Math.PI_OVER_TWO;
  return Cesium.Math.TWO_PI - Cesium.Math.zeroToTwoPi(heading);
}
/**
 * 获取向量AB的heading（朝向） getPitch
 * https://blog.csdn.net/u010447508/article/details/105562542
 * @param {*} pointA  Cesium.Cartesian3
 * @param {*} pointB  Cesium.Cartesian3
 * @returns number
 */
export function getPitch(pointA, pointB) {
  let transfrom = Cesium.Transforms.eastNorthUpToFixedFrame(pointA);
  const vector = Cesium.Cartesian3.subtract(pointB, pointA, new Cesium.Cartesian3());
  let direction = Cesium.Matrix4.multiplyByPointAsVector(Cesium.Matrix4.inverse(transfrom, transfrom), vector, vector);
  Cesium.Cartesian3.normalize(direction, direction);
  //因为direction已归一化，斜边长度等于1，所以余弦函数等于direction.z
  return Cesium.Math.PI_OVER_TWO - Cesium.Math.acosClamped(direction.z);
}

/**
 * 获取向量AB的Roll（旋转） getRoll
 * @param {*} pointA  Cesium.Cartesian3
 * @param {*} pointB  Cesium.Cartesian3
 * @returns number
 */
export function getRoll(pointA, pointB) {
  // Transform the vector AB into the ENU coordinate system at pointA
  const transform = Cesium.Transforms.eastNorthUpToFixedFrame(pointA);
  const vector = Cesium.Cartesian3.subtract(pointB, pointA, new Cesium.Cartesian3());
  const vectorENU = Cesium.Matrix4.multiplyByPointAsVector(
    Cesium.Matrix4.inverse(transform, new Cesium.Matrix4()),
    vector,
    new Cesium.Cartesian3()
  );

  // Normalize the vector
  const direction = Cesium.Cartesian3.normalize(vectorENU, new Cesium.Cartesian3());

  // Calculate the roll angle
  // For a unit vector in ENU, the roll is the angle between the vector and the East (X-axis)
  // We use the dot product between the vector and the East unit vector (1, 0, 0) in ENU
  // Since the vector is normalized, we only need the x component to find the roll
  // The roll is the angle between the vector and the plane defined by the East vector and the vector itself
  const eastUnitVector = new Cesium.Cartesian3(1, 0, 0); // East unit vector in ENU
  const dotProduct = Cesium.Cartesian3.dot(eastUnitVector, direction);
  const roll = Math.asin(-dotProduct);
  return Cesium.Math.TWO_PI - Cesium.Math.zeroToTwoPi(roll);
}
