<!-- 饼图 -->
<template>
  <div class="pd16">
    <div :id="id" :class="className" :style="{ height, width }" />
  </div>
</template>

<script setup>
import * as echarts from 'echarts';
import { getFlyNum } from '@/api/dashboard';

const uavInfo = ref({});
const props = defineProps({
  id: {
    type: String,
    default: 'pieChart'
  },
  className: {
    type: String,
    default: ''
  },
  width: {
    type: String,
    default: '200px',
    required: true
  },
  height: {
    type: String,
    default: '200px',
    required: true
  }
});
const options = {
  legend: {
    top: 10,
    type: 'scroll',
    orient: 'horizontal',
    icon: 'circle',
    itemWidth: 6,
    itemHeight: 6,
    itemGap: 8,
    textStyle: {
      color: '#8C8C8C'
    }
  },
  //中央自定义文字
  title: {
    text: '1343',
    left: 'center',
    top: '46%',
    textStyle: {
      color: '#fff',
      fontSize: 16,
      align: 'center',
      fontWeight: '700'
    }
  },
  //提示框
  tooltip: {
    trigger: 'item',
    backgroundColor: '#fff',
    textStyle: {
      // 提示框浮层的文本样式。
      color: 'rgba(0,0,0,0.45)',
      fontSize: 14
    },
    extraCssText: 'box-shadow: 0 2px 8px 0 rgba(0,0,0,0.15);', // 额外附加到浮层的 css 样式
    formatter: item => {
      let txtCon = `<div style="display:flex;align-items:center;padding:0px 12px"><span style="color: rgba(0,0,0,0.65);">${item.value}</span>
            <span style="background-color:${item.color};width:6px;height:6px;border-radius:50%;display:inline-block;margin:0 8px 0 15px"></span>
            <span style="color: rgba(0,0,0,0.45);">${item.name}</span></div>`;
      return txtCon;
    }
  },
  series: [
    {
      name: '飞行次数',
      type: 'pie',
      radius: ['50%', '65%'],
      center: ['50%', '50%'],
      avoidLabelOverlap: false,
      label: {
        show: false
      },
      itemStyle: {
        borderRadius: 1,
        color: function (params) {
          //自定义颜色
          const colorList = ['#2E90FA', '#FDB022', '#68CEBA', '#F97066'];
          return colorList[params.dataIndex];
        }
      },
      data: []
    }
  ]
};

//获取各机型飞行次数
async function getUavNum() {
  //设为异步函数 数据整理完以后再渲染echart
  try {
    const res = await getFlyNum();
    uavInfo.value = res.map(key => {
      return { name: key.device_type , value: key.flight_count };
    });
    options.series[0].data = uavInfo.value ? uavInfo.value : [];
    options.title.text = uavInfo.value.reduce((sum, item) => sum + item.value, 0);
  } catch (error) {
    options.series[0].data = [];
    options.title.text = 0;
  }
  const chart = echarts.init(document.getElementById(props.id));
  chart.setOption(options);
  // 监听图例点击事件
  chart.on('legendselectchanged', function (params) {
    console.log(params); // 输出点击事件的参数信息，可以根据需要处理逻辑
    const selected = params.selected;
    let total = 0;
    // 计算选中图例的总和
    for (let name in selected) {
      if (selected[name]) {
        const data = options.series[0].data.find(item => item.name === name);
        if (data) {
          total += data.value;
        }
      }
    }
    options.title.text = total;
    chart.setOption(options);
  });
  // window.addEventListener('resize', () => {
  //   chart.resize();
  // });
}

onMounted(() => {
  getUavNum();
});
</script>
<style scoped>
.pd16 {
  padding: 16px;
}
</style>
