import * as Cesium from 'cesium';
import {
  addPropertiesToEntity,
  getPropertyByKey,
  calculatePointFromCenter,
  toCartesian3,
  toNumber,
  toDegrees,
  px2Catesian3,
  arrayToCartesian3,
  c3ArrToDegress,
  calculatArea,
  calculatPerimeter,
  getPointsFromMap
} from '../common';
import { generateKey } from '@/utils';
import { getCenter } from '../turfUtils';
import { toRaw } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { dialogDataRect } from '@/views/flight-manage/flight-area/flightAreaHandle';

//#region 绘制部分
let viewer = null;
let handler = null;
let drawResultObjectGlobal = null;
export let isPolygonCreate = ref(false);
const endpointIcon = new URL('@/assets/plan/wrj/endpoint.png', import.meta.url).href;
/**
 * 绘制圆
 * @param {*} viewer viewer 对象
 * @param {*} CallBack 完成后的回调
 */
export const drawPolygon = (v, options, CallBack) => {
  if (!v) {
    throw new Error('viewer is required');
  }
  polygonDispose();
  viewer = v;
  let drawResultObject = {
    geomType: options.geomType || 'polygon',
    flightAreaType: options.flightAreaType || 'dfence',
    id: generateKey(),
    entityPoints: [],
    cartesianPoints: [],
    _endPointPositionMap: new Map(),
    polygon: null,
    action: 'add',
    _tempMouseEntity: null,
    title: options?.title || '',
    color: options?.color || '',
    area: 0,
    length: 0
  };
  document.body.style.cursor = 'crosshair'; //
  try {
    handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas); // 选中的经纬度
    // 最后
    drawResultObject.id = generateKey();
    handler.setInputAction(click => {
      let cartesian = viewer.camera.pickEllipsoid(click.position, viewer.scene.globe.ellipsoid);
      if (!cartesian) return;
      document.body.style.cursor = 'crosshair';
      drawResultObject.cartesianPoints.push(cartesian);
      let np = createPoint(viewer, cartesian, { pixelSize: 10, outlineWidth: 2 });
      drawResultObject.entityPoints.push(np);
      drawResultObject._endPointPositionMap.set(np.id, {
        position: cartesian,
        id: np.id
      });
      isPolygonCreate.value = true;
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

    handler.setInputAction(event => {
      document.body.style.cursor = 'crosshair';
      if (drawResultObject.cartesianPoints.length < 1) return;
      let cartesian3 = px2Catesian3(event.endPosition, viewer);
      if (!cartesian3) return;
      if (drawResultObject._tempMouseEntity) {
        drawResultObject._tempMouseEntity.position = cartesian3;
      } else {
        drawResultObject._tempMouseEntity = createPoint(viewer, cartesian3, { pixelSize: 10, outlineWidth: 2 });
      }
      if (drawResultObject.cartesianPoints.length === 2) {
        let pp = createPolygon(viewer, drawResultObject.cartesianPoints, {
          id: drawResultObject.id,
          name: 'Polygon_Edit_' + drawResultObject.id,
          color: options.color
        });
        drawResultObject.polygon = pp != null ? pp : null;
        drawResultObjectGlobal = drawResultObject.polygon;
        // 这一步比较关键
        drawResultObject.cartesianPoints.push(cartesian3);
      }
      if (drawResultObject.cartesianPoints.length > 2) {
        drawResultObject.cartesianPoints.pop();
        drawResultObject.cartesianPoints.push(cartesian3);
      }
      dialogDataRect.entity = drawResultObject.polygon;
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

    handler.setInputAction(event => {
      if (drawResultObject.cartesianPoints.length < 3) {
        ElMessage.warning('请先绘制多边形');
        return;
      }
      handler?.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
      handler?.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      handler?.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
      handler.destroy();
      // 添加最后一个点
      drawResultObject._endPointPositionMap.set(drawResultObject._tempMouseEntity.id, {
        position: drawResultObject._tempMouseEntity.position._value,
        id: drawResultObject._tempMouseEntity.id
      });
      // 清理临时点
      if (drawResultObject._tempMouseEntity) {
        viewer.entities.remove(drawResultObject._tempMouseEntity);
        drawResultObject._tempMouseEntity = null;
      }
      // 移除线上点位
      drawResultObject.entityPoints.forEach(ppointEntity => {
        viewer.entities.remove(ppointEntity);
      });
      let baseInfo = getPolygonBaseInfoFromPositions(drawResultObject.entityPoints);
      drawResultObject.area = baseInfo.area;
      drawResultObject.length = baseInfo.length;
      drawResultObjectGlobal = drawResultObject.polygon;
      // 这里构建点位信息
      addPropertiesToEntity(drawResultObject.polygon, {
        type: drawResultObject.geomType || 'polygon',
        title: drawResultObject.title,
        id: drawResultObject.id,
        flightAreaType: drawResultObject.flightAreaType || 'dfence',
        positions: drawResultObject.cartesianPoints,
        area: drawResultObject.area,
        length: toNumber(drawResultObject.length, 2),
        color: options?.color || Cesium.Color.WHITE.withAlpha(0.4)
      });
      document.body.style.cursor = 'default';
      isPolygonCreate.value = true;
      CallBack &&
        CallBack({
          data: drawResultObject,
          polygon: drawResultObject.polygon,
          clickType: 'RIGHT_CLICK',
          event: 'update'
        });
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  } catch (error) {}
};

export const polygonDispose = () => {
  document.body.style.cursor = 'default';
  if (handler && handler.isDestroyed() === false) {
    handler?.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    handler?.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
    handler?.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    handler.destroy();
  }
  isPolygonCreate.value = false;
  if (viewer && drawResultObjectGlobal) {
    viewer.entities.remove(drawResultObjectGlobal);
  }
  drawResultObjectGlobal = null;
};

export const cancelPolygonAdd = () => {
  if (viewer && drawResultObjectGlobal) {
    viewer.entities.remove(drawResultObjectGlobal);
  }
  drawResultObjectGlobal = null;
  polygonDispose();
};

//#endregion

//#region 公用部分

/**
 * 通用产生采集点
 * @param {*} cartesian
 * @returns
 */
export function createPoint(viewer, cartesian, options) {
  let point = viewer.entities.add({
    id: options.id || generateKey(),
    name: 'ENDPOINT',
    position: toCartesian3(cartesian),
    billboard: {
      image: endpointIcon,
      width: 14,
      height: 14,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      disableDepthTestDistance: Number.POSITIVE_INFINITY // 点总是在最上层
    }
  });
  return point;
}

/**
 * 通用产生采集点
 * @param {*} cartesian
 * @returns
 */
export function createPolygon(viewer, positions, options) {
  try {
    if (!viewer || !positions) {
      return;
    }
    return viewer.entities.add({
      id: options?.id || generateKey(),
      name: options?.name || 'Polygon_Edit_' + generateKey(),
      polygon: {
        hierarchy: new Cesium.CallbackProperty(() => {
          let hierarchyTemp = new Cesium.PolygonHierarchy(positions, []);
          return hierarchyTemp;
        }, false),
        show: true,
        material: options?.color || Cesium.Color.WHITE.withAlpha(0.4),
        outline: true,
        outlineColor: Cesium.Color.ALICEBLUE,
        outlineWidth: 2,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    });
  } catch (error) {}
}

//#endregion
const getPolygonBaseInfoFromPositions = (endEntityPoints = []) => {
  if (endEntityPoints.length === 0) {
    throw new Error('坐标组合不能为空');
  }
  let entityPoints = [];
  endEntityPoints.forEach(entity => {
    entityPoints.push(entity.position._value);
  });
  let degList = c3ArrToDegress(entityPoints);
  let center = getCenter(degList);
  // 计算面积
  let area = calculatArea(entityPoints);
  // 计算周长
  let length = calculatPerimeter(entityPoints);
  return { center: toCartesian3(center), length, area };
};
