<script>
export default { name: 'DroneList' };
</script>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import VideoPlayer from '../../videos/live-stream/components/VideoPlayer.vue';
import { refreshStreams } from '@/api/devices'
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
	showClose: {
		type: Boolean,
		default: true, //是否显示关闭按钮
	},
	showBlowUp: {
		type: Boolean,
		default: false, //是否显示与地图切换按钮
	},
	isBig: {
		type: Boolean,
		default: false, //是否为实时飞行右侧大屏展示
	},
	heigth: {
		type: Number,
    default: 328
	},
	device: {
		type: Array,
		default() {
      return [];
    }
	},
	playerId: {
		type: String,
		default: ''
	},
	showUp: {
		type: Boolean,
		default: true, //是否显示全屏按钮
	},
	showRefresh: {
		type: Boolean,
		default: false, //是否显示刷新按钮
	},
	showAI: {
		type: Boolean,
		default: false, //是否显示AI烟火识别
	},
	showShare: {
		type: Boolean,
		default: false, //是否显示AI烟火识别
	},
});
const emit = defineEmits(['onClose','showAI','showShare']);
const checkedDevice=ref([])
const activeName = ref('无人机')
function handleCountChange(count) {
  window.$bus.emit('changeVideoCount', 1);
}

watch(
  () => props.device,
  newVal => {
    checkedDevice.value = [];
		let params = {
			...toRaw(newVal)[0],
			droneSelected: toRaw(newVal)[0]?.device_sn,
			cameraSelected: toRaw(newVal)[0]?.index,
			cameraName: toRaw(newVal)[0]?.nickname,
			url_type: toRaw(newVal)[0]?.url_type,
			source: toRaw(newVal)[0]?.source,
			claritySelected: 3,
			cameraId: '417dc9aa-0db3-4cd8-91ea-1387ae59e716'
		}
    checkedDevice.value = [params] || [];
  },
  { deep: true, immediate: true }
);

onMounted(()=>{
	handleCountChange();
})

function closeVideo () {
	emit('onClose')
}

function refreshVideo () {
	refreshStreams({
		url: '',
		video_id: `${checkedDevice.value[0]?.droneSelected}/${checkedDevice.value[0]?.cameraSelected}/normal-0`,
		url_type: 3,
		video_quality: checkedDevice.value[0]?.claritySelected
	}).then(res=>{
    window.$bus.emit('refreshVideo',res.url);
	})
}

function blowUp () {
  window.$bus.emit('changeVideoCount', 1);
	emit('change')
}

function changeAI () {
	emit('showAI')
}

function openShare () {
	emit('showShare')
}

</script>

<template>
	<div v-show="visible" style="height: 100%">
		<div :class="isBig ? ['big-title','flex'] : ['alarm-title','flex']">
			<div>	
				<svg-icon icon-class="title" style="margin-right: 4" />
				<span>{{ checkedDevice[0]?.nickname || '' }}</span>
			</div>
			<div class="flex">
				<div v-if="showShare" style="margin-right: 10px;cursor: pointer" @click="openShare">直播分享</div>
				<div v-if="showAI" style="margin-right: 10px;cursor: pointer" @click="changeAI">烟火识别</div>
				<div v-if="showRefresh" @click="refreshVideo"><svg-icon icon-class="refresh" style="width:16px;height:16px;margin-right: 6;cursor: pointer;" /></div>
				<div v-if="showClose" @click="closeVideo"><svg-icon icon-class="close" style="width:16px;height:16px;margin-right: 4;cursor: pointer;" /></div>
				<div v-if="showBlowUp" @click="blowUp"><svg-icon icon-class="blow-up" style="width:16px;height:16px;margin-right: 12;color: #fff;cursor: pointer;" /></div>
			</div>
		</div>
		<div :class="isBig ? 'bigBox' : 'alarm-ul'">
			<VideoPlayer :playerId="props.playerId" :data="checkedDevice" :show-close="false" :show-title="false" :show-blow-up= "showUp"/>
		</div>
	</div>
</template>

<style lang="scss" scoped>
.bigBox{
	height: 95.1%;
}
.big-title {
	height: 4%;
	max-height: 40px;
	background: #11253E;
	color: #fff;
	font-family: SourceHanSansSC-Bold;
	font-size: 14px;
	text-align: left;
	font-weight: 700;
	border-bottom: 1px solid #344054;
	padding-left: 8px;
	z-index: 999;
}
.alarm-title {
	height: 15.2%;
	max-height: 38px;
	line-height: 38px;
	background: #11253E;
	color: #fff;
	font-family: SourceHanSansSC-Bold;
	font-size: 14px;
	text-align: left;
	font-weight: 700;
	border-bottom: 1px solid #344054;
	padding-left: 8px;
	z-index: 999;
}
.alarm-ul {
	background: #001129;
	height: 93.5%;
	.alarm-item {
		min-height: 76px;
		background: #11253E;
		margin-bottom: 12px;
		padding: 8px;
		font-family: SourceHanSansSC-Regular;
		font-size: 14px;
		color: #F5F6F8;
		text-align: left;
		line-height: 22px;
		font-weight: 400;
		.alarm-time {
			font-family: SourceHanSansSC-Regular;
			font-size: 12px;
			color: #98A2B3;
			text-align: right;
			line-height: 20px;
			font-weight: 400;
		}
		.alarm-address{
			font-family: SourceHanSansSC-Regular;
			font-size: 14px;
			color: #FFFFFF;
			text-align: justify;
			line-height: 22px;
			font-weight: 400;
			margin-top: 8px;
			overflow: hidden;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			text-overflow: ellipsis;
			white-space: normal;
		}
	}
}
.flex {
	height: 15.2%;
	line-height: 38px;
	display: flex;
	justify-content: space-between;
}
</style>
