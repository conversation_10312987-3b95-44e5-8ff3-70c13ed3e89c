<template>
  <el-dialog
    title="机场详情"
    :model-value="visible"
    v-if="visible"
    width="500px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <!-- <div class="org-name">{{ formData.workspace_name }}</div> -->
    <div class="left-content">
      <div class="line-left">
        <img src="https://fh.dji.com/assets/img/EA220.71009ce8.png" alt="" class="" />
        <el-descriptions label-align="right" :column="1">
          <el-descriptions-item label="机场名称">{{ formData.nickname }}</el-descriptions-item>
          <el-descriptions-item label="机场型号">{{ formData.device_name }}</el-descriptions-item>
          <el-descriptions-item label="设备SN">{{ formData.device_sn }}</el-descriptions-item>
          <el-descriptions-item label="固件版本">
            {{ formData.firmware_version }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    <div class="content-line">
      <span class="foot">加入组织时间：</span>
      <span class="title">{{ formData.bound_time }}</span>
    </div>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  formData: {
    type: Object,
    default(rawProps) {
      return {};
    }
  }
});
const form = reactive({});
const dataFormRef = ref(ElForm);

watch(
  () => props.formData,
  (newVal, oldVal) => {
    Object.assign(form, newVal);
  },
  { deep: true }
);
const emit = defineEmits(['update:visible']);

const typeOptions = ref([]);

function getTypeOptiopn() {
  typeOptions.value = [];
}

// 关闭弹窗
function closeDialog() {
  emit('update:visible', false);
}

onMounted(() => {
  getTypeOptiopn();
});
</script>
<style lang="scss" scoped>
.org-name {
  font-size: 14px;
  color: #000000a6;
}
.left-content {
  display: flex;
  flex-direction: row;
  img {
    width: 136px;
    height: 136px;
    margin-right: 24px;
  }
  .line-left {
    display: flex;
    padding: 20px 8px;
  }
  .line-middle {
    width: 1px;
    height: 314px;
    background: rgba(0, 0, 0, 0.06);
    margin: auto 32px;
  }
  .line-right {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    .item {
      padding: 6px;
      width: 100%;
      color: #000000d9;
      .color {
        color: #000000a6;
      }
    }
  }
}
.content-line {
  display: flex;
  font-size: 14px;
  line-height: 22px;
  .foot {
    color: #98a2b3;
  }
  .title {
    color: #98a2b3;
  }
}
</style>
