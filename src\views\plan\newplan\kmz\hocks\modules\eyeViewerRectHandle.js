// 根据计算出的矩形长宽设置到鹰眼图上
export const createRectDom = (parentEl = 'container2') => {
  if (!parentEl) {
    return null;
  }
  //   // 获取 #container2 元素的引用
  //   const container2 = document.getElementById(parentEl);
  //   // 创建 .zoom-frame 元素
  //   const zoomFrame = document.createElement('div');
  //   zoomFrame.classList.add('zoom-frame');
  //   zoomFrame.style.width = '80px';
  //   zoomFrame.style.height = '60px';
  //   // 定义模板字符串,用于添加子元素
  //   const childElementsTemplate = `
  //   <div class="top left corner"></div>
  //   <div class="top right corner"></div>
  //   <div class="bottom left corner"></div>
  //   <div class="bottom right corner"></div>
  //   <span class="frame-title map-text-shadow"> 5X </span>
  // `;
  //   // 将模板字符串插入到 .zoom-frame 元素中
  //   zoomFrame.innerHTML = childElementsTemplate;
  //   // 将 .zoom-frame 元素添加到 #container2 容器中
  //   container2.appendChild(zoomFrame);
};
