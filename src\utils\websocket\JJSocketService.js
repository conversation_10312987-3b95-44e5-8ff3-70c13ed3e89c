import BaseSocketService from './BaseSocketService'
/**
 * 接处警单例websocket
 * 使用示例：
 * import JJSocketService from '@/utils/websocket/JJSocketService';
 *
 * const wsIns = JJSocketService.Instance;
 * wsIns.connect(()=>{
 *      // 连接成功可以执行发送订阅
 *      wsIns.send(xx,{})
 *      wsIns.subscribe(xx,()=>{
 *          // 执行订阅成功后事件
 *      })
 * })
 *
 * // 应用销毁时断开连接
 * wsIns.disconnect()
 */
class JJSocketService extends BaseSocketService {
  static instance = null;

  constructor() {
    super()
    const wsUrl = 'ws://125.77.202.162:24174/serv/alarmSocket';
    const userId = '123';
    this.wsRequestUrl= this.generateWsUrl(wsUrl, 'ALARM_MANAGE', userId)
  }
  generateWsUrl(prefixUrl, type, userId, infoId = undefined) {
    let url = prefixUrl;
    if (type || userId || infoId) {
      url += '?';
      if (type) url += `type=${type}&`;
      if (userId) url += `wsu=${userId}&`;
      if (infoId) url += `infoId=${infoId}&`;
      url = url.substr(0, url.length - 1);
    }
    return url;
  }
  static get Instance() {
    if (!this.instance) {
      this.instance = new JJSocketService();
    }
    return this.instance;
  }
}

export default JJSocketService;

