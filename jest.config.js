module.exports = {
  testMatch: [
    '**/tests/unit/**/*.spec.[jt]s?(x)',
    '**/__tests__/*.[jt]s?(x)',
    '**/*.helper.test.js',
    '<rootDir>/src/**/*.test.js'
  ],
  transform: {
    '^.+\\.vue$': '@vue/vue3-jest',
    '^.+\\js$': ['babel-jest', { configFile: './babel-test.config.js' }]
  },
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  collectCoverage: true,

  moduleFileExtensions: ['js', 'vue', 'ts', 'mjs'],
  coverageReporters: ['html', 'text-summary'],
  transformIgnorePatterns: ['/node_modules/']
};
