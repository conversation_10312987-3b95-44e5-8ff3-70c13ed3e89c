<template>
  <div class="area-item-wrap">
    <div class="title ellipsis">
      <el-tooltip class="box-item" show-after="1" effect="dark" :content="data.name" raw-content="true" placement="top">
        {{ data.name }}
      </el-tooltip>
    </div>
    <div class="item-content">
      <div class="left">
        <span class="time">{{ convertTimestamp(data.create_time) }}</span>
      </div>
      <div class="right">
        <el-tooltip class="box-item" show-after="1" content="开启或关闭" raw-content="true" placement="top">
          <el-switch
            v-model="data.status"
            class="ml-2"
            style="--el-switch-on-color: #39bfa4; --el-switch-off-color: #6d6f70"
            @change="setAreaVisibleHandle"
          />
        </el-tooltip>

        <div class="icon-item">
          <el-tooltip class="box-item" show-after="1" content="定位到区域" raw-content="true" placement="top">
            <el-icon class="iconStyle" @click="zoomToArea"><MapLocation /></el-icon>
          </el-tooltip>
        </div>
        <div class="icon-item">
          <el-tooltip class="box-item" show-after="1" content="移除该区域" raw-content="true" placement="top">
            <el-icon class="iconStyle" @click="deleteArea"><Delete /></el-icon>
          </el-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default { name: 'AreaItem' };
</script>
<script setup>
import { ElMessage } from 'element-plus';
import 'element-plus/dist/index.css';
import { Delete, MapLocation } from '@element-plus/icons-vue';
import { defineProps, computed, defineEmits, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  flightData: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
// const data = computed({
//   get: () => props.flightData,
//   set: value => {
//     emits('update:flightData', value);
//   }
// });

const data = computed(() => props.flightData);
const emits = defineEmits(['onDeleteHandle', 'onZoomToAreaHandle', 'onSetAreaVisibleHandle']);
function convertTimestamp2(timestamp = 0) {
  if (timestamp === 0 || !timestamp) {
    return '0000-00-00 00:00:00';
  }
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
function convertTimestamp(n) {
  n = new Date(n);
  return n.toLocaleDateString().replace(/\//g, '-') + ' ' + n.toTimeString().substr(0, 8);
}
//#region 方法
const deleteArea = () => {
  emits('onDeleteHandle', data.value);
};
const zoomToArea = () => {
  emits('onZoomToAreaHandle', data.value);
};
const setAreaVisibleHandle = value => {
  emits('onSetAreaVisibleHandle', {
    data: data.value,
    flag: value
  });
};

//#endregion

onMounted(() => {});

onUnmounted(() => {});
</script>
<style lang="scss" scoped>
@import '@/styles/common/global.scss';
.area-item-wrap {
  height: 72px;
  padding: 8px 2px;
  width: 100%;
  border-radius: 5px;
  background-color: #11253e;
}
.title {
  width: 300px;
  height: 22px;
  margin: 2px 8px;
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #ffffff;
  text-align: justify;
  line-height: 22px;
  font-weight: 400;
}

.item-content {
  height: 20px;
  margin: 8px;
  display: flex;
  justify-content: space-between;
  .left {
    display: flex;
    .time {
      font-family: SourceHanSansSC-Regular;
      font-size: 12px;
      color: #98a2b3;
      text-align: right;
      line-height: 20px;
      font-weight: 400;
    }
  }
  .right {
    display: flex;
    align-items: center;
  }
}
.icon-item {
  margin: 0px 13px;
  display: flex;
  align-items: center;
}
// 第一个和最后一个 不做margin
.icon-item:first-child,
.icon-item:last-child {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.iconStyle {
  color: #9c9ea1 !important;
  cursor: pointer;
}
</style>
