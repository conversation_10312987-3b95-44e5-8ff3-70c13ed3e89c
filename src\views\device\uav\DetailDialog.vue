<template>
  <el-dialog
    title="无人机详情"
    width="1162px"
    v-if="visible"
    :model-value="visible"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <div class="left-content">
      <div class="line-left">
        <div class="left-info">
          <div>
            <img src="@/assets/uav_img.png" alt="" class="" />
          </div>
          <el-descriptions label-align="right" :column="1">
            <el-descriptions-item width="350" label="无人机名称：" label-class-name="label-class" class-name="class-name"><span :title="formData.nickname">{{ formData.nickname }}</span></el-descriptions-item>
            <el-descriptions-item label="设备SN：" label-class-name="label-class" class-name="class-name">{{ formData.device_sn }}</el-descriptions-item>

            <el-descriptions-item width="350" label="组织名称：" label-class-name="label-class" class-name="class-name">
              {{ formData.dept_name }}
            </el-descriptions-item>
            <el-descriptions-item label="最后上线时间：" label-class-name="label-class" class-name="class-name">
              {{ formData.login_time }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="batteries">
          <div class="batteries-text">电池</div>
          <el-table highlight-current-row :data="dataList" v-loading="loading" stripe height="540">
            <el-table-column label="序列号" prop="sn" width="150px" show-overflow-tooltip> </el-table-column>
            <el-table-column label="循环次数" prop="loop_times" show-overflow-tooltip> </el-table-column>
            <el-table-column label="电压（mV）" prop="voltage" width="120px" show-overflow-tooltip />
            <el-table-column label="温度（°C）" prop="temperature" width="120px" show-overflow-tooltip />
            <el-table-column label="剩余电量" prop="capacity_percent" show-overflow-tooltip />
            <el-table-column
              label="高电压存储天数（日）"
              prop="high_voltage_storage_days"
              width="180px"
              show-overflow-tooltip
            />
          </el-table>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { getDevicesOsd } from '@/api/devices';

const loading = ref(false);
const total = ref(0);

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  queryType: 2,
  message: ''
});

const dataList = ref([]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  formData: {
    type: Object,
    default(rawProps) {
      return {};
    }
  }
});
const form = reactive({});
const dataFormRef = ref(ElForm);

watch(
  () => props.formData,
  (newVal, oldVal) => {
    if (!props.visible || !props.formData) return;
    Object.assign(form, newVal);
    if (newVal.status) {
      handleQuery();
    } else {
      dataList.value = [];
    }
  },
  { deep: true }
);
const emit = defineEmits(['update:visible']);

const typeOptions = ref([]);

/**
 * 查询
 */
function handleQuery() {
  if (!form.device_sn) return;
  loading.value = true
  getDevicesOsd(form.device_sn).then(data => {
    const { battery = {} } = data;
    dataList.value = battery.batteries || [];
    loading.value = false
  });
}
function getTypeOptiopn() {
  typeOptions.value = [];
}

// 关闭弹窗
function closeDialog() {
  emit('update:visible', false);
}

onMounted(() => {
  getTypeOptiopn();
});
</script>
<style lang="scss">
.label-class {
  display: inline-block;
  width: 100px;
  text-align: right;
}
.class-name {
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transform: translateY(6px);
}
</style>
<style lang="scss" scoped>
.org-name {
  font-size: 14px;
  color: #000000a6;
}
.left-content {
  display: flex;
  flex-direction: row;
  img {
    width: 136px;
    height: 136px;
    margin-right: 24px;
  }
  .line-left {
    display: flex;
    padding: 20px 8px;
    .left-info {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .batteries {
      border-left: 1px solid #eeeeee;
      padding-left: 15px;
      margin-left: 15px;
      .batteries-text {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 16px;
      }
    }
  }
  .line-middle {
    width: 1px;
    height: 314px;
    background: rgba(0, 0, 0, 0.06);
    margin: auto 32px;
  }
  .line-right {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    .item {
      padding: 6px;
      width: 100%;
      color: #000000d9;
      .color {
        color: #000000a6;
      }
    }
  }
}
.content-line {
  display: flex;
  font-size: 14px;
  line-height: 22px;
  .foot {
    color: #000000d9;
  }
  .title {
    color: #000000a6;
  }
}
</style>
