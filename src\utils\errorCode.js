/**
 * 根据错误码翻译错误信息
 * @param code
 * @param errorMsg
 * @returns
 */
export function getErrorMessage(code, errorMsg) {
  const errorInfo = ERROR_CODE.find(item => item.code === code);
  return errorInfo ? errorInfo.msg : errorMsg || 'Server error';
}

// 暂时只添加航线错误
export const ERROR_CODE = [
  {
    code: 314001,
    msg: '下发的航线任务URL为空'
  },
  {
    code: 314002,
    msg: '下发的航线任务MD5为空'
  },
  {
    code: 314003,
    msg: '任务ID无效'
  },
  {
    code: 314004,
    msg: '从云端发送飞行航线任务失败'
  },
  {
    code: 314005,
    msg: '航线MD5校验失败'
  },
  {
    code: 314006,
    msg: '等待飞机上传航线超时（等待gs_state）'
  },
  {
    code: 314007,
    msg: '上传航线至飞机失败'
  },
  {
    code: 314008,
    msg: '等待飞机进入航线可执行状态超时'
  },
  {
    code: 314009,
    msg: '打开航线任务失败'
  },
  {
    code: 314010,
    msg: '航线执行失败'
  },
  {
    code: 316001,
    msg: '设置备降点失败'
  },
  {
    code: 316002,
    msg: '备降安全过渡高度设备失败'
  },
  {
    code: 316003,
    msg: '设置起飞高度失败。备注：当前DJI Dock设置的飞机默认安全起飞高度为：1.8'
  },
  {
    code: 316004,
    msg: '设置失控行为失败'
  },
  {
    code: 316005,
    msg: '飞机RTK收敛失败'
  },
  {
    code: 316013,
    msg: 'DJI Dock移动'
  },
  {
    code: 316015,
    msg: '飞机RTK收敛位置距离DJI Dock太远'
  },
  {
    code: 316007,
    msg: '等待飞机准备就绪时设置参数超时'
  },
  {
    code: 316008,
    msg: '无法控制飞机'
  },
  {
    code: 316009,
    msg: '飞机电量低'
  },
  {
    code: 316010,
    msg: '上电后，飞机未连接超过2分钟（飞控OSD接收超时）'
  },
  {
    code: 316011,
    msg: '着陆位置偏移'
  },

  {
    code: 317001,
    msg: '获取媒体文件数量失败'
  },

  {
    code: 319001,
    msg: '任务中心当前不空闲'
  },
  {
    code: 319002,
    msg: '无人机巢通信超时'
  },
  {
    code: 319999,
    msg: '未知错误，例如崩溃后重新启动'
  },
  {
    code: 321000,
    msg: '航线执行失败，未知错误'
  },
  {
    code: 321257,
    msg: '航线已经开始，不能再次开始'
  },
  {
    code: 321258,
    msg: '此状态下不能中断航线'
  },
  {
    code: 321259,
    msg: '航线未开始，不能结束航线'
  },
  {
    code: 321513,
    msg: '达到高度限制'
  },
  {
    code: 321514,
    msg: '达到限制'
  },
  {
    code: 321515,
    msg: '越过限制飞行区'
  },
  {
    code: 321516,
    msg: '低限制'
  },

  {
    code: 321517,
    msg: '避障'
  },
  {
    code: 321769,
    msg: 'GPS信号弱'
  },
  {
    code: 321770,
    msg: '当前档位状态不能执行，B控制夺取控制，档位切换'
  },
  {
    code: 321771,
    msg: '返航点未刷新'
  },
  {
    code: 321772,
    msg: '当前电池电量过低无法启动任务'
  },
  {
    code: 321773,
    msg: '低电返航'
  },
  {
    code: 321776,
    msg: 'RTK未准备就绪'
  },
  {
    code: 321778,
    msg: '飞机在地面空闲且不允许启动航线，认为用户未准备好'
  },
  {
    code: 322282,
    msg: '用户中断（B控制夺权）'
  },
  {
    code: 322548,
    msg: '航线文件格式有误，航点数量异常。请检查航线文件格式'
  },
  {
    code: 514100,
    msg: '命令不支持'
  },
  {
    code: 514101,
    msg: '关闭推杆失败'
  },
  {
    code: 514102,
    msg: '释放推杆失败'
  },
  {
    code: 514103,
    msg: '飞机电量低'
  },
  {
    code: 514104,
    msg: '启动充电失败'
  },
  {
    code: 514105,
    msg: '停止充电失败'
  },
  {
    code: 514106,
    msg: '重启飞机失败'
  },
  {
    code: 514107,
    msg: '打开舱盖失败'
  },
  {
    code: 514108,
    msg: '关闭舱盖失败'
  },
  {
    code: 514109,
    msg: '打开飞机失败'
  },
  {
    code: 514110,
    msg: '关闭飞机失败'
  },
  {
    code: 514111,
    msg: '飞机内舱慢速旋转螺旋桨启动失败'
  },
  {
    code: 514112,
    msg: '飞机内舱慢速旋转螺旋桨停止失败'
  },
  {
    code: 514113,
    msg: '与飞机建立有线连接失败'
  },
  {
    code: 514114,
    msg: '获取飞机电源状态，命令超时或返回码非0'
  },
  {
    code: 514116,
    msg: 'DJI Dock忙碌，正在执行其他控制指令'
  },
  {
    code: 514117,
    msg: '检查舱盖状态失败'
  },
  {
    code: 514118,
    msg: '检查推杆状态失败'
  },
  {
    code: 514120,
    msg: 'DJI Dock和飞机SDR连接失败'
  },
  {
    code: 514121,
    msg: '紧急停止状态'
  },
  {
    code: 514122,
    msg: '无法获取飞机充电状态（获取充电状态失败，影响充电和远程故障排除）'
  },
  {
    code: 514123,
    msg: '由于电量低无法开机'
  },
  {
    code: 514124,
    msg: '获取电池信息失败'
  },
  {
    code: 514125,
    msg: '电池已充满，无法再次充电'
  },
  {
    code: 514145,
    msg: '现场调试中无法工作'
  },
  {
    code: 514146,
    msg: '远程调试中无法工作'
  },
  {
    code: 514147,
    msg: '升级状态中无法工作'
  },
  {
    code: 514148,
    msg: '任务状态中无法执行新任务'
  },
  {
    code: 514150,
    msg: 'DJI Dock正在自动重启'
  }
];

