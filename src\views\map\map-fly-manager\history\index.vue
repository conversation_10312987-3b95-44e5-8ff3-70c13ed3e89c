<template>
  <div class="historyfly-container">
    <div class="map-100" :class="{ 'is-active': showFullScreen }">
      <mainMapWidget ref="mainMapWidgetRef" :showPoltLyr="false" />
      <div class="toper">
        <div class="toper-title">
          <el-row>
            <el-col :span="1" v-if="showReturnLeft">
              <div class="return-left" @click="quitBack()" title="返回">
                <el-image style="width: 20px; height: 20px" :src="returnLeftIconUrl" />
              </div>
            </el-col>
            <el-col :span="3" class="nick">
              <svg-icon icon-class="drone" style="width: 14px; height: 14px; margin-left: 18px" />
            </el-col>
            <el-col :span="20" class="nick ellipsis" :title="flyNickname">
              {{ flyNickname }}
            </el-col>
          </el-row>
        </div>
        <div class="toper-content">
          <el-row class="c-row" style="padding-top: 5px">
            <el-col :span="9" class="c-title">任务开始时间</el-col>
            <el-col :span="15" class="c-text">{{ trackInfo.startTime }} </el-col>
          </el-row>
          <el-row class="c-row">
            <el-col :span="9" class="c-title">任务结束时间</el-col>
            <el-col :span="15" class="c-text">{{ trackInfo.endTime }} </el-col>
          </el-row>
          <el-row class="c-row">
            <el-col :span="9" class="c-title">实际飞行距离</el-col>
            <el-col :span="15" class="c-text">{{ trackInfo.flyDistance }} </el-col>
          </el-row>
          <el-row class="c-row">
            <el-col :span="9" class="c-title">实际飞行时长</el-col>
            <el-col :span="15" class="c-text">{{ trackInfo.flyTime }} </el-col>
          </el-row>
          <!-- 罗盘 -->
          <div class="c-compass">
            <div class="attitude-view attitude-view-md compass-item">
              <div class="pitch-editor">
                <span class="gimbal-pitch angle-number map-text-shadow">{{ osdInfo.gimbalPitch }}</span>
                <div class="pitch-bar" style="background: currentcolor"></div>
                <div class="marker" style="top: 37.5%"></div>
                <div class="marker zero-marker"></div>
                <div class="marker" style="top: 68.75%"></div>
                <div class="marker" style="top: 87.5%"></div>
                <div class="marker limit-marker" style="top: 31.25%"></div>
                <span class="gimbal-icon2" style="font-size: 10px" :style="{ top: osdInfo.gimbalPitchPercent }">
                  <span class="gimbal-icon-inner" style="font-size: 8px"></span>
                </span>
              </div>
              <div class="yaw-editor with-button">
                <div class="yaw-limit">
                  <div class="limit-marker" style="transform: rotate(-90deg)"></div>
                  <div class="limit-marker" style="transform: rotate(90deg)"></div>
                </div>
                <div class="yaw-compass active">
                  <img src="@/assets/plan/znz.png" :style="{ transform: osdInfo.transform }" />
                </div>
                <div class="yaw-drone">
                  <img src="@/assets/plan/纸飞机.png" />
                </div>
                <div class="yaw-gimbal">
                  <span class="gimbal-icon" style="font-size: 14px; transform: rotate(0deg)"
                    ><span class="gimbal-icon-inner" style="font-size: 12px"></span
                  ></span>
                </div>
                <span class="angle-number degree drone map-text-shadow">{{ osdInfo.heading }}</span>
              </div>
              <div class="obstacle-info">
                <div class="obstacle-bar"></div>
                <div class="marker zero-marker"></div>
                <span class="agl-number map-text-shadow">{{ osdInfo.aglHeight }}<span class="unit">m AGL</span></span>
              </div>
            </div>
            <div class="operation-earth">
              <img src="@/assets/plan/flow.png" width="24" />
            </div>
          </div>
          <el-row class="c-row" style="padding-top: 10px">
            <el-col :span="4" class="c-title">电量</el-col>
            <el-col :span="4" class="c-text">{{ osdInfo.capacity_percent + ' %' }} </el-col>
            <el-col :span="4" class="c-title">卫星数量</el-col>
            <el-col :span="4" class="c-text">{{ osdInfo.gps_number }}</el-col>
            <el-col :span="4" class="c-title">RTK</el-col>
            <el-col :span="4" class="c-text">{{ osdInfo.rtk_number }}</el-col>
          </el-row>
          <el-row class="c-row">
            <el-col :span="6" class="c-title">水平速度</el-col>
            <el-col :span="6" class="c-text">{{ osdInfo.horizontal_speed + ' m/s' }}</el-col>
            <el-col :span="6" class="c-title">垂直速度</el-col>
            <el-col :span="6" class="c-text">{{ osdInfo.vertical_speed + ' m/s' }}</el-col>
          </el-row>
          <el-row class="c-row">
            <el-col :span="6" class="c-title">绝对高度</el-col>
            <el-col :span="6" class="c-text">{{ osdInfo.height + ' m' }}</el-col>
            <el-col :span="6" class="c-title">距离返航点</el-col>
            <el-col :span="6" class="c-text">{{ osdInfo.home_distance + ' m' }}</el-col>
          </el-row>
        </div>
      </div>
      <div class="vedio-box">
        <div class="vedio-content" :class="{ 'is-active': !isShowVedio }">
          <el-tabs v-model="activeVedioName" class="demo-tabs" @tab-change="tabVedioChange" v-show="isShowVedio">
            <el-tab-pane label="高清" name="高清" key="0">
              <div class="video-div-none">
                <div
                  class="video-div"
                  v-for="(item, index) in v_videoList"
                  :key="item.file_id || 'v-video-' + index"
                  v-show="item.isShow"
                >
                  <div class="video-info">
                    <video-component
                      :src="item.file_url"
                      :video-id="item.file_id"
                      ref="videoRefs"
                      @metadata-loaded="handleMetadataLoaded"
                    />
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="红外" name="红外" key="1">
              <div class="video-div-none">
                <div
                  class="video-div"
                  v-for="(item, index) in t_videoList"
                  :key="item.file_id || 't-video-' + index"
                  v-show="item.isShow"
                >
                  <div class="video-info">
                    <video-component
                      :src="item.file_url"
                      :video-id="item.file_id"
                      ref="videoRefs"
                      @metadata-loaded="handleMetadataLoaded"
                    />
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <!-- <el-tab-pane v-for="(item, index) in videoList" :label="item.showLabel" :name="item.file_id" :key="index">
              <div class="video-info">
                <video-component
                  :src="item.file_url"
                  :video-id="item.file_id"
                  ref="videoRefs"
                  @metadata-loaded="handleMetadataLoaded"
                />
              </div>
            </el-tab-pane> -->
          </el-tabs>
        </div>
      </div>
      <div class="footer">
        <div class="start" @click="setClockPlayer()" :class="{ 'is-active': clockIsStoped }"></div>
        <div class="timeline">
          <el-slider
            class="slider-demo-block"
            v-model="selectedTime"
            size="large"
            :show-tooltip="true"
            :format-tooltip="clockTooltip"
            :min="0"
            :max="maxSliderTimeValue"
            :step="1"
            @input="changeTimeNode()"
          />
        </div>
        <div class="timeinfo">
          {{ timingInfo }}
        </div>
        <div>
          <el-select
            v-model="clockParams.multiplier"
            placeholder="请选择"
            size="small"
            style="width: 80px; margin-top: 6px"
            @change="changeMultiplier"
          >
            <el-option v-for="item in multiplierOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
      </div>
    </div>
    <div class="media-box" v-if="!showFullScreen">
      <div class="media-title">
        <svg-icon icon-class="title" style="margin-right: 5px; margin-left: 10px" />
        <span>资源列表</span>
      </div>
      <div class="media-content">
        <el-scrollbar>
          <div class="media-item" v-for="(item, index) in mediaList" :key="item.file_id || 'media-item-' + index">
            <div class="media-item-top">
              <el-image
                v-if="item?.file_name.indexOf('.mp4') == -1 && item?.file_name.indexOf('.MP4') == -1"
                class="left-img"
                :src="item.file_url"
                @click="openImgDetail(item)"
                fit="cover"
              />
              <video
                v-if="item?.file_name.indexOf('.mp4') !== -1 || item?.file_name.indexOf('.MP4') !== -1"
                class="vedio-img"
                :src="item.file_url"
              >
                <source :src="item.file_url" type="video/mp4" />
              </video>
              <div
                v-if="item?.file_name.indexOf('.mp4') !== -1 || item?.file_name.indexOf('.MP4') !== -1"
                class="video-play-view"
              >
                <img class="video-play-btn" @click="openImgDetail(item)" src="@/assets/home/<USER>" />
              </div>
            </div>
            <div class="media-item-bottom">
              <div class="name" :title="item.file_name">{{ item.file_name }}</div>
              <div class="info-div">
                <div class="item-info">
                  {{ item.create_time }}
                </div>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>
    <!--图片视频详情组件-->
    <ImgDetail v-model:visible="imgDetail.visible" :title="imgDetail.title" :imgUrl="imgDetail.imgUrl" />
    <panoramaView
      v-model:visible="panoramaDetail.visible"
      :title="panoramaDetail.title"
      :imgUrl="panoramaDetail.imgUrl"
    />
  </div>
</template>
<script>
export default {
  name: 'Historyfly'
};
</script>
<script setup>
import { onMounted, onUnmounted, onBeforeUnmount, ref, computed, nextTick } from 'vue';
import * as Cesium from 'cesium';
import ImgDetail from '@/views/result-manage/media-library/imgDetail.vue';
import panoramaView from '@/views/result-manage/media-library/panoramaView.vue';
import { getTerrainHeight } from '@/components/Cesium/libs/cesium/index';
import mainMapWidget from '@/views/map/map-fly-manager/components/homeMainMap.vue';
import { getCesiumEngineInstance } from '@/components/Cesium/libs/cesium';
import { fetchFlightRecord, fetchRecordTrack, getWaylines, fetchWaylinesTaskDetail } from '@/api/wayline';
import { getFlightRecordList, getRecordByid } from '@/api/task';
import { getDevicesBySn } from '@/api/devices';
import { getMediaFiles } from '@/api/live';
import { getDevicesBound } from '@/api/devices';
import { useRouter } from 'vue-router';
import Wayline from '../components/wayline';
import { DOMAIN } from '@/utils/constants';
import { addWaylineOnMap, delWayline } from '@/views/plan/surface/hocks/modules/wayLineRouteCtrl';
import CreateFrustum from '@/components/Cesium/libs/cesium/frustum';
import { AnimationFrameManager } from '@/components/Cesium/libs/cesium/AnimationFrameManager';
import VideoComponent from './VideoComponent.vue'; // 引入 Video 组件
import * as turf from '@turf/turf';
const returnLeftIconUrl = new URL('@/assets/return-left.png', import.meta.url).href;
const showReturnLeft = ref(true); // 默认显示
const mainMapWidgetRef = ref(null);
const ani = new AnimationFrameManager();
const router = useRouter();
const trackList = ref([]);
const flyRecords = ref([]);
// 无人机 + 机场名称
const flyNickname = ref('');
const trackInfo = reactive({
  startTime: '',
  endTime: '',
  flyDistance: '',
  flyTime: '',
  onflyTime: ''
});
let osdInfo = reactive({
  mode_code: 14, // 状态
  capacity_percent: '--', //电量
  height: '--', //飞行高度
  horizontal_speed: '--', // 水平速度
  vertical_speed: '--', // 垂直速度
  gps_number: '--', //GPS数量
  rtk_number: '--', //RTK数量
  home_distance: '--', // 离机场距离
  transform: 'rotate(90deg)',
  gimbalPitchPercent: '50%'
});
const selectedTime = ref(0);
const timingInfo = ref('');
const maxSliderTimeValue = ref(0);
// 当没有媒体文件时满屏展示地图
const showFullScreen = ref(true);
const airModel = new URL('/resource/models/wrj2.glb', import.meta.url).href;
// 无人机飞行定位
let intervalTimer = null;
let navModel = null;

// 设置媒体选项
const mediaList = ref([]);
const imgDetail = reactive({
  visible: false,
  title: '图片详情',
  imgUrl: ''
});
const panoramaDetail = reactive({
  visible: false,
  title: '图片详情',
  imgUrl: ''
});

// 时钟参数
const clockParams = reactive({
  startTime: null, //开始时间
  stopTime: null, //结束时间
  currentTime: null, //当前时间
  clockRange: Cesium.ClockRange.LOOP_STOP,
  multiplier: 1 // 倍速
});

// 播放是否暂停
const clockIsStoped = ref(true);

// 倍速选项
const multiplierOptions = [
  { label: '0.5倍速', value: 0.5 },
  { label: '1倍速', value: 1 },
  { label: '1.5倍速', value: 1.5 },
  { label: '2倍速', value: 2 }
];

// 视场锥体
let frustum = null;
let frustumOption = null;

// 视频文件列表
const videoList = ref([]);
const v_videoList = computed(() => videoList.value.filter(item => item.file_name.includes('_V')));
const t_videoList = computed(() => videoList.value.filter(item => item.file_name.includes('_T')));
const isShowVedio = ref(false);
// const isShowVedio_V = ref(false);
// const isShowVedio_T = ref(false);
const activeVedioName = ref('高清');
const videoRefs = ref(null);

function handleRouteParams(query) {
  // 获取媒体文件,任务基本信息
  if (query.job_id) {
    getRecordInfo(query.job_id, query.flight_id);
    getWaylinesTaskDetail(query.job_id);
    getVideos(query.job_id);
    // getFlightRecord(query.job_id);
  }

  // 填充飞手名称
  // if (query.drone_name) {
  //   flyNickname.value = query.drone_name;
  // }
}

function getRecordInfo(jobId, flight_id) {
  getRecordByid(jobId).then(data => {
    // console.log(data);
    if (data.driving_id !== undefined && data.driving_id !== '') {
      if (mainMapWidgetRef.value) {
        mainMapWidgetRef.value.contorlAirPortArea(false);
      }
    }
    trackInfo.startTime = data.start_time;
    trackInfo.endTime = data.end_time;
    trackInfo.flyDistance = data.distance_show;
    trackInfo.flyTime = data.duration_show;
    // 如果有机场名称则更新信息头的前缀
    if (data.dock_name) {
      flyNickname.value = `${data.dock_name} - ` + `${data.drone_name}`;
    } else {
      flyNickname.value = `${data.drone_name}`;
    }
    // 获取飞行记录轨迹
    if (flight_id) {
      getRecordTrack(flight_id);
    }
  });
}

/**
 * 获取媒体文件
 */
function getVideos(jobId) {
  // 优先获取视频文件列表，需要进行联动
  getMediaFiles({
    page: 1,
    page_size: 1000,
    job_id: jobId,
    file_type: 2
  }).then(data => {
    const { list, pagination } = data;
    if (list.length > 0) {
      // 移出手飞'_S'的录屏视频
      const filterList = list.filter(file => !file.file_name.includes('_S'));
      filterList.sort((a, b) => {
        const dateA = new Date(a.create_time);
        const dateB = new Date(b.create_time);
        return dateA - dateB;
      });
      filterList.forEach(item => {
        videoList.value.push(item);
      });
      // 排序
      videoList.value.sort((a, b) => {
        const hasV_A = a.file_name.includes('_T');
        const hasV_B = b.file_name.includes('_T');
        // 如果名字包含 "_T" 的项优先
        if (hasV_A && !hasV_B) return -1;
        if (!hasV_A && hasV_B) return 1;

        // 如果都包含或都不包含 "_T"，则保持原顺序
        return 0;
      });
      // activeVedioName.value = videoList.value[0].file_id;
      videoList.value.forEach((item, index) => {
        // item.showLabel = extractSecondUnderscoreData(item.file_name.replace('.mp4', ''));
        item.refIndex = index;
        item.videoLoaded = false;
        item.nowPlyayTime = 0;
        item.isShow = false;
        // item.playStatus = 'pause';
      });
      // console.log(videoList.value);
      if (v_videoList.value.length === 0) {
        activeVedioName.value = '红外';
      }
    }
    // 延迟获取照片列表
    setTimeout(() => {
      getPhotos(jobId);
    }, 2000);
  });
}

/**
 * 获取图片文件列表
 */
function getPhotos(jobId) {
  // 获取照片文件列表
  getMediaFiles({
    page: 1,
    page_size: 1000,
    job_id: jobId
    // file_type: 1
  }).then(data => {
    const { list, pagination } = data;
    if (videoList.value.length > 0) {
      videoList.value.forEach(item => {
        mediaList.value.push(item);
      });
    }
    if (list.length > 0) {
      list.forEach(item => {
        // 除去视频文件
        if (item.file_type !== '2') {
          mediaList.value.push(item);
        }
      });
      mediaList.value.sort((a, b) => {
        const dateA = new Date(a.create_time);
        const dateB = new Date(b.create_time);
        return dateA - dateB;
      });
    }
    // 判断是否有媒体资源，则进行
    if (mediaList.value.length > 0) {
      showFullScreen.value = false;
    }
  });
}

/**
 * 获取机场任务详情
 */
function getWaylinesTaskDetail(jobId) {
  fetchWaylinesTaskDetail(jobId).then(res => {
    // console.log(res);
    if (res === undefined) {
      return;
    }
    // 获取航线
    if (res.file_id) {
      getWaylines({
        order_by: 'update_time desc',
        wayline_id: res.file_id
      }).then(data => {
        const { list, pagination } = data;
        if (list.length > 0) {
          //Todo 绘制航线
          drawWayline(list[0]);
        }
      });
    }
    //if (res.dock_sn) {
    //flyNickname.value = `${res.dock_name} - ` + flyNickname.value;
    // getDevicesBySn(res.dock_sn).then(dock => {
    //   getDevicesBySn(dock.child_device_sn).then(nav => {
    //     flyNickname.value = `${nav.nickname} - ${dock.nickname}`;
    //   });
    // });
    //}
  });
}

/**
 * 更改选项卡中视屏名称
 * @param {*} str
 */
function extractSecondUnderscoreData(str) {
  if (!str || typeof str !== 'string') {
    return '';
  }

  const parts = str.split('_');
  if (parts.length <= 2) {
    return '';
  }

  let newStr = parts.slice(2).join('_');
  newStr = newStr.replace('V', '高清');
  newStr = newStr.replace('T', '红外');
  return newStr;
}

/**
 * 打开图片详情弹窗
 */
const openImgDetail = item => {
  if (item.file_type === '3') {
    panoramaDetail.visible = true;
    panoramaDetail.title = item.file_name;
    panoramaDetail.imgUrl = item.file_url;
  } else {
    imgDetail.visible = true;
    imgDetail.title = item.file_name;
    imgDetail.imgUrl = item.file_url;
  }
};

/**
 * 绘制航线
 */
function drawWayline(waylineInfo) {
  setTimeout(() => {
    const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
    if (waylineInfo.template_types[0] === 0) {
      const planJson = JSON.parse(waylineInfo.airline_json);
      let wayline = new Wayline(flyView, planJson);
      wayline.createWayLine();
    } else if (waylineInfo.template_types[0] === 1) {
      const planJson = JSON.parse(waylineInfo.airline_json);
      addWaylineOnMap(flyView, {
        id: waylineInfo.id,
        airline_json: planJson
      });
    }
  }, 1500);
}

/**
 * 获取飞行记录
 */
// function getFlightRecord(job_id) {
//   getFlightRecordList({
//     job_id: job_id,
//     page: 1,
//     page_size: 10
//   }).then(res => {
//     console.log('飞行记录', res);
//     const { list } = res;
//     let dataList = list || [];
//     if (dataList.length > 0) {
//       trackInfo.flyDistance = dataList[0].distance_show;
//       trackInfo.flyTime = dataList[0].duration_show;
//     }
//   });
// }

/**
 * 获取飞行记录轨迹
 */
function getRecordTrack(flight_id) {
  fetchRecordTrack(flight_id).then(res => {
    // console.log('飞行轨迹', res);
    trackList.value = res || [];
    if (trackList.value.length > 0) {
      trackList.value.forEach((item, index) => {
        const existingRecord = flyRecords.value.find(record => record.flyTime === item.report_time);
        if (!existingRecord) {
          let theflyItem = JSON.parse(item.report_data);
          if (theflyItem.latitude === 0 || theflyItem.longitude === 0) {
            return;
          }
          flyRecords.value.push({
            flyTime: item.report_time,
            flyItem: theflyItem
          });
        }
      });
      flyRecords.value.sort((a, b) => {
        const dateA = new Date(a.flyTime);
        const dateB = new Date(b.flyTime);
        return dateA - dateB;
      });

      // console.log('轨迹列表', flyRecords.value);
      // trackInfo.startTime = trackList.value[0].report_time;
      // trackInfo.endTime = trackList.value[trackList.value.length - 1].report_time;
      trackInfo.onflyTime = calculateTimeDifference(
        flyRecords.value[0].flyTime,
        flyRecords.value[flyRecords.value.length - 1].flyTime,
        false
      );
      timingInfo.value = "0'00''/" + trackInfo.onflyTime.replace("'", '分').replace("''", '秒');
      maxSliderTimeValue.value = flyRecords.value.length - 1;

      setTimeout(() => {
        setNavModel();
      }, 1000);
    }
  });
}

let property = new Cesium.SampledPositionProperty();

/**
 * 设置无人机
 */
function setNavModel() {
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  // 取样位置 相当于一个集合

  // 取样偏航角
  let hprProperty = new Cesium.SampledProperty(Cesium.Quaternion);
  let startTime = Cesium.JulianDate.fromDate(new Date(flyRecords.value[0].flyTime));
  let stopTime = Cesium.JulianDate.fromDate(new Date(flyRecords.value[flyRecords.value.length - 1].flyTime));
  clockParams.startTime = startTime;
  clockParams.stopTime = stopTime;
  for (let i = 0; i < flyRecords.value.length; i++) {
    const nowFlyItem = flyRecords.value[i].flyItem;
    const nowPosition = new Cesium.Cartesian3.fromDegrees(nowFlyItem.longitude, nowFlyItem.latitude, nowFlyItem.height);
    let nowFlyTime = Cesium.JulianDate.fromDate(new Date(flyRecords.value[i].flyTime));
    // 添加位置，和时间对应
    property.addSample(nowFlyTime, nowPosition);
    let hpr = new Cesium.HeadingPitchRoll(
      Cesium.Math.toRadians(nowFlyItem.attitudeHead),
      Cesium.Math.toRadians(nowFlyItem.attitudePitch),
      Cesium.Math.toRadians(nowFlyItem.attitudeRoll)
    );
    // 添加偏航角,和时间对应
    let qua = Cesium.Transforms.headingPitchRollQuaternion(nowPosition, hpr);
    hprProperty.addSample(nowFlyTime, qua);
  }

  if (!navModel) {
    navModel = flyView.entities.add({
      id: 'nav',
      availability: new Cesium.TimeIntervalCollection([
        new Cesium.TimeInterval({
          start: clockParams.startTime,
          stop: clockParams.stopTime
        })
      ]),
      position: property,
      orientation: hprProperty,
      model: {
        uri: airModel,
        scale: 0.1,
        silhouetteColor: Cesium.Color.BLUE.withAlpha(0.9),
        silhouetteSize: 2
      }
    });
    // 飞过的航线
    flyView.entities.add({
      id: 'runedWayline',
      polyline: {
        positions: new Cesium.CallbackProperty(function (time, result) {
          let allPositions = [];
          for (let i = 0; i < selectedTime.value; i++) {
            const nowFlyItem = flyRecords.value[i].flyItem;
            const nowPosition = new Cesium.Cartesian3.fromDegrees(
              nowFlyItem.longitude,
              nowFlyItem.latitude,
              nowFlyItem.height
            );
            allPositions.push(nowPosition);
          }
          if (navModel !== null) {
            const cartesian = navModel.position._property.getValue(flyView.clock.currentTime);
            if (cartesian !== undefined) {
              allPositions.push(cartesian);
            }
          }
          allPositions = removeDuplicates(allPositions);
          return allPositions.reverse();
        }, false),
        width: 4,
        material: Cesium.Color.fromCssColorString('#F2FF3B')
      }
    });
  }
  navModel.viewFrom = new Cesium.Cartesian3(0, -1000, 1000);
  setTimeout(() => {
    flyView.trackedEntity = navModel;
    clockParams.currentTime = clockParams.startTime;
    flyView.clock.startTime = clockParams.startTime; //开始时间
    flyView.clock.stopTime = clockParams.stopTime; //结束时间
    flyView.clock.currentTime = clockParams.currentTime; //当前时间
    flyView.clock.clockRange = Cesium.ClockRange.LOOP_STOP;
    flyView.clock.multiplier = clockParams.multiplier;
    // 添加事件监听器
    flyView.clock.onTick.addEventListener(onClockTick);
  }, 200);
}

// 去重函数
function removeDuplicates(positions) {
  const uniquePositions = []; // 存储去重后的坐标数组
  const seen = new Set(); // 记录已经见过的坐标

  positions.forEach(pos => {
    // 生成一个唯一的键
    const key = `${pos.x},${pos.y},${pos.z}`;
    if (!seen.has(key)) {
      // 如果 key 未出现过，则添加到 seen 中，并将 pos 添加到 uniquePositions 中
      seen.add(key);
      uniquePositions.push(pos);
    }
  });

  return uniquePositions;
}

/**
 * 监听时钟时间更新相关数据
 */
function onClockTick() {
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  // if (Cesium.JulianDate.equals(currentTime, flyView.clock.stopTime)) {
  //   flyView.clock.shouldAnimate = false; // 暂停时钟
  // }
  var currentTime = DateTimeFormatter(flyView.clock.currentTime);
  let nowTimeDate = new Date(currentTime.replace(/-/g, '/'));
  let selectedIndex = flyRecords.value.findIndex(record => {
    // 将record.flyTime字符串转换为Date对象
    let recordTimeDate = new Date(record.flyTime.replace(/-/g, '/'));
    // 比较两个Date对象是否相等（精确到秒）
    return recordTimeDate.getTime() === nowTimeDate.getTime();
  });

  if (selectedIndex === -1) {
    return [];
  }
  // 更新时间轴
  selectedTime.value = selectedIndex;
  // const spendTime = calculateTimeDifference(trackInfo.startTime, flyRecords.value[selectedTime.value].flyTime, false);
  const spendTime = calculateTimeDifference(
    flyRecords.value[0].flyTime,
    flyRecords.value[selectedTime.value].flyTime,
    false
  );
  timingInfo.value = spendTime + '/' + trackInfo.onflyTime.replace("'", '分').replace("''", '秒');
  // 更新左上角数据
  const flyItem = flyRecords.value[selectedIndex].flyItem;
  const str = '--';
  osdInfo.capacity_percent = flyItem?.battery.capacityPercent ?? str;
  osdInfo.height = flyItem?.height.toFixed(2) ?? str;
  osdInfo.horizontal_speed = flyItem?.horizontalSpeed.toFixed(2) ?? str;
  osdInfo.vertical_speed = flyItem?.verticalSpeed.toFixed(2) ?? str;
  osdInfo.gps_number = flyItem?.positionState.gpsNumber ?? str;
  osdInfo.rtk_number = flyItem?.positionState.rtkNumber ?? str;
  osdInfo.home_distance = flyItem?.homeDistance.toFixed(2) ?? str;

  // 偏航轴角度与真北角（经线）的角度，0到6点钟方向为正值，6到12点钟方向为负值。
  let degrees = -Number(flyItem.attitudeHead.toFixed(2));
  osdInfo.heading = -degrees + '°';
  osdInfo.transform = 'rotate(' + degrees + 'deg)';

  // AGL 地面以上的高度（height-terrainheight）
  let navPosition = Cesium.Cartesian3.fromDegrees(flyItem.longitude, flyItem.latitude, 0);
  getTerrainHeight(flyView, navPosition).then(obj => {
    osdInfo.aglHeight = Number(flyItem.height - obj.terrainHeight).toFixed(2);
  });

  const nowPosition = new Cesium.Cartesian3.fromDegrees(flyItem.longitude, flyItem.latitude, flyItem.height);

  // 云台俯仰角
  if (flyItem.payloads !== undefined) {
    const nowGimbalPitch = flyItem.payloads[0]?.gimbalPitch ?? undefined;
    if (nowGimbalPitch !== undefined) {
      osdInfo.gimbalPitch = nowGimbalPitch.toFixed(2) + '°';
      const pitchRatioTimes100 = (nowGimbalPitch / 90) * 100;
      let gimbalPitchPercent = nowGimbalPitch >= 0 ? 50 - pitchRatioTimes100 : Math.abs(pitchRatioTimes100);
      osdInfo.gimbalPitchPercent = `${gimbalPitchPercent.toFixed(2)}%`;

      // 创建视锥体
      frustumOption = {
        viewer: flyView,
        position: nowPosition,
        heading: -degrees,
        roll: nowGimbalPitch - 90
      };
    }
  }

  // 播放暂停视频
  followVideo(currentTime);
}
function updateFrustum() {
  ani.start(() => {
    if (frustumOption !== null) {
      if (frustum === null) {
        frustum = new CreateFrustum(frustumOption);
      } else {
        const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
        const entity = flyView.entities.getById('nav');
        const cartesian = entity.position._property.getValue(flyView.clock.currentTime);
        frustumOption.position = cartesian;
        frustum.update(frustumOption);
      }
    }
  });
}

// 切换时间节点
function changeTimeNode() {
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  let nowTime = flyRecords.value[selectedTime.value].flyTime;
  let nowJulianTime = Cesium.JulianDate.fromDate(new Date(nowTime));
  // const spendTime = calculateTimeDifference(trackInfo.startTime, flyRecords.value[selectedTime.value].flyTime, false);
  const spendTime = calculateTimeDifference(
    flyRecords.value[0].flyTime,
    flyRecords.value[selectedTime.value].flyTime,
    false
  );
  timingInfo.value = spendTime + '/' + trackInfo.onflyTime.replace("'", '分').replace("'", '秒');
  flyView.clock.currentTime = nowJulianTime;

  // 暂停时钟情况下更新视锥体
  if (!clockIsStoped.value) {
    ani.resume();
    updateFrustum();
    setTimeout(() => {
      ani.pause();
    }, 200);
  }

  // 播放暂停视频
  followVideo(nowTime);
}

// 跟随视频
function followVideo(nowTime) {
  // 播放暂停视频
  if (videoList.value.length > 0) {
    // 不在时段内的视频数量
    let noInTimeVideoCount = 0;
    videoList.value.forEach(oneVideo => {
      if (oneVideo.videoLoaded) {
        const videoBeginTime = new Date(oneVideo.create_time);
        const videoEndTime = new Date(oneVideo.end_time);
        const nowCurrentTime = new Date(nowTime);
        if (nowCurrentTime >= videoBeginTime && nowCurrentTime <= videoEndTime) {
          // const diffTime = Math.floor((nowCurrentTime - videoBeginTime) / 1000);
          const diffTime = (nowCurrentTime - videoBeginTime) / 1000;
          // console.log('diffTime1----', diffTime);
          // 大于则关闭
          // if (diffTime >= oneVideo.videoDuration - 2) {
          if (diffTime >= oneVideo.videoDuration) {
            // if (isShowVedio.value) {
            //   isShowVedio.value = false;
            // }
            if (oneVideo.isShow) {
              oneVideo.isShow = false;
            }
          }
          // 其余则播放
          else {
            // 如果暂停状态则暂停播放
            if (!clockIsStoped.value) {
              videoRefs.value[oneVideo.refIndex].togglePause();
            }
            if (oneVideo.nowPlyayTime !== diffTime) {
              if (clockIsStoped.value) {
                videoRefs.value[oneVideo.refIndex].seekTo(diffTime);
                videoRefs.value[oneVideo.refIndex].togglePlay();
                oneVideo.nowPlyayTime = diffTime;
                if (!oneVideo.isShow) {
                  oneVideo.isShow = true;
                }
              }
              if (!isShowVedio.value) {
                isShowVedio.value = true;
              }
            }
          }
        } else {
          if (oneVideo.isShow) {
            oneVideo.isShow = false;
          }
          noInTimeVideoCount++;
        }
      }
    });
    // 当视频处于播放状态，时间轴快进或倒退，全部视频均不处于播放时间段则暂停播放
    if (isShowVedio.value && noInTimeVideoCount === videoList.value.length) {
      isShowVedio.value = false;
    }

    // 判断该时段内是否有高清或者红外视频，有则显示，无则隐藏
    // if (
    //   v_videoList.value.filter(oneVideo => oneVideo.isShow).length > 0 &&
    //   t_videoList.value.filter(oneVideo => oneVideo.isShow).length === 0
    // ) {
    //   activeVedioName.value = '高清';
    // }
    // if (
    //   v_videoList.value.filter(oneVideo => oneVideo.isShow).length === 0 &&
    //   t_videoList.value.filter(oneVideo => oneVideo.isShow).length > 0
    // ) {
    //   if (activeVedioName.value === '高清') {
    //     activeVedioName.value = '红外';
    //   }
    // }
    // isShowVedio_V.value = v_videoList.value.filter(oneVideo => oneVideo.isShow).length > 0;
    // isShowVedio_T.value = t_videoList.value.filter(oneVideo => oneVideo.isShow).length > 0;
  }
}

// 展示时间轴提示语
function clockTooltip(val) {
  let timeVal = '0';
  if (flyRecords.value.length > 0) {
    timeVal = flyRecords.value[val].flyTime;
  }
  return `当前时间: ${timeVal}`;
}

// 切换倍速
function changeMultiplier(val) {
  clockParams.multiplier = val;
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  flyView.clock.multiplier = clockParams.multiplier;
}

// 时钟是否暂停
function setClockPlayer() {
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  if (clockIsStoped.value) {
    flyView.clock.multiplier = clockParams.multiplier;
    clockIsStoped.value = false;
    ani.pause();
  } else {
    clockIsStoped.value = true;
    ani.resume();
  }
  flyView.clock.shouldAnimate = clockIsStoped.value;
  // 更新视频播放暂停
  let nowTime = flyRecords.value[selectedTime.value].flyTime;
  followVideo(nowTime);
}

// 时间格式化
function DateTimeFormatter(datetime) {
  var julianDT = new Cesium.JulianDate();
  Cesium.JulianDate.addHours(datetime, 8, julianDT);
  var gregorianDT = Cesium.JulianDate.toGregorianDate(julianDT);
  var objDT;
  objDT = new Date(gregorianDT.year, gregorianDT.month - 1, gregorianDT.day);
  objDT = gregorianDT.year + '-' + gregorianDT.month.toString().padStart(2, '0') + '-' + gregorianDT.day + ' ';
  return (
    objDT +
    `${gregorianDT.hour.toString().padStart(2, '0')}:${gregorianDT.minute
      .toString()
      .padStart(2, '0')}:${gregorianDT.second.toString().padStart(2, '0')}`
  );
}

// 计算时间差
function calculateTimeDifference(date1, date2, isEn) {
  // 将日期字符串转换为Date对象
  let startDate = new Date(date1);
  let endDate = new Date(date2);

  // 确保startDate是较早的日期，endDate是较晚的日期
  if (startDate > endDate) {
    [startDate, endDate] = [endDate, startDate];
  }
  // 计算时间差，单位为毫秒
  const timeDifferenceMs = endDate.getTime() - startDate.getTime();
  // 转换为秒
  const seconds = Math.floor(timeDifferenceMs / 1000);
  // 转换为分钟
  const minutes = Math.floor(seconds / 60);
  // 转换为小时
  const hours = Math.floor(minutes / 60);
  // 转换为天数
  const days = Math.floor(hours / 24);
  let result = '';
  if (days > 0) {
    result += `${days}天`;
  }
  if (hours > 0 || (days > 0 && hours === 0)) {
    // 确保小时数在天数大于0时总是显示
    result += `${hours % 24}小时`;
  }
  if (minutes > 0 || (hours > 0 && minutes === 0) || days > 0) {
    // 同样确保分钟数在小时数大于0或天数大于0时显示
    result += `${minutes % 60}分`;
  }
  result += `${seconds % 60}秒`;

  if (isEn) {
    result = result.replace('天', 'd').replace('小时', ':').replace('分钟', "'").replace('秒', "''");
  }
  return result;
}

function tabVedioChange(name) {
  activeVedioName.value = name;
}

function handleMetadataLoaded(videoOption) {
  // 设置视频加载成功
  if (videoList.value.length > 0) {
    videoList.value.forEach(item => {
      if (item.file_id === videoOption.videoId) {
        item.videoDuration = videoOption.videoDuration;
        item.videoLoaded = true;

        const createTime = new Date(item.create_time);
        if (!isNaN(createTime.getTime())) {
          item.end_time = new Date(createTime.getTime() + videoOption.videoDuration * 1000);
        }
      }
    });
  }
}

const dealAirport = async item => {
  getDevicesBound({
    domain: DOMAIN.DOCK,
    page: 1,
    page_size: 10000
  }).then(data => {
    const { list, pagination } = data;
    if (list.length > 0) {
      list.forEach(item => {
        if (item.max_flight_distance !== undefined && item.max_flight_distance > 0) {
          setTimeout(() => {
            loadAirPortArea(item);
          }, 200);
        }
      });
    }
  });
};

// 加载机场覆盖范围
const loadAirPortArea = item => {
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  const targetPoint = {
    lon: item.longitude || 0,
    lat: item.latitude || 0,
    height: item.altitude || 0
  };
  const center = turf.point([targetPoint.lon, targetPoint.lat]); // 中心点坐标
  const options = { steps: 64, units: 'meters' }; // 设置步数以控制精度，单位为米
  const radius = item.max_flight_distance; // 半径
  const circleFeature = turf.circle(center, radius, options);
  // 将Turf的坐标转换为Cesium的Cartesian3坐标
  const positions = circleFeature.geometry.coordinates[0].map(coord => {
    return Cesium.Cartesian3.fromDegrees(coord[0], coord[1], 40);
  });
  // 闭合环状
  positions.push(positions[0]);
  const airPortAreaLyr = flyView.dataSources.getByName('airPortAreaLyr')[0];
  // 添加到机场覆盖图层中
  airPortAreaLyr.entities.add({
    id: `airPortArea_` + item.device_sn,
    polygon: {
      hierarchy: new Cesium.PolygonHierarchy(positions),
      material: Cesium.Color.fromCssColorString('#2E90FA').withAlpha(0.3), // 设置填充颜色
      outline: true,
      outlineColor: Cesium.Color.fromCssColorString('#2E90FA') // 设置轮廓颜色
    }
  });
};

const quitBack = () => {
  router.back();
};

onMounted(() => {
  // 获取当前路由的 fullPath 或 path
  const currentPath = router.currentRoute.value.fullPath;
  // 判断 URL 是否包含 "-no-header"
  if (currentPath.includes('-no-header')) {
    showReturnLeft.value = false;
  }
  const currentQuery = router.currentRoute.value.query;
  handleRouteParams(currentQuery);
  updateFrustum();
  dealAirport();
});

// 确保在组件销毁时清理资源
onUnmounted(() => {
  clearInterval(intervalTimer);

  // 尝试清理地图相关资源
  try {
    const flyView = getCesiumEngineInstance('homeMap-fly');
    if (flyView && flyView.clock) {
      flyView.clock.onTick.removeEventListener(onClockTick);
    }

    // 清理模型和其他实体
    if (flyView && flyView.entities) {
      if (navModel) {
        flyView.entities.remove(navModel);
        navModel = null;
      }
      const runedWayline = flyView.entities.getById('runedWayline');
      if (runedWayline) {
        flyView.entities.remove(runedWayline);
      }
    }
  } catch (error) {
    console.error('Error cleaning up map resources:', error);
  }
});

onBeforeUnmount(() => {
  // 移除时间监听器，以避免内存泄漏
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  flyView.clock.onTick.removeEventListener(onClockTick);
});
</script>

<style lang="scss" scoped>
.historyfly-container {
  height: 100%;
  width: 100%;
  display: flex;
}
.map-100 {
  height: 100%;
  width: calc(100% - 318px);
  position: relative;
  &.is-active {
    width: 100%;
  }
}

.media-box {
  height: 100%;
  width: 318px;
  background: #001129;
  .media-title {
    height: 38px;
    width: 100%;
    background: #11253e;
    position: relative;
    font-family: SourceHanSansSC-Bold;
    font-size: 14px;
    color: #ffffff;
    text-align: left;
    line-height: 38px;
    font-weight: 700;
    border-bottom: 1px solid #616161;
  }
  .media-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(to bottom, #001129, #11253e);
  }
  .media-content {
    height: calc(100% - 38px);
    width: 100%;
    overflow-y: auto;

    .media-item {
      margin-bottom: 20px;
      align-items: center;
      background: #11253e;
      border-radius: 4px;
      cursor: pointer;
      .media-item-top {
        position: relative;
        .video-play-view {
          position: absolute;
          top: 0;
          right: 0;
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          .video-play-btn {
            cursor: pointer;
            margin: auto;
          }
        }

        .left-img {
          padding: 10px;
          width: 100%;
          height: 180px;
        }
        .left-img img {
          width: 100%;
          max-height: 180px;
        }
        .vedio-img {
          padding: 10px;
          opacity: 0.7;
          width: 100%;
          height: 180px;
          object-fit: cover;
        }
      }

      .media-item-bottom {
        padding: 4px 16px;
        line-height: 20px;
        color: #98a2b3;
        font-size: 16px;

        .name {
          padding: 8px 0;
          line-height: 18px;
          font-weight: 700;
          color: #fff;
          font-size: 16px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .info-div {
          .item-info {
            display: flex;
            align-items: center;
            line-height: 22px;
            font-size: 12px;
            padding-bottom: 6px;

            img {
              margin-right: 4px;
              width: 16px;
              height: 16px;
            }
          }
        }
      }
    }
  }
}

.backToFlight {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  cursor: pointer;
}
.backspan {
  font-size: 32px;
  font-weight: bold;
  color: #2e90fa;
}
.footer {
  position: absolute;
  bottom: 10px;
  left: 5px;
  color: #fff;
  // background: linear-gradient(180deg, transparent, rgba(0, 0, 0, 0.5));
  background: #11253e;
  height: 40px;
  width: calc(100% - 60px);
  flex-shrink: 0;
  z-index: 1;
  font-weight: 400;
  display: flex;
  .play {
    display: flex;
    width: 86px;
    height: 100%;
    margin-left: 10px;
  }
  .timeline {
    width: calc(100% - 276px);
    height: 100%;
  }
  .timeinfo {
    display: flex;
    width: 140px;
    height: 100%;
    line-height: 40px;
  }
  .start {
    margin: 4px;
    width: 36px;
    height: 36px;
    background-image: url('@/assets/gis/track_play.png');
    cursor: pointer;
    &.is-active {
      background-image: url('@/assets/gis/track_stop.png');
    }
  }
}

.slider-demo-block {
  display: flex;
  align-items: center;
  width: 98%;
  margin-right: 1%;
  margin-left: 1%;
}

.slider-demo-block .el-slider {
  margin-top: 0;
  margin-left: 12px;
}
.footer-item {
  margin: 5px 30px 0 5px;
  .label {
    font-size: 18px;
    line-height: 26px;
  }
  .value {
    font-size: 20px;
    line-height: 28px;
  }
}
.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.toper {
  position: absolute;
  top: 0px;
  left: 0px;
  color: #fff;
  background: #001129;
  height: 500px;
  width: 320px;
  z-index: 10;
  pointer-events: auto;
  font-weight: 400;
  .toper-title {
    height: 38px;
    width: 100%;
    background: #11253e;
    position: relative;
    .nick {
      color: #f5f6f8;
      text-align: left;
      font-weight: 400;
      line-height: 42px;
    }
  }
  .toper-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(to bottom, #001129, #11253e);
  }
  .toper-content {
    height: 462px;
    width: 100%;
    .c-row {
      font-family: SourceHanSansSC-Bold;
      font-size: 14px;
      color: #e4e7ec;
      line-height: 24px;
      margin: 10px 0px;
    }
    .c-title {
      font-weight: 800;
      text-align: center;
      font-family: SourceHanSansSC-Bold;
      font-size: 14px;
      color: #e4e7ec;
      line-height: 22px;
      font-weight: 700;
    }
    .c-text {
      font-weight: 400;
      text-align: center;
      font-family: SourceHanSansSC-Regular;
      font-size: 14px;
      color: #e4e7ec;
      line-height: 22px;
      font-weight: 400;
    }
    .c-compass {
      padding-top: 10px;
      margin: 10px 16px;
      width: 288px;
      height: 190px;
      background: #11253e;
      .compass-item {
        padding-top: 25px;
        padding-left: 40px;
      }
    }
  }
}

.vedio-box {
  position: absolute;
  top: 500px;
  left: 0px;
  color: #fff;
  background: #001129;
  width: 320px;
  //height: calc(100% - 560px);
  height: 325px;
  border-top: 2px solid #11253e;
  .vedio-content {
    width: 100%;
    height: 100%;
    &.is-active {
      background: url('@/assets/empty.png') no-repeat center center;
      z-index: 999;
    }
  }
  .video-div {
    height: 275px;
    width: 100%;
    &.is-active {
      background: url('@/assets/empty.png') no-repeat center center;
      z-index: 999;
    }
  }
  .video-div-none {
    height: 275px;
    width: 100%;
    background: url('@/assets/empty.png') no-repeat center center;
    z-index: 999;
  }
  .video-info {
    height: 275px;
    width: 100%;
  }
  .video-info button {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
  }
}
.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}
.operation-earth {
  right: 50px;
  top: 220px;
  position: absolute;
}
.gimbal-pitch {
  margin-left: -15px;
  margin-top: -30px;
}

::v-deep .el-slider__button {
  width: 12px;
  height: 12px;
}
::v-deep .el-slider__bar {
  height: 4px;
}
::v-deep .el-slider__runway {
  height: 4px;
  background-color: #001129;
}

::v-deep .el-tabs__nav .el-tabs__item {
  padding-left: 10px !important;
}

.return-left {
  align-items: center;
  line-height: 50px;
  margin-left: 3px;
  &:hover {
    cursor: pointer;
  }
}
</style>
<style lang="scss" scoped>
@import '@/styles/plan/plan.scss';
</style>
