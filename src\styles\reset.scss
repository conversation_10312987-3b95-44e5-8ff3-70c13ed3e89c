*,
::before,
::after {
  margin: 0;
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: currentColor;
}

#app {
  width: 100%;
  height: 100%;
}

html {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

body {
  margin: 0;
  line-height: inherit;
  width: 100%;
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizelegibility;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

a {
  color: inherit;
  text-decoration: inherit;
}

img,
svg {
  display: inline-block;
}
svg {
  vertical-align: -0.15em; //因icon大小被设置为和字体大小一致，而span等标签的下边缘会和字体的基线对齐，故需设置一个往下的偏移比例，来纠正视觉上的未对齐效果
}

ul,
li {
  margin: 0;
  padding: 0;
  list-style: none;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

a:focus,
a:active,
div:focus {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}
