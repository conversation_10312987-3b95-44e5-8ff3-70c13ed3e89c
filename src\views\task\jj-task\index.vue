<!--接处警任务-->
<script>
export default {
  name: 'JJTask'
};
</script>

<script setup>
import { onBeforeUnmount, reactive } from 'vue';
import { toRaw } from '@vue/reactivity';
import DetailDialog from './DetailDialog.vue';
import EditDialog from './EditDialog.vue';
import {
  getJJTaskPage,
  deleteJJTask,
  executeAlarm,
  searchAlreadyRelevance,
  searchRelevanceList,
  linkAlarmTask
} from '@/api/task';
import optionData from '@/utils/option-data';
import { COMMON_COLOR } from '@/utils/constants';
import { getErrorMessage } from '@/utils/errorCode';
import { ElMessage } from 'element-plus';
import { authorityShow } from '@/utils/authority';

const editDialogRef = ref(null);

const loading = ref(false);
const total = ref(0);

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10
});
const searchParams = reactive({
  page_num: 1,
  page_size: 9999
});
const flyVisible = ref(false);
const alarmId = ref('');
const dataList = ref([]);
const dialog = reactive({
  visible: false
});
const editDialog = reactive({
  visible: false
});
const relevanceValue = ref([]);
const relevanceData = ref([]);
const showData = ref([]);
let formData = reactive({});
let detailData = reactive({});

/**
 * 查询
 */
function handleQuery(params = {}) {
  getJJTaskPage({
    ...queryParams,
    ...params,
    page: queryParams.pageNum,
    page_size: queryParams.pageSize
  }).then(data => {
    const { list, pagination } = data;
    dataList.value = list || [];
    total.value = pagination.total;
  });
}

/**
 * 重置查询
 */
function resetQuery(type = '') {
  queryParams.alarm_id = '';
  queryParams.alarm_name = '';
  queryParams.pageNum = 1;
  queryParams.pageSize = 10;
  queryParams.rangTime = '';
  queryParams.begin_time = '';
  queryParams.end_time = '';
  handleQuery(queryParams);
}

//查看任务
function openDialog(row) {
  dialog.visible = true;
  Object.keys(detailData).map(key => {
    delete detailData[key];
  });
  if (row) {
    Object.assign(detailData, { ...row });
  }
}

function initAlreadyRelevance() {
  searchAlreadyRelevance(
    {
      alarm_id: alarmId.value
    },
    {}
  ).then(data => {
    relevanceValue.value = data?.map(res => res.flight_task_id) || [];
  });
}

function openTaskDialog(row) {
  flyVisible.value = true;
  alarmId.value = toRaw(row).alarm_id;
  searchAirList(searchParams);
  initAlreadyRelevance();
}

function openEditDialog(row) {
  editDialog.title = '接处警任务配置';
  editDialog.visible = true;
}
/**
 * 删除
 */
function handleDelete(row) {
  ElMessageBox.confirm(`确认后将删除此任务及相关的历史执行记录，且无法进行恢复`, '确认删除所选任务？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    autofocus: false,
    closeOnClickModal: false,
    type: 'warning'
  }).then(() => {
    deleteJJTask(row.id).then(data => {
      ElMessage.success('删除成功');
      handleQuery();
    });
  });
}
function toExe(row) {
  executeAlarm(row.alarm_id).then(data => {
    ElMessage.success('出警成功');
    handleQuery();
  });
}
function getCodeMessage(code) {
  return getErrorMessage(code) + `（code: ${code}）`;
}

onUnmounted(() => {
  window.$bus.off('refreshJJTask');
});
onMounted(() => {
  handleQuery();
  window.$bus.on('refreshJJTask', () => {
    resetQuery();
  });
});

function handleClose() {
  flyVisible.value = false;
  relevanceValue.value = [];
  relevanceData.value = [];
}

function saveRelevance() {
  linkAlarmTask(
    {
      alarm_id: alarmId.value
    },
    {
      alarm_id: alarmId.value,
      task_ids: relevanceValue.value
    }
  ).then(data => {
    ElMessage.success('操作成功');
    handleQuery();
    handleClose();
  });
}

function handleSeek () {
  const params = {
    ...queryParams,
    begin_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[0]} 00:00:00` : '',
    end_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[1]} 23:59:59` : '',
    pageNum: 1
  };
  queryParams.pageNum = 1
  delete params.rangTime;
  handleQuery(params);
}

function handleSearch() {
  // Calculate the new total pages based on the new page size
  const newTotalPages = Math.ceil(total.value / queryParams.pageSize);
  
  // If current page is greater than new total pages, adjust to the last page
  if (queryParams.pageNum > newTotalPages) {
    queryParams.pageNum = newTotalPages || 1;
  }
  const params = {
    ...queryParams,
    begin_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[0]} 00:00:00` : '',
    end_time: queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[1]} 23:59:59` : '',
    pageNum: 1
  };
  delete params.rangTime;
  handleQuery(params);
}

/**
 * 查询
 */
function searchAirList(params) {
  searchRelevanceList(
    {
      alarm_id: 'alarm_id'
    },
    {
      ...params
    }
  ).then(data => {
    relevanceData.value = data || [];
    showData.value = data || [];
  });
}

function inputValue(value) {
  if (Array.isArray(value)) {
    relevanceValue.value = value;
  }
}

function filterMethod(value, row) {
  const beginTime = value.time.length >0 ? `${value.time[0]} 00:00:00` : ''
  const endTime = value.time.length >0 ? `${value.time[1]} 23:59:59` : ''
  if (value.job_type !== '' && value.keywords !== '' && beginTime !== '' && endTime !== '') {
    // 筛选三种类型
    return row.name.indexOf(value.keywords) > -1 && row?.job_type == value.job_type && row?.execute_time < endTime && row?.execute_time > beginTime;
  }
  if(value.job_type !== '' && value.keywords !== '' && beginTime === '' && endTime === '') {
    // 筛选任务类型以及关键字
    return row.name.indexOf(value.keywords) > -1 && row?.job_type == value.job_type;
  }
  if(value.job_type !== '' && value.keywords === '' && beginTime !== '' && endTime !== '') {
    // 筛选任务类型以及时间
    return row?.job_type == value.job_type && row?.job_type == value.job_type && row?.execute_time < endTime && row?.execute_time > beginTime;
  }
  if(value.job_type === '' && value.keywords !== '' && beginTime !== '' && endTime !== '') {
    // 任务类型和时间
    return row.name.indexOf(value.keywords) > -1 && row?.job_type == value.job_type && row?.execute_time < endTime && row?.execute_time > beginTime;
  }
  if (value.job_type === '' && value.keywords !== '' && beginTime === '' && endTime === '') {
    // 筛选关键字
    return row.name.indexOf(value.keywords) > -1;
  }
  if (value.job_type === '' && value.keywords === '' && beginTime !== '' && endTime !== '') {
    // 筛选时间
    return row?.execute_time < endTime && row?.execute_time > beginTime;
  }
  if ((value.job_type === 0 || value.job_type === 1 || value.job_type === 2) && value.keywords === '' && beginTime === '' && endTime === '') {
    // 筛选任务类型
    return row?.job_type == value.job_type;
  }
  if (value.job_type === '' && value.keywords === '' && beginTime === '' && endTime === '') {
    // 全部
    return row
  }
  // return row.name.indexOf(value.keywords) > -1 && row?.job_type == value.job_type
}
</script>

<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
          <el-form-item label="" prop="alarm_id">
            <el-input
              class="input-serach"
              v-model="queryParams.alarm_id"
              placeholder="请输入警情ID"
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
            />
          </el-form-item>
          <el-form-item label="" prop="alarm_name">
            <el-input
              class="input-serach"
              v-model="queryParams.alarm_name"
              placeholder="请输入警情描述"
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
            />
          </el-form-item>
          <el-form-item label="" prop="rangTime">
            <el-date-picker
              class="input-serach"
              v-model="queryParams.rangTime"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @change="handleSearch"
            />
          </el-form-item>
        </el-form>
      </div>
      <div class="search-btn">
        <el-button type="primary" @click="handleSeek()"><i-ep-search />搜索</el-button>
        <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
      </div>
    </div>
    <el-card shadow="never">
      <template #header v-if="authorityShow('settingJJTask')">
        <el-button type="primary" @click="openEditDialog()">接处警任务配置</el-button>
      </template>
      <el-table highlight-current-row :data="dataList" v-loading="loading" stripe height="540">
        <el-table-column label="序号" align="center" width="80">
          <template #default="scope">
            <span>{{ scope.$index + 1 + queryParams.pageSize * (queryParams.pageNum - 1) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="警情ID" prop="alarm_id" show-overflow-tooltip />
        <el-table-column label="警情描述" prop="alarm_name" width="550" show-overflow-tooltip />
        <el-table-column label="经纬度" width="280" prop="latitude" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ scope.row?.longitude }},{{ scope.row?.latitude }}</span>
          </template>
        </el-table-column>
        <el-table-column label="警情级别" width="80" prop="alarm_level_name" show-overflow-tooltip />
        <el-table-column label="警情接收时间" width="180" prop="receipt_time" show-overflow-tooltip />
        <!-- <el-table-column label="当前状态" prop="current_status" show-overflow-tooltip>
          <template #default="scope">
            <div class="flex-center">
              <div class="status" :style="{ backgroundColor: COMMON_COLOR[scope.row.current_status] }"></div>

              <span>{{ optionData.jjStatusList.find(item => item.value === scope.row.current_status).label }}</span>

              <el-tooltip
                v-if="scope.row.current_status == 5"
                :content="scope.row.error_code ? getCodeMessage(scope.row.error_code) : scope.row.remark"
                placement="top"
              >
                <i-ep-warning-filled style="color: #ff9900; margin-left: 5px" />
              </el-tooltip>
            </div>
          </template>
        </el-table-column> -->
        <el-table-column label="是否关联任务" width="140" prop="receipt_time" show-overflow-tooltip >
          <template #default="scope">
            <span v-if="scope.row.task_count !== 0">已关联</span>
            <span v-else>未关联</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="center" width="250" v-if="authorityShow('linkJJTask')">
          <template #default="scope">
            <!-- <el-button type="primary" link @click.stop="toExe(scope.row)" v-if="scope.row?.current_status == '1'"
              >出警</el-button
            > -->
            <!-- <el-button type="primary" link @click.stop="openDialog(scope.row)" v-if="scope.row?.job_id">查看</el-button> -->
            <el-button type="primary" link @click.stop="openTaskDialog(scope.row)">关联飞行任务</el-button>
            <!-- <el-button type="danger" link @click.stop="handleDelete(scope.row)">删除</el-button> -->
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleSearch"
      />
    </el-card>

    <div v-if="editDialog.visible">
      <EditDialog
        ref="editDialogRef"
        v-model:visible="editDialog.visible"
        :title="editDialog.title"
        @submit="resetQuery"
      />
    </div>

    <div v-if="dialog.visible">
      <DetailDialog v-model:visible="dialog.visible" :form-data="detailData" />
    </div>

    <div v-if="flyVisible">
      <el-dialog v-model="flyVisible" title="关联飞行任务" width="1500" :before-close="handleClose" :close-on-click-modal="false">
        <table-transfer
          :data="relevanceData"
          :value="relevanceValue"
          filterable
          :filter-method="filterMethod"
          rowKey="flight_task_id"
          :titles="['待选飞行任务', '已选飞行任务']"
          @input="inputValue"
        >
          <template v-slot:table>
            <el-table-column label="任务类型" prop="job_type_desc" width="100"></el-table-column>
            <el-table-column label="任务名称" prop="name" show-overflow-tooltip></el-table-column>
            <el-table-column label="执行时间" prop="execute_time" width="160"></el-table-column>
          </template>
        </table-transfer>
        <div class="fly-bottom">
          <el-button type="primary" style="margin-right: 10px" @click="saveRelevance">保存</el-button>
          <el-button @click="handleClose">取消</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<style scoped lang="scss">
.input-serach {
  width: 200px;
}
.fly-bottom {
  margin-top: 40px;
  text-align: center;
}
.flex-center {
  display: flex;
  align-items: center;
  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
}
:deep(.el-transfer-panel) {
  width: 40%;
}
:deep(.el-transfer-panel) {
  width: 40%;
}
.search {
  display: flex;
  align-items: center;
  padding: 24px;
  height: 64px;
  .search-form {
    flex: 1;
    padding-top: 16px;
  }
}
</style>
