import * as Cesium from 'cesium';
import { getHeading, getPitch } from './orientation';
import { getArea, getArea2 } from './turfUtils';
import { generateKey } from '@/utils';
/**
 * 飞到
 * @param {*} viewer 视图
 * @param {*} lon 经度
 * @param {*} lat 维度
 * @param {*} height 高度
 * @param {*} heading
 * @param {*} pitch
 * @param {*} range
 * @param {*} duration 时间间隔
 */
export function flyTo(viewer, lon, lat, height, heading, pitch, range, duration) {
  const center = toCartesian3([lon, lat, height]);
  const h = Cesium.Math.toRadians(heading);
  const p = Cesium.Math.toRadians(pitch);
  const r = range;
  console.log(center);
  viewer.camera.flyToBoundingSphere(new Cesium.BoundingSphere(center), {
    offset: new Cesium.HeadingPitchRange(h, p, r),
    duration: duration
  });
}

/**
 * 让相机看向目标点并且设置相机距离目标点位置 和相较于目标点高度的偏移量
 * @param {*} viewer
 * @param {*} options
 * options = {
 * lon, lat, h, 经纬度高程
 * offsetH,相较于目标点高度的偏移量  默认 100 比如目标点高度40 这里设置100 则相机位置为140 从上往下观察
 * distance 相机距离目标点位置 默认1500
 * }
 */
export function setCameraLookAt(viewer, options) {
  return new Promise((resolve, reject) => {
    const { lon, lat, height, offsetH, distance } = options;
    const offset = offsetH || 100;
    const distanceToTarget = distance || 1500;
    const h = height || 0;
    // 看向的目标点
    const targetPositon = Cesium.Cartesian3.fromDegrees(lon, lat, h);
    // 设置相机的高度
    const lookPoi = Cesium.Cartesian3.fromDegrees(lon, lat, h + offset);
    // const newCameraPosition = getNorthPointByDistance(lookPoi, distanceToTarget);
    const newCameraPosition = getSouthPointByDistance(lookPoi, distanceToTarget);
    // 计算方位角
    let pitch = getPitch(newCameraPosition, targetPositon);
    let heading = getHeading(newCameraPosition, targetPositon);
    // 将相机的位置和视角调整到目标点
    viewer.camera.flyTo({
      destination: newCameraPosition,
      orientation: {
        heading: heading, // CesiumMath.toRadians(0),// heading,
        pitch: pitch,
        roll: 0
      },
      duration: 0.75,
      complete: resolve, // 当飞行动画完成时解决 Promise
      cancel: reject // 如果飞行动画被取消则拒绝 Promise
    });
  });
}

function getNorthPointByDistance(position, distance) {
  //以点为原点建立局部坐标系（东方向为x轴,北方向为y轴,垂直于地面为z轴），得到一个局部坐标到世界坐标转换的变换矩阵
  var localToWorld_Matrix = Cesium.Transforms.eastNorthUpToFixedFrame(position);
  return Cesium.Matrix4.multiplyByPoint(
    localToWorld_Matrix,
    Cesium.Cartesian3.fromElements(0, distance, 0),
    new Cesium.Cartesian3()
  );
}

function getSouthPointByDistance(position, distance) {
  //以点为原点建立局部坐标系（东方向为x轴,北方向为y轴,垂直于地面为z轴），得到一个局部坐标到世界坐标转换的变换矩阵
  var localToWorld_Matrix = Cesium.Transforms.eastNorthUpToFixedFrame(position);
  return Cesium.Matrix4.multiplyByPoint(
    localToWorld_Matrix,
    Cesium.Cartesian3.fromElements(0, -distance, 0),
    new Cesium.Cartesian3()
  );
}

//#region 动画
// 播放全部动画
export function startAnimation(model, speedMultiplier = 10.0) {
  if (model) {
    model.activeAnimations.removeAll(); // 清除之前的动画
    model.activeAnimations.addAll({
      multiplier: speedMultiplier || 10,
      loop: Cesium.ModelAnimationLoop.REPEAT
    });
  }
}
// 停止全部动画
export function stopAnimation(model) {
  if (model) {
    model?.activeAnimations.removeAll(); // 清除所有动画
  }
}
//#endregion

//#region 数据转换

/**
 * 将 坐标点数组转为 cesium坐标
 * @param {*} coordinates  [[longitude1, latitude1, height1], [longitude2, latitude2, height2]....];
 * @returns
 */
export function convertToCartesian3ArrayHeights(coordinates) {
  // 使用 flat() 将嵌套数组展平
  const flatCoordinates = coordinates.flat();
  // 使用 fromDegreesArray 将经纬度转换为 Cartesian3
  return Cesium.Cartesian3.fromDegreesArrayHeights(flatCoordinates);
}
/**
 * 将 坐标点数组转为 cesium坐标
 * @param {*} coordinates  [[longitude1, latitude1], [longitude2, latitude2]....];
 * @returns
 */
export function convertToCartesian3Array(coordinates) {
  // 使用 flat() 将嵌套数组展平
  const flatCoordinates = coordinates.flat();
  // 使用 fromDegreesArray 将经纬度转换为 Cartesian3
  return Cesium.Cartesian3.fromDegreesArray(flatCoordinates);
}

//#endregion
/**
 * 创建实体点对象
 * @param {*} coordinate   [longitude, latitude, height] 或者[longitude, latitude]
 * @returns
 */
export function createPointEntity(coordinate) {
  let position;
  if (coordinate instanceof Array) {
    // 如果是坐标数组，将其转换为 Cartesian3
    position = toCartesian3(coordinate);
  } else if (coordinate instanceof Cesium.Cartesian3) {
    // 如果已经是 Cartesian3，直接使用
    position = coordinate;
  } else {
    throw new Error('Invalid coordinate type. Expected an array or Cartesian3.');
  }
  const pointEntity = new Cesium.Entity({
    position: position,
    point: {
      pixelSize: 6,
      color: Cesium.Color.RED
    }
  });
  return pointEntity;
}

//#region 插值加密

/**
 * 正常直线段插值加密
 * @param {*} coordinates [[x,y,z]]
 * @param {*} number 加密点数 100
 * @returns
 */
export function calculateLinearSplinePoints(coordinates, number = 100) {
  // 创建控制点
  let controls = [];
  coordinates?.forEach(point => {
    const ep = toCartesian3(point);
    controls.push(ep);
  });

  // 2 创建创建LinearSpline对象
  let times = [];
  let section = parseFloat(1 / (controls.length - 1));
  for (let i = 0; i < controls.length; i++) {
    times.push((section * i).toFixed(2));
  }
  // 这里times.length must be equal to points.length.
  let spline = new Cesium.LinearSpline({
    times: times,
    points: controls
  });
  // 3、插值100个点
  let interpolatedPoints = [];
  for (let i = 0; i <= number; i++) {
    let cartesian3 = spline.evaluate(i / number);
    interpolatedPoints.push(cartesian3);
  }
  return interpolatedPoints;
}

/**
 * 微曲线段插值加密
 * @param {*} coordinates [[x,y,z]]
 * @param {*} number 加密点数 100
 * @returns
 */
export function calculateHermiteSplinePoints(coordinates, number = 100) {
  // 创建控制点
  let controls = [];
  coordinates?.forEach(point => {
    const ep = toCartesian3(point);
    controls.push(ep);
  });

  // 2 创建创建LinearSpline对象
  let times = [];
  let section = parseFloat(1 / (controls.length - 1));
  for (let i = 0; i < controls.length; i++) {
    times.push((section * i).toFixed(2));
  }
  // 这里times.length must be equal to points.length.
  let spline = new Cesium.CatmullRomSpline({
    times: times,
    points: controls
  });
  // 3、插值100个点
  let interpolatedPoints = [];
  for (let i = 0; i <= number; i++) {
    let cartesian3 = spline.evaluate(i / number);
    interpolatedPoints.push(cartesian3);
    // this.viewer.entities.add({
    //   position: cartesian3,
    //   point: {
    //     pixelSize: 15, //点的大小
    //     color: Cesium.Color.BLUE, //点的颜色
    //     outlineWidth: 2, //边框宽度
    //     outlineColor: Cesium.Color.WHITE.withAlpha(0.4), //边框颜色
    //   },
    //   show: true,
    // });
  }
  return interpolatedPoints;
}
//#endregion

/**
 * 起止点插值算法 返回 返回插值的坐标点数组  计算经纬度无高程
 * @param {*} startPoi
 * @param {*} endPoi
 * @param {*} spacing 间距 单位mi
 * @returns
 */
export function interpolatePoints2D(startPoi, endPoi, spacing = 0.5) {
  const startCartographic = Cesium.Cartographic.fromDegrees(startPoi[0], startPoi[1], startPoi[2]);
  const endCartographic = Cesium.Cartographic.fromDegrees(endPoi[0], endPoi[1], endPoi[2]);
  const startCartesian = Cesium.Cartesian3.fromRadians(
    startCartographic.longitude,
    startCartographic.latitude,
    startCartographic.height
  );
  const endCartesian = Cesium.Cartesian3.fromRadians(
    endCartographic.longitude,
    endCartographic.latitude,
    endCartographic.height
  );
  const distance = Cesium.Cartesian3.distance(startCartesian, endCartesian);
  const numberOfPoints = Math.ceil(distance / spacing);
  const interpolationPoints = [];
  for (let i = 0; i <= numberOfPoints; i++) {
    const t = i / numberOfPoints;
    const interpolatedCartesian = Cesium.Cartesian3.lerp(startCartesian, endCartesian, t, new Cesium.Cartesian3());
    const interpolatedCartographic = Cesium.Cartographic.fromCartesian(interpolatedCartesian);
    const interpolatedLongitude = Cesium.Math.toDegrees(interpolatedCartographic.longitude);
    const interpolatedLatitude = Cesium.Math.toDegrees(interpolatedCartographic.latitude);
    interpolationPoints.push({
      longitude: interpolatedLongitude,
      latitude: interpolatedLatitude
    });
  }
  return {
    points: interpolationPoints,
    count: interpolationPoints.length || 0
  };
}
/**
 * 起止点插值算法 返回 返回插值的坐标点数组  有高程
 * @param {*} startPoi [x,y,z]
 * @param {*} endPoi [x,y,z]
 * @param {*} spacing 间距 单位mi
 * @returns
 */
export function interpolatePoints(startPoi, endPoi, spacing = 0.5) {
  const startCartesian = toCartesian3(startPoi);
  const endCartesian = toCartesian3(endPoi);
  const distance = parseFloat(Cesium.Cartesian3.distance(startCartesian, endCartesian).toFixed(2));
  const numberOfPoints = Math.ceil(distance / spacing);
  const interpolationPoints = [];
  for (let i = 0; i <= numberOfPoints; i++) {
    const t = i / numberOfPoints;
    const interpolatedCartesian = Cesium.Cartesian3.lerp(startCartesian, endCartesian, t, new Cesium.Cartesian3());
    const interpolatedCartographic = Cesium.Cartographic.fromCartesian(interpolatedCartesian);
    const interpolatedLongitude = Cesium.Math.toDegrees(interpolatedCartographic.longitude);
    const interpolatedLatitude = Cesium.Math.toDegrees(interpolatedCartographic.latitude);
    const interpolatedHeight = interpolatedCartographic.height;
    const p = toCartesian3([interpolatedLongitude, interpolatedLatitude, interpolatedHeight]);
    interpolationPoints.push(p);
  }
  return {
    points: interpolationPoints,
    count: interpolationPoints.length || 0
  };
}

// 添加图片图标
export function addImageIcon(viewer, coordinate, iconPath) {
  if (!viewer) {
    return;
  }
  let position;
  if (coordinate instanceof Array) {
    // 如果是坐标数组，将其转换为 Cartesian3
    position = toCartesian3(coordinate);
  } else if (coordinate instanceof Cesium.Cartesian3) {
    // 如果已经是 Cartesian3，直接使用
    position = coordinate;
  }
  const billboardEntity = new Cesium.Entity({
    position: position,
    billboard: {
      image: iconPath,
      width: 25, // default: undefined
      height: 25, // default: undefined
      show: true, // default
      eyeOffset: new Cesium.Cartesian3(0.0, 0.0, 0.0), // default
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER, // default
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM // default: CENTER
    }
  });
  viewer.entities.add(billboardEntity);
  return billboardEntity;
}

// 添加点
export function addMarkPoint(viewer, coordinate, size = 5) {
  if (!viewer) {
    return;
  }
  let position;
  if (coordinate instanceof Array) {
    // 如果是坐标数组，将其转换为 Cartesian3
    position = toCartesian3(coordinate);
  } else if (coordinate instanceof Cesium.Cartesian3) {
    // 如果已经是 Cartesian3，直接使用
    position = coordinate;
  }
  const p = new Cesium.Entity({
    // fromDegrees（经度，纬度，高度，椭球，结果）从以度为单位的经度和纬度值返回Cartesian3位置
    position: position,
    point: {
      // 点的大小（像素）
      pixelSize: size || 5,
      // 点位颜色，fromCssColorString 可以直接使用CSS颜色
      color: Cesium.Color.fromCssColorString('#ee0000'),
      // 边框颜色
      outlineColor: Cesium.Color.fromCssColorString('#fff'),
      // 边框宽度(像素)
      outlineWidth: 2,
      // 显示在距相机的距离处的属性，多少区间内是可以显示的
      // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 1500),
      // 是否显示
      show: true
    }
  });
  viewer.entities.add(p);
  return p;
}

/**
 * 生成圆上的点 生成目标点的 环绕圈的点集合
 * @param {*} center [x,y,z] 经纬度高程
 * @param {*} radius 环绕半径 米
 * @returns 返回圆的点集合
 */
export function calculateCirclePoints(center, radius) {
  // 将中心点的经纬度转换为笛卡尔坐标
  const centerCartesian = toCartesian3(center);
  // 创建一个以中心点为圆心、以半径为radius的圆的几何体
  const circleGeometry = new Cesium.CircleOutlineGeometry({
    center: centerCartesian,
    radius: radius
  });
  const geometry = Cesium.CircleOutlineGeometry.createGeometry(circleGeometry);
  // 提取圆的顶点
  const circlePositions = geometry.attributes.position.values;
  // const circlePoints = extractCoordinates(circlePositions);
  // 将笛卡尔坐标转换为经纬度并添加到结果集合中
  const circlePoints = [];
  for (let i = 0; i < circlePositions.length; i += 3) {
    const cartesian = new Cesium.Cartesian3(circlePositions[i], circlePositions[i + 1], circlePositions[i + 2]);
    const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
    const longitude = Cesium.Math.toDegrees(cartographic.longitude);
    const latitude = Cesium.Math.toDegrees(cartographic.latitude);
    const height = center[2];
    circlePoints.push([longitude, latitude, height]);
  }
  return circlePoints;
}

/**
 * 封装方法，计算线上距离终点指定距离的点的经纬度和高程
 * @param {*} start [x,y,z]
 * @param {*} end [x,y,z]
 * @param {*} distanceFromEnd 100 米
 * @returns
 */
export function pointAlongLineToEnd(start, end, distanceFromEnd = 100) {
  let startLongitude = start[0];
  let startLatitude = start[1];
  let startHeight = start[2] || 0.0;
  let endLongitude = end[0];
  let endLatitude = end[1];
  let endHeight = end[2] || 0.0;
  // 将经纬度转换为笛卡尔坐标
  const startPoint = toCartesian3([startLongitude, startLatitude, startHeight]);
  const endPoint = toCartesian3([endLongitude, endLatitude, endHeight]);

  // 计算线段的总距离
  let totalDistance = Cesium.Cartesian3.distance(startPoint, endPoint);
  // 计算比例
  let ratio = distanceFromEnd / totalDistance;
  // 使用插值计算目标点的笛卡尔坐标
  let targetPoint = Cesium.Cartesian3.lerp(endPoint, startPoint, ratio, new Cesium.Cartesian3());

  // 将目标点的笛卡尔坐标转换为经纬度
  let cartographic = Cesium.Cartographic.fromCartesian(targetPoint);
  let targetLatitude = Cesium.Math.toDegrees(cartographic.latitude);
  let targetLongitude = Cesium.Math.toDegrees(cartographic.longitude);
  let targetHeight = cartographic.height || 0.0;
  const p = toCartesian3([targetLongitude, targetLatitude, targetHeight]);
  return {
    x: targetLongitude,
    y: targetLatitude,
    z: targetHeight,
    target: p
  };
}

export function distancePoints(start, end) {
  let startLongitude = start[0];
  let startLatitude = start[1];
  let startHeight = start[2] || 0.0;
  let endLongitude = end[0];
  let endLatitude = end[1];
  let endHeight = end[2] || 0.0;
  // 将经纬度转换为笛卡尔坐标
  let startPoint = toCartesian3([startLongitude, startLatitude, startHeight]);
  let endPoint = toCartesian3([endLongitude, endLatitude, endHeight]);
  // 计算线段的总距离
  let totalDistance = Cesium.Cartesian3.distance(startPoint, endPoint);
  return totalDistance;
}

// 提取坐标点
export function extractCoordinates(positions) {
  const coordinates = [];
  for (let i = 0; i < positions.length; i += 3) {
    const longitude = Cesium.Math.toDegrees(positions[i]);
    const latitude = Cesium.Math.toDegrees(positions[i + 1]);
    const elevation = positions[i + 2];
    coordinates.push([longitude, latitude, elevation]);
  }

  return coordinates;
}

// 找到最近的点

/**
 *
 * @param {*} point  Cartesian3
 * @param {*} pointList Cartesian3 集合
 * @returns
 */
export function findNearestPoint(sp, pointList) {
  // 计算点A与集合B中每个点的距离，并找到最近的点
  let point = toCartesian3(sp);
  let nearestPoint = pointList[0];
  let firstP = toCartesian3(nearestPoint);
  let nearestDistanceSquared = Cesium.Cartesian3.distanceSquared(point, firstP);
  for (let i = 1; i < pointList.length; i++) {
    const p = toCartesian3(pointList[i]);
    const distanceSquared = Cesium.Cartesian3.distanceSquared(point, p);
    if (distanceSquared < nearestDistanceSquared) {
      nearestPoint = pointList[i];
      nearestDistanceSquared = distanceSquared;
    }
  }
  return nearestPoint;
}

/**
 * 坐标转换
 * @param {*} coordinate [x,y,z ] 或者 Cesium.Cartesian3 坐标对象
 * @returns Cesium.Cartesian3 坐标对象
 */
export function toCartesian3(coordinate) {
  let position = null;
  if (coordinate instanceof Array) {
    position = Cesium.Cartesian3.fromDegrees(coordinate[0], coordinate[1], coordinate[2] || 0.0);
  } else if (coordinate instanceof Cesium.Cartesian3) {
    position = coordinate;
  }
  return position;
}

/**
 * 坐标转换 将对二维数组进行转换
 * @param {*} coordinates [ [x,y,z ], [x,y,z ]...]
 * @returns Cesium.Cartesian3 坐标对象数组
 */
export function arrayToCartesian3(coordinates) {
  // 检查coordinates是否为非空二维数组
  if (!Array.isArray(coordinates) || coordinates.length === 0) {
    return null; // 或者你可以抛出一个错误，因为这不是预期的输入
  }
  const dim = getArrayDimension(coordinates);
  if (dim === 1) {
    return toCartesian3(coordinates);
  } else if (dim === 2) {
    let arr = [];
    // 将坐标转换为Cesium.Cartesian3对象数组
    coordinates.forEach(coord => {
      arr.push(toCartesian3(coord));
    });
    return arr;
  }
  return null;
}

/**
 * 坐标转换
 * @param {*} coordinate [x,y  ] 或者 Cesium.Cartesian2 坐标对象
 * @returns Cesium.Cartesian2坐标对象
 */
export function toCartesian2(coordinate) {
  let position = null;
  if (coordinate instanceof Array) {
    position = new Cesium.Cartesian2(coordinate[0], coordinate[1]);
  } else if (coordinate instanceof Cesium.Cartesian2) {
    position = coordinate;
  }
  return position;
}

/**
 * 坐标转换
 * @param {Cesium.Cartesian3} cartesian3 Cartesian3 对象
 * @returns Cesium.Cartesian2坐标对象
 */
export function c3toDegress(cartesian3) {
  const cartographic = Cesium.Cartographic.fromCartesian(cartesian3);
  const longitude = toNumber(Cesium.Math.toDegrees(cartographic.longitude), 7); // 经度
  const latitude = toNumber(Cesium.Math.toDegrees(cartographic.latitude), 7);
  const height = toNumber(cartographic.height, 3); // 高度
  return {
    longitude,
    latitude,
    height
  };
}
/**
 * 坐标转换
 * @param {Cesium.Cartesian3[]} cartesian3Arr Cartesian3 对象
 * @returns Cesium.Cartesian2坐标对象
 */
export function c3ArrToDegress(cartesian3Arr) {
  let list = [];
  (cartesian3Arr || []).forEach(cartesian3 => {
    const cartographic = Cesium.Cartographic.fromCartesian(cartesian3);
    const longitude = toNumber(Cesium.Math.toDegrees(cartographic.longitude), 7); // 经度
    const latitude = toNumber(Cesium.Math.toDegrees(cartographic.latitude), 7);
    const height = toNumber(cartographic.height, 3); // 高度
    list.push([longitude, latitude, height]);
  });
  return list;
}
/**
 * 坐标转换
 * @param {*} coordinate Cartesian3 对象
 * @returns 经纬度 坐标对象
 */
export function toDegrees(cartesian3Coordinate) {
  let position = null;
  if (cartesian3Coordinate instanceof Cesium.Cartesian3) {
    // 将 Cartesian3 转换为 Cartographic
    let cartographicCoordinate = Cesium.Cartographic.fromCartesian(cartesian3Coordinate);
    // 从 Cartographic 获取经纬度和高度
    let longitude = toNumber(Cesium.Math.toDegrees(cartographicCoordinate.longitude), 7);
    //  parseFloat(Cesium.Math.toDegrees(cartographicCoordinate.longitude).toFixed(7));
    let latitude = toNumber(Cesium.Math.toDegrees(cartographicCoordinate.latitude), 7); // parseFloat(Cesium.Math.toDegrees(cartographicCoordinate.latitude).toFixed(7));
    let height = toNumber(cartographicCoordinate.height, 3); // parseFloat(cartographicCoordinate.height.toFixed(3));
    position = [longitude, latitude, height];
  }
  return position;
}

/**
 * 坐标转换
 * @param {*} coordinate Cartesian3 对象
 * @returns 经纬度 坐标对象
 */
export function px2Catesian3(evtPoi, viewer) {
  try {
    if (!viewer || !evtPoi) {
      return null;
    }
    const cartesian3 = viewer.scene.globe.pick(viewer.camera.getPickRay(evtPoi), viewer.scene);
    return cartesian3;
  } catch (error) {
    return null;
  }
}
// 对圆形数组 重新排序

/**
 * 对坐标集合 B 重新排序，将离A点最近的集合中的点作为数组第一个点 下标排序为0
 * @param {*} pointA 用于计算起点的点坐标
 * @param {*} pointsB 点集合（针对圆形的点数组）
 * @returns 新排序的点数组
 */
export function reorderPoints(pointA, pointsB) {
  const nearestPoint = findNearestPoint(pointA, pointsB);
  // 找到最近的点在集合B中的索引
  const nearestPointIndex = pointsB.indexOf(nearestPoint);
  // 将集合B分割成两部分，分别是离最近点较近的点和离最近点较远的点
  const closerPoints = pointsB.slice(0, nearestPointIndex);
  const fartherPoints = pointsB.slice(nearestPointIndex + 1);
  // 重新排列点集合B，将离最近点较远的点放在集合B的前面，离最近点较近的点放在集合B的后面
  const reorderedPoints = [nearestPoint, ...fartherPoints, ...closerPoints];
  return reorderedPoints;
}
/**
 * 这里对圆形的数组进行插值加密
 * @param {*} circlepoints
 * @returns 返回加密的插值的数组  注意这里要将尾点和首个点连接（也就是数组要闭合）
 */
// 加密圆形点数组
export function circleInterpolation(circlepoints) {
  // 最后一个点和第一个点要闭合 这里重要
  const arrs = circlepoints;
  arrs.push(circlepoints[0]);
  const newPointsAll = [];
  for (let i = 0; i < arrs.length - 1; i++) {
    const point1 = arrs[i];
    const point2 = arrs[i + 1];
    const { points } = interpolatePoints(point1, point2, 2);
    const arr = calculateLinearSplinePoints([point1, point2], points.length);
    arr.forEach(p => {
      newPointsAll.push(p);
    });
  }
  return newPointsAll;
}

/**
 * 这里对圆形的数组进行插值加密
 * @param {*} pointArr 数组
 * @returns 返回加密的插值的数组  注意这里要将尾点和首个点连接（也就是数组要闭合）
 */
// 加密圆形点数组
export function interpolationDataArr(pointArr) {
  // 最后一个点和第一个点要闭合 这里重要
  const result = [];
  for (let i = 0; i < pointArr.length - 1; i++) {
    const point1 = pointArr[i];
    const point2 = pointArr[i + 1];
    const { points } = interpolatePoints(point1, point2, 2);
    const arr = calculateLinearSplinePoints([point1, point2], points.length);
    arr.forEach(p => {
      result.push(p);
    });
  }
  return result;
}

// 旋转变换
/**
 *
 * @param {*} position 位置 []
 * @param {*} headingPitchRoll HeadingPitchRoll
 */
export function HeadingPitchRollToQuaternion2(position, headingPitchRoll) {
  let p = toCartesian3(position);
  m = Cesium.Transforms.headingPitchRollToFixedFrame(
    position,
    headingPitchRoll,
    Cesium.Ellipsoid.WGS84,
    Cesium.Transforms.eastNorthUpToFixedFrame,
    new Cesium.Matrix4()
  );
  // 计算中心处的变换矩阵
  var m1 = Cesium.Transforms.eastNorthUpToFixedFrame(
    Cesium.Matrix4.getTranslation(m, new Cesium.Cartesian3()),
    Cesium.Ellipsoid.WGS84,
    new Cesium.Matrix4()
  );
  // 矩阵相除
  var m3 = Cesium.Matrix4.multiply(Cesium.Matrix4.inverse(m1, new Cesium.Matrix4()), m, new Cesium.Matrix4());
  // 得到旋转矩阵
  //var mat3 = Cesium.Matrix4.getMatrix3(m3, new Cesium.Matrix3());
  var mat3 = Cesium.Matrix4.getRotation(m3, new Cesium.Matrix3());
  // 计算四元数
  var q = Cesium.Quaternion.fromRotationMatrix(mat3);
  return q;
}

/**
 *
 * @param {*} matrix4 model.modelMatrix
 */
export function getHeadingPitchRollFromMatrix4(matrix4) {
  if (!matrix4) {
    return null;
  }
  let obj = Cesium.Matrix4.getRotation(matrix4, new Cesium.Matrix3());
  obj = Cesium.Quaternion.fromRotationMatrix(obj);
  obj = Cesium.HeadingPitchRoll.fromQuaternion(obj);
  // 获取 Heading、Pitch 和 Roll 的值
  return {
    heading: obj.heading,
    pitch: obj.pitch,
    roll: obj.roll
  };
}
/**
 * 设置 Cesium 相机视图
 * @param {Cesium.Viewer} viewer - Cesium 视图实例
 * @param {Object} options - 相机视图设置选项
 * @param {Cesium.Cartesian3} options.destination - 相机目标位置
 * @param {Cesium.HeadingPitchRoll} options.orientation - 相机朝向
 * @param {number} [options.duration] - 相机移动到目标位置的持续时间,单位为秒
 */
export function setCameraView(viewer, options) {
  if (!viewer) {
    return;
  }
  const { destination, orientation = { heading: 0, pitch: -90, roll: 0 }, duration = 2 } = options;
  viewer.camera.setView({
    destination: destination,
    orientation: {
      headingPitchRoll: orientation
    },
    duration: duration
  });
}

export function getCameraHeight(viewer) {
  return viewer.camera.positionCartographic.height;
}

/**
 * 坐标转换
 * @param {*} coordinate [x,y,z ] 或者 Cesium.Cartesian3 坐标对象
 * @returns Cesium.Cartesian3 坐标对象
 */
export function distance2target(viewer, target) {
  // 获取相机位置
  const cameraPosition = viewer.camera.position;
  // 假设有一个目标点的位置
  const targetPosition = toCartesian3(target);
  // 计算相机与目标点的距离
  const distance = Cesium.Cartesian3.distance(cameraPosition, targetPosition);
  const distanceWithTwoDecimals = parseFloat(distance.toFixed(2));
  return distanceWithTwoDecimals;
}

/**
 * 拾取屏幕像素位置的cesium要素，并判断是什么类型
 * @param x 像素横坐标
 * @param y 像素纵坐标
 * @returns {*}
 */
export function pickFeatureFromScreen(viewer, position) {
  // 存放拾取结果
  let resp = {
    pickResult: null
  };
  // 从像素坐标拾取对象
  let pickCartesian2 = toCartesian2(position);
  let feature = viewer.scene.pick(pickCartesian2);

  // 判断拾取结果
  if (Cesium.defined(feature)) {
    // feature.primitive.constructor.name 也可以获取类型
    resp.pickResult = feature; // 拾取结果
    if (feature.hasOwnProperty('id') && feature.id instanceof Cesium.Entity) {
      // 是entity: {collection, id, primitive}
      resp.type = 'Entity';
      resp.detailType = feature.primitive.constructor.name;
      resp.entity = feature.id;
      resp.id = feature.id.id;
    } else if (feature.primitive instanceof Cesium.Cesium3DTileset) {
      // 是3DTile: {content, primitive}
      resp.type = 'Cesium3DTileset';
    } else if (feature.primitive instanceof Cesium.Billboard) {
      // 是primitive-billboard: {collection, id, primitive}
      resp.type = 'Billboard';
      resp.id = feature.id;
      resp.billboardCollection = feature.collection;
      resp.billboard = feature.primitive;
    } else if (feature.primitive instanceof Cesium.Primitive) {
      // 是primitive: { primitive}
      resp.type = 'Primitive';
      resp.primitive = feature.primitive;
    } else if (feature.primitive instanceof Cesium.Model) {
      // 是mode
      resp.type = 'Primitive';
      resp.detailType = 'Model';
      resp.primitive = feature.primitive;
    }
  }
  return resp;
}

/**
 * 转为数字
 * @param {*} value
 * @param {*} precision
 * @returns
 */
export function toNumber(value = 0, precision = 2) {
  if (isNaN(value)) {
    return 0;
  }
  if (value === 0) {
    return 0;
  }
  // 将参数转换为数字类型，如果无法转换则抛出错误
  const numValue = Number(value);
  const numPrecision = Number(precision);
  if (isNaN(numValue) || isNaN(numPrecision)) {
    console.log('numValue:,numPrecision', numValue, numPrecision);
    throw new Error('Parameters must be convertible to numbers');
  }

  // 将 numValue 四舍五入到指定精度
  const multiplier = Math.pow(10, numPrecision);
  return Math.round(numValue * multiplier) / multiplier;
}

// // 示例用法
// let number = 3.14159;
// let roundedNumber = toNumber(number, 3); // 保留三位小数
// console.log(roundedNumber); // 输出 3.142

/**
 * 随机生成数字ID
 * @returns
 */
export const generateTemplateId = () => {
  return Math.floor(Math.random() * 10000) + 1;
};

/**
 * 获取数组维度
 * @param {*} arr
 * @returns 返回数组维度
 */
export function getArrayDimension(arr) {
  // 先判断是否为数组
  if (!Array.isArray(arr)) {
    return 'Input is not an array';
  }
  // 判断是一维还是二维数组
  if (arr.every(item => !Array.isArray(item))) {
    return 1;
  } else if (arr.every(item => Array.isArray(item))) {
    return 2;
  } else {
    return 3; //'Mixed Array';
  }
}

/**
 * 剔除掉数组中相同的元素
 * @param {*} arr
 * @returns
 */
export function removeDuplicates(arr) {
  const uniqueObjects = [];
  const seen = new Set();

  for (const obj of arr) {
    const key = JSON.stringify({ height: obj.height, lat: obj.lat, lng: obj.lng });
    if (!seen.has(key)) {
      seen.add(key);
      uniqueObjects.push(obj);
    }
  }

  return uniqueObjects;
}

/**
 * 两个对象值判断
 * @param {*} obj1
 * @param {*} obj2
 * @returns
 */
export function deepEqual(obj1, obj2) {
  // 深度比较两个对象是否相同
  if (typeof obj1 !== 'object' || typeof obj2 !== 'object') {
    return obj1 === obj2;
  }

  if (Object.keys(obj1).length !== Object.keys(obj2).length) {
    return false;
  }

  for (const key in obj1) {
    if (obj1.hasOwnProperty(key)) {
      if (!obj2.hasOwnProperty(key) || !deepEqual(obj1[key], obj2[key])) {
        return false;
      }
    }
  }

  return true;
}

/**
 *  根据经纬度获取最小最大经纬度
 * @param {*} coordinates 经纬度数组 [[x,y],[x,y]]
 * @returns
 */
// const coordinates = [
//   [118.0227853, 24.5676271, 120],
//   [118.0195461, 24.5716333, 120],
//   [118.0198327, 24.5699121, 120],
//   [118.0222917, 24.5705161, 120],
//   [118.0223904, 24.5721862, 120]
// ];
export function getMinMaxCenterLonglats(coordinates, margin = 0) {
  // 初始化变量
  let minX = Infinity,
    maxX = -Infinity,
    minY = Infinity,
    maxY = -Infinity;

  // 遍历坐标数组，找出X和Y的最大值和最小值
  for (let i = 0; i < coordinates.length; i++) {
    const [x, y] = coordinates[i];
    if (x < minX) minX = x;
    if (x > maxX) maxX = x;
    if (y < minY) minY = y;
    if (y > maxY) maxY = y;
  }
  // 使用这些值构建四个外接矩形的坐标
  let rectangle = [
    [minX, minY], // 左下角
    [minX, maxY], // 左上角
    [maxX, minY], // 右下角
    [maxX, maxY] // 右上角
  ];
  if (margin > 0) {
    margin = margin / 1000000;
    // 计算外扩后的四个角点坐标
    rectangle = [
      [minX - margin, minY - margin], // 左下角
      [minX - margin, maxY + margin], // 左上角
      [maxX + margin, minY - margin], // 右下角
      [maxX + margin, maxY + margin] // 右上角
    ];
  }
  // 针对 rectangle 计算中间点坐标经纬度
  const center = [(minX + maxX) / 2, (minY + maxY) / 2];
  return { rectangle: rectangle ?? [], center: center ?? [] };
}

/**
 *  根据经纬度获取最小最大经纬度
 * @param {*} viewer 视图对象
 * @param {*} coordinates 经纬度数组 [[x,y],[x,y]]
 * @param {*} margin 外扩距离 单位米 这里通过米和经纬度的转换构建外扩区域
 * @returns
 */
export function zoomtoLonglats(viewer = null, coordinates = [], margin = 1000) {
  try {
    if (!viewer || coordinates.length === 0) {
      return;
    }
    const { rectangle, center } = getMinMaxCenterLonglats(coordinates, margin);
    if (center.length === 0 || rectangle.length === 0) {
      return;
    }
    // 计算两个点之间的距离  角度为30度 高度为距离的2 倍
    const distance = Cesium.Cartesian3.distance(toCartesian3(rectangle[0]), toCartesian3(center));
    // 设置相机朝向
    const heading = Cesium.Math.toRadians(0); // 朝向北方
    const pitch = Cesium.Math.toRadians(-90); // 向下看
    const roll = Cesium.Math.toRadians(0); // 无翻滚
    // 设置相机视图
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(center[0], center[1], 3 * distance),
      orientation: {
        heading: heading,
        pitch: pitch,
        roll: roll
      }
    });
  } catch (error) {}
}

/**
 *  根据经纬度获取最小最大经纬度
 * @param {*} viewer 视图对象
 * @param {*} coordinates 经纬度数组 [[x,y],[x,y]]
 * @param {*} margin 外扩距离 单位米 这里通过米和经纬度的转换构建外扩区域
 * @returns
 */
export function zoomtoLonglat(viewer = null, coordinates = null, margin = 1000) {
  if (!viewer || coordinates.length < 2) {
    return;
  }

  // 设置相机视图
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(coordinates[0], coordinates[1], 3 * margin),
    orientation: {
      heading: Cesium.Math.toRadians(0), // 朝向北方
      pitch: Cesium.Math.toRadians(-90), // 向下看
      roll: Cesium.Math.toRadians(0) // 无翻滚
    }
  });
}

//#region 属性添加器
/**
 * 向 Cesium Entity 添加属性的方法
 * @param {Cesium.Entity} entity - 要添加属性的 Cesium Entity 对象
 * @param {Object} propertiesObj - 包含要添加属性的对象
 */
export function addPropertiesToEntity(entity = null, propertiesObj = null) {
  if (!entity || !propertiesObj) {
    return entity;
  }
  // 检查 entity 是否为 Cesium.Entity 类型
  if (!(entity instanceof Cesium.Entity)) {
    throw new Error('第一个参数必须是 Cesium.Entity 类型');
  }
  if ('{}' === JSON.stringify(propertiesObj)) {
    return entity;
  }
  // 创建一个新的 PropertyBag 对象
  let propertyBag = new Cesium.PropertyBag();
  // 遍历传入的对象，将属性添加到 PropertyBag 中
  for (let key in propertiesObj) {
    if (propertiesObj.hasOwnProperty(key)) {
      // 添加属性到 PropertyBag
      propertyBag.addProperty(key, propertiesObj[key]);
    }
  }
  // 将 PropertyBag 对象赋值给 entity 的 properties
  entity.properties = propertyBag;
}

// 这里写一个从properties 中获取特定key的方法
export function getPropertyByKey(entity = null, key = null) {
  if (!entity || !key) {
    return null;
  } else if (entity.properties && entity.properties[key]) {
    return entity.properties[key].getValue();
  }
  return null;
}

// // 使用示例
// var entity = new Cesium.Entity();
// var propertiesObj = {
//   objectid: entity.properties.objectid,
//   name: "Building Name",
//   floorNo: 3,
//   clickEvent: new Cesium.ClickEvent(Cesium用微信扫地码)
// };

// addPropertiesToEntity(entity, propertiesObj);
//#endregion

//#region 在正东方向距离的点

/**
 *
 * @param {*} center [x,y]
 * @param {*} distance
 * @returns
 */
export function calculatePointFromCenter(center = [], distance = 0) {
  if (center.length === 0 || distance === 0) {
    return null;
  }
  let degreeMeter = 101194; //经度1度=101194米,该值在不同的纬度上不同的值，可以根据自己的情况进行调整
  const degreeTemp = distance / degreeMeter;
  const circleBorderCartesian = Cesium.Cartesian3.fromDegrees(center[0] + degreeTemp, center[1], 0);
  return circleBorderCartesian;
}
//#endregion

//#region 根据C3 坐标组计算面积和周长
// 计算面积
export function calculatArea(c3Poistions = []) {
  try {
    if (c3Poistions.length < 3) {
      return 0;
    }
    let degList = c3ArrToDegress(c3Poistions);

    let degList2 = [];
    degList.forEach(element => {
      degList2.push([element[0], element[1]]);
    });
    let area = 0;
    // 判断点是否为空
    if (degList2.length >= 3) {
      // 第一个和最后一个合并
      degList2.push(degList2[0]);
      area = getArea2([degList2]);
      // const { area: newArea, unit } = getArea([degList2]);
      // area = newArea + ' ' + unit;
    }
    return area;
  } catch (error) {
    return 0;
  }
}
// 计算周长
export function calculatPerimeter(c3Poistions = []) {
  if (c3Poistions.length < 2) {
    return 0; // 如果点的数量少于2个，直接返回0
  }
  let totalDistance = 0;
  if (c3Poistions && c3Poistions.length > 0) {
    for (let i = 0; i < c3Poistions.length - 1; i++) {
      const point1 = c3Poistions[i];
      const point2 = c3Poistions[i + 1];
      totalDistance += Cesium.Cartesian3.distance(point1, point2);
    }
    if (c3Poistions.length > 2) {
      // 添加末尾点和第一个点的中间点
      const firstPoint = c3Poistions[0];
      const lastPoint = c3Poistions[c3Poistions.length - 1];
      totalDistance += Cesium.Cartesian3.distance(firstPoint, lastPoint);
    }
  }
  return totalDistance;
}

//#endregion

export function getPointsFromMap(map = null) {
  let midPositions = [];
  let endPositions = [];
  if (!map) {
    return {
      endPositions: [],
      midPositions: []
    };
  }
  // 执行创建中点点图标
  let points = Array.from(map.values()) || [];
  if (points && points.length > 0) {
    for (let i = 0; i < points.length - 1; i++) {
      let midPointInfoStruct = {
        type: 'MIDPOINT',
        previewPointIndex: 0,
        position: null,
        id: null
      };
      const point1 = points[i];
      const point2 = points[i + 1];
      const middlePoint = Cesium.Cartesian3.midpoint(point1.position, point2.position, new Cesium.Cartesian3());
      midPointInfoStruct.previewPointIndex = i;
      midPointInfoStruct.id = generateKey() || i;
      midPointInfoStruct.position = middlePoint;
      midPositions.push(midPointInfoStruct);
    }

    // 添加末尾点和第一个点的中间点
    const firstPoint = points[0];
    const lastPoint = points[points.length - 1];
    let lp = lastPoint.position;
    if (!lastPoint.position.x) {
      lp = lastPoint.position._value;
    }
    const firstLastMiddlePoint = Cesium.Cartesian3.midpoint(firstPoint.position, lp, new Cesium.Cartesian3());

    midPositions.push({
      type: 'MIDPOINT',
      previewPointIndex: points.length - 1,
      position: firstLastMiddlePoint
    });
  }

  // 提取线上的端点
  if (points && points.length > 0) {
    for (let i = 0; i < points.length; i++) {
      let endPointInfoStruct = {
        type: 'ENDPOINT',
        previewPointIndex: 0,
        position: null,
        id: null
      };
      const point1 = points[i];
      endPointInfoStruct.id = point1.id;
      endPointInfoStruct.previewPointIndex = i;
      endPointInfoStruct.position = point1.position;
      endPositions.push(endPointInfoStruct);
    }
  }

  return {
    endPositions,
    midPositions
  };
}

// 计算中心点 经纬度数组
export function getCenterCoordinates(points) {
  let sumLat = 0;
  let sumLon = 0;
  for (let i = 0; i < points.length; i++) {
    sumLat += points[i][1]; // 纬度
    sumLon += points[i][0]; // 经度
  }
  const avgLat = sumLat / points.length;
  const avgLon = sumLon / points.length;
  return [avgLon, avgLat]; // 返回中间点的经纬度，纬度在前
}

//#region 纬经度互换

/**
 * 纬经度 转 经纬度
 * @param {*} latlong
 * @returns
 */
export const latLong2Longlat = latlong => {
  if (!latlong || latlong.length < 2) {
    return null;
  }
  // 返回经纬度;
  return [latlong[1], latlong[0], latlong[2] ?? 0];
};
/**
 * 经纬度  转  纬经度
 * @param {*} longLat
 * @returns
 */
export const longLat2LatLong = longLat => {
  if (!longLat || longLat.length < 2) {
    return null;
  }
  // 返回经纬度;
  return [longLat[1], longLat[0], longLat[2] ?? 0];
};

/**
 * 经纬度 数组转字符串
 * @param {*} longLat
 * @returns
 */
export const arrTostring = longLat => {
  if (!longLat || longLat.length === 0) {
    return '';
  }
  let str = longLat.join(',');
  // 返回经纬度;
  return str;
};
//#endregion

//#region 检查是否是二维数组
export const is2DimensionalArray = arr => {
  // 如果数组为空，或者不是数组类型，则直接返回 false
  if (!Array.isArray(arr) || arr.length === 0) {
    return false;
  }

  // 检查数组中的每个元素是否为数组
  for (let i = 0; i < arr.length; i++) {
    // 如果任何一个元素不是数组，则返回 false
    if (!Array.isArray(arr[i])) {
      return false;
    }
  }

  // 所有元素都是数组，返回 true
  return true;
};

export const isThreeDimensionalArray = arr => {
  // 如果数组为空，或者不是数组类型，则直接返回 false
  if (!Array.isArray(arr) || arr.length === 0) {
    return false;
  }

  // 检查数组中的每个元素是否为数组
  for (let i = 0; i < arr.length; i++) {
    // 如果任何一个元素不是数组，则返回 false
    if (!Array.isArray(arr[i])) {
      return false;
    }
    // 检查子数组中的每个元素是否为数组
    for (let j = 0; j < arr[i].length; j++) {
      if (!Array.isArray(arr[i][j])) {
        return false;
      }
    }
  }

  // 所有元素都是数组，返回 true
  return true;
};

//#endregion
