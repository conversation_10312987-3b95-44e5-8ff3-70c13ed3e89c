class LocalCacheListener {
  constructor(key) {
    this.key = key;
    this.initialValue = this.getValue();
    this.listeners = [];
    window.addEventListener(
      'setItemEvent',
      this.handleStorageChange.bind(this)
    );
  }

  getValue() {
    return localStorage.getItem(this.key);
  }

  handleStorageChange(event) {
    if (event.key === this.key) {
      const newValue = event.newValue;
      this.notifyListeners(newValue);
    }
  }

  subscribe(callback) {
    this.listeners.push(callback);
  }

  unsubscribe(callback) {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }

  notifyListeners(value) {
    this.listeners.forEach(listener => {
      if (typeof listener === 'function') {
        listener(value);
      }
    });
  }
}

export default LocalCacheListener;
// https://blog.csdn.net/xiaoenwu/article/details/120126639?utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-0-120126639-blog-120160098.235^v40^pc_relevant_anti_t3&spm=1001.2101.3001.4242.1&utm_relevant_index=3

// //解决this指向问题，在window.addEventListener中this是指向window的。
// //需要将vue实例赋值给_this,这样在window.addEventListener中通过_this可以为vue实例上data中的变量赋值
// let _this = this;
// //根据自己需要来监听对应的key
// window.addEventListener("setItemEvent", function (e) {
//   //e.key : 是值发生变化的key
//   //例如 e.key==="token";
//   //e.newValue : 是可以对应的新值
//   if (e.key === "token") {
//     console.log(e.newValue);
//     _this.token = e.newValue;
//   }
// })

// // 示例用法
// const cacheListener = new LocalCacheListener('ff_flight_manage_data_key');

// // 添加监听器
// cacheListener.subscribe(newValue => {
//   console.log(`Key 'ff_flight_manage_data_key' changed. New value: ${newValue}`);
// });

// // 修改浏览器的本地缓存（可以在其他窗口或标签中模拟）
// localStorage.setItem('ff_flight_manage_data_key', 'new value');

// // 移除监听器
// cacheListener.unsubscribe();
