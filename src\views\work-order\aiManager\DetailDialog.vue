<template>
  <el-dialog title="详情" v-if="visible" :model-value="visible" align-center :close-on-click-modal="false" @close="closeDialog">
    
    <el-form class="app-form" ref="dataFormRef"  label-width="130px"  v-loading="loading">
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备名称：" >
              {{form?.device_name}}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备ID：" >
              {{ form?.device_id}}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="任务名称：" >
              {{form?.task_name}}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务ID：" >
             {{ form?.task_id}}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="算法应用名称：" >
              {{form?.app_name}}
            </el-form-item>
          </el-col>
           <el-col :span="12">
            <el-form-item label="算法应用ID：" >
              {{ form?.app_id}}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="摄像头名称：" >
              {{form?.src_name}}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="摄像头ID：" >
              {{ form?.src_id}}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否已生成预警：" >
              {{ form?.has_alarm == false?'未生成':'已生成'}}
            </el-form-item>
          </el-col>
           <el-col :span="12">
            <el-form-item label="创建时间：" >
              {{ form?.create_time}}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form> 
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { getAIEventDetail } from '@/api/workOrder';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  id: {
    type: String,
    default: ''
  },
});
const form = ref({});
const loading = ref(true);
const emit = defineEmits(['update:visible']);

// 关闭弹窗
function closeDialog() {
  emit('update:visible', false);
}

// 获取详情
function getDetail() {
  loading.value = true
  getAIEventDetail(props.id).then(res => {
    form.value = res
  }).finally(()=>{
    loading.value = false
  })
}

onMounted(() => {
  getDetail();
});
</script>
<style lang="scss">
.label-class {
  display: inline-block;
  width: 110px;
  text-align: right;
}
</style>
<style lang="scss" scoped>
:deep(.el-descriptions__body .el-descriptions__table:not(.is-bordered) .el-descriptions__cell) {
  padding-bottom: 20px;
}
.ellipsis {
  display: inline-block;width: 300px;transform: translateY(8px);
}
.left-content {
  display: flex;
  flex-direction: row;
  img {
    width: 136px;
    height: 136px;
    margin-right: 24px;
  }
  .line-left {
    display: flex;
    padding: 20px 8px;
  }
  .line-middle {
    width: 1px;
    height: 314px;
    background: rgba(0, 0, 0, 0.06);
    margin: auto 32px;
  }
  .line-right {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    .item {
      padding: 6px;
      width: 100%;
      color: #000000d9;
      .color {
        color: #000000a6;
      }
    }
  }
}

</style>
