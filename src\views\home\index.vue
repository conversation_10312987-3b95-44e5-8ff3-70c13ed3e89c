<script>
export default { name: 'Home' };
</script>

<script setup>
import { onMounted, reactive, ref } from 'vue';
import { getSatisticsInfo } from '@/api/base';
import { useUserStoreHook } from '@/store/modules/user';
const infos = reactive({});

const date = new Date();
const orgData = ref([
  // {
  //   id: '1',
  //   title: '在线设备',
  //   numKey: 'drone_count',
  //   unit: '台',
  //   btnText: '查看设备',
  //   path: '#/map-fly'
  // },
  // {
  //   id: '2',
  //   title: '成员数量',
  //   num: 1,
  //   unit: '人',
  //   // btnText: '管理组织'
  // },
  {
    id: '3',
    title: '无人机数量',
    numKey: 'drone_count',
    unit: '架',
    btnText: '管理设备',
    path: '#/uav-manage'
  },
  {
    id: '4',
    title: '机场数量',
    numKey: 'airport_count',
    unit: '台',
    btnText: '管理机场',
    path: '#/airport-manage'
  },
  {
    id: '5',
    title: '航线数量',
    numKey: 'route_count',
    unit: '条',
    btnText: '管理航线',
    path: '#/plan'
  },
  {
    id: '6',
    title: '飞行次数',
    numKey: 'flight_count',
    unit: '次',
    // btnText: '飞行历史',
    path: '#/map-fly-history'
  },
  // {
  //   id: '7',
  //   title: '成果数量',
  //   num: 0,
  //   unit: '个',
  //   // btnText: '查看成果'
  // },
  // {
  //   id: '8',
  //   title: 'AI能力',
  //   num: 5,
  //   unit: '种',
  //   // btnText: 'AI配置'
  // }
]);
const greetings = computed(() => {
  if (date.getHours() >= 6 && date.getHours() < 8) {
    return '晨起披衣出草堂，轩窗已自喜微凉🌅！';
  } else if (date.getHours() >= 8 && date.getHours() < 12) {
    return '上午好🌞！';
  } else if (date.getHours() >= 12 && date.getHours() < 18) {
    return '下午好☕！';
  } else if (date.getHours() >= 18 && date.getHours() < 24) {
    return '晚上好🌃！';
  } else if (date.getHours() >= 0 && date.getHours() < 6) {
    return '偷偷向银河要了一把碎星，只等你闭上眼睛撒入你的梦中，晚安🌛！';
  }
});
const satisticsInfo = reactive({});
onMounted(() => {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  Object.assign(infos, userData);
  getSatisticsInfo().then(data => {
    Object.assign(satisticsInfo, { ...data });
  });
});
</script>

<template>
  <div class="dashboard-container">
    <!-- 用户信息 -->
    <div class="header-panel">
      <div class="card-item">
        <div class="ant-row ant-row-space-between">
          <div class="ant-row">
            <img src="@/assets/wrj_logo.png" alt="" />
            <div class="header-left">
              <div class="header-left-name">{{ infos.username }}</div>
              <div class="header-left-role">
                <span>超级管理员</span>
                <span>厦门四信</span>
              </div>
            </div>
          </div>
          <!-- <div class="box">
            <div class="header-left-item">
              <div class="header-left-unit">
                <span class="value">1</span>
                <span>次</span>
              </div>
              <div class="header-left-title">组织飞行次数</div>
            </div>

            <div class="header-left-item">
              <div class="header-left-unit">
                <span class="value">2</span>
                <span>km</span>
              </div>
              <div class="header-left-title">组织飞行里程</div>
            </div>

            <div class="header-left-item">
              <div class="header-left-unit">
                <span class="value">2</span>
                <span>h</span>
                <span class="value">10</span>
                <span>min</span>
              </div>
              <div class="header-left-title">组织飞行时长</div>
            </div>
          </div> -->
        </div>
      </div>

      <!-- <div class="leading-[40px]">
          {{ greetings }}
        </div> -->
    </div>
    <div class="card-item org-panel">
      <div class="card-title">
        <span>组织详情</span>
      </div>
      <div class="org-box">
        <div class="org-item" v-for="item in orgData" :key="item.id">
          <div class="org-name">{{ item.title }}</div>
          <div class="org-data">
            <span class="org-value">{{ satisticsInfo[item.numKey] ||'-'}}</span>
            <span class="org-unit">{{ item.unit }}</span>
          </div>
          <a :href="item.path || '#'" class="org-link"> {{ item.btnText }}</a>
        </div>
      </div>
    </div>

    <!-- <div class="card-item package-panel">
      <div class="card-title">
        <span>套餐信息</span>
        <div class="package-title">
          <span>套餐到期时间：</span><span class="package-time">2024-06-30</span
          ><span>剩余天数：</span><span class="package-remain">167</span
          ><span>天</span>
        </div>
      </div>
      <div class="package-box">
        <div class="package-item">
          <div class="package-title">
            <div class="package-name"><img src="@/assets/home/<USER>" alt="" /><span>设备数</span></div>
            <div class="font">可用：3 台</div>
          </div>
          <div class="package-progress">
            <div style="width: 25%"></div>
          </div>
          <div class="font package-value">
            已用： <span>1</span><span>台</span><span>/</span> 总量： <span>4</span>台
          </div>
        </div>
        <div class="package-item">
          <div class="package-title">
            <div class="package-name"><img src="@/assets/home/<USER>" alt="" /><span>云存储空间</span></div>
            <div class="font">可用：298.75 GB</div>
          </div>
          <div class="package-progress">
            <div style="width: 0.42%"></div>
          </div>
          <div class="font package-value">
            已用： <span>1.25</span><span>GB</span><span>/</span> 总量： <span>300</span>GB
          </div>
        </div>
        <div class="package-item">
          <div class="package-title">
            <div class="package-name">
              <img src="@/assets/home/<USER>" alt="" /><span>单兵视频时长</span>
            </div>
            <div class="font">可用：1000 min</div>
          </div>
          <div class="package-progress">
            <div style="width: 0%"></div>
          </div>
          <div class="font package-value">
            已用： <span>0</span><span>min</span><span>/</span> 总量： <span>1000</span>min
          </div>
        </div>
      </div>
    </div> -->
  </div>
</template>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  position: relative;
  .card-item {
    margin-bottom: 8px;
    padding: 24px;
    background: #ffffff;
    border-radius: 4px;
    overflow: hidden;
    font-size: 14px;
    flex: 1;

    .ant-row {
      display: flex;
      flex-flow: row wrap;
    }
  }
  .header-panel {
    width: 100%;
    height: 128px;
    display: flex;
    .ant-row-space-between {
      justify-content: space-between;
      img {
        margin-right: 24px;
        width: 80px;
        height: 80px;
        border-radius: 50%;
        cursor: pointer;

        vertical-align: middle;
        border-style: none;
      }
      .header-left {
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      .header-left-name {
        margin-top: 6px;
        font-size: 24px;
      }

      .header-left-role {
        span {
          color: #000000a6;
          margin-right: 16px;
        }
      }
      .box {
        display: flex;
        flex-direction: row;
        align-items: center;
        .header-left-item {
          position: relative;
          margin-top: 6px;
          display: inline-flex;
          flex-direction: column;
          align-items: center;
          min-width: 142px;
          .header-left-unit {
            padding: 0 16px;
            font-weight: 500;
            color: #00000063;
            .value {
              margin: 0 4px;
              font-size: 28px;
              font-weight: 600;
              color: #333;
            }
          }
          .header-left-title {
            color: #00000080;
            font-weight: 500;
          }
        }

        .header-left-item:after {
          content: '';
          position: absolute;
          top: 14px;
          right: 0;
          width: 1px;
          height: 40px;
          background: rgba(0, 0, 0, 0.1);
        }

        .header-left-item:last-child:after {
          width: 0;
        }
      }
    }
  }
  .org-panel {
    .card-title {
      margin-bottom: 24px;
      display: inline-flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      font-size: 16px;
      font-weight: 500;
      color: #000;
    }
    .org-box {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }
    .org-item {
      font-weight: 500;
      padding: 16px;
      width: calc(25% - 12px);
      background: #f8f9fb;
      border-radius: 4px;
      a {
        color: #409eff;
      }
      .org-name {
        color: #0000007d;
      }
      .org-data {
        .org-value {
          font-size: 26px;
          margin-right: 4px;
          color: #333;
          font-weight: 500;
        }
        .org-unit {
          color: #0006;
        }
      }
      .org-link {
        margin-top: 20px;
        display: inline-block;
        font-weight: 400;
      }
    }
  }
  .package-panel {
    height: 338px;
    .card-title {
      margin-bottom: 24px;
      display: inline-flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      font-size: 16px;
      font-weight: 500;
      color: #000;
      .package-title {
        font-size: 14px;
        color: #00000080;
        .package-time {
          margin: 0 32px 0 4px;
        }
        .package-remain {
          margin: 0 8px;
        }
      }
    }
    .package-box {
      display: flex;
      flex-wrap: wrap;
      color: #333;
      font-weight: 500;
      .package-item {
        height: 104px;
        line-height: 104px;
        margin-bottom: 16px;
        padding: 16px;
        width: calc(50% - 8px);
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid rgba(0, 0, 0, 0.15);
        .package-title {
          height: 20px;
          line-height: 20px;
          display: flex;
          justify-content: space-between;
          .package-name {
            display: flex;
            flex-direction: row;
            align-items: center;
            color: #333;
            img {
              margin-right: 8px;
            }
          }
          .font {
            color: #0000007d;
          }
        }
        .package-progress {
          position: relative;
          margin: 15px 0 14px;
          height: 6px;
          background: #f5f5f5;
          border-radius: 18px;
          div {
            position: absolute;
            top: 0;
            height: 4px;
            background: #409eff;
            border-radius: 18px;
          }
        }
        .package-value {
          height: 17px;
          line-height: 17px;
          font-size: 12px;
          color: #0006;
          span {
            margin-right: 4px;
          }
        }
      }
      .package-item:nth-child(2n + 1) {
        margin-right: 16px;
      }
    }
  }
}
</style>
