<template>
  <div class="wrapper">
    <el-card>
      <template #header> 任务信息 </template>
      <div class="dinfo">
        <div class="item">
          <div class="label">任务信息:</div>
          <div class="value">{{ data?.jobInfo }}</div>
        </div>

        <div class="item">
          <div class="label">设备信息:</div>
          <div class="value">{{ data?.deviceName }}</div>
        </div>

        <div class="item">
          <div class="label">飞行信息:</div>
          <div class="value">{{ data?.flyInfo }}</div>
        </div>
        <!-- <div class="item">
          <div class="label">飞行地点:</div>
          <div class="value">{{ data?.flyplace }}</div>
        </div> -->
        <div class="item">
          <div class="label">飞手:</div>
          <div class="value">{{ data?.driver }}</div>
        </div>
        <div class="item">
          <div class="label">所属部门:</div>
          <div class="value">{{ data?.belong }}</div>
        </div>
        <div class="item">
          <div class="label">备注:</div>
          <div class="value">{{ data?.content }}</div>
        </div>
        <!-- <div class="item">
          <div class="label">目标经度:</div>
          <div class="value">{{ data?.lon }}</div>
        </div>
        <div class="item">
          <div class="label">目标纬度:</div>
          <div class="value">{{ data?.lat }}</div>
        </div> -->
      </div>
    </el-card>
  </div>
</template>

<script setup>
import {
  ref,
  reactive,
  onUpdated,
  toRefs,
  onBeforeMount,
  onMounted,
  onUnmounted,
  onBeforeUpdate,
  onBeforeUnmount,
  onErrorCaptured
} from 'vue';
import { KEY_FLY_DATA_INFO } from '@/config/Map';
let data = ref();
const init = () => {
  setTimeout(() => {
    let tmdata = localStorage.getItem(KEY_FLY_DATA_INFO);
    data.value = tmdata && JSON.parse(tmdata);
    let tmdata2 = sessionStorage.getItem('taskData') || '';
    if (tmdata2.length > 0) {
      const d = JSON.parse(tmdata2);
      data.value.jobInfo = d[d.length - 1].taskName;
      data.value.content = d[d.length - 1].taskName;
    }
  }, 500);
};

onMounted(() => {
  init();
});

onUnmounted(() => {});
onBeforeUnmount(() => {});

//#endregion
</script>
<style scoped lang="scss">
.wrapper {
  position: absolute;
  top: 15px;
  left: 15px;
  min-width: 310px;
  max-width: 500px;
}

.dinfo {
  .item {
    display: flex;
    justify-content: flex-start;
    align-content: center;
    // background-color: rgba(165, 165, 165, 0.261);
    margin-top: 5px;
    padding: 5px 10px;
    border-radius: 5px;
    .label {
      width: 100px;
      user-select: none;
      // text-align: right;
      // padding-right: 15px;
    }
  }
}

.warn {
  color: red;
}
</style>
