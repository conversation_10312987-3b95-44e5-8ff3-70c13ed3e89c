<template>
  <div class="plan-container">
    <!--  顶部栏 -->
    <div class="wayline-point-header bg-header-height bg-dark-blue">
      <div class="action-bar">
        <div class="return-left" @click="returnBack('saveAndBack')">
          <el-image style="width: 24px; height: 20px" :src="returnLeftIconUrl" />
        </div>
        <div class="action" @click="savePlan()">
          <el-image style="width: 20px; height: 20px" :src="saveIconUrl" />
        </div>
      </div>
      <div class="title">
        <span style="padding-right: 20px">{{ info.planName }}</span>
      </div>
    </div>

    <div class="middle">
      <div class="left-sidebar">
        <!-- 左侧 sidebar -->
        <div class="left-sidebar-wrap">
          <div class="sidebar bg-light-blue">
            <wayline-base-info v-model="info" />
          </div>
          <div class="action-wrap bg-light-blue">
            <div class="action-list">
              <div
                class="action-item"
                :class="{ 'action-active': tabState.routeShow === 'set' }"
                @click="setRouteShow('set')"
              >
                航线设置
              </div>

              <div
                class="action-item"
                :class="{ 'action-active': tabState.routeShow === 'list' }"
                @click="setRouteShow('list')"
              >
                航线列表
              </div>
            </div>
          </div>
          <div class="action-content-wrap bg-dark-blue">
            <div v-show="routeJson.routeList" class="action-content-2">
              <WayPointList
                v-show="routeJson.routeList"
                ref="wayPointList"
                v-bind:select-plan-point="selectPlanPoint"
              />
            </div>
            <div v-show="routeJson.routeSet" class="action-content">
              <plan-route-set v-show="routeJson.routeSet" ref="planRouteSet" v-bind:wrj-info-set="wrjInfoSet" />
            </div>
          </div>
        </div>
      </div>
      <div class="mid-sidebar">
        <ActionsToolBar />
        <MapToolBar />
        <div id="container"></div>
      </div>
      <div class="right-sidebar">
        <div class="up-content"><Actions /></div>
        <div class="down-content">
          <EyeViewRect />
          <div id="container2"></div>
        </div>
      </div>
    </div>

    <div class="float-control">
      <div class="longlat" v-show="curPlanPoint.isShow">
        <p style="padding: 10px 5px 5px 15px">
          经度: <label> {{ toNumber(curPlanPoint.lon, 7) }}</label>
        </p>
        <p style="padding: 5px 5px 5px 15px">
          纬度: <label>{{ toNumber(curPlanPoint.lat, 7) }}</label>
        </p>
      </div>
      <div class="flight-controller">
        <div class="controller-area controller-horizontal">
          <div class="hotkeys-card">
            <div class="hotkey-item">
              <span class="uranus-icon">
                <svg class="svgfont svgfont-drone-turn" style="width: 1em; height: 1em">
                  <use xlink:href="#drone-turn">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" id="drone-turn">
                      <path
                        d="M26.955 7.055A7 7 0 0 0 15 12h1.5a5.5 5.5 0 0 1 9.395-3.884l-1.943 1.942H29V5.01l-2.045 2.045z"
                        fill-rule="evenodd"
                        fill="#FFFFFF"
                        transform="matrix(-1 0 0 1 30 0)"
                        style="mix-blend-mode: passthrough"
                      ></path>
                    </svg>
                  </use>
                </svg> </span
              ><input type="button" class="btn" value="Q" @mousedown="setClickDown(81)" @mouseup="setClickUp(81)" />
            </div>
            <div class="hotkey-item">
              <span class="uranus-icon"> <img src="@/assets/plan/箭头上.png" width="16" /> </span
              ><input type="button" class="btn" value="W" @mousedown="setClickDown(87)" @mouseup="setClickUp(87)" />
            </div>
            <div class="hotkey-item">
              <span class="uranus-icon" style="transform: scaleX(-1)">
                <svg class="svgfont svgfont-drone-turn" style="width: 1em; height: 1em">
                  <use xlink:href="#drone-turn">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" id="drone-turn">
                      <path
                        d="M26.955 7.055A7 7 0 0 0 15 12h1.5a5.5 5.5 0 0 1 9.395-3.884l-1.943 1.942H29V5.01l-2.045 2.045z"
                        fill-rule="evenodd"
                        fill="#FFFFFF"
                        transform="matrix(-1 0 0 1 30 0)"
                        style="mix-blend-mode: passthrough"
                      ></path>
                    </svg>
                  </use>
                </svg> </span
              ><input type="button" class="btn" value="E" @mousedown="setClickDown(69)" @mouseup="setClickUp(69)" />
            </div>
          </div>

          <div class="main-text">
            <span class="main-status">虚拟飞行中</span>
          </div>
          <div class="hotkeys-card bottom">
            <div class="hotkey-item">
              <span class="uranus-icon"> <img src="@/assets/plan/箭头左.png" width="16" /> </span
              ><input type="button" class="btn" value="A" @mousedown="setClickDown(65)" @mouseup="setClickUp(65)" />
            </div>
            <div class="hotkey-item">
              <span class="uranus-icon"> <img src="@/assets/plan/箭头下.png" width="16" /> </span
              ><input type="button" class="btn" value="S" @mousedown="setClickDown(83)" @mouseup="setClickUp(83)" />
            </div>
            <div class="hotkey-item">
              <span class="uranus-icon"> <img src="@/assets/plan/箭头右.png" width="16" /> </span
              ><input type="button" class="btn" value="D" @mousedown="setClickDown(68)" @mouseup="setClickUp(68)" />
            </div>
          </div>
        </div>

        <div class="speed-text text-wrapper map-text-shadow"></div>
        <div class="attitude-view with-background attitude-view-md">
          <div class="pitch-editor">
            <div class="pitch-bar" style="background: currentcolor"></div>
            <div class="marker" style="top: 37.5%"></div>
            <div class="marker zero-marker"></div>
            <div class="marker" style="top: 68.75%"></div>
            <div class="marker" style="top: 87.5%"></div>
            <div class="marker limit-marker" style="top: 31.25%"></div>
            <span class="gimbal-icon2" style="font-size: 10px; top: 50%"
              ><span class="gimbal-icon-inner" style="font-size: 8px"></span></span
            ><span class="angle-number map-text-shadow">0</span>
          </div>
          <div class="yaw-editor with-button">
            <div class="yaw-limit">
              <div class="limit-marker" style="transform: rotate(-90deg)"></div>
              <div class="limit-marker" style="transform: rotate(90deg)"></div>
            </div>

            <div class="yaw-compass active">
              <img src="@/assets/plan/znz.png" :style="{ transform: wrjJson.transform }" />
            </div>
            <div class="yaw-drone">
              <img src="@/assets/plan/纸飞机.png" />
            </div>
            <div class="yaw-gimbal">
              <span class="gimbal-icon" style="font-size: 14px; transform: rotate(0deg)"
                ><span class="gimbal-icon-inner" style="font-size: 12px"></span
              ></span>
            </div>
            <span class="angle-number degree drone map-text-shadow">{{ wrjJson.heading }}</span>
          </div>
          <div class="obstacle-info">
            <div class="obstacle-bar"></div>
            <div class="marker zero-marker"></div>
            <span class="agl-number map-text-shadow">{{ wrjJson.aglHeight }}<span class="unit">m AGL</span></span>
          </div>
        </div>
        <!--跟随航线-->
        <el-tooltip
          class="box-item"
          show-after="500"
          effect="dark"
          :content="wrjJson.flowTip"
          raw-content="true"
          placement="top"
        >
          <span class="operation operation-mode active" @click="setFlow()">
            <img :src="wrjJson.flowImg" width="24" />
          </span>
        </el-tooltip>
        <div class="text-wrapper map-text-shadow height-text">
          <div class="main-text">
            <span class="main-number">{{ wrjJson.altHeight }}</span>
            <div class="main-unit"><span>ALT</span><span>m</span></div>
          </div>
          <div class="other-text">
            <span>{{ wrjJson.aslHeight }}</span
            ><span>m ASL</span>
          </div>
        </div>
        <div class="controller-area controller-vertical">
          <div class="hotkeys-card">
            <div class="hotkey-item">
              <span class="uranus-icon"
                ><svg class="svgfont svgfont-drone_up" style="width: 1em; height: 1em">
                  <use xlink:href="#drone_up">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" id="drone_up">
                      <path
                        d="M5.405 35.699c-.273 0-.328-.066-.328-.35.004-.605.004-2.089.004-2.693 0-.359-.05-.403-.386-.403-.575.005-1.147.013-1.722-.004-.117-.004-.31-.07-.332-.145a.588.588 0 0 1 .068-.437c.831-1.2 1.68-2.387 2.52-3.578.478-.683.952-1.366 1.431-2.049.076-.105.147-.214.24-.298.033-.03.155-.013.184.022.29.381.576.767.853 1.16.726 1.025 1.449 2.054 2.175 3.079.382.538.76 1.081 1.142 1.62.034.048.06.1.08.153.11.276-.004.469-.29.473-.58.009-1.163.004-1.742.004-.395 0-.433.04-.433.451 0 .6.004 2.07.008 2.67 0 .259-.058.325-.306.329-.53 0-1.058-.004-1.592-.004H5.405z"
                        fill-rule="evenodd"
                        fill="#FFF"
                        transform="translate(0 -23.453)"
                        style="mix-blend-mode: passthrough"
                      ></path>
                    </svg>
                  </use></svg></span
              ><input type="button" class="btn" value="C" @mousedown="setClickDown(67)" @mouseup="setClickUp(67)" />
            </div>
          </div>
          <div class="hotkeys-card bottom">
            <div class="hotkey-item">
              <span class="uranus-icon" style="transform: rotate(180deg)"
                ><svg class="svgfont svgfont-drone_up" style="width: 1em; height: 1em">
                  <use xlink:href="#drone_up">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" id="drone_up">
                      <path
                        d="M5.405 35.699c-.273 0-.328-.066-.328-.35.004-.605.004-2.089.004-2.693 0-.359-.05-.403-.386-.403-.575.005-1.147.013-1.722-.004-.117-.004-.31-.07-.332-.145a.588.588 0 0 1 .068-.437c.831-1.2 1.68-2.387 2.52-3.578.478-.683.952-1.366 1.431-2.049.076-.105.147-.214.24-.298.033-.03.155-.013.184.022.29.381.576.767.853 1.16.726 1.025 1.449 2.054 2.175 3.079.382.538.76 1.081 1.142 1.62.034.048.06.1.08.153.11.276-.004.469-.29.473-.58.009-1.163.004-1.742.004-.395 0-.433.04-.433.451 0 .6.004 2.07.008 2.67 0 .259-.058.325-.306.329-.53 0-1.058-.004-1.592-.004H5.405z"
                        fill-rule="evenodd"
                        fill="#FFF"
                        transform="translate(0 -23.453)"
                        style="mix-blend-mode: passthrough"
                      ></path>
                    </svg>
                  </use></svg></span
              ><input type="button" class="btn" value="Z" @mousedown="setClickDown(90)" @mouseup="setClickUp(90)" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'planInfo'
};
</script>
<script setup>
import '../style/common.css';
import '../../plan/style/waypoint/waypoint.css';
import { onMounted, onUnmounted, toRaw } from 'vue';
import WaylineBaseInfo from '../surface/components/WaylineBaseInfo.vue';
import { ElMessage, ElMessageBox, ElInput, ElButton } from 'element-plus';
import {
  canvas2base64,
  getOrCreateCesiumEngineInstance,
  setPlanInstance
} from '../../../components/Cesium/libs/cesium';
import Plan from '../../../components/Cesium/libs/cesium/plan';
import 'element-plus/dist/index.css';
import PlanRouteSet from '../components/planRouteSet.vue';
import {
  addWayPoint,
  addPlacemark,
  complementActionGroup,
  inttWayline,
  reBuidPlacemarkAndActionGroup,
  convertPlacemarksToWayPointList,
  getWaylinesWpmlObject,
  setActionComponentVisible,
  wayLineHandleSetPlan,
  setCurrentPlaceMark,
  getFirstActionInPlaceMark,
  positionCameraToViewPoints,
  hasWayPoint,
  clearExtraActionGroup,
  destoryWayline
} from '../newplan/kmz/hocks/modules/waylineshandle';
import WayPointList from '../components/WayPointList.vue';
import Actions from '../newplan/components/Actions.vue';
import ActionsToolBar from '../newplan/components/ActionsToolBar.vue';
import MapToolBar from '@/views/plan/components/MapToolBar.vue';
import EyeViewRect from '../newplan/components/EyeViewRect.vue';
import { DEVICE_MODEL_KEY, PAYLOAD_TYPE, DEVICE_NAME } from '@/utils/constants';
import { useWayPointStore } from '@/store/modules/wayPointInfo.js';
import { storeToRefs } from 'pinia';
import { useUserStoreHook } from '@/store/modules/user';
import { usePlanInfoStore } from '@/store/modules/planInfo.js';
import { createOrUpdateWayline } from '@/api/wayline';
import { getFrustum, clearFrustum, frustumMap, calculateFOV, updateFrustumWithActionValue } from '../newplan/kmz/hocks';
import { useRouter, useRoute } from 'vue-router';
import { getWaylines } from '@/api/wayline';
import DataTracker from '@/views/plan/libs/DataTracker';
import { toNumber, zoomtoLonglats } from '@/components/Cesium/libs/cesium/common';
import { useDeviceStore } from '@/store/modules/device.js';
import { ACTION_ACTUATOR_FUNC } from '@/utils/constants';
import { getDroneInfoByKey } from '../common/devConfigHelper';
import { useEditTrackerStore } from '@/store/modules/dataEditTracker.js';
import { WAY_POINT_TURN_MODE } from '@/views/plan/newplan/kmz/props';
import { appendChildToBtns } from '../common/el-btn.js';
import { removeMapToolTips } from '../common/tips';
import { ruleCheck } from '../newplan/kmz/hocks/modules/rules';
import { cloneDeep } from 'lodash';

const editTrackerStore = useEditTrackerStore();
const deviceStore = useDeviceStore();
const userStore = useUserStoreHook();
const { userData } = userStore;
const { username } = userData;
const wayPointStore = useWayPointStore();
const planInfoStore = usePlanInfoStore();
const { currentPointId, currentActionId, pointList = [] } = storeToRefs(wayPointStore);
const saveIconUrl = new URL('@/assets/save.png', import.meta.url).href;
const returnLeftIconUrl = new URL('@/assets/return-left.png', import.meta.url).href;
const router = useRouter();
const route = useRoute();
//航线名称、飞行器及负载信息
let info = ref({
  planId: null,
  planName: '新建航点航线',
  droneEnumVal: 91, //飞行器
  droneSubEnumVal: 1,
  droneSubEnumLabel: '', //'Matrice 3TD',
  payloadEnumVal: 81, //负载
  payloadSubEnumVal: 0,
  droneModelKey: '',
  payloadModelKey: ''
});
// 使用 reactive 创建一个响应式对象
const tabState = reactive({
  routeShow: 'set'
});
//航线设置组件对象
const planRouteSet = ref();
//航线列表组件对象
const wayPointList = ref();
let viewer;
let eyeViewer;
let plan;
let wrjJson = ref({
  lon: '',
  lat: '',
  aslHeight: 0, //绝对高度
  altHeight: 0, //相对起飞点
  aglHeight: 0, //相对地形
  // ellipsoidHeight: 100,//全局航线高度（椭球高）相对,* 注：如果 wpml:height 选用相对起飞点高度，则 wpml:ellipsoidHeight 和 wpml:height 相同；如果 wpml:height 选用 EGM96 海拔高度或 AGL 相对地面高度，则 wpml:wpml:ellipsoidHeight 由 wpml:height 做相应转换得到。
  // height: 100, //全局航线高度（EGM96海拔高/相对起飞点高度/AGL相对地面高度）* 注：该元素与 wpml:ellipsoidHeight 配合使用，二者是同一位置不同高程参考平面的表达。
  heading: 0,
  transform: 'rotate(0deg)',
  isStartFlyPoint: false,
  isFlow: true,
  flowImg: new URL('@/assets/plan/flow_blue.png', import.meta.url).href,
  flowTip: '高度模式：跟随航线'
});

const selectPlanPoint = planPoint => {
  curPlanPoint.value.lon = planPoint.lon;
  curPlanPoint.value.lat = planPoint.lat;
  curPlanPoint.value.isShow = true;
  wrjJson.value.isFlow = planPoint.flow;
  if (wrjJson.value.isFlow == true) {
    wrjJson.value.flowImg = new URL('@/assets/plan/flow_blue.png', import.meta.url).href;
    wrjJson.value.flowTip = '高度模式：跟随航线';
    plan.planPointFlow = false;
  } else {
    wrjJson.value.flowImg = new URL('@/assets/plan/flow.png', import.meta.url).href;
    wrjJson.value.flowTip = '高度模式：不跟随航线';
    plan.planPointFlow = false;
  }
  wrjInfoSet(planPoint);
};

//航线列表、航线设置按钮
let routeJson = ref({
  routeList: false,
  routeSet: true
});

let curPlanPoint = ref({
  isShow: false,
  lon: '',
  lat: ''
});

//按钮颜色设置，#2E90FA代表蓝色开启，#353436代表灰色关闭
let colorJson = ref({
  routeList: '#353436',
  routeSet: '#2E90FA'
});

/**
 * 键盘事件
 * @param {*} keyCode
 */
function setClickDown(keyCode) {
  plan.setClickDown(keyCode);
}
/**
 * 键盘事件
 * @param {*} keyCode
 */
function setClickUp(keyCode) {
  plan.setClickUp(keyCode);
}

/**
 * 设置左侧航线列表、航线设置的显示/隐藏
 * @param {*} val list为航线列表，set为航线设置
 */
function setRouteShow(val) {
  // 定义一个状态变量来记录当前的选项
  switch (val) {
    case 'list':
      tabState.routeShow = 'list';
      colorJson.value.routeList = '#2E90FA';
      colorJson.value.routeSet = '#353436';
      break;
    case 'set':
      tabState.routeShow = 'set';
      colorJson.value.routeList = '#353436';
      colorJson.value.routeSet = '#2E90FA';
      break;
    default:
      tabState.routeShow = null;
      colorJson.value.routeList = '#353436';
      colorJson.value.routeSet = '#353436';
      routeJson.value.routeList = true;
      routeJson.value.routeSet = false;
  }
  // 根据当前的选项更新其他相关的状态
  if (tabState.routeShow === 'list') {
    // 更新其他相关的状态
    routeJson.value.routeList = true;
    routeJson.value.routeSet = false;
  } else if (tabState.routeShow === 'set') {
    // 更新其他相关的状态
    routeJson.value.routeList = false;
    routeJson.value.routeSet = true;
  } else {
    routeJson.value.routeList = true;
    routeJson.value.routeSet = false;
  }
}
/**
 * 返回
 */
function returnBack(saveType = 'saveAndBack') {
  const dataStatus = editTrackerStore.dataTracker.getDataStatus();
  if (dataStatus === DataTracker.DataStatus.SAVED_AND_UNMODIFIED) {
    // 数据已保存且未修改
    setTimeout(() => {
      router.back();
      editTrackerStore.dataTracker.reset();
    }, 500);
  } else if (dataStatus === DataTracker.DataStatus.MODIFIED_AND_UNSAVED) {
    setTimeout(() => {
      appendChildToBtns();
    }, 100);

    // 数据已修改但未保存
    ElMessageBox.confirm(`返回将退出航线编辑，尚未保存的内容将丢失，是否确定退出？`, '注意', {
      confirmButtonText: '保存并退出',
      cancelButtonText: '不保存',
      type: 'warning',
      closeOnClickModal: false,
      showClose: false
    })
      .then(() => {
        savePlan(saveType);
      })
      .catch(() => {
        setTimeout(() => {
          router.back();
          editTrackerStore.dataTracker.reset();
        }, 500);
      });
  } else {
    // 数据未修改且未保存
    setTimeout(() => {
      editTrackerStore.dataTracker.reset();
      router.back();
    }, 500);
  }
}
/**
 * 保存航线
 */
function savePlan(saveType = 'save') {
  if (info.value.planName == null || info.value.planName === '') {
    ElMessage.warning('请输入航线名称');
    return;
  }
  // 判断是否有绘制航点
  if (!hasWayPoint()) {
    ElMessage.warning('请至少添加一个航点后再保存航线');
    return;
  }
  // 先执行动作冲突点检查 需要处理完成后才能执行保存
  let { hasWarnning = false, warningPlacemarkList = [] } = ruleCheck();
  if (hasWarnning) {
    const warningPlacemarkListStr = warningPlacemarkList.map(index => index + 1).join(', ');
    ElMessage.warning(`航线中存在动作冲突，请在航点号为 [${warningPlacemarkListStr}] 的航点中处理冲突`);
    return;
  }
  // 重新定位相机到航点
  positionCameraToViewPoints(viewer);
  // 重新构建航点及动作组
  reBuidPlacemarkAndActionGroup();
  // 清理多余的动作组 空动作组要清理
  clearExtraActionGroup();
  //航线文件template.kml的json
  const templateJson = planRouteSet.value.getTemplateJsons().value; //获取航线文件template
  //航线文件waylines.wpml的json 获取航线文件waylines
  const wayJson = cloneDeep(getWaylinesWpmlObject());
  const time = new Date().getTime();
  templateJson.wpml_author = username;
  templateJson.wpml_updateTime = time;
  templateJson.Folder.Placemark = plan.placemarkJson;
  // 获取drone和payload的枚举值
  templateJson.Folder.wpml_payloadParam.wpml_imageFormat = deviceStore.getCameraSelect().join(',') ?? '';
  //循环每一个航点
  for (let i = 0; i < wayJson.folder[0].placemark.length; i++) {
    const waylinePlacemark = wayJson.folder[0].placemark[i];
    delete waylinePlacemark.uuid;
    const templatePlacemark = plan.placemarkJson[i];
    //需要对wayline中的航点信息进行赋值处理
    if (waylinePlacemark.point != undefined && waylinePlacemark.point != nul) delete waylinePlacemark.point;
    waylinePlacemark.Point = templatePlacemark.Point;
    waylinePlacemark.wpml_executeHeight = templatePlacemark.wpml_ellipsoidHeight;
    waylinePlacemark.wpml_waypointSpeed = templatePlacemark.wpml_waypointSpeed;
    waylinePlacemark.wpml_waypointTurnParam = templatePlacemark.wpml_waypointTurnParam;
    waylinePlacemark.wpml_waypointHeadingParam = templatePlacemark.wpml_waypointHeadingParam;
    waylinePlacemark.wpml_useStraightLine = templatePlacemark.wpml_useStraightLine;
    waylinePlacemark.wpml_waypointHeadingMode = templatePlacemark.wpml_waypointHeadingParam.wpml_waypointHeadingMode;
    let cameras = deviceStore?.getCameraSelect().join(',') ?? '';
    // 新增 移除 组的 uuid 和动画的 uuid
    waylinePlacemark.wpml_actionGroup?.forEach(g => {
      delete g.uuid;
      delete g.type;
      delete g.trigger;
      (g.wpml_action || []).forEach(a => {
        let useGlobalIndex = false;
        if (a.wpml_actionActuatorFuncParam.wpml_useGlobalPayloadLensIndex === 1) {
          useGlobalIndex = true;
        }
        switch (a.wpml_actionActuatorFunc) {
          case ACTION_ACTUATOR_FUNC.startRecord:
          case ACTION_ACTUATOR_FUNC.takePhoto:
            if (a.wpml_actionActuatorFuncParam.wpml_payloadLensIndex && useGlobalIndex) {
              a.wpml_actionActuatorFuncParam.wpml_payloadLensIndex = cameras;
            }
            break;
          case ACTION_ACTUATOR_FUNC.timeIntervalTakePhoto: // 等时拍照
            if (a.wpml_actionActuatorFunc) {
              a.wpml_actionActuatorFunc = ACTION_ACTUATOR_FUNC.takePhoto;
            }
            if (a.wpml_actionActuatorFuncParam.wpml_payloadLensIndex && useGlobalIndex) {
              a.wpml_actionActuatorFuncParam.wpml_payloadLensIndex = cameras;
            }
            break;
          case ACTION_ACTUATOR_FUNC.distanceIntervalTakePhoto: // 等距拍照
            if (a.wpml_actionActuatorFunc) {
              a.wpml_actionActuatorFunc = ACTION_ACTUATOR_FUNC.takePhoto;
            }
            if (a.wpml_actionActuatorFuncParam.wpml_payloadLensIndex && useGlobalIndex) {
              a.wpml_actionActuatorFuncParam.wpml_payloadLensIndex = cameras;
            }
            break;
          default:
            break;
        }
        delete a.uuid;
        delete a.type;
        delete a.trigger;
      });
    });

    //有动作的需要同时添加到template中
    if (waylinePlacemark.wpml_actionGroup != undefined && waylinePlacemark.wpml_actionGroup != null) {
      if (waylinePlacemark.wpml_actionGroup.length > 0)
        templatePlacemark.wpml_actionGroup = waylinePlacemark.wpml_actionGroup;
      else delete waylinePlacemark.wpml_actionGroup;
    }
  }

  //waylines的执行高度模式设置，与template中的值有不同，故单独变量判定
  let wpml_executeHeightMode = 'WGS84';
  if (templateJson.Folder.wpml_waylineCoordinateSysParam.wpml_heightMode != 'EGM96') {
    wpml_executeHeightMode = 'relativeToStartPoint';
  }
  // 打补丁 这里直接对设备的类型进行重新赋值
  let devInfo = deviceStore.getCurrentDevice();
  templateJson.wpml_missionConfig.wpml_droneInfo.wpml_droneEnumValue = devInfo.droneEnumVal;
  templateJson.wpml_missionConfig.wpml_droneInfo.wpml_droneSubEnumValue = devInfo.droneSubEnumVal;
  templateJson.wpml_missionConfig.wpml_payloadInfo.wpml_payloadEnumValue = devInfo.payloadEnumVal ?? 0;
  templateJson.wpml_missionConfig.wpml_payloadInfo.wpml_payloadSubEnumValue = 0; // devInfo.payloadSubEnumVal ?? 0;
  let waylinesJson = {
    wpml_missionConfig: {
      wpml_flyToWaylineMode: templateJson.wpml_missionConfig.wpml_flyToWaylineMode,
      wpml_finishAction: templateJson.wpml_missionConfig.wpml_finishAction,
      wpml_exitOnRCLost: templateJson.wpml_missionConfig.wpml_exitOnRCLost,
      wpml_executeRCLostAction: templateJson.wpml_missionConfig.wpml_executeRCLostAction,
      wpml_takeOffSecurityHeight: templateJson.wpml_missionConfig.wpml_takeOffSecurityHeight,
      wpml_globalTransitionalSpeed: templateJson.wpml_missionConfig.wpml_globalTransitionalSpeed,
      wpml_globalRTHHeight: templateJson.wpml_missionConfig.wpml_globalRTHHeight,
      wpml_droneInfo: toRaw(templateJson.wpml_missionConfig.wpml_droneInfo),
      wpml_payloadInfo: toRaw(templateJson.wpml_missionConfig.wpml_payloadInfo)
    },
    Folder: {
      wpml_templateId: templateJson.Folder.wpml_templateId, //模板ID，* 注：在一个kmz文件内该ID唯一。建议从0开始单调连续递增。在template.kml和waylines.wpml文件中，将使用该id将模板与所生成的可执行航线进行关联。
      wpml_waylineId: 0, //航线ID，* 注：在一条航线中该ID唯一。建议从0开始单调连续递增。
      wpml_executeHeightMode: wpml_executeHeightMode, //执行高度模式,WGS84：椭球高模式;relativeToStartPoint：相对起飞点高度模式;realTimeFollowSurface: 使用实时仿地模式，仅支持M3E/M3T/M3M，M3D/M3TD
      wpml_autoFlightSpeed: templateJson.Folder.wpml_autoFlightSpeed, //全局航线飞行速度
      Placemark: wayJson.folder[0].placemark //航点信息（包括航点经纬度和高度等）
    }
  };

  //#region 航线特例处理
  // 曲线飞行飞行器到点停 toPointAndStopWithContinuityCurvature 这里wayline 航线的 起点终点是直线飞行到点停 toPointAndStopWithDiscontinuityCurvature 直线飞行，飞行器到点停
  if (templateJson.Folder.wpml_globalWaypointTurnMode === WAY_POINT_TURN_MODE.toPointAndStopWithContinuityCurvature) {
    waylinesJson.Folder.Placemark[0].wpml_waypointTurnParam.wpml_waypointTurnMode =
      WAY_POINT_TURN_MODE.toPointAndStopWithDiscontinuityCurvature;
    let lastIndex = waylinesJson.Folder.Placemark.length - 1;
    waylinesJson.Folder.Placemark[lastIndex].wpml_waypointTurnParam.wpml_waypointTurnMode =
      WAY_POINT_TURN_MODE.toPointAndStopWithDiscontinuityCurvature;
  }
  //#endregion
  const base64 = canvas2base64(viewer);
  const deviceInfo = deviceStore.getCurrentDevice();
  //提交后端的最终json
  const planJson = {
    wayline_id: info.value.planId ?? null,
    name: info.value.planName,
    drone_model: deviceInfo.droneModelKey || info.value.droneModelKey || '',
    payload_model: deviceInfo.payloadModelKey || info.value.payloadModelKey || '',
    template_type: 0,
    thumbnail: base64 || '',
    airline_json: JSON.stringify({
      template: templateJson,
      wayLines: waylinesJson
    })
  };
  //提交
  createOrUpdateWayline(planJson)
    .then(res => {
      if (res) {
        if (res === '航线文件名已存在') {
          ElMessage.warning('该航线名称已存在，请重新命名');
          return;
        }
        info.value.planId = res;
        ElMessage.success('保存航线成功');
        // 标记已保存
        editTrackerStore.dataTracker.save();
        if (saveType === 'saveAndBack') {
          setTimeout(() => {
            router.back();
            editTrackerStore.dataTracker.reset();
          }, 500);
        }
      }
    })
    .catch(err => {
      ElMessage.error('保存航线错误！原因：' + err.message);
    });
}

/**
 * 切换跟随模式，主要是调整高度时，航点是否跟随调整变化，不跟随则不变
 */
function setFlow() {
  if (wrjJson.value.isFlow == false) {
    wrjJson.value.isFlow = true;
    wrjJson.value.flowImg = new URL('@/assets/plan/flow_blue.png', import.meta.url).href;
    wrjJson.value.flowTip = '高度模式：跟随航线';
    plan.planPointFlow = true;
    if (plan.curPlanPointJson != null) {
      plan.curPlanPointJson.flow = true;
    }
  } else {
    wrjJson.value.isFlow = false;
    wrjJson.value.flowImg = new URL('@/assets/plan/flow.png', import.meta.url).href;
    wrjJson.value.flowTip = '高度模式：不跟随航线';
    plan.planPointFlow = false;
    if (plan.curPlanPointJson != null) {
      plan.curPlanPointJson.flow = false;
    }
  }
}

/**
 * 刷新无人机页面值
 * @param {*} pJson {lon,lat,startHeight,height,UAVHPR}
 */
function wrjInfoSet(pJson) {
  if (pJson) {
    wrjJson.value.aslHeight = pJson.height.toFixed(1);
    wrjJson.value.altHeight = (pJson.height - pJson.startHeight).toFixed(1);
    wrjJson.value.aglHeight = (pJson.height - pJson.terrainHeight).toFixed(1);

    let degrees = (pJson.UAVHPR.heading * (180 / Math.PI)).toFixed(0);
    let heading = degrees;
    if (degrees > 180 && degrees < 360) {
      heading = degrees - 360;
    } else if (degrees == 360) {
      heading = 0;
    }
    wrjJson.value.transform = 'rotate(' + degrees + 'deg)';
    wrjJson.value.heading = heading;
    wrjJson.value.lon = pJson.lon.toFixed(8);
    wrjJson.value.lat = pJson.lat.toFixed(8);
  }
}

function callback(type, pJson) {
  removeMapToolTips();
  if (type == 'add') {
    wrjInfoSet(pJson);
    const templateJsons = planRouteSet.value.getTemplateJsons(); //获取航线文件模版
    //添加航点
    const json = {
      lon: pJson.lon,
      lat: pJson.lat,
      wpml_ellipsoidHeight: pJson.globalHeight,
      wpml_height: pJson.globalHeight,
      wpml_waypointSpeed: templateJsons.value.Folder.wpml_autoFlightSpeed,
      wpml_waypointHeadingMode: templateJsons.value.Folder.wpml_globalWaypointHeadingParam.wpml_waypointHeadingMode,
      wpml_waypointTurnMode: templateJsons.value.Folder.wpml_globalWaypointTurnMode
    };
    const placemark = plan.addPlacemark(json);
    addWayPoint(placemark);
    editTrackerStore.dataTracker.markAsModified();
  } else if (type == 'start') {
    const templateJsons = planRouteSet.value.getTemplateJsons(); //获取航线文件模版
    //添加起飞点
    const json = plan.setFlyPoint(
      pJson.lon,
      pJson.lat,
      pJson.height,
      templateJsons.value.wpml_missionConfig.wpml_flyToWaylineMode,
      templateJsons.value.wpml_missionConfig.wpml_takeOffSecurityHeight
    );
    //   pJson={
    //   index: 0,
    //   type: '起飞点',
    //   lon: lon,
    //   lat: lat,
    //   startHeight: height,//海拔高度
    //   terrainHeight: 0,//地形高度
    //   height: this.wrjHeight,
    //   UAVHPR: hpr, //无人机方位
    //   position: this.curUAVPoint, //无人机停留高度点
    //   planLineEntity: planLineEntity, //起飞路线
    //   pointEntity: null,
    //   lineEntity: null,
    //   dPointEntity: pointEntity, //起飞点
    //   length: (this.wrjHeight - height) //无人机与起飞点的距离
    // }

    wrjJson.value.isStartFlyPoint = true;
    if (templateJsons.value.wpml_missionConfig.wpml_flyToWaylineMode == 'safely') {
      wrjInfoSet(json);
    } else if ((templateJsons.value.wpml_missionConfig.wpml_flyToWaylineMode = 'pointToPoint')) {
      let tmpJson = {
        lon: json.lon,
        lat: json.lat,
        startHeight: json.startHeight,
        height: json.globalHeight,
        terrainHeight: json.terrainHeight,
        UAVHPR: json.UAVHPR
      };
      wrjInfoSet(tmpJson);
    }
    //设置参考起飞点
    planRouteSet.value.setFirstPoint(json.lon, json.lat, json.startHeight);
    editTrackerStore.dataTracker.markAsModified();
  } else if (type == 'move') {
    // {
    //   lon: lng,
    //   lat: lat,
    //   startHeight: this.flyStartPoint[2],
    //   height: height, //无人机的高度
    //   UAVHPR: hpr, //方位
    //   position: position, //航点位置
    // }
    wrjInfoSet(pJson);
  }
  //选中航点
  else if (type == 'sel') {
    const index = parseInt(pJson.index);
    wayPointStore.setCurrentPoint(pointList.value[index]);
    selectPlanPoint(pJson);
  }
}

/**
 * 载入航线航点信息，初始化地图及航点列表
 */
async function initPlanInfo() {
  return new Promise((resolve, reject) => {
    //先初始化
    destoryWayline();
    //获取选中的航线信息
    getWaylinesByID().then(json => {
      if (json && json.airline_json) {
        //航线的基本信息
        info.value.planId = json.id;
        info.value.planName = json.name;
        const droneParm = json.drone_model_key.split('-');
        info.value.droneModelKey = json.drone_model_key ?? '';
        info.value.payloadModelKey = json.payload_model_keys[0] ?? '';
        info.value.droneEnumVal = parseInt(droneParm[1]);
        info.value.droneSubEnumVal = parseInt(droneParm[2]);
        // 将当前设备信息保存到全局变量中
        const baseInfo = getDroneInfoByKey(json.drone_model_key);
        if (!baseInfo) {
          ElMessage.error('未找到基础设备信息！');
          return;
        }
        let deviceInfo = baseInfo?.device ?? null;
        if (!deviceInfo) {
          ElMessage.error('配置信息中未找到该设备信息！');
          return;
        }

        deviceStore.setCurrentDevice({
          planId: json.id,
          planName: json.name,
          series: deviceInfo.series,
          droneEnumVal: deviceInfo.droneEnumVal,
          droneEnumLabel: deviceInfo.droneEnumLabel ?? '',
          droneSubEnumVal: deviceInfo.droneSubEnumVal,
          droneSubEnumLabel: deviceInfo.droneSubEnumLabel ?? '',
          payloadEnumVal: deviceInfo.payloadEnumVal || '',
          payloadSubEnumVal: deviceInfo.payloadSubEnumVal || '',
          payloadModelKey: deviceInfo.payloadModelKey || '',
          droneModelKey: deviceInfo.droneModelKey
        });

        // 设置新的设备类型
        deviceStore.getDeviceInfoByType(json.drone_model_key);
        const payloadParm = json.payload_model_keys[0].split('-');
        info.value.payloadEnumVal = parseInt(payloadParm[1]);
        info.value.payloadSubEnumVal = parseInt(payloadParm[2]);
        // planRouteSet.value.setDronePayload(
        //   info.value.droneEnumVal,
        //   info.value.droneSubEnumVal,
        //   info.value.payloadEnumVal,
        //   info.value.payloadSubEnumVal
        // );
        planInfoStore.setCurDroneInfo(info.value);
        //航线航点
        const planJson = JSON.parse(json.airline_json);
        console.log('1--planJson:', planJson);
        planRouteSet.value.setTemplateJsons(planJson.template);
        // 设置被选相机类型
        let cameraTypeSelected = planJson.template.Folder.wpml_payloadParam.wpml_imageFormat;
        if (cameraTypeSelected === '' || cameraTypeSelected === undefined) {
          deviceStore.setCameraSelect([]);
          deviceStore.setWayLineCameraType([]);
        } else {
          deviceStore.setCameraSelect(cameraTypeSelected.split(','));
          deviceStore.setWayLineCameraType(cameraTypeSelected.split(','));
        }
        //1、创建起飞点
        const flyStr = planJson.template.wpml_missionConfig.wpml_takeOffRefPoint;
        const flyParm = flyStr.split(',');
        const lat = parseFloat(flyParm[0]);
        const lon = parseFloat(flyParm[1]);
        const height = parseFloat(flyParm[2]);
        plan.setFlyPoint(
          lon,
          lat,
          height,
          planJson.template.wpml_missionConfig.wpml_flyToWaylineMode,
          planJson.template.wpml_missionConfig.wpml_takeOffSecurityHeight
        );
        // 获取航点数组
        const placemarkArr = planJson.template.Folder.Placemark;
        //2、循环添加航点
        let lonlats = [];
        for (let i = 0; i < placemarkArr.length; i++) {
          const placemark = placemarkArr[i];
          plan.placemarkJson.push(placemark);
          const point = placemark.Point.coordinates.split(',');
          const lon = toNumber(point[0], 9);
          const lat = toNumber(point[1], 9);
          let wrjHeight = toNumber(placemark.wpml_height, 2);
          if (planJson.template.Folder.wpml_waylineCoordinateSysParam.wpml_heightMode == 'EGM96') {
            wrjHeight = toNumber(placemark.wpml_height, 2);
          } else if (
            planJson.template.Folder.wpml_waylineCoordinateSysParam.wpml_heightMode == 'relativeToStartPoint'
          ) {
            wrjHeight = parseInt(placemark.wpml_height) + parseFloat(flyParm[2]);
          }
          const pJson = {
            index: i,
            type: '航点',
            lon: lon,
            lat: lat,
            flow: true,
            startHeight: toNumber(flyParm[2], 2), //起飞点海拔高度
            terrainHeight: 0, //地形高度
            globalHeight: toNumber(placemark.wpml_height, 2), //parseInt(placemark.wpml_height),
            height: wrjHeight, //无人机的高度
            UAVHPR: null, //方位
            position: null, //航点位置
            planLineEntity: null, //航线对象
            pointEntity: null, //航点对象
            lineEntity: null, //航点离地虚线
            dPointEntity: null, //航点地面点
            length: 0
          };
          plan.planPointJson.push(pJson);
          lonlats.push([lon, lat]);
        }
        if (lonlats.length === 0) {
          lonlats.push([lon, lat]);
          zoomtoLonglats(viewer, lonlats);
        }
        // 获取地形高度后
        plan.getTerrainHeight(lonlats, function (updatedPositions) {
          if (updatedPositions != null) {
            for (let i = 0; i < updatedPositions.length; i++) {
              const position = updatedPositions[i];
              if (plan.planPointJson[i + 1]?.terrainHeight) {
                plan.planPointJson[i + 1].terrainHeight = position.height;
              }
            }
          }
          plan.refreshPoint(0);
          // 创建wayline对象
          inttWayline();
          const wayLinesPlacemarkArr = planJson.wayLines.Folder.Placemark ?? [];
          // 这里将在folder 内部构件 placemark 同时构建出 航点的uuid 、动作、动作组的UUID 等
          wayLinesPlacemarkArr.forEach((placemark, i) => {
            // 根据json初始化placemark
            addPlacemark(placemark);
          });
          // 完成上面的循环操作后执行动作组补充
          complementActionGroup();
          // 重构动作和动作组序列
          reBuidPlacemarkAndActionGroup();
          // 需要重新获取 placemark的 数组构建组件点集合
          const waylinesXpmlObj = getWaylinesWpmlObject(); //获取航线文件waylines
          const folder = waylinesXpmlObj.getFolder(0);
          const placemarkList = folder.getPlacemark();
          const data = convertPlacemarksToWayPointList(placemarkList);
          wayPointStore.setPointList(data);
          //#region 设置第一个点初始化时候进行定位和选中
          // 选中地图上的第一个点 列表选中
          wayPointStore.initFirstSelect();
          // 设置第一个航点列表被选中
          setCurrentPlaceMark();
          setActionComponentVisible();
          // 设置地图第一个点被选中
          const firstPointjson = plan.selectPlanPoint(0, true);
          if (firstPointjson) {
            // 添加第一个视锥体处理视锥体
            const firstAction = getFirstActionInPlaceMark(placemarkList[0]);
            selectPlanPoint(firstPointjson);
            // 初始化时处理视锥体
            if (firstAction) {
              const { lon, lat, height, UAVHPR: hpr = null } = firstPointjson;
              let frustun = getFrustum('action');
              if (frustun) {
                const op = frustun?.getOptions() ?? {};
                const options = {
                  ...op,
                  position: [lon, lat, height]
                };
                frustun.update(options);
              }
              if (firstAction.wpml_actionActuatorFunc === ACTION_ACTUATOR_FUNC.zoom) {
                let sensorSize = 22.5; //12.0823; //
                let focalLength = firstAction.wpml_actionActuatorFuncParam.wpml_focalLength;
                let fovDeg = calculateFOV(focalLength, sensorSize);
                const options = {
                  action: firstAction,
                  actionUuid: firstAction.uuid,
                  type: ACTION_ACTUATOR_FUNC.zoom,
                  value: fovDeg,
                  zoom: Number(focalLength / 24)
                };
                updateFrustumWithActionValue(options);
              }
            }
          }
        });
        resolve(true);
        //#endregion
      } else {
        ElMessage.error('加载航线信息失败，未获取到航线的配置及航点信息。');
        reject(false);
      }
    });
  });
}

function getWaylinesByID() {
  return new Promise((resolve, reject) => {
    try {
      let json = planInfoStore.getCurPlanData();
      // let import_type = json.import_type || 0;
      // import_type： 创建类型（0：平台创建，1：其他平台导入，2：遥控器同步）   0 都为创建，1、2为导入
      // job_type：工作类型（0：普通任务，1：接处警任务
      if (json.job_type === 1) {
        planInfoStore.setCurPlanJobType(json.job_type);
      }
      if (!json || !json.airline_json) {
        let id = route.query.id;
        getWaylines({
          order_by: 'update_time desc',
          wayline_id: id
        }).then(data => {
          const { list = [] } = data;
          if (list?.length > 0) {
            json = list[0];
            planInfoStore.setCurPlanData(json);
            resolve(json);
          }
        });
      } else {
        resolve(json);
      }
    } catch (error) {
      reject(error);
    }
  });
}

onMounted(async () => {
  removeMapToolTips();
  clearFrustum();
  const engine = getOrCreateCesiumEngineInstance('plan');
  const engine2 = getOrCreateCesiumEngineInstance('eyeViewer');
  engine?.init('container'); //地图
  engine2?.init('container2'); //鹰眼地图
  viewer = engine.viewer;
  eyeViewer = engine2.viewer;
  plan = new Plan(viewer, eyeViewer);
  setPlanInstance(plan);
  //将航线计划对象传递给子组件
  planRouteSet.value.setPlan(plan);
  wayPointList.value.setPlan(plan);
  plan.init(false);
  plan.initMouse(callback);
  plan.initKey(callback);
  // 将当前plan对象传递给waylineHandle中使用
  wayLineHandleSetPlan(plan);
  await initPlanInfo();
});

onUnmounted(() => {
  plan.dispose();
  clearFrustum();
  removeMapToolTips();
  planInfoStore.setCurPlanJobType(0);
});
</script>

<style lang="scss" scoped>
@import '../../../styles/plan/plan.scss';
.plan-container {
  width: 100%;
  height: 100%;
  background-color: #171717;
  user-select: none;
}
#container {
  width: 100%;
  height: 100%;
}
#container2 {
  width: 100%;
  height: 100%;
}

.middle {
  width: 100%;
  height: calc(100% - 50px);
  display: flex;
  background-color: #dddddd41;
}
.middle .left-sidebar {
  width: 350px;
  height: 100%;
  color: white;
  background-color: #dddddd41;
}
.middle .mid-sidebar {
  height: 100%;
  display: flex;
  flex: 1;
  color: white;
  background-color: #dddddd41;
}
.middle .right-sidebar {
  width: 350px;
  height: 100%;
  color: white;
}
.middle .right-sidebar .up-content {
  width: 100%;
  height: calc(100% - 350px);
  z-index: 1;
}
.middle .right-sidebar .down-content {
  height: 350px;
  height: 350px;
  padding: 5px;
  background-color: #ffffff !important;
  z-index: 2;
}

// 自适应 居中
.float-control {
  position: absolute;
  width: 100%;
  height: 200px;
  left: 50%;
  bottom: 0px;
  transform: translateX(-50%);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;
}
.float-control .longlat {
  width: 170px;
  height: 80px;
  background-color: rgba(0, 0, 0, 0.65);
  border-radius: 10px;
  color: #fff;
  margin: 0px 20px;
}

.menu-top {
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  height: 40px;
  background-color: #101010;
  display: flex;
  align-items: center;
  .action-box {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .action-return {
      height: 100%;
      display: flex;
      align-items: center;
      margin-left: 5px;
      &:hover {
        cursor: pointer;
      }
    }
    .action {
      height: 100%;
      display: flex;
      margin-left: 15px;
      margin-right: 20px;
      align-items: center;
      &:hover {
        cursor: pointer;
      }
    }
  }
}

.menu-left {
  position: absolute;
  top: 40px;
  left: 0px;
  bottom: 0px;
  width: 350px;
  background-color: #101010;
  overflow-y: auto;
}

.name-and-device {
  display: flex;
  align-items: center;
  background-color: #3c3c3c;
  border: 1px solid #707070;
  position: absolute;
  top: 3px;
  left: 350px;
  color: #fff;
  border-radius: 4px;
  padding: 3px 16px;
  cursor: pointer;
  // max-width: 700px;
}

// /* 滚动条样式 */
// ::-webkit-scrollbar {
//   width: 8px;
//   /*  设置纵轴（y轴）轴滚动条 */
//   height: 8px;
//   /*  设置横轴（x轴）轴滚动条 */
// }

// /* 滚动条滑块（里面小方块） */
// ::-webkit-scrollbar-thumb {
//   border-radius: 4px;
//   border: none;
//   box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
//   background-color: #5f5f5f;
// }

// /* 滚动条轨道 */
// ::-webkit-scrollbar-track {
//   border-radius: 0;
//   box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
//   background: transparent;
// }

.action {
  height: 100%;
  display: flex;
  align-items: center;
  margin-left: 10px;
  &:hover {
    cursor: pointer;
  }
}
</style>
