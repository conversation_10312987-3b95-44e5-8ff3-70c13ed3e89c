# 无人机实战平台V1.4.0

## 1. 版本迭代概述

### 1.1 迭代目标

本次V1.4.0版本迭代主要完成以下功能模块的设计与开发：

1. 预警管理模块
2. 工单管理模块
3. 地图数据管理（图层与图元管理）
4. 电子围栏功能
5. 语音通话功能
6. 数据字典功能
7. 附件管理模块
8. 用户在线状态管理模块
9. AI算法集成模块

### 1.2 迭代背景

在V1.3.0版本基础上，为了更好地支持无人机实战过程中的异常发现、处理和指挥调度，需要增加预警和工单管理功能，以及相关的地图数据管理、电子围栏和通话功能。本次迭代将显著提升系统的实战应用能力和指挥效率。

## 2. 功能模块设计

### 2.1 预警管理模块

#### 2.1.1 功能描述

预警管理模块用于接收、展示和处理无人机在执行任务过程中通过AI识别算法发现的异常情况。系统产生预警信息后，需要经过人工复核，确认为问题后可转为工单进行处理，或确认为误报后关闭预警。

#### 2.1.2 功能结构

```mermaid
graph TD
    A[预警管理] --> B[预警接收]
    A --> C[预警列表]
    A --> D[预警处置]
    
    B --> B1[AI识别接口]
    
    C --> C1[预警信息展示]
    C --> C2[预警筛选查询]
    
    D --> D1[转工单处置]
    D --> D2[直接关闭处置]
```

#### 2.1.3 业务流程

```mermaid
flowchart TD
    A(开始预警流程) --> B1[AI算法识别]
    B1 --> B2[预警信息生成]
    B2 --> C[显示预警信息]
    C --> D[人工复核预警]
    D --> E{确认问题类型}
    E -->|确认为隐患或事故| F[转为工单]
    E -->|确认为误报| G[关闭预警]
    F --> H[发送短信通知]
```

##### 预警管理时序图

```mermaid
sequenceDiagram
    participant AI as AI算法服务
    participant Server as 系统服务端
    participant DB as 数据库
    participant Admin as 管理员
    participant SMS as 短信服务
    participant WorkOrder as 工单管理

    AI->>Server: 发送AI识别事件
    Server->>DB: 保存预警信息
    Server->>Admin: 显示预警通知
    Admin->>Server: 查看预警详情
    Server->>DB: 获取预警数据
    Server->>Admin: 展示预警信息
    
    alt 确认为实际问题
        Admin->>Server: 选择"转为工单"
        Server->>WorkOrder: 创建工单记录
        Server->>DB: 更新预警状态为"已处理"
        Server->>DB: 记录处理结果为"转工单"
        Server->>SMS: 发送通知给相关人员
    else 确认为误报
        Admin->>Server: 选择"关闭预警"
        Admin->>Server: 输入关闭原因
        Server->>DB: 更新预警状态为"已处理"
        Server->>DB: 记录处理结果为"直接关闭"
    end
```

#### 2.1.4 数据库设计

```sql
-- 预警表
CREATE TABLE uav_alarm (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `alarm_time` datetime NOT NULL COMMENT '预警时间',
  `alarm_type` varchar(32) NOT NULL COMMENT '预警类型：烟火(fire)、入侵(intrusion)、禁飞(no_fly)、占用(occupation)等',
  `alarm_source` varchar(32) NOT NULL COMMENT '预警来源：AI识别(ai)',
  `alarm_desc` text NOT NULL COMMENT '预警描述',
  `alarm_addr` varchar(255) DEFAULT NULL COMMENT '预警地址',
  `longitude` decimal(10,6) NOT NULL COMMENT '预警经度',
  `latitude` decimal(10,6) NOT NULL COMMENT '预警纬度',
  `alarm_status` varchar(32) NOT NULL COMMENT '预警处置状态：未处理(pending)、处理中(processing)、已处理(processed)',
  `alarm_result` varchar(32) DEFAULT NULL COMMENT '预警处置结果：转工单(to_work)、直接关闭(close)',
  `process_content` text DEFAULT NULL COMMENT '预警处理结果描述',
  `processor` varchar(64) DEFAULT NULL COMMENT '处理人',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `work_no` bigint DEFAULT NULL COMMENT '工单号',
  `remark` varchar(256) DEFAULT '' COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  INDEX `idx_alarm_status` (`alarm_status`),
  INDEX `idx_alarm_time` (`alarm_time`),
  INDEX `idx_work_no` (`work_no`)
);
```

#### 2.1.5 接口设计

```plain
POST /api/alarm/receive      # 接收预警信息
GET  /api/alarm/list         # 获取预警列表
GET  /api/alarm/{id}         # 获取预警详情
POST /api/alarm/process      # 处理预警
POST /api/alarm/to-work      # 预警转工单
GET  /api/alarm/unread-count # 获取未处理预警数量
```

### 2.2 工单管理模块

#### 2.2.1 功能描述

工单管理模块用于管理需要人工处置的事项，包括预警核查处置、隐患排查、日常巡检等。工单可以由预警转入或人工创建，工单产生后会通知相关处置人，处置人可以对工单进行处置并记录处置过程。

#### 2.2.2 功能结构

```mermaid
graph TD
    A[工单管理] --> B[工单列表]
    A --> C[新建工单]
    A --> D[工单处置]
    A --> E[工单关闭]
    A --> F[工单详情查看]
    
    B --> B1[工单信息展示]
    B --> B2[工单筛选查询]
    
    C --> C1[基本信息录入]
    C --> C2[附件上传]
    
    D --> D1[处置记录新增]
    D --> D2[附件上传]
```

#### 2.2.3 业务流程

```mermaid
flowchart TD
    A(开始工单流程) --> B{工单来源}
    B -->|人工创建| C[人工创建工单]
    B -->|预警转入| D[预警转为工单]
    C --> E[工单信息录入]
    D --> E
    E --> F[分派处理人]
    F --> G[短信通知]
    G --> H[处理人处置工单]
    H --> I{处置结果}
    I -->|继续处置| J[提交处置记录]
    I -->|处置完成| K[关闭工单]
    J --> H
```

##### 工单管理时序图

```mermaid
sequenceDiagram
    participant Creator as 创建者
    participant Alert as 预警管理
    participant Server as 系统服务端
    participant DB as 数据库
    participant SMS as 短信服务
    participant Handler as 处理人

    alt 人工创建
        Creator->>Server: 点击"新建工单"
        Server->>Creator: 显示工单表单
        Creator->>Server: 填写工单信息并提交
    else 预警转入
        Alert->>Server: 发起"转为工单"
        Server->>Alert: 获取预警信息
    end
    
    Server->>DB: 保存工单信息
    Creator->>Server: 选择处理人
    Server->>DB: 更新工单处理人
    Server->>SMS: 发送通知给处理人
    
    Handler->>Server: 查看工单详情
    Server->>DB: 获取工单信息
    Server->>Handler: 展示工单详情
    
    loop 工单处置过程
        Handler->>Server: 提交处置记录
        Server->>DB: 保存处置记录
        alt 处置完成
            Handler->>Server: 点击"关闭工单"
            Server->>DB: 更新工单状态为"已关闭"
        end
    end
```

#### 2.2.4 数据库设计

```sql
-- 工单表
CREATE TABLE uav_work_order (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `work_time` datetime NOT NULL COMMENT '工单创建时间',
  `work_type` varchar(32) NOT NULL COMMENT '工单类型：事故处置(accident)、隐患排查(hazard)、日常巡检(routine)等',
  `work_source` varchar(32) NOT NULL COMMENT '工单来源：人工创建(manual)、预警转入(alarm)',
  `work_desc` text NOT NULL COMMENT '工单描述',
  `work_addr` varchar(255) DEFAULT NULL COMMENT '工单地址',
  `longitude` decimal(10,6) NOT NULL COMMENT '经度',
  `latitude` decimal(10,6) NOT NULL COMMENT '纬度',
  `work_status` varchar(32) NOT NULL COMMENT '工单状态：未分配(unassigned)、处理中(processing)、已关闭(closed)',
  `work_level` varchar(32) NOT NULL COMMENT '工单优先级：最高(highest)、高(high)、中(medium)、低(low)、最低(lowest)',
  `processor` varchar(64) DEFAULT NULL COMMENT '处理人',
  `remark` varchar(256) DEFAULT '' COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  INDEX `idx_work_status` (`work_status`),
  INDEX `idx_processor` (`processor`),
  INDEX `idx_work_time` (`work_time`)
);

-- 处置记录表
CREATE TABLE uav_process_record (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `work_no` bigint NOT NULL COMMENT '工单号',
  `process_content` text NOT NULL COMMENT '处置内容',
  `processor` varchar(64) NOT NULL COMMENT '处置人',
  `process_time` datetime NOT NULL COMMENT '处置时间',
  `remark` varchar(256) DEFAULT '' COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  INDEX `idx_work_no` (`work_no`),
  CONSTRAINT `fk_process_work` FOREIGN KEY (`work_no`) REFERENCES `uav_work_order` (`id`)
);
```

#### 2.2.5 接口设计

```plain
GET  /api/work/list          # 获取工单列表
POST /api/work/create        # 创建工单
GET  /api/work/{id}          # 获取工单详情
POST /api/work/process       # 提交处置记录
POST /api/work/close         # 关闭工单
```

### 2.3 地图数据管理模块

#### 2.3.1 功能描述

地图数据管理分为图层管理和图元管理两部分，用于扩充和完善业务应用所需的地图专题数据，如消防通道、重点设施、应急资源等，为无人机巡航提供更精准的数据支持。

**主要功能特点：**

1. **图层管理**：支持多种图层类型（点、线、面、路网、栅格），提供完整的CRUD操作
2. **图元管理**：支持在图层中添加具体的地理要素，包含位置、属性、联系方式等信息
3. **自定义排序**：图层支持自定义排序，便于控制地图显示优先级
4. **条件查询**：支持按名称、类型、可见性等条件进行筛选查询
5. **软删除机制**：删除操作采用软删除，保证数据安全可恢复
6. **扩展配置**：支持JSON格式的扩展配置，满足个性化需求
7. **地图集成**：与地图组件深度集成，支持可视化操作

#### 2.3.2 功能结构

```mermaid
graph TD
    A[地图数据管理] --> B[图层管理]
    A --> C[图元管理]

    B --> B1[图层列表]
    B --> B2[新建图层]
    B --> B3[修改图层]
    B --> B4[删除图层]
    B --> B5[图层详情]
    B --> B6[图层排序]

    C --> C1[图元列表]
    C --> C2[新建图元]
    C --> C3[修改图元]
    C --> C4[删除图元]
    C --> C5[图元详情]
    C --> C6[按图层查询]

    B1 --> B11[条件查询]
    B1 --> B12[分页显示]
    B1 --> B13[排序显示]

    C1 --> C11[条件查询]
    C1 --> C12[分页显示]
    C1 --> C13[地图可视化]
```

##### 地图数据管理时序图

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant Server as 系统服务端
    participant DB as 数据库
    participant Map as 自定义图层
    
    note over Admin,Map: 图层管理流程
    Admin->>Server: 请求图层列表
    Server->>DB: 查询图层数据
    DB->>Server: 返回图层列表
    Server->>Admin: 显示图层列表
    
    alt 新建图层
        Admin->>Server: 点击"新建图层"
        Server->>Admin: 显示图层表单
        Admin->>Server: 提交图层信息
        Server->>DB: 保存图层数据
        Server->>Map: 创建图层配置
    else 修改图层
        Admin->>Server: 点击"编辑"按钮
        Server->>DB: 获取图层详情
        DB->>Server: 返回图层数据
        Server->>Admin: 显示编辑表单
        Admin->>Server: 提交修改信息
        Server->>DB: 更新图层数据
        Server->>Map: 更新图层配置
    else 删除图层
        Admin->>Server: 点击"删除"按钮
        Server->>Admin: 显示确认对话框
        Admin->>Server: 确认删除
        Server->>DB: 标记图层为已删除
        Server->>Map: 移除图层配置
    end
    
    note over Admin,Map: 图元管理流程
    Admin->>Server: 请求图元列表
    Server->>DB: 查询图元数据
    DB->>Server: 返回图元列表
    Server->>Admin: 显示图元列表
    
    alt 新建图元
        Admin->>Server: 点击"新建图元"
        Server->>Admin: 显示图元表单
        Admin->>Server: 提交图元信息
        Server->>DB: 保存图元数据
        Server->>Map: 添加图元到地图
    else 修改图元
        Admin->>Server: 点击"编辑"按钮
        Server->>DB: 获取图元详情
        DB->>Server: 返回图元数据
        Server->>Admin: 显示编辑表单
        Admin->>Server: 提交修改信息
        Server->>DB: 更新图元数据
        Server->>Map: 更新地图图元
    else 删除图元
        Admin->>Server: 点击"删除"按钮
        Server->>Admin: 显示确认对话框
        Admin->>Server: 确认删除
        Server->>DB: 标记图元为已删除
        Server->>Map: 从地图移除图元
    end
```

#### 2.3.3 数据库设计

```sql
-- 图层表
CREATE TABLE `map_layer` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `layer_name` VARCHAR(100) NOT NULL COMMENT '图层名称',
    `layer_type` VARCHAR(50) NOT NULL COMMENT '图层类型：点(point)、线(line)、面(polygon)、路网(roadnetwork)、栅格(tile)等',
    `min_level` INT(11) DEFAULT 1 COMMENT '最小显示级别',
    `max_level` INT(11) DEFAULT 20 COMMENT '最大显示级别',
    `is_visible` TINYINT(1) DEFAULT 1 COMMENT '是否显示',
    `label_field` VARCHAR(100) COMMENT '标签字段',
    `label_color` VARCHAR(20) COMMENT '标签颜色',
    `label_font` VARCHAR(50) COMMENT '标签字体',
    `layer_config` TEXT COMMENT '图层配置JSON',
    `sort_order` INT(11) DEFAULT 0 COMMENT '排序字段，数值越小越靠前',
    `remark` VARCHAR(500) COMMENT '备注',
    `creator` VARCHAR(64) DEFAULT '' COMMENT '创建者',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` VARCHAR(64) DEFAULT '' COMMENT '更新者',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    `deleted_time` DATETIME COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_layer_name` (`layer_name`),
    KEY `idx_layer_type_visible` (`layer_type`, `is_visible`),
    KEY `idx_sort_order_id` (`sort_order`, `id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地图图层表';

-- 图元表
CREATE TABLE `map_feature` (
   `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
   `feature_name` VARCHAR(100) NOT NULL COMMENT '图元名称',
   `layer_id` BIGINT(20) NOT NULL COMMENT '所属图层ID',
   `feature_addr` VARCHAR(255) COMMENT '图元地址',
   `longitude` DECIMAL(10,7) NOT NULL COMMENT '图元中心点经度',
   `latitude` DECIMAL(10,7) NOT NULL COMMENT '图元中心点纬度',
   `contact` VARCHAR(50) COMMENT '联系人',
   `telephone` VARCHAR(20) COMMENT '联系电话',
   `feature_coords` TEXT COMMENT '线面的坐标集合，二维数组JSON格式',
   `extra_info` TEXT COMMENT '图元扩展属性',
   `remark` VARCHAR(500) COMMENT '备注',
   `creator` VARCHAR(64) DEFAULT '' COMMENT '创建者',
   `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `updater` VARCHAR(64) DEFAULT '' COMMENT '更新者',
   `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
   `deleted_time` DATETIME COMMENT '删除时间',
   PRIMARY KEY (`id`),
   KEY `idx_layer_id` (`layer_id`),
   KEY `idx_feature_name` (`feature_name`),
   KEY `idx_longitude_latitude` (`longitude`, `latitude`),
   KEY `idx_feature_addr` (`feature_addr`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地图图元表';
```

#### 2.3.4 接口设计

##### 图层管理接口

```plain
POST /map/api/v1/layer/create      # 创建图层
PUT  /map/api/v1/layer/update      # 更新图层
DELETE /map/api/v1/layer/{id}      # 删除图层
GET  /map/api/v1/layer/{id}        # 获取图层详情
POST /map/api/v1/layer/list        # 获取图层列表（支持条件查询）
```

##### 图元管理接口

```plain
POST /map/api/v1/feature/create         # 创建图元
PUT  /map/api/v1/feature/update         # 更新图元
DELETE /map/api/v1/feature/{id}         # 删除图元
GET  /map/api/v1/feature/{id}           # 获取图元详情
POST /map/api/v1/feature/list           # 获取图元列表（支持条件查询）
GET  /map/api/v1/feature/layer/{layerId} # 根据图层ID获取图元列表
```

##### 原有接口（保留兼容）

```plain
POST /map/api/v1/getInfoByAlarmId  # 根据告警ID获取信息
POST /map/api/v1/carLocation       # 车辆位置
```

#### 2.3.5 接口参数详细说明

##### 创建图层接口

**请求地址：** `POST /map/api/v1/layer/create`

**请求参数：**
```json
{
  "layerName": "消防站点",           // 图层名称（必填）
  "layerType": "point",             // 图层类型（必填）：point、line、polygon、roadnetwork、tile
  "minLevel": 1,                    // 最小显示级别（必填）
  "maxLevel": 20,                   // 最大显示级别（必填）
  "isVisible": true,                // 是否显示（可选，默认true）
  "labelField": "name",             // 标签字段（可选）
  "labelColor": "#FF0000",          // 标签颜色（可选）
  "labelFont": "12px Arial",        // 标签字体（可选）
  "layerConfig": "{\"icon\": \"fire-station\", \"size\": [32, 32]}", // 图层配置JSON（可选）
  "sortOrder": 10,                  // 排序字段（可选，默认0，数值越小越靠前）
  "remark": "消防站点图层"           // 备注（可选）
}
```

**响应示例：**
```json
{
  "code": 200,
  "success": true,
  "message": "操作成功",
  "data": 1  // 返回创建的图层ID
}
```

##### 获取图层列表接口

**请求地址：** `POST /map/api/v1/layer/list`

**请求参数：**
```json
{
  "layerName": "消防",              // 图层名称（模糊查询，可选）
  "layerType": "point",             // 图层类型（可选）
  "isVisible": true,                // 是否显示（可选）
  "pageNum": 1,                     // 页码（可选）
  "pageSize": 10                    // 页大小（可选）
}
```

**响应示例：**
```json
{
  "code": 200,
  "success": true,
  "data": [
    {
      "id": 1,
      "layerName": "消防站点",
      "layerType": "point",
      "minLevel": 1,
      "maxLevel": 20,
      "isVisible": true,
      "labelField": "name",
      "labelColor": "#FF0000",
      "labelFont": "12px Arial",
      "layerConfig": "{\"icon\": \"fire-station\", \"size\": [32, 32]}",
      "sortOrder": 10,
      "remark": "消防站点图层",
      "creator": "system",
      "createTime": "2024-12-23T10:00:00",
      "updater": "system",
      "updateTime": "2024-12-23T10:00:00"
    }
  ]
}
```

##### 创建图元接口

**请求地址：** `POST /map/api/v1/feature/create`

**请求参数：**
```json
{
  "featureName": "中央消防站",       // 图元名称（必填）
  "layerId": 1,                     // 所属图层ID（必填）
  "featureAddr": "北京市朝阳区建国路88号", // 图元地址（可选）
  "longitude": 116.4074,            // 经度（必填）
  "latitude": 39.9042,              // 纬度（必填）
  "contact": "张队长",               // 联系人（可选）
  "telephone": "010-12345678",      // 联系电话（可选）
  "featureCoords": null,            // 坐标集合JSON（线面图元必填）
  "extraInfo": "{\"capacity\": 50, \"vehicles\": 8}", // 扩展属性JSON（可选）
  "remark": "主要消防站"             // 备注（可选）
}
```

**响应示例：**
```json
{
  "code": 200,
  "success": true,
  "message": "操作成功",
  "data": 1  // 返回创建的图元ID
}
```

##### 获取图元列表接口

**请求地址：** `POST /map/api/v1/feature/list`

**请求参数：**
```json
{
  "featureName": "消防站",          // 图元名称（模糊查询，可选）
  "layerId": 1,                     // 所属图层ID（可选）
  "featureAddr": "朝阳区",          // 图元地址（模糊查询，可选）
  "contact": "张",                  // 联系人（模糊查询，可选）
  "pageNum": 1,                     // 页码（可选）
  "pageSize": 10                    // 页大小（可选）
}
```

**响应示例：**
```json
{
  "code": 200,
  "success": true,
  "data": [
    {
      "id": 1,
      "featureName": "中央消防站",
      "layerId": 1,
      "layerName": "消防站点",
      "featureAddr": "北京市朝阳区建国路88号",
      "longitude": 116.4074,
      "latitude": 39.9042,
      "contact": "张队长",
      "telephone": "010-12345678",
      "featureCoords": null,
      "extraInfo": "{\"capacity\": 50, \"vehicles\": 8}",
      "remark": "主要消防站",
      "creator": "system",
      "createTime": "2024-12-23T10:00:00",
      "updater": "system",
      "updateTime": "2024-12-23T10:00:00"
    }
  ]
}
```

#### 2.3.6 排序功能说明

##### 排序规则
1. **主排序**：按 `sortOrder` 字段升序排列，数值越小越靠前
2. **次排序**：当 `sortOrder` 相同时，按 `id` 升序排列
3. **默认值**：新创建的图层如果未指定 `sortOrder`，默认为 0

##### 排序建议
- 建议为不同类型的图层设置不同的排序值范围：
  - 基础图层：0-99
  - 业务图层：100-199
  - 临时图层：200-299
- 预留排序值间隔，便于后续插入新图层

#### 2.3.7 数据字典

##### 图层类型 (layerType)
- `point`: 点图层（如消防站、监控点等）
- `line`: 线图层（如道路、管线等）
- `polygon`: 面图层（如行政区域、建筑物等）
- `roadnetwork`: 路网图层
- `tile`: 栅格图层

##### 响应状态码
- `200`: 操作成功
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

#### 2.3.8 前端集成说明

##### 图层管理页面功能
1. **图层列表展示**：支持分页、搜索、排序
2. **图层创建**：表单验证、类型选择、配置设置
3. **图层编辑**：支持所有字段修改，包括排序调整
4. **图层删除**：软删除，支持批量操作
5. **图层排序**：拖拽排序或数值调整

##### 图元管理页面功能
1. **图元列表展示**：支持按图层筛选、地址搜索
2. **地图可视化**：在地图上直观显示图元位置
3. **图元创建**：支持地图点击选点、坐标输入
4. **图元编辑**：支持拖拽移动、属性修改
5. **图元删除**：支持单个和批量删除

##### 地图集成要点
1. **图层加载**：根据排序顺序加载图层
2. **级别控制**：根据地图缩放级别显示/隐藏图层
3. **标签显示**：根据配置显示图元标签
4. **交互操作**：支持点击查看详情、编辑等操作
5. **样式配置**：根据 `layerConfig` 设置图层样式

#### 2.3.9 技术实现要点

##### 后端实现
1. **架构设计**：采用分层架构，Controller-Service-Mapper-Entity
2. **数据验证**：使用 Bean Validation 进行参数校验
3. **软删除**：支持软删除机制，保证数据安全
4. **事务管理**：关键操作使用事务保证数据一致性
5. **排序优化**：使用复合索引优化排序查询性能

##### 前端实现建议
1. **地图组件**：建议使用 Leaflet 或 OpenLayers 等地图库
2. **图层管理**：实现图层树组件，支持拖拽排序
3. **图元编辑**：支持地图上直接编辑图元位置和属性
4. **数据缓存**：合理使用缓存减少接口调用
5. **错误处理**：完善的错误提示和异常处理机制

##### 性能优化
1. **分页查询**：大数据量时使用分页避免性能问题
2. **索引优化**：合理设计数据库索引提升查询效率
3. **缓存策略**：对频繁查询的数据进行缓存
4. **异步加载**：地图数据采用异步加载方式
5. **数据压缩**：大量坐标数据可考虑压缩传输

### 2.4 电子围栏模块

#### 2.4.1 功能描述

电子围栏功能用于在地图上设置虚拟区域，当电子围栏创建后，系统会自动生成巡检航线，但不会自动创建巡检任务。管理员可根据生成的航线手动创建巡检任务，并设置执行频率和条件。

无人机在执行巡检任务过程中，会主动采集视频数据并发送至AI算法服务进行实时分析。系统会根据电子围栏的配置，判断目标对象（如车辆、人员）是否在围栏内或围栏外，从而决定是否触发预警和自动响应措施。

主要功能特点：

1. **空间化管理**：在地图上直观绘制并管理电子围栏区域
2. **航线自动生成**：围栏创建后自动生成最优巡检航线
3. **任务手动创建**：管理员基于航线手动创建巡检任务
4. **智能决策**：基于AI检测结果和围栏规则，智能判断是否需要触发预警
5. **即时响应**：可配置多种前置处置措施（如喊话、照明等）进行现场干预
6. **预警联动**：与预警管理和工单系统无缝集成，确保问题得到及时处理

电子围栏支持多种触发条件和响应方式，可广泛应用于消防通道占用监测、区域安全管理、人员离岗监控等场景。系统会记录所有AI检测结果和预警信息，方便后期分析和改进围栏设置。

#### 2.4.2 功能结构

```mermaid
graph TD
    A[电子围栏] --> B[围栏列表]
    A --> C[新建围栏]
    A --> D[修改围栏]
    A --> E[删除围栏]
    A --> F[围栏详情]
    A --> G[围栏航线管理]
    A --> H[围栏任务管理]
    A --> I[围栏AI检测]
    
    C --> C1[基本信息设置]
    C --> C2[区域绘制]
    C --> C3[围栏属性配置]
    
    G --> G1[航线自动生成]
    G --> G2[航线预览]
    G --> G3[航线调整]
    
    H --> H1[手动创建巡检任务]
    H --> H2[设置执行频率]
    H --> H3[设置执行条件]
    H --> H4[任务执行管理]
    H --> H5[任务历史记录]
    
    I --> I1[AI检测结果查看]
    I --> I2[检测规则配置]
    I --> I3[响应措施管理]
```

#### 2.4.3 业务流程

```mermaid
flowchart TD
    A[创建电子围栏] --> B[在图层中保存电子围栏坐标和属性信息]
    B --> C[系统自动生成巡检航线]
    C --> D[航线预览与调整]
    D --> E[管理员手动创建巡检任务]
    E --> F[设置任务执行频率与条件]
    F --> G[执行任务,升降无人机,开始巡检]
    G --> H[AI算法检测进行目标检测]
    H --> I{检查目标物是否在围栏中}
    I -->|是| J[预设自动化设置响应]
    I -->|否| K[更新AI事件has_alarm=0]
```

##### 电子围栏时序图

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant Fence as 电子围栏服务
    participant Route as 航线服务
    participant Task as 任务管理
    participant UAV as 无人机
    participant AI as AI算法服务
    participant Alert as 预警管理
    participant Device as 执行设备
    
    Admin->>Fence: 创建电子围栏
    Fence->>Fence: 保存围栏坐标和属性
    Fence->>Route: 触发航线自动生成
    Route->>Route: 根据围栏生成最优航线
    Route->>Fence: 返回生成的航线
    Fence->>Admin: 展示航线预览
    
    Admin->>Admin: 确认/调整航线
    Admin->>Task: 手动创建巡检任务
    Admin->>Task: 配置任务执行频率与条件
    Task->>Task: 关联电子围栏和航线
    Task->>UAV: 下发巡检任务
    
    UAV->>UAV: 执行巡检任务
    UAV->>AI: 传输摄像头视频流
    AI->>AI: 执行目标识别算法
    AI->>Fence: 发送识别结果

    Fence->>Fence: 检查目标是否在围栏内
    Fence->>AI: 保存AI事件
    
    alt 目标在围栏内
        Fence->>AI: 更新AI事件has_alarm=1
        Fence->>Device: 触发预设响应措施
        Fence->>UAV: 指令执行(喊话/照明等)
        Fence->>Alert: 生成预警信息
        Alert->>Alert: 记录预警待处理
    else 目标不在围栏内
        Fence->>AI: 更新AI事件has_alarm=0
    end
    
    UAV->>Task: 任务完成报告
```

#### 2.4.4 数据库设计

```sql
-- 电子围栏表
CREATE TABLE fence_info (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `fence_name` varchar(100) NOT NULL COMMENT '围栏名称',
  `fence_addr` varchar(255) DEFAULT NULL COMMENT '围栏地址',
  `fence_type` varchar(32) NOT NULL COMMENT '围栏类型：入侵(intrusion)、离开(leave)',
  `fence_target` varchar(32) NOT NULL COMMENT '识别目标：车辆(vehicle)、人员(person)、动物(animal)、其他(other)',
  `business_type` varchar(32) NOT NULL COMMENT '业务类型：消防通道占用(fire_access)、人员离岗(person_leave)、区域安全(area_security)',
  `pre_process` varchar(64) NOT NULL COMMENT '前期处置措施：喊话器喊话(speaker)、打开探照灯(searchlight)、发送短信通知(sms)、自动录像(record)',
  `contact` varchar(64) DEFAULT NULL COMMENT '联系人',
  `telephone` varchar(32) DEFAULT NULL COMMENT '联系电话',
  `fence_status` varchar(32) NOT NULL DEFAULT 'active' COMMENT '围栏状态：活动(active)、已禁用(disabled)',
  `fence_coords` text NOT NULL COMMENT '围栏的坐标集合，二维数组JSON格式',
  `layer_id` bigint DEFAULT NULL COMMENT '所属图层ID',
  `remark` varchar(256) DEFAULT '' COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  INDEX `idx_fence_type` (`fence_type`),
  INDEX `idx_business_type` (`business_type`),
  INDEX `idx_fence_status` (`fence_status`)
);

-- 已有表更新脚本
-- 修改飞行任务表的job_type字段注释
ALTER TABLE flight_task 
MODIFY COLUMN job_type tinyint DEFAULT NULL COMMENT '工作类型（0：普通任务，1：接处警任务，2：围栏巡检任务）';

-- 修改航线文件表的job_type字段注释和template_types字段注释
ALTER TABLE wayline_file 
MODIFY COLUMN job_type int DEFAULT NULL COMMENT '工作类型（0：普通任务，1：接处警任务，2：围栏巡检任务）';

-- 围栏使用的已有表：飞行任务表
-- 飞行任务表：用于关联围栏和巡检任务
-- 表名: flight_task
/*
CREATE TABLE `flight_task` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `flight_task_id` varchar(45) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '飞行任务UUID',
  `alarm_task_id` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '接处警任务UUID',
  `job_type` tinyint DEFAULT NULL COMMENT '工作类型（0：普通任务，1：接处警任务，2：围栏巡检任务）',
  `name` varchar(1024) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `file_id` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用于此任务的航线文件',
  `dock_sn` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '执行任务的机场',
  `dock_sn_desc` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '机场名称',
  `drone_sn` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '执行任务的无人机',
  `drone_sn_desc` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '无人机名称',
  `workspace_id` varchar(45) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '当前任务所属的工作区',
  `task_type` tinyint NOT NULL COMMENT '任务类型（0：立即执行，1：定时执行，2：条件执行，3：周期执行）',
  `wayline_type` tinyint DEFAULT NULL COMMENT '航线的模板类型',
  `rth_altitude` int NOT NULL COMMENT '返航高度。最小：20米；最大：500米',
  `out_of_control` int NOT NULL COMMENT '失控动作。0: 返航；1: 悬停；2: 降落',
  `execute_time` datetime DEFAULT NULL COMMENT '执行时间（首次执行时间）',
  `begin_date` date DEFAULT NULL COMMENT '周期开始时间',
  `end_date` date DEFAULT NULL COMMENT '周期结束时间',
  `period_type` tinyint DEFAULT NULL COMMENT '周期类型（1：每天，2：每周）',
  `period_time` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '周期时间',
  `days_of_week` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '每周具体星期几',
  `timed_time` datetime DEFAULT NULL COMMENT '定时时间',
  `status` tinyint NOT NULL COMMENT '机场任务状态：0-开启；1-暂停；2-结束',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  `dept_id` bigint DEFAULT NULL COMMENT '部门ID',
  `creator` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_user_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者姓名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_user_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者姓名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uni_idx_flight_task_id` (`flight_task_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='飞行任务表（逻辑上的飞行任务，不执行实际任务，只用来生成任务）';
*/

-- 围栏使用的已有表：航线文件表
-- 航线文件表：存储围栏生成的巡检航线
-- 表名: wayline_file
/*
CREATE TABLE `wayline_file` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'wayline name',
  `wayline_id` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'uuid',
  `drone_model_key` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'device product enum. format: domain-device_type-sub_type',
  `payload_model_keys` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'payload product enum. format: domain-device_type-sub_type',
  `workspace_id` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'Which workspace the current wayline belongs to.',
  `sign` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'The md5 of the wayline file.',
  `favorited` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Whether the file is favorited or not.',
  `template_types` varchar(32) NOT NULL DEFAULT '' COMMENT 'wayline file template type. 0: waypoint;\n0- (waypoint：航点飞行) \n1-(mapping2d：建图航拍) \n2-(mapping3d：倾斜摄影) \n3-(mappingStrip：航带飞行) \n4-(fence：围栏巡检) ',
  `object_key` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'The key of the file in the bucket.',
  `thumbnail` varchar(200) DEFAULT '' COMMENT '缩略图文件地址',
  `user_name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'The name of the creator.',
  `create_time` bigint NOT NULL,
  `update_time` bigint NOT NULL COMMENT 'required, can''t modify.',
  `airline_json` longtext,
  `job_type` int DEFAULT NULL COMMENT '工作类型（0：普通任务，1：接处警任务，2：围栏巡检任务）',
  `import_type` int DEFAULT NULL COMMENT '创建类型（0：平台创建，1：其他平台导入，2：遥控器同步）',
  `dept_id` bigint DEFAULT NULL COMMENT '部门ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `wayline_id_UNIQUE` (`wayline_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='航线文件';
*/

```

#### 2.4.5 接口设计

```plain
# 围栏基本管理接口
GET  /api/fence/list               # 获取围栏列表
POST /api/fence/create             # 创建围栏
PUT  /api/fence/{id}               # 更新围栏
DELETE /api/fence/{id}             # 删除围栏
GET  /api/fence/{id}               # 获取围栏详情
PUT  /api/fence/{id}/status        # 更新围栏状态(激活/暂停/禁用)

# 围栏航线管理接口
GET  /api/fence/{id}/route         # 获取围栏生成的航线
POST /api/fence/{id}/route/generate # 重新生成围栏航线
PUT  /api/fence/{id}/route         # 调整围栏航线
GET  /api/fence/routes             # 获取所有围栏航线列表

# 围栏任务管理接口
POST /api/fence/{id}/task          # 手动创建围栏巡检任务
GET  /api/fence/{id}/tasks         # 获取围栏关联的巡检任务列表
GET  /api/fence/task/{taskId}      # 获取围栏巡检任务详情
PUT  /api/fence/task/{taskId}      # 更新围栏巡检任务
DELETE /api/fence/task/{taskId}    # 删除围栏巡检任务
POST /api/fence/task/{taskId}/execute  # 手动执行围栏巡检任务
PUT  /api/fence/task/{taskId}/cancel   # 取消执行中的围栏巡检任务

# 围栏AI检测接口
POST /api/fence/detection          # 接收AI检测结果
GET  /api/fence/{id}/detections    # 获取围栏历史检测记录
```

### 2.5 用户在线状态管理模块

#### 2.5.1 功能描述

用户在线状态管理模块是实现语音通话等实时交互功能的基础，用于维护和管理所有用户（包括PC端和手柄端）的在线状态。该模块通过WebSocket STOMP协议实现心跳机制，实时监测用户连接状态，提供状态查询和订阅功能，确保系统能够准确识别哪些用户当前可以进行通话。

#### 2.5.2 功能结构

```mermaid
graph TD
    A[用户在线状态管理] --> B[状态维护]
    A --> C[状态查询]
    A --> D[状态订阅]
    A --> E[状态变更通知]
    
    B --> B1[PC端状态维护]
    B --> B2[手柄端状态维护]
    B --> B3[心跳机制]
    B --> B4[超时检测]
    
    C --> C1[单用户状态查询]
    C --> C2[批量状态查询]
    C --> C3[在线用户列表]
    C --> C4[在线飞手列表]
    
    D --> D1[状态变更订阅]
    D --> D2[订阅管理]
    
    E --> E1[上线通知]
    E --> E2[离线通知]
    E --> E3[状态变化通知]
```

#### 2.5.3 数据库设计

用户基本信息存储在`system_users`表中，但用户的在线状态主要存储在Redis缓存中以提高性能。以下是`system_users`表的结构及所需的更新：

```sql
-- 用户表更新脚本
ALTER TABLE `system_users` 
ADD COLUMN `online_status` tinyint DEFAULT 0 COMMENT '在线状态: 0-离线, 1-PC端在线, 2-手柄端在线, 3-双端在线',
ADD COLUMN `last_login_device` varchar(32) DEFAULT NULL COMMENT '最后登录设备类型: pc, pilot',
ADD COLUMN `last_heartbeat` bigint DEFAULT NULL COMMENT '最后心跳时间(时间戳)',
ADD INDEX `idx_online_status` (`online_status`),
ADD INDEX `idx_pilot` (`pilot`);

-- Redis中存储用户在线状态的结构
-- 键名格式：user:online:user:{user_id}
-- 值: {"userId":"xxx", "username":"user1", "deviceType":"pc/pilot", "lastHeartbeat":1686123456789, "ip":"*************", "deviceInfo":"Chrome 92.0", "sessionId":"xxx", "isPilot": true/false, "onlineStatus": 1}
-- 过期时间: 30分钟(WEBSOCKET_SESSION_TIMEOUT=1800秒)

-- PC端会话键: session:pc:{session_id} -> user_id
-- 手柄端会话键: session:pilot:{session_id} -> user_id
-- 会话信息键: session:info:{session_id} -> {userId, username, deviceType, createTime, lastHeartbeat}
```

#### 2.5.4 多设备登录与优先级管理

系统支持用户通过不同设备（PC端和手柄端）同时登录，并根据以下规则管理在线状态：

1. **在线状态定义**：
   - 0：离线
   - 1：PC端在线
   - 2：手柄端在线
   - 3：双端在线

2. **状态计算规则**：
   - PC端登录时，如之前未有设备在线，则状态为1
   - 手柄端登录时，如之前未有设备在线，则状态为2
   - 如PC端已在线，手柄端登录，则状态为3
   - 如手柄端已在线，PC端登录，则状态为3
   - 如双端在线，手柄端下线，则状态为1
   - 如双端在线，PC端下线，则状态为2
   - 如仅一端在线，该端下线，则状态为0

3. **数据持久化策略**：
   - Redis中存储实时状态信息，用于高性能查询
   - 数据库中的`online_status`字段按需同步更新，避免频繁操作数据库
   - 使用`lastDbUpdateTime`缓存最后数据库更新时间，确保数据库更新间隔不低于5分钟

4. **会话管理**：
   - 为减少数据库压力，采用Redis存储会话信息，设置30分钟过期时间
   - 使用会话映射关系快速查找用户ID，便于心跳处理
   - 提供会话异常恢复机制，当Redis状态丢失但会话仍活跃时能自动恢复

#### 2.5.5 基于WebSocket STOMP的心跳机制

系统采用基于WebSocket的STOMP协议实现心跳机制，具体实现如下：

1. **WebSocket连接建立**：
   - 用户登录后，客户端自动建立WebSocket连接
   - 连接成功后，通过STOMP协议进行消息订阅和发布
   - 服务端在握手时为每个连接分配唯一的会话ID

2. **STOMP心跳机制**：
   - 利用STOMP协议内置的心跳机制保持连接活跃
   - 服务端在`WebSocketConfiguration`中配置心跳检测
   - 心跳帧为轻量级帧，仅包含少量字节，减少网络开销
   - 使用`StompHeartbeatInterceptor`拦截器处理心跳消息，检测空消息体

3. **心跳处理流程**：
   - 客户端定期发送心跳，服务端通过`updateHeartbeat`方法更新最后心跳时间
   - 心跳时同时延长会话信息和用户状态的Redis过期时间
   - 超过指定间隔（默认5分钟）才会更新数据库中的状态信息
   - 使用`lastDbUpdateTime`缓存控制数据库更新频率

4. **心跳异常处理**：
   - 当检测不到心跳超过30分钟时，认为会话已断开
   - 服务端自动清理过期会话，更新用户在线状态
   - 支持用户状态缺失时的自动恢复机制

```mermaid
sequenceDiagram
    participant PCClient as PC客户端
    participant ControllerClient as 手柄客户端
    participant WebSocket as WebSocket服务
    participant STOMP as STOMP代理
    participant StatusService as 状态管理服务
    participant Redis as Redis缓存
    participant DB as 数据库
    participant Subscribers as 状态订阅者
    
    PCClient->>WebSocket: 1a. 建立WebSocket连接(device_type=pc)
    WebSocket->>STOMP: 2a. 升级为STOMP协议
    STOMP->>StatusService: 3a. 处理PC端连接
    StatusService->>Redis: 4a. 记录PC端会话状态
    StatusService->>DB: 5a. 更新system_users表online_status=1
    StatusService->>Subscribers: 6a. 广播用户PC端上线事件
    
    ControllerClient->>WebSocket: 1b. 建立WebSocket连接(device_type=pilot)
    WebSocket->>STOMP: 2b. 升级为STOMP协议
    STOMP->>StatusService: 3b. 处理手柄端连接
    StatusService->>Redis: 4b. 记录手柄端会话状态
    StatusService->>StatusService: 5b. 检查现有会话和优先级
    StatusService->>DB: 6b. 更新system_users表online_status=2
    StatusService->>Subscribers: 7b. 广播用户手柄端上线事件
    
    loop STOMP心跳循环(每15秒)
        PCClient->>STOMP: 8a. 发送PC端STOMP心跳帧
        ControllerClient->>STOMP: 8b. 发送手柄端STOMP心跳帧
        STOMP->>StatusService: 9. 处理心跳事件
        StatusService->>Redis: 10. 更新最后心跳时间
    end
    
    alt 手柄端断开连接
        Note over ControllerClient,STOMP: 手柄端心跳中断
        STOMP->>StatusService: 11a. 检测手柄端心跳超时
        StatusService->>Redis: 12a. 更新手柄端会话状态为离线
        StatusService->>StatusService: 13a. 重新计算用户综合状态
        StatusService->>DB: 14a. 更新system_users表online_status=1
        StatusService->>Subscribers: 15a. 广播用户状态变更事件
    else PC端断开连接
        Note over PCClient,STOMP: PC端心跳中断
        STOMP->>StatusService: 11b. 检测PC端心跳超时
        StatusService->>Redis: 12b. 更新PC端会话状态为离线
        StatusService->>StatusService: 13b. 重新计算用户综合状态
        StatusService->>DB: 14b. 更新system_users表online_status=2
        StatusService->>Subscribers: 15b. 广播用户PC端离线事件
    end
```

#### 2.5.6 STOMP主题设计与状态订阅

基于STOMP协议的主题机制，系统设计了以下主题用于状态变更通知和订阅：

1. **用户状态主题**：
   - `/topic/user/status` - 全局用户状态变更主题，所有用户状态变更都会发布到此主题
   - `/topic/user/status/{userId}` - 特定用户的状态变更主题，只包含该用户的状态变更

2. **状态订阅机制**：
   - 客户端通过STOMP协议订阅感兴趣的主题
   - 服务端状态变更时，向相应主题发布消息
   - 订阅者实时接收状态变更通知，无需轮询

3. **通知消息格式**：
   - 状态变更消息采用JSON格式
   - 包含用户ID、设备类型、状态类型、时间戳等信息
   - 增加飞手标识和综合在线状态信息

```json
{
  "userId": "user123",
  "username": "pilot01",
  "isPilot": true,
  "onlineStatus": 2,
  "deviceType": "pilot",
  "statusType": "online",
  "timestamp": 1686123456789,
  "deviceInfo": "Android 12.0",
  "ip": "*************",
  "sessionId": "session123",
  "lastHeartbeat": 1686123456789
}
```

#### 2.5.7 接口设计

```plain
# WebSocket STOMP连接端点
WS   /api/v1/ws                     # WebSocket STOMP连接端点

# STOMP订阅主题
SUBSCRIBE /topic/user/status        # 订阅全局用户状态变更
SUBSCRIBE /topic/user/status/{userId}   # 订阅特定用户状态变更

# REST API接口
GET  /api/user/status/{userId}          # 获取指定用户在线状态
GET  /api/user/status/online            # 获取所有在线用户列表
GET  /api/user/status/online/pilots     # 获取在线飞手列表
GET  /api/user/status/sessions/{userId} # 获取用户活跃会话列表
```

#### 2.5.8 与语音通话模块的集成

用户在线状态管理模块通过WebSocket STOMP机制为语音通话模块提供实时状态支持：

1. **实时状态监控**：
   - 语音通话模块订阅用户状态主题，实时获取状态变更
   - 当通话参与者状态变更时，立即接收通知并处理

2. **呼叫前检查**：
   - 发起通话前，通过状态查询接口获取用户的最新在线状态
   - 仅允许对在线用户发起通话请求

3. **会话状态同步**：
   - 通话过程中实时监控参与者状态
   - 当参与者离线时，自动处理通话状态变更

```mermaid
sequenceDiagram
    participant UserA as 用户A
    participant StompA as A的STOMP客户端
    participant Server as STOMP服务端
    participant StompB as B的STOMP客户端
    participant UserB as 用户B
    participant Call as 通话模块
    
    UserA->>StompA: 1. 连接WebSocket
    StompA->>Server: 2. 建立STOMP连接
    UserB->>StompB: 3. 连接WebSocket
    StompB->>Server: 4. 建立STOMP连接
    
    StompA->>Server: 5. 订阅用户状态主题
    Server->>StompA: 6. 发送在线用户列表
    
    UserA->>Call: 7. 发起对用户B的通话
    Call->>StompA: 8. 检查用户B的在线状态
    StompA->>Call: 9. 确认用户B在线
    
    Call->>Server: 10. 发送通话请求消息
    Server->>StompB: 11. 转发通话请求给用户B
    StompB->>UserB: 12. 显示来电界面
    
    alt 用户B网络异常
        Note over StompB,Server: B的STOMP心跳超时
        Server->>Server: 13a. 检测到用户B离线
        Server->>StompA: 14a. 发布用户B离线状态
        StompA->>Call: 15a. 通知通话模块用户B离线
        Call->>UserA: 16a. 提示"对方已离线"
    else 通话正常进行
        UserB->>StompB: 13b. 接受通话
        StompB->>Server: 14b. 发送接受通话消息
        Server->>StompA: 15b. 转发接受通话消息
        StompA->>Call: 16b. 建立语音通道
    end
```

### 2.6 语音通话模块

#### 2.6.1 功能描述

语音通话模块用于系统用户之间的实时语音通信，支持四种呼叫场景：PC端呼叫手柄端、手柄端呼叫PC端、手柄端之间互相呼叫，以及PC端之间互相呼叫。该功能方便在无人机执行任务过程中进行指挥调度和信息沟通。

支持一对一通话和多人通话功能，多人通话支持动态加入参与者，支持实时查看通话状态，并能够进行群组式通话。特别适用于指挥中心对多个无人机操作员进行协调指挥的场景。通话过程中能够有效处理网络异常、参与者中途离开、通话超时等各种异常情况，确保指挥通信的稳定可靠。

该模块依赖于用户在线状态管理模块，在发起通话前需检查目标用户的在线状态，并在通话过程中实时响应参与者的状态变化。特别关注飞手用户的在线状态，确保指挥中心能够随时与执行任务的飞手建立通话连接。

#### 2.6.2 功能结构

```mermaid
graph TD
    A[语音通话] --> B[呼叫功能]
    A --> C[通话管理]
    A --> D[通话记录]
    
    B --> B1[PC呼叫手柄]
    B --> B2[手柄呼叫PC]
    B --> B3[手柄呼叫手柄]
    B --> B4[PC呼叫PC]
    
    C --> C1[一对一通话]
    C --> C2[多人通话]
    C --> C3[参与者管理]
    C --> C4[通话异常处理]
    
    D --> D1[通话记录查询]
    D --> D2[按用户筛选]
    D --> D3[按设备筛选]
```

#### 2.6.3 数据库设计

为支持一对一通话和动态加入多人通话的场景，语音通话模块采用双表设计：通话会话表和通话参与者表。

```sql
-- 通话会话表：记录每个通话会话的基本信息
CREATE TABLE uav_call_session (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话标识UUID',
  `agora_channel` varchar(64) NOT NULL COMMENT '声网频道ID',
  `creator_id` varchar(64) NOT NULL COMMENT '创建者ID',
  `creator_type` varchar(16) NOT NULL COMMENT '创建者类型：pc、controller',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration` int DEFAULT 0 COMMENT '会话持续时长(秒)',
  `status` varchar(16) NOT NULL COMMENT '会话状态：active(进行中)、ended(已结束)',
  `participant_count` int DEFAULT 0 COMMENT '参与人数',
  `remark` varchar(256) DEFAULT '' COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_session_id` (`session_id`),
  INDEX `idx_creator_id` (`creator_id`)
);

-- 通话参与者表：记录每个通话会话中的参与人员信息
CREATE TABLE uav_call_participant (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `session_id` varchar(64) NOT NULL COMMENT '关联的会话ID',
  `user_id` varchar(64) NOT NULL COMMENT '参与者ID',
  `user_type` varchar(16) NOT NULL COMMENT '参与者类型：pc、controller',
  `role` varchar(16) NOT NULL COMMENT '角色：caller(呼叫方)、callee(被叫方)',
  `join_time` datetime DEFAULT NULL COMMENT '加入时间',
  `leave_time` datetime DEFAULT NULL COMMENT '离开时间',
  `duration` int DEFAULT 0 COMMENT '参与时长(秒)',
  `status` varchar(16) NOT NULL COMMENT '状态：pending(待接听)、rejected(已拒绝)、joined(已加入)、left(已离开)',
  `remark` varchar(256) DEFAULT '' COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  INDEX `idx_session_id` (`session_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_status` (`status`)
);
```

#### 2.6.4 声网集成架构与通话流程

##### 声网集成架构

本系统采用声网(Agora)作为实时音视频通信平台，实现一对一和多人通话功能。以下是声网集成的系统架构：

```mermaid
graph TD
    subgraph 客户端
        PC[PC客户端]
        Pilot[手柄端]
    end
    
    subgraph 服务端
        API[系统API服务]
        CallService[通话服务]
        TokenService[Token服务]
        StateManager[状态管理服务]
        DB[(数据库)]
    end
    
    subgraph 声网服务
        AgoraRTC[声网RTC服务]
        AgoraRTM[声网实时消息服务]
    end
    
    PC -- 集成SDK --> AgoraRTC
    PC -- 集成SDK --> AgoraRTM
    Pilot -- 集成SDK --> AgoraRTC
    Pilot -- 集成SDK --> AgoraRTM
    
    PC <--> API
    Pilot <--> API
    API <--> CallService
    CallService <--> TokenService
    CallService <--> StateManager
    StateManager <--> DB
    TokenService <--> AgoraRTC
    StateManager <--> AgoraRTM
```

##### 声网集成流程

1. **SDK集成**
   - PC端和手柄端集成声网SDK(Agora RTC SDK和Agora RTM SDK)
   - 服务端与声网平台对接，管理通话会话和Token生成

2. **声网接口配置**
   - 注册声网账号，获取App ID和App Certificate
   - 配置声网项目权限，启用实时通话和实时消息功能
   - 生成临时Token用于客户端鉴权
   - 设置心跳检测机制，及时发现异常连接

3. **通话流程**

```mermaid
sequenceDiagram
    participant Caller as 呼叫方
    participant Server as 系统服务端
    participant Agora as 声网服务
    participant Callee as 被叫方
    participant ThirdParty as 其他参与者
    
    Caller->>Server: 1. 发起通话请求
    Server->>Server: 2. 创建通话会话记录
    Server->>Server: 3. 生成声网Token
    Server->>Caller: 4. 返回Token和频道信息
    
    Caller->>Agora: 5. 加入声网频道
    Server->>Callee: 6. 推送通话请求
    Note over Callee: 7. 显示来电界面
    
    alt 接受通话
        Callee->>Server: 8a. 接受通话
        Server->>Callee: 9a. 返回Token和频道信息
        Callee->>Agora: 10a. 加入声网频道
        Note over Caller,Callee: 11a. 实时通话建立
        
        opt 添加其他参与者
            Caller->>Server: 12. 发送添加参与者请求
            Server->>Server: 13. 增加参与者计数
            Server->>ThirdParty: 14. 推送通话邀请
            
            alt 接受加入
                ThirdParty->>Server: 15a. 接受通话
                Server->>ThirdParty: 16a. 返回Token和频道信息
                ThirdParty->>Agora: 17a. 加入声网频道
                Note over Caller,ThirdParty: 18a. 多人实时通话
            else 拒绝加入
                ThirdParty->>Server: 15b. 拒绝通话
                Server->>Caller: 16b. 通知被拒绝
                Server->>Server: 17b. 更新参与者状态为rejected
            else 超时未应答
                Server->>Server: 15c. 检测到邀请超时
                Server->>Caller: 16c. 通知邀请超时
                Server->>Server: 17c. 清理未响应的参与者记录
            end
        end
        
        alt 参与者离开通话
            Callee->>Agora: 19a. 离开频道
            Callee->>Server: 20a. 通知离开通话
            Server->>Server: 21a. 更新参与者状态为left
            Server->>Caller: 22a. 通知参与者已离开(可选)
            
            alt 会话中无其他参与者
                Server->>Server: 23a. 检测到仅剩发起者
                Server->>Caller: 24a. 通知所有参与者已离开
            end
        end
        
        alt 发起人结束整个会话
            Caller->>Server: 19b. 结束整个通话会话
            Server->>Server: 20b. 更新会话状态为ended
            Server->>AllParticipants: 21b. 通知会话已结束
            Server->>Server: 22b. 更新所有参与者状态
            AllParticipants->>Agora: 23b. 所有人离开频道
        end
        
        alt 网络异常断开
            Note over Caller,Agora: 19c. 检测到网络中断
            Agora->>Server: 20c. 上报连接异常(通过心跳检测)
            Server->>Caller: 21c. 尝试重连通知
            
            alt 重连成功
                Caller->>Agora: 22c1. 重新连接频道
                Caller->>Server: 23c1. 通知重连成功
                Server->>Server: 24c1. 恢复参与者状态
            else 重连失败
                Server->>Server: 22c2. 超过重连时间
                Server->>AllParticipants: 23c2. 通知连接中断
                Server->>Server: 24c2. 更新参与者状态为异常离开
            end
        end
        
    else 拒绝通话
        Callee->>Server: 8b. 拒绝通话
        Server->>Caller: 9b. 通知通话被拒绝
        Server->>Server: 10b. 更新通话记录和参与者状态
    else 未接听超时
        Note over Callee: 8c. 来电提醒超时未应答
        Server->>Server: 9c. 检测到呼叫超时
        Server->>Caller: 10c. 通知呼叫超时
        Server->>Server: 11c. 更新通话记录为未接听
    end
```

##### 四种通话场景说明

1. **PC端呼叫手柄端**
   - PC端显示所有在线手柄设备列表，特别标识飞手用户
   - 选择目标手柄发起呼叫，系统创建通话会话，生成声网Token
   - 手柄端收到呼叫推送后播放铃声并显示来电界面
   - 接受后加入同一声网频道，建立实时通话
   - 通话过程中可添加其他参与者，PC端可管理通话

2. **手柄端呼叫PC端**
   - 手柄端显示简化的在线PC用户列表
   - 选择PC用户并发起呼叫，系统创建通话会话，生成声网Token
   - PC端收到呼叫推送后播放提示音并显示来电窗口
   - 接受后加入同一声网频道，建立实时通话
   - 通话过程中任何参与者可添加其他人员

3. **手柄端呼叫手柄端**
   - 手柄端显示其他在线手柄设备列表，优先显示飞手用户
   - 选择目标手柄发起呼叫，系统创建通话会话，生成声网Token
   - 被叫手柄收到呼叫后显示来电界面
   - 接受后加入同一声网频道，建立实时通话
   - 系统可监测通话状态，处理异常连接情况

4. **PC端呼叫PC端**
   - PC端显示其他在线PC用户列表
   - 选择目标用户发起呼叫，系统创建通话会话，生成声网Token
   - 被叫PC收到呼叫后显示来电窗口
   - 接受后加入同一声网频道，建立实时通话
   - 支持通话中参与者变化，动态调整通话界面

5. **飞手用户优先处理**
   - 系统识别标记为飞手的用户，在通话相关界面中特别标识
   - 飞手用户的呼叫请求具有更高的显示优先级
   - 指挥中心可以查看所有在线飞手，并发起一对多通话
   - 飞手用户状态变更时，系统会向指挥中心发送通知

6. **异常处理机制**
   - 所有场景均支持呼叫超时自动取消
   - 网络异常时自动尝试重连，并保持通话状态
   - 参与者离开后，系统自动更新通话状态
   - 当会话仅剩发起者时，发送提示通知
   - 飞手用户断线重连时，可自动恢复到原通话会话

##### 多人通话与异常处理支持

本系统支持通话会话管理和异常情况处理：

1. **通话会话管理**：
   - 支持一对一通话动态添加参与者转为多人通话
   - 支持参与者实时加入和离开，保持通话连续性
   - 服务端维护参与者状态和计数，确保会话数据一致性

2. **会话控制**：
   - 会话创建者(发起人)可添加新参与者、管理参与者或结束整个会话
   - 普通参与者可自行加入或离开会话
   - 系统自动处理参与者离开后的会话状态更新

3. **异常情况处理**：
   - 呼叫超时处理：被叫方未接听超时自动结束呼叫
   - 网络中断处理：检测到连接异常，支持自动重连
   - 所有参与者离开处理：当仅剩发起者时提供通知
   - 会话异常结束处理：服务端定期检查并清理异常会话

4. **技术实现**：
   - 所有参与者加入同一个声网频道，实现实时通话
   - 使用声网RTM SDK实现实时消息传递，包括用户状态通知
   - 服务端设置心跳检测，及时发现异常连接并处理
   - 客户端实现自动重连和状态恢复机制

技术选型

| 技术方案       | 优缺点                                                       | 私有化部署            | 离线环境支持        | 成本                  |
| -------------- | ------------------------------------------------------------ | --------------------- | ------------------- | --------------------- |
| **声网Agora**  | **优点**：接入简单，使用成熟   **缺点**：需要互联网连接，数据经过第三方 | 不支持   (依赖云服务) | 不支持   (需互联网) | 高   (按通话时长计费) |
| **Jitsi Meet** | **优点**：开源免费，可自行搭建   **缺点**：配置复杂，需专业维护，界面无法调整、定制，没法做优化，目前在接处警app体验不理想 | 支持   (可私有部署)   | 支持   (局域网可用) | 中   (服务器成本)     |
| **ZLMediaKit** | **优点**：国产开源，功能全面   **缺点**：文档较少，社区支持有限，有问题不好排查，尤其是连接问题，优化空间有很大限制 | 支持   (可私有部署)   | 支持   (局域网可用) | 低   (开发成本高)     |
| **WebRTC**     | **优点**：开源免费，局域网可用   **缺点**：提升音质等优化需二次开发，优化空间大但开发难度也高 | 支持   (完全私有化)   | 支持   (局域网即可) | 高   (服务器成本)     |


#### 2.6.5 接口设计

```plain
# REST API接口

## 声网Token管理
GET  /api/call/token                # 获取声网Token
    参数: uid(Integer) - 声网UID
         channelName(String) - 频道名称，可选
    
POST /api/call/token/refresh        # 刷新Token
    参数: uid(Integer) - 声网UID
         channelName(String) - 频道名称

## 会话管理接口
POST /api/call/session/create       # 创建通话会话
    请求体: CreateCallSessionDTO
        {
            "creatorId": "用户ID",
            "creatorType": "设备类型(pc/controller)",
            "creatorName": "创建者名称",
            "callees": [
                {
                    "userId": "被叫用户ID",
                    "userType": "设备类型(pc/controller)"
                }
            ]
        }

GET  /api/call/session/{id}         # 获取会话详情
    路径参数: id - 会话ID

PUT  /api/call/session/end          # 结束会话
    请求体: CallSessionActionDTO
        {
            "sessionId": "会话ID",
            "userId": "用户ID",
            "targetUserIds": ["目标用户ID1", "目标用户ID2"]
        }

GET  /api/call/session/active       # 获取当前活动会话
    参数: userId(String) - 用户ID

## 用户状态接口
GET  /api/call/online-users         # 获取在线用户列表（支持过滤）
    参数:
        deviceType(String, 可选) - 设备类型过滤: pc-PC端, pilot-飞手端, 空值/不传-所有设备(默认)
        userType(String, 可选) - 用户类型过滤: pilot-仅飞手, normal-仅普通用户, 空值/不传-所有用户(默认)

    示例:
        /api/call/online-users                          # 获取所有在线用户
        /api/call/online-users?deviceType=pc            # 获取在线PC用户
        /api/call/online-users?userType=pilot           # 获取在线飞手
        /api/call/online-users?deviceType=pilot         # 获取在线飞手端设备
        /api/call/online-users?deviceType=pc&userType=normal  # 获取在线PC端普通用户
        /api/call/online-users?deviceType=&userType=    # 传空值，等同于获取所有在线用户

## 通话控制接口
POST /api/call/answer               # 接听通话
    请求体: CallSessionActionDTO
        {
            "sessionId": "会话ID",
            "userId": "用户ID"
        }

POST /api/call/reject               # 拒绝通话
    请求体: CallSessionActionDTO
        {
            "sessionId": "会话ID",
            "userId": "用户ID"
        }

POST /api/call/leave                # 离开通话
    请求体: CallSessionActionDTO
        {
            "sessionId": "会话ID",
            "userId": "用户ID"
        }

POST /api/call/add-participant      # 添加参与者
    请求体: CallSessionActionDTO
        {
            "sessionId": "会话ID",
            "userId": "操作用户ID",
            "targetUserIds": ["目标用户ID1", "目标用户ID2"]
        }

# WebSocket STOMP接口

## 连接端点
WS   /api/v1/ws                 # WebSocket STOMP连接端点

## 发送目的地
SEND /app/call/heartbeat            # 发送心跳消息
    消息格式:
        {
            "sessionId": "会话ID",
            "userId": "用户ID"
        }

SEND /app/call/event                # 发送通话事件
    消息格式:
        {
            "eventType": "事件类型",
            "sessionId": "会话ID",
            "userId": "用户ID"
        }

## 订阅主题
SUBSCRIBE /topic/call/incoming/{userId}  # 订阅来电通知
    消息格式:
        {
            "sessionId": "会话ID",
            "callerId": "呼叫者ID",
            "callerName": "呼叫者名称",
            "channelName": "声网频道名"
        }

SUBSCRIBE /topic/call/end/{userId}       # 订阅通话结束通知
    消息格式:
        {
            "sessionId": "会话ID"
        }

SUBSCRIBE /topic/call/answer/{userId}    # 订阅通话接听通知
    消息格式:
        {
            "sessionId": "会话ID",
            "userId": "接听用户ID",
            "userName": "接听用户名称"
        }

SUBSCRIBE /topic/call/reject/{userId}    # 订阅通话拒绝通知
    消息格式:
        {
            "sessionId": "会话ID",
            "userId": "拒绝用户ID",
            "userName": "拒绝用户名称"
        }

SUBSCRIBE /topic/call/leave/{userId}     # 订阅用户离开通知
    消息格式:
        {
            "sessionId": "会话ID",
            "userId": "离开用户ID",
            "userName": "离开用户名称"
        }
```

#### 2.6.5.1 客户端集成说明

##### PC端集成

1. **REST API调用流程**:
   - 登录后获取用户ID
   - 获取在线用户或飞手列表
   - 发起通话时创建会话
   - 处理通话接听/拒绝/离开等操作

2. **WebSocket连接步骤**:
   - 建立WebSocket连接并连接STOMP客户端
   - 订阅相关主题: `/topic/call/incoming/{userId}`, `/topic/call/end/{userId}`等
   - 每30秒发送心跳消息到`/app/call/heartbeat`

3. **通话处理流程**:
   - 收到来电通知时显示来电界面
   - 接听/拒绝通话调用相应API
   - 监听通话事件并更新UI状态
   - 使用声网SDK加入/退出频道

##### 手柄端集成

1. **REST API调用流程**:
   - 与PC端相同，但使用简化的UI界面
   - 通话功能优先考虑语音交互

2. **WebSocket连接步骤**:
   - 与PC端相同，确保高优先级维持连接
   - 设置自动重连机制，处理网络波动

3. **通话处理流程**:
   - 简化的来电显示和操作
   - 针对手柄设备优化的声网SDK配置
   - 支持快捷接听/拒绝操作

#### 2.6.5.2 示例代码片段

```java
// WebSocket STOMP客户端连接示例
function connectStomp() {
    const socket = new SockJS('/api/v1/ws');
    const stompClient = Stomp.over(socket);
    
    stompClient.connect({}, frame => {
        console.log('Connected: ' + frame);
        
        // 订阅来电通知
        stompClient.subscribe('/topic/call/incoming/' + userId, notification => {
            const data = JSON.parse(notification.body);
            showIncomingCall(data);
        });
        
        // 订阅通话结束通知
        stompClient.subscribe('/topic/call/end/' + userId, notification => {
            const data = JSON.parse(notification.body);
            handleCallEnd(data);
        });
        
        // 发送心跳
        setInterval(() => {
            stompClient.send("/app/call/heartbeat", {}, 
                JSON.stringify({userId: userId}));
        }, 30000);
    });
}
```

#### ******* 错误码定义

```plain
CALL_PARAM_ERROR: 100400 - 调用参数错误
CALL_CREATOR_OFFLINE: 100401 - 创建者不在线
CALL_ALL_CALLEES_OFFLINE: 100402 - 所有被叫用户均不在线
CALL_SESSION_NOT_EXISTS: 100403 - 通话会话不存在
CALL_SESSION_ENDED: 100404 - 通话会话已结束
CALL_USER_NOT_CALLEE: 100405 - 用户不是被叫参与者
CALL_USER_NOT_PARTICIPANT: 100406 - 用户不是会话参与者
CALL_USER_NOT_JOINED: 100407 - 用户未加入会话
CALL_NOT_CREATOR: 100408 - 用户不是会话创建者
```

### 2.7 数据字典管理模块

#### 2.7.1 功能描述

数据字典管理模块用于统一定义和管理平台内通用选项，避免相同选项在不同场景中出现不一致的定义，从而保证系统数据的规范性和一致性。数据字典采用单表树状结构进行管理，实现父子级关系，便于分类和查找。

#### 2.7.2 功能结构

```mermaid
graph TD
    A[数据字典管理] --> B[字典列表]
    A --> C[新建字典]
    A --> D[修改字典]
    A --> E[删除字典]
    
    B --> B1[树形结构展示]
    B --> B2[筛选查询]
    
    C --> C1[新建字典分类]
    C --> C2[新建字典项]
    
    D --> D1[修改字典信息]
    D --> D2[字典排序]
    
    E --> E1[删除字典项]
    E --> E2[删除字典分类]
```

#### 2.7.3 数据库设计

```sql
-- 数据字典表（树形结构）
CREATE TABLE `system_dict` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '字典编码',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '字典名称',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '字典值',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述',
  `parent_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '-1' COMMENT '父级编码，顶级为-1',
  `parent_id` bigint NOT NULL DEFAULT -1 COMMENT '父级ID，顶级为-1',
  `full_path` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '节点完整路径',
  `sort_no` int NOT NULL DEFAULT 0 COMMENT '排序号',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态（0正常 1停用）',
  `color_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '颜色类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT 'css样式',
  `is_sys` tinyint NOT NULL DEFAULT 0 COMMENT '是否系统内置（0否 1是）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `deleted_time` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_dict_code` (`dict_code`),
  KEY `idx_parent_code` (`parent_code`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据字典表';
```

#### 2.7.4 字典数据时序图

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant Server as 系统服务端
    participant DB as 数据库
    
    note over Admin,DB: 字典管理
    Admin->>Server: 请求字典树列表
    Server->>DB: 查询字典数据
    DB->>Server: 返回字典列表
    Server->>Admin: 显示字典树列表
    
    alt 新建字典分类
        Admin->>Server: 点击"新建字典分类"
        Server->>Admin: 显示字典表单
        Admin->>Server: 提交字典分类信息
        Server->>DB: 保存字典数据(parent_code=-1)
    else 新建字典项
        Admin->>Server: 选择字典分类
        Admin->>Server: 点击"新建字典项"
        Server->>Admin: 显示字典表单
        Admin->>Server: 提交字典项信息
        Server->>DB: 保存字典数据(parent_code=分类编码)
    else 修改字典
        Admin->>Server: 点击"编辑"按钮
        Server->>DB: 获取字典详情
        DB->>Server: 返回字典数据
        Server->>Admin: 显示编辑表单
        Admin->>Server: 提交修改信息
        Server->>DB: 更新字典数据
    else 删除字典
        Admin->>Server: 点击"删除"按钮
        Server->>Admin: 显示确认对话框
        Admin->>Server: 确认删除
        Server->>DB: 检查字典是否有子项
        alt 有子项
            Server->>Admin: 提示不能删除有子项的字典
        else 无子项
            Server->>DB: 标记字典为已删除
        end
    end
```

#### 2.7.5 接口设计

```plain
# 字典管理接口
GET  /api/dict/list                # 获取字典树列表
GET  /api/dict/children/{code}     # 获取指定编码的子字典
POST /api/dict/create              # 创建字典
PUT  /api/dict/{id}                # 更新字典
DELETE /api/dict/{id}              # 删除字典
GET  /api/dict/{id}                # 获取字典详情
GET  /api/dict/code/{dictCode}     # 根据字典编码获取字典信息
```

#### 2.7.6 数据字典编码值定义

为确保系统中使用的字典数据一致性和规范性，以下定义各业务表中字典类型字段的标准编码值。在新的树状结构中，字典值按层级组织，顶级节点为分类，子节点为具体值。

##### 2.7.6.1 预警相关字典

```sql
-- 预警类型字典（顶级分类）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`, `is_sys`) 
VALUES ('alarm_type', '预警类型', NULL, '-1', -1, '/alarm_type', 1, 0, 1);

-- 预警类型值（子节点）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`) 
VALUES 
('alarm_type_fire', '烟火', 'fire', 'alarm_type', #{ID_alarm_type}, '/alarm_type/alarm_type_fire', 1, 0),        -- 发现明火或烟雾
('alarm_type_intrusion', '入侵', 'intrusion', 'alarm_type', #{ID_alarm_type}, '/alarm_type/alarm_type_intrusion', 2, 0),   -- 未授权人员进入限制区域
('alarm_type_no_fly', '禁飞', 'no_fly', 'alarm_type', #{ID_alarm_type}, '/alarm_type/alarm_type_no_fly', 3, 0),      -- 在禁飞区域发现飞行器
('alarm_type_occupation', '占用', 'occupation', 'alarm_type', #{ID_alarm_type}, '/alarm_type/alarm_type_occupation', 4, 0);  -- 重要通道或区域被占用

-- 预警来源字典（顶级分类）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`, `is_sys`) 
VALUES ('alarm_source', '预警来源', NULL, '-1', -1, '/alarm_source', 2, 0, 1);

-- 预警来源值（子节点）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`) 
VALUES ('alarm_source_ai', 'AI识别', 'ai', 'alarm_source', #{ID_alarm_source}, '/alarm_source/alarm_source_ai', 1, 0);  -- AI算法自动识别的预警

-- 预警状态字典（顶级分类）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`, `is_sys`) 
VALUES ('alarm_status', '预警状态', NULL, '-1', -1, '/alarm_status', 3, 0, 1);

-- 预警状态值（子节点）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`) 
VALUES 
('alarm_status_pending', '未处理', 'pending', 'alarm_status', #{ID_alarm_status}, '/alarm_status/alarm_status_pending', 1, 0),    -- 新产生尚未处理的预警
('alarm_status_processing', '处理中', 'processing', 'alarm_status', #{ID_alarm_status}, '/alarm_status/alarm_status_processing', 2, 0), -- 已分配正在处理的预警
('alarm_status_processed', '已处理', 'processed', 'alarm_status', #{ID_alarm_status}, '/alarm_status/alarm_status_processed', 3, 0);  -- 已完成处理的预警

-- 预警处置结果字典（顶级分类）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`, `is_sys`) 
VALUES ('alarm_result', '预警处置结果', NULL, '-1', -1, '/alarm_result', 4, 0, 1);

-- 预警处置结果值（子节点）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`) 
VALUES 
('alarm_result_to_work', '转工单', 'to_work', 'alarm_result', #{ID_alarm_result}, '/alarm_result/alarm_result_to_work', 1, 0),  -- 确认预警并转为工单处理
('alarm_result_close', '直接关闭', 'close', 'alarm_result', #{ID_alarm_result}, '/alarm_result/alarm_result_close', 2, 0);  -- 确认为误报或已处理完成
```

##### 2.7.6.2 工单相关字典

```sql
-- 工单类型字典（顶级分类）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`, `is_sys`) 
VALUES ('work_type', '工单类型', NULL, '-1', -1, '/work_type', 5, 0, 1);

-- 工单类型值（子节点）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`) 
VALUES 
('work_type_accident', '事故处置', 'accident', 'work_type', #{ID_work_type}, '/work_type/work_type_accident', 1, 0),  -- 突发事故的处理工单
('work_type_hazard', '隐患排查', 'hazard', 'work_type', #{ID_work_type}, '/work_type/work_type_hazard', 2, 0),    -- 安全隐患排查工单
('work_type_routine', '日常巡检', 'routine', 'work_type', #{ID_work_type}, '/work_type/work_type_routine', 3, 0);   -- 常规巡检工单

-- 工单来源字典（顶级分类）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`, `is_sys`) 
VALUES ('work_source', '工单来源', NULL, '-1', -1, '/work_source', 6, 0, 1);

-- 工单来源值（子节点）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`) 
VALUES 
('work_source_manual', '人工创建', 'manual', 'work_source', #{ID_work_source}, '/work_source/work_source_manual', 1, 0),  -- 用户手动创建的工单
('work_source_alarm', '预警转入', 'alarm', 'work_source', #{ID_work_source}, '/work_source/work_source_alarm', 2, 0);   -- 由预警转化而来的工单

-- 工单状态字典（顶级分类）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`, `is_sys`) 
VALUES ('work_status', '工单状态', NULL, '-1', -1, '/work_status', 7, 0, 1);

-- 工单状态值（子节点）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`) 
VALUES 
('work_status_unassigned', '未分配', 'unassigned', 'work_status', #{ID_work_status}, '/work_status/work_status_unassigned', 1, 0), -- 尚未分配处理人的工单
('work_status_processing', '处理中', 'processing', 'work_status', #{ID_work_status}, '/work_status/work_status_processing', 2, 0), -- 已分配并正在处理的工单
('work_status_closed', '已关闭', 'closed', 'work_status', #{ID_work_status}, '/work_status/work_status_closed', 3, 0);     -- 处理完成并已关闭的工单

-- 工单优先级字典（顶级分类）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`, `is_sys`) 
VALUES ('work_level', '工单优先级', NULL, '-1', -1, '/work_level', 8, 0, 1);

-- 工单优先级值（子节点）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`, `color_type`) 
VALUES 
('work_level_highest', '最高', 'highest', 'work_level', #{ID_work_level}, '/work_level/work_level_highest', 1, 0, 'danger'),   -- 需立即处理的紧急工单
('work_level_high', '高', 'high', 'work_level', #{ID_work_level}, '/work_level/work_level_high', 2, 0, 'warning'),       -- 优先处理的重要工单
('work_level_medium', '中', 'medium', 'work_level', #{ID_work_level}, '/work_level/work_level_medium', 3, 0, 'primary'),     -- 正常优先级的工单
('work_level_low', '低', 'low', 'work_level', #{ID_work_level}, '/work_level/work_level_low', 4, 0, 'info'),           -- 可延后处理的工单
('work_level_lowest', '最低', 'lowest', 'work_level', #{ID_work_level}, '/work_level/work_level_lowest', 5, 0, 'success');   -- 闲时处理的工单
```

##### 2.7.6.3 地图数据相关字典

```sql
-- 图层类型字典（顶级分类）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`, `is_sys`) 
VALUES ('layer_type', '图层类型', NULL, '-1', -1, '/layer_type', 9, 0, 1);

-- 图层类型值（子节点）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`) 
VALUES 
('layer_type_point', '点', 'point', 'layer_type', #{ID_layer_type}, '/layer_type/layer_type_point', 1, 0),            -- 点状地图元素
('layer_type_line', '线', 'line', 'layer_type', #{ID_layer_type}, '/layer_type/layer_type_line', 2, 0),             -- 线状地图元素
('layer_type_polygon', '面', 'polygon', 'layer_type', #{ID_layer_type}, '/layer_type/layer_type_polygon', 3, 0),          -- 面状地图元素
('layer_type_roadnetwork', '路网', 'roadnetwork', 'layer_type', #{ID_layer_type}, '/layer_type/layer_type_roadnetwork', 4, 0),    -- 道路网络图层
('layer_type_tile', '栅格', 'tile', 'layer_type', #{ID_layer_type}, '/layer_type/layer_type_tile', 5, 0);           -- 栅格类图层
```

##### 2.7.6.4 电子围栏相关字典

```sql
-- 围栏类型字典（顶级分类）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`, `is_sys`) 
VALUES ('fence_type', '围栏类型', NULL, '-1', -1, '/fence_type', 10, 0, 1);

-- 围栏类型值（子节点）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`) 
VALUES 
('fence_type_intrusion', '入侵', 'intrusion', 'fence_type', #{ID_fence_type}, '/fence_type/fence_type_intrusion', 1, 0),  -- 检测目标进入围栏区域
('fence_type_leave', '离开', 'leave', 'fence_type', #{ID_fence_type}, '/fence_type/fence_type_leave', 2, 0);      -- 检测目标离开围栏区域

-- 识别目标字典（顶级分类）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`, `is_sys`) 
VALUES ('fence_target', '识别目标', NULL, '-1', -1, '/fence_target', 11, 0, 1);

-- 识别目标值（子节点）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`) 
VALUES 
('fence_target_vehicle', '车辆', 'vehicle', 'fence_target', #{ID_fence_target}, '/fence_target/fence_target_vehicle', 1, 0),  -- 识别车辆目标
('fence_target_person', '人员', 'person', 'fence_target', #{ID_fence_target}, '/fence_target/fence_target_person', 2, 0),   -- 识别人员目标
('fence_target_animal', '动物', 'animal', 'fence_target', #{ID_fence_target}, '/fence_target/fence_target_animal', 3, 0),   -- 识别动物目标
('fence_target_other', '其他', 'other', 'fence_target', #{ID_fence_target}, '/fence_target/fence_target_other', 4, 0);    -- 识别其他类型目标

-- 业务类型字典（顶级分类）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`, `is_sys`) 
VALUES ('business_type', '围栏业务类型', NULL, '-1', -1, '/business_type', 12, 0, 1);

-- 业务类型值（子节点）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`) 
VALUES 
('business_type_fire_access', '消防通道占用', 'fire_access', 'business_type', #{ID_business_type}, '/business_type/business_type_fire_access', 1, 0),  -- 消防通道被占用监测
('business_type_person_leave', '人员离岗', 'person_leave', 'business_type', #{ID_business_type}, '/business_type/business_type_person_leave', 2, 0),     -- 工作人员离岗监测
('business_type_area_security', '区域安全', 'area_security', 'business_type', #{ID_business_type}, '/business_type/business_type_area_security', 3, 0);    -- 安全区域入侵监测

-- 前期处置措施字典（顶级分类）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`, `is_sys`) 
VALUES ('pre_process', '前期处置措施', NULL, '-1', -1, '/pre_process', 13, 0, 1);

-- 前期处置措施值（子节点）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`) 
VALUES 
('pre_process_speaker', '喊话器喊话', 'speaker', 'pre_process', #{ID_pre_process}, '/pre_process/pre_process_speaker', 1, 0),     -- 触发无人机喊话器喊话
('pre_process_searchlight', '打开探照灯', 'searchlight', 'pre_process', #{ID_pre_process}, '/pre_process/pre_process_searchlight', 2, 0), -- 开启无人机探照灯照明
('pre_process_sms', '发送短信通知', 'sms', 'pre_process', #{ID_pre_process}, '/pre_process/pre_process_sms', 3, 0),       -- 向相关人员发送短信通知
('pre_process_record', '自动录像', 'record', 'pre_process', #{ID_pre_process}, '/pre_process/pre_process_record', 4, 0);        -- 开始录制视频存档

-- 围栏状态字典（顶级分类）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`, `is_sys`) 
VALUES ('fence_status', '围栏状态', NULL, '-1', -1, '/fence_status', 14, 0, 1);

-- 围栏状态值（子节点）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`, `color_type`) 
VALUES 
('fence_status_active', '活动', 'active', 'fence_status', #{ID_fence_status}, '/fence_status/fence_status_active', 1, 0, 'success'),       -- 围栏正常启用状态
('fence_status_paused', '暂停', 'paused', 'fence_status', #{ID_fence_status}, '/fence_status/fence_status_paused', 2, 0, 'warning'),       -- 围栏暂时停用状态
('fence_status_disabled', '已禁用', 'disabled', 'fence_status', #{ID_fence_status}, '/fence_status/fence_status_disabled', 3, 0, 'danger');    -- 围栏长期禁用状态

-- 围栏任务类型字典（顶级分类）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`, `is_sys`) 
VALUES ('fence_task_type', '围栏任务类型', NULL, '-1', -1, '/fence_task_type', 15, 0, 1);


##### 2.7.6.5 附件相关字典

```sql
-- 附件类型字典（顶级分类）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`, `is_sys`) 
VALUES ('attachment_type', '附件类型', NULL, '-1', -1, '/attachment_type', 16, 0, 1);

-- 附件类型值（子节点）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`) 
VALUES 
('attachment_type_alarm', '预警附件', 'alarm', 'attachment_type', #{ID_attachment_type}, '/attachment_type/attachment_type_alarm', 1, 0),        -- 预警证据图片、现场视频等
('attachment_type_work', '工单附件', 'work', 'attachment_type', #{ID_attachment_type}, '/attachment_type/attachment_type_work', 2, 0),         -- 工单相关图片、处置记录文件等
('attachment_type_ai_event', 'AI事件附件', 'ai_event', 'attachment_type', #{ID_attachment_type}, '/attachment_type/attachment_type_ai_event', 3, 0),  -- AI算法识别的原始图片、标记图片等
('attachment_type_fence', '围栏附件', 'fence', 'attachment_type', #{ID_attachment_type}, '/attachment_type/attachment_type_fence', 4, 0),        -- 围栏区域截图、触发事件图片等
('attachment_type_system', '系统附件', 'system', 'attachment_type', #{ID_attachment_type}, '/attachment_type/attachment_type_system', 5, 0);       -- 系统日志、配置文件等

-- 文件类型字典（顶级分类）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`, `is_sys`) 
VALUES ('file_type', '文件类型', NULL, '-1', -1, '/file_type', 17, 0, 1);

-- 文件类型值（子节点）
INSERT INTO `system_dict` (`dict_code`, `dict_name`, `dict_value`, `parent_code`, `parent_id`, `full_path`, `sort_no`, `status`) 
VALUES 
('file_type_image', '图片', 'image', 'file_type', #{ID_file_type}, '/file_type/file_type_image', 1, 0),          -- 图片文件
('file_type_video', '视频', 'video', 'file_type', #{ID_file_type}, '/file_type/file_type_video', 2, 0),          -- 视频文件
('file_type_audio', '音频', 'audio', 'file_type', #{ID_file_type}, '/file_type/file_type_audio', 3, 0),          -- 音频文件
('file_type_document', '文档', 'document', 'file_type', #{ID_file_type}, '/file_type/file_type_document', 4, 0),      -- 文档文件
('file_type_archive', '压缩包', 'archive', 'file_type', #{ID_file_type}, '/file_type/file_type_archive', 5, 0),       -- 压缩包文件
('file_type_other', '其他', 'other', 'file_type', #{ID_file_type}, '/file_type/file_type_other', 6, 0);           -- 其他类型文件
```

##### 2.7.6.6 字典使用规范

在系统开发过程中，应遵循以下字典使用规范：

1. 数据存储使用字典的`dict_value`值而非`dict_name`值，以便于后期字典项的调整和国际化支持
2. 前端显示使用字典的`dict_name`值，并根据需要使用`color_type`和`css_class`进行样式渲染
3. 新增字典项时，应统一在本文档中进行记录，并通过数据字典管理模块进行维护
4. 对于系统级字典项（`is_sys`为1），不允许在系统运行过程中进行删除或修改值
5. 字典编码应遵循规范：分类使用业务领域名称，子项使用"分类编码_子项名称"格式
6. 字典数据导入时，占位符#{ID_XXX}需要替换为实际的父级ID值，可使用程序动态生成SQL或使用存储过程处理

### 2.8 AI算法集成模块

#### 2.8.1 功能描述

AI算法集成模块用于接收AI推理平台的实时事件通知，解析事件信息并自动创建预警记录。该模块作为服务端接收AI算法平台推送的事件信息（包括事件详情JSON、原始图片、带框图片或视频等），并将这些信息转化为预警记录，供后续人工处置和工单流转使用。

#### 2.8.2 功能结构

```mermaid
graph TD
    A[AI算法集成] --> B[事件接收服务]
    A --> C[事件解析处理]
    A --> D[预警自动生成]
    
    B --> B1[JSON事件信息接收]
    B --> B2[图片/视频文件接收]
    
    C --> C1[事件信息解析]
    C --> C2[目标检测结果解析]
    C --> C3[文件存储管理]
    
    D --> D1[预警记录创建]
    D --> D2[附件关联]
    D --> D3[通知推送]
```

#### 2.8.3 接口设计

```plain
POST /api/ai/event/upload      # 接收AI算法事件信息和文件
GET  /api/ai/event/list        # 获取AI事件列表
GET  /api/ai/event/{id}        # 获取AI事件详情
```

#### 2.8.4 数据流程设计

AI算法服务将通过HTTP接口向系统推送事件数据，主要包括三类数据：事件信息JSON、原始图片、带框图片或视频。这些数据会按顺序发送，系统需按以下流程处理：

1. **事件信息处理（首先接收）**：
   - 接收事件信息JSON文件，解析其中的事件ID、事件时间、算法应用名称、目标检测信息等
   - 创建新的AI事件记录，保存基本信息

2. **图片和视频处理（后续接收）**：
   - 接收原始图片文件，通过事件ID找到已创建的事件记录
   - 接收带框图片或视频文件，同样通过事件ID关联
   - 将这些文件保存到附件表中，设置link_id为事件ID，attachment_type为"ai_event"

3. **预警生成**：
   - 基于事件信息的分析结果，判断是否需要创建预警
   - 如需创建预警，则设置预警类型、预警来源、预警描述等信息
   - 更新AI事件的has_alarm字段为1，表示已生成预警
   - 预警记录中存储关联的事件ID，便于后续查询

4. **附件关联**：
   - 如果生成了预警，则可以将AI事件的相关附件关联到预警记录
   - 系统可根据附件表中的link_id和attachment_type查询相关图片和视频

#### 2.8.5 数据库设计

```sql
-- AI事件表
CREATE TABLE ai_event (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `event_id` varchar(64) NOT NULL COMMENT 'AI事件ID',
  `device_id` varchar(64) NOT NULL COMMENT '设备ID',
  `device_name` varchar(64) DEFAULT NULL COMMENT '设备名称',
  `task_id` varchar(64) NOT NULL COMMENT '任务ID',
  `task_name` varchar(64) DEFAULT NULL COMMENT '任务名称',
  `app_id` varchar(64) NOT NULL COMMENT '算法应用ID',
  `app_name` varchar(64) DEFAULT NULL COMMENT '算法应用名称',
  `src_id` varchar(64) NOT NULL COMMENT '摄像头ID',
  `src_name` varchar(64) DEFAULT NULL COMMENT '摄像头名称',
  `event_time` datetime NOT NULL COMMENT '事件时间',
  `event_state` int DEFAULT 0 COMMENT '事件状态',
  `event_details` text COMMENT '事件详情JSON',
  `has_alarm` tinyint(1) DEFAULT 0 COMMENT '是否已生成预警(0-未生成, 1-已生成)',
  `remark` varchar(256) DEFAULT '' COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_event_id` (`event_id`),
  INDEX `idx_event_time` (`event_time`)
);
```

#### 2.8.6 实现方案

##### ******* AI事件处理流程图

```mermaid
sequenceDiagram
    participant AI as AI算法服务
    participant API as 系统API接口
    participant Event as 事件处理服务
    participant Attach as 附件服务
    participant Alert as 预警管理模块
    participant User as 系统用户
    
    Note over AI,Event: 步骤1: 事件信息处理
    AI->>API: 发送事件信息JSON
    API->>Event: 解析事件信息
    Event->>Event: 创建新的事件记录
    
    Note over AI,Attach: 步骤2: 图片/视频处理
    AI->>API: 发送原始图片
    API->>Event: 查找对应事件记录
    Event->>Attach: 保存为附件(link_id=事件ID)
    
    AI->>API: 发送带框图片/视频
    API->>Event: 查找对应事件记录
    Event->>Attach: 保存为附件(link_id=事件ID)
    
    Note over Event,Alert: 步骤3: 预警生成
    Event->>Event: 分析事件，判断是否需生成预警
    alt 需要生成预警
        Event->>Alert: 创建预警记录(关联事件ID)
        Event->>Event: 更新事件has_alarm=1
        Alert->>User: 展示预警信息
        User->>Alert: 预警复核
        alt 确认问题
            User->>Alert: 转工单处理
        else 确认误报
            User->>Alert: 关闭预警
        end
    end
```

##### 2.8.6.2 AI事件数据流转图

```mermaid
flowchart TD
    A[AI算法识别事件] --> B[按顺序推送数据]
    B --> C1[1. 推送事件JSON数据]
    B --> C2[2. 推送原始图片]
    B --> C3[3. 推送标记图片/视频]
    
    C1 --> D1[解析事件信息]
    D1 --> E1[创建AI事件记录]
    
    C2 --> D2[查找对应事件记录]
    D2 --> E2[保存为附件]
    
    C3 --> D3[查找对应事件记录]
    D3 --> E3[保存为附件]
    
    E1 --> F[分析事件内容]
    E2 --> F
    E3 --> F
    
    F --> G{需要生成预警?}
    G -->|是| H[创建预警记录]
    G -->|否| I[更新事件状态]
    
    H --> J[更新事件has_alarm=1]
    J --> K[通知相关人员]
    K --> L[预警人工复核]
    L --> M{确认结果}
    M -->|确认问题| N[转为工单处理]
    M -->|误报| O[关闭预警]
```

##### 2.8.6.3 AI集成系统架构图

```mermaid
flowchart LR
    subgraph AIService["AI算法服务"]
        A1[图像采集] --> A2[目标检测]
        A2 --> A3[事件判断]
        A3 --> A4[生成事件数据]
    end
    
    subgraph Platform["无人机实战平台"]
        B1[事件接收服务] --> B2[事件解析服务]
        B2 --> B3[附件存储服务]
        B3 --> B4[预警生成服务]
        B4 --> B5[预警管理模块]
        B5 --> B6[工单管理模块]
    end
    
    A4 -->|按顺序推送| B1
    
    subgraph Storage["存储服务"]
        C1[文件存储]
        C2[数据库存储]
    end
    
    B3 -->|存储文件| C1
    B4 -->|存储记录| C2
```

##### ******* AI事件处理时序图

```mermaid
sequenceDiagram
    participant Camera as 无人机摄像头
    participant AI as AI算法服务
    participant API as 系统API
    participant EventDB as 事件数据库
    participant FileDB as 附件数据库
    participant Alert as 预警模块
    participant User as 系统用户
    participant Work as 工单模块
    
    Camera->>AI: 视频流数据
    AI->>AI: 目标检测算法分析
    AI->>AI: 事件判断
    
    Note over AI,EventDB: 步骤1: 事件数据处理
    AI->>API: POST /api/ai/event/upload (事件JSON)
    API->>EventDB: 创建AI事件记录
    
    Note over AI,FileDB: 步骤2: 图片/视频处理
    AI->>API: POST /api/ai/event/upload (原始图片)
    API->>EventDB: 查询对应事件记录
    API->>FileDB: 保存为附件(link_id=事件ID)
    
    AI->>API: POST /api/ai/event/upload (标记图片/视频)
    API->>EventDB: 查询对应事件记录
    API->>FileDB: 保存为附件(link_id=事件ID)
    
    Note over EventDB,Alert: 步骤3: 预警生成
    API->>EventDB: 分析事件数据
    API->>EventDB: 判断是否需要生成预警
    
    alt 需要生成预警
        API->>Alert: 创建预警记录(关联事件ID)
        API->>EventDB: 更新has_alarm=1
        Alert->>User: 通知预警信息
        User->>Alert: 复核预警
        alt 确认问题
            User->>Alert: 转工单处理
            Alert->>Work: 创建工单记录
            Work->>User: 通知处理人
        else 误报
            User->>Alert: 关闭预警
        end
    end
```

##### 2.8.6.5 AI事件处理组件图

```mermaid
flowchart TD
    subgraph "AI事件处理模块"
        EventReceiver[事件接收服务] --> EventParser[事件解析服务]
        EventParser --> FileStorage[附件存储服务]
        FileStorage --> AlertGenerator[预警生成服务]
    end
    
    subgraph "数据存储"
        AIEventDB[(AI事件表)]
        AlertDB[(预警表)]
        AttachmentDB[(附件表)]
    end
    
    subgraph "业务模块"
        AlertModule[预警管理模块]
        WorkOrderModule[工单管理模块]
    end
    
    AIService[AI算法服务] -->|1.事件信息| EventReceiver
    AIService -->|2.图片/视频| EventReceiver
    EventParser -->|创建/更新事件| AIEventDB
    FileStorage -->|存储附件| AttachmentDB
    AlertGenerator -->|创建预警| AlertDB
    AIEventDB -->|查询事件| AlertGenerator
    AttachmentDB -->|关联附件| AlertGenerator
    AlertDB --> AlertModule
    AlertModule -->|转工单| WorkOrderModule
```

##### 2.8.6.6 AI事件到预警的数据转换

```mermaid
flowchart LR
    subgraph AIEvent[AI事件数据]
        A1[event_id] 
        A2[device_id/name]
        A3[app_id/name]
        A4[event_time]
        A5[targets]
        A6[coordinates]
        A7[confidence]
        A8[has_alarm]
    end
    
    subgraph Attachments[附件数据]
        F1[原始图片]
        F2[带框图片]
        F3[视频文件]
    end
    
    subgraph AlertData[预警数据]
        B1[alarm_time]
        B2[alarm_type]
        B3[alarm_source]
        B4[alarm_desc]
        B5[longitude/latitude]
        B6[alarm_status]
        B7[ai_event_id]
    end
    
    A1 -->|关联| B7
    A3 -->|基于算法名称转换| B2
    A4 -->|直接映射| B1
    A5 -->|目标信息| B4
    A6 -->|坐标提取| B5
    C[AI识别] --> B3
    D[未处理] --> B6
    
    A1 -->|link_id| Attachments
    Attachments -->|复制关联| AlertData
```

### 2.9 附件管理模块

#### 2.9.1 功能描述

附件管理模块是基础服务模块，用于统一管理系统中各类业务对象（如预警、工单、AI事件等）的附件文件。该模块提供基础的文件上传、下载和预览功能，支持常见的图片、视频、文档等文件格式。

本版本实现基础的附件管理功能，后续版本将根据需求进一步优化和扩展功能。

#### 2.9.2 功能结构

```mermaid
graph TD
    A[附件管理] --> B[文件上传]
    A --> C[文件下载]
    A --> D[文件预览]
    A --> E[附件关联查询]
    
    B --> B1[单文件上传]
    B --> B2[多文件上传]
    
    C --> C1[单文件下载]
    
    D --> D1[图片预览]
    D --> D2[视频预览]
    
    E --> E1[按业务ID查询]
```

#### 2.9.3 数据库设计

```sql
-- 附件表
CREATE TABLE infra_attachment (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `link_id` bigint NOT NULL COMMENT '关联ID',
  `attachment_type` varchar(32) NOT NULL COMMENT '附件类型：预警附件-alarm，工单附件-work，AI事件附件-ai_event',
  `file_type` varchar(32) NOT NULL COMMENT '文件类型',
  `file_name` varchar(255) NOT NULL COMMENT '文件名称',
  `file_path` varchar(255) NOT NULL COMMENT '文件存放路径',
  `access_url` varchar(255) NOT NULL COMMENT '文件访问地址',
  `file_size` bigint DEFAULT 0 COMMENT '文件大小(字节)',
  `remark` varchar(256) DEFAULT '' COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  INDEX `idx_link_id` (`link_id`),
  INDEX `idx_attachment_type` (`attachment_type`),
  INDEX `idx_file_type` (`file_type`)
);
```

#### 2.9.4 接口设计

```plain
# 文件上传接口
POST /api/attachment/upload                     # 单文件上传
POST /api/attachment/upload/multi               # 多文件上传

# 文件下载接口
GET  /api/attachment/download/{id}              # 单文件下载

# 文件预览接口
GET  /api/attachment/preview/{id}               # 获取预览URL

# 附件关联查询接口
GET  /api/attachment/list/by-link/{linkId}     # 按关联ID查询
GET  /api/attachment/list/by-type/{type}       # 按附件类型查询
```

#### 2.9.5 附件类型定义

系统支持以下附件类型，用于区分不同业务场景的附件：

1. **预警附件(alarm)**：预警证据图片、现场视频等
2. **工单附件(work)**：工单相关图片、处置记录文件等
3. **AI事件附件(ai_event)**：AI算法识别的原始图片、标记图片等
4. **围栏附件(fence)**：围栏区域截图、触发事件图片等
5. **系统附件(system)**：系统日志、配置文件等

## 3. 前端界面设计

### 3.1 预警管理页面

+ 预警列表页：展示所有预警信息，支持按类型、状态、时间等筛选

![](https://cdn.nlark.com/yuque/0/2025/png/26370983/1749461834767-854c8766-ea20-4c4f-a28d-3dd0f5f3bb16.png)

+ 预警详情页：展示预警详细信息，包括位置、描述、图片等

![](https://cdn.nlark.com/yuque/0/2025/png/26370983/1749461844142-d9bc87cd-8d2f-452a-8bbc-f4ec62c2c444.png)

+ 预警处置页：提供预警处置功能，包括转工单和直接关闭两种处置方式

![](https://cdn.nlark.com/yuque/0/2025/png/26370983/1749461849299-c76d4666-9fdf-4812-883d-7d90f9f8a705.png)

### 3.2 工单管理页面

+ 工单列表页：展示所有工单信息，支持按类型、状态、优先级等筛选

![](https://cdn.nlark.com/yuque/0/2025/png/26370983/1749461861505-2836a62e-e675-4a30-b439-4c978cb38b6b.png)

+ 工单详情页：展示工单详细信息，包括位置、描述、处置记录等

![](https://cdn.nlark.com/yuque/0/2025/png/26370983/1749461871297-eebe1068-1f9b-40fe-8271-e2f11edce1fb.png)

![](https://cdn.nlark.com/yuque/0/2025/png/26370983/1749461876244-a017f9c5-1ca8-4fdf-abe5-76733ef52c5a.png)

+ 工单处置页：提供处置记录录入功能，支持附件上传

![](https://cdn.nlark.com/yuque/0/2025/png/26370983/1749461889230-d2dc60fa-9051-4f90-810a-8211ef00bb7e.png)

### 3.3 地图数据管理页面

+ 图层管理页：展示和管理图层信息

![](https://cdn.nlark.com/yuque/0/2025/png/26370983/1749461901802-3fbea436-078a-4cf6-983c-f58f73be408d.png)

+ 图元管理页：展示和管理图元信息，支持在地图上进行可视化操作

![](https://cdn.nlark.com/yuque/0/2025/png/26370983/1749461972679-bfed0ab3-d2b8-43c1-9d08-b5493406951c.png)

![](https://cdn.nlark.com/yuque/0/2025/png/26370983/1749462002886-2031dc2d-342d-4b8a-9242-b1e3d4b31ef1.png)

### 3.4 电子围栏管理页面<font style="color:#DF2A3F;">（UI设计）</font>

+ 围栏列表页：展示所有围栏信息

### 3.5 语音通话管理页面

+ 通话记录页：展示所有通话记录，支持按用户、设备、时间等筛选

### 3.6 数据字典管理页面

+ 字典列表页：以列表结构展示所有字典项
+ 字典编辑页：提供字典项的新增、修改和删除功能
