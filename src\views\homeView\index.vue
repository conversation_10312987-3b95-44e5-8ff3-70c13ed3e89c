<script>
export default { name: 'HomeView' };
</script>

<script setup>
import { ref, onMounted, onUnmounted, watch, toRaw, computed } from 'vue';
import HomeMap from '@/views/map/map-fly-manager/homeMap/index.vue';
import AlarmList from './component/alarmList.vue';
import mapTools from './component/mapTools.vue';
import AirportAndDrone from './component/airportAndDrone.vue';
import TaskInformationList from './component/taskInformationList.vue';
import LoadManagement from './component/loadManagement.vue';
import PilotList from './component/pilotList.vue';
import DroneVideo from './component/droneVideo.vue';
import taskDelivery from './component/taskDelivery.vue';
import VoiceCallPanel from '@/components/VoiceCallPanel.vue';
import { submitTask } from '@/api/task';
import { ElMessage, ElMessageBox } from 'element-plus';
import { authorityShow } from '@/utils/authority';
import { useStompWebSocket } from '@/hooks/useStompWebSocket';
import { useUserStoreHook } from '@/store/modules/user';
import {
  callState,
  currentChannel,
  remoteUser,
  isCallActive,
  CALL_STATES,
  initiateCall,
  handleIncomingCall,
  answerCall,
  rejectCall,
  endCall,
  startCallTimer,
  participants,
  currentSession
} from '@/utils/callService';

const videoVisible = ref(false);
const droneVisible = ref(false);
const pilotDroneVisible = ref(false);
const outVisible = ref(false);
const alarmId = ref('');
const activeName = ref('airport');
const showControl = ref(false);
const homeMapRef = ref(null);
const alarmRef = ref(null);
const taskVisible = ref(false);
const videoData = ref([]);
const droneData = ref([]);
const pilotDroneData = ref([]);
const outData = ref([]);
// WebSocket连接状态
const isWebSocketConnected = ref(false);
// 要传递给子组件的WebSocket消息数据
const webSocketMessageData = ref({
  topic: '',
  data: null
});

// 使用callService替换语音通话相关状态
const showVoiceCallPanel = computed(() => isCallActive.value);

const userStore = useUserStoreHook();

function hiddenOrShow() {
  if (showControl.value) {
    showControl.value = false;
    activeName.value = 'airport';
  } else {
    showControl.value = true;
  }
}

function tabchange(name) {
  activeName.value = name;
  taskVisible.value = false;
}

// 一键下发
function oneClick(item) {
  videoVisible.value = false;
  droneVisible.value = false;
  alarmId.value = toRaw(item).alarm_id;
  taskVisible.value = true;
}

// 点击列表定位到地图
function locationJump(item) {
  const data = toRaw(item);
  if (homeMapRef.value) {
    homeMapRef.value.fireAlarmJump(data);
  }
}

// 任务下发取消
function cancelTask() {
  taskVisible.value = false;
}

function handleTask(res) {
  let alarmConfig = JSON.parse(JSON.stringify(res));
  delete alarmConfig.dock_sn;
  submitTask(
    { alarmId: alarmId._value },
    {
      dock_sn: res.dock_sn,
      alarm_config: {
        ...alarmConfig
      }
    }
  )
    .then(resp => {
      ElMessage.success({
        message: '任务已创建',
        offset: 105
      });
      if (alarmRef.value) {
        alarmRef.value.updateStatus(alarmId._value);
      }
      taskVisible.value = false;
      data.value = {};
    })
    .catch(e => {
      // if(alarmRef.value) {
      //   alarmRef.value.updateStatus();
      // }
    });
}

// 行点击
function selectDrone(item) {
  if (homeMapRef.value) {
    // 在地图上点击展示弹窗
    // homeMapRef.value.clickDockOrNav(item);
    if (item.longitude && item.latitude) {
      homeMapRef.value.flyToDock(item);
    }
  }
}

function clickItem(type, item) {
  taskVisible.value = false;
  closeVideo();
  if (type == 'drone') {
    if (!item.status || !item.index) {
      ElMessage.warning({
        message: '无人机已离线！',
        offset: 105
      });
      closeVideo();
      return;
    }
    // droneVisible.value = true;
    // videoVisible.value = false;
    // outVisible.value = false;
    // pilotDroneVisible.value = false;
    pilotDroneData.value = [];
    droneData.value = [toRaw(item)];
    videoData.value = [];
    outData.value = [];
    nextTick(() => {
      droneVisible.value = true;
    });
  } else if (type == 'airport') {
    if (!item.status || !item.index) {
      ElMessage.warning({
        message: '机场已离线！',
        offset: 105
      });
      closeVideo();
      return;
    }
    // videoVisible.value = true;
    // droneVisible.value = false;
    // outVisible.value = false;
    // pilotDroneVisible.value = false;
    pilotDroneData.value = [];
    videoData.value = [toRaw(item)];
    droneData.value = [];
    outData.value = [];
    nextTick(() => {
      videoVisible.value = true;
    });
  } else if (type == 'pilotDrone') {
    if (item.status) {
      if (!item.index) {
        ElMessage.warning({
          message: '无人机正处于关机中，无法开启直播!',
          offset: 105
        });
        closeVideo();
        return;
      }
    } else {
      ElMessage.warning({
        message: '无人机已离线！',
        offset: 105
      });
      closeVideo();
      return;
    }
    // pilotDroneVisible.value = true;
    // droneVisible.value = false;
    // videoVisible.value = false;
    // outVisible.value = false;
    pilotDroneData.value = [toRaw(item)];
    droneData.value = [];
    videoData.value = [];
    outData.value = [];
    nextTick(() => {
      pilotDroneVisible.value = true;
    });
  } else {
    // 外部视频
    if (!item.external_video_id) {
      ElMessage.warning({
        message: '外部视频已离线！',
        offset: 105
      });
      closeVideo();
      return;
    }
    // outVisible.value = true;
    // videoVisible.value = false;
    // droneVisible.value = false;
    // pilotDroneVisible.value = false;
    pilotDroneData.value = [];
    outData.value = [
      {
        device_sn: item.device_sn,
        index: item.index,
        nickname: '外部视频',
        source: item.source,
        cameraId: item.external_video_id,
        url_type: null
      }
    ];
    videoData.value = [];
    droneData.value = [];
    nextTick(() => {
      outVisible.value = true;
    });
  }
}

function closeVideo() {
  droneVisible.value = false;
  videoVisible.value = false;
  outVisible.value = false;
  pilotDroneVisible.value = false;
}

// Handle call pilot action
async function handleCallPilot(callData) {
  console.log('处理呼叫飞手:', callData);

  const { pilot } = callData;

  if (pilot) {
    try {
      // 使用我们的服务发起通话
      const session = await initiateCall(pilot);

      if (session) {
        // 显示通知
        ElMessage.success(`正在呼叫 ${pilot.username}...`);
      }
    } catch (error) {
      console.error('呼叫失败:', error);
      ElMessage.error('呼叫失败: ' + (error.message || '未知错误'));
    }
  }
}

// Handle call actions from child components
function handleCallAction(actionData) {
  console.log('处理通话操作---:', actionData);
  const { action, session_id, caller_name, channel_name, user_id, user_name } = actionData;

  switch (action) {
    case 'incoming':
      // 处理来电
      handleIncomingCall({
        session_id,
        caller_name,
        channel_name
      });

      // 显示确认对话框
      ElMessageBox.confirm(`${caller_name || '有人'} 邀请您加入语音通话，是否接受？`, '语音通话邀请', {
        confirmButtonText: '接受',
        cancelButtonText: '拒绝',
        type: 'info'
      })
        .then(() => {
          // 用户接受通话
          answerCall(session_id);
        })
        .catch(() => {
          // 用户拒绝通话
          rejectCall();
        });
      break;

    case 'accept':
      // 处理通话已接受
      ElMessage.success(`${user_name || '对方'} 已接受通话邀请`);
      // 更新通话状态为已连接并开始计时
      callState.value = CALL_STATES.CONNECTED;
      // 如果有会话ID但没有开始计时，则开始计时
      if (currentChannel.value) {
        startCallTimer();
      }
      break;

    case 'reject':
      // 移除拒绝接听的参与者
      participants.value = participants.value.filter(p => p.user_id !== user_id);
      // 处理通话已拒绝
      ElMessage.info(`${user_name || '对方'} 已拒绝通话邀请`);
      // 判断有多少个参与者，1个则结束通话
      if (participants.value.length === 1 && currentSession.value.creator_id === userStore.userData.user_id) {
        handleCallEnded();
      }
      break;

    case 'end':
      // 处理通话已结束
      handleCallEnded();
      break;

    case 'leave':
      // 移除离开的参与者
      participants.value = participants.value.filter(p => p.user_id !== user_id);
      // 处理参与者离开通话
      ElMessage.info(`${user_name || '对方'}已离开当前通话`);
      break;
  }
}

// WebSocket message handler
function handleWebSocketMessage(message) {
  if (!message || !message.body) return;

  try {
    console.log('收到全局WebSocket消息:', message);

    // 如果消息体是字符串则解析
    const data = typeof message.body === 'string' ? JSON.parse(message.body) : message.body;

    // 从消息目的地提取主题
    const topic = message.headers?.destination;

    if (topic) {
      // 更新子组件的WebSocket消息数据
      webSocketMessageData.value = {
        topic,
        data
      };

      // 处理通话相关主题
      if (topic.includes(`/user/${userStore.userData?.user_id}/call/`)) {
        // 个人通话消息
        processUserCallMessage(topic, data);
      } else if (topic === '/topic/user/status') {
        // 用户状态更新消息，直接传递给子组件
        console.log('收到用户状态更新:', data);
      }
    }
  } catch (error) {
    console.error('处理WebSocket消息出错:', error);
  }
}

// 处理个人通话消息
function processUserCallMessage(topic, data) {
  // 获取消息类型并确保没有空格
  const topicParts = topic.trim().split('/');
  const msgType = topicParts[topicParts.length - 1].trim();

  console.log('处理个人通话消息:', { topic: topic.trim(), msgType, data });

  switch (msgType) {
    case 'incoming':
      // 处理个人通话邀请
      handleCallAction({
        action: 'incoming',
        sessionId: data.sessionId,
        from: {
          user_id: data.callerId,
          username: data.callerName || '未知用户'
        },
        channelName: data.channelName
      });
      break;

    case 'answer':
      // 处理个人通话应答
      handleCallAction({
        action: 'accept',
        userId: data.userId,
        sessionId: data.sessionId
      });
      break;

    case 'reject':
      // 处理个人通话拒绝
      handleCallAction({
        action: 'reject',
        userId: data.userId,
        sessionId: data.sessionId
      });
      break;

    case 'end':
      // 处理个人通话结束
      handleCallAction({
        action: 'end',
        sessionId: data.sessionId
      });
      break;

    case 'leave':
      // 处理个人通话参与者离开
      handleCallAction({
        action: 'leave',
        userId: data.userId,
        sessionId: data.sessionId
      });
      break;
  }
}

// 监听连接状态变化
function watchConnectionStatus() {
  isWebSocketConnected.value = isConnected.value;

  const unwatch = watch(
    isConnected,
    (newValue, oldValue) => {
      isWebSocketConnected.value = newValue;
      if (newValue) {
        console.log('WebSocket已连接，订阅主题...');
        subscribeTopics();

        // 如果之前断开过连接，现在重新连接成功，显示提示
        if (oldValue === false) {
          ElMessage.success({
            message: 'WebSocket通信连接已恢复',
            offset: 105
          });
        }
      } else if (oldValue === true) {
        // 从连接状态变为断开状态，显示警告
        ElMessage.warning({
          message: 'WebSocket通信连接已断开，正在尝试重新连接...',
          offset: 105,
          duration: 3000
        });
      }
    },
    { immediate: true }
  );

  return unwatch;
}

let unwatchConnection;
let statusUpdateInterval;

onMounted(() => {
  console.log('HomeView组件已挂载');
  // 监听连接状态变化
  unwatchConnection = watchConnectionStatus();
});

// 组件卸载时清理资源
onUnmounted(() => {
  console.log('HomeView组件已卸载');
  if (unwatchConnection) {
    unwatchConnection();
  }
  if (statusUpdateInterval) {
    clearInterval(statusUpdateInterval);
  }

  // 确保WebSocket连接在组件卸载时关闭
  if (isWebSocketConnected.value) {
    console.log('HomeView卸载时关闭WebSocket连接');
    // 延迟断开连接，确保离线状态消息发送完成
    setTimeout(() => {
      disconnect();
    }, 300);
  }
});

// 处理通话结束
function handleCallEnded() {
  endCall();
  console.log('通话已结束');
}

// 使用STOMP WebSocket Hook
const { isConnected, send, subscribe, reconnect, disconnect } = useStompWebSocket(handleWebSocketMessage, {
  path: '/api/v1/ws',
  debug: true,
  heartbeatOutgoing: 10000,
  heartbeatIncoming: 10000,
  reconnectDelay: 5000,
  maxReconnectAttempts: 10
});

// 订阅WebSocket主题
function subscribeTopics() { 
  console.log('正在订阅WebSocket主题');

  // 订阅全局用户状态变化
  subscribe('/topic/user/status', data => {
    webSocketMessageData.value = {
      topic: '/topic/user/status',
      data: data
    };
  });

  // 订阅预警信息状态变化
  // subscribe('/topic/user/status', (data) => { 
  //   window.$bus.emit('updateAlarmInfo', data);
  // });

  // 订阅个人主题
  if (userStore.userData?.user_id) {
    const userId = userStore.userData.user_id;

    // 个人通话相关主题 - 确保没有空格
    const userCallTopics = [
      `/topic/call/incoming/${userId}`.trim(), // 个人通话邀请
      `/topic/call/answer/${userId}`.trim(), // 个人通话应答
      `/topic/call/reject/${userId}`.trim(), // 个人通话拒绝
      `/topic/call/end/${userId}`.trim(), // 个人通话结束
      `/topic/call/leave/${userId}`.trim() // 个人通话参与者离开
    ];

    userCallTopics.forEach(topic => {
      const cleanTopic = topic.trim();
      console.log(`订阅主题: "${cleanTopic}"`);

      subscribe(cleanTopic, data => {
        console.log(`收到个人通话消息 ${cleanTopic}:`, data);
        webSocketMessageData.value = {
          topic: cleanTopic,
          data: data
        };
      });
    });
  }
}
</script>

<template>
  <div class="home-container">
    <div class="map-container">
      <!-- 地图区域 -->
      <HomeMap ref="homeMapRef" :showLayerControl="false" />
      <div class="mapTools-box"><mapTools /></div>

      <!-- WebSocket连接状态指示器 -->
      <div
        class="ws-status-indicator"
        :class="{ connected: isWebSocketConnected, disconnected: !isWebSocketConnected }"
      >
        <el-tooltip :content="isWebSocketConnected ? '通信连接正常' : '通信连接断开'" placement="bottom">
          <div class="status-dot"></div>
        </el-tooltip>
      </div>
    </div>
    <!-- 任务下发 -->
    <div class="task-container">
      <task-delivery v-if="taskVisible" :visible="taskVisible" @onCancel="cancelTask" @ok="handleTask" />
    </div>
    <!-- 视频播放 -->
    <div
      :class="
        !videoVisible && !droneVisible && !outVisible && !pilotDroneVisible ? 'video-container-none' : 'video-container'
      "
      style="z-index: 100"
    >
      <drone-video
        player-id="video"
        :visible="videoVisible"
        @onClose="closeVideo"
        :device="videoData"
        v-if="videoVisible"
      />
      <drone-video
        player-id="drone"
        :visible="droneVisible"
        @onClose="closeVideo"
        :device="droneData"
        v-if="droneVisible"
      />
      <drone-video
        player-id="pilotDrone"
        :visible="pilotDroneVisible"
        @onClose="closeVideo"
        :device="pilotDroneData"
        v-if="pilotDroneVisible"
      />
      <drone-video player-id="out" :visible="outVisible" @onClose="closeVideo" :device="outData" v-if="outVisible" />
    </div>

    <!-- 语音通话面板 -->
    <div class="voice-call-container" v-if="showVoiceCallPanel">
      <VoiceCallPanel
        :channelName="currentChannel"
        :localUser="userStore.userData"
        :remoteUser="remoteUser"
        @call-ended="handleCallEnded"
      />
    </div>

    <div :class="!showControl ? 'tabs-box' : 'tabs-box-none'">
      <div :class="showControl ? 'hidden-box' : 'show-box'" @click="hiddenOrShow"></div>
      <div class="left-navbar" v-if="!showControl">
        <el-tabs v-model="activeName" class="demo-tabs" @tab-change="tabchange">
          <el-tab-pane label="机场&无人机" name="airport">
            <airport-and-drone @onClick="clickItem" @select="selectDrone" />
          </el-tab-pane>
          <el-tab-pane label="飞手列表" name="pilot" v-if="authorityShow('pilotList')">
            <pilot-list
              :isConnected="isWebSocketConnected"
              :websocketMessage="webSocketMessageData"
              @reconnect="reconnect"
              @callPilot="handleCallPilot"
              @call-action="handleCallAction"
            />
          </el-tab-pane>
          <el-tab-pane label="负载设备" name="load" v-if="authorityShow('loadManagement')">
            <load-management v-if="activeName == 'load'" />
          </el-tab-pane>
          <el-tab-pane label="任务信息" name="taskList" v-if="authorityShow('taskInformationList')">
            <task-information-list v-if="activeName == 'taskList'" />
          </el-tab-pane>
          <el-tab-pane label="警情列表" name="alarmList" v-if="authorityShow('alarmList')">
            <alarm-list
              ref="alarmRef"
              v-if="activeName == 'alarmList'"
              @OneClickDelivery="oneClick"
              @LocationJump="locationJump"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-empty__image) {
  width: 120px;
}
:deep(.el-tabs__item:focus-visible) {
  box-shadow: none;
}
:deep(.el-tabs) {
  height: 100%;
}
:deep(.el-tabs__content) {
  height: 93%;
}
:deep(.el-tab-pane) {
  height: 100%;
}
:deep(.el-tabs__header) {
  background-color: #11253e;
  border-bottom: 2px solid #344054;
  margin-bottom: 0;
}
:deep(.el-tabs--top .el-tabs__item.is-top:nth-child(2)) {
  padding-left: 8px;
}
:deep(.el-tabs__item.is-active) {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #0094ff;
  text-align: center;
  line-height: 22px;
  font-weight: 400;
}
:deep(.el-tabs__item) {
  padding: 0 8px;
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #98a2b3;
  text-align: center;
  line-height: 22px;
  font-weight: 400;
}
.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}
.video-container {
  width: 400px;
  height: 270px;
  position: absolute;
  left: 420px;
  top: 12%;
}
.video-container-none {
  width: 0px;
  height: 270px;
  position: absolute;
  left: 420px;
  top: 12%;
}
.home-container {
  position: relative;
  display: flex;
  flex-direction: row;
  height: 100%;
  width: 100%;
  background: #fff;
  overflow: hidden;
}
.tabs-box-none {
  position: absolute;
  bottom: 0;
  width: 0px;
  height: 88%;
  left: 0;
  z-index: 99;
  .hidden-box {
    height: 80px;
    width: 24px;
    top: 50%;
    left: 0;
    cursor: pointer;
    position: absolute;
    background-image: url('@/assets/homeViewImg/open.png');
    z-index: 9999;
  }
  .show-box {
    height: 80px;
    width: 24px;
    top: 50%;
    right: -24px;
    cursor: pointer;
    position: absolute;
    background-image: url('@/assets/homeViewImg/hidden.png');
    z-index: 9999;
  }
}
.tabs-box {
  position: absolute;
  bottom: 0;
  width: 390px;
  height: 88%;
  left: 0;
  z-index: 99;
  .hidden-box {
    height: 80px;
    width: 24px;
    top: 50%;
    left: 0;
    cursor: pointer;
    position: absolute;
    background-image: url('@/assets/homeViewImg/open.png');
    z-index: 9999;
  }
  .show-box {
    height: 80px;
    width: 24px;
    top: 50%;
    right: -24px;
    cursor: pointer;
    position: absolute;
    background-image: url('@/assets/homeViewImg/hidden.png');
    z-index: 9999;
  }
}
.left-navbar {
  width: 100%;
  height: 100%;
  background-color: #001129;
}
.task-container {
  z-index: 99;
  width: 480px;
  position: absolute;
  left: 425px;
  top: 12%;
}
.map-container {
  width: 100%;
  height: 100%;
  position: relative;
}
.mapTools-box {
  position: absolute;
  right: 10px;
  top: 11%;
  width: 50px;
  height: 89%;
}

/* WebSocket连接状态指示器样式 */
.ws-status-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  padding: 5px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;

  .status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
  }

  &.connected .status-dot {
    background-color: #67c23a; /* 绿色 - 连接正常 */
    box-shadow: 0 0 5px #67c23a;
  }

  &.disconnected .status-dot {
    background-color: #f56c6c; /* 红色 - 连接断开 */
    box-shadow: 0 0 5px #f56c6c;
    animation: blink 1s infinite;
  }
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
  }
}

.voice-call-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600px;
  height: 400px;
  z-index: 1000;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
