<script>
export default { name: 'ControlList' };
</script>

<script setup>
import { onMounted, onUnmounted, reactive, ref, toRaw, watch } from 'vue';
import { useRouter } from 'vue-router';
import { postPayloadCommands } from '@/api/task';
import { useManualControl, Key_Code, useDroneControlWsEvent, usePayloadControl } from '../../../utils/constants';
import {
  takeoffToPoint,
  postDrcEnter,
  postFlightAuth,
  postDrcExit,
  courseReversal,
  getAuthority,
  textToVoice,
  setVolume,
  audioList,
  audioPlay,
  alarmLampChange,
  setLoopType,
  stopPlay
} from '@/api/task';
import optionData from '@/utils/option-data';
import { ElMessage } from 'element-plus';
import { useMqttStoreHook } from '@/store/modules/mqttService';
import { useMqtt } from '@/hooks/useMqtt';
import { getCameraSelected, changeLiveStreams } from '../../../api/devices';

const router = useRouter();
const currentQuery = router.currentRoute.value.query;

// 添加消息防抖控制
const messageControl = {
  lastMessage: '',
  lastMessageTime: 0,
  messageDelay: 2000, // 2秒内不重复显示相同消息
  showMessage(message) {
    const currentTime = new Date().getTime();
    // 如果是首次显示消息或者与上次消息间隔超过指定时间
    if (
      this.lastMessage !== message ||
      this.lastMessageTime === 0 ||
      currentTime - this.lastMessageTime >= this.messageDelay
    ) {
      ElMessage.error(message);
      this.lastMessage = message;
      this.lastMessageTime = currentTime;
    }
  }
};

// 负载控制
const payloadSelectInfo = {
  value: currentQuery.device_sn,
  controlSource: undefined,
  options: [],
  payloadIndex: '',
  camera: undefined // 当前负载osd信息
};
const emit = defineEmits(['OneClickDelivery']);

const { droneControlSource, payloadControlSource } = useDroneControlWsEvent(
  currentQuery.dock_sn,
  payloadSelectInfo.value
);
const deviceTopicInfo = reactive({
  sn: currentQuery.device_sn,
  pubTopic: '',
  subTopic: ''
});

const mqttStore = useMqttStoreHook();
const mqttHooks = useMqtt(deviceTopicInfo);
const form = reactive({
  target_height: 100,
  rth_altitude: 100,
  target_longitude: currentQuery.lon,
  target_latitude: currentQuery.lat,
  exit_wayline_when_rc_lost: 1,
  rth_mode: 1,
  security_takeoff_height: 100,
  commander_mode_lost_action: 0,
  commander_flight_mode: 1,
  commander_flight_height: null,
  rc_lost_action: 2,
  max_speed: 14,
  commander_flight_height: 80
});
const dataFormRef = ref(ElForm);
const loadType = ref('text'); //speak 喊话 play播放 text文字转语音 lamp警灯
const distanceType = ref(''); // zoom 变焦 wide 广角 ir 红外
const cameraType = ref('photograph'); // photograph 拍照 video 录像
const startNum = ref(1); //判断是否开始录像
const hasAuth = ref(false); //是否已经有云台控制权
const hasAirportAuth = ref(false); //是否已经有机场控制权
const rules = reactive({
  target_height: [{ required: true, message: '请输入飞行高度', trigger: 'blur' }],
  rth_altitude: [{ required: true, message: '请输入返航高度', trigger: 'blur' }],
  commander_mode_lost_action: [{ required: true, message: '请选择失航动作', trigger: 'blur' }]
});
const zoomValue = ref(0); //云台控制 变焦的值
const irValue = ref(false); // 云台控制 红外的值
const wideValue = ref(0); // 云台控制 广角的值
const speedValue = ref(50); // 云台控制-速度
const textareaValue = ref(''); //文本转语音
const lampType = ref(''); // 文本转语音音量
const speekVolumeValue = ref(50); // 讲话音量
const mouseDown = ref(''); // 当前按键
const videoList = ref([]);
const textPlay = ref(false); // 文字转语音播放状态
const recordPlay = ref(false); // 录音播放状态
const isLoop = ref(false);
const recordValue = ref({});
let timerRef = null;
const isBack = ref(false);
const timeObj = reactive({
  m: 0,
  s: 0,
  h: 0
});
defineExpose({
  initPostDrcEnter,
  initPostDrcOut
});
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  sn: {
    type: String,
    default: ''
  },
  status: {
    // 无人机状态
    type: String,
    default: '14' //离线
  },
  airStatus: {
    // 机场状态
    type: String,
    default: '0' // 0-空闲 ,4-作业中
  }
});
const flightController = ref(false);

watch(
  () => props.airStatus,
  val => {
    console.log('val', val);
  }
);

watch(
  () => props.status,
  val => {
    if (val == '14' || val == '9' || val == '10') {
      localStorage.setItem('hasAirportAuth', false);
    }
    if (val == '14') {
      isBack.value = false;
      hasAuth.value = false;
      cameraType.value = 'photograph';
      timeObj.s = 0;
      timeObj.h = 0;
      timeObj.m = 0;
    }
    console.log('props.status', val);
  }
);

const { handleKeyup, activeCodeKey, handleEmergencyStop } = useManualControl(deviceTopicInfo, flightController);

const {
  takeCameraPhoto,
  startCameraRecording,
  stopCameraRecording,
  changeCameraFocalLength,
  cloudControls,
  resetGimbal,
  switchCameraMode
} = usePayloadControl();

function initPostDrcOut() {
  console.log('initPostDrcOut');
  if (hasAirportAuth.value == true) {
    postDrcExit({
      client_id: 'drc_web',
      dock_sn: currentQuery.dock_sn
    }).then(data => {
      hasAirportAuth.value = false;
      localStorage.setItem('hasAirportAuth', false);
    });
  }
}

function initPostDrcEnter() {
  console.log('initPostDrcEnter');
  postDrcEnter({
    client_id: 'drc_web',
    dock_sn: currentQuery.dock_sn
  }).then(data => {
    flightController.value = true;
    if (data.sub && data.sub.length > 0) {
      deviceTopicInfo.subTopic = data.sub[0];
    }
    if (data.pub && data.pub.length > 0) {
      deviceTopicInfo.pubTopic = data.pub[0];
    }
    // 获取飞行控制权
    postFlightAuth({ sn: currentQuery.dock_sn }, {}).then(res => {
      mqttStore.initMqtt();
      // hasAirportAuth.value  = true
      // localStorage.setItem('hasAirportAuth', true);
    });
  });
}
onMounted(() => {
  isBack.value = false;
  getAudioList();
  searchIndex();
  if (localStorage.getItem('hasAirportAuth') == 'true') {
    postDrcEnter({
      client_id: 'drc_web',
      dock_sn: currentQuery.dock_sn
    }).then(data => {
      flightController.value = true;
      if (data.sub && data.sub.length > 0) {
        deviceTopicInfo.subTopic = data.sub[0];
      }
      if (data.pub && data.pub.length > 0) {
        deviceTopicInfo.pubTopic = data.pub[0];
      }
      // 获取飞行控制权
      postFlightAuth({ sn: currentQuery.dock_sn }, {}).then(res => {
        mqttStore.initMqtt();
        hasAirportAuth.value = true;
        localStorage.setItem('hasAirportAuth', true);
      });
    });
  }
  hasAirportAuth.value = localStorage.getItem('hasAirportAuth') == 'true' ? true : false;
  window.addEventListener('keyup', onKeyup);
});

onUnmounted(() => {
  window.removeEventListener('keyup', () => {});
  window.removeEventListener('keydown', () => {});
  localStorage.removeItem('hasAirportAuth');
});

function onKeyup() {
  mouseDown.value = '';
}

function getAudioList() {
  audioList({}).then(res => {
    videoList.value = res;
  });
}

// 键盘监听
function onKeydown(e) {
  // console.log('-----------按键', e);
  switch (e.code) {
    case 'KeyC':
      if (e.code == activeCodeKey.value) {
        mouseDown.value = 'KeyC';
      }
      onMouseDown(Key_Code.KEY_C);
      break;
    case 'KeyF':
      break;
    case 'KeyZ':
      if (e.code == activeCodeKey.value) {
        mouseDown.value = 'KeyZ';
      }
      onMouseDown(Key_Code.KEY_Z);
      break;
    case 'KeyA':
      if (e.code == activeCodeKey.value) {
        mouseDown.value = e.code;
      }
      onMouseDown(Key_Code.KEY_A);
      break;
    case 'KeyW':
      if (e.code == activeCodeKey.value) {
        mouseDown.value = e.code;
      }
      onMouseDown(Key_Code.KEY_W);
      break;
    case 'KeyS':
      if (e.code == activeCodeKey.value) {
        mouseDown.value = e.code;
      }
      onMouseDown(Key_Code.KEY_S);
      break;
    case 'KeyD':
      if (e.code == activeCodeKey.value) {
        mouseDown.value = e.code;
      }
      onMouseDown(Key_Code.KEY_D);
      break;
    case 'KeyQ':
      if (e.code == activeCodeKey.value) {
        mouseDown.value = e.code;
      }
      onMouseDown(Key_Code.KEY_Q);
      break;
    case 'KeyE':
      if (e.code == activeCodeKey.value) {
        mouseDown.value = e.code;
      }
      onMouseDown(Key_Code.KEY_E);
      break;
    case 'Space':
      //急停
      mouseDown.value = e.code;
      handleStop();
      break;
    case 'Digit1':
      //广角
      changeDistance('wide');
      break;
    case 'Digit2':
      //变焦
      changeDistance('zoom');
      break;
    case 'Digit3':
      //红外
      changeDistance('ir');
      break;
    case 'ArrowUp':
      //云台向上
      handleCloud('top');
      break;
    case 'ArrowDown':
      //云台向下
      handleCloud('bottom');
      break;
    case 'ArrowLeft':
      //云台向左
      handleCloud('left');
      break;
    case 'ArrowRight':
      //云台向右
      handleCloud('right');
      break;
    default:
      break;
  }
}

function subCloudSpeed() {
  speedValue.value -= 1;
  ElMessage({
    message: '操作成功',
    grouping: true,
    type: 'success'
  });
}

function addCloudSpeed() {
  speedValue.value += 1;
  ElMessage({
    message: '操作成功',
    grouping: true,
    type: 'success'
  });
}

function subDistance() {
  if (!hasAuth.value) {
    ElMessage({
      message: '请先开启控制',
      grouping: true,
      type: 'warning'
    });
    return;
  }
  if (distanceType.value == 'zoom') {
    zoomValue.value = zoomValue.value - 1;
    changeCameraFocalLength(currentQuery.dock_sn, {
      payload_index: payloadSelectInfo.payloadIndex,
      camera_type: 'zoom',
      zoom_factor: zoomValue.value
    });
  } else if (distanceType.value == 'ir') {
    irValue.value = irValue.value - 1;
    changeCameraFocalLength(currentQuery.dock_sn, {
      payload_index: payloadSelectInfo.payloadIndex,
      camera_type: 'ir',
      zoom_factor: irValue.value
    });
  }
}

function addDistance() {
  if (!hasAuth.value) {
    ElMessage({
      message: '请先开启控制',
      grouping: true,
      type: 'warning'
    });
    return;
  }
  if (distanceType.value == 'zoom') {
    zoomValue.value += 1;
    changeCameraFocalLength(currentQuery.dock_sn, {
      payload_index: payloadSelectInfo.payloadIndex,
      camera_type: 'zoom',
      zoom_factor: zoomValue.value
    });
  } else if (distanceType.value == 'ir') {
    irValue.value += 1;
    changeCameraFocalLength(currentQuery.dock_sn, {
      payload_index: payloadSelectInfo.payloadIndex,
      camera_type: 'ir',
      zoom_factor: irValue.value
    });
  }
}

function timers() {
  timeObj.s = timeObj.s + 1;
  if (timeObj.s >= 60) {
    timeObj.s = 0;
    timeObj.m = timeObj.m + 1;
  }
  if (timeObj.m >= 60) {
    timeObj.m = 0;
    timeObj.h = timeObj.h + 1;
  }
}

function startTime() {
  timerRef = setInterval(timers, 1000);
}

function searchIndex() {
  getCameraSelected({
    device_sn: currentQuery.device_sn
  }).then(data => {
    payloadSelectInfo.payloadIndex = data.index;
  });
}

function changeLoad(type) {
  loadType.value = type;
}

function changeDistance(type) {
  if (!hasAuth.value) {
    ElMessage({
      message: '请先开启控制',
      grouping: true,
      type: 'warning'
    });
    return;
  }
  distanceType.value = type;
  changeLiveStreams({
    video_id: `${currentQuery.device_sn}/${payloadSelectInfo.payloadIndex}/normal-0`,
    video_type: type
  }).then(res => {
    ElMessage({
      message: '操作成功',
      grouping: true,
      type: 'success'
    });
  });
}

function changeCameraType(type) {
  if (!payloadSelectInfo.payloadIndex) {
    searchIndex();
    // ElMessage.error('无人机不在线！')
    // return;
  }
  if (!hasAuth.value) {
    ElMessage({
      message: '请先开启控制',
      grouping: true,
      type: 'warning'
    });
    return;
  }
  if (cameraType.value == type && type == 'photograph') {
    ElMessage.warning('无人机已处于摄影状态，可进行拍照操作');
    return;
  } else if (cameraType.value == type && type == 'video') {
    ElMessage.warning('无人机已处于录像状态，可进行录像操作');
    return;
  }
  setTimeout(() => {
    cameraType.value = type;
    switchCameraMode(currentQuery.dock_sn, {
      payload_index: payloadSelectInfo.payloadIndex,
      camera_mode: type == 'video' ? 1 : 0
    });
  }, 500);
}

function onekeyFly() {
  dataFormRef.value.validate(isValid => {
    if (isValid) {
      takeoffToPoint(
        {
          sn: currentQuery.dock_sn
        },
        {
          ...form
        }
      ).then(res => {
        window.addEventListener('keydown', onKeydown);
        ElMessage({
          message: '操作成功',
          grouping: true,
          type: 'success'
        });
      });
    }
  });
}

function handleStop() {
  if (!hasAirportAuth.value) {
    return;
  }
  mouseDown.value = '';
  handleEmergencyStop();
}

function handleLamp(item) {
  if (lampType.value == item.key) {
    alarmLampChange(
      { sn: currentQuery.dock_sn },
      {
        index: 1,
        value: 0
      }
    ).then(res => {
      lampType.value = '';
      ElMessage({
        message: '操作成功',
        grouping: true,
        type: 'success'
      });
    });
    return;
  }
  alarmLampChange(
    { sn: currentQuery.dock_sn },
    {
      index: 1,
      value: item.value
    }
  ).then(res => {
    lampType.value = item.key;
    ElMessage({
      message: '操作成功',
      grouping: true,
      type: 'success'
    });
  });
}

function handleName() {
  if (lampType.value == '') {
    return '警灯';
  } else if (lampType.value == 'rlks') {
    return '红蓝快闪';
  } else if (lampType.value == 'hhbs') {
    return '红红爆闪';
  } else if (lampType.value == 'hlms') {
    return '红蓝慢闪';
  } else if (lampType.value == 'llbs') {
    return '蓝蓝爆闪';
  }
}

function handleclass() {
  if (lampType.value == '') {
    return 'alarmIcon';
  } else if (lampType.value == 'rlks' || lampType.value == 'hlms') {
    return 'alarmIcon-hl';
  } else if (lampType.value == 'hhbs') {
    return 'alarmIcon-rr';
  } else {
    return 'alarmIcon-ll';
  }
}

function handleLoop() {
  setLoopType(
    {
      sn: currentQuery.dock_sn
    },
    {
      play_mode: isLoop ? 1 : 0
    }
  ).then(data => {
    isLoop.value = !isLoop.value;
    ElMessage({
      message: '操作成功',
      grouping: true,
      type: 'success'
    });
  });
}

function handleClickAudio(item) {
  recordValue.value = toRaw(item);
}

function handlePlayAudio() {
  if (props.status == '14') {
    ElMessage.error('无人机离线，不支持此操作');
    return;
  }
  if (speekVolumeValue.value == 50) {
    setVolume(
      {
        sn: currentQuery.dock_sn
      },
      {
        play_volume: 50
      }
    ).then(data => {
      speekVolumeValue.value = 50;
    });
  }
  audioPlay(
    {
      sn: currentQuery.dock_sn
    },
    {
      name: recordValue.value.file_name,
      url: recordValue.value.file_path
    }
  ).then(data => {
    ElMessage({
      message: '操作成功',
      grouping: true,
      type: 'success'
    });
  });
}

function oneKeyBack() {
  if (isBack.value) {
    return;
  }
  courseReversal(
    {
      sn: currentQuery.dock_sn,
      service_identifier: 'return_home'
    },
    { action: '1' }
  ).then(data => {
    ElMessage({
      message: '操作成功',
      grouping: true,
      type: 'success'
    });
    hasAirportAuth.value = false;
    isBack.value = true;
    localStorage.setItem('hasAirportAuth', false);
  });
}

// 进入飞行控制
async function enterFlightControl() {
  if (hasAirportAuth.value === true || isBack.value === true) {
    return;
  }
  try {
    postDrcEnter({
      client_id: 'drc_web',
      dock_sn: currentQuery.dock_sn
    }).then(data => {
      flightController.value = true;
      if (data.sub && data.sub.length > 0) {
        deviceTopicInfo.subTopic = data.sub[0];
      }
      if (data.pub && data.pub.length > 0) {
        deviceTopicInfo.pubTopic = data.pub[0];
      }
      // 获取飞行控制权
      postFlightAuth({ sn: currentQuery.dock_sn }, {}).then(res => {
        mqttStore.initMqtt();
        hasAirportAuth.value = true;
        localStorage.setItem('hasAirportAuth', true);
        ElMessage({
          message: '操作成功',
          grouping: true,
          type: 'success'
        });
      });
    });
  } catch (error) {}
}

function onMouseDown(type) {
  if (!hasAirportAuth.value) {
    return;
  }
  if (props.status == '5') {
    ElMessage.error('请先进行急停，再进行操作');
    return;
  }
  mouseDown.value = type;
  handleKeyup(type);
}

// 开启控制
function openControl() {
  if (props.status == '14') {
    ElMessage.error('无人机离线，不支持此操作');
    return;
  }
  if (hasAuth.value) {
    return;
  }
  searchIndex();
  setTimeout(() => {
    holderAuth();
  }, 300);
}

// 获取控制权
function holderAuth() {
  if (!payloadSelectInfo.payloadIndex) {
    searchIndex();
    // ElMessage.error('无人机不在线！')
    // return;
  }
  setTimeout(() => {
    getAuthority(
      {
        sn: currentQuery.dock_sn
      },
      {
        payload_index: payloadSelectInfo.payloadIndex
      }
    ).then(data => {
      hasAuth.value = true;
      ElMessage({
        message: '操作成功',
        grouping: true,
        type: 'success'
      });
    });
  }, 500);
}

// 摄影
async function playVideo() {
  if (!hasAuth.value) {
    // holderAuth()
    ElMessage({
      message: '请先开启控制',
      grouping: true,
      type: 'warning'
    });
    return;
  }
  if (startNum.value == 1) {
    // 开始录像
    // await startCameraRecording(currentQuery.dock_sn, payloadSelectInfo.payloadIndex)
    postPayloadCommands(currentQuery.dock_sn, {
      cmd: 'camera_recording_start',
      data: {
        payload_index: payloadSelectInfo.payloadIndex
      }
    }).then(data => {
      ElMessage.success('开始录像成功！');
      startTime();
      startNum.value += 1;
    });
  } else {
    // 结束录像
    // await stopCameraRecording(currentQuery.dock_sn, payloadSelectInfo.payloadIndex)
    postPayloadCommands(currentQuery.dock_sn, {
      cmd: 'camera_recording_stop',
      data: {
        payload_index: payloadSelectInfo.payloadIndex
      }
    }).then(data => {
      ElMessage.success('停止录像成功！');
      timeObj.s = 0;
      timeObj.h = 0;
      timeObj.m = 0;
      clearInterval(timerRef);
      startNum.value = 1;
    });
  }
}

// 拍照
function phoneOrVideo() {
  if (!hasAuth.value) {
    // holderAuth()
    ElMessage({
      message: '请先开启控制',
      grouping: true,
      type: 'warning'
    });
    return;
  }
  takeCameraPhoto(currentQuery.dock_sn, payloadSelectInfo.payloadIndex);
}

function changeSpeedValue(value) {
  speedValue.value = value;
}

function changeCloudValue(value) {
  if (!hasAuth.value) {
    ElMessage({
      message: '请先开启控制',
      grouping: true,
      type: 'warning'
    });
    return;
  }
  if (distanceType.value == 'zoom') {
    zoomValue.value = value;
    changeCameraFocalLength(currentQuery.dock_sn, {
      payload_index: payloadSelectInfo.payloadIndex,
      camera_type: 'zoom',
      zoom_factor: value
    });
  } else if (distanceType.value == 'wideAngle') {
    wideValue.value = value;
    changeCameraFocalLength(currentQuery.dock_sn, {
      payload_index: payloadSelectInfo.payloadIndex,
      camera_type: 'wide',
      zoom_factor: value
    });
  } else {
    irValue.value = value;
    changeCameraFocalLength(currentQuery.dock_sn, {
      payload_index: payloadSelectInfo.payloadIndex,
      camera_type: 'ir',
      zoom_factor: value
    });
  }
}

function textToSpeek() {
  if (textareaValue.value == '') {
    ElMessage.error('请先输入想要转换成语音的文字');
    return;
  }
  if (props.status == '14') {
    ElMessage.error('无人机离线，不支持此操作');
    return;
  }
  if (speekVolumeValue.value == 50) {
    setVolume(
      {
        sn: currentQuery.dock_sn
      },
      {
        play_volume: 50
      }
    ).then(data => {
      speekVolumeValue.value = 50;
    });
  }
  if (!textPlay.value) {
    textToVoice(
      { sn: currentQuery.dock_sn },
      {
        name: '测试',
        text: textareaValue.value
      }
    ).then(data => {
      textPlay.value = !textPlay.value;
      ElMessage({
        message: '操作成功',
        grouping: true,
        type: 'success'
      });
    });
  } else {
    stopPlay(
      {
        sn: currentQuery.dock_sn
      },
      {}
    ).then(data => {
      textPlay.value = !textPlay.value;
      ElMessage({
        message: '操作成功',
        grouping: true,
        type: 'success'
      });
    });
  }
}

function handleCloud(type) {
  if (!hasAuth.value) {
    ElMessage({
      message: '请先开启控制',
      grouping: true,
      type: 'warning'
    });
    return;
  }
  if (type == 'top') {
    cloudControls(currentQuery.dock_sn, {
      payload_index: payloadSelectInfo.payloadIndex,
      locked: false,
      pitch_speed: speedValue.value,
      yaw_speed: 0
    });
  } else if (type == 'bottom') {
    cloudControls(currentQuery.dock_sn, {
      payload_index: payloadSelectInfo.payloadIndex,
      locked: false,
      pitch_speed: -speedValue.value,
      yaw_speed: 0
    });
  } else if (type == 'left') {
    cloudControls(currentQuery.dock_sn, {
      payload_index: payloadSelectInfo.payloadIndex,
      locked: false,
      yaw_speed: -speedValue.value,
      pitch_speed: 0
    });
  } else if (type == 'right') {
    cloudControls(currentQuery.dock_sn, {
      payload_index: payloadSelectInfo.payloadIndex,
      locked: false,
      yaw_speed: speedValue.value,
      pitch_speed: 0
    });
  } else {
    resetGimbal(currentQuery.dock_sn, {
      payload_index: payloadSelectInfo.payloadIndex,
      reset_mode: 0
    });
  }
}

// 退出飞行控制
async function exitFlightCOntrol() {
  if (!hasAirportAuth.value) {
    return;
  }
  if (mouseDown.value != '') {
    ElMessage.error('请先进行急停，再进行操作');
    return;
  }
  try {
    postDrcExit({
      client_id: 'drc_web',
      dock_sn: currentQuery.dock_sn
    })
      .then(data => {
        hasAirportAuth.value = false;
        localStorage.setItem('hasAirportAuth', false);
        ElMessage({
          message: '操作成功',
          grouping: true,
          type: 'success'
        });
      })
      .catch(() => {
        hasAirportAuth.value = false;
        localStorage.setItem('hasAirportAuth', false);
      });
  } catch (error) {
    console.log(error);
    // ElMessage.error(error.message)
  }
}

function addSpeekVolume() {
  setVolume(
    {
      sn: currentQuery.dock_sn
    },
    {
      play_volume: speekVolumeValue.value
    }
  ).then(data => {
    ElMessage.success('设置成功');
    speekVolumeValue.value += 1;
  });
}

function subSpeekVolume() {
  setVolume(
    {
      sn: currentQuery.dock_sn
    },
    {
      play_volume: speekVolumeValue.value
    }
  ).then(data => {
    ElMessage.success('设置成功');
    speekVolumeValue.value -= 1;
  });
}

function changeSpeekVolume(value) {
  setVolume(
    {
      sn: currentQuery.dock_sn
    },
    {
      play_volume: value
    }
  ).then(data => {
    speekVolumeValue.value = value;
    ElMessage.success('设置成功');
  });
}
</script>

<template>
  <div class="flex">
    <div class="control-box">
      <div class="alarm-title">
        <svg-icon icon-class="title" style="margin-right: 4" />
        <span>{{ status == '14' ? '机场控制' : '飞行器控制' }}</span>
        <el-popover placement="top" title="" offset="20" :width="850" trigger="click">
          <template #default>
            <img src="../../../assets/Shortcutkey.png" alt="" width="828" height="376" />
          </template>
          <template #reference>
            <img src="../../../assets/help.png" alt="" class="help-btn" />
          </template>
        </el-popover>
      </div>
      <div class="control-item" v-if="visible">
        <div class="one-click-fly" v-if="airStatus == '0' && (status == '14' || status == '0')" @click="onekeyFly">
          <svg-icon icon-class="fly" style="margin-right: 4" />
          一键起飞
        </div>
        <div v-if="airStatus == '0' && (status == '14' || status == '0')">
          <el-form
            class="app-form"
            ref="dataFormRef"
            :model="form"
            :rules="rules"
            label-width="150px"
            hide-required-asterisk
          >
            <el-form-item label="飞行高度（ALT）" prop="target_height">
              <el-input-number
                v-model="form.target_height"
                :min="0"
                :step="1"
                style="width: 200px"
                controls-position="right"
                placeholder="请输入飞行高度"
              />
            </el-form-item>
            <el-form-item label="返航高度（ALT）" prop="rth_altitude">
              <el-input-number
                v-model="form.rth_altitude"
                :min="0"
                :step="1"
                style="width: 200px"
                controls-position="right"
                placeholder="请输入返航高度"
              />
            </el-form-item>
            <el-form-item label="失航动作" prop="commander_mode_lost_action">
              <el-select
                disabled
                v-model="form.commander_mode_lost_action"
                placeholder="请选择航线失控动作"
                style="width: 200px"
              >
                <el-option
                  v-for="item in optionData.outofControlActionList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div class="flex" v-if="airStatus != '0' && (status != '14' || status != '0')">
          <div class="Keyboard">
            <div class="flex">
              <div class="fly-btn">
                <svg-icon icon-class="turn-left" style="margin-right: 4" />
              </div>
              <div class="fly-btn">
                <svg-icon icon-class="top" style="margin-right: 4" />
              </div>
              <div class="fly-btn">
                <svg-icon icon-class="turn-right" style="margin-right: 4" />
              </div>
              <div class="fly-btn">
                <svg-icon icon-class="up-arrow" style="margin-right: 4" />
              </div>
            </div>
            <div class="flex">
              <div
                :class="
                  mouseDown == 'KeyQ'
                    ? ['letter', 'letter-current']
                    : hasAirportAuth === true
                    ? ['letter']
                    : 'letter-not'
                "
                @click="onMouseDown(Key_Code.KEY_Q)"
              >
                Q
              </div>
              <div
                :class="
                  mouseDown == 'KeyW'
                    ? ['letter', 'letter-current']
                    : hasAirportAuth === true
                    ? ['letter']
                    : 'letter-not'
                "
                @click="onMouseDown(Key_Code.KEY_W)"
              >
                W
              </div>
              <div
                :class="
                  mouseDown == 'KeyE'
                    ? ['letter', 'letter-current']
                    : hasAirportAuth === true
                    ? ['letter']
                    : 'letter-not'
                "
                @click="onMouseDown(Key_Code.KEY_E)"
              >
                E
              </div>
              <div
                :class="
                  mouseDown == 'KeyC'
                    ? ['letter', 'letter-current']
                    : hasAirportAuth === true
                    ? ['letter']
                    : 'letter-not'
                "
                @click="onMouseDown(Key_Code.KEY_C)"
              >
                C
              </div>
            </div>
            <div class="flex">
              <div
                :class="
                  mouseDown == 'KeyA'
                    ? ['letter', 'letter-current']
                    : hasAirportAuth === true
                    ? ['letter']
                    : 'letter-not'
                "
                @click="onMouseDown(Key_Code.KEY_A)"
              >
                A
              </div>
              <div
                :class="
                  mouseDown == 'KeyS'
                    ? ['letter', 'letter-current']
                    : hasAirportAuth === true
                    ? ['letter']
                    : 'letter-not'
                "
                @click="onMouseDown(Key_Code.KEY_S)"
              >
                S
              </div>
              <div
                :class="
                  mouseDown == 'KeyD'
                    ? ['letter', 'letter-current']
                    : hasAirportAuth === true
                    ? ['letter']
                    : 'letter-not'
                "
                @click="onMouseDown(Key_Code.KEY_D)"
              >
                D
              </div>
              <div
                :class="
                  mouseDown == 'KeyZ'
                    ? ['letter', 'letter-current']
                    : hasAirportAuth === true
                    ? ['letter']
                    : 'letter-not'
                "
                @click="onMouseDown(Key_Code.KEY_Z)"
              >
                Z
              </div>
            </div>
            <div class="flex">
              <div class="fly-btn">
                <svg-icon icon-class="left" style="margin-right: 4; color: #667085" />
              </div>
              <div class="fly-btn">
                <svg-icon icon-class="bottom" style="margin-right: 4" />
              </div>
              <div class="fly-btn">
                <svg-icon icon-class="right" style="margin-right: 4; color: #667085" />
              </div>
              <div class="fly-btn">
                <svg-icon icon-class="down-arrow" style="margin-right: 4" />
              </div>
            </div>
          </div>
          <div>
            <div
              :class="
                isBack === true ? 'one-chick-fly-not' : hasAirportAuth === true ? 'one-chick-fly-not' : 'one-click-fly'
              "
              @click="enterFlightControl"
            >
              {{ hasAirportAuth === true ? '控制中' : '远程控制' }}
            </div>
            <div :class="isBack ? 'one-chick-fly-not' : 'one-click-fly'" @click="oneKeyBack">一键返航</div>
            <div :class="hasAirportAuth === true ? 'one-click-fly' : 'one-chick-fly-not'" @click="exitFlightCOntrol">
              恢复任务
            </div>
            <div
              :class="
                mouseDown == 'Space'
                  ? ['one-click-fly', 'letter-current']
                  : hasAirportAuth === true
                  ? ['one-click-fly']
                  : 'one-chick-fly-not'
              "
              @click="handleStop"
            >
              急停Space
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="control-box">
      <div class="alarm-title">
        <svg-icon icon-class="title" style="margin-right: 4" />
        <span>云台控制</span>
      </div>
      <div class="cloud-item" v-if="visible">
        <div>
          <div :class="hasAuth ? 'one-chick-fly-not' : 'one-click-fly'" @click="openControl">
            <svg-icon icon-class="cloud_control" style="margin-right: 4" />
            {{ hasAuth ? '控制中' : '开启控制' }}
          </div>
          <div class="cloud">
            <div class="top" @click="handleCloud('top')"></div>
            <div class="bottom" @click="handleCloud('bottom')"></div>
            <div class="left" @click="handleCloud('left')"></div>
            <div class="right" @click="handleCloud('right')"></div>
            <div class="center" @click="handleCloud('reset')"></div>
          </div>
        </div>
        <div class="cloud-right">
          <div class="action-type">
            <div
              :class="cameraType == 'video' ? ['shutter-box', 'currentColor'] : ['shutter-box']"
              @click="changeCameraType('video')"
              title="录像功能"
            >
              <svg-icon icon-class="video" style="margin-top: 7; width: 26px; height: 26px" />
            </div>
            <div
              class="shutter-box"
              v-show="cameraType == 'photograph'"
              style="margin: 0 10px 0 20px"
              @click="phoneOrVideo"
              title="拍照"
            >
              <svg-icon icon-class="shutter" style="margin-top: 5; width: 30px; height: 30px" />
            </div>
            <div
              class="shutter-box"
              v-show="cameraType == 'video'"
              style="margin: 0 10px 0 20px"
              @click="playVideo"
              title="录像"
            >
              <div class="video-box"></div>
            </div>
            <div
              :class="cameraType == 'photograph' ? ['shutter-box', 'currentColor'] : ['shutter-box']"
              @click="changeCameraType('photograph')"
              title="拍照功能"
            >
              <svg-icon icon-class="photograph" style="margin-top: 7; width: 26px; height: 26px" />
            </div>
          </div>
          <div class="timepiece" v-show="cameraType == 'video'">
            {{ `${timeObj.h} : ${timeObj.m} : ${timeObj.s}` }}
          </div>
          <div class="cloud-type">
            <div class="cloud-type-top">
              <div :class="distanceType == 'zoom' ? 'currentColor' : ''" @click="changeDistance('zoom')">变焦</div>
              <div :class="distanceType == 'wide' ? 'currentColor' : ''" @click="changeDistance('wide')">广角</div>
              <div :class="distanceType == 'ir' ? 'currentColor' : ''" @click="changeDistance('ir')">红外</div>
            </div>
            <div class="cloud-type-bottom">
              <div class="cloud-type-change">
                <svg-icon
                  icon-class="add"
                  class="add-icon"
                  style="margin-right: 4"
                  v-if="distanceType != 'wide'"
                  @click="addDistance"
                />
                <el-slider
                  class="slider"
                  :min="2"
                  :max="56"
                  v-if="distanceType == 'zoom' || distanceType == ''"
                  v-model="zoomValue"
                  style="width: 120px"
                  @change="changeCloudValue"
                />
                <!-- <el-slider class="slider" :min="2" :max="200" v-if="distanceType == 'wideAngle'" v-model="wideValue" style="width:120px;" @change="changeCloudValue"/> -->
                <!-- <el-switch v-model="irValue" v-if="distanceType == 'infrared'" @change="changeCloudValue" style="margin-left: 70px"/> -->
                <el-slider
                  class="slider"
                  :min="2"
                  :max="20"
                  v-if="distanceType == 'ir'"
                  v-model="irValue"
                  style="width: 120px"
                  @change="changeCloudValue"
                />
                <svg-icon
                  icon-class="minus"
                  class="sub-icon"
                  style="margin-right: 4"
                  v-if="distanceType != 'wide'"
                  @click="subDistance"
                />
              </div>
            </div>
          </div>
          <div class="cloud-speed">
            <div class="speed-title">云台速度</div>
            <div class="speed-change">
              <svg-icon icon-class="add" class="add-icon" style="margin-right: 4" @click="addCloudSpeed" />
              <el-slider
                class="slider"
                :min="1"
                :max="200"
                v-model="speedValue"
                style="width: 120px"
                @change="changeSpeedValue"
              />
              <svg-icon icon-class="minus" class="sub-icon" style="margin-right: 4" @click="subCloudSpeed" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="control-box">
      <div class="alarm-title">
        <svg-icon icon-class="title" style="margin-right: 4" />
        <span>负载控制</span>
      </div>
      <div class="control-item flex" v-if="visible">
        <div>
          <div
            :class="loadType == 'play' ? ['one-click-fly', 'currentColor'] : 'one-click-fly'"
            @click="changeLoad('play')"
          >
            <svg-icon icon-class="video_play" style="margin-right: 4" />播放录音
          </div>
          <div
            :class="loadType == 'text' ? ['one-click-fly', 'currentColor'] : 'one-click-fly'"
            @click="changeLoad('text')"
          >
            <svg-icon icon-class="text_to_speech" style="margin-right: 4" />文字转语音
          </div>
          <!-- <div :class=" loadType == 'speak' ? ['one-click-fly','currentColor'] : 'one-click-fly'" @click="changeLoad('speak')">
						<svg-icon icon-class="microphone" style="margin-right: 4;"/>实时喊话
					</div> -->
          <div
            style="position: relative"
            :class="loadType == 'alarm' ? ['one-click-fly', 'currentColor'] : 'one-click-fly'"
            @click="changeLoad('alarm')"
          >
            <span :class="handleclass()"></span>
            <span :class="lampType == '' ? '' : 'ml-10'">{{ handleName() }}</span>
          </div>
        </div>
        <div v-if="loadType == 'speak'">
          <div class="play-box" v-if="loadType == 'speak'">
            <div class="microphone"></div>
          </div>
          <div class="volume-box">
            <div>音量</div>
            <div class="cloud-change">
              <svg-icon icon-class="add" class="add-icon" style="margin-right: 4; top: 13px" />
              <el-slider v-model="speekVolumeValue" style="width: 170px; height: 40px" @change="changeSpeekVolume" />
              <svg-icon icon-class="minus" class="sub-icon" style="margin-right: 4; bottom: 13px" />
            </div>
          </div>
        </div>
        <div v-else-if="loadType == 'text'">
          <div class="play-box">
            <el-input
              v-model="textareaValue"
              :rows="6"
              :autosize="{ minRows: 6, maxRows: 6 }"
              type="textarea"
              placeholder="请输入"
              resize="none"
              :autofocus="false"
            />
          </div>
          <div class="volume-box">
            <div>音量</div>
            <div class="cloud-change">
              <svg-icon icon-class="add" class="add-icon" style="margin-right: 4; top: 13px" @click="addSpeekVolume" />
              <el-slider v-model="speekVolumeValue" style="width: 170px; height: 40px" @change="changeSpeekVolume" />
              <svg-icon
                icon-class="minus"
                class="sub-icon"
                style="margin-right: 4; bottom: 13px"
                @click="subSpeekVolume"
              />
            </div>
            <!-- <div :class="isLoop ? 'loop-play-current' : 'loop-play'" @click="handleLoop">
							<svg-icon icon-class="loop" style="width: 16px;height: 16px"/>
						</div> -->
            <div class="video-play" @click="textToSpeek" title="播放">
              <svg-icon :icon-class="textPlay ? 'stop' : 'volume_play'" style="width: 16px; height: 16px" />
            </div>
          </div>
        </div>
        <div v-else-if="loadType == 'play'">
          <div class="play-box">
            <div
              :class="recordValue.file_name == item.file_name ? 'record-current' : 'record-list'"
              v-for="(item, index) in videoList"
              :key="index"
              @click="handleClickAudio(item)"
            >
              {{ item.file_name }}
            </div>
          </div>
          <div class="volume-box">
            <div>音量</div>
            <div class="cloud-change">
              <svg-icon icon-class="add" class="add-icon" style="margin-right: 4; top: 13px" @click="addSpeekVolume" />
              <el-slider v-model="speekVolumeValue" style="width: 140px; height: 40px" @change="changeSpeekVolume" />
              <svg-icon
                icon-class="minus"
                class="sub-icon"
                style="margin-right: 4; bottom: 13px"
                @click="subSpeekVolume"
              />
            </div>
            <div :class="'loop-play'" @click="handleLoop" title="刷新">
              <svg-icon icon-class="loop" style="width: 16px; height: 16px" />
            </div>
            <div class="video-play" @click="handlePlayAudio()" title="播放">
              <svg-icon :icon-class="recordPlay ? 'stop' : 'volume_play'" style="width: 16px; height: 16px" />
            </div>
          </div>
        </div>
        <div v-else>
          <div class="play-box">
            <div
              :class="lampType == item.key ? ['lamp-list', 'currentColor'] : 'lamp-list'"
              v-for="(item, index) in optionData.lampList"
              :key="index"
              @click="handleLamp(item)"
            >
              {{ item.label }}
            </div>
          </div>
          <div class="volume-box"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.ml-10 {
  margin-left: 10px;
}
:deep(.el-input__wrapper) {
  background-color: #11253e;
  border: 1px solid #475467;
  border-color: #475467 !important;
  box-shadow: none;
}
:deep(.el-input-number__decrease) {
  background-color: #11253e;
  color: #fff;
  box-shadow: none;
  border-left: 1px solid #475467;
}
:deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: #11253e;
  box-shadow: none;
  color: #606266;
}
:deep(.el-select) {
  --el-select-border-color-hover: none;
  --el-select-disabled-border: none;
}
:deep(.el-input__inner) {
  color: #98a2b3;
}
:deep(.el-input-number.is-controls-right .el-input-number__increase) {
  border-bottom: 1px solid #475467;
  border-left: 1px solid #475467;
  box-shadow: none;
}
:deep(.el-input-number.is-controls-right .el-input-number__decrease) {
  border-left: 1px solid #475467;
  box-shadow: none;
}
:deep(.el-input-number__increase) {
  color: #fff;
  box-shadow: none;
  border-left: 1px solid #475467;
  background-color: #11253e;
}
:deep(.el-slider__button) {
  width: 15px;
  height: 15px;
}
:deep(.el-slider__runway) {
  background-color: #27374a;
}
:deep(.el-textarea__inner) {
  color: #fff;
  box-shadow: none;
  background-color: #11253e;
}
:deep(.el-textarea__inner:focus) {
  box-shadow: none;
}
.control-box {
  flex: 1;
  color: #fff;
  background: #001129;
  .alarmIcon {
    position: absolute;
    display: inline-block;
    top: 10px;
    left: 20px;
    width: 20px;
    height: 20px;
    margin-right: 5px;
    background-image: url('../../../assets/alarmLamp.png');
  }
  .alarmIcon-hl {
    position: absolute;
    display: inline-block;
    top: 10px;
    left: 20px;
    width: 20px;
    height: 20px;
    margin-right: 5px;
    background-image: url('../../../assets/redblue.png');
  }
  .alarmIcon-rr {
    position: absolute;
    display: inline-block;
    top: 10px;
    left: 20px;
    width: 20px;
    height: 20px;
    margin-right: 5px;
    background-image: url('../../../assets/redred.png');
  }
  .alarmIcon-ll {
    position: absolute;
    display: inline-block;
    top: 10px;
    left: 20px;
    width: 20px;
    height: 20px;
    margin-right: 5px;
    background-image: url('../../../assets/blueblue.png');
  }
}
.Keyboard {
  width: 250px;
  margin: 0 30px;
  .letter {
    width: 52px;
    height: 52px;
    line-height: 52px;
    text-align: center;
    background-image: linear-gradient(180deg, #1f416b 14%, #11253e 82%);
    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.5), inset 0 1px 3px 0 #587eae;
    border-radius: 4px;
    font-family: SourceHanSansSC-Bold;
    font-size: 20px;
    color: #ffffff;
    font-weight: 700;
    margin-right: 15px;
    margin-bottom: 15px;
    cursor: pointer;
    transition: all 0.2s linear;
  }
  .letter-not {
    width: 52px;
    height: 52px;
    line-height: 52px;
    text-align: center;
    background-image: linear-gradient(180deg, #11243c 14%, #071323 82%);
    box-shadow: 0 1px 1px 1px rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    font-family: SourceHanSansSC-Bold;
    font-size: 20px;
    color: #ffffff;
    font-weight: 700;
    margin-right: 15px;
    margin-bottom: 15px;
    cursor: not-allowed;
    transition: all 0.2s linear;
  }
  .letter:active {
    transform: scale3d(0.92, 0.92, 1);
  }
  .letter-current {
    transform: scale3d(0.92, 0.92, 1);
  }
  .fly-btn {
    width: 52px;
    height: 15px;
    margin-right: 15px;
    margin-bottom: 15px;
    text-align: center;
  }
}
.microphone {
  width: 126px;
  height: 126px;
  text-align: center;
  cursor: pointer;
  margin-top: 10px;
  margin-left: 80px;
  background: url('@/assets/microphone.png') 126px 126px;
}
.play-box {
  width: 314px;
  height: 160px;
  padding: 8px;
  background-color: #11253e;
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #ffffff;
  text-align: justify;
  line-height: 22px;
  font-weight: 400;
  .record-current {
    background: #2e90fa;
    height: 30px;
    line-height: 30px;
  }
  .record-list {
    height: 30px;
    line-height: 30px;
  }
  div {
    cursor: pointer;
    margin-bottom: 3px;
  }
  .lamp-list {
    width: 298px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    background: #344054;
    border-radius: 2px;
  }

  .currentColor {
    background: #2e90fa;
  }
}
.volume-box {
  position: relative;
  display: flex;
  width: 314px;
  height: 40px;
  padding: 0 8px;
  background-color: #11253e;
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #ffffff;
  text-align: justify;
  line-height: 40px;
  font-weight: 400;
  margin-top: 2px;
}
:deep(.el-form-item__label) {
  color: #fff;
}
.cloud-type-change {
  position: relative;
  .slider {
    margin-left: 35px;
  }
  .add-icon {
    cursor: pointer;
    position: absolute;
    top: 7px;
    right: 0px;
  }
  .sub-icon {
    cursor: pointer;
    position: absolute;
    bottom: 7px;
    left: 3px;
    color: #fff;
  }
}
.speed-change {
  position: relative;
  .slider {
    margin-left: 35px;
  }
  .add-icon {
    cursor: pointer;
    position: absolute;
    top: 7px;
    right: 0px;
  }
  .sub-icon {
    cursor: pointer;
    position: absolute;
    bottom: 7px;
    left: 3px;
    color: #fff;
  }
}
.cloud-change {
  position: relative;
  margin-left: 35px;
  .add-icon {
    cursor: pointer;
    position: absolute;
    top: 7px;
    right: -20px;
  }
  .sub-icon {
    cursor: pointer;
    position: absolute;
    bottom: 7px;
    left: -25px;
    color: #fff;
  }
}
.shutter-box {
  cursor: pointer;
  color: #fff;
  width: 40px !important;
  height: 40px !important;
  line-height: 40px;
  text-align: center;
  margin-left: 10px;
  border-radius: 50%;
  background-image: linear-gradient(180deg, #244d7f 14%, #152f4f 82%);
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.5);
}
.video-box {
  width: 24px !important;
  height: 24px !important;
  background: #e6575e !important;
  border-radius: 50%;
  box-shadow: none !important;
  border: none !important;
  margin-top: 8px;
  margin-left: 8px;
}
.control-item,
.cloud-item {
  border-right: 1px solid #344054;
  color: #fff;
  height: 234px;
  padding-top: 16px;
  .currentColor {
    color: #fff;
    background: #2e90fa;
  }
}
.help-btn {
  margin-left: 5px;
  transform: translateY(3px);
  cursor: pointer;
}
:deep(.el-textarea) {
  --el-input-focus-border: none;
}
.video-play,
.loop-play {
  position: absolute;
  top: 5px;
  right: 10px;
  width: 30px;
  height: 30px;
  cursor: pointer;
  text-align: center;
  line-height: 30px;
  background-color: #344054;
}
.loop-play-current {
  position: absolute;
  top: 5px;
  right: 10px;
  width: 30px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  background-color: #2e90fa;
}
.loop-play {
  right: 50px;
}
.cloud-item {
  padding: 15px;
  display: flex;
}
.timepiece {
  width: 180px;
  height: 17px;
  line-height: 17px;
  text-align: center;
  margin-left: 25px;
}
.cloud-right {
  margin-left: 40px;
  .cloud-type {
    margin-left: 25px;
    margin-top: 13px;
    margin-bottom: 10px;
    .cloud-type-top {
      display: flex;
      div {
        width: 60px;
        height: 32px;
        line-height: 32px;
        background-image: linear-gradient(180deg, #1f416b 14%, #11253e 82%);
        border-bottom: 1px solid #344054;
        text-align: center;
        cursor: pointer;
      }
      .currentColor {
        color: #fff;
        background: #2e90fa;
      }
    }
    .cloud-type-bottom {
      width: 180px;
      height: 35px;
      background: #11253e;
      border-radius: 0 0 2px 2px;
    }
  }
  .cloud-speed {
    width: 180px;
    height: 68px;
    background: #11253e;
    margin-left: 25px;
    .speed-title {
      height: 32px;
      line-height: 32px;
      width: 100%;
      text-align: center;
      font-family: SourceHanSansSC-Regular;
      font-size: 14px;
      color: #ffffff;
      text-align: center;
      font-weight: 400;
      border-bottom: 1px solid #344054;
    }
  }
}

.action-type {
  display: flex;
  margin-left: 25px;
  div {
    width: 60px;
    cursor: pointer;
    height: 38px;
    line-height: 38px;
    background-image: linear-gradient(180deg, #1f416b 14%, #11253e 82%);
    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.5), inset 0 1px 3px 0 #587eae;
    text-align: center;
  }
  .currentColor {
    color: #fff;
    background: #2e90fa;
  }
}
.cloud {
  position: relative;
  width: 168px;
  height: 168px;
  background-image: url('../../../assets/cloud-control.png');
  .top {
    width: 18px;
    height: 18px;
    position: absolute;
    cursor: pointer;
    top: 12px;
    left: 50%;
    transform: translateX(-50%);
    background-image: url('../../../assets/top.png');
  }
  .bottom {
    width: 18px;
    height: 15px;
    position: absolute;
    cursor: pointer;
    bottom: 18px;
    left: 50%;
    transform: translateX(-50%);
    background-image: url('../../../assets/bottom.png');
  }
  .left {
    width: 15px;
    height: 20px;
    position: absolute;
    cursor: pointer;
    top: 50%;
    transform: translateY(-50%);
    left: 12px;
    background-image: url('../../../assets/left.png');
  }
  .right {
    width: 15px;
    height: 20px;
    position: absolute;
    cursor: pointer;
    top: 50%;
    transform: translateY(-50%);
    right: 12px;
    background-image: url('../../../assets/right.png');
  }
  .center {
    width: 20px;
    height: 22px;
    position: absolute;
    cursor: pointer;
    top: 49%;
    right: 50%;
    transform: translate(50%, -50%);
    background-image: url('../../../assets/reset.png');
  }
}
.one-click-fly {
  width: 138px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #ffffff;
  text-align: center;
  font-weight: 400;
  background-image: linear-gradient(180deg, #1f416b 14%, #11253e 82%);
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.5), inset 0 1px 3px 0 #587eae;
  border-radius: 4px;
  margin: 16px;
  margin-top: 0;
  cursor: pointer;
  transition: all 0.2 linear;
}
.one-chick-fly-not {
  width: 138px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #ffffff;
  text-align: center;
  font-weight: 400;
  margin: 16px;
  margin-top: 0;
  cursor: not-allowed;
  transition: all 0.2 linear;
  background-image: linear-gradient(180deg, #11243c 14%, #071323 82%);
  box-shadow: 0 1px 1px 1px rgba(0, 0, 0, 0.5);
  border-radius: 4px;
}
.one-click-fly:active {
  transform: scale3d(0.92, 0.92, 1);
}
.alarm-title {
  height: 38px;
  line-height: 38px;
  background: #11253e;
  color: #fff;
  font-family: SourceHanSansSC-Bold;
  font-size: 14px;
  text-align: left;
  font-weight: 700;
  border-bottom: 1px solid #344054;
  padding-left: 8px;
}
.info-container {
  color: #fff;
  width: 100%;
  height: 200px;
  background: #001129;
  padding-top: 20px;
}
.info-item {
  width: 50%;
  text-align: center;
}
.flex {
  display: flex;
  justify-content: start;
}
</style>
