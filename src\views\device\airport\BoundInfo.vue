<template>
  <el-dialog
    title="设备绑定码"
    :model-value="visible"
    v-if="visible"
    width="500px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form class="app-form" ref="dataFormRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="设备绑定码" prop="mqtt_addr">
        <el-input
          v-model="infos.bind_code"
          placeholder="请输入设备绑定码"
          maxlength="64"
          @blur="infos.bind_code = $event.target.value.trim()"
        />
      </el-form-item>
      <el-form-item label="MQTT网关地址" prop="mqtt_addr">
        <el-input
          disabled
          v-model="infos.mqtt_addr"
          placeholder="请输入MQTT网关地址"
          maxlength="64"
        />
      </el-form-item>
      <el-form-item label="MQTT账号" prop="mqtt_username">
        <el-input
          disabled
          v-model="infos.mqtt_username"
          placeholder="请输入MQTT账号"
          maxlength="64"
        />
      </el-form-item>
      <el-form-item label="MQTT密码" prop="mqtt_password">
        <el-input
          disabled
          v-model="infos.mqtt_password"
          placeholder="请输入MQTT密码"
          maxlength="64"
        />
      </el-form-item>
      <el-form-item label="组织ID" prop="workspace_id">
        <el-input
          disabled
          v-model="infos.workspace_id"
          placeholder="请输入组织ID"
          maxlength="64"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-loading="loading">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
  
  <script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { useUserStoreHook } from '@/store/modules/user';
import { updateBindCode } from '@/api/devices';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  formData: {
    type: Object,
    default(rawProps) {
      return {};
    }
  }
});
const form = reactive({});
const dataFormRef = ref(ElForm);
const loading = ref(false);
const infos = reactive({});

watch(
  () => props.formData,
  (newVal, oldVal) => {
    Object.assign(form, newVal);
  },
  { deep: true }
);
const emit = defineEmits(['update:visible']);

// 关闭弹窗
function closeDialog() {
  emit('update:visible', false);
}

onMounted(() => {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  Object.assign(infos, userData);
});
const handleSubmit = async() => {
  const userStore = useUserStoreHook();
  dataFormRef.value.validate(isValid => {
    if (isValid) {
      try {
        loading.value = true;
        updateBindCode({
          ...infos
        })
          .then(res => {
            loading.value = false;
            userStore.getInfo();
            ElMessage.success('更新成功');
            closeDialog();
            emit('submit');
          })
          .catch(e => {
            loading.value = false;
          });
      }catch (error) {
        console.log('error: ', error);
      }
    } else {
      loading.value = false;
    }
  });
}
</script>
  <style lang="scss" scoped>
.org-name {
  font-size: 14px;
  color: #000000a6;
}
.left-content {
  display: flex;
  flex-direction: row;
  img {
    width: 136px;
    height: 136px;
    margin-right: 24px;
  }
  .line-left {
    display: flex;
    padding: 20px 8px;
  }
  .line-middle {
    width: 1px;
    height: 314px;
    background: rgba(0, 0, 0, 0.06);
    margin: auto 32px;
  }
  .line-right {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    .item {
      display: flex;
      flex-direction: row;
      padding: 6px;
      width: 100%;
      color: #98a2b3;
      .color {
        color: #98a2b3;
      }
    }
  }
}
.content-line {
  display: flex;
  font-size: 14px;
  line-height: 22px;
  .foot {
    color: #98a2b3;
  }
  .title {
    color: #98a2b3;
  }
}
</style>
  