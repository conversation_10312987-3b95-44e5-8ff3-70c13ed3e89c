import * as Cesium from 'cesium';
import gatherPoint from './resource/gatherPoint.png';

export const drawElementGather = drawDataSource => ({
  //一次仅有一个采集事件,gatherHandler
  gatherHandler: null,
  dataSource: drawDataSource,
  //鼠标提示
  openMouseTipHandler: null,
  openMouseTipLabelEntity: null,
  openMouseTipLabel: '',
  openMouseTipOption: { backgroundColor: '#001129', color: '#FFFFFF' },
  //如果存在gatherHandler，则先销毁
  gatherHandlerDestroy() {
    if (this.gatherHandler) {
      this.gatherHandler.destroy();
      this.gatherHandler = null;
    }
    //关闭鼠标提示
    this.closeMouseTip();
  },
  /**
   * 将椭球坐标转换为经纬度
   *
   * 本函数的目的是将给定的椭球坐标（cartographic）转换成更常见的经纬度表示，
   * 同时保留高度信息。这种转换常用于地理信息系统(GIS)中，以便于显示或进一步处理。
   *
   * @param {Cartographic} cartographic 椭球坐标对象，包含经度、纬度和高度
   * @returns {Object} 包含度数表示的经度（lng）、纬度（lat）和高度（height）的对象
   */
  getPntDegrees(cartographic) {
    let obj = {};
    // 将椭球坐标中的经度从弧度转换为度数
    obj.lng = Cesium.Math.toDegrees(cartographic.longitude);
    // 将椭球坐标中的纬度从弧度转换为度数
    obj.lat = Cesium.Math.toDegrees(cartographic.latitude);
    // 保留椭球坐标中的高度信息
    obj.height = cartographic.height;
    return obj;
  },

  /**
   * 坐标转换
   * @param {*} coordinate Cartesian3 对象
   * @returns 经纬度 坐标对象
   */
  toDegrees(cartesian3Coordinate) {
    let the = this;
    let position = null;
    if (cartesian3Coordinate instanceof Cesium.Cartesian3) {
      // 将 Cartesian3 转换为 Cartographic
      let cartographicCoordinate = Cesium.Cartographic.fromCartesian(cartesian3Coordinate);
      // 从 Cartographic 获取经纬度和高度
      let longitude = the.toNumber(Cesium.Math.toDegrees(cartographicCoordinate.longitude), 7);
      //  parseFloat(Cesium.Math.toDegrees(cartographicCoordinate.longitude).toFixed(7));
      let latitude = the.toNumber(Cesium.Math.toDegrees(cartographicCoordinate.latitude), 7); // parseFloat(Cesium.Math.toDegrees(cartographicCoordinate.latitude).toFixed(7));
      let height = the.toNumber(cartographicCoordinate.height, 3); // parseFloat(cartographicCoordinate.height.toFixed(3));
      position = [longitude, latitude, height];
    }
    return position;
  },

  /**
   * 转为数字
   * @param {*} value
   * @param {*} precision
   * @returns
   */
  toNumber(value = 0, precision = 2) {
    if (isNaN(value)) {
      return 0;
    }
    if (value === 0) {
      return 0;
    }
    // 将参数转换为数字类型，如果无法转换则抛出错误
    const numValue = Number(value);
    const numPrecision = Number(precision);
    if (isNaN(numValue) || isNaN(numPrecision)) {
      console.log('numValue:,numPrecision', numValue, numPrecision);
      throw new Error('Parameters must be convertible to numbers');
    }

    // 将 numValue 四舍五入到指定精度
    const multiplier = Math.pow(10, numPrecision);
    return Math.round(numValue * multiplier) / multiplier;
  },

  /**
   * 通用产生采集点
   * @param {*} cartesian 坐标点
   * @param {*} dataSource 数据源
   * @returns
   */
  createGatherPoint(cartesian, dataSource) {
    let point = dataSource.entities.add({
      position: cartesian,
      billboard: {
        image: gatherPoint,
        width: 12,
        height: 12,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    });
    return point;
  },

  /**
   * 采集点功能。
   * 通过左键点击屏幕添加点，计算并返回点的地理坐标和高度信息。
   *
   * @param {Function} callback - 点绘制完成后的回调函数，接收点实体作为参数。
   * @param {Object} option - 绘制点的选项，
   *         color: '#FBFF65' - 颜色
   *         alpha: 0.5 - 透明度
   *         viewer: getCesiumEngineInstance('homeMap-fly').viewer - viewer对象
   *         elementId: 'mainMapContainer' - 容器id
   */
  pointGather(callback, option) {
    let the = this;
    //移除事件
    this.openMouseTip('左键点击即可完成点选绘制', option.viewer);
    let gatherPointEntity = null;
    //鼠标变成加号
    document.getElementById(option.elementId).style.cursor = 'crosshair';
    this.gatherHandler = new Cesium.ScreenSpaceEventHandler(option.viewer.canvas);
    // 对鼠标按下事件的监听
    this.gatherHandler.setInputAction(function (event) {
      //获取加载地形后对应的经纬度和高程：地标坐标
      var ray = option.viewer.camera.getPickRay(event.position);
      var cartesian = option.viewer.scene.globe.pick(ray, option.viewer.scene);
      if (!Cesium.defined(cartesian)) {
        return;
      }
      gatherPointEntity = the.createGatherPoint(cartesian, the.dataSource);
      //鼠标变成默认
      document.getElementById(option.elementId).style.cursor = 'default';
      //移除事件
      the.gatherHandlerDestroy();
      the.closeMouseTip();
      //设置属性
      let centerPosition = the.toDegrees(gatherPointEntity.position.getValue(Cesium.JulianDate.now()));
      gatherPointEntity.centerPosition = centerPosition;
      callback(gatherPointEntity);
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
  },

  /**
   * 采集线功能。
   * 通过左键不断点击屏幕添加点，计算并返回点的地理坐标和高度信息。
   *
   * @param {Function} callback - 点绘制完成后的回调函数，接收点实体作为参数。
   * @param {Object} option - 绘制点的选项，
   *         color: '#FBFF65' - 颜色
   *         alpha: 0.5 - 透明度
   *         viewer: getCesiumEngineInstance('homeMap-fly').viewer - viewer对象
   *         elementId: 'mainMapContainer' - 容器id
   */
  polylineGather(callback, option) {
    let the = this;
    this.openMouseTip('左键不断的点击地图，右击即可完成线绘制', option.viewer);
    let gatherPolylineEntity = null;
    let entityPoints = [];
    let cartesianPoints = [];
    //鼠标变成加号
    document.getElementById(option.elementId).style.cursor = 'crosshair';
    this.gatherHandler = new Cesium.ScreenSpaceEventHandler(option.viewer.canvas);
    // 对鼠标按下事件的监听
    this.gatherHandler.setInputAction(function (event) {
      //获取加载地形后对应的经纬度和高程：地标坐标
      var ray = option.viewer.camera.getPickRay(event.position);
      var cartesian = option.viewer.scene.globe.pick(ray, option.viewer.scene);
      if (!Cesium.defined(cartesian)) {
        return;
      }
      var point = the.createGatherPoint(cartesian, the.dataSource);
      entityPoints.push(point);
      cartesianPoints.push(cartesian);

      if (cartesianPoints.length >= 2) {
        if (gatherPolylineEntity == null) {
          gatherPolylineEntity = the.dataSource.entities.add({
            polyline: {
              positions: new Cesium.CallbackProperty(function (time, result) {
                return cartesianPoints;
              }, false),
              width: 5,
              material: new Cesium.Color.fromCssColorString(option.color).withAlpha(option.alpha),
              clampToGround: true,
              ...option
            }
          });
        } else {
          //CallbackProperty监听point变化值会自动set
        }
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

    this.gatherHandler.setInputAction(function (rightClick) {
      //移除地图点
      for (var i = 0; i < entityPoints.length; i++) {
        the.dataSource.entities.remove(entityPoints[i]);
      }
      entityPoints = [];
      //鼠标变成加号
      document.getElementById(option.elementId).style.cursor = 'default';
      //移除事件
      the.gatherHandlerDestroy();
      the.closeMouseTip();

      //设置属性
      let dke = gatherPolylineEntity.polyline.positions.getValue();
      gatherPolylineEntity.gatherPoints = [];
      for (let i = 0; i < dke.length; i++) {
        let ellipsoid = option.viewer.scene.globe.ellipsoid;
        let cartesian3 = new Cesium.Cartesian3(dke[i].x, dke[i].y, dke[i].z);
        let cartographic = ellipsoid.cartesianToCartographic(cartesian3);
        let obj = the.getPntDegrees(cartographic);
        gatherPolylineEntity.gatherPoints.push(obj);
      }
      callback(gatherPolylineEntity);
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  },

  /**
   * 采集矩形功能。
   * 通过点击屏幕添加点，拉框形成矩形。
   * 右键点击完成矩形的绘制，计算并返回矩形的地理坐标和高度信息。
   *
   * @param {Function} callback - 矩形绘制完成后的回调函数，接收矩形实体作为参数。
   * @param {Object} option - 绘制多边形的选项，
   *         color: '#FBFF65' - 颜色
   *         alpha: 0.5 - 透明度
   *         viewer: getCesiumEngineInstance('homeMap-fly').viewer - viewer对象
   *         elementId: 'mainMapContainer' - 容器id
   */
  rectangleGather(callback, option) {
    let the = this;
    this.openMouseTip('左击地图后进行拖动，右击完成框选绘制', option.viewer);
    let gatherRectangleEntity = null;
    let startPoint = null;
    //鼠标变成加号
    document.getElementById(option.elementId).style.cursor = 'crosshair';
    //进制地图移动
    option.viewer.scene.screenSpaceCameraController.enableRotate = false;
    option.viewer.scene.screenSpaceCameraController.enableZoom = false;
    this.gatherHandler = new Cesium.ScreenSpaceEventHandler(option.viewer.scene.canvas);
    //鼠标点击事件
    this.gatherHandler.setInputAction(event => {
      //获取加载地形后对应的经纬度和高程：地标坐标
      let ray = option.viewer.camera.getPickRay(event.position);
      let cartesian = option.viewer.scene.globe.pick(ray, option.viewer.scene);
      if (!Cesium.defined(cartesian)) {
        return;
      }
      startPoint = the.createGatherPoint(cartesian, the.dataSource);
      gatherRectangleEntity = the.dataSource.entities.add({
        rectangle: {
          coordinates: Cesium.Rectangle.fromCartesianArray([cartesian, cartesian]),
          material: Cesium.Color.fromCssColorString(option.color).withAlpha(option.alpha)
        }
      });
      if (this.gatherHandler) {
        this.gatherHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOWN);
      }
    }, Cesium.ScreenSpaceEventType.LEFT_DOWN);
    // 对鼠标移动事件的监听
    this.gatherHandler.setInputAction(event => {
      if (startPoint == null || gatherRectangleEntity == null) {
        return;
      }
      //获取加载地形后对应的经纬度和高程：地标坐标
      let ray = option.viewer.camera.getPickRay(event.endPosition);
      let cartesian = option.viewer.scene.globe.pick(ray, option.viewer.scene);
      if (!cartesian) {
        return;
      }
      let startCartesian = startPoint.position.getValue(Cesium.JulianDate.now());
      gatherRectangleEntity.rectangle.coordinates = new Cesium.CallbackProperty(function (time, result) {
        return Cesium.Rectangle.fromCartesianArray([startCartesian, cartesian]);
      }, false);
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

    // 对鼠标抬起事件的监听(结束点采集)
    this.gatherHandler.setInputAction(event => {
      //鼠标变成默认
      document.getElementById(option.elementId).style.cursor = 'default';
      option.viewer.scene.screenSpaceCameraController.enableRotate = true;
      option.viewer.scene.screenSpaceCameraController.enableZoom = true;
      the.dataSource.entities.remove(startPoint);

      let rectangle = gatherRectangleEntity.rectangle.coordinates.getValue();
      gatherRectangleEntity.gatherPoints = [];
      // 获取四个角的经纬度坐标
      let nw = the.getPntDegrees(Cesium.Rectangle.northwest(rectangle));
      let ne = the.getPntDegrees(Cesium.Rectangle.northeast(rectangle));
      let se = the.getPntDegrees(Cesium.Rectangle.southeast(rectangle));
      let sw = the.getPntDegrees(Cesium.Rectangle.southwest(rectangle));
      gatherRectangleEntity.gatherPoints.push(nw);
      gatherRectangleEntity.gatherPoints.push(ne);
      gatherRectangleEntity.gatherPoints.push(se);
      gatherRectangleEntity.gatherPoints.push(sw);
      //移除事件
      the.gatherHandlerDestroy();
      the.closeMouseTip();
      callback(gatherRectangleEntity);
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  },

  /**
   * 采集圆形功能。
   * 通过点击屏幕添加点，拖动形成圆形。
   * 右键点击完成多边形的绘制，计算并返回矩形的地理坐标和高度信息。
   *
   * @param {Function} callback - 圆形绘制完成后的回调函数，接收圆形实体作为参数。
   * @param {Object} option - 绘制圆形的选项，
   *         color: '#FBFF65' - 颜色
   *         alpha: 0.5 - 透明度
   *         viewer: getCesiumEngineInstance('homeMap-fly').viewer - viewer对象
   *         elementId: 'mainMapContainer' - 容器id
   */
  circleGather(callback, option) {
    this.openMouseTip('左击后拖动，右击即可完成圆形绘制', option.viewer);
    let the = this;
    let viewer = option.viewer;
    let gatherCircleEntity = null;
    let centerPoint = null;
    //鼠标变成加号
    document.getElementById(option.elementId).style.cursor = 'crosshair';
    //进制地图移动
    viewer.scene.screenSpaceCameraController.enableRotate = false;
    viewer.scene.screenSpaceCameraController.enableZoom = false;
    this.gatherHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    //鼠标点击事件
    this.gatherHandler.setInputAction(event => {
      //获取加载地形后对应的经纬度和高程：地标坐标
      var ray = viewer.camera.getPickRay(event.position);
      var cartesian = viewer.scene.globe.pick(ray, viewer.scene);
      if (!Cesium.defined(cartesian)) {
        return;
      }
      centerPoint = the.createGatherPoint(cartesian, the.dataSource);
      //默认生成半径为0.1米的圆。
      gatherCircleEntity = the.dataSource.entities.add({
        position: cartesian,
        ellipse: {
          semiMinorAxis: 0.1, //椭圆短轴（单位米）
          semiMajorAxis: 0.1, //椭圆长轴（单位米）
          material: Cesium.Color.fromCssColorString(option.color).withAlpha(option.alpha),
          outline: true,
          outlineColor: Cesium.Color.WHITE,
          outlineWidth: 3
        }
      });
      this.gatherHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOWN);
    }, Cesium.ScreenSpaceEventType.LEFT_DOWN);

    // 对鼠标移动事件的监听
    this.gatherHandler.setInputAction(event => {
      if (centerPoint == null || gatherCircleEntity == null) {
        return;
      }
      //获取加载地形后对应的经纬度和高程：地标坐标
      var ray = viewer.camera.getPickRay(event.endPosition);
      var radiusCartesian = viewer.scene.globe.pick(ray, viewer.scene);
      if (!radiusCartesian) {
        return;
      }
      var centerCartesian = centerPoint.position.getValue(Cesium.JulianDate.now());
      //计算移动点与中心点的距离（单位米）
      var centerTemp = viewer.scene.globe.ellipsoid.cartesianToCartographic(centerCartesian);
      var radiusTemp = viewer.scene.globe.ellipsoid.cartesianToCartographic(radiusCartesian);
      var geodesic = new Cesium.EllipsoidGeodesic();
      geodesic.setEndPoints(centerTemp, radiusTemp);
      var radius = geodesic.surfaceDistance;
      //console.log("radius",radius);
      //如果半径小于0,则不更新圆信息
      if (radius <= 0) {
        return;
      }
      gatherCircleEntity.ellipse.semiMinorAxis = new Cesium.CallbackProperty(function (time, result) {
        return radius;
      }, false);
      gatherCircleEntity.ellipse.semiMajorAxis = new Cesium.CallbackProperty(function (time, result) {
        return radius;
      }, false);
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

    // 对鼠标抬起事件的监听(结束点采集)
    this.gatherHandler.setInputAction(event => {
      //鼠标变成默认
      document.getElementById(option.elementId).style.cursor = 'default';
      //开始鼠标操作地图
      viewer.scene.screenSpaceCameraController.enableRotate = true;
      viewer.scene.screenSpaceCameraController.enableZoom = true;

      //如果圆半径小于0.5米则删除，防止出现默认为0.1米的被采集。该种情况则是用户取消圆采集
      //if (gatherCircleEntity.ellipse.semiMinorAxis.getValue() < 0.5) {
      // viewer.entities.remove(gatherCircleEntity);
      // gatherCircleEntity = null;
      // return;
      //}
      //清除圆中心点和半径点
      the.dataSource.entities.remove(centerPoint);
      centerPoint = null;
      the.closeMouseTip();
      //返回圆心和半径
      let centerPosition = the.toDegrees(gatherCircleEntity.position.getValue(Cesium.JulianDate.now()));
      let radius = gatherCircleEntity.ellipse.semiMinorAxis.getValue();
      gatherCircleEntity.centerPosition = centerPosition;
      gatherCircleEntity.radius = radius;
      //移除事件
      the.gatherHandlerDestroy();
      callback(gatherCircleEntity);
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  },

  /**
   * 采集多边形面功能。
   * 通过点击屏幕添加点，当点的数量大于等于3时，形成多边形。
   * 右键点击完成多边形的绘制，计算并返回多边形的地理坐标和高度信息。
   *
   * @param {Function} callback - 多边形绘制完成后的回调函数，接收多边形实体作为参数。
   * @param {Object} option - 绘制多边形的选项，
   *         color: '#FBFF65' - 颜色
   *         alpha: 0.5 - 透明度
   *         viewer: getCesiumEngineInstance('homeMap-fly').viewer - viewer对象
   *         elementId: 'mainMapContainer' - 容器id
   */
  polygonGather(callback, option) {
    let the = this;
    this.openMouseTip('左击地图大于等于3次后,右击完成多边形绘制', option.viewer);
    let gatherPolygonEntity = null;
    let entityPoints = [];
    let cartesianPoints = [];
    //鼠标变成加号
    document.getElementById(option.elementId).style.cursor = 'crosshair';
    this.gatherHandler = new Cesium.ScreenSpaceEventHandler(option.viewer.canvas);
    // 对鼠标按下事件的监听
    this.gatherHandler.setInputAction(function (event) {
      //获取加载地形后对应的经纬度和高程：地标坐标
      let ray = option.viewer.camera.getPickRay(event.position);
      let cartesian = option.viewer.scene.globe.pick(ray, option.viewer.scene);
      if (!Cesium.defined(cartesian)) {
        return;
      }
      let point = the.dataSource.entities.add({
        position: cartesian,
        point: {
          color: Cesium.Color.WHITE,
          pixelSize: 5,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 1
        }
      });
      entityPoints.push(point);
      cartesianPoints.push(cartesian);

      if (cartesianPoints.length >= 3) {
        if (gatherPolygonEntity == null) {
          gatherPolygonEntity = the.dataSource.entities.add({
            polygon: {
              hierarchy: new Cesium.CallbackProperty(function (time, result) {
                let hierarchyTemp = new Cesium.PolygonHierarchy(cartesianPoints, null);
                return hierarchyTemp;
              }, false),
              material: Cesium.Color.fromCssColorString(option.color).withAlpha(option.alpha)
            }
          });
        } else {
          gatherPolygonEntity.polygon.hierarchy = new Cesium.CallbackProperty(function (time, result) {
            let hierarchyTemp = new Cesium.PolygonHierarchy(cartesianPoints, null);
            return hierarchyTemp;
          }, false);
        }
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

    this.gatherHandler.setInputAction(function (rightClick) {
      let dke = gatherPolygonEntity.polygon.hierarchy.getValue().positions;
      gatherPolygonEntity.gatherPoints = [];
      for (let i = 0; i < dke.length; i++) {
        let ellipsoid = option.viewer.scene.globe.ellipsoid;
        let cartesian3 = new Cesium.Cartesian3(dke[i].x, dke[i].y, dke[i].z);
        let cartographic = ellipsoid.cartesianToCartographic(cartesian3);
        let obj = the.getPntDegrees(cartographic);
        gatherPolygonEntity.gatherPoints.push(obj);
      }
      for (let i = 0; i < entityPoints.length; i++) {
        the.dataSource.entities.remove(entityPoints[i]);
      }
      entityPoints = [];
      //鼠标变成加号
      document.getElementById(option.elementId).style.cursor = 'default';
      //移除事件
      the.gatherHandlerDestroy();
      the.closeMouseTip();
      callback(gatherPolygonEntity);
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  },

  /**
   * 打开鼠标提示
   * @param {*} label
   * @param {*} viewer
   */
  openMouseTip(label, viewer) {
    this.openMouseTipLabel = label;
    //关闭鼠标提示
    this.closeMouseTip();
    let the = this;
    this.openMouseTipHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    this.openMouseTipHandler.setInputAction(function (event) {
      var position = event.endPosition;
      var ray = viewer.camera.getPickRay(position);
      var cartesian = viewer.scene.globe.pick(ray, viewer.scene);
      if (Cesium.defined(cartesian)) {
        if (!the.openMouseTipLabelEntity) {
          the.openMouseTipLabelEntity = the.dataSource.entities.add({
            position: cartesian,
            label: {
              text: the.openMouseTipLabel,
              font: '14px 宋体',
              showBackground: true,
              pixelOffset: new Cesium.Cartesian2(20, 0),
              horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
              backgroundColor: new Cesium.Color.fromCssColorString(the.openMouseTipOption.backgroundColor).withAlpha(1),
              fillColor: new Cesium.Color.fromCssColorString(the.openMouseTipOption.color).withAlpha(1),
              disableDepthTestDistance: Number.POSITIVE_INFINITY
            }
          });
        } else {
          the.openMouseTipLabelEntity.position = cartesian;
          the.openMouseTipLabelEntity.label.text = the.openMouseTipLabel;
        }
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
  },
  /**
   * 关闭鼠标提示
   */
  closeMouseTip() {
    //移除事件
    if (this.openMouseTipHandler) {
      this.openMouseTipHandler.destroy();
      this.openMouseTipHandler = null;
    }
    //移除标注
    if (this.openMouseTipLabelEntity) {
      this.dataSource.entities.remove(this.openMouseTipLabelEntity);
      this.openMouseTipLabelEntity = null;
    }
  },
  /**
   * 更新鼠标提示内容
   * @param {*} label
   */
  updateMouseTip(label) {
    this.openMouseTipLabel = label;
    if (this.openMouseTipLabelEntity) {
      this.openMouseTipLabelEntity.label.text = this.openMouseTipLabel;
    }
  }
});
