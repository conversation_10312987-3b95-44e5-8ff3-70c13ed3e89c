{"openapi": "3.0.1", "info": {"title": "无人机平台", "description": "", "version": "1.0.0"}, "tags": [], "paths": {"/map/api/v1/layer/create": {"post": {"summary": "创建图层", "deprecated": false, "description": "创建图层", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MapLayerCreateReqVO", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpResultResponseLong"}, "example": {"code": 0, "message": "", "data": 0}}}, "headers": {}}}, "security": [{"apikey-header-X-Auth-Token": []}]}}, "/map/api/v1/layer/update": {"put": {"summary": "更新图层", "deprecated": false, "description": "更新图层", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MapLayerUpdateReqVO", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpResultResponseBoolean"}, "example": {"code": 0, "message": "", "data": false}}}, "headers": {}}}, "security": [{"apikey-header-X-Auth-Token": []}]}}, "/map/api/v1/layer/{id}": {"delete": {"summary": "删除图层", "deprecated": false, "description": "删除图层", "tags": [], "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "query", "description": "图层ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpResultResponseBoolean"}, "example": {"code": 0, "message": "", "data": false}}}, "headers": {}}}, "security": [{"apikey-header-X-Auth-Token": []}]}, "get": {"summary": "获取图层详情", "deprecated": false, "description": "获取图层详情", "tags": [], "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "query", "description": "图层ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpResultResponse", "description": "com.dji.sdk.common.HttpResultResponse"}, "example": {"code": 0, "message": "操作成功", "data": {"id": 0, "layerName": "", "layerType": "", "minLevel": 0, "maxLevel": 0, "isVisible": false, "labelField": "", "labelColor": "", "labelFont": "", "layerConfig": "", "sortOrder": 0, "remark": "", "creator": "", "createTime": "", "updater": "", "updateTime": ""}}}}, "headers": {}}}, "security": [{"apikey-header-X-Auth-Token": []}]}}, "/map/api/v1/layer/list": {"post": {"summary": "获取图层列表", "deprecated": false, "description": "获取图层列表", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MapLayerQueryReqVO", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpResultResponse", "description": "com.dji.sdk.common.HttpResultResponse"}, "example": {"code": 0, "message": "操作成功", "data": [{"id": 0, "layerName": "", "layerType": "", "minLevel": 0, "maxLevel": 0, "isVisible": false, "labelField": "", "labelColor": "", "labelFont": "", "layerConfig": "", "sortOrder": 0, "remark": "", "creator": "", "createTime": "", "updater": "", "updateTime": ""}]}}}, "headers": {}}}, "security": [{"apikey-header-X-Auth-Token": []}]}}, "/map/api/v1/feature/create": {"post": {"summary": "创建图元", "deprecated": false, "description": "创建图元", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MapFeatureCreateReqVO", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpResultResponseLong"}, "example": {"code": 0, "message": "", "data": 0}}}, "headers": {}}}, "security": [{"apikey-header-X-Auth-Token": []}]}}, "/map/api/v1/feature/update": {"put": {"summary": "更新图元", "deprecated": false, "description": "更新图元", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MapFeatureUpdateReqVO", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpResultResponseBoolean"}, "example": {"code": 0, "message": "", "data": false}}}, "headers": {}}}, "security": [{"apikey-header-X-Auth-Token": []}]}}, "/map/api/v1/feature/{id}": {"delete": {"summary": "删除图元", "deprecated": false, "description": "删除图元", "tags": [], "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "query", "description": "图元ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpResultResponseBoolean"}, "example": {"code": 0, "message": "", "data": false}}}, "headers": {}}}, "security": [{"apikey-header-X-Auth-Token": []}]}, "get": {"summary": "获取图元详情", "deprecated": false, "description": "获取图元详情", "tags": [], "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "query", "description": "图元ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpResultResponse", "description": "com.dji.sdk.common.HttpResultResponse"}, "example": {"code": 0, "message": "操作成功", "data": {"id": 0, "featureName": "", "layerId": 0, "layerName": "", "featureAddr": "", "longitude": 0, "latitude": 0, "contact": "", "telephone": "", "featureCoords": "", "extraInfo": "", "remark": "", "creator": "", "createTime": "", "updater": "", "updateTime": ""}}}}, "headers": {}}}, "security": [{"apikey-header-X-Auth-Token": []}]}}, "/map/api/v1/feature/list": {"post": {"summary": "获取图元列表", "deprecated": false, "description": "获取图元列表", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MapFeatureQueryReqVO", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpResultResponse", "description": "com.dji.sdk.common.HttpResultResponse"}, "example": {"code": 0, "message": "操作成功", "data": [{"id": 0, "featureName": "", "layerId": 0, "layerName": "", "featureAddr": "", "longitude": 0, "latitude": 0, "contact": "", "telephone": "", "featureCoords": "", "extraInfo": "", "remark": "", "creator": "", "createTime": "", "updater": "", "updateTime": ""}]}}}, "headers": {}}}, "security": [{"apikey-header-X-Auth-Token": []}]}}, "/map/api/v1/feature/layer/{layerId}": {"get": {"summary": "根据图层ID获取图元列表", "deprecated": false, "description": "根据图层ID获取图元列表", "tags": [], "parameters": [{"name": "layerId", "in": "path", "description": "", "required": true, "schema": {"type": "string"}}, {"name": "layerId", "in": "query", "description": "图层ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpResultResponse", "description": "com.dji.sdk.common.HttpResultResponse"}, "example": {"code": 0, "message": "操作成功", "data": [{"id": 0, "featureName": "", "layerId": 0, "layerName": "", "featureAddr": "", "longitude": 0, "latitude": 0, "contact": "", "telephone": "", "featureCoords": "", "extraInfo": "", "remark": "", "creator": "", "createTime": "", "updater": "", "updateTime": ""}]}}}, "headers": {}}}, "security": [{"apikey-header-X-Auth-Token": []}]}}}, "components": {"schemas": {"HttpResultResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "0 means success, non-zero means error.", "example": 0}, "message": {"type": "string", "description": "The response message.", "example": "操作成功"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Object", "description": "java.lang.Object"}, "description": "The response data."}}}, "HttpResultResponseBoolean": {"type": "object", "properties": {"code": {"type": "integer", "description": "0 means success, non-zero means error.", "example": 0}, "message": {"type": "string", "description": "The response message.", "example": "操作成功"}, "data": {"type": "boolean", "description": "The response data."}}}, "HttpResultResponseLong": {"type": "object", "properties": {"code": {"type": "integer", "description": "0 means success, non-zero means error.", "example": 0}, "message": {"type": "string", "description": "The response message.", "example": "操作成功"}, "data": {"type": "integer", "description": "The response data."}}}, "MapLayerCreateReqVO": {"type": "object", "properties": {"layerName": {"type": "string", "description": "图层名称"}, "layerType": {"type": "string", "description": "图层类型：点(point)、线(line)、面(polygon)、路网(roadnetwork)、栅格(tile)等"}, "minLevel": {"type": "integer", "description": "最小显示级别"}, "maxLevel": {"type": "integer", "description": "最大显示级别"}, "isVisible": {"type": "boolean", "description": "是否显示"}, "labelField": {"type": "string", "description": "标签字段"}, "labelColor": {"type": "string", "description": "标签颜色"}, "labelFont": {"type": "string", "description": "标签字体"}, "layerConfig": {"type": "string", "description": "图层配置JSON"}, "sortOrder": {"type": "integer", "description": "排序字段，数值越小越靠前"}, "remark": {"type": "string", "description": "备注"}}, "required": ["layerName", "layerType", "minLevel", "maxLevel"]}, "MapLayerUpdateReqVO": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键ID"}, "layerName": {"type": "string", "description": "图层名称"}, "layerType": {"type": "string", "description": "图层类型：点(point)、线(line)、面(polygon)、路网(roadnetwork)、栅格(tile)等"}, "minLevel": {"type": "integer", "description": "最小显示级别"}, "maxLevel": {"type": "integer", "description": "最大显示级别"}, "isVisible": {"type": "boolean", "description": "是否显示"}, "labelField": {"type": "string", "description": "标签字段"}, "labelColor": {"type": "string", "description": "标签颜色"}, "labelFont": {"type": "string", "description": "标签字体"}, "layerConfig": {"type": "string", "description": "图层配置JSON"}, "sortOrder": {"type": "integer", "description": "排序字段，数值越小越靠前"}, "remark": {"type": "string", "description": "备注"}}, "required": ["id"]}, "MapLayerQueryReqVO": {"type": "object", "properties": {"layerName": {"type": "string", "description": "图层名称（模糊查询）"}, "layerType": {"type": "string", "description": "图层类型"}, "isVisible": {"type": "boolean", "description": "是否显示"}, "pageNum": {"type": "integer", "description": "页码"}, "pageSize": {"type": "integer", "description": "页大小"}}}, "MapFeatureCreateReqVO": {"type": "object", "properties": {"featureName": {"type": "string", "description": "图元名称"}, "layerId": {"type": "integer", "description": "所属图层ID"}, "featureAddr": {"type": "string", "description": "图元地址"}, "longitude": {"type": "number", "description": "图元中心点经度"}, "latitude": {"type": "number", "description": "图元中心点纬度"}, "contact": {"type": "string", "description": "联系人"}, "telephone": {"type": "string", "description": "联系电话"}, "featureCoords": {"type": "string", "description": "线面的坐标集合，二维数组JSON格式"}, "extraInfo": {"type": "string", "description": "图元扩展属性"}, "remark": {"type": "string", "description": "备注"}}, "required": ["featureName", "layerId", "longitude", "latitude"]}, "Object": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键ID"}, "featureName": {"type": "string", "description": "图元名称"}, "layerId": {"type": "integer", "description": "所属图层ID"}, "layerName": {"type": "string", "description": "所属图层名称"}, "featureAddr": {"type": "string", "description": "图元地址"}, "longitude": {"type": "number", "description": "图元中心点经度"}, "latitude": {"type": "number", "description": "图元中心点纬度"}, "contact": {"type": "string", "description": "联系人"}, "telephone": {"type": "string", "description": "联系电话"}, "featureCoords": {"type": "string", "description": "线面的坐标集合，二维数组JSON格式"}, "extraInfo": {"type": "string", "description": "图元扩展属性"}, "remark": {"type": "string", "description": "备注"}, "creator": {"type": "string", "description": "创建者"}, "createTime": {"type": "string", "description": "创建时间"}, "updater": {"type": "string", "description": "更新者"}, "updateTime": {"type": "string", "description": "更新时间"}}}, "MapFeatureUpdateReqVO": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键ID"}, "featureName": {"type": "string", "description": "图元名称"}, "layerId": {"type": "integer", "description": "所属图层ID"}, "featureAddr": {"type": "string", "description": "图元地址"}, "longitude": {"type": "number", "description": "图元中心点经度"}, "latitude": {"type": "number", "description": "图元中心点纬度"}, "contact": {"type": "string", "description": "联系人"}, "telephone": {"type": "string", "description": "联系电话"}, "featureCoords": {"type": "string", "description": "线面的坐标集合，二维数组JSON格式"}, "extraInfo": {"type": "string", "description": "图元扩展属性"}, "remark": {"type": "string", "description": "备注"}}, "required": ["id"]}, "MapFeatureQueryReqVO": {"type": "object", "properties": {"featureName": {"type": "string", "description": "图元名称（模糊查询）"}, "layerId": {"type": "integer", "description": "所属图层ID"}, "featureAddr": {"type": "string", "description": "图元地址（模糊查询）"}, "contact": {"type": "string", "description": "联系人（模糊查询）"}, "pageNum": {"type": "integer", "description": "页码"}, "pageSize": {"type": "integer", "description": "页大小"}}}}, "securitySchemes": {"apikey-header-X-Auth-Token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-Auth-Token"}}}, "servers": [], "security": [{"apikey-header-X-Auth-Token": []}]}