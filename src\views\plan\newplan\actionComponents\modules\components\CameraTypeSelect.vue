<template>
  <!-- 相机选择组件 -->
  <div class="camera-select-wrapper">
    <div v-if="cameraDataRect.isCameraConfiged" class="camera-select">
      <div class="left">
        <!-- 相机类型选择按钮 -->
        <el-button
          v-for="item in cameraDataRect.cameraData"
          :key="item.id"
          type="primary"
          class="left-item"
          :class="{
            active: item.active && !isFollowWayline,
            active2: item.active && isFollowWayline
          }"
          :disabled="item.disabled"
          @click="onClickHandle(item)"
        >
          {{ item.label }}
        </el-button>
      </div>
      <div class="right">
        <!-- 跟随航线按钮 -->
        <el-button type="primary" class="right-item" :class="{ active: isFollowWayline }" @click="toggleFollowWayline">
          跟随航线
        </el-button>
      </div>
    </div>
    <div v-else class="camera-select">
      <!-- 未配置相机数据时显示的提示 -->
      <div class="camera-none">未配置相机数据请核实</div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { useDeviceStore } from '@/store/modules/device.js';
import { cloneDeep } from 'lodash';

const deviceStore = useDeviceStore();

// 组件属性定义
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
});

// 组件事件定义
const emit = defineEmits(['update:modelValue', 'cameraTypeChange']);

// 双向绑定的数据
const data = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value)
});

// 相机数据响应式对象
const cameraDataRect = reactive({
  cameraData: [], // 相机数据列表
  isFollowWayline: true, // 是否跟随航线
  isCameraConfiged: true // 是否已配置相机数据
});

// 计算属性：是否跟随航线
const isFollowWayline = computed(() => cameraDataRect.isFollowWayline);

/**
 * 获取选中的相机类型列表
 * @param {Object} obj - 当前点击的相机对象
 * @param {Array} options - 所有相机选项
 * @returns {Array} 选中的相机类型值列表
 */
const getCheckActiveList = (obj, options) => {
  return options
    .filter(item => {
      if (item.value === obj.value) {
        item.active = !obj.active;
      }
      return item.active;
    })
    .map(item => item.value);
};

/**
 * 处理相机类型点击事件
 * @param {Object} item - 被点击的相机对象
 */
const onClickHandle = item => {
  if (cameraDataRect.isFollowWayline) return;

  const list = getCheckActiveList(item, cloneDeep(cameraDataRect.cameraData));
  if (list.length < 1) return;

  cameraDataRect.cameraData.forEach(t => {
    if (t.value === item.value) {
      t.active = !item.active;
    }
  });

  data.value.wpml_payloadLensIndex = cameraDataRect.cameraData
    .filter(f => f.active)
    .map(f => f.value)
    .join(',');

  emit('cameraTypeChange', data.value);
};

/**
 * 切换是否跟随航线
 */
const toggleFollowWayline = () => {
  cameraDataRect.isFollowWayline ? unSelecteFollowWayline() : selectedFollowWayline();
  emit('cameraTypeChange', data.value);
};

/**
 * 取消选择跟随航线
 */
const unSelecteFollowWayline = () => {
  data.value.wpml_useGlobalPayloadLensIndex = 0;
  cameraDataRect.isFollowWayline = false;
  cameraDataRect.cameraData.forEach(matchedData => {
    matchedData.active = deviceStore.cameraSelectedwithWayLine.includes(matchedData.value);
    matchedData.disabled = false;
  });
  emit('cameraTypeChange', data.value);
};

/**
 * 选择跟随航线
 */
const selectedFollowWayline = () => {
  data.value.wpml_useGlobalPayloadLensIndex = 1;
  cameraDataRect.isFollowWayline = true;
  data.value.wpml_payloadLensIndex = cameraDataRect.cameraData.map(data => data.value).join(',');
  cameraDataRect.cameraData.forEach(matchedData => {
    matchedData.active = deviceStore.cameraSelectedwithWayLine.includes(matchedData.value);
    matchedData.disabled = true;
  });
  emit('cameraTypeChange', data.value);
};

/**
 * 解析wpml_payloadLensIndex字符串
 * @param {string} value - wpml_payloadLensIndex字符串
 * @returns {Object|null} 解析后的对象
 */
const parseWpmlPayloadLensIndex = value => {
  if (!value) return null;
  const lensTypes = value.split(',').map(type => type.trim());
  return lensTypes.length ? Object.fromEntries(lensTypes.map(l => [l, l])) : null;
};

/**
 * 渲染相机数据
 */
const renderData = () => {
  if (data.value.wpml_useGlobalPayloadLensIndex === 1) {
    cameraDataRect.isFollowWayline = true;
    cameraDataRect.cameraData.forEach(matchedData => {
      matchedData.active = deviceStore.cameraSelectedwithWayLine.includes(matchedData.value);
      matchedData.disabled = true;
    });
  } else if (data.value.wpml_useGlobalPayloadLensIndex === 0) {
    cameraDataRect.isFollowWayline = false;
    const payloadLensObj = parseWpmlPayloadLensIndex(data.value.wpml_payloadLensIndex);
    if (!payloadLensObj) return;

    cameraDataRect.cameraData.forEach(matchedData => {
      matchedData.active = payloadLensObj.hasOwnProperty(matchedData.value);
      matchedData.disabled = false;
    });
  }
};

/**
 * 初始化相机数据
 */
const init = () => {
  cameraDataRect.isFollowWayline = true;
  const adp = deviceStore.deviceAdapter;
  if (!adp) {
    cameraDataRect.isCameraConfiged = false;
    return;
  }
  const cmosList = adp?.getCmosValue() ?? [];
  if (!cmosList.length) {
    cameraDataRect.isCameraConfiged = false;
    return;
  }
  cameraDataRect.isCameraConfiged = true;
  cameraDataRect.cameraData = cmosList.map((cameraInfo, i) => ({
    id: i,
    value: cameraInfo.cameraType,
    label: cameraInfo.cameraLabel,
    disabled: cameraDataRect.isFollowWayline,
    active: !cameraDataRect.isFollowWayline
  }));
  renderData();
};

/**
 * 重置相机选择状态
 */
const restCamera = () => {
  const cameraSelectedwithWayLine = deviceStore.getWayLineCameraType() ?? [];
  if (!cameraSelectedwithWayLine.length) return;
  cameraDataRect.cameraData.forEach(matchedData => {
    matchedData.active = cameraSelectedwithWayLine.includes(matchedData.value);
  });
};

// 监听数据变化
watch(() => data.value, renderData, { deep: true });
watch(() => deviceStore.deviceAdapter, init);
watch(
  () => deviceStore.cameraSelectedwithWayLine,
  () => {
    if (isFollowWayline.value) {
      restCamera();
    }
  }
);

// 组件挂载时初始化
onMounted(init);
</script>

<style lang="scss" scoped>
::v-deep .el-button.is-disabled {
  // background-color: #2d8cf082 !important;
  color: #bdbdbd;
  cursor: not-allowed !important;
  border-color: #737577;
}
.camera-select-wrapper {
  width: 100%;
  color: white;
  user-select: none;
  .camera-select {
    display: flex;
    justify-content: space-between;
    .camera-none {
      display: flex;
      justify-content: center;
      width: 100%;
      background-color: #404040 !important;
      padding: 3px 10px;
      border-radius: 5px;
      color: white;
      cursor: not-allowed;
    }
    .left {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      flex-wrap: wrap;
      .left-item {
        width: auto;
        height: 30px;
        padding: 3px 6px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        background-color: #314257;
        color: #fff;
        margin: 5px 5px;
        user-select: none;
        border-color: #314257;
      }
      .left-item:first-child {
        margin-left: 0px !important;
      }
      .left-item:last-child {
      }
    }
    .right {
      display: flex;
      justify-content: center;
      align-items: flex-start;
      .right-item {
        width: 80px;
        height: 30px;
        padding: 2px 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        background-color: #314257;
        border-color: #314257;
        color: #ffffffb5;
        margin: 5px 2px;
        user-select: none;
        &:hover {
          cursor: pointer;
        }
      }
    }
  }
}
.active {
  color: #fff !important;
  background: #2e90fa !important;
}
.active2 {
  color: #fff !important;
  background: #2e91fa7a !important;
}
.disableActive {
  color: #fff !important;
  background: #2e91fa7a !important;
}
</style>
