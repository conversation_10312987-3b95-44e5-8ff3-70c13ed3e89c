<script setup>
import { useSettingsStore } from '@/store/modules/settings';
import { useTagsViewStore } from '@/store/modules/tagsView.js';
import { computed } from 'vue';
import { RouterView } from 'vue-router';
const tagsViewStore = useTagsViewStore();
const settingStore = useSettingsStore();

const appStyle = computed(() =>
  settingStore.tagsView ? 'calc(100% - 35px)' : 'height: 100vh'
);

const isDevMode = computed(() => {
  return import.meta.env.VITE_APP_NODE_ENV === 'development';
});
</script>

<script>
export default {
  name: 'AppMain'
};
</script>

<template>
  <section class="app-main" :style="appStyle">
    <RouterView v-slot="{ Component, route }">
      <transition name="router-fade" :mode="isDevMode ? null : 'out-in'">
        <keep-alive :include="tagsViewStore.cachedViews">
          <component :is="Component" :key="route.name" />
        </keep-alive>
      </transition>
    </RouterView>
  </section>
</template>

<style lang="scss" scoped>
.app-main {
  /* 60= navbar  60  */
  /* height: 100vh; */
  width: 100%;
  position: relative;
  overflow: auto;
  background-color: var(--el-bg-color-page);
  // padding: 20px;
  overflow-y: auto;
}
.app-main::-webkit-scrollbar{
  width: 0 !important;
}

.fixed-header + .app-main {
  padding-top: 60px;
}

.hasTagsView {
  .app-main {
    /* 94 = navbar + tags-view = 60 + 34 */
    height: calc(100vh - 97px);
  }

  .fixed-header + .app-main {
    padding-top: 94px;
    min-height: 100vh;
  }
}
</style>
