<script>
export default { name: 'AirportAndDrone' };
</script>

<script setup>
import { ref, reactive, toRaw } from 'vue';
import { getDevicesBound } from '@/api/devices';
import { DOMAIN } from '@/utils/constants';
import { useDeviceStateStore } from '@/store/modules/deviceState.js';
import { useConnectWebSocket } from '@/hooks/useConnectWebSocket';
import { EBizCode } from '@/utils/constants';
import router from '@/router';

const deviceStateStore = useDeviceStateStore();
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10
});
const dataList = ref([]);
const total = ref(0);
const loading = ref(true);
const deviceSn = ref('');
const emit = defineEmits(['onClick', 'select']);
const isDisabled = ref(false);
const isRefresh = ref(true);

watch(
  () => deviceStateStore.deviceState.dock,
  async val => {
    if (val) {
      // dataList.value = deviceStateStore.getDockBySn(nowOsdVisible.dock_sn);
    }
  },
  { deep: true }
);

/**
 * 查询
 */
function handleQuery() {
  loading.value = true;
  getDevicesBound({
    airportOrDrone: true,
    pageNum: queryParams.pageNum,
    pageSize: queryParams.pageSize
  })
    .then(data => {
      const { list, pagination } = data;
      if (list && list.length > 0) {
        if (isRefresh.value) {
          dataList.value = [...list];
          isRefresh.value = false;
        } else {
          // 去重追加数据
          const newList = list.filter(newItem => {
            // 使用device_sn作为唯一标识符进行去重
            return !dataList.value.some(existingItem => existingItem.device_sn === newItem.device_sn);
          });
          dataList.value = [...dataList.value, ...newList];
        }
      } else {
        // 没有更多数据时禁用无限滚动
        isDisabled.value = true;
      }
      if (dataList.value.length === pagination.total) {
        isDisabled.value = true;
      }
      total.value = pagination.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

/*
 * 清空数据
 */
function clearData() {
  dataList.value = [];
  queryParams.pageNum = 1;
  queryParams.pageSize = 10;
  total.value = 0;
  loading.value = true;
  isRefresh.value = true;
}

/*
 * 加载更多
 */

function loadMore() {
  if (loading.value || isDisabled.value) {
    return;
  }
  queryParams.pageNum += 1;
  isRefresh.value = false;
  handleQuery();
}

useConnectWebSocket(payload => {
  if (!payload) {
    return;
  }
  switch (payload.biz_code) {
    // 设备上下线
    case EBizCode.DeviceOnline: {
      // 上线
      clearData();
      nextTick(() => {
        handleQuery(); // 查询新数据
      });
      break;
    }
    case EBizCode.DeviceOffline: {
      // 下线
      clearData();
      nextTick(() => {
        handleQuery(); // 查询新数据
      });
      break;
    }
  }
});

onMounted(() => {
  handleQuery();
});

function handleClick(type, item) {
  emit('onClick', type, item);
}

function locationAirport(item) {
  deviceSn.value = toRaw(item).device_sn;
  emit('select', item);
}

function goToDetail(item) {
  if (item.children) {
    router.push({
      path: '/real-time-flight',
      query: {
        dock_sn: item.device_sn, //机场序列号
        nickname: item.nickname, //机场名
        device_sn: item.child_device_sn, //飞机序列号
        device_nickname: item.children.nickname, //无人机名
        lon: item.longitude, // 经纬度
        lat: item.latitude, // 经纬度
        altitude: item.altitude, // 高度
        camera_selected: '',
        type: item.type
      }
    });
  } else {
    router.push({
      path: '/pilot-real-time-flight',
      query: {
        device_sn: item.device_sn, //飞机序列号
        device_nickname: item.nickname, //无人机名
        is_carrier: item.is_carrier
      }
    });
  }
}
</script>

<template>
  <div v-loading="loading" class="loading-container">
    <div
      class="alarm-ul"
      v-if="dataList.length > 0"
      v-infinite-scroll="loadMore"
      :infinite-scroll-disabled="isDisabled"
      :infinite-scroll-distance="10"
    >
      <div class="alarm-item" v-for="(item, index) in dataList" :key="index">
        <div class="flex" @click="locationAirport(item)">
          <div class="ellipsis" style="max-width: 210px" :title="item.nickname">
            <svg-icon
              v-if="!item.is_carrier"
              :icon-class="item?.children ? 'airfield_home' : 'drone'"
              style="margin-right: 4; width: 14px; height: 14px"
            />
            <svg-icon v-else icon-class="carrier_nav" style="margin-right: 0; width: 20px; height: 20px;margin-top: 2px;" />
            {{ item.nickname }}
          </div>
          <div>
            <span class="device-name">{{ item?.device_name }}</span>
            <span :class="item?.status ? 'green' : 'grey'">{{ item?.status ? '在线' : '离线' }}</span>
          </div>
        </div>
        <div class="flex" @click="locationAirport(item?.children)" v-if="item.children">
          <div class="ellipsis" style="max-width: 205px" :title="item?.children?.nickname">
            <svg-icon icon-class="drone" style="margin-right: 4" />{{ item?.children?.nickname || '' }}
          </div>
          <div>
            <span class="device-name">{{ item.children?.device_name }}</span>
            <span :class="item.children?.status ? 'green' : 'grey'">{{ item.children?.status ? '在线' : '离线' }}</span>
          </div>
        </div>
        <div class="flex" v-if="item.children">
          <div>
            <el-button type="primary" size="small" class="mr8" @click="goToDetail(item)" :disabled="!item?.status"
              >详情</el-button
            >
            <span class="dept-name"></span>
          </div>
          <div>
            <svg-icon
              class="pointer"
              v-if="!item?.external_video_id"
              icon-class="out_camera"
              style="margin-left: 8"
              @click.stop="handleClick('out', item)"
            />
            <svg-icon
              class="pointer"
              v-if="item?.external_video_id"
              icon-class="out_camera_active"
              style="margin-left: 8"
              @click.stop="handleClick('out', item)"
            />
            <svg-icon
              class="pointer"
              v-if="!item?.status"
              icon-class="airport_camera"
              style="margin-left: 8"
              @click.stop="handleClick('airport', item)"
            />
            <svg-icon
              class="pointer"
              v-if="item?.status"
              icon-class="airport_camera_active"
              style="margin-left: 8"
              @click.stop="handleClick('airport', item)"
            />
            <svg-icon
              class="pointer"
              v-if="item.children?.status && item.children?.index"
              icon-class="drone_camera"
              style="margin-left: 8"
              @click.stop="handleClick('drone', item.children)"
            />
            <svg-icon
              class="pointer"
              v-if="!item.children?.status || (item.children?.status && !item.children?.index)"
              icon-class="drone_camera_off"
              style="margin-left: 8"
              @click.stop="handleClick('drone', item.children)"
            />
          </div>
        </div>
        <div class="flex" v-if="!item.children">
          <div>
            <el-button type="primary" size="small" class="mr8" @click="goToDetail(item)" :disabled="!item?.status"
              >详情</el-button
            >
            <span class="dept-name"></span>
          </div>
          <div>
            <svg-icon
              class="pointer"
              v-if="item?.status && item?.index"
              icon-class="drone_camera"
              style="margin-left: 8"
              @click.stop="handleClick('pilotDrone', item)"
            />
            <svg-icon
              class="pointer"
              v-if="!item?.status || (item?.status && !item?.index)"
              icon-class="drone_camera_off"
              style="margin-left: 8"
              @click.stop="handleClick('pilotDrone', item)"
            />
          </div>
        </div>
      </div>
    </div>
    <el-empty v-else-if="!loading & (dataList.length == 0)" description="暂无数据">
      <template #image>
        <img src="../../../assets/empty_home.png" />
      </template>
    </el-empty>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-tabs__header) {
  margin-bottom: 0;
}
.loading-container {
  height: 100vh; /* 确保容器高度占满整个视口 */
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.dept-name {
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #98a2b3;
  line-height: 20px;
  font-weight: 400;
}
.device-name {
  max-width: 10px;
  margin-right: 8px;
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #98a2b3;
  text-align: right;
  line-height: 20px;
  font-weight: 400;
}
.right-icon {
  margin-top: 5px;
  display: flex;
}
.green {
  display: inline-block;
  background: rgba(42, 139, 125, 0.3);
  color: #39bfa4;
  font-weight: 400;
  font-family: SourceHanSansSC-Regular;
  font-size: 12px;
  line-height: 24px;
  height: 24px;
  text-align: center;
  padding: 0 4px;
}
.grey {
  display: inline-block;
  background: rgba($color: #98a2b3, $alpha: 0.2);
  border-radius: 2px;
  text-align: center;
  line-height: 24px;
  height: 24px;
  font-weight: 400;
  padding: 0 4px;
  font-family: SourceHanSansSC-Regular;
  font-size: 12px;
  color: #98a2b3;
  text-align: center;
}

.pointer {
  cursor: pointer;
}
.currentColor {
  background: #175091 !important;
}
:deep(.el-tabs__header) {
  border-bottom: 1px solid #344054;
}
:deep(.el-tabs__nav-wrap) {
  background: #11253e;
  color: #fff;
  height: 38px;
  line-height: 38px;
  font-family: SourceHanSansSC-Bold;
  font-size: 14px;
  text-align: left;
  font-weight: 700;
  border-bottom: 1px solid #344054;
  padding-left: 8px;
}
::-webkit-scrollbar {
  width: 8px; /* 设置滚动条的宽度 */
}
::-webkit-scrollbar-thumb {
  background-color: rgba(46, 144, 255, 0.5);
  border-radius: 2px; /* 设置滚动条滑块的背景色 */
}
.alarm-title {
  height: 38px;
  line-height: 38px;
  background: #11253e;
  color: #fff;
  font-family: SourceHanSansSC-Bold;
  font-size: 14px;
  text-align: left;
  font-weight: 700;
  border-bottom: 1px solid #344054;
  padding-left: 8px;
}
.alarm-ul {
  background: #001129;
  height: calc(100vh - 180px);
  overflow: auto;
  margin: 8px;
  // height: 99%;
  .alarm-item {
    min-height: 76px;
    background: #11253e;
    margin-bottom: 12px;
    padding: 8px;
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;
    color: #f5f6f8;
    text-align: left;
    line-height: 22px;
    font-weight: 400;
    .list {
      height: 38px;
      line-height: 38px;
      vertical-align: middle;
    }
    .alarm-time {
      font-family: SourceHanSansSC-Regular;
      font-size: 12px;
      color: #98a2b3;
      text-align: right;
      line-height: 20px;
      font-weight: 400;
    }
    .alarm-address {
      font-family: SourceHanSansSC-Regular;
      font-size: 14px;
      color: #ffffff;
      text-align: justify;
      line-height: 22px;
      font-weight: 400;
      margin-top: 8px;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      text-overflow: ellipsis;
      white-space: normal;
    }
  }
}
.flex {
  height: 38px;
  line-height: 38px;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
}
</style>
