import * as Cesium from 'cesium';
import axios from 'axios';
import { readRemoteJsonFile } from '@/utils/configHelper';
import { getDeptSysSetting } from '@/api/wayline';
let globalConfigResource = {};
// 全局对象用于存储全局资源
const globalResource = {};
await readRemoteJsonFile('').then(async res => {
  globalConfigResource = res;
  // 获取地图基本信息
  await getDeptSysSetting({}).then(res => {
    const { base_config = {}, position_config = {} } = res; 
    globalConfigResource['tdtImgLayer'] = position_config.tdt_url + "img_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default.jpg&tk=" + position_config.tdt_token;
    globalConfigResource['tdtVecLayer'] = position_config.tdt_url + "vec_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=vec&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default.jpg&tk=" + position_config.tdt_token;
    globalConfigResource['tdtCiaLayer'] = position_config.tdt_url + "cia_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default.jpg&tk=" + position_config.tdt_token;
    globalConfigResource['tdtCvaLayer'] = position_config.tdt_url + "cva_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cva&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default.jpg&tk=" + position_config.tdt_token;
    globalConfigResource['terrainOnline'] = position_config.terrain_online;
    globalConfigResource['terrainMapUrl'] = position_config.terrain_map_url;
    // globalConfigResource['init']['lat'] = Number(position_config.latitude);
    // globalConfigResource['init']['lon'] = Number(position_config.longitude);
    globalConfigResource['showAI'] = base_config.show_ai;
  });
});
// 天地图影像
const imglayer = new Cesium.WebMapTileServiceImageryProvider({
  url: globalConfigResource['tdtImgLayer'],
  layer: 'tdtImgLayer',
  style: 'default', //WMTS请求的样式名称
  format: 'tiles', //MIME类型，用于从服务器检索图像
  tileMatrixSetID: 'GoogleMapsCompatible', //	用于WMTS请求的TileMatrixSet的标识符
  subdomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'], //天地图8个服务器
  minimumLevel: 0, //最小层级
  maximumLevel: 18 //最大层级
});

// 天地图矢量
const veclayer = new Cesium.WebMapTileServiceImageryProvider({
  url: globalConfigResource['tdtVecLayer'],
  layer: 'tdtVecLayer',
  style: 'default', //WMTS请求的样式名称
  format: 'tiles', //MIME类型，用于从服务器检索图像
  tileMatrixSetID: 'GoogleMapsCompatible', //	用于WMTS请求的TileMatrixSet的标识符
  subdomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'], //天地图8个服务器
  minimumLevel: 0, //最小层级
  maximumLevel: 18 //最大层级
});

// 天地图影像注记
const cialayer = new Cesium.WebMapTileServiceImageryProvider({
  url: globalConfigResource['tdtCiaLayer'],
  layer: 'tdtCiaLayer',
  style: 'default', //WMTS请求的样式名称
  format: 'tiles', //MIME类型，用于从服务器检索图像
  subdomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'], //天地图8个服务器
  tileMatrixSetID: 'GoogleMapsCompatible'
});

// 天地图矢量注记
const cvalayer = new Cesium.WebMapTileServiceImageryProvider({
  url: globalConfigResource['tdtCvaLayer'],
  layer: 'tdtCvaLayer',
  style: 'default', //WMTS请求的样式名称
  format: 'tiles', //MIME类型，用于从服务器检索图像
  subdomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'], //天地图8个服务器
  tileMatrixSetID: 'GoogleMapsCompatible'
});

// 是否展示默认地形
const projectTerrainOnline = globalConfigResource['terrainOnline'];

// 项目地形
let projectCustomTerrainProvider;
if (!projectTerrainOnline) {
  projectCustomTerrainProvider = new Cesium.CesiumTerrainProvider({
    url: globalConfigResource['terrainMapUrl']
  });
} else {
  // 如果 terrainOnline 为 false，可以设置为 null
  projectCustomTerrainProvider = null;
}

// 大疆禁飞区
async function getNoFlyZoneData() {
  try {
    const response = await axios.get('/resource/layers/noFlyZone.json');
    return response.data.data.areas;
  } catch (error) {
    console.error(error);
  }
}
globalResource['imglayer'] = imglayer;
globalResource['veclayer'] = veclayer;
globalResource['cialayer'] = cialayer;
globalResource['cvalayer'] = cvalayer;
export {
  imglayer,
  veclayer,
  cialayer,
  cvalayer,
  projectCustomTerrainProvider,
  projectTerrainOnline,
  getNoFlyZoneData,
  globalResource,
  globalConfigResource
};
