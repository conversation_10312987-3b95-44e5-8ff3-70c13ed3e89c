<template>
  <el-dialog
    :title="title"
    :model-value="visible"
    v-if="visible"
    width="600px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <div class="upload-container">
      <el-upload
        ref="uploadImgRef"
        :on-change="handleChange"
        :on-error="handleError"
        :on-progress="handleProgress"
        :on-remove="handRemove"
        v-model:file-list="fileList"
        :on-success="handleSuccess"
        :auto-upload="false"
        action="#"
        :headers="headers"
        drag
        multiple
      >
        <template #default>
          <div class="upload-tip-box">
            <i-ep-uploadFilled class="el-icon-upload" />
            <div class="el-upload__text">点击或拖动文件上传</div>
          </div>
          <div class="tipB">支持图片格式：JPEG</div>
          <div class="tipB">支持视频格式：MP4</div>
        </template>
        <template #file> </template>
      </el-upload>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="btDis" v-loading="loading">上 传</el-button>
        <el-button @click="closeDialog">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useUserStoreHook } from '@/store/modules/user';
import { delMediaUploadFiles } from '@/api/live';
import axios, { CancelToken } from 'axios';
import _ from 'lodash';
const uploadImgRef = ref('');

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  }
});

const emit = defineEmits(['update:visible', 'submit']);
const loading = ref(false);
// 创建取消令牌
const cancelTokenSource = CancelToken.source();
// 关闭弹窗
function closeDialog() {
  fileList.value = [];
  uploadImgRef.value.abort();
  uploadImgRef.value.clearFiles();
  cancelTokenSource.cancel();
  console.log('关闭弹框的文件列表：', fileList.value);
  emit('update:visible', false);
  emit('submit');
}

//确定按钮
const btDis = async () => {
  try {
    loading.value = true;
    const res = await handleSubmit();
    // 所有文件上传完成
    loading.value = false;
  } catch (error) {
    loading.value = false;
  }
};
const handleProgress = (e, file, v) => {
  console.log('进度条处理');
};
//手动上传
const handleSubmit = async () => {
  if (!fileList.value.length) {
    ElMessage.warning('请先上传文件！');
    return;
  }
  if(fileList.value.length > 10) {
    ElMessage.warning('一次最多上传10个文件');
    return;
  }
  console.log('手动上传');
  console.log('🚀 ~ handleChange ~ fileList:', fileList.value);
  const tempA = fileList.value.filter(arr => arr.status === 'success');
  if (tempA.length === fileList.value.length) {
    ElMessage.warning(`文件已全部上传!`);
    return;
  }
  let flagPro = false;

  for (let i = 0; i < fileList.value.length; i++) {
    const file = fileList.value[i];
    if (file.status === 'success') {
      console.log('跳过当前循环');
      continue;
      console.log('ZZZ');
    }
    console.log('xxws');
    file.status = 'uploading';
    console.log('🚀 ~ uploadFile ~ file:', file);
    // 使用FormData上传
    const formData = new FormData();
    formData.append('file', file.raw, file.name);
    try {
      const response = await axios.post(
        import.meta.env.VITE_APP_BASE_API + `/media/api/v1/files/${userStore.userData.workspace_id}/uploadFile`,
        formData,
        {
          ...config,
          onUploadProgress: progressEvent => {
            // 计算进度百分比
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            console.log('🚀 ~ handleSubmit ~ percentCompleted:', percentCompleted);
            if (percentCompleted === 100 && !flagPro) {
              console.log('这是前端的100进度条改99等后端返回0才100');
              file.percentage = 99;
            } else if (flagPro) {
              file.percentage = 100;
            } else {
              file.percentage = percentCompleted; //更新实时进度条
            }
          },
          cancelToken: cancelTokenSource.token // 将取消令牌传递给请求配置
        }
      );

      if (response.data.code === 0) {
        flagPro = true;
        console.log('文件上传成功', response.data);
        file.file_id = response.data.data.file_id;
        file.uploadProgress = 100; //进度条拉满
        file.status = 'success';
      } else {
        ElMessage.error(response.data.msg)
        flagPro = false;
        file.status = 'error';
      }
    } catch (error) {
      file.status = 'error';
      // ElMessage.warning(`网络异常，文件${file.name}上传失败！`);
      console.error('文件上传失败', error);
    }
  }

  // uploadImgRef.value.submit(); //调用组件的上传，这样不能接收返回值，用手动去请求
};
//下面是上传相关
const fileList = ref([]);
const userStore = useUserStoreHook();
const config = {
  headers: {
    'X-Auth-Token': userStore.token
  }
};
const addApi = import.meta.env.VITE_APP_BASE_API + `/media/api/v1/files/${userStore.userData.workspace_id}/uploadFile`;
const headers = reactive({ 'X-Auth-Token': userStore.token });
// console.log('获取到的token', headers);

const fileSize = 50;
const mp4FileSize = 500;
const fileType = ['MP4', 'JPEG'];
const handleChange = (file, updatedFileList) => {
  console.log('=====================触发了handleChange的生命周期开始！！！');
  console.log('🚀 ~ handleChange ~ updatedFileList:', updatedFileList);
  console.log('🚀 ~ handleChange ~ file:', file);
  // 校检文件类型
  let fileExtension = '';
  if (file.name.lastIndexOf('.') > -1) {
    fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1);
  }
  const isTypeOk = fileType.some(type => {
    if (fileExtension && fileExtension.toUpperCase().indexOf(type.toUpperCase()) > -1) return true;
    return false;
  });
  if (!isTypeOk) {
    ElMessage.warning(`文件格式不正确, 请上传${fileType.join('/')}格式文件!`);
    uploadImgRef.value.handleRemove(file);
    return false;
  }
  //文件名30
  const nameA = file.name.split('.').slice(0, -1).join('.');
  if (nameA.length > 30) {
    ElMessage.warning(`文件名过长，请限制30字内!`);
    uploadImgRef.value.handleRemove(file);
    return false;
  }
  const isLt = file.size / 1024 / 1024  < fileSize;
  const mp4IsLt = file.size / 1024 / 1024  < mp4FileSize;
  if (!isLt && fileExtension == 'jpeg') {
    ElMessage.warning(`单个图片不超过 ${fileSize} MB!`);
    uploadImgRef.value.handleRemove(file);
    return false;
  }
  if (!mp4IsLt && fileExtension == 'mp4') {
    ElMessage.warning(`单个视频不超过 ${mp4FileSize} MB!`);
    uploadImgRef.value.handleRemove(file);
    return false;
  }
  handleShowList(file, updatedFileList);

  console.log('=====================触发了handleChange的生命周期结束！！！');
};
const handleShowList = (file, updatedFileList) => {
  // 查找目标元素的全部索引
  let indices = [];
  for (let i = updatedFileList.length - 1; i >= 0; i--) {
    if (updatedFileList[i].name === file.name) {
      console.log('name相等！！');
      indices.push(i);
    }
  }
  console.log('🚀 ~ handleChange ~ indices:', indices);
  // 如果有多个索引，删除第一个索引对应的元素
  if (indices.length > 1) {
    updatedFileList.splice(indices[0], 1);
  }
};
//上传失败
const handleError = (file, updatedFileList) => {
  ElMessage.error(updatedFileList.name + '上传失败');
  console.log('handleError的生命周期！！！');
};
//文件列表移除文件时的钩子
const handRemove = (file, updatedFileList) => {
  console.log('🚀 ~ handRemove ~ file:', file);
  console.log('🚀 ~handRemove的生命周期开始-updatedFileList:', updatedFileList);
  if (file.file_id) {
    delMediaUploadFiles(file.file_id).then(response => {
      console.log('🚀 ~ deleteModelT ~ data:', response);
      ElMessage.success('删除成功');
    });
  }

  fileList.value = updatedFileList;
  console.log('🚀 ~ 移除后的文件列表:', fileList.value);
  console.log('handRemove的生命周期结束！！！');
};
//上传成功
const handleSuccess = (file, updatedFileList) => {
  console.log('🚀 ~ handleSuccess的生命周期！！！', updatedFileList);
  console.log(updatedFileList.name + '上传成功');
  console.log('🚀 ~ 上传成功后 ~ fileList.value:', fileList.value);
  console.log('-----------------------------------------------------');
};
</script>
<style scoped lang="scss">
.app-form {
  ::v-deep {
    .el-input-number,
    .el-select {
      width: 100%;
    }
    .el-input-number .el-input__inner {
      text-align: left;
    }
    .el-input-number.is-controls-right .el-input__wrapper {
      padding-left: 11px;
    }
    .el-date-editor.el-input,
    .el-date-editor.el-input__wrapper {
      width: 100%;
    }
  }
}
.upload-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 300px;
}

:deep(.el-upload--picture-card) {
  height: 100%;
  width: 100%;
}
:deep(.el-upload__text) {
  font-size: 13px;
  margin-bottom: 13px;
  margin-top: -5px;
}

:deep(.el-upload-dragger) {
  width: 565px;
  background-color: #f9f9f9;
}
:deep(.el-upload-dragger:hover) {
  // width: 565px;
  background-color: white;
}
.el-icon-upload {
  color: #b7d9fd;
  font-size: 70px;
  margin-top: -34px;
}
.tipB {
  font-size: 13px;
  color: #cccccc;
  margin-top: 10px;
}
</style>
