<template>
  <el-dialog title="任务详情" v-if="visible" :model-value="visible" align-center :close-on-click-modal="false" @close="closeDialog">
    <div class="left-content">
      <div class="line-left">
        <el-descriptions label-align="right" :column="2">
          <el-descriptions-item width="500" label="任务名称：" label-class-name="label-class" >
            <span :title="formData.job_name" class="ellipsis" style="display: inline-block;width: 300px;transform: translateY(8px);">{{formData.job_name}}</span>
          </el-descriptions-item>
          <el-descriptions-item label="任务执行方式："
            ><span>{{
              optionData.typeOptions.find(item => item.value === formData.task_type)?.label
            }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="执行机场：" label-class-name="label-class" >
            <span :title="formData.dock_name" class="ellipsis" style="display: inline-block;width: 300px;transform: translateY(8px);">{{ formData.dock_name }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="航线名称：" label-class-name="label-class" >
            <el-link style="color: #409eff">
              <span @click="openPlanInfo(formData.file_id)" class="ellipsis" style="max-width: 180px;display: inline-block;" :title="formData.file_name"> {{ formData.file_name }} </span>
            </el-link>
          </el-descriptions-item>
          <el-descriptions-item label="执行时间：" label-class-name="label-class" >
            {{ formData.execute_time }}
          </el-descriptions-item>
          <el-descriptions-item label="返航高度：" label-class-name="label-class" > {{ formData.rth_altitude }}m </el-descriptions-item>
          <el-descriptions-item label="航线失控动作：" label-class-name="label-class" >
            <span>{{
              optionData.outofControlActionList.find(item => item.value === formData.out_of_control_action)?.label
            }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    <div class="record-title"><span class="title-left"></span>执行记录</div>
    <el-table highlight-current-row :data="recordList" stripe height="300">
      <el-table-column label="任务记录ID" prop="id" show-overflow-tooltip />
      <el-table-column label="执行时间" width="200" prop="update_time" show-overflow-tooltip />
      <el-table-column label="执行状态" prop="exeStatus" show-overflow-tooltip>
        <template>
          <div class="flex-center">
            <div class="status" :style="{ backgroundColor: COMMON_COLOR[form.status] }"></div>

            <span>{{ optionData.statusList.find(item => item.value === form.status)?.label }}</span>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="描述" prop="remark" show-overflow-tooltip>
      </el-table-column> -->

      <el-table-column fixed="right" label="操作" align="center" width="200" v-if="form.status === 3">
        <template #default="scope">
          <el-button type="primary" @click="checkHistory(scope.row)" link>飞行记录</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import optionData from '@/utils/option-data';
import { useRouter } from 'vue-router';
// import router from '@/router';
import { fetchFlightRecord, getWaylines } from '@/api/wayline';
import { COMMON_COLOR } from '@/utils/constants';
import { usePlanInfoStore } from '@/store/modules/planInfo.js';
const planInfoStore = usePlanInfoStore();
const router = useRouter();
const recordList = ref([]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  formData: {
    type: Object,
    default(rawProps) {
      return {};
    }
  }
});
const form = reactive({});
const dataFormRef = ref(ElForm);

watch(
  () => props.formData,
  (newVal, oldVal) => {
    Object.assign(form, newVal);
    getFlightRecord();
  },
  { deep: true }
);
const emit = defineEmits(['update:visible']);

const typeOptions = ref([]);

function getTypeOptiopn() {
  typeOptions.value = [];
}

// 关闭弹窗
function closeDialog() {
  resetForm();

  emit('update:visible', false);
}

function openPlanInfo(file_id) {
  getWaylines({
    order_by: 'update_time desc',
    wayline_id: file_id
  }).then(data => {
    const { list, pagination } = data;
    if (list.length > 0) {
      planInfoStore.setCurPlanData(list[0]);
      router.push({ path: '/planinfo' });
    }
  });
}

/**
 * 重置表单
 */
function resetForm() {
  Object.keys(form).map(key => {
    delete form[key];
  });
}

// 点击查看历史记录 跳转
function checkHistory(item) {
  // 路由跳转
  router.push({
    path: '/mapfly-manager-history',
    query: {
      job_id: item.job_id,
      flight_id: item.flight_id,
      // wayline_id: props.formData.file_id
    }
  });
}

// 获取飞行记录
function getFlightRecord() {
  fetchFlightRecord(form.job_id).then(res => {
    recordList.value = res || [];
  });
}

onMounted(() => {
  getTypeOptiopn();
});
</script>
<style lang="scss">
.label-class {
  display: inline-block;
  width: 100px;
  text-align: right;
}
</style>
<style lang="scss" scoped>
.flex-center {
  display: flex;
  align-items: center;
  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
}
.left-content {
  display: flex;
  flex-direction: row;
  padding: 0 54px;
  img {
    width: 136px;
    height: 136px;
    margin-right: 24px;
  }
  .line-left {
    display: flex;
    padding: 20px 8px;
  }
  .line-middle {
    width: 1px;
    height: 314px;
    background: rgba(0, 0, 0, 0.06);
    margin: auto 32px;
  }
  .line-right {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    .item {
      padding: 6px;
      width: 100%;
      color: #000000d9;
      .color {
        color: #000000a6;
      }
    }
  }
}
.record-title {
  margin: 0 0 16px;
  padding-bottom: 8px;
  font-size: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
  .title-left {
    display: inline-block;
    margin-right: 3px;
    height: 14px;
    border-left: 2px solid #275ba9;
  }
}
</style>
