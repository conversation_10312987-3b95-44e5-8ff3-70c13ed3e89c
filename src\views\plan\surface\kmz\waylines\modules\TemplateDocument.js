import { TemplateFolder } from './TemplateFolder.js';
import { TemplateMissionConfig } from './TemplateMissionConfig.js';
class TemplateDocument {
  constructor() {
    this.wpml_author = '';
    this.wpml_missionConfig = new TemplateMissionConfig();
    this.Folder = [new TemplateFolder()] || [];
  }

  setFolder(f) {
    this.Folder = f;
  }

  setMissionConfig(value) {
    this.wpml_missionConfig = value;
  }

  /**
   * 向当前waylines实例的folder数组中添加一个Folder实例。
   * @param {TemplateFolder} Folder - 要添加的Folder实例。
   */
  addFolder(Folder) {
    if (Folder instanceof TemplateFolder && Array.isArray(this.Folder)) {
      this.Folder.push(Folder);
    } else {
      throw new Error('Invalid argument or Folder array is not initialized properly.');
    }
  }

  // 默认获取第一个
  getFolder(index = 0) {
    return this.Folder[index] || null;
  }

  getAllFolder() {
    return this.Folder;
  }
  /**
   * 返回航点对象
   * @returns
   */
  getWaylines() {
    return {
      document: {
        Folder: this.Folder,
        missionConfig: this.wpml_missionConfig
      }
    };
  }
}
export { TemplateDocument };
