<template>
  <el-dialog
    :title="title"
    v-if="visible"
    :model-value="visible"
    width="500px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form class="app-form" ref="dataFormRef" :model="form" :rules="rules" label-width="120px">
      <el-row :gutter="20">
        <!-- <el-col :span="12"> -->
        <el-form-item label="任务名称" prop="name">
          <el-input
            style="width: 260px;"
            v-model="form.name"
            placeholder="请输入任务名称"
            maxlength="50"
            @blur="form.name = $event.target.value.trim()"
          />
        </el-form-item>
        <!-- </el-col> -->
        <!-- <el-col :span="12">
          <el-form-item label="机场型号" prop="type">
            <el-select
              v-model="form.type"
              placeholder="请选择应用类型"
              :disabled="form.id"
            >
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col> -->
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-loading="loading">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { editAirTaskList } from '@/api/task';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  formData: {
    type: Object,
    default(rawProps) {
      return {};
    }
  }
});
const form = reactive({});
const dataFormRef = ref(ElForm);

watch(
  () => props.formData,
  (newVal, oldVal) => {
    Object.assign(form, newVal);
  },
  { deep: true }
);
const emit = defineEmits(['update:visible', 'submit']);
const rules = reactive({
  name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }]
});
defineExpose({ setDefaultValue });
// 设置默认值
function setDefaultValue() {
  if (!form.type && typeOptions.value.length > 0) {
    form.type = typeOptions.value[0].value;
  }
}
const typeOptions = ref([]);
const loading = ref(false);

function getTypeOptiopn() {
  typeOptions.value = [];
}

// 关闭弹窗
function closeDialog() {
  resetForm();
  emit('update:visible', false);
}
/**
 * 重置表单
 */
function resetForm() {
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  loading.value = false;
  Object.keys(form).map(key => {
    delete form[key];
  });
}

function handleSubmit() {
  dataFormRef.value.validate(isValid => {
    if (isValid) {
      let params = { name: form.name };

      loading.value = true;
      editAirTaskList({
        task_id: props.formData.flight_task_id,
        ...params
      })
        .then(res => {
          loading.value = false;
          ElMessage.success('更新成功');
          closeDialog();
          emit('submit');
        })
        .catch(e => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

onMounted(() => {
  getTypeOptiopn();
});
</script>
