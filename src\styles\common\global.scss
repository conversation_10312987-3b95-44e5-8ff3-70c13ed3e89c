//#region 全局滚动条处理
// /* 滚动条样式 */
// ::-webkit-scrollbar {
//   width: 8px;
//   /*  设置纵轴（y轴）轴滚动条 */
//   height: 8px;
//   /*  设置横轴（x轴）轴滚动条 */
// }

// /* 滚动条滑块（里面小方块） */
// ::-webkit-scrollbar-thumb {
//   border-radius: 1px;
//   border: none;
//   box-shadow: inset 0 0 5px #175094;
//   // background-color: #5f5f5f;
// }

// /* 滚动条轨道 */
// ::-webkit-scrollbar-track {
//   border-radius: 0;
//   background: transparent;
// }

// /* 非激活窗口 */
// ::-webkit-scrollbar-thumb:window-inactive {
//   background: rgba(0, 0, 0, 0.2);
// }

/* 滚动条样式 */

// 重点上、下方向同理
// ::-webkit-scrollbar-button:vertical 给垂直方向的滚动条设置样式
// ::-webkit-scrollbar-button:vertical:start 上方向的按钮
// ::-webkit-scrollbar-button:vertical:start:decrement 上方向单个按钮
// 原文链接：https://blog.csdn.net/silence_xiang/article/details/137883599
/* 设置滚动条的样式 */
// ::-webkit-scrollbar {
//   width: 14px !important;
//   height: 8px;
// }
// /* 滚动槽 */
// ::-webkit-scrollbar-track {
//   -webkit-box-shadow: inset 0 0 6px rgba(190, 92, 92, 0.2);
//   color: #175192;
//   border-radius: 2px;
// }
// // 上箭头
// ::-webkit-scrollbar-button:start {
//   background-image: url('../../assets/up-arrow.png');
//   background-size: 14px !important;
//   background-repeat: no-repeat;
//   background-position: center center;
// }
// ::-webkit-scrollbar-button:end {
//   background-image: url('../../assets/down-arrow.png');
//   background-repeat: no-repeat;
//   background-size: 14px !important;
//   background-position: center center;
// }
// /* 滚动条滑块（里面小方块） */
// ::-webkit-scrollbar-thumb {
//   border-radius: 2px;
//   width: 12px !important;
//   background: #175192 !important;
//   -webkit-box-shadow: inset 0 0 6px #175192 !important;
// }

//#endregion

//#region 全局背景颜色
.bg-light-blue {
  background-color: #1f2f49 !important;
}
//#endregion

.ellipsis {
  overflow: hidden; /* 超出一行文字自动隐藏 */
  text-overflow: ellipsis; /* 文字隐藏后添加省略号 */
  white-space: nowrap; /* 强制文本不换行 */
}
.ellipsis-3 {
  display: -webkit-box; /* 盒子类型 */
  word-break: break-all; /* 自动换行的处理方法。允许在单词内换行 */
  text-overflow: ellipsis; /* 溢出时用... */
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3; /* 最大行数 */
}
.ellipsis-4 {
  display: -webkit-box; /* 盒子类型 */
  word-break: break-all; /* 自动换行的处理方法。允许在单词内换行 */
  text-overflow: ellipsis; /* 溢出时用... */
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3; /* 最大行数 */
}
.ellipsis-5 {
  display: -webkit-box; /* 盒子类型 */
  word-break: break-all; /* 自动换行的处理方法。允许在单词内换行 */
  text-overflow: ellipsis; /* 溢出时用... */
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3; /* 最大行数 */
}
