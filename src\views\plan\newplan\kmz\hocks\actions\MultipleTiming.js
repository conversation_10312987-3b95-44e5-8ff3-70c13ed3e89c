// 等时拍照相关
// kml
{
  /* <wpml:actionGroup>
  <wpml:actionGroupId>1</wpml:actionGroupId>
  <wpml:actionGroupStartIndex>0</wpml:actionGroupStartIndex>
  <wpml:actionGroupEndIndex>0</wpml:actionGroupEndIndex>
  <wpml:actionGroupMode>sequence</wpml:actionGroupMode>
  <wpml:actionTrigger>
    <wpml:actionTriggerType>multipleTiming</wpml:actionTriggerType>
    <wpml:actionTriggerParam>3</wpml:actionTriggerParam>
  </wpml:actionTrigger>
  <wpml:action>
    <wpml:actionId>0</wpml:actionId>
    <wpml:actionActuatorFunc>takePhoto</wpml:actionActuatorFunc>
    <wpml:actionActuatorFuncParam>
      <wpml:payloadPositionIndex>0</wpml:payloadPositionIndex>
      <wpml:useGlobalPayloadLensIndex>1</wpml:useGlobalPayloadLensIndex>
      <wpml:payloadLensIndex>wide,zoom,ir</wpml:payloadLensIndex>
    </wpml:actionActuatorFuncParam>
  </wpml:action>
</wpml:actionGroup>; */
}
import { PAYLOAD_LENS_INDEX } from '../../props';
import { Action, ActionGroup } from '../../waylines';
import { generateKey } from '@/utils';
import { getMaxActionGroupIndex, getMinMaxPlacemarkInfo } from '../../../kmz/hocks/modules/waylineshandle';
import { ACTION_ACTUATOR_FUNC, ACTION_TRIGGER_TYPE, OPERATION_TYPE } from '@/utils/constants';
import { useDeviceStore } from '@/store/modules/device.js';
const deviceStore = useDeviceStore();
//#region 等时拍照
/**
 * 创建悬停动作
 * @param {Object} options 动作配置项，应包含actionId、actionActuatorFunc
 * @param {Object|null} placemark 航点对象
 * @returns {Action|null} 返回Action实例或在配置不正确时返回null
 */
export function createMultipleTimingActionGroup(options = null) {
  if (!options) {
    return null;
  }
  try {
    // 创建动作
    let { actionTrigger, action } = getMultipleTimingActionGroupDefaultParam();
    const placemarkIndex = options?.placemark.wpml_index || 0;
    let maxActionGroupId = getMaxActionGroupIndex();
    if (!maxActionGroupId) {
      maxActionGroupId = 0;
    }
    // 取得最大的航点信息
    const { index: maxPlacemarkIndex } = getMinMaxPlacemarkInfo({ type: 'max' });
    const ac = new Action({
      actionId: options.actionId || 0,
      actionActuatorFunc: action?.wpml_actionActuatorFunc || ACTION_ACTUATOR_FUNC.takePhoto,
      actionActuatorFuncParam: action?.wpml_actionActuatorFuncParam,
      uuid: options.actionUuid || generateKey(), // 动作id
      trigger: ACTION_TRIGGER_TYPE.multipleTiming // 动作触发类型
    });
    const ag = new ActionGroup({
      placemarkIndex: placemarkIndex,
      actionGroupId: maxActionGroupId,
      actionGroupStartIndex: placemarkIndex,
      actionGroupEndIndex: maxPlacemarkIndex,
      actionGroupMode: options?.actionGroupMode || 'sequence',
      actionTriggerType: actionTrigger?.wpml_actionTriggerType || 'reachPoint',
      actionTriggerParam: actionTrigger?.wpml_actionTriggerParam || null,
      actions: [],
      uuid: generateKey(),
      type: options?.type || OPERATION_TYPE.interval
    });
    ag && ag.addAction(ac);
    return {
      actionGroup: ag || null,
      action: ac || null
    };
  } catch (error) {
    console.error('创建 Action 实例失败:', error);
    return null;
  }
}

// 获取等时拍照默认参数
export function getMultipleTimingActionGroupDefaultParam() {
  let cameras = deviceStore?.getCameraSelect().join(',') ?? '';
  return {
    actionTrigger: {
      wpml_actionTriggerType: ACTION_TRIGGER_TYPE.multipleTiming,
      wpml_actionTriggerParam: 3
    },
    action: {
      wpml_actionActuatorFunc: ACTION_ACTUATOR_FUNC.takePhoto,
      wpml_actionActuatorFuncParam: {
        wpml_fileSuffix: '', // 这里如果为空则不写入
        wpml_payloadPositionIndex: 0,
        wpml_useGlobalPayloadLensIndex: 1,
        wpml_payloadLensIndex: cameras
      }
    }
  };
}
//#endregion
