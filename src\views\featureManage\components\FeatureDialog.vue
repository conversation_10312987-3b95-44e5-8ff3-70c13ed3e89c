<template>
  <el-dialog
    :model-value="visible"
    :title="isEdit ? '编辑图元' : '新增图元'"
    width="800px"
    :close-on-click-modal="false"
    @update:model-value="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="right"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="图元名称" prop="featureName">
            <el-input
              v-model="form.featureName"
              placeholder="请输入图元名称"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属图层" prop="layerId">
            <el-select
              v-model="form.layerId"
              placeholder="请选择图层"
              style="width: 100%"
              filterable
            >
              <el-option
                v-for="item in layerOptions"
                :key="item.id"
                :label="item.layerName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="图元地址">
        <el-input
          v-model="form.featureAddr"
          placeholder="请输入图元地址"
          maxlength="255"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="经度" prop="longitude">
            <el-input-number
              v-model="form.longitude"
              :precision="7"
              :min="-180"
              :max="180"
              style="width: 100%"
              placeholder="请输入经度"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="纬度" prop="latitude">
            <el-input-number
              v-model="form.latitude"
              :precision="7"
              :min="-90"
              :max="90"
              style="width: 100%"
              placeholder="请输入纬度"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <div class="map-picker">
        <el-button type="primary" @click="showMapPicker">地图选点</el-button>
        <span v-if="form.longitude && form.latitude" class="coordinate-info">
          当前坐标：{{ form.longitude }}, {{ form.latitude }}
        </span>
      </div>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系人">
            <el-input
              v-model="form.contact"
              placeholder="请输入联系人"
              maxlength="50"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话">
            <el-input
              v-model="form.telephone"
              placeholder="请输入联系电话"
              maxlength="20"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="坐标集合">
        <el-input
          v-model="form.featureCoords"
          type="textarea"
          :rows="4"
          placeholder="线面图元的坐标集合，JSON格式的二维数组，如：[[116.404, 39.915], [116.405, 39.916]]"
        />
        <div class="form-tip">
          仅线图层和面图层需要填写，点图层可留空
        </div>
      </el-form-item>

      <el-form-item label="扩展属性">
        <el-input
          v-model="form.extraInfo"
          type="textarea"
          :rows="4"
          placeholder="请输入JSON格式的扩展属性"
        />
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>

    <!-- 地图选点弹窗 -->
    <!-- 注意：MapPickerDialog组件需要单独实现，这里先注释掉 -->
    <!-- <MapPickerDialog
      v-model:visible="mapPickerVisible"
      :initial-coordinate="{ longitude: form.longitude, latitude: form.latitude }"
      @confirm="handleMapPickerConfirm"
    /> -->
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { createFeature, updateFeature } from '@/api/map/feature';
// import MapPickerDialog from './MapPickerDialog.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  layerOptions: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:visible', 'success']);

const formRef = ref();
const loading = ref(false);
const mapPickerVisible = ref(false);

// 表单数据
const form = reactive({
  featureName: '',
  layerId: null,
  featureAddr: '',
  longitude: null,
  latitude: null,
  contact: '',
  telephone: '',
  featureCoords: '',
  extraInfo: '',
  remark: ''
});

// 表单验证规则
const rules = {
  featureName: [
    { required: true, message: '请输入图元名称', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  layerId: [
    { required: true, message: '请选择所属图层', trigger: 'change' }
  ],
  longitude: [
    { required: true, message: '请输入经度', trigger: 'blur' },
    { type: 'number', min: -180, max: 180, message: '经度范围为 -180 到 180', trigger: 'blur' }
  ],
  latitude: [
    { required: true, message: '请输入纬度', trigger: 'blur' },
    { type: 'number', min: -90, max: 90, message: '纬度范围为 -90 到 90', trigger: 'blur' }
  ]
};

// 监听表单数据变化
watch(
  () => props.formData,
  (newData) => {
    if (newData && Object.keys(newData).length > 0) {
      Object.assign(form, {
        featureName: newData.featureName || '',
        layerId: newData.layerId || null,
        featureAddr: newData.featureAddr || '',
        longitude: newData.longitude || null,
        latitude: newData.latitude || null,
        contact: newData.contact || '',
        telephone: newData.telephone || '',
        featureCoords: newData.featureCoords || '',
        extraInfo: newData.extraInfo || '',
        remark: newData.remark || ''
      });
    } else {
      // 重置表单
      Object.assign(form, {
        featureName: '',
        layerId: null,
        featureAddr: '',
        longitude: null,
        latitude: null,
        contact: '',
        telephone: '',
        featureCoords: '',
        extraInfo: '',
        remark: ''
      });
    }
  },
  { immediate: true, deep: true }
);

/**
 * 显示地图选点
 */
function showMapPicker() {
  // 暂时使用简单的提示，后续可以集成地图组件
  ElMessage.info('地图选点功能待实现，请手动输入坐标');
  // mapPickerVisible.value = true;
}

/**
 * 地图选点确认
 */
function handleMapPickerConfirm(coordinate) {
  form.longitude = coordinate.longitude;
  form.latitude = coordinate.latitude;
  mapPickerVisible.value = false;
}

/**
 * 关闭弹窗
 */
function handleClose() {
  emit('update:visible', false);
  // 重置表单验证
  nextTick(() => {
    formRef.value?.resetFields();
  });
}

/**
 * 提交表单
 */
async function handleSubmit() {
  try {
    await formRef.value.validate();
    
    // 验证坐标集合JSON格式
    if (form.featureCoords) {
      try {
        const coords = JSON.parse(form.featureCoords);
        if (!Array.isArray(coords)) {
          ElMessage.error('坐标集合必须是数组格式');
          return;
        }
      } catch (error) {
        ElMessage.error('坐标集合必须是有效的JSON格式');
        return;
      }
    }

    // 验证扩展属性JSON格式
    if (form.extraInfo) {
      try {
        JSON.parse(form.extraInfo);
      } catch (error) {
        ElMessage.error('扩展属性必须是有效的JSON格式');
        return;
      }
    }

    loading.value = true;

    const submitData = { ...form };
    if (props.isEdit) {
      submitData.id = props.formData.id;
      await updateFeature(submitData);
      ElMessage.success('更新成功');
    } else {
      await createFeature(submitData);
      ElMessage.success('创建成功');
    }

    emit('success');
  } catch (error) {
    console.error('提交失败:', error);
    ElMessage.error('操作失败');
  } finally {
    loading.value = false;
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

.map-picker {
  margin: 10px 0;
  
  .coordinate-info {
    margin-left: 10px;
    color: #409eff;
    font-size: 14px;
  }
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
