import { ACTION_ACTUATOR_FUNC } from '@/utils/constants';
import PhotoAndRecord from './modules/PhotoAndRecord.vue';
import StopRecord from './modules/StopRecord.vue';
import StopInterval from './modules/StopInterval.vue';
import PhotoInterval from './modules/PhotoInterval.vue';
import Hover from './modules/Hover.vue';
import PanoShot from './modules/PanoShot.vue';
import RotateYaw from './modules/RotateYaw.vue';
import GimbalRotate from './modules/GimbalRotate.vue';
import CustomDirName from './modules/CustomDirName.vue';
import Zoom from './modules/Zoom.vue';
export const ActionComponentMap = {
  [ACTION_ACTUATOR_FUNC.takePhoto]: PhotoAndRecord,
  [ACTION_ACTUATOR_FUNC.startRecord]: PhotoAndRecord,
  [ACTION_ACTUATOR_FUNC.stopRecord]: StopRecord,
  [ACTION_ACTUATOR_FUNC.timeIntervalTakePhoto]: PhotoInterval,
  [ACTION_ACTUATOR_FUNC.distanceIntervalTakePhoto]: PhotoInterval,
  [ACTION_ACTUATOR_FUNC.stopIntervalTakePhoto]: StopInterval,
  [ACTION_ACTUATOR_FUNC.hover]: Hover,
  [ACTION_ACTUATOR_FUNC.gimbalRotate]: GimbalRotate,
  [ACTION_ACTUATOR_FUNC.rotateYaw]: RotateYaw,
  [ACTION_ACTUATOR_FUNC.panoShot]: PanoShot,
  [ACTION_ACTUATOR_FUNC.customDirName]: CustomDirName,
  [ACTION_ACTUATOR_FUNC.zoom]: Zoom
};
