<script>
export default { name: 'H5player' };
</script>

<script setup>
import { onMounted, onUnmounted, reactive, computed } from 'vue';
import moment from 'moment';
import { stopLivestream, startLivestream, setLivestreamQuality, getVideoUrl } from '@/api/live';
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  playerId: {
    type: String,
    default: ''
  },
  dronePara: {
    type: Object,
    default: {}
  },
  index: {
    type: Number,
    default: 0
  },
});
const nonSwitchable = 'normal';
const MSE_IS_SUPPORT = !!window.MediaSource 
const player = ref(null);
const droneParaObj = reactive({}); //摄像头信息
const counts = ref(0); //重连次数
const finalUrl = ref(null); //播放地址
const videoPlayError = ref(false); //播放出差页面
const tabActive = ref(MSE_IS_SUPPORT ? 'mse' : 'decoder')
const playback = reactive({
  startTime: '',
  endTime: '',
  valueFormat: moment.HTML5_FMT.DATETIME_LOCAL_SECONDS,
  seekStart: '',
  rate: ''
});
const show = ref(false);
const mode = computed(() => {
  return tabActive === 'mse' ? 0 : 1
})

defineExpose({
  onStop,
  realplay
});

//播放
function videoPlay() {
  // index = player.value.currentWindowIndex,
  console.log('index12333',props.index)
  let playURL = finalUrl.value
  player.value.JS_Play(playURL, { playURL, mode: 1 }, 2).then(
    () => { console.log('realplay success') },
    e => { console.error(e) }
  )
}

function playExternalVideo() {
  getVideoUrl({ id: droneParaObj.cameraId })
    .then(res => {
      finalUrl.value = res;
      if (finalUrl.value) {
        videoPlay();
      }
    })
    .catch(err => {
      console.error(err);
    });
}

/* 播放 */
function realplay(data) {
  // if (finalUrl.value) return;
  if (data?.droneSelected == null || data?.cameraId == null || data?.claritySelected == null) {
    console.log('waring: not select live para!!!');
    return;
  }
  Object.assign(droneParaObj, data);
  let videoId = data?.droneSelected + '/' + data?.cameraSelected + '/' + (data?.videoSelected || nonSwitchable + '-0');

  const timestamp = new Date().getTime().toString();
  if (data.isExternal) {
    // 外部视频
    if(data.source == '2') {
      startLivestream({
      url: '',
      video_id: `${data?.droneSelected}/165-0-7/normal-0`,
      url_type: 3,
      video_quality: data?.claritySelected
    })
      .then(res => {
        finalUrl.value = res?.url;
        if (finalUrl.value) {
          videoPlay();
        }
      })
      .catch(err => {
        console.error(err);
      });
    }else {
      playExternalVideo();
    }
  } else {
    startLivestream({
      url: `${import.meta.env.VITE_STREAM_BASE_URL}/live/${timestamp}`,
      video_id: videoId,
      url_type: 1,
      video_quality: data?.claritySelected
    })
      .then(res => {
        finalUrl.value = res?.url;
        if (finalUrl.value) {
          videoPlay();
        }
      })
      .catch(err => {
        console.error(err);
      });
  }
}

// 停止播放
function onStop() {
  player.value.JS_Stop().then(
    () => { playback.rate = 0; console.log('stop realplay success') },
    e => { console.error(e) }
  )
}

// 全屏
function wholeFullScreen() {
  player.value.JS_FullScreenDisplay(true).then(
    () => { console.log(`wholeFullScreen success`) },
    e => { console.error(e) }
  )
}

// 创建视频
function createPlayer() {
  player.value = new window.JSPlugin({
    szId: props.playerId,
    szBasePath: "../../../public/js/isc-h5",
    iMaxSplit: 1,
    iCurrentSplit: 1,
    oStyle: {
      borderSelect: '#000',
    }
  })

  // 事件回调绑定
  player.value.JS_SetWindowControlCallback({
    windowEventSelect: function (iWndIndex) {  //插件选中窗口回调
        console.log('windowSelect callback: ', iWndIndex);
    },
    pluginErrorHandler: function (iWndIndex, iErrorCode, oError) {  //插件错误回调
        console.log('pluginError callback: ', iWndIndex, iErrorCode, oError);
    },
    windowEventOver: function (iWndIndex) {  //鼠标移过回调
        console.log(iWndIndex);
        show.value = true
    },
    windowEventOut: function (iWndIndex) {  //鼠标移出回调
        //console.log(iWndIndex);
        show.value = false
    },
    windowEventUp: function (iWndIndex) {  //鼠标mouseup事件回调
        //console.log(iWndIndex);
    },
    windowFullCcreenChange: function (bFull) {  //全屏切换回调
        console.log('fullScreen callback: ', bFull);
    },
    // firstFrameDisplay: function (iWndIndex, iWidth, iHeight) {  //首帧显示回调
    //     console.log('firstFrame loaded callback: ', iWndIndex, iWidth, iHeight);
    // },
    performanceLack: function () {  //性能不足回调
        console.log('performanceLack callback: ');
    }
  });
}

onMounted(()=>{
  // player.style.setProperty('display', 'block')
  init()
  createPlayer()
})

function init() {
  // 设置播放容器的宽高并监听窗口大小变化
  window.addEventListener('resize', () => {
    player.value.JS_Resize()
  })
}

onUnmounted(() => {
  console.log('关闭===============');
  onStop();
  window.removeEventListener('resize', () => {})
});

// 抓图
function capture(imageType) {
  let index = player.value.currentWindowIndex

  player.value.JS_CapturePicture(index, 'img', imageType).then(
    () => { console.log('capture success', imageType) },
    e => { console.error(e) }
  )
}
</script>

<template>
	<div class="palyer-container" v-if="!videoPlayError">
		<div class="player" :id="props.playerId"></div>
		<div class="title-name">{{ droneParaObj?.cameraName || '无视频' }}</div>
    <div class="close-icon" v-if="droneParaObj?.droneSelected" @click="closeVideo"><i-ep-close /></div>
    <div class="toolbar" v-show="show">
      <svg-icon icon-class="cut" class="cursor" style="margin-right: 15;width: 20px;height: 20px;" @click="capture()"/>
      <svg-icon icon-class="full_screen" class="cursor" style="margin-right: 25;width: 20px;height: 20px;" @click="wholeFullScreen()"/>
    </div>
	</div>
  <div class="videoPlayError" v-if="videoPlayError">
    <el-button type="primary" plain @click="realplay()">播放失败，立即重试</el-button>
  </div>
</template>

<style scoped lang="scss">
.palyer-container {
  position: relative;
  background-color: #fff;
  height: 100%;
  border: 1px solid #101010;
  .player {
    width: 100%;
    height: 100%;
  }
  .cursor {
    cursor: pointer;
  }
  .toolbar {
    text-align: right;
    position: absolute;
    width: 100%;
    height: 40px;
    line-height: 40px;
    background: #fff;
    left: 0;
    bottom: 0;
  }
  .title-name {
    position: absolute;
    color: white;
    font-size: 14px;
    top: 10px;
    left: 10px;
  }
  .close-icon {
    background-color: #262c33;
    position: absolute;
    color: white;
    font-size: 14px;
    top: 10px;
    right: 10px;
    cursor: pointer;
    border-radius: 50%;
    width: 22px;
    height: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.videoPlayError {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #262c33;
  border: 1px solid #101010;
}
</style>