<template>
  <div class="camera-select-wrapper">
    <div class="camera-select">
      <div class="left">
        <div v-for="item in dataRef">
          <el-button
            type="primary"
            class="left-item"
            :class="{
              active: item.active
            }"
            :disabled="item.disabled"
            @click="onClickHandle(item)"
          >
            {{ item.label }}
          </el-button>
        </div>
      </div>
      <div class="right">
        <el-button
          type="primary"
          class="right-item"
          :class="{ active: isFollowWayline === true }"
          @click="onSetClickFollowWayline"
        >
          跟随航线
        </el-button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'CameraTypeSelect'
};
</script>
<script setup>
import { onMounted, onUnmounted, defineExpose, computed } from 'vue';
import { getPayloadLensIndex, PAYLOAD_LENS_INDEX_CN_NAME } from '../../../kmz/props';

//#region 数据双向绑定
// const data = {
//   deviceType: '间隔距离',
//   wpml_payloadLensIndex: '',
//   wpml_useGlobalPayloadLensIndex: "",
// };
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
const data = computed({
  get: () => props.modelValue,
  set: value => {
    emits('update:modelValue', value);
  }
});
const dataRef = reactive([]);
const isFollowWayline = ref(true);
// 对外定义事件
const emits = defineEmits(['update:data', 'cameraTypeChange']); // 触发事件
//#region 初始化

watch(
  () => data.value,
  newVal => {
    if (newVal) {
      console.log('newVal', newVal);
      resteData();
    }
  },
  {
    deep: true
  }
);
//#region 方法

function parseWpmlPayloadLensIndex(value) {
  if (!value) {
    return null;
  } else {
    // 值可能为 "visible, ir" 或 "visible" 这种方式
    const lensTypes = value.split(',').map(type => type.trim());
    if (!lensTypes || lensTypes.length === 0) {
      return null;
    }
    let result = {};
    lensTypes.forEach(l => {
      result[l] = l;
    });
    console.log('result', result);
    // 返回数组
    return result;
  }
}
const onClickHandle = item => {
  // 点击时获取当前点击项
  if (isFollowWayline.value) {
    return;
  }
  const curActive = item.active;
  if (curActive) {
    // 如果当前是取消激活状态 就要检查当前选中的数据个数 如果 = 1 则不往下进行
    const isActiveCountGreaterThanOne = checkActiveCount(dataRef);
    if (!isActiveCountGreaterThanOne) {
      return;
    }
  }
  // 找到当前的 支持类型
  let curValue = item.value;
  dataRef?.forEach(item => {
    if (item.value === curValue) {
      item.active = !curActive;
      item.disabled = false;
    }
  });
  // 计算 最新的 parms.wpml_payloadLensIndex
  let _wpml_payloadLensIndex = [];
  dataRef?.forEach(item => {
    if (item.active) {
      _wpml_payloadLensIndex.push(item.value);
    }
  });
  data.value.wpml_payloadLensIndex = _wpml_payloadLensIndex.join(',');
  emits('cameraTypeChange', data.value);
};

function checkActiveCount(arr = []) {
  let activeCount = 0;
  for (let i = 0; i < arr.length; i++) {
    if (arr[i].active === true) {
      activeCount++;
    }
  }
  return activeCount > 1;
}
const onSetClickFollowWayline = () => {
  // 点击时获取当前点击项
  if (isFollowWayline.value) {
    // 其他被选中
    unSelecteFollowWayline();
  } else {
    // 跟随被选中
    selectedFollowWayline();
  }
  isFollowWayline.value = !isFollowWayline.value;
  emits('cameraTypeChange', data.value);
};
// 取消选中跟随航点 设置其他类型
const unSelecteFollowWayline = () => {
  // 选中跟随 设置该参数下全局设置
  data.value.wpml_useGlobalPayloadLensIndex = 0;
  isFollowWayline.value = false;
  dataRef?.forEach(data => {
    data.active = true;
    data.disabled = false;
  });
  emits('cameraTypeChange', data.value);
};

// 如果选中跟随航点
const selectedFollowWayline = () => {
  // 选中跟随 设置该参数下全局设置
  data.value.wpml_useGlobalPayloadLensIndex = 1;
  isFollowWayline.value = true;
  let _wpml_payloadLensIndex = '';
  dataRef?.forEach(data => {
    _wpml_payloadLensIndex += data.value + ',';
  });
  data.value.wpml_payloadLensIndex = _wpml_payloadLensIndex;
  // 设置
  dataRef?.forEach(data => {
    data.active = false;
    data.disabled = true;
  });
  emits('cameraTypeChange', data.value);
};

//#region 初始化

const resteData = () => {
  // 选中跟随 设置该参数下全局设置
  // 初始时设置
  dataRef.length = 0;
  // 根据设备编号获取对应支持的拍照方式
  const payloadLensIndexList = getPayloadLensIndex(data.value.deviceType);
  if (data.value.wpml_useGlobalPayloadLensIndex === 1) {
    isFollowWayline.value = true;
    for (const key in payloadLensIndexList) {
      let cnname = PAYLOAD_LENS_INDEX_CN_NAME[key] || null;
      cnname &&
        dataRef.push({
          value: key, // wide,ir...
          label: cnname, // 中文名称
          data: null,
          disabled: true, // 是否允许点击
          active: false // 是否激活
        });
    }
  } else if (data.value.wpml_useGlobalPayloadLensIndex === 0) {
    isFollowWayline.value = false;
    // 这里判断 wpml_payloadLensIndex 和 payloadLensIndexList 的匹配关系 wpml_payloadLensIndex 未"visible、ir"
    let payloadLensObj = parseWpmlPayloadLensIndex(data.value.wpml_payloadLensIndex);
    // 遍历对象构建 设置初始值这里设置默认值
    for (const key in payloadLensIndexList) {
      let hasPayLoad = false;
      if (!payloadLensObj) {
        hasPayLoad = false;
      } else {
        if (payloadLensObj.hasOwnProperty(key)) {
          hasPayLoad = true;
        }
      }
      let cnname = PAYLOAD_LENS_INDEX_CN_NAME[key] || null;
      cnname &&
        dataRef.push({
          value: key, // wide,ir...
          label: cnname, // 中文名称
          data: null,
          disabled: isFollowWayline.value, // 是否允许点击
          active: hasPayLoad // 是否允许点击
        });
    }
  }
};

function init() {
  resteData();
}
//#endregion

onMounted(() => {
  init();
});
onUnmounted(() => {});
</script>
<style lang="scss" scoped>
::v-deep .el-button.is-disabled {
  background-color: #314257 !important;
  color: #bdbdbd;
  cursor: not-allowed !important;
  background-color: #63778e;
  border-color: #737577;
}
.camera-select-wrapper {
  width: 100%;
  color: white;
  user-select: none;
  .camera-select {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      flex-wrap: wrap;
      .left-item {
        width: auto;
        height: 30px;
        padding: 2px 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        background-color: #314257;
        color: #fff;
        margin: 5px 5px;
        user-select: none;
        border-color: #314257;
      }
    }
    .right {
      display: flex;
      justify-content: center;
      align-items: flex-start;
      .right-item {
        width: 80px;
        height: 30px;
        padding: 2px 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        background-color: #314257;
        border-color: #314257;
        color: #ffffffb5;
        margin: 5px 2px;
        user-select: none;
        &:hover {
          cursor: pointer;
        }
      }
    }
  }
}
.active {
  color: #fff !important;
  background: #2e90fa !important;
}
</style>
