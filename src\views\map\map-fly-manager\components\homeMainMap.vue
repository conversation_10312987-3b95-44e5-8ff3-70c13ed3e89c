<template>
  <div id="mainMapContainer" class="homemap-container">
    <!-- 切换 -->
    <div class="switch-container" v-if="showLayerControl">
      <div class="sc-item" :class="{ 'is-active': isLayerActive }" @click="controlMapLayer('layer')">
        <svg-icon icon-class="layer" class="sc-item-svg" />
      </div>
      <div class="sc-item" :class="{ 'is-active': isEarthActive }" @click="controlMapLayer('earth')">
        <svg-icon icon-class="earth" class="sc-item-svg" />
      </div>
    </div>
    <!-- 图层管理 -->
    <div class="lyr-manager" v-if="isLayerActive" :class="{ 'is-active': !showPoltLyr }">
      <div class="lyr-manager-title">
        <el-row>
          <el-col :span="2"></el-col>
          <el-col :span="10" style="font-size: 16px">图层</el-col>
          <el-col :span="11" style="text-align: right; cursor: pointer">
            <svg-icon icon-class="close" class="sc-item-svg" @click="controlMapLayer('layer')" />
          </el-col>
          <el-col :span="1"></el-col>
        </el-row>
      </div>
      <div class="lyr-manager-content" :class="{ 'is-active': !showPoltLyr }">
        <el-row class="lmc-row">
          <el-checkbox
            v-model="lyr_noFly"
            label="禁飞区"
            text-color="#ffffff"
            @change="handleCheckboxChange('禁飞区')"
          />
        </el-row>
        <el-row class="lmc-row">
          <el-checkbox v-model="lyr_limitHeight" label="限高区" @change="handleCheckboxChange('限高区')" />
        </el-row>
        <el-row class="lmc-row">
          <el-checkbox
            v-model="lyr_limitFlightArea"
            label="自定义限飞区"
            @change="handleCheckboxChange('自定义限飞区')"
          />
        </el-row>
        <el-row class="lmc-row">
          <el-checkbox
            v-model="lyr_workFlightArea"
            label="自定义作业区"
            @change="handleCheckboxChange('自定义作业区')"
          />
        </el-row>
        <el-row class="lmc-row">
          <el-checkbox
            v-model="lyr_airPortArea"
            v-if="showAirPortAreaLyr"
            label="机场覆盖范围"
            @change="handleCheckboxChange('机场覆盖范围')"
          />
        </el-row>
        <el-row class="lmc-row" v-if="showPoltLyr">
          <el-checkbox v-model="lyr_polt" label="标绘" @change="handleCheckboxChange('标绘')" />
        </el-row>
      </div>
    </div>
    <!-- 底图切换 -->
    <div class="map-manager" v-if="isEarthActive">
      <div class="map-manager-title">
        <el-row>
          <el-col :span="2"></el-col>
          <el-col :span="10" style="font-size: 14px">地图</el-col>
          <el-col :span="11" style="text-align: right; cursor: pointer">
            <svg-icon icon-class="close" class="sc-item-svg" @click="controlMapLayer('earth')" />
          </el-col>
          <el-col :span="1"></el-col>
        </el-row>
      </div>
      <div class="map-manager-content">
        <div class="mmc-item">
          <div class="map-img" :class="{ 'is-active': lyr_img }" @click="handleCheckboxChange('天地图影像')"></div>
          <div class="mmc-item-title" :class="{ 'is-active': lyr_img }">影像</div>
        </div>
        <div class="mmc-item">
          <div class="vec-img" :class="{ 'is-active': lyr_vec }" @click="handleCheckboxChange('天地图矢量')"></div>
          <div class="mmc-item-title" :class="{ 'is-active': lyr_vec }">矢量</div>
        </div>
      </div>
    </div>
  </div>

  <SituationPlotting
    v-if="showFlag"
    :ffCesium="ffCesium"
    :plotDataArray="plotDataArray"
    :alarmId="nowAlarmId"
    useType="view"
    style="position: absolute; top: 50px; right: 50px; width: 250px; background-color: white"
  ></SituationPlotting>
</template>
<script>
export default {
  name: 'mainMap'
};
</script>
<script setup>
import { onMounted, onUnmounted, onBeforeUpdate, onBeforeUnmount, nextTick, ref, toRaw } from 'vue';
import * as Cesium from 'cesium';
import * as turf from '@turf/turf';
import { generateKey } from '@/utils';
import {
  getOrCreateCesiumEngineInstance,
  getOrCreateTimeLineCesiumEngineInstance,
  CesiumLayerManager,
  globalConfigResource,
  imglayer,
  veclayer,
  cialayer,
  cvalayer,
  getNoFlyZoneData,
  flyTo,
  CesiumGLBLoader,
  startAnimation,
  stopAnimation,
  createPointEntity,
  CesiumFlight,
  interpolatePoints,
  addImageIcon,
  toCartesian3,
  delCesiumEngineInstance,
  setCameraLookAt,
  projectTerrainOnline,
  projectCustomTerrainProvider,
  getCenterCoordinates,
  addPropertiesToEntity,
  arrayToCartesian3,
  toNumber
} from '@/components/Cesium/libs/cesium';

//SituationPlotting参数与逻辑-start
import FFCesium from '@/views/map/map-fly-manager/realtimeflight/SituationPlotting/FFCesium/core/index.js';
import SituationPlotting from '@/views/map/map-fly-manager/realtimeflight/SituationPlotting/index.vue';
import { plotList, plotDelete } from '@/api/plot/index.js';
import { getFlightDataList } from '@/views/flight-manage/flight-area/flightAreaAxiosHandle';
import { FLIGHTAREATYPE } from '@/views/flight-manage/flight-area/flightAreaHandle';
import { GEOMTYPE } from '@/views/flight-manage/flight-area/flightAreaHandle';
import { getDeptSysSetting } from '@/api/wayline';
let ffCesium = null;
const showFlag = ref(false);
let plotDataArray = [];
const initSituationPlotting = () => {
  const viewerTemp = mapEngine.viewer;
  console.log('initSituationPlotting--viewerTemp', viewerTemp);
  ffCesium = new FFCesium('mainMapContainer', { viewer: viewerTemp });
  //取消双击选中跟踪对象
  var handler = new ffCesium.Cesium.ScreenSpaceEventHandler(ffCesium.viewer.scene.canvas);
  handler.setInputAction(function (event) {
    ffCesium.viewer.trackedEntity = undefined;
  }, ffCesium.Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);

  //删除数据
  findPlotList(nowAlarmId.value);
};
const findPlotList = id => {
  if (!id) {
    return;
  }
  plotDataArray = [];
  plotList(id).then(res => {
    res.forEach(item => {
      item.plotObj = JSON.parse(item.plot_obj);
      plotDataArray.push(item);
    });
    console.log('plotDataArray123', plotDataArray);
    if (lyr_polt.value == true) {
      showFlag.value = true;
    }
  });
};

//SituationPlotting参数与逻辑-end

const props = defineProps({
  // 是否显示时间线
  showTimeline: {
    type: Boolean,
    default: false
  },
  // 是否显示图层控制
  showLayerControl: {
    type: Boolean,
    default: true
  },
  // 是否显示标绘图层
  showPoltLyr: {
    type: Boolean,
    default: false
  }
});
// 是否显示
const showAirPortAreaLyr = ref(true);
// 当前警情ID
const nowAlarmId = ref('');
// 图层控制
const isLayerActive = ref(false);
// 底图控制
const isEarthActive = ref(false);

let mapEngine = null;
// 地图2d/3d切换
const map_type = ref('3D');
// 图层控制显影
const lyr_img = ref(true);
const lyr_vec = ref(false);
const lyr_noFly = ref(false);
const lyr_limitHeight = ref(false);
const lyr_airPortArea = ref(false);
const lyr_limitFlightArea = ref(false);
const lyr_workFlightArea = ref(false);
const lyr_polt = ref(false);
const lyr_fireCar = ref(true);
// 禁飞区数据源
const noFlyZoneLyr = new Cesium.CustomDataSource('noFlyZoneLyr');
// 限高区数据源
const limitHeightLyr = new Cesium.CustomDataSource('limitHeightLyr');

// 自定义限飞区数据源
const limitFlightLyr = new Cesium.CustomDataSource('limitFlightLyr');
// 自定义限飞区数据源
const workFlightLyr = new Cesium.CustomDataSource('workFlightLyr');

// 机场覆盖范围数据源
const airPortAreaLyr = new Cesium.CustomDataSource('airPortAreaLyr');
// 警情相关的消防车、消防站数据源
const fireCarLyr = new Cesium.CustomDataSource('fireCarLyr');
// 测量距离、面积数据源
const drawLayer = new Cesium.CustomDataSource('measureLayer');
// 地图页面定时刷新防止浏览器崩溃
let map_intervalTimer = null;
const initCesium = async () => {
  try {
    if (!props.showTimeline) {
      mapEngine = getOrCreateCesiumEngineInstance('homeMap-fly');
    } else {
      mapEngine = getOrCreateTimeLineCesiumEngineInstance('homeMap-fly');
    }
    // 挂载主地图
    mapEngine?.init('mainMapContainer');
    
    // 确保viewer已经初始化
    if (!mapEngine || !mapEngine.viewer) {
      console.error('Cesium viewer initialization failed');
      return;
    }
    
    // 天地图影像、矢量、影像注记、矢量注记
    try {
      const imageryLayers = mapEngine.viewer.imageryLayers;
      imageryLayers.addImageryProvider(imglayer, 0);
      imageryLayers.addImageryProvider(cialayer, 1);
      imageryLayers.addImageryProvider(veclayer, 2);
      imageryLayers.addImageryProvider(cvalayer, 3);
      // 修改影像图层的gamma值
      imageryLayers.get(0).gamma = 0.9;
      imageryLayers.get(1).gamma = 0.9;
      imageryLayers.get(2).show = lyr_vec.value;
      imageryLayers.get(3).show = lyr_vec.value;
    } catch (err) {
      console.error('Error adding imagery layers:', err);
    }
    
    // 定位到项目区域
    try {
      await getDeptSysSetting({}).then(res => {
        const { wayline_config = {} } = res;
        const targetPoint = {
          lon: Number(wayline_config.longitude) || 0,
          lat: Number(wayline_config.latitude) || 0,
          height: Number(wayline_config.height) || 0
        };
        setCameraLookAt(mapEngine.viewer, {
          lon: Number(targetPoint.lon),
          lat: Number(targetPoint.lat),
          height: Number(0),
          offsetH: 500,
          distance: 1800
        });
      });
    } catch (err) {
      console.error('Error setting camera position:', err);
      // 设置默认位置
      setCameraLookAt(mapEngine.viewer, {
        lon: 0,
        lat: 0,
        height: 0,
        offsetH: 500,
        distance: 1800
      });
    }

    try {
      // 禁飞区、限高区添加到Viewer中
      noFlyZoneLyr.show = lyr_noFly.value;
      limitHeightLyr.show = lyr_limitHeight.value;
      mapEngine.viewer.dataSources.add(noFlyZoneLyr);
      mapEngine.viewer.dataSources.add(limitHeightLyr);
      // 自定义限飞区、作业区
      limitFlightLyr.show = lyr_limitFlightArea.value;
      mapEngine.viewer.dataSources.add(limitFlightLyr);
      workFlightLyr.show = lyr_workFlightArea.value;
      mapEngine.viewer.dataSources.add(workFlightLyr);

      // 机场覆盖范围添加到Viewer中
      let _airPortAreaLyr = mapEngine.viewer.dataSources.getByName('airPortAreaLyr')[0];
      if (!_airPortAreaLyr) {
        airPortAreaLyr.show = lyr_airPortArea.value;
        mapEngine.viewer.dataSources.add(airPortAreaLyr);
      }

      // 添加警情相关的消防车、消防站数据图层
      fireCarLyr.show = lyr_fireCar.value;
      mapEngine.viewer.dataSources.add(fireCarLyr);
      // 添加测量距离、面积图层
      mapEngine.viewer.dataSources.add(drawLayer);
    } catch (err) {
      console.error('Error adding data sources:', err);
    }
    
    // 加载禁飞区、限高区数据
    try {
      loadTerrain();
      loadNoFlyZoneData();
      // 加载自定义限飞区和工作区数据
      loadManMadeNoFlyZoneData();
    } catch (err) {
      console.error('Error loading terrain or zone data:', err);
    }

    // 定时刷新防止浏览器崩溃
    map_intervalTimer = setInterval(() => {
      try {
        const event = new MouseEvent('mousemove', {
          bubbles: true,
          cancelable: true,
          view: window
        });
        mapEngine.viewer.container.dispatchEvent(event);
      } catch (err) {
        console.error('Error in map refresh interval:', err);
      }
    }, 60000);
  } catch (err) {
    console.error('Error initializing Cesium:', err);
  }
};

// 加载禁飞区、限高区数据
const loadNoFlyZoneData = async () => {
  try {
    const noFlyZoneData = await getNoFlyZoneData();
    if (!noFlyZoneData || !Array.isArray(noFlyZoneData)) {
      console.error('Invalid no-fly zone data:', noFlyZoneData);
      return;
    }
    
    noFlyZoneData.forEach(item => {
      const sub_areas = item.sub_areas;
      if (sub_areas != null && Array.isArray(sub_areas)) {
        sub_areas.forEach((childItem, index) => {
          if (childItem.polygon_points != null && Array.isArray(childItem.polygon_points)) {
            try {
              const hierarchyPolygon = [];
              childItem.polygon_points.forEach(points => {
                if (!Array.isArray(points) || points.length === 0) {
                  return;
                }
                
                const cartesianPoints = points.map(point => {
                  if (!Array.isArray(point) || point.length < 2) {
                    return null;
                  }
                  const longitude = point[0];
                  const latitude = point[1];
                  if (!isFinite(longitude) || !isFinite(latitude)) {
                    return null;
                  }
                  return Cesium.Cartesian3.fromDegrees(longitude, latitude);
                }).filter(point => point !== null);
                
                if (cartesianPoints.length > 2) {
                  hierarchyPolygon.push(cartesianPoints);
                }
              });
              
              if (hierarchyPolygon.length > 0 && hierarchyPolygon[0].length > 2) {
                const itemColor = childItem.color;
                // 区分禁飞区、限高区
                if (itemColor === '#979797') {
                  limitHeightLyr.entities.add({
                    id: `limitHeight_${item.area_id}_${index}`,
                    polygon: {
                      hierarchy: new Cesium.PolygonHierarchy(hierarchyPolygon[0]),
                      material: Cesium.Color.fromCssColorString(itemColor).withAlpha(0.4)
                    }
                  });
                } else if (itemColor === '#DE4329') {
                  noFlyZoneLyr.entities.add({
                    id: `noFlyZone_${item.area_id}_${index}`,
                    polygon: {
                      hierarchy: new Cesium.PolygonHierarchy(hierarchyPolygon[0]),
                      material: Cesium.Color.fromCssColorString(itemColor).withAlpha(0.4)
                    }
                  });
                }
              }
            } catch (err) {
              console.error('Error creating polygon for no-fly zone:', err);
            }
          }
        });
      }
    });
  } catch (err) {
    console.error('Error in loadNoFlyZoneData:', err);
  }
};

// 添加地形
const loadTerrain = () => {
  try {
    // 默认地形
    const onlineTerrainProvider = Cesium.createWorldTerrain({
      requestWaterMask: false,
      requestVertexNormals: true
    });
    if (projectTerrainOnline) {
      mapEngine.viewer.terrainProvider = onlineTerrainProvider;
    } else {
      mapEngine.viewer.terrainProvider = projectCustomTerrainProvider || customTerrainProvider || onlineTerrainProvider;
    }
  } catch (err) {
    console.error('Error loading terrain:', err);
    // 如果加载地形失败，使用默认的椭球体地形
    mapEngine.viewer.terrainProvider = new Cesium.EllipsoidTerrainProvider();
  }
};

// 加载自定义限飞区、作业区数据
const loadManMadeNoFlyZoneData = async () => {
  let list = [];
  getFlightDataList().then(res => {
    if (!res || res.length === 0) {
      return;
    }
    list = res;
    list.forEach(item => {
      if (item.type === FLIGHTAREATYPE.NFZ) {
        item.content.properties.color = '#FF4500';
      } else if (item.type === FLIGHTAREATYPE.DFENCE) {
        item.content.properties.color = '#2DFFDC';
      }
      if (item.type === FLIGHTAREATYPE.DFENCE) {
        if (item.content.geometry.type === GEOMTYPE.CIRCLE || item.content.geometry.type === 'Circle') {
          if (item.status) {
            // 创建圆形
            let circleEntity = createCircle(item);
            workFlightLyr.entities.add(circleEntity);
          }
        } else if (item.content.geometry.type === GEOMTYPE.POLYGON || item.content.geometry.type === 'Polygon') {
          if (item.status) {
            let polygonEntity = createPolygon(item);
            workFlightLyr.entities.add(polygonEntity);
          }
        }
      } else if (item.type === FLIGHTAREATYPE.NFZ) {
        if (item.content.geometry.type === GEOMTYPE.CIRCLE || item.content.geometry.type === 'Circle') {
          if (item.status) {
            // 创建圆形
            let circleEntity = createCircle(item);
            limitFlightLyr.entities.add(circleEntity);
          }
        } else if (item.content.geometry.type === GEOMTYPE.POLYGON || item.content.geometry.type === 'Polygon') {
          if (item.status) {
            let polygonEntity = createPolygon(item);
            limitFlightLyr.entities.add(polygonEntity);
          }
        }
      }
    });
  });
};

// 控制地图和图层面板
const toggleActivation = (target, dependent) => {
  if (dependent.value && target.value !== dependent.value) {
    dependent.value = false;
  }
  target.value = !target.value;
};

const controlMapLayer = type => {
  const targets = {
    layer: { target: isLayerActive, dependent: isEarthActive },
    earth: { target: isEarthActive, dependent: isLayerActive }
  };

  if (targets[type]) {
    toggleActivation(targets[type].target, targets[type].dependent);
  }
};

// 图层控制
const handleCheckboxChange = lyr => {
  if (lyr === '天地图影像') {
    const imageryLayers = mapEngine.viewer.imageryLayers;
    lyr_img.value = !lyr_img.value;
    lyr_vec.value = !lyr_vec.value;
    imageryLayers.get(0).show = lyr_img.value;
    imageryLayers.get(1).show = lyr_img.value;
    imageryLayers.get(2).show = lyr_vec.value;
    imageryLayers.get(3).show = lyr_vec.value;
  } else if (lyr === '天地图矢量') {
    const imageryLayers = mapEngine.viewer.imageryLayers;
    lyr_img.value = !lyr_img.value;
    lyr_vec.value = !lyr_vec.value;
    imageryLayers.get(0).show = lyr_img.value;
    imageryLayers.get(1).show = lyr_img.value;
    imageryLayers.get(2).show = lyr_vec.value;
    imageryLayers.get(3).show = lyr_vec.value;
  } else if (lyr === '禁飞区') {
    noFlyZoneLyr.show = lyr_noFly.value;
  } else if (lyr === '限高区') {
    limitHeightLyr.show = lyr_limitHeight.value;
  } else if (lyr === '机场覆盖范围') {
    airPortAreaLyr.show = lyr_airPortArea.value;
  } else if (lyr === '自定义限飞区') {
    limitFlightLyr.show = lyr_limitFlightArea.value;
  } else if (lyr === '自定义作业区') {
    workFlightLyr.show = lyr_workFlightArea.value;
  } else if (lyr === '标绘') {
    if (lyr_polt.value == true) {
      findPlotList(nowAlarmId.value);
    } else {
      showFlag.value = false;
    }
  }
};

// 赋值当前警情ID
function fireAlarmIDJump(alarm_id) {
  console.log('alarm_id', alarm_id);
  nowAlarmId.value = alarm_id;
  showFlag.value = false;
  nextTick(() => {
    findPlotList(nowAlarmId.value);
  });
}

// 是否显示机场覆盖范围选项
const contorlAirPortArea = isShow => {
  showAirPortAreaLyr.value = isShow;
};

//#region 辅助方法
const createCircle = (options = null) => {
  if (!options) {
    return;
  }
  let radius = options.content.geometry.radius;
  const semiMinorAxisCb = new Cesium.CallbackProperty(function () {
    return radius || 1;
  }, false);

  let color = new Cesium.Color.fromCssColorString(options.content.properties.color).withAlpha(0.4);
  // console.log('options', options);
  let entity = new Cesium.Entity({
    id: options.area_id || generateKey(),
    position: new Cesium.Cartesian3.fromDegrees(...options.content.geometry.coordinates, 0),
    name: 'circle',
    label: {
      text: options.name || '',
      font: '14px sans-serif',
      style: Cesium.LabelStyle.FILL_AND_OUTLINE, //FILL  FILL_AND_OUTLINE OUTLINE
      fillColor: Cesium.Color.WHITE,
      showBackground: true, //指定标签后面背景的可见性
      backgroundColor: new Cesium.Color(0.165, 0.165, 0.165, 0.8), // 背景颜色
      backgroundPadding: new Cesium.Cartesian2(6, 6), //指定以像素为单位的水平和垂直背景填充padding
      pixelOffset: new Cesium.Cartesian2(0, -25),
      disableDepthTestDistance: Number.POSITIVE_INFINITY
    },
    ellipse: {
      material: color || Cesium.Color.WHITE.withAlpha(0.4),
      outline: true,
      outlineColor: Cesium.Color.RED,
      outlineWidth: 1, // 是否被提供的材质填充
      fill: true,
      semiMajorAxis: semiMinorAxisCb,
      semiMinorAxis: semiMinorAxisCb,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND //贴地
    }
  });
  return entity;
};

const createPolygon = (options = null) => {
  if (!options) {
    return;
  }
  let positions = toRaw(options.content.geometry.coordinates[0]);
  const cb = new Cesium.CallbackProperty(function () {
    let t = arrayToCartesian3(positions);
    let arrPoint = new Cesium.PolygonHierarchy(t);
    return arrPoint;
  }, false);
  const center = getCenterCoordinates(positions);
  let color = new Cesium.Color.fromCssColorString(options.content.properties.color).withAlpha(0.4);
  // 根据点位置创建面
  let entity = new Cesium.Entity({
    id: options.area_id || generateKey(),
    name: 'polygon',
    position: toCartesian3(center),
    label: {
      text: options.name || '',
      font: '14px sans-serif',
      style: Cesium.LabelStyle.FILL_AND_OUTLINE, //FILL  FILL_AND_OUTLINE OUTLINE
      fillColor: Cesium.Color.WHITE,
      showBackground: true, //指定标签后面背景的可见性
      backgroundColor: new Cesium.Color(0.165, 0.165, 0.165, 0.8), // 背景颜色
      backgroundPadding: new Cesium.Cartesian2(6, 6), //指定以像素为单位的水平和垂直背景填充padding
      pixelOffset: new Cesium.Cartesian2(0, -25),
      disableDepthTestDistance: Number.POSITIVE_INFINITY
    },
    polygon: {
      hierarchy: cb,
      show: true,
      fill: true,
      material: color || Cesium.Color.WHITE.withAlpha(0.4),
      outline: true,
      outlineColor: Cesium.Color.ALICEBLUE,
      outlineWidth: 2,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
    }
  });
  return entity;
};

//#endregion

// 暴露方法给父组件
defineExpose({
  fireAlarmIDJump,
  contorlAirPortArea
});

onMounted(() => {
  try {
    // Use a slightly longer timeout to ensure DOM is fully rendered
    setTimeout(() => {
      try {
        initCesium();
      } catch (err) {
        console.error('Failed to initialize Cesium in timeout callback:', err);
      }
    }, 100);
  } catch (err) {
    console.error('Error in onMounted:', err);
  }
});
onUnmounted(() => {
  try {
    clearInterval(map_intervalTimer);
    delCesiumEngineInstance('homeMap-fly');
  } catch (err) {
    console.error('Error in onUnmounted:', err);
  }
});
onBeforeUnmount(() => {
  // delCesiumEngineInstance('homeMap-fly');
});
</script>

<style lang="scss" scoped>
::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 10px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 10px;
}

::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  // border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #2e90fa;
}

::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  // border-radius: 5px;
  background: #001129;
}

.homemap-container {
  position: relative;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
  bottom: 0px;
  right: 0px;
  margin: 0;
  padding: 0;

  .viewer-ctrl {
    position: fixed;
    width: 32px;
    height: 32px;
    bottom: 135px;
    right: 5px;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 2;
    border-radius: 2px;
    line-height: 32px;
    text-align: center;
    cursor: pointer;
  }
}
.switch-container {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.sc-item {
  width: 38px;
  height: 38px;
  border-radius: 2px;
  background-color: #11253e;
  cursor: pointer;
  &.is-active {
    background-color: #2e90fa;
  }
}

.sc-item-svg {
  font-size: 22px;
  color: #fff;
  margin: 7px;
}

.el-checkbox {
  color: #fff;
}

.lyr-manager {
  position: absolute;
  bottom: 10px;
  right: 60px;
  background-color: #001129;
  z-index: 2;
  border-radius: 2px;
  width: 210px;
  height: 186px;
  &.is-active {
    height: 154px;
  }
}

.lyr-manager-title {
  width: 210px;
  height: 38px;
  background: #11253e;
  font-family: SourceHanSansSC-Bold;
  font-size: 14px;
  color: #ffffff;
  line-height: 36px;
}

.lyr-manager-content {
  padding-left: 10px;
  height: 148px;
  width: 210px;
  overflow-y: auto;
  &.is-active {
    height: 108px;
  }
}

.lmc-row {
  padding-top: 4px;
}

.map-manager {
  position: absolute;
  bottom: 10px;
  right: 60px;
  background-color: #001129;
  z-index: 2;
  border-radius: 2px;
  width: 156px;
  height: 150px;
}

.map-manager-title {
  width: 156px;
  height: 38px;
  background: #11253e;
  font-family: SourceHanSansSC-Bold;
  font-size: 12px;
  color: #ffffff;
  line-height: 36px;
}

.map-manager-content {
  display: flex;
  height: 112px;
  width: 156px;
  .mmc-item {
    width: 50%;
  }

  .mmc-item-title {
    color: #fff;
    font-size: 13px;
    text-align: center;
    padding-top: 10px;
    &.is-active {
      color: #2e90fa;
    }
  }

  .map-img {
    width: 54px;
    height: 54px;
    border-radius: 2px;
    background-image: url('@/assets/gis/img.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-left: 12px;
    margin-top: 12px;
    cursor: pointer;
    &.is-active {
      border: 1px solid #2e90fa;
    }
  }
  .vec-img {
    width: 54px;
    height: 54px;
    border-radius: 2px;
    background-image: url('@/assets/gis/vec.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-left: 12px;
    margin-top: 12px;
    cursor: pointer;
    &.is-active {
      border: 1px solid #2e90fa;
    }
  }
}
</style>
