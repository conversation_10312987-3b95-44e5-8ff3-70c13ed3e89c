import { deviceConfigs } from '@/views/plan/common/devConfigHelper.js';
import { DeviceAdapter } from './DeviceAdapter';
class DeviceAdapterFactory {
  static getAdapter(deviceType) {
    try {
      const config = deviceConfigs[deviceType];
      if (config) {
        return new DeviceAdapter(config);
      } else {
        return null;
      }
    } catch (error) {
      return new Error(`Unsupported device type: ${deviceType}`);
    }
  }
}
export { DeviceAdapterFactory };
