<template>
  <div class="GimbalRotate-wrapper">
    <Slide v-model="dataRef" @changeHandle="onChangeHandle" />
  </div>
</template>
<script>
export default {
  name: 'Zoom'
};
</script>
<script setup>
import { onMounted, onUnmounted, defineExpose, reactive, ref } from 'vue';
import Slide from './components/Slide.vue';
import { updateFrustumWithActionValue, calculateFOV, updateEyeViewerFov } from '../../kmz/hocks';
import { ACTION_ACTUATOR_FUNC } from '@/utils/constants';
import { useEditTrackerStore } from '@/store/modules/dataEditTracker.js';
const editTrackerStore = useEditTrackerStore();
//#region 初始化
const dataInfo = reactive({
  deviceInfo: null,
  action: null,
  deviceType: 'Matrice_3TD'
});
const dataRef = reactive({
  title: '相机变焦倍率',
  unit: 'X',
  acttionType: ACTION_ACTUATOR_FUNC.zoom,
  min: 1,
  max: 56,
  value: 2, // 默认最小单位值
  step: 1 // 设置加减步长
});
//#endregion

//#region 方法

// 数据变更后进行修改
const onChangeHandle = v => {
  // 这里v缩放比 比率为24
  const focalLength = v * 24;
  // 获取动作中的参数进行涉资
  if (dataInfo.action) {
    dataInfo.action.wpml_actionActuatorFuncParam.wpml_focalLength = focalLength;
    dataInfo.action.wpml_actionActuatorFuncParam.wpml_isUseFocalFactor = 0;
    dataInfo.action.wpml_actionActuatorFuncParam.wpml_payloadPositionIndex = 0;
    let sensorSize = 22.5; //12.0823; //
    let fovDeg = calculateFOV(focalLength, sensorSize);
    // // 更新视锥体状态
    const options = {
      action: dataInfo.action,
      actionUuid: dataInfo.action.uuid,
      type: ACTION_ACTUATOR_FUNC.zoom,
      min: dataRef.min,
      max: dataRef.max,
      value: fovDeg,
      sensorSize,
      focalLength,
      fovDeg,
      zoom: Number(v)
    };
    editTrackerStore.dataTracker.markAsModified();
    updateFrustumWithActionValue(options);
  }
};

/**
 * 设置组件数据
 * @param {*} options
 * @param {*} options.actionFuncParam // 动作参数
 * @param {*} options.deviceInfo // 设备信息
 * @param {*} options.action // 动作对象
 */
const setComponentData = options => {
  // 设置数据前先初始组件数据及界面
  const { actionFuncParam, action, deviceInfo } = options;
  //  获取设备信息
  const { droneSubEnumLabel } = deviceInfo;
  // 设备信息及型号
  dataInfo.deviceInfo = deviceInfo;
  dataInfo.action = action;
  dataInfo.deviceType = droneSubEnumLabel;
  const { wpml_focalLength = 0, wpml_isUseFocalFactor = 0, wpml_payloadPositionIndex } = actionFuncParam;
  if (wpml_focalLength > 0) {
    // 设置当前的组件数据
    // dataRef.value = wpml_focalLength / 24;
    try {
      dataRef.value = Number((wpml_focalLength / 24).toFixed(1));
    } catch (error) {
      dataRef.value = 5;
    }
  }
};

const updateComponentValue = options => {
  // 设置数据前先初始组件数据及界面
  const { value = 0 } = options;
  dataRef.value = value;
};
const getComponentData = () => {
  return dataInfo;
};

//#endregion

//#region 对外抛出方法
defineExpose({
  setComponentData,
  getComponentData,
  updateComponentValue
});
//#endregion
//#region 生命周期
onMounted(() => {});
onUnmounted(() => {});
//#endregion
</script>
<style lang="scss" scoped>
.GimbalRotate-wrapper {
}
</style>
