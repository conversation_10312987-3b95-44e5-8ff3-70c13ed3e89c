import request from '@/utils/request';
import { MAP_PATH, API_VERSION, APPLICTION_LAYER } from '../config/index';

// 图层管理API主路径
const BASE_URL = MAP_PATH + API_VERSION + APPLICTION_LAYER;

/**
 * 创建图层
 * @param {Object} data 图层数据
 * @returns {Promise}
 */
export function createLayer(data) {
  return request({
    url: `${BASE_URL}/create`,
    method: 'POST',
    data
  });
}

/**
 * 更新图层
 * @param {Object} data 图层数据
 * @returns {Promise}
 */
export function updateLayer(data) {
  return request({
    url: `${BASE_URL}/update`,
    method: 'PUT',
    data
  });
}

/**
 * 删除图层
 * @param {string|number} id 图层ID
 * @returns {Promise}
 */
export function deleteLayer(id) {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'DELETE'
  });
}

/**
 * 获取图层详情
 * @param {string|number} id 图层ID
 * @returns {Promise}
 */
export function getLayerDetail(id) {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'GET'
  });
}

/**
 * 获取图层列表
 * @param {Object} data 查询参数
 * @returns {Promise}
 */
export function getLayerList(data) {
  return request({
    url: `${BASE_URL}/list`,
    method: 'POST',
    data
  });
}

/**
 * 获取所有图层（不分页）
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getAllLayers(params = {}) {
  return request({
    url: `${BASE_URL}/list`,
    method: 'POST',
    data: {
      ...params,
      pageNum: 1,
      pageSize: 1000 // 获取大量数据，用于下拉选择
    }
  });
}
