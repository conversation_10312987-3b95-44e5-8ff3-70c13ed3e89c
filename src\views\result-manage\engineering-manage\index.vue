<!--工程管理-->
<script>
export default {
  name: 'Engineer<PERSON><PERSON>ge'
};
</script>

<script setup>
import { reactive } from 'vue';
import EditDialog from './EditDialog.vue';
import optionData from '@/utils/option-data';
import { useRouter, useRoute } from 'vue-router';
import { getProjectList, deleteProject } from '@/api/achievement/project';
import { authorityShow } from '@/utils/authority';

const editDialogRef = ref(null);
const router = useRouter();
const loading = ref(false);
const total = ref(0);

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  title: ''
});

const dataList = ref([]);
const dialog = reactive({
  visible: false
});
const editDialog = reactive({
  visible: false
});

let formData = reactive({});

/**
 * 查询
 */
function handleQuery() {
  // Calculate the new total pages based on the new page size
  const newTotalPages = Math.ceil(total.value / queryParams.pageSize);
  
  // If current page is greater than new total pages, adjust to the last page
  if (queryParams.pageNum > newTotalPages) {
    queryParams.pageNum = newTotalPages || 1;
  }
  getProjectList({
    title: queryParams.title,
    begin_time: queryParams.begin_time,
    end_time: queryParams.end_time,
    page_num: queryParams.pageNum,
    page_size: queryParams.pageSize
  }).then(data => {
    const { list, pagination } = data;
    dataList.value = list || [];
    total.value = pagination.total;
  });
}
function handleSearch() {
  queryParams.begin_time = queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[0]} 00:00:00` : '',
  queryParams.end_time = queryParams.rangTime?.length === 2 ? `${queryParams.rangTime[1]} 23:59:59` : '',
  queryParams.pageNum = 1;
  handleQuery();
}
/**
 * 重置查询
 */
function resetQuery() {
  queryParams.title = '';
  queryParams.begin_time = '';
  queryParams.end_time = '';
  queryParams.rangTime = '';
  queryParams.pageNum = 1;
  queryParams.pageSize = 10
  handleQuery();
}

//查看工程详情
function viewDetail(row) {
  console.log('我点击了', row);
  router.push({ path: '/map-project', query: { useType: 'projectMap', projectId: row.project_id } });
}

function openEditDialog(row) {
  editDialog.visible = true;
  Object.keys(formData).map(key => {
    delete formData[key];
  });
  if (row) {
    editDialog.title = '编辑工程';
    Object.assign(formData, { ...row });
  } else {
    editDialog.title = '新增工程';
  }
}
/**
 * 删除
 */
function handleDelete(row) {
  ElMessageBox.confirm(`确认后将删除此工程，且无法进行恢复`, '确认删除所选工程？', {
    confirmButtonText: '确定',
    autofocus: false,
    closeOnClickModal: false,
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteProject(row.project_id).then(data => {
      ElMessage.success('删除成功');
      handleQuery();
    });
  });
}

onMounted(() => {
  handleQuery();
});
</script>

<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
          <el-form-item label="" prop="title">
            <el-input
              class="input-serach"
              v-model="queryParams.title"
              placeholder="请输入工程名称"
              clearable
              @keyup.enter="handleSearch"
              maxlength="32"
            />
          </el-form-item>
          <el-form-item label="" prop="keyWord">
            <el-date-picker
              class="input-serach"
              v-model="queryParams.rangTime"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @change="handleSearch"
            />
          </el-form-item>
        </el-form>
      </div>
      <div class="search-btn">
        <el-button type="primary" @click="handleSearch()"><i-ep-search />搜索</el-button>
        <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
      </div>
    </div>
    <el-card shadow="never">
      <template #header>
        <el-button type="primary" @click="openEditDialog()" v-if="authorityShow('createEngineering')"><i-ep-plus />新增工程</el-button>
      </template>
      <el-table highlight-current-row :data="dataList" v-loading="loading" stripe height="540">
        <el-table-column label="序号" align="center" width="80">
          <template #default="scope">
            <span>{{ scope.$index + 1 + queryParams.pageSize * (queryParams.pageNum - 1) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="工程名称" prop="title" show-overflow-tooltip />
        <el-table-column label="创建用户" prop="creator" show-overflow-tooltip />
        <el-table-column label="创建时间" prop="create_time" show-overflow-tooltip />
        <el-table-column fixed="right" label="操作" align="center" width="200" v-if="authorityShow('editEngineering') || authorityShow('deleteEngineering')">
          <template #default="scope">
            <el-button type="primary" link @click.stop="viewDetail(scope.row)" v-if="authorityShow('editEngineering')">编辑</el-button>
            <el-button type="danger" link @click.stop="handleDelete(scope.row)" v-if="authorityShow('deleteEngineering')">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </el-card>

    <EditDialog
      ref="editDialogRef"
      v-model:visible="editDialog.visible"
      :title="editDialog.title"
      :form-data="formData"
      @submit="resetQuery"
    />
  </div>
</template>
<style scoped lang="scss">
.input-serach {
  width: 200px;
}
.flex-center {
  display: flex;
  align-items: center;
  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
}
.search {
  display: flex;
  align-items: center;
  padding: 0 24px;
  .search-form {
    padding-top: 16px;
    flex: 1;
  }
}
</style>
