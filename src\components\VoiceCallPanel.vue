<template>
  <div class="voice-call-panel">
    <div class="call-header">
      <span class="call-title">{{ callTitle }}</span>
      <div class="header-actions">
        <!-- <el-button
          v-if="callState === CALL_STATES.CONNECTED"
          type="primary"
          plain
          round
          size="small"
          @click="showInviteDialog"
        >
          邀请
        </el-button> -->
        <el-button type="info" circle @click="handleEndCall">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
    </div>

    <div class="call-body">
      <div v-if="callState === CALL_STATES.OUTGOING" class="connecting-status">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <span>正在呼叫 {{ remoteUserName }}...</span>
      </div>

      <div v-else-if="callState === CALL_STATES.INCOMING" class="incoming-status">
        <el-icon class="incoming-icon"><Phone /></el-icon>
        <span>{{ remoteUserName }} 邀请您加入通话</span>
        <div class="action-buttons">
          <el-button type="success" @click="handleAnswerCall">接听</el-button>
          <el-button type="danger" @click="handleRejectCall">拒绝</el-button>
        </div>
      </div>

      <div v-else-if="callState === CALL_STATES.CONNECTED" class="connected-status">
        <!-- <div class="connection-status-bar">
          <span>通话时长: {{ formattedDuration }}</span>
          <el-button size="small" @click="checkAudio" :icon="Microphone" circle title="检查音频"></el-button>
          <el-button size="small" @click="runAudioTroubleshoot" :icon="Setting" circle title="音频问题排查"></el-button>
        </div> -->

        <div class="participants-grid">
          <!-- Local user card -->
          <div class="user-card local-user">
            <div class="user-avatar-container">
              <div class="user-avatar">{{ localUserInitial }}</div>
              <div class="audio-indicator active">
                <el-icon><Microphone /></el-icon>
              </div>
            </div>
            <div class="user-info">
              <div class="user-name">{{ localUserName }} (我)</div>
              <div class="user-status">通话中</div>
            </div>
          </div>

          <!-- Remote participants -->
          <div
            v-for="(participant, index) in displayParticipants"
            :key="participant.user_id"
            class="user-card remote-user"
          >
            <div class="user-avatar-container">
              <div class="user-avatar">{{ getInitial(participant.user_name) }}</div>
              <div class="audio-indicator">
                <el-icon><Microphone /></el-icon>
              </div>
            </div>
            <div class="user-info">
              <div class="user-name">{{ participant.user_name }}</div>
              <div class="user-status">{{ hasParticipantAudio(participant) ? '通话中' : '已连接' }}</div>
            </div>
          </div>

          <!-- Empty slots for future participants -->
          <div v-for="i in emptySlots" :key="`empty-${i}`" class="user-card empty-slot" @click="showInviteDialog">
            <div class="empty-avatar">
              <el-icon><Plus /></el-icon>
            </div>
            <div class="empty-text">邀请用户加入</div>
          </div>
        </div>
      </div>

      <div v-else-if="callState === CALL_STATES.ERROR" class="error-status">
        <el-icon class="error-icon"><CircleClose /></el-icon>
        <span>{{ errorMessage || '连接失败，请重试' }}</span>
      </div>
    </div>

    <div class="call-footer">
      <span class="call-duration">{{ formattedDuration }}</span>
    </div>

    <!-- 邀请用户对话框 -->
    <el-dialog v-model="inviteDialogVisible" title="邀请用户加入通话" width="500px">
      <div class="invite-dialog-body">
        <el-input v-model="searchQuery" placeholder="搜索用户..." prefix-icon="Search" clearable />

        <div v-if="isLoadingUsers" class="loading-users">
          <el-icon class="loading-icon"><Loading /></el-icon>
          <span>加载用户列表中...</span>
        </div>
        <div v-else-if="availableUsers.length === 0" class="empty-users">
          <el-empty description="没有找到可邀请的用户" />
        </div>
        <div v-else class="user-list">
          <div
            v-for="user in availableUsers"
            :key="user.user_id"
            class="user-list-item"
            :class="{ 'already-invited': isUserInCall(user.user_id) }"
            @click="() => handleInvite(user)"
          >
            <div class="user-item-avatar">{{ getInitial(user.user_name) }}</div>
            <div class="user-item-info">
              <div class="user-item-name">{{ user.user_name }}</div>
              <div class="user-item-status">{{ getUserStatus(user) }}</div>
            </div>
            <el-button
              type="primary"
              size="small"
              :disabled="isUserInCall(user.user_id)"
              :loading="invitingUsers[user.user_id]"
            >
              {{ isUserInCall(user.user_id) ? '已在通话中' : '邀请' }}
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  remoteUsers,
  isJoined,
  isSecure,
  isConnecting,
  forcePlayRemoteAudio,
  troubleshootAudio
} from '@/utils/agoraService';
import {
  callState,
  currentChannel,
  remoteUser,
  errorMessage,
  callDuration,
  CALL_STATES,
  answerCall,
  rejectCall,
  endCall,
  getFormattedDuration,
  participants,
  inviteToCall
} from '@/utils/callService';
import { CircleClose, Close, Loading, Microphone, Phone, Plus, Search, Setting } from '@element-plus/icons-vue';
import { listOnlineUser } from '@/api/system/user';

const props = defineProps({
  localUser: {
    type: Object,
    default: () => ({ username: '用户', user_id: null })
  }
});

const emit = defineEmits(['call-ended']);

// 邀请对话框状态
const inviteDialogVisible = ref(false);
const searchQuery = ref('');
const availableUsers = ref([]);
const isLoadingUsers = ref(false);
// 正在邀请的用户
const invitingUsers = ref({});

// 显示的参与者（包括本地和远程）
const displayParticipants = computed(() => {
  if (participants.value.length === 0 && remoteUser.value) {
    // 如果参与者列表为空但有远程用户，使用远程用户作为参与者
    return [remoteUser.value];
  }
  return participants.value.filter(p => p.user_id !== props.localUser.user_id);
});

// 空位数量（最多显示6个用户，包括本地用户）
const emptySlots = computed(() => {
  const maxUsers = 5; // 本地用户 + 5个远程用户
  const currentCount = displayParticipants.value.length;
  return Math.max(0, maxUsers - currentCount);
});

// 根据状态确定通话标题
const callTitle = computed(() => {
  switch (callState.value) {
    case CALL_STATES.OUTGOING:
      return '正在呼叫...';
    case CALL_STATES.INCOMING:
      return '来电...';
    case CALL_STATES.CONNECTED:
      return '通话中';
    case CALL_STATES.ERROR:
      return '通话错误';
    default:
      return '语音通话';
  }
});

// 格式化用户名称和首字母
const localUserName = computed(() => props.localUser?.username || '用户');
const localUserInitial = computed(() => {
  const name = props.localUser?.username || '用户';
  return name.charAt(0).toUpperCase();
});

const remoteUserName = computed(() => remoteUser.value?.username || '对方');

// 格式化通话时长
const formattedDuration = computed(() => getFormattedDuration());

// 获取用户首字母
const getInitial = name => {
  return (name || '用户').charAt(0).toUpperCase();
};

// 检查参与者是否有音频
const hasParticipantAudio = participant => {
  const uid = participant.user_id;
  const remoteUser = remoteUsers.value.find(u => u.uid === uid);
  return remoteUser && remoteUser.hasAudio;
};

// 检查用户是否已经在通话中
const isUserInCall = userId => {
  return (
    // participants.value.some(p => p.user_id === userId) || (remoteUser.value && remoteUser.value.user_id === userId)
    participants.value.some(p => p.user_id === userId)
  );
};

// 获取用户状态
const getUserStatus = user => {
  if (isUserInCall(user.user_id)) {
    return '已在通话中';
  }

  switch (user.status) {
    case 0:
      return '离线';
    case 1:
      return 'PC端在线';
    case 2:
      return '忙碌';
    case 3:
      return '双端在线';
    default:
      return '状态未知';
  }
};

// 在组件挂载时预加载音频
onMounted(() => {
  if (!isSecure.value) {
    ElMessage.error('语音通话需要在安全上下文中运行，请使用HTTPS协议或localhost访问');
  }
});

// 处理通话操作
const handleEndCall = async () => {
  // 检查当前用户是否为创建者
  const currentUser = participants.value.find(p => p.user_id === props.localUser.user_id);
  const isCreator = currentUser?.isCreator || false;
  
  // 根据是否为创建者显示不同的确认信息
  const confirmTitle = isCreator ? '结束通话' : '离开通话';
  const confirmMessage = isCreator 
    ? '您是通话发起者，结束通话将断开所有参与者的连接，确定要结束通话吗？'
    : '确定要离开当前通话吗？';
  
  try {
    await ElMessageBox.confirm(
      confirmMessage,
      confirmTitle,
      {
        confirmButtonText: isCreator ? '结束通话' : '离开通话',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'call-end-confirm-dialog'
      }
    );
    
    // 用户确认后执行结束通话
    await endCall();
    emit('call-ended');
  } catch (error) {
    // 用户取消操作，不做任何处理
    console.log('用户取消了结束通话操作');
  }
};

const handleAnswerCall = async () => {
  try {
    const success = await answerCall();
    if (!success) {
      emit('call-ended');
    }
  } catch (error) {
    console.error('接听通话失败:', error);
    ElMessage.error('接听通话失败');
  }
};

const handleRejectCall = async () => {
  await rejectCall();
  emit('call-ended');
};

// 显示邀请对话框
const showInviteDialog = () => {
  inviteDialogVisible.value = true;
  fetchAvailableUsers();
};

// 获取可邀请的在线用户列表
const fetchAvailableUsers = async () => {
  try {
    isLoadingUsers.value = true;
    const response = await listOnlineUser({
      pageNum: 1,
      pageSize: 50,
      keyword: searchQuery.value
    });

    if (response && response.list) {
      console.log('在线用户-----', response);
      const activeUsers = response.list.filter(user => user.online_status !== 0);
      availableUsers.value = activeUsers.map(user => ({
        user_id: user.user_id,
        user_name: user.username,
        status: user.online_status
      }));
      console.log('过滤后的在线用户:', availableUsers.value);
    }
  } catch (error) {
    console.error('获取可邀请用户列表失败:', error);
    ElMessage.error('获取用户列表失败');
  } finally {
    isLoadingUsers.value = false;
  }
};

// 监听搜索词变化，触发搜索
watch(
  searchQuery,
  debounce(() => {
    fetchAvailableUsers();
  }, 500)
);

// 防抖函数
function debounce(fn, delay) {
  let timer = null;
  return function () {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      fn.apply(this, arguments);
    }, delay);
  };
}

// 处理邀请用户
const handleInvite = async user => {
  if (isUserInCall(user.user_id)) {
    return;
  }

  try {
    invitingUsers.value = { ...invitingUsers.value, [user.user_id]: true };
    await inviteToCall(user);
  } catch (error) {
    console.error('邀请用户失败:', error);
  } finally {
    invitingUsers.value = { ...invitingUsers.value, [user.user_id]: false };
  }
};

// 检查是否在安全上下文中运行
onMounted(() => {
  if (!isSecure.value) {
    ElMessage.error('语音通话需要在安全上下文中运行，请使用HTTPS协议或localhost访问');
  }
});

// 组件卸载时清理资源
onBeforeUnmount(() => {});

// 在组件中添加音频检查函数
const checkAudio = async () => {
  console.log('正在检查音频...');
  await forcePlayRemoteAudio();
  ElMessage.info('已尝试强制播放远程音频，请检查是否能听到声音');
};

const runAudioTroubleshoot = () => {
  troubleshootAudio();
  ElMessage.info('音频问题排查已在控制台输出，请按F12查看');
};
</script>

<style scoped>
.voice-call-panel {
  background-color: #f5f7fa;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  width: 600px;
  height: 400px;
  min-height: 200px;
}

.call-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #1890ff;
  border-radius: 8px 8px 0 0;
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.call-title {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}

.call-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  overflow-y: auto;
}

.connecting-status,
.incoming-status,
.error-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.loading-icon {
  font-size: 32px;
  color: #409eff;
  animation: rotate 1.5s linear infinite;
}

.incoming-icon {
  font-size: 32px;
  color: #67c23a;
  animation: pulse 1.5s infinite;
}

.error-icon {
  font-size: 32px;
  color: #f56c6c;
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

/* Connection status bar */
.connection-status-bar {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  gap: 12px;
  background-color: #f0f9eb;
  padding: 8px;
  border-radius: 4px;
  color: #67c23a;
}

/* Participants grid layout */
.connected-status {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.participants-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  width: 100%;
}

.user-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
  height: 120px;
  position: relative;
}

.user-card:hover {
  transform: translateY(-3px);
}

.local-user {
  border-top: 4px solid #409eff;
}

.remote-user {
  border-top: 4px solid #67c23a;
}

.empty-slot {
  border: 2px dashed #c0c4cc;
  background-color: #f9f9f9;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  cursor: pointer;
  box-shadow: none;
}

.empty-slot:hover {
  background-color: #f0f2f5;
  border-color: #a6a9ad;
}

.empty-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #f0f2f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #909399;
  margin-bottom: 8px;
}

.empty-text {
  font-size: 14px;
  color: #909399;
}

.user-avatar-container {
  position: relative;
  margin-bottom: 8px;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #409eff;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 600;
}

.remote-user .user-avatar {
  background-color: #67c23a;
}

.audio-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: #67c23a;
  animation: pulse 2s infinite;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  border: 2px solid white;
}

.user-info {
  text-align: center;
  width: 100%;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-status {
  font-size: 12px;
  color: #606266;
}

.call-footer {
  padding: 12px 16px;
  text-align: center;
  border-top: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.call-duration {
  font-size: 14px;
  color: #606266;
  font-weight: 600;
}

.call-info {
  font-size: 14px;
  color: #909399;
}

/* Invite dialog styles */
.invite-dialog-body {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 4px;
}

.user-list {
  max-height: 230px;
  overflow-y: auto;
  border: 1px solid #344054;
  border-radius: 8px;
  background-color: #1f2f49;
}

.user-list-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #344054;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.user-list-item:last-child {
  border-bottom: none;
}

.user-list-item:hover {
  background-color: #2a3f5f;
}

.user-list-item.already-invited {
  background-color: #2d3748;
  cursor: not-allowed;
  opacity: 0.6;
}

.user-list-item.already-invited:hover {
  transform: none;
  background-color: #2d3748;
}

.user-item-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 600;
  margin-right: 16px;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.user-item-avatar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.user-list-item:hover .user-item-avatar::before {
  transform: translateX(100%);
}

.user-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-item-name {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.user-item-status {
  font-size: 13px;
  color: #94a3b8;
  display: flex;
  align-items: center;
  gap: 6px;
}

.user-item-status::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #67c23a;
  animation: pulse 2s infinite;
}

.loading-users {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 40px;
  color: #94a3b8;
}

.loading-users .loading-icon {
  font-size: 36px;
  color: #409eff;
}

.empty-users {
  padding: 40px;
  text-align: center;
}

.empty-users :deep(.el-empty__description) {
  color: #94a3b8;
}

/* 搜索框样式优化 */
.invite-dialog-body :deep(.el-input) {
  .el-input__wrapper {
    background-color: #1f2f49;
    border: 1px solid #344054;
    border-radius: 8px;
    box-shadow: none;
    transition: all 0.3s ease;
  }

  .el-input__wrapper:hover {
    border-color: #409eff;
  }

  .el-input__wrapper.is-focus {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }

  .el-input__inner {
    color: #ffffff;
    background-color: transparent;
  }

  .el-input__inner::placeholder {
    color: #94a3b8;
  }

  .el-input__prefix-inner {
    color: #94a3b8;
  }
}

/* 邀请按钮样式优化 */
.user-list-item .el-button {
  min-width: 80px;
  border-radius: 20px;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.user-list-item .el-button--primary {
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.user-list-item .el-button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
}

.user-list-item .el-button--primary:disabled {
  background: #475569;
  color: #94a3b8;
  transform: none;
  box-shadow: none;
}

/* 滚动条样式 */
.user-list::-webkit-scrollbar {
  width: 6px;
}

.user-list::-webkit-scrollbar-track {
  background: #1f2f49;
  border-radius: 3px;
}

.user-list::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 3px;
  transition: background 0.3s ease;
}

.user-list::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* 对话框整体样式优化 */
:deep(.el-dialog) {
  background: linear-gradient(135deg, #1f2f49 0%, #2a3f5f 100%);
  border: 1px solid #344054;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

:deep(.el-dialog__header) {
  background: linear-gradient(135deg, #2a3f5f 0%, #1f2f49 100%);
  border-bottom: 1px solid #344054;
  border-radius: 12px 12px 0 0;
  padding: 20px 24px;
}

:deep(.el-dialog__title) {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
}

:deep(.el-dialog__headerbtn) {
  top: 20px;
  right: 20px;
}

:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: #94a3b8;
  font-size: 18px;
  transition: all 0.3s ease;
}

:deep(.el-dialog__headerbtn .el-dialog__close:hover) {
  color: #ffffff;
  transform: rotate(90deg);
}

:deep(.el-dialog__body) {
  padding: 24px;
  background: transparent;
}

/* 响应式优化 */
@media (max-width: 640px) {
  .invite-dialog-body {
    gap: 16px;
  }

  .user-list {
    max-height: 280px;
  }

  .user-list-item {
    padding: 12px;
  }

  .user-item-avatar {
    width: 40px;
    height: 40px;
    font-size: 18px;
    margin-right: 12px;
  }

  .user-item-name {
    font-size: 14px;
  }

  .user-item-status {
    font-size: 12px;
  }
}

/* 确认对话框样式 */
:deep(.call-end-confirm-dialog) {
  .el-message-box__header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border-radius: 8px 8px 0 0;
    padding: 20px 24px;
  }
  
  .el-message-box__title {
    color: white;
    font-weight: 600;
  }
  
  .el-message-box__content {
    padding: 24px;
    background: #f8f9fa;
  }
  
  .el-message-box__message {
    color: #495057;
    font-size: 14px;
    line-height: 1.6;
  }
  
  .el-message-box__btns {
    padding: 16px 24px;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
  }
  
  .el-button--primary {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    border: none;
    border-radius: 6px;
    font-weight: 500;
  }
  
  .el-button--primary:hover {
    background: linear-gradient(135deg, #ff5252 0%, #d63031 100%);
  }
  
  .el-button--default {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    color: #6c757d;
  }
  
  .el-button--default:hover {
    background: #e9ecef;
    border-color: #adb5bd;
  }
}
</style>
