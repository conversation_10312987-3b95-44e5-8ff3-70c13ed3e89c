<template>
  <el-dropdown trigger="click" class="cursor-pointer">
    <div class="flex justify-center items-center mx-3">
      <img v-show="showAvatar" src="@/assets/wrj_logo.png" class="w-[40px] h-[40px] rounded-8" />
      <span class="username" :title="userStore.userData.username">{{ userStore.userData.username }}</span>
      <SvgIcon icon-class="arrowDown" class="arrow-down w-3 h-3" />
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <!-- <router-link to="/profile">
          <el-dropdown-item>{{ $t('navbar.userCenter') }}</el-dropdown-item>
        </router-link>
        <router-link to="/tenant-information">
          <el-dropdown-item>{{ $t('navbar.tenantCenter') }}</el-dropdown-item>
        </router-link>
        <router-link to="">
          <el-dropdown-item @click="handleChangePwd"
            >{{ $t('navbar.changePwd') }}
          </el-dropdown-item>
        </router-link> -->
        <el-dropdown-item  @click="gotoBackstage" v-if="authorityShow('dashboardBtn') && showDashboard">
          {{ '后台管理' }}
        </el-dropdown-item>
        <el-dropdown-item  @click="logout">
          {{ $t('navbar.logout') }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
  <ChangePwd ref="changePwdRef" />
</template>

<script>
export default {
  name: 'avatarArea'
};
</script>

<script setup>
import i18n from '@/lang';
import { useRoute, useRouter } from 'vue-router';
import CustomMessageBox from '@/components/CustomMsgBox/msgBox';

import { useTagsViewStore } from '@/store/modules/tagsView.js';
import { useUserStore } from '@/store/modules/user.js';
import { useSettingsStore } from '@/store/modules/settings';
import ChangePwd from '../ChangePwd/index.vue';
import { authorityShow } from '@/utils/authority';
import { onMounted } from 'vue';
const path = ref('/dashboard')
const changePwdRef = ref('changePwdRef');
const settingStore = useSettingsStore();

const tagsViewStore = useTagsViewStore();
const userStore = useUserStore();

const route = useRoute();
const router = useRouter();

const props = defineProps({
  // 登录类型
  showAvatar: {
    type: Boolean,
    default: true
  },
  showDashboard: {
    type: Boolean,
    default: false
  }
});

function gotoBackstage () {
  const url = router.resolve(path.value).href;
  window.open(url, '_blank');
}

// 注销
function logout() {
  CustomMessageBox.confirm(
    i18n.global.t('退出系统吗'),
    i18n.global.t('page.tips'),
    {
      confirmButtonText: i18n.global.t('page.confirm'),
      cancelButtonText: i18n.global.t('page.Cancel'),
      type: 'warning'
    }
  ).then(() => {
    userStore
      .logout()
      .then(() => {
        if (settingStore.tagsView) {
          tagsViewStore.delAllViews();
        }
      })
      .then(() => {
        router.push(`/login?redirect=${route.fullPath}`);
      });
  });
}

const handleChangePwd = () => {
  changePwdRef.value.handleOpen();
};

onUnmounted(() => {
  window.$bus.off('dialogBeforeClose');
});
</script>

<style lang="scss">
.username {
  margin: 0 10px 0 10px;
  color: #fff;
}
.arrow-down {
  color: #fff;
}
</style>
