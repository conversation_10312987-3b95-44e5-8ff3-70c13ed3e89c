<template>
  <template v-if="device?.source === '3' && styleProps.width !== 0">
    <YsPlayer 
      ref="itemRefs"
      :width="computedWidth"
      :height="computedHeight"
      :videoName="device?.cameraName"
      :playerId="`playerBox_${Date.now()}`" 
      :deviceNo="device?.channel"
      showTitle
      showClose
      from="videoWall"
      :deviceData="device"
    />
  </template>
  <template v-else-if="device && device?.source !== '3'">
    <JessibucaPro 
      ref="itemRefs"
      :show-blow-up="showBlowUp"
      :showTitle="showTitle"
      :index="i"
      :playerId="playerId"
      :dronePara="device"
      :showClose="showClose"
    />
  </template>
  <template v-else>
    <div class="vEmpty">
      <div class="titleName">无视频</div>
    </div>
  </template>
</template>

<script setup>
import { computed ,ref,defineExpose,watch} from 'vue';

const props = defineProps({
  index: Number,
  i: Number,
  device: Object,
  styleProps: Object,
  playerId: String,
  showBlowUp: Boolean,
  showTitle: Boolean,
  cellCount: Number,
  showClose: Boolean
});

defineExpose({
  onVideoStart,
  onVideoStop,
  onH5VideoPlay
})
const itemRefs = ref(null);

const computedWidth = computed(() => props.styleProps?.width || 0);
const computedHeight = computed(() => {
  if (props.cellCount === 6 && props.i === 2) {
    return (props.styleProps?.height / 2).toFixed(0);
  }
  return props.styleProps?.height || 0;
});
watch(
  () => props.device,
  newVal => {
    console.log('device==newVal', newVal);
  }
);
//播放
function onVideoStart(device) {
  if (device.droneSelected) {
    itemRefs.value && itemRefs.value.onStart(device)
  }
}
//暂停
function onVideoStop(device) {
  itemRefs.value && itemRefs.value.onStop();
}

function onH5VideoPlay(device){
  itemRefs.value && itemRefs.value.h5VideoPlay(device)
}

</script>
<style scoped lang="scss"> 
.vEmpty {
  width: 100%;
  height: 100%;
  position: relative;
  border: 1px solid #101010;
  background: url('../../../../assets/empty.png') no-repeat center center;
  z-index: 999;
  background-color: #262c33;
}
.titleName {
  position: absolute;
  color: white;
  font-size: 14px;
  top: 10px;
  left: 10px;
}
</style>