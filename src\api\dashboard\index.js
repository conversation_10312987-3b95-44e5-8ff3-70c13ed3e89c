import request from '@/utils/request';
import { MAIN_PATH, CONTROL_PATH ,APPLICTION_DEVICE, API_VERSION } from '../config/index';
import { useUserStoreHook } from '@/store/modules/user';

// 主路径
const BASE_URL = MAIN_PATH + API_VERSION + APPLICTION_DEVICE;
const CONTROL_URL=CONTROL_PATH+ API_VERSION + APPLICTION_DEVICE;


//获取无人机相关统计信息
export function getUavInfo() {
  return request({
    url: `/statistics/api/v1/device/stats`,
    method: 'get',
  });
}

//获取飞行统计
export function getFlyInfo() {
  return request({
    url: `/statistics/api/v1/flight/stats`,
    method: 'get',
  });
}

//获取飞手飞行次数排名前五
export function getPilotTop5() {
  return request({
    url: `/statistics/api/v1/flight/topPilotsByFlightCount`,
    method: 'get',
  });
}

//获取飞手飞行距离排名前五
export function getPilotDistanceTop5() {
  return request({
    url: `/statistics/api/v1/flight/topPilotsByFlightDistance`,
    method: 'get',
  });
}

//获取各机型飞行次数
export function getFlyNum() {
  return request({
    url: `/statistics/api/v1/flight/device-type-stats`,
    method: 'get',
  });
}

//获取总的飞行里程
export function getFlyMileage() {
  return request({
    url: `/statistics/api/v1/flight/distance/lastSevenDays`,
    method: 'get',
  });
}