<script>
export default { name: '<PERSON>armList' };
</script>

<script setup>
import optionData from '@/utils/option-data';
import { onMounted, ref, toRaw } from 'vue';
import { getJJTaskPage } from '@/api/task';
import { EBizCode } from '@/utils/constants';
import { useConnectWebSocket } from '@/hooks/useConnectWebSocket';
import { ElMessage } from 'element-plus';
const emit = defineEmits(['OneClickDelivery','LocationJump']);
const dataList = ref([]);
const total = ref(0);
const alarmId = ref('')
const queryParams = reactive({
  pageNum: 1,
  pageSize: 20,
	time_type: 0
});

useConnectWebSocket(payload => {
  if (!payload) {
    return;
  }
  switch (payload.biz_code) {
    // 刷新警情列表
    case EBizCode.AlarmTaskReport: {
      // 刷新警情列表
			ElMessage.success(`接收到新的警情，${payload.data.alarm_name}`)
			queryParams.pageNum = 1
      handleQuery();
      break;
    }
  }
});

function oneClickDelibery (item) {
	handleQuery();
	emit('OneClickDelivery',item);
}

function locationJump (item) {
	alarmId.value = toRaw(item).alarm_id
	emit('LocationJump',item)
}

onMounted(()=>{
	handleQuery();
})

/**
 * 查询
 */
 function handleQuery() {
  getJJTaskPage({
		time_type: queryParams.time_type,
    page: queryParams.pageNum,
    page_size: queryParams.pageSize
  }).then(data => {
    const { list, pagination } = data;
    dataList.value = list || [];
    total.value = pagination.total;
  });
}

function load () {
	const totalPage = Math.ceil(total.value / 20)
	if(queryParams.pageNum >= totalPage || dataList.value.length < 20) {
		return;
	}
	queryParams.pageNum += 1
	
	getJJTaskPage({
		time_type: queryParams.time_type,
    page: queryParams.pageNum,
    page_size: queryParams.pageSize
  }).then(data => {
    const { list } = data;
		let arr = toRaw(dataList.value).concat(list)
		dataList.value = arr
  });
}

function changeTimeType (val) {
	queryParams.pageNum = 1
	queryParams.time_type = val
	handleQuery();
}
</script>

<template>
  <div class="alarm-title flex">
		<div>
			<svg-icon icon-class="title" style="margin-right: 4" />
			<span>警情列表</span>
		</div>
		<div>
			<el-select
				v-model="queryParams.time_type"
				placeholder="请选择"
				size="small"
				style="width: 80px"
				@change="changeTimeType"
			>
				<el-option
					v-for="item in optionData.alarmOptions"
					:key="item.value"
					:label="item.label"
					:value="item.value"
				/>
			</el-select>
		</div>
	</div>
	<div class="alarm-ul" v-infinite-scroll="load">
		<!-- <el-scrollbar height="490px" > -->
			<div :class="alarmId == item.alarm_id ? ['alarm-item','currentColor'] : 'alarm-item'" v-for="item in dataList" :key="item.id" @click="locationJump(item)">
				<div class="flex">
					<div class="flex">
						<div class="danger">{{ item.alarm_level_name || ''}}</div>
						<div class="danger alarm-type" style="margin: 0 8px;">{{ item.alarm_type }}</div>
						<div :class="item.current_status_name == '已下发' ? 'green' : 'orange'">{{ item.current_status_name || ''}}</div>
					</div>
					<div class="alarm-time">
						{{ item.create_time || '' }}
					</div>
				</div>
				<div class="alarm-address">
					{{ item.alarm_name || '福建省厦门市集美区新兵街道时代焦点厦门影视传媒-拍摄基地拍摄记得拍摄基地拍摄记得拍摄基地拍摄记得' }}
				</div>
				<el-button type="primary" size="small" @click="oneClickDelibery(item)">一键下发</el-button>
			</div>
		<!-- </el-scrollbar> -->
	</div>
</template>

<style lang="scss" scoped>
::-webkit-scrollbar {
  width: 10px;  /* 设置滚动条的宽度 */
}
::-webkit-scrollbar-thumb {
  background-color: #bebebe;  /* 设置滚动条滑块的背景色 */
}
:deep(.el-input__wrapper) {
	// background-color: #11253e;
	border: none;
	color: #fff;
}
.alarm-title {
	height: 38px;
	line-height: 38px;
	background: #11253E;
	color: #fff;
	font-family: SourceHanSansSC-Bold;
	font-size: 14px;
	text-align: left;
	font-weight: 700;
	border-bottom: 1px solid #344054;
	padding: 0 8px;
}
:deep(.el-input__wrapper) {
	background-color: #11253E;
	border: 1px solid #475467;
	border-color: #475467 !important;
	box-shadow: none;
	color: #fff;
}
:deep(.el-input__inner) {
	color: #98A2B3;
}
:deep(.el-input-number__decrease)  {
	background-color: #11253E;
	color: #fff;
	box-shadow: none;
	border-left: 1px solid #475467;
}
:deep(.el-input.is-disabled .el-input__wrapper) {
	background-color: #11253E;
	box-shadow: none;
	color: #606266;
}
:deep(.el-select) {
	--el-select-border-color-hover: none;
	--el-select-disabled-border:none;
}
:deep(.el-input-number.is-controls-right .el-input-number__increase) {
	border-bottom: 1px solid #475467;
	border-left: 1px solid #475467;
	box-shadow: none;
}
:deep(.el-input-number.is-controls-right .el-input-number__decrease) {
	border-left: 1px solid #475467;
	box-shadow: none;
}
:deep(.el-input-number__increase) {
	color: #fff;
	box-shadow: none;
	border-left: 1px solid #475467;
	background-color: #11253E;
}
.alarm-type {
	max-width: 85px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.danger,
.green,
.orange {
	font-family: SourceHanSansSC-Regular;
	font-size: 12px;
	color: #F97066;
	text-align: center;
	line-height: 24px;
	height: 24px;
	font-weight: 400;
	background:  rgba($color: #912828, $alpha: 0.5);
	border-radius: 2px;
	padding: 0 4px;
	cursor: default;
}
.green {
	background: rgba(42,139,125,0.30);
	color: #39BFA4;
}
.orange {
	background: rgb(253, 176, 34,0.30);
	color: #FDB022;
}
.currentColor {
	background: #175091 !important;
}
.alarm-ul {
	background: #001129;
	// height: 490px;
	height: 98%;
	padding-bottom: 20px;
	overflow: auto;
	.alarm-item {
		min-height: 90px;
		background: #11253E;
		color: #fff;
		margin-bottom: 12px;
		padding: 8px;
		.alarm-time {
			font-family: SourceHanSansSC-Regular;
			font-size: 12px;
			color: #98A2B3;
			text-align: right;
			line-height: 20px;
			font-weight: 400;
		}
		.alarm-address{
			font-family: SourceHanSansSC-Regular;
			font-size: 14px;
			color: #FFFFFF;
			text-align: justify;
			line-height: 22px;
			font-weight: 400;
			margin-top: 8px;
			overflow: hidden;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			text-overflow: ellipsis;
			white-space: normal;
			margin-bottom: 8px;
		}
	}
	.alarm-item:last-child {
		// margin-bottom: 140px;
	}
}
.flex {
	display: flex;
	justify-content: space-between;
}
</style>
