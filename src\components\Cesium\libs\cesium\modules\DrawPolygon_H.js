import * as Cesium from 'cesium';
import { debounce } from 'lodash';
import { toCartesian3, toCartesian2, pickFeatureFromScreen, generateNumberId, generateUUID } from '../index';
const POINT_TYPE = {
  MIDPOINT: 'MIDPOINT',
  ENDPOINT: 'ENDPOINT',
  DELETE: 'DELETE'
};
// 绘制组件
class DrawPolygon {
  static instance = null;
  static getInstance(viewer) {
    if (!DrawPolygon.instance) {
      DrawPolygon.instance = new DrawPolygon(viewer);
    }
    return DrawPolygon.instance;
  }

  constructor(viewer) {
    // this.dispose()
    this.viewer = viewer;
    this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas); // 选中的经纬度
    this.modifyHandler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
    this.polygon = null;
    this.polyline = null;
    this.state = 0; // 1为新增 2为编辑 0已经完成绘制
    this.endPointPositionMap = new Map(); // []; // 点坐标集合 c3 集合
    this.midPositionMap = new Map(); // 点坐标集合 c3 集合 中间点
    this.config = {
      pointColor: Cesium.Color.WHEAT,
      pointSize: 5,
      lineWidth: 5.0,
      lineColor: Cesium.Color.GOLD,
      polygonColor: Cesium.Color.WHITE.withAlpha(0.4)
    };
    this.fun = null; // 回调
    // 鼠标相关操作和选中的操作
    this.mouse_down = false;
    this.selectedPointEntity = null;
    // 临时的鼠标点
    this.tempMouseEntity = null;
    this.deleteLabel = null; // 标注删除的
    this.willDeletePointId = null; // 将要被删除的点id
    this.alreadyPolygonSelected = null; // 标记可以删除面
    this.state = 1;
    this.polygonHierarchyProperty = null;
  }

  init() {
    let that = this;
    that.handler = new Cesium.ScreenSpaceEventHandler(that.viewer.scene.canvas); // 选中的经纬度
    that.modifyHandler = new Cesium.ScreenSpaceEventHandler(that.viewer.scene.canvas);
    that.endPointPositionMap = new Map();
    that.midPositionMap = new Map();
    // 临时的鼠标点
    that.tempMouseEntity = null;
    // 鼠标相关操作和选中的操作
    that.mouse_down = false;
    that.selectedPointEntity = null;
    that.deleteLabel = null; // 标注删除的
    that.willDeletePointId = null; // 将要被删除的点id
    that.state = 1;
    // 开始监听执行方法
    that.modify();
  }
  drawing(fn) {
    let timer = null;
    let that = this;
    that.init();
    that.fun = fn;
    // that.polygonHierarchyProperty = new Cesium.CallbackProperty(that.polygonHierarchyCallback, false);
    that.polygonHierarchyProperty = that.createPolygonHierarchyProperty(that.endPointPositionMap);

    // 监听鼠标移动
    that.handler.setInputAction(function (evt) {
      try {
        timer && clearTimeout(timer);
        if (that.state === 0) return;
        if (!that.polygon) return;
        let cartesian3 = that.px2Catesian3(evt.position, that.viewer);
        if (!cartesian3) return;
        // 去掉被中选的
        if (that.selectedPointEntity) {
          that.viewer.entities.remove(that.selectedPointEntity);
          that.selectedPointEntity = null;
        }
        // 绘制最后一个点
        let lastPoint = that.createPointGeom({
          position: cartesian3,
          type: POINT_TYPE.ENDPOINT
        });
        // 检查 endPointPositionMap 中是否已经存在该点实体的坐标
        const hasobj = that.endPointPositionMap.has(lastPoint.id);
        // 如果坐标已经存在,则删除它
        if (hasobj) {
          that.endPointPositionMap.delete(lastPoint.id);
        }
        // 将新的点实体坐标添加到 endPointPositionMap 中
        that.endPointPositionMap.set(lastPoint.id, {
          id: lastPoint.id,
          index: that.endPointPositionMap.size,
          type: POINT_TYPE.ENDPOINT,
          position: cartesian3,
          entity: lastPoint
        });

        // 最后要去掉临时的鼠标移动的坐标值
        if (that.tempMouseEntity) {
          // 这里要去掉临时的鼠标坐标点
          const hasTemp = that.endPointPositionMap.has(that.tempMouseEntity.id);
          // 如果端点坐标已存在,则删除它
          if (hasTemp) {
            that.endPointPositionMap.delete(that.tempMouseEntity.id);
          }
          that.viewer.entities.remove(that.tempMouseEntity);
          that.tempMouseEntity = null;
        }

        // 绘制点之间的中间点
        that.createMiddlePoints();
        that.polygon.hierarchy = that.polygonHierarchyCallback;
        that.callback();
      } catch (error) {
      } finally {
        that.forbidWorld(false);
        that.state = 0;
        document.body.style.cursor = 'default';
      }
    }, Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);
    // 监听鼠标移动
    that.handler.setInputAction(function (e) {
      try {
        clearTimeout(timer);
        timer = window.setTimeout(function () {
          console.log('LEFT_CLICK', e);
          if (that.state === 0) return;
          let cartesian3 = that.px2Catesian3(e.position, that.viewer);
          if (!cartesian3) return;
          let pointEntity = that.createPointGeom({
            position: cartesian3,
            type: POINT_TYPE.ENDPOINT
          });
          that.endPointPositionMap.set(pointEntity.id, {
            id: pointEntity.id,
            index: that.endPointPositionMap.size,
            position: cartesian3,
            type: POINT_TYPE.ENDPOINT,
            entity: pointEntity
          });
          that.forbidWorld(true);
          document.body.style.cursor = 'crosshair';
        }, 100);
      } catch (error) {}
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    // 监听鼠标移动
    that.handler.setInputAction(function (e) {
      try {
        if (that.state === 0) return;
        if (that.endPointPositionMap.size < 1) return;
        let cartesian3 = that.px2Catesian3(e.endPosition, that.viewer);
        if (!cartesian3) return;

        if (that.tempMouseEntity) {
          that.tempMouseEntity.position = cartesian3;
        } else {
          that.tempMouseEntity = that.createPointGeom({
            position: cartesian3,
            color: Cesium.Color.fromCssColorString('#08D0B9'),
            type: POINT_TYPE.ENDPOINT
          });
        }
        if (that.endPointPositionMap.size == 2) {
          if (!that.polygon) {
            that.polygon = that.createPolygonGeom();
          }
        }
        if (that.polygon) {
          // 从 endPointPositionMap 中获取临时鼠标实体的端点坐标
          const existingPosition = that.endPointPositionMap.get(that.tempMouseEntity.id);
          // 如果端点坐标已存在,则删除它
          if (existingPosition) {
            that.endPointPositionMap.delete(that.tempMouseEntity.id);
          }
          // 将新的端点坐标添加到 endPointPositionMap 中
          that.endPointPositionMap.set(that.tempMouseEntity.id, {
            id: that.tempMouseEntity.id,
            index: that.endPointPositionMap.size,
            position: cartesian3,
            type: POINT_TYPE.ENDPOINT,
            entity: that.tempMouseEntity
          });
        }
        document.body.style.cursor = 'crosshair';
      } catch (error) {}
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    // 监听鼠标移动
    that.handler.setInputAction(function (evt) {
      try {
        if (that.state === 0) return;
        if (!that.polygon) return;
        let cartesian3 = that.px2Catesian3(evt.position, that.viewer);
        if (!cartesian3) return;
        // 去掉被中选的
        if (that.selectedPointEntity) {
          that.viewer.entities.remove(that.selectedPointEntity);
          that.selectedPointEntity = null;
        }
        // 绘制最后一个点
        let lastPoint = that.createPointGeom({
          position: cartesian3,
          type: POINT_TYPE.ENDPOINT
        });
        // 检查 endPointPositionMap 中是否已经存在该点实体的坐标
        const hasobj = that.endPointPositionMap.has(lastPoint.id);
        // 如果坐标已经存在,则删除它
        if (hasobj) {
          that.endPointPositionMap.delete(lastPoint.id);
        }
        // 将新的点实体坐标添加到 endPointPositionMap 中
        that.endPointPositionMap.set(lastPoint.id, {
          id: lastPoint.id,
          index: that.endPointPositionMap.size,
          type: POINT_TYPE.ENDPOINT,
          position: cartesian3,
          entity: lastPoint
        });

        // 最后要去掉临时的鼠标移动的坐标值
        if (that.tempMouseEntity) {
          // 这里要去掉临时的鼠标坐标点
          const hasTemp = that.endPointPositionMap.has(that.tempMouseEntity.id);
          // 如果端点坐标已存在,则删除它
          if (hasTemp) {
            that.endPointPositionMap.delete(that.tempMouseEntity.id);
          }
          that.viewer.entities.remove(that.tempMouseEntity);
          that.tempMouseEntity = null;
        }
        // 绘制点之间的中间点
        that.createMiddlePoints();
        that.callback();
        // if (that.fun && typeof that.fun === 'function') {
        //   let positions = Array.from(that.endPointPositionMap.values()) || [];
        //   // 提取每个对象的 position 属性
        //   let extractedPositions = positions.map(positionObj => positionObj.position);
        //   setTimeout(() => {
        //     that.fun({
        //       positions: extractedPositions
        //     });
        //   }, 200);
        // }
      } catch (error) {
      } finally {
        that.forbidWorld(false);
        that.state = 0;
        document.body.style.cursor = 'default';
      }
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    // 监听鼠标移动
    that.handler.setInputAction(function () {
      if (that.state === 0) return;
      that.forbidWorld(false);
    }, Cesium.ScreenSpaceEventType.LEFT_UP);
  }
  createPolygonHierarchyProperty(newEndPointPositionMap) {
    let polygonHierarchyCallback = endPointPositionMap => {
      try {
        let positionArr = [];
        for (const key of endPointPositionMap.keys()) {
          let obj = endPointPositionMap.get(key);
          positionArr.push(obj.position);
        }
        console.info('11111111111:', endPointPositionMap, positionArr);
        return new Cesium.PolygonHierarchy(positionArr, []);
      } catch (error) {
        console.error('Error creating PolygonHierarchy:', error);
        return [];
      }
    };

    let cb = new Cesium.CallbackProperty(time => {
      return polygonHierarchyCallback(newEndPointPositionMap);
    }, false);
    return cb;
  }
  modify() {
    let that = this;
    // 监听鼠标移动
    that.modifyHandler.setInputAction(function (e) {
      if (that.state !== 0) return;
      // 这里检查 鼠标是否在点上 在端点上或者在中间点上、如果点击不是端点和中间点则返回、检查鼠标是否在点上、端点上或者中间点上
      let pickedObject = pickFeatureFromScreen(that.viewer, toCartesian2([e.position.x, e.position.y]));
      const id = pickedObject.id;
      const type = pickedObject.type;
      if (type !== 'Entity' || !pickedObject.entity) {
        that.selectedPointEntity = null;
        return;
      }
      const geomType = pickedObject.entity.pointType || null;
      if (!geomType) {
        that.selectedPointEntity = null;
        return;
      }
      if (geomType === 'POLYGON') {
        return;
      } else if (geomType === POINT_TYPE.ENDPOINT) {
        that.selectedPointEntity = pickedObject.entity;
      } else if (geomType === POINT_TYPE.MIDPOINT) {
        // 如果点击选中的是中间点图标 获取该中间点图标相关信息 构建端点
        that.selectedPointEntity = pickedObject.entity;
        // 将中间点变为端点
        let id = pickedObject.id;
        if (that.midPositionMap) {
          let obj = {
            type: POINT_TYPE.ENDPOINT,
            previewPointIndex: 0,
            position: null,
            id: '',
            entity: null
          };
          for (const [key, value] of that.midPositionMap.entries()) {
            if (id === key) {
              obj.entity = value.entity;
              obj.position = value.position;
              obj.id = value.id;
              obj.previewPointIndex = value.previewPointIndex;
              break;
            }
          }

          let previewPointIndex = obj.previewPointIndex;
          // 获取点坐标信息  需要插入到位置 previewPointIndex 后面一位
          let pointEntity = that.createPointGeom({
            position: obj.position,
            type: POINT_TYPE.ENDPOINT
          });

          // 将 pointEntity 插入到 previewPointIndex 后面的位置
          let positions = Array.from(that.endPointPositionMap.values());
          positions.splice(previewPointIndex + 1, 0, {
            id: pointEntity.id,
            index: positions.length,
            position: obj.position,
            type: POINT_TYPE.ENDPOINT,
            entity: pointEntity
          });
          // 将数组转回 Map 对象
          let newEndPointPositionMap = new Map();
          positions.forEach((pos, index) => {
            newEndPointPositionMap.set(pos.id, {
              ...pos,
              index: index
            });
          });
          that.endPointPositionMap = newEndPointPositionMap;
        }
        // 绘制点之间的中间点
        that.createMiddlePoints();
      } else {
        return;
      }
      // 如果在中间点上 将该点添加到端点中
      that.forbidWorld(true);
      that.mouse_down = true;
      // 如果在端点上 这里改变状态 在移动的时候移动端点重新构建面和点数据
    }, Cesium.ScreenSpaceEventType.LEFT_DOWN);
    // 监听鼠标抬起
    that.modifyHandler.setInputAction(function () {
      if (that.state !== 0) return;
      that.mouse_down = false;
      that.selectedPointEntity = null;
      // that.polygon.hierarchy = that.polygonHierarchyCallback;
      that.forbidWorld(false);
    }, Cesium.ScreenSpaceEventType.LEFT_UP);
    // 监听鼠标移动
    that.modifyHandler.setInputAction(function (e) {
      try {
        if (that.state !== 0) return; // 状态不为 就是还没有绘制好
        if (!that.polygon) return; // 没有面
        // 鼠标抬起了就不移动了
        if (!that.mouse_down) {
          // 检查鼠标是否在点上，在端点上或者在中间点上
          let pickedObject = pickFeatureFromScreen(that.viewer, toCartesian2([e.endPosition.x, e.endPosition.y]));
          if (pickedObject.type === 'Entity' && pickedObject.entity) {
            const geomType = pickedObject.entity.pointType || null;
            if (!that.alreadyPolygonSelected) {
              document.body.style.cursor = geomType && geomType === 'POLYGON' ? 'pointer' : 'default';
            } else {
              if (geomType && geomType === 'POLYGON') {
                document.body.style.cursor = 'pointer';
                // 修改多边形的颜色为浅绿色
                that.polygon.polygon.material = Cesium.Color.fromCssColorString('#98FB98').withAlpha(0.6); // 浅绿色半透明
              } else {
                document.body.style.cursor = 'default';
              }
            }
          } else {
            document.body.style.cursor = 'default';
          }
          return;
        }

        let cartesian3 = that.px2Catesian3(e.endPosition, that.viewer);
        if (!cartesian3) return;
        // 重新设置点位置
        if (!that.selectedPointEntity) return;
        // 移动被选中的点
        let id = that.selectedPointEntity.id;
        const entity = that.viewer.entities.getById(id);
        if (entity) {
          entity.position.setValue(cartesian3);
        }
        // 根据id 找到对应坐标
        let curSelectedPoint = that.endPointPositionMap.get(id);
        if (curSelectedPoint) {
          curSelectedPoint.position = cartesian3;
          curSelectedPoint.id = id;
          that.endPointPositionMap.set(id, curSelectedPoint);
        }
        // 绘制点之间的中间点
        that.createMiddlePoints();
        if (that.fun && typeof that.fun === 'function') {
          let positions = Array.from(that.endPointPositionMap.values()) || [];
          let extractedPositions = positions.map(positionObj => positionObj.position);
          let debounce_fun = debounce(function () {
            that.fun({
              positions: extractedPositions
            });
          }, 500);
          debounce_fun();
        }
        // 修改数组点坐标值 / 找到 cartesian3 最近的点 并将值替换
      } catch (error) {}
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    // 监听鼠标右键
    that.modifyHandler.setInputAction(function (e) {
      try {
        if (that.state !== 0) return; // 状态不为 就是还没有绘制好
        if (!that.polygon) return; // 没有面
        let cartesian3 = that.px2Catesian3(e.position, that.viewer);
        if (!cartesian3) return;
        // 这里检查 鼠标是否在点上 在端点上或者在中间点上、如果点击不是端点和中间点则返回、检查鼠标是否在点上、端点上或者中间点上
        let pickedObject = pickFeatureFromScreen(that.viewer, toCartesian2([e.position.x, e.position.y]));
        const id = pickedObject.id;
        const type = pickedObject.type;
        if (type !== 'Entity' || !pickedObject.entity) {
          return;
        }
        const geomType = pickedObject.entity.pointType || null;
        if (!geomType) {
          return;
        }
        if (geomType === POINT_TYPE.ENDPOINT) {
          that.willDeletePointId = id;
          that.selectedPointEntity = pickedObject.entity;
          // 这里如果是端点则添加个label 提示是否删除
          that.deleteLabel = that.addLabelGraphic(cartesian3, '删除端点');
        } else {
          that.deleteLabel && that.viewer.entities.remove(that.deleteLabel);
          that.deleteLabel = null;
          that.selectedPointEntity = null;
          that.willDeletePointId = null;
        }
      } catch (error) {}
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    // 监听鼠标左键点击
    that.modifyHandler.setInputAction(function (e) {
      try {
        if (that.state !== 0) return; // 状态不为 就是还没有绘制好
        if (!that.polygon) return; // 没有面
        let cartesian3 = that.px2Catesian3(e.position, that.viewer);
        if (!cartesian3) return;
        // 这里检查 鼠标是否在点上 在端点上或者在中间点上、如果点击不是端点和中间点则返回、检查鼠标是否在点上、端点上或者中间点上
        let pickedObject = pickFeatureFromScreen(that.viewer, toCartesian2([e.position.x, e.position.y]));
        const type = pickedObject.type;
        if (type !== 'Entity' || !pickedObject.entity) {
          that.deleteLabel && that.viewer.entities.remove(that.deleteLabel);
          that.deleteLabel = null;
          that.polygon.polygon.material = Cesium.Color.fromCssColorString('#87CEEB').withAlpha(0.6); // 浅绿色半透明
          that.alreadyPolygonSelected = false;
          document.body.style.cursor = 'default';
          return;
        }
        const geomType = pickedObject.entity.pointType || null;
        if (!geomType) {
          that.deleteLabel && that.viewer.entities.remove(that.deleteLabel);
          that.deleteLabel = null;
          return;
        }

        if (geomType === 'POLYGON') {
          document.body.style.cursor = 'pointer';
          // 修改多边形的颜色为浅绿色
          that.polygon.polygon.material = Cesium.Color.fromCssColorString('#98FB98').withAlpha(0.6); // 浅绿色半透明
          that.alreadyPolygonSelected = true;
        } else {
          document.body.style.cursor = 'default';
          that.polygon.polygon.material = Cesium.Color.fromCssColorString('#87CEEB').withAlpha(0.6); // 浅绿色半透明
          that.alreadyPolygonSelected = false;
        }

        if (geomType !== POINT_TYPE.DELETE && that.deleteLabel) {
          that.deleteLabel && that.viewer.entities.remove(that.deleteLabel);
          that.deleteLabel = null;
        } else {
          if (!that.willDeletePointId) {
            return;
          }
          const hasTemp = that.endPointPositionMap.has(that.willDeletePointId);
          // 如果端点坐标已存在,则删除它
          if (hasTemp) {
            let delEntity = that.endPointPositionMap.get(that.willDeletePointId) || null;
            delEntity && that.viewer.entities.remove(delEntity.entity);
            that.endPointPositionMap.delete(that.willDeletePointId);
          }
          // 绘制点之间的中间点
          that.createMiddlePoints();
          that.deleteLabel && that.viewer.entities.remove(that.deleteLabel);
          that.deleteLabel = null;
          that.willDeletePointId = null;
          that.selectedPointEntity = null;
          that.callback();
        }
      } catch (error) {}
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    document.addEventListener('keydown', function (event) {
      if (event.key === 'Delete') {
        // 在这里处理删除键按下的逻辑
        that.handleDeleteKeyPress();
      }
    });
  }
  // 绘制
  createPolygon(positions = [], fn) {
    let that = this;
    that.init();
    that.fun = fn;
    that.forbidWorld(false);
    that.state = 0;
    document.body.style.cursor = 'default';
    that.endPointPositionMap.clear();
    // that.polygonHierarchyProperty = new Cesium.CallbackProperty(that.polygonHierarchyCallback, false);
    that.polygonHierarchyProperty = that.createPolygonHierarchyProperty(that.endPointPositionMap);
    // 根据点位置创建面
    positions.forEach(position => {
      let point = toCartesian3([position.lng, position.lat, position.height]);
      // 绘制最后一个点
      let lastPoint = that.createPointGeom({
        position: point,
        type: POINT_TYPE.ENDPOINT
      });
      // 将新的点实体坐标添加到 endPointPositionMap 中
      that.endPointPositionMap.set(lastPoint.id, {
        id: lastPoint.id,
        index: that.endPointPositionMap.size,
        type: POINT_TYPE.ENDPOINT,
        position: point,
        entity: lastPoint
      });
    });
    if (!that.polygon) {
      that.polygon = that.createPolygonGeom();
    }
  }
  /**
   * 移除面对象
   */
  handleDeleteKeyPress() {
    if (this.alreadyPolygonSelected) {
      this.clearEntity();
      this.alreadyPolygonSelected = false; // 重置已选标志
      document.body.style.cursor = 'default'; // 重置光标样式
      this.state = 1;
      this.polygonHierarchyProperty = new Cesium.CallbackProperty(this.polygonHierarchyCallback, false);
      this.fun({
        clear: true
      });
    }
  }
  /**
   * 回调 绘制完成后、修改完成后进行数据返回
   */
  callback() {
    if (this.fun && typeof this.fun === 'function') {
      let positions = Array.from(this.endPointPositionMap.values()) || [];
      let extractedPositions = positions.map(positionObj => positionObj.position);
      setTimeout(() => {
        this.fun({
          positions: extractedPositions
        });
      }, 200);
    }
  }
  createPointGeom(options) {
    try {
      const { position = null, id = null, color = null, type = null } = options;
      if (position) {
        // 这里是点集合数组
        let p = this.viewer.entities.add({
          id: id || generateUUID(),
          name: 'Point',
          position: toCartesian3(position),
          // point: {
          //   color: color || Cesium.Color.fromCssColorString("#FF6B00"), // 蓝色
          //   pixelSize: 10, // 大小
          //   outlineColor: Cesium.Color.WHITE, // 白色边框
          //   outlineWidth: 2, // 边框宽度
          //   disableDepthTestDistance: Number.POSITIVE_INFINITY, // 点总是在最上层
          //   heightReference: Cesium.HeightReference.CLAMP_TO_GROUND, // 贴地显示
          // },
          billboard: {
            image: new URL('../../../../../assets/plan/wrj/endpoint.png', import.meta.url).href,
            width: 14,
            height: 14,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            disableDepthTestDistance: Number.POSITIVE_INFINITY // 点总是在最上层
          },
          pointType: type || POINT_TYPE.ENDPOINT
        });
        return p;
      }
    } catch (error) {}
  }
  createMiddlePointGeom(options) {
    const { position = null, id = null, type = null } = options;
    // 这里是点集合数组
    let midp = this.viewer.entities.add({
      id: id || generateUUID(),
      name: 'Point' + id,
      position: toCartesian3(position),
      billboard: {
        image: new URL('../../../../../assets/plan/wrj/middlepoint.png', import.meta.url).href,
        width: 10,
        height: 10,
        disableDepthTestDistance: Number.POSITIVE_INFINITY, // 点总是在最上层
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      },
      pointType: type || POINT_TYPE.MIDPOINT
    });
    return midp;
  }
  createPolygonGeom() {
    try {
      // 这里一定要用到 that
      let that = this;
      return that.viewer.entities.add({
        id: generateUUID(),
        name: 'Polygon',
        polygon: {
          hierarchy: that.polygonHierarchyProperty,
          show: true,
          fill: true,
          material: Cesium.Color.fromCssColorString('#87CEEB').withAlpha(0.6), // 浅蓝色半透明
          outline: true,
          outlineColor: Cesium.Color.ALICEBLUE,
          outlineWidth: 2,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        },
        pointType: 'POLYGON'
      });
    } catch (error) {
      console.log('that.error', error);
    }
  }
  // 获取动态变更的坐标值
  getPolygonHierarchyProperty() {
    return new Cesium.CallbackProperty(() => {
      try {
        let positionArr = [];
        for (const key of this.endPointPositionMap.keys()) {
          let obj = this.endPointPositionMap.get(key);
          positionArr.push(obj.position);
        }
        return new Cesium.PolygonHierarchy(positionArr, []);
      } catch (error) {
        return [];
      }
    }, false);
  }

  //objMap =
  polygonHierarchyCallback = positionMap => {
    try {
      let positionArr = [];
      for (const key of positionMap.keys()) {
        let obj = positionMap.get(key);
        positionArr.push(obj.position);
      }
      console.log('222222222222');
      return new Cesium.PolygonHierarchy(positionArr, []);
    } catch (error) {
      return [];
    }
  };

  polygonHierarchyCallback2 = () => {
    try {
      let positionArr = [];
      for (const key of this.endPointPositionMap.keys()) {
        let obj = this.endPointPositionMap.get(key);
        positionArr.push(obj.position);
      }
      return new Cesium.PolygonHierarchy(positionArr, []);
    } catch (error) {
      return [];
    }
  };

  ccreatePolyLineGeom() {
    try {
      // 这里一定要用到 that
      let that = this;
      return this.viewer.entities.add({
        id: 'POLYLINE_' + generateNumberId(),
        name: 'Polyline',
        polyline: {
          endPointPositionMap: new Cesium.CallbackProperty(function (time, result) {
            return that.endPointPositionMap;
          }, false),
          width: 10,
          material: new Cesium.PolylineGlowMaterialProperty({
            glowPower: 0.2,
            taperPower: 0.5,
            color: Cesium.Color.CORNFLOWERBLUE
          }),
          loop: true
        }
      });
    } catch (error) {
      console.log('that.error', error);
    }
  }
  /**
   * 创建中间点
   */
  createMiddlePoints() {
    if (this.midPositionMap) {
      for (const [key, value] of this.midPositionMap.entries()) {
        this.viewer.entities.remove(value?.entity);
      }
    }
    let midPositions = [];
    // 执行创建中点点图标
    let points = Array.from(this.endPointPositionMap.values()) || [];
    if (points && points.length > 0) {
      for (let i = 0; i < points.length - 1; i++) {
        let midPointInfoStruct = {
          type: POINT_TYPE.MIDPOINT,
          previewPointIndex: 0,
          position: null,
          id: null
        };
        const point1 = points[i];
        const point2 = points[i + 1];
        const middlePoint = Cesium.Cartesian3.midpoint(point1.position, point2.position, new Cesium.Cartesian3());

        midPointInfoStruct.previewPointIndex = i;
        midPointInfoStruct.position = middlePoint;
        midPositions.push(midPointInfoStruct);
      }

      // 添加末尾点和第一个点的中间点
      const firstPoint = points[0];
      const lastPoint = points[points.length - 1];
      const firstLastMiddlePoint = Cesium.Cartesian3.midpoint(
        firstPoint.position,
        lastPoint.position,
        new Cesium.Cartesian3()
      );
      midPositions.push({
        type: POINT_TYPE.MIDPOINT,
        previewPointIndex: points.length - 1,
        position: firstLastMiddlePoint
      });

      (midPositions || []).forEach(p => {
        let mp = this.createMiddlePointGeom({
          position: p.position,
          type: POINT_TYPE.MIDPOINT
        });
        let mpInfo = {
          type: POINT_TYPE.MIDPOINT,
          previewPointIndex: p.previewPointIndex,
          position: p.position,
          id: mp.id,
          entity: mp
        };
        this.midPositionMap.set(mp.id, mpInfo);
      });
    }
  }
  /**
   * 添加标签
   * @param position c3 坐标对象
   * @param text
   */
  addLabelGraphic(position, text) {
    return this.viewer.entities.add(
      new Cesium.Entity({
        id: generateUUID(),
        position: position,
        label: {
          text: text,
          font: '14px sans-serif',
          style: Cesium.LabelStyle.FILL_AND_OUTLINE, //FILL  FILL_AND_OUTLINE OUTLINE
          fillColor: Cesium.Color.WHITE,
          showBackground: true, //指定标签后面背景的可见性
          backgroundColor: new Cesium.Color(0.165, 0.165, 0.165, 0.8), // 背景颜色
          backgroundPadding: new Cesium.Cartesian2(6, 6), //指定以像素为单位的水平和垂直背景填充padding
          pixelOffset: new Cesium.Cartesian2(0, -25),
          disableDepthTestDistance: Number.POSITIVE_INFINITY
        },
        pointType: POINT_TYPE.DELETE
      })
    );
  }
  // 清空已绘制线或面
  clearEntity = () => {
    if (this.polygon) {
      this.viewer.entities.remove(this.polygon);
      this.polygon = null;
    }
    if (this.polyline) {
      this.viewer.entities.remove(this.polyline);
      this.polyline = null;
    }
    if (this.tempMouseEntity) {
      this.viewer.entities.remove(this.tempMouseEntity);
      this.tempMouseEntity = null;
    }

    if (this.midPositionMap && this.midPositionMap.size > 0) {
      let midobjs = Array.from(this.midPositionMap.values()) || [];
      midobjs.forEach(obj => {
        this.viewer.entities.remove(obj?.entity);
      });
      this.midPositionMap.clear();
    }
    if (this.endPointPositionMap && this.endPointPositionMap.size > 0) {
      let objs = Array.from(this.endPointPositionMap.values()) || [];
      objs.forEach(obj => {
        this.viewer.entities.remove(obj?.entity);
      });
      this.endPointPositionMap.clear();
    }
  };
  /**
   * 移除屏幕空间事件监听
   * @param {Cesium.ScreenSpaceEventType} type 屏幕空间事件类型
   */
  removeInputAction() {
    if (this.handler) {
      this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);
      this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_UP);
      this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
      this.handler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
      this.handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      if (!this.handler.isDestroyed()) {
        this.handler = this.handler && this.handler.destroy();
      }
    }
  }
  dispose() {
    this.polygonHierarchyProperty = null; // this.createPolygonHierarchyProperty([]);
    if (this.handler) {
      this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);
      this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_UP);
      this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
      this.handler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
      this.handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      if (!this.handler.isDestroyed()) {
        this.handler = this.handler && this.handler.destroy();
      }
      this.handler = null;
    }
    if (this.modifyHandler) {
      this.modifyHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);
      this.modifyHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_UP);
      this.modifyHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
      this.modifyHandler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
      this.modifyHandler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      if (!this.modifyHandler.isDestroyed()) {
        this.modifyHandler = this.modifyHandler && this.modifyHandler.destroy();
      }
    }
    // 移除delete事件监听
    document.addEventListener('keydown', this.handleDeleteKeyPress);
    this.state = 0;
    this.clearEntity();
    this.endPointPositionMap.clear(); // []; // 点坐标集合 c3 集合
    this.midPositionMap.clear(); // 点坐标集合 c3 集合 中间点
  }

  /**
   * 屏幕坐标转经纬度高程
   * @param {*} evtPoi Catesian2 对象
   * @param {*} viewer 视图
   * @returns
   */
  px2Catesian3(evtPoi, viewer) {
    try {
      const cartesian3 = viewer.scene.globe.pick(viewer.camera.getPickRay(evtPoi), viewer.scene);
      return cartesian3;
    } catch (error) {
      return null;
    }
  }
  forbidWorld(isForbid) {
    this.viewer.scene.screenSpaceCameraController.enableRotate = !isForbid;
    this.viewer.scene.screenSpaceCameraController.enableTilt = !isForbid;
    this.viewer.scene.screenSpaceCameraController.enableTranslate = !isForbid;
    this.viewer.scene.screenSpaceCameraController.enableInputs = !isForbid;
  }
}
export { DrawPolygon };
