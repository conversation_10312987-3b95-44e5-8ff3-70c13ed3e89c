<template>
  <div class="geom-info-wrap" v-show="dialogDataRect.visible">
    <div class="title">设置</div>
    <div class="geom-info-content">
      <el-row :span="24" :gutter="20" class="row-item">
        <el-col :span="6">
          <span> 名称 </span>
        </el-col>
        <el-col :span="18">
          <div>
            <el-input maxlength="20" clearable v-model="dialogDataRect.title" placeholder="请输入区域名称" />
          </div>
        </el-col>
      </el-row>
      <el-row :span="24" :gutter="20" class="row-item">
        <el-col :span="6"> 颜色 </el-col>
        <el-col :span="18">
          <div class="color-box">
            <div class="color" :style="{ background: dialogDataRect.color }"></div>
          </div>
        </el-col>
      </el-row>
      <el-row :span="24" :gutter="20" class="row-item">
        <el-col :span="6"> 数据 </el-col>
        <el-col :span="18">
          <div class="databox">
            <div>面积: {{ parseAreaFloat(dialogDataRect.area) }}</div>
            <div>周长: {{ parseLengthFloat(dialogDataRect.length) }}</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <div class="bottom-box">
      <el-button @click="submit" type="primary">确定</el-button><el-button @click="cancel" type="info">取消</el-button>
    </div>
  </div>
</template>
<script>
export default { name: 'GeomInfoDialog' };
</script>
<script setup>
import { onMounted, watch, onUnmounted } from 'vue';
import {
  dialogDataRect,
  GEOMTYPE,
  COLOR_TYPE_STRING,
  parseLengthFloat,
  parseAreaFloat,
  initDataList,
  editStatue,
  FLIGHTAREATYPE
} from '../flight-area/flightAreaHandle.js';
import {
  addCircleFlightArea,
  editCircleFlightArea,
  editPolygonFlightArea,
  addPolygonFlightArea
} from '../flight-area/flightAreaAxiosHandle';
import * as Cesium from 'cesium';
import { isCircleEdit, cancelCircleEdit } from '@/components/Cesium/libs/cesium/superEdit/CircleEditer';
import { isPolygonEdit, cancelPolygonEdit } from '@/components/Cesium/libs/cesium/superEdit/PolygonEditer';
import {
  cancelPolygonAdd,
  polygonDispose,
  isPolygonCreate
} from '@/components/Cesium/libs/cesium/superEdit/PolygonCreater';
import {
  cancelCircleAdd,
  circleDispose,
  isCircleCreate
} from '@/components/Cesium/libs/cesium/superEdit/CircleCreater';
import { ElMessage, ElMessageBox } from 'element-plus';

const colorRect = {
  NFZ: '#FF4500',
  DFENCE: '#2DFFDC'
};
const colorListRect = reactive({
  colorcss: ''
});
const co = computed(() => {
  return dialogDataRect;
});
//#region  方法
const submit = () => {
  console.log('dialogDataRect', dialogDataRect);
  if (dialogDataRect.title.length === 0) {
    ElMessage.warning('飞行区域名称不能为空！');
    return;
  }
  let action = dialogDataRect.action; // add / edit
  let areaType = dialogDataRect.flightAreaType; // nfz
  let geomType = dialogDataRect.geomInfo?.geomType || dialogDataRect.geomType; //
  editStatue.value = false;
  switch (geomType) {
    case GEOMTYPE.CIRCLE:
      if (action === 'add') {
        addCircleFlightArea(dialogDataRect, isSuccess => {
          if (isSuccess) {
            dialogDataRect.visible = false;
            isCircleCreate.value = false;
            circleDispose();
          }
        });
      } else if (action === 'edit') {
        editCircleFlightArea(dialogDataRect, isSuccess => {
          if (isSuccess) {
            dialogDataRect.visible = false;
            isCircleEdit.value = false;
            cancelCircleEdit();
          }
        });
      }
      break;
    case GEOMTYPE.POLYGON:
      if (action === 'add') {
        addPolygonFlightArea(dialogDataRect, isSuccess => {
          if (isSuccess) {
            dialogDataRect.visible = false;
            isPolygonEdit.value = false;
            isPolygonCreate.value = false;
            polygonDispose();
          }
        });
      } else if (action === 'edit') {
        editPolygonFlightArea(dialogDataRect, isSuccess => {
          if (isSuccess) {
            dialogDataRect.visible = false;
            isPolygonEdit.value = false;
            cancelPolygonEdit();
          }
        });
      }
      break;
    default:
      break;
  }
};
const cancel = () => {
  cancelCircleAdd();
  cancelCircleEdit();
  cancelPolygonEdit();
  cancelPolygonAdd();
  editStatue.value = false;
  dialogDataRect.visible = false;
};

// 获取颜色
const getColor = color => {
  try {
    if (color instanceof Cesium.Color) {
      return color;
    } else if (typeof color === 'string') {
      return new Cesium.Color.fromCssColorString(color).withAlpha(0.4);
    }
  } catch (error) {
    return Cesium.Color.GREEN.withAlpha(0.4);
  }
};

const init = () => {
  if (dialogDataRect.flightAreaType === FLIGHTAREATYPE.NFZ) {
    dialogDataRect.color = colorRect.NFZ;
  } else if (dialogDataRect.flightAreaType === FLIGHTAREATYPE.DFENCE) {
    dialogDataRect.color = colorRect.DFENCE;
  }
  colorListRect.colorcss = getColor(dialogDataRect.color);
};
//#endregion
watch(
  () => dialogDataRect.flightAreaType,
  (newValue, old) => {
    console.log('newValue, old:', newValue, old);
    if (newValue === FLIGHTAREATYPE.NFZ) {
      dialogDataRect.color = colorRect.NFZ;
    } else if (newValue === FLIGHTAREATYPE.DFENCE) {
      dialogDataRect.color = colorRect.DFENCE;
    }
    colorListRect.colorcss = getColor(dialogDataRect.color);
  }
);

onMounted(() => {
  init();
});

onUnmounted(() => {});
</script>
<style lang="scss" scoped>
.geom-info-wrap {
  background: #c8cfa5;
  width: 230px;
  background-color: #11253e;
  .title {
    width: 200px;
    height: 30px;
    display: flex;
    align-items: center;
    padding: 5px 5px;
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;
    color: #ffffff;
    text-align: justify;
    font-weight: 400;
    background-color: #11253e;
  }
  .geom-info-content {
    width: 230px;
    padding: 8px 10px;

    .row-item {
      margin: 5px 0px;
      padding: 5px 0px;
      font-family: SourceHanSansSC-Regular;
      font-size: 14px;
      color: #ffffff;
      text-align: justify;
      font-weight: 400;
      display: flex;
      align-items: center;
    }
    background-color: #001129;
  }
  .bottom-box {
    display: flex;
    margin: 10px 10px;
    align-items: center;
    justify-content: center;
  }
}
.item {
  padding: 6px 5px;
  display: flex;
  align-items: center;
  background-color: #11253e;
}
.item.is-selected {
  border-radius: 2px;
  background-color: #4c5562 !important;
}

.databox {
  padding: 15px 10px;
  background-color: #11253e;
  border: 1px solid #5f5f5faf;
}

.color-box {
  width: 25px;
  height: 25px;
  background-color: #b9b9b94b;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  display: flex;
  .color {
    width: 20px;
    height: 20px;
    align-items: center;
    display: flex;
  }
}
</style>
