import _ from 'lodash';
import { getDeviceGroupTree } from '@/api/deviceGroup';
import {
  getBelongList,
  getDictItemList,
  getProductList as _getProductList,
  postTypeList
} from '@/api/product';
import { SysGroupTree } from '@/api/structrue';
import i18n from '@/lang';
import { getHandleUserList as _getHandleUserList } from '@/api/notice';

export default function useCommonRequests(initData) {
  const warnTypeList = ref([]);
  const groupTreeList = ref([]);
  const productBelongList = ref({});
  const productTypeList = ref([]);
  const productList = ref([]);
  const allProductList = ref([]);
  const handleUserList = ref([]);
  const structureTreeData = ref([
    {
      children: [],
      groupName: i18n.global.t('page.allGroup'),
      id: '-1'
    }
  ]);
  const treeProp = {
    checkStrictly: true,
    emitPath: false,
    value: 'id',
    label: 'groupName',
    leaf: 'leaf',
    expandTrigger: 'hover'
  };
  const _initData = ref(initData || {});
  const colorValue = ref('');

  async function getWarnType() {
    try {
      const res = await getDictItemList({
        dictCode: 'WarnType'
      });
      warnTypeList.value = res.data || [];
    } catch (e) {
      console.log('productBelongList error', e);
    }
  }

  async function getTreeData() {
    try {
      const res = await getDeviceGroupTree();
      groupTreeList.value = rinseGroupTree(res.data);
    } catch {
      console.log('getTreeData error');
    }
  }

  async function getHandleUserList() {
    try {
      const res = await _getHandleUserList();
      handleUserList.value = res.data;
    } catch {
      console.log('getTreeData error');
    }
  }

  async function getProductBelongList() {
    try {
      const res = await getBelongList();
      productBelongList.value = res.data;
    } catch (e) {
      console.log('productBelongList error', e);
    }
  }

  async function getProductSeriesList(searchData) {
    try {
      const res = await postTypeList({
        productBelong: searchData
          ? searchData.productBelong
          : _initData.value.productBelong
      });
      productTypeList.value = res.data;
    } catch (err) {
      console.log('getTreeData error', err);
    }
  }

  async function getProductList(searchData) {
    try {
      const res = await _getProductList({
        productTypeId: searchData
          ? searchData.productTypeId
          : _initData.value.productTypeId,
        productBelong: searchData
          ? searchData.productBelong
          : _initData.value.productBelong
      });
      productList.value = res.data;
    } catch {
      console.log('getProductList error');
    }
  }

  async function getSysGroupTreeData() {
    try {
      const res = await SysGroupTree();
      structureTreeData.value[0].children = res.data;
    } catch {
      console.log('getTreeData error');
    }
  }

  async function getAllProductList() {
    try {
      const res = await _getProductList({});
      allProductList.value = res.data;
    } catch {
      console.log('getProductList error');
    }
  }

  function productBelongChange(searchData) {
    searchData.productTypeId = '';
    searchData.productId = '';
    getProductSeriesList(searchData);
    getProductList(searchData);
  }

  function productTypeChange(searchData) {
    searchData.productId = '';
    getProductList(searchData);
  }

  function rinseGroupTree(tree) {
    const treeList = _.cloneDeep(tree);
    treeList.map(treeData => {
      treeData['leaf'] = false;
      if (treeData.children.length > 0) {
        treeData.children = rinseGroupTree(treeData.children);
      } else {
        treeData['leaf'] = true;
        delete treeData.children;
      }
    });
    return treeList;
  }

  return {
    warnTypeList,
    groupTreeList,
    productBelongList,
    productTypeList,
    productList,
    colorValue,
    allProductList,
    structureTreeData,
    handleUserList,
    treeProp,
    getAllProductList,
    getWarnType,
    getTreeData,
    getProductBelongList,
    getProductSeriesList,
    getProductList,
    getSysGroupTreeData,
    getHandleUserList,
    productBelongChange,
    productTypeChange
  };
}
