<template>
  <el-dialog
    :title="title"
    v-if="visible"
    :model-value="visible"
    width="600px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form class="app-form" ref="dataFormRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="负载品牌" prop="brand">
        <el-input
          v-model="form.brand"
          placeholder="请输入负载品牌"
          maxlength="20"
          @blur="form.brand = $event.target.value.trim()"
        />
      </el-form-item>
      <el-form-item label="负载名称" prop="payload_name">
        <el-input
          v-model="form.payload_name"
          placeholder="请输入负载名称"
          maxlength="30"
          @blur="form.payload_name = $event.target.value.trim()"
        />
      </el-form-item>
      <el-form-item label="负载ID" prop="payload_sn">
        <el-input
          :disabled="title=='编辑'?true:false"
          v-model="form.payload_sn"
          placeholder="请输入负载ID"
          maxlength="32"
          @blur="form.payload_sn = $event.target.value.trim()"
        />
      </el-form-item>
      <el-form-item label="负载型号" prop="model">
        <el-input
          v-model="form.model"
          placeholder="请输入负载型号"
          maxlength="20"
          @blur="form.model = $event.target.value.trim()"
        />
      </el-form-item>
      <el-form-item label="关联无人机" prop="device_sn">
        <el-select v-model="form.device_sn" placeholder="请选择关联无人机">
          <el-option v-for="item in deviceList" :key="item.device_sn" :label="item.nickname" :value="item.device_sn" />
        </el-select>
      </el-form-item>
      <el-form-item label="负载类型" prop="payload_type">
        <el-select v-model="form.payload_type" placeholder="请选择负载类型">
          <el-option v-for="item in optionData.loadOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item  label="所属组织" prop="orgId" class="mt-[16px]">
        <el-tree-select
          v-model="form.parent_id"
          :data="deptList"
          :props="{ value: 'id', label: 'name', children: 'children' }"
          value-key="id"
          placeholder="选择上级组织"
          check-strictly
          style="width: 100%"
        />
      </el-form-item> -->

    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-loading="loading">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { DOMAIN } from '@/utils/constants';
import optionData from '@/utils/option-data';
import {  addLoadManage, editLoadManage, getDevicesBound } from '@/api/devices';
import { listDept } from '@/api/system/dept';
import handleTree from '@/utils/handleTree';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  formData: {
    type: Object,
    default(rawProps) {
      return {};
    }
  }
});
const form = reactive({});
const dataFormRef = ref(ElForm);
const deptList = ref([]);
watch(
  () => props.formData,
  (newVal, oldVal) => {
    dataFormRef.value && dataFormRef.value.clearValidate && dataFormRef.value.clearValidate();
    Object.assign(form, newVal);
  },
  { deep: true }
);
const emit = defineEmits(['update:visible', 'submit']);
const rules = reactive({
  brand: [{ required: true, message: '请输入负载品牌', trigger: 'blur' }],
  payload_name: [{ required: true, message: '请输入负载名称', trigger: 'blur' }],
  device_sn: [{ required: true, message: '请选择关联无人机', trigger: ['change','blue'] }],
  model: [{ required: true, message: '请输入负载型号', trigger: 'blur' }],
  payload_type: [{ required: true, message: '请选择负载类型', trigger: 'change' }],
  payload_sn: [{ required: true, message: '请输入负载ID', trigger: ['change','blue'] }, {
      required: true,
      pattern: /^[a-zA-Z0-9]+$/,
      message: '负载ID只能包含数字和字母',
      trigger: 'blur'
    }],
});
const loading = ref(false);
const deviceList = ref([])
// 关闭弹窗
function closeDialog() {
  resetForm();
  emit('update:visible', false);
}
/**
 * 重置表单
 */
function resetForm() {
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  loading.value = false;
  Object.keys(form).map(key => {
    delete form[key];
  });
}

onMounted(()=>{
  getDeviceList();
  listDept().then(response => {
    deptList.value = handleTree(response, 'id');
  });
})

function getDeviceList () {
  getDevicesBound({
    domain: DOMAIN.DRONE,
    page: 1,
    page_size: 9999
  }).then(data => {
    const { list, pagination } = data;
    deviceList.value = list || [];
  });
}

function handleSubmit() {
  
  dataFormRef.value.validate(isValid => {
    if (isValid) {

      let params = { ...form};

      loading.value = true;
      if(props.title == '编辑负载信息') {
        editLoadManage(params)
        .then(res => {
          loading.value = false;

          ElMessage.success('更新成功');

          closeDialog();
          emit('submit');
        })
        .catch(e => {
          loading.value = false;
        });
      }else {
        addLoadManage(params)
        .then(res => {
          loading.value = false;

          ElMessage.success('新增成功');

          closeDialog();
          emit('submit');
        })
        .catch(e => {
          loading.value = false;
        });
      }
    } else {
      loading.value = false;
    }
  });
}

</script>
<style scoped lang="scss">
.input-serach {
  width: 200px;
}
.app-form {
  .select-time {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 5px 0;
  }
  ::v-deep {
    .el-input-number,
    .el-select {
      width: 100%;
    }
    .el-input-number .el-input__inner {
      text-align: left;
    }
    .el-input-number.is-controls-right .el-input__wrapper {
      padding-left: 11px;
    }
    .el-date-editor.el-input,
    .el-date-editor.el-input__wrapper {
      width: 100%;
    }
  }
}
</style>
