<script>
export default { name: 'Home' };
</script>

<script setup>
import { ref } from 'vue';
import VideoPlayer from './components/VideoPlayer.vue';
import DeviceSelect from './components/DeviceSelect.vue';
const checkedDevice=ref([])

function changeDevice(data){
  checkedDevice.value= data || []
  console.log("切换设备", toRaw(checkedDevice.value))
}
</script>

<template>
  <div class="video-container">
    <VideoPlayer :data="checkedDevice" />
    <DeviceSelect @onChange="changeDevice"/>
  </div>
</template>

<style lang="scss" scoped>
.video-container {
  display: flex;
  flex-direction: row;
  height: 100%;
  width: 100%;
  background: #101010; 
  position: relative;
}
</style>
