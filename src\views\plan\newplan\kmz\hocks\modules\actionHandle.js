// 动作相关
import {
  ACTION_ACTUATOR_FUNC,
  ACTION_TRIGGER_TYPE,
  ACTION_ACTUATOR_FUNC_ICON,
  ACTION_ACTUATOR_FUNC_NAME
} from '@/utils/constants';
import { getAllPrevActions, getAllActions } from './waylineshandle';
import { calculateFOV } from './actionFrustumHandle';

// 记录当前点击后 最新获取到的 姿态信息 这些信息都是全局的
export const globalFrustumOption = reactive({
  fov: calculateFOV(26), // 26, //视场角。角度越大，视野越大
  heading: 0, // 默认向北（左右朝向）
  pitch: 0, // 绕着Y轴旋转 这个基本不动
  roll: -90, // 默认向北（上下朝向-俯仰角跳转）
  zoom: 2,
  focalLength: 120
});

/** 根据触发类型和动作名称获取相关信息
 * @param {*} actionTriggerType 出发类型
 * @param {*} actionName 动作名称
 * @returns 动作的相关信息
 */
export const getActionInfo = (actionTriggerType, actionName) => {
  try {
    if (!actionTriggerType) {
      return {
        title: null,
        icon: null
      };
    }
    // 如果触发方式是 间距触发
    if (actionTriggerType !== ACTION_TRIGGER_TYPE.reachPoint) {
      if (actionTriggerType === ACTION_TRIGGER_TYPE.multipleTiming) {
        if (
          actionName === ACTION_ACTUATOR_FUNC.takePhoto ||
          actionName === ACTION_ACTUATOR_FUNC.timeIntervalTakePhoto
        ) {
          return {
            title: ACTION_ACTUATOR_FUNC_NAME[ACTION_ACTUATOR_FUNC.timeIntervalTakePhoto],
            icon: ACTION_ACTUATOR_FUNC_ICON.timeIntervalTakePhoto
          };
        }
      } else if (actionTriggerType === ACTION_TRIGGER_TYPE.multipleDistance) {
        if (
          actionName === ACTION_ACTUATOR_FUNC.takePhoto ||
          actionName === ACTION_ACTUATOR_FUNC.distanceIntervalTakePhoto
        ) {
          return {
            title: ACTION_ACTUATOR_FUNC_NAME[ACTION_ACTUATOR_FUNC.distanceIntervalTakePhoto],
            icon: ACTION_ACTUATOR_FUNC_ICON.distanceIntervalTakePhoto
          };
        }
      } else {
        return {
          title: ACTION_ACTUATOR_FUNC_NAME[ACTION_ACTUATOR_FUNC[actionName]],
          icon: ACTION_ACTUATOR_FUNC_ICON[actionName] || ''
        };
      }
    } else {
      // 如触发方式是到点执行
      return {
        title: ACTION_ACTUATOR_FUNC_NAME[ACTION_ACTUATOR_FUNC[actionName]],
        icon: ACTION_ACTUATOR_FUNC_ICON[actionName] || ''
      };
    }
  } catch (error) {
    return {
      title: null,
      icon: null
    };
  }
};

//#region 获取当前动作及当前点之前的所有的 heading pitch roll zoom的值

/**
 * 获取该动作之前的所有动作的相关参数
 * @param {*} actionOption
 * @param {string} actionOption.actionUuid
 * @returns 动画数组
 */
export const getPrevHeadingPitchRollZoom = (actionOption = null) => {
  if (!actionOption) {
    return;
  }
  const { actionUuid } = actionOption;
  globalFrustumOption.fov = calculateFOV(26);
  globalFrustumOption.heading = 0;
  globalFrustumOption.roll = -90;
  globalFrustumOption.zoom = 2;
  globalFrustumOption.focalLength = 120;
  // 获取动作数组
  const actionList = getAllPrevActions({ actionUuid });
  actionList.forEach(action => {
    switch (action.wpml_actionActuatorFunc) {
      case ACTION_ACTUATOR_FUNC.gimbalRotate:
        let rotateAngle = action.wpml_actionActuatorFuncParam.wpml_gimbalPitchRotateAngle; //
        globalFrustumOption.roll = rotateAngle - 90;
        break;
      case ACTION_ACTUATOR_FUNC.rotateYaw:
        let rotateYawAngle = action.wpml_actionActuatorFuncParam.wpml_aircraftHeading; //
        globalFrustumOption.heading = rotateYawAngle;
        break;
      case ACTION_ACTUATOR_FUNC.zoom:
        const { wpml_focalLength } = action.wpml_actionActuatorFuncParam; // 优化点1
        globalFrustumOption.focalLength = wpml_focalLength;
        let fov = calculateFOV(wpml_focalLength);
        globalFrustumOption.fov = fov;
        // 优化点2和3
        let zoom = wpml_focalLength / 24;
        globalFrustumOption.zoom = zoom === 0 ? 120 : zoom;
        break;
      default:
        break;
    }
  });
  return globalFrustumOption;
};

/**
 * 获取该动作之前的所有动作的相关参数
 * @param {*} actionOption
 * @param {string} actionOption.actionUuid
 * @returns 动画数组
 */
export const getLastActionInfoByPlacemarkIndex = (placemarkIndex = 0) => {
  const option = {
    fov: calculateFOV(26), // 26, //视场角。角度越大，视野越大
    heading: 0, // 默认向北（左右朝向）
    pitch: 0, // 绕着Y轴旋转 这个基本不动
    roll: -90, // 默认向北（上下朝向-俯仰角跳转）
    zoom: 2,
    focalLength: 120
  };
  // 获取动作数组
  const allActions = getAllActions();
  let lastAction = allActions[allActions.length - 1];
  const actionList = getAllPrevActions({ actionUuid: lastAction.uuid ?? null });
  actionList.forEach(action => {
    if (action.placemarkIndex <= placemarkIndex) {
      switch (action.wpml_actionActuatorFunc) {
        case ACTION_ACTUATOR_FUNC.gimbalRotate:
          let rotateAngle = action.wpml_actionActuatorFuncParam.wpml_gimbalPitchRotateAngle; //
          option.roll = rotateAngle - 90;
          break;
        case ACTION_ACTUATOR_FUNC.rotateYaw:
          let rotateYawAngle = action.wpml_actionActuatorFuncParam.wpml_aircraftHeading; //
          option.heading = rotateYawAngle;
          break;
        case ACTION_ACTUATOR_FUNC.zoom:
          const { wpml_focalLength } = action.wpml_actionActuatorFuncParam; // 优化点1
          option.focalLength = wpml_focalLength;
          let fov = calculateFOV(wpml_focalLength);
          option.fov = fov;
          // 优化点2和3
          let zoom = wpml_focalLength / 24;
          option.zoom = zoom === 0 ? 2 : zoom;
          break;
        default:
          break;
      }
    }
  });
  return {
    ...option,
    lastAction: lastAction
  };
};

//#endregion

//#region 设置全局的相机镜头选择方法
// 记录全局的相机镜头选择
// export const globalCameraOption = reactive({
//   cameraTypes: []
// });

/**
 * 获取全局的相机选择方法
 * @returns 相机类型
 */
// export const getGlobalCameraType = () => {
//   return globalCameraOption.cameraType ?? [];
// };

// /**
//  * 设置相机类型
//  * @param {*} cameraTypeArr
//  * @returns
//  */
// export const setGlobalCameraType = (cameraTypeArr = []) => {
//   globalCameraOption.cameraType = [];
//   cameraTypeArr.forEach(cameraType => {
//     globalCameraOption.cameraType.push(cameraType);
//   });
//   return globalCameraOption.cameraType.join(',');
// };

//#endregion
