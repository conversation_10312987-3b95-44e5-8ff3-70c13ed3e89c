//  这里写关于动画的通用方法
import { ACTION_ACTUATOR_FUNC } from '@/utils/constants';
import { createTakePhoteAction, getTakePhoteActionDefaultParam } from './TakePhoto';
import { createStopMultipleTakePhotoActionGroup, getStopMultipleTakePhotoActionDefaultParam } from './StopMultiple';
import {
  createStopRecordAction,
  createStartRecordAction,
  getStartRecordActionDefaultParam,
  getStopRecordActionDefaultParam
} from './Record';
import { createHoverAction, getHoverActionDefaultParam } from './Hover';
import { createMultipleTimingActionGroup, getMultipleTimingActionGroupDefaultParam } from './MultipleTiming';
import { createMultipleDistanceActionGroup, getMultipleDistanceActionGroupDefaultParam } from './MultipleDistance';
import { createPanoShotAction, getPanoShotActionDefaultParam } from './PanoShot';
import { createRotateYawAction, getRotateYawActionDefaultParam } from './RotateYaw';
// import { createGimbalYawRotateAction, getGimbalYawRotateActionDefaultParam } from './GimbalYawRotate';
// import { createGimbalPitchRotateAction, getGimbalPitchRotateActionDefaultParam } from './GimbalPitchRotate';
import { createGimbalRotateAction, getGimbalRotateActionDefaultParam } from './GimbalRotate';
import { createCustomDirNameAction, getCustomDirNameActionDefaultParam } from './CustomDirName';
import { createZoomAction, getZoomActionDefaultParam } from './Zoom';
// 默认创建方法
export const actionCreateFuncMap = {
  // 拍照  actionActuatorFuncParamOptions 这里用于后期更新使用
  [ACTION_ACTUATOR_FUNC.takePhoto]: {
    createFunc: (actionOptions, actionActuatorFuncParamOptions) => {
      return createTakePhoteAction(actionOptions, actionActuatorFuncParamOptions);
    },
    defaultParamFunc: () => {
      return getTakePhoteActionDefaultParam();
    }
  },
  [ACTION_ACTUATOR_FUNC.startRecord]: {
    createFunc: (actionOptions, actionActuatorFuncParamOptions) => {
      return createStartRecordAction(actionOptions, actionActuatorFuncParamOptions);
    },
    defaultParamFunc: () => {
      return getStartRecordActionDefaultParam();
    }
  },
  [ACTION_ACTUATOR_FUNC.stopRecord]: {
    createFunc: (actionOptions, actionActuatorFuncParamOptions) => {
      return createStopRecordAction(actionOptions, actionActuatorFuncParamOptions);
    },
    defaultParamFunc: () => {
      return getStopRecordActionDefaultParam();
    }
  },
  [ACTION_ACTUATOR_FUNC.hover]: {
    createFunc: (actionOptions, actionActuatorFuncParamOptions) => {
      return createHoverAction(actionOptions, actionActuatorFuncParamOptions);
    },
    defaultParamFunc: () => {
      return getHoverActionDefaultParam();
    }
  },
  [ACTION_ACTUATOR_FUNC.timeIntervalTakePhoto]: {
    createFunc: (options, actionActuatorFuncParamOptions) => {
      return createMultipleTimingActionGroup(options, actionActuatorFuncParamOptions);
    },
    defaultParamFunc: () => {
      return getMultipleTimingActionGroupDefaultParam();
    }
  },
  [ACTION_ACTUATOR_FUNC.distanceIntervalTakePhoto]: {
    createFunc: (options, actionActuatorFuncParamOptions) => {
      return createMultipleDistanceActionGroup(options, actionActuatorFuncParamOptions);
    },
    defaultParamFunc: () => {
      return getMultipleDistanceActionGroupDefaultParam();
    }
  },
  [ACTION_ACTUATOR_FUNC.stopIntervalTakePhoto]: {
    createFunc: (options, actionActuatorFuncParamOptions) => {
      return createStopMultipleTakePhotoActionGroup(options, actionActuatorFuncParamOptions);
    },
    defaultParamFunc: () => {
      return getStopMultipleTakePhotoActionDefaultParam();
    }
  },
  [ACTION_ACTUATOR_FUNC.panoShot]: {
    createFunc: (actionOptions, actionActuatorFuncParamOptions) => {
      return createPanoShotAction(actionOptions, actionActuatorFuncParamOptions);
    },
    defaultParamFunc: () => {
      return getPanoShotActionDefaultParam();
    }
  },
  [ACTION_ACTUATOR_FUNC.rotateYaw]: {
    createFunc: (actionOptions, actionActuatorFuncParamOptions) => {
      return createRotateYawAction(actionOptions, actionActuatorFuncParamOptions);
    },
    defaultParamFunc: () => {
      return getRotateYawActionDefaultParam();
    }
  },
  [ACTION_ACTUATOR_FUNC.gimbalRotate]: {
    createFunc: (actionOptions, actionActuatorFuncParamOptions) => {
      return createGimbalRotateAction(actionOptions, actionActuatorFuncParamOptions);
    },
    defaultParamFunc: () => {
      return getGimbalRotateActionDefaultParam();
    }
  },
  // [ACTION_ACTUATOR_FUNC.gimbalYawRotate]: {
  //   createFunc: (actionOptions, actionActuatorFuncParamOptions) => {
  //     return createGimbalYawRotateAction(actionOptions, actionActuatorFuncParamOptions);
  //   },
  //   defaultParamFunc: () => {
  //     return getGimbalYawRotateActionDefaultParam();
  //   }
  // },
  // [ACTION_ACTUATOR_FUNC.gimbalPitchRotate]: {
  //   createFunc: (actionOptions, actionActuatorFuncParamOptions) => {
  //     return createGimbalPitchRotateAction(actionOptions, actionActuatorFuncParamOptions);
  //   },
  //   defaultParamFunc: () => {
  //     return getGimbalPitchRotateActionDefaultParam();
  //   }
  // },
  [ACTION_ACTUATOR_FUNC.customDirName]: {
    createFunc: (actionOptions, actionActuatorFuncParamOptions) => {
      return createCustomDirNameAction(actionOptions, actionActuatorFuncParamOptions);
    },
    defaultParamFunc: () => {
      return getCustomDirNameActionDefaultParam();
    }
  },
  [ACTION_ACTUATOR_FUNC.zoom]: {
    createFunc: (actionOptions, actionActuatorFuncParamOptions) => {
      return createZoomAction(actionOptions, actionActuatorFuncParamOptions);
    },
    defaultParamFunc: p => {
      return getZoomActionDefaultParam(p);
    }
  }
};
