// domain
import { useMqtt } from '@/hooks/useMqtt';
import EventBus from './eventbus';
import { ElMessage } from 'element-plus';
import { postPayloadCommands } from '@/api/task';

export const EComponentName = {
  Thing: 'thing',
  Liveshare: 'liveshare',
  Api: 'api',
  Ws: 'ws',
  Map: 'map',
  Tsa: 'tsa',
  Media: 'media',
  Mission: 'mission'
};

export const EPhotoType = {
  Original: 0,
  Preview: 1,
  Unknown: -1
}

export const EDownloadOwner = {
  Mine: 0,
  Others: 1,
  Unknown: -1
};

export const ELocalStorageKey = {
  Username: 'username',
  WorkspaceId: 'workspace_id',
  Token: 'x-auth-token',
  PlatformName: 'platform_name',
  WorkspaceName: 'workspace_name',
  WorkspaceDesc: 'workspace_desc',
  Flag: 'flag',
  UserId: 'user_id',
  Device: 'device',
  GatewayOnline: 'gateway_online'
};

export const ERouterName = {
  ELEMENT: 'element',
  PROJECT: 'project',
  HOME: 'home',
  TSA: 'tsa',
  LAYER: 'layer',
  MEDIA: 'media',
  WAYLINE: 'wayline',
  LIVESTREAM: 'livestream',
  LIVING: 'living',
  WORKSPACE: 'workspace',
  MEMBERS: 'members',
  DEVICES: 'devices',
  TASK: 'task',
  CREATE_PLAN: 'create-plan',
  SELECT_PLAN: 'select-plan',
  FIRMWARES: 'firmwares',
  FLIGHT_AREA: 'flight-area',
  PILOT: 'pilot-login',
  PILOT_HOME: 'pilot-home',
  PILOT_MEDIA: 'pilot-media',
  PILOT_LIVESHARE: 'pilot-liveshare',
  PILOT_BIND: 'pilot-bind'
};

export const EVideoPublishType = {
  VideoOnDemand: '按需发布视频',
  VideoByManual: '手动发布视频',
  VideoDemandAuxManual: '辅助手动发布视频'
}

export const ELiveTypeName = {
  Unknown : 'Unknown',
  Agora : 'Agora',
  RTMP : 'RTMP',
  RTSP : 'RTSP',
  GB28181 : 'GB28181'
}

export const ELiveTypeValue = {
  Unknown: 0,
  Agora: 1,
  RTMP: 2,
  RTSP: 3,
  GB28181: 4
}

export const ELiveStatusValue  = {
  DISCONNECT: 'DISCONNECT',
  CONNECTED : 'CONNECTED',
  LIVING: 'LIVING'
}

export const EStatusValue = {
  CONNECTED: 0,
  DISCONNECT: 1,
  LIVING: 2
};

export const DOMAIN = {
  DRONE: '0', //飞机类
  PAYLOAD: '1', //负载类
  RC: '2', //遥控器类
  DOCK: '3' // 机场类
};
// DJI飞机类型
export const DRONE_TYPE = {
  M30: 67,
  M300: 60,
  Mavic3EnterpriseAdvanced: 77,
  M350: 89,
  M3D: 91
};
// DJI负载类型枚举值
export const PAYLOAD_TYPE = {
  FPV: 39,
  H20: 42,
  H20T: 43,
  H20N: 61,
  EP600: 50,
  EP800: 90742,
  M30D: 52,
  M30T: 53,
  XT2: 26,
  XTS: 41,
  Z30: 20,
  DockTopCamera: 165,
  M3E: 66,
  M3T: 67,
  M3D: 80,
  M3TD: 81
  // UNKNOWN : 65535
};
// RC type
export const RC_TYPE = {
  RC: 56,
  RCPlus: 119,
  RC144: 144
};

// DOCK type
export const DOCK_TYPE = {
  Dock: 1,
  Dock2: 2
};

// 设备sub_type 从0升序
export const DEVICE_SUB_TYPE = {
  ZERO: 0,
  ONE: 1,
  TWO: 2,
  THREE: 3,
  UNKNOWN: 65535
};

// 航线类型
export const WAYLINE_TYPE = {
  0: '航点航线',
  1: '面状航线'
};

export const DEVICE_MODEL_KEY = {
  M30: `${DOMAIN.DRONE}-${DRONE_TYPE.M30}-${DEVICE_SUB_TYPE.ZERO}`,
  M30T: `${DOMAIN.DRONE}-${DRONE_TYPE.M30}-${DEVICE_SUB_TYPE.ONE}`,

  M3E: `${DOMAIN.DRONE}-${DRONE_TYPE.Mavic3EnterpriseAdvanced}-${DEVICE_SUB_TYPE.ZERO}`,
  M3T: `${DOMAIN.DRONE}-${DRONE_TYPE.Mavic3EnterpriseAdvanced}-${DEVICE_SUB_TYPE.ONE}`,

  M300: `${DOMAIN.DRONE}-${DRONE_TYPE.M300}-${DEVICE_SUB_TYPE.ZERO}`,
  M350: `${DOMAIN.DRONE}-${DRONE_TYPE.M350}-${DEVICE_SUB_TYPE.ZERO}`,

  M3D: `${DOMAIN.DRONE}-${DRONE_TYPE.M3D}-${DEVICE_SUB_TYPE.ZERO}`,
  M3TD: `${DOMAIN.DRONE}-${DRONE_TYPE.M3D}-${DEVICE_SUB_TYPE.ONE}`,

  FPV: `${DOMAIN.PAYLOAD}-${PAYLOAD_TYPE.FPV}-${DEVICE_SUB_TYPE.ZERO}`,
  H20: `${DOMAIN.PAYLOAD}-${PAYLOAD_TYPE.H20}-${DEVICE_SUB_TYPE.ZERO}`,
  H20T: `${DOMAIN.PAYLOAD}-${PAYLOAD_TYPE.H20T}-${DEVICE_SUB_TYPE.ZERO}`,
  H20N: `${DOMAIN.PAYLOAD}-${PAYLOAD_TYPE.H20N}-${DEVICE_SUB_TYPE.ZERO}`,
  EP600: `${DOMAIN.PAYLOAD}-${PAYLOAD_TYPE.EP600}-${DEVICE_SUB_TYPE.UNKNOWN}`,
  EP800: `${DOMAIN.PAYLOAD}-${PAYLOAD_TYPE.EP800}-${DEVICE_SUB_TYPE.ZERO}`,
  M30Camera: `${DOMAIN.PAYLOAD}-${PAYLOAD_TYPE.M30D}-${DEVICE_SUB_TYPE.ZERO}`,
  M30TCamera: `${DOMAIN.PAYLOAD}-${PAYLOAD_TYPE.M30T}-${DEVICE_SUB_TYPE.ZERO}`,

  M3ECamera: `${DOMAIN.PAYLOAD}-${PAYLOAD_TYPE.M3E}-${DEVICE_SUB_TYPE.ZERO}`,
  M3TCamera: `${DOMAIN.PAYLOAD}-${PAYLOAD_TYPE.M3T}-${DEVICE_SUB_TYPE.ZERO}`,
  M3DCamera: `${DOMAIN.PAYLOAD}-${PAYLOAD_TYPE.M3D}-${DEVICE_SUB_TYPE.ZERO}`,
  M3TDCamera: `${DOMAIN.PAYLOAD}-${PAYLOAD_TYPE.M3TD}-${DEVICE_SUB_TYPE.ZERO}`,
  M3TDCamera2: `${DOMAIN.PAYLOAD}-${PAYLOAD_TYPE.M3TD}-${DEVICE_SUB_TYPE.TWO}`,
  // M3MCamera: `${DOMAIN.PAYLOAD}-${PAYLOAD_TYPE.M3M}-${DEVICE_SUB_TYPE.ZERO}`,

  XT2: `${DOMAIN.PAYLOAD}-${PAYLOAD_TYPE.XT2}-${DEVICE_SUB_TYPE.ZERO}`,
  XTS: `${DOMAIN.PAYLOAD}-${PAYLOAD_TYPE.XTS}-${DEVICE_SUB_TYPE.ZERO}`,
  Z30: `${DOMAIN.PAYLOAD}-${PAYLOAD_TYPE.Z30}-${DEVICE_SUB_TYPE.ZERO}`,
  DockTopCamera: `${DOMAIN.PAYLOAD}-${PAYLOAD_TYPE.DockTopCamera}-${DEVICE_SUB_TYPE.ZERO}`,

  RC: `${DOMAIN.RC}-${RC_TYPE.RC}-${DEVICE_SUB_TYPE.ZERO}`,
  RCPlus: `${DOMAIN.RC}-${RC_TYPE.RCPlus}-${DEVICE_SUB_TYPE.ZERO}`,

  Dock: `${DOMAIN.DOCK}-${DOCK_TYPE.Dock}-${DEVICE_SUB_TYPE.ZERO}`,
  Dock2: `${DOMAIN.DOCK}-${DOCK_TYPE.Dock2}-${DEVICE_SUB_TYPE.ZERO}`
};

export const DEVICE_NAME = {
  // drone
  [DEVICE_MODEL_KEY.M30]: 'M30',
  [DEVICE_MODEL_KEY.M30T]: 'M30T',
  [DEVICE_MODEL_KEY.M3E]: 'Mavic 3E',
  [DEVICE_MODEL_KEY.M3T]: 'Mavic 3T',
  // [DEVICE_MODEL_KEY.M3M]: 'Mavic 3M',
  [DEVICE_MODEL_KEY.M300]: 'M300 RTK',
  [DEVICE_MODEL_KEY.M350]: 'M350 RTK',
  [DEVICE_MODEL_KEY.M3D]: 'M3D',
  [DEVICE_MODEL_KEY.M3TD]: 'M3TD',

  // payload
  [DEVICE_MODEL_KEY.FPV]: 'FPV',
  [DEVICE_MODEL_KEY.H20]: 'H20',
  [DEVICE_MODEL_KEY.H20T]: 'H20T',
  [DEVICE_MODEL_KEY.H20N]: 'H20N',
  [DEVICE_MODEL_KEY.EP600]: 'P1',
  [DEVICE_MODEL_KEY.EP800]: 'L1',
  [DEVICE_MODEL_KEY.M30Camera]: 'M30 Camera',
  [DEVICE_MODEL_KEY.M30TCamera]: 'M30T Camera',
  [DEVICE_MODEL_KEY.M3ECamera]: 'Mavic 3E',
  [DEVICE_MODEL_KEY.M3TCamera]: 'Mavic 3T',
  // [DEVICE_MODEL_KEY.M3MCamera]: 'Mavic 3M',
  [DEVICE_MODEL_KEY.XT2]: 'XT2',
  [DEVICE_MODEL_KEY.XTS]: 'XTS',
  [DEVICE_MODEL_KEY.Z30]: 'Z30',
  [DEVICE_MODEL_KEY.DockTopCamera]: 'Dock Camera',
  [DEVICE_MODEL_KEY.M3DCamera]: 'M3D Camera',
  [DEVICE_MODEL_KEY.M3TDCamera]: 'M3TD Camera',
  [DEVICE_MODEL_KEY.M3TDCamera2]: 'M3TD Camera', // 与 DEVICE_MODEL_KEY.M3TDCamera 同
  // rc
  [DEVICE_MODEL_KEY.RC]: 'RC',
  [DEVICE_MODEL_KEY.RCPlus]: 'RC Plus',

  // dock
  [DEVICE_MODEL_KEY.Dock]: 'Dock',
  [DEVICE_MODEL_KEY.Dock2]: 'Dock2'
};

// 任务状态颜色
export const COMMON_COLOR = {
  1: '#666666', // 灰色
  2: '#00a0ff', // 蓝色
  3: '#19BE6B', // 绿色
  4: '#eeeeee', // 灰色
  5: '#E02020', // 红色
  6: '#F7C0BA' // 粉
};

// 告警类型颜色
export const HMS_COLOR = {
  0: '#19BE6B', // 绿色
  1: '#FDB022', // 黄色
  2: '#E02020' // 红色
};
// 任务类型
export const TASK_TYPE = {
  Immediate: 0, // 立即执行
  Timed: 1, // 单次定时任务
  Condition: 3 // 周期任务
};

export const EBizCode = {
  GatewayOsd: 'gateway_osd',
  DeviceOsd: 'device_osd',
  DockOsd: 'dock_osd',
  MapElementCreate: 'map_element_create',
  MapElementUpdate: 'map_element_update',
  MapElementDelete: 'map_element_delete',
  DeviceOnline: 'device_online',
  DeviceOffline: 'device_offline',
  DeviceHms: 'device_hms',
  AlarmTaskReport: 'alarm_task_report', //警情列表自动刷新
  AlarmTaskUpdate: 'alarm_task_update', //一键下发刷新页面
  // 机场任务
  FlightTaskProgress: 'flighttask_progress', // 机场任务执行进度
  FlightTaskMediaProgress: 'file_upload_callback', // 机场任务媒体上传进度
  FlightTaskMediaHighestPriority: 'highest_priority_upload_flighttask_media', // 机场任务媒体优先级上报

  // 设备指令
  DeviceReboot: 'device_reboot', // 机场重启
  DroneOpen: 'drone_open', // 飞行器开机
  DroneClose: 'drone_close', // 飞行器关机
  DeviceFormat: 'device_format', // 机场数据格式化
  DroneFormat: 'drone_format', // 飞行器数据格式化
  CoverOpen: 'cover_open', // 打开舱盖
  CoverClose: 'cover_close', // 关闭舱盖
  PutterOpen: 'putter_open', // 推杆展开
  PutterClose: 'putter_close', // 推杆闭合
  ChargeOpen: 'charge_open', // 打开充电
  ChargeClose: 'charge_close', // 关闭充电

  // 设备升级
  DeviceUpgrade: 'ota_progress', // 设备升级

  // 设备日志
  DeviceLogUploadProgress: 'fileupload_progress', // 设备日志上传

  // 飞行指令消息
  ControlSourceChange: 'control_source_change', // 控制权更新
  FlyToPointProgress: 'fly_to_point_progress', // 飞向目标点
  TakeoffToPointProgress: 'takeoff_to_point_progress', // 一键起飞
  JoystickInvalidNotify: 'joystick_invalid_notify', // 设备端退出drc模式
  DrcStatusNotify: 'drc_status_notify', // 飞行控制模式状态

  // custom flight area
  FlightAreasSyncProgress: 'flight_areas_sync_progress',
  FlightAreasDroneLocation: 'flight_areas_drone_location',
  FlightAreasUpdate: 'flight_areas_update'
};

export const ControlSourceChangeType = {
  Flight: 1,
  Payload: 2
};

// 任务类型
export const Key_Code = {
  KEY_W: 'KeyW',
  KEY_A: 'KeyA',
  KEY_S: 'KeyS',
  KEY_D: 'KeyD',
  KEY_Q: 'KeyQ',
  KEY_E: 'KeyE',
  KEY_C: 'KeyC',
  KEY_Z: 'KeyZ'
};

export function useDroneControlWsEvent(sn, payloadSn, funcs) {
  const eventBus = new EventBus();
  const droneControlSource = ref('A');
  const payloadControlSource = ref('B');
  function onControlSourceChange(data) {
    if (data.type === ControlSourceChangeType.Flight && data.sn === sn) {
      droneControlSource.value = data.control_source;
      message.info(`Flight control is changed to ${droneControlSource.value}`);
      return;
    }
    if (data.type === ControlSourceChangeType.Payload && data.sn === payloadSn) {
      payloadControlSource.value = data.control_source;
      message.info(`Payload control is changed to ${payloadControlSource.value}.`);
    }
  }

  function handleProgress(key, message, error) {
    if (error !== 0) {
      notification.error({
        key: key,
        message: key + 'Error code:' + error,
        description: message,
        duration: null
      });
    } else {
      notification.info({
        key: key,
        message: key,
        description: message,
        duration: 30
      });
    }
  }

  function handleDroneControlWsEvent(payload) {
    if (!payload) {
      return;
    }

    switch (payload.biz_code) {
      case EBizCode.ControlSourceChange: {
        onControlSourceChange(payload.data);
        break;
      }
      case EBizCode.FlyToPointProgress: {
        const { sn: deviceSn, result, message: msg } = payload.data;
        if (deviceSn !== sn) return;
        handleProgress(EBizCode.FlyToPointProgress, `device(sn: ${deviceSn}) ${msg}`, result);
        break;
      }
      case EBizCode.TakeoffToPointProgress: {
        const { sn: deviceSn, result, message: msg } = payload.data;
        if (deviceSn !== sn) return;
        handleProgress(EBizCode.TakeoffToPointProgress, `device(sn: ${deviceSn}) ${msg}`, result);
        break;
      }
      case EBizCode.JoystickInvalidNotify: {
        const { sn: deviceSn, result, message: msg } = payload.data;
        if (deviceSn !== sn) return;
        handleProgress(EBizCode.JoystickInvalidNotify, `device(sn: ${deviceSn}) ${msg}`, result);
        break;
      }
      case EBizCode.DrcStatusNotify: {
        const { sn: deviceSn, result, message: msg } = payload.data;
        // handleProgress(EBizCode.DrcStatusNotify, `device(sn: ${deviceSn}) ${msg}`, result)

        break;
      }
    }
    // eslint-disable-next-line no-unused-expressions
    // console.log('payload.biz_code', payload.data)
  }

  onMounted(() => {
    eventBus.on('droneControlWs', handleDroneControlWsEvent);
  });

  onBeforeUnmount(() => {
    eventBus.off('droneControlWs', handleDroneControlWsEvent);
  });

  return {
    droneControlSource: droneControlSource,
    payloadControlSource: payloadControlSource
  };
}

export const PayloadCommandsEnum = {
  CameraModeSwitch: 'camera_mode_switch',
  CameraPhotoTake: 'camera_photo_take',
  CameraRecordingStart: 'camera_recording_start',
  CameraRecordingStop: 'camera_recording_stop',
  CameraFocalLengthSet: 'camera_focal_length_set',
  GimbalReset: 'gimbal_reset',
  CameraAim: 'camera_aim',
  CameraScreenDrag: 'camera_screen_drag'
};

export function usePayloadControl() {
  function checkPayloadAuth(controlSource) {
    if (controlSource !== ControlSource.A) {
      ElMessage.error('Get Payload Control first');
      return false;
    }
    return true;
  }

  async function authPayload(sn, payloadIndx) {
    const { code } = await postPayloadAuth(sn, {
      payload_index: payloadIndx
    });
    if (code === 0) {
      ElMessage.success('Get Payload Control successfully');
      return true;
    }
    return false;
  }

  async function resetGimbal(sn, data) {
    postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.GimbalReset,
      data: data
    }).then(data => {
      ElMessage.success('重置成功');
    });
  }

  async function switchCameraMode(sn, data) {
    postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraModeSwitch,
      data: data
    }).then(data => {});
  }

  function takeCameraPhoto(sn, payloadIndx) {
    postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraPhotoTake,
      data: {
        payload_index: payloadIndx
      }
    }).then(data => {
      ElMessage.success('拍照成功！');
    });
  }

  function cloudControls(sn, params) {
    postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraScreenDrag,
      data: {
        ...params
      }
    }).then(data => {
      // ElMessage.success('操作成功');
      ElMessage({
        message: '操作成功',
        grouping: true,
        type: 'success'
      });
    });
  }

  function startCameraRecording(sn, payloadIndx) {
    postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraRecordingStart,
      data: {
        payload_index: payloadIndx
      }
    }).then(data => {
      ElMessage.success('开始录像成功！');
    });
  }

  function stopCameraRecording(sn, payloadIndx) {
    postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraRecordingStop,
      data: {
        payload_index: payloadIndx
      }
    }).then(data => {
      ElMessage.success('停止录像成功！');
    });
  }

  function changeCameraFocalLength(sn, data) {
    postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraFocalLengthSet,
      data: data
    }).then(res => {
      ElMessage.success('操作成功');
    });
  }

  async function cameraAim(sn, data) {
    const { code } = await postPayloadCommands(sn, {
      cmd: PayloadCommandsEnum.CameraAim,
      data: data
    });
    if (code === 0) {
      ElMessage.success('Zoom Aim successfully');
    }
  }

  return {
    checkPayloadAuth,
    authPayload,
    resetGimbal,
    switchCameraMode,
    takeCameraPhoto,
    startCameraRecording,
    stopCameraRecording,
    changeCameraFocalLength,
    cloudControls,
    cameraAim
  };
}

export function useManualControl(deviceTopicInfo, isCurrentFlightController) {
  const activeCodeKey = ref(null);
  let myInterval = ref(null);
  const mqttHooks = useMqtt(deviceTopicInfo);
  let seq = 0;
  function handlePublish(params) {
    const body = {
      method: DRC_METHOD.DRONE_CONTROL,
      data: params
    };
    handleClearInterval();
    myInterval = setInterval(() => {
      body.data.seq = seq++;
      seq++;
      window.console.log('keyCode>>>>', activeCodeKey.value, body);
      mqttHooks?.publishMqtt(deviceTopicInfo.pubTopic, body, { qos: 0 });
    }, 50);
  }

  function handleKeyup(keyCode) {
    if (!deviceTopicInfo.pubTopic) {
      ElMessage.error('请先进行远程控制');
      return;
    }
    const SPEED = 5; //  check
    const HEIGHT = 5; //  check
    const W_SPEED = 20; // 机头角速度
    seq = 0;
    switch (keyCode) {
      case 'KeyA':
        if (activeCodeKey.value === keyCode) return;
        handlePublish({ y: -SPEED });
        activeCodeKey.value = keyCode;
        break;
      case 'KeyW':
        if (activeCodeKey.value === keyCode) return;
        handlePublish({ x: SPEED });
        activeCodeKey.value = keyCode;
        break;
      case 'KeyS':
        if (activeCodeKey.value === keyCode) return;
        handlePublish({ x: -SPEED });
        activeCodeKey.value = keyCode;
        break;
      case 'KeyD':
        if (activeCodeKey.value === keyCode) return;
        handlePublish({ y: SPEED });
        activeCodeKey.value = keyCode;
        break;
      case 'KeyC':
        if (activeCodeKey.value === keyCode) return;
        handlePublish({ h: HEIGHT });
        activeCodeKey.value = keyCode;
        break;
      case 'KeyZ':
        if (activeCodeKey.value === keyCode) return;
        handlePublish({ h: -HEIGHT });
        activeCodeKey.value = keyCode;
        break;
      case 'KeyQ':
        if (activeCodeKey.value === keyCode) return;
        handlePublish({ w: -W_SPEED });
        activeCodeKey.value = keyCode;
        break;
      case 'KeyE':
        if (activeCodeKey.value === keyCode) return;
        handlePublish({ w: W_SPEED });
        activeCodeKey.value = keyCode;
        break;
      default:
        break;
    }
  }

  function handleClearInterval() {
    clearInterval(myInterval);
    myInterval = undefined;
  }

  function resetControlState() {
    activeCodeKey.value = null;
    seq = 0;
    handleClearInterval();
  }

  function onKeyup() {
    resetControlState();
  }

  function onKeydown(e) {
    handleKeyup(e.code);
  }

  function startKeyboardManualControl() {
    window.addEventListener('keydown', onKeydown);
    window.addEventListener('keyup', onKeyup);
  }

  function closeKeyboardManualControl() {
    resetControlState();
    window.removeEventListener('keydown', onKeydown);
    window.removeEventListener('keyup', onKeyup);
  }

  watch(
    () => isCurrentFlightController.value,
    val => {
      if (val && deviceTopicInfo.pubTopic) {
        startKeyboardManualControl();
      } else {
        closeKeyboardManualControl();
      }
    },
    { immediate: true }
  );

  onUnmounted(() => {
    closeKeyboardManualControl();
  });

  function handleEmergencyStop() {
    if (!deviceTopicInfo.pubTopic) {
      // ElMessage.error('请确保已经建立DRC链路')
      ElMessage.error('请先进行远程控制');
      return;
    }
    const body = {
      method: DRC_METHOD.DRONE_EMERGENCY_STOP,
      data: {}
    };
    resetControlState();
    ElMessage.success('操作成功');
    window.console.log('handleEmergencyStop>>>>', deviceTopicInfo.pubTopic, body);
    mqttHooks?.publishMqtt(deviceTopicInfo.pubTopic, body, { qos: 1 });
  }

  return {
    activeCodeKey,
    handleKeyup,
    handleEmergencyStop,
    resetControlState
  };
}

// 目标动作
export const TARGET_TYPE = {
  hover: 0, // 悬停
  radio: 1, //环绕飞行
  panorama: 2 //全景拍摄
};

export const DRC_METHOD = {
  HEART_BEAT: 'heart_beat',
  DRONE_CONTROL: 'drone_control', // 飞行控制-虚拟摇杆
  DRONE_EMERGENCY_STOP: 'drone_emergency_stop', // 急停
  OSD_INFO_PUSH: 'osd_info_push', // 高频osd信息上报
  HSI_INFO_PUSH: 'hsi_info_push', // 避障信息上报
  DELAY_TIME_INFO_PUSH: 'delay_info_push' // 图传链路延时信息上报
};

// 机场当前状态 ,TODO 待验证后面的参数
export const EDockModeCode = {
  Disconnected: -1,
  Idle: 0,
  Debugging: 1,
  Remote_Debugging: 2,
  Upgrading: 3,
  Working: 4
};
// 动作触发器类型
export const ACTION_TRIGGER_TYPE = {
  reachPoint: 'reachPoint', //到达航点时执行
  betweenAdjacentPoints: 'betweenAdjacentPoints', // 航段触发，均匀转云台
  multipleTiming: 'multipleTiming', // 等时触发
  multipleDistance: 'multipleDistance', // 等距触发
  default: 'reachPoint', // 默认是 到达航点时执行,
  stopMultiple: 'stopMultiple' // 停止间距拍照动作额外定义的类型，仅在代码内使用用于标识停止间隔动作类型
};

// 	动作类型
export const ACTION_ACTUATOR_FUNC = {
  takePhoto: 'takePhoto', // 单拍
  startRecord: 'startRecord', // 开始录像
  stopRecord: 'stopRecord', // 结束录像
  timeIntervalTakePhoto: 'timeIntervalTakePhoto', // 等时拍照
  distanceIntervalTakePhoto: 'distanceIntervalTakePhoto', // 等距拍照
  stopIntervalTakePhoto: 'stopIntervalTakePhoto', // 结束间距拍照
  hover: 'hover', // 悬停等待
  gimbalRotate: 'gimbalRotate', // 旋转云台,
  // gimbalYawRotate: 'gimbalYawRotate', // 云台偏航角,
  // gimbalPitchRotate: 'gimbalPitchRotate', // 云台俯仰角,
  gimbalPitchRotate: 'gimbalRotate', // 云台俯仰角,
  rotateYaw: 'rotateYaw', // 飞行器偏航
  gimbalEvenlyRotate: 'gimbalEvenlyRotate', // 航段间均匀转动云台pitch角
  zoom: 'zoom', // 变焦
  customDirName: 'customDirName', // 创建新文件夹
  // orientedShoot: 'orientedShoot', // 精准复拍动作
  panoShot: 'panoShot', // 全景拍照动作（仅支持M30/M30T）
  focus: 'focus' // 对焦
};

// 特殊类型的互相转换定义
export const ACTION_CONVERT_TRIGGER_TYPE_TO_FUNC = {
  [ACTION_TRIGGER_TYPE.multipleTiming]: ACTION_ACTUATOR_FUNC.timeIntervalTakePhoto, // 等时触发
  [ACTION_TRIGGER_TYPE.multipleDistance]: ACTION_ACTUATOR_FUNC.distanceIntervalTakePhoto // 等距触发
};
export const ACTION_CONVERT_FUNC_TO_TRIGGER_TYPE = {
  [ACTION_ACTUATOR_FUNC.timeIntervalTakePhoto]: ACTION_TRIGGER_TYPE.multipleTiming, // 等时触发
  [ACTION_ACTUATOR_FUNC.distanceIntervalTakePhoto]: ACTION_TRIGGER_TYPE.multipleDistance // 等时触发
};

// 	动作类型图标
export const ACTION_ACTUATOR_FUNC_ICON = {
  [ACTION_ACTUATOR_FUNC.takePhoto]: new URL('@/assets/plan/拍照.png', import.meta.url).href,
  [ACTION_ACTUATOR_FUNC.startRecord]: new URL('@/assets/plan/录制视频.png', import.meta.url).href,
  [ACTION_ACTUATOR_FUNC.stopRecord]: new URL('@/assets/plan/录像关.png', import.meta.url).href,
  [ACTION_ACTUATOR_FUNC.focus]: '',
  [ACTION_ACTUATOR_FUNC.zoom]: new URL('@/assets/plan/相机变焦.png', import.meta.url).href,
  [ACTION_ACTUATOR_FUNC.customDirName]: new URL('@/assets/plan/创建文件夹.png', import.meta.url).href,
  [ACTION_ACTUATOR_FUNC.gimbalRotate]: new URL('@/assets/plan/云台俯仰角.png', import.meta.url).href,
  [ACTION_ACTUATOR_FUNC.rotateYaw]: new URL('@/assets/plan/飞行器偏航角.png', import.meta.url).href,
  [ACTION_ACTUATOR_FUNC.hover]: new URL('@/assets/plan/无人机.png', import.meta.url).href,
  // [ACTION_ACTUATOR_FUNC.gimbalEvenlyRotate]: '',
  [ACTION_ACTUATOR_FUNC.orientedShoot]: '',
  [ACTION_ACTUATOR_FUNC.panoShot]: new URL('@/assets/plan/全景拍照.png', import.meta.url).href,
  [ACTION_ACTUATOR_FUNC.recordPointCloud]: '',
  [ACTION_ACTUATOR_FUNC.timeIntervalTakePhoto]: new URL('@/assets/plan/开始等时间拍照.png', import.meta.url).href,
  [ACTION_ACTUATOR_FUNC.distanceIntervalTakePhoto]: new URL('@/assets/plan/开始等距离拍照.png', import.meta.url).href,
  [ACTION_ACTUATOR_FUNC.stopIntervalTakePhoto]: new URL('@/assets/plan/停止间隔拍照.png', import.meta.url).href
  // [ACTION_ACTUATOR_FUNC.gimbalYawRotate]: new URL('@/assets/plan/云台偏航角.png', import.meta.url).href,
  // [ACTION_ACTUATOR_FUNC.gimbalPitchRotate]: new URL('@/assets/plan/云台俯仰角.png', import.meta.url).href
};

// 	动作类型名称
export const ACTION_ACTUATOR_FUNC_NAME = {
  [ACTION_ACTUATOR_FUNC.takePhoto]: '拍照',
  [ACTION_ACTUATOR_FUNC.startRecord]: '开始录像',
  [ACTION_ACTUATOR_FUNC.stopRecord]: '停止录像',
  [ACTION_ACTUATOR_FUNC.focus]: '对焦',
  [ACTION_ACTUATOR_FUNC.zoom]: '变焦',
  [ACTION_ACTUATOR_FUNC.customDirName]: '创建文件夹',
  [ACTION_ACTUATOR_FUNC.gimbalRotate]: '云台俯仰角', //
  // [ACTION_ACTUATOR_FUNC.gimbalYawRotate]: '云台偏航角', //
  // [ACTION_ACTUATOR_FUNC.gimbalPitchRotate]: '云台俯仰角',
  [ACTION_ACTUATOR_FUNC.rotateYaw]: '飞行器偏航',
  [ACTION_ACTUATOR_FUNC.hover]: '悬停',
  // [ACTION_ACTUATOR_FUNC.gimbalEvenlyRotate]: '云台俯仰角', //'航段间均匀转动云台pitch角',
  [ACTION_ACTUATOR_FUNC.orientedShoot]: '精准复拍动作',
  [ACTION_ACTUATOR_FUNC.panoShot]: '全景拍照',
  [ACTION_ACTUATOR_FUNC.recordPointCloud]: '点云录制操作',
  [ACTION_ACTUATOR_FUNC.timeIntervalTakePhoto]: '等时间间隔拍照',
  [ACTION_ACTUATOR_FUNC.distanceIntervalTakePhoto]: '等距离间隔拍照',
  [ACTION_ACTUATOR_FUNC.stopIntervalTakePhoto]: '结束间隔拍照'
};

// 用于标记是间隔动作、停止间隔动作、正常动作
export const OPERATION_TYPE = {
  interval: 'interval', // 间隔动作组
  normal: 'normal', // 正常动作组合
  stopInterval: 'stopInterval' // 停止间隔动作
};
//
export const ACTION_TYPE = {
  [ACTION_ACTUATOR_FUNC.stopIntervalTakePhoto]: '结束间隔拍照'
};

// 航点列表右键菜单删除类型
export const DELETE_ACTION_TYPE = {
  point: 0, // 航点
  action: 1 // 动作
};

// 夜航灯开关
export const NightLightsStateEnum = {
  CLOSE: 0, // 0-关闭
  OPEN: 1 // 1-打开
};

// 限远开关
export const DistanceLimitStatusEnum = {
  UNSET: 0, // 0-未设置
  SET: 1 // 1-已设置
};

// 避障
export const ObstacleAvoidanceStatusEnum = {
  CLOSE: 0, // 0-关闭
  OPEN: 1 // 1-开启
};

// 设备管理设置key
export const DeviceSettingKeyEnum = {
  NIGHT_LIGHTS_MODE_SET: 'night_lights_state', // 夜航灯开关
  HEIGHT_LIMIT_SET: 'height_limit', // 限高设置
  DISTANCE_LIMIT_SET: 'distance_limit_status', // 限远开关
  OBSTACLE_AVOIDANCE_HORIZON: 'obstacle_avoidance_horizon', // 水平避障状态
  OBSTACLE_AVOIDANCE_UPSIDE: 'obstacle_avoidance_upside', // 上视避障状态
  OBSTACLE_AVOIDANCE_DOWNSIDE: 'obstacle_avoidance_downside' // 下视避障状态
};

export const initDeviceSetting = {
  [DeviceSettingKeyEnum.NIGHT_LIGHTS_MODE_SET]: {
    label: '飞行器夜航灯',
    value: '',
    trueValue: NightLightsStateEnum.CLOSE,
    editable: false,
    popConfirm: {
      visible: false,
      loading: false,
      // content: '为保证飞行器的作业安全，建议打开夜航灯',
      label: '飞行器夜航灯'
    },
    settingKey: DeviceSettingKeyEnum.NIGHT_LIGHTS_MODE_SET
  },
  [DeviceSettingKeyEnum.HEIGHT_LIMIT_SET]: {
    label: '限高',
    value: '',
    trueValue: 120,
    editable: false,
    popConfirm: {
      visible: false,
      loading: false,
      // content: '限高：20 - 1500m',
      // info: '修改限高会影响当前机场的所有作业任务，建议确认作业情况后再进行修改',
      label: '限高'
    },
    settingKey: DeviceSettingKeyEnum.HEIGHT_LIMIT_SET
  },
  [DeviceSettingKeyEnum.DISTANCE_LIMIT_SET]: {
    label: '限远',
    value: '',
    trueValue: DistanceLimitStatusEnum.UNSET,
    // info: '限远（15 - 8000m）是约束飞行器相对机场的最大作业距离',
    editable: false,
    popConfirm: {
      visible: false,
      loading: false,
      // content: '限远 (15- 8000m) 是约束飞行器相对机场的最大作业距离',
      // info: '修改限远会影响当前机场的所有作业任务，建议确认作业情况后再进行修改',
      label: '限远'
    },
    settingKey: DeviceSettingKeyEnum.DISTANCE_LIMIT_SET
  },
  [DeviceSettingKeyEnum.OBSTACLE_AVOIDANCE_HORIZON]: {
    label: '水平避障',
    value: '',
    trueValue: ObstacleAvoidanceStatusEnum.CLOSE,
    // info: '飞行器的避障工作状态显示，可以快速开启/关闭飞行器避障，如需进一步设置请在设备运维页面设置',
    editable: false,
    popConfirm: {
      visible: false,
      loading: false,
      // content: '飞行器避障是保障飞行作业安全的基础功能，建议保持飞行器避障开启',
      label: '水平避障'
    },
    settingKey: DeviceSettingKeyEnum.OBSTACLE_AVOIDANCE_HORIZON
  },
  [DeviceSettingKeyEnum.OBSTACLE_AVOIDANCE_UPSIDE]: {
    label: '上视避障',
    value: '',
    trueValue: ObstacleAvoidanceStatusEnum.CLOSE,
    // info: '飞行器的避障工作状态显示，可以快速开启/关闭飞行器避障，如需进一步设置请在设备运维页面设置',
    editable: false,
    popConfirm: {
      visible: false,
      loading: false,
      // content: '飞行器避障是保障飞行作业安全的基础功能，建议保持飞行器避障开启',
      label: '上视避障'
    },
    settingKey: DeviceSettingKeyEnum.OBSTACLE_AVOIDANCE_UPSIDE
  },
  [DeviceSettingKeyEnum.OBSTACLE_AVOIDANCE_DOWNSIDE]: {
    label: '下视避障',
    value: '',
    trueValue: ObstacleAvoidanceStatusEnum.CLOSE,
    // info: '飞行器的避障工作状态显示，可以快速开启/关闭飞行器避障，如需进一步设置请在设备运维页面设置',
    editable: false,
    popConfirm: {
      visible: false,
      loading: false,
      // content: '飞行器避障是保障飞行作业安全的基础功能，建议保持飞行器避障开启',
      label: '下视避障'
    },
    settingKey: DeviceSettingKeyEnum.OBSTACLE_AVOIDANCE_DOWNSIDE
  }
};

export const initDeviceSettingFormModel = {
  nightLightsState: false, // 夜航灯开关
  heightLimit: 20, // 限高设置
  distanceLimitStatus: { state: false, distanceLimit: 15 }, // 限远开关
  obstacleAvoidanceHorizon: false, // 飞行器避障-水平开关设置
  obstacleAvoidanceUpside: false, // 飞行器避障-上视开关设置
  obstacleAvoidanceDownside: false // 飞行器避障-下视开关设置
};

export const EDeviceTypeName = {
  Aircraft: 0,
  Gateway: 2,
  Dock: 3
};

//jessibuca Pro播放器错误码信息
export const jessibucaError = {
  playError: '播放错误，url 为空的时候，调用play方法',
  fetchError: 'http 请求失败',
  websocketError: 'websocket 请求失败',
  webcodecsUnsupportedConfigurationError: 'webcodecs 解码类型不支持',
  webcodecsH265NotSupport: 'webcodecs 解码 h265 失败',
  webcodecsDecodeError: 'webcodecs 解码失败',
  mediaSourceH265NotSupport: 'mediaSource 解码 h265 失败',
  wasmDecodeError: 'wasm 解码失败',
  hlsError: 'hls 播放失败',
  wasmDecodeVideoNoResponseError: ' wasm 解码视频没有响应',
  mediaSourceUseCanvasRenderPlayFailed: 'mediaSource 使用 canvas 渲染播放失败',
  mseWidthOrHeightChange: '流分辨率发生变化，该变化有可能导致内存溢出',
  wcsWidthOrHeightChange: '流分辨率发生变化，该变化有可能导致内存溢出',
  mediaSourceFull: 'mse 缓存已满',
  mediaSourceAppendBufferError: 'mse 解码错误',
  mediaSourceBufferListLarge: 'mse 缓存列表过大',
  mediaSourceAppendBufferEndTimeout: 'mse 缓存超时',
  mediaSourceDecoderConfigurationError: 'mse 解码器配置错误',
  mediaSourceTsIsMaxDiff: 'mse 解码ts 时间戳差值过大（时间戳被重置了）',
  webrtcError: 'webrtc 播放失败',
  audioChannelError: '音频通道错误',
  simdH264DecodeVideoWidthIsTooLarge: 'simd 解码H264视频宽度过大',
  wasmWidthOrHeightChange: '流分辨率发生变化，该变化有可能导致内存溢出',
  flvDemuxBufferSizeTooLarge: 'flv 解码缓存过大',
  webglContextLostError: 'webgl上下文丢失',
  simdDecodeError: 'simd 解码报错',
  tallWebsocketClosedByError: '语音通讯websocket连接失败',
  webglAlignmentError: 'webgl 对齐错误',
  wasmUseVideoRenderError: 'wasm 使用 video 渲染失败(有些移动端浏览器不支持)',
  mseAddSourceBufferError: 'mse 添加缓存失败',
  mseSourceBufferError: 'mse 添加缓存失败'
};
