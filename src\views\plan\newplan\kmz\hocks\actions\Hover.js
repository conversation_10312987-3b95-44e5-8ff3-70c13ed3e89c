// 悬停相关
// kml 录像默认结构如下
// {
/* <wpml:action>
<wpml:actionId>0</wpml:actionId>
<wpml:actionActuatorFunc>hover</wpml:actionActuatorFunc>
<wpml:actionActuatorFuncParam>
  <wpml:hoverTime>11</wpml:hoverTime>
</wpml:actionActuatorFuncParam>
</wpml:action> */
// }
import { Action } from '../../waylines';
import { generateKey } from '@/utils';
import { ACTION_ACTUATOR_FUNC, ACTION_TRIGGER_TYPE } from '@/utils/constants';
//#region 悬停动作
/**
 * 创建悬停动作
 * @param {Object} options 动作配置项，应包含actionId、actionActuatorFunc
 * @param {Object|null} actionActuatorFuncParamOptions 动作执行器参数配置，可选
 * @returns {Action|null} 返回Action实例或在配置不正确时返回null
 */
export function createHoverAction(options, actionActuatorFuncParamOptions = null) {
  try {
    if (!options) {
      return null;
    }
    const actionOptions = {
      actionId: options.actionId || 0,
      actionActuatorFunc: ACTION_ACTUATOR_FUNC.hover,
      actionActuatorFuncParam: actionActuatorFuncParamOptions || getHoverActionDefaultParam(),
      uuid: options.actionUuid || generateKey(), // 动作id
      trigger: ACTION_TRIGGER_TYPE.reachPoint // 动作触发类型
    };
    // 创建动作
    return new Action(actionOptions);
  } catch (error) {
    console.error('创建 Action 实例失败:', error);
    return null;
  }
}

// 获取悬停默认参数
export function getHoverActionDefaultParam() {
  const actionActuatorFuncParam = {
    wpml_hoverTime: 10 // 默认10秒
  };
  return actionActuatorFuncParam;
}
//#endregion
