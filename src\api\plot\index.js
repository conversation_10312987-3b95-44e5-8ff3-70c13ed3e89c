import request from '@/utils/request';

export function plotList(alarmId) {
  return request({
    url: `/map/api/v1/plot/list/` + alarmId,
    method: 'get'
  });
}

export function plotSave(data) {
  return request({
    url: `/map/api/v1/plot/save`,
    method: 'post',
    data
  });
}

export function plotDelete(plotId) {
  return request({
    url: `/map/api/v1/plot/delete/` + plotId,
    method: 'post',
    plotId
  });
}

export function plotEdit(data) {
  return request({
    url: `/map/api/v1/plot/update`,
    method: 'post',
    data
  });
}

export function getProjectModel(workspace_id, project_id) {
  let queryParams = {};
  queryParams.workspace_id = workspace_id;
  queryParams.project_id = project_id;
  return request({
    url: `/achievement/api/v1/project/${workspace_id}/getProjectModel/${project_id}`,
    method: 'get'
  });
}

export function saveProjectModel(workspace_id, data) {
  return request({
    url: `/achievement/api/v1/project/${workspace_id}/saveProjectModel`,
    method: 'post',
    data
  });
}

export function getProjectLists(workspace_id, data) {
  return request({
    url: `/achievement/api/v1/model/${workspace_id}/lists`,
    method: 'post',
    data
  });
}
