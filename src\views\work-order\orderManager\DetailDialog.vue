<template>
  <el-dialog title="详情" 
  v-if="visible" 
  :model-value="visible" 
  align-center 
  :close-on-click-modal="false" 
  @close="closeDialog">
    <el-scrollbar height="650px" v-loading="loading">
      <el-form class="app-form" ref="dataFormRef"  label-width="120px"  v-loading="loading">
        <el-row>
          <el-col :span="12">
            <el-form-item label="工单类型：" >
              事故处置
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工单来源：" >
              AI识别
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="优先级：" >
              最高
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工单处置人：" >
              张三
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="地址：" >
              厦门市集美区xxxxwetqwetqwetwetqwtewfdsafsdfasfasfdasd
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="经度：" >
              xxx
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度：" >
              xxx
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="工单内容：" >
              事故发生地xxxxwetqwetqwetwetqwtewfdsafsdfasfasfdasd
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="附件：" >
               <multi-upload v-model="fileList" :limit="fileList.length" :showDelete="false"></multi-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    <div class="record-title">
      <span class="title-left"></span>
      处置记录
    </div>
    <div class="timeLine">
      <el-timeline style="max-width: 800px;">
        <el-timeline-item
          v-for="(activity, index) in activities"
          :key="index"
          hide-timestamp
          color="#275ba9"
        >
          <div>
            <div>2025-5-8 15:14:25</div>
            <div>处置人：张三</div>
            <div style="margin-top: 12px;">处置说明处置说明处置说明处置说明处置说明处置说明处置说明处置说明处置说明处置说明处置说明处置说明处置说明处置说明处置说明处置说明处置说明处置说明处置说明处置说明</div>
            <div style="width: 200px;margin-left: -8px;margin-top: 12px;">
                <multi-upload 
                v-model="fileList" 
                :limit="fileList.length"
                :showDelete="false"
                >
              </multi-upload>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>
    </el-scrollbar>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { getAIEventDetail } from '@/api/workOrder';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  id: {
    type: String,
    default: ''
  },
});
const form = ref({});
const loading = ref(false);
const emit = defineEmits(['update:visible']);
const fileList = [
  {
    name: 'test.mp4',
    url: 'https://testing.ff-iot.com:24135/ff_user/Know/2025/06/09/73FC67D9E38B4739AA2236474BA430CC.mp4',
  },
  {
    name: 'element-plus-logo2.svg',
    url: 'http://**************:24176/uavfile/wayline/thumbnai/2025/04/wayline/2025/04/org_0f7089d44ad4e389_1744250928000.jpg_1744251780410_thumb.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=uav%2F20250723%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250723T081436Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=36f01afe566f8c09498287d010fa7ca83ef2fb0f06c906448017fd14b8d034ef',
  },
]
const activities = ref([
  {
    content: 'Event start',
    timestamp: '2018-04-15',
  },
  {
    content: 'Approved',
    timestamp: '2018-04-13',
  },
  {
    content: 'Success',
    timestamp: '2018-04-11',
  },
])

// 关闭弹窗
function closeDialog() {
  emit('update:visible', false);
}

// 获取详情
function getDetail() {
  loading.value = true
  getAIEventDetail(props.id).then(res => {
    form.value = res
  }).finally(()=>{
    loading.value = false
  })
}

onMounted(() => {
  // getDetail();
});
</script>
<style lang="scss">
.label-class {
  display: inline-block;
  width: 110px;
  text-align: right;
}
</style>
<style lang="scss" scoped>
.record-title {
  margin: 0 0 16px;
  padding-bottom: 8px;
  font-size: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
  .title-left {
    display: inline-block;
    margin-right: 3px;
    height: 14px;
    border-left: 2px solid #275ba9;
  }
}
.timeLine {
  :deep(.el-timeline) {
    --el-timeline-node-color:#275ba9
  }
}
</style>
