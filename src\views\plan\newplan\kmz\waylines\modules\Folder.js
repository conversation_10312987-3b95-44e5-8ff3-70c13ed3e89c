import { generateKey } from '@/utils';

class Folder {
  constructor() {
    this.uuid = generateKey();
    // 航点集合
    this.placemark = [];
  }
  initFolder(options = {}) {
    // 定义航线ID，用于标识这条具体的航线  它默认为 0。
    this.wpml_waylineId = options.waylineId || 0;
    //  定义模板ID，用于标识航线模板的唯一编号   它默认为 0。
    this.wpml_templateId = options.templateId || 0;
    // 定义执行高度模式，这里设置为"WGS84"，意味着高度基于世界大地测量系统(WGS84)的海拔高度,它默认为 'WGS84'。
    this.wpml_executeHeightMode = options.executeHeightMode || 'WGS84';
    //  定义航线总长度或总距离，单位通常是米（meters），本例中为2633.92749023438米 它默认为 0.0。
    this.distance = parseFloat(options.distance) || 0.0;
    // 定义预计完成航线所需的时间，单位通常是秒（seconds），本例中为448.************秒,它默认为 0.0。
    this.duration = parseFloat(options.duration) || 0.0;
    // 定义航线中的全局自动飞行速度，单位通常是米/秒（meters per second），本例中设定为10米/秒,它默认为 10。
    this.wpml_autoFlightSpeed = parseInt(options.autoFlightSpeed) || 10;
  }
  //#region

  // Getter methods
  getWaylineId() {
    return this.wpml_waylineId;
  }

  getTemplateId() {
    return this.wpml_templateId;
  }

  getExecuteHeightMode() {
    return this.wpml_executeHeightMode;
  }

  getDistance() {
    return this.distance;
  }

  getDuration() {
    return this.duration;
  }

  getAutoFlightSpeed() {
    return this.wpml_autoFlightSpeed;
  }
  // 获取所有的 placemarks
  getPlacemark() {
    return this.placemark;
  }

  setWaylineId(id) {
    this.wpml_waylineId = id;
  }
  setTemplateId(id) {
    this.wpml_templateId = id;
  }
  setExecuteHeightMode(mode) {
    this.wpml_executeHeightMode = mode;
  }
  setDistance(dist) {
    this.distance = parseFloat(dist);
  }
  setDuration(time) {
    this.duration = parseFloat(time);
  }
  setAutoFlightSpeed(speed) {
    this.wpml_autoFlightSpeed = parseInt(speed);
  }
  //#endregion
  addPlacemark(pm) {
    this.placemark.push(pm);
    this.resetPlaceMark();
  }

  getPlacemarkByIndex(index) {
    return this.placemark[index];
  }

  // 将当前的集合变为一个map
  getPlacemarkMap() {
    const placemarkMap = new Map();
    this.placemark?.forEach(item => {
      placemarkMap.set(item.wpml_index, item);
    });
    return placemarkMap;
  }
  // 获取最小的placemark
  getMinPlacemark() {
    if (this.placemark && this.placemark.length > 0) {
      return {
        placemark: this.placemark[0],
        index: 0
      };
    }
    return {
      placemark: null,
      index: null
    };
  }

  // 获取最大的placemark
  getMaxPlacemark() {
    if (this.placemark && this.placemark.length > 0) {
      const maxIndex = this.placemark.length - 1;
      return {
        placemark: this.placemark[maxIndex],
        index: maxIndex
      };
    }
    return {
      placemark: null,
      index: null
    };
  }

  /**
   * 根据传入的 index 查找placemark
   * @param {*} options
   * @returns
   */
  findPlacemark(options) {
    const { wpml_index = 0 } = options || {};
    return this.placemark.find(p => p.wpml_index === wpml_index) || null;
  }

  updatePlacemark(index, newPlacemark) {
    this.placemark[index] = newPlacemark;
    this.resetPlaceMark();
  }

  deletePlacemark(index) {
    this.placemark.splice(index, 1);
    this.resetPlaceMark();
  }

  deletePlacemarkByIndex(wpml_index) {
    const i = this.placemark.findIndex(item => item.wpml_index === wpml_index);
    if (i !== -1) {
      this.placemark.splice(i, 1);
      this.resetPlaceMark();
    }
    return this.placemark;
  }

  resetPlaceMark() {
    let tempPlacemark = [];
    for (let index = 0; index < this.placemark.length; index++) {
      const item = this.placemark[index];
      item.wpml_index = index;
      if (item.hasOwnProperty('wpml_actionGroup')) {
        item.wpml_actionGroup?.forEach(actionGroup => {
          if (actionGroup.hasOwnProperty('placemarkIndex')) {
            actionGroup.placemarkIndex = item.wpml_index;
          }
        });
      }
      tempPlacemark.push(item);
    }
    this.placemark = tempPlacemark;
  }
}

export { Folder };
