<template>
  <div class="page">
    <div class="left" style="border-radius: 4px;">
      <div style="width:90%; height: 90%; margin: 4vh">
        <div style="height: 20%; margin-top: 3vh; background-color: white;display: flex; ">
          <div style="width: 25%; text-align: center">
            <el-avatar :size="60" :src="cloudapi">
            </el-avatar>
          </div>
          <div style="margin-left: 1vw;" @click="showStatus">
            <div style="height: 50%;">
              <span style="font-size: 16px; font-weight: bolder">{{ workspaceName }}</span>
              <ArrowRight style="float: right; margin-top: 5px; color: #8894a0;width: 14px;height: 14px;" />
            </div>
            <div style="height: 50%;">
              <PartlyCloudy v-if="thingState === EStatusValue.CONNECTED" style="color: #75c5f6;width: 14px;height: 14px;" />
              <Refresh style="width: 14px;height: 14px;" spin v-else/>
              <span style="color: #737373; margin-left: 3px;">{{ thingState == 0 ? '在线' : thingState == 1 ? '离线' : '运行中' }}</span>
            </div>
            <el-drawer  placement="right" v-model:visible="drawerVisible" width="340px">
              <div class="mb10 flex-row flex-justify-center flex-align-center">
                <p class="fz14" style="font-weight: 100;">模块状态</p>
              </div>
              <div class= "width-100 mb10 flex-align-start" v-for="m in modules" :key="m.name" style="height: 30px;">
                <div class="ml5" style="float: left; color: #000000;">{{m.name}}：</div>
                <div class="ml10" style="float: right; margin-bottom: 8px;">
                  <span :key="m.state" :class="m.state.value === EStatusValue.CONNECTED ? 'green' : 'red'">{{ m.state.value }}&nbsp;</span>
                  <el-button-group >
                  <el-button class="ml5" type="primary" size="small" @click.stop="moduleInstall(m)">安装</el-button>
                  <el-button class="ml5 mr5" type="danger" size="small" @click.stop="moduleUninstall(m)">卸载</el-button>
                  </el-button-group>
                </div>
                <el-divider />
              </div>
            </el-drawer>
          </div>
        </div>
        <el-divider  style="height: 2px; background-color: #f5f5f5; margin-top: 3vh;" />

        <el-button id="exitBtn" class="fz18" @click="confirmAgain"
          style="width: 10vw; height: 10vh; position: fixed; bottom: 13vh; left: 15vw; background-color: #e6e6e6; color: red; border: 0;"
          type="primary">退出
        </el-button>
        <el-dialog v-if="exitVisible" v-model="exitVisible" width="400px" :show-close="false" :close-on-click-modal="false">
          <template #footer>
            <el-button type="primary" style="width: 48%; float: left;" @click="onBack">取消</el-button>
            <el-button type="primary" style="width: 48%;" @click="onExit">确认</el-button>
          </template>
          <p>退出后，DJI Pilot与该服务器之间的数据将不会同步.</p>
        </el-dialog>
      </div>
    </div>
    <div class="right flex-column">
      <div class="mb5">
        <span class="ml5" style="color: #939393;">序列号</span>
      </div>
      <div class="fz16" style="background-color: white; border-radius: 4px;">
        <el-row style="border-bottom: 1px solid #f4f8f9; height: 45px;" align="middle">
          <el-col :span="1"></el-col>
            <el-col :span="9">
            我的呼号
            </el-col>
          <el-col :span="13" class="flex-align-end flex-column">
            <span style="color: #737373">{{ device.data.gateway_sn }}</span>
          </el-col>
        </el-row>
        <el-row style="border-bottom: 1px solid #f4f8f9; height: 45px;" align="middle" v-if="device.data.online_status && device.data.sn">
          <el-col :span="1"></el-col>
          <el-col :span="9">无人机序列号</el-col>
          <el-col :span="13" class="flex-align-end flex-column" >
            <span style="color: #737373">{{ device.data.sn }}</span>
          </el-col>
        </el-row>
      </div>
      <div class="mt5 mb5">
        <span class="ml5" style="color: #939393;">设置</span>
      </div>
      <div class="fz16" style="background-color: white; border-radius: 4px;">
        <!-- v-if="device.data.online_status && device.data.sn" -->
        <el-row v-if="device.data.online_status && device.data.sn" style="border-bottom: 1px solid #f4f8f9; height: 45px;" align="middle" @click="bindingDevice">
          <el-col :span="1"></el-col>
          <el-col :span="11">
            设备绑定
          </el-col>
          <el-col :span="10" style="text-align: right">
            <span v-if="device.data.bound_status" style="color: #737373">已绑定</span>
            <span v-else style="color: #737373">未绑定</span>
          </el-col>
          <el-col :span="2" class="flex-align-center flex-column" >
            <ArrowRight style="color: #8894a0; font-size: 20px;" />
          </el-col>
        </el-row>
        <el-row style="border-bottom: 1px solid #f4f8f9; height: 45px;" align="middle" @click="onMediaSetting">
          <el-col :span="1"></el-col>
          <el-col :span="21">
            媒体资源上传设置
          </el-col>
          <el-col :span="2" class="flex-align-center flex-column" >
            <ArrowRight style="color: #8894a0; font-size: 20px;" />
          </el-col>
        </el-row>
        <el-row style="border-bottom: 1px solid #f4f8f9; height: 45px;" align="middle" @click="onLiveshareSetting">
          <el-col :span="1"></el-col>
          <el-col :span="21">实时视频直播设置</el-col>
          <el-col :span="2" class="flex-align-center flex-column">
            <ArrowRight style="color: #8894a0; font-size: 20px;" />
          </el-col>
        </el-row>
        <!-- <el-row style="border-bottom: 1px solid #f4f8f9; height: 45px;" align="middle" @click="onOpen3rdApp">
          <el-col :span="1"></el-col>
          <el-col :span="21">Open 3rd Party APP</el-col>
          <el-col :span="2" class="flex-align-center flex-column">
            <ArrowRight style="color: #8894a0; font-size: 20px;" />
          </el-col>
        </el-row> -->
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { CURRENT_CONFIG } from '@/api/http/config'
import { bindDevice, getDeviceBySn, getPlatformInfo, getUserInfo } from '@/api/pilot-login/manage.js'
import apiPilot from '@/api/pilot-login/pilot-bridge'
import { useRouter } from 'vue-router';
import { EBizCode, EComponentName, EDownloadOwner, ELocalStorageKey, ERouterName, EStatusValue } from '@/utils/constants'
import cloudapi from '@/assets/pilot-home-avatar.png';
import { ArrowRight, MostlyCloudy, PartlyCloudy, Refresh } from '@element-plus/icons-vue';
import { useUserStoreHook } from '@/store/modules/user';
import { useConnectWebSocket } from '@/hooks/useConnectWebSocket';
import { ElMessage } from 'element-plus'

const router = useRouter();
const gatewayState = ref(localStorage.getItem(ELocalStorageKey.GatewayOnline) === 'true')
const thingState = ref(EStatusValue.DISCONNECT)
const apiState = ref(EStatusValue.DISCONNECT)
const liveState = ref(EStatusValue.DISCONNECT)
const wsState = ref(EStatusValue.DISCONNECT)
const mapState = ref(EStatusValue.DISCONNECT)
const tsaState = ref(EStatusValue.DISCONNECT)
const mediaState = ref(EStatusValue.DISCONNECT)
const waylineState = ref(EStatusValue.DISCONNECT)
const workspaceName = ref(localStorage.getItem(ELocalStorageKey.WorkspaceName)!)
const username = ref(localStorage.getItem(ELocalStorageKey.Username)!)
const wsId = ref(localStorage.getItem(ELocalStorageKey.WorkspaceId)!)
const components = apiPilot.init()
const exitVisible = ref(false)
const drawerVisible = ref(false)
let minitor = null;
let bindNum = null;
const device = reactive({
  data: {
    sn: '',
    online_status: false,
    device_callsign: '',
    user_id: '',
    user_callsign: '',
    bound_status: false,
    model: '',
    gateway_sn: EStatusValue.DISCONNECT,
    domain: -1
  }
})
const bindParam = {
  device_sn: '',
  user_id: '',
  workspace_id: wsId.value
}

const modules = [{
  name: 'Cloud',
  state: thingState,
  module: EComponentName.Thing
}, {
  name: 'Api',
  state: apiState,
  module: EComponentName.Api
}, {
  name: 'Live',
  state: liveState,
  module: EComponentName.Liveshare
}, {
  name: 'Ws',
  state: wsState,
  module: EComponentName.Ws
}, {
  name: 'Map',
  state: mapState,
  module: EComponentName.Map
}, {
  name: 'Tsa',
  state: tsaState,
  module: EComponentName.Tsa
}, {
  name: 'Media',
  state: mediaState,
  module: EComponentName.Media
}, {
  name: 'Wayline',
  state: waylineState,
  module: EComponentName.Mission
}]
const userStore = useUserStoreHook();
const { userData } = userStore;
const { workspace_id } = userData;
useConnectWebSocket(payload => {
  if (!payload) {
    return
  }
  switch (payload.biz_code) {
    case EBizCode.DeviceOnline: {
      if (payload.data.sn === device.data.gateway_sn) {
        localStorage.setItem(ELocalStorageKey.GatewayOnline, gatewayState.value.toString())
        break
      }
      if (payload.data.gateway_sn === device.data.gateway_sn) {
        device.data = payload.data
        localStorage.setItem(ELocalStorageKey.Device, JSON.stringify(device.data))
      }
      break
    }
    case EBizCode.DeviceOffline: {
      if (payload.data.sn === device.data.sn) {
        device.data.online_status = payload.data.online_status
        localStorage.setItem(ELocalStorageKey.Device, JSON.stringify(device.data))
      }
      break
    }
    default:
      break
  }
});

onMounted(() => {
  apiPilot.onBackClickReg()
  apiPilot.onStopPlatform()
  if(localStorage.getItem(ELocalStorageKey.Device) == 'desktop') {
    localStorage.setItem(ELocalStorageKey.Device,'')
  }
  localStorage.setItem('isHome', 'true')
  const oldDevice = localStorage.getItem(ELocalStorageKey.Device)
  if (oldDevice) {
    device.data = JSON.parse(oldDevice)
  }
  window.connectCallback = (arg: any) => {
    connectCallback(arg)
  }
  window.wsConnectCallback = (arg: any) => {
    wsConnectCallback(arg)
  }
  device.data.gateway_sn = apiPilot.getRemoteControllerSN()
  if (device.data.gateway_sn === EStatusValue.DISCONNECT.toString()) {
    ElMessage.warning('Data is not available, please restart the remote control.')
    return
  }
  device.data.sn = apiPilot.getAircraftSN()
  console.log('workspace_id',workspace_id)
  console.log('device.data.sn',device.data.sn)  
  getDeviceInfo()

  const isLoaded = apiPilot.isComponentLoaded(EComponentName.Thing)
  if (isLoaded) {
    username.value = '' + localStorage.getItem(ELocalStorageKey.Username)
    workspaceName.value = '' + localStorage.getItem(ELocalStorageKey.WorkspaceName)
    refreshStatus()
    apiPilot.setPlatformMessage(
      '' + localStorage.getItem(ELocalStorageKey.PlatformName),
      workspaceName.value,
      '' + localStorage.getItem(ELocalStorageKey.WorkspaceDesc)
    )
    return
  }

  setWorkspaceInfo()

  getUserInfo().then(res => {
    username.value = res.username
    localStorage.setItem(ELocalStorageKey.Username, username.value)
    // thing
    const param = {
      host: res.mqtt_addr,
      username: res.mqtt_username,
      password: res.mqtt_password,
      connectCallback: 'connectCallback'
    }
    components.set(EComponentName.Thing, param)
    apiPilot.loadComponent(EComponentName.Thing, components.get(EComponentName.Thing))

    bindParam.device_sn = device.gateway_sn
    bindParam.user_id = res.user_id
    bindParam.workspace_id = res.workspace_id
  })
})

const connectCallback = async (arg) => {
  if (arg) {
    thingState.value = EStatusValue.CONNECTED
    // liveshare
    apiPilot.loadComponent(EComponentName.Liveshare, components.get(EComponentName.Liveshare))

    // ws
    const wsParam = components.get(EComponentName.Ws)
    wsParam.token = apiPilot.getToken()
    apiPilot.loadComponent(EComponentName.Ws, components.get(EComponentName.Ws))

    // map
    const mapParam = components.get(EComponentName.Map)
    mapParam.userName = username.value
    apiPilot.loadComponent(EComponentName.Map, components.get(EComponentName.Map))

    // tsa
    apiPilot.loadComponent(EComponentName.Tsa, components.get(EComponentName.Tsa))

    // media
    apiPilot.loadComponent(EComponentName.Media, components.get(EComponentName.Media))
    apiPilot.setDownloadOwner(EDownloadOwner.Mine.valueOf())

    // mission
    apiPilot.loadComponent(EComponentName.Mission, {})

    bindNum = setInterval(() => {
      if (!bindParam.device_sn) {
        device.data.gateway_sn = apiPilot.getRemoteControllerSN()
        bindParam.device_sn = device.data.gateway_sn
        return
      }
      bindDevice(bindParam).then(bindRes => {
        clearInterval(bindNum)
        // if (bindRes.code !== 0) {
        //   ElMessage.error(bindRes.message)
        // } else {
        //   clearInterval(bindNum)
        // }
      })
    }, 2000)
    setTimeout(getDeviceInfo, 3000)
  } else {
    thingState.value = EStatusValue.DISCONNECT
  }
  refreshStatus()
}

const wsConnectCallback = async (arg) => {
  if (arg) {
    wsState.value = EStatusValue.CONNECTED
  } else {
    wsState.value = EStatusValue.DISCONNECT
  }
}

const confirmAgain = () => {
  exitVisible.value = true
}

const onBack = () => {
  exitVisible.value = false
}

const onExit = () => {
  localStorage.clear()
  apiPilot.stopwebview()
}

const bindingDevice = async () => {
  router.push(ERouterName.PILOT_BIND)
}

const onMediaSetting = async () => {
  router.push(ERouterName.PILOT_MEDIA)
}
const onLiveshareSetting = async () => {
  router.push(ERouterName.PILOT_LIVESHARE)
}
const onOpen3rdApp = () => {
  const packageName = 'com.dji.sample'
  const isInstalled = apiPilot.isAppInstalled(packageName)
  if (isInstalled) {
    window.open('https://www.dji.com')
  } else {
    ElMessage.error(packageName + ' is not installed.')
  }
}

const showStatus = async () => {
  minitor = setInterval(() => {
    refreshStatus()
    if (!drawerVisible.value) {
      clearInterval(minitor)
    }
  }, 2000)
  drawerVisible.value = true
}

function setWorkspaceInfo () {
  if (localStorage.getItem(ELocalStorageKey.WorkspaceName)) {
    apiPilot.setPlatformMessage(
      '' + localStorage.getItem(ELocalStorageKey.PlatformName),
      workspaceName.value,
      '' + localStorage.getItem(ELocalStorageKey.WorkspaceDesc)
    )
    apiPilot.setWorkspaceId(wsId.value)

    return
  }
  getPlatformInfo().then(res => {
    workspaceName.value = res.workspace_name
    wsId.value = res.workspace_id
    localStorage.setItem(ELocalStorageKey.PlatformName, res.platform_name)
    localStorage.setItem(ELocalStorageKey.WorkspaceName, workspaceName.value)
    localStorage.setItem(ELocalStorageKey.WorkspaceDesc, res.workspace_desc)
    apiPilot.setPlatformMessage(
      res.platform_name,
      workspaceName.value,
      res.workspace_desc
    )
    apiPilot.setWorkspaceId(wsId.value)
  })
}

function refreshStatus () {
  thingState.value = apiPilot.thingGetConnectState() ? EStatusValue.CONNECTED : EStatusValue.DISCONNECT
  apiState.value = apiPilot.isComponentLoaded(EComponentName.Api) ? EStatusValue.CONNECTED : EStatusValue.DISCONNECT
  liveState.value = apiPilot.isComponentLoaded(EComponentName.Liveshare) ? EStatusValue.CONNECTED : EStatusValue.DISCONNECT
  wsState.value = apiPilot.isComponentLoaded(EComponentName.Ws) && apiPilot.wsGetConnectState()
    ? EStatusValue.CONNECTED
    : EStatusValue.DISCONNECT
  mapState.value = apiPilot.isComponentLoaded(EComponentName.Map) ? EStatusValue.CONNECTED : EStatusValue.DISCONNECT
  tsaState.value = apiPilot.isComponentLoaded(EComponentName.Tsa) ? EStatusValue.CONNECTED : EStatusValue.DISCONNECT
  mediaState.value = apiPilot.isComponentLoaded(EComponentName.Media) ? EStatusValue.CONNECTED : EStatusValue.DISCONNECT
  waylineState.value = apiPilot.isComponentLoaded(EComponentName.Mission) ? EStatusValue.CONNECTED : EStatusValue.DISCONNECT
}

function moduleInstall (m) {
  let param
  switch (m.module) {
    case EComponentName.Thing:
      param = apiPilot.thingGetConfigs()
      break
    case EComponentName.Api: {
      const apiParam = {
        host: apiPilot.getHost(),
        token: apiPilot.getToken()
      }
      param = apiParam
      break
    }
    case EComponentName.Map: {
      const mapParam = components.get(EComponentName.Map)
      mapParam.userName = '' + localStorage.getItem(ELocalStorageKey.Username)
      param = mapParam
      break
    }
    case EComponentName.Ws: {
      const wsParam = components.get(EComponentName.Ws)
      wsParam.token = '' + localStorage.getItem(ELocalStorageKey.Token)
      param = wsParam
      break
    }
    default:
      param = components.get(m.module)
  }

  components.set(m.module, param)
  apiPilot.loadComponent(m.module, components.get(m.module))
  refreshStatus()
}

function moduleUninstall (m) {
  ElMessage.info('uninstall ' + m.module)
  apiPilot.unloadComponent(m.module)
  refreshStatus()
}

function getDeviceInfo () {
  console.log('device',device)
  if (!device.data.sn || device.data.sn === EStatusValue.DISCONNECT) {
    return
  }
  getDeviceBySn({device_sn: device.data.sn}).then(res => {
    console.log('res',res)
    device.data.online_status = res.status
    device.data.bound_status = res.bound_status
    device.data.device_callsign = res.nickname
    device.data.model = res.device_name
    localStorage.setItem(ELocalStorageKey.Device, JSON.stringify(device.data))
  })
}
</script>

<style lang="scss" scoped>
@import url('@/styles/pilot-login.scss');
// @import '/@/styles/index.scss';
:deep(.el-dialog__header) {
  background-color: #fff;
}
.page {
  display: flex;
  position: absolute;
  transition: width 0.2s ease;
  height: 100%;
  width: 100%;
  background-color: #f0f2f5;
  .left {
    width: 40%;
    height: 90%;
    background-color: white;
    margin-top: 6vh;
    margin-left: 2vh;
  }
  .right {
    width: 55%;
    height: 90%;
    margin-top: 6vh;
    margin-left: 5vh;
    margin-right: 5vh;
  }
}
.green {
  color: green
}
.red {
  color: red;
}
#exitBtn:hover :active {
  background-color: rgb(77, 75, 75);
  width: 10vw;
  height: 10vh;
  position: fixed;
  bottom: 13vh;
  left: 15vw;
  line-height: 10vh;
}

</style>
