import request from '@/utils/request';
import { LINE_PATH, APPLICTION_WORKSPACES, API_VERSION, APPLICTION_FLIGHT, APPLICTION_RECORD } from '../config/index';
import { useUserStoreHook } from '@/store/modules/user';

// 主路径
const BASE_URL = LINE_PATH + API_VERSION + APPLICTION_WORKSPACES;
const FLIGHT_URL = LINE_PATH + API_VERSION + APPLICTION_FLIGHT;
const RECORD_URL = LINE_PATH + API_VERSION + APPLICTION_RECORD;

/**
 * 获取航线
 *
 * @param queryParams
 */
export function getWaylines(queryParams) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;

  return request({
    url: `${BASE_URL}/${workspace_id}/waylines`,
    method: 'get',
    params: queryParams
  });
}

/**
 * 导入航线
 */
export function uploadWayLine(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/waylines/file/upload`,
    method: 'post',
    responseType: 'upload',
    data
  });
}

/**
 * 创建、更新航线
 */
export function createOrUpdateWayline(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/createOrUpdateWayline`,
    method: 'post',
    data
  });
}

/**
 * 导出航线
 */
export function downloadWaylineFile(waylineId) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/waylines/${waylineId}/url`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 删除航线
 *
 * @param waylineId
 */
export function deleteWayLine(waylineId) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/waylines/${waylineId}`,

    method: 'delete'
  });
}
/**
 * 批量删除航线
 *
 * @param waylineId
 */
export function batchDeleteWayLine(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/waylines/deleteByIds`,
    method: 'delete',
    data
  });
}

/**
 * 获取指定任务ID的飞行记录列表
 */
export function fetchFlightRecord(jobId) {
  return request({
    url: `${FLIGHT_URL}/record/${jobId}`,
    method: 'get'
  });
}

/**
 * 获取指定航线ID的飞行轨迹记录
 */
export function fetchRecordTrack(flightId) {
  return request({
    url: `${RECORD_URL}/track/${flightId}`,
    method: 'get'
  });
}

/**
 * 获取机场任务详情
 *
 * @param queryParams
 */
export function fetchWaylinesTaskDetail(jobId) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;

  return request({
    url: `${BASE_URL}/${workspace_id}/jobs/${jobId}`,
    method: 'get'
  });
}

// 默认个性化设置
export function initPersonalSetting(params) {
  return request({
    url: `/infra/config/get-sys-setting`,
    method: 'get'
  });
}

// 更新个性化设置
export function updatePersonalSetting(data) {
  return request({
    url: `/infra/config/update-sys-setting`,
    method: 'put',
    data: data
  });
}

// 获取部门个性化设置
export function getDeptSysSetting(params) {
  return request({
    url: `/infra/config/get-dept-sys-setting`,
    method: 'get'
  });
}

/**
 *
 * @param {*} queryParams {  0: 飞机类}
 * @returns
 */
export function getDeviceModelList(
  queryParams = {
    domain: 0
  }
) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `/manage/api/v1/devices/${workspace_id}/getDeviceModelList`,
    method: 'get',
    params: queryParams
  });
}

export function uploadLOGO(data) {
  return request({
    url: `/media/api/v1/files/uploadAttachment`,
    method: 'post',
    responseType: 'upload',
    data
  });
}

// 更新设备视频初始化配置
export function updateDeviceVideoConfig(data) {
  return request({
    url: `/infra/config/update-device-video-config`,
    method: 'put',
    data: data
  });
}

// 获取设备视频初始化配置
export function getDeviceVideoConfig(params) {
  return request({
    url: `/infra/config/get-device-video-config`,
    method: 'get'
  });
}

/**
 * 创建设备视频
 */
export function createDeviceVideo(data) {
  return request({
    url: `/manage/api/v1/deviceVideo/create`,
    method: 'post',
    data
  });
}

/**
 * 更新设备视频
 */
export function updateDeviceVideo(data) {
  return request({
    url: `/manage/api/v1/deviceVideo/update`,
    method: 'put',
    data: data
  });
}

/**
 * 删除设备视频
 *
 * @param deviceId
 */
export function deleteDeviceVideo(deviceId) {
  return request({
    url: `/manage/api/v1/deviceVideo/delete?id=` + deviceId,
    method: 'delete'
  });
}

/**
 * 分页查询设备视频
 *
 * @param deviceId
 */
export function pageDeviceVideo(queryParams) {
  return request({
    url: `/manage/api/v1/deviceVideo/page`,
    method: 'get',
    params: queryParams
  });
}
