<template>
  <div class="section-wrapper">
    <div class="section-header">
      <div class="title">{{ title }}</div>
    </div>
    <div v-if="hasCameraSelectedRef">
      <div v-for="item in cameraSeleactedRef" :key="item.id" class="section bg-light-blue">
        <Numbers v-model="item.gsdData" @changeHandle="onChangeHandle(item)" />
      </div>
    </div>
    <div class="section-content" v-else>
      <div class="camera-none">请先选择相机类型</div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'GSD'
};
</script>
<script setup>
import { ref } from 'vue';
import { useDeviceStore } from '@/store/modules/device.js';
import Numbers from '@/views/plan/surface/components/Numbers.vue';
import { CAMERA_TYPE_NAME } from '@/config';
import { caculateHeightByGSD, caculateGSDByHeight, parmsInfoRect } from '@/views/plan/surface/hocks/index.js';

import { unref } from 'vue';
import { useEditTrackerStore } from '@/store/modules/dataEditTracker.js';
const editTrackerStore = useEditTrackerStore();
const deviceStore = useDeviceStore();
const title = ref('GSD计算');
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {};
    }
  }
});

const data = computed({
  get: () => props.modelValue,
  set: value => {
    emits('update:modelValue', value);
  }
});

// 对外定义事件
const emits = defineEmits(['update:modelValue', 'changeHandle']); // 触发事件
// 这里是一个对象数组
const cameraSeleactedRef = ref([]);
const hasCameraSelectedRef = ref(false);
const onChangeHandle = obj => {
  if (!obj) {
    return;
  }
  let caculteH = caculateHeightByGSD(obj.type, obj.gsdData.value);
  (cameraSeleactedRef.value || []).forEach((item, i) => {
    item.gsdData.value = caculateGSDByHeight(item.type, caculteH);
    parmsInfoRect.gsd[item.type] = unref(item.gsdData.value);
  });
  editTrackerStore.dataTracker.markAsModified();
  emits('changeHandle', {
    height: caculteH
  });
};
const init = () => {
  if (!deviceStore) {
    return;
  }
  cameraSeleactedRef.value.length = 0;
  let arr = deviceStore?.getCameraSelect();
  hasCameraSelectedRef.value = arr?.length > 0;
  // 渲染相机
  (arr || []).forEach((camera, i) => {
    // 计算当前GSD的值
    cameraSeleactedRef.value.push({
      id: i,
      type: camera,
      gsdData: {
        title: `GSD(${CAMERA_TYPE_NAME[camera]})`,
        unit: 'cm/pixel',
        min: 0.5,
        max: 356,
        value: parmsInfoRect.gsd[camera] || 5, // 默认最小单位值
        range: [1, 0.1],
        step: 0.01
      }
    });
  });
};

// 外部触发传入高度进行计算当前所有GSD
const caculateHeight = caculteH => {
  (cameraSeleactedRef.value || []).forEach((item, i) => {
    item.gsdData.value = caculateGSDByHeight(item.type, caculteH);
    parmsInfoRect.gsd[item.type] = unref(item.gsdData.value);
  });
};
// 侦听deviceAdapter的变化
watch(
  () => deviceStore.cameraSelected,
  () => {
    // 重新初始化数据
    init();
  }
);
onMounted(() => {
  init();
  window.$bus.on('caculateHeight', caculateHeight);
});
onUnmounted(() => {
  window.$bus.off('caculateHeight', caculateHeight);
});
</script>

<style scoped>
.section-wrapper {
  color: white;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  user-select: none;
  padding: 0px 0px;
}
.section-wrapper .section-header .title {
  font-size: 16px;
}
.section-wrapper .section-content {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 10px;
}
.section-wrapper .section-content .section-content-item {
  height: 30px;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  background-color: #555658;
}
.section-content-item:not(:first-child) {
  margin-left: 10px;
}
.section-content-item.active {
  background-color: #007bff !important;
  color: white;
}

.camera-none {
  background-color: #404040 !important;
  padding: 1px 10px;
  border-radius: 5px;
  color: white;
  cursor: not-allowed;
}
</style>
