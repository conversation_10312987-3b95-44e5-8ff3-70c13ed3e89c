import {
  getOrCreateCesiumEngineInstance,
  CesiumLayerManager,
  imglayer,
  cialayer,
  zoomtoLonglats,
  c3ArrToDegress,
  setCameraView
} from '../../../components/Cesium/libs/cesium';
import * as Cesium from 'cesium';
import { ElMessage } from 'element-plus';
import { reactive, createApp } from 'vue';
import { getPictures } from '@/api/compare';
import { getDeptSysSetting } from '@/api/wayline';
import { readRemoteJsonFile } from '@/utils/configHelper';
import { AnimationFrameManager } from '@/components/Cesium/libs/cesium/AnimationFrameManager';
export let viewer = null;
export let scene = null;
export let layerManager = null;
export let loading = ref(false);
const ani = new AnimationFrameManager();
//实现聚合效果
let dataSourceCollection = null;
let dataSourcePromise = null;
const pic_location_icon = new URL('@/assets/gis/pic_location.png', import.meta.url).href;
let billboardsMap = new Map();
//#region 方法
export const initMap = () => {
  const engine = getOrCreateCesiumEngineInstance('picComparsion');
  engine?.init('container'); //地图
  viewer = engine.viewer;
  scene = viewer.scene;
  layerManager = new CesiumLayerManager(engine.viewer);
  layerManager.addLayer(imglayer);
  layerManager.addLayer(cialayer);
};

export const data2Geojson = data => {
  const geojson = {
    type: 'FeatureCollection',
    features: []
  };
  data.forEach((item, i) => {
    if (item.lng < 10 || item.lat < 10) {
      return;
    }
    geojson.features.push({
      type: 'Feature',
      geometry: {
        type: 'Point',
        coordinates: [parseFloat(item.lng), parseFloat(item.lat)]
      },
      properties: {
        ...item,
        id: i + 1,
        name: `Point ${i + 1}` // 可选，为每个点生成一个简单的名称
      }
    });
  });
  return geojson;
};

//清除聚合
export const removeEntityCluster = viewer => {
  // dataSourceCollection && viewer.dataSources.remove('entityClusterGeojson');
  dataSourceCollection = dataSourceCollection && dataSourceCollection.destroy();
  let dataSourcesLayer = viewer?.dataSources.getByName('entityClusterGeojson')[0] ?? null;
  if (dataSourcesLayer) {
    viewer.dataSources.remove(dataSourcesLayer);
    dataSourcePromise = null;
  }
};

export const setEntityCluster = async (data = null) => {
  if (!viewer || !data) {
    return;
  }
  // 测试
  removeEntityCluster(viewer); //清除上一次绘制的聚合效果
  let geojson = data2Geojson(data); //数据转换为geojson格式
  //读取geojson，并将数据源添加至DataSourceCollection
  dataSourceCollection = new Cesium.DataSourceCollection();
  dataSourcePromise = dataSourceCollection.add(Cesium.GeoJsonDataSource.load(geojson));
  dataSourcePromise.then(dataSource => {
    //命名用于后续删除
    dataSource.name = 'entityClusterGeojson';
    // 加载geojson
    viewer.dataSources.add(dataSource);
    // 视角切换到geojson
    let positions = [];
    dataSource.entities.values.forEach(entity => {
      positions.push(entity.position._value);
    });
    positions = c3ArrToDegress(positions);
    zoomtoLonglats(viewer, positions);
    //设置entity属性
    dataSource.entities.values.forEach(entity => {
      let properties = entity.properties;
      entity.uuid = properties.id._value;
      entity.name = properties.name._value || '';
    });
    //聚合参数设置
    dataSource.clustering.enabled = true; //开启聚合功能
    dataSource.clustering.pixelRange = 30; //聚合像素范围
    dataSource.clustering.minimumClusterSize = 1; //最小聚合数量
    let removeListener;
    let count = 0;
    const customStyle = () => {
      if (Cesium.defined(removeListener)) {
        removeListener();
        removeListener = undefined;
      } else {
        removeListener = dataSource.clustering.clusterEvent.addEventListener((clusteredEntities, cluster) => {
          if (cluster.label.id.length == 0 || !cluster.label.id[0]) {
            // cluster.label.show = false;
            cluster.billboard.show = false;
            return;
          }
          count++;
          cluster.label.text = '';
          // 这里添加自定义的 照片集合
          cluster.datas = clusteredEntities;
          //设置聚合后billboard样式，当有多个图标时，显示第一个图标和对应label
          canvasImage(pic_location_icon, clusteredEntities.length.toString() || '', cluster);
          cluster.label.show = false;
          cluster.billboard.show = true;
          cluster.billboard.id = cluster.label.id;
          cluster.entityType = 'entityCluster';
          cluster.billboard.horizontalOrigin = Cesium.HorizontalOrigin.CENTER;
          cluster.billboard.heightReference = Cesium.HeightReference.CLAMP_TO_GROUND;
          cluster.billboard.disableDepthTestDistance = Number.POSITIVE_INFINITY;
        });
      }
      const pixelRange = dataSource.clustering.pixelRange;
      dataSource.clustering.pixelRange = 0;
      dataSource.clustering.pixelRange = pixelRange;
    };
    //设置鼠标点击事件
    let isFirst = true; //设置标识，避免重复执行点击事件
    viewer.screenSpaceEventHandler.setInputAction(click => {
      const pickedObject = viewer.scene.pick(click.position);
      if (pickedObject && pickedObject.id && pickedObject.id instanceof Array) {
        if (pickedObject && isFirst) {
          setImagelist(pickedObject);
          setTimeout(() => {
            isFirst = true;
          }, 500);
        }
      } else {
        pictureListRect.list = [];
        pictureListRect.visible = false;
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    customStyle(); //调用聚合监听事件
  });
};

export const canvasImage = (bgsrc, text, entity) => {
  let canvas = document.createElement('canvas');
  canvas.width = 80;
  canvas.height = 110;
  let context = canvas.getContext('2d');
  let bgImg = new Image();
  bgImg.src = bgsrc; // 背景图的url
  bgImg.crossOrigin = 'Anonymous';
  bgImg.onload = () => {
    // 绘制一个透明的矩形
    // context.fillStyle = '#007bff'; // 蓝色背景
    context.fillStyle = 'rgba(0, 1, 0, 0)'; // 设置填充颜色为完全透明
    context.fillRect(0, 0, canvas.width, canvas.height); // 绘制矩形，参数为矩形的起始坐标和尺寸
    // 设置样式
    fillRoundRect(context, 0, 0, 78, 30, 15, '#133963', '#2278b9', 5);
    // 文字
    context.textAlign = 'center'; // 设置文字对齐方式
    context.fillStyle = '#fff';
    context.font = 'bold 20px Arial'; // 字体样式
    let textWidth = context.measureText(text).width;
    if (textWidth > canvas.width) {
      let scaled = canvas.width / textWidth;
      context.scale(scaled, scaled);
    }
    let mid = canvas.width / 2;
    context.fillText(text, mid, 22);
    // 绘制图片
    context.drawImage(bgImg, 15, 40, 50, 70);
    // 设置按钮样式
    let base64 = canvas.toDataURL('image/png');
    entity.billboard.image = null;
    entity.billboard.image = base64;
  };

  bgImg.onerror = () => {
    new Error('Image load failed');
  };

  // 确保图片加载完成前不会执行其他操作
  bgImg.onabort = () => {
    new Error('Image load aborted');
  };
};

export const canvasImage2 = (bgsrc, text) => {
  return new Promise((resolve, reject) => {
    let canvas = document.createElement('canvas');
    canvas.width = 80;
    canvas.height = 110;
    let context = canvas.getContext('2d');
    let bgImg = new Image();
    bgImg.src = bgsrc; // 背景图的url
    bgImg.crossOrigin = 'Anonymous';
    bgImg.onload = () => {
      // 绘制一个透明的矩形
      // context.fillStyle = '#007bff'; // 蓝色背景
      context.fillStyle = 'rgba(0, 1, 0, 0)'; // 设置填充颜色为完全透明
      context.fillRect(0, 0, canvas.width, canvas.height); // 绘制矩形，参数为矩形的起始坐标和尺寸
      // 设置样式
      fillRoundRect(context, 0, 0, 78, 28, 15, '#133963', '#2278b9', 2);
      // 文字
      context.textAlign = 'center'; // 设置文字对齐方式
      context.fillStyle = '#fff';
      context.font = 'bold 20px Arial'; // 字体样式
      let textWidth = context.measureText(text).width;
      if (textWidth > canvas.width) {
        let scaled = canvas.width / textWidth;
        context.scale(scaled, scaled);
      }
      let mid = canvas.width / 2;
      context.fillText(text, mid, 22);
      // 绘制图片
      context.drawImage(bgImg, 15, 40, 50, 70);
      // 设置按钮样式
      let base64 = canvas.toDataURL('image/png');
      resolve(base64);
    };

    bgImg.onerror = () => {
      reject(new Error('Image load failed'));
    };

    // 确保图片加载完成前不会执行其他操作
    bgImg.onabort = () => {
      reject(new Error('Image load aborted'));
    };
  });
};
/**该方法用来绘制一个有填充色的圆角矩形
 *@param context:canvas的上下文环境
 *@param x:左上角x轴坐标
 *@param y:左上角y轴坐标
 *@param width:矩形的宽度
 *@param height:矩形的高度
 *@param radius:圆的半径
 *@param fillColor:填充颜色 'rgba(0,0,0,0.7)'
 *@param strokeColor:边框颜色 #2278b9
 *@param lineWidth:线宽度 2
 **/
function fillRoundRect(context, x, y, width, height, radius, fillColor, strokeColor = '#2278b9', lineWidth = 3) {
  //圆的直径必然要小于矩形的宽高
  if (2 * radius > width || 2 * radius > height) {
    return false;
  }

  context.save();
  context.translate(x, y);
  //绘制圆角矩形的各个边
  drawRoundRectPath(context, width, height, radius);
  // 设置填充颜色和边框颜色
  context.fillStyle = fillColor || '#000';
  context.strokeStyle = strokeColor || '#000';
  context.lineWidth = lineWidth || 3;
  // 先填充，再描边
  context.fill();
  context.stroke();
  context.restore();
}

/**该方法用来绘制圆角矩形
 *@param context:canvas的上下文环境
 *@param x:左上角x轴坐标
 *@param y:左上角y轴坐标
 *@param width:矩形的宽度
 *@param height:矩形的高度
 *@param radius:圆的半径
 *@param lineWidth:线条粗细
 *@param strokeColor:线条颜色 'rgba(0,0,0,0.7)'
 **/
function strokeRoundRect(context, x, y, width, height, radius, /*optional*/ lineWidth, /*optional*/ strokeColor) {
  //圆的直径必然要小于矩形的宽高
  if (2 * radius > width || 2 * radius > height) {
    return false;
  }
  context.save();
  context.translate(x, y);
  //绘制圆角矩形的各个边
  drawRoundRectPath(context, width, height, radius);
  context.lineWidth = lineWidth || 2; //若是给定了值就用给定的值否则给予默认值2
  context.strokeStyle = strokeColor || '#000';
  context.fillStyle = '#fff'; // 文本填充颜色
  context.stroke();
  context.restore();
}

function drawRoundRectPath(context, width, height, radius) {
  context.beginPath(0);
  //从右下角顺时针绘制，弧度从0到1/2PI
  context.arc(width - radius, height - radius, radius, 0, Math.PI / 2);

  //矩形下边线
  context.lineTo(radius, height);

  //左下角圆弧，弧度从1/2PI到PI
  context.arc(radius, height - radius, radius, Math.PI / 2, Math.PI);

  //矩形左边线
  context.lineTo(0, radius);

  //左上角圆弧，弧度从PI到3/2PI
  context.arc(radius, radius, radius, Math.PI, (Math.PI * 3) / 2);

  //上边线
  context.lineTo(width - radius, 0);

  //右上角圆弧
  context.arc(width - radius, radius, radius, (Math.PI * 3) / 2, Math.PI * 2);

  //右边线
  context.lineTo(width, height - radius);
  context.closePath();
}

export const setImagelist = pickedObject => {
  if (!pickedObject) {
    return;
  }
  let entitys = pickedObject.id ?? null;
  pictureListRect.list = [];
  if (!entitys || entitys.length === 0) {
    pictureListRect.visible = false;
    return;
  }

  // 获取临时列表
  let tempList = [];
  entitys.forEach(entity => {
    tempList.push({
      id: entity.id,
      file_url: entity.properties.file_url._value,
      url: entity.properties.file_url._value,
      job_id: entity.properties.job_id._value,
      file_name: entity.properties?.file_name?._value || '',
      create_time: entity.properties.create_time._value,
      create_time_stamp: new Date(entity.properties.create_time._value).getTime(),
      lng: entity.properties.lng._value,
      lat: entity.properties.lat._value
    });
  });
  // 这里create_time 是字符串的时间 转成时间戳再比对
  pictureListRect.list = tempList.sort((a, b) => a.create_time_stamp - b.create_time_stamp);

  if (pictureListRect.list.length > 0) {
    pictureListRect.visible = true;
  } else {
    pictureListRect.visible = false;
  }
  return pictureListRect.list;
};

//#endregion

//#endregion

//#region 编辑 监听点击空白区域

//#endregion

//#region 获取列表构建的图层

export const initPicturesList = () => {
  const { start = null, end = null, lng = null, lat = null } = getTimeEndPoint('seven');
  onSearchHnadle({ start, end });
};
//#endregion

//#region 根据数据初始化地图
export const initFligheAreaOnMap = (flightList = []) => {
  // 根据列表 构建 飞行区域
  if (!viewer) {
    return;
  }
  // 先移除所有要素
  viewer.entities.removeAll();
};
//#endregion
// ------------------------------------------

//#region 照片列表
export const pictureListRect = reactive({
  list: [],
  visible: false
});
//#endregion

//#region  右侧窗体的操作及相关方法
export const timerRect = reactive({
  value: [],
  visible: false
});
export const timeSelectTypeRect = reactive({
  value: 'seven'
});
export const timeSelectTypeOptions = [
  {
    value: 'seven',
    label: '近7天'
  },
  {
    value: 'month',
    label: '近30天'
  },
  {
    value: '3month',
    label: '近3个月'
  },
  {
    value: 0,
    label: '自定义时间'
  },
  {
    value: 'all',
    label: '全部'
  }
];
// 时间变后
export const onTimeChangeHandle = value => {
  console.log('时间变后', value);
  if (value.length < 2) {
    return;
  }
  loading.value = true;
  // 这里根据时间构建开始和结束时间
  let s = new Date(value[0]);
  let e = new Date(value[1]);
  s = formatDateTime(s);
  e = formatDateTime(e, true);
  onSearchHnadle({ start: s, end: e });
};

// 获取时间节点方法
export const getTimeEndPoint = value => {
  console.log('1时间变后', value);
  // 这里执行快捷时间查询处理
  let start = new Date(); // 开始是今天的 时间格式化 时间要拼接到00.00.00
  let end = new Date();
  // 计算7 天、一个月前 、三个月前 和一年前的时间  格式化
  switch (value) {
    case 'seven':
      start.setDate(start.getDate() - 7);
      break;
    case 'month': // 一个月前
      start.setMonth(start.getMonth() - 1);
      break;
    case '3month': // 3个月前
      start.setMonth(start.getMonth() - 3);
      break;
    case 'all': // 全部
      start.setFullYear(start.getFullYear() - 100);
      break;
    default:
      // 默认查7 天
      start.setDate(start.getDate() - 7);
      break;
  }
  start = formatDateTime(start);
  end = formatDateTime(end, true);
  return {
    start: start,
    end: end
  };
};
// 将时间转换为 'YYYY-MM-DD 00:00:00' 格式
const formatDateTime = (date, isEnd = false) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  let t = `${year}-${month}-${day} 00:00:00`;
  if (isEnd) {
    t = `${year}-${month}-${day} 23:59:59`;
  }
  // 将格式化后的日期字符串转换为时间戳
  const timestamp = new Date(t).getTime();
  return timestamp;
};
export const onQuickerTimeChangeHandle = value => {
  // 这里如果是0 代表自定义时间
  if (value === 0) {
    timerRect.value = [];
    timerRect.visible = true;
  } else {
    timerRect.visible = false;
    timerRect.value = [];
    const { start, end } = getTimeEndPoint(value);
    onSearchHnadle({ start, end });
    pictureListRect.list = []; // 清空列表
  }
};

//  时间处理
function getTime(time) {
  let year = time.getFullYear(); // 获取当前年份
  let month = time.getMonth() + 1; // 获取当前月份（需要加1，因为月份从0开始计数）
  let day = time.getDate(); // 获取当前日期
  let hours = time.getHours(); // 获取当前小时数
  let minutes = time.getMinutes(); // 获取当前分钟数
  let seconds = time.getSeconds(); // 获取当前秒数
  // 格式化日期和时间
  let formattedDate = year + '-' + addLeadingZero(month) + '-' + addLeadingZero(day);
  let formattedTime = addLeadingZero(hours) + ':' + addLeadingZero(minutes) + ':' + addLeadingZero(seconds);

  function addLeadingZero(number) {
    if (number < 10) {
      return '0' + number;
    }
    return number;
  }
  return formattedDate + ' ' + formattedTime;
}

export const onSearchHnadle = (options, cb = null) => {
  if (!options) {
    ElMessage.warning('请正确传参入查询参数');
    loading.value = false;
    return;
  }
  loading.value = true;
  // ElMessage.info('请正确传参入查询参数');
  const { start = null, end = null, lng = null, lat = null } = options;

  // 获取数据
  getPictures({
    begin_time: start,
    end_time: end,
    lng: lng,
    lat: lat
  })
    .then(res => {
      pictureListRect.list.forEach(element => {
        viewer.dataSources.remove(element.entity);
      });
      pictureListRect.list = [];
      if (!res || res.length === 0) {
        ElMessage.warning('该时间段内未查询到飞行照片数据');
        loading.value = false;
        getDeptSysSetting().then(res => {
          const { wayline_config = {} } = res;
          setCameraView(viewer, {
            destination: Cesium.Cartesian3.fromDegrees(
              Number(wayline_config.longitude),
              Number(wayline_config.latitude),
              80000
            ),
            orientation: new Cesium.HeadingPitchRoll(
              Cesium.Math.toRadians(0),
              Cesium.Math.toRadians(-90),
              Cesium.Math.toRadians(0)
            ),
            duration: 1
          });
        });
        return;
      }
      loading.value = false;
      setEntityCluster(res);
    })
    .catch(err => {
      console.log(err);
      loading.value = false;
    });
};

//#endregion

//#region 左边图片信息
export const comparePicturesRect = reactive({
  leftPicture: {},
  rightPicture: {},
  data: {},
  list: [],
  isShow: false
});

export const getPicturesByLnglats = (CallBack = null) => {
  if (!comparePicturesRect.leftPicture) {
    ElMessage.warning('请正确传参入查询参数');
    return;
  }
  const { start = null, end = null, lng = null, lat = null } = comparePicturesRect.leftPicture || {};
  // 获取数据
  getPictures({
    begin_time: start,
    end_time: end,
    lng: lng,
    lat: lat
  })
    .then(res => {
      console.log('getPictures 获取到的数据', res);
      if (!res || res.length === 0) {
        ElMessage.warning('该经纬度附近无相关照片信息');
        comparePicturesRect.list = [];
        comparePicturesRect.isShow = false;
        CallBack && CallBack(res);
        return;
      }
      // 这里去掉和leftPicture相同的照片  res 循环 过滤掉 leftPicture 中 file_name 相同的
      const filteredResults = res.filter(i => i.file_name !== comparePicturesRect.leftPicture.file_name);
      if (filteredResults.length === 0) {
        ElMessage.warning('该经纬度附近无相关照片信息');
        comparePicturesRect.list = [];
        comparePicturesRect.isShow = false;
        CallBack && CallBack(filteredResults);
        return;
      }
      comparePicturesRect.list = filteredResults;
      comparePicturesRect.isShow = true;
      CallBack && CallBack(filteredResults);
    })
    .catch(err => {
      ElMessage.error('获取该经纬度附近照片出错:' + err);
      comparePicturesRect.isShow = false;
      console.log(err);
    });
};

//#endregion
