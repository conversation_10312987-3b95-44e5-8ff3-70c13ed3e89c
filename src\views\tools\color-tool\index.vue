<script>
export default {
  name: 'Color-tool'
};
</script>

<script setup>
import { reactive } from 'vue';
import ColorPicker from 'color-gradient-picker-vue3';
import 'color-gradient-picker-vue3/dist/style.css';

import { mixColors } from '@/utils/helper';

const state = reactive({
  color: {
    red: 46,
    green: 144,
    blue: 250,
    alpha: 1
  },
  showColorPicker: false,
  baseShade: '#ffffff',
  options: [
    {
      label: '白色',
      value: '#ffffff'
    },
    {
      label: '黑色',
      value: '#000000'
    }
  ],
  colorArray: []
});

function rgbToHex(color) {
  let red = color.red.toString(16);
  let green = color.green.toString(16);
  let blue = color.blue.toString(16);

  // 确保每个十六进制值都有两个字符
  if (red.length == 1) red = '0' + red;
  if (green.length == 1) green = '0' + green;
  if (blue.length == 1) blue = '0' + blue;

  return '#' + red + green + blue;
}

function hexToRgb(hexColor) {
  const hex = hexColor.replace('#', '');
  return {
    red: parseInt(hex.substring(0, 2), 16),
    green: parseInt(hex.substring(2, 4), 16),
    blue: parseInt(hex.substring(4, 6), 16),
    alpha: 1
  };
}

function handleChooseColor() {
  state.showColorPicker = !state.showColorPicker;
}

function handleChangeColor(color) {
  state.colorArray = [];
  state.color = color;
  state.color.alpha = 1;
  state.showColorPicker = false;
}

function handleMixColor() {
  let tempArr = [];
  new Array(9).fill('').map((d, i) => {
    tempArr.unshift(
      rgbToHex(mixColors(state.color, hexToRgb(state.baseShade), (i + 1) * 10))
    );
  });
  tempArr.unshift(rgbToHex(state.color));
  state.colorArray = tempArr;
}
</script>

<template>
  <div class="page-container flex flex-col items-center">
    <h3 class="w-full flex justify-center">
      选择一个颜色与白色或者黑色进行混合
    </h3>
    <div class="action-area mb-20">
      <div
        class="color-area relative flex justify-center items-center cursor-pointer"
      >
        <div
          class="left flex justify-center items-center"
          @click="handleChooseColor"
        >
          <div
            class="w-[40px] h-[20px] bg-blue mr3"
            :style="{ background: rgbToHex(state.color) }"
          />
          <div>当前颜色:{{ rgbToHex(state.color) }} 点击此处切换颜色</div>
        </div>
        <div class="right flex justify-center items-center ml-5">
          当前基色:
          <el-select
            v-model="state.baseShade"
            class="m-2"
            placeholder="Select"
            size="small"
          >
            <el-option
              v-for="item in state.options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <el-button
          class="ml-5"
          type="primary"
          size="small"
          @click="handleMixColor"
          >混合颜色</el-button
        >
        <ColorPicker
          class="color-picker-custom absolute top-10"
          v-if="state.showColorPicker"
          :color="state.color"
          :on-start-change="color => handleChangeColor(color, 'start')"
          :on-change="color => handleChangeColor(color, 'change')"
          :on-end-change="color => handleChangeColor(color, 'end')"
        />
      </div>
    </div>
    <el-card class="box-card w-[480px]">
      <template #header>
        <div class="card-header">
          <span>色阶列表</span>
          <el-button class="button" text>复制</el-button>
        </div>
      </template>
      <div
        v-for="item in state.colorArray"
        :key="item"
        class="text item"
        :style="{ background: item }"
      >
        {{ item }}
      </div>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.color-picker-custom {
  left: 0%;
  transform: translateX(0%);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
