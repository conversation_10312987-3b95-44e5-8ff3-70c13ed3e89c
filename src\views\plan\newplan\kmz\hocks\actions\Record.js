// 录像相关
// kml 录像默认结构如下
// <wpml:action>
// <wpml:actionId>0</wpml:actionId>
// <wpml:actionActuatorFunc>startRecord</wpml:actionActuatorFunc>
// <wpml:actionActuatorFuncParam>
//   <wpml:fileSuffix>888</wpml:fileSuffix>
//   <wpml:payloadPositionIndex>0</wpml:payloadPositionIndex>
//   <wpml:useGlobalPayloadLensIndex>1</wpml:useGlobalPayloadLensIndex>
//   <wpml:payloadLensIndex>wide,zoom,ir</wpml:payloadLensIndex>
// </wpml:actionActuatorFuncParam>
// </wpml:action>
// <wpml:action>
// <wpml:actionId>1</wpml:actionId>
// <wpml:actionActuatorFunc>stopRecord</wpml:actionActuatorFunc>
// <wpml:actionActuatorFuncParam>
//   <wpml:payloadPositionIndex>0</wpml:payloadPositionIndex>
// </wpml:actionActuatorFuncParam>
// </wpml:action>
import { ACTION_TRIGGER_TYPE } from '@/utils/constants';
import { PAYLOAD_LENS_INDEX, ACTION_ACTUATOR_FUNC } from '../../props';
import { Action } from '../../waylines';
import { generateKey } from '@/utils';
import { useDeviceStore } from '@/store/modules/device.js';
const deviceStore = useDeviceStore();
//#region 录像动作

/**
 * 创建录像动作
 * @param {Object} options 动作配置项，应包含actionId、actionActuatorFunc
 * @param {Object|null} actionActuatorFuncParamOptions 动作执行器参数配置，可选
 * @returns {Action|null} 返回Action实例或在配置不正确时返回null
 */
export function createStartRecordAction(options, actionActuatorFuncParamOptions = null) {
  try {
    // 创建动画组
    if (!options) {
      return null;
    }
    return new Action({
      actionId: options.actionId || 0,
      actionActuatorFunc: ACTION_ACTUATOR_FUNC.startRecord,
      actionActuatorFuncParam: actionActuatorFuncParamOptions || getStartRecordActionDefaultParam(),
      uuid: options.actionUuid || generateKey(), // 动作id
      trigger: ACTION_TRIGGER_TYPE.reachPoint
    });
  } catch (error) {
    console.error('创建 Action 实例失败:', error);
    return null;
  }
}

// 设置开始录像默认参数默认参数
export function getStartRecordActionDefaultParam() {
  let cameras = deviceStore?.getCameraSelect().join(',') ?? '';
  const actionActuatorFuncParam = {
    wpml_fileSuffix: '', // 这里如果为空则不写入
    wpml_payloadPositionIndex: 0,
    wpml_useGlobalPayloadLensIndex: 1,
    wpml_payloadLensIndex: cameras
  };
  return actionActuatorFuncParam;
}

//#endregion

//#region 停止录像动作

/**
 * 创建停止录像动作
 * @param {Object} options 动作配置项，应包含actionId、actionActuatorFunc
 * @param {Object|null} actionActuatorFuncParamOptions 动作执行器参数配置，可选
 * @returns {Action|null} 返回Action实例或在配置不正确时返回null
 */
export function createStopRecordAction(options, actionActuatorFuncParamOptions = null) {
  if (!options) {
    return null;
  }
  const actionOptions = {
    actionId: options.actionId || 0,
    actionActuatorFunc: ACTION_ACTUATOR_FUNC.stopRecord,
    actionActuatorFuncParam: actionActuatorFuncParamOptions || getStopRecordActionDefaultParam(),
    uuid: options.actionUuid || generateKey() // 动作id
  };
  try {
    // 创建动画组
    return new Action(actionOptions);
  } catch (error) {
    console.error('创建 Action 实例失败:', error);
    return null;
  }
}

// 设置停止录像默认拍照默认参数
export function getStopRecordActionDefaultParam() {
  const actionActuatorFuncParam = {
    wpml_payloadPositionIndex: 0
  };
  return actionActuatorFuncParam;
}
//#endregion
