{"comment": "设备类型参数设置", "0-91-1": {"device": {"series": "Matrice 3D 系列", "droneName": "Matrice 3E", "drone": 0, "droneSub": 1, "droneType": 91, "droneKey": "0-91-1", "description": "大疆机场2系列 Matrice 3TD 无人机", "payload": 1, "payloadType": 81, "payloadSub": 2, "payloadKey": "1-81-0", "deviceName": "M3TD"}, "imageFormat": ["visable", "ir"], "cmos": [{"cameraType": "visable", "cameraLabel": "可见光", "format": "1 / 1.32", "sensorWidth": 9.691, "sensorHeight": 7.278, "equivalentFocalLength": 24, "focalLength": 6.78, "pixels": 1200, "pixel": 1.197, "pixels_width": 4000, "pixels_height": 3000}, {"cameraType": "ir", "cameraLabel": "红外", "format": "1 / 2", "sensorWidth": 7.68, "sensorHeight": 6.144, "equivalentFocalLength": 40, "focalLength": 9.1, "pixels": 30, "pixel": 12, "pixels_width": 640, "pixels_height": 512}], "actions": ["<PERSON><PERSON><PERSON><PERSON>", "startRecord", "stopRecord", "hover", "focus", "zoom", "gimbalRotate", "rotateYaw", "timeIntervalTakePhoto", "distanceIntervalTakePhoto", "stopIntervalTakePhoto", "customDirName", "panoShot"]}, "0-91-0": {"device": {"series": "Matrice 3D 系列", "droneName": "Matrice <PERSON>", "drone": 0, "droneSub": 0, "droneType": 91, "droneKey": "0-91-0", "description": "大疆机场2系列 Matrice 3D 无人机", "payload": 1, "payloadType": 81, "payloadSub": 0, "payloadKey": "1-80-0", "deviceName": "M3D"}, "imageFormat": ["visable"], "cmos": [{"cameraType": "visable", "cameraLabel": "可见光", "format": "1 / 1.32", "sensorWidth": 9.691, "sensorHeight": 7.278, "equivalentFocalLength": 24, "focalLength": 6.78, "pixels": 1200, "pixel": 1.197, "pixels_width": 4000, "pixels_height": 3000}], "actions": []}, "0-77-1": {"device": {"series": "Mavic 3 行业系列", "droneName": "<PERSON>vic 3T", "drone": 0, "droneSub": 1, "droneType": 77, "droneKey": "0-77-1", "description": "测试", "payload": 1, "payloadType": 67, "payloadSub": 0, "payloadKey": "1-67-0", "deviceName": "<PERSON>vic 3T"}, "imageFormat": ["visable", "ir"], "cmos": [{"cameraType": "visable", "cameraLabel": "可见光", "format": "1 / 1.32", "sensorWidth": 9.691, "sensorHeight": 7.278, "equivalentFocalLength": 24, "focalLength": 6.78, "pixels": 1200, "pixel": 1.197, "pixels_width": 4000, "pixels_height": 3000}, {"cameraType": "ir", "cameraLabel": "红外", "format": "1 / 2", "sensorWidth": 7.68, "sensorHeight": 6.144, "equivalentFocalLength": 40, "focalLength": 9.1, "pixels": 30, "pixel": 12, "pixels_width": 640, "pixels_height": 512}], "actions": ["<PERSON><PERSON><PERSON><PERSON>", "startRecord", "stopRecord", "hover", "focus", "zoom", "gimbalRotate", "rotateYaw", "timeIntervalTakePhoto", "distanceIntervalTakePhoto", "stopIntervalTakePhoto", "customDirName"]}, "0-67-1": {"device": {"series": "Matrice 30T 行业系列", "droneName": "Matrice 30T", "drone": 0, "droneSub": 1, "droneType": 67, "droneKey": "0-67-1", "description": "测试", "payload": 1, "payloadType": 67, "payloadSub": 0, "payloadKey": "1-67-0", "deviceName": "Matrice 30T"}, "imageFormat": ["visable", "ir"], "cmos": [{"cameraType": "visable", "cameraLabel": "可见光", "format": "1 / 1.32", "sensorWidth": 9.691, "sensorHeight": 7.278, "equivalentFocalLength": 24, "focalLength": 6.78, "pixels": 1200, "pixel": 1.197, "pixels_width": 4000, "pixels_height": 3000}, {"cameraType": "ir", "cameraLabel": "红外", "format": "1 / 2", "sensorWidth": 7.68, "sensorHeight": 6.144, "equivalentFocalLength": 40, "focalLength": 9.1, "pixels": 30, "pixel": 12, "pixels_width": 640, "pixels_height": 512}], "actions": ["<PERSON><PERSON><PERSON><PERSON>", "startRecord", "stopRecord", "hover", "focus", "zoom", "gimbalRotate", "rotateYaw", "timeIntervalTakePhoto", "distanceIntervalTakePhoto", "stopIntervalTakePhoto", "customDirName"]}, "0-89-0": {"device": {"series": "Matrice 350 RTK", "droneName": "Matrice 350 RTK", "drone": 0, "droneSub": 0, "droneType": 89, "droneKey": "0-89-0", "description": "测试", "payload": 0, "payloadType": 89, "payloadSub": 0, "payloadKey": "0-89-0", "deviceName": "Matrice 350 RTK"}, "imageFormat": ["visable", "ir"], "cmos": [{"cameraType": "visable", "cameraLabel": "可见光", "format": "1 / 1.32", "sensorWidth": 9.691, "sensorHeight": 7.278, "equivalentFocalLength": 24, "focalLength": 6.78, "pixels": 1200, "pixel": 1.197, "pixels_width": 4000, "pixels_height": 3000}, {"cameraType": "ir", "cameraLabel": "红外", "format": "1 / 2", "sensorWidth": 7.68, "sensorHeight": 6.144, "equivalentFocalLength": 40, "focalLength": 9.1, "pixels": 30, "pixel": 12, "pixels_width": 640, "pixels_height": 512}], "actions": ["<PERSON><PERSON><PERSON><PERSON>", "startRecord", "stopRecord", "hover", "focus", "zoom", "gimbalRotate", "rotateYaw", "timeIntervalTakePhoto", "distanceIntervalTakePhoto", "stopIntervalTakePhoto", "customDirName"]}, "0-60-0": {"device": {"series": "Matrice 300 RTK", "droneName": "Matrice 300 RTK", "drone": 0, "droneSub": 0, "droneType": 60, "droneKey": "0-60-0", "description": "测试", "payload": 0, "payloadType": 60, "payloadSub": 0, "payloadKey": "0-60-0", "deviceName": "Matrice 300 RTK"}, "imageFormat": ["visable", "ir"], "cmos": [{"cameraType": "visable", "cameraLabel": "可见光", "format": "1 / 1.32", "sensorWidth": 9.691, "sensorHeight": 7.278, "equivalentFocalLength": 24, "focalLength": 6.78, "pixels": 1200, "pixel": 1.197, "pixels_width": 4000, "pixels_height": 3000}, {"cameraType": "ir", "cameraLabel": "红外", "format": "1 / 2", "sensorWidth": 7.68, "sensorHeight": 6.144, "equivalentFocalLength": 40, "focalLength": 9.1, "pixels": 30, "pixel": 12, "pixels_width": 640, "pixels_height": 512}], "actions": ["<PERSON><PERSON><PERSON><PERSON>", "startRecord", "stopRecord", "hover", "focus", "zoom", "gimbalRotate", "rotateYaw", "timeIntervalTakePhoto", "distanceIntervalTakePhoto", "stopIntervalTakePhoto", "customDirName"]}, "0-99-0": {"device": {"series": "Matrice 4E", "droneName": "Matrice 4E", "drone": 0, "droneSub": 0, "droneType": 99, "droneKey": "0-99-0", "description": "Matrice 4 系列（M4E 相机）", "payload": 0, "payloadType": 99, "payloadSub": 0, "payloadKey": "0-99-0", "deviceName": "Matrice 4E"}, "imageFormat": ["visable", "ir"], "cmos": [{"cameraType": "visable", "cameraLabel": "可见光", "format": "1 / 1.32", "sensorWidth": 9.691, "sensorHeight": 7.278, "equivalentFocalLength": 24, "focalLength": 6.78, "pixels": 1200, "pixel": 1.197, "pixels_width": 4000, "pixels_height": 3000}, {"cameraType": "ir", "cameraLabel": "红外", "format": "1 / 2", "sensorWidth": 7.68, "sensorHeight": 6.144, "equivalentFocalLength": 40, "focalLength": 9.1, "pixels": 30, "pixel": 12, "pixels_width": 640, "pixels_height": 512}], "actions": ["<PERSON><PERSON><PERSON><PERSON>", "startRecord", "stopRecord", "hover", "focus", "zoom", "gimbalRotate", "rotateYaw", "timeIntervalTakePhoto", "distanceIntervalTakePhoto", "stopIntervalTakePhoto", "customDirName", "panoShot"]}, "0-99-1": {"device": {"series": "Matrice 4T", "droneName": "Matrice 4T", "drone": 0, "droneSub": 0, "droneType": 99, "droneKey": "0-99-1", "description": "Matrice 4 系列（M4T 相机）", "payload": 0, "payloadType": 99, "payloadSub": 0, "payloadKey": "0-99-1", "deviceName": "Matrice 4T"}, "imageFormat": ["visable", "ir"], "cmos": [{"cameraType": "visable", "cameraLabel": "可见光", "format": "1 / 1.32", "sensorWidth": 9.691, "sensorHeight": 7.278, "equivalentFocalLength": 24, "focalLength": 6.78, "pixels": 1200, "pixel": 1.197, "pixels_width": 4000, "pixels_height": 3000}, {"cameraType": "ir", "cameraLabel": "红外", "format": "1 / 2", "sensorWidth": 7.68, "sensorHeight": 6.144, "equivalentFocalLength": 40, "focalLength": 9.1, "pixels": 30, "pixel": 12, "pixels_width": 640, "pixels_height": 512}], "actions": ["<PERSON><PERSON><PERSON><PERSON>", "startRecord", "stopRecord", "hover", "focus", "zoom", "gimbalRotate", "rotateYaw", "timeIntervalTakePhoto", "distanceIntervalTakePhoto", "stopIntervalTakePhoto", "customDirName", "panoShot"]}}