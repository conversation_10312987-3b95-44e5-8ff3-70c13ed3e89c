//#region missionConfig
// 飞向首航点模式
export const FLYTO_WAY_LINE_MODE = {
  safely: 'safely', // 安全模式
  pointToPoint: 'pointToPoint' // 倾斜飞行模式
};
// 航线结束动作
export const FINISH_ACTION = {
  goHome: 'goHome', // 飞行器完成航线任务后,退出航线模式并返航
  noAction: 'noAction', // 飞行器完成航线任务后,退出航线模式
  autoLand: 'autoLand', // 飞行器完成航线任务后,退出航线模式并原地降落
  gotoFirstWaypoint: 'gotoFirstWaypoint' // 飞行器完成航线任务后,立即飞向航线起始点,到达后退出航线模式
};
// 失控是否继续执行航线
export const EXIT_ON_RCLOST = {
  goContinue: 'goContinue', // 继续执行航线
  executeLostAction: 'executeLostAction' // 退出航线,执行失控动作
};
// 失控动作类型
export const EXECUTE_RCLOST_ACTION = {
  goBack: 'goBack', // 返航,飞行器从失控位置飞向起飞点
  landing: 'landing', // 降落,飞行器从失控位置原地降落
  hover: 'hover' // 悬停,飞行器从失控位置悬停
};
//#endregion
