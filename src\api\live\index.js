import request from '@/utils/request';
import { MAIN_PATH, MEDIA_PATH, MODEL_PATH, APPLICTION_LIVE, API_VERSION, ACHIEVEMENT_PATH } from '../config/index';
import { useUserStoreHook } from '@/store/modules/user';

// 主路径
const BASE_URL = MAIN_PATH + API_VERSION + APPLICTION_LIVE;

/**
 * 获取可直播在线设备
 *
 * @param queryParams
 */
export function getLiveCapacity(queryParams) {
  return request({
    url: `${BASE_URL}/capacity`,
    method: 'get',
    params: queryParams
  });
}

/**
 * 开始直播
 */
export function startLivestream(data) {
  return request({
    url: `${BASE_URL}/streams/start`,
    method: 'post',
    data
  });
}
/**
 * 停止直播
 */
export function stopLivestream(data) {
  return request({
    url: `${BASE_URL}/streams/stop`,
    method: 'post',
    data
  });
}

/**
 * 切换直播质量、模式
 * 
 * @param {Object} data - 包含切换直播模式所需的数据
 * @param {number} data.url_type - 直播URL类型
 *   - 0: AGORA
 *   - 1: RTMP
 *   - 2: RTSP
 *   - 3: GB28181
 *   - 4: WHIP
 * @param {string} data.video_id - 视频ID
 * @param {number} data.video_quality - 视频质量
 *   - 0: 自动
 *   - 1: 流畅
 *   - 2: 标准
 *   - 3: 高清
 *   - 4: 超清
 * @param {number} data.LensChangeVideoTypeEnum - 直播模式
 *   - zoom: 变焦模式 ZOOM
 *   - wide: 广角模式 WIDE
 *   - ir: 红外模式 IR
 * @returns {Promise} - 返回请求的Promise对象
 */
export function setLivestreamQuality(data) {
  return request({
    url: `${BASE_URL}/streams/update`,
    method: 'post',
    data
  });
}

/**
 * 切换镜头
 */
export function changeLivestreamLens(data) {
  return request({
    url: `${BASE_URL}/streams/switch`,
    method: 'post',
    data
  });
}

//获取播放链接
export function getVideoUrl(data) {
  return request({
    url: `${MAIN_PATH + API_VERSION}/deviceVideo/getVideoUrl`,
    method: 'post',
    data
  });
}

//获取媒体库
export function getMediaFiles(queryParams) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${MEDIA_PATH + API_VERSION}/files/${workspace_id}/files`,
    method: 'get',
    params: queryParams
  });
}

export function delMediaUploadFiles(file_id) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${MEDIA_PATH + API_VERSION}/files/${workspace_id}/delete/${file_id}`,
    method: 'get',
  });
}
// 更新缩略图
export function updateThumbnails() { 
  return request({
    url: `${MEDIA_PATH + API_VERSION}/files/updateThumbnails/`,
    method: 'get',
  });
}
/**
 * 批量删除媒体库文件
 *
 * @param fileIds
 */
export function batchDeleteMedia(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${MEDIA_PATH + API_VERSION}/files/${workspace_id}/deleteByIds`,
    method: 'delete',
    data
  });
}
/**
 * 下载媒体文件
 */
export function downloadMediaFile(fileId) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${MEDIA_PATH + API_VERSION}/files/${workspace_id}/file/${fileId}/url`,
    method: 'get',
    responseType: 'blob'
  });
}

//获取模型库
export function getModelFiles(queryParams) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${ACHIEVEMENT_PATH + API_VERSION}/model/${workspace_id}/pages`,
    method: 'get',
    params: queryParams
  });
}
//删除模型库
export function deleteModelT(file_id) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${ACHIEVEMENT_PATH + API_VERSION}/model/${workspace_id}/del/${file_id}`,
    method: 'get',
  });
}
//修改模型库
export function updateModel(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${ACHIEVEMENT_PATH + API_VERSION}/model/${workspace_id}/update`,
    method: 'post',
    data
  });
}


//上传文件夹
export function uploadFiles(formData) {//文件夹上传
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${MODEL_PATH + API_VERSION}/files/${workspace_id}/uploadFile`,
    method: 'post',
    formData
  });
}

//获取宇视平台配置
export function getYSConfig(data) {
  return request({
    url: `${MAIN_PATH + API_VERSION}/deviceVideo/uniview/config`,
    method: 'get',
    params: data
  });
}
