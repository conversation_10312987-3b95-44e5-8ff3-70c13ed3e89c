// 118.046371,24.456022 地图左下角
// 118.22402,24.547694 地图右上角

// 使用示例
const leftBottom = [118.046371, 24.456022]; // 地图左下角坐标
const rightTop = [118.22402, 24.547694]; // 地图右上角坐标
const numberOfPoints = 50; // 你想要生成的点的数量
function generateRandomPointsPic() {
  // 定义图片数组
  const pics = [
    new URL('@/assets/test/1.png', import.meta.url).href,
    new URL('@/assets/test/2.png', import.meta.url).href,
    new URL('@/assets/test/3.png', import.meta.url).href,
    new URL('@/assets/test/4.png', import.meta.url).href,
    new URL('@/assets/test/5.png', import.meta.url).href,
    new URL('@/assets/test/6.png', import.meta.url).href,
    new URL('@/assets/test/7.png', import.meta.url).href,
    new URL('@/assets/test/8.png', import.meta.url).href
  ];

  // 生成一个随机索引
  const randomIndex = Math.floor(Math.random() * pics.length);
  // 返回随机选中的图片路径
  return pics[randomIndex];
}

function generateRandomPoints(leftBottom, rightTop, numberOfPoints) {
  const points = [];
  const leftLon = leftBottom[0];
  const bottomLat = leftBottom[1];
  const rightLon = rightTop[0];
  const topLat = rightTop[1];

  for (let i = 0; i < numberOfPoints; i++) {
    // 生成随机的经度和纬度
    const randomLat = bottomLat + Math.random() * (topLat - bottomLat);
    const randomLon = leftLon + Math.random() * (rightLon - leftLon);
    // 将生成的点添加到数组中
    points.push({
      latitude: randomLat,
      longitude: randomLon,
      lng: randomLon,
      lat: randomLat,
      file_url: generateRandomPointsPic(),
      jobId: 'fdsfdsaf ',
      createTime: '2024 08 12 12:00:00',
      id: i + 1,
      name: `Point ${i + 1}` // 可选，为每个点生成一个简单的名称
    });
  }
  return points;
}
export const randomPoints = generateRandomPoints(leftBottom, rightTop, numberOfPoints);
console.log(randomPoints);
