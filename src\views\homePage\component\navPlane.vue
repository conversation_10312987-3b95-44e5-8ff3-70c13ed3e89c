<script>
export default { name: 'NavPlane' };
</script>

<script setup>
import { ref, defineExpose } from 'vue';
import { EModeCode } from '@/views/map/map-fly-manager/components/osdInfo';
const emit = defineEmits(['OneClickDelivery']);

let dockInfo = reactive({
  nickname: '', //机场名
  dock_sn: '', //机场SN号
  dock_xyz: '', //机场XYZ
  device_nickname: '', //无人机名
  device_sn: '', //无人机SN号
  device_xyz: '', //无人机XYZ
  alarm_id: '', //警情ID
  flight_id: '', //飞行任务ID
  wayline_id: '' //航线文件ID
});

let osdInfo = reactive({
  mode_code: 14, // 状态
  capacity_percent: '--', //电量
  height: '--', //飞行高度
  horizontal_speed: '--', // 水平速度
  vertical_speed: '--', // 垂直速度
  gps_number: '--', //GPS数量
  rtk_number: '--', //RTK数量
  home_distance: '--' // 离机场距离
});

function oneClickDelibery() {
  emit('OneClickDelivery');
}

// 枚举获取值
const getEnumKey = (enumObject, value) => {
  return Object.keys(enumObject).find(key => enumObject[key] === value);
};

/**
 * 设置组件数据
 * @param {*} options {donkInfo:{},osdInfo:{}}
 */
const setComponentData = options => {
  if (options && typeof options === 'object') {
    const nowNavInfo = options.navInfo;
    for (const key in dockInfo) {
      if (nowNavInfo.hasOwnProperty(key)) {
        dockInfo[key] = nowNavInfo[key];
      }
    }
    const str = '--';
    const nowOsdInfo = options.osdInfo;
    if (nowOsdInfo?.mode_code === 14) {
      osdInfo.mode_code = 14;
      osdInfo.capacity_percent = '--';
      osdInfo.height = '--';
      osdInfo.horizontal_speed = '--';
      osdInfo.vertical_speed = '--';
      osdInfo.gps_number = '--';
      osdInfo.rtk_number = '--';
      osdInfo.home_distance = '--';
      return;
    }
    if (nowOsdInfo !== null && nowOsdInfo !== undefined) {
      osdInfo.mode_code = nowOsdInfo?.mode_code;
      osdInfo.capacity_percent = nowOsdInfo?.battery.capacity_percent ?? str;
      osdInfo.height = nowOsdInfo?.height.toFixed(2) ?? str;
      osdInfo.horizontal_speed = nowOsdInfo?.horizontal_speed.toFixed(2) ?? str;
      osdInfo.vertical_speed = nowOsdInfo?.vertical_speed.toFixed(2) ?? str;
      osdInfo.gps_number = nowOsdInfo?.position_state.gps_number ?? str;
      osdInfo.rtk_number = nowOsdInfo?.position_state.rtk_number ?? str;
      osdInfo.home_distance = nowOsdInfo?.home_distance.toFixed(2) ?? str;
    }
  }
};
// 对外抛出方法
defineExpose({
  setComponentData
});

onMounted(() => {});
onUnmounted(() => {});
onBeforeUnmount(() => {});
</script>

<template>
  <div style="height: 100%">
    <div class="alarm-title">
      <el-row>
        <el-col :span="2" class="nick">
          <svg-icon icon-class="drone" style="width: 14px; height: 14px; margin-left: 5px; margin-top: 12px" />
        </el-col>
        <el-col :span="10" class="nick ellipsis" :title="dockInfo.device_nickname">
          {{ dockInfo.device_nickname }}</el-col
        >
        <el-col :span="4"></el-col>
        <el-col :span="8">
          <div class="status" :class="{ 'is-active': osdInfo.mode_code === 14 }">
            {{ getEnumKey(EModeCode, osdInfo.mode_code) }}
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="info-container">
      <el-row class="c-row">
        <el-col :span="5" class="c-title">电量</el-col>
        <el-col :span="7" class="c-text">{{ osdInfo.capacity_percent + ' %' }} </el-col>
        <el-col :span="5" class="c-title">绝对高度</el-col>
        <el-col :span="7" class="c-text">{{ osdInfo.height + ' m' }}</el-col>
      </el-row>
      <el-row class="c-row">
        <el-col :span="5" class="c-title">水平速度</el-col>
        <el-col :span="7" class="c-text">{{ osdInfo.horizontal_speed + ' m/s' }}</el-col>
        <el-col :span="5" class="c-title">垂直速度</el-col>
        <el-col :span="7" class="c-text">{{ osdInfo.vertical_speed + ' m/s' }}</el-col>
      </el-row>
      <el-row class="c-row">
        <el-col :span="5" class="c-title">RTK</el-col>
        <el-col :span="7" class="c-text">{{ osdInfo.rtk_number }}</el-col>
        <el-col :span="5" class="c-title">卫星数量</el-col>
        <el-col :span="7" class="c-text">{{ osdInfo.gps_number }}</el-col>
      </el-row>
      <el-row class="c-row">
        <el-col :span="6" class="c-title" style="transform: translateX(-15px)">距离返航点</el-col>
        <el-col :span="6" class="c-text" style="transform: translateX(-15px)">{{
          osdInfo.home_distance + ' m'
        }}</el-col>
      </el-row>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.alarm-title {
  height: 21.6%;
  line-height: 40px;
  background: #11253e;
  color: #fff;
  font-family: SourceHanSansSC-Bold;
  font-size: 14px;
  text-align: left;
  font-weight: 700;
  border-bottom: 1px solid #344054;
  padding-left: 8px;
  .nick {
    font-size: 14px;
    color: #f5f6f8;
    text-align: left;
    font-weight: 400;
  }
  .status {
    margin-top: 7px;
    background: rgba(42, 139, 125, 0.5);
    border-radius: 2px;
    width: 110px;
    height: 24px;
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;
    color: #39bfa4;
    text-align: center;
    line-height: 22px;
    font-weight: 400;
    &.is-active {
      color: #98a2b3;
      background: rgba(44, 62, 86, 0.5);
    }
  }
}
.info-container {
  color: #fff;
  width: 100%;
  height: 16vh;
  background: #001129;
  // padding-top: 20px;
  .c-row {
    font-size: 16px;
    color: #e4e7ec;
    line-height: 22px;
    font-weight: 500;
    padding: 10px;
    padding-bottom: 0;
  }
  .c-title {
    text-align: right;
    font-family: SourceHanSansSC-Bold;
    font-size: 14px;
    color: #e4e7ec;
    text-align: right;
    line-height: 22px;
    font-weight: 700;
  }
  .c-text {
    text-align: left;
    padding-left: 14px;
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;
    color: #e4e7ec;
    text-align: left;
    line-height: 22px;
    font-weight: 400;
  }
}
.info-item {
  width: 50%;
  text-align: center;
}
.flex {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
</style>
