import { DeviceAdapterFactory } from './DeviceAdapterFactory';
import { DeviceAdapter } from './DeviceAdapter';
class DeviceParameterParser {
  static parseDeviceParams(deviceType) {
    if (!deviceType) {
      return null;
    }
    const adapter = DeviceAdapterFactory.getAdapter(deviceType) || null;
    if (adapter instanceof Error) {
      console.log('An error occurred:', result.message);
      return null;
    }
    if (adapter && adapter instanceof DeviceAdapter) {
      adapter.buildRequestParams();
    }
    return adapter;
  }
}
export { DeviceParameterParser };
