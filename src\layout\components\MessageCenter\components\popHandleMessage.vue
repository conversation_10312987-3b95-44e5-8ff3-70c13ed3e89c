<template>
  <el-dialog
    v-model="dialogVisible"
    v-if="dialogVisible"
    width="950px"
    :append-to-body="true"
    :close-on-click-modal="false"
    :before-close="dialogCancel"
    :title="$t('page.dialog.alarmHandling')"
  >
    <el-form ref="formRef" :model="formData" :rules="rules">
      <el-form-item label="" prop="handleContent">
        <el-input
          v-model="formData.handleContent"
          :rows="5"
          type="textarea"
          :maxlength="locale === 'zh' ? 20 : 100"
          show-word-limit
          :placeholder="$t('page.dialog.enterProcessingDescription')"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div>
        <el-button @click="dialogCancel">{{ $t('page.Cancel') }}</el-button>
        <el-button type="primary" :loading="submitLoading" @click="save()">
          {{ $t('page.confirm') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { ElMessage } from 'element-plus';

import { warnHandle } from '@/api/notice';

import useDialog from '@/hooks/useDialog';

import i18n from '@/lang';

import { validatenull } from '@/utils/helper';

const { locale } = useI18n();
const initFormData = () => ({
  handleContent: ''
});
const emit = defineEmits(['handleMessage']);

const formRef = ref('formRef');
const { dialogVisible, dialogClose, dialogOpen, formData, dialogCancel } =
  useDialog(formRef, { initFormData });

const rules = {
  handleContent: [
    {
      required: true,
      message: i18n.global.t('page.dialog.enterProcessingDescription'),
      trigger: ['change', 'blur']
    }
  ]
};
const submitLoading = ref(false);
const messageIds = ref([]);

const handleOpen = data => {
  messageIds.value = [];
  if (!validatenull(data)) {
    messageIds.value = data.map(item => item.noticeRecordId);
  }

  dialogOpen();
};

const save = () => {
  if (!messageIds.value.length) {
    ElMessage({
      message: i18n.global.t('page.dialog.proccessErrorTips'),
      customClass: 'custom-message-box',
      type: 'error'
    });
    return;
  }

  formRef.value.validate(async valid => {
    if (valid) {
      submitLoading.value = true;
      try {
        await warnHandle({
          noticeRecordIdList: messageIds.value,
          handleContent: formData.value.handleContent
        });
        ElMessage({
          message: i18n.global.t('page.dialog.processedSuccessfully'),
          type: 'success',
          customClass: 'custom-message-box'
        });
        emit('handleMessage', messageIds.value);
        dialogClose();
      } catch (err) {
        console.log(err);
      }
      submitLoading.value = false;
    } else {
      console.log('error submit!!');
      return false;
    }
  });
};

defineExpose({
  handleOpen
});
</script>
<style lang="scss" scoped></style>
