/**
 * AnimationFrameManager 帧动画管理
 */
class AnimationFrameManager {
  constructor() {
    this.isRunning = false;
    this.isPaused = false;
    this.callback = null;
    this.frameId = null;
  }

  // 启动
  start (callback) {
    if (!this.isRunning) {
      this.isRunning = true;
      this.callback = callback;
      this.requestFrame();
    }
  }
  // 停止
  stop () {
    if (this.isRunning) {
      this.isRunning = false;
      cancelAnimationFrame(this.frameId);
    }
  }

  // 暂停
  pause () {
    this.isPaused = true;
  }

  // 恢复
  resume () {
    if (this.isPaused) {
      this.isPaused = false;
      this.requestFrame();
    }
  }

  // 执行事件
  requestFrame () {
    this.frameId = requestAnimationFrame(() => {
      if (this.isRunning && !this.isPaused) {
        this.callback();
        this.requestFrame();
      }
    });
  }
}

export { AnimationFrameManager }
// // Usage
// const animationManager = new AnimationFrameManager();

// // 开启动画
// animationManager.start(() => {
//   // 在这里执行你的循环逻辑
// });

// // 暂停动画
// animationManager.pause();

// // 恢复动画
// animationManager.resume();

// // 停止动画
// animationManager.stop();
