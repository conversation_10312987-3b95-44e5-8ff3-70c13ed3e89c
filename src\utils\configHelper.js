import axios from 'axios';
export const configUrl = '/resource/sys_config/config.json';
export const deviceConfigUrl = '/resource/sys_config/device.json';
export const readRemoteJsonFile = () => {
  return axios
    .get(configUrl)
    .then(response => {
      return response.data;
    })
    .catch(error => {
      return Promise.reject(error); // 实际上，这里的return可以省略，因为catch已经隐式地拒绝了Promise
    });
};

export const loadDeviceJsonFile = async () => {
  try {
    const response = await axios.get(deviceConfigUrl);
    return response.data;
  } catch (error) {
    // 错误处理逻辑
    console.error('Failed to load device config:', error);
    throw error;
  }
};
