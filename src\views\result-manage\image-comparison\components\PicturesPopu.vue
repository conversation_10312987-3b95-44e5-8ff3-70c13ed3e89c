<template>
  <div class="research-dialog">
    <div class="research-dialog-header">选择图片</div>
    <div class="research-content">
      <div class="picture-item" v-for="(item, index) in comparePicturesRect.list">
        <div class="image-box" :class="{ active: selectedImageId === index }" @click="compareThisPiture(item, index)">
          <el-image class="img" style="width: 80px; height: 80px" :src="item.file_url" fit="fill" />
        </div>
      </div>
    </div>
    <div class="fotter">
      <el-button type="primary" @click="submit"> 确定 </el-button>
      <el-button type="info" @click="cancel">取消</el-button>
    </div>
  </div>
</template>
<script>
export default { name: 'PicturesPopu' };
</script>
<script setup>
import { defineEmits, onMounted, onUnmounted } from 'vue';
import { comparePicturesRect } from '../imageComparison';
import { ElMessage, ElMessageBox } from 'element-plus';
const selectedImage = ref(null);
const selectedImageId = ref('');
const emits = defineEmits(['compareChange']);

const cancel = () => {
  emits('compareChange', null);
};
const submit = () => {
  if (selectedImage.value) {
    emits('compareChange', selectedImage.value);
  } else {
    ElMessage.warning('请选择图片');
  }
};

const compareThisPiture = (item, index) => {
  selectedImageId.value = index;
  selectedImage.value = item;
};

onMounted(() => {});
onUnmounted(() => {});
</script>
<style lang="scss" scoped>
::v-deep .el-button--info {
  background-color: #344055 !important;
  border-color: #344055 !important;
  &:hover {
    background-color: #455672 !important;
  }
}

.research-dialog {
  overflow: hidden;
  width: 375px;
  height: 440px;
  z-index: 999;
  display: flex;
  flex-direction: column;
  .research-dialog-header {
    width: 100%;
    height: 40px;
    color: aliceblue;
    display: flex;
    align-items: center;
    padding-left: 15px;
    background-color: #14315b;
  }
  .research-content {
    width: 100%;
    flex: 1;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    align-content: flex-start;
    padding: 5px 0;
    overflow-x: hidden;
    overflow-y: scroll;
    .picture-item {
      width: 90px;
      height: 90px;
      padding: 5px;
      .image-box {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
  .fotter {
    padding: 15px 0px;
    color: aliceblue;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #14315b;
  }
}

.active {
  border: 1px solid rgba(0, 119, 255, 0.185);
  background-color: rgba(73, 175, 48, 0.596) !important;
}

/* 滚动条样式 -----*/
/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 8px !important;
}

/* 滚动槽 */
::-webkit-scrollbar-track {
  // -webkit-box-shadow: inset 0 0 6px rgba(190, 92, 92, 0.2);
  color: #175192;
  border-radius: 2px;
}

// 上箭头
// ::-webkit-scrollbar-button:start {
//   background-image: url('../../../../assets/up-arrow.png');
//   background-size: 14px !important;
//   background-repeat: no-repeat;
//   background-position: center center;
// }
// ::-webkit-scrollbar-button:end {
//   background-image: url('../../../../assets/down-arrow.png');
//   background-repeat: no-repeat;
//   background-size: 14px !important;
//   background-position: center center;
// }
/* 滚动条滑块（里面小方块） */
::-webkit-scrollbar-thumb {
  border-radius: 2px;
  // width: 12px !important;
  background: #175192 !important;
  -webkit-box-shadow: inset 0 0 6px #175192 !important;
}
</style>
