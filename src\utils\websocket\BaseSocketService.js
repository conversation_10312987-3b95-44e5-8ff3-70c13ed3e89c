import Stomp from 'stompjs';

class BaseSocketService {
  static instance = null;
  wsClient = null;
  wsTimer = null;
  isConnect = false;
  wsRequestUrl = null;

  constructor() {}
  static get Instance() {
    if (!this.instance) {
      this.instance = new BaseSocketService();
    }
    return this.instance;
  }

  //定义连接服务器的方法
  connect(successCallBack = () => {}, errorCallBack = () => {}) {
    if (!this.wsClient && !this.isConnect) {
      this.wsClient = Stomp.client(this.wsRequestUrl);
      this.wsClient.connect(
        {},
        () => {
          this.connectSuccess(successCallBack);
        },
        () => {
          this.connectError(successCallBack, errorCallBack);
        }
      );
    } else {
      successCallBack();
    }
  }
  send(desc, body) {
    if (this.wsClient && this.isConnect) {
      const obj = typeof obj !== 'string' ? JSON.stringify(body) : body;
      this.wsClient.send(desc, {}, obj);
    } else {
      console.error('ws未连接');
    }
  }
  subscribe(desc, callback = () => {}) {
    if (this.wsClient && this.isConnect) {
      this.wsClient.subscribe(desc, callback);
    } else {
      console.error('ws未连接');
    }
  }
  // 断开连接
  disconnect() {
    if (this.wsTimer) {
      clearInterval(this.wsTimer);
      this.wsTimer = null;
    }
    if (this.wsClient && this.isConnect) {
      try {
        this.wsClient.disconnect(() => {});
        this.wsClient = null;
        this.isConnect = false;
      } catch (error) {
        console.log(error);
      }
    }
  }

  connectSuccess(successCallBack) {
    console.log('ws连接成功');
    // 成功后可进行订阅通知
    this.isConnect = true;

    this.wsTimer = setInterval(() => {
      if (this.wsClient) {
        this.wsClient.send('ping');
      } else {
        clearInterval(this.wsTimer);
        this.wsTimer = null;
      }
    }, 570000);

    successCallBack();
  }
  connectError(successCallBack, errorCallBack) {
    console.log('ws连接失败');

    if (this.wsTimer) {
      clearInterval(this.wsTimer);
      this.wsTimer = null;
    }
    this.wsClient = null;
    this.isConnect = false;
    setTimeout(() => {
      // 连接失败重连
      console.log('ws重连');
      this.connect(successCallBack, errorCallBack);
    }, 1000);
  }
}
export default BaseSocketService;
