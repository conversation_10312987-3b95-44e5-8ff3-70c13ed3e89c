const PI = 3.1415926535897932384626;
const a = 6378245.0;
const ee = 0.00669342162296594323;
const byteSizes = ['B', 'K', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y'];

/**
 * 检查元素是否具有指定的类。
 *
 * @param {HTMLElement} ele - 要检查的元素。
 * @param {string} cls - 要检查的类名。
 * @returns {boolean} - 如果元素具有指定类，返回 true，否则返回 false.
 * <AUTHOR>
 */
export function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'));
}

/**
 * 向元素添加类。
 *
 * @param {HTMLElement} ele - 要添加类的元素。
 * @param {string} cls - 要添加的类名.
 * <AUTHOR>
 */
export function addClass(ele, cls) {
  if (!hasClass(ele, cls)) ele.className += ' ' + cls;
}

/**
 * 从元素中移除类。
 *
 * @param {HTMLElement} ele - 要移除类的元素.
 * @param {string} cls - 要移除的类名.
 * <AUTHOR>
 */
export function removeClass(ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp('(\\s|^)' + cls + '(\\s|$)');
    ele.className = ele.className.replace(reg, ' ');
  }
}

/**
 * 检查路径是否为外部链接.
 *
 * @param {string} path - 要检查的路径.
 * @returns {Boolean} - 如果路径为外部链接，返回 true，否则返回 false.
 * <AUTHOR>
 */
export function isExternal(path) {
  const isExternal = /^(https?:|http?:|mailto:|tel:)/.test(path);
  return isExternal;
}

export const DEFAULT_PLACEHOLDER = '--'; // 默认占位符

//  获取转化后数据及单位
export function getBytesObject(bytes, holder = DEFAULT_PLACEHOLDER, fix = 1) {
  if (isNaN(bytes) || bytes === 0) {
    return {
      value: holder,
      size: '',
      index: -1
    };
  }
  // 兼容负数
  let prefix = '';
  if (bytes < 0) {
    bytes = 0 - bytes;
    prefix = '-';
  }
  const k = 1024;
  const sizes = byteSizes; // ['B', 'K', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y']
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return {
    value: prefix + (bytes / Math.pow(k, i)).toFixed(fix),
    size: sizes[i],
    index: i
  };
}
export function transformlng(lng, lat) {
  var ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
  ret += ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0) / 3.0;
  ret += ((20.0 * Math.sin(lng * PI) + 40.0 * Math.sin((lng / 3.0) * PI)) * 2.0) / 3.0;
  ret += ((150.0 * Math.sin((lng / 12.0) * PI) + 300.0 * Math.sin((lng / 30.0) * PI)) * 2.0) / 3.0;
  return ret;
}

function transformlat(lng, lat) {
  var ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
  ret += ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0) / 3.0;
  ret += ((20.0 * Math.sin(lat * PI) + 40.0 * Math.sin((lat / 3.0) * PI)) * 2.0) / 3.0;
  ret += ((160.0 * Math.sin((lat / 12.0) * PI) + 320 * Math.sin((lat * PI) / 30.0)) * 2.0) / 3.0;
  return ret;
}

/**
 * Judge whether you are in the country or not if you are not in the country
 * @param lng
 * @param lat
 * @returns {boolean}
 */
function out_of_china(lng, lat) {
  return lng < 72.004 || lng > 137.8347 || lat < 0.8293 || lat > 55.8271 || false;
}
/**
 * WGS84 to Mars coordinate system GCj02
 * @param lng
 * @param lat
 * @returns {*[]}
 */
export function wgs84togcj02(lng, lat) {
  if (out_of_china(lng, lat)) {
    return [lng, lat];
  } else {
    var dlat = transformlat(lng - 105.0, lat - 35.0);
    var dlng = transformlng(lng - 105.0, lat - 35.0);
    var radlat = (lat / 180.0) * PI;
    var magic = Math.sin(radlat);
    magic = 1 - ee * magic * magic;
    var sqrtmagic = Math.sqrt(magic);
    dlat = (dlat * 180.0) / (((a * (1 - ee)) / (magic * sqrtmagic)) * PI);
    dlng = (dlng * 180.0) / ((a / sqrtmagic) * Math.cos(radlat) * PI);
    var mglat = lat + dlat;
    var mglng = lng + dlng;
    return [mglng, mglat];
  }
}

/**
 * 生成唯一guid
 * 类似 three模型 里面的ID 方式生成对应的key值
 * @returns XXXX-XXXX-XXXX-XXXX
 */
export function generateKey() {
  function S4() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }
  return S4() + S4() + '-' + S4() + '-' + S4() + '-' + S4() + '-' + S4() + S4() + S4();
}
/**
 * 获取当前项目hont URL
 * @returns 返回诚实URL字符串
 */
export function getHonstUrl() {
  let pathSuffix = '';
  if (import.meta.env.VITE_APP_NODE_ENV === 'development') {
    pathSuffix = 'http://125.77.202.162:24176';
  } else {
    pathSuffix = window.location.origin;
  }
  pathSuffix = pathSuffix + '/uavfile/';
  return pathSuffix;
}

/**
 * 获取当前资源的url
 * @param {*} url
 * @returns
 */
export function getResourceUrl(url = '') {
  let pathSuffix = getHonstUrl() + url;
  return pathSuffix;
}

/**
 * 检查鼠标是否在对应的元素内
 * @param {*} element
 * @param {*} mouseX
 * @param {*} mouseY
 * @returns
 */
export function isMouseInElementRect(element, mouseX, mouseY) {
  if (!element || !mouseX || !mouseY) {
    return false;
  }
  // 获取元素的边界
  const rect = element.getBoundingClientRect();
  // 检查鼠标坐标是否在元素的边界内
  const isInsideX = mouseX >= rect.left && mouseX <= rect.right;
  const isInsideY = mouseY >= rect.top && mouseY <= rect.bottom;

  // 如果鼠标在元素的水平和垂直边界内，返回 true，否则返回 false
  return isInsideX && isInsideY;
}
