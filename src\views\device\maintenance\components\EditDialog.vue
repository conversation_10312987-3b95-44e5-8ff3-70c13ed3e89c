<template>
  <el-dialog
    :title="title"
    v-if="visible"
    :model-value="visible"
    width="600px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form class="app-form" ref="dataFormRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="计划名称" prop="plan_name">
        <el-input
          v-model="form.plan_name"
          placeholder="请输入计划名称"
          maxlength="50"
          clearable
          @blur="form.plan_name = $event.target.value.trim()"
        />
      </el-form-item>
      <el-form-item label="计划执行时间" prop="range">
        <el-date-picker
          class="input-serach"
          v-model="form.range"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="机场或无人机" prop="nickname">
        <el-select v-model="form.nickname" placeholder="请选择机场或无人机" @change="selectUav" :fit-input-width="true">
          <el-option
            v-for="item in uavList"
            :key="item.device_sn"
            :label="item.nickname"
            :value="item.device_sn"
            clearable
          />
        </el-select>
      </el-form-item>
      <el-form-item label="设备SN" prop="device_sn">
        <el-input
          disabled
          v-model="form.device_sn"
          placeholder="请输入设备SN"
          maxlength="50"
          clearable
          @blur="form.device_sn = $event.target.value.trim()"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-loading="loading">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { addPlan, editPlan, getAirportAndDrone} from '@/api/devices/maintenance.js';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  formData: {
    type: Object,
    default(rawProps) {
      return {};
    }
  }
});
const form = reactive({});
const dataFormRef = ref(ElForm);
const uavList = ref([]);
watch(
  () => props.formData,
  (newVal, oldVal) => {
    dataFormRef.value && dataFormRef.value.clearValidate && dataFormRef.value.clearValidate();
    Object.assign(form, newVal);
    if(props.title == '编辑维保计划') {
      form.range = [form.start_date?.substring(0,10), form.end_date?.substring(0,10)];
    }
  },
  { deep: true }
);
watch(
  () => props.visible,
  (newVal, oldVal) => {
    if(newVal) {
      dataFormRef.value && dataFormRef.value.clearValidate && dataFormRef.value.clearValidate();
    }
  },
)
const emit = defineEmits(['update:visible', 'submit']);
const rules = reactive({
  plan_name: [{ required: true, message: '请输入计划名称', trigger: 'blur' }],
  range: [{ required: true, message: '请选择计划开始时间', trigger: ['change', 'blur'] }],
  nickname: [{ required: true, message: '请选择机场或无人机', trigger: ['change','blur'] }],
  device_sn: [{ required: true, message: '请输入设备SN', trigger: ['change','blur'] }],
});

const loading = ref(false);

// 关闭弹窗 
function closeDialog() {
  resetForm();
  emit('update:visible', false);
}

/**
 * 重置表单
 */
function resetForm() {
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  Object.keys(form).map(key => {
    delete form[key];
  });
}

function handleSubmit() {
  dataFormRef.value.validate(isValid => {
    if (isValid) {
      let params = { 
        ...form,
        start_date: form.range?.length === 2 ? `${form.range[0]} 00:00:00` : '',
        end_date: form.range?.length === 2 ? `${form.range[1]} 23:59:59` : '',
      };
      loading.value = true;
      if (props.title == '新增维保计划') {
        addPlan(params)
          .then(res => {
            loading.value = false;
            ElMessage.success('新增成功');
            closeDialog();
            emit('submit');
          })
          .catch(e => {
            loading.value = false;
          });
      } else {
        //编辑保单
        let params = {
          start_date: form.range?.length === 2 ? `${form.range[0]} 00:00:00` : '',
          end_date: form.range?.length === 2 ? `${form.range[1]} 23:59:59` : '',
          device_sn: form.device_sn,
          id: `${form.id}`,
          plan_name: form.plan_name
        };
        editPlan(params)
          .then(res => {
            loading.value = false;
            ElMessage.success('更新成功');
            closeDialog();
            emit('submit');
          })
          .catch(e => {
            loading.value = false;
          });
      }
    } else {
      loading.value = false;
    }
  });
}

//选择无人机列表
function selectUav(value) {
  form.device_sn = value;
}

//获取可选的无人机列表
function initAirportList() {
  getAirportAndDrone({
  })
    .then(res => {
      uavList.value = res;
    })
    .catch(e => {});
}
onMounted(() => {
  initAirportList();
  dataFormRef.value && dataFormRef.value.clearValidate && dataFormRef.value.clearValidate();
});

defineExpose({ resetForm });
</script>
<style scoped lang="scss">
:deep(.el-select-dropdown .el-select-dropdown__item:not(:hover)) {
  max-width: 450px;
}
:deep(.el-select-dropdown .el-select-dropdown__item:hover) {
  max-width: 450px;
}
.input-serach {
  width: 200px;
}
.fly-bottom {
  margin-top: 40px;
  text-align: center;
}
.app-form {
  .select-time {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 5px 0;
  }
  ::v-deep {
    .el-input-number,
    .el-select {
      width: 100%;
    }
    .el-input-number .el-input__inner {
      text-align: left;
    }
    .el-input-number.is-controls-right .el-input__wrapper {
      padding-left: 11px;
    }
    .el-date-editor.el-input,
    .el-date-editor.el-input__wrapper {
      width: 100%;
    }
  }
}
</style>
