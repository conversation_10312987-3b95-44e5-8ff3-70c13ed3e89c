<template>
  <!-- 机场OSD -->
  <div class="osd-panel" :style="{ position: isDialog ? 'absolute' : 'unset' }">
    <div class="osd-title">
      <span class="t-label">{{ nowOsdVisible.dock_callsign }} - {{ nowOsdVisible.device_callsign }}</span>
      <span v-if="showDeleteIcon" class="ff-cloud-icon clound-close t-close" @click="hideOSD()"></span>
    </div>
    <!-- 机场 -->
    <div class="dock-content m5">
      <el-row style="font-weight: bold">
        <el-col :span="12">
          机场_
          {{ nowOsdVisible.dock_sn }}
        </el-col>
        <el-col :span="2"> 状态: </el-col>
        <el-col
          :span="10"
          :style="
            nowDeviceInfo.dock.basic_osd?.mode_code === EDockModeCode.离线
              ? 'color: red; font-weight: 700;'
              : 'color: rgb(25,190,107)'
          "
        >
          <!-- :style="
            nowDeviceInfo.dock.basic_osd?.mode_code ===
            EDockModeCode.Disconnected
              ? 'color: red; font-weight: 700;'
              : 'color: rgb(25,190,107)'
          " -->
          {{ getEnumKey(EDockModeCode, nowDeviceInfo.dock.basic_osd?.mode_code) }}
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <span>累计运转时间:</span>
          <span class="ml10">
            <!-- 10368570 -->
            <span v-if="nowDeviceInfo.dock.work_osd?.acc_time >= 2592000">
              {{ Math.floor(nowDeviceInfo.dock.work_osd?.acc_time / 2592000) }}m
            </span>
            <span v-if="nowDeviceInfo.dock.work_osd?.acc_time % 2592000 >= 86400">
              {{ Math.floor((nowDeviceInfo.dock.work_osd?.acc_time % 2592000) / 86400) }}d
            </span>
            <span v-if="(nowDeviceInfo.dock.work_osd?.acc_time % 2592000) % 86400 >= 3600">
              {{ Math.floor(((nowDeviceInfo.dock.work_osd?.acc_time % 2592000) % 86400) / 3600) }}h
            </span>
            <span v-if="((nowDeviceInfo.dock.work_osd?.acc_time % 2592000) % 86400) % 3600 >= 60">
              {{ Math.floor((((nowDeviceInfo.dock.work_osd?.acc_time % 2592000) % 86400) % 3600) / 60) }}min
            </span>
            <span>{{ Math.floor((((nowDeviceInfo.dock.work_osd?.acc_time % 2592000) % 86400) % 3600) % 60) }}s</span>
          </span>
        </el-col>
        <el-col :span="12">
          <span>激活时间:</span>
          <span class="ml10"
            >{{ new Date((nowDeviceInfo.dock.work_osd?.activation_time ?? 0) * 1000).toLocaleString() }}
          </span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <span class="ff-cloud-icon clound-signal" :style="qualityStyle">
            <span v-if="nowDeviceInfo.dock.basic_osd?.network_state?.type === NetworkStateTypeEnum.FOUR_G"></span>
            <span v-else></span>
          </span>
          <span class="ml10">{{ nowDeviceInfo.dock.basic_osd?.network_state?.rate }} kb/s</span>
        </el-col>
        <el-col :span="6">
          <span>任务数量:</span>
          <span class="ml10">{{ nowDeviceInfo.dock.work_osd?.job_number }} </span>
        </el-col>
        <el-col :span="6">
          <span>媒体文件:</span>
          <span class="ml10">{{ nowDeviceInfo.dock.link_osd?.media_file_detail?.remain_upload }}</span>
        </el-col>
        <el-col :span="6">
          <el-tooltip placement="top">
            <template #content>
              <div>
                <p>全量: {{ nowDeviceInfo.dock.basic_osd?.storage?.total }}</p>
                <p>使用: {{ nowDeviceInfo.dock.basic_osd?.storage?.used }}</p>
              </div>
            </template>
            <div>
              <span>存储状态:</span>
              <span class="ml10" v-if="nowDeviceInfo.dock.basic_osd?.storage?.total > 0">
                <el-progress
                  type="circle"
                  :percentage="
                    (nowDeviceInfo.dock.basic_osd?.storage?.used * 100) / nowDeviceInfo.dock.basic_osd?.storage?.total
                  "
                  :stroke-width="2"
                  :width="20"
                  :show-text="false"
                  :color="
                    (nowDeviceInfo.dock.basic_osd?.storage?.used * 100) / nowDeviceInfo.dock.basic_osd?.storage?.total >
                    80
                      ? 'red'
                      : '#00ee8b'
                  "
                />
              </span>
            </div>
          </el-tooltip>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <span>风速:</span>
          <span class="ml10">{{ (nowDeviceInfo.dock.basic_osd?.wind_speed ?? str) + ' m/s' }}</span>
        </el-col>
        <el-col :span="6">
          <!-- 🌧 -->
          <span>雨量:</span>
          <span class="ml10">{{ getEnumKey(RainfallEnum, nowDeviceInfo.dock.basic_osd?.rainfall) }}</span>
        </el-col>
        <el-col :span="6">
          <span>环境°C</span>
          <span class="ml10">{{ nowDeviceInfo.dock.basic_osd?.environment_temperature }}</span>
        </el-col>
        <el-col :span="6">
          <span>机场°C</span>
          <span class="ml10">{{ nowDeviceInfo.dock.basic_osd?.temperature }}</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <!-- 💦 -->
          <span>湿度:</span>
          <span class="ml10">{{ nowDeviceInfo.dock.basic_osd?.humidity }}</span>
        </el-col>
        <el-col :span="6">
          <!-- <span
            style="
              border: 1px solid;
              border-radius: 50%;
              width: 18px;
              height: 18px;
              line-height: 20px;
              text-align: center;
              float: left;
            "
            >V</span
          > -->
          <span>电压:</span>
          <span class="ml10">{{ (nowDeviceInfo.dock.work_osd?.working_voltage ?? str) + ' mV' }}</span>
        </el-col>
        <el-col :span="6">
          <!-- <span
            style="
              border: 1px solid;
              border-radius: 50%;
              width: 18px;
              height: 18px;
              line-height: 15px;
              text-align: center;
              float: left;
            "
            >A</span
          > -->
          <span>电流:</span>
          <span class="ml10">{{ (nowDeviceInfo.dock.work_osd?.working_current ?? str) + ' mA' }}</span>
        </el-col>
        <el-col :span="6">
          <span>是否在机舱:</span>
          <span class="ml10">{{ nowDeviceInfo.dock.basic_osd?.drone_in_dock }}</span>
        </el-col>
      </el-row>
    </div>
    <!-- 飞机 -->
    <div class="dock-content dock-pdtop m5">
      <el-row style="font-weight: bold">
        <el-col :span="12">
          飞机_
          {{ nowOsdVisible.device_sn }}
        </el-col>
        <el-col :span="2"> 状态: </el-col>
        <el-col
          :span="10"
          :style="
            nowDeviceInfo.device?.mode_code === EModeCode.离线
              ? 'color: red; font-weight: 700;'
              : 'color: rgb(25,190,107)'
          "
        >
          {{ getEnumKey(EModeCode, nowDeviceInfo.device.mode_code) }}
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <span>上传速度:</span>
          <span class="ml10">{{ nowDeviceInfo.dock.link_osd?.sdr?.up_quality }}</span>
        </el-col>
        <el-col :span="6">
          <span>下载速度:</span>
          <span class="ml10">{{ nowDeviceInfo.dock.link_osd?.sdr?.down_quality }}</span>
        </el-col>
        <el-col :span="6">
          <span>电量:</span>
          <span class="ml10">{{
            nowDeviceInfo.device && nowDeviceInfo.device.battery.capacity_percent !== str
              ? nowDeviceInfo.device?.battery.capacity_percent + ' %'
              : str
          }}</span>
        </el-col>
        <el-col :span="6">
          <el-tooltip placement="top">
            <template #content>
              <div>
                <p>全量: {{ nowDeviceInfo.device?.storage?.total }}</p>
                <p>使用: {{ nowDeviceInfo.device?.storage?.used }}</p>
              </div>
            </template>
            <div>
              <span>存储状态:</span>
              <span class="ml10" v-if="nowDeviceInfo.device?.storage?.total > 0">
                <el-progress
                  type="circle"
                  :percentage="(nowDeviceInfo.device?.storage?.used * 100) / nowDeviceInfo.device?.storage?.total"
                  :stroke-width="2"
                  :width="20"
                  :show-text="false"
                  :color="
                    (nowDeviceInfo.device?.storage?.used * 100) / nowDeviceInfo.device?.storage?.total > 80
                      ? 'red'
                      : '#00ee8b'
                  "
                />
              </span>
            </div>
          </el-tooltip>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <span>Fixed</span>
          <span
            class="ml10 circle"
            :style="
              nowDeviceInfo.device?.position_state.is_fixed === 1 ? 'backgroud: rgb(25,190,107);' : ' background: red;'
            "
          ></span>
        </el-col>
        <el-col :span="6">
          <span>卫星数量</span>
          <span class="ml10">{{ nowDeviceInfo.device ? nowDeviceInfo.device.position_state.gps_number : str }}</span>
        </el-col>
        <el-col :span="6">
          <span>RTK</span>
          <span class="ml10">{{ nowDeviceInfo.device ? nowDeviceInfo.device.position_state.rtk_number : str }}</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <span>飞行模式:</span>
          <span class="ml10">{{ nowDeviceInfo.device ? EGear[nowDeviceInfo.device?.gear] : str }}</span>
        </el-col>
        <el-col :span="6">
          <span>ASL</span>
          <!-- <span>绝对高度</span> -->
          <span class="ml10">{{
            nowDeviceInfo.device.height === str ? str : nowDeviceInfo.device?.height.toFixed(2) + ' m'
          }}</span>
        </el-col>
        <el-col :span="6">
          <span>ALT</span>
          <!-- <span>相对起飞点高度</span> -->
          <span class="ml10">{{
            nowDeviceInfo.device.elevation === str ? str : nowDeviceInfo.device?.elevation.toFixed(2) + ' m'
          }}</span>
        </el-col>
        <el-col :span="6">
          <span
            style="
              border: 1px solid;
              border-radius: 50%;
              width: 18px;
              height: 18px;
              line-height: 18px;
              text-align: center;
              display: block;
              float: left;
            "
            >H</span
          >
          <span class="ml10">{{
            nowDeviceInfo.device.home_distance === str ? str : nowDeviceInfo.device?.home_distance.toFixed(2) + ' m'
          }}</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <!-- <span>H.S</span> -->
          <span>水平速度</span>
          <span class="ml10">{{
            nowDeviceInfo.device?.horizontal_speed === str
              ? str
              : nowDeviceInfo.device?.horizontal_speed.toFixed(2) + ' m/s'
          }}</span>
        </el-col>
        <el-col :span="6">
          <!-- <span>V.S</span> -->
          <span>垂直速度</span>
          <span class="ml10">{{
            nowDeviceInfo.device.vertical_speed === str ? str : nowDeviceInfo.device?.vertical_speed.toFixed(2) + ' m/s'
          }}</span>
        </el-col>
        <el-col :span="6">
          <!-- <span>W.S</span> -->
          <span>当前风速</span>
          <span class="ml10">{{
            nowDeviceInfo.device.wind_speed === str ? str : (nowDeviceInfo.device?.wind_speed / 10).toFixed(2) + ' m/s'
          }}</span>
        </el-col>
        <el-col :span="6">
          <!-- <span>W.S</span> -->
          <span>当前风向</span>
          <span class="ml10">{{ getEnumKey(WindDirectionEnum, nowDeviceInfo.device?.wind_direction) }}</span>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
export default {
  name: 'osd'
};
</script>
<script setup>
import { onMounted, onUnmounted, onBeforeUnmount, reactive } from 'vue';
import {
  deviceInfo,
  NetworkStateTypeEnum,
  NetworkStateQualityEnum,
  RainfallEnum,
  WindDirectionEnum,
  EModeCode,
  EDockModeCode,
  EGear
} from './osdInfo';
// import { EDockModeCode } from '@/utils/constants';
import { useDeviceStateStore } from '@/store/modules/deviceState.js';
const props = defineProps({
  isDialog: {
    // 是否弹窗显示
    type: Boolean,
    default: true
  },
  showDeleteIcon: {
    // 是否显示删除按钮
    type: Boolean,
    default: true
  }
});
const deviceStateStore = useDeviceStateStore();

const str = '--';

// 当前展示的设备信息
const nowOsdVisible = deviceStateStore.nowOsdVisible;

// 当前展示的设备信息
const nowDeviceInfo = reactive({
  dock:
    deviceStateStore.getDockBySn(nowOsdVisible.dock_sn) === undefined
      ? deviceInfo.dock
      : deviceStateStore.getDockBySn(nowOsdVisible.dock_sn),
  device:
    deviceStateStore.getNavBySn(nowOsdVisible.device_sn) === undefined
      ? deviceInfo.device
      : deviceStateStore.getNavBySn(nowOsdVisible.device_sn)
});

// 监听飞机上线后状态需要修改
watch(
  () => deviceStateStore.deviceState.device,
  async val => {
    if (val) {
      nowDeviceInfo.device = deviceStateStore.getNavBySn(nowOsdVisible.device_sn);
    }
  },
  { deep: true }
);

watch(
  () => deviceStateStore.deviceState.dock,
  async val => {
    if (val) {
      nowDeviceInfo.dock = deviceStateStore.getDockBySn(nowOsdVisible.dock_sn);
    }
  },
  { deep: true }
);

// 枚举获取值
const getEnumKey = (enumObject, value) => {
  return Object.keys(enumObject).find(key => enumObject[key] === value);
};

// 信号质量
const qualityStyle = computed(() => {
  if (
    nowDeviceInfo.dock?.basic_osd?.network_state?.type === NetworkStateTypeEnum.ETHERNET ||
    (nowDeviceInfo.dock?.basic_osd?.network_state?.quality || 0) > NetworkStateQualityEnum.FAIR
  ) {
    return 'color: #00ee8b';
  }
  if ((nowDeviceInfo.dock?.basic_osd?.network_state?.quality || 0) === NetworkStateQualityEnum.FAIR) {
    return 'color: yellow';
  }
  return 'color: red';
});

/**
 * 查看关闭OSD实时面板信息
 */
function hideOSD() {
  const osdinfo = {};
  osdinfo.visible = !nowOsdVisible.visible;
  osdinfo.dock_sn = '';
  osdinfo.device_sn = '';
  osdinfo.dock_callsign = '';
  osdinfo.device_callsign = '';
  deviceStateStore.setNowOsdVisible(osdinfo);
}

onMounted(() => {});
onUnmounted(() => {});
onBeforeUnmount(() => {});
</script>

<style lang="scss" scoped>
.osd-panel {
  position: absolute;
  margin-left: 10px;
  right: 5px;
  top: 10px;
  width: 580px;
  height: 300px;
  background: #11253e;
  color: #ffffff;
  border-radius: 5px;
  // opacity: 0.8;
}
.osd > div:not(.dock-control-panel) {
  margin-top: 5px;
  padding-left: 5px;
}
.osd-title {
  display: flex;
  color: #ffffff;
  border-bottom: 1px solid #515151;
  height: 40px;
  .t-label {
    margin: 8px;
    font-weight: bold;
    font-size: 18px;
  }
  .t-close {
    position: absolute;
    margin: 8px;
    right: 0px;
    cursor: pointer;
  }
}
.osd-type-panel {
  height: 120px;
  width: 100px;
  background: #2d2d2d;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.dock-panel {
  height: 120px;
  border-bottom: 1px solid #515151;
  display: flex;
}
.dock-content {
  height: 120px;
  width: 580px;
  cursor: pointer;
}
.dock-pdtop {
  padding-top: 5px;
  border-top: 1px solid #515151;
}
.circle {
  display: inline-block;
  width: 15px;
  height: 15px;
  border-radius: 50%;
}
</style>
