<template>
  <div class="section-wrapper bg-dark-blue">
    <div class="sidebar-section">
      <div class="section bg-light-blue">
        <div class="section-custom">
          <span>设置参考起飞点</span>
          <span class="blue" @click="setAirPlaceHandler()">
            {{ isAlreadySetAirPlace ? '设置起飞点' : '取消设置' }}
          </span>
        </div>
      </div>
      <!-- 采集方式 -->
      <div class="section bg-light-blue">
        <div class="section-wrapper">
          <div class="section-header">
            <div class="title">采集方式</div>
          </div>
          <div class="section-content">
            <div
              class="section-content-item"
              :class="{ active: parmsData.templateType === TEMPLATE_TYPE_ENUM.mapping2d }"
              @click="collectionWayHandler(TEMPLATE_TYPE_ENUM.mapping2d)"
            >
              正射影像
            </div>
          </div>
        </div>
      </div>
      <!-- 镜头类型选择 -->
      <div class="section bg-light-blue">
        <CameraSelect v-model="parmsData" @changeHandle="onCameraTypeChangeHandle" />
      </div>

      <div class="section bg-light-blue">
        <GSD v-model="parmsData" @changeHandle="onGSDChangeHandle" />
      </div>

      <!-- 安全起飞高度 -->
      <div class="section bg-light-blue">
        <Numbers v-model="takeOffSecurityHeightDataRect" @changeHandle="onTakeOffSecurityHeightChangeHandle" />
      </div>
      <!-- 航线高度模式 -->
      <div class="section bg-light-blue">
        <div class="section-custom-column">
          <div class="mb-10">航线高度模式</div>
          <div>
            <el-select
              v-model="heightModeDataRect.value"
              placeholder="请选择"
              style="width: 100%"
              :clearable="false"
              @change="onHeightModeChangeHandle"
            >
              <el-option
                v-for="item in heightModeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
              />
            </el-select>
          </div>
          <div style="padding-top: 10px">
            <!-- 测试-机场高度： {{ parmsData.value.airPortPlaceASL }} m -->
            <Numbers v-model="flightHightDataRect" @changeHandle="onFlightHightChangeHandle" />
          </div>
          <div style="padding-top: 10px"><el-image :src="heightModeImageCmpt" /></div>
          <span class="warning">全球高程数据仅供参考，请注意飞行安全。</span>
        </div>
      </div>
      <!-- 航线速度 -->
      <div class="section bg-light-blue">
        <Numbers v-model="flightSpeedDataRect" @changeHandle="onFlightSpeedChangeHandle" />
      </div>
      <!-- 航线角度 -->
      <div class="section bg-light-blue">
        <Slide v-model="directionDataRect" @changeHandle="onDirectionChangeHandle" />
      </div>
      <div class="section bg-light-blue">
        <div class="section-custom">
          <span>高程优化</span>
          <el-tooltip
            class="box-item"
            effect="dark"
            content="开启后，飞机会在航线飞行完毕后，飞向测区中心采集一组倾斜照片，最终模型将有更准确的高度信息"
            placement="right"
          >
            <el-switch v-model="elevationOptimizationDataRect.value" @change="onElevationOptimizaChangeHandle" />
          </el-tooltip>
        </div>
      </div>
      <div class="section bg-light-blue">
        <div class="section-custom-column">
          <div class="mb-10">完成动作</div>
          <div>
            <el-select
              v-model="finishActionDataRect.value"
              placeholder="请选择"
              style="width: 100%"
              :clearable="false"
              @change="onFinishActionChangeHandle"
            >
              <el-option
                v-for="item in flightDoneOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
              />
            </el-select>
          </div>
        </div>
      </div>
      <div class="section bg-light-blue">
        <div class="section-custom-column">
          <div class="section-custom-header" @click="openContentHandler">
            <div class="v-center">高级设置</div>
            <div class="arrow v-center" :class="{ 'rotate-180': isOpen }">
              <el-icon><ArrowUpBold /></el-icon>
            </div>
          </div>
          <div v-show="isOpen">
            <div class="section-advanced-setting bg-light-blue">
              <Numbers v-model="startflightSpeedDataRect" @changeHandle="onStartFlightSpeedChangeHandle" />
            </div>
            <!-- 被摄面绝对高度 -->
            <!-- <div class="section-advanced-setting">
              <Numbers v-model="projectAltitudeHeightDataRect" @changeHandle="onProjectAltitudeHeightChangeHandle" />
            </div> -->
            <div class="section-advanced-setting bg-light-blue">
              <!-- 旁向重叠率 -->
              <Numbers v-model="sideOverlapDataRect" @changeHandle="onSideOverlapChangeHandle" />
            </div>
            <div class="section-advanced-setting bg-light-blue">
              <Numbers v-model="courseOverlapDataRect" @changeHandle="onCourseOverlapChangeHandle" />
            </div>
            <div class="section-advanced-setting bg-light-blue">
              <Numbers v-model="marginDataRect" @changeHandle="onMarginChangeHandle" />
            </div>
            <div class="section-advanced-setting bg-light-blue">
              <div class="section-custom-column">
                <div class="mb-10">拍照模式</div>
                <div>
                  <el-select
                    v-model="shootTypeDataRect.value"
                    placeholder="请选择"
                    style="width: 100%"
                    :clearable="false"
                    @change="onShootTypeChangeHandle"
                  >
                    <el-option
                      v-for="item in shootTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      :disabled="item.disabled"
                    />
                  </el-select>
                </div>
              </div>
            </div>
            <!-- <div class="section-advanced-setting">
              <div class="section-custom">
                <span>航线绕行 </span>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="航线绕行开启后,飞行器在执行航线过程中遇到障碍物将尝试绕行，若绕行失败将中断航线。绕行过程中可能造成建图成果不符合预期。"
                  placement="right"
                >
                  <el-switch v-model="parmsData.autoRerouteInfo" />
                </el-tooltip>
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ParmsSection'
};
</script>
<script setup>
import '../style/common.css';
import { onMounted, onUnmounted, computed } from 'vue';
import 'element-plus/dist/index.css';
import { ArrowUpBold } from '@element-plus/icons-vue';
import Numbers from './Numbers.vue';
import Slide from './Slide.vue';
import CameraSelect from '@/views/plan/components/CameraSelect.vue';
import GSD from '@/views/plan/components/GSD.vue';
import { caculateGSDByHeight, isAlreadySetAirPlace } from '../hocks/index.js';
import { shootTypeOptions, HEIGHT_MODE_IMAGE_ENUM, flightDoneOptions, heightModeOptions } from '../props/config.js';
import { FINISH_ACTION, HEIGHT_MODE_ENUM, SHOOTTYPE_ENUM, TEMPLATE_TYPE_ENUM } from '../../newplan/kmz/props';
import { toNumber } from '@/components/Cesium/libs/cesium';
import { ElMessage } from 'element-plus';
import { removeMapToolTips, setMapToolTips } from '../../common/tips';
//#region 数据双向绑定
// 传入组件数据结构
// const parmsInfoRect = reactive({
//   alreadySetAirPlace:false,//是否设置机场位置
//   planName: '新建航线',
//   cameraType: [CAMERA_TYPE_ENUM.visable, cameraTypeEnum.ir],
//   templateType: TEMPLATE_TYPE_ENUM.mapping2d, // 正射影像
//   takeOffSecurityHeight: 2,
//   autoFlightSpeed: 9,
//   executeHeightMode: '绝对高度',
//   direction: 0,
//   elevationOptimization: true,
//   finishAction: '自动返航',
//   globalTransitionalSpeed: 2,
//   orthoCameraOverlapW: 70,
//   orthoCameraOverlapH: 80,
//   margin: 0,
//   shootType: '等时间隔拍照',
//   autoRerouteInfo: true,
//   space: 10
// });

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {};
    }
  }
});

const parmsData = computed({
  get: () => props.modelValue,
  set: value => {
    emits('update:modelValue', value);
  }
});

const heightModeImageCmpt = computed(() => {
  return HEIGHT_MODE_IMAGE_ENUM[parmsData.value.heightMode] || HEIGHT_MODE_IMAGE_ENUM[HEIGHT_MODE_ENUM.EGM96];
});

// 对外定义事件
const emits = defineEmits(['update:modelValue', 'setAirPlace', 'parmsChangeHandle']); // 触发事件
// 对外触发变更事件
const setParmsChange = v => {
  try {
    let parmsInfo = parmsData.value;
    emits('parmsChangeHandle', parmsInfo);
  } catch (error) {
    console.log('error:', error);
  }
};
//#endregion

//#region 起飞点是否设置
// 设置飞机位置
const setAirPlaceHandler = () => {
  if (isAlreadySetAirPlace.value) {
    setMapToolTips('点击地图设置起飞点');
  } else {
    removeMapToolTips();
  }
  emits('setAirPlace', isAlreadySetAirPlace.value);
  isAlreadySetAirPlace.value = !isAlreadySetAirPlace.value;
};
//#endregion

//#region 镜头类型选择
// 镜头类型选择
const onCameraTypeChangeHandle = cameraSelected => {
  // 最后找出 所有激活的相机类型
  parmsData.value.cameraType.length = 0;
  (cameraSelected || []).forEach(item => {
    parmsData.value.cameraType.push(item);
    parmsData.value.gsd[item] = caculateGSDByHeight(item, parmsData.value.realFlightHight);
  });
  setParmsChange();
};

//#endregion

//#region 采集类型选择
// 镜头类型选择
const collectionWayHandler = item => {
  if (item === TEMPLATE_TYPE_ENUM.mapping3d) {
    // 提示
    ElMessage({
      message: '暂不支持倾斜摄影采集',
      type: 'warning'
    });
    return;
  }
};
//#endregion

//#region GSD
/**
 * GSD 计算变更处理
 * @param option 对象
 */
const onGSDChangeHandle = option => {
  if (!option) {
    return;
  }
  parmsData.value.flightHight = option.height ?? 0;
  // 根据高程模式获取无人机的高度
  const { realFlightHight } = cacalateHeight();
  parmsData.value.realFlightHight = realFlightHight;
  flightHightDataRect.value = option.height;
  setParmsChange();
};

//#endregion

//#region 安全起飞高度
const takeOffSecurityHeightDataRect = computed(() => {
  return {
    title: '安全起飞高度',
    unit: 'm',
    min: 2,
    max: 1500.0,
    value: parmsData.value.takeOffSecurityHeight, //
    range: [100, 10, 1], //
    step: 1
  };
});

const onTakeOffSecurityHeightChangeHandle = value => {
  // 将 value 转换为数字
  let numericValue = Number(value).toFixed(0);
  parmsData.value.takeOffSecurityHeight = toNumber(numericValue, 0);
  setParmsChange();
};
//#endregion

//#region 高程模式
const heightModeDataRect = computed(() => {
  return {
    value: parmsData.value.heightMode || HEIGHT_MODE_ENUM.EGM96 //   椭球高模式
  };
});
const onHeightModeChangeHandle = value => {
  parmsData.value.heightMode = value;
  // 根据高程模式获取无人机的高度
  const { flightHight, realFlightHight } = cacalateHeight();
  parmsData.value.flightHight = flightHight;
  parmsData.value.realFlightHight = realFlightHight;
  setParmsChange();
};

const cacalateHeight = () => {
  let flightHight = 0;
  let realFlightHight = 0;
  // 根据高程模式获取无人机的高度
  switch (parmsData.value.heightMode) {
    case HEIGHT_MODE_ENUM.EGM96:
      // 绝对高度
      flightHight = Number(parmsData.value.flightHight + Number(parmsData.value.airPortPlaceASL));
      // 实际飞机高度
      realFlightHight = Number(flightHight);
      break;
    case HEIGHT_MODE_ENUM.relativeToStartPoint:
      // 实际飞机高度
      realFlightHight = Number(parmsData.value.flightHight);
      // 相对起飞点高度 减去机场高度
      flightHight = Number(parmsData.value.flightHight - Number(parmsData.value.airPortPlaceASL));
      break;
    default:
      break;
  }
  return {
    flightHight: flightHight || 0,
    realFlightHight: realFlightHight || 0
  };
};

//#endregion

//#region 航线高度 设置数字组件数据
// const flightHightDataRect = reactive({
//   title: '航线高度',
//   unit: 'm',
//   min: 20,
//   max: 10000.0,
//   value: parmsData.value.flightHight,
//   realValue: parmsData.value.realFlightHight,
//   range: [100, 10, 1], //
//   step: 1
// });

const flightHightDataRect = computed(() => {
  return {
    title: '航线高度',
    unit: 'm',
    min: 20,
    max: 10000.0,
    value: parmsData.value.flightHight,
    realValue: parmsData.value.realFlightHight,
    range: [100, 10, 1],
    step: 1
  };
});

const onFlightHightChangeHandle = value => {
  // 将 value 转换为数字
  let numericValue = Number(value).toFixed(0);
  // 这里通过选着不同的高度模式设置不同的高度值
  parmsData.value.flightHight = toNumber(numericValue, 0);
  // 这里通过高度计算
  const { realFlightHight } = cacalateHeight();
  // 更新GSD
  window.$bus.emit('caculateHeight', realFlightHight);
  setParmsChange();
};

//#endregion

//#region 全局飞行速度
const flightSpeedDataRect = computed(() => {
  return {
    title: '全局航线速度',
    unit: 'm/s',
    min: parmsData.value.minSpeed || 1,
    max: parmsData.value.maxSpeed || 15.0,
    value: parmsData.value.autoFlightSpeed, //
    range: [1, 0.1], // 从大到小排布
    step: 0.1
  };
});

const onFlightSpeedChangeHandle = value => {
  parmsData.value.autoFlightSpeed = toNumber(value, 1);
  setParmsChange();
};

// 监听 parmsData.value.autoFlightSpeed 的变化并同步更新 flightSpeedDataRect.value
watch(
  () => parmsData.value.autoFlightSpeed,
  newVal => {
    // flightSpeedDataRect.value = newVal;
    // flightSpeedDataRect.max = newVal;
  }
);

//#endregion

//#region 主航线角度
const directionDataRect = computed(() => {
  return {
    title: '主航线角度',
    unit: '°',
    min: 0,
    max: 359.0,
    value: parmsData.value.direction, //
    step: 1
  };
});

const onDirectionChangeHandle = value => {
  // 将 value 转换为数字
  let numericValue = Number(value).toFixed(0);
  parmsData.value.direction = toNumber(numericValue, 0);
  setParmsChange();
};
//#endregion

//#region 高程优化
const elevationOptimizationDataRect = computed(() => {
  return {
    value: parmsData.value.elevationOptimization
  };
});
const onElevationOptimizaChangeHandle = v => {
  parmsData.value.elevationOptimization = v; //
  setParmsChange();
};
//#endregion

//#region 完成动作

const finishActionDataRect = computed(() => {
  return {
    value: parmsData.value.finishAction || FINISH_ACTION.goHome
  };
});
const onFinishActionChangeHandle = value => {
  parmsData.value.finishAction = value;
  setParmsChange();
};
//#endregion

//#region 高级设置
const isOpen = ref(false);
const openContentHandler = () => {
  isOpen.value = !isOpen.value;
};

//#region 起飞速度
const startflightSpeedDataRect = computed(() => {
  return {
    title: '起飞速度',
    unit: 'm/s',
    min: 1,
    max: 15.0,
    value: parmsData.value.globalTransitionalSpeed || 15,
    range: [1], // 从大到小排布
    step: 1
  };
});
const onStartFlightSpeedChangeHandle = value => {
  parmsData.value.globalTransitionalSpeed = value;
  setParmsChange();
};
//#endregion

//#region 被摄面绝对高度
const projectAltitudeHeightDataRect = reactive({
  title: '被摄面绝对高度',
  unit: 'm',
  min: 0.0,
  max: 10000.0,
  value: parmsData.value.projectAltitudeHeigh || 0, // 默认最小单位值
  range: [100, 10, 1], // 从大到小排布
  step: 1
});
const onProjectAltitudeHeightChangeHandle = value => {
  parmsData.value.projectAltitudeHeigh = value;
  setParmsChange();
};
//#endregion

//#region 旁向重叠率
const sideOverlapDataRect = computed(() => {
  return {
    title: '旁向重叠率',
    unit: '%',
    min: 10.0,
    max: 90.0,
    value: parmsData.value.orthoCameraOverlapW || 70, // 默认最小单位值
    range: [10, 5, 1], // 从大到小排布
    step: 1
  };
});
const onSideOverlapChangeHandle = value => {
  parmsData.value.orthoCameraOverlapW = value;
  setParmsChange();
};
//#endregion

//#region 航向重叠率 Course overlap
const courseOverlapDataRect = computed(() => {
  return {
    title: '航向重叠率',
    unit: '%',
    min: 10.0,
    max: 90.0,
    value: parmsData.value.orthoCameraOverlapH || 80, //
    range: [10, 5, 1], // 从大到小排布
    step: 1
  };
});
const onCourseOverlapChangeHandle = value => {
  parmsData.value.orthoCameraOverlapH = value;
  setParmsChange();
};
//#endregion

//#region 边距
const marginDataRect = computed(() => {
  return {
    title: '边距',
    unit: 'm',
    min: 0.0,
    max: 200.0,
    value: parmsData.value.margin || 0, //
    range: [100, 10, 1], // 从大到小排布
    step: 1
  };
});

const onMarginChangeHandle = value => {
  parmsData.value.margin = value;
  setParmsChange();
};
//#endregion

//#region 拍照模式

const shootTypeDataRect = computed(() => {
  return {
    value: parmsData.value.shootType || SHOOTTYPE_ENUM.time // '等时间隔拍照'
  };
});

const onShootTypeChangeHandle = value => {
  parmsData.value.shootType = value;
  setParmsChange();
};
//#endregion

//#endregion

//#region 生命周期

const init = () => {
  // 相机类型初始化
  if (parmsData.value.cameraType && parmsData.value.cameraType.length > 0) {
    // 检查parmsData.value中是否存在 gsd
    if (!parmsData.value.gsd) {
      parmsData.value.gsd = {};
    }
    // 在parmsData.value.cameraType 中进行遍历 检查 parmsData.value.gsd 是否存在其中项 不存在则添加 并且赋值为0
    parmsData.value.cameraType.forEach(ct => {
      if (!parmsData.value.gsd[ct]) {
        parmsData.value.gsd[ct] = 5;
      }
    });
  }
};

onMounted(() => {
  init();
});
onUnmounted(() => {});
//#endregion
</script>
<style lang="scss" scoped>
::v-deep.el-input {
  background-color: #313131;
  color: #dadada;
}

::v-deep.el-input .el-input__inner {
  font-size: 16px !important;
  color: #dadada !important;
}
::v-deep .el-input-numbert.is-disabled .el-input__wrapper {
  background-color: #11253e;
}
::v-deep.el-input .el-input__wrapper {
  background-color: #11253e;
  box-shadow: 0 0 0 1px #cfcfcf8f inset;
}

::v-deep .el-select .el-input {
  border: 1px solid #cfcfcf8f !important;
}
::v-deep .el-select .el-input__inner {
  border: 1px solid transparent !important;
  color: #dadada !important;
}
::v-deep .el-select .el-input__wrapper {
  background-color: #11253e !important;
  color: #dadada;
  box-shadow: none !important;
}
::v-deep .el-select .el-input .el-select__caret {
  color: #fff !important;
}
::v-deep .el-input .el-input__wrapper.is-focus {
  box-shadow: none !important;
}

.section-wrapper {
  color: white;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  user-select: none;
  padding: 0px 0px;
  .sidebar-section {
    width: 100%;
    height: 100%;
    padding: 10px 10px;
    overflow-y: auto;
    user-select: none;
  }
}
.section-wrapper .section-header {
  .title {
    font-size: 16px;
  }
}
.section-wrapper .section-content {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 10px;
  .section-content-item {
    height: 30px;
    border-radius: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
    background-color: #555658;
  }
}
.section-content-item:not(:first-child) {
  margin-left: 10px;
}

.section {
  margin: 0px 0px;
  width: 100%;
  padding: 10px 10px;
  border-radius: 10px;
  background-color: #232323;
}
.section:not(:first-child, :last-child) {
  margin: 15px 0px;
}

.section-advanced-setting {
  margin: 0px 0px;
  width: 100%;
  padding: 10px 0px;
  border-radius: 10px;
  background-color: #232323;
}
.section-advanced-setting:not(:first-child, :last-child) {
  margin: 5px 0px;
}

/* 滚动条样式 */
/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 8px !important;
}
/* 滚动槽 */
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(190, 92, 92, 0.2);
  color: #175192;
  border-radius: 2px;
}

// 上箭头
// ::-webkit-scrollbar-button:start {
//   // background-color: slateblue;
//   background-image: url('../../../../assets/up-arrow.png');
//   background-size: 14px !important;
//   background-repeat: no-repeat;
//   background-position: center center;
// }
// ::-webkit-scrollbar-button:end {
//   background-image: url('../../../../assets/down-arrow.png');
//   background-repeat: no-repeat;
//   background-size: 14px !important;
//   background-position: center center;
// }
/* 滚动条滑块（里面小方块） */
::-webkit-scrollbar-thumb {
  border-radius: 2px;
  width: 12px !important;
  background: #175192 !important;
  -webkit-box-shadow: inset 0 0 6px #175192 !important;
}

.blue {
  color: #2d8cf0;
  &:hover {
    color: #4d9df3;
  }
}
.section-custom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0px;
}
.section-custom-column {
  padding: 10px 0px;
}

.section-custom-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 5px;
}

.active {
  background-color: #2d8cf0 !important;
}

.mb-10 {
  margin-bottom: 10px;
}
.padding-10 {
  padding: 10px;
}
.v-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow {
  cursor: pointer;
  transition: transform 0.3s ease-in-out;
}

.arrow.rotate-180 {
  transform: rotate(180deg);
}

.warning {
  color: #f90;
  margin-top: 8px;
  font-size: 16px;
  line-height: 20px;
  font-weight: 400;
}
</style>
