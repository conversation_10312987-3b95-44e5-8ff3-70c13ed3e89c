// 舱盖状态
export const CoverStateEnum = {
  Close: 0, // 关闭
  Open: 1, // 打开
  HalfOpen: 2, // 半打开
  Failed: 3 // 失败
};

export const EComponentName = {
  Thing:'thing',
  Liveshare: 'liveshare',
  Api: 'api',
  Ws: 'ws',
  Map: 'map',
  Tsa: 'tsa',
  Media: 'media',
  Mission: 'mission'
}

export const ELocalStorageKey = {
  Username : 'username',
  WorkspaceId : 'workspace_id',
  Token : 'accessToken',
  PlatformName : 'platform_name',
  WorkspaceName : 'workspace_name',
  WorkspaceDesc : 'workspace_desc',
  Flag : 'flag',
  UserId : 'user_id',
  Device : 'device',
  GatewayOnline : 'gateway_online',
}

export const EUserType = {
  Web: 1,
  Pilot: 2,
}

export const EVideoPublishType  = {
  VideoOnDemand: 'video-on-demand',
  VideoByManual: 'video-by-manual',
  VideoDemandAuxManual: 'video-demand-aux-manual'
}

export const ERouterName = {
  ELEMENT: 'element',
  PROJECT: 'project',
  HOME: 'home',
  TSA: 'tsa',
  LAYER: 'layer',
  MEDIA: 'media',
  WAYLINE: 'wayline',
  LIVESTREAM: 'livestream',
  LIVING: 'living',
  WORKSPACE: 'workspace',
  MEMBERS: 'members',
  DEVICES: 'devices',
  TASK: 'task',
  CREATE_PLAN: 'create-plan',
  SELECT_PLAN: 'select-plan',
  FIRMWARES: 'firmwares',
  FLIGHT_AREA: 'flight-area',
  PILOT: 'pilot-login',
  PILOT_HOME: 'pilot-home',
  PILOT_MEDIA: 'pilot-media',
  PILOT_LIVESHARE: 'pilot-liveshare',
  PILOT_BIND: 'pilot-bind'
}

export const EPhotoType = {
  Original: 0,
  Preview: 1,
  Unknown: -1
}

// 推杆状态
export const PutterStateEnum = {
  Close: 0, // 关闭
  Open: 1, // 打开
  HalfOpen: 2, // 半打开
  Failed: 3 // 失败
};

// 充电状态
export const ChargeStateEnum = {
  NotCharge: 0, // 空闲
  Charge: 1 // 正在充电
};

// 补光灯状态
export const SupplementLightStateEnum = {
  Close: 0, // 关闭
  Open: 1 // 打开
};

// 机场声光报警状态
export const AlarmModeEnum = {
  CLOSE: 0, // 关闭
  OPEN: 1 // 开启
};

// 电池保养
export const BatteryStoreModeEnum = {
  BATTERY_PLAN_STORE: 1, // 电池计划存储策略
  BATTERY_EMERGENCY_STORE: 2 // 电池应急存储策略
};

// 飞行器电池保养
export const DroneBatteryStateEnum = {
  NoMaintenanceRequired: 0, // 0-无需保养
  MaintenanceRequired: 1, // 1-待保养
  MaintenanceInProgress: 2 // 2-正在保养
};

export const DroneBatteryModeEnum = {
  CLOSE: 0, // 关闭
  OPEN: 1 // 开启
};

// 4g链路连接状态
export const FourGLinkStateEnum = {
  CLOSE: 0, // 断开
  OPEN: 1 // 连接
};

//  Sdr链路连接状态
export const SdrLinkStateEnum = {
  CLOSE: 0, // 断开
  OPEN: 1 // 连接
};

// 机场的图传链路模式
export const LinkWorkModeEnum = {
  SDR: 0, // sdr模式
  FourG_FUSION_MODE: 1 // 4G融合模式
};
