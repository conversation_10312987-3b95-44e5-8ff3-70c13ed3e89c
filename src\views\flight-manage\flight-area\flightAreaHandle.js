import { getFllightAreas } from '@/api/flightArea';
import { reactive } from 'vue';
import {
  getOrCreateCesiumEngineInstance,
  CesiumLayerManager,
  imglayer,
  cialayer,
  getPropertyByKey,
  ScreenEventHandler,
  ScreenSpaceEventTypes,
  toNumber,
  zoomtoLonglats,
  calculatArea,
  calculatPerimeter,
  setCameraView
} from '../../../components/Cesium/libs/cesium';
import * as Cesium from 'cesium';
import {
  cancelCircleEdit,
  isCircleEdit,
  editCircle,
  closeCircleEdit
} from '@/components/Cesium/libs/cesium/superEdit/CircleEditer';
import { getDeptSysSetting } from '@/api/wayline';
import { drawCircle, circleDispose, isCircleCreate } from '@/components/Cesium/libs/cesium/superEdit/CircleCreater';
import {
  editPolygon,
  cancelPolygonEdit,
  isPolygonEdit,
  closePolygonEdit
} from '@/components/Cesium/libs/cesium/superEdit/PolygonEditer';
import { drawPolygon, isPolygonCreate, polygonDispose } from '@/components/Cesium/libs/cesium/superEdit/PolygonCreater';
import { addCircleFlightArea, getFlightDataList } from './flightAreaAxiosHandle';
import { createCircle, createPolygon, getFeatureColorString } from './gemotryHandle';
import { ElMessage } from 'element-plus';
export let viewer = null;
export let scene = null;
export let eventHandler = null;
export let editStatue = ref(false);
export const iconSource = {
  circle: new URL('@/assets/flightArea/svg/circle.svg', import.meta.url),
  polygon: new URL('@/assets/flightArea/svg/polygon.svg', import.meta.url),
  circle_limit: new URL('@/assets/flightArea/svg/circle_limit.svg', import.meta.url),
  polygon_limit: new URL('@/assets/flightArea/svg/polygon_limit.svg', import.meta.url)
};
export const GEOMTYPE = {
  CIRCLE: 'circle',
  POLYGON: 'polygon'
};
export const FLIGHTAREATYPE = {
  NFZ: 'nfz', // no-fly-zoom
  DFENCE: 'dfence' // 作业区
};
export const FLIGHTDATASTRUCT = {
  id: '',
  time: '',
  title: '名称标题',
  areaType: FLIGHTAREATYPE.NFZ, // limit,work
  color: '#009688', // 颜色字符串
  geomType: GEOMTYPE.CIRCLE, // 类型 Polygon,圆Circle
  positions: [], // 坐标点数组
  radius: 0, // 半径 在geomType 为 Circle 时有用
  center: [], // 圆心坐标数组
  area: 0, // 面积
  length: 0, // 周长
  statue: 1 // 状态 开启或者关闭
};
export const COLORTYPE = {
  [FLIGHTAREATYPE.NFZ]: new Cesium.Color.fromCssColorString('#FF4500').withAlpha(0.4), //
  [FLIGHTAREATYPE.DFENCE]: new Cesium.Color.fromCssColorString('#2DFFDC').withAlpha(0.4) //
};

export const COLOR_TYPE_STRING = {
  [FLIGHTAREATYPE.NFZ]: '#FF4500', //
  [FLIGHTAREATYPE.DFENCE]: '#2DFFDC' //
};
//#region 当前绘制的内容响应数据
export const curDarwPolygonDataRect = reactive({
  geomType: GEOMTYPE.CIRCLE,
  flightAreaType: FLIGHTAREATYPE.DFENCE,
  title: '标题',
  color: '#', // 颜色字符串
  positions: [], // 坐标点数组
  area: 0, //  面积
  length: 0 // 周长
});
//#endregion

//#region 主页面列表数据列表对象
export const flightDataListaRect = reactive({
  list: [], // 列表数据
  nfz: [], // no-fly-zoom
  dfence: [] // 作业区
});
//#endregion

//#region 窗体相关参数
export const dialogDataRect = reactive({
  visible: false,
  geomInfo: null,
  geomType: '',
  id: '',
  title: '',
  color: '',
  flightAreaType: FLIGHTAREATYPE.DFENCE,
  type: GEOMTYPE.CIRCLE,
  action: null,
  area: 0,
  length: 0,
  radius: 0,
  entity: null
});
//#endregion

//#region 方法
export const initMap = async () => {
  const engine = getOrCreateCesiumEngineInstance('flightArea');
  engine?.init('container'); //地图
  viewer = engine.viewer;
  scene = viewer.scene;
  const layerManager = new CesiumLayerManager(engine.viewer);
  layerManager.addLayer(imglayer);
  layerManager.addLayer(cialayer); 
  await getDeptSysSetting({}).then(res => {
    const { wayline_config = {} } = res;
    setCameraView(viewer, {
      destination: Cesium.Cartesian3.fromDegrees(wayline_config.longitude, wayline_config.latitude, 80000),
      orientation: new Cesium.HeadingPitchRoll(
        Cesium.Math.toRadians(0),
        Cesium.Math.toRadians(-90),
        Cesium.Math.toRadians(0)
      ),
      duration: 0.75
    });
  });
  // await readRemoteJsonFile('').then(res => {
  //   configData = res;
  //   setCameraView(viewer, {
  //     destination: Cesium.Cartesian3.fromDegrees(configData.airPort.lon, configData.airPort.lat, 80000),
  //     orientation: new Cesium.HeadingPitchRoll(
  //       Cesium.Math.toRadians(0),
  //       Cesium.Math.toRadians(-90),
  //       Cesium.Math.toRadians(0)
  //     ),
  //     duration: 0.75
  //   });
  // });
};
//#endregion

//#region 键盘ESC 键盘 按下的操作
// document.addEventListener('keydown', function (event) {
//   // 检查按下的是否是ESC键
//   if (event.key === 'Escape' || event.key === 'Esc') {
//     console.log('ESC键被按下');
//     curDarwPolygonDataRect.geomType = null;
//     circleDispose();
//     document.body.style.cursor = 'default';
//     // 在这里执行你想要进行的操作
//   }
// });
//#endregion

//#region 绘制 绘制圆与多边形
export const drawHandle = (options, callBack) => {
  // 开始绘制
  document.body.style.cursor = 'crosshair';
  if (options.geomType === GEOMTYPE.CIRCLE) {
    drawCircle(viewer, options, data => {
      if (!data) {
        return;
      }
      editStatue.value = true;
      dialogDataRect.visible = true;
      document.body.style.cursor = 'default';
      dialogDataRect.type = options.geomType;
      dialogDataRect.action = 'add';
      dialogDataRect.flightAreaType = options.flightAreaType;
      dialogDataRect.geomInfo = data;
      dialogDataRect.title = data.title;
      dialogDataRect.area = data.area;
      dialogDataRect.length = data.length;
    });
  } else if (options.geomType === GEOMTYPE.POLYGON) {
    drawPolygon(viewer, options, data => {
      if (!data) {
        return;
      }
      editStatue.value = true;
      document.body.style.cursor = 'default';
      dialogDataRect.visible = true;
      dialogDataRect.type = options.geomType;
      dialogDataRect.action = 'add';
      dialogDataRect.flightAreaType = options.flightAreaType;
      dialogDataRect.geomInfo = data.data || data;
      dialogDataRect.title = data.data.title;
      dialogDataRect.area = data.data.area;
      dialogDataRect.length = data.data.length;
    });
  }
};

//#endregion

//#region 编辑 监听点击空白区域
export const initMapClick = (successCb = null, errorCB = null) => {
  if (eventHandler) {
    eventHandler.removeAllEventListeners();
  }
  eventHandler = new ScreenEventHandler(viewer);
  eventHandler.addEventListener(ScreenSpaceEventTypes.LEFT_CLICK, pickCartesian2 => {
    if (editStatue.value) {
      ElMessage.warning('请先完成当前的编辑 ');
      return;
    }
    let feature = viewer.scene.pick(pickCartesian2);
    console.log('feature:', feature);
    if (Cesium.defined(feature)) {
      if (feature.hasOwnProperty('id') && feature.id instanceof Cesium.Entity) {
        // 如果选的是圆
        let entity = feature.id;
        let entityType = getPropertyByKey(entity, 'type');
        if (!entityType) {
          console.log('好不到类型数据:', entity);
          return;
        }
        if (!isCircleEdit.value && entityType === 'circle') {
          if (isPolygonEdit.value) {
            ElMessage.warning('请先完成多边形区域编辑');
            return;
          }
          const baseInfo = getPropertys(feature.id);
          dialogDataRect.color = getFeatureColorString(baseInfo) || ''; //baseInfo.color
          dialogDataRect.area = baseInfo.area || 0;
          dialogDataRect.length = baseInfo.length || 0;
          dialogDataRect.title = baseInfo.title || '';
          editCircle(viewer, feature.id, options => {
            const { data } = options;
            dialogDataRect.action = 'edit';
            dialogDataRect.id = baseInfo.id || data.id;
            dialogDataRect.geomInfo = data;
            if (dialogDataRect.geomInfo) {
              dialogDataRect.geomInfo.geomType = GEOMTYPE.CIRCLE;
            }
            dialogDataRect.area = data.area;
            dialogDataRect.length = data.length;
            dialogDataRect.title = data.title;
            editStatue.value = true;
            dialogDataRect.visible = true;
            successCb && successCb(data);
          });
        } else if (!isPolygonEdit.value && entityType === 'polygon') {
          if (isPolygonCreate.value) {
            ElMessage.info('请先完成多边形区域新增操作');
            return;
          }
          if (isPolygonEdit.value) {
            ElMessage.warning('请先完成多边形区域编辑');
            return;
          }
          // 这里通过 feature 提取对应数据填错窗体
          const baseInfo = getPropertys(feature.id);
          dialogDataRect.flightAreaType = baseInfo.flightAreaType;
          // dialogDataRect.color = baseInfo.color || '';
          dialogDataRect.color = getFeatureColorString(baseInfo) || ''; //baseInfo.color
          dialogDataRect.area = baseInfo.area || ''; // "2.35 km²"
          dialogDataRect.length = baseInfo.length || 0;
          dialogDataRect.title = baseInfo.title || '';
          editPolygon(viewer, feature.id, options => {
            const { data } = options;
            dialogDataRect.action = 'edit';
            dialogDataRect.geomInfo = data;
            if (dialogDataRect.geomInfo) {
              dialogDataRect.geomInfo.geomType = GEOMTYPE.POLYGON;
            }
            dialogDataRect.id = baseInfo.id || data.id;
            dialogDataRect.area = data.area;
            dialogDataRect.length = data.length;
            dialogDataRect.title = data.title;
            dialogDataRect.visible = true;
            editStatue.value = true;
            successCb && successCb(data);
          });
        } else {
        }
      }
    } else {
      dialogDataRect.visible = false;
      dialogDataRect.action = null;
      dialogDataRect.flightAreaType = true;
      dialogDataRect.geomInfo = null;
      dialogDataRect.title = '';
      if (isPolygonCreate.value) {
        polygonDispose();
        return;
      }
      if (isCircleCreate.value) {
        circleDispose();
        return;
      }

      if (isPolygonEdit.value) {
        cancelPolygonEdit();
        return;
      }
      if (isCircleEdit.value) {
        cancelCircleEdit();
        return;
      }

      errorCB && errorCB();
    }
  });
};

//#endregion

//#region 通过id获取实体对实体的一些要素进行变更
//#endregion

//#region 公用部分

/**
 * 米到公里的转换方法
 * @param {*} num
 * @returns
 */

export const parseLengthFloat = num => {
  num = toNumber(num, 3);
  let result =
    Number(num) === 0
      ? ''
      : Number(num) > 1000
      ? (Number(num) / 1000).toFixed(2) + ' km'
      : Number(num).toFixed(2) + ' m';
  return result;
};
export const parseAreaFloat = num => {
  num = toNumber(num, 3);
  let result;

  if (Number(num) === 0) {
    result = '';
  } else if (Number(num) > 1000000) {
    // 大于1000000平方米，转换为平方千米
    result = (Number(num) / 1000000).toFixed(2) + ' km²';
  } else {
    // 小于1000平方米，保留两位小数
    result = Number(num).toFixed(2) + ' m²';
  }

  return result;
};
//#endregion

//#region 获取列表构建的图层

export const initDataList = (options = null) => {
  editStatue.value = false;
  dialogDataRect.visible = false;
  closeCircleEdit();
  closePolygonEdit();
  polygonDispose();
  circleDispose();
  flightDataListaRect.list.length = 0;
  flightDataListaRect.nfz.length = 0;
  flightDataListaRect.dfence.length = 0;
  viewer && viewer.entities.removeAll();
  getFlightDataList().then(res => {
    if (!res || res.length === 0) {
      flightDataListaRect.list.length = 0;
      flightDataListaRect.nfz.length = 0;
      flightDataListaRect.dfence.length = 0;
      return;
    }
    flightDataListaRect.list.length = 0;
    flightDataListaRect.nfz.length = 0;
    flightDataListaRect.dfence.length = 0;
    flightDataListaRect.list = res;
    // 对res数组进行遍历 抽取对象中 type为 nfz的
    flightDataListaRect.list.forEach(item => {
      if (item.type === FLIGHTAREATYPE.NFZ) {
        flightDataListaRect.nfz.push(item);
      } else if (item.type === FLIGHTAREATYPE.DFENCE) {
        flightDataListaRect.dfence.push(item);
      }
    });
    // 列表排序
    flightDataListaRect.nfz.sort((a, b) => {
      let at = a.update_time ? Math.max(a.create_time, a.update_time) : a.create_time;
      let bt = b.update_time ? Math.max(b.create_time, b.update_time) : b.create_time;
      return bt - at;
    });
    flightDataListaRect.dfence.sort((a, b) => {
      let at = a.update_time ? Math.max(a.create_time, a.update_time) : a.create_time;
      let bt = b.update_time ? Math.max(b.create_time, b.update_time) : b.create_time;
      return bt - at;
    });
    initFligheAreaOnMap(flightDataListaRect.list, options?.zoom);
  });
};
//#endregion

//#region 根据数据初始化地图
export const initFligheAreaOnMap = (flightList = [], zoom = false) => {
  // 根据列表 构建 飞行区域
  if (!viewer) {
    return;
  }
  try {
    // 先移除所有要素
    viewer.entities.removeAll();
    if (flightList.length > 0) {
      flightList.forEach(item => {
        if (item.type === FLIGHTAREATYPE.NFZ) {
          item.content.properties.color = COLORTYPE[FLIGHTAREATYPE.NFZ];
        } else if (item.type === FLIGHTAREATYPE.DFENCE) {
          item.content.properties.color = COLORTYPE[FLIGHTAREATYPE.DFENCE];
        }
        if (item.content.geometry.type === GEOMTYPE.CIRCLE || item.content.geometry.type === 'Circle') {
          if (item.status) {
            createCircle(viewer, item);
          }
        } else if (item.content.geometry.type === FLIGHTAREATYPE.DFENCE || item.content.geometry.type === 'Polygon') {
          if (item.status) {
            createPolygon(viewer, item);
          }
        } else {
          console.log('未知的几何类型', item);
        }
      });

      // 初始化后 地图居中定位
      if (!zoom) {
        return;
      }
    } else {
      ///TODO... 定位到初始区域 1018
    }
  } catch (error) {}
};
//#endregion

//#region  通过entity 获取 相关属性

export const getPropertys = (entity = null) => {
  if (!entity) return;
  let response = {
    title: getPropertyByKey(entity, 'title') || '',
    color: getPropertyByKey(entity, 'color') || '',
    flightAreaType: getPropertyByKey(entity, 'flightAreaType'),
    id: getPropertyByKey(entity, 'id'),
    area: getPropertyByKey(entity, 'area'),
    length: getPropertyByKey(entity, 'length'),
    positions: getPropertyByKey(entity, 'positions') ?? []
  };
  let positions = getPropertyByKey(entity, 'positions') ?? [];
  // 计算面积
  response.area = calculatArea(positions);
  // 计算周长
  response.length = calculatPerimeter(positions);
  return response;
};

//#endregion
