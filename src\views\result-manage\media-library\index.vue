<!--媒体库-->
<script>
export default {
  name: 'MediaLibrary'
};
</script>

<script setup>
import { reactive, ref } from 'vue';
import { ElLoading, ElMessage } from 'element-plus';
import optionData from '@/utils/option-data';
import panoramaView from '@/views/result-manage/media-library/panoramaView.vue';
import EditDialog from './EditDialog.vue';
import ImgDetail from './imgDetail.vue';
import { getMediaFiles, batchDeleteMedia, downloadMediaFile, updateThumbnails } from '@/api/live';
import { downFile } from '@/utils/helper';
// 引入 JSZip 库
import JSZip from 'jszip';
import moment from 'moment';
import { authorityShow } from '@/utils/authority';
const editDialogRef = ref(null);
const editDialog = reactive({
  visible: false
});
const imgDetail = reactive({
  visible: false,
  title: '图片详情',
  imgUrl: ''
});
const panoramaDetail = reactive({
  visible: false,
  title: '图片详情',
  imgUrl: ''
});
const total = ref(0);
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  queryType: 2,
  keyWord: '',
  alarm_id: ''
});
const loading = ref(false);
const dataList = ref([]);
const checkedItems = ref([]); //选中的项
const checkedList = ref([]); //勾选过的数组（不论后续是否取消都记录），批量下载分页记录过滤用

let formData = reactive({});
let pathSuffix = '';
if (import.meta.env.VITE_APP_NODE_ENV === 'development') {
  pathSuffix = 'http://**************:24176';
} else {
  pathSuffix = window.location.origin;
}
pathSuffix = pathSuffix + '/uavfile/';
/**
 * 查询
 */
function handleQuery() {
  let startTime = '';
  let endTime = '';
  if (queryParams.range && queryParams.range.length === 2) {
    console.log(queryParams.range);
    startTime = new Date(moment(queryParams.range[0]).format('YYYY-MM-DD HH:mm:ss')).getTime();
    endTime = new Date(moment(queryParams.range[1]).format('YYYY-MM-DD HH:mm:ss')).getTime();
    // ;
  }
  loading.value = true;
  getMediaFiles({
    // order_by: 'update_time desc',
    keyword: queryParams.keyWord,
    page: queryParams.pageNum,
    page_size: queryParams.pageSize,
    begin_time: startTime,
    end_time: endTime,
    file_type: queryParams.file_type,
    alarm_id: queryParams.alarm_id
  }).then(data => {
    const { list, pagination } = data;
    list.forEach(item => {
      if (checkedItems.value.indexOf(item.file_id) != -1) {
        item.checked = true;
      } else {
        item.checked = false;
      }
    });
    dataList.value = list || [];
    total.value = pagination.total;
    setTimeout(() => {
      loading.value = false;
    }, 1000);
  });
}
function handleSearch() {
  queryParams.pageNum = 1;
  handleQuery();
}

function handleUpdateThumbnails() {
  updateThumbnails();
}
/**
 * 重置查询
 */
function resetQuery() {
  queryParams.file_type = '';
  queryParams.alarm_id = '';
  queryParams.keyWord = '';
  queryParams.range = [];
  queryParams.pageNum = 1;
  queryParams.pageSize = 10;
  handleQuery();
}
/**
 * 打开上传弹框编辑
 * @param {*} row
 */
function openEditDialog(row) {
  editDialog.title = '上传';
  editDialog.visible = true;
}

function disabledDate(time) {
  return new Date(time) > new Date();
}
/**
 * 打开图片详情弹窗
 */
const openImgDetail = item => {
  if (item.file_type === '3') {
    panoramaDetail.visible = true;
    panoramaDetail.title = item.file_name;
    panoramaDetail.imgUrl = item.file_url;
  } else {
    imgDetail.visible = true;
    imgDetail.title = item.file_name;
    imgDetail.imgUrl = item.file_url;
  }
};

/**
 * 批量删除
 */
const batchDelete = () => {
  // 批量删除选中的项
  console.log('选中的项：', checkedItems.value);
  ElMessageBox.confirm(`批量删除后不能撤回！`, '确认批量删除所选媒体文件？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    autofocus: false,
    closeOnClickModal: false,
    type: 'warning'
  }).then(() => {
    if (checkedItems.value.length) {
      batchDeleteMedia(checkedItems.value).then(data => {
        ElMessage.success('删除成功');
        checkedItems.value = []; //删除后记得清空所选ID数组
        checkedList.value = [];
        handleQuery();
      });
    } else {
      ElMessage.warning('请先勾选要删除的媒体文件');
    }
  });
};
//记录勾选过的媒体文件记录
function selectMedia(item) {
  if (checkedItems.value.indexOf(item.file_id) == -1) {
    checkedItems.value.push(item.file_id);
  } else {
    checkedItems.value.splice(checkedItems.value.indexOf(item.file_id), 1);
  }
  if (checkedList.value.indexOf(item) == -1) {
    checkedList.value.push(item);
  } else {
    checkedList.value.splice(checkedList.value.indexOf(item), 1);
  }
}
/**
 * 批量下载
 */
function batchDownload() {
  if (checkedItems.value.length) {
    ElMessage.warning('下载中');
    //获取当前时间时分秒
    const nowTime = moment().format('yyyyMMDDHHmmss');
    // 创建一个 JSZip 实例
    const zip = new JSZip();
    // 构造下载每个文件的 Promise 数组
    const downloadPromises = checkedItems.value.map(item => {
      //通过ID取勾选记录列表寻找对应的媒体文件名称并赋值文件名
      const foundItems = checkedList.value.find(i => item == i.file_id);
      return downloadMediaFile(item).then(res => {
        if (res) {
          // 将每个文件内容添加到 zip 中，使用文件名作为 key
          zip.file(foundItems.file_name, res);
        }
      });
    });
    // 等待所有文件下载完成
    Promise.all(downloadPromises).then(() => {
      // 生成 zip 文件并下载
      zip.generateAsync({ type: 'blob' }).then(function (content) {
        // 下载生成的 zip 文件
        downFile(content, '媒体文件_' + nowTime + '.zip');
        ElMessage.success('下载成功');
        handleQuery();
        checkedItems.value = []; //下载完成后记得清空所选ID数组
        checkedList.value = [];
      });
    });
  } else {
    ElMessage.warning('请先勾选要下载的媒体文件');
  }
}

onMounted(() => {
  handleQuery();
});
</script>

<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-position="right">
          <el-form-item label="" prop="alarm_id">
            <el-input
              class="input-serach"
              v-model="queryParams.alarm_id"
              placeholder="请输入警情ID"
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
            />
          </el-form-item>
          <el-form-item label="" prop="keyWord">
            <el-input
              class="input-serach"
              v-model="queryParams.keyWord"
              placeholder="请输入媒体名称"
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
            />
          </el-form-item>

          <el-form-item label="" prop="keyWord">
            <el-date-picker
              class="input-serach"
              v-model="queryParams.range"
              type="datetimerange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              date-format="YYYY/MM/DD"
              time-format="HH:mm:ss"
              @change="handleSearch"
              :disabled-date="disabledDate"
            />
          </el-form-item>
          <el-form-item label="" prop="file_type">
            <el-select
              class="input-serach"
              v-model="queryParams.file_type"
              placeholder="请选择媒体类型"
              clearable
              @change="handleSearch"
            >
              <el-option
                v-for="(item, index) in optionData.mediaTypeOption"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="search-btn">
        <el-button type="warn" @click="handleUpdateThumbnails()" v-show="queryParams.alarm_id === 'updateThumbnails'"
          ><i-ep-refresh />更新缩略图</el-button
        >
        <el-button type="primary" @click="handleSearch()"><i-ep-search />搜索</el-button>
        <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
      </div>
    </div>
    <el-card shadow="never">
      <template #header>
        <el-button type="primary" @click="openEditDialog()" v-if="authorityShow('importMedia')"
          ><i-ep-upload />导入</el-button
        >
        <el-button type="primary" @click="batchDownload()" v-if="authorityShow('downloadMedia')"
          ><i-ep-download />下载</el-button
        >
        <el-button type="primary" @click="batchDelete()" v-if="authorityShow('deleteMedia')"
          ><i-ep-delete />删除</el-button
        >
      </template>
      <div class="plan-info" v-loading="loading">
        <el-row :span="24" :gutter="20" class="data-box" v-if="dataList.length > 0">
          <el-col :span="5" v-for="item in dataList" :key="item.id">
            <div class="plan-item">
              <div class="plan-item-top">
                <img
                  v-lazy="item.thumbnail ? item.thumbnail : item.file_url"
                  v-show="item?.file_name.indexOf('.mp4') == -1 && item?.file_name.indexOf('.MP4') == -1"
                  class="left-img"
                  @click="openImgDetail(item)"
                  fit="cover"
                />
                <video
                  v-show="item?.file_name.indexOf('.mp4') !== -1 || item?.file_name.indexOf('.MP4') !== -1"
                  class="vedio-img"
                  :src="item.file_url"
                >
                  <source :src="item.file_url" type="video/mp4" />
                </video>
                <div
                  v-show="item?.file_name.indexOf('.mp4') !== -1 || item?.file_name.indexOf('.MP4') !== -1"
                  class="video-play-view"
                >
                  <img @click="openImgDetail(item)" class="video-play-btn" src="@/assets/home/<USER>" />
                </div>
                <el-checkbox
                  class="select"
                  v-model="item.checked"
                  :label="item.file_id"
                  @click.stop="selectMedia(item)"
                  v-if="authorityShow('deleteMedia') || authorityShow('downloadMedia')"
                >
                  <!-- 使用 slot 来自定义 Checkbox 的内容 -->
                  <span style="display: none">{{ item.file_id }}</span>
                </el-checkbox>
              </div>
              <div class="plan-item-bottom">
                <div class="name" :title="item.file_name">{{ item.file_name }}</div>
                <div class="info-div">
                  <div class="item-info">
                    {{ item.create_time }}
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <div class="empty" v-else>暂无数据</div>
      </div>
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
      <!-- <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      /> -->
    </el-card>

    <div v-if="editDialog.visible">
      <EditDialog
        ref="editDialogRef"
        v-model:visible="editDialog.visible"
        :title="editDialog.title"
        @submit="resetQuery"
      />
    </div>
    <!--图片详情组件-->
    <ImgDetail v-model:visible="imgDetail.visible" :title="imgDetail.title" :imgUrl="imgDetail.imgUrl" />
    <panoramaView
      v-model:visible="panoramaDetail.visible"
      :title="panoramaDetail.title"
      :imgUrl="panoramaDetail.imgUrl"
    />
  </div>
</template>

<style scoped lang="scss">
.input-serach {
  width: 200px;
}
.empty {
  height: 480px;
  line-height: 480px;
  width: 100%;
  text-align: center;
}

.flex-center {
  display: flex;
  align-items: center;

  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #6e6e6e;
  }
}

.search {
  display: flex;
  align-items: center;
  padding: 0 24px;

  .search-form {
    padding-top: 16px;
    flex: 1;
  }
}

.dialog-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;

  .select-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 4px;
    cursor: pointer;
    height: 204px;
    width: 176px;
    background: #f8f9fb;
    border-radius: 4px;
    border: 1px solid #e7e8f2;
    text-align: center;

    img {
      width: 80px;
      height: 80px;
      margin: 24px auto 5px;
    }

    .title {
      font-size: 16px;
      font-weight: 700;
      margin-bottom: 8px;
    }

    .desc {
      font-size: 12px;
      color: #000;
      padding: 0 28px;
    }
  }

  .select-item:hover {
    background: #eff6ff;
  }
}
::-webkit-scrollbar {
  width: 8px; /* 设置滚动条的宽度 */
}
::-webkit-scrollbar-thumb {
  background-color: rgba(46, 144, 255, 0.5);
  border-radius: 2px; /* 设置滚动条滑块的背景色 */
}
.plan-info {
  height: 570px;
  overflow-y: auto;
  overflow-x: hidden;
  .plan-item {
    margin-bottom: 20px;
    align-items: center;
    color: #fff;
    border: 1px solid #475467;
    box-shadow: 0 8px 14px #959cb600;
    border-radius: 4px;
    cursor: pointer;
    .plan-item-top {
      position: relative;

      .video-play-view {
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        .video-play-btn {
          cursor: pointer;
          margin: auto;
        }
      }

      .left-img {
        width: 100%;
        height: 180px;
      }
      .left-img img {
        width: 100%;
        max-height: 180px;
      }
      .vedio-img {
        opacity: 0.5;
        width: 100%;
        height: 180px;
        object-fit: cover;
      }

      .btn-box {
        position: absolute;
        top: 10px;
        left: 10px;
        display: flex;
        flex-direction: column;

        .delete {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 20px;
          background: #0000007a;
          border-radius: 4px;
          opacity: 0;
          margin-bottom: 6px;
          padding: 5px;
        }
      }

      .time-box {
        position: absolute;
        left: 0;
        bottom: 0;
        display: inline-flex;
        justify-content: space-between;
        padding: 0 12px;
        width: 176px;
        height: 26px;
        line-height: 26px;
        font-size: 12px;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
      }
    }

    .plan-item-bottom {
      padding: 4px 16px;
      line-height: 20px;
      color: #0009;
      font-size: 16px;

      .name {
        padding: 8px 0;
        line-height: 18px;
        font-weight: 700;
        color: #fff;
        font-size: 16px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .info-div {
        .item-info {
          display: flex;
          align-items: center;
          line-height: 22px;
          font-size: 14px;
          padding-bottom: 6px;
          color: #98a2b3;
          img {
            margin-right: 4px;
            width: 16px;
            height: 16px;
          }
        }
      }
    }
  }
}
.select {
  position: absolute;
  top: 10px;
  right: 10px;
}
.data-box {
  .el-col-5 {
    max-width: 20%;
    flex: 0 0 20%;
  }
}
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8); /* 半透明白色背景 */
  z-index: 9999; /* 确保在最上层 */
  display: flex;
  justify-content: center;
  align-items: center;
}
::v-deep .el-checkbox__inner {
  background-color: #bdbdbd;
  border: 1px solid #ffffff;
}
</style>
