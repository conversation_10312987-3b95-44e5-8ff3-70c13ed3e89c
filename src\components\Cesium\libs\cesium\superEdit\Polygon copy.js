import * as Cesium from 'cesium';
import {
  addPropertiesToEntity,
  getPropertyByKey,
  calculatePointFromCenter,
  toCartesian3,
  toNumber,
  toDegrees,
  px2Catesian3,
  arrayToCartesian3,
  c3ArrToDegress,
  calculatArea,
  calculatPerimeter,
  getPointsFromMap
} from '../common';
import { generateKey } from '@/utils';
import { getCenter } from '../turfUtils';
import { toRaw } from 'vue';
import { ElMessageBox } from 'element-plus';
//#region 绘制部分
let viewer = null;
let handler = null;
let labelEntity = null;
const endpointIcon = new URL('@/assets/plan/wrj/endpoint.png', import.meta.url).href;
const midpointIcon = new URL('@/assets/plan/wrj/middlepoint.png', import.meta.url).href;
/**
 * 绘制圆
 * @param {*} viewer viewer 对象
 * @param {*} CallBack 完成后的回调
 */
export const drawPolygon = (v, options, CallBack) => {
  if (!v) {
    throw new Error('viewer is required');
  }
  labelEntity = null;
  polygonDispose();
  viewer = v;
  let drawResultObject = {
    geomType: options.geomType || 'polygon',
    flightAreaType: options.flightAreaType || 'dfence',
    id: generateKey(),
    entityPoints: [],
    cartesianPoints: [],
    _endPointPositionMap: new Map(),
    polygon: null,
    action: 'add',
    _tempMouseEntity: null,
    title: options?.title || '',
    color: options?.color || '',
    area: 0,
    length: 0
  };
  document.body.style.cursor = 'crosshair'; //
  try {
    handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas); // 选中的经纬度
    // 最后
    drawResultObject.id = generateKey();
    handler.setInputAction(click => {
      let cartesian = viewer.camera.pickEllipsoid(click.position, viewer.scene.globe.ellipsoid);
      if (!cartesian) return;
      document.body.style.cursor = 'crosshair';
      drawResultObject.cartesianPoints.push(cartesian);
      let np = createPoint(viewer, cartesian, { pixelSize: 10, outlineWidth: 2 });
      drawResultObject.entityPoints.push(np);
      drawResultObject._endPointPositionMap.set(np.id, {
        position: cartesian,
        id: np.id
      });
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

    handler.setInputAction(event => {
      document.body.style.cursor = 'crosshair';
      if (drawResultObject.cartesianPoints.length < 1) return;
      let cartesian3 = px2Catesian3(event.endPosition, viewer);
      if (!cartesian3) return;
      if (drawResultObject._tempMouseEntity) {
        drawResultObject._tempMouseEntity.position = cartesian3;
      } else {
        drawResultObject._tempMouseEntity = createPoint(viewer, cartesian3, { pixelSize: 10, outlineWidth: 2 });
      }
      if (drawResultObject.cartesianPoints.length === 2) {
        let pp = createPolygon(viewer, drawResultObject.cartesianPoints, {
          id: drawResultObject.id,
          name: 'Polygon_Edit_' + drawResultObject.id,
          color: options.color
        });
        drawResultObject.polygon = pp != null ? pp : null;
        // 这一步比较关键
        drawResultObject.cartesianPoints.push(cartesian3);
      }
      if (drawResultObject.cartesianPoints.length > 2) {
        drawResultObject.cartesianPoints.pop();
        drawResultObject.cartesianPoints.push(cartesian3);
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

    handler.setInputAction(event => {
      clearLabelEntity();
      handler?.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
      handler?.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      handler?.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
      handler.destroy();
      // 添加最后一个点
      drawResultObject._endPointPositionMap.set(drawResultObject._tempMouseEntity.id, {
        position: drawResultObject._tempMouseEntity.position._value,
        id: drawResultObject._tempMouseEntity.id
      });
      // 清理临时点
      if (drawResultObject._tempMouseEntity) {
        viewer.entities.remove(drawResultObject._tempMouseEntity);
        drawResultObject._tempMouseEntity = null;
      }
      // 移除线上点位
      drawResultObject.entityPoints.forEach(ppointEntity => {
        viewer.entities.remove(ppointEntity);
      });
      let baseInfo = getPolygonBaseInfoFromPositions(drawResultObject);
      drawResultObject.area = baseInfo.area;
      drawResultObject.length = baseInfo.length;
      // 这里构建点位信息
      addPropertiesToEntity(drawResultObject.polygon, {
        type: drawResultObject.geomType || 'polygon',
        title: drawResultObject.title,
        id: drawResultObject.id,
        flightAreaType: drawResultObject.flightAreaType || 'dfence',
        positions: drawResultObject.cartesianPoints,
        area: drawResultObject.area,
        length: toNumber(drawResultObject.length, 2),
        color: options?.color || Cesium.Color.WHITE.withAlpha(0.4)
      });
      forbidWorld(false);
      CallBack &&
        CallBack({
          data: drawResultObject,
          polygon: drawResultObject.polygon,
          clickType: 'RIGHT_CLICK',
          event: 'update'
        });
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  } catch (error) {}
};

export const polygonDispose = () => {
  if (handler && handler.isDestroyed() === false) {
    handler?.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    handler?.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
    handler?.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    handler.destroy();
  }
  document.body.style.cursor = 'default';
};

//#endregion

// export const createEndPoints = (viewer, drawResultObject) => {
//   let result = {
//     entityPoints_: [],
//     cartesianPoints_: []
//   };
//   let cartesianPoints = drawResultObject.cartesianPoints;
//   if (cartesianPoints && cartesianPoints.length <= 2) {
//     return result;
//   }
//   // 移除中间点
//   (drawResultObject.entityPoints || []).forEach(entityPoint => {
//     if (entityPoint.name === 'MIDPOINT') {
//       viewer.entities.remove(entityPoint);
//     }
//   });

//   cartesianPoints.forEach(c3p => {
//     let id = generateKey();
//     let p = createPoint(viewer, c3p, { pixelSize: 10, outlineWidth: 2, id: id });
//     result.entityPoints_.push(p);
//     result.cartesianPoints_.push(c3p);
//   });
//   return result;
// };

//#region 编辑部分
export let isPolygonEdit = ref(false);
// 适合多有圆形的编辑方法
export const editPolygon = (v, polygonEntity, CallBack) => {
  // 检查是否满足绘制条件
  if (!v || !polygonEntity || isPolygonEdit.value) {
    return; // 如果任何条件不满足，则不执行
  }
  try {
    viewer = v;
    let curSelectedEntity = null;
    let flightAreaType_ = getPropertyByKey(polygonEntity, 'flightAreaType');
    let id_ = getPropertyByKey(polygonEntity, 'id');
    let drawResultObject = {
      geomType: 'polygon',
      action: 'edit',
      flightAreaType: flightAreaType_ || 'dfence',
      id: id_ || generateKey(),
      endEntityPoints: [],
      midEntityPoints: [],
      polygon: null,
      positions: [],
      _endPointPositionMap: new Map(),
      title: getPropertyByKey(polygonEntity, 'title') || '',
      color: getPropertyByKey(polygonEntity, 'color') || '',
      area: 0,
      length: 0
    };
    // 创建圆心
    let properties = polygonEntity.properties ?? null;
    if (!properties) {
      return;
    }
    document.body.style.cursor = 'default';
    // 从实体自定义属性中获取 经纬度 坐标集合
    let c3Positions = getPropertyByKey(polygonEntity, 'positions') ?? [];
    drawResultObject.positions = c3Positions;
    const { entityPoints, midEntityPoints } = firstInitPointArr(viewer, drawResultObject);

    drawResultObject.endEntityPoints = entityPoints;
    drawResultObject.midEntityPoints = midEntityPoints;
    // 定义事件监听
    polygonEntity.handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    // 点击事件
    polygonEntity.handler.setInputAction(event => {
      isPolygonEdit.value = true;
      let pickedObject = viewer.scene.pick(event.position);
      if (Cesium.defined(pickedObject)) {
        if (pickedObject.id.name === 'ENDPOINT') {
          curSelectedEntity = pickedObject.id;
        } else if (pickedObject.id.name === 'MIDPOINT') {
          curSelectedEntity = pickedObject.id;
          // 将当前的点转换为 端点 索引位置插入 然后清楚重新创建中间点
          clearMidPoints(viewer, drawResultObject, curSelectedEntity);
          const { entityPoints, midEntityPoints } = reInitPointArr(viewer, drawResultObject);
          drawResultObject.endEntityPoints = entityPoints;
          drawResultObject.midEntityPoints = midEntityPoints;
          if (!curSelectedEntity) {
            throw new Error('转换失败');
          }
        }
        let baseInfo = getPolygonBaseInfoFromPositions(drawResultObject);
        drawResultObject.area = baseInfo.area;
        drawResultObject.length = baseInfo.length;
        if (isPolygonEdit.value) {
          CallBack &&
            CallBack({
              data: drawResultObject,
              polygon: polygonEntity,
              clickType: 'LEFT_DOWN',
              event: 'update'
            });
        }
      }

      // 选中的如果是
    }, Cesium.ScreenSpaceEventType.LEFT_DOWN);

    // 对鼠标移动事件的监听
    polygonEntity.handler.setInputAction(event => {
      //获取加载地形后对应的经纬度和高程：地标坐标
      let ray = viewer.camera.getPickRay(event.endPosition);
      let newCartesian = viewer.scene.globe.pick(ray, viewer.scene);
      if (!newCartesian) {
        return;
      }
      if (curSelectedEntity == null) {
        document.body.style.cursor = 'default';
        return;
      }
      // 选中的实体位置变更
      curSelectedEntity.position = newCartesian;
      //移动的是半径点，则更新半径
      if (curSelectedEntity.name === 'ENDPOINT') {
        document.body.style.cursor = 'pointer';
        updateMidPoints(drawResultObject);
      }
      polygonEntity.polygon.hierarchy = getPolygonHierarchyProperty(drawResultObject);
      viewer.scene.screenSpaceCameraController.enableRotate = false;
      viewer.scene.screenSpaceCameraController.enableZoom = false;
      let baseInfo = getPolygonBaseInfoFromPositions(drawResultObject);
      drawResultObject.area = baseInfo.area;
      drawResultObject.length = baseInfo.length;
      if (isPolygonEdit.value) {
        CallBack &&
          CallBack({
            data: drawResultObject,
            polygon: polygonEntity,
            clickType: 'MOUSE_MOVE',
            event: 'update'
          });
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

    // 对鼠标抬起事件的监听
    polygonEntity.handler.setInputAction(event => {
      curSelectedEntity = null;
      viewer.scene.screenSpaceCameraController.enableRotate = true;
      viewer.scene.screenSpaceCameraController.enableZoom = true;
    }, Cesium.ScreenSpaceEventType.LEFT_UP);

    // 右击事件的监听
    polygonEntity.handler.setInputAction(event => {
      // setTimeout(() => {
      //   ElMessageBox.confirm(`确定提交吗？`, {
      //     confirmButtonText: '确定',
      //     cancelButtonText: '取消',
      //     autofocus: false,
      //     type: 'warning'
      //   }).then(() => {
      //   });
      // }, 100);

      isPolygonEdit.value = false;
      drawResultObject.endEntityPoints.forEach(p => {
        viewer.entities.remove(p);
      });
      drawResultObject.midEntityPoints.forEach(p => {
        viewer.entities.remove(p.entity);
      });
      drawResultObject.midEntityPoints = [];
      updateProperty(polygonEntity);
      closePolygonEdit(polygonEntity);
      clearLabelEntity();
      viewer.scene.screenSpaceCameraController.enableRotate = true;
      viewer.scene.screenSpaceCameraController.enableZoom = true;
      let baseInfo = getPolygonBaseInfoFromPositions(drawResultObject);
      drawResultObject.area = baseInfo.area;
      drawResultObject.length = baseInfo.length;

      // 计算坐标组
      drawResultObject.positions = [];
      drawResultObject.endEntityPoints.forEach(entity => {
        drawResultObject.positions.push(entity.position._value);
      });
      addPropertiesToEntity(polygonEntity, {
        type: drawResultObject.geomType || 'polygon',
        title: drawResultObject.title,
        id: drawResultObject.id,
        flightAreaType: drawResultObject.flightAreaType || 'dfence',
        positions: drawResultObject.positions,
        area: drawResultObject.area,
        length: toNumber(drawResultObject.length, 2),
        color: drawResultObject?.color || Cesium.Color.WHITE.withAlpha(0.4)
      });
      CallBack &&
        CallBack({
          data: drawResultObject,
          polygon: polygonEntity,
          clickType: 'RIGHT_CLICK',
          event: 'clear'
        });
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    // 计算圆周上的一个点 在正东方的距离圆心的半径长度位置的点
  } catch (error) {}
};

export const firstInitPointArr = (viewer, drawResultObject) => {
  let result = {
    entityPoints_: [],
    midEntityPoints_: []
  };
  // 创建完当前的数组 端点
  (drawResultObject.positions || []).forEach(c3p => {
    let id = generateKey();
    let p = createPoint(viewer, c3p, { pixelSize: 10, outlineWidth: 2, id: id });
    result.entityPoints_.push(p);
  });
  // 通过端点构建 中点 插入到对应的索引位
  for (let i = 0; i < result.entityPoints_.length; i++) {
    let oneTemp = null;
    let twoTemp = null;
    let midEntity = null;
    let objMidPoint = {
      entity: null,
      prevId: '',
      nextId: ''
    };
    if (i === result.entityPoints_.length - 1) {
      oneTemp = result.entityPoints_[i];
      twoTemp = result.entityPoints_[0];
      // 创建中间点
      midEntity = createMidPoint(
        viewer,
        Cesium.Cartesian3.midpoint(oneTemp.position._value, twoTemp.position._value, new Cesium.Cartesian3()),
        { pixelSize: 10, outlineWidth: 2 }
      );
      objMidPoint.entity = midEntity;
      objMidPoint.prevId = oneTemp.id;
      objMidPoint.nextId = twoTemp.id;
    } else {
      oneTemp = result.entityPoints_[i];
      twoTemp = result.entityPoints_[i + 1];
      // 创建中间点
      midEntity = createMidPoint(
        viewer,
        Cesium.Cartesian3.midpoint(oneTemp.position._value, twoTemp.position._value, new Cesium.Cartesian3()),
        { pixelSize: 10, outlineWidth: 2 }
      );
      objMidPoint.entity = midEntity;
      objMidPoint.prevId = oneTemp.id;
      objMidPoint.nextId = twoTemp.id;
    }
    result.midEntityPoints_.push(objMidPoint);
  }

  return {
    entityPoints: result.entityPoints_,
    midEntityPoints: result.midEntityPoints_
  };
};

export const reInitPointArr = (viewer, drawResultObject) => {
  let result = {
    entityPoints_: drawResultObject.endEntityPoints,
    midEntityPoints_: []
  };
  // 通过端点构建 中点 插入到对应的索引位
  for (let i = 0; i < result.entityPoints_.length; i++) {
    let oneTemp = null;
    let twoTemp = null;
    let midEntity = null;
    let objMidPoint = {
      entity: null,
      prevId: '',
      nextId: ''
    };
    if (i === result.entityPoints_.length - 1) {
      oneTemp = result.entityPoints_[i];
      twoTemp = result.entityPoints_[0];
      // 创建中间点
      midEntity = createMidPoint(
        viewer,
        Cesium.Cartesian3.midpoint(oneTemp.position._value, twoTemp.position._value, new Cesium.Cartesian3()),
        { pixelSize: 10, outlineWidth: 2 }
      );
      objMidPoint.entity = midEntity;
      objMidPoint.prevId = oneTemp.id;
      objMidPoint.nextId = twoTemp.id;
    } else {
      oneTemp = result.entityPoints_[i];
      twoTemp = result.entityPoints_[i + 1];
      // 创建中间点
      midEntity = createMidPoint(
        viewer,
        Cesium.Cartesian3.midpoint(oneTemp.position._value, twoTemp.position._value, new Cesium.Cartesian3()),
        { pixelSize: 10, outlineWidth: 2 }
      );
      objMidPoint.entity = midEntity;
      objMidPoint.prevId = oneTemp.id;
      objMidPoint.nextId = twoTemp.id;
    }
    result.midEntityPoints_.push(objMidPoint);
  }
  return result;
};

// 更新中间点位置
export const updateMidPoints = drawResultObject => {
  // 创建完当前的数组 端点
  (drawResultObject.midEntityPoints || []).forEach(tempEntityPoint => {
    const { entity = null, prevId = '', nextId = '' } = tempEntityPoint;
    let p1 = drawResultObject.endEntityPoints.find(p => p.id === prevId);
    let p2 = drawResultObject.endEntityPoints.find(p => p.id === nextId);
    entity.position = Cesium.Cartesian3.midpoint(p1.position._value, p2.position._value, new Cesium.Cartesian3());
  });
};

// 获取动态变更的坐标值positionArr
export const getPolygonHierarchyProperty = (drawResultObject, positionArr2) => {
  return new Cesium.CallbackProperty(() => {
    let positionArr = [];
    drawResultObject.endEntityPoints.forEach(entity => {
      if (entity.name === 'MIDPOINT') {
        return;
      }
      positionArr.push(entity.position._value);
    });
    return new Cesium.PolygonHierarchy(positionArr, null);
  }, false);
};

export const closePolygonEdit = polygon => {
  document.body.style.cursor = 'default';
  // 移除地图事件
  polygon.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOWN);
  polygon.handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
  polygon.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_UP);
  polygon.handler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  isPolygonEdit.value = false;
  clearLabelEntity();
  return polygon;
};

const getPolygonBaseInfoFromPositions = drawResultObject => {
  let entityPoints = [];
  drawResultObject.endEntityPoints.forEach(entity => {
    entityPoints.push(entity.position._value);
  });
  let degList = c3ArrToDegress(entityPoints);
  let center = getCenter(degList);
  // 计算面积
  let area = calculatArea(entityPoints);
  // 计算周长
  let length = calculatPerimeter(entityPoints);
  return { center: toCartesian3(center), length, area };
};

/**
 * 更新属性信息 entity
 */
const updateProperty = entity => {
  entity.polygon.hierarchy = entity.polygon.hierarchy.getValue();
};

//#endregion

//#region 公用部分

/**
 * 通用产生采集点
 * @param {*} cartesian
 * @returns
 */
export function createPoint(viewer, cartesian, options) {
  let point = viewer.entities.add({
    id: options.id || generateKey(),
    name: 'ENDPOINT',
    position: toCartesian3(cartesian),
    billboard: {
      image: endpointIcon,
      width: 14,
      height: 14,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      disableDepthTestDistance: Number.POSITIVE_INFINITY // 点总是在最上层
    }
  });
  return point;
}

export function createMidPoint(viewer, cartesian, options) {
  let point = viewer.entities.add({
    id: options.id || generateKey(),
    name: 'MIDPOINT',
    position: toCartesian3(cartesian),
    billboard: {
      image: midpointIcon,
      width: 10,
      height: 10,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      disableDepthTestDistance: Number.POSITIVE_INFINITY // 点总是在最上层
    }
  });
  return point;
}

export function clearMidPoints(viewer, drawResultObject, midEntity) {
  if (!midEntity || !drawResultObject) {
    return null;
  }
  let midEntityPointEntity = (drawResultObject.midEntityPoints || []).find(
    mepObj => mepObj.entity.id === midEntity?.id
  );
  if (!midEntityPointEntity) {
    return null;
  }
  // 获取当前点实体
  const { entity = null, prevId = '' } = midEntityPointEntity;
  entity.name = 'ENDPOINT';
  entity.billboard.image = endpointIcon;
  entity.billboard.width = 14;
  entity.billboard.height = 14;
  // 根据这里的 prevId 和 nextId  在 result.entityPoints_ 中找到对应的 prevId 和 nextId 位置,然后将 entity 插入到 该索引中间
  let index = drawResultObject.endEntityPoints.findIndex(p => p.id === prevId);
  index > -1 && drawResultObject.endEntityPoints.splice(index + 1, 0, entity);
  drawResultObject.midEntityPoints.forEach(midEntityPoint => {
    const { entity = null } = midEntityPoint;
    if (entity.name === 'ENDPOINT') {
      return;
    }
    viewer.entities.remove(entity);
  });
  drawResultObject.midEntityPoints = [];
}

/**
 * 通用产生采集点
 * @param {*} cartesian
 * @returns
 */
export function createPolygon(viewer, positions, options) {
  try {
    if (!viewer || !positions) {
      return;
    }
    return viewer.entities.add({
      id: options?.id || generateKey(),
      name: options?.name || 'Polygon_Edit_' + generateKey(),
      polygon: {
        hierarchy: new Cesium.CallbackProperty(() => {
          let hierarchyTemp = new Cesium.PolygonHierarchy(positions, []);
          return hierarchyTemp;
        }, false),
        show: true,
        material: options?.color || Cesium.Color.WHITE.withAlpha(0.4),
        outline: true,
        outlineColor: Cesium.Color.ALICEBLUE,
        outlineWidth: 2,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    });
  } catch (error) {}
}
export function forbidWorld(isForbid) {
  if (!viewer) {
    return;
  }
  viewer.scene.screenSpaceCameraController.enableRotate = !isForbid;
  viewer.scene.screenSpaceCameraController.enableTilt = !isForbid;
  viewer.scene.screenSpaceCameraController.enableTranslate = !isForbid;
  viewer.scene.screenSpaceCameraController.enableInputs = !isForbid;
}

//#endregion

//#region 创建辅助部分

// 清除辅助部分
export function clearLabelEntity() {
  document.body.style.cursor = 'default';
  if (!viewer) {
    return;
  }
  if (labelEntity) {
    viewer.entities.remove(labelEntity);
    labelEntity = null;
  }
}
// 创建辅助部分
function createOrUpdateLabelEntity(drawResultObject = null) {
  if (!viewer || !drawResultObject) {
    return;
  }
  // 更新标注功能
  let options = getPolygonBaseInfoFromPositions(drawResultObject);
  const { center, area = 0 } = options;
  let midPoint = toCartesian3(center);
  if (!midPoint || !area) {
    return;
  }
  if (labelEntity) {
    labelEntity.position = midPoint;
    labelEntity.label.text = area || 0;
  } else {
    labelEntity = viewer.entities.add({
      position: toCartesian3(midPoint),
      point: {
        // 点的大小（像素）
        pixelSize: 15,
        // 点位颜色，fromCssColorString 可以直接使用CSS颜色
        color: Cesium.Color.RED,
        // 边框颜色
        outlineColor: Cesium.Color.fromCssColorString('#fff'),
        // 边框宽度(像素)
        outlineWidth: 2,
        // 是否显示
        show: true,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND //贴地
      },
      label: {
        text: area || 0,
        show: true,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        font: '30px monospace', // 字体边框
        outline: true,
        fillColor: Cesium.Color.WHITE,
        outlineWidth: 5,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -30),
        showBackground: true,
        backgroundColor: new Cesium.Color(0.117, 0.117, 0.117, 0.7),
        eyeOffset: new Cesium.Cartesian3(0, 0, 2),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND //贴地
      }
    });
  }
}

//#endregion
