//#region action
// 动作类型;
export const ACTION_ACTUATOR_FUNC = {
  takePhoto: 'takePhoto', // 单拍
  startRecord: 'startRecord', // 开始录像
  stopRecord: 'stopRecord', // 结束录像
  focus: 'focus', // 对焦
  zoom: 'zoom', // 变焦
  customDirName: 'customDirName', // 创建新文件夹
  gimbalRotate: 'gimbalRotate', // 旋转云台
  rotateYaw: 'rotateYaw', // 飞行器偏航
  hover: 'hover', // 悬停等待
  gimbalEvenlyRotate: 'gimbalEvenlyRotate', // 航段间均匀转动云台pitch角
  accurateShoot: 'accurateShoot', // 精准复拍动作（已暂停维护,建议使用orientedShoot）
  orientedShoot: 'orientedShoot', // 精准复拍动作
  panoShot: 'panoShot', // 全景拍照动作（仅支持M30/M30T）
  recordPointCloud: 'recordPointCloud' // 点云录制操作
};

export const ACTION_ACTUATOR_FUNC_OPTIONS = [
  {
    value: 'takePhoto',
    label: '单拍'
  },
  {
    value: 'startRecord',
    label: '开始录像'
  },
  {
    value: 'stopRecord',
    label: '结束录像'
  },
  {
    value: 'focus',
    label: '对焦'
  },
  {
    value: 'zoom',
    label: '变焦'
  },
  {
    value: 'customDirName',
    label: '创建新文件夹'
  },
  {
    value: 'gimbalRotate',
    label: '云台偏航角' //'旋转云台'
  },
  {
    value: 'rotateYaw',
    label: '飞行器偏航'
  },
  {
    value: 'hover',
    label: '悬停等待'
  },
  {
    value: 'gimbalEvenlyRotate',
    label: '云台俯仰角' //'航段间均匀转动云台pitch角'
  },
  {
    value: 'accurateShoot',
    label: '精准复拍动作(已暂停维护,建议使用orientedShoot)'
  },
  {
    value: 'orientedShoot',
    label: '精准复拍动作'
  },
  {
    value: 'panoShot',
    label: '全景拍照动作(仅支持M30/M30T)'
  },
  {
    value: 'recordPointCloud',
    label: '点云录制操作'
  }
];
//#endregion
