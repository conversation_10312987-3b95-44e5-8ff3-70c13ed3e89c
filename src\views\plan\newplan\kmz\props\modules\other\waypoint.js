// WayPointList组件数据结构
export function getWayPointTemplateData() {
  const wayPointData = {
    pointId: 0,
    index: 0,
    placemarkIndex: 0, //（新增） 具体是第几个Placemark
    longitude: 0, // 经纬度
    latitude: 0,
    actionGroup: [], // 动画组
    action: [
      // 格式如下
      // { "actionGroupId": 1, //  组ID
      // "actionIndex": 0,// 动作索引
      // "actionId": 1713874217152,// 这里的动作id 实际上作为组件的唯一标识不再业务上使用
      // "actionActuatorFunc": "startRecord"
      // warning: false,
    ],
    warning: false,
    uuid: ''
  };
  return wayPointData;
}

// 动画所著组数据结构
export const wayPointActionGroup = {
  actionGroupId: 0,
  actionGroupMode: '',
  actionTriggerType: ''
};

// 动画数据结构
export const wayPointActions = {
  actionGroupId: 0,
  actionId: 0,
  actionActuatorFunc: '',
  actionActuatorFuncParam: {
    gimbalRotateMode: ''
  }
};
