<template>
  <div
    :class="
      isShareVideo
        ? ['palyer-container']
        : droneParaObj?.cameraName
        ? ['palyer-container']
        : ['palyer-container', 'empty']
    "
    v-if="!videoPlayError"
  >
    <div :class="isShareVideo ? 'player' : droneParaObj?.cameraName ? 'player' : 'hiddenPlayer'" :id="playerId"></div>
    <div class="title-name" v-if="showTitle">{{ droneParaObj?.cameraName || '无视频' }}</div>
    <div class="close-icon" v-if="droneParaObj?.droneSelected && showClose" @click="closeVideo"><i-ep-close /></div>
    <div
      class="toolbar"
      v-if="
        (
          droneParaObj?.droneSelected &&
          showBlowUp) ||
        isShareVideo
      "
    >
      <!-- 切换清晰度 -->
      <el-dropdown
        class="quality"
        placement="top"
        size="small"
        popper-class="quality-popper"
        @command="commandQuality"
        trigger="hover"
        v-if="!isShareVideo && droneParaObj?.url_type"
      >
        <div class="quality-title">
          {{ qualityTitle }}
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item :command="0">自动</el-dropdown-item>
            <el-dropdown-item :command="1">流畅</el-dropdown-item>
            <el-dropdown-item :command="2">标准</el-dropdown-item>
            <el-dropdown-item :command="3">高清</el-dropdown-item>
            <el-dropdown-item :command="4">超清</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <!-- <svg-icon icon-class="cut" class="cursor" style="margin-right: 15;width: 20px;height: 20px;color: #fff;" @click="capture()"/> -->
      <svg-icon
        icon-class="full_screen"
        class="cursor"
        style="margin-right: 20; margin-top: 10; width: 16px; height: 16px"
        @click="wholeFullScreen()"
      />
    </div>
  </div>
  <div class="videoPlayError" v-if="videoPlayError && !isShareVideo">
    <el-button type="primary" plain @click="rePlay">播放失败，立即重试</el-button>
  </div>
  <!-- h5分享播放使用 -->
  <div class="videoPlayError" style="color: #fff" v-if="videoPlayError && isShareVideo">
    <span>播放失败，请刷新页面</span>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, reactive } from 'vue';
import { jessibucaError } from '@/utils/constants';
import { ElMessage } from 'element-plus';
import { startLivestream, setLivestreamQuality, getVideoUrl } from '@/api/live';
import optionData from '@/utils/option-data';
import moment from 'moment';
import { refreshStreams } from '@/api/devices';

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  playerId: {
    type: String,
    default: ''
  },
  dronePara: {
    type: Object,
    default: {}
  },
  index: {
    type: Number,
    default: 0
  },
  showClose: {
    type: Boolean,
    default: true
  },
  showTitle: {
    type: Boolean,
    default: false
  },
  showBlowUp: {
    type: Boolean,
    default: true
  }
});
const qualityTitle = ref('高清');
const playback = reactive({
  startTime: '',
  endTime: '',
  valueFormat: moment.HTML5_FMT.DATETIME_LOCAL_SECONDS,
  seekStart: '',
  rate: ''
});
const nonSwitchable = 'normal';
//jessibucaPro 播放器
const player = ref(null);
const operateBtns = reactive({
  // 视频播放器操作台显示的属性按钮
  fullscreen: true, // 是否显示全屏
  play: false, // 是否显示播放按钮
  screenshot: false, // 是否显示截图按钮
  quality: true, //是否显示清晰度
  audio: false //是否显示音量按钮
});
const counts = ref(0); //重连次数
const finalUrl = ref(null); //播放地址
const videoPlayError = ref(false); //播放出差页面
const droneParaObj = reactive({}); //摄像头信息
const isShareVideo = ref(false);
const decoderUrl =
  import.meta.env.VITE_APP_NODE_ENV == 'development'
    ? '../../../public/jessibuca/js/decoder-pro.js'
    : '../../../jessibuca/js/decoder-pro.js';
onMounted(() => {
  initHK();
  window.$bus.on('refreshVideo', url => {
    finalUrl.value = url;
    setTimeout(() => {
      videoPlayHK();
    }, 500);
  });

  window.$bus.on('hkPlayerResize', () => {
    player?.value && player?.value?.JS_Resize && player?.value?.JS_Resize();
  });
});
//创建视频播放器
// 播放器属性详见https://jessibuca.com/api.html
function createPlayer() {
  try {
    player.value = new JessibucaPro({
      container: document.getElementById(props.playerId),
      videoBuffer: 0.2, // 缓存时长
      videoBufferDelay: 1, // 1000s
      decoder: decoderUrl,
      isResize: false,
      text: '',
      useSIMD: false, //默认走的wasm解码器
      demuxUseWorker: true,
      loadingText: '加载中',
      loadingTimeout: 60,
      heartTimeout: 60,
      heartTimeoutReplayTimes: 3,
      // debug: true,
      // debugLevel: 'debug',
      operateBtns: { ...operateBtns },
      isNotMute: false,
      heartTimeoutReplayUseLastFrameShow: true,
      audioEngine: 'worklet',
      // qualityConfig: ['流畅', '高清'],
      controlAutoHide: true,
      useCanvasRender: false,
      useWebGPU: true,
      supportHls265: true,
      isFlv: true
    });
    player.value.on('error', error => {
      console.log('error:', error);
      if ('fetchError' === error && counts.value < 3) {
        player.value.close().then(() => {
          setTimeout(() => {
            //延迟500毫秒执行
            console.log('尝试重新播放', counts.value);
            videoPlayError.value = true;
            // rePlay(); //重新播放
            counts.value += 1;
          }, 500);
        });
      } else if (error !== 'mseWidthOrHeightChange') {
        cancelPlay(); //播放异常
        ElMessage.warning('播放错误，错误信息：' + (jessibucaError[error] || error));
      }
    });
    player.value.on('timeUpdate', ts => {
      //当前视频帧
    });
    player.value.on('play', flag => {
      //触发播放事件
      counts.value = 0;
    });
    player.value.on('loadingTimeout', () => {
      //播放超时
      console.log('timeout');
      ElMessage.warning('播放超时');
      cancelPlay();
    });
    player.value.on('streamQualityChange', value => {
      //清晰度切换
      console.log('streamQualityChange', value);
      changeTheQuality(value);
    });
    player.value.on('videoInfo', function (data) {
      console.log('width:', data.width, 'height:', data.width, 'encType', data.encType);
    });
  } catch (error) {
    console.log('error', error);
  }
}
//播放
function videoPlay() {
  player.value = null;
  videoPlayError.value = false;
  nextTick(() => {
    !player.value && createPlayer();
    player.value && player.value.play(finalUrl.value);
  });
}

// 视频分享页面调用接口
function h5VideoPlay(url) {
  if (url) {
    isShareVideo.value = true;
  } else {
    isShareVideo.value = false;
  }
  finalUrl.value = url;
  videoPlayError.value = false;
  if (url.indexOf('.flv') > -1) {
    videoPlay();
  } else {
    videoPlayHK();
  }
}

function commandQuality(val) {
  switch (val) {
    case 0:
      qualityTitle.value = '自动';
      break;
    case 1:
      qualityTitle.value = '流畅';
      break;
    case 2:
      qualityTitle.value = '标准';
      break;
    case 3:
      qualityTitle.value = '高清';
      break;
    case 4:
      qualityTitle.value = '超清';
      break;
    default:
      console.warn('未知的清晰度值:', val);
      return;
  }
  if (droneParaObj.droneSelected != null && droneParaObj.cameraSelected != null) {
    // 构建视频ID
    const videoId = `${droneParaObj?.droneSelected}/${droneParaObj?.cameraSelected}/${
      droneParaObj?.videoSelected || nonSwitchable + '-0'
    }`;
    // 调用接口设置清晰度
    setLivestreamQuality({
      video_id: videoId,
      video_quality: val
    })
      .then(res => {
        // 处理响应结果
        console.log('清晰度切换成功:', res);
      })
      .catch(err => {
        console.error('清晰度切换失败:', err);
      });
  }
}
//尝试重新播放
async function rePlay() {
  console.log('重新播放重新播放', droneParaObj);
  player.value = null;
  videoPlayError.value = false;
  if (finalUrl.value) {
    if (!droneParaObj.url_type) {
      console.log('重新播放外部视频');
      // 外部视频
      playExternalVideo();
    } else {
      if (!droneParaObj?.droneSelected && !droneParaObj?.cameraSelected) {
        ElMessage.error('无人机当前状态不支持播放视频');
        videoPlayError.value = false;
        return;
      }
      console.log('重新播放视频');
      refreshStreams({
        url: '',
        video_id: `${droneParaObj?.droneSelected}/${droneParaObj?.cameraSelected}/normal-0`,
        url_type: droneParaObj.url_type,
        video_quality: droneParaObj?.claritySelected
      }).then(res => {
        finalUrl.value = res?.url;
        if (finalUrl.value) {
          if (droneParaObj.source == '2') {
            console.log('海康');
            videoPlayHK();
          } else {
            console.log('国标');
            videoPlay();
          }
        }
      });
    }
  }
}
function playExternalVideo() {
  getVideoUrl({ id: droneParaObj.cameraId })
    .then(res => {
      finalUrl.value = res;
      if (finalUrl.value) {
        if(droneParaObj?.source == '2'){
          videoPlayHK();
        }else{
          videoPlay();    
        }
      } else {
        videoPlayError.value = true;
      }
    })
    .catch(err => {
      console.error(err);
    });
}

function realplayHK() {
  // player.value = null;
  counts.value = 0;
  videoPlayError.value = true;
}

//停止播放器移除后执行一些重置操作
async function cancelPlay(type = '') {
  player.value && player.value?.destroy();
  player.value = null;
  counts.value = 0;
  if (type != 'closeVideo') {
    //停止播放
    videoPlayError.value = true;
  }
  window.$bus.emit('closeVideoView');
}
//切换清晰度
function changeTheQuality(type) {
  let video_quality = 3;
  switch (type) {
    case '自动':
      video_quality = 0;
      break;
    case '流畅':
      video_quality = 1;
      break;
    case '标准':
      video_quality = 2;
      break;
    case '高清':
      video_quality = 3;
      break;
    case '超清':
      video_quality = 4;
      break;
    default:
      break;
  }
  if (droneParaObj.isExternal) {
    // 外部视频
    getVideoUrl({ id: droneParaObj.cameraId, video_quality: video_quality })
      .then(res => {
        finalUrl.value = res;
        if (finalUrl.value) {
          videoPlay();
        }
      })
      .catch(err => {
        console.error(err);
      });
    return;
  }
}

function closeVideo() {
  window.$bus.emit('liveStreamClose', { ...droneParaObj });
  onStop();
}

// 加载海康视频
function initHK() {
  // 设置播放容器的宽高并监听窗口大小变化
  window.addEventListener('resize', () => {
    player.value && player.value.JS_Resize();
  });
}

//播放海康视频
function videoPlayHK() {
  videoPlayError.value = false;
  !player.value && createPlayerHK();
  let playURL = finalUrl.value;
  setTimeout(() => {
    player.value && player?.value.JS_Resize();
  }, 150);
  player.value.JS_Play(playURL, { playURL, mode: 1 }, props.index).then(
    () => {
      console.log('realplay success');
    },
    e => {
      console.error(e);
    }
  );
}

// 创建海康视频
function createPlayerHK() {
  player.value = new window.JSPlugin({
    szId: props.playerId,
    szBasePath:
      import.meta.env.VITE_APP_NODE_ENV === 'development' ? '../../../public/js/isc-h5' : '../../../js/isc-h5', // 本地
    iMaxSplit: 1,
    iCurrentSplit: 1,
    bSupporDoubleClickFull: false,
    oStyle: {
      borderSelect: '#000'
    }
  });

  // 事件回调绑定
  player.value.JS_SetWindowControlCallback({
    windowEventSelect: function (iWndIndex) {
      //插件选中窗口回调
      console.log('windowSelect callback: ', iWndIndex);
    },
    pluginErrorHandler: function (iWndIndex, iErrorCode, oError) {
      //插件错误回调
      console.log('pluginError callback: ', iWndIndex, iErrorCode, oError);
      realplayHK();
    },
    windowEventOver: function (iWndIndex) {
      //鼠标移过回调
      console.log(iWndIndex);
    },
    windowEventOut: function (iWndIndex) {
      //鼠标移出回调
      //console.log(iWndIndex);
    },
    windowEventUp: function (iWndIndex) {
      //鼠标mouseup事件回调
      //console.log(iWndIndex);
    },
    windowFullCcreenChange: function (bFull) {
      //全屏切换回调
      console.log('fullScreen callback: ', bFull);
    },
    // firstFrameDisplay: function (iWndIndex, iWidth, iHeight) {  //首帧显示回调
    //     console.log('firstFrame loaded callback: ', iWndIndex, iWidth, iHeight);
    // },
    performanceLack: function () {
      //性能不足回调
      console.log('performanceLack callback: ');
    }
  });
}

// 停止播放海康
function onStopHK() {
  player.value &&
    player.value.JS_Stop &&
    player.value.JS_Stop().then(
      () => {
        playback.rate = 0;
        player.value = null;
        counts.value = 0;
        window.$bus.emit('closeVideoView');
        console.log('stop realplay success');
      },
      e => {
        console.error(e);
      }
    );
}

// 全屏-海康
function wholeFullScreen() {
  if (droneParaObj.source == '2') {
    // 海康视频全屏
    player.value.JS_FullScreenDisplay(true).then(
      () => {
        console.log(`wholeFullScreen success`);
      },
      e => {
        console.error(e);
      }
    );
  } else {
    // JessibucaPro 全屏
    if (player.value) {
      const element = document.getElementById(props.playerId);
      if (element.requestFullscreen) {
        element.requestFullscreen();
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
      }
    }
  }
}

function clearDronePara() {
  droneParaObj.droneSelected = null;
  droneParaObj.cameraSelected = null;
  droneParaObj.cameraName = null;
  droneParaObj.cameraId = null;
  droneParaObj.videoSelected = null;
  droneParaObj.claritySelected = optionData.clarityList[0].value;
  droneParaObj.lensSelected = null;
  droneParaObj.isDockLive = false;
  droneParaObj.isExternal = false;
}

function onStop() {
  console.log('停止播放', droneParaObj);
  if (droneParaObj?.droneSelected == null || droneParaObj?.cameraId == null || droneParaObj?.claritySelected == null) {
    console.log('waring: not select live para!!!');
    return;
  }
  if (droneParaObj.source == '2' && droneParaObj.url_type != '1') {
    onStopHK();
  }
  videoPlayError.value = false;
  cancelPlay('closeVideo');
  droneParaObj.cameraName = '';
  droneParaObj.droneSelected = '';
  console.log('stop play livestream', droneParaObj);
  finalUrl.value = '';
  clearDronePara();
}

async function onStart(data) {
  console.log('data', data);
  if (finalUrl.value) {
    setTimeout(() => {
      player.value && player?.value.JS_Resize();
    }, 50);
    return;
  }
  if (data?.droneSelected == null || data?.cameraId == null || data?.claritySelected == null) {
    console.log('waring: not select live para!!!!');
    return;
  }
  if (data.source == '2' && data?.cameraSelected == null) {
    console.log('播放错误');
    return;
  }
  if (data?.claritySelected != null) {
    commandQuality(data.claritySelected);
  }
  videoPlayError.value = false;
  Object.assign(droneParaObj, data);
  if (!data.url_type) {
    // 外部视频
    playExternalVideo();
    return;
  }
  startLivestream({
    url: '',
    video_id: `${data?.droneSelected}/${data?.cameraSelected}/normal-0`,
    url_type: data.url_type || 1,
    video_quality: data?.claritySelected
  })
    .then(res => {
      finalUrl.value = res?.url;
      if (finalUrl.value) {
        if (data.source == '1') {
          videoPlay();
        } else {
          videoPlayHK();
        }
      }
    })
    .catch(err => {
      console.error(err);
    });
}
onUnmounted(() => {
  console.log('关闭===============');
  window.$bus.off('refreshVideo');
  window.$bus.off('changeVideo');
  // window.$bus.off('hkPlayerResize');
  onStop();
  // window.removeEventListener('resize', () => {})
});

defineExpose({
  onStop,
  onStart,
  h5VideoPlay
});
</script>
<style lang="scss">
.quality-popper {
  border: none;
  .el-dropdown-menu {
    background-color: #161616;
    opacity: 0.7;
    border-color: #161616;
  }
  .el-dropdown-menu--small .el-dropdown-menu__item {
    color: #fff;
  }
  .el-dropdown-menu__item {
    color: #fff;
  }
  .el-dropdown-menu__item:not(.is-disabled):focus {
    background-color: #161616;
    opacity: 0.3;
  }
}
.el-dropdown__popper.el-popper {
  border: none;
}
</style>
<style scoped lang="scss">
.empty {
  background: url('../../assets/empty.png') no-repeat center center;
  z-index: 999;
}
.palyer-container {
  position: relative;
  background-color: #262c33;
  height: 100%;
  border: 1px solid #101010;
  .player {
    width: 100% !important;
    height: 100% !important;
  }
  .hiddenPlayer {
    display: none;
  }
  :deep(.sub-wnd) {
    background-color: rgb(38, 44, 51) !important;
    border-color: rgb(38, 44, 51) !important;
  }

  .title-name {
    position: absolute;
    color: white;
    font-size: 14px;
    top: 0px;
    left: 10px;
  }
  .close-icon {
    background-color: #262c33;
    position: absolute;
    color: white;
    font-size: 14px;
    top: 10px;
    right: 10px;
    cursor: pointer;
    border-radius: 50%;
    width: 22px;
    height: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .cursor {
    cursor: pointer;
  }
  .full-icon {
    background-color: #262c33;
    position: absolute;
    color: white;
    font-size: 14px;
    cursor: pointer;
    border-radius: 50%;
    width: 22px;
    height: 22px;
    bottom: 10px;
    right: 10px;
  }
}
.quality {
  line-height: 38px;
  margin-right: 10px;
  cursor: pointer;
  .quality-title {
    color: #fff;
    border: none;
    &.active {
      border: none;
    }
    &.hover {
      border: none;
    }
  }
}
.toolbar {
  text-align: right;
  position: absolute;
  display: flex;
  justify-content: flex-end;
  width: 100%;
  height: 38px;
  line-height: 38px;
  background: #161616;
  color: #fff;
  opacity: 0.8;
  left: 0;
  bottom: 0;
}
.videoPlayError {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #262c33;
  border: 1px solid #101010;
}
</style>
