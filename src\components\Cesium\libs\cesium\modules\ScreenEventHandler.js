import * as Cesium from 'cesium';
class ScreenEventHandler {
  constructor(viewer) {
    this.viewer = viewer;
    this.handlers = {};
  }

  addEventListener(eventType, callback) {
    this.handlers[eventType] = callback;
    this.viewer.screenSpaceEventHandler.setInputAction(movement => {
      switch (eventType) {
        case 'LEFT_CLICK':
          if (movement.position) {
            this.handlers[eventType](movement.position);
          }
          break;
        case 'LEFT_UP':
          if (movement.position) {
            this.handlers[eventType](movement.position);
          }
          break;
        case 'MOUSE_MOVE':
          if (movement.endPosition) {
            this.handlers[eventType](movement.endPosition);
          }
          break;
        case 'LEFT_DOWN':
          if (movement.endPosition) {
            this.handlers[eventType](movement.endPosition);
          }
          break;
        // 添加其他事件类型的处理代码
      }
    }, eventTypeToActionType(eventType));
  }

  removeEventListener(eventType) {
    if (this.handlers[eventType]) {
      this.viewer.screenSpaceEventHandler.removeInputAction(eventTypeToActionType(eventType));
      delete this.handlers[eventType];
    }
  }

  removeAllEventListeners() {
    for (const eventType in this.handlers) {
      this.removeEventListener(eventType);
    }
  }
}

//#region 支持的方法
// const ScreenSpaceEventTypes = {
//   LEFT_DOWN: 0,
//   LEFT_UP: 1,
//   LEFT_CLICK: 2,
//   LEFT_DOUBLE_CLICK: 3,
//   RIGHT_DOWN: 5,
//   RIGHT_UP: 6,
//   RIGHT_CLICK: 7,
//   MIDDLE_DOWN: 10,
//   MIDDLE_UP: 11,
//   MIDDLE_CLICK: 12,
//   MOUSE_MOVE: 15,
//   WHEEL: 16,
//   PINCH_START: 17,
//   PINCH_END: 18,
//   PINCH_MOVE: 19,
// };

const ScreenSpaceEventTypes = {
  LEFT_DOWN: 'LEFT_DOWN',
  LEFT_UP: 'LEFT_UP',
  LEFT_CLICK: 'LEFT_CLICK',
  LEFT_DOUBLE_CLICK: 'LEFT_DOUBLE_CLICK',
  RIGHT_DOWN: 'RIGHT_DOWN',
  RIGHT_UP: 'RIGHT_UP',
  RIGHT_CLICK: 'RIGHT_CLICK',
  MIDDLE_DOWN: 'MIDDLE_DOWN',
  MIDDLE_UP: 'MIDDLE_UP',
  MIDDLE_CLICK: 'MIDDLE_CLICK',
  MOUSE_MOVE: 'MOUSE_MOVE',
  WHEEL: 'WHEEL',
  PINCH_START: 'PINCH_START',
  PINCH_END: 'PINCH_END',
  PINCH_MOVE: 'PINCH_MOVE'
};
function eventTypeToActionType(eventType) {
  switch (eventType) {
    case 'LEFT_CLICK':
      return Cesium.ScreenSpaceEventType.LEFT_CLICK;
    case 'LEFT_UP':
      return Cesium.ScreenSpaceEventType.LEFT_UP;
    case 'MOUSE_MOVE':
      return Cesium.ScreenSpaceEventType.MOUSE_MOVE;
    case 'LEFT_DOWN':
      return Cesium.ScreenSpaceEventType.LEFT_DOWN;
    // 添加其他事件类型的映射
    default:
      return null;
  }
}

//#endregion
export { ScreenSpaceEventTypes, ScreenEventHandler };
