<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-position="right" label-width="90px">
          <el-form-item label="" prop="key">
            <el-input
              class="input-serach"
              v-model="queryParams.key"
              placeholder="请输入航线名称"
              clearable
              @keyup.enter="handleSearch"
              maxlength="50"
            />
          </el-form-item>

          <el-form-item label="" prop="key">
            <el-date-picker
              class="input-serach"
              v-model="queryParams.time_range"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @change="handleSearch"
            />
          </el-form-item>
          <el-form-item label="" prop="template_type">
            <el-select
              class="input-serach"
              v-model="queryParams.template_type"
              placeholder="请选择航线类型"
              clearable
              @change="handleSearch"
            >
              <el-option
                v-for="(item, index) in optionData.airlineTypeOption"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="设备型号：" prop="deviceModel">
            <el-select
              class="input-serach"
              v-model="queryParams.deviceModel"
              placeholder="请选择设备型号"
              clearable
              @change="handleSearch"
            >
              <el-option v-for="(item, index) in optionData" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>

          <el-form-item label="行政区：" prop="areaId">
            <el-input
              class="input-serach"
              v-model="queryParams.areaId"
              placeholder="请输入行政区"
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
            />
          </el-form-item> -->
        </el-form>
      </div>
      <div class="search-btn">
        <el-button type="primary" @click="handleSearch()"><i-ep-search />搜索</el-button>
        <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
      </div>
    </div>
    <el-card shadow="never">
      <template #header>
        <div style="display: flex">
          <el-button type="primary" @click="openSelectDialog()" v-if="authorityShow('createPlan')"><i-ep-plus />新建航线</el-button>
          <el-upload
            class="single-uploader"
            style="margin-left: 10px"
            :show-file-list="false"
            :before-upload="handleBeforeUpload"
            :http-request="uploadFile"
            v-if="authorityShow('importPlan')"
          >
            <el-button type="primary" v-if="authorityShow('importPlan')"><i-ep-upload />导入</el-button>
          </el-upload>
          <el-button style="margin-left: 10px" type="primary" @click="batchDownload()" v-if="authorityShow('downPlan')"><i-ep-download />下载</el-button>
          <el-button type="info" @click="batchDelete()" v-if="authorityShow('deletePlan')"><i-ep-delete />删除</el-button>
        </div>
      </template>

      <div class="plan-info">
        <el-row :span="24" :gutter="20" class="data-box" v-if="dataList.length > 0">
          <el-col :span="5" v-for="item in dataList" :key="item.id">
            <div class="plan-item" @click="openPlanInfo(item)">
              <div class="plan-item-top">
                <!-- job_type ：工作类型（0：普通任务，1：接处警任务） -->
                <warning-label
                  :job_type="item.job_type || 0"
                  :template-type="item.template_types[0] || 0"
                  class="warning-label"
                  v-show="item.job_type != null && item.job_type !== 0"
                />
                <img class="left-img" :src="item.thumbnail" alt="" />
                <div class="btn-box">
                  <div class="delete" @click.stop="handleDelete(item)" v-if="authorityShow('deletePlan')">
                    <i-ep-delete />
                  </div>

                  <div class="delete" @click.stop="downloadWayline(item)" v-if="authorityShow('downPlan')">
                    <i-ep-download />
                  </div>
                </div>
                <el-checkbox class="select" v-model="checkedItems" :label="item.id" @click.stop="selectWayLine(item)">
                  <!-- 使用 slot 来自定义 Checkbox 的内容 -->
                  <span style="display: none">{{ item.id }}</span>
                </el-checkbox>
              </div>
              <div class="plan-item-bottom">
                <div class="title-box">
                  <!-- import_type： 创建类型（0：平台创建，1：其他平台导入，2：遥控器同步）
                  0 都为创建，1、2为导入 -->
                  <type-mark :import_type="item.import_type || 0" />
                  <div class="name" :title="item.name">{{ item.name }}</div>
                </div>
                <!-- <div class="time-box">
                  {{ item.payload_model_keys }}
                </div> -->
                <div class="time-box">
                  <span>{{ moment(item.update_time).format('YYYY-MM-DD HH:mm:ss') }}</span>
                </div>
                <div class="info-div">
                  <div class="item-info">
                    <img class="svgColor" :src="cameraIcon" alt="" />
                    <span v-for="payload in item.payload_model_keys" :key="payload.id">
                      {{ DEVICE_NAME[payload] }}
                    </span>
                  </div>
                  <div class="item-info">
                    <img class="svgColor" :src="cameraTypeIcon" alt="" />
                    {{ DEVICE_NAME[item.drone_model_key] }}
                  </div>

                  <div class="item-info ellipsis">
                    <img class="svgColor" :src="companyIcon" alt="" />
                    <el-tooltip
                      show-after="1"
                      :content="item.user_name"
                      raw-content="true"
                      placement="top"
                      v-if="userNameWidth(item.user_name) > 110"
                    >
                      <div class="user-name ellipsis">
                        {{ item.user_name }}
                      </div>
                    </el-tooltip>
                    <div v-else class="user-name ellipsis">
                      {{ item.user_name }}
                    </div>
                  </div>

                  <div class="item-info">
                    <img class="svgColor" :src="WAYLINE_ICONS[item.template_types[0]]" alt="" />
                    {{ WAYLINE_TYPE[item.template_types[0]] }}
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <div class="empty" v-else>暂无数据</div>
      </div>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </el-card>
    <el-dialog
      v-if="dialogVisible"
      v-model="dialogVisible"
      title="新建航线"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="dialog-content">
        <flight-info @change-handle="onChangeHandle" />
      </div>
    </el-dialog>
  </div>
</template>

<!--应用管理-->
<script>
export default {
  name: 'Airroute'
};
</script>

<script setup>
import { reactive } from 'vue';
import router from '@/router';
import optionData from '@/utils/option-data';
import cameraIcon from '@/assets/icons/camera.svg';
import cameraTypeIcon from '@/assets/icons/camera_type.svg';
import companyIcon from '@/assets/icons/company.svg';
import addressIcon from '@/assets/icons/address.svg';
import waylineIcon0 from '@/assets/icons/wayline0.svg';
import waylineIcon1 from '@/assets/icons/wayline1.svg';
import { uploadWayLine, getWaylines, downloadWaylineFile, deleteWayLine, batchDeleteWayLine } from '@/api/wayline';
import moment from 'moment';
import { DEVICE_NAME, WAYLINE_TYPE } from '@/utils/constants';
import MainSocketService from '@/utils/websocket/MainSocketService';
import { downFile } from '@/utils/helper';
import { usePlanInfoStore } from '@/store/modules/planInfo.js';
import FlightInfo from './components/FlightInfo.vue';
import WarningLabel from './components/WarningLabel.vue';
import TypeMark from './components/TypeMark.vue';
// 引入 JSZip 库
import JSZip from 'jszip';
import { useDeviceStore } from '@/store/modules/device.js';
import { getDroneInfoByPlanData } from '../common/devConfigHelper.js';
import { authorityShow } from '@/utils/authority';
const deviceStore = useDeviceStore();
const planInfoStore = usePlanInfoStore();
const airlineDefaultPng = new URL('@/assets/airline-default.png', import.meta.url);
const airpolygonDefaultPng = new URL('@/assets/airpolygon-default.png', import.meta.url);
const otherDefaultPng = new URL('@/assets/other-default.png', import.meta.url);

const props = defineProps({
  isDialogMode: {
    type: Boolean,
    default: false
  }
});

// 标记
let waylineInfo = ref({
  inputType: 0, // 输入类型
  isWarning: true,
  templateType: 0 //航线类型
});

const ids = ref([]);
const total = ref(0);
const dialogVisible = ref(false);
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  time_range: '',
  key: '',
  template_type: '',
  begin_time: '',
  end_time: ''
});
//航线名称、飞行器及负载信息
let info = ref({
  planId: '',
  planName: '新建航点航线',
  droneEnumVal: 91, //飞行器
  droneSubEnumVal: 1,
  droneSubEnumLabel: '', //'Matrice 3TD',
  payloadEnumVal: 81, //负载
  payloadSubEnumVal: 0
});
const dataList = ref([]);
const checkedItems = ref([]); //选中的项
const checkedList = ref([]); //勾选过的数组（不论后续是否取消都记录），批量下载分页记录过滤用

const WAYLINE_ICONS = {
  0: waylineIcon0,
  1: waylineIcon1
};

function userNameWidth(userName) {
  // 创建一个临时的div元素来计算宽度
  const tempDiv = document.createElement('div');
  tempDiv.style.position = 'absolute';
  tempDiv.style.left = '-9999px'; // 将元素移出可视区域
  tempDiv.style.whiteSpace = 'nowrap';
  tempDiv.style.fontSize = '14px'; // 设置字体大小，以确保宽度计算准确
  tempDiv.style.fontFamily = 'Arial'; // 设置字体，以确保宽度计算准确
  tempDiv.style.visibility = 'hidden';
  tempDiv.style.maxWidth = '130px'; // 设置最大宽度
  tempDiv.textContent = userName; // 设置文本内容
  // 将临时元素添加到文档中
  document.body.appendChild(tempDiv);
  // 计算宽度
  const width = tempDiv.offsetWidth;
  // 从文档中移除临时元素
  document.body.removeChild(tempDiv);
  return width;
}
/**
 * 查询
 */
function handleQuery() {
  let params = {
    order_by: 'update_time desc',
    page: queryParams.pageNum,
    page_size: queryParams.pageSize,
    key: queryParams.key,
    begin_time: queryParams.begin_time,
    end_time: queryParams.end_time
  };
  if (queryParams.template_type !== '') {
    params.template_type = queryParams.template_type;
  }
  getWaylines(params)
    .then(data => {
      dataList.value = [];
      const { list, pagination } = data;
      console.log('list:', list);
      (list || []).forEach(e => {
        if (e.thumbnail?.length === 0) {
          // 这里按照类别进行划分
          if (e.template_types[0] === 0) {
            e.thumbnail = airlineDefaultPng.href;
          } else if (e.template_types[0] === 1) {
            e.thumbnail = airpolygonDefaultPng.href;
          } else if (e.template_types[0] === 2) {
            e.thumbnail = otherDefaultPng.href;
          }
        }
        dataList.value.push(e);
      });
      total.value = pagination.total;
    })
    .catch(e => {
      console.log('e:', e);
    });
}
function handleSearch() {
  if (queryParams.time_range) {
    queryParams.begin_time = new Date(queryParams.time_range[0] + ' 00:00:00').getTime();
    queryParams.end_time = new Date(queryParams.time_range[1] + ' 23:59:59').getTime();
  } else {
    queryParams.begin_time = '';
    queryParams.end_time = '';
  }
  queryParams.pageNum = 1;
  handleQuery();
}
/**
 * 重置查询
 */
function resetQuery() {
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    time_range: '',
    key: '',
    template_type: '',
    begin_time: '',
    end_time: ''
  });
  handleQuery();
}
//选择日期范围
function selectTime() {
  if (queryParams.time_range) {
    queryParams.begin_time = new Date(queryParams.time_range[0] + ' 00:00:00').getTime();
    queryParams.end_time = new Date(queryParams.time_range[1] + ' 23:59:59').getTime();
  } else {
    queryParams.begin_time = '';
    queryParams.end_time = '';
  }
}

/**
 * 行checkbox change事件
 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
}

function openSelectDialog() {
  dialogVisible.value = true;
}

function toCreate(type) {
  dialogVisible.value = false;
  if (type === '0') {
    // 航点模式
    if (props.isDialogMode) {
      router.push({ path: '/newplan-no-header' });
    } else {
      router.push({ path: '/newplan' });
    }
  } else if (type === '1') {
    // 航点模式 新建清空寄存器数据
    planInfoStore.setCurPlanData(null);
    if (props.isDialogMode) {
      router.push({ path: '/surface-no-header' });
    } else {
      router.push({ path: '/surface' });
    }
  } else {
    ElMessage.warning('暂不支持该类型航线的创建设置。');
  }
}
/**
 * 打开计划信息面板
 * @param {Object} item - 计划对象，包含计划的详细信息
 *                        例如：{ id: 1, name: '计划A', description: '这是计划A的描述' }
 * @returns {void}
 * 说明：
 * - 该函数用于展示特定计划的详细信息，如计划名称、描述等。
 * - 当用户点击或悬停在某个计划上时，会调用此函数以显示更多详细信息。
 */
function openPlanInfo(item) {
  if (!item.id) {
    ElMessage.warning('传入的ID不能为空！');
    return;
  }
  if (item.airline_json == undefined || item.airline_json == null) {
    ElMessage.warning('航线配置信息为空，请核实。');
    return;
  }
  getWaylines({
    order_by: 'update_time desc',
    wayline_id: item.id
  }).then(data => {
    const { list = [] } = data;
    if (list?.length > 0) {
      let planData = list[0];
      let templateType = planData.template_types[0];
      let id = planData.id || '';
      planInfoStore.setCurPlanData(planData);
      // 通过计划数据获取基础的设备信息
      const droneBaseInfo = getDroneInfoByPlanData(planData);
      deviceStore.setCurrentDevice(droneBaseInfo);
      deviceStore.getDeviceInfoByType(droneBaseInfo.droneModelKey);

      let basePath = '';
      let queryParams = { id: id };

      if (templateType === 0) {
        basePath = '/planinfo';
      } else if (templateType === 1) {
        basePath = '/surface';
        queryParams.import_type = planData.import_type || 0;
      } else {
        ElMessage.warning('暂不支持该类型航线的创建设置。');
        return;
      }

      const finalPath = props.isDialogMode ? `${basePath}-no-header` : basePath;
      router.push({
        path: finalPath,
        query: queryParams
      });
    }
  });
}
function openPlanInfo2(item) {
  let templateType = item.template_types[0];
  // 保存航线类型等基础数据
  // localStorage.setItem('templateType', item);
  if (templateType === 0) {
    if (item.airline_json == undefined || item.airline_json == null) {
      ElMessage.warning('暂不支持该航线的修改设置。');
      return;
    } else {
      getWaylines({
        order_by: 'update_time desc',
        wayline_id: item.id
      }).then(data => {
        const { list = [] } = data;
        if (list?.length > 0) {
          let planData = list[0];
          let id = planData.id || '';
          planInfoStore.setCurPlanData(planData);
          // 通过计划数据获取基础的设备信息
          const droneBaseInfo = getDroneInfoByPlanData(planData);
          deviceStore.setCurrentDevice(droneBaseInfo);
          deviceStore.getDeviceInfoByType(droneBaseInfo.droneModelKey);
          router.push({
            path: '/planinfo',
            query: {
              id: id
            }
          });
        }
      });
    }
  } else if (templateType === 1) {
    // 航面模式
    if (item.airline_json == undefined || item.airline_json == null) {
      ElMessage.warning('暂不支持该航线的修改设置。');
      return;
    } else {
      getWaylines({
        order_by: 'update_time desc',
        wayline_id: item.id
      }).then(data => {
        const { list = [] } = data;
        if (list?.length > 0) {
          let planData = list[0];
          let id = planData.id || '';
          planInfoStore.setCurPlanData(planData);
          // 通过计划数据获取基础的设备信息
          const droneBaseInfo = getDroneInfoByPlanData(planData);
          deviceStore.setCurrentDevice(droneBaseInfo);
          deviceStore.getDeviceInfoByType(droneBaseInfo.droneModelKey);
          let import_type = planData.import_type || 0;
          router.push({
            path: '/surface',
            query: {
              id: id,
              import_type: import_type
            }
          });
        }
      });
    }
  } else {
    ElMessage.warning('暂不支持该类型航线的创建设置。');
  }
}
/**
 * 删除
 */
function handleDelete(row) {
  ElMessageBox.confirm(`删除后不能撤回！`, '确认删除该航线？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    autofocus: false,
    closeOnClickModal: false,
    type: 'warning'
  }).then(() => {
    deleteWayLine(row.id).then(data => {
      ElMessage.success('删除成功');
      handleQuery();
    });
  });
}
/**
 * 批量删除
 */
const batchDelete = () => {
  // 批量删除选中的项
  console.log('选中的项：', checkedItems.value);
  ElMessageBox.confirm(`批量删除后不能撤回！`, '确认批量删除所选航线？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    autofocus: false,
    closeOnClickModal: false,
    type: 'warning'
  }).then(() => {
    if (checkedItems.value.length) {
      batchDeleteWayLine(checkedItems.value).then(data => {
        ElMessage.success('删除成功');
        checkedItems.value = []; //删除后记得清空所选ID数组
        checkedList.value = [];
        handleQuery();
      });
    } else {
      ElMessage.warning('请先勾选要删除的航线');
    }
  });
};
//记录勾选过的航线记录
function selectWayLine(item) {
  checkedList.value.push(item);
  console.log('获取当前航', item);
}
/**
 * 批量下载
 */
function batchDownload() {
  if (checkedItems.value.length) {
    //获取当前时间时分秒
    const nowTime = moment().format('yyyyMMDDHHmmss');
    // 创建一个 JSZip 实例
    const zip = new JSZip();
    // 构造下载每个文件的 Promise 数组
    const downloadPromises = checkedItems.value.map(item => {
      //通过ID取勾选记录列表寻找对应的航线名称并赋值文件名
      const foundItems = checkedList.value.find(i => item == i.id);
      return downloadWaylineFile(item).then(res => {
        if (res) {
          // 将每个文件内容添加到 zip 中，使用文件名作为 key
          zip.file(foundItems.name + '.kmz', res);
        }
      });
    });
    // 等待所有文件下载完成
    Promise.all(downloadPromises).then(() => {
      // 生成 zip 文件并下载
      zip.generateAsync({ type: 'blob' }).then(function (content) {
        // 下载生成的 zip 文件
        downFile(content, '航线文件_' + nowTime + '.zip');
        ElMessage.success('下载成功');
        checkedItems.value = []; //下载完成后记得清空所选ID数组
        checkedList.value = [];
      });
    });
  } else {
    ElMessage.warning('请先勾选要下载的航线');
  }
}

/**
 * 自定义图片上传
 *
 * @param options
 */
async function uploadFile(options) {
  if (options.file.name.length === 0) {
    ElMessage.warning('文件名称不能为空，请修改后重试');
    return;
  }
  // 这里 name 包括 .kmz 后缀 会增加后缀判断长度 需要去掉后最
  const name = options.file.name.slice(0, options.file.name.lastIndexOf('.')) ?? '';
  if (name.length > 32) {
    ElMessage.warning('文件名称不能超过32个字符，请修改后重试');
    return;
  }
  try {
    const formData = new FormData();
    formData.append('file', options.file);
    await uploadWayLine(formData);
    ElMessage.success('上传成功');
    handleSearch();
  } catch (error) {
    console.log(error);
  }
}

/**
 * 限制用户上传文件的格式和大小
 */
function handleBeforeUpload(file) {
  let name = file.name + '';
  if (!name.includes('.kmz')) {
    ElMessage.warning('请上传kmz文件');
    return false;
  }
  if (file.size > 100 * 1048 * 1048) {
    ElMessage.warning('上传文件不能大于100M');
    return false;
  }

  return true;
}

function downloadWayline(item) {
  downloadWaylineFile(item.id).then(res => {
    if (!res) {
      return;
    }
    const data = new Blob([res], { type: 'application/zip' });
    downFile(data, item.name + '.kmz');
  });
}

function onChangeHandle(option) {
  if (!option) {
    return;
  }
  const { checked, type } = option;
  if (checked) {
    toCreate(type);
  }
}

onMounted(() => {
  handleQuery();
});
</script>

<style scoped lang="scss">
.input-serach {
  width: 200px;
}

.flex-center {
  display: flex;
  align-items: center;

  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #6e6e6e;
  }
}

.search {
  display: flex;
  align-items: center;
  padding: 0 24px;

  .search-form {
    padding-top: 16px;
    flex: 1;
  }
}

.dialog-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;

  .select-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 4px;
    cursor: pointer;
    height: 204px;
    width: 176px;
    background: #f8f9fb;
    border-radius: 4px;
    border: 1px solid #e7e8f2;
    text-align: center;

    img {
      width: 80px;
      height: 80px;
      margin: 24px auto 5px;
    }

    .title {
      font-size: 16px;
      font-weight: 700;
      margin-bottom: 8px;
      color: #465467;
    }

    .desc {
      font-size: 12px;
      color: #465467;
      padding: 0 28px;
    }
  }

  .select-item:hover {
    background: #eff6ff;
  }
}
.empty {
  height: 480px;
  line-height: 480px;
  width: 100%;
  text-align: center;
}

.plan-info {
  .plan-item:hover {
    .delete {
      opacity: 1 !important;
      cursor: pointer !important;
    }
    // animation: shadowBreathe 2s ease-in-out infinite;
  }

  .plan-item {
    margin-bottom: 20px;
    align-items: center;
    color: #fff;
    border: 1px solid #475467;
    box-shadow: 0 8px 14px #959cb600;
    border-radius: 4px;
    cursor: pointer !important;
    transition: box-shadow 0.3s ease; /* 平滑过渡效果 */
    .plan-item-top {
      position: relative;
      .left-img {
        width: 100%;
        height: 200px;
      }

      .btn-box {
        position: absolute;
        top: 40px;
        right: 12px;
        display: flex;
        flex-direction: column;

        .delete {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 20px;
          background: #0000007a;
          border-radius: 4px;
          opacity: 0;
          margin-bottom: 6px;
          padding: 5px 5px;
        }
        .delete:hover {
          background: #292929;
        }
      }

      .warning-label {
        position: absolute;
        top: 10px;
        left: 10px;
      }
    }
    .time-box {
      justify-content: space-between;
      height: 26px;
      line-height: 26px;
      font-size: 12px;
      color: #98a2b3;
    }
    .plan-item-bottom {
      padding: 5px 16px 0;
      flex: 1;
      height: 120px;
      line-height: 20px;
      color: #0009;
      font-size: 12px;
      overflow: hidden;
      .title-box {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 6px;
        .name {
          line-height: 18px;
          font-weight: 700;
          color: #fff;
          font-size: 16px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-left: 5px;
          flex-grow: 1; /* 让.name独占剩余宽度 */
        }
      }

      .info-div {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        margin-top: 8px;
        .item-info {
          display: flex;
          flex-basis: 50%;
          align-items: center;
          line-height: 22px;
          color: #98a2b3;
          img {
            margin-right: 4px;
            width: 16px;
            height: 16px;
          }
        }
      }
    }
  }
  .plan-item:hover::after {
    transform: translateY(0); /* 鼠标滑过时，阴影渐变显示出来 */
  }
}
@keyframes shadowBreathe {
  0%,
  100% {
    box-shadow: 0px 0px 5px 5px rgba(106, 106, 106, 0.3);
  }
  50% {
    box-shadow: 0px 0px 15px 5px rgba(106, 106, 106, 0.6);
  }
}
.select {
  position: absolute;
  top: 10px;
  right: 10px;
}
.data-box {
  .el-col-5 {
    max-width: 20%;
    flex: 0 0 20%;
  }
}
.svgColor {
  color: #98a2b3;
  // filter: hue-rotate(70deg) brightness(95%);
}
.user-name {
  max-width: 130px;
}

.ellipsis {
  overflow: hidden; /* 超出一行文字自动隐藏 */
  text-overflow: ellipsis; /* 文字隐藏后添加省略号 */
  white-space: nowrap; /* 强制文本不换行 */
}
</style>
