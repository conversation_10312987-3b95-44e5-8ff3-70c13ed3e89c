{"openapi": "3.0.1", "info": {"title": "无人机平台", "description": "", "version": "1.0.0"}, "tags": [], "paths": {"/fence/create": {"post": {"summary": "创建电子围栏", "deprecated": false, "description": "创建电子围栏", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FenceCreateReqVO", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonResultLong"}, "example": {"code": 0, "data": 0, "msg": ""}}}, "headers": {}}}, "security": [{"apikey-header-X-Auth-Token": []}]}}, "/fence/update": {"put": {"summary": "更新电子围栏", "deprecated": false, "description": "更新电子围栏", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FenceUpdateReqVO", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonResultBoolean"}, "example": {"code": 0, "data": false, "msg": ""}}}, "headers": {}}}, "security": [{"apikey-header-X-Auth-Token": []}]}}, "/fence/delete": {"delete": {"summary": "删除电子围栏", "deprecated": false, "description": "删除电子围栏", "tags": [], "parameters": [{"name": "id", "in": "query", "description": "编号", "required": true, "example": 1024, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonResultBoolean"}, "example": {"code": 0, "data": false, "msg": ""}}}, "headers": {}}}, "security": [{"apikey-header-X-Auth-Token": []}]}}, "/fence/get": {"get": {"summary": "获得电子围栏详情", "deprecated": false, "description": "获得电子围栏详情", "tags": [], "parameters": [{"name": "id", "in": "query", "description": "编号", "required": true, "example": 1024, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonResultFenceRespVO"}, "example": {"code": 0, "data": {"id": 0, "fence_name": "", "fence_addr": "", "fence_type": "", "fence_target": "", "business_type": "", "pre_process": "", "contact": "", "telephone": "", "fence_status": "", "fence_coords": [[116.404, 39.915], [116.404, 39.917], [116.406, 39.917]], "layer_id": 0, "remark": "", "create_time": "", "creator": ""}, "msg": ""}}}, "headers": {}}}, "security": [{"apikey-header-X-Auth-Token": []}]}}, "/fence/list": {"get": {"summary": "获得电子围栏列表", "deprecated": false, "description": "获得电子围栏列表\n支持关键字搜索，可按名称或地址筛选", "tags": [], "parameters": [{"name": "keyword", "in": "query", "description": "关键字", "required": false, "example": "北区", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonResultListFenceRespVO"}, "example": {"code": 0, "data": [{"id": 0, "fence_name": "", "fence_addr": "", "fence_type": "", "fence_target": "", "business_type": "", "pre_process": "", "contact": "", "telephone": "", "fence_status": "", "fence_coords": [[116.404, 39.915], [116.404, 39.917], [116.406, 39.917]], "layer_id": 0, "remark": "", "create_time": "", "creator": ""}], "msg": ""}}}, "headers": {}}}, "security": [{"apikey-header-X-Auth-Token": []}]}}, "/fence/update-status": {"put": {"summary": "更新电子围栏状态", "deprecated": false, "description": "更新电子围栏状态", "tags": [], "parameters": [{"name": "id", "in": "query", "description": "编号", "required": true, "example": 1024, "schema": {"type": "integer"}}, {"name": "status", "in": "query", "description": "", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonResultBoolean"}, "example": {"code": 0, "data": false, "msg": ""}}}, "headers": {}}}, "security": [{"apikey-header-X-Auth-Token": []}]}}}, "components": {"schemas": {"CommonResultLong": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "data": {"type": "integer", "description": "返回数据"}, "msg": {"type": "string", "description": "错误提示，用户可阅读"}}}, "FenceCreateReqVO": {"type": "object", "properties": {"fenceName": {"type": "string", "description": "围栏名称", "examples": ["北区消防通道"], "maxLength": 100}, "fenceAddr": {"type": "string", "description": "围栏地址", "examples": ["北区3号楼旁"], "maxLength": 255}, "fenceType": {"type": "string", "description": "围栏类型", "examples": ["intrusion"], "maxLength": 32}, "fenceTarget": {"type": "string", "description": "识别目标", "examples": ["vehicle"], "maxLength": 32}, "businessType": {"type": "string", "description": "业务类型", "examples": ["fire_access"], "maxLength": 32}, "preProcess": {"type": "string", "description": "前期处置措施", "examples": ["speaker"], "maxLength": 64}, "contact": {"type": "string", "description": "联系人", "examples": ["张三"], "maxLength": 64}, "telephone": {"type": "string", "description": "联系电话", "examples": ["***********"], "maxLength": 32}, "fenceStatus": {"type": "string", "description": "围栏状态", "examples": ["active"], "maxLength": 32}, "fenceCoords": {"type": "array", "items": {"type": "array", "items": {"type": "number"}}, "description": "围栏坐标集合", "examples": ["[[116.404, 39.915], [116.404, 39.917], [116.406, 39.917]]"]}, "layerId": {"type": "integer", "description": "所属图层ID", "examples": [1]}, "remark": {"type": "string", "description": "备注", "examples": ["这是一个电子围栏"], "maxLength": 256}}, "required": ["fence<PERSON>ame", "fenceType", "<PERSON><PERSON><PERSON><PERSON>", "businessType", "preProcess", "fenceStatus", "fenceCoords"]}, "CommonResultBoolean": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "data": {"type": "boolean", "description": "返回数据"}, "msg": {"type": "string", "description": "错误提示，用户可阅读"}}}, "FenceUpdateReqVO": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键ID", "examples": [1]}, "fenceName": {"type": "string", "description": "围栏名称", "examples": ["北区消防通道"], "maxLength": 100}, "fenceAddr": {"type": "string", "description": "围栏地址", "examples": ["北区3号楼旁"], "maxLength": 255}, "fenceType": {"type": "string", "description": "围栏类型", "examples": ["intrusion"], "maxLength": 32}, "fenceTarget": {"type": "string", "description": "识别目标", "examples": ["vehicle"], "maxLength": 32}, "businessType": {"type": "string", "description": "业务类型", "examples": ["fire_access"], "maxLength": 32}, "preProcess": {"type": "string", "description": "前期处置措施", "examples": ["speaker"], "maxLength": 64}, "contact": {"type": "string", "description": "联系人", "examples": ["张三"], "maxLength": 64}, "telephone": {"type": "string", "description": "联系电话", "examples": ["***********"], "maxLength": 32}, "fenceStatus": {"type": "string", "description": "围栏状态", "examples": ["active"], "maxLength": 32}, "fenceCoords": {"type": "array", "items": {"type": "array", "items": {"type": "number"}}, "description": "围栏坐标集合", "examples": ["[[116.404, 39.915], [116.404, 39.917], [116.406, 39.917]]"]}, "layerId": {"type": "integer", "description": "所属图层ID", "examples": [1]}, "remark": {"type": "string", "description": "备注", "examples": ["这是一个电子围栏"], "maxLength": 256}}, "required": ["id", "fence<PERSON>ame", "fenceType", "<PERSON><PERSON><PERSON><PERSON>", "businessType", "preProcess", "fenceStatus", "fenceCoords"]}, "FenceRespVO": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键ID", "examples": [1]}, "fence_name": {"type": "string", "description": "围栏名称", "examples": ["北区消防通道"]}, "fence_addr": {"type": "string", "description": "围栏地址", "examples": ["北区3号楼旁"]}, "fence_type": {"type": "string", "description": "围栏类型", "examples": ["intrusion"]}, "fence_target": {"type": "string", "description": "识别目标", "examples": ["vehicle"]}, "business_type": {"type": "string", "description": "业务类型", "examples": ["fire_access"]}, "pre_process": {"type": "string", "description": "前期处置措施", "examples": ["speaker"]}, "contact": {"type": "string", "description": "联系人", "examples": ["张三"]}, "telephone": {"type": "string", "description": "联系电话", "examples": ["***********"]}, "fence_status": {"type": "string", "description": "围栏状态", "examples": ["active"]}, "fence_coords": {"type": "array", "items": {"type": "array", "items": {"type": "number"}}, "description": "围栏坐标集合", "examples": ["[[116.404, 39.915], [116.404, 39.917], [116.406, 39.917]]"]}, "layer_id": {"type": "integer", "description": "所属图层ID", "examples": [1]}, "remark": {"type": "string", "description": "备注", "examples": ["这是一个电子围栏"]}, "create_time": {"type": "string", "description": "创建时间"}, "creator": {"type": "string", "description": "创建者", "examples": ["admin"]}}, "required": ["id", "fence_name", "fence_type", "fence_target", "business_type", "pre_process", "fence_status", "fence_coords", "create_time"]}, "CommonResultFenceRespVO": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "data": {"$ref": "#/components/schemas/FenceRespVO", "description": "返回数据"}, "msg": {"type": "string", "description": "错误提示，用户可阅读"}}}, "CommonResultListFenceRespVO": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/FenceRespVO", "description": "电子围栏 Response VO"}, "description": "返回数据"}, "msg": {"type": "string", "description": "错误提示，用户可阅读"}}}}, "securitySchemes": {"apikey-header-X-Auth-Token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-Auth-Token"}}}, "servers": [], "security": [{"apikey-header-X-Auth-Token": []}]}