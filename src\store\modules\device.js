import { defineStore } from 'pinia';
import { reactive } from 'vue';
import { DeviceParameterParser } from '@/views/plan/libs';
// 1、方法
// 通过接口返回 设备信息数据 对象数组 赋值给deviceData
// 方法 获取当前设备型号 这里要做匹配检查是否和当前的配置项中的配置信息是否存在不存在则提示 存在
export const useDeviceStore = defineStore('device', () => {
  const deviceList = reactive([]); // 通过api获取所有设备列表信息
  const deviceAdapter = ref({}); // 记录当前的设备适配器 包括当前选中的设备的系列、型号、配置信息等
  const deviceModelKey = ref(''); // 记录当前的设备型号唯一ID
  const cameraSelected = ref([]); // 当前被选中的相机类型 这里是字符串数组
  const cameraSelectedwithWayLine = ref([]); // 在高级设置中面板的相机类型 这里是航线上的高级设置 点中的相机类型有一个跟随航线就是使用这个数据
  const currentDeviceInfo = ref({}); // 当前设备信息
  //#region 获取设备列表
  // 获取设备信息列表用于表格渲染
  function getDeviceList() {
    return deviceList.length === 0 ? getDeviceDetails() : Promise.resolve(deviceList);
  }
  //#endregion

  /**
   * 获取设备详情
   *
   * @param param 参数对象
   * @returns 返回Promise对象，包含设备详情列表
   */
  function getDeviceDetails() {}

  /**
   * 设置设备类型 这里完成后需要添加回调便于在修改设备幸好之后执行后续刷新动作
   *
   * @param options 设置设备类型的选项
   * @param options.deviceType 设备型号  也是 deviceModelKey 设备型号key
   * @param options.fn 回调函数
   * @returns 无返回值
   * @throws 参数必须是一个对象
   * @throws 设备型号不能为空
   * @throws 未找到适配器，请检查配置文件
   */
  function setDeviceType(options) {
    // 加入对options的校验
    if (!options || typeof options !== 'object') {
      throw new Error('参数必须是一个对象');
    }
    const { deviceType = '', fn = null } = options;
    if (!deviceType || deviceType.length == 0) {
      throw new Error('设备型号不能为空');
    }
    try {
      const devAdp = DeviceParameterParser.parseDeviceParams(deviceType);
      if (!devAdp) {
        deviceAdapter.value = null;
        throw new Error('未找到适配器，请检查配置文件');
      }
      // 获取设备适配器 设置当前store中的其他数据
      deviceAdapter.value = devAdp;
      cameraSelected.value = [];
      if (fn) {
        fn({
          deviceAdapter: deviceAdapter,
          deviceType
        });
      }
    } catch (error) {}
  }
  /**
   * 根据设备型号获取设备信息
   *
   * @param deviceType 设备型号
   * @returns 返回设备信息，若解析失败则返回null
   * @throws 当设备型号为空时，抛出错误
   */
  function getDeviceInfoByType(deviceType) {
    if (!deviceType || deviceType.length == 0) {
      throw new Error('设备型号不能为空');
    }
    deviceAdapter.value = null;
    const devAdp = DeviceParameterParser.parseDeviceParams(deviceType);
    // 这里设置 当前的相机全部选中？
    cameraSelected.value = devAdp.config.imageFormat ?? [];
    if (devAdp) {
      deviceAdapter.value = devAdp;
      return devAdp;
    }
    return null;
  }
  /**
   * 设置选中的相机类型
   *
   * @param cameraList 被选中的相机类型
   * @returns
   */
  function setCameraSelect(cameraList) {
    cameraSelected.value = [];
    (cameraList || []).forEach(item => {
      cameraSelected.value.push(item);
    });
    return cameraSelected.value;
  }
  /**
   * 添加已选摄像头
   *
   * @param c 摄像头标识
   * @returns 返回已选摄像头列表，若未选择任何摄像头则返回空数组
   */
  function addSelectedCamera(c) {
    let clist = cameraSelected.value.find(item => item === c);
    if (!clist) {
      cameraSelected.value.push(c);
    }
    return cameraSelected.value ?? [];
  }
  /**
   * 获取已选择的相机列表
   *
   * @returns 返回已选择的相机列表，如果没有选择则返回空数组
   */
  function getCameraSelect() {
    return cameraSelected.value ?? [];
  }
  /**
   * 设置设备模型键
   *
   * @param dmk 设备模型键
   * @returns 无返回值
   */
  function setDeviceModelKey() {}

  //#region 当前设备信息相关方法
  function getCurrentDevice() {
    return currentDeviceInfo.value ?? {};
  }

  function setCurrentDevice(dev) {
    currentDeviceInfo.value = dev;
  }

  //#endregion

  //#region 当前航线上的相机类型
  function getWayLineCameraType() {
    return cameraSelectedwithWayLine.value ?? [];
  }

  function setWayLineCameraType(cameras = []) {
    cameraSelectedwithWayLine.value = cameras;
  }

  //#endregion

  return {
    cameraSelectedwithWayLine,
    setWayLineCameraType,
    getWayLineCameraType,
    currentDeviceInfo,
    setCurrentDevice,
    getCurrentDevice,
    cameraSelected,
    getCameraSelect,
    addSelectedCamera,
    setCameraSelect,
    deviceList,
    deviceAdapter,
    deviceModelKey,
    // setDeviceType,
    // setDeviceModelKey,
    // getDeviceDetails,
    // getDeviceList,
    getDeviceInfoByType
  };
});
