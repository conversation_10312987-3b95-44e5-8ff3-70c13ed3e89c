class DeviceAdapter {
  constructor(config) {
    this.config = config;
  }

  getConfig() {
    return this.config;
  }

  buildRequestParams() {
    const params = {};
    for (let key in this.config) {
      params[key] = this.config[key];
    }
    return params;
  }

  getValue(key) {
    return this.config[key];
  }

  getDeviceValue() {
    return this.config['device'] ?? null;
  }

  getDeviceValueWithKey(key) {
    let dev = this.config['device'] ?? null;
    if (dev) {
      return dev[key] ?? null;
    }
    return null;
  }

  getCmosValue() {
    return this.config['cmos'] ?? null;
  }

  /**
   * 根据相机类型获取CMOS信息
   * @param cameraType 相机类型
   * @param key CMOS信息的键值 可选
   * @returns 返回目标相机的CMOS信息，如果未指定key则返回整个相机对象，否则返回对应key的CMOS信息值，如果未找到目标相机则返回null
   */
  getCmosValueWithCameraType(cameraType = '', key = '') {
    const cmosList = this.config['cmos'] ?? [];
    const targetCameraObj = cmosList.find(item => item.cameraType === cameraType);
    // 如果存在key 则对key 进行查询
    if (key) {
      return targetCameraObj ? targetCameraObj[key] : null;
    } else {
      return targetCameraObj;
    }
  }

  getImageFormatValue() {
    return this.config['imageFormat'] ?? [];
  }
}

export { DeviceAdapter };
