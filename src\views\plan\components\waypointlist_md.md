# WayPointList 使用示例

      使用示例：
      <WayPointList  />

      import { useWayPointStore } from '@/store/modules/wayPointInfo.js';
      const wayPointStore = useWayPointStore();
      // 设置航点列表
        wayPointStore.setPointList([{
      pointId: 0, // 航点唯一id
      placemarkIndex: 0, //（新增） 具体是第几个Placemark
      longitude: 113,
      latitude: 120,
      actionGroup:[{
        actionGroupId: 0, // 从0开始单调连续递增
        actionGroupMode: 'sequence', // 动作执行模式,sequence：串行执行。即动作组内的动作依次按顺序执行
        actionTriggerType: ACTION_TRIGGER_TYPE.reachPoint, // 动作触发器类型;
      }],
      action: [{
            // 动作列表
            "actionGroupId": 1, //  组ID
            "actionIndex": 0,// 动作索引
            "actionId": 1713874217152,// 这里的动作id 实际上作为组件的唯一标识不再业务上使用
            "actionActuatorFunc": "startRecord"
          }]
    }])

// 添加航点 wayPointStore.addPoint()

// 添加动作 wayPointStore.addAction(ACTION_ACTUATOR_FUNC.takePhoto)

window.$bus.on('changeAction',(res)=>{ // 切换或者修改航点和动作后触发 }); // 清空航点和动作 wayPointStore.clearAll()
