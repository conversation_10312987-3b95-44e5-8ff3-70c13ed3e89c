// 组件控制 主要完成组件的添加、删除，组件数据填充、更新、数据获取等操作，组件从dom上移除，组件挂着到指定的dom上
// 引入组件
import { ActionComponentMap } from '../../actionComponents/index';
import {
  ACTION_ACTUATOR_FUNC,
  ACTION_TRIGGER_TYPE,
  ACTION_ACTUATOR_FUNC_ICON,
  ACTION_ACTUATOR_FUNC_NAME,
  DELETE_ACTION_TYPE
} from '@/utils/constants';
import { createApp } from 'vue';
import { usePlanInfoStore } from '@/store/modules/planInfo.js';
const planInfoStore = usePlanInfoStore();
// 组件存储集合
let actionsComponentMapObj = new Map();
// 组将相关数据保存模板
const componentSaveTemplaeObj = {
  element: null,
  instance: null,
  data: null,
  actionUuid: '',
  actionName: ''
};
/**
 * 清空组件map对象
 */
export const clearActionComponentMap = () => {
  if (actionsComponentMapObj) {
    actionsComponentMapObj.clear();
  } else {
    actionsComponentMapObj = new Map();
  }
};

/**
 * 添加动作组件 空数据或者默认数据时
 *
 * @param {object} options
 * @param {string} options.actionName 其实是动作标识 就是key
 * @param {string} options.actionUuid
 * @returns 返回VUE组件对象
 */
export const addNewActionToComponentMap = options => {
  const { actionName = '', actionUuid = 0, data = null } = options;
  const curActionName = ACTION_ACTUATOR_FUNC[actionName] || null;
  const componentObj = curActionName ? ActionComponentMap[curActionName] : null;
  if (!componentObj) {
    return null;
  }
  const { element, instance } = vueComponentToElement(componentObj, { data: data });

  // 构建保存对象
  // const obj = cloneDeep(componentSaveTemplaeObj);
  // obj.actionName = actionName;
  // obj.actionUuid = actionUuid;
  // obj.data = actionUdataid;
  // obj.element = element;
  // obj.instance = instance;
  // 利用对象解构和简化赋值的方式来构建保存对象obj
  const obj = {
    ...componentSaveTemplaeObj,
    actionName,
    actionUuid,
    data,
    element,
    instance
  };
  // 这里
  actionsComponentMapObj.set(actionUuid, obj);
  return obj;
};

/**
 * 根据动画id获取动画对象
 * @param {*} actionUuid 动作ID
 * @returns component vue组件对象
 */
export const getActionComponent = actionUuid => {
  if (!actionUuid) {
    return null;
  }
  return actionsComponentMapObj.get(actionUuid) || null;
};

/**
 * 设置组件数据
 * @param {object} options
 * @param {string} options.actionUuid
 * @param {object} options.actionFuncParam 对应组件的 动作参数 （每个动作都不同） 以及 设备类型
 * @param {object} options.action 对应 动作对象
 * @returns
 */
export const setActionComponentData = options => {
  // 获取数据
  const { actionUuid = null, actionFuncParam = null, action, actionGroup = null } = options;
  if (!actionUuid || !action || !actionFuncParam) {
    return;
  }
  const { instance } = getActionComponent(actionUuid);
  if (!instance) {
    return;
  }
  // 组件对外暴露方式 实现数据更新
  const droneInfo = planInfoStore.getCurDroneInfo() || null;
  // 统一的组件数据传入格式如下
  const optionsParms = {
    actionFuncParam: actionFuncParam,
    deviceInfo: droneInfo,
    action: action,
    actionGroup: actionGroup
  };
  if (instance && instance?.hasOwnProperty('setComponentData') && typeof instance.setComponentData === 'function') {
    instance?.setComponentData(optionsParms);
  }
};

//
export const getActionComponentData = options => {
  // 获取数据
  const { actionUuid = null } = options;
  if (!actionUuid) {
    return;
  }
  const { instance } = getActionComponent(actionUuid);
  if (!instance) {
    return;
  }
  // 获取组件数据方法
  const data = instance?.getComponentData() || null;
  return data;
};

// 组件元素动态的渲染到dom上
export const vueComponentToElement = (vueModel, data) => {
  const element = document.createElement('div');
  const app = createApp(vueModel, { data: data });
  const instance = app.mount(element);
  return { element, instance };
};
