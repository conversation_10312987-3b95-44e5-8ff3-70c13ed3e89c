import { createRouter, createWebHistory } from 'vue-router';
export const Layout = () => import('@/layout/index.vue');
export const homePageLayout = () => import('@/layout/homePage.vue');
export const backstageLayout = () => import('@/layout/backstagePage.vue');
export const homeViewLayout = () => import('@/layout/homeViewPage.vue');
export const noHeaderLayout = () => import('@/layout/noHeaderPage.vue');

// 静态路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    meta: { hidden: true },
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue')
      }
    ]
  },
  {
    path: '/homeView',
    component: homeViewLayout,
    meta: { hidden: true },
    children: [
      {
        path: '/homeView',
        component: () => import('@/views/homeView/index.vue'),
        name: 'homeView',
        meta: { hidden: true }
      }
    ]
  },
  {
    path: '/flight',
    component: backstageLayout,
    meta: { hidden: true },
    children: [
      {
        path: '/real-time-flight',
        component: () => import('@/views/homePage/component/realTimeFlight.vue'),
        name: 'RealTimeFlight',
        meta: { title: '实时飞行', keepAlive: true, hidden: true }
      },
      {
        path: '/pilot-real-time-flight',
        component: () => import('@/views/homeView/component/pilotRealTimeFlight.vue'),
        name: 'PilotRealTimeFlight',
        meta: { title: '飞手实时飞行', keepAlive: true, hidden: true }
      },
      {
        path: '401',
        component: () => import('@/views/error-page/401.vue'),
        meta: { hidden: true }
      },
      {
        path: '404',
        component: () => import('@/views/error-page/404.vue'),
        meta: { hidden: true }
      }
    ]
  },
  {
    path: '/',
    redirect: '/homePage',
    meta: { title: '首页', icon: 'home', hidden: true },
    component: homeViewLayout,
    children: [
      {
        path: 'homePage',
        component: () => import('@/views/homeView/index.vue'),
        name: 'home',
        meta: { hidden: true }
        // children:[]
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login/index.vue'),
    meta: { hidden: true }
  },
  {
    path: '/dashboard',
    component: homePageLayout,
    meta: { title: '仪表盘', icon: '' },
    authority: ['dashboard'],
    children: [
      {
        path: '/dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        name: 'dashboard',
        meta: { hidden: true }
      }
    ]
  },
  {
    path: '/plan-manage',
    name: 'plan-manage',
    component: homePageLayout,
    authority: ['plan', 'surface'],
    meta: { title: '航线管理', icon: '' },
    children: [
      {
        path: '/newplan',
        component: () => import('@/views/plan/newplan/index.vue'),
        name: 'newplan',
        meta: { title: '新建航线', keepAlive: true, hidden: true }
      },
      {
        path: '/plan',
        component: () => import('@/views/plan/airroute/index.vue'),
        name: 'plan',
        authority: ['plan'],
        meta: { title: '航线', keepAlive: true }
      },
      {
        path: '/surface',
        component: () => import('@/views/plan/surface/index.vue'),
        name: 'surface',
        authority: ['surface'],
        meta: { title: '航面', keepAlive: true, hidden: true }
      },
      // {
      //   path: '/worker',
      //   component: () => import('@/views/plan/worker/index.vue'),
      //   name: 'worker',
      //   meta: { title: '作业范围', keepAlive: true }
      // },
      // {
      //   path: '/nofly',
      //   component: () => import('@/views/plan/nofly/index.vue'),
      //   name: 'nofly',
      //   meta: { title: '禁飞区', keepAlive: true }
      // },
      {
        path: '/planinfo',
        component: () => import('@/views/plan/planInfo/index.vue'),
        name: 'planinfo',
        meta: { title: '航线信息', keepAlive: true, hidden: true }
      }
    ]
  },
  {
    path: '/task',
    name: 'task',
    authority: ['task', 'airport-task', 'jj-task'],
    component: homePageLayout,
    meta: { title: '任务管理', icon: '' },
    children: [
      {
        path: '/airport-task',
        component: () => import('@/views/task/airport-task/index.vue'),
        name: 'airport',
        authority: ['airport-task'],
        meta: { title: '机场任务', keepAlive: true }
      },
      {
        path: '/drone-pilot',
        component: () => import('@/views/task/drone-pilot/index.vue'),
        name: 'dronePilot',
        authority: ['drone-pilot'],
        meta: { title: '飞手任务', keepAlive: true }
      },
      {
        path: '/jj-task',
        component: () => import('@/views/task/jj-task/index.vue'),
        authority: ['jj-task'],
        name: 'jjtask',
        meta: { title: '警情列表', keepAlive: true }
      }
    ]
  },
  // {
  //   path: '/map',
  //   name: 'map',
  //   component: homePageLayout,
  //   authority: ['map','Realtimefly'],
  //   meta: { title: '飞行管理', icon: 'publish' },
  //   children: [
  //     {
  //       path: '/Realtimefly',
  //       // component: () => import('@/views/map/map-fly/index.vue'),
  //       component: () => import('@/views/map/map-fly-manager/realtime/index.vue'),
  //       name: 'Mapfly',
  //       authority: ['Realtimefly'],
  //       meta: { title: '实时飞行轨迹', keepAlive: true }
  //     },
  //     {
  //       path: '/mapfly-manager-history',
  //       component: () => import('@/views/map/map-fly-manager/history/index.vue'),
  //       name: 'Historyfly',
  //       meta: { title: '飞行历史记录', keepAlive: true, hidden: true }
  //     },
  //     {
  //       path: '/map-fly-history',
  //       component: () => import('@/views/map/history/index.vue'),
  //       name: 'MapFlyHistory',
  //       meta: { title: '飞行历史记录2', keepAlive: true, hidden: true }
  //     },
  //     {
  //       path: '/map-project',
  //       component: () => import('@/views/map/map-project/index.vue'),
  //       name: 'MapProject',
  //       meta: { title: '地图工程', keepAlive: true, hidden: true }
  //     }
  //   ]
  // },
  {
    path: '/videos',
    name: 'video',
    authority: ['video', 'live-stream'],
    component: homePageLayout,
    meta: { title: '视频管理', icon: '' },
    children: [
      {
        path: '/live-stream',
        authority: ['live-stream'],
        component: () => import('@/views/videos/live-stream/index.vue'),
        name: 'videos',
        meta: { title: '视频墙', keepAlive: true }
      }
    ]
  },
  {
    path: '/result-manage',
    name: 'result-manage',
    authority: ['result-manage', 'engineering-manage', 'media-library', 'model-library', 'one-map'],
    component: homePageLayout,
    meta: { title: '成果管理', icon: '' },
    children: [
      {
        path: '/media-library',
        component: () => import('@/views/result-manage/media-library/index.vue'),
        name: 'MediaLibrary',
        authority: ['media-library'],
        meta: { title: '媒体库', keepAlive: true }
      },
      {
        path: '/model-library',
        component: () => import('@/views/result-manage/model-library/index.vue'),
        authority: ['model-library'],
        name: 'ModelLibrary',
        meta: { title: '模型库', keepAlive: true }
      },
      {
        path: '/model-upload',
        component: () => import('@/views/result-manage/model-library/model-upload/index.vue'),
        meta: { title: '导入模型', keepAlive: true },
        name: 'ModelUpload'
      },
      {
        path: '/one-map',
        component: () => import('@/views/map/map-project/index.vue'),
        authority: ['one-map'],
        name: 'OneMap',
        meta: { title: '一张图', keepAlive: true }
      },
      {
        path: '/engineering-manage',
        authority: ['engineering-manage'],
        component: () => import('@/views/result-manage/engineering-manage/index.vue'),
        name: 'EngineeringManage',
        meta: { title: '工程管理', keepAlive: true }
      },
      {
        path: '/image-comparison',
        authority: ['image-comparison'],
        component: () => import('@/views/result-manage/image-comparison/index.vue'),
        name: 'ImageComparison',
        meta: { title: '图片对比', keepAlive: true }
      },
      {
        path: '/mapfly-manager-history',
        component: () => import('@/views/map/map-fly-manager/history/index.vue'),
        name: 'Historyfly',
        meta: { title: '飞行历史记录', keepAlive: true, hidden: true }
      },
      {
        path: '/map-fly-history',
        component: () => import('@/views/map/history/index.vue'),
        name: 'MapFlyHistory',
        meta: { title: '飞行历史记录2', keepAlive: true, hidden: true }
      },
      {
        path: '/map-project',
        component: () => import('@/views/map/map-project/index.vue'),
        name: 'MapProject',
        meta: { title: '地图工程', keepAlive: true, hidden: true }
      }
    ]
  },
  {
    path: '/device',
    name: 'device',
    authority: ['device', 'airport-manage', 'uav-manage', 'load-manage', 'battery-management', 'order-management'],
    component: homePageLayout,
    meta: { title: '设备管理', icon: '' },
    children: [
      {
        path: '/uav-manage',
        authority: ['uav-manage'],
        component: () => import('@/views/device/uav/index.vue'),
        name: 'uav',
        meta: { title: '无人机管理', keepAlive: true }
      },
      {
        path: '/airport-manage',
        authority: ['airport-manage'],
        component: () => import('@/views/device/airport/index.vue'),
        name: 'airport-manage',
        meta: { title: '机场管理', keepAlive: true }
      },
      {
        path: '/load-manage',
        authority: ['load-manage'],
        component: () => import('@/views/device/load-management/index.vue'),
        name: 'load-manage',
        meta: { title: '负载管理', keepAlive: true }
      },
      {
        path: '/battery-management',
        authority: ['battery-management'],
        component: () => import('@/views/device/battery-management/index.vue'),
        name: 'battery',
        meta: { title: '电池管理', keepAlive: true }
      },
      {
        path: '/order-management',
        authority: ['order-management'],
        component: () => import('@/views/device/order-management/index.vue'),
        name: 'order',
        meta: { title: '保单管理', keepAlive: true }
      },
      {
        path: '/maintenance',
        authority: ['maintenance'],
        component: () => import('@/views/device/maintenance/index.vue'),
        name: 'maintenance',
        meta: { title: '维保管理', keepAlive: true }
      },
      {
        path: '/scheme',
        component: () => import('@/views/device/maintenance/scheme.vue'),
        name: 'scheme',
        meta: { title: '维保方案管理', keepAlive: true, hidden: true }
      },
      {
        path: '/maintenanceTask',
        component: () => import('@/views/device/maintenance/maintenanceTask.vue'),
        name: 'maintenanceTask',
        meta: { title: '维保任务管理', keepAlive: true, hidden: true }
      }
      // {
      //   path: '/remote-control',
      //   component: () => import('@/views/device/remote-control/index.vue'),
      //   name: 'remote-control',
      //   meta: { title: '遥控器', keepAlive: true }
      // },
      // {
      //   path: '/camera',
      //   component: () => import('@/views/device/camera/index.vue'),
      //   name: 'camera',
      //   meta: { title: '相机', keepAlive: true }
      // }
    ]
  },
  {
    path: '/history-record',
    name: 'HistoryRecord',
    authority: ['history-record', 'flight-record'],
    component: homePageLayout,
    meta: { title: '历史记录', icon: '' },
    children: [
      {
        path: '/flight-record',
        authority: ['flight-record'],
        component: () => import('@/views/history-record/flight-record/index.vue'),
        name: 'flight-record',
        meta: { title: '飞行记录', keepAlive: true }
      }
    ]
  },
  {
    path: '/pilot-login',
    component: () => import('@/views/device/pilot-login/index.vue'),
    meta: { hidden: true }
  },
  {
    path: '/h5-video',
    component: () => import('@/views/H5/h5Video.vue'),
    meta: { hidden: true }
  },
  {
    path: '/pilot-media',
    component: () => import('@/views/device/pilot-login/components/pilot-media.vue'),
    meta: { hidden: true }
  },
  {
    path: '/pilot-home',
    component: () => import('@/views/device/pilot-login/components/pilot-home.vue'),
    meta: { hidden: true }
  },
  {
    path: '/pilot-bind',
    component: () => import('@/views/device/pilot-login/components/pilot-bind.vue'),
    meta: { hidden: true }
  },
  {
    path: '/pilot-liveshare',
    component: () => import('@/views/device/pilot-login/components/pilot-liveshare.vue'),
    meta: { hidden: true }
  },
  {
    path: '/flight-manage',
    name: 'flight-manage',
    authority: ['flight-manage', 'flight-area', 'fence-manage', 'layer-manage', 'feature-manage'],
    component: homePageLayout,
    meta: { title: '地图数据管理', icon: '' },
    children: [
      {
        path: '/flight-area',
        name: 'flight-area',
        authority: ['flight-area'],
        component: () => import('@/views/flight-manage/flight-area/index.vue'),
        meta: { title: '自定义飞行区域', keepAlive: true }
      },
      {
        path: '/fence-manage',
        name: 'fence-manage',
        authority: ['fence-manage'],
        component: () => import('@/views/fenceManage/index.vue'),
        meta: { title: '围栏管理', keepAlive: true }
      },
      {
        path: '/layer-manage',
        name: 'layer-manage',
        authority: ['layer-manage'],
        component: () => import('@/views/layerManage/index.vue'),
        meta: { title: '图层管理', keepAlive: true }
      },
      {
        path: '/feature-manage',
        name: 'feature-manage',
        authority: ['feature-manage'],
        component: () => import('@/views/featureManage/index.vue'),
        meta: { title: '图元管理', keepAlive: true }
      },
      {
        path: '/feature-edit',
        name: 'feature-edit',
        authority: ['feature-manage'],
        component: () => import('@/views/featureManage/editIndex.vue'),
        meta: { title: '图元维护', keepAlive: true, hidden: true }
      },
      {
        path: '/fence-edit',
        name: 'fence-edit',
        authority: ['fence-edit'],
        component: () => import('@/views/fenceManage/editIndex.vue'),
        meta: { title: '围栏维护', keepAlive: true }
      }
    ]
  },
  {
    path: '/setting',
    name: 'setting',
    authority: ['setting', 'dept', 'user', 'role', 'personalized-setting'],
    meta: { title: '系统管理', icon: '' },
    component: homePageLayout,
    children: [
      {
        path: '/user',
        authority: ['user'],
        component: () => import('@/views/setting/user/index.vue'),
        name: 'user',
        meta: { title: '用户管理', keepAlive: true }
      },
      {
        path: '/role',
        authority: ['role'],
        component: () => import('@/views/setting/role/index.vue'),
        name: 'role',
        meta: { title: '角色管理', keepAlive: true }
      },
      {
        path: '/dept',
        authority: ['dept'],
        component: () => import('@/views/setting/dept/index.vue'),
        name: 'dept',
        meta: { title: '组织管理', keepAlive: true }
      },
      {
        path: '/personalized-setting',
        authority: ['personalized-setting'],
        component: () => import('@/views/setting/personal/index.vue'),
        name: 'personalizedSetting',
        meta: { title: '个性化设置', keepAlive: true }
      },
      {
        path: '/gb-device',
        authority: ['admin'],
        component: () => import('@/views/gbSetting/gbDevice/index.vue'),
        name: 'gbDevice',
        meta: { title: '国标配置', keepAlive: true }
      },
      {
        path: '/dict-manage',
        authority: ['admin'],
        component: () => import('@/views/dictManage/index.vue'),
        name: 'dictManage',
        meta: { title: '字典管理', keepAlive: true }
      }
    ]
  },
  {
    path: '/work-order',
    name: 'workOrder',
    authority: ['work-order', 'aiManager', 'alarmManager', 'orderManager'],
    meta: { title: '业务管理', icon: '' },
    component: homePageLayout,
    children: [
      {
        path: '/alarmManager',
        authority: ['alarmManager'],
        component: () => import('@/views/work-order/alarmManager/index.vue'),
        name: 'alarmManager',
        meta: { title: '预警管理', keepAlive: true }
      },
      {
        path: '/orderManager',
        authority: ['orderManager'],
        component: () => import('@/views/work-order/orderManager/index.vue'),
        name: 'orderManager',
        meta: { title: '工单管理', keepAlive: true }
      },
      {
        path: '/aiManager',
        authority: ['aiManager'],
        component: () => import('@/views/work-order/aiManager/index.vue'),
        name: 'aiManager',
        meta: { title: 'AI事件管理', keepAlive: true }
      }
    ]
  },
  {
    path: '/',
    component: backstageLayout,
    meta: { hidden: true },
    children: [
      {
        path: 'mapfly-manager-history-no-header',
        component: () => import('@/views/map/map-fly-manager/history/index.vue'),
        name: 'NoHeaderHistory',
        meta: { title: '飞行历史记录', keepAlive: true, hidden: true }
      },
      {
        path: 'surface-no-header',
        component: () => import('@/views/plan/surface/index.vue'),
        name: 'NoHeadersurface',
        authority: ['surface'],
        meta: { title: '航面', keepAlive: true, hidden: true }
      },
      {
        path: 'planinfo-no-header',
        component: () => import('@/views/plan/planInfo/index.vue'),
        name: 'NoHeaderplaninfo',
        meta: { title: '航线信息', keepAlive: true, hidden: true }
      },
      {
        path: 'newplan-no-header',
        component: () => import('@/views/plan/newplan/index.vue'),
        name: 'NoHeadernewplan',
        meta: { title: '新建航线', keepAlive: true, hidden: true }
      }
    ]
  }
];

export const pilotRoutes = [
  {
    path: '/pilot-login',
    component: () => import('@/views/device/pilot-login/index.vue'),
    meta: { hidden: true }
  },
  {
    path: '/pilot-media',
    component: () => import('@/views/device/pilot-login/components/pilot-media.vue'),
    meta: { hidden: true }
  },
  {
    path: '/pilot-home',
    component: () => import('@/views/device/pilot-login/components/pilot-home.vue'),
    meta: { hidden: true }
  },
  {
    path: '/pilot-bind',
    component: () => import('@/views/device/pilot-login/components/pilot-bind.vue'),
    meta: { hidden: true }
  },
  {
    path: '/pilot-liveshare',
    component: () => import('@/views/device/pilot-login/components/pilot-liveshare.vue'),
    meta: { hidden: true }
  }
];
/**
 * 创建路由
 */
const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  // routes: pilotRoutes,
  // 刷新时，滚动条位置还原
  scrollBehavior: () => ({ left: 0, top: 0 })
});

/**
 * 重置路由
 */
export function resetRouter() {
  router.replace({ path: '/login' });
  window.location.reload();
}

export default router;
