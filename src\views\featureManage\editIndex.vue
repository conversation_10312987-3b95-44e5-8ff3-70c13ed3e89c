<template>
  <div class="feature-edit-container">
    <!-- 全局加载遮罩 -->
    <div v-if="pageLoading" class="page-loading-mask">
      <div class="loading-spinner">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <div class="loading-text">加载中...</div>
      </div>
    </div>

    <!-- 加载错误提示 -->
    <div v-if="loadError" class="load-error-mask">
      <div class="error-container">
        <el-icon class="error-icon"><WarningFilled /></el-icon>
        <div class="error-title">加载失败</div>
        <div class="error-message">{{ errorMessage }}</div>
        <el-button type="primary" @click="handleRetry">重试</el-button>
      </div>
    </div>

    <div id="cesium-container"></div>
    <div class="left-wrap">
      <div class="left-content-panel bg-dark-blue">
        <div class="panel-header">
          <div class="title">{{ pageTitle }}</div>
          <div class="actions">
            <el-button v-if="!isDetail" type="primary" @click="handleSubmit" :loading="submitLoading">保存</el-button>
            <el-button @click="handleCancel">返回</el-button>
          </div>
        </div>
        <div class="panel-content">
          <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-width="100px"
            v-loading="loading"
            :disabled="isDetail"
            :class="{ 'detail-form': isDetail }"
          >
            <el-form-item prop="featureName" label="图元名称">
              <el-input v-model="form.featureName" placeholder="请输入图元名称" maxlength="16" show-word-limit />
            </el-form-item>

            <el-form-item prop="layerId" label="所属图层">
              <el-select
                v-model="form.layerId"
                placeholder="请选择图层"
                style="width: 100%"
                @change="handleLayerChange"
              >
                <el-option v-for="item in layerOptions" :key="item.id" :label="item.layerName" :value="item.id" />
              </el-select>
            </el-form-item>

            <el-form-item prop="featureAddr" label="图元地址">
              <el-input v-model="form.featureAddr" placeholder="请输入图元地址" maxlength="64" show-word-limit />
            </el-form-item>

            <el-form-item prop="contact" label="联系人">
              <el-input v-model="form.contact" placeholder="请输入联系人" maxlength="16" show-word-limit />
            </el-form-item>

            <el-form-item prop="telephone" label="联系电话">
              <el-input v-model="form.telephone" placeholder="请输入联系电话" maxlength="20" />
            </el-form-item>

            <el-form-item prop="remark" label="备注">
              <el-input
                v-model="form.remark"
                placeholder="请输入备注"
                type="textarea"
                maxlength="200"
                class="coords-textarea"
                show-word-limit
              />
            </el-form-item>

            <!-- 中心经纬度显示 -->
            <el-form-item label="中心经度">
              <el-input :value="form.longitude || ''" placeholder="请在右侧地图上选择或绘制图形" disabled />
            </el-form-item>

            <el-form-item label="中心纬度">
              <el-input :value="form.latitude || ''" placeholder="请在右侧地图上选择或绘制图形" disabled />
            </el-form-item>

            <!-- 坐标集合显示（仅非点图元） -->
            <el-form-item v-if="currentDrawType !== 'point'" label="坐标集合">
              <el-input
                v-model="form.featureCoords"
                placeholder="请在右侧地图上绘制图形"
                type="textarea"
                :rows="4"
                disabled
                class="coords-textarea"
              />
            </el-form-item>

            <!-- 图元统计信息 -->
            <el-form-item v-if="currentFeatureData.area > 0 || currentFeatureData.length > 0" label="图元统计">
              <div class="feature-info">
                <div v-if="currentFeatureData.area > 0" class="info-item">
                  <span class="label">面积:</span>
                  <span class="value">{{ formatArea(currentFeatureData.area) }}</span>
                </div>
                <div v-if="currentFeatureData.length > 0" class="info-item">
                  <span class="label">长度:</span>
                  <span class="value">{{ formatLength(currentFeatureData.length) }}</span>
                </div>
              </div>
            </el-form-item>

            <!-- 扩展属性 -->
            <el-form-item label="扩展属性">
              <div class="extra-info-container full-width">
                <div class="extra-info-header">
                  <el-button v-if="!isDetail" type="primary" size="small" @click="addExtraInfoItem">
                    添加属性
                  </el-button>
                </div>
              </div>
            </el-form-item>
            <!-- <el-form-item > -->
            <div class="extra-info-container">
              <div v-if="extraInfoList.length === 0" class="empty-extra-info">暂无扩展属性</div>
              <el-table v-else :data="extraInfoList" border size="small" class="extra-info-table">
                <el-table-column prop="index" label="序号" width="60" align="center">
                  <template #default="{ $index }">
                    {{ $index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column prop="attributeName" label="属性名称" width="140">
                  <template #default="{ row }">
                    <el-input
                      v-if="!isDetail"
                      v-model="row.attributeName"
                      placeholder="请输入属性名称"
                      size="small"
                      maxlength="16" 
                      @input="updateExtraInfo"
                    />
                    <span v-else>{{ row.attributeName }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="attributeValue" label="属性值" width="140">
                  <template #default="{ row }">
                    <el-input
                      v-if="!isDetail"
                      v-model="row.attributeValue"
                      placeholder="请输入属性值"
                      size="small"
                      maxlength="64" 
                      @input="updateExtraInfo"
                    />
                    <span v-else>{{ row.attributeValue }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="attributeDesc" label="属性说明" min-width="120">
                  <template #default="{ row }">
                    <el-input
                      v-if="!isDetail"
                      v-model="row.attributeDesc"
                      placeholder="请输入属性说明"
                      size="small"
                      maxlength="64" 
                      @input="updateExtraInfo"
                    />
                    <span v-else>{{ row.attributeDesc }}</span>
                  </template>
                </el-table-column>
                <el-table-column v-if="!isDetail" label="操作" width="80" align="center">
                  <template #default="{ $index }">
                    <el-button type="danger" size="small" @click="removeExtraInfoItem($index)" :icon="Delete" />
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <!-- </el-form-item> -->
          </el-form>
        </div>
      </div>
    </div>

    <!-- 绘制工具栏 -->
    <div class="toolbar" v-if="!isDetail">
      <div v-if="currentDrawType === 'point'" class="tool-item" @click="handleDrawPoint">
        <el-tooltip content="点击选择位置" placement="left">
          <div class="tool-icon point-icon"></div>
        </el-tooltip>
      </div>
      <div v-if="currentDrawType === 'line'" class="tool-item" @click="handleDrawLine">
        <el-tooltip content="绘制线条" placement="left">
          <div class="tool-icon line-icon"></div>
        </el-tooltip>
      </div>
      <div v-if="currentDrawType === 'polygon'" class="tool-item" @click="handleDrawPolygon">
        <el-tooltip content="绘制多边形" placement="left">
          <div class="tool-icon polygon-icon"></div>
        </el-tooltip>
      </div>
      <div class="tool-item" @click="handleClearMap">
        <el-tooltip content="清除绘制" placement="left">
          <el-icon class="tool-delete-icon"><Delete /></el-icon>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Delete, Loading, WarningFilled } from '@element-plus/icons-vue';
import { getFeatureDetail, createFeature, updateFeature } from '@/api/map/feature';
import { getAllLayers } from '@/api/map/layer';
import {
  initMap,
  startDrawPoint,
  startDrawLine,
  startDrawPolygon,
  clearMap,
  drawExistingFeature,
  destroyMap,
  currentFeatureData
} from './featureHandle';

const route = useRoute();
const router = useRouter();
const formRef = ref(null);
const loading = ref(false);
const submitLoading = ref(false);
const pageLoading = ref(true);
const mapLoaded = ref(false);
const dataLoaded = ref(true);
const loadError = ref(false);
const errorMessage = ref('');
const layerOptions = ref([]);
const currentDrawType = ref('point'); // 当前绘制类型
const extraInfoList = ref([]); // 扩展属性列表

// 判断页面模式
const isAdd = computed(() => route.query.mode === 'add');
const isDetail = computed(() => route.query.mode === 'detail');

// 页面标题
const pageTitle = computed(() => {
  if (isAdd.value) return '新增图元';
  if (isDetail.value) return '图元详情';
  return '编辑图元';
});

// 监听地图和数据加载状态
watch(
  [mapLoaded, dataLoaded],
  ([mapLoadedVal, dataLoadedVal]) => {
    if (mapLoadedVal && dataLoadedVal) {
      setTimeout(() => {
        pageLoading.value = false;
      }, 300);
    }
  },
  { immediate: true }
);

// 表单数据
const form = reactive({
  id: undefined,
  featureName: '',
  layerId: null,
  featureAddr: '',
  longitude: null,
  latitude: null,
  contact: '',
  telephone: '',
  featureCoords: '',
  extraInfo: '',
  remark: ''
});

// 表单验证规则
const rules = reactive({
  featureName: [{ required: true, message: '图元名称不能为空', trigger: 'blur' }],
  layerId: [{ required: true, message: '请选择所属图层', trigger: 'change' }],
  telephone: [
    {
      validator: (_, value, callback) => {
        if (!value || value.trim() === '') {
          callback();
          return;
        }

        // 手机号码正则：1开头，第二位为3-9，总共11位数字
        const mobileRegex = /^1[3-9]\d{9}$/;
        // 固定电话正则：区号-号码 或 区号号码，支持分机号
        const landlineRegex = /^(0\d{2,3}-?\d{7,8}(-\d{1,6})?)$/;

        if (!mobileRegex.test(value) && !landlineRegex.test(value)) {
          callback(new Error('请输入正确的手机号码或固定电话'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
});

/**
 * 字段名转换：下划线转驼峰
 */
function toCamelCase(obj) {
  if (Array.isArray(obj)) {
    return obj.map(item => toCamelCase(item));
  } else if (obj !== null && typeof obj === 'object') {
    const newObj = {};
    Object.keys(obj).forEach(key => {
      const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
      newObj[camelKey] = toCamelCase(obj[key]);
    });
    return newObj;
  }
  return obj;
}

/**
 * 字段名转换：驼峰转下划线
 */
function toSnakeCase(obj) {
  if (Array.isArray(obj)) {
    return obj.map(item => toSnakeCase(item));
  } else if (obj !== null && typeof obj === 'object') {
    const newObj = {};
    Object.keys(obj).forEach(key => {
      const snakeKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
      newObj[snakeKey] = toSnakeCase(obj[key]);
    });
    return newObj;
  }
  return obj;
}

/**
 * 计算坐标数组的质心
 * @param {Array} coordinates 坐标数组 [[lng, lat], [lng, lat], ...]
 * @returns {Object} {longitude, latitude}
 */
function calculateCentroid(coordinates) {
  if (!coordinates || coordinates.length === 0) {
    return { longitude: null, latitude: null };
  }

  if (coordinates.length === 1) {
    return { longitude: coordinates[0][0], latitude: coordinates[0][1] };
  }

  let totalLng = 0;
  let totalLat = 0;

  coordinates.forEach(coord => {
    totalLng += coord[0];
    totalLat += coord[1];
  });

  return {
    longitude: totalLng / coordinates.length,
    latitude: totalLat / coordinates.length
  };
}

/**
 * 获取图层选项
 */
async function loadLayerOptions() {
  try {
    const result = await getAllLayers();
    if (result && result.data && Array.isArray(result.data)) {
      layerOptions.value = toCamelCase(result.data);
    } else if (result && Array.isArray(result)) {
      layerOptions.value = toCamelCase(result);
    }
  } catch (error) {
    console.error('获取图层选项失败:', error);
  }
}

/**
 * 图层变化处理
 */
function handleLayerChange(layerId) {
  const selectedLayer = layerOptions.value.find(layer => layer.id === layerId);
  if (selectedLayer) {
    // 根据图层类型设置绘制类型
    switch (selectedLayer.layerType) {
      case 'point':
        currentDrawType.value = 'point';
        break;
      case 'line':
        currentDrawType.value = 'line';
        break;
      case 'polygon':
        currentDrawType.value = 'polygon';
        break;
      default:
        currentDrawType.value = 'point';
    }
    // 清除之前的绘制
    clearMap();
  }
}

/**
 * 添加扩展属性项
 */
function addExtraInfoItem() {
  extraInfoList.value.push({
    attributeName: '',
    attributeValue: '',
    attributeDesc: ''
  });
  updateExtraInfo();
}

/**
 * 删除扩展属性项
 */
function removeExtraInfoItem(index) {
  extraInfoList.value.splice(index, 1);
  updateExtraInfo();
}

/**
 * 更新扩展属性JSON
 */
function updateExtraInfo() {
  // 过滤掉空的属性项
  const validItems = extraInfoList.value.filter(item => item.attributeName && item.attributeName.trim() !== '');
  form.extraInfo = JSON.stringify(validItems);
}

/**
 * 解析扩展属性JSON
 */
function parseExtraInfo(extraInfoStr) {
  if (!extraInfoStr) {
    extraInfoList.value = [];
    return;
  }

  try {
    const parsed = JSON.parse(extraInfoStr);
    if (Array.isArray(parsed)) {
      extraInfoList.value = parsed.map(item => ({
        attributeName: item.attributeName || '',
        attributeValue: item.attributeValue || '',
        attributeDesc: item.attributeDesc || ''
      }));
    } else {
      extraInfoList.value = [];
    }
  } catch (error) {
    console.warn('解析扩展属性失败:', error);
    extraInfoList.value = [];
  }
}

// 监听当前图元数据变化，更新表单
watch(
  () => currentFeatureData.coordinates,
  newVal => {
    if (newVal && newVal.length > 0) {
      if (currentDrawType.value === 'point' && newVal.length >= 1) {
        form.longitude = newVal[0][0];
        form.latitude = newVal[0][1];
      } else {
        form.featureCoords = JSON.stringify(newVal);
      }
    }
  },
  { deep: true }
);

// 格式化面积
function formatArea(area) {
  if (!area || area <= 0) return '0 m²';
  if (area < 10000) {
    return `${area.toFixed(2)} m²`;
  } else {
    return `${(area / 10000).toFixed(2)} km²`;
  }
}

// 格式化长度
function formatLength(length) {
  if (!length || length <= 0) return '0 m';
  if (length < 1000) {
    return `${length.toFixed(2)} m`;
  } else {
    return `${(length / 1000).toFixed(2)} km`;
  }
}

// 绘制点
function handleDrawPoint() {
  startDrawPoint(data => {
    if (data && data.coordinates && data.coordinates.length > 0) {
      form.longitude = data.coordinates[0][0];
      form.latitude = data.coordinates[0][1];
    }
  });
}

// 绘制线
function handleDrawLine() {
  startDrawLine(data => {
    if (data && data.coordinates) {
      form.featureCoords = JSON.stringify(data.coordinates);
      currentFeatureData.length = data.length || 0;
    }
  });
}

// 绘制多边形
function handleDrawPolygon() {
  startDrawPolygon(data => {
    if (data && data.coordinates) {
      form.featureCoords = JSON.stringify(data.coordinates);
      currentFeatureData.area = data.area || 0;
      currentFeatureData.length = data.perimeter || 0;
    }
  });
}

// 清除地图
function handleClearMap() {
  clearMap();
  form.longitude = null;
  form.latitude = null;
  form.featureCoords = '';
  currentFeatureData.area = 0;
  currentFeatureData.length = 0;
}

// 重试加载
async function handleRetry() {
  loadError.value = false;
  pageLoading.value = true;
  mapLoaded.value = false;
  dataLoaded.value = !isAdd.value && route.query.id;

  try {
    await initMap();
    mapLoaded.value = true;

    if (!isAdd.value && route.query.id) {
      loadFeatureData();
    }
  } catch (error) {
    handleLoadError('地图初始化失败，请检查网络连接后重试');
  }
}

// 处理加载错误
function handleLoadError(message) {
  pageLoading.value = false;
  loadError.value = true;
  errorMessage.value = message || '加载失败，请稍后重试';
}

// 加载图元数据
async function loadFeatureData() {
  if (!isAdd.value && route.query.id) {
    loading.value = true;
    dataLoaded.value = false;
    try {
      const res = await getFeatureDetail(route.query.id);
      if (res) {
        // 转换字段名
        const camelData = toCamelCase(res);
        Object.assign(form, camelData);

        // 解析扩展属性
        parseExtraInfo(form.extraInfo);

        // 设置绘制类型
        const selectedLayer = layerOptions.value.find(layer => layer.id === form.layerId);
        if (selectedLayer) {
          handleLayerChange(form.layerId);
        }

        // 如果有坐标数据，在地图上绘制
        if (form.featureCoords) {
          try {
            const coords = JSON.parse(form.featureCoords);
            const drawType = selectedLayer?.layerType || 'point';

            if (drawType === 'point') {
              // 点图元：直接使用坐标
              form.longitude = coords[0][0];
              form.latitude = coords[0][1];
              drawExistingFeature(coords, 'point');
            } else {
              // 线或面图元：计算并显示质心坐标
              const centroid = calculateCentroid(coords);
              form.longitude = centroid.longitude;
              form.latitude = centroid.latitude;
              drawExistingFeature(coords, drawType);
            }
          } catch (error) {
            console.error('解析坐标数据失败:', error);
          }
        } 
      }
    } catch (error) {
      console.error('获取图元详情失败:', error);
      handleLoadError('获取图元详情失败，请稍后重试');
    } finally {
      loading.value = false;
      dataLoaded.value = true;
    }
  }
}

// 提交表单
async function handleSubmit() {
  try {
    await formRef.value.validate();

    // 验证坐标数据
    if (currentDrawType.value === 'point') {
      if (!form.longitude || !form.latitude) {
        ElMessage.error('请在地图上选择图元位置');
        return;
      }
    } else {
      if (!form.featureCoords) {
        ElMessage.error('请在地图上绘制图元');
        return;
      }
    }

    submitLoading.value = true;

    // 构造请求参数
    const params = toSnakeCase({ ...form });

    // 确保坐标数据格式正确
    if (currentDrawType.value === 'point') {
      params.longitude = form.longitude;
      params.latitude = form.latitude;
      params.feature_coords = JSON.stringify([[form.longitude, form.latitude]]);
    } else {
      // 线、面图元：使用质心坐标作为longitude、latitude
      try {
        const coordinates = JSON.parse(form.featureCoords);
        const centroid = calculateCentroid(coordinates);

        params.longitude = centroid.longitude;
        params.latitude = centroid.latitude;
        params.feature_coords = form.featureCoords;
      } catch (error) {
        ElMessage.error('坐标数据格式错误');
        return;
      }
    }

    const request = form.id ? updateFeature : createFeature;
    await request(params);
    ElMessage.success('保存成功');
    router.push('/feature-manage');
  } catch (error) {
    console.error('保存失败', error);
    ElMessage.error('保存失败');
  } finally {
    submitLoading.value = false;
  }
}

// 取消编辑
function handleCancel() {
  router.push('/feature-manage');
}

// 生命周期钩子
onMounted(async () => {
  // 加载图层选项
  await loadLayerOptions();

  // 初始化地图
  try {
    await initMap();
    mapLoaded.value = true;
  } catch (error) {
    console.error('地图初始化失败', error);
    handleLoadError('地图初始化失败，请检查网络连接后重试');
  }

  // 加载图元数据
  loadFeatureData();
});

onUnmounted(() => {
  destroyMap();
});
</script>

<style lang="scss" scoped>
.feature-edit-container {
  width: 100%;
  height: 100%;
  background-color: #171717;
  user-select: none;
  position: relative;
}

/* 复用围栏管理的样式 */
.page-loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 17, 41, 0.8);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;

  .loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .loading-icon {
    font-size: 40px;
    color: #fff;
    margin-bottom: 10px;
    animation: rotate-loading 1.5s linear infinite;
  }

  .loading-text {
    font-size: 16px;
    color: #fff;
    font-weight: 500;
    letter-spacing: 1px;
  }
}

.load-error-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 17, 41, 0.9);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;

  .error-container {
    background-color: #001129;
    border-radius: 8px;
    padding: 30px;
    width: 400px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
    border: 1px solid #1a2c47;
  }

  .error-icon {
    font-size: 48px;
    color: #ff4d4f;
    margin-bottom: 16px;
  }

  .error-title {
    font-size: 20px;
    color: #fff;
    font-weight: 600;
    margin-bottom: 12px;
  }

  .error-message {
    font-size: 14px;
    color: #a5b7d1;
    margin-bottom: 24px;
    line-height: 1.5;
  }
}

@keyframes rotate-loading {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}

#cesium-container {
  width: 100%;
  height: 100%;
  background-color: #171717;
  z-index: 1;
  position: absolute;
  left: 0px;
  top: 0px;
  right: 0px;
  bottom: 0px;
}

.left-wrap {
  width: 400px;
  height: 100%;
  position: absolute;
  left: 0px;
  transition: left 0.3s ease-in-out;
  top: 0px;
  z-index: 999;
}

.left-content-panel {
  width: 100%;
  height: 100%;
  background-color: #001129;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
}

.panel-header {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #1a2c47;
}

.panel-header .title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.toolbar {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 48px;
  background-color: rgba(0, 17, 41, 0.8);
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
  z-index: 999;
}

.tool-item {
  width: 40px;
  height: 40px;
  margin: 10px auto;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s;
}

.tool-item:hover {
  background-color: rgba(46, 144, 250, 0.3);
}

.tool-icon {
  width: 24px;
  height: 24px;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.tool-delete-icon {
  font-size: 20px;
  color: #fff;
}

.point-icon {
  position: relative;
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #fff;
    border: 2px solid #2e90fa;
  }
}

.line-icon {
  position: relative;
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 20%;
    right: 20%;
    height: 2px;
    background-color: #fff;
    transform: translateY(-50%);
  }
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 20%;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: #2e90fa;
    transform: translate(-50%, -50%);
  }
}

.polygon-icon {
  background-image: url('@/assets/flightArea/svg/polygon.svg');
}

.coordinate-info {
  .coordinate-display {
    padding: 8px 12px;
    background-color: #1a2c47;
    border-radius: 4px;
    color: #2e90fa;
    border: 1px solid #2c4a6a;
  }

  .coordinate-display.empty {
    color: #98a2b3;
    font-style: italic;
  }
}

.feature-info {
  width: 100%;
  margin-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
  background-color: rgba(21, 41, 66, 0.5);
  border-radius: 4px;
  padding: 10px;
  border: 1px solid #2c4a6a;
}

.info-item {
  width: 50%;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.info-item .label {
  color: #98a2b3;
  margin-right: 8px;
}

.info-item .value {
  color: #2e90fa;
  font-weight: 500;
}

.bg-dark-blue {
  background-color: #001129;
}

/* 扩展属性样式 */
.extra-info-container {
  width: 100%;

  &.full-width {
    margin-left: -100px; /* 抵消label-width的偏移 */
    width: calc(100% + 100px); /* 撑满整行 */
  }
}

.extra-info-header {
  margin-bottom: 12px;
  display: flex;
  justify-content: flex-end;
}

.empty-extra-info {
  text-align: center;
  color: #98a2b3;
  padding: 20px;
  font-style: italic;
  background-color: rgba(21, 41, 66, 0.3);
  border-radius: 4px;
  border: 1px dashed #2c4a6a;
}

.extra-info-table {
  width: 100%;

  :deep(.el-table) {
    background-color: transparent;
    color: #fff;
  }

  :deep(.el-table__header) {
    background-color: #1a2c47;
  }

  :deep(.el-table__header th) {
    background-color: #1a2c47;
    color: #2e90fa;
    border-bottom: 1px solid #2c4a6a;
  }

  :deep(.el-table__body tr) {
    background-color: rgba(21, 41, 66, 0.5);
  }

  :deep(.el-table__body tr:hover) {
    background-color: rgba(21, 41, 66, 0.8);
  }

  :deep(.el-table__body td) {
    border-bottom: 1px solid #2c4a6a;
    color: #fff;
  }

  :deep(.el-table__border) {
    border-color: #2c4a6a;
  }

  :deep(.el-table__border::after) {
    background-color: #2c4a6a;
  }

  :deep(.el-table__border::before) {
    background-color: #2c4a6a;
  }

  :deep(.el-input__inner) {
    background-color: #1a2c47;
    border-color: #2c4a6a;
    color: #fff;

    &::placeholder {
      color: #98a2b3;
    }

    &:focus {
      border-color: #2e90fa;
    }
  }

  :deep(.el-button--danger) {
    background-color: #f56c6c;
    border-color: #f56c6c;

    &:hover {
      background-color: #f78989;
      border-color: #f78989;
    }
  }
}

/* 坐标集合textarea样式 */
.coords-textarea {
  :deep(.el-textarea__inner) {
    &::-webkit-scrollbar {
      width: 8px !important;
    }

    &::-webkit-scrollbar-track {
      background-color: #001129;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 2px;
      width: 8px !important;
      background: #175192 !important;
    }
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px !important;
}

::-webkit-scrollbar-track {
  background-color: #001129;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb {
  border-radius: 2px;
  width: 8px !important;
  background: #175192 !important;
}
</style>
