<template>
  <el-dialog title="任务详情" v-if="visible" :model-value="visible" align-center :close-on-click-modal="false" @close="closeDialog">
    <div class="left-content">
      <div class="line-left">
        <el-descriptions label-align="right" :column="2">
          <el-descriptions-item width="500" label="任务名称：" label-class-name="label-class">
            <span :title="formData.name" class="ellipsis" style="display: inline-block;width: 300px;transform: translateY(8px);">{{formData.name}}</span>
          </el-descriptions-item>
          <el-descriptions-item label="任务执行方式：" label-class-name="label-class">
            <span>{{ optionData.typeOptions.find(item => item.value === formData.task_type)?.label }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="执行机场：" label-class-name="label-class">
            <span class="ellipsis" style="display: inline-block;width: 300px;transform: translateY(8px);" :title="formData.dock_sn_desc">{{ formData.dock_sn_desc }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="计划执行时间：" label-class-name="label-class">
            {{ formData.execute_time }}
          </el-descriptions-item>
          <el-descriptions-item label="返航高度：" label-class-name="label-class">
            {{ formData.rth_altitude }}m
          </el-descriptions-item>
          <el-descriptions-item label="航线失控动作：" label-class-name="label-class">
            <span>{{
              optionData.outofControlActionList.find(item => item.value === formData.out_of_control)?.label
            }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="航线名称：" label-class-name="label-class" v-if="authorityShow('planShow')">
            <el-link style="color: #409eff">
              <span @click="openPlanInfo(formData.wayline_id)"  class="ellipsis" style="max-width: 180px;display: inline-block;" :title="formData.wayline_file_name"> {{ formData.wayline_file_name }} </span>
            </el-link>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    <div class="record-title">
      <span class="title-left"></span>
      执行记录
    </div>
    <el-table highlight-current-row :data="recordList" stripe height="300" v-loading="loading">
      <el-table-column label="任务记录ID" width="450" prop="job_id" show-overflow-tooltip />
      <el-table-column label="执行时间" width="180" prop="begin_time" show-overflow-tooltip />
      <el-table-column label="执行状态" prop="status_desc" show-overflow-tooltip>
        <template #default="scope">
          <div class="flex-center">
            <!-- <div class="status" :style="{ backgroundColor: COMMON_COLOR[scope.row.status] }"></div> -->
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="`${scope.row.code}：${scope.row.code_desc}`"
              placement="top"
              v-if="scope.row.status == 5"
            >
              <div class="flex">
                <div :class="scope.row.status == '3' ? 'green' : 'red'">
                  {{ scope.row.status_desc }}
                </div>
                <i-ep-warning-filled style="color: #ff9900; margin-left: 5px; transform: translateY(3px)" />
              </div>
            </el-tooltip>
            <div v-else :class="scope.row.status == '3' ? 'green' : 'red'">
              {{scope.row.status_desc}}
            </div>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="描述" prop="codeDesc" show-overflow-tooltip>
      </el-table-column> -->

      <el-table-column fixed="right" label="操作" align="center" width="200" v-if="authorityShow('flight-record')">
        <template #default="scope">
          <el-button v-if="scope.row.status === 3" type="primary" @click="checkHistory(scope.row)" link
            >飞行记录</el-button
          >
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import optionData from '@/utils/option-data';
import { useRouter } from 'vue-router';
// import router from '@/router';
import { fetchFlightRecord, getWaylines } from '@/api/wayline';
import { getTaskList } from '@/api/task';
import { COMMON_COLOR } from '@/utils/constants';
import { usePlanInfoStore } from '@/store/modules/planInfo.js';
import { getDroneInfoByPlanData } from '@/views/plan/common/devConfigHelper';
import { useDeviceStore } from '@/store/modules/device.js';
import { authorityShow } from '@/utils/authority';
const deviceStore = useDeviceStore();
const planInfoStore = usePlanInfoStore();
const router = useRouter();
const recordList = ref([]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  formData: {
    type: Object,
    default(rawProps) {
      return {};
    }
  }
});
const form = reactive({});
const dataFormRef = ref(ElForm);
const loading = ref(true);
watch(
  () => props.formData,
  (newVal, oldVal) => {
    console.log('newVal', newVal);
    Object.assign(form, newVal);
    getFlightRecord();
  },
  { deep: true }
);
const emit = defineEmits(['update:visible']);

const typeOptions = ref([]);

function getTypeOptiopn() {
  typeOptions.value = [];
}

// 关闭弹窗
function closeDialog() {
  resetForm();
  emit('update:visible', false);
}

function openPlanInfo(wayline_id) {
  if (!wayline_id) {
    ElMessage.warning('传入的ID不能为空！');
    return;
  }
  getWaylines({
    order_by: 'update_time desc',
    wayline_id: wayline_id
  }).then(data => {
    const { list, pagination } = data;
    if (list.length > 0) {
      let planData = list[0];
      let templateType = planData.template_types[0];
      let id = planData.id || '';
      planInfoStore.setCurPlanData(planData);
      // 通过计划数据获取基础的设备信息
      const droneBaseInfo = getDroneInfoByPlanData(planData);
      deviceStore.setCurrentDevice(droneBaseInfo);
      deviceStore.getDeviceInfoByType(droneBaseInfo.droneModelKey);
      if (templateType === 0) {
        router.push({
          path: '/planinfo',
          query: {
            id: id
          }
        });
      } else if (templateType === 1) {
        let import_type = planData.import_type || 0;
        router.push({
          path: '/surface',
          query: {
            id: id,
            import_type: import_type
          }
        });
      }
    }
  });
}

/**
 * 重置表单
 */
function resetForm() {
  Object.keys(form).map(key => {
    delete form[key];
  });
  recordList.value = []
}

// 点击查看历史记录 跳转
function checkHistory(item) {
  // 路由跳转
  router.push({
    path: '/mapfly-manager-history',
    query: {
      job_id: item.job_id,
      flight_id: item.job_id
      // wayline_id: item.wayline_id,
      // dock_sn: item.dock_sn
    }
  });
}

// 获取飞行记录
function getFlightRecord() {
  loading.value = true
  console.log('form', form);
  getTaskList({
    task_id: form.flight_task_id
  }).then(res => {
    recordList.value = res.list || [];
  }).finally(()=>{
    loading.value = false
  })
  // fetchFlightRecord(form.flight_task_id).then(res => {
  //   recordList.value = res || [];
  // });
}

onMounted(() => {
  getTypeOptiopn();
});
</script>
<style lang="scss">
.label-class {
  display: inline-block;
  width: 100px;
  text-align: right;
}
</style>
<style lang="scss" scoped>
:deep(.el-descriptions__body .el-descriptions__table:not(.is-bordered) .el-descriptions__cell) {
  padding-bottom: 20px;
}
.green,
.red {
  // width: 45px;
  padding: 0 5px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  background: rgba(42, 139, 125, 0.3);
  border-radius: 2px;
  font-family: SourceHanSansSC-Regular;
  font-size: 12px;
  color: #39bfa4;
  text-align: center;
  font-weight: 400;
}
.red {
  color: #f97066;
  background: rgba(249, 112, 102, 0.2);
}
.flex-center {
  display: flex;
  align-items: center;
  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
}
.left-content {
  display: flex;
  flex-direction: row;
  img {
    width: 136px;
    height: 136px;
    margin-right: 24px;
  }
  .line-left {
    display: flex;
    padding: 20px 8px;
  }
  .line-middle {
    width: 1px;
    height: 314px;
    background: rgba(0, 0, 0, 0.06);
    margin: auto 32px;
  }
  .line-right {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    .item {
      padding: 6px;
      width: 100%;
      color: #000000d9;
      .color {
        color: #000000a6;
      }
    }
  }
}
.record-title {
  margin: 0 0 16px;
  padding-bottom: 8px;
  font-size: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
  .title-left {
    display: inline-block;
    margin-right: 3px;
    height: 14px;
    border-left: 2px solid #275ba9;
  }
}
</style>
