<template>
  <el-dialog
    v-if="visible"
    :title="comparePicturesRect.leftPicture.file_name || title"
    :model-value="visible"
    width="1100px"
    height="1056px"
    align-center
    :close-on-click-modal="false"
    class="dia"
    @close="closeDialog"
    @opened="onDialogOpened"
  >
    <!--图片类型-->
    <div class="content">
      <div class="centered-element">
        <div class="header-wrapper">
          <div class="image-info">
            <div class="name">{{ comparePicturesRect.leftPicture.file_name || '未命名' }}</div>
            <div class="timer">{{ comparePicturesRect.leftPicture.create_time }}</div>
          </div>
          <div class="image-info" style="padding-left: 90px" v-if="showComparePage">
            <div class="name">{{ comparePicturesRect.rightPicture.file_name || '未命名' }}</div>
            <div class="timer">{{ comparePicturesRect.rightPicture.create_time }}</div>
          </div>
          <el-popover ref="picturesPopuRef" placement="right" :width="400" trigger="click">
            <template #reference>
              <div class="more-image" v-if="showComparePage">
                <el-button type="primary" size="large" @click="showSearchDialog">重选照片</el-button>
              </div>
            </template>
            <pictures-popu @compareChange="onCompareChangeHandle" />
          </el-popover>
        </div>
        <!-- 滑动轨道  ref="tipRef"-->
        <Tip @change="rollerChangehandle" ref="tipRef" :loc="compareLocation" v-if="showComparePage" />
        <div class="check-wrapper">
          <div class="image-detail image-canvaa-1">
            <canvas
              ref="imageCanvas"
              class="image-canvas"
              :width="dialogInfoRect.width"
              :height="dialogInfoRect.height"
            ></canvas>
          </div>
          <div class="image-detail-2 image-canvaa-2" v-if="showComparePage">
            <canvas
              ref="imageCanvas2"
              class="image-canvas"
              :width="dialogInfoRect.width"
              :height="dialogInfoRect.height"
            ></canvas>
          </div>
        </div>
        <div class="btns">
          <div class="btn-box">
            <el-tooltip class="box-item" show-after="1" content="放大" raw-content="true" placement="top">
              <div
                class="picture btn"
                :class="{ notallowed: iconRect.zoomIn.disable, active: iconRect.zoomIn.active }"
                @click="actionHandle(iconRect.zoomIn)"
              >
                <el-image class="img" style="width: 100%" :src="iconRect.zoomIn.icon" fit="fill" />
              </div>
            </el-tooltip>

            <el-tooltip class="box-item" show-after="1" content="缩小" raw-content="true" placement="top">
              <div
                class="picture btn"
                :class="{ notallowed: iconRect.zoomOut.disable, active: iconRect.zoomOut.active }"
                @click="actionHandle(iconRect.zoomOut)"
              >
                <el-image class="img" style="width: 100%" :src="iconRect.zoomOut.icon" fit="fill" />
              </div>
            </el-tooltip>

            <el-tooltip class="box-item" show-after="1" content="对比" raw-content="true" placement="top">
              <div
                class="picture btn"
                :class="{ notallowed: iconRect.compare.disable, active: iconRect.compare.active }"
                @click="actionHandle(iconRect.compare)"
              >
                <el-image class="img" style="width: 100%" :src="iconRect.compare.icon" fit="fill" />
              </div>
            </el-tooltip>

            <el-tooltip class="box-item" show-after="1" content="重置" raw-content="true" placement="top">
              <div
                class="picture btn"
                :class="{ notallowed: iconRect.reset.disable, active: iconRect.reset.active }"
                @click="actionHandle(iconRect.reset)"
              >
                <el-image class="img" style="width: 100%" :src="iconRect.reset.icon" fit="fill" />
              </div>
            </el-tooltip>

            <el-tooltip class="box-item" show-after="1" content="向左旋转90°" raw-content="true" placement="top">
              <div
                class="picture btn"
                :class="{ notallowed: iconRect.turnLeft.disable, active: iconRect.turnLeft.active }"
                @click="actionHandle(iconRect.turnLeft)"
              >
                <el-image class="img" style="width: 100%" :src="iconRect.turnLeft.icon" fit="fill" />
              </div>
            </el-tooltip>
            <el-tooltip class="box-item" show-after="1" content="向右旋转90°" raw-content="true" placement="top">
              <div
                class="picture btn"
                :class="{ notallowed: iconRect.turnRight.disable, active: iconRect.turnRight.active }"
                @click="actionHandle(iconRect.turnRight)"
              >
                <el-image class="img" style="width: 100%" :src="iconRect.turnRight.icon" fit="fill" />
              </div>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, nextTick } from 'vue';
import Tip from './Tip.vue';
import PicturesPopu from './PicturesPopu.vue';
import { comparePicturesRect, getPicturesByLnglats } from '../imageComparison';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '照片弹窗'
  },
  imgUrl: {
    type: String,
    default: ''
  }
});
const emit = defineEmits(['update:visible']);
//#region 定义变量
const tipRef = ref(null);
const picturesPopuRef = ref(null);
const compareImageUrl = ref(''); // 用于对比的图片地址
const imageCanvas = ref(null);
const imageCanvas2 = ref(null);
let context = ref(null);
let compareContext = ref(null);
let compareLocation = ref(500);
let canvas = ref(null);
let compareCanvas = ref(null);
const scale = ref(1);
const rotation = ref(0);
const dialogInfoRect = reactive({
  width: 1000,
  height: 500
});
let showComparePage = ref(false); // 是否展示对比

const iconRect = reactive({
  compare: {
    icon: new URL('@/assets/compare/compare.png', import.meta.url).href,
    label: 'compare',
    disable: false,
    active: false
  },
  reset: {
    icon: new URL('@/assets/compare/reset.png', import.meta.url).href,
    label: 'reset',
    disable: false,
    active: false
  },
  turnLeft: {
    icon: new URL('@/assets/compare/turnLeft.png', import.meta.url).href,
    label: 'turnLeft',
    disable: false,
    active: false
  },
  turnRight: {
    icon: new URL('@/assets/compare/turnRight.png', import.meta.url).href,
    label: 'turnRight',
    disable: false,
    active: false
  },
  zoomIn: {
    icon: new URL('@/assets/compare/zoomIn.png', import.meta.url).href,
    label: 'zoomIn',
    disable: false,
    active: false
  },
  zoomOut: {
    icon: new URL('@/assets/compare/zoomOut.png', import.meta.url).href,
    label: 'zoomOut',
    disable: false,
    active: false
  }
});

//#endregion

function actionHandle(item) {
  const { label, disable, active } = item;
  if (disable) {
    return;
  }
  for (const key in iconRect) {
    if (key !== label) {
      iconRect[key].active = false;
    }
  }
  if (!item.active && label !== 'compare') {
    item.active = !item.active;
  }
  switch (label) {
    case 'compare':
      item.active = !item.active;
      reset();
      for (const key in iconRect) {
        if (key !== label) {
          iconRect[key].disable = item.active;
          if (item.active) {
            iconRect[key].active = false;
          }
        }
      }
      if (item.active) {
        getPicturesByLnglats(data => {
          if (!data || data.length === 0) {
            showComparePage.value = false;
            comparePicturesRect.rightPicture = null;
            iconRect.compare.disable = false;
            iconRect.compare.active = false;
            for (const key in iconRect) {
              if (key !== label) {
                iconRect[key].active = false;
                iconRect[key].disable = false;
              }
            }
            return;
          }
          showComparePage.value = true;
          comparePicturesRect.rightPicture = data[0];
          check();
        });
      } else {
        showComparePage.value = false;
        comparePicturesRect.rightPicture = null;
      }
      break;
    case 'reset':
      reset();
      break;
    case 'zoomIn':
      zoomIn();
      break;
    case 'zoomOut':
      zoomOut();
      break;
    case 'turnLeft':
      rotateLeft();
      break;
    case 'turnRight':
      rotateRight();
      break;
    default:
      break;
  }
}

function onCompareChangeHandle(data) {
  if (!data) {
    picturesPopuRef?.value?.hide();
    return;
  }
  picturesPopuRef?.value?.hide();
  comparePicturesRect.rightPicture = data;
  compareImageUrl.value = comparePicturesRect.rightPicture.file_url;
  compareLocation.value = 460; // 初始化位置 中间
  tipRef.value?.resetTrack(compareLocation.value);
  drawCompareImageHalf();
  // 获取当前数字及x坐标
  // const ctx = compareContext.value;
  // if (!ctx || !compareImageUrl.value) return;
  // let img = new Image();
  // img.onload = () => {
  //   let leaveW = 1000 - compareLocation.value;
  //   ctx.rect(compareLocation.value, 0, leaveW, 600); // 从 x=40 开始，宽度为 canvas 宽度减去 40，高度为 canvas 高度
  //   ctx.clip(); // 应用裁剪区域
  //   ctx.drawImage(img, 0, 0, img.width, img.height);
  // };
  // img.src = compareImageUrl.value;
}

// 关闭弹窗
function closeDialog() {
  showComparePage.value = false;
  for (const key in iconRect) {
    iconRect[key].active = false;
    iconRect[key].disable = false;
  }
  reset();
  emit('update:visible', false);
}

function drawImage() {
  const ctx = context.value;
  if (!ctx || !props.imgUrl) return;
  ctx.clearRect(0, 0, 1000, 600);
  let img = new Image();
  img.onload = () => {
    canvas.width = img.width;
    canvas.height = img.height;
    ctx.save();
    ctx.translate(canvas.width / 2, canvas.height / 2);
    ctx.rotate(rotation.value);
    ctx.scale(scale.value, scale.value);
    ctx.translate(-img.width / 2, -img.height / 2);
    ctx.drawImage(img, 0, 0, img.width, img.height);
    ctx.restore();
  };
  img.src = props.imgUrl;
}

function drawCompareImage() {
  const ctx = compareContext.value;
  if (!ctx || !compareImageUrl.value) return;
  ctx.clearRect(0, 0, 1000, 600);
  let img = new Image();
  img.onload = () => {
    compareCanvas.width = img.width;
    compareCanvas.height = img.height;
    ctx.save();
    ctx.translate(compareCanvas.width / 2, compareCanvas.height / 2);
    ctx.rotate(rotation.value);
    ctx.scale(scale.value, scale.value);
    ctx.translate(-img.width / 2, -img.height / 2);
    ctx.drawImage(img, 0, 0, img.width, img.height);
    ctx.restore();
  };
  img.src = compareImageUrl.value;
}

function drawCompareImageHalf() {
  const ctx = compareContext.value;
  if (!ctx || !compareImageUrl.value) return;
  ctx.clearRect(0, 0, 1000, 600);
  let img = new Image();
  img.onload = () => {
    compareCanvas.width = img.width;
    compareCanvas.height = img.height;
    ctx.save();
    ctx.translate(compareCanvas.width / 2, compareCanvas.height / 2);
    ctx.rotate(rotation.value);
    ctx.scale(scale.value, scale.value);
    ctx.translate(-img.width / 2, -img.height / 2);
    // 绘制一半
    ctx.rect(img.width / 2, 0, img.width, img.height); // 从 x=40 开始，宽度为 canvas 宽度减去 40，高度为 canvas 高度
    ctx.clip(); // 应用裁剪区域
    ctx.drawImage(img, 0, 0, img.width, img.height);
    ctx.restore();
  };
  img.src = compareImageUrl.value;
}

function check() {
  console.log('正在获取当前相似照片');
  compareImageUrl.value = comparePicturesRect.rightPicture.file_url;
  nextTick(() => {
    compareCanvas = imageCanvas2.value;
    if (!compareCanvas) return; // 如果 canvas 为 null，则直接返回
    compareContext.value = compareCanvas.getContext('2d');
    // drawCompareImage();
    drawCompareImageHalf();
  });
}

function check2() {
  console.log('正在获取当前相似照片');
  compareImageUrl.value = comparePicturesRect.rightPicture.file_url;
  nextTick(() => {
    compareCanvas = imageCanvas2.value;
    if (!compareCanvas) return; // 如果 canvas 为 null，则直接返回
    compareContext.value = compareCanvas.getContext('2d');
    drawCompareImage();
  });
}

function zoomIn() {
  scale.value *= 1.1;
  drawImage();
}

function zoomOut() {
  scale.value /= 1.1;
  drawImage();
}

function rotateLeft() {
  rotation.value -= Math.PI / 2;
  drawImage();
}

function rotateRight() {
  rotation.value += Math.PI / 2; // 45 degrees in radians
  drawImage();
}

function reset() {
  scale.value = 1;
  rotation.value = 0;
  drawImage();
}

function onDialogOpened() {
  nextTick(() => {
    canvas = imageCanvas.value;
    if (!canvas) return; // 如果 canvas 为 null，则直接返回
    context.value = canvas.getContext('2d');
    drawImage();
  });
}

// 卷帘效果
/**
 * 传入当前所在的位置 0-1000 之间
 */
function rollerChangehandle(v) {
  // 获取当前数字及x坐标
  const ctx = compareContext.value;
  if (!ctx || !compareImageUrl.value) return;
  let img = new Image();
  img.onload = () => {
    let value = (v / 1000) * img.width;
    compareLocation.value = v;
    compareCanvas.width = img.width;
    compareCanvas.height = img.height;
    let leaveW = img.width - value;
    ctx.rect(value, 0, leaveW, img.height); // 从 x=40 开始，宽度为 canvas 宽度减去 40，高度为 canvas 高度
    ctx.clip(); // 应用裁剪区域
    ctx.drawImage(img, 0, 0, img.width, img.height);
  };
  img.src = compareImageUrl.value;
}

// 显示重新选择窗体
function showSearchDialog() {
  getPicturesByLnglats();
}

function move(img) {
  //全局定义变量，来保存开始的屏幕的坐标
  var beginX, beginY;
  var canMove = false; //这里用改变坐标原点的方式来画图，让坐标原点始终在图片的中心
  var PO = { x: 0, y: 0 };
  if (!canvas) {
    return;
  }
  var imgW = canvas.width;
  var imgH = canvas.height;
  //window屏幕坐标转化为canvas坐标
  function convertCoordinate(x, y) {
    //在屏幕坐标系中，相对canvas坐标系原点PO的偏移,所以要减去canvas坐标原点
    x = x - PO.x;
    y = y - PO.y;
    return { x: x, y: y };
  } //判断鼠标是否在图片上按下

  function imgIsDown(x, y) {
    //找到图片的最小值和最大值，因为画图是从-imgW / 2开始的，那么这就是图片占据的位置的最小值，最大值是imgW / 2,y轴同理
    return -imgW / 2 < x && x < imgW / 2 && -imgH / 2 < y && y < imgH / 2;
  }
  const ctx = canvas.getContext('2d');
  canvas.onmousedown = e => {
    //e.offsetX是鼠标点击到canvas边的位置
    beginX = e.offsetX;
    beginY = e.offsetY;
    //把点击的win坐标转为canvas坐标
    var Cp = convertCoordinate(beginX, beginY);
    //判断在canvas坐标点上是否在图片上
    canMove = imgIsDown(Cp.x, Cp.y);
    if (canMove) {
      canvas.onmousemove = e => {
        var x = e.offsetX;
        var y = e.offsetY;
        //算出来移动的像素（每次都是减去上次的值）
        var Mx = x - beginX;
        var My = y - beginY;
        //Mx和My是win上面移动的像素，还需要转为canvas坐标,加上坐标是因为要从坐标原点开始算
        var CPO = convertCoordinate(Mx + PO.x, My + PO.y);
        //改变canvas原点坐标
        ctx.translate(CPO.x, CPO.y);
        //先清除画布，清除两倍的画布，因为要改变坐标原点，只有这样才能不管原点在哪里都能完全清除画布
        ctx.clearRect(-canvas.width, -canvas.height, canvas.width * 2, canvas.height * 2);
        //画图片，因为原点在图片的中心点，所以每次画图只需要从图片的负一半坐标开始画，就能看到我们想要的效果
        ctx.drawImage(img, -imgW / 2, -imgH / 2);
        //画完以后要保存起来最终画到哪里了
        PO.x += Mx; //canvas坐标原点
        PO.y += My;
        beginX = x; //保存起来这次图画到了哪里
        beginY = y;
      };
    }
    document.onmouseup = () => {
      canvas.onmousemove = null;
      document.onmouseup = null;
      canMove = false;
    };
  };
}

onMounted(() => {});
</script>
<style lang="scss" scoped>
.dia {
  background-color: #011129 !important; //1e4469
}
.content {
  position: relative;
  user-select: none;
  width: 1060px;
  height: 640px;
  max-height: 660px;
}

.centered-element {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  /* 元素的宽度和高度 */
  width: 1000px;
  .header-wrapper {
    position: relative;
    width: 1000px;
    padding: 2px 0px;
    // margin-bottom: 0px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .image-info {
      .name {
        color: aliceblue;
      }
      .title {
        color: aliceblue;
      }
    }
  }
  .image-detail {
    padding: 1px;
    // border: 1px solid #e6e6e6;
    width: 1000px;
    height: 500px;
    max-width: 1000px;
    max-height: 500px;
    background-color: rgba(123, 123, 123, 0.212);
    .image-canvas {
      transition: transform 0.3s ease; /* Optional: Smooth transition for rotation */
      width: 1000px;
      height: 500px;
    }
  }
  .image-detail-2 {
    padding: 1px;
    width: 1000px;
    height: 500px;
    max-width: 1000px;
    max-height: 500px;
    .image-canvas {
      transition: transform 0.3s ease; /* Optional: Smooth transition for rotation */
      width: 1000px;
      height: 500px;
    }
  }
  .track {
    position: relative;
    width: 1000px;
    padding: 1px;
    width: 1000px;
    height: 30px;
    background-color: rgba(132, 205, 55, 0.212);
    .tip {
      position: absolute;
      top: 0px;
      left: 0px;
      width: 100%;
      height: 100%;
      z-index: 9999;
    }
  }
  .btns {
    width: 100%;
    height: 60px;
    margin-top: 10px;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999 !important;

    .btn-box {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #11273d;
      padding: 0px 0px;
      .btn {
        padding: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 5px;
        background-color: #11273d;
        &:hover {
          background-color: #1e4469;
          // cursor: pointer;
        }
        .img {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
  .check-wrapper {
    padding: 1px;
    width: 1000px;
    height: 500px;
    max-width: 1000px;
    max-height: 500px;
    background-color: rgba(123, 123, 123, 0.212);
    position: relative;
    .image-canvaa-1 {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }

    .image-canvaa-2 {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
}

.notallowed {
  // background-color: rgba(197, 21, 21, 0.212) !important;
  cursor: not-allowed !important;
}

.active {
  background-color: #0790ff !important;
}
</style>
