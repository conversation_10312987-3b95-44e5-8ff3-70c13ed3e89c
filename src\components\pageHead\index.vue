<!-- 部分页面的公共同步组件 -->
<template>
  <div>
    <div class="head-content" :class="[customClass]">
      <div class="logo-content flex text-center">
        <template v-if="showName">
          <img class="ss-logo" :src="logo" alt="" />
          <div v-if="title" class="ml-3">
            <span class="text-lg logo-text">{{ headTitle }}</span>
          </div>
        </template>
      </div>
      <template v-if="showLanguage">
        <el-select
          class="custom-select"
          v-model="$i18n.locale"
          @change="handleLanguageChange"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import i18n from '@/lang';
import { useAppStore } from '@/store/modules/app';
import logoImg from '@/assets/sx_logo.png';

const appStore = useAppStore();

function handleLanguageChange(lang) {
  appStore.changeLanguage(lang);

  ElMessage({
    message: i18n.global.t('navbar.switchLanguageSuccess'),
    type: 'success',
    duration: 800
  });

  setTimeout(() => {
    window.location.reload();
  }, 900);
}
// Props
const props = defineProps({
  showLanguage: {
    type: Boolean,
    default: true
  },
  showName: {
    type: Boolean,
    default: true
  },
  customClass: {
    type: String,
    default: ''
  },
  logo: {
    type: String,
    default: logoImg
  },
  title: {
    type: String,
    default: 'platforms.Four Faith Cloud Platform'
  }
});

const options = ref([
  {
    value: 'zh',
    label: '中   文'
  },
  {
    value: 'en',
    label: 'English'
  }
]);

// Computed
const headTitle = computed(() => {
  return import.meta.env.VITE_APP_NODE_ENV === 'productionByEn'
    ? 'IOT Platform'
    : i18n.global.t(props.title);
});
</script>
<style lang="scss" scoped>
.head-content {
  display: flex;
  padding: 4% 16% 0 11%;
  background-color: transparent;
  justify-content: space-between;
  align-items: center;
  .ss-logo {
    width: 44px;
    height: 26px;
  }

  .logo-text {
    font-size: 25px;
    font-family: Alibaba PuHuiTi;
    font-weight: bold;
    color: #ffffff;
  }
}

:deep(.el-select) {
  width: 90px;
}

:deep(.head-content) {
  .el-input__wrapper {
    background: inherit;
    box-shadow: none;

    &:hover {
      box-shadow: none;
    }
  }

  .el-input.is-focus .el-input__wrapper {
    box-shadow: none !important;
    &:hover {
      box-shadow: none !important;
    }
  }

  .el-input__wrapper.is-focus {
    box-shadow: none !important;
    &:hover {
      box-shadow: none;
    }
  }
  .is-focus {
    box-shadow: none;
  }
  .el-select {
    .el-input__inner {
      color: #606266;
    }
  }

  .el-icon-arrow-up:before {
    color: #63bbf1 !important;
  }
}
</style>
