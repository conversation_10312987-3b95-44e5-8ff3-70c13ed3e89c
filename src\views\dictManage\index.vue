<template>
    <div class="app-container">
      <div class="search">
        <div class="search-form">
          <el-input
            v-model="queryParams.keyword"
            placeholder="请输入字典编码/名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </div>
        <div class="search-btn">
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button type="primary" @click="handleAdd">新增</el-button>
        </div>
      </div>
      <el-card shadow="never">
        <el-table :data="dataList" v-loading="loading" stripe height="630" style="width: 100%; margin-top: 10px" row-key="id">
          <el-table-column prop="dict_code" label="字典编码" min-width="120" />
          <el-table-column prop="dict_name" label="字典名称" min-width="120" />
          <el-table-column prop="dict_value" label="字典值" min-width="120" />
          <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
          <el-table-column prop="sort_no" label="排序" width="80" align="center" />
          <el-table-column prop="status" label="状态" width="80" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">
                {{ scope.row.status === 0 ? '正常' : '停用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="is_sys" label="是否系统数据" width="120" align="center">
            <template #default="scope">
              <el-tag type="info" v-if="scope.row.is_sys === 1">是</el-tag>
              <span v-else>否</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="300" align="center" fixed="right">
            <template #default="scope">
              <el-button type="primary" link @click="handleUpdate(scope.row)">编辑</el-button>
              <el-button type="primary" link @click="handleDetail(scope.row)">详情</el-button>
              <el-button type="success" link @click="handleAddChild(scope.row)">添加下级字典</el-button>
              <el-popconfirm v-if="scope.row.is_sys !== 1" title="确认删除该字典吗？" @confirm="handleDelete(scope.row)">
                <template #reference>
                  <el-button type="danger" link>删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-content">
          <pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.pageNo"
            v-model:limit="queryParams.pageSize"
            @pagination="handleSearch"
          />
        </div>
      </el-card>
      
      <!-- 字典编辑对话框 -->
      <DictDialog 
        v-model:visible="dictDialog.visible"
        :title="dictDialog.title"
        :formData="dictDialog.data"
        @submit="handleSearch"
      />
      
      <!-- 字典详情对话框 -->
      <el-dialog
        title="字典详情"
        v-if="detailDialog.visible"
        :model-value="detailDialog.visible"
        width="600px"
        @close="detailDialog.visible = false"
      >
        <el-descriptions :column="1" border>
          <el-descriptions-item label="字典编码">{{ detailDialog.data.dict_code }}</el-descriptions-item>
          <el-descriptions-item label="字典名称">{{ detailDialog.data.dict_name }}</el-descriptions-item>
          <el-descriptions-item label="字典值">{{ detailDialog.data.dict_value }}</el-descriptions-item>
          <el-descriptions-item label="描述">{{ detailDialog.data.description }}</el-descriptions-item>
          <el-descriptions-item label="父级编码">{{ detailDialog.data.parent_code }}</el-descriptions-item>
          <el-descriptions-item label="排序号">{{ detailDialog.data.sort_no }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="detailDialog.data.status === 0 ? 'success' : 'danger'">
              {{ detailDialog.data.status === 0 ? '正常' : '停用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="是否系统数据">
            <el-tag type="info" v-if="detailDialog.data.is_sys === 1">是</el-tag>
            <span v-else>否</span>
          </el-descriptions-item>
          <el-descriptions-item label="备注">{{ detailDialog.data.remark || '-' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ detailDialog.data.create_time }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ detailDialog.data.update_time }}</el-descriptions-item>
        </el-descriptions>
      </el-dialog>
    </div>
  </template>
  
  <script setup>
  import { ref, reactive, onMounted } from 'vue'; 
  import optionData from '@/utils/option-data'; 
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { getDictList, getDict, deleteDict } from '@/api/system/dict';
  import DictDialog from './components/DictDialog.vue';
  
  const loading = ref(false);
  const dataList = ref([]);
  const total = ref(0);

  const queryParams = reactive({
    pageNo: 1,
    pageSize: 10,
    keyword: ''
  });
  
  const dictDialog = reactive({
    visible: false,
    title: '添加字典',
    data: {}
  });

  const detailDialog = reactive({
    visible: false,
    data: {}
  });
  
  /**
   * 查询字典列表
   */
  function handleQuery() {
    loading.value = true;
    
    // 构建查询参数
    const params = {
      pageNo: queryParams.pageNo,
      pageSize: queryParams.pageSize
    };
    
    // 只有当keyword有值时才添加到查询参数
    if (queryParams.keyword) {
      params.keyword = queryParams.keyword.trim();
    }
    
    getDictList(params)
      .then(data => {
        // 转换返回的数据中的字段名为下划线格式
        if (Array.isArray(data)) {
          dataList.value = data.map(item => {
            const formattedItem = {};
            Object.keys(item).forEach(key => {
              const newKey = key.replace(/([A-Z])/g, "_$1").toLowerCase();
              formattedItem[newKey] = item[key];
            });
            return formattedItem;
          });
        } else if (data && data.records) {
          // 如果返回的是分页数据结构
          dataList.value = data.records.map(item => {
            const formattedItem = {};
            Object.keys(item).forEach(key => {
              const newKey = key.replace(/([A-Z])/g, "_$1").toLowerCase();
              formattedItem[newKey] = item[key];
            });
            return formattedItem;
          });
          total.value = data.total || 0;
        } else {
          dataList.value = [];
          total.value = 0;
        }
        loading.value = false;
        
        // 如果没有设置total，则使用数组长度
        if (!total.value) {
          total.value = dataList.value.length;
        }
      })
      .catch(err => {
        loading.value = false;
        console.error(err);
        dataList.value = [];
        total.value = 0;
      });
  }
  
  /**
   * 搜索
   */
  function handleSearch() { 
    queryParams.pageNo = 1;
    handleQuery();
  }
  
  /**
   * 重置查询条件
   */
  function resetQuery() {
    queryParams.keyword = '';
    queryParams.pageNo = 1;
    handleQuery();
  }
  
  /**
   * 新增字典
   */
  function handleAdd() {
    dictDialog.title = '添加字典';
    dictDialog.data = {};
    dictDialog.visible = true;
  }
  
  /**
   * 修改字典
   */
  function handleUpdate(row) {
    getDict(row.id).then(res => {
      dictDialog.title = '编辑字典';
      dictDialog.data = res;
      dictDialog.visible = true;
    });
  }
  
  /**
   * 添加下级字典
   */
  function handleAddChild(row) {
    dictDialog.title = '添加下级字典';
    dictDialog.data = {
      parent_code: row.dict_code
    };
    dictDialog.visible = true;
  }
  
  /**
   * 查看字典详情
   */
  function handleDetail(row) {
    getDict(row.id).then(res => {
      // 转换返回的数据中的字段名为下划线格式
      const formattedData = {};
      Object.keys(res).forEach(key => {
        const newKey = key.replace(/([A-Z])/g, "_$1").toLowerCase();
        formattedData[newKey] = res[key];
      });
      detailDialog.data = formattedData;
      detailDialog.visible = true;
    });
  }
  
  /**
   * 删除字典
   */
  function handleDelete(row) {
    if (row.is_sys === 1) {
      ElMessage.error('系统数据不允许删除！');
      return;
    }
    
    deleteDict(row.id).then(() => {
      ElMessage.success('删除成功');
      handleSearch();
    });
  }
  
  //  初始化
  onMounted(() => { 
    handleQuery();
  });
  </script>
  
  <style lang="scss" scoped>
  .app-container {
    padding: 16px 20px;
    .search {
      display: flex;
      align-items: center;
      padding: 24px;
      height: 64px;
      .search-form {
        flex: 1;
        color: #fff;
      }
      .search-btn {
        margin-left: 16px;
      }
    }
    .app-content {
      width: 100%;
      max-height: calc(100vh - 154px);
      padding: 16px 24px;
      background: #fff;
      overflow: auto;
      box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
      border-radius: 4px;
      &::-webkit-scrollbar {
        width: 10px !important;
        height: 10px !important;
        background: #e4e7ec;
        border-radius: 4px;
      }
      &::-webkit-scrollbar-thumb {
        width: 10px !important;
        min-height: 20px !important;
        background: #b7d9fd !important;
        border-radius: 4px !important;
      }
      .btn-box {
        margin-bottom: 16px;
      }
      .textHidden {
        width: 180px;
        height: 20px;
        line-height: 20px;
        text-align: left;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
  .pagination-content {
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px;
  
    &.hidden {
      display: none;
    }
  }
  </style>
  