<template>
  <el-dropdown trigger="click" class="setting-container cursor-pointer">
    <div class="flex justify-center items-center mx-3">
      <i-ep-setting class="icon-color" />
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <!-- <el-dropdown-item class="text-center">
          {{ $t('navbar.tagViewSettings') }}
          <el-switch class="ml-2" v-model="switchValue" />
        </el-dropdown-item> -->
        <el-dropdown-item class="text-center">
          <span @click="dashBoardSettingRef.handleOpen()">
            {{ $t('navbar.dashboardSettings') }}
          </span>
        </el-dropdown-item>
        <el-dropdown-item class="text-center">
          <span @click="dialogLangRef.handleOpen()">
            {{ $t('Language Setting') }}
          </span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
  <!-- <DashBoardSetting ref="dashBoardSettingRef" /> -->
  <DialogLang ref="dialogLangRef" />
</template>

<script>
export default {
  name: 'settings'
};
</script>

<script setup>
import { useSettingsStore } from '@/store/modules/settings';
import { getTagView } from '@/utils/store';
// import DashBoardSetting from '@/components/DashboardSetting/index.vue';
import DialogLang from '../dialogLang/index.vue';

const settingStore = useSettingsStore();
const switchValue = ref(getTagView() || false);
const dashBoardSettingRef = ref('dashBoardSettingRef');
const dialogLangRef = ref('dialogLangRef');

watch(
  () => switchValue.value,
  val => {
    settingStore.changeSetting({
      key: 'tagsView',
      value: val && !settingStore.tagsView ? true : false
    });
  }
);

onMounted(() => {
  window.$bus.on('openCardSetting', () => {
    dashBoardSettingRef.value.handleOpen();
  });
});

onUnmounted(() => {
  window.$bus.off('openCardSetting');
});
</script>

<style lang="scss" scoped>
:global(.el-dropdown-menu__item:focus .tagViewText) {
  color: var(--el-dropdown-menuItem-hover-color);
}

:global(.el-dropdown-menu__item:visited .tagViewText) {
  color: var(--el-dropdown-menuItem-hover-color);
}
.icon-color {
  color: #fff;
  font-size: 18px;
}
</style>
