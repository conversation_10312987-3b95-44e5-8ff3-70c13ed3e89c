import { MissionConfig } from './MissionConfig.js';
import { Folder } from './Folder.js';
class Wayline {
  constructor() {
    this.wpml_missionConfig = new MissionConfig();
    this.folder = [new Folder()] || []; //是个数组
  }
  /**
   * 设置文件夹
   * @param {*} f
   */
  setFolder(f) {
    this.folder = f;
  }
  /**
   * 设置任务配置
   * @param {*} value
   */
  setMissionConfig(value) {
    this.wpml_missionConfig = value;
  }
  /**
   * 向当前waylines实例的folder数组中添加一个Folder实例。
   * @param {Folder} folder - 要添加的Folder实例。
   */
  addFolder(folder) {
    if (folder instanceof Folder && Array.isArray(this.folder)) {
      this.folder.push(folder);
    } else {
      throw new Error('Invalid argument or folder array is not initialized properly.');
    }
  }
  /**
   *  默认获取第一个
   * @param {*} index
   * @returns
   */
  getFolder(index = 0) {
    return this.folder[index] || null;
  }
  // 获取所有文件夹
  getAllFolder() {
    return this.folder;
  }
  // 返回航点对象
  getWaylines() {
    return {
      document: {
        folder: this.folder,
        missionConfig: this.wpml_missionConfig
      }
    };
  }
  // 销毁
  destroy() {
    this.folder = [];
    this.wpml_missionConfig = null;
  }
}
export { Wayline };
