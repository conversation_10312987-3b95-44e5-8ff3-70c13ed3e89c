// 必须元素的配置方案 暂时必须元素的适配 全部都上
import { EXIT_ON_RCLOST } from '@/views/plan/newplan/kmz/props';
export const getKmlApiConfig = () => {
  const djApi = {
    template: {},
    waylines: {
      missionConfig: [
        {
          key: 'flyToWaylineMode',
          value: null,
          necessary: true,
          supportedModels: ['M300 RTK', 'M350 RTK', 'M30', 'M30T', 'M3E', 'M3T', 'M3M', 'M3D', 'M3TD']
        },
        {
          key: 'exitOnRCLost',
          value: null,
          necessary: true,
          supportedModels: ['M300 RTK', 'M350 RTK', 'M30', 'M30T', 'M3E', 'M3T', 'M3M', 'M3D', 'M3TD']
        },
        {
          key: 'executeRCLostAction',
          value: null,
          necessary: false,
          supportedModels: ['M300 RTK', 'M350 RTK', 'M30', 'M30T', 'M3E', 'M3T', 'M3M', 'M3D', 'M3TD'],
          checkVariable: () => {
            const exitOnRCLost = djApi.waylines.missionConfig.find(item => item.key === 'exitOnRCLost');
            if (exitOnRCLost.value === EXIT_ON_RCLOST.executeLostAction) {
              const executeRCLostAction = djApi.waylines.missionConfig.find(item => item.key === 'executeRCLostAction');
              executeRCLostAction.necessary = true;
            }
          }
        }
      ]
    }
  };
  return djApi;
};
