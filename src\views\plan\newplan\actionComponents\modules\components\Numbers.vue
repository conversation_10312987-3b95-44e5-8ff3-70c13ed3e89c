<template>
  <div class="wrapper">
    <div class="title">
      {{ data.title }} ( <span class="unit"> {{ data.unit }} </span> )
    </div>
    <div class="header">
      <div class="left">
        <span
          class="uranus-btn"
          :class="{
            uranusBtnDisabled: decreaseVisible,
            notAllowed: decreaseVisible
          }"
          @click="onDecrease(100)"
          >-100</span
        >
        <span
          class="uranus-btn"
          :class="{
            uranusBtnDisabled: decreaseVisible,
            notAllowed: decreaseVisible
          }"
          @click="onDecrease(10)"
          >-10</span
        >
        <span
          class="uranus-btn"
          :class="{
            uranusBtnDisabled: decreaseVisible,
            notAllowed: decreaseVisible
          }"
          @click="onDecrease(1)"
          >-1</span
        >
      </div>
      <div class="middle">
        <el-input-number
          :controls="false"
          class="input-number"
          type="number"
          size="small"
          v-model="data.value"
          :max="data.max"
          :min="data.min"
          precision="0"
        ></el-input-number>
        <!-- <span class="unit"> {{ data.unit }} </span> -->
      </div>
      <div class="right">
        <span
          class="uranus-btn"
          :class="{
            uranusBtnDisabled: addVisible,
            notAllowed: addVisible
          }"
          @click="onAdd(1)"
          >+1</span
        >
        <span
          class="uranus-btn"
          :class="{
            uranusBtnDisabled: addVisible,
            notAllowed: addVisible
          }"
          @click="onAdd(10)"
          >+10</span
        >
        <span
          class="uranus-btn"
          :class="{
            uranusBtnDisabled: addVisible,
            notAllowed: addVisible
          }"
          @click="onAdd(100)"
          >+100</span
        >
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'Numbers'
};
</script>
<script setup>
import { onMounted, onUnmounted, computed } from 'vue';
//#region 数据双向绑定
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
// const data = {
//   title: '间隔距离',
//   unit: 'm',
//   value: 0,
//   acttionType:  动作类型 这里暂时没用
//   min:1,
//   max:900
// };
const data = computed({
  get: () => props.modelValue,
  set: value => {
    emits('update:modelValue', value);
  }
});
// 对外定义事件
const emits = defineEmits(['update:data', 'changeHandle']); // 触发事件
const onDecrease = v => {
  const { min = 1, max = 900 } = props.modelValue;
  let dataValue = data.value?.value;
  dataValue -= v;
  if (dataValue <= min) {
    dataValue = min;
  }
  if (dataValue >= max) {
    dataValue = max;
  }
  data.value.value = dataValue;
  emits('changeHandle', data.value.value);
};
const onAdd = v => {
  const { min = 1, max = 900 } = props.modelValue;
  let dataValue = data.value?.value;
  dataValue += v;
  if (dataValue <= min) {
    dataValue = min;
  }
  if (dataValue >= max) {
    dataValue = max;
  }
  data.value.value = dataValue;
  emits('changeHandle', data.value.value);
};
const decreaseVisible = computed(() => {
  return data.value.value <= data.value.min;
});
const addVisible = computed(() => {
  return data.value.value >= data.value.max;
});
//#endregion
onMounted(() => {});
onUnmounted(() => {});
</script>
<style lang="scss" scoped>
::v-deep.el-input {
  background-color: #313131;
  color: white;
  width: 40px;
}

::v-deep.el-input-number .el-input__inner {
  background-color: #11253e;
  color: white;
  width: 40px;
  font-size: 20px !important;
  text-align: center;
  color: #2d8cf0 !important;
  font-weight: 600;
}
::v-deep .el-input-numbert.is-disabled .el-input__wrapper {
  background-color: #11253e;
}

::v-deep.el-input-number .el-input__wrapper {
  background-color: #11253e;
}

::v-deep.el-input-number .el-input__wrapper {
  background-color: #11253e;
  box-shadow: 0 0 0 1px #cfcfcf8f inset;
}
.wrapper {
  // background-color: rgb(49, 49, 49);
  background-color: #11253e !important;
  color: white;
  padding: 10px 5px;
  width: 100%;
  user-select: none;
  .header {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
    .left {
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }
    .right {
      display: flex;
      align-items: center;
    }
    .middle {
      display: flex;
      align-items: center;
      width: 80px;
      .input-number {
        font-size: 20px !important;
        height: 28px;
        line-height: 28px;
        text-align: center;
        color: #2d8cf0 !important;
        font-weight: 600;
      }
      .unit {
        width: 10px;
        margin-left: 5px;
      }
    }
  }

  .header-input {
    margin: 5px 0px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .btn {
      display: flex;
      align-items: center;
      margin-left: 10px;
    }
  }
}

.round {
  border: 5px;
  color: white;
}
.item {
  width: auto;
  padding: 2px 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  background: rgba(45, 140, 240, 0.35);
  color: #ffffff40;
  margin-left: 5px;
  user-select: none;
  &:hover {
    cursor: pointer;
  }
}
.active {
  cursor: pointer;
  text-decoration: none !important;
  background: #5d5f61;
}

.uranus-btn {
  background: #344054;
  color: #fff;
  margin: 2px 2px;
  padding: 2px 5px;
  user-select: none;
  cursor: pointer;
}

.uranus-btn:hover {
  cursor: pointer;
  text-decoration: none !important;
  background: #5d5f61;
}

.uranusBtnDisabled {
  cursor: not-allowed !important;
  color: hsla(0, 0%, 100%, 0.25);
}

.notAllowed {
  cursor: not-allowed !important;
  color: #c9c8c8 !important;
  background: #333c46 !important;
}

.color-blue {
  background: #2d8cf0;
  color: white;
}
</style>
