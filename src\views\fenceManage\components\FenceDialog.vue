<template>
  <el-dialog
    :title="title"
    v-if="visible"
    :model-value="visible"
    width="800px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form ref="dataFormRef" :model="form" :rules="rules" label-width="100px" v-loading="loading">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="fenceName" label="围栏名称">
            <el-input v-model="form.fenceName" placeholder="请输入围栏名称" maxlength="100" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="fenceAddr" label="围栏地址">
            <el-input v-model="form.fenceAddr" placeholder="请输入围栏地址" maxlength="255" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="fenceType" label="围栏类型">
            <el-select v-model="form.fenceType" placeholder="请选择围栏类型" style="width: 100%">
              <el-option
                v-for="item in fenceTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="fenceTarget" label="识别目标">
            <el-select v-model="form.fenceTarget" placeholder="请选择识别目标" style="width: 100%">
              <el-option
                v-for="item in targetOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="businessType" label="业务类型">
            <el-select v-model="form.businessType" placeholder="请选择业务类型" style="width: 100%">
              <el-option
                v-for="item in businessTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="preProcess" label="处置措施">
            <el-select v-model="form.preProcess" placeholder="请选择前期处置措施" style="width: 100%">
              <el-option
                v-for="item in preProcessOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="contact" label="联系人">
            <el-input v-model="form.contact" placeholder="请输入联系人" maxlength="64" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="telephone" label="联系电话">
            <el-input v-model="form.telephone" placeholder="请输入联系电话" maxlength="32" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="fenceStatus" label="围栏状态">
            <el-radio-group v-model="form.fenceStatus">
              <el-radio label="active">启用</el-radio>
              <el-radio label="inactive">停用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="layerId" label="所属图层">
            <el-input-number v-model="form.layerId" :min="0" :precision="0" style="width: 100%" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item prop="remark" label="备注">
        <el-input v-model="form.remark" placeholder="请输入备注" type="textarea" maxlength="256" />
      </el-form-item>
      
      <el-form-item prop="fenceCoords" label="围栏区域">
        <div class="map-container">
          <div id="map" style="width: 100%; height: 300px;"></div>
          <div class="map-tools">
            <el-button type="primary" size="small" >绘制围栏</el-button>
            <el-button type="danger" size="small" >清除围栏</el-button>
          </div>
        </div>
        <div v-if="form.fenceCoords && form.fenceCoords.length > 0" class="coords-display">
          已设置围栏坐标点：{{ form.fenceCoords.length }} 个
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-loading="submitLoading">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { createFence, updateFence } from '@/api/fence';

// 引入地图相关API，根据实际项目调整
let map = null;
let drawingManager = null;
let polygon = null;

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '添加围栏'
  },
  formData: {
    type: Object,
    default() {
      return {};
    }
  }
});

const emit = defineEmits(['update:visible', 'submit']);
const dataFormRef = ref(null);
const loading = ref(false);
const submitLoading = ref(false);

// 围栏类型选项
const fenceTypeOptions = [
  { label: '入侵检测', value: 'intrusion' },
  { label: '区域监控', value: 'monitor' }
];

// 识别目标选项
const targetOptions = [
  { label: '人员', value: 'person' },
  { label: '车辆', value: 'vehicle' },
  { label: '全部', value: 'all' }
];

// 业务类型选项
const businessTypeOptions = [
  { label: '消防通道', value: 'fire_access' },
  { label: '危险区域', value: 'danger_area' },
  { label: '重点区域', value: 'important_area' }
];

// 前期处置措施选项
const preProcessOptions = [
  { label: '语音提示', value: 'speaker' },
  { label: '预警通知', value: 'alert' },
  { label: '无措施', value: 'none' }
];

const form = reactive({
  id: undefined,
  fenceName: '',
  fenceAddr: '',
  fenceType: 'intrusion',
  fenceTarget: 'vehicle',
  businessType: 'fire_access',
  preProcess: 'speaker',
  contact: '',
  telephone: '',
  fenceStatus: 'active',
  fenceCoords: [],
  layerId: 0,
  remark: ''
});

const rules = reactive({
  fenceName: [{ required: true, message: '围栏名称不能为空', trigger: 'blur' }],
  fenceType: [{ required: true, message: '围栏类型不能为空', trigger: 'change' }],
  fenceTarget: [{ required: true, message: '识别目标不能为空', trigger: 'change' }],
  businessType: [{ required: true, message: '业务类型不能为空', trigger: 'change' }],
  preProcess: [{ required: true, message: '前期处置措施不能为空', trigger: 'change' }],
  fenceStatus: [{ required: true, message: '围栏状态不能为空', trigger: 'change' }],
  fenceCoords: [{ 
    required: true, 
    validator: (rule, value, callback) => {
      if (!value || value.length < 3) {
        callback(new Error('请至少绘制3个坐标点的围栏区域'));
      } else {
        callback();
      }
    }, 
    trigger: 'change' 
  }]
});

watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      loading.value = true;
      setTimeout(() => {
        dataFormRef.value?.clearValidate();
        loading.value = false;
        // 初始化地图
        nextTick(() => { 
        });
      }, 100);
    }
  }
);

watch(
  () => props.formData,
  (newVal) => {
    if (newVal && Object.keys(newVal).length > 0) {
      // 转换字段名
      Object.keys(newVal).forEach(key => {
        // 转换服务端返回的下划线格式字段名为驼峰格式
        const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
        form[camelKey] = newVal[key];
      });
      
      // 特殊处理围栏坐标
      if (newVal.fence_coords) {
        form.fenceCoords = newVal.fence_coords;
      }
      
      // 如果有围栏坐标，在地图上绘制出来
      nextTick(() => {
        if (map && form.fenceCoords && form.fenceCoords.length > 0) {
          drawPolygon(form.fenceCoords);
        }
      });
    } else {
      resetForm();
    }
  },
  { deep: true, immediate: true }
);

function resetForm() {
  Object.assign(form, {
    id: undefined,
    fenceName: '',
    fenceAddr: '',
    fenceType: 'intrusion',
    fenceTarget: 'vehicle',
    businessType: 'fire_access',
    preProcess: 'speaker',
    contact: '',
    telephone: '',
    fenceStatus: 'active',
    fenceCoords: [],
    layerId: 0,
    remark: ''
  });
   
}

function closeDialog() {
  emit('update:visible', false);
  resetForm();
}

function handleSubmit() {
  dataFormRef.value.validate((valid) => {
    if (valid) {
      submitLoading.value = true;
      
      // 构造请求参数
      const params = { ...form };
      
      const request = form.id ? updateFence : createFence;
      request(params)
        .then(res => {
          if (res.code === 0) {
            ElMessage.success('保存成功');
            emit('submit');
            closeDialog();
          } else {
            ElMessage.error(res.msg || '保存失败');
          }
        })
        .catch(err => {
          console.error('保存失败', err);
          ElMessage.error('保存失败');
        })
        .finally(() => {
          submitLoading.value = false;
        });
    }
  });
}

onMounted(() => { 
});
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
}

.map-container {
  position: relative;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  
  .map-tools {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 5px;
    border-radius: 4px;
  }
}

.coords-display {
  margin-top: 8px;
  font-size: 14px;
  color: #606266;
}
</style> 