<!--应用管理-->
<script>
export default {
  name: 'Hhistory'
};
</script>

<script setup>
import { reactive } from 'vue';
import router from '@/router';
const loading = ref(false);
const ids = ref([]);
const total = ref(0);

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  queryType: 2,
  keyWord: ''
});

const dataList = ref([]);

const dialog = reactive({
  visible: false
});

let formData = reactive({});

/**
 * 查询
 */
function handleQuery() {
  dataList.value = [
    {
      jobInfo: '灾害点巡检',
      deviceName: 'FF-*********',
      flyInfo: '100米|2分15秒',
      alarmTime: '2024-01-10 12:39:23',
      flytime: '2024-01-10 12:39:23',
      flyplace: '湖里区-莲岳里',
      driver: '沐风',
      belong: '厦门四信',
      content: '地震'
    }
  ];
  total.value = 1;
}
function handleSearch() {
  queryParams.pageNum = 1;
  handleQuery();
}
/**
 * 重置查询
 */
function resetQuery(type = '') {
  handleQuery();
}

/**
 * 行checkbox change事件
 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
}

/**
 * 打开应用表单弹窗
 *
 * @param dicTypeId 应用ID
 */
function openDialog(row) {
  router.push('/map-fly');
}

/**
 * 删除应用
 */
function handleDelete(dictTypeId) {
  const dictTypeIds = [dictTypeId || ids.value].join(',');
  if (!dictTypeIds) {
    ElMessage.warning('请勾选删除项');
    return;
  }
}

onMounted(() => {
  handleQuery();
});
</script>

<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
          <el-form-item label="任务名称：" prop="keyWord">
            <el-input
              class="input-serach"
              v-model="queryParams.keyWord"
              placeholder="请输入任务名称："
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
            />
          </el-form-item>
          <el-form-item label="设备名称：" prop="keyWord">
            <el-input
              class="input-serach"
              v-model="queryParams.deviceName"
              placeholder="请输入设备名称"
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
            />
          </el-form-item>
        </el-form>
      </div>
      <div class="search-btn">
        <el-button type="primary" @click="handleSearch()"><i-ep-search />搜索</el-button>
        <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
      </div>
    </div>
    <el-card shadow="never">
      <el-table highlight-current-row :data="dataList" v-loading="loading" stripe height="540">
        <el-table-column labeuavNodevIdl="序号" align="center" width="80">
          <template #default="scope">
            <span>{{ scope.$index + 1 + queryParams.pageSize * (queryParams.pageNum - 1) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="飞行时间" prop="flytime" show-overflow-tooltip />
        <el-table-column label="任务信息" prop="jobInfo" show-overflow-tooltip />
        <el-table-column label="设备名称" prop="deviceName" show-overflow-tooltip />
        <el-table-column label="飞行信息" prop="flyInfo" show-overflow-tooltip />

        <el-table-column label="飞手" prop="driver" show-overflow-tooltip />
        <el-table-column label="所属组织" prop="belong" show-overflow-tooltip />
        <el-table-column label="飞行地点" prop="flyplace" show-overflow-tooltip />
        <el-table-column label="备注" prop="content" width="200" show-overflow-tooltip />

        <el-table-column fixed="right" label="操作" align="center" width="100">
          <template #default="scope">
            <el-button type="primary" link @click.stop="openDialog(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </el-card>

    <!-- <EditDialog
      ref="editDialogRef"
      v-model:visible="dialog.visible"
      :title="dialog.title"
      :form-data="formData"
      @submit="resetQuery"
    /> -->
    <DetailDialog v-model:visible="dialog.visible" :form-data="formData" />
  </div>
</template>
<style lang="scss" scoped>
.input-serach {
  width: 200px;
}
.search {
  display: flex;
  align-items: center;
  padding: 24px;
  .search-form {
    flex: 1;
  }
}
</style>
