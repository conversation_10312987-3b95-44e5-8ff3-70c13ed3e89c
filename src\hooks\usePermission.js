import _ from 'lodash';
import { useUserStoreHook } from '@/store/modules/user';

/**
 * 自定义 Hook 用于权限控制。
 */
export default function usePermission() {
  /**
   * 传入单个权限，判断当前用户是否具有该权限。
   * @param {number} auth - 要检查的权限。
   * @returns {boolean} 如果用户具有指定权限，返回 true；否则返回 false。
   */
  function hasPermission(auth) {
    const userStore = useUserStoreHook();

    return _.includes(userStore.roles, auth * 1);
  }

  /**
   * 传入权限数组，判断当前用户是否具有其中任何一个权限。
   * @param {number[]} authList - 要检查的权限数组。
   * @returns {boolean} 如果用户具有数组中的任何一个权限，返回 true；否则返回 false。
   */
  function hasPermissions(authList) {
    const userStore = useUserStoreHook();
    const roleList = userStore.roles;

    return _.some(authList, item => _.includes(roleList, item));
  }

  return {
    hasPermission,
    hasPermissions
  };
}
