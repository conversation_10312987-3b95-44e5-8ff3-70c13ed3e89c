function toRadius(value) {
  return (value * Math.PI) / 180;
}

function toDegree(value) {
  return (value * 180) / Math.PI;
}

/**
 * 根据给定的起始经纬度、距离和方位角，计算出新的经纬度。
 * 这个函数用于地球表面上的地理计算，通过球面三角形的解算，得到在给定距离和方向上的新位置。
 * @param latLng 起始点的经纬度对象，包含latitude和longitude属性。
 * @param distance 从起始点到目标点的距离，单位为米。
 * @param bearing 从起始点到目标点的方位角，单位为度。
 * @returns 返回一个对象，包含计算得到的目标点的纬度和经度。
 */
function getBearingLatLng(latLng, distance, bearing) {
  // 地球平均半径，单位为米
  const R = 6371.393 * 1000;

  // 将距离转换为球面的弧度
  const δ = distance / R;
  // 将方位角转换为弧度
  const θ = toRadius(bearing);

  // 将起始点的纬度和经度转换为弧度
  const φ1 = toRadius(latLng.lat);
  const λ1 = toRadius(latLng.lng);

  // 计算目标点的纬度
  const sinφ2 = Math.sin(φ1) * Math.cos(δ) + Math.cos(φ1) * Math.sin(δ) * Math.cos(θ);
  const φ2 = Math.asin(sinφ2);

  // 计算目标点的经度
  const y = Math.sin(θ) * Math.sin(δ) * Math.cos(φ1);
  const x = Math.cos(δ) - Math.sin(φ1) * sinφ2;
  const λ2 = λ1 + Math.atan2(y, x);

  // 将目标点的纬度和经度转换为度
  const lat = toDegree(φ2);
  const lng = toDegree(λ2);

  // 返回目标点的经纬度对象
  return { lat: lat, lng: lng };
}

/**
 * 根据给定的起点和终点坐标，以及间隔和距离，生成一系列坐标点。
 * 这些坐标点在纬度方向上以固定间隔分布，经度则根据距离和方向计算得出。
 * @param {Object} nw - 西北角坐标，包含latitude和longitude。
 * @param {Object} sw - 东南角坐标，包含latitude和longitude。
 * @param {number} space - 坐标点在纬度上的间隔。
 * @returns {Array} 返回一个包含多个坐标点的对象数组，每个对象包含latitude和longitude。
 */
export function generateCoordinates(nw, sw, space) {
  // 初始化用于存储坐标点的数组
  const coordinates = [];
  // 初始化当前纬度和经度为西北角的坐标值
  let currentLat = nw.lat;
  let currentLng = nw.lng;
  // 当当前纬度大于等于东南角的纬度时，循环继续
  // 循环生成坐标点,直到纬度小于东南角
  while (currentLat >= sw.lat) {
    // 将当前纬度和经度的坐标点添加到数组中
    coordinates.push({ lat: currentLat, lng: currentLng });
    // 根据当前纬度和经度，以及距离和方向计算新的经度
    // 这里假设使用了某个函数getBearingLatLng来完成这个计算
    // 根据新纬度和距离,计算新的经度
    currentLat = getBearingLatLng({ lat: currentLat, lng: currentLng }, space, 180).lat;
  }
  // 将最后一个坐标点也添加进去
  coordinates.push({ lat: sw.lat, lng: currentLng });
  //  return coordinates;
  // 返回生成的坐标点数组
  let steps = coordinates.length;
  let lats = [];
  for (let i = 0; i < steps; i++) {
    lats[i] = coordinates[i].lat;
  }
  return {
    len: steps,
    lat: lats
  };
}
