//#region waypointTurnParam
//航点类型（航点转弯模式）
export const WAY_POINT_TURN_MODE = {
  coordinateTurn: 'coordinateTurn', // 协调转弯,不过点,提前转弯
  toPointAndStopWithDiscontinuityCurvature:
    'toPointAndStopWithDiscontinuityCurvature', // 直线飞行,飞行器到点停
  toPointAndStopWithContinuityCurvature:
    'toPointAndStopWithContinuityCurvature', // 曲线飞行,飞行器到点停
  toPointAndPassWithContinuityCurvature: 'toPointAndPassWithContinuityCurvature' // 曲线飞行,飞行器过点不停
};
export const waypointTurnModeOptions = [
  {
    value: WAY_POINT_TURN_MODE.coordinateTurn,
    label: '协调转弯,不过点,提前转弯'
  },
  {
    value: WAY_POINT_TURN_MODE.toPointAndStopWithDiscontinuityCurvature,
    label: '直线飞行,飞行器到点停'
  },
  {
    value: WAY_POINT_TURN_MODE.toPointAndStopWithContinuityCurvature,
    label: '曲线飞行,飞行器到点停'
  },
  {
    value: WAY_POINT_TURN_MODE.toPointAndPassWithContinuityCurvature,
    label: '曲线飞行,飞行器过点不停'
  }
];
