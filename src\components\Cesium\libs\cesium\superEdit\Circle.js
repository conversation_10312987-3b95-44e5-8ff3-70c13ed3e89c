import * as Cesium from 'cesium';
import {
  addPropertiesToEntity,
  getPropertyByKey,
  calculatePointFromCenter,
  toCartesian3,
  toNumber,
  toDegrees
} from '../common';
import { generateKey } from '@/utils';
import { ElMessageBox } from 'element-plus';

//#region 绘制部分
let viewer = null;
let handler = null;
let labelEntity = null;
const endpointIcon = new URL('@/assets/plan/wrj/endpoint.png', import.meta.url).href;
const midpointIcon = new URL('@/assets/plan/wrj/middlepoint.png', import.meta.url).href;
/**
 * 绘制圆
 * @param {*} viewer viewer 对象
 * @param {*} CallBack 完成后的回调
 */
export const drawCircle = (v, options, CallBack) => {
  if (!v) {
    throw new Error('viewer is required');
  }
  circleDispose();
  viewer = v;
  let drawResultObject = {
    geomType: options.geomType || 'circle',
    flightAreaType: options.flightAreaType || 'dfence',
    id: null,
    center: [],
    centerEntity: null,
    radius: 1,
    entity: null,
    startPosition: null,
    endPosition: null,
    title: options?.title || '',
    action: 'add',
    color: options?.color || '',
    area: 0,
    length: 0
  };
  document.body.style.cursor = 'crosshair'; //
  try {
    handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas); // 选中的经纬度
    // 最后
    handler.setInputAction(click => {
      drawResultObject.id = generateKey();
      let cartesian = viewer.camera.pickEllipsoid(click.position, viewer.scene.globe.ellipsoid);
      drawResultObject.startPosition = cartesian;
      let cartographic = Cesium.Cartographic.fromCartesian(
        cartesian,
        viewer.scene.globe.ellipsoid,
        new Cesium.Cartographic()
      );
      let lng = Cesium.Math.toDegrees(cartographic.longitude);
      let lat = Cesium.Math.toDegrees(cartographic.latitude);
      drawResultObject.center = [lng, lat];
      // 创建一个新的 CallbackProperty 实例
      const semiMinorAxisCb = new Cesium.CallbackProperty(function () {
        return drawResultObject.radius || 0;
      }, false);
      drawResultObject.entity = viewer.entities.add({
        position: new Cesium.Cartesian3.fromDegrees(...drawResultObject.center, 0),
        name: 'circle',
        id: drawResultObject.id,
        label: {
          text: options?.label || '',
          show: true,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: Cesium.VerticalOrigin.CENTER,
          font: '20px monospace', // 字体边框
          outline: true,
          fillColor: Cesium.Color.WHITE,
          outlineWidth: 5,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new Cesium.Cartesian2(0, -30),
          showBackground: true,
          backgroundColor: new Cesium.Color(0.117, 0.117, 0.117, 0.7),
          eyeOffset: new Cesium.Cartesian3(0, 0, 2),
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND //贴地
        },
        // point: {
        //   // 点的大小（像素）
        //   pixelSize: 10,
        //   // 点位颜色，fromCssColorString 可以直接使用CSS颜色
        //   color: Cesium.Color.RED,
        //   // 边框颜色
        //   outlineColor: Cesium.Color.fromCssColorString('#fff'),
        //   // 边框宽度(像素)
        //   outlineWidth: 1,
        //   // 是否显示
        //   show: true,
        //   heightReference: Cesium.HeightReference.CLAMP_TO_GROUND //贴地
        // },
        billboard: {
          image: endpointIcon,
          width: 14,
          height: 14,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          disableDepthTestDistance: Number.POSITIVE_INFINITY // 点总是在最上层
        },
        ellipse: {
          material: options?.color || Cesium.Color.WHITE.withAlpha(0.4),
          outline: true,
          outlineColor: Cesium.Color.RED,
          outlineWidth: 1, // 是否被提供的材质填充
          fill: true,
          semiMajorAxis: semiMinorAxisCb,
          semiMinorAxis: semiMinorAxisCb,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND //贴地
        }
      });
      // 这里移除左键监听
      if (drawResultObject.center) {
        handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
      }
      // 移动则动态绘制
      handler.setInputAction(move => {
        let newCartesian = viewer.camera.pickEllipsoid(move.endPosition, viewer.scene.globe.ellipsoid);
        if (!newCartesian) {
          return;
        }
        drawResultObject.endPosition = newCartesian;
        drawResultObject.radius = Cesium.Cartesian3.distance(cartesian, newCartesian);
        //  计算 cartesian, cartesian2中间点 这里在移动的时添加 一个label 用于展示半径 这里的label 位置在圆心和圆心连线中点
        const midpoint = Cesium.Cartesian3.midpoint(cartesian, newCartesian, new Cesium.Cartesian3());
        // 标注 如果存在了就直接更新位置和 label值
        createOrUpdateLabelEntity(drawResultObject, midpoint);
      }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

    handler.setInputAction(() => {
      handler?.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      handler?.destroy();
      document.body.style.cursor = 'default';
      drawResultObject.area = toNumber(drawResultObject.radius * drawResultObject.radius * Math.PI, 2);
      drawResultObject.length = toNumber(drawResultObject.radius * 2 * Math.PI, 2);
      addPropertiesToEntity(drawResultObject.entity, {
        type: 'circle',
        title: drawResultObject.title || '',
        id: drawResultObject.id,
        center: drawResultObject.center || [],
        flightAreaType: drawResultObject.flightAreaType || 'dfence',
        radius: toNumber(drawResultObject.radius, 2),
        area: toNumber(drawResultObject.radius * drawResultObject.radius * Math.PI, 2),
        length: toNumber(drawResultObject.radius * 2 * Math.PI, 2),
        color: options?.color || Cesium.Color.WHITE.withAlpha(0.4)
      });
      clearLabelEntity();
      CallBack && CallBack(drawResultObject);
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  } catch (error) {}
};

export const circleDispose = () => {
  if (handler && handler.isDestroyed() === false) {
    handler?.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    handler?.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
    handler?.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    handler.destroy();
  }
  document.body.style.cursor = 'default';
};

//#endregion

//#region 编辑部分
export let isCircleEdit = ref(false);
export let originalEntity = ref(null);
export let editCircleEntity = ref(null);
let editCenterPoint = null;
let editBorderPoint = null;
// 适合多有圆形的编辑方法
export const editCircle = (v, circle, CallBack) => {
  // 检查是否满足绘制条件
  if (!v || !circle || isCircleEdit.value || !circle.ellipse) {
    return; // 如果任何条件不满足，则不执行
  }
  try {
    viewer = v;
    originalEntity.value = circle;
    let currentPoint = null;
    let newRadius = 0; // 为了保证 name 唯一 这里使用 generateKey() 生成唯一值
    let uniqueName = generateKey();
    let flightAreaType_ = getPropertyByKey(circle, 'flightAreaType');
    let id_ = getPropertyByKey(circle, 'id');
    let drawResultObject = {
      geomType: 'circle',
      action: 'edit',
      flightAreaType: flightAreaType_ || 'dfence',
      id: id_ || null,
      center: [],
      centerEntity: null,
      radius: 1,
      entity: null,
      startPosition: null,
      endPosition: null,
      title: getPropertyByKey(circle, 'title') || '',
      color: getPropertyByKey(circle, 'color') || '',
      area: 0,
      length: 0
    };
    // 创建圆心
    let properties = circle.properties ?? null;
    if (!properties) {
      return;
    }
    document.body.style.cursor = 'default';
    drawResultObject.startPosition = toCartesian3(properties.center._value);
    //生成圆心编辑点
    editCenterPoint = createPoint(viewer, pointBorderC3, midpointIcon);
    // createPoint(viewer, drawResultObject.startPosition, { pixelSize: 10, outlineWidth: 2 });
    // editCenterPoint.name = 'circleCenterEdit_point';
    // 生成边界点
    let radius = toNumber(properties.radius._value, 2);
    let pointBorderC3 = calculatePointFromCenter(toDegrees(drawResultObject.startPosition), radius);
    if (!pointBorderC3) {
      return;
    }
    editBorderPoint = createPoint(viewer, pointBorderC3, endpointIcon);
    // createPoint(viewer, pointBorderC3, { pixelSize: 15, outlineWidth: 2 });
    editBorderPoint.name = uniqueName;
    drawResultObject.endPosition = pointBorderC3;
    drawResultObject.radius = Cesium.Cartesian3.distance(drawResultObject.startPosition, drawResultObject.endPosition);
    drawResultObject.area = toNumber(drawResultObject.radius * drawResultObject.radius * Math.PI, 2);
    drawResultObject.length = toNumber(drawResultObject.radius * 2 * Math.PI, 2);
    // 这里创建label辅助
    createOrUpdateLabelEntity(
      drawResultObject,
      Cesium.Cartesian3.midpoint(drawResultObject.startPosition, drawResultObject.endPosition, new Cesium.Cartesian3())
    );
    // 事件监听
    circle.handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    //点击事件
    circle.handler.setInputAction(event => {
      isCircleEdit.value = true;
      let pickedObject = viewer.scene.pick(event.position);
      if (Cesium.defined(pickedObject)) {
        if (pickedObject.id.name === uniqueName) {
          currentPoint = pickedObject.id;
        }
      }
      // 这里对外传数据进行窗体变更
      if (isCircleEdit.value) {
        CallBack &&
          CallBack({
            data: drawResultObject,
            circle: circle,
            clickType: 'LEFT_DOWN',
            event: 'update'
          });
      }
    }, Cesium.ScreenSpaceEventType.LEFT_DOWN);
    // 对鼠标移动事件的监听
    circle.handler.setInputAction(event => {
      //获取加载地形后对应的经纬度和高程：地标坐标
      let ray = viewer.camera.getPickRay(event.endPosition);
      let newCartesian = viewer.scene.globe.pick(ray, viewer.scene);
      if (!newCartesian) {
        return;
      }
      if (currentPoint == null) {
        return;
      }
      // 鼠标状态
      let pickedObject = viewer.scene.pick(event.endPosition);
      if (Cesium.defined(pickedObject)) {
        if (pickedObject.id.name === uniqueName) {
          document.body.style.cursor = 'pointer';
        } else {
          document.body.style.cursor = 'default';
        }
      } else {
        document.body.style.cursor = 'default';
      }

      //更新当前点的位置
      currentPoint.position = newCartesian;
      drawResultObject.endPosition = newCartesian;
      drawResultObject.radius = Cesium.Cartesian3.distance(
        drawResultObject.startPosition,
        drawResultObject.endPosition
      );
      drawResultObject.area = toNumber(drawResultObject.radius * drawResultObject.radius * Math.PI, 2);
      drawResultObject.length = toNumber(drawResultObject.radius * 2 * Math.PI, 2);
      //  计算 cartesian, cartesian2中间点 这里在移动的时添加 一个label 用于展示半径 这里的label 位置在圆心和圆心连线中点
      const midpoint = Cesium.Cartesian3.midpoint(
        drawResultObject.startPosition,
        newCartesian,
        new Cesium.Cartesian3()
      );
      // 标注 如果存在了就直接更新位置和 label值
      createOrUpdateLabelEntity(drawResultObject, midpoint);
      //移动的是半径点，则更新半径
      if (currentPoint.name == uniqueName) {
        viewer.scene.screenSpaceCameraController.enableRotate = false;
        viewer.scene.screenSpaceCameraController.enableZoom = false;
        let centerTemp = viewer.scene.globe.ellipsoid.cartesianToCartographic(
          circle.position.getValue(Cesium.JulianDate.now())
        );
        let radiusTemp = viewer.scene.globe.ellipsoid.cartesianToCartographic(
          currentPoint.position.getValue(Cesium.JulianDate.now())
        );
        let geodesic = new Cesium.EllipsoidGeodesic();
        geodesic.setEndPoints(centerTemp, radiusTemp);
        newRadius = geodesic.surfaceDistance;
        circle.ellipse.semiMinorAxis = new Cesium.CallbackProperty(function (time, result) {
          return newRadius;
        }, false);
        circle.ellipse.semiMajorAxis = new Cesium.CallbackProperty(function (time, result) {
          return newRadius;
        }, false);
        editCircleEntity.value = circle;
        // 这里对外传数据进行窗体变更
        if (isCircleEdit.value) {
          CallBack &&
            CallBack({
              data: drawResultObject,
              circle: circle,
              clickType: 'LEFT_UP',
              event: 'update'
            });
        }
      }
      //移动的是圆中心，则更新圆中心
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    // 对鼠标抬起事件的监听
    circle.handler.setInputAction(event => {
      currentPoint = null;
      viewer.scene.screenSpaceCameraController.enableRotate = true;
      viewer.scene.screenSpaceCameraController.enableZoom = true;
    }, Cesium.ScreenSpaceEventType.LEFT_UP);

    // 右击事件的监听
    circle.handler.setInputAction(event => {
      clearLabelEntity();
      isCircleEdit.value = false;
      updateProperty(circle);
      closeCircleEdit(circle);
      addPropertiesToEntity(drawResultObject.entity, {
        type: 'circle',
        title: drawResultObject.title || '',
        id: drawResultObject.id,
        center: drawResultObject.center || [],
        flightAreaType: drawResultObject.flightAreaType || 'dfence',
        radius: toNumber(drawResultObject.radius, 2),
        area: toNumber(drawResultObject.radius * drawResultObject.radius * Math.PI, 2),
        length: toNumber(drawResultObject.radius * 2 * Math.PI, 2),
        color: options?.color || Cesium.Color.WHITE.withAlpha(0.4)
      });
      CallBack &&
        CallBack({
          data: drawResultObject,
          circle: circle,
          clickType: 'RIGHT_CLICK',
          event: 'clear'
        });
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    // 计算圆周上的一个点 在正东方的距离圆心的半径长度位置的点
  } catch (error) {}
};
export const closeCircleEdit = circle => {
  document.body.style.cursor = 'default';
  //移除地图事件
  circle.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOWN);
  circle.handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
  circle.handler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  circle.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_UP);
  isCircleEdit.value = false;
  updateProperty(circle);
  clearLabelEntity();
  return circle;
};
export const cancelCircleEdit = () => {
  if (!originalEntity.value) {
    return;
  }
  if (!editCircleEntity.value) {
    return;
  }
  if (!viewer) {
    return;
  }
  viewer.entities.remove(editCircleEntity.value);
  viewer.entities.add(originalEntity.value);
  originalEntity.value = null;
  editCircleEntity.value = null;
};
/**
 * 更新属性信息
 * @param {*} circle
 */
const updateProperty = circle => {
  circle.properties.radius = circle.ellipse.semiMinorAxis.getValue();
};

//#endregion

//#region 公用部分
/**
 * 米到公里的转换方法
 * @param {*} num
 * @returns
 */

const parseFloat = num => {
  num = toNumber(num, 3);
  let result =
    Number(num) === 0
      ? ''
      : Number(num) > 1000
      ? (Number(num) / 1000).toFixed(2) + ' km'
      : Number(num).toFixed(2) + ' m';
  return result;
};

/**
 * 通用产生采集点
 * @param {*} cartesian
 * @returns
 */
export function createPoint(viewer, cartesian, iconurl = endpointIcon) {
  let point = viewer.entities.add({
    position: toCartesian3(cartesian),
    billboard: {
      image: iconurl || endpointIcon,
      width: 14,
      height: 14,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      disableDepthTestDistance: Number.POSITIVE_INFINITY // 点总是在最上层
    }
  });
  return point;
}
//#endregion

//#region 创建辅助部分

// 清除辅助部分
export function clearLabelEntity() {
  if (!viewer) {
    return;
  }
  if (labelEntity) {
    viewer.entities.remove(labelEntity);
    labelEntity = null;
  }
  if (editBorderPoint) {
    viewer.entities.remove(editBorderPoint);
    editBorderPoint = null;
  }
  if (editCenterPoint) {
    viewer.entities.remove(editCenterPoint);
    editCenterPoint = null;
  }
}
// 创建辅助部分
function createOrUpdateLabelEntity(drawResultObject = null, midpoint = null) {
  if (!viewer || !midpoint || !drawResultObject) {
    return;
  }
  if (labelEntity) {
    labelEntity.position = midpoint;
    labelEntity.label.text = parseFloat(drawResultObject.radius);
  } else {
    labelEntity = viewer.entities.add({
      position: midpoint,
      label: {
        text: parseFloat(drawResultObject.radius),
        show: true,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        font: '30px monospace', // 字体边框
        outline: true,
        fillColor: Cesium.Color.WHITE,
        outlineWidth: 5,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -30),
        showBackground: true,
        backgroundColor: new Cesium.Color(0.117, 0.117, 0.117, 0.7),
        eyeOffset: new Cesium.Cartesian3(0, 0, 2),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND //贴地
      },

      polyline: {
        positions: new Cesium.CallbackProperty(function (time, result) {
          let arr = [drawResultObject.startPosition, drawResultObject.endPosition];
          if (!drawResultObject.startPosition || !drawResultObject.endPosition) {
            return [];
          }
          return arr;
        }, false),
        width: 5,
        material: new Cesium.PolylineOutlineMaterialProperty({
          color: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.CRIMSON,
          outlineWidth: 2
        }),
        clampToGround: true
      }
    });
  }
}

//#endregion
