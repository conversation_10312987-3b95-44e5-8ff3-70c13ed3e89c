<template>
  <el-dialog
    :title="title"
    v-if="visible"
    :model-value="visible"
    width="50%"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form class="app-form" ref="dataFormRef" :model="form" :rules="rules" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备序列号" prop="device_sn">
            <el-input v-model="form.device_sn" placeholder="请输入设备序列号" maxlength="32" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="视频平台" prop="source">
            <el-select v-model="form.source" placeholder="请选择平台来源" clearable style="width: 100%">
              <el-option
                v-for="(item, index) in optionData.gbPlatform"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入设备名称" maxlength="64" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服务器IP" prop="server_ip">
            <el-input v-model="form.server_ip" placeholder="请输入服务器IP" maxlength="64" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="外部视频" prop="online">
            <el-switch v-model="form.online" @change="changeOnline" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服务器端口" prop="server_port">
            <el-input v-model="form.server_port" placeholder="请输入服务器端口" maxlength="64" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="本地端口" prop="local_port">
            <el-input v-model="form.local_port" placeholder="请输入本地端口" maxlength="64" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服务器ID" prop="server_id">
            <el-input v-model="form.server_id" placeholder="请输入服务器ID" maxlength="64" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="通道编码" prop="no">
            <el-input v-model="form.no" placeholder="请输入通道编码" maxlength="64" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="视频类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择视频类型" clearable style="width: 100%">
              <el-option
                v-for="(item, index) in optionData.gbVideoType"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="渠道编码" prop="channel">
            <el-input v-model="form.channel" placeholder="请输入通道编码" maxlength="64" />
          </el-form-item>
        </el-col>
        <el-col :span="12"
          ><el-form-item label="推流方式">
            <el-select v-model="form.url_type" placeholder="请选择推流方式" clearable style="width: 100%" :disabled="form.online">
              <el-option
                v-for="(item, index) in optionData.gbUrlType"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select> </el-form-item
        ></el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="isc索引" prop="index_code">
            <el-input v-model="form.index_code" placeholder="请输入海康isc索引" maxlength="64" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="流传输类型" prop="stream_transfer_type">
            <el-select v-model="form.stream_transfer_type" placeholder="请选择流传输类型" clearable style="width: 100%">
              <el-option
                v-for="(item, index) in optionData.streamTransferType"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="代理密码" prop="agent_password">
            <el-input v-model="form.agent_password" placeholder="请输入代理密码" maxlength="64" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-loading="loading">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { createDeviceVideo, updateDeviceVideo } from '@/api/wayline';
import optionData from '@/utils/option-data';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default(rawProps) {
      return {};
    }
  },
  title: {
    type: String,
    default: ''
  }
});
const form = reactive({});
const dataFormRef = ref(ElForm);

// 表单校验规则
const rules = reactive({
  device_sn: [
    { required: true, message: '请输入设备序列号', trigger: 'blur' },
    { min: 2, max: 32, message: '长度在 2 到 32 个字符', trigger: 'blur' }
  ],
  source: [{ required: true, message: '请选择视频平台', trigger: 'change' }],
  name: [
    { required: true, message: '请输入设备名称', trigger: 'blur' },
    { min: 2, max: 64, message: '长度在 2 到 64 个字符', trigger: 'blur' }
  ],
  type: [{ required: true, message: '请选择视频类型', trigger: 'change' }],
  local_port: [
    { required: true, message: '请输入本地端口', trigger: 'blur' },
    { pattern: /^\d+$/, message: '端口必须为数字', trigger: 'blur' }
  ],
  channel: [{ required: true, message: '请输入通道编码', trigger: 'blur' }],
  // url_type: [{ required: true, message: '请选择推流方式', trigger: 'change' }]
});

watch(
  () => props.visible,
  newVal => {
    if (newVal) {
      // 当对话框打开时，填充表单数据
      Object.assign(form, props.formData);
    }
  }
);

const emit = defineEmits(['update:visible', 'submit']);

const loading = ref(false);

// 关闭弹窗
function closeDialog() {
  resetForm();
  emit('update:visible', false);
}
/**
 * 重置表单
 */
function resetForm() {
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  loading.value = false;
  Object.keys(form).map(key => {
    delete form[key];
  });
}
//外部视频
function changeOnline(val){
  form.online = val;
  if(val){
    form.url_type = '';
  }
}

function handleSubmit() {
  dataFormRef.value.validate(isValid => {
    if (isValid) {
      loading.value = true;

      // 构建提交参数
      const params = {
        id: form.id,
        server_ip: form.server_ip,
        server_port: form.server_port,
        source: form.source,
        device_sn: form.device_sn,
        name: form.name,
        online: form.online,
        server_id: form.server_id,
        no: form.no,
        type: form.type,
        local_port: form.local_port,
        index_code: form.index_code,
        channel: form.channel,
        url_type: form.url_type,
        stream_transfer_type: form.stream_transfer_type,
        agent_password: form.agent_password
      };

      // 根据是title判断是新增还是编辑
      const request = props.title === '编辑设备' ? updateDeviceVideo(params) : createDeviceVideo(params);

      request
        .then(res => {
          loading.value = false;
          ElMessage.success(props.title === '编辑设备' ? '更新成功' : '新增成功');
          closeDialog();
          emit('submit');
        })
        .catch(e => {
          loading.value = false;
          ElMessage.error(e.message || (props.title === '编辑设备' ? '更新失败' : '新增失败'));
        });
    } else {
      loading.value = false;
    }
  });
}

onMounted(() => {});
</script>
