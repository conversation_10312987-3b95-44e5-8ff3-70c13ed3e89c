import {
  EXECUTE_HEIGHT_MODE,
  HEIGHT_MODE_ENUM,
  PAYLOAD_LENS_INDEX,
  SHOOTTYPE_ENUM,
  TEMPLATE_TYPE_ENUM,
  WAY_POINT_HEADING_MODE,
  WAY_POINT_TURN_MODE
} from '@/views/plan/newplan/kmz/props';
import { dataInfoRect, parmsInfoRect } from '../../hocks/index';
import { generateKey } from '@/utils';
import { TemplateDocument, WaylineDocument, TemplatePlacemark, WaylinePlacemark } from '../../kmz/waylines/index.js';
import {
  FLYTO_WAY_LINE_MODE,
  FINISH_ACTION,
  EXIT_ON_RCLOST,
  EXECUTE_RCLOST_ACTION
} from '@/views/plan/newplan/kmz/props';
import { CAMERA_TYPE_ENUM } from '@/config';
import { Action, ActionGroup } from '@/views/plan/newplan/kmz/waylines';
import { ACTION_ACTUATOR_FUNC, ACTION_TRIGGER_TYPE, OPERATION_TYPE } from '@/utils/constants';
import { generateTemplateId, longLat2LatLong, arrTostring } from '@/components/Cesium/libs/cesium';
import { toRaw } from 'vue';
import { useUserStore } from '@/store/modules/user.js';
import { useDeviceStore } from '@/store/modules/device.js';
const deviceStore = useDeviceStore();
const userStore = useUserStore();
let uniqueTemplateId = generateTemplateId();
let templateMissionConfig = null;
// 创建提交数据
export const createUploadJson = () => {
  let uploadJson = {
    template: createTemplateJson(),
    wayLines: createWayLineJson(),
    config: { parmsInfo: toRaw(parmsInfoRect), dataInfo: toRaw(dataInfoRect) }
  };
  return uploadJson;
};
// 构建 template.wpml 文件 完整航线信息
const createTemplateJson = () => {
  let templateDocument = new TemplateDocument();
  templateDocument.wpml_author = userStore.userData.username;
  templateMissionConfig = templateDocument.wpml_missionConfig;
  let devInfo = deviceStore.getCurrentDevice();
  //#region  构建 missionConfig 对象
  let templateMissionConfigOptions = {
    uuid: generateKey(),
    flyToWaylineMode: parmsInfoRect.flyToWaylineMode || FLYTO_WAY_LINE_MODE.safely,
    finishAction: parmsInfoRect.finishAction || FINISH_ACTION.goHome,
    exitOnRCLost: parmsInfoRect.exitOnRCLost || EXIT_ON_RCLOST.goContinue,
    executeRCLostAction: parmsInfoRect.executeRCLostAction || EXECUTE_RCLOST_ACTION.goBack,
    takeOffSecurityHeight: parmsInfoRect.takeOffSecurityHeight || 120,
    takeOffRefPoint: arrTostring(longLat2LatLong(parmsInfoRect.airPortPlace)) ?? '0,0,0',
    globalTransitionalSpeed: parmsInfoRect.globalTransitionalSpeed || 15,
    globalRTHHeight: parmsInfoRect.globalRTHHeight || 100,
    droneInfo: {
      droneEnumValue: devInfo.droneEnumVal ?? 91,
      droneSubEnumValue: devInfo.droneSubEnumVal ?? 1
    },
    payloadInfo: {
      payloadEnumValue: devInfo.payloadEnumVal ?? 81,
      payloadSubEnumValue: devInfo.payloadSubEnumVal ?? 0,
      payloadPositionIndex: 0
    }
  };
  templateMissionConfig.init(templateMissionConfigOptions);
  //#endregion

  //#region  构建 Folder 对象
  let templateFolder = templateDocument.Folder[0];
  let templateFolderOptions = {
    templateId: uniqueTemplateId,
    templateType: TEMPLATE_TYPE_ENUM.mapping2d || parmsInfoRect.templateType,
    autoFlightSpeed: parmsInfoRect.autoFlightSpeed || 10,
    waylineCoordinateSysParam: {
      coordinateMode: 'WGS84',
      globalShootHeight: parmsInfoRect.realFlightHight || parmsInfoRect.flightHight || 100,
      heightMode: parmsInfoRect.heightMode || HEIGHT_MODE_ENUM.EGM96
    },
    payloadParam: {
      payloadPositionIndex: 0,
      imageFormat: parmsInfoRect.cameraType.join(',') || CAMERA_TYPE_ENUM.visable
    }
  };
  templateFolder.init(templateFolderOptions);
  let placemark = new TemplatePlacemark({
    uuid: generateKey(),
    caliFlightEnable: 0,
    elevationOptimizeEnable: 1,
    smartObliqueEnable: 0,
    shootType: parmsInfoRect.shootType || SHOOTTYPE_ENUM.time,
    direction: parmsInfoRect.direction || 0,
    ellipsoidHeight: parmsInfoRect.ellipsoidHeight || 0, // 椭球高 WGS84高
    height: parmsInfoRect.height || 0, //EMG96高
    margin: parmsInfoRect.margin || 0, // 外扩
    overlap: {
      orthoCameraOverlapH: 80,
      orthoCameraOverlapW: 80,
      inclinedCameraOverlapH: 80,
      inclinedCameraOverlapW: 70
    },
    coordinates: convertToCoordinates(dataInfoRect?.originPolygon)
  });
  templateFolder.addPlacemark(placemark);
  //#endregion
  // 这里进行处理
  templateDocument = clearTemplateJson(templateDocument);
  return templateDocument;
};

function convertToCoordinates(inputArr = []) {
  const coordinates = [];
  for (const item of inputArr) {
    coordinates.push([item.lng, item.lat, 0]);
  }
  // const result = coordinates.map(part => part + ' ').join('');
  // // 这里如何
  // console.log('5555555555555555555555555-', result);
  // return result;
  return coordinates.join(',');
}

const clearTemplateJson = json => {
  // 删除对象属性的辅助函数
  const deleteAttributes = (obj, attributes) => {
    attributes.forEach(attr => {
      delete obj[attr];
    });
  };
  (json?.Folder ?? []).forEach(folder => {
    deleteAttributes(folder, ['uuid']);
    (folder?.Placemark ?? []).forEach(placemark => {
      deleteAttributes(placemark, ['uuid']);
      const actionGroup = placemark?.wpml_actionGroup ?? [];
      // 如果 wpml_actionGroup 为空，则删除该属性
      if (actionGroup.length === 0) {
        delete placemark.wpml_actionGroup;
      }
    });
  });
  return json;
};
const clearWaylineJson = json => {
  // 删除对象属性的辅助函数
  const deleteAttributes = (obj, attributes) => {
    attributes.forEach(attr => {
      delete obj[attr];
    });
  };
  (json?.Folder ?? []).forEach(folder => {
    deleteAttributes(folder, ['uuid']);
    (folder?.Placemark ?? []).forEach(placemark => {
      deleteAttributes(placemark, ['uuid']);
      const actionGroup = placemark?.wpml_actionGroup ?? [];
      // 如果 wpml_actionGroup 为空，则删除该属性
      if (actionGroup.length === 0) {
        delete placemark.wpml_actionGroup;
      } else {
        actionGroup?.forEach(g => {
          deleteAttributes(g, ['uuid', 'type', 'placemarkIndex', 'trigger']);
          (g.wpml_action || []).forEach(a => {
            deleteAttributes(a, ['uuid', 'type', 'trigger']);
          });
        });
      }
    });
  });
  return json;
};

// 构建 waylines.wpml 文件 航点动作信息
const createWayLineJson = () => {
  let waylineDocument = new WaylineDocument();
  if (templateMissionConfig) {
    waylineDocument.wpml_missionConfig = templateMissionConfig;
  }
  let waylineFolder = waylineDocument.Folder[0];
  let waylineFolderOptions = {
    templateId: uniqueTemplateId,
    executeHeightMode: getExecuteHeightMode(),
    waylineId: generateTemplateId(),
    autoFlightSpeed: parmsInfoRect.autoFlightSpeed || 10
  };
  waylineFolder.init(waylineFolderOptions);
  // 创建第一个航点 带动作组 只用于第一个
  let firstPoint = dataInfoRect.positions[0];
  let firstPlaceMarskOptions = {
    coordinates: `${firstPoint[0]},${firstPoint[1]}`,
    index: 0,
    executeHeight: parmsInfoRect.realFlightHight,
    waypointSpeed: parmsInfoRect.autoFlightSpeed,
    waypointHeadingParam: { waypointHeadingMode: WAY_POINT_HEADING_MODE.followWayline },
    waypointTurnParam: { waypointTurnMode: WAY_POINT_TURN_MODE.toPointAndStopWithContinuityCurvature }
  };
  let firstPlaceMarsk = new WaylinePlacemark(firstPlaceMarskOptions);
  // 创建动作组调整飞机状态
  let ag = createActionGroup();
  firstPlaceMarsk.addActionGroup(ag);
  waylineFolder.addPlacemark(firstPlaceMarsk);
  // 创建第二个航点 模板 该模板将会用到最后
  (dataInfoRect.positions || []).forEach((position, index) => {
    if (index === 0) return; // 跳过第一个元素
    let opt = {
      coordinates: `${position[0]},${position[1]}`,
      index: index,
      executeHeight: parmsInfoRect.realFlightHight,
      waypointSpeed: parmsInfoRect.autoFlightSpeed,
      waypointHeadingParam: { waypointHeadingMode: WAY_POINT_HEADING_MODE.followWayline },
      waypointTurnParam: { waypointTurnMode: WAY_POINT_TURN_MODE.toPointAndStopWithContinuityCurvature }
    };
    let otherPlaceMarsk = new WaylinePlacemark(opt);
    waylineFolder.addPlacemark(otherPlaceMarsk);
  });
  // 这里进行处理
  waylineDocument = clearWaylineJson(waylineDocument);
  return waylineDocument;
};

// 获取执行模式 该方法仅在waylines.wpml中使用
const getExecuteHeightMode = () => {
  if (parmsInfoRect.heightMode === HEIGHT_MODE_ENUM.EGM96) {
    return EXECUTE_HEIGHT_MODE.WGS84;
  } else if (parmsInfoRect.heightMode === HEIGHT_MODE_ENUM.relativeToStartPoint) {
    return EXECUTE_HEIGHT_MODE.relativeToStartPoint;
  } else if (parmsInfoRect.heightMode === HEIGHT_MODE_ENUM.realTimeFollowSurface) {
    return EXECUTE_HEIGHT_MODE.realTimeFollowSurface;
  } else {
    return EXECUTE_HEIGHT_MODE.WGS84;
  }
};

// 创建动作组

const createActionGroup = () => {
  let maxIndex = dataInfoRect.positions.length || 1; // 也是动作的结束索引位
  // 1 拍照动作
  const ac1 = new Action({
    actionId: 0,
    actionActuatorFunc: ACTION_ACTUATOR_FUNC.gimbalRotate,
    actionActuatorFuncParam: getGimbalYawRotateActionDefaultParam(),
    uuid: generateKey(), // 动作id
    trigger: ACTION_TRIGGER_TYPE.reachPoint // 动作触发类型
  });
  // 2 拍照动作
  const ac2 = new Action({
    actionId: 1,
    actionActuatorFunc: ACTION_ACTUATOR_FUNC.takePhoto,
    actionActuatorFuncParam: getTakePhoteActionDefaultParam(),
    uuid: generateKey(), // 动作id
    trigger: ACTION_TRIGGER_TYPE.multipleTiming // 动作触发类型
  });

  let { actionTrigger } = getMultipleTimingActionGroupDefaultParam();
  const ag = new ActionGroup({
    actionGroupId: 0,
    actionGroupStartIndex: 0,
    actionGroupEndIndex: maxIndex - 1,
    actionGroupMode: 'sequence',
    actionTriggerType: actionTrigger?.wpml_actionTriggerType || 'multipleTiming',
    actionTriggerParam: actionTrigger?.wpml_actionTriggerParam || null,
    actions: [],
    uuid: generateKey(),
    type: OPERATION_TYPE.interval
  });
  ag && ag.addAction(ac1);
  ag && ag.addAction(ac2);

  return ag;
};

///TODO...1015 设置拍照默认参数 这里相机类型选择要根据实际的选择情况
export function getTakePhoteActionDefaultParam() {
  let camers = deviceStore.getWayLineCameraType() ?? [];
  let selectedCameras = camers.join(',');
  const actionActuatorFuncParam = {
    wpml_payloadPositionIndex: 0,
    wpml_useGlobalPayloadLensIndex: 1,
    wpml_payloadLensIndex: selectedCameras ?? `${PAYLOAD_LENS_INDEX.visable}`
  };
  return actionActuatorFuncParam;
}

// 获取悬停默认参数
export function getGimbalYawRotateActionDefaultParam() {
  const actionActuatorFuncParam = {
    wpml_payloadPositionIndex: 0,
    wpml_gimbalHeadingYawBase: 'aircraft', // 北面为基准方面
    wpml_gimbalRotateMode: 'absoluteAngle',
    wpml_gimbalPitchRotateEnable: 1,
    wpml_gimbalPitchRotateAngle: -90,
    wpml_gimbalRollRotateEnable: 0,
    wpml_gimbalRollRotateAngle: 0,
    wpml_gimbalYawRotateEnable: 1,
    wpml_gimbalYawRotateAngle: 0,
    wpml_gimbalRotateTimeEnable: 0,
    wpml_gimbalRotateTime: 0
  };
  return actionActuatorFuncParam;
}

export function getMultipleTimingActionGroupDefaultParam() {
  return {
    actionTrigger: {
      wpml_actionTriggerType: ACTION_TRIGGER_TYPE.multipleTiming,
      wpml_actionTriggerParam: 2.5
    },
    action: {
      wpml_actionActuatorFunc: ACTION_ACTUATOR_FUNC.takePhoto,
      wpml_actionActuatorFuncParam: {
        wpml_payloadPositionIndex: 0,
        wpml_useGlobalPayloadLensIndex: 1,
        wpml_payloadLensIndex: `${PAYLOAD_LENS_INDEX.visable}`
      }
    }
  };
}
export { createTemplateJson, createWayLineJson };
