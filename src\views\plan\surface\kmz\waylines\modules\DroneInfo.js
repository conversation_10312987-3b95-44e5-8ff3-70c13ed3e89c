import { isDroneEnumValueValid } from '@/views/plan/newplan/kmz/hocks';

class DroneInfo {
  constructor(options = {}) {
    let hasSubEnumValue = false;
    if (!options.droneEnumValue) {
      hasSubEnumValue = false;
      this.wpml_droneEnumValue = this.wpml_droneSubEnumValue = null;
    } else {
      hasSubEnumValue = isDroneEnumValueValid(options.droneEnumValue); // 返回 true  /  false
      this.wpml_droneEnumValue = hasSubEnumValue ? options.droneEnumValue : null;
      this.wpml_droneSubEnumValue = hasSubEnumValue ? options.droneSubEnumValue : null;
    }
  }

  setDroneEnumValue(value) {
    this.wpml_droneEnumValue = value;
  }
  getDroneEnumValue() {
    return this.wpml_droneEnumValue;
  }

  setDroneSubEnumValue(value) {
    this.wpml_droneSubEnumValue = value;
  }
  getDroneSubEnumValue() {
    return this.wpml_droneSubEnumValue;
  }
}
export { DroneInfo };
