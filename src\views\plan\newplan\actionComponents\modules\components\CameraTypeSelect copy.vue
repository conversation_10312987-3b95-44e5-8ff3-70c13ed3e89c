<template>
  <div class="camera-select-wrapper">
    <div class="camera-select">
      <div class="left">
        <div v-for="item in dataRefComp">
          <div
            :disabled="item.active"
            class="left-item"
            :class="{
              active: item.active,
              notAllowed: item.notAllowed
            }"
            @click="onClickHandle(item)"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
      <div class="right">
        <div class="right-item" :class="{ active: isFollowWayline === true }" @click="onSetClickFollowWayline">
          跟随航线
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'CameraTypeSelect'
};
</script>
<script setup>
import { onMounted, onUnmounted, computed } from 'vue';
import { getPayloadLensIndex, PAYLOAD_LENS_INDEX_CN_NAME } from '../../../kmz/props';

//#region 数据双向绑定
// const data = {
//   deviceType: '间隔距离',
//   wpml_payloadLensIndex: '',
//   wpml_useGlobalPayloadLensIndex: "",
// };
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
const data = computed({
  get: () => props.modelValue,
  set: value => {
    emits('update:modelValue', value);
  }
});
const dataRef = reactive([]);
// 对外定义事件
const emits = defineEmits(['update:data', 'cameraTypeChange']); // 触发事件

const isFollowWayline = computed(() => {
  return data.value.wpml_useGlobalPayloadLensIndex === 1;
});
//#region 初始化

//#region 方法
const dataRefComp = computed(() => {
  // 初始化dataRef  清空数组 保持响应式
  dataRef.length = 0;
  // 根据设备编号获取对应支持的拍照方式
  const payloadLensIndexList = getPayloadLensIndex(data.value.deviceType);
  // 这里判断 wpml_payloadLensIndex 和 payloadLensIndexList 的匹配关系 wpml_payloadLensIndex 未"visible、ir"
  let payloadLensList = parseWpmlPayloadLensIndex(data.value.wpml_payloadLensIndex) || [];
  // 这里检查是否跟随 如果 都存在那么 就是跟随 反之就是单个被高亮
  let followWayline = false;
  for (const key in payloadLensIndexList) {
    let hasPayLoad = payloadLensList.find(item => {
      return item === key;
    });
    if (!hasPayLoad) {
      followWayline = false;
    }
  }

  // 遍历对象构建 设置初始值这里设置默认值
  for (const key in payloadLensIndexList) {
    let hasPayLoad = payloadLensList.find(item => {
      return item === key;
    });
    if (isFollowWayline) {
      hasPayLoad = false;
    }
    let cnname = PAYLOAD_LENS_INDEX_CN_NAME[key] || null;
    cnname &&
      dataRef.push({
        value: key, // wide,ir...
        label: cnname, // 中文名称
        data: null,
        notAllowed: true, // 是否允许点击
        active: hasPayLoad || false // 是否激活
      });
  }
  return dataRef;
});
function parseWpmlPayloadLensIndex(value) {
  if (!value) {
    return null;
  } else {
    // 值可能为 "visible, ir" 或 "visible" 这种方式
    const lensTypes = value.split(',').map(type => type.trim());
    console.log('lensTypes', lensTypes);
    // 返回数组
    return lensTypes;
  }
}
const onClickHandle = item => {
  // 点击时获取当前点击项
  if (isFollowWayline.value) {
    return;
  }

  const curActive = item.active;
  if (curActive) {
    // 如果当前是取消激活状态 就要检查当前选中的数据个数 如果 = 1 则不往下进行
    const isActiveCountGreaterThanOne = checkActiveCount(dataRef);
    if (!isActiveCountGreaterThanOne) {
      return;
    }
  }

  // 找到当前的 支持类型
  let curValue = item.value;
  dataRef?.forEach(item => {
    if (item.value === curValue) {
      item.active = !curActive;
      item.notAllowed = false;
    }
  });
  // 设置 最新的 parms.wpml_payloadLensIndex
  let _wpml_payloadLensIndex = [];
  dataRef?.forEach(item => {
    if (item.active) {
      _wpml_payloadLensIndex.push(item.value);
    }
  });
  data.value.wpml_payloadLensIndex = _wpml_payloadLensIndex.join(',');
  emits('cameraTypeChange', data.value);
};

function checkActiveCount(arr = []) {
  let activeCount = 0;
  for (let i = 0; i < arr.length; i++) {
    if (arr[i].active === true) {
      activeCount++;
    }
  }
  return activeCount > 1;
}
const onSetClickFollowWayline = () => {
  // 点击时获取当前点击项
  if (isFollowWayline.value) {
    // 其他被选中
    unSelecteFollowWayline();
  } else {
    // 跟随被选中
    selectedFollowWayline();
  }

  emits('cameraTypeChange', data.value);
};

// 取消选中跟随航点 设置其他类型
const unSelecteFollowWayline = () => {
  // 选中跟随 设置该参数下全局设置
  data.value.wpml_useGlobalPayloadLensIndex = 0;
  const wpml_payloadLensIndex = data.value.wpml_payloadLensIndex;
  // // 设置界面显示  选中跟随
  const arr = wpml_payloadLensIndex.split(',');
  for (let i = 0; i < arr.length; i++) {
    dataRef?.forEach(data => {
      if (data.value === arr[i]) {
        data.active = true;
        data.notAllowed = false;
      }
    });
  }
};

// 如果选中跟随航点
const selectedFollowWayline = () => {
  // 选中跟随 设置该参数下全局设置
  data.value.wpml_useGlobalPayloadLensIndex = 1;
  let _wpml_payloadLensIndex = '';
  dataRef?.forEach(data => {
    _wpml_payloadLensIndex += data.value + ',';
  });
  data.value.wpml_payloadLensIndex = _wpml_payloadLensIndex;
  // 设置
  dataRef?.forEach(data => {
    data.active = false;
    data.notAllowed = true;
  });
};
//#endregion

onMounted(() => {});
onUnmounted(() => {});
</script>
<style lang="scss" scoped>
.camera-select-wrapper {
  width: 100%;
  padding-bottom: 10px;
  // padding: 10px 0px;
  // background-color: rgb(49, 49, 49);
  background-color: #11253e !important;
  color: white;
  user-select: none;
  .camera-select {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      flex-wrap: wrap;
      .left-item {
        width: auto;
        height: 30px;
        padding: 2px 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        background: rgba(45, 140, 240, 0.35);
        color: #ffffff40;
        margin: 5px 2px;
        user-select: none;
        &:hover {
          cursor: pointer;
        }
      }
    }
    .right {
      display: flex;
      justify-content: center;
      align-items: flex-start;
      .right-item {
        width: 80px;
        height: 30px;
        padding: 2px 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        background: rgba(45, 140, 240, 0.35);
        color: #ffffff40;
        margin: 5px 2px;
        user-select: none;
        &:hover {
          cursor: pointer;
        }
      }
    }
  }
}
.active {
  color: #fff !important;
  background: #2d8cf0 !important;
}
.notAllowed {
  cursor: not-allowed !important;
}
</style>
