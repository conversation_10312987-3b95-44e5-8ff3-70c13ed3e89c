export default {
  data() {
    return {
      toolbarConfig: {
        modalAppendToBody: true
      },
      editorConfig: {
        placeholder: '请输入内容...',

        autoFocus: false,
        MENU_CONF: {
          uploadImage: {
            // 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
            allowedFileTypes: ['image/*'],
            base64LimitSize: 5 * 1024 * 1024,
            // 自定义上传
            async customUpload(file) {
              // JS 语法
              console.log(file);
              // file 即选中的文件
              // 自己实现上传，并得到图片 url(图片 src 必须) alt(图片描述文字，非必须) href(图片的链接，非必须)
              // 最后插入图片
              // insertFn(url, alt, href)
            }
          }
        }
      },
      mode: 'default' // or 'simple'
    };
  },

  methods: {}
};
