<template>
  <div class="Hover-wrapper">
    <Slide v-model="dataRef" @changeHandle="onChangeHandle" />
  </div>
</template>
<script>
export default {
  name: 'RotateYaw'
};
</script>
<script setup>
import { onMounted, onUnmounted, defineExpose, reactive } from 'vue';
import Slide from './components/Slide.vue';
import { updateFrustumWithActionValue } from '../../kmz/hocks';
import { ACTION_ACTUATOR_FUNC } from '@/utils/constants';
import { toNumber } from '@/components/Cesium/libs/cesium';
import { useEditTrackerStore } from '@/store/modules/dataEditTracker.js';
const editTrackerStore = useEditTrackerStore();
//#region 初始化
const dataInfo = reactive({
  deviceInfo: null,
  action: null,
  deviceType: 'Matrice_3TD'
});
const dataRef = reactive({
  title: '飞行器偏航角',
  unit: '°',
  acttionType: ACTION_ACTUATOR_FUNC.rotateYaw,
  min: -180,
  max: 180,
  value: 0.0 // 默认最小单位值
});
//#endregion

//#region 方法

// 数据变更后进行修改
const onChangeHandle = v => {
  // 获取动作中的参数进行涉资
  if (dataInfo.action) {
    dataInfo.action.wpml_actionActuatorFuncParam.wpml_aircraftHeading = v;
  }
  // 更新视锥体状态
  const options = {
    action: dataInfo.action,
    actionUuid: dataInfo.action.uuid,
    type: ACTION_ACTUATOR_FUNC.rotateYaw,
    min: dataRef.min,
    max: dataRef.max,
    value: v // 默认最小单位值
  };
  editTrackerStore.dataTracker.markAsModified();
  updateFrustumWithActionValue(options);
};
/**
 * 设置组件数据
 * @param {*} options
 * @param {*} options.actionFuncParam // 动作参数
 * @param {*} options.deviceInfo // 设备信息
 * @param {*} options.action // 动作对象
 */
const setComponentData = options => {
  // 设置数据前先初始组件数据及界面
  const { actionFuncParam, action, deviceInfo } = options;
  //  获取设备信息
  const { droneSubEnumLabel } = deviceInfo;
  // 设备信息及型号
  dataInfo.deviceInfo = deviceInfo;
  dataInfo.action = action;
  dataInfo.deviceType = droneSubEnumLabel;
  const { wpml_aircraftHeading = 0 } = actionFuncParam;
  // 设置当前的组件数据
  dataRef.value = toNumber(wpml_aircraftHeading, 1);
};
const updateComponentValue = options => {
  // 设置数据前先初始组件数据及界面
  const { value = 0 } = options;
  dataRef.value = value;
  //  获取设备信息
};
const getComponentData = () => {
  return dataInfo;
};

//#endregion

//#region 对外抛出方法
defineExpose({
  setComponentData,
  getComponentData,
  updateComponentValue
});
//#endregion
//#region 生命周期
onMounted(() => {});
onUnmounted(() => {});
//#endregion
</script>
<style lang="scss" scoped>
.Hover-wrapper {
}
</style>
