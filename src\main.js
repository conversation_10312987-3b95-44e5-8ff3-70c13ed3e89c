import { createApp } from 'vue';
import ElementPlus from 'element-plus';
import eventbus from '@/utils/eventbus';
import App from './App.vue';

import router from '@/router';
import pilotRouter from './router/pilotIndex'
import { setupStore } from '@/store';
import { setupDirective } from '@/directive';
import dispatchEventStroage from '@/utils/dispatchEventStroage';
import '@/permission';
import VConsole from 'vconsole';
import VueLazyload from 'vue-lazyload';
import emptyIcon from '@/assets/loading.png';
// 本地SVG图标
import 'virtual:svg-icons-register';

// 国际化
import i18n from '@/lang/index';

// 样式
import 'element-plus/dist/index.css';
import '@/styles/index.scss';
import 'uno.css';
if (import.meta.env.VITE_APP_NODE_ENV === 'development' || import.meta.env.VITE_APP_NODE_ENV === 'pilot') { // 如果为开发模式, 则注入 vConsole, 预防正式会不小心忘记删除
    new VConsole();
}
// import '@/styles/element/index.scss';

const app = createApp(App);
// 使用 vue-lazyload
app.use(VueLazyload, {
    preLoad: 1.3,
    loading: emptyIcon, // 可选：加载时显示的图片
    attempt: 1 // 可选：加载失败后尝试加载的次数
});
// 全局注册 自定义指令(directive)
setupDirective(app);
// 全局注册 状态管理(store)
setupStore(app);
window.$bus = new eventbus();
// 监听本地缓存所需
app.use(dispatchEventStroage);
if(import.meta.env.VITE_APP_NODE_ENV === 'pilot') {
    app.use(pilotRouter).use(i18n).use(ElementPlus).mount('#app');
}else {
    app.use(router).use(i18n).use(ElementPlus).mount('#app');
}
