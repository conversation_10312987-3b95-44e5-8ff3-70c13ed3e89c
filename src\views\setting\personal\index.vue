<!-- <script>
export default {
  name: 'personalizedSetting'
};
</script> -->

<script setup>
import { ElMessage } from 'element-plus';
import { getDeptSysSetting, updatePersonalSetting, uploadLOGO } from '@/api/wayline';
import { onMounted, onBeforeUnmount, ref, reactive, nextTick, onDeactivated } from 'vue';
import { useUserStore } from '@/store/modules/user';
import * as Cesium from 'cesium';
import {
  getOrCreateCesiumEngineInstance,
  delCesiumEngineInstance,
  CesiumLayerManager,
  imglayer,
  cialayer
} from '@/components/Cesium/libs/cesium/index';

const userStore = useUserStore();

const dataFrom = ref(ElForm); // 数据源表单
const isDataLoaded = ref(false); // 添加数据加载状态
const mapDialogVisible = ref(false);
const selectedCoordinates = ref(null);
const mapHandler = ref(null);
let cesiumEngine = null;
const instanceId = ref(`coordinateSelector_${Date.now()}`); // 生成唯一实例ID
const rules = reactive({
  name: [{ required: true, message: '请输入平台名称', trigger: 'blur' }],
  tdtToken: [{ required: true, message: '请输入天地图Token', trigger: 'blur' }],
  tdtUrl: [{ required: true, message: '请输入天地图地址', trigger: 'blur' }],
  wanyline_latitude: [{ required: true, message: '请输入初始点纬度,配置初始位置及起飞点', trigger: 'change' }],
  wanyline_longitude: [{ required: true, message: '请输入初始点经度,配置初始位置及起飞点', trigger: 'change' }],
  wanyline_height: [{ required: true, message: '请输入初始点高度,配置初始位置及起飞点', trigger: 'blur' }]
});
const form = reactive({});
const defaultLogoIcon = new URL('/resource/images/default-logo.jpeg', import.meta.url).href;;
// 默认logo图片路径
function handleImageError() {
  form.image = defaultLogoIcon;
}
onMounted(() => {
  initList();
});
function initList() {
  getDeptSysSetting({}).then(res => {
    const { base_config = {}, position_config = {}, wayline_config = {} } = res;
    form.name = base_config.sys_name;
    form.image = base_config.sys_logo_url;
    form.uploadImg = base_config.sys_logo;
    form.showAi = base_config.show_ai;

    // form.longitude = position_config.longitude;
    // form.latitude = position_config.latitude;
    form.terrainMapUrl = position_config.terrain_map_url;
    form.terrainOnline = position_config.terrain_online;
    form.tdtUrl = position_config.tdt_url;
    form.tdtToken = position_config.tdt_token;

    form.wanyline_longitude = wayline_config.longitude;
    form.wanyline_latitude = wayline_config.latitude;
    form.wanyline_height = wayline_config.height;

    isDataLoaded.value = true; // 数据加载完成后设置状态
  });
}
/**
 * 限制用户上传文件的格式和大小
 */
function handleBeforeUpload(file) {
  const imgType = ['image/png', 'image/jpeg', 'image/svg+xml'];
  if (imgType.indexOf(file.type) == -1) {
    ElMessage.error('请上传图片正确格式的图片');
    return false;
  }
  if (file.size > 10 * 1048 * 1048) {
    ElMessage.warning('上传图片不能大于10M');
    return false;
  }
  return true;
}

/**
 * 自定义图片上传
 *
 * @param options
 */
async function uploadFile(options) {
  const formData = new FormData();
  formData.append('file', options.file);
  uploadLOGO(formData).then(res => {
    form.uploadImg = res.object_key;
    form.image = res.file_url;
  });
}

function submitForm() {
  dataFrom.value.validate(isValid => {
    if (isValid) {
      if (!form.uploadImg) {
        ElMessage.error('请先上传LOGO');
        return;
      }
      updatePersonalSetting({
        base_config: {
          sys_name: form.name || '',
          sys_logo: form.uploadImg || '',
          show_ai: form.showAi || false
        },
        position_config: {
          terrain_map_url: form.terrainMapUrl || '',
          terrain_online: form.terrainOnline || false,
          tdt_url: form.tdtUrl || '',
          tdt_token: form.tdtToken || ''
          // longitude: form.longitude || '',
          // latitude: form.latitude || ''
        },
        wayline_config: {
          longitude: form.wanyline_longitude || '',
          latitude: form.wanyline_latitude || '',
          height: form.wanyline_height || ''
        }
      }).then(res => {
        ElMessage.success('更新成功');
      });
    }
  });
}

// 打开地图选点
function openMapDialog(isOpen) {
  mapDialogVisible.value = isOpen;
  nextTick(() => {
    if (isOpen) {
      initCesiumMap();
    }
  });
}

// 初始化Cesium地图
function initCesiumMap() {
  try {
    cesiumEngine = getOrCreateCesiumEngineInstance(instanceId.value);
    if (!cesiumEngine) {
      console.error('Failed to create Cesium engine instance');
      mapDialogVisible.value = false;
      return;
    }

    cesiumEngine?.init('cesiumMapContainer');
    const layerManager = new CesiumLayerManager(cesiumEngine.viewer);
    layerManager.addLayer(imglayer);
    layerManager.addLayer(cialayer);

    // 如果已有经纬度，飞到该位置
    if (form.wanyline_longitude && form.wanyline_latitude) {
      const longitude = parseFloat(form.wanyline_longitude);
      const latitude = parseFloat(form.wanyline_latitude);
      selectedCoordinates.value = {
        longitude: longitude,
        latitude: latitude
      };
      if (!isNaN(longitude) && !isNaN(latitude)) {
        cesiumEngine.viewer.camera.flyTo({
          destination: Cesium.Cartesian3.fromDegrees(longitude, latitude, 1000)
        });
        cesiumEngine.viewer.entities.add({
          position: Cesium.Cartesian3.fromDegrees(longitude, latitude),
          point: {
            pixelSize: 15,
            color: Cesium.Color.RED,
            outlineColor: Cesium.Color.WHITE,
            outlineWidth: 2
          },
          label: {
            text: `经度: ${longitude}, 纬度: ${latitude}`,
            font: '16px sans-serif bold',
            fillColor: Cesium.Color.BLACK,
            outlineColor: Cesium.Color.WHITE,
            outlineWidth: 2,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            pixelOffset: new Cesium.Cartesian2(0, -20)
          }
        });
      }
    }

    setupMapClickEvent(cesiumEngine.viewer);
  } catch (error) {
    console.error('Cesium initialization error:', error);
    mapDialogVisible.value = false;
  }
}

// 设置地图点击事件
function setupMapClickEvent(viewer) {
  if (mapHandler.value) {
    mapHandler.value.destroy();
    mapHandler.value = null;
  }

  mapHandler.value = new Cesium.ScreenSpaceEventHandler(viewer.canvas);
  mapHandler.value.setInputAction(function (event) {
    const ray = viewer.camera.getPickRay(event.position);
    const cartesian = viewer.scene.globe.pick(ray, viewer.scene);

    if (Cesium.defined(cartesian)) {
      const cartographic = viewer.scene.globe.ellipsoid.cartesianToCartographic(cartesian);
      const longitude = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6);
      const latitude = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6);

      selectedCoordinates.value = {
        longitude: longitude,
        latitude: latitude
      };

      viewer.entities.removeAll();
      viewer.entities.add({
        position: cartesian,
        point: {
          pixelSize: 15,
          color: Cesium.Color.RED,
          outlineColor: Cesium.Color.WHITE,
          outlineWidth: 2
        },
        label: {
          text: `经度: ${longitude}, 纬度: ${latitude}`,
          font: '16px sans-serif bold',
          fillColor: Cesium.Color.BLACK,
          outlineColor: Cesium.Color.WHITE,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new Cesium.Cartesian2(0, -20)
        }
      });
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
}

// 处理坐标选择确认
function handleCoordinateConfirm(coordinates) {
  if (coordinates) {
    form.wanyline_longitude = coordinates.longitude;
    form.wanyline_latitude = coordinates.latitude;
    mapDialogVisible.value = false;
    delCesiumEngineInstance(instanceId.value);
  }
}

// 组件销毁前清理资源
onBeforeUnmount(() => {
  delCesiumEngineInstance(instanceId.value);
});

// 组件被隐藏时清理资源
onDeactivated(() => {
  delCesiumEngineInstance(instanceId.value);
});

// 监听地图显示状态
watch(
  () => mapDialogVisible.value,
  newVal => {
    if (!newVal) {
      delCesiumEngineInstance(instanceId.value);
    }
  }
);
</script>

<template>
  <div class="app-container column-container">
    <div class="app-main-content">
      <div class="settings-layout">
        <el-form
          class="app-form"
          ref="dataFrom"
          :model="form"
          :rules="rules"
          label-width="120px"
          style="max-width: 600px"
        >
          <el-divider content-position="center">基础设置</el-divider>
          <el-form-item label="平台名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入"
              maxlength="10"
              @blur="form.name = $event.target.value.trim()"
            />
          </el-form-item>
          <el-form-item label="LOGO" required>
            <div>
              <el-upload
                class="upload-area"
                v-model="form.uploadImg"
                drag
                :show-file-list="false"
                list-type="picture-card"
                accept="image/*"
                :before-upload="handleBeforeUpload"
                :http-request="uploadFile"
              >
                <template #trigger>
                  <div class="upload-tip-box">
                    <i-ep-uploadFilled class="el-icon-upload" />
                    <div class="el-upload__text">将图片拖到此处</div>
                  </div>
                </template>
                <template #tip>
                  <div class="el-upload__tip">建议上传1:1的图片，例如：40*40</div>
                </template>
              </el-upload>
            </div>
            <div v-if="form.image" class="mt-[16px]" style="min-width: 300px">
              <el-image
                style="max-width: 300px; max-height: 300px"
                :src="form.image"
                :previewSrcList="[form.image]"
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                fit="cover"
                @error="handleImageError"
              />
            </div>
          </el-form-item>
          <el-divider content-position="center">地图设置</el-divider>
          <el-form-item label="地图Token" prop="tdtToken" v-if="userStore.userData.is_super_admin">
            <el-input v-model="form.tdtToken" placeholder="请输入" maxlength="50" />
          </el-form-item>
          <el-form-item label="地图地址" prop="tdtUrl" v-if="userStore.userData.is_super_admin">
            <el-input v-model="form.tdtUrl" placeholder="请输入" maxlength="50" />
          </el-form-item>
          <el-row :gutter="20" v-if="userStore.userData.is_super_admin">
            <el-col :span="12">
              <el-form-item label="在线地形" prop="terrainOnline">
                <el-switch v-model="form.terrainOnline" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="AI展示" prop="showAi">
                <el-switch v-model="form.showAi" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="地形地址" prop="terrainMapUrl" v-if="userStore.userData.is_super_admin">
            <el-input v-model="form.terrainMapUrl" placeholder="请输入" maxlength="50" />
          </el-form-item>
          <el-form-item label="初始高度" prop="wanyline_height">
            <el-input v-model="form.wanyline_height" placeholder="请输入" maxlength="30" />
          </el-form-item>
          <el-row :gutter="2">
            <el-col :span="9">
              <el-form-item label="初始经度" prop="wanyline_longitude">
                <el-input v-model="form.wanyline_longitude" placeholder="请输入" maxlength="30" />
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item label="初始纬度" prop="wanyline_latitude">
                <el-input v-model="form.wanyline_latitude" placeholder="请输入" maxlength="30" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <div style="padding-left: 40px">
                <el-button type="primary" @click="openMapDialog(true)">地图选点</el-button>
              </div>
            </el-col>
          </el-row>
          <div class="personal-btns">
            <el-button type="primary" @click="submitForm">更新</el-button>
          </div>
        </el-form>

        <!-- 地图组件 -->
        <div class="map-section" v-if="mapDialogVisible">
          <div class="map-header">
            <h3>地图选点</h3>
            <div>
              <el-button @click="openMapDialog(false)">取消</el-button>
              <el-button
                type="primary"
                @click="handleCoordinateConfirm(selectedCoordinates)"
                :disabled="!selectedCoordinates"
                >确认</el-button
              >
            </div>
          </div>
          <div class="map-container">
            <div id="cesiumMapContainer" class="cesium-container"></div>
            <div class="map-controls">
              <div class="coordinates-display">
                <div v-if="selectedCoordinates">
                  <p>已选择坐标：</p>
                  <p>经度：{{ selectedCoordinates.longitude }}</p>
                  <p>纬度：{{ selectedCoordinates.latitude }}</p>
                </div>
                <p v-else>请在地图上点击选择坐标点</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.upload-top {
  display: flex;
}
.el-upload__tip {
  color: #98a2b3;
}
:deep(.el-upload--picture-card) {
  width: 100%;
}
.upload-btn {
  position: absolute;
  padding: 8px 8px;
  top: -48px;
  left: 0;
}
.upload-area {
  width: 480px;
  position: relative;
}
.upload-tip-box {
  text-align: center;
}
.save-box {
  height: 60px;
  line-height: 60px;
  text-align: right;
}
.role-name {
  font-size: 14px;
  color: #2a8b7d;
  text-align: left;
  line-height: 22px;
  font-weight: 400;
}
.personal-btns {
  margin-left: 120px;
  margin-top: 20px;
}

.user-tree-title {
  padding-right: 88px;
  .user-tree-icon {
    display: none;
  }
  &:hover {
    .user-tree-icon {
      display: block;
    }
  }
}
.tree-limit {
  :deep(.el-tree) {
    .el-tree-node.is-current > .el-tree-node__content {
      background: #e3eef5;
      .user-tree-title {
        color: var(--el-color-primary) !important;
      }
    }
    .el-tree-node__content {
      width: 284px;
      height: 40px;
      line-height: 40px;
    }
  }
}
.app-container {
  padding: 16px 20px;
  .search {
    padding: 18px 0 0 10px;
    margin-bottom: 10px;
    border-radius: 4px;
    border: 1px solid var(--el-border-color-light);
    box-shadow: var(--el-box-shadow-light);
    background-color: var(--el-bg-color-overlay);
  }
  &.column-container {
    width: 100%;
    height: calc(100vh - 60px);
    .app-main-content {
      width: 100%;
      height: calc(100vh - 90px);
      background: #11253e;
      padding: 16px;
      .app-form {
        width: 600px;
        margin: 0 auto;
      }
    }
  }
  .user-list {
    max-width: 600px;
    height: 100%;
    padding-top: 20px;
    padding-left: 16px;
    position: relative;
    cursor: pointer;
    .user-name {
      color: #fff;
      margin-bottom: 10px;
      font-size: 14px;
    }
    .user-remark {
      color: #98a2b3;
      font-size: 14px;
    }
    .edit-icon {
      height: 16px;
      line-height: 16px;
      position: absolute;
      top: 0;
      right: 42px;
    }
    .delete-icon {
      height: 16px;
      line-height: 16px;
      color: #98a2b3;
      position: absolute;
      top: 0;
      right: 25px;
    }
  }
  .flex {
    display: flex;
    justify-content: space-between;
  }
  .currentColor {
    color: #337dfe;
    border-left: 2px solid #1177fb;
    background: linear-gradient(90deg, rgba(0, 148, 255, 0.17) 0%, rgba(0, 148, 255, 0) 100%);
  }
  .role-title {
    height: 58px;
    line-height: 58px;
    width: 100%;
    border-bottom: 1px solid #344054;
    color: #fff;
  }
  .custom-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 48px;
    line-height: 48px;
    margin-bottom: 16px;
    background: #fff;
    vertical-align: middle;
  }
  .custom-preview {
    width: 100%;
    height: 435px;
    overflow-y: hidden;
    padding: 16px 24px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
  }
  .custom-search-box {
    width: 100%;
    padding: 16px 24px;
    margin-bottom: 16px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
    position: relative;
    .custom-search {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      background: #fff;
      border-radius: 4px;
      box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
      margin-bottom: 12px;
      .custom-screen-search {
        position: absolute;
        right: 0;
        top: 0;
      }
      .search-content {
        display: flex;
        flex-direction: row;
        align-items: center;
      }
      .custom-library {
        width: 31%;
        margin-right: 8px;
      }
      .search-param {
        display: flex;
        align-items: center;
        white-space: nowrap;
        font-size: 14px;
        color: #344054;
        line-height: 22px;
        font-weight: 400;
        margin-right: 5px;
      }
      + .app-content {
        max-height: calc(100vh - 202px);
      }
    }
    .custom-content {
      width: 100%;
      max-height: calc(100vh - 154px);
      padding: 16px 24px;
      background: #fff;
      overflow: auto;
      box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
      border-radius: 4px;
      &::-webkit-scrollbar {
        width: 6px !important;
        height: 6px !important;
      }
      &::-webkit-scrollbar-thumb {
        width: 6px !important;
        min-height: 40px !important;
        background: hsla(0, 0%, 76.9%, 0.7) !important;
        border-radius: 62px !important;
      }
      .btn-box {
        margin-bottom: 16px;
      }
      .textHidden {
        width: 180px;
        height: 20px;
        line-height: 20px;
        text-align: left;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
  .search-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 16px 16px 16px 8px;
    margin-bottom: 16px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
    .search-content {
      display: flex;
      flex-direction: row;
      align-items: center;
    }
    .search-param {
      display: flex;
      align-items: center;
      white-space: nowrap;
      font-size: 14px;
      color: #475467;
      line-height: 22px;
      font-weight: 400;
      min-width: 56px;
      margin-left: 8px;
      margin-right: 8px;
    }
    + .app-content {
      max-height: calc(100vh - 202px);
    }
  }
  .app-content {
    width: 100%;
    max-height: calc(100vh - 154px);
    padding: 16px 24px;
    background: #fff;
    overflow: auto;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
    border-radius: 4px;
    &::-webkit-scrollbar {
      width: 10px !important;
      height: 10px !important;
      background: #e4e7ec;
      border-radius: 4px;
    }
    &::-webkit-scrollbar-thumb {
      width: 10px !important;
      min-height: 20px !important;
      background: #b7d9fd !important;
      border-radius: 4px !important;
    }
    .btn-box {
      margin-bottom: 16px;
    }
    .textHidden {
      width: 180px;
      height: 20px;
      line-height: 20px;
      text-align: left;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
  .page-title {
    font-size: 16px;
    color: #101828;
    text-align: left;
    line-height: 16px;
    font-weight: 600;
  }
}
.common-title {
  color: #fff;
}

.settings-layout {
  display: flex;
  gap: 20px;
  width: 100%;
  height: 100%;
}

.map-section {
  flex: 1;
  background: #11253e;
  border-radius: 4px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 200px);
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h3 {
    margin: 0;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
  }
}

.map-container {
  flex: 1;
  position: relative;
  border-radius: 4px;
  overflow: hidden;
}

.cesium-container {
  width: 100%;
  height: 100%;
}

.map-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.6);
  padding: 10px;
  border-radius: 4px;
  color: white;
  z-index: 100;
}

.coordinates-display {
  font-size: 14px;

  p {
    margin: 4px 0;
  }
}
</style>
