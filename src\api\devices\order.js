import request from '@/utils/request';
import { MAIN_PATH ,APPLICTION_ORDER, API_VERSION } from '../config/index';
import { useUserStoreHook } from '@/store/modules/user';

// 保单管理API主路径
const BASE_URL = MAIN_PATH + API_VERSION + APPLICTION_ORDER;
/**
 * 获取保单列表
 *
 * @param queryParams
 */
export function getOrderList(queryParams) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/pages`,
    method: 'get',
    params: queryParams
  });
}
/**
 * 新增保单
 *
 * @param queryParams
 */
export function addOrder(queryParams) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/add`,
    method: 'post',
    data: queryParams
  });
}
/**
 * 编辑保单
 *
 * @param queryParams
 */
export function editOrder(queryParams) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/update`,
    method: 'post',
    data: queryParams
  });
}
/**
 * 删除保单
 *
 * @param ids
 */
export function deleteOrder(query) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/del/${query.policy_id}`,
    method: 'DELETE',
    params: query
  });
}

export function getLinkUser(queryParams) {
  return request({
    url: `/manage/api/v1/policy/${queryParams.policy_id}/user/list`,
    method: 'GET',
  });
}
