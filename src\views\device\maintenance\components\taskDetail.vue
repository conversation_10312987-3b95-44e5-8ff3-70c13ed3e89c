<template>
	<el-dialog
		:title="title"
    v-if="title"
		:model-value="visible"
		width="800px"
    class="detailDialog"
		align-center
		:close-on-click-modal="false"
		@close="closeDialog"
	>
		<div class="detail-top flex">
			<div class="task-name ellipsis" :title="detail.scheme_name">{{ detail.scheme_name }}</div>
			<div class="task-timer">计划维保时间：{{ detail.due_date?.substring(0,10) }}</div>
    </div>
		<el-divider />
		<div class="task-upload">
			<svg-icon icon-class="attachments" style="width: 34px; height: 34px;color: #0094ff;margin: 10px 0" />
			<div class="upload-type" v-if="detail?.file_name ? !detail?.file_name :!fileList.file_name">支持 zip, pdf, jpg, png, jpeg</div>
			<div class="upload-type" v-if="detail?.file_name ? detail?.file_name : fileList.file_name">{{detail?.file_name ? detail?.file_name : fileList.file_name}}</div>
      <el-button style="margin-top: 10px" type="primary" @click="downLoadFile" v-if="detail.attachments"><i-ep-download />下载附件</el-button>
			<el-upload
        v-if="!detail.attachments"
        accept=".png,.zip,.jpg,.jpeg,.pdf"
				class="single-uploader"
				style="margin-top: 10px"
				:show-file-list="false"
				:before-upload="handleBeforeUpload"
				:http-request="uploadFile"
			>
				<el-button type="primary"><i-ep-upload />{{!fileList.file_name ? '上传附件' : '重新上传'}}</el-button>
			</el-upload>
		</div>
    <div class="detail-file">
      <div class="check-box">
        <el-scrollbar height="220px">
          <div style="margin-bottom: 15px" v-for="item in detail.task_item_vos" :key="item.id">
          <span class="item-title ellipsis" :title="item.item_name">{{ item.item_name }}</span>
            <el-radio-group v-model="item.status" :disabled="detail.attachments">
              <el-radio :value="1" :label="1">合格</el-radio>
              <el-radio :value="2" :label="2">不合格</el-radio>
            </el-radio-group>
          </div>
        </el-scrollbar>

      </div>
    </div>
		<template #footer v-if="!detail.attachments">
			<div class="dialog-footer">
				<el-button type="primary" @click="handleSubmit" v-loading="loading">确 定</el-button>
				<el-button @click="closeDialog">取 消</el-button>
			</div>
		</template>
	</el-dialog>
</template>
  
  <script setup>
  import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
  import { Download } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus';
  import { getTaskDetail, maintenanceTask, uploadTask } from '@/api/devices/maintenance';
  
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '查看维保记录'
    },
    formData: {
      type: Object,
      default(rawProps) {
        return {};
      }
    }
  });
  const form = reactive({});
  const emit = defineEmits(['update:visible', 'submit']);
  const loading = ref(false);
  const detail = ref({});
  const fileList = ref({})

  // 关闭弹窗
  function closeDialog() {
    resetForm();
    emit('update:visible', false);
  }

  watch(
    () => props.formData,
    (newVal, oldVal) => {
      Object.assign(form, newVal);
      console.log('点击编辑获取详情', form);
			if(props.visible == true) {
				initDetail();
			}
    },
    { deep: true }
  );

	/**
 * 限制用户上传文件的格式和大小
 */
function handleBeforeUpload(file) {
  let typeList = ['image/jpeg','image/jpeg','image/png','application/pdf','application/x-zip-compressed']
  if (typeList.indexOf(file.type) == -1) {
    ElMessage.warning('请上传zip, pdf, jpg, png, jpeg类型的文件');
    return false;
  }
  if (file.size > 100 * 1048 * 1048) {
    ElMessage.warning('上传文件不能大于100M');
    return false;
  }
  return true;
}

// 下载文件
function downLoadFile () {
  const link = document.createElement('a');
  link.download = detail.value.file_name;
  link.href = detail.value.attachments;
  document.body.appendChild(link);
  const evt = document.createEvent('MouseEvents');
  evt.initEvent('click', false, false);
  link.dispatchEvent(evt);
  document.body.removeChild(link);
}

function handleSubmit () {
  let i
  for(i=0;i<detail.value.task_item_vos.length;i++) {
    if(!detail.value.task_item_vos[i].status) {
      ElMessage.error(`请选择第${i+1}个检查项`)
      return;
    }
  }
  if(!fileList.value.file_path && !fileList.value.file_name) {
    ElMessage.error('请先上传附件');
    return;
  }
  maintenanceTask({
    id: detail.value.id,
    attachments: fileList.value.file_path,
    file_name: fileList.value.file_name,
    object_key: fileList.value.object_key,
    task_item_vos: detail.value.task_item_vos
  }).then(res=>{
    ElMessage.success('操作成功');
    emit('submit')
    closeDialog();
  })
}

/**
 * 自定义上传
 *
 * @param options
 */
 async function uploadFile(options) {
  const formData = new FormData();
  formData.append('file', options.file);
  uploadTask(formData).then(res=>{
    fileList.value = res
  })
}

  
  /**
   * 重置表单
   */
  function resetForm() {
    loading.value = false;
    Object.keys(form).map(key => {
      delete form[key];
    });
    fileList.value = {}
    detail.value = {}
  }

  function initDetail () {
    getTaskDetail({
      id: form.id
    }).then(res=>{
      res.task_item_vos.forEach(item=>{
        if(item.status === 0) {
          item.status = null
        }
      })
      detail.value = res
    })
  }

  onMounted(() => {
  });
  
  defineExpose({ resetForm });
  </script>
<style lang="scss">
.detailDialog {
  .el-dialog__body{
    background-color: #001129 !important;
  }
}
</style>
<style scoped lang="scss">
:deep(.el-dialog__body) {
  background-color: #001129;
}
.flex {
  display: flex;
  justify-content: space-between;
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.detail-top {
  width: 768px;
  padding: 16px;
	padding-bottom: 0;
  .task-name {
    max-width: 350px;
		font-family: SourceHanSansSC-Bold;
		font-size: 16px;
		color: #FFFFFF;
		text-align: left;
		line-height: 24px;
		font-weight: 700;
  }
  .task-timer {
    font-family: SourceHanSansSC-Regular;
		font-size: 14px;
		color: #FFFFFF;
		text-align: right;
		line-height: 22px;
		font-weight: 400;
  }
}
.task-upload {
  width: 768px;
	height: 135px;
	background-color: #11253E;
	border-radius: 2px;
	text-align: center;
	.upload-type {
		font-family: SourceHanSansSC-Regular;
		font-size: 12px;
		color: #FFFFFF;
		text-align: center;
		line-height: 20px;
		font-weight: 400;
	}
}
.detail-file{
  width: 768px;
  background: #11253E;
  padding: 16px;
  margin: 16px 0;
  .status-red,
  .status-green {
    width: 44px;
    height: 24px;
    display: inline-block;
    font-family: SourceHanSansSC-Regular;
    font-size: 12px;
    color: #F97066;
    background-color: #573c4a;
    padding: 0 auto;
    border-radius: 2px;
    text-align: center;
    line-height: 22px;
    font-weight: 400;
    margin-right: 8px;
  }
  .status-green {
    background: rgba(57,191,164,0.30);
    color: #35af99;
  }
  .file-time {
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;
    color: #FFFFFF;
    text-align: right;
    line-height: 22px;
    font-weight: 400;
  }
  .file-name {
    font-family: SourceHanSansSC-Bold;
    font-size: 16px;
    color: #FFFFFF;
    text-align: left;
    line-height: 24px;
    font-weight: 700;
    margin-bottom: 12px;
  }
  .item-title {
    min-width: 100px;
    max-width: 200px;
    display: inline-block;
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;
    color: #FFFFFF;
    text-align: left;
    line-height: 22px;
    font-weight: 400;
  }
  .check-box {
    width: 100%;
    // height: 220px;
    // overflow-y: auto;
  }
}
</style>
  