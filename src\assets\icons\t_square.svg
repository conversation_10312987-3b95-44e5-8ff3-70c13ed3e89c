<?xml version="1.0" encoding="UTF-8"?>
<svg width="28px" height="28px" viewBox="0 0 28 28" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>框选</title>
    <defs>
        <filter x="-40.5%" y="-40.5%" width="181.0%" height="181.0%" filterUnits="objectBoundingBox" id="filter-1">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.0747784628   0 0 0 0 0.217110551   0 0 0 0 0.373216712  0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="CurrentColor" fill-rule="evenodd">
        <g id="首页备份" transform="translate(-1857.000000, -370.000000)" fill="CurrentColor" fill-rule="nonzero">
            <g id="编组-24" transform="translate(1846.000000, 106.000000)">
                <g id="框选" filter="url(#filter-1)" transform="translate(15.117647, 268.117647)">
                    <path d="M17.6470588,0 C18.816603,0 19.7647059,0.948102883 19.7647059,2.11764706 C19.7647059,2.82594353 19.4169677,3.4530214 18.8829003,3.83747175 L18.8823529,10.5339367 C18.8823529,10.8263227 18.6453272,11.0633484 18.3529412,11.0633484 C18.0605551,11.0633484 17.8235294,10.8263227 17.8235294,10.5339367 L17.8235652,4.22804285 C17.7653653,4.23284523 17.7064981,4.23529412 17.6470588,4.23529412 C16.4775146,4.23529412 15.5294118,3.28719123 15.5294118,2.11764706 C15.5294118,2.05820783 15.5318607,1.99934056 15.536663,1.94114066 L4.22804285,1.94114066 C4.23284523,1.99934056 4.23529412,2.05820783 4.23529412,2.11764706 C4.23529412,3.28719123 3.28719123,4.23529412 2.11764706,4.23529412 C2.05820783,4.23529412 1.99934056,4.23284523 1.94114066,4.22804285 L1.94114066,15.536663 C1.99934056,15.5318607 2.05820783,15.5294118 2.11764706,15.5294118 C3.28719123,15.5294118 4.23529412,16.4775146 4.23529412,17.6470588 C4.23529412,17.7064981 4.23284523,17.7653653 4.22804285,17.8235652 L9.88235294,17.8235294 C10.174739,17.8235294 10.4117647,18.0605551 10.4117647,18.3529412 C10.4117647,18.6453272 10.174739,18.8823529 9.88235294,18.8823529 L3.83747175,18.8829003 C3.4530214,19.4169677 2.82594353,19.7647059 2.11764706,19.7647059 C0.948102883,19.7647059 0,18.816603 0,17.6470588 C0,16.9383405 0.34815253,16.3109375 0.882760079,15.9265474 L0.882760079,3.83815844 C0.34815253,3.45376837 0,2.8263654 0,2.11764706 C0,0.948102883 0.948102883,0 2.11764706,0 C2.8263654,0 3.45376837,0.34815253 3.83815844,0.882760079 L15.9265474,0.882760079 C16.3109375,0.34815253 16.9383405,0 17.6470588,0 Z M10.1007361,9.19162557 L18.9705939,13.5349185 C19.0256223,13.561851 19.0602164,13.6201894 19.0588235,13.6836338 C19.0573446,13.7470782 19.02015,13.803645 18.9639646,13.8278327 L15.8833301,15.1541096 L18.1627,17.9907353 C18.2715119,18.1261675 18.2545965,18.3280677 18.1249168,18.4417111 L17.2442921,19.2133719 C17.182009,19.2679506 17.1015139,19.2944559 17.020516,19.2870565 C16.9395181,19.2796571 16.8646528,19.2389592 16.8123909,19.1739165 L14.5259702,16.3285274 L12.7208064,19.298285 C12.6882372,19.3518441 12.6284483,19.3802595 12.5683816,19.3707268 C12.508315,19.3611941 12.4593097,19.3155129 12.4434485,19.2542678 L9.88767869,9.37833835 C9.87208682,9.31815962 9.89137379,9.25400268 9.93710441,9.21392668 C9.98283504,9.17385067 10.0466876,9.16514831 10.1007361,9.19162557 Z" id="形状结合"></path>
                </g>
            </g>
        </g>
    </g>
</svg>