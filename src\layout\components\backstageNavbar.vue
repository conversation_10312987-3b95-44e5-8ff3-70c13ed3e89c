<script setup>
import { onMounted, onUnmounted, reactive} from 'vue';
import AvatarArea from './AvatarArea/index.vue';
import { refreshToken } from '@/api/devices';
import { useRouter } from 'vue-router';
import { useStorage } from '@vueuse/core';
import { constantRoutes } from '@/router/index';
import { getDeptSysSetting } from '@/api/wayline'
const defaultLogoIcon = new URL('/resource/images/default-logo.jpeg', import.meta.url).href;
const returnLeftIconUrl = new URL('@/assets/return-left.png', import.meta.url).href;
let timerRef = null;
const router = useRouter();
const token = useStorage('accessToken', '');
const path = ref('/dashboard')
const data = reactive({})
let authority = localStorage.getItem('menu') && JSON?.parse(localStorage.getItem('menu'))

function toBackstage () {
  const url = router.resolve(path.value).href;
  window.open(url, '_blank');
}

function handleMenu () {
  if (authority === null) {
    authority = localStorage.getItem('menu') && JSON?.parse(localStorage.getItem('menu'));
  }
  let arr = []
  arr = constantRoutes?.filter(item=>{
   return authority?.indexOf(item.path?.replace('/','')) >-1
  })
  arr.forEach(res=>{
    res.children=res.children.filter(resp=>authority?.indexOf(resp.path?.replace('/','')) >-1)
  })
  if(arr.length > 0) {
    path.value = arr[0]?.children[0]?.path
  }
}

onUnmounted(() => {
  clearInterval(timerRef);
});

function initData () {
  getDeptSysSetting({}).then(res=>{
    const { base_config = {} } = res;
    data.name = base_config.sys_name
    data.logo = base_config.sys_logo_url

  
  })
}

// 默认logo图片路径
function handleImageError() {
  data.logo = defaultLogoIcon;
}

onMounted(()=>{
  initData();
  handleMenu();
  timerRef = setInterval(() => {
    refreshToken({}).then(res=>{
      token.value = res.access_token
    })
  }, 1000 * 60 * 15);
})

function closeCurrentPage() {
  // Use router navigation instead of window.close()
  router.back();
}
</script>

<template>
  <!-- 顶部导航栏 -->
  <div class="navbar">
    <!-- 左侧面包屑 -->
    <div class="flex logo">
		  <!-- <svg-icon icon-class="new-logo" style="margin-left: 25;width: 40px;height: 40px;" /> -->
      <el-image
        style="margin-left: 25px;width: 40px;height: 40px;margin-right: 10px"
        :src="data.logo"
        :zoom-rate="1.2"
        :max-scale="7"
        :min-scale="0.2"
        fit="cover"
        @error="handleImageError"
      />
      <span class="navbar-title">{{ data.name }}</span>
    </div>
    <!-- 右侧导航设置 -->
    <div class="flex items-center">
      <!-- <div class="backstage" @click="toBackstage()">
        <svg-icon icon-class="backstage" style="margin-left: 4;" />
        后台管理
      </div> -->
      <div class="return-btn" @click="closeCurrentPage" title="返回">
        <el-image style="width: 24px; height: 20px" :src="returnLeftIconUrl" />
      </div>
      <AvatarArea :show-dashboard="true" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.navbar {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 0 1px #0003;
  background-color: #11253e;
  padding-right: 10px;
  :deep(.el-switch__core) {
    border: 1px solid #fff;
  }
  .return-btn {
    cursor: pointer;
    margin-right: 20px;
    display: flex;
    align-items: center;
    &:hover {
      opacity: 0.8;
    }
  }
  .backstage {
    font-family: SourceHanSansSC-Regular;
    font-size: 16px;
    color: #FFFFFF;
    text-align: center;
    line-height: 24px;
    font-weight: 400;
    margin-right: 20px;
    cursor: pointer;
  }
  .navbar-title {
    font-family: SourceHanSansSC-Bold;
    font-size: 20px;
    color: #FFFFFF;
    text-align: left;
    line-height: 40px;
    font-weight: 700;
    margin-left: 16px;
  }
  .setting-container {
    display: flex;
    align-items: center;
    color: #fff;
  }

  .manual-icon {
    width: 21px;
    height: 21px;
  }
}
</style>
