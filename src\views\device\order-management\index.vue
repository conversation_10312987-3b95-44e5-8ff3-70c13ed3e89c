<!--保单管理-->
<script>
export default {
  name: 'OrderManage'
};
</script>

<script setup>
import { reactive } from 'vue';
import EditDialog from './EditDialog.vue';
import optionData from '@/utils/option-data';
import { getOrderList, deleteOrder } from '@/api/devices/order.js';
import { authorityShow } from '@/utils/authority';
const editDialogRef = ref(null);

const loading = ref(false);
const total = ref(0);

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  order_name: '',
  insurance_company: '',
  nick_name: '',
  status: ''
});

const dataList = ref([]);
const dialog = reactive({
  visible: false
});
const editDialog = reactive({
  visible: false
});

let formData = reactive({});
let detailData = reactive({});

/**
 * 查询
 */
function handleQuery() {
  // Calculate the new total pages based on the new page size
  const newTotalPages = Math.ceil(total.value / queryParams.pageSize);
  
  // If current page is greater than new total pages, adjust to the last page
  if (queryParams.pageNum > newTotalPages) {
    queryParams.pageNum = newTotalPages || 1;
  }
  getOrderList(queryParams).then(data => {
    const { list, pagination } = data;
    dataList.value = list || [];
    total.value = pagination.total;
  });
}
function handleSearch() {
  queryParams.pageNum = 1;
  handleQuery();
}
/**
 * 重置查询
 */
function resetQuery() {
  queryParams.order_name = '';
  queryParams.insurance_company = '';
  queryParams.nick_name = '';
  queryParams.status = '';
  queryParams.pageNum = 1;
  queryParams.pageSize = 10;
  handleQuery();
}

function openEditDialog(row) {
  editDialog.visible = true;
  //清空表单验证和表单数据
  if (!row) {
    nextTick(() => {
      editDialogRef.value.resetForm();
    });
  }
  Object.keys(formData).map(key => {
    delete formData[key];
  });

  if (row) {
    editDialog.title = '编辑保单';
    Object.assign(formData, { ...row });
  } else {
    editDialog.title = '新增保单';
  }
}
/**
 * 删除
 */
function handleDelete(row) {
  ElMessageBox.confirm(`确认后将删除此保单，且无法进行恢复`, '确认删除所选保单？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    autofocus: false,
    closeOnClickModal: false,
    type: 'warning'
  }).then(() => {
    deleteOrder({
      policy_id: row.policy_id,
    }).then(data => {
      ElMessage.success('删除成功');
      handleQuery();
    });
  });
}

onMounted(() => {
  handleQuery();
});
</script>

<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
          <el-form-item label="" prop="order_name">
            <el-input
              class="input-serach"
              v-model="queryParams.order_name"
              placeholder="请输入保单号"
              clearable
              @keyup.enter="handleSearch"
              maxlength="32"
            />
          </el-form-item>
          <el-form-item label="" prop="insurance_company">
            <el-input
              class="input-serach"
              v-model="queryParams.insurance_company"
              placeholder="请输入保险公司"
              clearable
              @keyup.enter="handleSearch"
              maxlength="32"
            />
          </el-form-item>
          <el-form-item label="" prop="nick_name">
            <el-input
              class="input-serach"
              v-model="queryParams.nick_name"
              placeholder="请输入无人机名称"
              clearable
              @keyup.enter="handleSearch"
              maxlength="32"
            />
          </el-form-item>
          <el-form-item label="" prop="status">
            <el-select class="input-serach" v-model="queryParams.status" placeholder="请选择保单状态" clearable @change="handleSearch">
              <el-option
                v-for="(item, index) in optionData.orderStatusOption"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="search-btn">
        <el-button type="primary" @click="handleSearch()"><i-ep-search />搜索</el-button>
        <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
      </div>
    </div>
    <el-card shadow="never">
      <template #header v-if="authorityShow('createOrder')">
        <el-button type="primary" @click="openEditDialog()"><i-ep-plus />新增保单</el-button>
      </template>
      <el-table highlight-current-row :data="dataList" v-loading="loading" stripe height="540">
        <el-table-column label="序号" align="center" width="80">
          <template #default="scope">
            <span>{{ scope.$index + 1 + queryParams.pageSize * (queryParams.pageNum - 1) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="保单号" prop="order_name" show-overflow-tooltip />
        <el-table-column label="组织名称" prop="dept_name" show-overflow-tooltip />
        <el-table-column label="无人机名称" prop="nick_name" show-overflow-tooltip />
        <el-table-column label="无人机序列号" prop="device_sn" show-overflow-tooltip />
        <el-table-column label="保险公司" prop="insurance_company" show-overflow-tooltip />
        <el-table-column label="保单状态" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ optionData.orderStatusOption.find(item => item.value == scope.row.status).label || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="保险期限" prop="insurance_time" show-overflow-tooltip
          ><template #default="scope">
            <span>{{
              scope.row.insurance_begin_time
                ? scope.row.insurance_begin_time.substring(0, 10) +
                  ' 至 ' +
                  scope.row.insurance_end_time.substring(0, 10)
                : '-'
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建人" prop="creator" show-overflow-tooltip />
        <el-table-column fixed="right" label="操作" align="center" width="200" v-if="authorityShow('editOrder') || authorityShow('deleteOrder')">
          <template #default="scope">
            <el-button type="primary" link @click.stop="openEditDialog(scope.row)" v-if="authorityShow('editOrder')">编辑</el-button>
            <el-button type="danger" link @click.stop="handleDelete(scope.row)" v-if="authorityShow('deleteOrder')">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </el-card>

    <EditDialog
      ref="editDialogRef"
      v-model:visible="editDialog.visible"
      :title="editDialog.title"
      :form-data="formData"
      @submit="resetQuery"
    />
  </div>
</template>
<style scoped lang="scss">
.input-serach {
  width: 200px;
}
.flex-center {
  display: flex;
  align-items: center;
  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
}
.search {
  display: flex;
  align-items: center;
  padding: 0 24px;
  .search-form {
    padding-top: 16px;
    flex: 1;
  }
}
</style>
