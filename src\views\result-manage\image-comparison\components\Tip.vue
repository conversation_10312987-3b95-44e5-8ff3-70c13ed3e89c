<template>
  <div class="tip-wrapper">
    <div class="tip" id="tip"><span class="t1">原始</span> <span class="tmid"></span> <span class="t2">对比</span></div>
  </div>
</template>
<script>
export default {
  name: 'tip'
};
</script>
<script setup>
import { onMounted, defineProps, defineExpose, onUnmounted } from 'vue';
const props = defineProps({
  loc: {
    type: Number,
    default: 0
  }
});

const locComp = computed(() => {
  return props.loc;
});

const resetTrack = e => {
  console.log('locComp', e);
  if (track) {
    track.style.left = e + 'px';
  }
};

const emit = defineEmits(['change']);
let track = null;
const actualValueRef = ref(1);
const handleMouseDown = e => {
  // 判断点击的是tipRef.value 元素
  if (!e.currentTarget.classList.contains('tip')) {
    return;
  }
  let startX = e.clientX;
  let tipDom = e.currentTarget;
  let initialX = tipDom.offsetLeft;
  function mouseMoveHandler(e) {
    console.log('e.clientX', e.clientX);
    // 计算鼠标移动的距离并更新元素的位置
    let newLeft = initialX + (e.clientX - startX);
    let actualValue = 0;
    if (newLeft <= -40) {
      newLeft = -40;
      actualValue = 0;
    } else if (newLeft > 960) {
      newLeft = 960;
      actualValue = 1000;
    } else {
      actualValue = newLeft + 40;
    }
    if (track) {
      track.style.left = newLeft + 'px';
    }
    actualValueRef.value = actualValue;
    emit('change', actualValueRef.value);
  }
  const mouseUpHandler = () => {
    document.removeEventListener('mousemove', mouseMoveHandler);
    document.removeEventListener('mouseup', mouseUpHandler);
  };
  document.addEventListener('mousemove', mouseMoveHandler);
  document.addEventListener('mouseup', mouseUpHandler);
};

defineExpose({
  resetTrack
});
onMounted(() => {
  track = document.getElementById('tip');
  track.addEventListener('mousedown', handleMouseDown);
});
onUnmounted(() => {
  track && track.removeEventListener('mousedown', handleMouseDown);
});
</script>
<style lang="scss" scoped>
.tip-wrapper {
  position: relative;
  user-select: none;
  width: 1000px;
  padding: 1px;
  width: 1000px;
  height: 30px;
  .tip {
    user-select: none;
    position: absolute;
    top: 0px;
    left: 460px; // 500 居中要减去40
    z-index: 9999;
    display: flex;
    cursor: pointer;
  }
}
.t1 {
  padding: 3px;
  height: 30px;
  width: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  background-color: aliceblue;
  color: rgb(4, 122, 225);
  cursor: pointer;
}
.tmid {
  cursor: pointer;
  display: block;
  width: 2px;
  height: 530px;
  background-color: rgb(4, 122, 225);
}
.t2 {
  padding: 3px;
  height: 30px;
  width: 40px;
  display: flex;
  cursor: pointer;
  justify-content: center;
  align-items: center;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
  background-color: rgb(4, 122, 225);
  color: aliceblue;
}
</style>
