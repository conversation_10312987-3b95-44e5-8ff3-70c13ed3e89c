import { DroneInfo } from './DroneInfo';
import { PayloadInfo } from './PayloadInfo';
class MissionConfig {
  constructor() {}
  initMissionConfig(options = {}) {
    // 设置默认值
    this.wpml_flyToWaylineMode = options.flyToWaylineMode || 'pointToPoint';
    this.wpml_finishAction = options.finishAction || 'goHome';
    this.wpml_exitOnRCLost = options.exitOnRCLost || 'goContinue';
    this.wpml_executeRCLostAction = options.executeRCLostAction || 'goBack';
    this.wpml_takeOffSecurityHeight = options.takeOffSecurityHeight || 20;
    this.wpml_globalTransitionalSpeed = options.globalTransitionalSpeed || 10;
    this.wpml_globalRTHHeight = options.globalRTHHeight || 100;
    // 初始化无人机信息，默认为空
    this.wpml_droneInfo = new DroneInfo({
      droneEnumValue: options.droneInfo?.droneEnumValue || null,
      droneSubEnumValue: options.droneInfo?.droneSubEnumValue || null
    });
    this.wpml_payloadInfo = new PayloadInfo({
      payloadEnumValue: options.payloadInfo?.payloadEnumValue || null,
      payloadSubEnumValue: options.payloadInfo?.payloadSubEnumValue || null,
      payloadPositionIndex: options.payloadInfo?.payloadPositionIndex || null
    });
  }

  //#region get set
  setFlyToWaylineMode(value) {
    this.wpml_flyToWaylineMode = value;
  }
  setFinishAction(value) {
    this.wpml_finishAction = value;
  }
  setExitOnRCLost(value) {
    this.wpml_exitOnRCLost = value;
  }

  setExecuteRCLostAction(value) {
    this.wpml_executeRCLostAction = value;
  }

  setTakeOffSecurityHeight(value) {
    this.wpml_takeOffSecurityHeight = value;
  }

  setGlobalTransitionalSpeed(value) {
    this.wpml_globalTransitionalSpeed = value;
  }

  setGlobalRTHHeight(value) {
    this.wpml_globalRTHHeight = value;
  }

  getFlyToWaylineMode() {
    return this.wpml_flyToWaylineMode;
  }

  getFinishAction() {
    return this.wpml_finishAction;
  }

  getExitOnRCLost() {
    return this.wpml_exitOnRCLost;
  }

  getExecuteRCLostAction() {
    return this.wpml_executeRCLostAction;
  }

  getTakeOffSecurityHeight() {
    return this.wpml_takeOffSecurityHeight;
  }

  getGlobalTransitionalSpeed() {
    return this.wpml_globalTransitionalSpeed;
  }

  getGlobalRTHHeight() {
    return this.wpml_globalRTHHeight;
  }
  getDroneInfo() {
    return this.wpml_droneInfo;
  }
  gePayloadInfo() {
    return this.wpml_payloadInfo;
  }

  //#endregion

  setDroneDetails(enumValue, subEnumValue) {
    this.wpml_droneInfo.droneEnumValue = enumValue;
    this.wpml_droneInfo.droneSubEnumValue = subEnumValue;
  }

  setPayloadDetails(payloadEnumValue, payloadSubEnumValue, payloadPositionIndex) {
    this.wpml_payloadInfo.payloadEnumValue = payloadEnumValue;
    this.wpml_payloadInfo.payloadSubEnumValue = payloadSubEnumValue;
    this.wpml_payloadInfo.payloadPositionIndex = payloadPositionIndex;
  }
}

export { MissionConfig };
