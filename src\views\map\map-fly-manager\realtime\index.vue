<template>
  <div class="map-100">
    <mainMapWidget />
    <!-- <deviceList v-show="!nowOsdVisible.visible" /> -->
    <deviceList/>
    <osdWidget v-if="nowOsdVisible.visible" />
    <div v-if="nowUavVisible.visible" class="camera-box">
      <JessibucaPro
        ref="uavRefs"
        playerId="livePlayerFly"
        :dronePara="nowUavVisible"
        @onClose="liveOnClose"
      />
    </div>
  </div>
</template>
<script>
export default {
  name: 'Realtimefly'
};
</script>
<script setup>
import {
  onMounted,
  onUnmounted,
  onBeforeUpdate,
  onBeforeUnmount,
  nextTick
} from 'vue';
import * as Cesium from 'cesium';
import {
  getCesiumEngineInstance,
  flyTo
} from '@/components/Cesium/libs/cesium';
import mainMapWidget from '../components/mainMap.vue';
import deviceList from '../components/deviceList.vue';
import osdWidget from '../components/osd.vue';
import { useDeviceStateStore } from '@/store/modules/deviceState.js';
const deviceStateStore = useDeviceStateStore();

// 当前展示的设备信息
const nowOsdVisible = deviceStateStore.nowOsdVisible;
// 当前展示无人机信息
const nowUavVisible = deviceStateStore.nowUavVisible;

// 当前展示的设备信息
const nowDeviceInfo = reactive({
  dock: deviceStateStore.getDockBySn(nowOsdVisible.dock_sn),
  device: deviceStateStore.getNavBySn(nowOsdVisible.device_sn)
});
const uavRefs = ref(null);

// const airModel = new URL('/resource/models/dajiang.glb', import.meta.url).href;
const airModel = new URL('/model/三棱箭头.glb', import.meta.url).href;

// 无人机飞行定位
let intervalTimer = null;
let navModel = null;
let navModelMatrix = null;

watch(
  () => nowUavVisible,
  newVal => {
    console.log('newVal====', newVal);
    nextTick(() => {
      if (newVal.droneSelected && newVal.visible) {
        console.log('111111视频初始化');
        uavRefs.value.onStart && uavRefs.value.onStart(newVal);
      }
    });
  },
  { deep: true }
);
function liveOnClose() {
  deviceStateStore.setUavVisible({ visible: false });
}
function setNavModel() {
  const flyView = getCesiumEngineInstance('mainMap-fly').viewer;
  const nav = nowDeviceInfo.device;
  const position = Cesium.Cartesian3.fromDegrees(
    nav.longitude,
    nav.latitude,
    nav.height
  );

  var hpr = new Cesium.HeadingPitchRoll(
    Cesium.Math.toRadians(nav.attitude_head),
    Cesium.Math.toRadians(nav.attitude_pitch),
    Cesium.Math.toRadians(nav.attitude_roll)
  );
  var orientation = Cesium.Transforms.headingPitchRollQuaternion(position, hpr);
  // const navModelMatrix = Cesium.Transforms.headingPitchRollToFixedFrame(
  //   position,
  //   hpr,
  //   Cesium.Ellipsoid.WGS84
  // );
  if (!navModel) {
    // navModel = flyView.scene.primitives.add(
    //   Cesium.Model.fromGltf({
    //     id: 'wrj',
    //     url: airModel,
    //     modelMatrix: navModelMatrix,
    //     scale: 4,
    //     minimumPixelSize: 64,
    //     maximumScale: 128
    //   })
    // );
    // // 检查模型是否有动画 等待模型加载完成
    // navModel.readyPromise.then(function (gltfModel) {
    //   gltfModel.activeAnimations.addAll({
    //     loop: Cesium.ModelAnimationLoop.REPEAT
    //   });
    //   locateNav();
    // });
    navModel = flyView.entities.add({
      name: 'wrj',
      position: position,
      orientation: orientation,
      model: {
        uri: airModel,
        minimumPixelSize: 64,
        maximumScale: 20000
      }
    });
    flyView.trackedEntity = navModel;
  } else {
    // navModel.modelMatrix = navModelMatrix;
    // locateNav();
    navModel.position = position;
    navModel.orientation = orientation;
  }
}

// 定位无人机
function locateNav() {
  if (navModel) {
    const flyView = getCesiumEngineInstance('mainMap-fly').viewer;
    flyView.trackedEntity = navModel;
    // 获取模型包围盒的半径
    const modelBoundingSphere = navModel.boundingSphere;
    const modelBoundingSphereRadius = modelBoundingSphere.radius;

    // 将相机设置为模型位置的偏移量
    flyView.camera.lookAt(
      modelBoundingSphere.center,
      new Cesium.Cartesian3(0.0, 0.0, modelBoundingSphereRadius * 50)
    );

    // 将相机方向指向模型
    flyView.camera.lookAtTransform(navModelMatrix);
  }
}

onMounted(() => {
  intervalTimer = setInterval(() => {
    if (nowOsdVisible.device_sn === '' && nowDeviceInfo.device === undefined) {
      return;
    } else {
      nowDeviceInfo.device = deviceStateStore.getNavBySn(
        nowOsdVisible.device_sn
      );
      if (nowDeviceInfo.device !== undefined) {
        setNavModel();
      }
    }
  }, 1000);
});
onUnmounted(() => {
  clearInterval(intervalTimer);
  deviceStateStore.setUavVisible({ visible: false });
});
onBeforeUnmount(() => {});
</script>

<style lang="scss" scoped>
.map-100 {
  height: 100%;
  width: 100%;
  // position: relative;

  .tool {
    position: absolute;
    top: 50px;
    color: red;
    background-color: antiquewhite;
    z-index: 100;
  }
}
.camera-box {
  position: absolute;
  width: 600px;
  height: 350px;
  bottom: 5px;
  left: 5px;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 2;
  border-radius: 2px;
}
</style>
