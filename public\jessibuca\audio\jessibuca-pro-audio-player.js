!function(e){"function"==typeof define&&define.amd?define(e):e()}(function(){"use strict";class t{on(e,t,A){var s=this.e||(this.e={});return(s[e]||(s[e]=[])).push({fn:t,ctx:A}),this}once(s,i,r){const o=this;function a(){o.off(s,a);for(var e=arguments.length,t=new Array(e),A=0;A<e;A++)t[A]=arguments[A];i.apply(r,t)}return a._=i,this.on(s,a,r)}emit(e){for(var t=((this.e||(this.e={}))[e]||[]).slice(),A=arguments.length,s=new Array(1<A?A-1:0),i=1;i<A;i++)s[i-1]=arguments[i];for(let e=0;e<t.length;e+=1)t[e].fn.apply(t[e].ctx,s);return this}off(e,A){const t=this.e||(this.e={});if(e){var s=t[e],i=[];if(s&&A)for(let e=0,t=s.length;e<t;e+=1)s[e].fn!==A&&s[e].fn._!==A&&i.push(s[e]);return i.length?t[e]=i:delete t[e],this}Object.keys(t).forEach(e=>{delete t[e]}),delete this.e}}const A="fetch",s="websocket",o="player",a="playerAudio",h="debug",n="warn",i="init",r="initAudio",l="audioCode",u="closeEnd",d={fullscreen:"fullscreen$2",webFullscreen:"webFullscreen",decoderWorkerInit:"decoderWorkerInit",play:"play",playing:"playing",pause:"pause",mute:"mute",load:"load",loading:"loading",zooming:"zooming",videoInfo:"videoInfo",timeUpdate:"timeUpdate",audioInfo:"audioInfo",log:"log",error:"error",kBps:"kBps",timeout:"timeout",delayTimeout:"delayTimeout",delayTimeoutRetryEnd:"delayTimeoutRetryEnd",loadingTimeout:"loadingTimeout",loadingTimeoutRetryEnd:"loadingTimeoutRetryEnd",stats:"stats",performance:"performance",videoSmooth:"videoSmooth",faceDetectActive:"faceDetectActive",objectDetectActive:"objectDetectActive",record:"record",recording:"recording",recordingTimestamp:"recordingTimestamp",recordStart:"recordStart",recordEnd:"recordEnd",recordCreateError:"recordCreateError",recordBlob:"recordBlob",buffer:"buffer",videoFrame:"videoFrame",videoSEI:"videoSEI",start:"start",metadata:"metadata",resize:"resize",volumechange:"volumechange",destroy:"destroy",beforeDestroy:"beforeDestroy",streamEnd:"streamEnd",streamRate:"streamRate",streamAbps:"streamAbps",streamVbps:"streamVbps",streamDts:"streamDts",streamSuccess:"streamSuccess",streamMessage:"streamMessage",streamError:"streamError",streamStats:"streamStats",mseSourceOpen:"mseSourceOpen",mseSourceClose:"mseSourceClose",mseSourceended:"mseSourceended",mseSourceBufferError:"mseSourceBufferError",mseAddSourceBufferError:"mseAddSourceBufferError",mseSourceBufferBusy:"mseSourceBufferBusy",mseSourceBufferFull:"mseSourceBufferFull",videoWaiting:"videoWaiting",videoTimeUpdate:"videoTimeUpdate",videoSyncAudio:"videoSyncAudio",playToRenderTimes:"playToRenderTimes",playbackTime:"playbackTime",playbackTimestamp:"playbackTimestamp",playbackTimeScroll:"playbackTimeScroll",playbackPrecision:"playbackPrecision",playbackShowPrecisionChange:"playbackShowPrecisionChange",playbackJustTime:"playbackJustTime",playbackStats:"playbackStats",playbackSeek:"playbackSeek",playbackPause:"playbackPause",playbackPauseOrResume:"playbackPauseOrResume",playbackRateChange:"playbackRateChange",playbackPreRateChange:"playbackPreRateChange",ptz:"ptz",streamQualityChange:"streamQualityChange",visibilityChange:"visibilityChange",netBuf:"netBuf",close:"close",networkDelayTimeout:"networkDelayTimeout",togglePerformancePanel:"togglePerformancePanel",viewResizeChange:"viewResizeChange",flvDemuxBufferSizeTooLarge:"flvDemuxBufferSizeTooLarge",talkGetUserMediaSuccess:"talkGetUserMediaSuccess",talkGetUserMediaFail:"talkGetUserMediaFail",talkGetUserMediaTimeout:"talkGetUserMediaTimeout",talkStreamStart:"talkStreamStart",talkStreamOpen:"talkStreamOpen",talkStreamClose:"talkStreamClose",talkStreamError:"talkStreamError",talkStreamInactive:"talkStreamInactive",webrtcDisconnect:"webrtcDisconnect",webrtcFailed:"webrtcFailed",webrtcClosed:"webrtcClosed",webrtcOnConnectionStateChange:"webrtcOnConnectionStateChange",webrtcOnIceConnectionStateChange:"webrtcOnIceConnectionStateChange",crashLog:"crashLog",focus:"focus",blur:"blur",visibilityHiddenTimeout:"visibilityHiddenTimeout",websocketOpen:"websocketOpen",websocketClose:"websocketClose",websocketError:"websocketError",websocketMessage:"websocketMessage",aiObjectDetectorInfo:"aiObjectDetectorInfo",aiFaceDetectorInfo:"aiFaceDetectorInfo",playFailedAndPaused:"playFailedAndPaused",audioResumeState:"audioResumeState",webrtcStreamH265:"webrtcStreamH265",flvMetaData:"flvMetaData",talkFailedAndStop:"talkFailedAndStop",removeLoadingBgImage:"removeLoadingBgImage",memoryLog:"memoryLog",downloadMemoryLog:"downloadMemoryLog",pressureObserverCpu:"pressureObserverCpu"},c={load:d.load,timeUpdate:d.timeUpdate,audioInfo:d.audioInfo,error:d.error,kBps:d.kBps,start:d.start,timeout:d.timeout,loadingTimeout:d.loadingTimeout,loadingTimeoutRetryEnd:d.loadingTimeoutRetryEnd,delayTimeout:d.delayTimeout,delayTimeoutRetryEnd:d.delayTimeoutRetryEnd,play:d.play,pause:d.pause,mute:d.mute,stats:d.stats,playToRenderTimes:d.playToRenderTimes,crashLog:d.crashLog,websocketOpen:d.websocketOpen,websocketClose:d.websocketClose,playFailedAndPaused:d.playFailedAndPaused,audioResumeState:d.audioResumeState},p={playError:"playIsNotPauseOrUrlIsNull",fetchError:"fetchError",websocketError:"websocketError",webcodecsH265NotSupport:"webcodecsH265NotSupport",webcodecsDecodeError:"webcodecsDecodeError",webcodecsUnsupportedConfigurationError:"webcodecsUnsupportedConfigurationError",mediaSourceH265NotSupport:"mediaSourceH265NotSupport",mediaSourceDecoderConfigurationError:"mediaSourceDecoderConfigurationError",mediaSourceFull:d.mseSourceBufferFull,mseSourceBufferError:d.mseSourceBufferError,mseAddSourceBufferError:d.mseAddSourceBufferError,mediaSourceAppendBufferError:"mediaSourceAppendBufferError",mediaSourceBufferListLarge:"mediaSourceBufferListLarge",mediaSourceAppendBufferEndTimeout:"mediaSourceAppendBufferEndTimeout",mediaSourceTsIsMaxDiff:"mediaSourceTsIsMaxDiff",mediaSourceUseCanvasRenderPlayFailed:"mediaSourceUseCanvasRenderPlayFailed",wasmDecodeError:"wasmDecodeError",wasmUseVideoRenderError:"wasmUseVideoRenderError",hlsError:"hlsError",webrtcError:"webrtcError",webrtcClosed:d.webrtcClosed,webrtcIceCandidateError:"webrtcIceCandidateError",webglAlignmentError:"webglAlignmentError",wasmWidthOrHeightChange:"wasmWidthOrHeightChange",mseWidthOrHeightChange:"mseWidthOrHeightChange",wcsWidthOrHeightChange:"wcsWidthOrHeightChange",tallWebsocketClosedByError:"tallWebsocketClosedByError",flvDemuxBufferSizeTooLarge:d.flvDemuxBufferSizeTooLarge,wasmDecodeVideoNoResponseError:"wasmDecodeVideoNoResponseError",audioChannelError:"audioChannelError",simdH264DecodeVideoWidthIsTooLarge:"simdH264DecodeVideoWidthIsTooLarge",simdDecodeError:"simdDecodeError",webglContextLostError:"webglContextLostError",videoElementPlayingFailed:"videoElementPlayingFailed",videoElementPlayingFailedForWebrtc:"videoElementPlayingFailedForWebrtc",decoderWorkerInitError:"decoderWorkerInitError",videoInfoError:"videoInfoError",videoCodecIdError:"videoCodecIdError"},g="notConnect",m="open",f={10:"AAC",7:"ALAW",8:"MULAW",2:"MP3"},y="worklet",b="script",B="active",H={playType:a,timeout:10,loadingTimeout:10,heartTimeout:10,debug:!1,debugLevel:n,protocol:2,demuxType:"flv",isFlv:!1,isNotMute:!1,videoBuffer:1e3,videoBufferDelay:1e3,videoBufferMax:5e3,loadingTimeoutReplay:!0,heartTimeoutReplay:!0,loadingTimeoutReplayTimes:3,heartTimeoutReplayTimes:3,decoder:"decoder-pro-audio-player.js",isDecoderUseCDN:!1,weiXinInAndroidAudioBufferSize:4800,audioEngine:null,supportLockScreenPlayAudio:!0};class C{constructor(r){this.log=function(e){if(r._opt.debug&&r._opt.debugLevel==h){for(var t=r._opt.debugUuid?`[${r._opt.debugUuid}]`:"",A=arguments.length,s=new Array(1<A?A-1:0),i=1;i<A;i++)s[i-1]=arguments[i];console.log(`JbPro${t}[✅✅✅][${e}]`,...s)}},this.warn=function(e){if(r._opt.debug&&(r._opt.debugLevel==h||r._opt.debugLevel==n)){for(var t=r._opt.debugUuid?`[${r._opt.debugUuid}]`:"",A=arguments.length,s=new Array(1<A?A-1:0),i=1;i<A;i++)s[i-1]=arguments[i];console.log(`JbPro${t}[❗❗❗][${e}]`,...s)}},this.error=function(e){for(var t=r._opt.debugUuid?`[${r._opt.debugUuid}]`:"",A=arguments.length,s=new Array(1<A?A-1:0),i=1;i<A;i++)s[i-1]=arguments[i];console.error(`JbPro${t}[❌❌❌][${e}]`,...s)}}}var w;function E(){}function k(){return(new Date).getTime()}function W(e,t,A){return Math.max(Math.min(e,Math.max(t,A)),Math.min(t,A))}function G(){return(performance&&"function"==typeof performance.now?performance:Date).now()}function S(A){let s=0,i=G();return e=>{var t;"[object Number]"===Object.prototype.toString.call(e)&&(s+=e,1e3<=(t=(e=G())-i))&&(A(s/t*1e3),i=e,s=0)}}(function(e){var o,t,a,A,s;o="undefined"!=typeof window&&void 0!==window.document?window.document:{},t=e.exports,a=function(){for(var e,t=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],A=0,s=t.length,i={};A<s;A++)if((e=t[A])&&e[1]in o){for(A=0;A<e.length;A++)i[t[0][A]]=e[A];return i}return!1}(),A={change:a.fullscreenchange,error:a.fullscreenerror},s={request:function(i,r){return new Promise(function(e,t){var A=function(){this.off("change",A),e()}.bind(this),s=(this.on("change",A),(i=i||o.documentElement)[a.requestFullscreen](r));s instanceof Promise&&s.then(A).catch(t)}.bind(this))},exit:function(){return new Promise(function(e,t){var A,s;this.isFullscreen?(A=function(){this.off("change",A),e()}.bind(this),this.on("change",A),(s=o[a.exitFullscreen]())instanceof Promise&&s.then(A).catch(t)):e()}.bind(this))},toggle:function(e,t){return this.isFullscreen?this.exit():this.request(e,t)},onchange:function(e){this.on("change",e)},onerror:function(e){this.on("error",e)},on:function(e,t){e=A[e];e&&o.addEventListener(e,t,!1)},off:function(e,t){e=A[e];e&&o.removeEventListener(e,t,!1)},raw:a},a?(Object.defineProperties(s,{isFullscreen:{get:function(){return Boolean(o[a.fullscreenElement])}},element:{enumerable:!0,get:function(){return o[a.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return Boolean(o[a.fullscreenEnabled])}}}),t?e.exports=s:window.screenfull=s):t?e.exports={isEnabled:!1}:window.screenfull={isEnabled:!1}})(w={exports:{}}),w.exports.isEnabled;try{if("object"==typeof WebAssembly&&"function"==typeof WebAssembly.instantiate){var j=new WebAssembly.Module(Uint8Array.of(0,97,115,109,1,0,0,0));if(j instanceof WebAssembly.Module)new WebAssembly.Instance(j)instanceof WebAssembly.Instance}}catch(e){}function T(){var e=window.navigator.userAgent.toLowerCase();return e&&/iphone|ipad|ipod|ios/.test(e)}function I(e){return null==e}function v(e){return!I(e)}const O=()=>{var e=window.navigator.userAgent;return/MicroMessenger/i.test(e)},F=()=>{var e=window.navigator.userAgent;return/Chrome/i.test(e)};function x(e){return R(e.hasAudio)&&(e.useMSE||e.useWCS&&!e.useOffscreen)&&R(e.demuxUseWorker)}function Y(){return"https:"===window.location.protocol||"localhost"===window.location.hostname}function _(){{var A=H;let t="";if("object"==typeof A)try{t=JSON.stringify(A),t=JSON.parse(t)}catch(e){t=A}else t=A;return t}}function P(e){return!0===e||"true"===e}function R(e){return!0!==e&&"true"!==e}class z{constructor(e){this.destroys=[],this.proxy=this.proxy.bind(this),this.master=e}proxy(t,e,A){let s=3<arguments.length&&void 0!==arguments[3]?arguments[3]:{};if(t){if(Array.isArray(e))return e.map(e=>this.proxy(t,e,A,s));t.addEventListener(e,A,s);var i=()=>{"function"==typeof t.removeEventListener&&t.removeEventListener(e,A,s)};return this.destroys.push(i),i}}destroy(){this.master.debug&&this.master.debug.log("Events","destroy"),this.destroys.forEach(e=>e()),this.destroys=[]}}var U=A=>{A.on(d.decoderWorkerInit,()=>{A.debug.log("audioPlayer","listen decoderWorkerInit and set loaded true"),A.loaded=!0}),A.on(d.play,()=>{A.loading=!1}),A._opt.debug&&(Object.keys(d).forEach(t=>{A.on(d[t],e=>{A.debug.log("audioPlayer events",d[t],e)})}),Object.keys(p).forEach(t=>{A.on(p[t],e=>{A.debug.log("audioPlayer event error",p[t],e)})}))};class V extends t{constructor(t){super(),this.TAG_NAME="FetchWorkerLoader",this.player=t,this.playing=!1,this.fetchWorker=null,this.workerClearTimeout=null,this.workerUrl=null,this.abortController=new AbortController,this.streamRate=S(e=>{t.emit(d.kBps,(e/1024).toFixed(2))}),this.streamRateInterval=null,this._initFetchWorker(),t.debug.log(this.TAG_NAME,"init")}destroy(){this.off(),this.workerUrl&&(window.URL.revokeObjectURL(this.workerUrl),this.workerUrl=null),this.workerClearTimeout&&(clearTimeout(this.workerClearTimeout),this.workerClearTimeout=null),this.fetchWorker&&(this.fetchWorker.postMessage({cmd:"destroy"}),this.fetchWorker.terminate(),this.fetchWorker=null),this._stopStreamRateInterval(),this.streamRate=null,this.player.debug.log(this.TAG_NAME,"destroy")}_initFetchWorker(){var e=function(){function s(e){return!0===e||"true"===e}function e(e){return!1===e||"false"===e}const i="The user aborted a request",r="AbortError",o="AbortError",a="fetchError",h="fetchClose",n="idle",l="buffering",u="complete";let A=new class{constructor(){this._requestAbort=!1,this._status=n,this.writableStream=null,this.isChrome=!1,this.abortController=new AbortController}destroy(){this.abort(),this.writableStream&&e(this.writableStream.locked)&&this.writableStream.close().catch(e=>{}),this.writableStream=null,this._status=n}fetchStream(e){var t=Object.assign({signal:this.abortController.signal},{headers:(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).headers||{}});fetch(e,t).then(e=>{if(this._requestAbort)this._status=n,e.body.cancel();else if(e.ok&&200<=e.status&&e.status<=299)if(postMessage({cmd:"fetchSuccess"}),"undefined"!=typeof WritableStream)this.writableStream=new WritableStream({write:e=>{this.abortController&&this.abortController.signal&&this.abortController.signal.aborted||s(this._requestAbort)?this._status=u:(this._status=l,this.streamRate&&this.streamRate(e.byteLength),postMessage({cmd:"buffer",buffer:e},[e.buffer]))},close:()=>{this._status=u,postMessage({cmd:h})},abort:e=>{var t;this.abortController&&this.abortController.signal&&this.abortController.signal.aborted?this._status=u:-1===(t=e.toString()).indexOf(i)&&-1===t.indexOf(r)&&e.name!==o&&(this.abort(),postMessage({cmd:a,message:e.toString()}))}}),e.body.pipeTo(this.writableStream);else{const t=e.body.getReader(),A=()=>{t.read().then(e=>{var{done:e,value:t}=e;e?(this._status=u,postMessage({cmd:h})):this.abortController&&this.abortController.signal&&this.abortController.signal.aborted||s(this._requestAbort)?this._status=u:(this._status=l,postMessage({cmd:"buffer",buffer:t},[t.buffer]),A())}).catch(e=>{var t;this.abortController&&this.abortController.signal&&this.abortController.signal.aborted?this._status=u:-1===(t=e.toString()).indexOf(i)&&-1===t.indexOf(r)&&e.name!==o&&(this.abort(),postMessage({cmd:a,message:e.toString()}))})};A()}else this.abort(),postMessage({cmd:a,message:`fetch response status is ${e.status} and ok is `+e.ok})}).catch(e=>{this.abortController&&this.abortController.signal&&this.abortController.signal.aborted||"AbortError"!==e.name&&(this.abort(),postMessage({cmd:a,message:e.toString()}))})}abort(){if(this._requestAbort=!0,this._status!==l||e(A.isChrome)){if(this.abortController){try{this.abortController.abort()}catch(e){}this.abortController=null}}else this.abortController=null}};self.onmessage=e=>{var t=e.data;switch(t.cmd){case"fetch":A.isChrome=s(t.isChrome),A.fetchStream(t.url,JSON.parse(t.options));break;case"destroy":A.destroy(),A=null}}}.toString().trim().match(/^function\s*\w*\s*\([\w\s,]*\)\s*{([\w\W]*?)}$/)[1],e=new Blob([e],{type:"text/javascript"}),e=URL.createObjectURL(e),t=new Worker(e);this.workerUrl=e,this.workerClearTimeout=setTimeout(()=>{window.URL.revokeObjectURL(this.workerUrl),this.workerUrl=null,this.workerClearTimeout=null},1e4),t.onmessage=e=>{var t=this.player["demux"],A=e.data;switch(A.cmd){case"buffer":this.streamRate&&this.streamRate(A.buffer.byteLength),t.dispatch(A.buffer);break;case"fetchSuccess":this.emit(d.streamSuccess),this._startStreamRateInterval();break;case"fetchClose":t.close(),this.emit(d.streamEnd);break;case"fetchError":t.close(),this.emit(p.fetchError,A.message)}},this.fetchWorker=t}_startStreamRateInterval(){this._stopStreamRateInterval(),this.streamRateInterval=setInterval(()=>{this.streamRate&&this.streamRate(0)},1e3)}_stopStreamRateInterval(){this.streamRateInterval&&(clearInterval(this.streamRateInterval),this.streamRateInterval=null)}fetchStream(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};this.player._times.streamStart=k(),this.fetchWorker.postMessage({cmd:"fetch",url:e,isChrome:F(),options:JSON.stringify(t)})}getStreamType(){return A}}class q extends t{constructor(t){super(),this.player=t,this.socket=null,this.socketStatus=g,this.wsUrl=null,this.requestAbort=!1,this.socketDestroyFnList=[],this.streamRate=S(e=>{t.emit(d.kBps,(e/1024).toFixed(2))}),this.streamRateInterval=null,t.debug.log("WebsocketStream","init")}destroy(){this._closeWebSocket(),this.stopStreamRateInterval(),this.wsUrl=null,this.off(),this.player.debug.log("WebsocketStream","destroy")}startStreamRateInterval(){this.stopStreamRateInterval(),this.streamRateInterval=setInterval(()=>{this.streamRate&&this.streamRate(0)},1e3)}stopStreamRateInterval(){this.streamRateInterval&&(clearInterval(this.streamRateInterval),this.streamRateInterval=null)}_createWebSocket(){const e=this.player,{debug:t,events:{proxy:A},demux:s}=e;this.socket=new WebSocket(this.wsUrl),this.socket.binaryType="arraybuffer";var i=A(this.socket,"open",()=>{t.log("WebsocketStream","socket open"),this.socketStatus=m,this.emit(d.streamSuccess),this.player.emit(d.websocketOpen),this.startStreamRateInterval()}),r=A(this.socket,"message",e=>{this.streamRate&&this.streamRate(e.data.byteLength),this._handleMessage(e.data)}),o=A(this.socket,"close",e=>{t.log("WebsocketStream","socket close and code is "+e.code),1006===e.code&&t.error("WebsocketStream","socket close abnormally and code is "+e.code),P(this.requestAbort)?(this.requestAbort=!1,t.log("WebsocketStream","socket close and requestAbort is true")):(s.close(),this.socketStatus="close",this.player.emit(d.websocketClose),this.emit(d.streamEnd))}),a=A(this.socket,"error",e=>{t.error("WebsocketStream","socket error",e),this.socketStatus="error",this.emit(p.websocketError,e),s.close(),t.log("WebsocketStream","socket error:",e.isTrusted?"websocket user aborted":"websocket error")});this.socketDestroyFnList.push(i,r,o,a)}_closeWebSocket(){this.socketDestroyFnList.forEach(e=>e()),!this.socket||0!==this.socket.readyState&&1!==this.socket.readyState?this.socket&&this.player.debug.log("WebsocketStream","_closeWebSocket() socket is null or socket status is "+(this.socket&&this.socket.readyState)):(this.requestAbort=!0,this.socket.close(1e3,"Client disconnecting")),this.socket=null,this.socketStatus=g,this.streamRate=null}_handleMessage(e){var t=this.player["demux"];t?t.dispatch(e):this.player.debug.warn("WebsocketStream","websocket handle message demux is null")}fetchStream(e,t){this.player._times.streamStart=k(),this.wsUrl=e,this._createWebSocket()}sendMessage(e){this.socket?this.socketStatus===m?this.socket.send(e):this.player.debug.error("WebsocketStream","websocket send message error and  socket status is "+this.socketStatus):this.player.debug.error("WebsocketStream","websocket send message socket is null")}resetFetchStream(){this._closeWebSocket(),this._createWebSocket()}getStreamType(){return s}}class J extends t{constructor(e){super(),(this.player=e).debug.log("HlsStream","init")}destroy(){this.off(),this.player.debug.log("HlsStream","destroy")}fetchStream(e){var t=this.player["hlsDecoder"];this.player._times.streamStart=k(),t.loadSource(e).then(()=>{this.player.debug.log("HlsStream","loadSource success"),this.emit(d.streamSuccess)}).catch(e=>{this.emit(p.hlsError,e)})}getStreamType(){return"hls"}}class Z extends t{constructor(e){super(),this.player=e,this.webrctUrl=null,e.debug.log("WebrtcStream","init")}destroy(){this.webrctUrl=null,this.off(),this.player.debug.log("WebrtcStream","destroy")}fetchStream(e){const t=this.player["webrtc"];if(this.player._times.streamStart=k(),this.webrctUrl=e.replace("webrtc:",window.location.protocol),-1===this.webrctUrl.indexOf("/webrtc/play")&&this.player.isWebrtcForM7S()){const t=new URL(this.webrctUrl),A="/webrtc/play"+t.pathname;this.webrctUrl=t.origin+A+t.search,this.player.debug.log("WebrtcStream",`original url is ${e}, and new url is: `+this.webrctUrl)}t.loadSource(this.webrctUrl).then(()=>{this.player.debug.log("WebrtcStream","loadSource success"),this.emit(d.streamSuccess)}).catch(e=>{this.emit(p.webrtcError,e)})}getStreamType(){return"webrtc"}}class X extends t{constructor(t){super(),this.player=t,this.transport=null,this.wtUrl=null,this.streamRate=S(e=>{t.emit(d.kBps,(e/1024).toFixed(2))}),this.streamRateInterval=null,t.debug.log("WebTransportLoader","init")}destroy(){this.abort(),this.off(),this.player.debug.log("WebTransportLoader","destroy")}startStreamRateInterval(){this.stopStreamRateInterval(),this.streamRateInterval=setInterval(()=>{this.streamRate&&this.streamRate(0)},1e3)}stopStreamRateInterval(){this.streamRateInterval&&(clearInterval(this.streamRateInterval),this.streamRateInterval=null)}_createWebTransport(){const e=this.player,{events:{},demux:t}=e;try{this.transport=new WebTransport(this.wtUrl),this.transport.ready.then(()=>{this.emit(d.streamSuccess),this.startStreamRateInterval(),this.transport.createBidirectionalStream().then(e=>{e.readable.pipeTo(new WritableStream(t.input))})}).catch(e=>{this.player.debug.warn("WebTransportLoader","_createWebTransport-ready",e)})}catch(e){this.player.debug.warn("WebTransportLoader","_createWebTransport",e)}}fetchStream(e){this.player._times.streamStart=k(),this.wtUrl=e.replace(/^wt:/,"https:"),this._createWebTransport()}abort(){if(this.transport)try{this.transport.close(),this.transport=null}catch(e){this.transport=null}}getStreamType(){return"webTransport"}}class M extends t{constructor(e){super(),this.player=e,this.workUrl=null,e.debug.log("WorkerStream","init")}destroy(){this.workUrl=null,this.off(),this.player.debug.log("WorkerStream","destroy")}sendMessage(e){this.player.decoderWorker.workerSendMessage(e)}fetchStream(e){this.workUrl=e,this.player._times.streamStart=k(),this.player.decoderWorker.workerFetchStream(e)}getStreamType(){return"worker "+(2===this.player._opt.protocol?A:s)}}class ${constructor(e){return new($.getLoaderFactory(e._opt))(e)}static getLoaderFactory(e){var{protocol:t,useWasm:A,playType:s,playbackConfig:i,demuxUseWorker:r}=e;return 2===t?s===a?M:s===o?A&&!x(e)||r?M:V:!i.useWCS&&!i.useMSE||r?M:V:1===t?s===a?M:s===o?A&&!x(e)||r?M:q:!i.useWCS&&!i.useMSE||r?M:q:3===t?J:4===t?Z:5===t?X:void 0}}class K extends t{constructor(e){super(),this.bufferList=[],this.player=e,this.$audio=null,this.scriptNode=null,this.workletProcessorNode=null,this.hasInitScriptNode=!1,this.audioContext=new(window.AudioContext||window.webkitAudioContext)({sampleRate:48e3}),this.gainNode=this.audioContext.createGain();e=this.audioContext.createBufferSource();e.buffer=this.audioContext.createBuffer(1,1,22050),e.connect(this.audioContext.destination),e.noteOn?e.noteOn(0):e.start(0),this.audioBufferSourceNode=e,this.mediaStreamAudioDestinationNode=this.audioContext.createMediaStreamDestination(),this.gainNode.gain.value=0,this.playing=!1,this.audioSyncVideoOption={diff:null},this.audioInfo={encType:"",channels:"",sampleRate:"",depth:""},this.init=!1,this.hasAudio=!1,this.audioResumeStateTimeout=null,this.on(d.videoSyncAudio,e=>{this.audioSyncVideoOption=e})}destroy(){this.closeAudio(),this.resetInit(),this.audioContext.close(),this.audioContext=null,this.gainNode=null,this.hasAudio=!1,this.playing=!1,this.scriptNode&&(this.scriptNode.onaudioprocess=E,this.scriptNode=null),this.workletProcessorNode&&(this.workletProcessorNode.port.onmessage=E,this.workletProcessorNode=null),this.audioResumeStateTimeout&&(clearTimeout(this.audioResumeStateTimeout),this.audioResumeStateTimeout=null),this.audioBufferSourceNode=null,this.mediaStreamAudioDestinationNode=null,this.hasInitScriptNode=!1,this.audioSyncVideoOption={diff:null},this.off()}resetInit(){this.audioInfo={encType:"",channels:"",sampleRate:"",depth:""},this.init=!1}getAudioInfo(){return this.audioInfo}updateAudioInfo(e){e.encTypeCode&&(this.audioInfo.encTypeCode=e.encTypeCode,this.audioInfo.encType=f[e.encTypeCode]),e.channels&&(this.audioInfo.channels=e.channels),e.sampleRate&&(this.audioInfo.sampleRate=e.sampleRate),e.depth&&(this.audioInfo.depth=e.depth),this.audioInfo.sampleRate&&this.audioInfo.channels&&this.audioInfo.encType&&!this.init&&(this.player.emit(d.audioInfo,this.audioInfo),this.init=!0)}get isPlaying(){return this.playing}get isMute(){return 0===this.gainNode.gain.value}get volume(){return this.gainNode.gain.value}get bufferSize(){return this.bufferList.length}initScriptNode(){}initMobileScriptNode(){}initWorkletScriptNode(){}getEngineType(){return""}mute(e){e?(this.isMute||this.player.emit(d.mute,e),this.setVolume(0),this.clear()):(this.isMute&&this.player.emit(d.mute,e),this.setVolume(this.player.lastVolume||.5))}setVolume(e){e=parseFloat(e).toFixed(2),isNaN(e)||(this.audioEnabled(!0),e=W(e,0,1),this.gainNode.gain.value=e,this.player.emit(d.volumechange,this.player.volume))}closeAudio(){this.hasInitScriptNode&&(this.scriptNode&&this.scriptNode.disconnect(this.gainNode),this.workletProcessorNode&&this.workletProcessorNode.disconnect(this.gainNode),this.gainNode)&&(this.gainNode.disconnect(this.mediaStreamAudioDestinationNode),this.$audio||this.gainNode.disconnect(this.audioContext.destination)),this.clear()}audioEnabled(e){e?this.isStateSuspended()&&(this.audioContext.resume().then(()=>{this.player.emit(d.audioResumeState,{state:this.audioContext.state,isRunning:this.isStateRunning()})}),this.audioResumeStateTimeout=setTimeout(()=>{clearTimeout(this.audioResumeStateTimeout),this.audioResumeStateTimeout=null,this.isStateSuspended()&&this.player.emit(d.audioResumeState,{state:this.audioContext.state,isRunning:this.isStateRunning()})},1e3)):this.isStateRunning()&&this.audioContext.suspend()}isStateRunning(){return"running"===this.audioContext.state}isStateSuspended(){return"suspended"===this.audioContext.state}clear(){this.bufferList=[]}play(e,t){}pause(){this.audioSyncVideoOption={diff:null},this.playing=!1}resume(){this.playing=!0}setRate(e){}getAudioBufferSize(){return 0}}class ee{constructor(e,t,A,s){this.player=e,this.audio=t,this.channel=A,this.bufferSize=s}extract(t,e){var A=this.provide(e);for(let e=0;e<A.size;e++)t[2*e]=A.left[e],t[2*e+1]=A.right[e];return this.audio.tempAudioTimestamp=A.ts,A.size}provide(t){let A=new Float32Array(t),s=new Float32Array(t),e=0,i=0,r=0,o=t/this.bufferSize;var a=this.audio.bufferList;if(o&&a.length>=o){try{for(let e=0;e<o;e++){const t=a.shift();2===this.channel?(A.set(t.buffer[0],r),s.set(t.buffer[1],r)):(A.set(t.buffer[0],r),s.set(t.buffer[0],r)),r+=this.bufferSize,i=t.ts}}catch(t){this.player.debug.warn("Processor","provide()",t),A=new Float32Array(0),s=new Float32Array(0)}e=A.length}return{size:e,ts:i,left:A,right:s}}destroy(){this.buffer=null,this.channel=null}}class D{constructor(){this._vector=new Float32Array,this._position=0,this._frameCount=0}get vector(){return this._vector}get position(){return this._position}get startIndex(){return 2*this._position}get frameCount(){return this._frameCount}get endIndex(){return 2*(this._position+this._frameCount)}clear(){this.receive(this._frameCount),this.rewind()}put(e){this._frameCount+=e}putSamples(e,t){let A=2<arguments.length&&void 0!==arguments[2]?arguments[2]:0;var t=2*(t=t||0),s=2*(A=0<=A?A:(e.length-t)/2),i=(this.ensureCapacity(A+this._frameCount),this.endIndex);this.vector.set(e.subarray(t,t+s),i),this._frameCount+=A}putBuffer(e,t){let A=2<arguments.length&&void 0!==arguments[2]?arguments[2]:0;t=t||0,0<=A||(A=e.frameCount-t),this.putSamples(e.vector,e.position+t,A)}receive(e){0<=e&&!(e>this._frameCount)||(e=this.frameCount),this._frameCount-=e,this._position+=e}receiveSamples(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,A=this.startIndex;e.set(this._vector.subarray(A,A+2*t)),this.receive(t)}extract(e){var t=this.startIndex+2*(1<arguments.length&&void 0!==arguments[1]?arguments[1]:0);e.set(this._vector.subarray(t,t+2*(2<arguments.length&&void 0!==arguments[2]?arguments[2]:0)))}ensureCapacity(){var e=parseInt(2*(0<arguments.length&&void 0!==arguments[0]?arguments[0]:0));this._vector.length<e?((e=new Float32Array(e)).set(this._vector.subarray(this.startIndex,this.endIndex)),this._vector=e,this._position=0):this.rewind()}ensureAdditionalCapacity(){this.ensureCapacity(this._frameCount+(0<arguments.length&&void 0!==arguments[0]?arguments[0]:0))}rewind(){0<this._position&&(this._vector.set(this._vector.subarray(this.startIndex,this.endIndex)),this._position=0)}}class te{constructor(e){e?(this._inputBuffer=new D,this._outputBuffer=new D):this._inputBuffer=this._outputBuffer=null}get inputBuffer(){return this._inputBuffer}set inputBuffer(e){this._inputBuffer=e}get outputBuffer(){return this._outputBuffer}set outputBuffer(e){this._outputBuffer=e}clear(){this._inputBuffer.clear(),this._outputBuffer.clear()}}class Ae extends te{constructor(e){super(e),this.reset(),this._rate=1}set rate(e){this._rate=e}reset(){this.slopeCount=0,this.prevSampleL=0,this.prevSampleR=0}clone(){var e=new Ae;return e.rate=this._rate,e}process(){var e=this._inputBuffer.frameCount,e=(this._outputBuffer.ensureAdditionalCapacity(e/this._rate+1),this.transpose(e));this._inputBuffer.receive(),this._outputBuffer.put(e)}transpose(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0;if(0===e)return 0;var t=this._inputBuffer.vector,A=this._inputBuffer.startIndex,s=this._outputBuffer.vector,i=this._outputBuffer.endIndex;let r=0,o=0;for(;this.slopeCount<1;)s[i+2*o]=(1-this.slopeCount)*this.prevSampleL+this.slopeCount*t[A],s[i+2*o+1]=(1-this.slopeCount)*this.prevSampleR+this.slopeCount*t[A+1],o+=1,this.slopeCount+=this._rate;if(--this.slopeCount,1!==e)e:for(;;){for(;1<this.slopeCount;)if(--this.slopeCount,(r+=1)>=e-1)break e;var a=A+2*r;s[i+2*o]=(1-this.slopeCount)*t[a]+this.slopeCount*t[a+2],s[i+2*o+1]=(1-this.slopeCount)*t[a+1]+this.slopeCount*t[a+3],o+=1,this.slopeCount+=this._rate}return this.prevSampleL=t[A+2*e-2],this.prevSampleR=t[A+2*e-1],o}}function se(){}class ie extends class{constructor(e){this._pipe=e}get pipe(){return this._pipe}get inputBuffer(){return this._pipe.inputBuffer}get outputBuffer(){return this._pipe.outputBuffer}fillInputBuffer(){throw new Error("fillInputBuffer() not overridden")}fillOutputBuffer(){let e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0;for(;this.outputBuffer.frameCount<e;){const e=16384-this.inputBuffer.frameCount;if(this.fillInputBuffer(e),this.inputBuffer.frameCount<16384)break;this._pipe.process()}}clear(){this._pipe.clear()}}{constructor(e,t){var A=2<arguments.length&&void 0!==arguments[2]?arguments[2]:se;super(t),this.callback=A,this.sourceSound=e,this.historyBufferSize=22050,this._sourcePosition=0,this.outputBufferPosition=0,this._position=0}get position(){return this._position}set position(e){if(e>this._position)throw new RangeError("New position may not be greater than current position");var t=this.outputBufferPosition-(this._position-e);if(t<0)throw new RangeError("New position falls outside of history buffer");this.outputBufferPosition=t,this._position=e}get sourcePosition(){return this._sourcePosition}set sourcePosition(e){this.clear(),this._sourcePosition=e}onEnd(){this.callback()}fillInputBuffer(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,t=new Float32Array(2*e),e=this.sourceSound.extract(t,e,this._sourcePosition);this._sourcePosition+=e,this.inputBuffer.putSamples(t,0,e)}extract(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,t=(this.fillOutputBuffer(this.outputBufferPosition+t),Math.min(t,this.outputBuffer.frameCount-this.outputBufferPosition)),e=(this.outputBuffer.extract(e,this.outputBufferPosition,t),this.outputBufferPosition+t);return this.outputBufferPosition=Math.min(this.historyBufferSize,e),this.outputBuffer.receive(Math.max(e-this.historyBufferSize,0)),this._position+=t,t}handleSampleData(e){this.extract(e.data,4096)}clear(){super.clear(),this.outputBufferPosition=0}}const re=[[124,186,248,310,372,434,496,558,620,682,744,806,868,930,992,1054,1116,1178,1240,1302,1364,1426,1488,0],[-100,-75,-50,-25,25,50,75,100,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[-20,-15,-10,-5,5,10,15,20,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[-4,-3,-2,-1,1,2,3,4,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]];class oe extends te{constructor(e){super(e),this._quickSeek=!0,this.midBufferDirty=!1,this.midBuffer=null,this.overlapLength=0,this.autoSeqSetting=!0,this.autoSeekSetting=!0,this._tempo=1,this.setParameters(44100,0,0,8)}clear(){super.clear(),this.clearMidBuffer()}clearMidBuffer(){this.midBufferDirty&&(this.midBufferDirty=!1,this.midBuffer=null)}setParameters(e,t,A,s){0<e&&(this.sampleRate=e),0<s&&(this.overlapMs=s),0<t?(this.sequenceMs=t,this.autoSeqSetting=!1):this.autoSeqSetting=!0,0<A?(this.seekWindowMs=A,this.autoSeekSetting=!1):this.autoSeekSetting=!0,this.calculateSequenceParameters(),this.calculateOverlapLength(this.overlapMs),this.tempo=this._tempo}set tempo(e){this._tempo=e,this.calculateSequenceParameters(),this.nominalSkip=this._tempo*(this.seekWindowLength-this.overlapLength),this.skipFract=0,e=Math.floor(this.nominalSkip+.5),this.sampleReq=Math.max(e+this.overlapLength,this.seekWindowLength)+this.seekLength}get tempo(){return this._tempo}get inputChunkSize(){return this.sampleReq}get outputChunkSize(){return this.overlapLength+Math.max(0,this.seekWindowLength-2*this.overlapLength)}calculateOverlapLength(){var e=this.sampleRate*(0<arguments.length&&void 0!==arguments[0]?arguments[0]:0)/1e3;e=e<16?16:e,this.overlapLength=e-=e%8,this.refMidBuffer=new Float32Array(2*this.overlapLength),this.midBuffer=new Float32Array(2*this.overlapLength)}checkLimits(e,t,A){return e<t?t:A<e?A:e}calculateSequenceParameters(){var e;this.autoSeqSetting&&(e=150+-50*this._tempo,e=this.checkLimits(e,50,125),this.sequenceMs=Math.floor(e+.5)),this.autoSeekSetting&&(e=28.333333333333332+-10/1.5*this._tempo,e=this.checkLimits(e,15,25),this.seekWindowMs=Math.floor(e+.5)),this.seekWindowLength=Math.floor(this.sampleRate*this.sequenceMs/1e3),this.seekLength=Math.floor(this.sampleRate*this.seekWindowMs/1e3)}set quickSeek(e){this._quickSeek=e}clone(){var e=new oe;return e.tempo=this._tempo,e.setParameters(this.sampleRate,this.sequenceMs,this.seekWindowMs,this.overlapMs),e}seekBestOverlapPosition(){return this._quickSeek?this.seekBestOverlapPositionStereoQuick():this.seekBestOverlapPositionStereo()}seekBestOverlapPositionStereo(){let e,t,A,s=0;for(this.preCalculateCorrelationReferenceStereo(),e=0,t=Number.MIN_VALUE;s<this.seekLength;s+=1)(A=this.calculateCrossCorrelationStereo(2*s,this.refMidBuffer))>t&&(t=A,e=s);return e}seekBestOverlapPositionStereoQuick(){let t,A,s,i,r,o=0;for(this.preCalculateCorrelationReferenceStereo(),A=Number.MIN_VALUE,t=0,i=0,r=0;o<4;o+=1){let e=0;for(;re[o][e]&&!((r=i+re[o][e])>=this.seekLength);)(s=this.calculateCrossCorrelationStereo(2*r,this.refMidBuffer))>A&&(A=s,t=r),e+=1;i=t}return t}preCalculateCorrelationReferenceStereo(){let e,t,A=0;for(;A<this.overlapLength;A+=1)t=A*(this.overlapLength-A),e=2*A,this.refMidBuffer[e]=this.midBuffer[e]*t,this.refMidBuffer[1+e]=this.midBuffer[1+e]*t}calculateCrossCorrelationStereo(e,t){var A=this._inputBuffer.vector;e+=this._inputBuffer.startIndex;let s=0,i=2;for(var r,o=2*this.overlapLength;i<o;i+=2)r=i+e,s+=A[r]*t[i]+A[r+1]*t[i+1];return s}overlap(e){this.overlapStereo(2*e)}overlapStereo(e){var t=this._inputBuffer.vector,A=(e+=this._inputBuffer.startIndex,this._outputBuffer.vector),s=this._outputBuffer.endIndex;let i,r,o=0;for(var a,h,n,l=1/this.overlapLength;o<this.overlapLength;o+=1)r=(this.overlapLength-o)*l,a=o*l,A[(n=(i=2*o)+s)+0]=t[(h=i+e)+0]*a+this.midBuffer[0+i]*r,A[n+1]=t[h+1]*a+this.midBuffer[1+i]*r}process(){var e;if(null===this.midBuffer){if(this._inputBuffer.frameCount<this.overlapLength)return;this.midBuffer=new Float32Array(2*this.overlapLength),this._inputBuffer.receiveSamples(this.midBuffer,this.overlapLength)}for(;this._inputBuffer.frameCount>=this.sampleReq;){e=this.seekBestOverlapPosition(),this._outputBuffer.ensureAdditionalCapacity(this.overlapLength),this.overlap(Math.floor(e)),this._outputBuffer.put(this.overlapLength),0<(t=this.seekWindowLength-2*this.overlapLength)&&this._outputBuffer.putBuffer(this._inputBuffer,e+this.overlapLength,t);var t=this._inputBuffer.startIndex+2*(e+this.seekWindowLength-this.overlapLength);this.midBuffer.set(this._inputBuffer.vector.subarray(t,t+2*this.overlapLength)),this.skipFract+=this.nominalSkip,e=Math.floor(this.skipFract),this.skipFract-=e,this._inputBuffer.receive(e)}}}function ae(e,t){return 1e-10<(t<e?e-t:t-e)}class he{constructor(){this.transposer=new Ae(!1),this.stretch=new oe(!1),this._inputBuffer=new D,this._intermediateBuffer=new D,this._outputBuffer=new D,this._rate=0,this._tempo=0,this.virtualPitch=1,this.virtualRate=1,this.virtualTempo=1,this.calculateEffectiveRateAndTempo()}clear(){this.transposer.clear(),this.stretch.clear()}clone(){var e=new he;return e.rate=this.rate,e.tempo=this.tempo,e}get rate(){return this._rate}set rate(e){this.virtualRate=e,this.calculateEffectiveRateAndTempo()}set rateChange(e){this._rate=1+.01*e}get tempo(){return this._tempo}set tempo(e){this.virtualTempo=e,this.calculateEffectiveRateAndTempo()}set tempoChange(e){this.tempo=1+.01*e}set pitch(e){this.virtualPitch=e,this.calculateEffectiveRateAndTempo()}set pitchOctaves(e){this.pitch=Math.exp(.69314718056*e),this.calculateEffectiveRateAndTempo()}set pitchSemitones(e){this.pitchOctaves=e/12}get inputBuffer(){return this._inputBuffer}get outputBuffer(){return this._outputBuffer}calculateEffectiveRateAndTempo(){var e=this._tempo,t=this._rate;this._tempo=this.virtualTempo/this.virtualPitch,this._rate=this.virtualRate*this.virtualPitch,ae(this._tempo,e)&&(this.stretch.tempo=this._tempo),ae(this._rate,t)&&(this.transposer.rate=this._rate),1<this._rate?this._outputBuffer!=this.transposer.outputBuffer&&(this.stretch.inputBuffer=this._inputBuffer,this.stretch.outputBuffer=this._intermediateBuffer,this.transposer.inputBuffer=this._intermediateBuffer,this.transposer.outputBuffer=this._outputBuffer):this._outputBuffer!=this.stretch.outputBuffer&&(this.transposer.inputBuffer=this._inputBuffer,this.transposer.outputBuffer=this._intermediateBuffer,this.stretch.inputBuffer=this._intermediateBuffer,this.stretch.outputBuffer=this._outputBuffer)}process(){(1<this._rate?(this.stretch.process(),this.transposer):(this.transposer.process(),this.stretch)).process()}}class ne{constructor(e,t,A){this.player=e,this.audio=t,this.soundTouch=new he,this.soundTouch.tempo=1,this.soundTouch.rate=1,this.filter=new ie(A,this.soundTouch)}setRate(e){e!==this.soundTouch.rate&&(this.soundTouch.tempo=e)}provide(e){var t=new Float32Array(2*e),A=this.filter.extract(t,e),s=new Float32Array(A),i=new Float32Array(A);for(let e=0;e<A;e++)s[e]=t[2*e],i[e]=t[2*e+1];return{size:A,left:s,right:i,ts:this.audio.tempAudioTimestamp||0}}destroy(){this.soundTouch&&(this.soundTouch.clear(),this.soundTouch=null),this.filter&&(this.filter=null)}}class Q extends K{constructor(e){super(e),this.defaultPlaybackRate=1,this.playbackRate=1,this.rateProcessor=null,this.processor=null,this.scriptNodeInterval=null,this.engineType=this.getAutoAudioEngineType(),this.audioBufferSize=this.getAudioBufferSizeByType(),this.$audio=null,this._delayPlay=!1,this.eventListenList=[],this.workletUrl=null,this.clearWorkletUrlTimeout=null,this.player._opt.supportLockScreenPlayAudio&&T()&&(this.$audio=document.createElement("audio"),Object.assign(this.$audio.style,{position:"absolute",left:"-100%",top:"-100%"}),(e.$container||document.body).appendChild(this.$audio),this._bindAudioProxy(),this.player.debug.log("AudioContext","create audio element")),this.scriptStartTime=0,this.player.debug.log("AudioContext","init",`engineType: ${this.engineType}, audioBufferSize: `+this.audioBufferSize)}destroy(){super.destroy(),this.workletUrl&&(URL.revokeObjectURL(this.workletUrl),this.workletUrl=null),this.clearWorkletUrlTimeout&&(clearTimeout(this.clearWorkletUrlTimeout),this.clearWorkletUrlTimeout=null),this.eventListenList&&(this.eventListenList.forEach(e=>{e()}),this.eventListenList=[]),this.$audio&&(this.$audio.pause(),this.$audio.srcObject=null,this.$audio.parentNode&&this.$audio.parentNode.removeChild(this.$audio),this.$audio=null),this.processor&&(this.processor.destroy(),this.processor=null),this.rateProcessor&&(this.rateProcessor.destroy(),this.rateProcessor=null),this.scriptNodeInterval&&(clearInterval(this.scriptNodeInterval),this.scriptNodeInterval=null),this.defaultPlaybackRate=1,this.playbackRate=1,this.scriptStartTime=0,this.audioBufferSize=0,this.engineType=b,this.player.debug.log("AudioContext","destroy")}isAudioPlaying(){return this.$audio&&R(this.$audio.paused)&&R(this.$audio.ended)&&0!==this.$audio.playbackRate&&0!==this.$audio.readyState}_bindAudioProxy(){var e=this.player.events["proxy"],e=e(this.$audio,"canplay",()=>{this.player.debug.log("AudioContext","canplay"),this._delayPlay&&this._audioElementPlay()});this.eventListenList.push(e)}_getAudioElementReadyState(){let e=0;return e=this.$audio?this.$audio.readyState:e}audioElementPlay(){var e;this.$audio&&(e=this._getAudioElementReadyState(),this.player.debug.log("AudioContext","play and readyState: "+e),0!==e||O()&&T()?this._audioElementPlay():(this.player.debug.warn("AudioContext","readyState is 0 and set _delayPlay to true"),this._delayPlay=!0))}_audioElementPlay(){this.$audio&&this.$audio.play().then(()=>{this._delayPlay=!1,this.player.debug.log("AudioContext","_audioElementPlay success"),setTimeout(()=>{this.isAudioPlaying()||(this.player.debug.warn("AudioContext","play failed and retry play"),this._audioElementPlay())},100),this.isAudioPlaying()&&(this.player.debug.log("AudioContext","play success and remove document click event listener"),document.removeEventListener("click",this._audioElementPlay.bind(this)))}).catch(e=>{this.player.debug.error("AudioContext","_audioElementPlay error",e),document.addEventListener("click",this._audioElementPlay.bind(this))})}getAudioBufferSize(){return this.audioBufferSize}get oneBufferDuration(){return this.audioBufferSize/this.audioContext.sampleRate*1e3}get isActiveEngineType(){return this.engineType===B}initProcessor(){this.processor=new ee(this.player,this,this.audioInfo.channels,this.audioBufferSize),this.rateProcessor=new ne(this.player,this,this.processor)}getAutoAudioEngineType(){let t=this.player._opt.audioEngine||b;var e=()=>{var e;t=O()&&(e=window.navigator.userAgent.toLowerCase(),/android/i.test(e))?B:(!T()||!this.player._opt.supportLockScreenPlayAudio)&&Y()?y:b};return this.player._opt.audioEngine?this.player._opt.audioEngine===y&&Y()?t=y:this.player._opt.audioEngine===B?t=B:this.player._opt.audioEngine===b?t=b:e():e(),t}getAudioBufferSizeByType(){var e=this.engineType,t=(this.player._opt.hasVideo,this.player._opt.weiXinInAndroidAudioBufferSize);return e!==y&&e===B?t||4800:1024}initScriptNode(){this.playing=!0,this.hasInitScriptNode||(this.initProcessor(),this.engineType===y?this.initWorkletScriptNode():this.engineType===B?this.initIntervalScriptNode():this.engineType===b&&this.initProcessScriptNode(),this.audioElementPlay())}getEngineType(){return this.engineType}isPlaybackRateSpeed(){return this.playbackRate>this.defaultPlaybackRate}initProcessScriptNode(){var e=this.audioContext.createScriptProcessor(this.audioBufferSize,0,this.audioInfo.channels);e.onaudioprocess=e=>{e=e.outputBuffer;this.handleScriptNodeCallback(e)},e.connect(this.gainNode),this.scriptNode=e,this.gainNode.connect(this.mediaStreamAudioDestinationNode),this.$audio?this.$audio.srcObject=this.mediaStreamAudioDestinationNode.stream:this.gainNode.connect(this.audioContext.destination),this.hasInitScriptNode=!0}initIntervalScriptNode(){this.scriptStartTime=0;var e=1e3*this.audioBufferSize/this.audioContext.sampleRate;this.scriptNodeInterval=setInterval(()=>{if(0===this.bufferList.length||R(this.playing)||this.isMute)this.playing&&R(this.isMute)&&this.player.debug.log("AudioContext",`interval script node and bufferList is ${this.bufferList.length} or playing is `+this.playing);else{const e=this.audioContext.createBufferSource(),t=this.audioContext.createBuffer(this.audioInfo.channels,this.audioBufferSize,this.audioContext.sampleRate);this.handleScriptNodeCallback(t,()=>{this.scriptStartTime<this.audioContext.currentTime&&(this.player.debug.log("AudioContext",`script start time ${this.scriptStartTime} is less than current time `+this.audioContext.currentTime),this.scriptStartTime=this.audioContext.currentTime),e.buffer=t,e.connect(this.gainNode),e.start(this.scriptStartTime),this.scriptStartTime+=t.duration})}},e),this.gainNode.connect(this.mediaStreamAudioDestinationNode),this.$audio?this.$audio.srcObject=this.mediaStreamAudioDestinationNode.stream:this.gainNode.connect(this.audioContext.destination),this.hasInitScriptNode=!0}initWorkletScriptNode(){e=function(){class e extends AudioWorkletProcessor{constructor(){super(),this.audioBufferSize=1024,this.start=!1,this.channels=1,this.samplesArray=[],this.offset=0,this.state=0,this.port.onmessage=e=>{"init"===e.data.message?(this.audioBufferSize=e.data.audioBufferSize,this.start=e.data.start,this.channels=e.data.channels,this.state=0,this.offset=0,this.samplesArray=[]):"stop"===e.data.message?(this.state=0,this.start=!1,this.offset=0,this.samplesArray=[]):"data"===e.data.message?this.samplesArray.push(e.data.buffer):"zero"===e.data.message&&this.samplesArray.push({left:new Float32Array(this.audioBufferSize).fill(0),right:new Float32Array(this.audioBufferSize).fill(0)})}}process(t,e,A){const s=e[0][0],i=e[0][1];if(0===this.offset&&this.port.postMessage({message:"beep"}),0===this.state)this.state=1;else if(1===this.state&&this.samplesArray.length>=4)this.state=2;else if(2===this.state){const t=this.samplesArray[0];for(let e=0;e<s.length;e++)1===this.channels?s[e]=t.left[e+this.offset]:2===this.channels&&(s[e]=t.left[e+this.offset],i&&(i[e]=t.right[e+this.offset]))}else 1===this.channels?s.fill(0):2===this.channels&&(s.fill(0),i&&i.fill(0));return this.offset+=128,this.offset===this.audioBufferSize&&(this.offset=0,2===this.state&&this.samplesArray.shift(),0===this.samplesArray.length&&(this.state=0)),this.start}}registerProcessor("worklet-processor",e)}.toString().trim().match(/^function\s*\w*\s*\([\w\s,]*\)\s*{([\w\W]*?)}$/)[1],e=new Blob([e],{type:"application/javascript"});var e=URL.createObjectURL(e);this.workletUrl=e,this.audioContext&&this.audioContext.audioWorklet.addModule(e).then(()=>{if(this.player.isDestroyed())this.player.debug.log("AudioContext","initWorkletScriptNode() player is destroyed");else if(this.audioContext){let e=[1];2===this.audioInfo.channels&&(e=[1,1]);try{this.workletProcessorNode=new AudioWorkletNode(this.audioContext,"worklet-processor",{numberOfOutputs:this.audioInfo.channels,outputChannelCount:e})}catch(e){this.player.debug.error("AudioContext","initWorkletScriptNode error",e),this.workletProcessorNode=null,this.tierDownToProcessScript()}this.workletProcessorNode&&(this.workletProcessorNode.connect(this.gainNode),this.gainNode.connect(this.mediaStreamAudioDestinationNode),this.$audio?this.$audio.srcObject=this.mediaStreamAudioDestinationNode.stream:this.gainNode.connect(this.audioContext.destination),this.hasInitScriptNode=!0,this.workletProcessorNode.port.postMessage({message:"init",audioBufferSize:this.audioBufferSize,start:!0,channels:this.audioInfo.channels}),this.workletProcessorNode.port.onmessage=e=>{this.workletProcessorNode?this.audioContext?this.handleScriptNodeCallback(this.workletProcessorNode,null,!0):this.workletProcessorNode.port.postMessage({message:"zero"}):this.player.debug.error("AudioContext","workletProcessorNode is null")})}else this.player.debug.warn("AudioContext","initWorkletScriptNode audioContext is null")}),this.clearWorkletUrlTimeout=setTimeout(()=>{URL.revokeObjectURL(this.workletUrl),this.workletUrl=null,this.clearWorkletUrlTimeout=null},1e4)}tierDownToProcessScript(){this.player.debug.log("AudioContext","tierDownToProcessScript"),this.engineType=b,this.audioBufferSize=this.getAudioBufferSizeByType(),this.initProcessScriptNode(),this.audioElementPlay()}handleScriptNodeCallback(e,t){let A,s=2<arguments.length&&void 0!==arguments[2]&&arguments[2],i=(t=t||E,e.length);s&&(A=e,i=this.audioBufferSize);var r,o=this.audioInfo.channels;this.bufferList.length&&this.playing?(r=this.player._opt).syncAudioAndVideo&&r.hasVideo&&(this.calcPlaybackRateBySync(),this.audioSyncVideoOption.diff>this.player._opt.syncAudioAndVideoDiff)?(this.player.debug.warn("AudioContext",`audioSyncVideoOption more than diff :${this.audioSyncVideoOption.diff}, waiting`),s?A.port.postMessage({message:"zero"}):this.fillScriptNodeOutputBuffer(e,o)):0===(r=this._provide(i)).size?(this.player.debug.warn("AudioContext",`bufferList size is ${this.bufferList.length} outputBufferLength is ${i},and bufferItem.size is 0`),s?A.port.postMessage({message:"zero"}):this.fillScriptNodeOutputBuffer(e,o)):(r&&r.ts&&(this.player.audioTimestamp=r.ts),s?A.port.postMessage({message:"data",buffer:r}):this.fillScriptNodeOutputBuffer(e,o,r)):(0===this.bufferList.length&&this.playing&&R(this.isMute)&&this.player.debug.warn("AudioContext","bufferList size is 0 and outputBufferLength is "+i),s?A.port.postMessage({message:"zero"}):this.fillScriptNodeOutputBuffer(e,o)),t()}fillScriptNodeOutputBuffer(e,t,A){if(1===t){const t=e.getChannelData(0);!A||0===A.size?t.fill(0):t.set(A.left)}else if(2===t){const t=e.getChannelData(0),s=e.getChannelData(1);!A||0===A.size?(t.fill(0),s.fill(0)):(t.set(A.left),s.set(A.right))}}play(e,t){this.isMute||(this.hasInitScriptNode?(this.hasAudio=!0,this.bufferList.push({buffer:e,ts:t}),this.player._opt.syncAudioAndVideo||this.calcPlaybackRateByBuffer()):this.player.debug.warn("AudioContext","play has not init script node"))}calcPlaybackRateBySync(){if(!this.isMute&&this.playing){var e=Math.floor(2e3/this.oneBufferDuration);if(this.bufferList.length>e)this.player.debug.warn("AudioContext",`bufferList length ${this.bufferList.length} more than ${e}, and drop`),this.clear();else{let e=this.playbackRate;this.audioSyncVideoOption.diff<-this.player._opt.syncAudioAndVideoDiff?(e=this.defaultPlaybackRate+.2,this.player.debug.log("AudioContext",`audioSyncVideoOption ${-this.player._opt.syncAudioAndVideoDiff} less than diff :${this.audioSyncVideoOption.diff}, speed up, playbackRate is `+e)):this.audioSyncVideoOption.diff>-this.player._opt.syncAudioAndVideoDiff/2&&(e=this.defaultPlaybackRate),this.updatePlaybackRate(e)}}}calcPlaybackRateByBuffer(){if(!this.isMute&&this.playing&&this.getEngineType()!==B){let e=this.playbackRate,t=1e3,A=5e3;this.isAudioPlayer&&(t=this.player._opt.videoBufferDelay,A=this.player._opt.videoBufferMax);var s=Math.floor(t/this.oneBufferDuration),i=Math.floor(A/this.oneBufferDuration);this.bufferList.length>i?(this.player.debug.warn("AudioContext",`bufferList length ${this.bufferList.length} more than ${i}, and drop`),this.clear()):(this.bufferList.length>s?(e=this.defaultPlaybackRate+.2,this.player.debug.log("AudioContext",`bufferList length ${this.bufferList.length} more than ${s}, speed up, playbackRate is `+e)):this.bufferList.length<s/2&&(e=this.defaultPlaybackRate),this.updatePlaybackRate(e))}}updatePlaybackRate(e){this.rateProcessor&&(this.playbackRate=e,this.rateProcessor.setRate(this.playbackRate))}_provide(e){return(1===this.playbackRate?this.processor:this.rateProcessor).provide(e)}}class le extends t{constructor(e){super(),this.player=e,this.$video=e.video.$videoElement,this.init=!1,this.player._opt.hlsUseCanvasRender&&(this.$video=this.player.hlsDecoder.$videoElement),this.audioInfo={encType:"",channels:"",sampleRate:""},this.player.debug.log("Audio","init")}destroy(){this.resetInit(),this.off(),this.player.debug.log("Audio","destroy")}resetInit(){this.init=!1,this.audioInfo={encType:"",channels:"",sampleRate:""}}getAudioInfo(){return this.audioInfo}updateAudioInfo(e){v(e.encTypeCode)&&(this.audioInfo.encType=f[e.encTypeCode]),v(e.encType)&&(this.audioInfo.encType=e.encType),v(e.channels)&&(this.audioInfo.channels=e.channels),v(e.sampleRate)&&(this.audioInfo.sampleRate=e.sampleRate),v(this.audioInfo.sampleRate)&&v(this.audioInfo.channels)&&v(this.audioInfo.encType)&&!this.init&&(this.player.debug.log("Audio","audioInfo",JSON.stringify(this.audioInfo)),this.player.emit(d.audioInfo,this.audioInfo),this.init=!0)}get isPlaying(){return!0}get volume(){return P(this.$video.muted)?0:this.$video.volume}get isMute(){return 0===this.$video.volume||P(this.$video.muted)}mute(e){this.setVolume(e?0:this.player.lastVolume||.5)}setVolume(e){e=parseFloat(e),isNaN(e)||(e=W(e,0,1),this.$video.muted&&(this.$video.muted=!1),this.$video.volume=e,this.player.emit(d.volumechange,this.player.volume))}clear(){}play(){}pause(){}resume(){}getEngineType(){return"audio"}isPlaybackRateSpeed(){return!1}getAudioBufferSize(){return 0}}class ue extends Q{constructor(e){super(e),this.delayTimeout=null,this.player.on(d.playbackPause,e=>{this.listenPlaybackPause(e)}),this.player.debug.log("AudioPlaybackContext","init")}destroy(){this.delayTimeout&&(clearTimeout(this.delayTimeout),this.delayTimeout=null),super.destroy(),this.player.debug.log("AudioPlaybackLoader","destroy")}listenPlaybackPause(e){e?(this.pause(),this.player.playback.isPlaybackPauseClearCache&&this.clear()):this.resume()}initScriptNodeDelay(){var e=this.player._opt.playbackDelayTime;0<e?this.delayTimeout=setTimeout(()=>{this.initScriptNode()},e):this.initScriptNode()}setRate(e){e!==this.defaultPlaybackRate&&this.rateProcessor&&(this.player.debug.log("AudioPlaybackContext","setRate",e),this.defaultPlaybackRate=e,this.updatePlaybackRate(e))}}class de extends Q{constructor(e){super(e),this.TAG_NAME="AudioPlayerLoader",this.isAudioPlayer=!0,this.player.debug.log(this.TAG_NAME,"init")}destroy(){super.destroy(),this.player.debug.log(this.TAG_NAME,"destroy")}play(e,t){R(this.playing)||super.play(e,t)}pause(){this.player.debug.log(this.TAG_NAME,"pause"),this.playing=!1,this.clear()}resume(){this.player.debug.log(this.TAG_NAME,"resume"),this.playing=!0}}class L{constructor(e){return new(L.getLoaderFactory(e._opt))(e)}static getLoaderFactory(e){return"playbackTF"===e.playType?ue:e.playType===a?de:e.isHls&&R(e.supportHls265)||e.isWebrtc&&R(e.isWebrtcH265)?le:Q}}const N=()=>"wakeLock"in navigator&&-1===window.navigator.userAgent.indexOf("Samsung")&&R(T());class ce{constructor(e){this.player=e,this.enabled=!1,N()?(this.player.debug.log("NoSleep","Native Wake Lock API supported."),this._wakeLock=null,this.handleVisibilityChange=()=>{null!==this._wakeLock&&"visible"===document.visibilityState&&this.enable()},document.addEventListener("visibilitychange",this.handleVisibilityChange),document.addEventListener("fullscreenchange",this.handleVisibilityChange)):(this.player.debug.log("NoSleep","Native Wake Lock API not supported. so use video element."),this.noSleepVideo=document.createElement("video"),this.noSleepVideo.setAttribute("title","No Sleep"),this.noSleepVideo.setAttribute("playsinline",""),this._addSourceToVideo(this.noSleepVideo,"webm","data:video/webm;base64,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"),this._addSourceToVideo(this.noSleepVideo,"mp4","data:video/mp4;base64,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"),Object.assign(this.noSleepVideo.style,{position:"absolute",left:"-100%",top:"-100%"}),document.querySelector("body").append(this.noSleepVideo),this.handleNoSleepVideoTimeUpdate=()=>{this.noSleepVideo&&4<this.noSleepVideo.currentTime&&(this.noSleepVideo.currentTime=1)},this.noSleepVideo.addEventListener("timeupdate",this.handleNoSleepVideoTimeUpdate))}destroy(){if(this._wakeLock&&(this._wakeLock.release(),this._wakeLock=null),this.noSleepVideo){this.handleNoSleepVideoTimeUpdate&&this.noSleepVideo.removeEventListener("timeupdate",this.handleNoSleepVideoTimeUpdate);try{this.noSleepVideo.parentNode&&this.noSleepVideo.parentNode.removeChild(this.noSleepVideo)}catch(e){this.player.debug.warn("NoSleep","Failed to remove noSleepVideo element.")}this.noSleepVideo=null}this.handleVisibilityChange&&(document.removeEventListener("visibilitychange",this.handleVisibilityChange),document.removeEventListener("fullscreenchange",this.handleVisibilityChange))}_addSourceToVideo(e,t,A){var s=document.createElement("source");s.src=A,s.type="video/"+t,e.appendChild(s)}get isEnabled(){return this.enabled}enable(){const t=this.player.debug;return N()?navigator.wakeLock.request("screen").then(e=>{this._wakeLock=e,this.enabled=!0,t.log("wakeLock","Wake Lock active."),this._wakeLock.addEventListener("release",()=>{t.log("wakeLock","Wake Lock released.")})}).catch(e=>{throw this.enabled=!1,t.warn("wakeLock",e.name+", "+e.message),e}):this.noSleepVideo.play().then(e=>(t.log("wakeLock","noSleepVideo Wake Lock active."),this.enabled=!0,e)).catch(e=>{throw t.warn("wakeLock",`noSleepVideo ${e.name}, `+e.message),this.enabled=!1,e})}disable(){N()?(this._wakeLock&&this._wakeLock.release(),this._wakeLock=null):this.noSleepVideo&&this.noSleepVideo.pause(),this.enabled=!1,this.player.debug.log("wakeLock","Disabling wake lock.")}}class pe{constructor(e){this.player=e,this.TAG_NAME="decoderAudioWorker",this.destroyResolve=null,this.workerClearTimeout=null,this.workerUrl=null;let t=this.player._opt.decoder;if(this.decoderWorkerCloseTimeout=null,0===t.indexOf("http")&&this.player._opt.isDecoderUseCDN){const e=new Blob([`importScripts("${t}")`],{type:"application/javascript"});t=window.URL.createObjectURL(e),this.workerUrl=t,this.workerClearTimeout=setTimeout(()=>{window.URL.revokeObjectURL(this.workerUrl),this.workerUrl=null,this.workerClearTimeout=null},1e4)}this.decoderWorker=new Worker(t),this._initDecoderWorker(),e.debug.log(this.TAG_NAME,"init and decoder url is "+t)}destroy(){return new Promise((e,t)=>{this.player.loaded?(this.player.debug.log(this.TAG_NAME,"has loaded and post message to destroy"),this.decoderWorker?(this.decoderWorker.postMessage({cmd:"close"}),this.destroyResolve=e,this.decoderWorkerCloseTimeout=setTimeout(()=>{this.player.debug.warn(this.TAG_NAME,"send close but not response and destroy directly"),this.decoderWorkerCloseTimeout&&(clearTimeout(this.decoderWorkerCloseTimeout),this.decoderWorkerCloseTimeout=null),this._destroy(),setTimeout(()=>{e()},0)},2e3)):(this.player.debug.warn(this.TAG_NAME,"has loaded but decoderWorker is null and destroy directly"),this._destroy(),setTimeout(()=>{e()},0))):(this.player.debug.log(this.TAG_NAME,"has not loaded and destroy directly"),this._destroy(),setTimeout(()=>{e()},0))})}_destroy(){this.decoderWorkerCloseTimeout&&(clearTimeout(this.decoderWorkerCloseTimeout),this.decoderWorkerCloseTimeout=null),this.workerUrl&&(window.URL.revokeObjectURL(this.workerUrl),this.workerUrl=null),this.workerClearTimeout&&(clearTimeout(this.workerClearTimeout),this.workerClearTimeout=null),this.decoderWorker&&(this.decoderWorker.terminate(),this.decoderWorker.onerror=null,this.decoderWorker.onmessageerror=null,this.decoderWorker.onmessage=null,this.decoderWorker=null),this.player.debug.log(this.TAG_NAME,"destroy"),this.destroyResolve&&(this.destroyResolve(),this.destroyResolve=null)}_initDecoderWorker(){const{debug:A,events:{}}=this.player;this.decoderWorker.onerror=e=>{this.player.debug.error(this.TAG_NAME,"onerror",e),this.player.emitError(p.decoderWorkerInitError,e)},this.decoderWorker.onmessageerror=e=>{this.player.debug.error(this.TAG_NAME,"onmessageerror",e)},this.decoderWorker.onmessage=e=>{var t=e.data;switch(t.cmd){case i:A.log(this.TAG_NAME,"onmessage:",i),this.decoderWorker&&this._initWork(),this.player.loaded||this.player.emit(d.load),this.player.emit(d.decoderWorkerInit);break;case l:A.log(this.TAG_NAME,"onmessage:",l,t.code),this.player.audio&&this.player.audio.updateAudioInfo({encTypeCode:t.code});break;case r:(A.log(this.TAG_NAME,"onmessage:",r,`channels:${t.channels},sampleRate:`+t.sampleRate),2<t.channels)?(this.player.emit(d.error,p.audioChannelError),this.player.emit(p.audioChannelError,`audio channel is ${t.channels}, max is 2`)):this.player.audio&&(this.player.audio.updateAudioInfo(t),this.player.audio.initScriptNode());break;case"playAudio":this.player.audio?(this.player.handleRender(),this.player.updateStats({ts:t.ts,buf:t.delay}),this.player.audio.play(t.buffer,t.ts)):A.warn(this.TAG_NAME,"onmessage playAudio but audio is null");break;case"workerFetch":t.type===d.streamSuccess?this.player.stream?this.player.stream.emit(d.streamSuccess):A.warn(this.TAG_NAME,"onmessage and workerFetch response stream success but stream is null"):t.type===d.streamRate?this.player.emit(d.kBps,(t.value/1024).toFixed(2)):t.type===d.streamEnd?this.player?(t.value===s&&this.player.emit(d.websocketClose),this.player.stream?this.player.stream.emit(d.streamEnd):A&&A.warn(this.TAG_NAME,"onmessage and workerFetch response stream end but player.stream is null")):A&&A.warn(this.TAG_NAME,"onmessage and workerFetch response stream end but player is null"):t.type===p.websocketError?(this.player&&this.player.stream?this.player.stream.emit(p.websocketError,t.value):A&&A.warn(this.TAG_NAME,"onmessage and workerFetch response websocket error but stream is null"),this.player&&this.player.emit(d.error,p.websocketError)):t.type===p.fetchError?(this.player&&this.player.stream?this.player.stream.emit(p.fetchError,t.value):A&&A.warn(this.TAG_NAME,"onmessage and workerFetch response fetch error but stream is null"),this.player&&this.player.emit(d.error,p.fetchError)):t.type===d.streamAbps&&this.player.updateStats({abps:t.value});break;case u:A.log(this.TAG_NAME,"onmessage:",u),this._destroy();break;default:A.warn(this.TAG_NAME,"onmessage:","unknown msg.cmd:"+t.cmd)}}}_initWork(){var e={debug:this.player._opt.debug,debugLevel:this.player._opt.debugLevel,sampleRate:this.player.audio&&this.player.audio.audioContext&&this.player.audio.audioContext.sampleRate||0,audioBufferSize:this.player.audio&&this.player.audio.getAudioBufferSize()||1024,videoBuffer:this.player._opt.videoBuffer,isChrome:F()};this.decoderWorker.postMessage({cmd:"init",opt:JSON.stringify(e)})}updateWorkConfig(e){this.decoderWorker&&this.decoderWorker.postMessage({cmd:"updateConfig",key:e.key,value:e.value})}workerFetchStream(e){var t=this.player["_opt"],t={protocol:t.protocol,isFlv:t.isFlv};this.decoderWorker.postMessage({cmd:"fetchStream",url:e,opt:JSON.stringify(t)})}workerSendMessage(e){this.decoderWorker.postMessage({cmd:"sendWsMessage",message:e})}}class ge extends t{constructor(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=(super(),this._opt={},this.TAG_NAME="AudioPlayer",_());this._opt=Object.assign({},t,e),this.debug=new C(this),this._loading=!1,this._playing=!1,this._hasLoaded=!1,this._destroyed=!1,this._checkHeartTimeout=null,this._checkLoadingTimeout=null,this._checkStatsInterval=null,this._startBpsTime=null,this._stats={abps:0,ts:0,buf:0,audioBufferTs:0},this._audioTimestamp=0,this.stream=null,this._lastVolume=null,this._historyAbpsList=[],this._times={playInitStart:"",playStart:"",streamStart:"",streamResponse:"",demuxStart:"",decodeStart:"",videoStart:"",playTimestamp:"",streamTimestamp:"",streamResponseTimestamp:"",demuxTimestamp:"",decodeTimestamp:"",videoTimestamp:"",allTimestamp:""},this._tempWorkerStats=null,/(iphone|ipad|ipod|ios|android)/i.test(window.navigator.userAgent.toLowerCase())&&R(this._opt.supportLockScreenPlayAudio&&T())&&(this.keepScreenOn=new ce(this)),U(this),this.stream=null,this.events=new z(this),this.decoderWorker=new pe(this),this.audio=new L(this),this.debug.log(this.TAG_NAME,"_opt",JSON.stringify(this._opt))}destroy(){return new Promise((e,t)=>{this._destroyed=!0,this.emit("destroy"),this.off(),this.events&&(this.events.destroy(),this.events=null),this.clearCheckHeartTimeout(),this.clearCheckLoadingTimeout(),this.clearStatsInterval(),this.decoderWorker?this.decoderWorker.destroy().then(()=>{this.decoderWorker=null,this._destroy(),e()}):(this._destroy(),e())})}_destroy(){this._loading=!1,this._playing=!1,this._hasLoaded=!1,this._destroyed=!1,this._checkHeartTimeout=null,this._checkLoadingTimeout=null,this._checkStatsInterval=null,this._resetStats(),this._audioTimestamp=0,this._times={playInitStart:"",playStart:"",streamStart:"",streamResponse:"",demuxStart:"",decodeStart:"",videoStart:"",playTimestamp:"",streamTimestamp:"",streamResponseTimestamp:"",demuxTimestamp:"",decodeTimestamp:"",videoTimestamp:"",allTimestamp:""},this._tempWorkerStats=null,this.audio&&(this.audio.destroy(),this.audio=null),this.stream&&(this.stream.destroy(),this.stream=null),this.debug.log(this.TAG_NAME,"destroy end"),this._opt=_()}_resetStats(){this._startBpsTime=null,this._playingStartTimestamp=null,this._historyAbpsList=[],this._stats={abps:0,ts:0,buf:0,audioBufferTs:0}}set loaded(e){this._hasLoaded=e}get loaded(){return this._hasLoaded}set playing(e){e&&(this.loading=!1),this.playing!==e&&(this._playing=e,this.emit(d.playing,e),this.emit(d.volumechange,this.volume),e?this.emit(d.play):this.emit(d.pause))}get playing(){return this._playing}get volume(){return this.audio&&this.audio.volume||0}set volume(e){e!==this.volume&&(this.audio?(this.audio.setVolume(e),this._lastVolume=this.volume):this.debug.warn(this.TAG_NAME,"set volume error, audio is null"))}get lastVolume(){return this._lastVolume}set loading(e){this.loading!==e&&(this._loading=e,this.emit(d.loading,this._loading))}get loading(){return this._loading}set audioTimestamp(e){null!==e&&(this._audioTimestamp=e)}get audioTimestamp(){return this._audioTimestamp}get isDebug(){return P(this._opt.debug)}isDestroyed(){return this._destroyed}updateOption(e){this._opt=Object.assign({},this._opt,e)}init(){return new Promise((e,t)=>{this.audio||(this.audio=new L(this)),this.stream||(this.stream=new $(this)),this.decoderWorker?this.loaded?e():this.once(d.decoderWorkerInit,()=>{this.loaded=!0,e()}):(this.decoderWorker=new pe(this),this.once(d.decoderWorkerInit,()=>{this.loaded=!0,e()}))})}play(){let A=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"";return new Promise((e,t)=>{if(!A&&!this._opt.url)return t("url is empty");this.loading=!0,this.playing=!1,this._times.playInitStart=k(),A=A||this._opt.url,this._opt.url=A,this.clearCheckHeartTimeout(),this.init().then(()=>{this._times.playStart=k(),this._opt.isNotMute&&this.mute(!1),this.enableWakeLock(),this.checkLoadingTimeout(),this.stream&&(this.stream.once(p.fetchError,e=>{this.emitError(p.fetchError,e)}),this.stream.once(p.websocketError,e=>{this.emitError(p.websocketError,e)}),this.stream.once(d.streamEnd,()=>{this.emit(d.streamEnd)}),this.stream.once(d.streamSuccess,()=>{e(),this._times.streamResponse=k(),this.checkStatsInterval(),this.debug.log(this.TAG_NAME,"stream success and start play")}),this.stream.fetchStream(A))}).catch(()=>{t()})})}checkLoadingTimeout(){this._checkLoadingTimeout=setTimeout(()=>{this.playing?this.debug.warn(this.TAG_NAME,`checkLoadingTimeout but loading is ${this.loading} and playing is `+this.playing):this.isDestroyed()?this.debug&&this.debug.warn(this.TAG_NAME,"checkLoadingTimeout but player is destroyed"):(this.debug.warn(this.TAG_NAME,"checkLoadingTimeout and pause and emit loadingTimeout event"),this.emit(d.timeout,d.loadingTimeout),this.emit(d.loadingTimeout))},1e3*this._opt.loadingTimeout)}clearCheckLoadingTimeout(){this._checkLoadingTimeout&&(this.debug.log(this.TAG_NAME,"clearCheckLoadingTimeout"),clearTimeout(this._checkLoadingTimeout),this._checkLoadingTimeout=null)}checkStatsInterval(){this.debug.log(this.TAG_NAME,"checkStatsInterval"),this._checkStatsInterval=setInterval(()=>{this.updateStats()},1e3)}clearCheckHeartTimeout(){this.debug.log(this.TAG_NAME,"clearCheckHeartTimeout"),this._checkHeartTimeout&&(clearTimeout(this._checkHeartTimeout),this._checkHeartTimeout=null)}clearStatsInterval(){this.debug.log(this.TAG_NAME,"clearStatsInterval"),this._checkStatsInterval&&(clearInterval(this._checkStatsInterval),this._checkStatsInterval=null)}updateStats(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},A=(this._startBpsTime||(this._startBpsTime=k()),v(e.ts)&&(this._stats.ts=e.ts,null===this._playingStartTimestamp)&&0<e.ts&&(this._playingStartTimestamp=e.ts),v(e.buf)&&(this._stats.buf=e.buf),k()),t=A-this._startBpsTime;if(e.abps&&(this._stats.abps+=e.abps),!(t<1e3)){let e,t=0;this.audio&&this.audio.bufferList&&(e=this.audio.bufferList.length,t=e*this.audio.oneBufferDuration),this._stats.audioBufferTs=parseInt(t,10),this.emit(d.stats,this._stats),this.emit(d.timeUpdate,this._stats.ts),this.updateHistoryAbpsList(this._stats.abps),this._stats.abps=0,this._startBpsTime=A}}enableWakeLock(){this._opt.keepScreenOn&&this.keepScreenOn&&this.keepScreenOn.enable()}releaseWakeLock(){this._opt.keepScreenOn&&this.keepScreenOn&&this.keepScreenOn.disable()}pause(){return new Promise((e,t)=>{const A=()=>{this.stream&&(this.stream.destroy(),this.stream=null),this.off(),this.clearCheckHeartTimeout(),this.clearCheckLoadingTimeout(),this.clearStatsInterval(),this.releaseWakeLock(),this._resetStats(),this.loading=!1,this.playing=!1,this.audio&&this.audio.pause(),this._audioTimestamp=0,this._hasLoaded=!1,this._times={playInitStart:"",playStart:"",streamStart:"",streamResponse:"",demuxStart:"",decodeStart:"",videoStart:"",playTimestamp:"",streamTimestamp:"",streamResponseTimestamp:"",demuxTimestamp:"",decodeTimestamp:"",videoTimestamp:"",allTimestamp:""},U(this)};this.decoderWorker?this.decoderWorker.destroy().then(()=>{this.decoderWorker=null,A(),e()}).catch(e=>{this.debug.error(this.TAG_NAME,"pause() decoderWorker destroy error",e),t(e)}):(A(),setTimeout(()=>{e()},0))})}handleRender(){this.isDestroyed()?this.debug&&this.debug.warn("player","handleRender but player is destroyed"):(this.loading&&(this.clearCheckLoadingTimeout(),this.emit(d.start),this.loading=!1),this.playing||(this.playing=!0))}updateHistoryAbpsList(e){this._historyAbpsList.length>this._opt.heartTimeout&&this._historyAbpsList.shift(),this._historyAbpsList.push(e),this.isHistoryAbpsListAllZero()&&this.checkHeartTimeout$2()}isHistoryAbpsListAllZero(){let t=!0;if(t=this._historyAbpsList.length<this._opt.heartTimeout?!1:t)for(let e=0;e<this._historyAbpsList.length;e++)if(0<this._historyAbpsList[e]){t=!1;break}return t}checkHeartTimeout$2(){var e;this.playing?this.isDestroyed()?this.debug&&this.debug.warn("player","checkHeartTimeout$2 but player is destroyed"):R(this.isHistoryAbpsListAllZero())?this.debug&&this.debug.warn("player","checkHeartTimeout$2 but fps is not all zero"):R(this.visibility)&&0!==this._stats.abps?this.debug&&this.debug.warn("player","checkHeartTimeout$2 but page is not visibility and vbps is "+this._stats.vbps):(e=this._historyAbpsList.join(","),this.debug.warn("player",`checkHeartTimeout$2 and
                pause and emit delayTimeout event and
                current abps is ${this._stats.abps} and
                history abpsList is ${e} and
                current visibility is ${this.visibility} and`),this.emit(d.timeout,d.delayTimeout),this.emit(d.delayTimeout)):this.debug.log("player","checkHeartTimeout$2()  playing is "+this.playing)}getOption(){return this._opt}getDemuxType(){return this.getOption().demuxType}getAudioEngineType(){let e="";return e=this.audio?this.audio.getEngineType():e}mute(e){this.audio&&this.audio.mute(e)}isAudioMute(){let e=!0;return e=this.audio?this.audio.isMute:e}isAudioNotMute(){return!this.isAudioMute()}getPlayType(){return this._opt.playType}emitError(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"";this.emit(e,t),this.emit(d.error,e,t)}}window.JessibucaProAudio=class extends t{constructor(t){super(),this._opt={},this.TAG_NAME="JbProAudio",Object.keys(t).forEach(e=>{if(void 0===t[e])throw new Error(`JbPro option "${e}" can not be undefined`)});var e=_(),e=Object.assign({},e,t);v(e.videoBuffer)&&(e.videoBuffer=1e3*Number(e.videoBuffer)),v(e.videoBufferDelay)&&(e.videoBufferDelay=1e3*Number(e.videoBufferDelay)),v(e.videoBufferMax)&&(e.videoBufferMax=1e3*Number(e.videoBufferMax)),v(e.timeout)&&(I(e.loadingTimeout)&&(e.loadingTimeout=e.timeout),I(e.heartTimeout))&&(e.heartTimeout=e.timeout),this._opt=e,this._destroyed=!1,this._loadingTimeoutReplayTimes=0,this._heartTimeoutReplayTimes=0,this.debug=new C(this),this.events=new z(this),this._initAudioPlayer(),this.debug.log(this.TAG_NAME,'init success and version is "10-10-2023"'),console.log('JbProAudio version is "10-10-2023"')}destroy(){return new Promise((e,t)=>{this.debug.log(this.TAG_NAME,"destroy()"),this._destroyed=!0,this.off(),this.player?this.player.destroy().then(()=>{this.player=null,this._destroy(),e()}).catch(()=>{t()}):(this._destroy(),e())})}_destroy(){this.events&&(this.events.destroy(),this.events=null),this.debug&&this.debug.log("JbPro","destroy end"),this._resetOpt(),this._loadingTimeoutReplayTimes=0,this._heartTimeoutReplayTimes=0}_initAudioPlayer(){this.player=new ge(this._opt),this.debug.log(this.TAG_NAME,"_initPlayer",this.player.getOption()),this._bindEvents()}_resetOpt(){this._opt=_()}_bindEvents(){Object.keys(c).forEach(t=>{this.player.on(c[t],e=>{this.emit(t,e)})})}_play(){let r=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"";return new Promise((t,A)=>{var s,i=0===r.indexOf("http")?2:1;if(-1!==r.indexOf(".flv")&&!this._opt.isFlv&&(this._opt.isFlv=!0),s=this._opt.isFlv?"flv":"m7s",!i||!s)return A(`play protocol is ${i}, demuxType is `+s);this.player.updateOption({protocol:i,demuxType:s,isFlv:this._opt.isFlv}),this.player.once(p.fetchError,e=>{this.emit(d.crashLog,this._getCrashLog(p.fetchError,e)),this.debug.log(this.TAG_NAME,"fetch error and reset player"),this.pause().then(()=>{this.emit(d.playFailedAndPaused,p.fetchError)}).catch(e=>{this.debug.error(this.TAG_NAME,"fetch error and pause",e)})}),this.player.once(d.streamEnd,e=>{this.emit(d.crashLog,this._getCrashLog(d.streamEnd,e)),this.debug.log(this.TAG_NAME,"streamEnd and reset player"),this.pause().then(()=>{this.emit(d.playFailedAndPaused,d.streamEnd)}).catch(e=>{this.debug.error(this.TAG_NAME,"streamEnd and pause",e)})}),this.player.once(p.websocketError,e=>{this.emit(d.crashLog,this._getCrashLog(p.websocketError,e)),this.debug.log(this.TAG_NAME,"websocketError and reset player"),this.pause().then(()=>{this.emit(d.playFailedAndPaused,p.websocketError)}).catch(e=>{this.debug.error(this.TAG_NAME,"websocketError and pause",e)})}),this.player.on(d.delayTimeout,e=>{this.emit(d.crashLog,this._getCrashLog(d.delayTimeout,e)),this.isDestroyed()?this.debug.log(this.TAG_NAME,"delay timeout but player is destroyed"):this.pause().then(()=>{var e;this.player&&this.player._opt.heartTimeoutReplay&&(this._heartTimeoutReplayTimes<this.player._opt.heartTimeoutReplayTimes||-1===this.player._opt.heartTimeoutReplayTimes)?(this.debug.log(this.TAG_NAME,`delay timeout and
                        replay time is ${this._heartTimeoutReplayTimes} and
                        heartTimeoutReplayTimes is `+this.player._opt.heartTimeoutReplayTimes),this.isDestroyed()?this.debug&&this.debug.warn("Jessibuca","delay timeout replay but player is destroyed"):(this._heartTimeoutReplayTimes+=1,e=this._opt.url,this.play(e).then(()=>{}).catch(e=>{this.debug.error(this.TAG_NAME,"delay timeout replay error",e)}))):(this.emit(d.playFailedAndPaused,d.delayTimeout),this.emit(d.delayTimeoutRetryEnd),this.debug.warn(this.TAG_NAME,`delayTimeoutRetryEnd and
                            opt.heartTimeout is ${this.player&&this.player._opt.heartTimeout} and
                            opt.heartTimeoutReplay is ${this.player&&this.player._opt.heartTimeoutReplay} and
                            opt.heartTimeoutReplayTimes is ${this.player&&this.player._opt.heartTimeoutReplayTimes},and
                            local._heartTimeoutReplayTimes is `+this._heartTimeoutReplayTimes))}).catch(e=>{this.debug.error(this.TAG_NAME,"delay timeout and pause error",e)})}),this.player.on(d.loadingTimeout,t=>{this.emit(d.crashLog,this._getCrashLog(d.loadingTimeout,t)),this.isDestroyed()?this.debug.log(this.TAG_NAME,"loading timeout but player is destroyed"):this.pause().then(()=>{var t;this.player&&this.player._opt.loadingTimeoutReplay&&(this._loadingTimeoutReplayTimes<this.player._opt.loadingTimeoutReplayTimes||-1===this.player._opt.loadingTimeoutReplayTimes)?(this.debug.log(this.TAG_NAME,`loading timeout and
                             replay time is ${this._loadingTimeoutReplayTimes} and
                             loadingTimeoutReplayTimes is `+this.player._opt.loadingTimeoutReplayTimes),this.isDestroyed()?this.debug&&this.debug.warn(this.TAG_NAME,"loading timeout replay but player is destroyed"):(this._loadingTimeoutReplayTimes+=1,t=this._opt.url,this.play(t).then(()=>{}).catch(()=>{this.debug.error("Jessibuca","loading timeout replay error",e)}))):(this.emit(d.playFailedAndPaused,d.loadingTimeout),this.emit(d.loadingTimeoutRetryEnd))}).catch(e=>{this.debug.error(this.TAG_NAME,"loading timeout and pause error",e)})}),this.player.play(r).then(()=>{t()}).catch(e=>{this.debug.error(this.TAG_NAME,"hasLoaded and play error",e),this.emit(d.crashLog,this._getCrashLog("hasLoaded and play error",e)),this.player.pause().then(()=>{A(e)}).catch(e=>{this.debug.error(this.TAG_NAME,"hasLoaded and play error and next pause error",e)})})})}isDestroyed(){return this._destroyed}play(){let A=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"";return new Promise((e,t)=>(this.debug.log(this.TAG_NAME,"play() "+A),A||this._opt.url?(A&&(A=(""+A).trim(),this._opt.url=A),this.player?void(this.player.playing||this.player.loading?(this.debug.warn(this.TAG_NAME,`play() and player is playing ${this.player.playing} or loading ${this.player.loading} and pause to play`),this.player.pause().then(()=>{this._play(this._opt.url).then(()=>{e()}).catch(e=>{t(e)})})):this._play(this._opt.url).then(()=>{e()}).catch(e=>{t(e)})):t("player is null")):(this.emit(d.error,p.playError),void t("url is null and this._opt.url is null"))))}mute(){this.debug.log(this.TAG_NAME,"mute()"),this.player&&this.player.mute(!0)}cancelMute(){this.debug.log(this.TAG_NAME,"cancelMute()"),this.player&&this.player.mute(!1)}pause(){return new Promise((t,A)=>{this.debug.log(this.TAG_NAME,"pause()"),this.player?this.player.pause().then(e=>{this._bindEvents(),t(e)}).catch(e=>{A(e)}):A("player is null")})}setVolume(e){this.debug.log(this.TAG_NAME,"setVolume() "+e),this.player&&(this.player.volume=e)}getVolume(){let e=null;return this.player&&(e=this.player.volume,e=parseFloat(e).toFixed(2)),e}setTimeout(e){this.debug.log(this.TAG_NAME,"setTimeout() "+e),e=Number(e),this.player&&this.player.updateOption({timeout:e,loadingTimeout:e,heartTimeout:e})}audioResume(){this.debug.log(this.TAG_NAME,"audioResume()"),this.player&&this.player.audio?this.player.audio.audioEnabled(!0):this.debug.warn(this.TAG_NAME,"audioResume() player is not init")}setDebug(e){this.debug.log(this.TAG_NAME,"setDebug() "+e),this.player?this.player.updateOption({debug:!!e}):this.debug.warn(this.TAG_NAME,"setDebug() player is not init")}_getCrashLog(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"";if(this.player){var A=this.player,t={url:this._opt.url,playType:a,demuxType:A.getDemuxType(),audioInfo:{encType:"",sampleRate:"",channels:""},audioEngine:A.getAudioEngineType(),timestamp:k(),type:e,error:function(e){const t=Object.prototype.toString;return function(e){switch(t.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return 1;default:try{return e instanceof Error}catch(e){}}}(e)?e.message:null==e?"":"object"==typeof e?JSON.stringify(e,null,2):String(e)}(t)||e};if(A.audio){const e=A.audio.audioInfo||{};t.audioInfo={encType:e.encType||"",sampleRate:e.sampleRate||"",channels:e.channels||""}}return t}}}});
