import request from '@/utils/request';
import {
  LINE_PATH,
  MAIN_PATH,
  APPLICTION_WORKSPACES,
  API_VERSION,
  APPLICTION_FLIGHT,
  APPLICTION_RECORD,
  MAP_PATH
} from '../config/index';
import { useUserStoreHook } from '@/store/modules/user';

// 主路径

const BASE_URL = MAP_PATH + API_VERSION + APPLICTION_WORKSPACES;

/**
 * 获取飞行区域列表
 *
 * @param
 */
export function getFllightAreas() {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/flight-areas`,
    method: 'get'
  });
}

/**
 * 创建飞行区域
 */
export function addFlightArea(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/flight-area`,
    method: 'post',
    data
  });
}

/**
 * 删除飞行区域
 *
 * @param area_id
 */
export function deleteFlighArea(area_id) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/flight-area/${area_id}`,
    method: 'delete'
  });
}

/**
 * 更新
 */
export function updateFlighArea(data, area_id) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/flight-area/${area_id}`,
    method: 'put',
    data: data
  });
}

// 同步
export function syncUpdateFlighArea(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/flight-area/sync`,
    method: 'post',
    data: data
  });
}
