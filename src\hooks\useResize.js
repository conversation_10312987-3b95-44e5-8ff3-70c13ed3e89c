export default function useResize() {
  /**
   * Attaches an event listener to the window's resize event and calls the
   * provided callback function whenever the window is resized. The event
   * listener is automatically removed when the component is unmounted.
   *
   * @param {function} onResize - The callback function to be called when the
   * window is resized.
   * @return {void}
   */
  function callResize(onResize) {
    onMounted(() => {
      window.addEventListener('resize', onResize);
    });
    onBeforeUnmount(() => {
      window.removeEventListener('resize', onResize);
    });
  }

  return {
    callResize
  };
}
