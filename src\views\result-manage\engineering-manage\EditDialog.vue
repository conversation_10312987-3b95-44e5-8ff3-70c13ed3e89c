<template>
  <el-dialog
    :title="title"
    :model-value="visible"
    v-if="visible"
    width="600px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form class="app-form" ref="dataFormRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="工程名称" prop="title">
        <el-input
          v-model="form.title"
          placeholder="请输入工程名称"
          maxlength="50"
          clearable
          @blur="form.title = $event.target.value.trim()"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-loading="loading">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import optionData from '@/utils/option-data';
import moment from 'moment';
import { addProject } from '@/api/achievement/project';
import { Plus, Minus } from '@element-plus/icons-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  formData: {
    type: Object,
    default(rawProps) {
      return {};
    }
  }
});
const form = reactive({});
const dataFormRef = ref(null);

watch(
  () => props.formData,
  (newVal, oldVal) => {
    Object.assign(form, newVal);
  },
  { deep: true }
);
const emit = defineEmits(['update:visible', 'submit']);

const rules = reactive({
  title: [{ required: true, message: '请输入工程名称', trigger: 'blur' }]
});

defineExpose({});

const loading = ref(false);

// 关闭弹窗
function closeDialog() {
  resetForm();
  emit('update:visible', false);
}
/**
 * 重置表单
 */
function resetForm() {
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  loading.value = false;
  Object.keys(form).map(key => {
    delete form[key];
  });
}

function handleSubmit() {
  dataFormRef.value.validate(isValid => {
    if (isValid) {
      let params = { ...form };
      loading.value = true;
      addProject(params)
        .then(res => {
          loading.value = false;
          ElMessage.success('新增成功');
          closeDialog();
          emit('submit');
        })
        .catch(e => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}

onMounted(() => {});
</script>
<style scoped lang="scss">
.input-serach {
  width: 200px;
}
.app-form {
  .select-time {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 5px 0;
  }
  ::v-deep {
    .el-input-number,
    .el-select {
      width: 100%;
    }
    .el-input-number .el-input__inner {
      text-align: left;
    }
    .el-input-number.is-controls-right .el-input__wrapper {
      padding-left: 11px;
    }
    .el-date-editor.el-input,
    .el-date-editor.el-input__wrapper {
      width: 100%;
    }
  }
}
</style>
