#app {
  // min-width: $appMainMinWidth;
  .main-container {
    position: relative;
    height: 100%;
    transition: margin-left 0.28s;
    overflow-y: hidden;
    margin-left: $sideBarWidth;
  }

  .sidebar-container {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    width: $sideBarWidth;
    height: 100%;
    // overflow: hidden;
    background-color: $menuBg;
    transition: width 0.28s;

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out,
        0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 60px);
        padding-top: 2px;
      }
    }

    .is-horizontal {
      display: none;
    }

    .svg-icon {
      margin-right: 16px;
    }

    .sidebar-icon {
      margin-right: 16px;
    }

    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100%;

      & .el-menu-item:hover {
        color: $menuHoverText;
      }
    }

    // menu hover
    .el-sub-menu__title {
      &:hover {
        background-color: $menuHover !important;
        color: $menuHoverText !important;
      }
    }

    .is-active > .el-sub-menu__title {
      color: $menuActiveText !important;
      &:hover {
        background: none !important;
      }
    }

    .el-sub-menu .el-menu-item.is-active {
      color: $subMenuActiveText;
      &:hover {
        background: none;
        color: $subMenuActiveHoverText !important;
      }
    }

    & .nest-menu .el-sub-menu > .el-sub-menu__title,
    & .el-sub-menu .el-menu-item {
      min-width: $sideBarWidth;
      background-color: $subMenuBg;

      &:hover {
        color: $subMenuHoverText;
        background: $menuHover;
      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: $miniSideBarWidth;

      .svg-icon {
        margin-right: 0px;
      }

      .sidebar-icon {
        margin-right: 0px;
      }
    }

    .main-container {
      // width: calc(100vw - $miniSideBarWidth);
      margin-left: $miniSideBarWidth;
    }

    .el-sub-menu {
      overflow: hidden;

      & > .el-sub-menu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

        .sidebar-icon {
          margin-left: 20px;
        }

        .sub-el-icon {
          margin-left: 19px;
        }

        .el-sub-menu__icon-arrow {
          display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-sub-menu {
        & > .el-sub-menu__title {
          & > span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-sub-menu {
    min-width: $sideBarWidth;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
      width: 100vw;
    }

    .sidebar-container {
      transition: transform 0.28s;
      // 在小屏下，侧边栏的宽度为sideBarWidth
      width: $sideBarWidth;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d($hideSideBarWidth, 0, 0);
      }
    }
  }

  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  & > .el-menu {
    .svg-icon {
      margin-right: 16px;
    }

    .sidebar-icon {
      margin-right: 16px;
    }

    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }
  }

  .nest-menu .el-sub-menu > .el-sub-menu__title,
  .el-menu-item {
    margin-bottom: 2px;
    &:hover {
      // you can use $subMenuHover
      background-color: $menuHover;
    }
  }

  // the scroll bar appears when the subMenu is too long
  > .el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}
