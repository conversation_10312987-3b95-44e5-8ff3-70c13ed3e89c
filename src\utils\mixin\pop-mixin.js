export default {
  data() {
    return {
      dialogVisible: false,
      submitLoading: false
    };
  },
  methods: {
    // 关闭弹窗，若存在form表单则刷新表单
    dialogCancel(done) {
      Object.assign(this.$data, this.$options.data.call(this));
      nextTick(() => {
        if (this.$refs['form']) {
          this.$refs['form'].clearValidate();
        }
      });
      this.$emit('dialogCencel');
      if (typeof done === 'function') {
        done();
      }
    },
    // 处理弹窗关闭操作(如，新增后刷新列表)
    dialogClose(data) {
      this.$emit('beforeClose', data);
      this.dialogCancel();
    },
    dialogOpen() {
      this.dialogVisible = true;
    }
  }
};
