import BaseSocketService from './BaseSocketService';

import { useUserStoreHook } from '@/store/modules/user';
/**
 * 平台通用websocket
 * 使用示例：
 * import MainSocketService from '@/utils/websocket/MainSocketService';
 *
 * const wsIns = MainSocketService.Instance;
 * wsIns.connect(()=>{
 *      // 连接成功可以执行发送订阅
 *      wsIns.send(xx,{})
 *      wsIns.subscribe(xx,()=>{
 *          // 执行订阅成功后事件
 *      })
 * })
 *
 * // 应用销毁时断开连接
 * wsIns.disconnect()
 */
class MainSocketService extends BaseSocketService {
  static instance = null;

  constructor() {
    super();
    const userStore = useUserStoreHook();

    let baseUrl = '';
    if (import.meta.env.VITE_APP_NODE_ENV === 'development') {
      baseUrl = import.meta.env.VITE_WS_BASE_URL;
    } else {
      baseUrl = location.host + import.meta.env.VITE_APP_BASE_API + '/';
    }

    this.wsRequestUrl = `ws://${baseUrl}api/v1/ws?x-auth-token=${userStore.token}`;
  }

  static get Instance() {
    if (!this.instance) {
      this.instance = new MainSocketService();
    }
    return this.instance;
  }
}

export default MainSocketService;
