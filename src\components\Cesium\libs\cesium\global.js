import CesiumEngine from './engine';
import TimeLineCesiumEngine from './timeLineEngine';
// 全局对象用于存储实例
const globalInstances = {};
// 实例集合
const planInstances = {};

/**
 * 全局函数用于获取或创建实例
 * @param {string} instanceKey
 * @returns
 */
export function getOrCreateCesiumEngineInstance(instanceKey) {
  if (!globalInstances[instanceKey]) {
    globalInstances[instanceKey] = new CesiumEngine();
  }
  return globalInstances[instanceKey];
}

/**
 * 全局函数用于获取或创建带有时间轴的实例
 * @param {string} instanceKey
 * @returns
 */
export function getOrCreateTimeLineCesiumEngineInstance(instanceKey) {
  if (!globalInstances[instanceKey]) {
    globalInstances[instanceKey] = new TimeLineCesiumEngine();
  }
  return globalInstances[instanceKey];
}

export function getCesiumEngineInstance(instanceKey) {
  if (!globalInstances[instanceKey]) {
    return null;
  }
  return globalInstances[instanceKey];
}

/**
 * 全局函数用于在卸载时移除实例
 * @param {string} instanceKey - 实例的唯一标识符
 * @returns {boolean} - 表示是否成功移除实例
 */
export function delCesiumEngineInstance(instanceKey) {
  const instance = globalInstances[instanceKey];

  // 检查实例是否存在
  if (!instance) {
    return false;
  }

  // 销毁 Cesium 实例
  if (instance.viewer) {
    instance.destroyed();
    instance.viewer.destroy();
  }

  // 从全局实例对象中移除
  delete globalInstances[instanceKey];
  return true;
}

/**
 * 设置plan实例
 * @param {*} planInstance plan实体
 * @param {*} instanceKey 实体对应的key
 * @returns 返回 planInstance
 */
export function setPlanInstance(planInstance, instanceKey = 'default') {
  if (instanceKey === 'default') {
    planInstances['default'] = planInstance;
  } else if (!planInstances[instanceKey]) {
    planInstances[instanceKey] = planInstance;
  }
  return planInstances[instanceKey];
}

/**
 * 根据key获取plan实例
 * @param {*} instanceKey 实体对应的key
 * @returns 返回 planInstance
 */
export function getPlanInstance(instanceKey = 'default') {
  if (instanceKey === 'default') {
    return planInstances['default'];
  }
  return planInstances[instanceKey] || null;
}
