import 'element-plus/dist/index.css';
import { ElMessageBox } from 'element-plus';
export const appendChildToBtns = () => {
  // 获取div元素
  let btns = document.querySelector('.el-message-box__btns');
  // 创建一个新的btn元素
  let btn = document.createElement('button');
  // 为btn元素设置文本内容 添加饿了么组件按钮样式class
  btn.className = 'el-button el-button--default';
  btn.textContent = '取消';
  // 将btn元素添加到btns中 插入到第一个位置
  btns.insertBefore(btn, btns.children[0]);
  // btns.appendChild(btn);
  // 点击按钮关闭提示框
  btn.onclick = () => {
    ElMessageBox.close();
  };
};
