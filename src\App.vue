<template>
  <el-config-provider :locale="appStore.locale" :size="appStore.size">
    <router-view />
  </el-config-provider>
 
</template>
<script setup>
import { ElConfigProvider } from 'element-plus';
import { useAppStore } from '@/store/modules/app.js';
import { onMounted, onUnmounted, ref } from 'vue';

const appStore = useAppStore();

onUnmounted(() => {

});
</script>
<style scoped lang="scss">
.content {
  font-size: 16px;
  margin-bottom: 10px;
  .label-color {
    color: #333;
  }
}
</style>
