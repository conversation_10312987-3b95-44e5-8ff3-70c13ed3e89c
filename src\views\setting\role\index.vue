<script>
export default {
  name: 'user'
};
</script>

<script setup>
import { toRaw } from 'vue';
import {
  getRolePage,
  addRole,
  editRoleInfo,
  deleteUserList,
  getMenuTree,
  getUserMenuInfo,
  saveMenu
} from '@/api/system/dept.js';
import { ElMessage } from 'element-plus';
import { EditPen } from '@element-plus/icons-vue';
import { useUserStoreHook } from '@/store/modules/user';
const userStore = useUserStoreHook();
const { userData } = userStore;
const { is_super_admin } = userData;

const dataFrom = ref(ElForm); // 数据源表单
const userVisible = ref(false); // 新增用户-显隐
const userRecord = reactive({
  name: '',
  remark: ''
});
const queryParams = reactive({
  pageNo: 1,
  pageSize: 9999
});
const userList = ref();
const userId = ref('');
const menuTree = ref([]);
const defaultProps = {
  children: 'children',
  label: 'name'
};
const roleTreeRef = ref(ElTree); // 新增角色权限树
const selectMenu = ref([]);
const canEdit = ref(false);
const isEdit = ref(false);
const userRule = reactive({
  name: [{ required: true, message: '角色名称不能为空', trigger: 'blur' }],
  code: [
    { required: true, message: '角色编码不能为空', trigger: 'blur' },
    {
      required: true,
      pattern: /^[a-zA-Z0-9]+$/,
      message: '角色编码只能包含数字和字母',
      trigger: 'blur'
    }
  ],
  sort: [{ required: true, message: '显示顺序不能为空', trigger: 'blur' }]
});

/**
 * 获取角色下拉列表
 */
async function getRoleOptions() {
  const res = await getMenuTree({});
  menuTree.value = res;
}

function renderTree(value = true) {
  menuTree.value.forEach(item => {
    if (item.id === '3') {
      item.disabled = true;
      roleTreeRef.value.setChecked('3', true);
    } else {
      item.disabled = value;
    }

    if (item?.children && item?.children.length > 0) {
      item.children.forEach(res => {
        res.disabled = value;
        if (res?.children && res?.children.length > 0) {
          res.children.forEach(resp => {
            resp.disabled = value;
          });
        }
      });
    }
  });
}

// 关闭数据源弹框
function closeData() {
  dataFrom.value.clearValidate();
  userVisible.value = false;
  userRecord.name = '';
  userRecord.remark = '';
  userRecord.sort = '';
  userRecord.code = '';
}

function saveData() {
  dataFrom.value.validate(valid => {
    if (valid) {
      if (isEdit.value) {
        editRoleInfo({
          id: userId.value,
          ...userRecord
        }).then(data => {
          ElMessage.success('更新成功');
          userVisible.value = false;
          userRecord.name = '';
          userRecord.code = '';
          userRecord.sort = '';
          userRecord.remark = '';
          initRoleList();
        });
        return;
      }
      addRole({
        ...userRecord
      }).then(data => {
        ElMessage.success('新增成功！');
        userVisible.value = false;
        userRecord.name = '';
        userRecord.code = '';
        userRecord.sort = '';
        userRecord.remark = '';
        initRoleList();
      });
    }
  });
}

// 获取成员列表
function initRoleList() {
  getRolePage({
    ...queryParams
  }).then(data => {
    const list = data.list;
    userList.value = list;

    if (list.length === 0) {
      userId.value = '';
      canEdit.value = false;
      renderTree(true);
      searchMenuInfo();
      return;
    }

    const selectedItem = list[0];
    userId.value = selectedItem.id || '';
    const isCustomRole = selectedItem.type === 2;

    canEdit.value = is_super_admin || isCustomRole;

    if (is_super_admin) {
      renderTree(false);
    } else {
      renderTree(!isCustomRole);
    }

    searchMenuInfo();
  });
}

function searchMenuInfo() {
  getUserMenuInfo({
    roleId: userId.value
  }).then(data => {
    roleTreeRef.value.setCheckedKeys(data);
  });
}

onMounted(async () => {
  await getRoleOptions();
  initRoleList();
});

/**
 * 根据选定的用户项更新用户信息和界面权限
 * 此函数用于当用户在界面选择不同的用户项时，更新用户的ID、编辑权限，并重新加载菜单信息
 * 同时，根据用户类型和是否为超级管理员，决定是否启用树形结构的渲染
 *
 * @param {Object} item - 用户项对象，包含用户的相关信息，如ID和类型
 */
function changeUser(item) {
  // 将响应式对象转换为原始对象，以便直接访问其属性
  const rawItem = toRaw(item);
  // 更新当前用户的ID
  userId.value = rawItem.id;
  // 根据是否为超级管理员或用户类型为2，决定是否可以编辑
  canEdit.value = is_super_admin || rawItem.type === 2;

  // 重置所有权限，以确保权限的正确性
  takeall();
  // 搜索并加载菜单信息，以更新界面
  searchMenuInfo();

  // 根据用户类型和是否为超级管理员，决定是否渲染树形结构
  if (rawItem.type === 1 && !is_super_admin) {
    renderTree(true);
  } else {
    renderTree(false);
  }
}
function addUser() {
  isEdit.value = false;
  userVisible.value = true;
}

function deleteUser(id) {
  ElMessageBox.confirm(`确认后无法进行恢复`, '确认删除所选角色？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    autofocus: false,
    closeOnClickModal: false,
    type: 'warning'
  }).then(() => {
    deleteUserList({
      id: id
    }).then(data => {
      ElMessage.success('删除成功');
      initRoleList();
    });
  });
}

function save() {
  saveMenu({
    role_id: userId.value,
    menu_ids: roleTreeRef.value.getCheckedKeys()
  }).then(data => {
    ElMessage.success('操作成功');
    searchMenuInfo();
  });
}

function cancel() {
  searchMenuInfo();
}

function takeall() {
  const nodes = roleTreeRef.value.store._getAllNodes();
  nodes.forEach(item => {
    item.expanded = false;
  });
}

function editRole(item) {
  const data = toRaw(item);
  isEdit.value = true;
  userVisible.value = true;
  userRecord.name = data.name;
  userRecord.sort = data.sort;
  userRecord.code = data.code;
  userRecord.remark = data.remark;
}

function handleCheck(data, checked) {
  const dashboardBtnId = '1001'; // 后台管理的节点id
  const dashboardId = '3'; // 仪表盘的节点id

  if (data.id === dashboardBtnId) {
    // 如果选中了后台管理，则自动选中仪表盘
    if (!checked.checkedKeys.includes(dashboardId)) {
      roleTreeRef.value.setChecked(dashboardId, true);
    } // 如果取消选中后台管理，则取消选中仪表盘
    else {
      roleTreeRef.value.setChecked(dashboardId, false);
    }
  }
}
</script>

<template>
  <div class="app-container column-container">
    <div class="app-aside">
      <el-scrollbar height="820px">
        <div class="app-aside-container">
          <div class="role-title flex">
            <span>系统角色</span>
            <svg-icon iconClass="add" style="margin-top: 20px; color: #98a2b3; cursor: pointer" @click="addUser" />
          </div>
          <div
            v-for="item in userList"
            :key="item.id"
            :class="userId == item.id ? ['user-list', 'currentColor'] : ['user-list']"
            @click="changeUser(item)"
          >
            <div class="user-name">{{ item.name }}</div>
            <div class="user-remark ellipsis" :title="item.remark">角色描述： {{ item.remark || '暂无' }}</div>
            <div class="edit-icon" v-if="item.type == 2">
              <el-icon style="margin-top: 20px; margin-right: 5px" color="#98A2B3"
                ><EditPen @click="editRole(item)"
              /></el-icon>
            </div>
            <div class="delete-icon" v-if="item.type == 2">
              <svg-icon
                iconClass="delete"
                style="margin-top: 20px; color: #98a2b3; cursor: pointer"
                @click="deleteUser(item.id)"
              />
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
    <div class="app-main-content">
      <div class="save-box">
        <el-button @click="cancel" :disabled="!canEdit">取消</el-button>
        <el-button type="primary" :disabled="!canEdit" @click="save">保存</el-button>
      </div>
      <el-tree
        style="max-width: 600px; max-height: 680px; overflow-y: auto"
        :data="menuTree"
        show-checkbox
        ref="roleTreeRef"
        v-model="selectMenu"
        node-key="id"
        :props="defaultProps"
      />
    </div>

    <!-- 创建角色弹窗 -->
    <el-dialog
      v-model="userVisible"
      v-if="userVisible"
      width="530px"
      append-to-body
      @close="closeData"
      class="common-dialog"
      :close-on-click-modal="false"
    >
      <template #header>
        <div class="common-title">{{ isEdit ? '编辑角色' : '新增角色' }}</div>
      </template>
      <el-form ref="dataFrom" :model="userRecord" label-width="80px" :rules="userRule">
        <div class="dataDialg-box">
          <el-form-item prop="name" label="角色名称">
            <el-input maxlength="15" v-model="userRecord.name" placeholder="请输入角色名称" />
          </el-form-item>
          <el-form-item prop="code" label="角色编码">
            <el-input maxlength="15" v-model="userRecord.code" placeholder="请输入角色编码" />
          </el-form-item>
          <el-form-item prop="sort" label="显示顺序">
            <el-input-number
              v-model="userRecord.sort"
              :min="1"
              :max="9999"
              :step="1"
              controls-position="right"
              style="width: 250px"
              placeholder="请输入显示顺序"
            />
          </el-form-item>
          <el-form-item label="角色描述">
            <el-input
              v-model="userRecord.remark"
              placeholder="请输入角色描述"
              maxlength="120"
              :autosize="{ minRows: 2, maxRows: 4 }"
              type="textarea"
            />
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="saveData">确认</el-button>
          <el-button @click="closeData">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
::-webkit-scrollbar {
  width: 8px; /* 设置滚动条的宽度 */
}
::-webkit-scrollbar-thumb {
  background-color: rgba(46, 144, 255, 0.5);
  border-radius: 2px; /* 设置滚动条滑块的背景色 */
}
.save-box {
  height: 60px;
  line-height: 60px;
  text-align: right;
}
.role-name {
  font-size: 14px;
  color: #2a8b7d;
  text-align: left;
  line-height: 22px;
  font-weight: 400;
}

.user-tree-title {
  padding-right: 88px;
  .user-tree-icon {
    display: none;
  }
  &:hover {
    .user-tree-icon {
      display: block;
    }
  }
}
.tree-limit {
  :deep(.el-tree) {
    .el-tree-node.is-current > .el-tree-node__content {
      background: #e3eef5;
      .user-tree-title {
        color: var(--el-color-primary) !important;
      }
    }
    .el-tree-node__content {
      width: 284px;
      height: 40px;
      line-height: 40px;
    }
  }
}
.app-container {
  padding: 16px 20px;
  .search {
    padding: 18px 0 0 10px;
    margin-bottom: 10px;
    border-radius: 4px;
    border: 1px solid var(--el-border-color-light);
    box-shadow: var(--el-box-shadow-light);
    background-color: var(--el-bg-color-overlay);
  }
  &.column-container {
    display: flex;
    flex-direction: row;
    width: 100%;
    height: calc(100vh - 60px);
    .app-aside {
      width: 23%;
      height: 100%;
      padding: 16px 20px;
      // padding-bottom: 0;
      background: #11253e;
      box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
      border-radius: 4px;
      .app-aside-container {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 95%;
        padding: 0 8px 20px 8px;
        overflow-x: hidden;
        .app-aside-content {
          flex: 1;
          width: 100%;
          height: 0;
          // overflow: auto;
          &::-webkit-scrollbar {
            width: 10px !important;
            height: 10px !important;
            background: #e4e7ec;
            border-radius: 4px;
          }
          &::-webkit-scrollbar-thumb {
            width: 10px !important;
            min-height: 20px !important;
            background: #b7d9fd !important;
            border-radius: 4px !important;
          }
        }
      }
    }
    .app-main-content {
      flex: 1;
      width: 0;
      height: calc(100vh - 90px);
      background: #11253e;
      padding: 16px;
      margin-left: 16px;
    }
  }
  .user-list {
    width: 100%;
    height: 98px;
    padding-top: 20px;
    padding-left: 16px;
    position: relative;
    cursor: pointer;
    .user-name {
      color: #fff;
      margin-bottom: 10px;
      font-size: 14px;
    }
    .user-remark {
      color: #98a2b3;
      font-size: 14px;
    }
    .edit-icon {
      height: 16px;
      line-height: 16px;
      position: absolute;
      top: 0;
      right: 42px;
    }
    .delete-icon {
      height: 16px;
      line-height: 16px;
      color: #98a2b3;
      position: absolute;
      top: 0;
      right: 25px;
    }
  }
  .flex {
    display: flex;
    justify-content: space-between;
  }
  .currentColor {
    color: #337dfe;
    border-left: 2px solid #1177fb;
    background: linear-gradient(90deg, rgba(0, 148, 255, 0.17) 0%, rgba(0, 148, 255, 0) 100%);
  }
  .role-title {
    height: 58px;
    line-height: 58px;
    width: 100%;
    border-bottom: 1px solid #344054;
    color: #fff;
  }
  .custom-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 48px;
    line-height: 48px;
    margin-bottom: 16px;
    background: #fff;
    vertical-align: middle;
  }
  .custom-preview {
    width: 100%;
    height: 435px;
    overflow-y: hidden;
    padding: 16px 24px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
  }
  .custom-search-box {
    width: 100%;
    padding: 16px 24px;
    margin-bottom: 16px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
    position: relative;
    .custom-search {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      background: #fff;
      border-radius: 4px;
      box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
      margin-bottom: 12px;
      .custom-screen-search {
        position: absolute;
        right: 0;
        top: 0;
      }
      .search-content {
        display: flex;
        flex-direction: row;
        align-items: center;
      }
      .custom-library {
        width: 31%;
        margin-right: 8px;
      }
      .search-param {
        display: flex;
        align-items: center;
        white-space: nowrap;
        font-size: 14px;
        color: #344054;
        line-height: 22px;
        font-weight: 400;
        margin-right: 5px;
      }
      + .app-content {
        max-height: calc(100vh - 202px);
      }
    }
    .custom-content {
      width: 100%;
      max-height: calc(100vh - 154px);
      padding: 16px 24px;
      background: #fff;
      overflow: auto;
      box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
      border-radius: 4px;
      &::-webkit-scrollbar {
        width: 6px !important;
        height: 6px !important;
      }
      &::-webkit-scrollbar-thumb {
        width: 6px !important;
        min-height: 40px !important;
        background: hsla(0, 0%, 76.9%, 0.7) !important;
        border-radius: 62px !important;
      }
      .btn-box {
        margin-bottom: 16px;
      }
      .textHidden {
        width: 180px;
        height: 20px;
        line-height: 20px;
        text-align: left;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
  .search-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 16px 16px 16px 8px;
    margin-bottom: 16px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
    .search-content {
      display: flex;
      flex-direction: row;
      align-items: center;
    }
    .search-param {
      display: flex;
      align-items: center;
      white-space: nowrap;
      font-size: 14px;
      color: #475467;
      line-height: 22px;
      font-weight: 400;
      min-width: 56px;
      margin-left: 8px;
      margin-right: 8px;
    }
    + .app-content {
      max-height: calc(100vh - 202px);
    }
  }
  .app-content {
    width: 100%;
    max-height: calc(100vh - 154px);
    padding: 16px 24px;
    background: #fff;
    overflow: auto;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
    border-radius: 4px;
    &::-webkit-scrollbar {
      width: 10px !important;
      height: 10px !important;
      background: #e4e7ec;
      border-radius: 4px;
    }
    &::-webkit-scrollbar-thumb {
      width: 10px !important;
      min-height: 20px !important;
      background: #b7d9fd !important;
      border-radius: 4px !important;
    }
    .btn-box {
      margin-bottom: 16px;
    }
    .textHidden {
      width: 180px;
      height: 20px;
      line-height: 20px;
      text-align: left;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
  .page-title {
    font-size: 16px;
    color: #101828;
    text-align: left;
    line-height: 16px;
    font-weight: 600;
  }
}
.common-title {
  color: #fff;
}
</style>
