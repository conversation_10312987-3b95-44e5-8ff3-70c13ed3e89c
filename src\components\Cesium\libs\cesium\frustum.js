import * as Cesium from 'cesium';
import { toCartesian3 } from './common';

class CreateFrustum {
  frustumGeometry = null;

  /**
   * 创建视锥体
   * @param {object} options - The options for configuring the camera.
   * @param {Viewer} options.viewer - The Cesium Viewer instance to which the camera will be added.
   * @param {Cesium.Cartesian3 || []} options.position - The initial position of the camera in Cartesian 3 coordinates.
   * @param {number} [options.fov=30] - The field of view of the camera in degrees.
   * @param {number} [options.near=0.10] - The near clipping plane distance of the camera.
   * @param {number} [options.far=200] - The far clipping plane distance of the camera.
   * @param {number} [options.heading=0] - 角度值 比如 90度 157 度.
   * @param {number} [options.pitch=0] - 角度值 比如 90度 157 度.
   * @param {number} [options.roll=-90] - 角度值 比如 90度 157 度.
   * @param {number} [options.height=45] - The aspect ratio of the camera.
   * @param {number} [options.width=60] - The aspect ratio of the camera.
   * @param {string} [options.colorType='camera'] - 用于区分颜色使用
   */
  constructor(options) {
    this.viewer = options.viewer;
    this.position = toCartesian3(options.position);
    this.fov = options.fov || 26;
    this.near = options.near || 0.1;
    this.far = options.far || 200;
    this.heading = options.heading || 0;
    this.pitch = options.pitch || 0; // 基本不动
    this.roll = options.roll || -90; // 向北
    this.width = options.width || 60;
    this.height = options.height || 45;
    this.colorType = options.colorType || 'camera';
    this.add();
  }

  colorConfig = {
    camera: {
      outlineColor: Cesium.Color.fromAlpha(Cesium.Color.AZURE, 1),
      frustumColor: Cesium.Color.fromAlpha(Cesium.Color.LIMEGREEN, 0.2)
    },
    action: {
      outlineColor: Cesium.Color.fromAlpha(Cesium.Color.AZURE, 1),
      frustumColor: Cesium.Color.fromAlpha(Cesium.Color.ORANGE, 0.2)
    }
  };

  /**
   * 更新视锥体的姿态
   * @param {object} options - The options for configuring the camera.
   * @param {Cesium.Cartesian3 || []} options.position - The initial position of the camera in Cartesian 3 coordinates.
   * @param {number} [options.fov=26] - The field of view of the camera in degrees.
   * @param {number} [options.near=0.10] - The near clipping plane distance of the camera.
   * @param {number} [options.far=200] - The far clipping plane distance of the camera.
   * @param {number} [options.heading=0] - 角度值 比如 90度 157 度.
   * @param {number} [options.pitch=0] - 角度值 比如 90度 157 度.
   * @param {number} [options.roll=-90] - 角度值 比如 90度 157 度.
   * @param {number} [options.height=45] - The aspect ratio of the camera.
   * @param {number} [options.width=60] - The aspect ratio of the camera.
   * @param {number} [options.aspectRatio=4/3] - The aspect ratio of the camera.
   */
  update(options) {
    this.setVisible(true);
    const {
      position = null,
      fov = 26,
      near = 0.1,
      far = 200,
      heading = 0,
      pitch = 0,
      roll = 0,
      width = 60,
      height = 45
    } = options;
    this.position = toCartesian3(position);
    this.fov = fov;
    this.far = far;
    this.near = near;
    this.heading = heading;
    this.pitch = pitch;
    this.roll = roll;
    this.width = width;
    this.height = height;
    this.add();
  }

  // 创建视锥体和轮廓线
  add() {
    this.clear();
    this.addFrustum();
    this.addOutline();
  }

  // 清除视锥体和轮廓线
  clear() {
    this.clearFrustum();
    this.clearOutline();
  }

  // 清除视锥体
  clearFrustum() {
    if (this.frustumPrimitive) {
      this.viewer.scene.primitives.remove(this.frustumPrimitive);
      this.frustumPrimitive = null;
    }
  }

  // 清除轮廓线
  clearOutline() {
    if (this.outlinePrimitive) {
      this.viewer.scene.primitives.remove(this.outlinePrimitive);
      this.outlinePrimitive = null;
    }
  }

  // 创建视锥体
  addFrustum() {
    this.frustumGeometry = new Cesium.PerspectiveFrustum({
      // 查看的视场角，绕Z轴旋转，以弧度方式输入
      // fov: Cesium.Math.PI_OVER_THREE,
      fov: Cesium.Math.toRadians(this.fov),
      // 视锥体的宽度/高度
      aspectRatio: 4 / 3, //this.width / this.height,
      // 近面距视点的距离
      near: this.near,
      // 远面距视点的距离
      far: this.far
    });
    let instance = new Cesium.GeometryInstance({
      geometry: new Cesium.FrustumGeometry({
        frustum: this.frustumGeometry.clone(),
        origin: toCartesian3(this.position),
        orientation: Cesium.Transforms.headingPitchRollQuaternion(
          toCartesian3(this.position),
          new Cesium.HeadingPitchRoll.fromDegrees(this.heading, this.pitch, this.roll)
        ),
        vertexFormat: Cesium.VertexFormat.POSITION_ONLY
      }),
      attributes: {
        color: Cesium.ColorGeometryInstanceAttribute.fromColor(this.colorConfig[this.colorType].frustumColor)
      }
    });
    let primitive = new Cesium.Primitive({
      geometryInstances: instance,
      appearance: new Cesium.PerInstanceColorAppearance({
        closed: true,
        flat: true
      }),
      asynchronous: false
    });
    this.frustumPrimitive = this.viewer.scene.primitives.add(primitive);
  }

  // 创建轮廓线
  addOutline() {
    //this.orientation 这里已经井盖计算
    let geometry = new Cesium.FrustumOutlineGeometry({
      frustum: this.frustumGeometry.clone(),
      origin: toCartesian3(this.position),
      orientation: Cesium.Transforms.headingPitchRollQuaternion(
        toCartesian3(this.position),
        new Cesium.HeadingPitchRoll.fromDegrees(this.heading, this.pitch, this.roll)
      ),
      vertexFormat: Cesium.VertexFormat.POSITION_ONLY
    });
    let instance = new Cesium.GeometryInstance({
      geometry: geometry,
      attributes: {
        color: Cesium.ColorGeometryInstanceAttribute.fromColor(this.colorConfig[this.colorType].outlineColor)
      }
    });
    let primitive = new Cesium.Primitive({
      geometryInstances: instance,
      appearance: new Cesium.PerInstanceColorAppearance({
        closed: true,
        flat: true
      }),
      asynchronous: false
    });
    this.outlinePrimitive = this.viewer.scene.primitives.add(primitive);
  }

  /**
   * 获取参数
   * @returns
   */
  getOptions() {
    try {
      let opt = {
        viewer: this.viewer,
        // eyeViewer: this.eyeViewer,
        position: toCartesian3(this.position),
        fov: this.fov,
        near: this.near,
        far: this.far,
        heading: this.heading,
        pitch: this.pitch,
        roll: this.roll,
        width: this.width,
        height: this.height,
        colorType: this.colorType,
        options: this.options
      };
      return opt;
    } catch (error) {
      return null;
    }
  }

  setVisible(v) {
    if (this.frustumPrimitive) {
      this.frustumPrimitive.show = v;
    }
    if (this.outlinePrimitive) {
      this.outlinePrimitive.show = v;
    }
  }
}

export default CreateFrustum;
