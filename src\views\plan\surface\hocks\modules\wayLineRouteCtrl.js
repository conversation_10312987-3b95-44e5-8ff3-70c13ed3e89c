// 对 aitcrift_line json 航面数据进行显示和操作

import { createPolyline, getCenter } from '@/components/Cesium/libs/cesium';
const map = new Map();
let viewer = null;
export const addWaylineOnMap = (v, options) => {
  viewer = v;
  let id = options.id;
  let airline_json = options.airline_json;
  let config = airline_json.config ?? null;
  let dataInfo = config?.dataInfo ?? null;
  let positions = dataInfo?.aircraftRoutePositions ?? [];
  if (!positions || positions.length === 0) {
    return;
  }
  let lineEntity = createPolyline(viewer, positions);
  map.set(id, lineEntity);
};

export const delWayline = options => {
  let id = options.id;
  let lineEntity = null;
  if (map.has(id)) {
    lineEntity = map.get(id);
    viewer.entities.remove(lineEntity);
    map.delete(id);
  }
};
