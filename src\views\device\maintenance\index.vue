<!--机场管理-->
<script>
export default {
  name: 'Maintenance'
};
</script>

<script setup>
import { reactive } from 'vue';
import { getDeviceModal } from '@/api/devices';
import { getPlanList, deletePlan } from '@/api/devices/maintenance';
import EditDialog from './components/EditDialog.vue';
import MaintenanceDetail from './components/maintenanceDetail.vue';
import { authorityShow } from '@/utils/authority';
const editDialogRef = ref(null);
const detailRef = ref(null);
const loading = ref(false);
const total = ref(0);
const queryParams = reactive({
  page_num: 1,
  page_size: 10,
  begin_time: '',
  end_time: '',
  plan_name: '',
  device_sn: '',
  nickname: ''
});
const modalList = ref([]); //型号列表
const dataList = ref([]);
const editDialog = reactive({
  visible: false
});
const detailDialog = reactive({
  visible: false
});
let formData = reactive({});
let detailData = reactive({});
/**
 * 查询
 */
function handleQuery() {
  // Calculate the new total pages based on the new page size
  const newTotalPages = Math.ceil(total.value / queryParams.page_size);
  
  // If current page is greater than new total pages, adjust to the last page
  if (queryParams.page_num > newTotalPages) {
    queryParams.page_num = newTotalPages || 1;
  }
  getPlanList({
    ...queryParams
  }).then(data => {
    const { records } = data;
    dataList.value = records || [];
    total.value = data.total;
  });
}
function handleSearch() {
  (queryParams.begin_time = queryParams.range?.length === 2 ? `${queryParams.range[0]} 00:00:00` : ''),
    (queryParams.end_time = queryParams.range?.length === 2 ? `${queryParams.range[1]} 23:59:59` : ''),
    (queryParams.page_num = 1);
  // delete queryParams.range;
  handleQuery();
}
/**
 * 重置查询
 */
function resetQuery() {
  queryParams.nickname = '';
  (queryParams.device_sn = ''), (queryParams.plan_name = '');
  (queryParams.page_num = 1), (queryParams.page_size = 10), (queryParams.begin_time = '');
  queryParams.end_time = '';
  queryParams.range = [];
  handleQuery();
}

function openDetailDialog(row) {
  detailDialog.visible = true;
  Object.keys(detailData).map(key => {
    delete detailData[key];
  });
  Object.assign(detailData, { ...row });
}

/**
 * 打开表单弹窗
 */
function openEditDialog(row) {
  editDialog.visible = true;
  Object.keys(formData).map(key => {
    delete formData[key];
  });
  if (row) {
    Object.assign(formData, { ...row });
    editDialog.title = '编辑维保计划';
  }
}
function initModal() {
  getDeviceModal({
    domain: '3'
  }).then(res => {
    modalList.value = res;
  });
}

function addAndEditPlan(row) {
  editDialog.visible = true;
  Object.keys(formData).map(key => {
    delete formData[key];
  });
  if (row) {
    editDialog.title = '编辑维保计划';
    Object.assign(formData, { ...row });
  } else {
    editDialog.title = '新增维保计划';
  }
}

/**
 * 删除
 */
function handleDelete(row) {
  ElMessageBox.confirm('删除后无法恢复，是否确认删除？', '确认删除所选维保计划？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    autofocus: false,
    closeOnClickModal: false,
    type: 'warning'
  }).then(() => {
    deletePlan({
      id: toRaw(row).id
    }).then(data => {
      ElMessage.success('删除成功');
      handleQuery();
    });
  });
}

function jumpToPage(url) {
  window.open(url, '_blank');
}

onMounted(() => {
  initModal();
  handleQuery();
});
</script>
<style lang="scss" scoped>
.input-serach {
  width: 200px;
}
</style>
<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
          <el-form-item label="" prop="plan_name">
            <el-input
              class="input-serach"
              v-model="queryParams.plan_name"
              placeholder="请输入计划名称"
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
            />
          </el-form-item>
          <el-form-item label="" prop="device_sn">
            <el-input
              class="input-serach"
              v-model="queryParams.device_sn"
              placeholder="请输入设备SN"
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
            />
          </el-form-item>
          <el-form-item label="" prop="nickname">
            <el-input
              class="input-serach"
              v-model="queryParams.nickname"
              placeholder="请输入无人机&机场名称"
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
            />
          </el-form-item>
          <el-form-item label="" prop="range">
            <el-date-picker
              class="input-serach"
              v-model="queryParams.range"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @change="handleSearch"
            />
          </el-form-item>
        </el-form>
      </div>
      <div class="search-btn">
        <el-button type="primary" @click="handleSearch()"><i-ep-search />搜索</el-button>
        <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
      </div>
    </div>
    <el-card shadow="never">
      <template #header v-if="authorityShow('createMaintenance') || authorityShow('maintenanceTask') || authorityShow('scheme')">
        <el-button type="primary" @click.stop="addAndEditPlan()" v-if="authorityShow('createMaintenance')"
          >新增维保计划</el-button
        >
        <el-button type="primary" @click.stop="jumpToPage('/maintenanceTask')" v-if="authorityShow('maintenanceTask')"
          >维保任务管理</el-button
        >
        <el-button type="primary" @click.stop="jumpToPage('/scheme')" v-if="authorityShow('scheme')"
          >维保方案管理</el-button
        >
      </template>
      <el-table highlight-current-row :data="dataList" v-loading="loading" stripe height="580">
        <el-table-column label="序号" width="80">
          <template #default="scope">
            <span>{{ scope.$index + 1 + queryParams.page_size * (queryParams.page_num - 1) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="组织名称" width="180" prop="dept_name" show-overflow-tooltip />
        <el-table-column label="计划名称" prop="plan_name" show-overflow-tooltip />
        <el-table-column label="计划执行时间" prop="end_date" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ `${scope.row.start_date.substring(0, 10)} 至 ${scope.row.end_date.substring(0, 10)}` }}</span>
          </template>
        </el-table-column>
        <el-table-column label="无人机&机场名称" prop="nickname" show-overflow-tooltip />
        <el-table-column label="设备SN" prop="device_sn" show-overflow-tooltip />
        <el-table-column label="创建人" prop="creator" show-overflow-tooltip />
        <el-table-column
          fixed="right"
          label="操作"
          width="250"
          v-if="
            authorityShow('editMaintenance') || authorityShow('checkMaintenance') || authorityShow('deleteMaintenance')
          "
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click.stop="openEditDialog(scope.row)"
              v-if="authorityShow('editMaintenance')"
              >编辑</el-button
            >
            <el-button
              type="primary"
              link
              @click.stop="openDetailDialog(scope.row)"
              v-if="authorityShow('checkMaintenance')"
              >详情</el-button
            >
            <el-button
              type="danger"
              link
              @click.stop="handleDelete(scope.row)"
              v-if="authorityShow('deleteMaintenance')"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.page_num"
        v-model:limit="queryParams.page_size"
        @pagination="handleQuery"
      />
    </el-card>
    <div>
      <EditDialog
        ref="editDialogRef"
        v-model:visible="editDialog.visible"
        :title="editDialog.title"
        :form-data="formData"
        @submit="resetQuery"
      />
    </div>
    <MaintenanceDetail
      ref="detailRef"
      v-model:visible="detailDialog.visible"
      :title="detailDialog.title"
      :form-data="detailData"
      @submit="resetQuery"
    />
  </div>
</template>
<style scoped lang="scss">
.flex-center {
  display: flex;
  align-items: center;
  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #4caf51;
  }
  .unstatus {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: red;
  }
}
.search {
  display: flex;
  align-items: center;
  padding: 24px;
  height: 64px;
  .search-form {
    flex: 1;
    padding-top: 16px;
  }
}
</style>
