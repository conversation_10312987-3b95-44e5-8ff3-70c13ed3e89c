<template>
  <div class="wrapper">
    <el-card>
      <template #header> 无人机信息 </template>
      <div class="dinfo">
        <div class="item">
          <div class="label">型号:</div>
          <div class="value">DJI Mini 4 Pro</div>
        </div>
        <div class="item">
          <div class="label">当前状态:</div>
          <div class="value">{{ getStatue(data?.statue) }}</div>
        </div>
        <div class="item">
          <div class="label">当前速度:</div>
          <div class="value">{{ data?.speed }}</div>
        </div>
        <div class="item">
          <div class="label">纬度:</div>
          <div class="value">{{ data?.lng }}</div>
        </div>
        <div class="item">
          <div class="label">纬度:</div>
          <div class="value">{{ data?.lat }}</div>
        </div>
        <div class="item">
          <div class="label">当前高度:</div>
          <div class="value">{{ data?.height }}</div>
        </div>

        <div class="item">
          <div class="label">电量:</div>
          <div class="value">100%</div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import {
  ref,
  reactive,
  onUpdated,
  toRefs,
  onBeforeMount,
  onMounted,
  onUnmounted,
  onBeforeUpdate,
  onBeforeUnmount,
  onErrorCaptured
} from 'vue';
import { DRONE_STATUE, KEY_FLY_DATA_INFO } from '@/config/Map';
import { CesiumFlight } from '@/components/Cesium/libs/cesium/cesiumFlight';
import { getOrCreateCesiumEngineInstance } from '@/components/Cesium/libs/cesium/global';
let data = ref();
let engine = null;
let timer = null;
let fly = null;
const init = () => {
  engine = getOrCreateCesiumEngineInstance('fly');
  fly = CesiumFlight.getInstance(engine.viewer);
  getInfo();
};

const getInfo = () => {
  timer = setInterval(() => {
    data.value = fly && fly.getModelInfo();
    if (data.value.statue === DRONE_STATUE.STOP) {
      fly.stopFlight();
      timer && clearInterval(timer);
    }
  }, 500);
};

const getStatue = statue => {
  let result = '预备';
  switch (statue) {
    case 'flying':
      result = '飞行';
      break;
    case 'stop':
      result = '停止';
      break;
    case 'prepare':
      result = '预备';
      break;
    default:
      result = '预备';
      break;
  }
  return result;
};
//#region 生命周期

onMounted(() => {
  init();
});

onUnmounted(() => {
  timer && clearInterval(timer);
});
onBeforeUnmount(() => {
  timer && clearInterval(timer);
});

//#endregion
</script>
<style scoped lang="scss">
.wrapper {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 310px;
}

.dinfo {
  .item {
    display: flex;
    justify-content: flex-start;
    align-content: center;
    // background-color: rgba(165, 165, 165, 0.261);
    margin-top: 5px;
    padding: 5px 10px;
    border-radius: 5px;
    width: 250px;
    .label {
      width: 100px;
      user-select: none;
      // text-align: right;
      // padding-right: 15px;
    }
  }
}

.warn {
  color: red;
}
</style>
