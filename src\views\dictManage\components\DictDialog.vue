<template>
  <el-dialog
    :title="title"
    v-if="visible"
    :model-value="visible"
    width="600px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form ref="dataFormRef" :model="form" :rules="rules" label-width="100px" v-loading="loading">
      <el-form-item prop="dict_code" label="字典编码">
        <el-input v-model="form.dict_code" placeholder="请输入字典编码" maxlength="64" />
      </el-form-item>
      <el-form-item prop="dict_name" label="字典名称">
        <el-input v-model="form.dict_name" placeholder="请输入字典名称" maxlength="100" />
      </el-form-item>
      <el-form-item prop="dict_value" label="字典值">
        <el-input v-model="form.dict_value" placeholder="请输入字典值" maxlength="100" />
      </el-form-item>
      <el-form-item prop="description" label="描述">
        <el-input v-model="form.description" placeholder="请输入描述" type="textarea" maxlength="500" />
      </el-form-item>
      <el-form-item prop="sort_no" label="排序号">
        <el-input-number v-model="form.sort_no" :min="0" :max="999" style="width: 100%" />
      </el-form-item>
      <el-form-item prop="status" label="状态">
        <el-radio-group v-model="form.status">
          <el-radio :label="0">正常</el-radio>
          <el-radio :label="1">停用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="is_sys" label="是否系统数据">
        <el-radio-group v-model="form.is_sys">
          <el-radio :label="0">否</el-radio>
          <el-radio :label="1">是</el-radio>
        </el-radio-group>
      </el-form-item> 
      <el-form-item prop="remark" label="备注">
        <el-input v-model="form.remark" placeholder="请输入备注" type="textarea" maxlength="500" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-loading="submitLoading">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElForm, ElMessage } from 'element-plus';
import { createDict, updateDict, getDictList } from '@/api/system/dict';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '添加字典'
  },
  formData: {
    type: Object,
    default() {
      return {};
    }
  }
});

const emit = defineEmits(['update:visible', 'submit']);
const dataFormRef = ref(null);
const loading = ref(false);
const submitLoading = ref(false);

const form = reactive({
  id: undefined,
  dict_code: '',
  dict_name: '',
  dict_value: '',
  description: '',
  parent_code: '',
  sort_no: 0,
  status: 0,
  is_sys: 0,
  color_type: '',
  css_class: '',
  remark: ''
});

const rules = reactive({
  dict_code: [{ required: true, message: '字典编码不能为空', trigger: 'blur' }],
  dict_name: [{ required: true, message: '字典名称不能为空', trigger: 'blur' }],
  sort_no: [{ required: true, message: '排序号不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
});

watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      loading.value = true;
      setTimeout(() => {
        dataFormRef.value?.clearValidate();
        loading.value = false;
      }, 100);
    }
  }
);

watch(
  () => props.formData,
  (newVal) => {
    if (newVal && Object.keys(newVal).length > 0) { 
      const formattedData = {};
      Object.keys(newVal).forEach(key => {
        const newKey = key.replace(/([A-Z])/g, "_$1").toLowerCase();
        formattedData[newKey] = newVal[key];
      });
      Object.assign(form, formattedData);
    } else {
      resetForm();
      // 如果是新增，默认设置父级编码为-1
      if (props.title === '添加字典') {
        form.parent_code = '-1';
      }
    }
  },
  { deep: true, immediate: true }
);

watch(
  () => props.title,
  (newVal) => {
    // 如果是新增，默认设置父级编码为-1
    if (newVal === '添加字典' && !form.id) {
      form.parent_code = '-1';
    }
  }
);

function resetForm() {
  Object.assign(form, {
    id: undefined,
    dict_code: '',
    dict_name: '',
    dict_value: '',
    description: '',
    parent_code: '',
    sort_no: 0,
    status: 0,
    is_sys: 0,
    color_type: '',
    css_class: '',
    remark: ''
  });
}

function closeDialog() {
  emit('update:visible', false);
  resetForm();
}

function handleSubmit() {
  dataFormRef.value.validate((valid) => {
    if (valid) {
      submitLoading.value = true;       
      const request = form.id ? updateDict : createDict;
      request(form)
        .then(res => {
          ElMessage.success('保存成功');
          emit('submit');
          closeDialog();
        })
        .catch(err => {
          console.error('保存失败', err);
        })
        .finally(() => {
          submitLoading.value = false;
        });
    }
  });
}

onMounted(() => {
  // 如果是新增，默认设置父级编码为-1
  if (props.title === '添加字典' && !form.id) {
    form.parent_code = '-1';
  }
});
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
}
</style> 