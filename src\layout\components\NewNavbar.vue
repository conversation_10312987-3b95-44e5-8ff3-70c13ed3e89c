<script setup>
import { onMounted, reactive } from 'vue';
import AvatarArea from './AvatarArea/index.vue';
// import MiddleNavbar from './middleNavbar/index.vue';
import AppLink from './Sidebar/Link.vue';
import { useRouter } from 'vue-router';
import { getDeptSysSetting } from '@/api/wayline';
const defaultLogoIcon = new URL('/resource/images/default-logo.jpeg', import.meta.url).href;;
const router = useRouter();
const data = reactive({});
onMounted(() => {
  initData();
});
function initData() {
  getDeptSysSetting({}).then(res => {
    const { base_config = {} } = res;
    data.name = base_config.sys_name;
    data.logo = base_config.sys_logo_url;
  });
}
// 默认logo图片路径
function handleImageError() {
  data.logo = defaultLogoIcon;
}
</script>

<template>
  <!-- 顶部导航栏 -->
  <div class="navbar">
    <!-- 左侧面包屑 -->
     <div></div>
    <!-- <div class="flex logo">
      <el-image
        style="margin-left: 25px; width: 40px; height: 40px; margin-right: 10px"
        :src="data.logo"
        :zoom-rate="1.2"
        :max-scale="7"
        :min-scale="0.2"
        fit="cover"
        @error="handleImageError"
      />
      <span class="navbar-title">{{ data.name }}</span>
    </div> -->
    <!-- 中间导航设置 -->
    <!-- <div>
      <MiddleNavbar />
    </div> -->
    <!-- 右侧导航设置 -->
    <div class="flex items-center">
      <div class="backstage">
        <app-link to="/">
          <svg-icon icon-class="back" style="margin-left: 4" />
          返回首页
        </app-link>
      </div>
      <AvatarArea :show-dashboard="false" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.navbar {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 0 1px #0003;
  background-color: #11253e;
  padding-right: 10px;
  :deep(.el-switch__core) {
    border: 1px solid #fff;
  }
  .backstage {
    font-family: SourceHanSansSC-Regular;
    font-size: 16px;
    color: #ffffff;
    text-align: center;
    line-height: 24px;
    font-weight: 400;
    margin-right: 20px;
    cursor: pointer;
  }
  .navbar-title {
    font-family: SourceHanSansSC-Bold;
    font-size: 20px;
    color: #ffffff;
    text-align: left;
    line-height: 40px;
    font-weight: 700;
    margin-left: 16px;
  }
  .setting-container {
    display: flex;
    align-items: center;
    color: #fff;
  }

  .manual-icon {
    width: 21px;
    height: 21px;
  }
}
</style>
