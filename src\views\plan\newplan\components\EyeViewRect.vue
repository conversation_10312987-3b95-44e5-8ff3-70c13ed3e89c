<template>
  <div class="zoom-frame-wrap">
    <div class=" "><img class="img-center" src="@/assets/plan/center.png" /></div>
    <!-- {{ data }} -->
    <div class="zoom-frame" v-if="!colorWaring" :style="{ width: data.width + 'px', height: data.height + 'px' }">
      <div class="top left corner"></div>
      <div class="top right corner"></div>
      <div class="bottom left corner"></div>
      <div class="bottom right corner"></div>
      <span class="frame-title map-text-shadow"> {{ data.zoom }} </span>
    </div>

    <div class="zoom-frame" v-else :style="{ width: data.width + 'px', height: data.height + 'px' }">
      <div class="top left corner-waring"></div>
      <div class="top right corner-waring"></div>
      <div class="bottom left corner-waring"></div>
      <div class="bottom right corner-waring"></div>
      <span class="frame-title-warning map-text-shadow"> {{ data.zoom }} </span>
    </div>
    <!-- <div class="statue-info">
      <span class="statue-item"> 高度:{{ data.cameraHeight }} 米</span>
      <span class="statue-item"> 缩放:{{ data.zoom }} </span>
    </div> -->
  </div>
</template>
<script>
export default {
  name: 'EyeViewRect'
};
</script>
<script setup>
import { onMounted, onUnmounted, reactive } from 'vue';
const data = reactive({
  zoom: '2X',
  width: 200,
  height: 150,
  cameraHeight: 100
});

const colorWaring = ref(false);

// 提前定义好框的变化 sequence 是缩放倍率
const dataset = [
  { sequence: 1, width: 230, height: 170 },
  { sequence: 2, width: 200, height: 150 },
  { sequence: 3, width: 132, height: 99 },
  { sequence: 4, width: 100, height: 75 },
  { sequence: 5, width: 80, height: 60 },
  { sequence: 6, width: 66, height: 50 },
  { sequence: 7, width: 57, height: 43 },
  { sequence: 8, width: 50, height: 38 },
  { sequence: 9, width: 43, height: 32 },
  { sequence: 10, width: 40, height: 30 },
  { sequence: 11, width: 36, height: 27 },
  { sequence: 12, width: 33, height: 25 },
  { sequence: 13, width: 30, height: 23 },
  { sequence: 14, width: 28, height: 21 },
  { sequence: 15, width: 26, height: 20 },
  { sequence: 16, width: 25, height: 18 },
  { sequence: 17, width: 23, height: 17 },
  { sequence: 18, width: 22, height: 16 },
  { sequence: 19, width: 21, height: 15.5 },
  { sequence: 20, width: 20, height: 15 },
  { sequence: 21, width: 19, height: 14 },
  { sequence: 22, width: 18, height: 13 },
  { sequence: 23, width: 17, height: 12.5 },
  { sequence: 24, width: 16, height: 12 },
  { sequence: 25, width: 16, height: 12 },
  { sequence: 26, width: 15, height: 11 },
  { sequence: 27, width: 14, height: 11 },
  { sequence: 28, width: 14, height: 10 },
  { sequence: 29, width: 13, height: 10 },
  { sequence: 30, width: 13, height: 10 },
  { sequence: 31, width: 12, height: 9 },
  { sequence: 31, width: 12, height: 9 },
  { sequence: 32, width: 12, height: 9 },
  { sequence: 33, width: 11, height: 8 },
  { sequence: 34, width: 11, height: 8 },
  { sequence: 35, width: 11, height: 8 },
  { sequence: 36, width: 11, height: 8 },
  { sequence: 37, width: 10, height: 8 },
  { sequence: 38, width: 10, height: 7 },
  { sequence: 39, width: 10, height: 7 },
  { sequence: 40, width: 10, height: 7 }
];
const updateRectDom = options => {
  const { zoom = 1 } = options;
  if (zoom >= 1) {
    const { width, height } = calculateRectSize(zoom);
    data.width = width || 80;
    data.height = height || 60;
    data.zoom = zoom + 'X' || '2X';
    if (zoom >= 1 && zoom <= 20) {
      colorWaring.value = false;
    } else {
      colorWaring.value = true;
    }
  }
};
/**
 * 根据输入的序号,计算出对应的 width 和 height 值。
 * @param {number} index - 输入的序号 2 3 4 5 ... 这个可以说是倍率
 * @returns {[number, number]} - 返回 width 和 height 值的数组
 */
const calculateRectSize = index => {
  if (index >= 1) {
    const data = dataset.filter(item => {
      return item.sequence === index;
    });
    if (!data || data.length === 0) {
      return { width: 10, height: 7 };
    }
    return { width: data[0].width, height: data[0].height };
  }
};
onMounted(() => {
  window.$bus.on('updateRectDom', updateRectDom);
});

onUnmounted(() => {
  window.$bus.off('updateRectDom', updateRectDom);
});
</script>
<style lang="scss" scoped>
.zoom-frame-wrap {
  width: 350px;
  height: 350px;
  bottom: 0px;
  right: 0px;
  margin: 0;
  padding: 0;
  position: absolute;
  z-index: 1;
  user-select: none;
  border: #adadad 1px solid;
  .img-center {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }

  .statue-info {
    position: absolute;
    width: 350px;
    bottom: 0px;
    background-color: #353535a3;
    padding: 2px 15px;
    color: white;
    margin: auto;
    .statue-item {
      margin: 0px 5px;
    }
  }
  .zoom-frame {
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    position: absolute;
    z-index: 1000;
    .corner {
      width: 50px;
      height: 50px;
      max-width: 40%;
      max-height: 40%;
      border: 3px solid #00ee8b;
      position: absolute;
    }
    .corner.left {
      left: 0;
      border-right: none;
    }
    .corner.top {
      top: 0;
      border-bottom: none;
    }
    .corner.bottom {
      bottom: 0;
      border-top: none;
    }
    .corner.right {
      right: 0;
      border-left: none;
    }

    .corner-waring {
      width: 50px;
      height: 50px;
      max-width: 40%;
      max-height: 40%;
      border: 3px solid #f86700;
      position: absolute;
    }
    .corner-waring.left {
      left: 0;
      border-right: none;
    }
    .corner-waring.top {
      top: 0;
      border-bottom: none;
    }
    .corner-waring.bottom {
      bottom: 0;
      border-top: none;
    }
    .corner-waring.right {
      right: 0;
      border-left: none;
    }
    .frame-title {
      color: #00ee8b;
      bottom: -8px;
      right: 0;
      transform: translateY(100%);
      text-transform: uppercase;
      position: absolute;
      font-size: 16px;
      line-height: 22px;
      font-weight: 500;
      white-space: nowrap;
    }
    .frame-title-warning {
      color: #f86700;
      bottom: -8px;
      right: 0;
      transform: translateY(100%);
      text-transform: uppercase;
      position: absolute;
      font-size: 16px;
      line-height: 22px;
      font-weight: 500;
      white-space: nowrap;
    }
    .map-text-shadow {
      text-shadow: 0 0 10px rgba(0, 0, 0, 0.6), -1px -1px 0 rgba(0, 0, 0, 0.5), 1px -1px 0 rgba(0, 0, 0, 0.5),
        -1px 1px 0 rgba(0, 0, 0, 0.5), 1px 1px 0 rgba(0, 0, 0, 0.5);
    }
  }
}

.waring {
  border: 3px solid #f86700 !important;
}
</style>
