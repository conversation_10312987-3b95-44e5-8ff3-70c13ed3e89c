<template>
  <el-dialog
    :title="title"
    v-if="visible"
    :model-value="visible"
    width="600px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form class="app-form" ref="dataFormRef" :model="form" :rules="rules" label-width="120px" v-loading="loading">
      <el-form-item label="工单描述" prop="workOrderDesc">
       预警信息：{{form?.alarm_desc}}
      </el-form-item>
      <el-form-item label="预警处置人" prop="processor" required>
        <el-select v-model="form.processor" placeholder="请选择处置人" filterable>
          <el-option
            v-for="item in userOptinos"
            :key="item.user_id"
            :label="item?.username"
            :value="item.user_id"
          />
        </el-select>
      </el-form-item>
      <el-form-item  label="通知模式"  prop="notificationMode">
        <el-checkbox label="短信通知" v-model="form.notificationMode" />
      </el-form-item>
      <el-form-item  label="处置说明" prop="process_content">
       <el-input
          v-model="form.process_content"
          placeholder="请输入处置说明"
          maxlength="250"
          type="textarea"
          :rows="3"
        />
      </el-form-item>
      <el-form-item
        label="附件"
        prop="fileList"
        class="fileList"
      >
      <multi-upload v-model="fileList" :limit="fileList.length" :showDelete="false"></multi-upload>
      </el-form-item>
    </el-form>
    <template #footer >
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-loading="loading">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { listUser } from '@/api/system/user';
import { getAlarmDetail,alarmProcess} from '@/api/workOrder';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  id: {
    type: String,
    default: ''
  },
});
const form = reactive({});

const dataFormRef = ref(ElForm);
const userOptinos = ref([]);
const emit = defineEmits(['update:visible', 'submit']);
const rules = reactive({
  processor: [{ required: true, message: '请选择处置人', trigger: ['change','blur'] }],
  process_content: [{ required: true, message: '请输入处置说明', trigger: ['change','blur']}],
});

const fileList = [
  {
    name: 'test.mp4',
    url: 'https://testing.ff-iot.com:24135/ff_user/Know/2025/06/09/73FC67D9E38B4739AA2236474BA430CC.mp4',
  },
  {
    name: 'element-plus-logo2.svg',
    url: 'http://**************:24176/uavfile/wayline/thumbnai/2025/04/wayline/2025/04/org_0f7089d44ad4e389_1744250928000.jpg_1744251780410_thumb.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=uav%2F20250723%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250723T081436Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=36f01afe566f8c09498287d010fa7ca83ef2fb0f06c906448017fd14b8d034ef',
  },
]

defineExpose({ setDefaultValue });

// 设置默认值
function setDefaultValue() {
  
}
const loading = ref(false);

// 关闭弹窗
function closeDialog() {
  resetForm();
  emit('update:visible', false);
}
/**
 * 重置表单
 */
function resetForm() {
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  loading.value = false;
  Object.keys(form).map(key => {
    delete form[key];
  });
}
// 获取详情
function getDetail() {
  loading.value = true
  getAlarmDetail(props.id).then(res => {
    form.value = res
  }).finally(()=>{
    loading.value = false
  })
}

function handleSubmit() {
  dataFormRef.value.validate(isValid => {
    if (isValid) {
    console.log('3=====',form)
    //   let params = {
    //     ...form,
      
    //   };
    //   loading.value = true;
    //   alarmProcess(params)
    //     .then(res => {
    //       loading.value = false;
    //       ElMessage.success('新增成功');
    //       closeDialog();
    //       emit('submit');
    //     })
    //     .catch(e => {
    //       loading.value = false;
    //     });
    } else {
      loading.value = false;
    }
  });
}
//处置人
function getUser() {
  listUser({
    pageNo: 1,
    pageSize: 999
  }).then(res => {
    const { list = [] } = res;
    userOptinos.value = list;
  });
}

onMounted(() => {
  getUser();
  getDetail();
});
</script>
<style scoped lang="scss">

:global(.el-loading-mask) {
  transform: opacity 0.9 !important;
  background-color: rgb(255 255 255 / 0.3);
}
.app-form {
  ::v-deep {
    .el-select {
      width: 100%;
    }
  }
}
</style>
