//#region  写规则 校验规则 检验对象是数组
// 间隔间距相关

// 1、间隔动作只能是 间隔时间拍照或者等距间隔拍照与停止间隔拍照交错排布 不能是 间隔时间拍照、等距间隔拍照、停止间隔拍照 这样的排布
// 2、可以连续多个停止间隔 如果1开始 2 结束 3结束那么 其起作用的结束是2 并非3
// 3、整个动作组只有一个间隔动作是ok的 actionGroupStartIndex = 0  actionGroupEndIndex =  最大航点索引 意思是从头开始到结尾
// 4、停止间隔拍照永远再最后一组 及组后一个
// 5、如果存在两个等时间拍照那么再保存时会将其中一个剔除  保存时自动剔除不对的 间隔项 保留完整的  如果存在间隔项那么必须有一个停止间隔项的动作 是代码自动生成的
// kml 文件中不会保存该动作

// 录像相关
// 1、连续多个停止录像不冲突
// 2、连续2个录像是冲突 ，连续两个录像之间需要设置一个停止录像 否则报错
// 输出 冲突 所在点 组编号 和对应 动作编号
// 3、如果全局只有一个录像是可以的

// 全景
// 全景拍照 要保证录像相关的规则是通过的 不能再录像时进行全景拍照
// 全景拍照只要保证前面的动作有停止录像
// 提示语：
// 航点的全景拍照动作处于录像过程中，无法执行。
// 解决方案: 调整航点动作顺序。

// 以下是一些相关提示参考
// 问题 1: 9#航点的"开始录像"航点动作间缺少"停止录像"动作。
// 解决方案: 请在"开始录像"动作之间插入"停止录像"动作。
//#endregion

import { ACTION_TRIGGER_TYPE, ACTION_ACTUATOR_FUNC } from '@/utils/constants.js';
import { useWayPointStore } from '@/store/modules/wayPointInfo.js';
import { getRuleCheckData, getMultipleActions } from './multipleActionHandle.js';

// 获取冲突列表
export const CONFLICT_ACTION_TYPE = {
  record: 'record',
  multiple: 'multiple',
  panoShot: 'panoShot'
};
export const ACTION_TYPE = {
  multiple: 'multiple',
  stopMultiple: 'stop'
};
/**
 * 对列表动作进行校验输出错误的航点信息及动作信息
 * @returns
 */
const ruleCheck = () => {
  const wayPointStore = useWayPointStore();
  let actionDetailList = getRuleCheckData();
  const recordList = ruleCheck_Record(actionDetailList);
  const MultipleTimingAndDistanceResult = ruleCheck_MultipleTimingAndDistance(actionDetailList);
  const panoramaResult = ruleCheck_PanoShot(actionDetailList);
  const data = [...recordList, ...MultipleTimingAndDistanceResult, ...panoramaResult];
  let warnningDataResult = wayPointStore.checkActionsRule(data);
  // console.log('warnning Data:', warnningDataResult);
  return warnningDataResult;
};

//#region  间距拍照
/**
 * 间距拍照
 * @param {*} actionList 动画列表
 * @returns
 */
const ruleCheck_MultipleTimingAndDistance = actionList => {
  const conflicts = new Map(); // 存储冲突的点
  // 提取间隔拍照的动作
  const resultList = getMultipleActions(actionList) || [];
  if (resultList.length > 1) {
    for (let i = 0; i < resultList.length - 1; i++) {
      let firstAction = resultList[i],
        secondAction = resultList[i + 1];
      // 如果当前动作是间隔动作
      let firstActionType = getActionTypeByMultiple(firstAction);
      let secondActionType = getActionTypeByMultiple(secondAction);
      if (!firstActionType || !secondActionType) {
        continue;
      }
      // 两个都是间隔动作
      if (firstActionType === ACTION_TYPE.multiple && secondActionType === ACTION_TYPE.multiple) {
        const msg = '【等距间隔拍照】: 在开始等距间隔拍照动作之前需要添加一个停止间隔操作，不能连续进行两个间隔操作！';
        // 将后面那个点放入
        conflicts.set(secondAction.actionUuid, {
          type: CONFLICT_ACTION_TYPE.multiple,
          warning: true,
          warningMsg: msg,
          ...secondAction
        });
      }
      // 第一个是停止间隔动作，第二个是间隔动作
      else if (firstActionType === ACTION_TYPE.stopMultiple && secondActionType === ACTION_TYPE.multiple) {
        conflicts.set(firstAction.actionUuid, {
          type: CONFLICT_ACTION_TYPE.multiple,
          warning: false,
          warningMsg: '',
          ...firstAction
        });
        // 将后面那个点放入
        conflicts.set(secondAction.actionUuid, {
          type: CONFLICT_ACTION_TYPE.multiple,
          warning: false,
          warningMsg: '',
          ...secondAction
        });
      } // 第一个是间隔动作，第二个是停止间隔动作
      else if (firstActionType === ACTION_TYPE.multiple && secondActionType === ACTION_TYPE.stopMultiple) {
        // 将后面那个点放入
        // 判断是否该点已经是被提示告警则不再进行处理
        const actionInfo = conflicts.get(firstAction.actionUuid);
        // 没有被标记过
        if (!actionInfo) {
          conflicts.set(firstAction.actionUuid, {
            type: CONFLICT_ACTION_TYPE.multiple,
            warning: false,
            warningMsg: '',
            ...firstAction
          });
        } else if (actionInfo.warning) {
          // 这里说明已经被标记过
        }
        conflicts.set(secondAction.actionUuid, {
          type: CONFLICT_ACTION_TYPE.multiple,
          warning: false,
          warningMsg: '',
          ...secondAction
        });
      }
      // 第一个是停止间隔动作，第二个停止间隔动作 该组合不操作
      else if (firstActionType === ACTION_TYPE.stopMultiple && secondActionType === ACTION_TYPE.stopMultiple) {
        // 将后面那个点放入
        conflicts.set(firstAction.actionUuid, {
          type: CONFLICT_ACTION_TYPE.multiple,
          warning: false,
          warningMsg: '',
          ...firstAction
        });
        conflicts.set(secondAction.actionUuid, {
          type: CONFLICT_ACTION_TYPE.multiple,
          warning: false,
          warningMsg: '',
          ...secondAction
        });
      }
    }
    // 最后一个点检测
    let lastAction = resultList[resultList.length - 1];
    let lastActionType = getActionTypeByMultiple(lastAction);
    conflicts.set(lastAction.actionUuid, {
      type: CONFLICT_ACTION_TYPE.multiple,
      warning: lastActionType === ACTION_TYPE.multiple,
      warningMsg: lastActionType === ACTION_TYPE.multiple ? '【间隔动作】需要添加一个停止间隔操作！' : '',
      ...lastAction
    });
  }
  return Array.from(conflicts.values());
};

// 给间隔动作使用的专门函数
const getActionTypeByMultiple = action => {
  try {
    if (!action) {
      return null;
    }

    // 确定其动作类型
    let isMultActionType =
      action.actionType === ACTION_ACTUATOR_FUNC.takePhoto ||
      ACTION_ACTUATOR_FUNC.distanceIntervalTakePhoto ||
      ACTION_ACTUATOR_FUNC.timeIntervalTakePhoto;

    // 确定其触发方式
    let isActionMultp =
      (action.actionTriggerType === ACTION_TRIGGER_TYPE.multipleTiming ||
        action.actionTriggerType === ACTION_TRIGGER_TYPE.multipleDistance) &&
      isMultActionType;

    let isStopMultp = action.actionType === ACTION_ACTUATOR_FUNC.stopIntervalTakePhoto;

    if (isActionMultp) {
      return ACTION_TYPE.multiple;
    } else if (isStopMultp) {
      return ACTION_TYPE.stopMultiple;
    }
  } catch {
    return null;
  }
};
//#endregion

//#region  录像停止录像
/**
 * 录像停止录像
 * @param {*} actionList 动画列表
 * @returns
 */
const ruleCheck_Record = actionList => {
  const conflicts = new Map(); // 存储冲突的点
  // 提取录像和停止图像
  const resultList = actionList.filter(d => {
    const isStartRecordAction =
      d.actionType === ACTION_ACTUATOR_FUNC.startRecord || d.actionType === ACTION_ACTUATOR_FUNC.stopRecord;
    return isStartRecordAction;
  });

  // 检查连续两个录像是否冲突
  if (resultList.length > 1) {
    for (let i = 0; i < resultList.length - 1; i++) {
      let firstAction = resultList[i],
        secondAction = resultList[i + 1];
      if (
        firstAction.actionType === ACTION_ACTUATOR_FUNC.startRecord &&
        secondAction.actionType === ACTION_ACTUATOR_FUNC.startRecord
      ) {
        const msg = '【开始录像】: 在开始录像之前需要添加一个停止录像操作，不能连续进行两个录像操作！';
        conflicts.set(secondAction.actionUuid, {
          type: CONFLICT_ACTION_TYPE.record,
          warning: true,
          warningMsg: msg,
          ...secondAction
        });
      } else if (
        firstAction.actionType === ACTION_ACTUATOR_FUNC.stopRecord &&
        secondAction.actionType === ACTION_ACTUATOR_FUNC.startRecord
      ) {
        conflicts.set(firstAction.actionUuid, {
          type: CONFLICT_ACTION_TYPE.record,
          warning: false,
          warningMsg: '',
          ...firstAction
        });
        conflicts.set(secondAction.actionUuid, {
          type: CONFLICT_ACTION_TYPE.record,
          warning: false,
          warningMsg: '',
          ...secondAction
        });
        // 正常
      } else if (
        firstAction.actionType === ACTION_ACTUATOR_FUNC.startRecord &&
        secondAction.actionType === ACTION_ACTUATOR_FUNC.stopRecord
      ) {
        // 判断是否该点已经是被提示告警则不再进行处理
        const actionInfo = conflicts.get(firstAction.actionUuid);
        // 没有被标记过
        if (!actionInfo) {
          conflicts.set(firstAction.actionUuid, {
            type: CONFLICT_ACTION_TYPE.record,
            warning: false,
            warningMsg: '',
            ...firstAction
          });
        } else if (actionInfo.warning) {
          // 这里说明已经被标记过
        }

        conflicts.set(secondAction.actionUuid, {
          type: CONFLICT_ACTION_TYPE.record,
          warning: false,
          warningMsg: '',
          ...secondAction
        });
        // 正常
      } else if (
        firstAction.actionType === ACTION_ACTUATOR_FUNC.stopRecord &&
        secondAction.actionType === ACTION_ACTUATOR_FUNC.stopRecord
      ) {
        // 正常
      }
    }

    // 最后一个点检测
    let lastAction = resultList[resultList.length - 1];
    conflicts.set(lastAction.actionUuid, {
      type: CONFLICT_ACTION_TYPE.record,
      warning: lastAction.actionType === ACTION_ACTUATOR_FUNC.startRecord,
      warningMsg:
        lastAction.actionType === ACTION_ACTUATOR_FUNC.startRecord
          ? '【录像动作】该动作后面需要添加一个停止录像操作！'
          : '',
      ...lastAction
    });
  }
  return Array.from(conflicts.values());
};
//#endregion

//#region  录像停止录像
/**
 * 全景拍照
 * @param {*} actionList 动画列表
 * @returns
 */
const ruleCheck_PanoShot = actionList => {
  const conflicts = new Map(); // 存储冲突的点
  // 提取录像和停止图像
  const resultList = actionList.filter(d => {
    const isPanoShotAction =
      d.actionType === ACTION_ACTUATOR_FUNC.startRecord ||
      d.actionType === ACTION_ACTUATOR_FUNC.stopRecord ||
      d.actionType === ACTION_ACTUATOR_FUNC.panoShot;
    return isPanoShotAction;
  });

  // 检查全景动作是否冲突
  if (resultList.length > 1) {
    for (let i = 0; i < resultList.length - 1; i++) {
      let firstAction = resultList[i],
        secondAction = resultList[i + 1];
      if (
        firstAction.actionType === ACTION_ACTUATOR_FUNC.startRecord &&
        secondAction.actionType === ACTION_ACTUATOR_FUNC.panoShot
      ) {
        // 第一个是录像,第二个是全景拍照
        const msg = '【全景拍照】: 录像过程中，无法执行动作【全景拍照】请先停止录像操作，再进行全景拍照！';
        conflicts.set(secondAction.actionUuid, {
          type: CONFLICT_ACTION_TYPE.panoShot,
          warning: true,
          warningMsg: msg,
          ...secondAction
        });
      } else if (
        firstAction.actionType === ACTION_ACTUATOR_FUNC.stopRecord &&
        secondAction.actionType === ACTION_ACTUATOR_FUNC.panoShot
      ) {
        // 第一个是停止录像,第二个是全景拍照 正常
        conflicts.set(secondAction.actionUuid, {
          type: CONFLICT_ACTION_TYPE.panoShot,
          warning: false,
          warningMsg: '',
          ...secondAction
        });
      } else if (
        firstAction.actionType === ACTION_ACTUATOR_FUNC.panoShot &&
        secondAction.actionType === ACTION_ACTUATOR_FUNC.panoShot
      ) {
        // 第一个是全景拍照,第二个是全景拍照 正常
        const actionInfo = conflicts.get(firstAction.actionUuid);
        // 没有被标记过
        if (!actionInfo) {
          conflicts.set(firstAction.actionUuid, {
            type: CONFLICT_ACTION_TYPE.panoShot,
            warning: false,
            warningMsg: '',
            ...firstAction
          });
        }
        conflicts.set(secondAction.actionUuid, {
          type: CONFLICT_ACTION_TYPE.panoShot,
          warning: false,
          warningMsg: '',
          ...secondAction
        });
      } else if (
        firstAction.actionType === ACTION_ACTUATOR_FUNC.panoShot &&
        secondAction.actionType === ACTION_ACTUATOR_FUNC.startRecord
      ) {
        // 第一个是全景拍照,第二个是全景拍照 正常
        const actionInfo = conflicts.get(firstAction.actionUuid);
        // 没有被标记过
        if (!actionInfo) {
          conflicts.set(firstAction.actionUuid, {
            type: CONFLICT_ACTION_TYPE.record,
            warning: false,
            warningMsg: '',
            ...firstAction
          });
        }
      } else if (
        firstAction.actionType === ACTION_ACTUATOR_FUNC.panoShot &&
        secondAction.actionType === ACTION_ACTUATOR_FUNC.stopRecord
      ) {
        const actionInfo = conflicts.get(firstAction.actionUuid);
        // 没有被标记过
        if (!actionInfo) {
          conflicts.set(firstAction.actionUuid, {
            type: CONFLICT_ACTION_TYPE.record,
            warning: false,
            warningMsg: '',
            ...firstAction
          });
        }
      }
    }
  }
  return Array.from(conflicts.values());
};
//#endregion

export {
  ruleCheck_Record,
  ruleCheck_MultipleTimingAndDistance,
  ruleCheck_PanoShot,
  getActionTypeByMultiple,
  ruleCheck
};
