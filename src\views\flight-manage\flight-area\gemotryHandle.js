// 创建 Polygon
import * as Cesium from 'cesium';
import { generateKey } from '@/utils';
import { FLIGHTAREATYPE, COLOR_TYPE_STRING } from './flightAreaHandle';
import {
  getCenterCoordinates,
  addPropertiesToEntity,
  arrayToCartesian3,
  getCenter,
  toNumber,
  toCartesian3,
  calculatArea,
  calculatPerimeter
} from '@/components/Cesium/libs/cesium';
import { toRaw } from 'vue';
export const createCircle = (viewer, options = null) => {
  if (!viewer || !options) {
    return;
  }
  let radius = options.content.geometry.radius;
  const semiMinorAxisCb = new Cesium.CallbackProperty(function () {
    return radius || 1;
  }, false);

  let center = options.content.geometry.coordinates;
  let color = getFeatureColor(options.content.properties?.color);
  // 根据点位置创建面
  let entity = viewer.entities.add({
    id: options.area_id || generateKey(),
    position: new Cesium.Cartesian3.fromDegrees(...options.content.geometry.coordinates, 0),
    name: 'circle',
    label: {
      text: options.name || '',
      font: '14px sans-serif',
      style: Cesium.LabelStyle.FILL_AND_OUTLINE, //FILL  FILL_AND_OUTLINE OUTLINE
      fillColor: Cesium.Color.WHITE,
      showBackground: true, //指定标签后面背景的可见性
      backgroundColor: new Cesium.Color(0.165, 0.165, 0.165, 0.8), // 背景颜色
      backgroundPadding: new Cesium.Cartesian2(6, 6), //指定以像素为单位的水平和垂直背景填充padding
      pixelOffset: new Cesium.Cartesian2(0, -25),
      disableDepthTestDistance: Number.POSITIVE_INFINITY
    },
    ellipse: {
      material: color || Cesium.Color.WHITE.withAlpha(0.4),
      outline: true,
      outlineColor: Cesium.Color.RED,
      outlineWidth: 1, // 是否被提供的材质填充
      fill: true,
      semiMajorAxis: semiMinorAxisCb,
      semiMinorAxis: semiMinorAxisCb,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND //贴地
    }
  });

  // 添加自定义属性
  addPropertiesToEntity(entity, {
    type: 'circle',
    title: options.name || '',
    id: options.area_id,
    center: center || [],
    flightAreaType: options.type || FLIGHTAREATYPE.DFENCE,
    area: toNumber(radius * radius * Math.PI, 2),
    length: toNumber(radius * 2 * Math.PI, 2),
    radius: toNumber(radius, 2),
    color: color || Cesium.Color.WHITE.withAlpha(0.4)
  });
  return entity;
};

export const createPolygon = (viewer, options = null) => {
  if (!viewer || !options) {
    return;
  }
  let positions = toRaw(options.content.geometry.coordinates[0]);
  const cb = new Cesium.CallbackProperty(function () {
    let t = arrayToCartesian3(positions);
    let arrPoint = new Cesium.PolygonHierarchy(t);
    return arrPoint;
  }, false);
  const center = getCenterCoordinates(positions);
  let color = getFeatureColor(options.content.properties?.color); // 根据点位置创建面
  let entity = viewer.entities.add({
    id: options.area_id || generateKey(),
    name: 'polygon',
    position: toCartesian3(center),
    label: {
      text: options.name || '',
      font: '14px sans-serif',
      style: Cesium.LabelStyle.FILL_AND_OUTLINE, //FILL  FILL_AND_OUTLINE OUTLINE
      fillColor: Cesium.Color.WHITE,
      showBackground: true, //指定标签后面背景的可见性
      backgroundColor: new Cesium.Color(0.165, 0.165, 0.165, 0.8), // 背景颜色
      backgroundPadding: new Cesium.Cartesian2(6, 6), //指定以像素为单位的水平和垂直背景填充padding
      pixelOffset: new Cesium.Cartesian2(0, -25),
      disableDepthTestDistance: Number.POSITIVE_INFINITY
    },
    polygon: {
      hierarchy: cb,
      show: true,
      fill: true,
      material: color || Cesium.Color.WHITE.withAlpha(0.4),
      outline: true,
      outlineColor: Cesium.Color.ALICEBLUE,
      outlineWidth: 2,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
    }
  });
  // 添加自定义属性
  let cartesianPoints = arrayToCartesian3(positions);
  // 计算面积
  let area = calculatArea(cartesianPoints);
  // // 计算周长
  let length = calculatPerimeter(cartesianPoints);
  addPropertiesToEntity(entity, {
    type: 'polygon',
    title: options.name || '',
    id: options.area_id,
    flightAreaType: options.type || FLIGHTAREATYPE.DFENCE,
    positions: cartesianPoints,
    area: area,
    length: length,
    color: color || Cesium.Color.WHITE.withAlpha(0.4)
  });
  return entity;
};

// 获取颜色
export const getFeatureColor = color => {
  try {
    if (color instanceof Cesium.Color) {
      return color;
    } else if (typeof color === 'string') {
      return new Cesium.Color.fromCssColorString(color).withAlpha(0.4);
    }
  } catch (error) {
    return Cesium.Color.GREEN.withAlpha(0.4);
  }
};

// 获取颜色
export const getFeatureColorString = option => {
  try {
    if (option.color instanceof Cesium.Color) {
      if (option.flightAreaType === FLIGHTAREATYPE.DFENCE) {
        return COLOR_TYPE_STRING[FLIGHTAREATYPE.DFENCE];
      } else if (option.flightAreaType === FLIGHTAREATYPE.NFZ) {
        return COLOR_TYPE_STRING[FLIGHTAREATYPE.NFZ];
      }
      return COLOR_TYPE_STRING[FLIGHTAREATYPE.DFENCE];
    } else if (typeof option.color === 'string') {
      return option.color;
    }
  } catch (error) {
    option.color;
  }
};
