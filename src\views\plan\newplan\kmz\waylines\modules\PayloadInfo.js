class PayloadInfo {
  wpml_payloadEnumValue = 0; //负载机型主类型
  wpml_payloadSubEnumValue = 0; //负载机型主类型
  wpml_payloadPositionIndex = 0; //负载挂载位置
  constructor(options = {}) {
    if (!options.payloadEnumValue || !options.payloadPositionIndex) {
      return null;
    }
    this.wpml_payloadEnumValue = options.payloadEnumValue;
    this.wpml_payloadPositionIndex = options.payloadPositionIndex;
    this.wpml_payloadSubEnumValue = options.payloadSubEnumValue;
  }

  setPayloadEnumValue(value) {
    this.wpml_payloadEnumValue = value;
  }
  getPayloadEnumValue() {
    return this.wpml_payloadEnumValue;
  }

  setPayloadPositionIndex(value) {
    this.wpml_payloadPositionIndex = value;
  }
  getPayloadPositionIndex() {
    return this.wpml_payloadPositionIndex;
  }

  setPayloadSubEnumValue(value) {
    this.wpml_payloadSubEnumValue = value;
  }
  getPayloadSubEnumValue() {
    return this.wpml_payloadSubEnumValue;
  }
}
export { PayloadInfo };
