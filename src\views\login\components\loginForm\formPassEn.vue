<template>
  <div>
    <el-form ref="loginFormRef" :model="loginFormData" :rules="loginRules">
      <template v-if="props.accountType === 0">
        <el-form-item prop="account">
          <div class="login-form-input">
            <span class="ff-cloud-icon clound-status-person custom-icon" />
            <el-input
              ref="account"
              v-model="loginFormData.account"
              :readonly="readonly"
              :placeholder="$t('login.enterUserName')"
              name="account"
              type="text"
              tabindex="1"
              autocomplete="new-password"
              @focus="readonly = false"
              @keyup.enter="handleLogin"
            />
          </div>
        </el-form-item>
      </template>
      <template v-if="props.accountType === 1">
        <el-form-item prop="tenantId">
          <div class="login-form-input">
            <span class="ff-cloud-icon cloud-tenant custom-icon" />
            <el-input
              ref="tenantId"
              v-model="loginFormData.tenantId"
              :readonly="readonly"
              :placeholder="$t('login.enterTenantID')"
              name="tenantId"
              type="text"
              tabindex="1"
              autocomplete="new-password"
              @focus="readonly = false"
              @keyup.enter="handleLogin"
            />
          </div>
        </el-form-item>
        <el-form-item prop="username">
          <div class="login-form-input">
            <span class="ff-cloud-icon clound-status-person custom-icon" />
            <el-input
              ref="username"
              v-model="loginFormData.username"
              :readonly="readonly"
              :placeholder="$t('login.enterUserName')"
              type="text"
              tabindex="2"
              autocomplete="new-password"
              @focus="readonly = false"
              @keyup.enter="handleLogin"
            />
          </div>
        </el-form-item>
      </template>
      <el-form-item prop="password">
        <div class="login-form-input">
          <span class="ff-cloud-icon cloud-lock custom-icon" />
          <el-input
            ref="passwordRef"
            v-model="loginFormData.password"
            :readonly="readonly"
            :placeholder="$t('login.enterPwd')"
            :type="passwordType"
            tabindex="3"
            autocomplete="new-password"
            @focus="readonly = false"
            @keyup.enter="handleLogin"
          />
          <span class="show-pwd cursor-pointer" @click="showPwd">
            <i
              class="ff-cloud-icon"
              :class="
                passwordType === 'password'
                  ? 'clound-open-eye'
                  : 'clound-close-eye'
              "
            />
          </span>
        </div>
      </el-form-item>
      <div class="login-box-foot">
        <el-checkbox v-model="remember">
          {{ $t('login.rememberMe') }}
        </el-checkbox>
      </div>
      <el-button
        :loading="submitLoading"
        type="primary"
        class="login-form-btn"
        @click="handleLogin"
      >
        {{ $t('login.login') }}
      </el-button>
    </el-form>
  </div>
</template>

<script setup>
import i18n from '@/lang';
import _ from 'lodash';
import { useRoute, useRouter } from 'vue-router';
//

import { getTenantRememberPwd, setTenantRememberPwd } from '@/utils/store';
import { useUserStore } from '@/store/modules/user.js';

const userStore = useUserStore();
const props = defineProps({
  // 账号类型 0主账号登录 1子账号登录
  accountType: {
    type: Number,
    default: 0
  },
  loginAction: {
    type: Function,
    default: () => {}
  }
});

const remember = ref(false);
const readonly = ref(true);
const isExclusive = ref(false);
const passwordRef = ref('passwordRef');
const loginFormRef = ref('loginFormRef');
const route = useRoute();
const router = useRouter();

const loginFormData = reactive({
  username: '',
  password: '',
  account: '',
  tenantId: ''
});

const loginRules = {
  username: [
    {
      required: true,
      trigger: ['blur', 'change'],
      message: `${i18n.global.t('login.enterUserName')}`
    }
  ],
  password: [
    {
      required: true,
      trigger: ['blur', 'change'],
      message: i18n.global.t('login.enterPwd')
    }
  ],
  account: [
    {
      required: true,
      trigger: ['blur', 'change'],
      message: `${i18n.global.t('login.enterUserName')}`
    }
  ],
  tenantId: [
    {
      required: true,
      trigger: ['blur', 'change'],
      message: i18n.global.t('login.enterTenantID')
    }
  ]
};

const submitLoading = ref(false);
const passwordType = ref('password');
const rememberConfig = reactive({});

const showPwd = () => {
  passwordType.value = passwordType.value === 'password' ? '' : 'password';
  nextTick(() => {
    passwordRef.value.focus();
  });
};

const handleLogin = () => {
  submitLoading.value = true;
  loginFormRef.value.validate(async valid => {
    if (valid) {
      props.loginAction();
    } else {
      submitLoading.value = false;
      return false;
    }
  });
};

const validSubmit = async validResult => {
  if (validResult) {
    try {
      if (props.accountType === 0) {
        if (isExclusive.value) {
          const form = _.cloneDeep(loginFormData);
          await userStore
            .exclusiveLogin({ data: form, tenantId: route.query.tenant })
            .then(() => {
              ElMessage.success(
                i18n.global.t('page.dialog.actionFb.successfullyLogin')
              );
              router.replace({ name: 'dashboard' });
            })
            .catch(() => {
              submitLoading.value = false;
            })
            .finally(() => {
              submitLoading.value = false;
            });
        } else {
          userStore
            .login(loginFormData)
            .then(() => {
              ElMessage.success(
                i18n.global.t('page.dialog.actionFb.successfullyLogin')
              );
              router.replace({ name: 'dashboard' });
            })
            .catch(() => {
              submitLoading.value = false;
            })
            .finally(() => {
              submitLoading.value = false;
            });
        }
      } else {
        // return
        userStore
          .subAccountLogin(loginFormData)
          .then(() => {
            ElMessage.success(
              i18n.global.t('page.dialog.actionFb.successfullyLogin')
            );
            router.replace({ name: 'dashboard' });
          })
          .catch(() => {
            submitLoading.value = false;
          })
          .finally(() => {
            submitLoading.value = false;
          });
      }

      rememberAccount();
    } catch {
      console.log('login error');
    }
  }
  submitLoading.value = false;
};

const setRememberConfig = () => {
  const config =
    props.accountType === 0
      ? isExclusive.value
        ? rememberConfig.exclusive
        : rememberConfig.mainAccound
      : rememberConfig.subAccound;

  remember.value = config ? true : false;

  Object.assign(loginFormData, config ? config[1] : {});
};

const rememberAccount = () => {
  const config =
    props.accountType === 0
      ? isExclusive.value
        ? 'exclusive'
        : 'mainAccound'
      : 'subAccound';

  rememberConfig[config] = [
    remember.value,
    remember.value ? loginFormData : {}
  ];

  setTenantRememberPwd(rememberConfig);
};

watch(
  () => props.accountType,
  newVal => {
    // accountType 0 为主账号 1 为子账号
    if (newVal >= 0) {
      Object.assign(loginFormData, {
        username: '',
        password: '',
        account: '',
        tenantId: ''
      });

      setRememberConfig();

      setTimeout(() => {
        loginFormRef?.value.clearValidate();
      }, 50);
    }
  }
);

const handleSubmit = data => {
  if (
    !_.isEmpty(data) &&
    data.emitData.loginType === 'enPwdLogin' &&
    data.emitName === 'dragVerify'
  ) {
    validSubmit(data.emitData);
  }
};

const handleCancle = data => {
  if (data.emitName === 'dragVerify') {
    submitLoading.value = false;
  }
};
onMounted(() => {
  Object.assign(rememberConfig, _.cloneDeep(getTenantRememberPwd()));

  if (JSON.stringify(route.query) !== '{}' && route.query.tenant) {
    isExclusive.value = true;
  }

  setRememberConfig();
  window.$bus.on('dialogCancel', handleCancle);

  // 监听滑动验证码弹窗beforeClose事件
  window.$bus.on('dialogBeforeClose', handleSubmit);
});

onUnmounted(() => {
  window.$bus.off('dialogCancel');
  window.$bus.off('dialogBeforeClose');
});

defineExpose({
  // clearForm
});
</script>

<style lang="scss" scoped>
:deep() {
  .el-form-item.is-error .el-input__wrapper {
    box-shadow: none;
    &:hover {
      box-shadow: none;
    }
  }

  .el-form-item.is-error .el-input__wrapper.is-focus {
    box-shadow: none !important;
  }
}

.login-form-input {
  width: 100%;
  display: flex;
  padding: 18px 20px;
  border-radius: 30px;
  border: 1px solid #555555;
  background: #ffffff;
  line-height: 1;

  align-items: center;

  :deep() {
    .el-input__wrapper {
      box-shadow: none;
    }

    .el-input__inner {
      height: unset;
      border: none;
      background: transparent;
      line-height: 1;
      flex: 1;
      -webkit-appearance: none;

      &:-webkit-autofill {
        -webkit-box-shadow: 0 0 0 30px white inset !important;
        -webkit-text-fill-color: initial !important;
      }
    }

    .el-input__prefix {
      position: relative;
    }

    .custom-icon {
      color: #cecece;
      font-size: 18px;
    }
  }
}

:deep() {
  .login-box-foot {
    padding-left: 5%;

    .el-checkbox {
      color: #555555;

      .el-checkbox__inner {
        border-color: #a8bdd3;
        background-color: transparent;
      }
    }

    .el-checkbox__input.is-checked + .el-checkbox__label {
      color: #555555;
    }

    .el-link--primary {
      color: #555555;
    }

    .el-checkbox__inner::after {
      border-color: #555555;
    }
  }
  .login-form-input-icon {
    width: 20px;
  }

  .login-form-btn {
    margin-top: 25px;
    width: 100%;
    height: 3vw;
    padding: 0;
    border-radius: 30px;
    font-weight: 500;
    font-size: 2.8vh;
    font-family: PingFangSC-Regular;
    color: #ffffff;
    text-align: center;
    background: #097efc;
    border: none;

    &:hover,
    &:focus {
      background: #2fb7ff;
      border: none;
    }
  }
  .el-form-item {
    margin-bottom: 20px;
  }
}
</style>
