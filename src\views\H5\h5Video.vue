<template>
	<div>
		<div class="drone-name"> {{`${videoInfo.nick_name || ''}-`}}{{videoInfo.device_name || ''}}</div>
		<div style="width: 100%;height: 360px;">
			<VideoPlayer :playerId="currentQuery.share_id" :show-close="false" :show-title="false" :videoData="videoData"/>
		</div>
	</div>
</template>

<script setup>
import { onMounted, onUnmounted, ref } from 'vue';
import VideoPlayer from '../videos/live-stream/components/VideoPlayer.vue';
import { useRouter } from 'vue-router';
import { getRealVideoUrl, checkShareUrl} from '@/api/devices/maintenance'
import { ElMessage } from 'element-plus';
const router = useRouter();
const currentQuery = router.currentRoute.value.query;
const videoInfo=ref({})
const videoData = ref([])
let intervalTimer = null;

onMounted(()=>{
	handleCountChange();
	setTimeout(()=>{
		initVideo();
	},500)
	intervalTimer = setInterval(() => {
		checkUrl();
	}, 1000 * 30)
})

function initVideo () {
	getRealVideoUrl({
		share_id: currentQuery.share_id
	}).then(res=>{
		videoInfo.value = res
		videoData.value = [res.video_url] || []
	})
}

function checkUrl () {
	checkShareUrl({
		share_id: currentQuery.share_id
	}).then(res=>{
		if(!res) {
			ElMessage.error('链接已过期')
			videoData.value = []
		}
	})
}

onUnmounted(() => {
  clearInterval(intervalTimer);
});

function handleCountChange() {
  window.$bus.emit('changeVideoCount', 1);
}
</script>
<style lang="scss" scoped>
.drone-name{
	height: 40px;
	line-height: 40px;
	padding: 0 8px;
	color: #fff;
	background-color: #ccc;
}
</style>