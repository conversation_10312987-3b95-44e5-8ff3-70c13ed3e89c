import AgoraRTC from 'agora-rtc-sdk-ng';
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { getAgoraToken, refreshAgoraToken } from '@/api/call';

// 检查是否在安全上下文中运行
const isSecureContext =
  window.isSecureContext ||
  window.location.protocol === 'https:' ||
  window.location.hostname === 'localhost' ||
  window.location.hostname === '127.0.0.1';

// 状态变量
const isJoined = ref(false);
const isAudioMuted = ref(false);
const currentChannel = ref('');
const currentUid = ref(null);
const remoteUsers = ref([]);
const isSecure = ref(isSecureContext);
const errorMessage = ref('');
const microphoneDevices = ref([]);
const selectedMicrophoneId = ref('');
const isConnecting = ref(false);
const hasMicrophonePermission = ref(false);

// RTC 对象
let rtcClient = null;
let localAudioTrack = null;

// 音频质量配置
const audioConfig = {
  // 高质量语音通话配置
  AEC: true, // 回声消除
  AGC: true, // 自动增益控制
  ANS: true, // 自动噪声抑制
  encoderConfig: {
    sampleRate: 48000, // 48kHz采样率
    stereo: true, // 立体声
    bitrate: 128 // 比特率
  }
};

/**
 * 获取声网Token
 * @param {string} uid - 用户ID
 * @param {string} channelName - 频道名
 */
const getToken = async (uid, channelName) => {
  try {
    const response = await getAgoraToken(uid, channelName);

    if (response && response.token) {
      return response;
    }
    throw new Error('获取Token失败');
  } catch (error) {
    console.error('获取声网Token失败:', error);
    throw error;
  }
};

/**
 * 刷新声网Token
 * @param {string} uid - 用户ID
 * @param {string} channelName - 频道名
 */
const refreshToken = async (uid, channelName) => {
  try {
    const response = await refreshAgoraToken(uid, channelName);

    if (response && response.token) {
      return response;
    }
    throw new Error('刷新Token失败');
  } catch (error) {
    console.error('刷新声网Token失败:', error);
    throw error;
  }
};

/**
 * 请求麦克风权限
 * @returns {Promise<boolean>} - 是否获得权限
 */
const requestMicrophonePermission = async () => {
  try {
    // 检查是否在安全上下文中运行
    if (!isSecureContext) {
      throw new Error('需要在安全上下文中运行才能访问媒体设备');
    }

    // 检查是否支持mediaDevices API
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      throw new Error('您的浏览器不支持媒体设备API');
    }

    // 请求媒体权限
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

    // 获取成功后立即释放资源
    stream.getTracks().forEach(track => track.stop());

    hasMicrophonePermission.value = true;
    return true;
  } catch (error) {
    console.error('请求麦克风权限失败:', error);
    hasMicrophonePermission.value = false;

    // 提供更详细的错误信息
    if (error.name === 'NotAllowedError') {
      errorMessage.value = '麦克风访问被拒绝，请检查浏览器权限设置';
      ElMessage.warning('麦克风访问被拒绝，您将无法在通话中发言');
    } else if (error.name === 'NotFoundError') {
      errorMessage.value = '未找到麦克风设备，请确认麦克风已连接';
      ElMessage.warning('未找到麦克风设备，您将无法在通话中发言');
    } else {
      errorMessage.value = `麦克风访问失败: ${error.message}`;
      ElMessage.warning('麦克风访问失败，您将无法在通话中发言');
    }

    return false;
  }
};

/**
 * 获取可用的麦克风设备列表
 * @returns {Promise<Array>} - 麦克风设备列表
 */
const getMicrophoneDevices = async () => {
  try {
    // 先请求麦克风权限
    const hasPermission = await requestMicrophonePermission();
    if (!hasPermission) {
      return [];
    }

    // 获取设备列表
    const devices = await navigator.mediaDevices.enumerateDevices();
    const audioDevices = devices.filter(device => device.kind === 'audioinput');

    microphoneDevices.value = audioDevices;

    // 如果有设备且没有选择过，默认选择第一个
    if (audioDevices.length > 0 && !selectedMicrophoneId.value) {
      selectedMicrophoneId.value = audioDevices[0].deviceId;
    }

    return audioDevices;
  } catch (error) {
    console.error('获取麦克风设备失败:', error);
    errorMessage.value = `获取麦克风设备失败: ${error.message}`;
    return [];
  }
};

/**
 * 选择麦克风设备
 * @param {string} deviceId - 设备ID
 */
const selectMicrophoneDevice = async deviceId => {
  try {
    selectedMicrophoneId.value = deviceId;

    // 如果已经在通话中，切换麦克风
    if (isJoined.value && localAudioTrack) {
      await localAudioTrack.setDevice(deviceId);
    }

    return true;
  } catch (error) {
    console.error('切换麦克风设备失败:', error);
    errorMessage.value = `切换麦克风设备失败: ${error.message}`;
    return false;
  }
};

/**
 * 初始化 Agora 客户端
 */
const initClient = () => {
  // 检查是否在安全上下文中运行
  if (!isSecureContext) {
    const msg = '语音通话需要在安全上下文中运行，请使用HTTPS协议或localhost访问';
    console.error(msg);
    errorMessage.value = msg;
    ElMessage.error(msg);
    return null;
  }

  if (!rtcClient) {
    try {
      // 创建RTC客户端，使用RTC模式和VP8编码
      rtcClient = AgoraRTC.createClient({ mode: 'rtc', codec: 'vp8' });

      // 设置日志级别，方便调试
      AgoraRTC.setLogLevel(0); // 0: DEBUG, 1: INFO, 2: WARNING, 3: ERROR, 4: NONE

      // 注册事件处理程序
      registerClientEvents();

      console.log('Agora RTC 客户端初始化成功');
    } catch (error) {
      console.error('创建Agora客户端失败:', error);
      errorMessage.value = `创建客户端失败: ${error.message}`;
      return null;
    }
  }

  return rtcClient;
};

/**
 * 注册客户端事件
 */
const registerClientEvents = () => {
  // 用户发布媒体
  rtcClient.on('user-published', handleUserPublished);

  // 用户取消发布媒体
  rtcClient.on('user-unpublished', (user, mediaType) => {
    if (mediaType === 'audio') {
      const existingUser = remoteUsers.value.find(u => u.uid === user.uid);
      if (existingUser) {
        existingUser.hasAudio = false;
      }
      console.log(`远程用户 ${user.uid} 取消发布了音频`);
    }
  });

  // 用户加入频道
  rtcClient.on('user-joined', user => {
    const existingUser = remoteUsers.value.find(u => u.uid === user.uid);
    if (!existingUser) {
      remoteUsers.value.push({
        uid: user.uid,
        hasAudio: false
      });
    }
    console.log(`远程用户 ${user.uid} 加入了频道`);
  });

  // 用户离开频道
  rtcClient.on('user-left', user => {
    remoteUsers.value = remoteUsers.value.filter(u => u.uid !== user.uid);
    console.log(`远程用户 ${user.uid} 离开了频道`);
  });

  // Token即将过期
  rtcClient.on('token-privilege-will-expire', async () => {
    console.log('Token即将过期，正在刷新...');
    try {
      const tokenData = await refreshToken(currentUid.value, currentChannel.value);
      await rtcClient.renewToken(tokenData.token);
      console.log('Token刷新成功');
    } catch (error) {
      console.error('Token刷新失败:', error);
      errorMessage.value = 'Token刷新失败，通话可能会中断';
      ElMessage.warning('通话Token即将过期，请尽快结束通话');
    }
  });

  // 异常处理
  rtcClient.on('exception', event => {
    console.warn('Agora客户端异常:', event);
    errorMessage.value = `通话异常: ${event.code}`;
    if (event.code === 'WEB_SECURITY_RESTRICT') {
      ElMessage.error('语音通话需要在安全上下文中运行，请使用HTTPS协议或localhost访问');
    }
  });

  // 连接状态变化
  rtcClient.on('connection-state-change', (curState, prevState) => {
    console.log(`连接状态从 ${prevState} 变为 ${curState}`);

    if (curState === 'CONNECTED') {
      // 连接成功，可以尝试播放远程音频
      setTimeout(forcePlayRemoteAudio, 1000);
    }
  });

  // 连接状态变化
  rtcClient.on('connection-state-change', (curState, prevState) => {
    console.log(`连接状态从 ${prevState} 变为 ${curState}`);

    if (curState === 'CONNECTED') {
      // 连接成功，可以尝试播放远程音频
      setTimeout(forcePlayRemoteAudio, 1000);
    }
  });
};

/**
 * 设置默认音频音量
 */
const setDefaultAudioVolume = async () => {
  try {
    // 设置本地音频采集音量（0-100）
    if (localAudioTrack) {
      await localAudioTrack.setVolume(80); // 设置本地采集音量为80%
      console.log('本地音频采集音量已设置为80%');
    }

    // 设置远端音频播放音量
    if (rtcClient) {
      // 遍历所有远程用户并设置音量
      remoteUsers.value.forEach(user => {
        const remoteUser = rtcClient.remoteUsers.find(u => u.uid === user.uid);
        if (remoteUser && remoteUser.audioTrack) {
          remoteUser.audioTrack.setVolume(90); // 设置远端播放音量为90%
          console.log(`远程用户 ${user.uid} 音频播放音量已设置为90%`);
        }
      });
    }
  } catch (error) {
    console.error('设置默认音频音量失败:', error);
  }
};

/**
 * 加入频道
 */
const joinChannel = async (channelName, uid) => {
  // 首先请求麦克风权限
  await requestMicrophonePermission();

  if (!isSecureContext) {
    const msg = '语音通话需要在安全上下文中运行，请使用HTTPS协议或localhost访问';
    console.error(msg);
    errorMessage.value = msg;
    ElMessage.error(msg);
    return false;
  }

  if (isJoined.value) {
    console.warn('已经在通话中，请先结束当前通话');
    return false;
  }

  isConnecting.value = true;
  errorMessage.value = '';

  try {
    // 初始化客户端
    const client = initClient();
    if (!client) {
      isConnecting.value = false;
      return false;
    }

    // 获取Token
    const tokenData = await getToken(uid, channelName);

    // 加入频道
    console.log(`正在加入频道: ${channelName}, 用户ID: ${uid}`);
    await client.join(tokenData.app_id, tokenData.channel_name, tokenData.token, uid);
    currentChannel.value = channelName;
    currentUid.value = uid;
    console.log(`已成功加入频道: ${channelName}`);

    try {
      // 获取可用的麦克风设备
      await getMicrophoneDevices();

      // 创建本地音频轨道，使用高质量配置
      const audioTrackConfig = {
        ...audioConfig,
        microphoneId: selectedMicrophoneId.value || undefined
      };

      // 只有在有麦克风权限的情况下才创建音频轨道
      if (hasMicrophonePermission.value) {
        console.log('正在创建本地音频轨道');
        localAudioTrack = await AgoraRTC.createMicrophoneAudioTrack(audioTrackConfig);
        
        // 发布本地音频轨道
        console.log('正在发布本地音频轨道');
        await client.publish([localAudioTrack]);
        console.log('本地音频轨道发布成功');
        
        // 设置默认音频音量
        await setDefaultAudioVolume();
        
        isAudioMuted.value = false;
      } else {
        console.log('没有麦克风权限，将以只听模式加入');
      }

      isJoined.value = true;
      isConnecting.value = false;

      // 延迟检查音频播放
      setTimeout(() => {
        checkAudioPlayback();
        forcePlayRemoteAudio();
      }, 2000);

      // 添加用户交互监听，确保音频能够播放
      const enableAudioPlayback = () => {
        forcePlayRemoteAudio();
        document.removeEventListener('click', enableAudioPlayback);
        document.removeEventListener('touchstart', enableAudioPlayback);
      };
      
      document.addEventListener('click', enableAudioPlayback, { once: true });
      document.addEventListener('touchstart', enableAudioPlayback, { once: true });

      return true;
    } catch (mediaError) {
      console.error('创建或发布音频轨道失败:', mediaError);
      // 即使没有麦克风，也允许加入通话（只能听不能说）
      isJoined.value = true;
      isAudioMuted.value = true;
      isConnecting.value = false;
      return true;
    }
  } catch (error) {
    console.error('加入频道失败:', error);
    errorMessage.value = `加入通话失败: ${error.message}`;
    isConnecting.value = false;
    ElMessage.error(errorMessage.value);
    return false;
  }
};

/**
 * 离开频道
 */
const leaveChannel = async () => {
  if (!isJoined.value) {
    return;
  }

  try {
    // 关闭本地音频轨道
    if (localAudioTrack) {
      localAudioTrack.close();
      localAudioTrack = null;
    }

    // 离开频道
    await rtcClient.leave();
    console.log('已成功离开频道');

    // 重置状态
    isJoined.value = false;
    isAudioMuted.value = false;
    currentChannel.value = '';
    remoteUsers.value = [];
    isConnecting.value = false;

    return true;
  } catch (error) {
    console.error('离开频道失败:', error);
    isConnecting.value = false;
    return false;
  }
};

/**
 * 切换静音状态
 */
const toggleMute = async () => {
  if (!localAudioTrack) return;

  try {
    if (isAudioMuted.value) {
      await localAudioTrack.setEnabled(true);
      isAudioMuted.value = false;
    } else {
      await localAudioTrack.setEnabled(false);
      isAudioMuted.value = true;
    }

    return isAudioMuted.value;
  } catch (error) {
    console.error('切换静音状态失败:', error);
    return isAudioMuted.value;
  }
};

/**
 * 邀请用户加入当前频道
 */
const inviteUser = user => {
  if (!isJoined.value || !currentChannel.value) {
    console.warn('未加入频道，无法邀请用户');
    return null;
  }

  return currentChannel.value;
};

/**
 * 检查音频播放状态
 */
const checkAudioPlayback = () => {
  console.log('=== 音频播放状态检查 ===');
  console.log('本地音频状态:', {
    hasLocalTrack: !!localAudioTrack,
    isEnabled: localAudioTrack?.enabled,
    isMuted: isAudioMuted.value
  });

  console.log('远程用户音频状态:');
  remoteUsers.value.forEach(user => {
    const remoteUser = rtcClient?.remoteUsers?.find(u => u.uid === user.uid);
    console.log(`用户 ${user.uid}:`, {
      hasAudioTrack: !!remoteUser?.audioTrack,
      isPlaying: remoteUser?.audioTrack?.isPlaying,
      volume: remoteUser?.audioTrack?.getVolumeLevel?.()
    });
  });

  // 检查浏览器音频上下文状态
  if (window.AudioContext || window.webkitAudioContext) {
    const AudioContext = window.AudioContext || window.webkitAudioContext;
    const audioContext = new AudioContext();
    console.log('浏览器音频上下文状态:', audioContext.state);

    if (audioContext.state === 'suspended') {
      console.warn('音频上下文被暂停，尝试恢复...');
      audioContext.resume().then(() => {
        console.log('音频上下文已恢复');
      }).catch(err => {
        console.error('恢复音频上下文失败:', err);
      });
    }
  }
};

/**
 * 确保音频上下文处于活动状态
 */
const ensureAudioContext = async () => {
  try {
    // 检查是否支持AudioContext
    if (window.AudioContext || window.webkitAudioContext) {
      const AudioContext = window.AudioContext || window.webkitAudioContext;
      const audioContext = new AudioContext();

      // 如果音频上下文被暂停，尝试恢复
      if (audioContext.state === 'suspended') {
        console.log('音频上下文被暂停，尝试恢复...');
        await audioContext.resume();
        console.log('音频上下文已恢复');
        return true;
      }

      return audioContext.state === 'running';
    }
    return false;
  } catch (error) {
    console.error('恢复音频上下文失败:', error);
    return false;
  }
};

/**
 * 强制播放所有远端音频
 */
const forcePlayRemoteAudio = async () => {
  try {
    // 确保音频上下文处于活动状态
    await ensureAudioContext();
    
    if (!rtcClient || !rtcClient.remoteUsers) {
      console.warn('没有远程用户或客户端未初始化');
      return;
    }

    console.log(`尝试强制播放 ${rtcClient.remoteUsers.length} 个远程用户的音频`);
    
    for (const user of rtcClient.remoteUsers) {
      if (user.audioTrack) {
        try {
          if (!user.audioTrack.isPlaying) {
            console.log(`强制播放远程用户 ${user.uid} 的音频`);
            await user.audioTrack.play();
          }
          user.audioTrack.setVolume(100);
          console.log(`远程用户 ${user.uid} 音频音量已设置为100%`);
        } catch (error) {
          console.error(`强制播放远程用户 ${user.uid} 音频失败:`, error);
        }
      } else {
        console.warn(`远程用户 ${user.uid} 没有音频轨道`);
      }
    }
  } catch (error) {
    console.error('强制播放远程音频失败:', error);
  }
};

/**
 * 音频问题排查
 */
const troubleshootAudio = () => {
  console.log('=== 开始音频问题排查 ===');

  // 检查浏览器支持
  console.log('浏览器支持检查:', {
    getUserMedia: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
    audioContext: !!(window.AudioContext || window.webkitAudioContext),
    isSecureContext: window.isSecureContext
  });

  // 检查权限
  console.log('权限检查:', {
    hasMicrophonePermission: hasMicrophonePermission.value,
    isSecure: isSecure.value
  });

  // 检查连接状态
  console.log('连接状态:', {
    isJoined: isJoined.value,
    currentChannel: currentChannel.value,
    remoteUsersCount: remoteUsers.value.length
  });

  // 执行音频播放检查
  checkAudioPlayback();

  // 尝试强制播放
  forcePlayRemoteAudio();
};

/**
 * 处理远程用户发布媒体
 */
const handleUserPublished = async (user, mediaType) => {
  try {
    console.log(`远程用户 ${user.uid} 发布了 ${mediaType} 媒体`);
    
    // 订阅远程用户
    await rtcClient.subscribe(user, mediaType);
    console.log(`已订阅远程用户 ${user.uid} 的 ${mediaType}`);

    if (mediaType === 'audio') {
      // 确保音频轨道存在
      if (user.audioTrack) {
        // 设置远端音频播放音量
        user.audioTrack.setVolume(100);
        
        // 立即播放远程用户的音频
        try {
          await user.audioTrack.play();
          console.log(`远程用户 ${user.uid} 音频开始播放`);
          
          // 验证播放状态
          setTimeout(() => {
            if (user.audioTrack.isPlaying) {
              console.log(`✓ 远程用户 ${user.uid} 音频正在播放`);
            } else {
              console.warn(`✗ 远程用户 ${user.uid} 音频未播放，尝试重新播放`);
              user.audioTrack.play().catch(console.error);
            }
          }, 500);
          
        } catch (playError) {
          console.error(`播放远程用户 ${user.uid} 音频失败:`, playError);
          
          // 如果自动播放失败，等待用户交互
          const playOnInteraction = async () => {
            try {
              await user.audioTrack.play();
              console.log(`用户交互后成功播放远程用户 ${user.uid} 音频`);
            } catch (err) {
              console.error(`用户交互后仍无法播放:`, err);
            }
            document.removeEventListener('click', playOnInteraction);
            document.removeEventListener('touchstart', playOnInteraction);
          };
          
          document.addEventListener('click', playOnInteraction, { once: true });
          document.addEventListener('touchstart', playOnInteraction, { once: true });
          ElMessage.warning('请点击页面以启用音频播放');
        }
      }

      // 更新远程用户状态
      const existingUser = remoteUsers.value.find(u => u.uid === user.uid);
      if (!existingUser) {
        remoteUsers.value.push({
          uid: user.uid,
          hasAudio: true,
          audioTrack: user.audioTrack
        });
      } else {
        existingUser.hasAudio = true;
        existingUser.audioTrack = user.audioTrack;
      }

      console.log(`已订阅并播放远程用户 ${user.uid} 的音频`);
    }
  } catch (error) {
    console.error('订阅用户媒体失败:', error);
    ElMessage.error(`订阅远程用户音频失败: ${error.message}`);
  }
};

export {
  joinChannel,
  leaveChannel,
  toggleMute,
  inviteUser,
  getToken as getAgoraToken,
  refreshToken as refreshAgoraToken,
  getMicrophoneDevices,
  selectMicrophoneDevice,
  requestMicrophonePermission,
  setDefaultAudioVolume,
  checkAudioPlayback,
  forcePlayRemoteAudio,
  troubleshootAudio,
  isJoined,
  isAudioMuted,
  remoteUsers,
  currentChannel,
  currentUid,
  isSecure,
  errorMessage,
  microphoneDevices,
  selectedMicrophoneId,
  isConnecting,
  hasMicrophonePermission
};
