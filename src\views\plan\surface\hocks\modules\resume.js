// 这里通过获取线上航面数据恢复航线
import {
  setCameraView,
  createAirPortPoint,
  toCartesian3,
  getBoundingRectangle
} from '@/components/Cesium/libs/cesium/index';
import * as Cesium from 'cesium';
import { usePlanInfoStore } from '@/store/modules/planInfo.js';
import {
  parmsInfoRect,
  dataInfoRect,
  aircraft,
  drawHandle,
  initWayLineInfo,
  setCurrentDrawrObject
} from './generateWayLineHandle';
import { useDeviceStore } from '@/store/modules/device.js';
import { ElMessageBox, ElMessage } from 'element-plus';
const deviceStore = useDeviceStore();
const planInfoStore = usePlanInfoStore();
export const STATUE_TYPE = {
  EDIT: 'EDIT',
  CREATE: 'CREATE'
};
export const statueRect = {
  statue: STATUE_TYPE.CREATE
};
let viewer,
  config,
  airline_json = null;
/**
 * 执行恢复方法
 * @param {*} planData 线上数据
 */
export const goResume = (v, drawerInstance, callback) => {
  if (getStatue() === STATUE_TYPE.CREATE) {
    return;
  }
  if (!v) return;

  try {
    viewer = v;
    setCurrentDrawrObject(drawerInstance);
    // 以下执行数据编辑操作
    // 获取当前航线信息
    const json = planInfoStore.getCurPlanData();
    let drone_model_key = json.drone_model_key;
    deviceStore.getDeviceInfoByType(drone_model_key);
    if (!json) {
      return;
    }

    // 这里做个判断json.airline_json 如果是字符串就用下面的方法如果是对象就直接赋值
    if (typeof json.airline_json === 'string') {
      airline_json = JSON.parse(json.airline_json);
    } else {
      airline_json = json.airline_json;
    }
    config = airline_json.config ?? null;
    // 这里获取 所选的相机信息
    deviceStore.setCameraSelect(config.parmsInfo.cameraType ?? []);
    deviceStore.setWayLineCameraType(config.parmsInfo.cameraType ?? []);
    if (config && JSON.stringify(config) === '{}') {
      ElMessage.warning('未找到配置文件,请核实 ' + err.message);
      return;
    }

    // 数据赋值
    Object.assign(parmsInfoRect, config.parmsInfo);
    Object.assign(dataInfoRect, config.dataInfo);
    // 这里做个判断json.airline_json 如果是字符串就用下面的方法如果是对象就直接赋值
    if (parmsInfoRect.airPortPlace.length === 0) {
      ElMessage.error('未找到机场信息,请核实导入的KMZ文件');
      return;
    }

    initWayLineInfo(json);
    // 定位到面处
    let br = getBoundingRectangle(config?.dataInfo.originPolygon || []);
    // 定位到面处
    if (br) {
      setCameraView(viewer, {
        destination: br,
        orientation: {
          heading: 0,
          pitch: -Math.PI / 2,
          roll: 0
        },
        duration: 1
      });
    }
    // 绘制机场位置
    aircraft.airPlaceMark = createAirPortPoint(viewer, {
      id: 'airport',
      position: toCartesian3(parmsInfoRect.airPortPlace)
    });
    parmsInfoRect.alreadySetAirPlace = true;
    drawerInstance?.createPolygon(dataInfoRect.originPolygon, drawHandle);
  } catch (error) {
    console.log('执行恢复方法失败,', error);
  } finally {
    callback && callback();
  }
};

export const getStatue = () => {
  const json = planInfoStore.getCurPlanData();
  if (!json || !json.template_types || json.template_types[0] !== 1 || !json.airline_json) {
    statueRect.statue = STATUE_TYPE.CREATE;
  } else {
    statueRect.statue = STATUE_TYPE.EDIT;
  }
  return statueRect.statue;
};
