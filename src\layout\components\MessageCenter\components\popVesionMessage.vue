<template>
  <el-dialog
    v-model="dialogVisible"
    v-if="dialogVisible"
    width="950px"
    :append-to-body="true"
    :close-on-click-modal="false"
    :before-close="handleBeforClose"
    title=" "
    class="version-special-dialog"
  >
    <div class="dialog-content">
      <div class="version-content">
        <div class="version-box">
          <span class="version-text">v{{ formData.version }}</span>
          {{ $t('page.dialog.versionUpdate') }}
        </div>
      </div>
      <div class="version-text">
        <div
          v-for="(rowData, rowIndex) in formData.updateContentPointList"
          :key="rowIndex"
          class="text-item"
        >
          <div class="dot-status" />
          <div>
            {{
              locale === 'zh'
                ? rowData.contentOfChinese
                : rowData.contentOfEnglish
            }}
          </div>
        </div>
      </div>
      <div class="version-time">
        <span class="version-time-text">
          {{ $t('page.dialog.preupdateTime') }}
        </span>
        {{ formData.preUpdateTimeStart }} - {{ formData.preUpdateTimeEnd }}
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer" />
    </template>
  </el-dialog>
</template>
<script setup>
import { useI18n } from 'vue-i18n';

import { getMessageRead } from '@/api/messageCenter';
import { validatenull } from '@/utils/helper';

const { locale } = useI18n();
const formData = ref({});
const dialogVisible = ref(false);
const emit = defineEmits(['updateCout']);

const handleOpen = data => {
  if (!validatenull(data)) {
    formData.value = data;
  }
  dialogVisible.value = true;
};

const handleBeforClose = async () => {
  await getMessageRead({
    messageId: formData.value.versionMessageId,
    messageType: 2
  });
  dialogVisible.value = false;
  formData.value = {};
  emit('updateCount');
};

defineExpose({
  handleOpen
});
</script>
<style lang="scss" scoped>
.dialog_title {
  height: 57px;
  background-color: #3a6cf5;
  background-image: url('@/assets/version/dialog-head.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.dialog-footer {
  height: 80px;
  background-image: url('@/assets/version/dialog-foot.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-color: #52abfc;
}

.version-special-dialog {
  :deep(.el-dialog__footer) {
    padding: 0 !important;
  }
}

:deep() {
  .el-dialog__header {
    border-bottom: none !important;
    padding: 0 !important;
    height: 57px;
  }

  .el-dialog__footer {
    padding: 0 !important;
  }

  .el-dialog__body {
    padding: 0 !important;
  }

  .el-dialog__close {
    color: #fff !important;
  }
}

.dialog-content {
  min-height: 170px;
  background-image: url('@/assets/version/dialog-center.png'),
    linear-gradient(0deg, #52abfc 0%, #3a6cf5 100%);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: flex;
  // justify-content: center;
  align-items: center;
  padding-top: 15px;
  flex-direction: column;

  .version-text {
    color: #fff;
    margin-top: 15px;
  }

  .text-item {
    display: flex;
    align-items: center;
    margin: 20px 0;
    font-size: 16px;
    line-height: 16px;
    font-weight: 600;
  }

  .dot-status {
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background-color: #ffffff;
    margin-right: 10px;
  }

  .version-box {
    font-size: 24px;
    color: #ffffff;
    font-weight: 500;
    border: 1px solid #ffffff;
    border-radius: 25px;
    padding: 5px 20px;
    letter-spacing: 3px;
  }

  .version-time {
    font-size: 14px;
    line-height: 14px;
    font-weight: 400;
    color: #ffffff;
    margin-top: 25px;
  }

  .version-time-text {
    margin-right: 20px;
  }
}
</style>
