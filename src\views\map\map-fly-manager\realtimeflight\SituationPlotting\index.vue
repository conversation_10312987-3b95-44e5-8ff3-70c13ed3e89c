<template>
  <div style="background-color: #11253e">
    <div
      v-if="isMoreShow"
      style="
        width: 125px;
        background-color: #11253e;
        font-family: SourceHanSansSC-Bold;
        font-size: 14px;
        color: #ffffff;
        text-align: left;
        line-height: 22px;
        font-weight: 700;
        padding-top: 8px;
        padding-left: 8px;
        position: absolute;
        right: 50px;
        top: 5px;
      "
    >
      <span style="padding-top: 10px; padding-left: 5px">样式</span><br />
      <div style="height: 5px"></div>
      <el-row>
        <el-tooltip class="item" popper-class="popperClass" effect="light" content="自定义箭头" placement="left">
          <el-col :span="12"
            ><img
              style="height: 50px; width: 50px; cursor: pointer"
              src="@/views/map/map-fly-manager/realtimeflight/SituationPlotting/image/plot/ywjt.png"
              @click="tailedAttackArrowGatherFun"
          /></el-col>
        </el-tooltip>
        <el-tooltip class="item" popper-class="popperClass" effect="light" content="直线箭头" placement="right">
          <el-col :span="12"
            ><img
              style="height: 50px; width: 50px; cursor: pointer"
              src="@/views/map/map-fly-manager/realtimeflight/SituationPlotting/image/plot/xzzx.png"
              @click="straightArrowGatherFun" /></el-col
        ></el-tooltip>
      </el-row>
      <el-row>
        <el-tooltip class="item" popper-class="popperClass" effect="light" content="钳击箭头" placement="left">
          <el-col :span="12"
            ><img
              style="height: 50px; width: 50px; cursor: pointer"
              src="@/views/map/map-fly-manager/realtimeflight/SituationPlotting/image/plot/qjjt.png"
              @click="doubleArrowGatherFun"
          /></el-col>
        </el-tooltip>
        <el-tooltip class="item" popper-class="popperClass" effect="light" content="集结地" placement="right">
          <el-col :span="12"
            ><img
              style="height: 50px; width: 50px; cursor: pointer"
              src="@/views/map/map-fly-manager/realtimeflight/SituationPlotting/image/plot/jjd.png"
              @click="rendezvousGatherFun"
          /></el-col>
        </el-tooltip>
      </el-row>

      <div style="height: 10px"></div>
    </div>

    <div v-if="!editFlag && useType == 'edit'" style="position: absolute; right: 0px; top: 0px">
      <div style="background-color: #11253e; margin: 5px">
        <el-tooltip popper-class="popperClass" effect="light" content="图标点采集" placement="left">
          <div class="iconClass" @click="gatherBillboard">
            <img
              style="height: 13px; width: 13px"
              src="@/views/map/map-fly-manager/realtimeflight/SituationPlotting/image/形状结合.svg"
            />
          </div>
        </el-tooltip>

        <el-tooltip popper-class="popperClass" class="item" effect="light" content="点采集" placement="left">
          <div class="iconClass" style="border-top: 1px solid #344054" @click="gatherPoint">
            <img
              style="height: 13px; width: 13px"
              src="@/views/map/map-fly-manager/realtimeflight/SituationPlotting/image/特殊点.svg"
            />
          </div>
        </el-tooltip>
        <el-tooltip popper-class="popperClass" class="item" effect="light" content="线采集" placement="left">
          <div class="iconClass" style="border-top: 1px solid #344054" @click="gatherPolyline">
            <img
              style="height: 13px; width: 13px"
              src="@/views/map/map-fly-manager/realtimeflight/SituationPlotting/image/形状结合 4.svg"
            />
          </div>
        </el-tooltip>
        <el-tooltip popper-class="popperClass" class="item" effect="light" content="面采集" placement="left">
          <div class="iconClass" style="border-top: 1px solid #344054" @click="gatherPolygon">
            <img
              style="height: 13px; width: 13px"
              src="@/views/map/map-fly-manager/realtimeflight/SituationPlotting/image/形状结合 2.svg"
            />
          </div>
        </el-tooltip>
        <el-tooltip popper-class="popperClass" class="item" effect="light" content="矩形采集" placement="left">
          <div class="iconClass" style="border-top: 1px solid #344054" @click="gatherRectangle">
            <img
              style="height: 13px; width: 13px"
              src="@/views/map/map-fly-manager/realtimeflight/SituationPlotting/image/矩形.svg"
            />
          </div>
        </el-tooltip>
        <el-tooltip popper-class="popperClass" class="item" effect="light" content="圆采集" placement="left">
          <div class="iconClass" style="border-top: 1px solid #344054" @click="circleGatherFun">
            <img
              style="height: 13px; width: 13px"
              src="@/views/map/map-fly-manager/realtimeflight/SituationPlotting/image/椭圆形.svg"
            />
          </div>
        </el-tooltip>
        <el-tooltip popper-class="popperClass" class="item" effect="light" content="更多" placement="left">
          <div class="iconClass" style="border-top: 1px solid #344054; color: white" @click="moreGatherFun">...</div>
        </el-tooltip>
      </div>
    </div>

    <div v-if="editFlag">
      <div
        style="
          width: 266px;
          height: 38px;
          background-color: #11253e;
          font-family: SourceHanSansSC-Bold;
          font-size: 14px;
          color: #ffffff;
          text-align: left;
          line-height: 22px;
          font-weight: 700;
          padding-top: 8px;
          padding-left: 8px;
        "
      >
        {{ editTitle }}
      </div>

      <div style="width: 266px; background-color: #001129">
        <el-form :model="form" label-width="auto" style="max-width: 600px; padding: 10px">
          <el-form-item label="名称">
            <el-input v-model="form.text" />
          </el-form-item>

          <el-form-item label="图标" v-if="form.imageType">
            <div class="iconList" style="padding-left: 15px; padding-top: 20px">
              <div v-for="item in iconList" style="margin: 0; padding: 0" :key="item.key">
                <img :key="item.key" @click="setIcon(item)" :src="item.value" :style="setIconClass(item)" /><br />
                <!-- -->
                <div
                  align="center"
                  style="
                    font-size: 12px;
                    color: white;
                    width: 50px;
                    margin-bottom: 5px;
                    margin-top: -8px;
                    line-height: 15px;
                  "
                >
                  <span>{{ item.name }}</span>
                </div>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="颜色" v-if="form.color">
            <el-color-picker v-model="form.color" show-alpha :predefine="predefineColors" color-format="hex" />
          </el-form-item>
          <el-form-item label="面积" v-if="form.area">
            <el-input v-model="form.area" :disabled="true">
              <template #append>m²</template>
            </el-input>
          </el-form-item>
          <el-form-item label="周长" v-if="form.length">
            <el-input v-model="form.length" :disabled="true">
              <template #append>m</template>
            </el-input>
          </el-form-item>
        </el-form>

        <div align="center" style="width: 266px; height: 48px; background-color: #11253e">
          <el-button type="primary" style="margin-top: 8px" @click="save">保存</el-button>
          <el-button style="margin-top: 8px; background-color: #475467; color: #ffffff; border: none" @click="cancle"
            >取消</el-button
          >
        </div>
      </div>
    </div>
    <!--菜单栏-->
    <Teleport to="#mainMapContainer">
      <div id="plotMenuID" style="background-color: #11253e; display: none; z-index: 999; height: 90px; width: 80px">
        <div class="hover-div" @click="editCurrentEntity('click')">编辑</div>
        <div class="hover-div" @click="confirmFun">删除</div>
        <div class="hover-div" @click="closeplotMenuHtmlOverlay">关闭</div>
      </div>
    </Teleport>
  </div>
</template>
<script setup>
import { plotSave, plotDelete, plotEdit } from '@/api/plot/index.js';
import { useUserStoreHook } from '@/store/modules/user';
import { onMounted, ref, reactive, computed, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

import _ from 'lodash';
import commonImage from './image/common.png';
import fireEngineImage from './image/fireEngine.png';
import fireStationImage from './image/fireStation.png';

//常用消防图标
import dd from './image/fire/dd.png';
import zfzzxfd from './image/fire/zfzzxfd.png';
import qyzzxfd from './image/fire/qyzzxfd.png';

import xczhb from './image/fire/xczhb.png';
import txy from './image/fire/txy.png';
import jjd from './image/fire/jjd.png';

import sgxfc from './image/fire/sgxfc.png';
import pmxfc from './image/fire/pmxfc.png';
import gfxfc from './image/fire/gfxfc.png';

import ytxfc from './image/fire/ytxfc.png';
import dgptxfc from './image/fire/dgptxfc.png';
import qxjyxfc from './image/fire/qxjyxfc.png';

import txzhxfc from './image/fire/txzhxfc.png';
import jhc from './image/fire/jhc.png';
import xfwrj from './image/fire/xfwrj.png';

import zhd from './image/fire/zhd.png';
import bkry from './image/fire/bkry.png';
import snxfx from './image/fire/snxfx.png';

const editTitle = ref('');

const iconList = [
  { key: 'common', name: '通用', value: commonImage, iconSize: [40, 52], pixelOffset: [0, -26] }, //默认
  { key: 'fireEngine', name: '消防车', value: fireEngineImage, iconSize: [40, 52], pixelOffset: [0, -26] },
  { key: 'fireStation', name: '消防站', value: fireStationImage, iconSize: [40, 52], pixelOffset: [0, -26] },
  { key: 'dd', name: '大队', value: dd, iconSize: [48, 48], pixelOffset: [0, 0] },
  { key: 'zfzzxfd', name: '政府专职消防队', value: zfzzxfd, iconSize: [48, 48], pixelOffset: [0, 0] },
  { key: 'qyzzxfd', name: '企业专职消防队', value: qyzzxfd, iconSize: [48, 48], pixelOffset: [0, 0] },
  { key: 'xczhb', name: '现场指挥部', value: xczhb, iconSize: [48, 48], pixelOffset: [0, 0] },
  { key: 'txy', name: '通信员', value: txy, iconSize: [48, 48], pixelOffset: [0, 0] },
  { key: 'jjd', name: '集结地', value: jjd, iconSize: [48, 48], pixelOffset: [0, 0] },
  { key: 'sgxfc', name: '水罐消防车', value: sgxfc, iconSize: [48, 48], pixelOffset: [0, 0] },
  { key: 'pmxfc', name: '泡沫消防车', value: pmxfc, iconSize: [48, 48], pixelOffset: [0, 0] },
  { key: 'gfxfc', name: '干粉消防车', value: gfxfc, iconSize: [48, 48], pixelOffset: [0, 0] },

  { key: 'ytxfc', name: '云梯消防车', value: ytxfc, iconSize: [48, 48], pixelOffset: [0, 0] },
  { key: 'dgptxfc', name: '登高平台消防车', value: dgptxfc, iconSize: [48, 48], pixelOffset: [0, 0] },
  { key: 'qxjyxfc', name: '抢险救援消防车', value: qxjyxfc, iconSize: [48, 48], pixelOffset: [0, 0] },

  { key: 'txzhxfc', name: '通信指挥消防车', value: txzhxfc, iconSize: [48, 48], pixelOffset: [0, 0] },
  { key: 'jhc', name: '救护车', value: jhc, iconSize: [48, 48], pixelOffset: [0, 0] },
  { key: 'xfwrj', name: '消防无人机', value: xfwrj, iconSize: [48, 48], pixelOffset: [0, 0] },

  { key: 'zhd', name: '着火点', value: zhd, iconSize: [48, 48], pixelOffset: [0, 0] },
  { key: 'bkry', name: '被困人员', value: bkry, iconSize: [48, 48], pixelOffset: [0, 0] },
  { key: 'snxfx', name: '室内消火栓', value: snxfx, iconSize: [48, 48], pixelOffset: [0, 0] }
];

const userChooseIcon = ref(iconList[0]);
const setIcon = icon => {
  userChooseIcon.value = icon;
};

const setIconClass = computed(() => {
  return function (item) {
    let styleTemp = {};
    if (item.key == userChooseIcon.value.key) {
      styleTemp.border = '1px solid #2E90FA';
    }
    if (item.key == 'common' || item.key == 'fireEngine' || item.key == 'fireStation') {
      styleTemp.height = '52px';
    } else {
      styleTemp.height = '40px';
    }
    styleTemp.width = '40px';
    styleTemp.marginLeft = '6px';
    styleTemp.marginRight = '12px';
    styleTemp.cursor = 'pointer';
    return styleTemp;
  };
});

const form = ref({
  imageType: '',
  text: '',
  color: ''
});

const percentToHex = percent => {
  // 确保百分比在0-100之间
  percent = Math.max(Math.min(percent, 100), 0);
  // 将百分比转换为0-255的整数
  var intValue = Math.round((percent / 100) * 255);
  // 转换为无符号的十六进制字符串
  var hexValue = intValue.toString(16);
  // 确保十六进制值是两位数
  return hexValue.padStart(2, '0');
};

const predefineColors = ref([
  '#ff4500',
  '#ff450099',
  '#ff8c0066',
  '#ffd70066',
  '#90ee9066',
  '#00ced166',
  '#1e90ff66',
  '#c7158577'
]);

const props = defineProps({
  ffCesium: Object,
  plotDataArray: Array,
  alarmId: String,
  useType: String
});

//useType  view  edit
const useType = ref(props.useType);
console.log('类型type12', useType);

let ffCesium = props.ffCesium;

const userStore = useUserStoreHook();
const { userData } = userStore;
const { workspace_id } = userData;
const cancle = () => {
  //console.log('cancle--currentEntity', currentEntity);
  //停止编辑
  stopEdit();
  ffCesium.removeFFEntity(currentEntity);
  console.log('cancle--oldEntityParam', oldEntityParam);
  //通过右键菜单点击进来的才回显
  if (editType == 'click') {
    let entity = null;
    if (currentEntity.FFType == 'FFPointEntity') {
      entity = ffCesium.addPointEntity(oldEntityParam.FFCoordinates, oldEntityParam.FFOption);
    } else if (currentEntity.FFType == 'FFBillboardEntity') {
      let iconTemp = getIcon(oldEntityParam.ForItem.plotObj.imageType);
      oldEntityParam.FFOption.image = iconTemp.value;
      entity = ffCesium.addBillboardEntity(oldEntityParam.FFCoordinates, oldEntityParam.FFOption);
    } else if (currentEntity.FFType == 'FFPolylineEntity') {
      entity = ffCesium.addPolylineEntity(oldEntityParam.FFCoordinates, oldEntityParam.FFOption);
    } else if (currentEntity.FFType == 'FFPolygonEntity') {
      entity = ffCesium.addPolygonEntity(oldEntityParam.FFCoordinates, oldEntityParam.FFOption);
    } else if (currentEntity.FFType == 'FFRectangleEntity') {
      entity = ffCesium.addRectangleEntity(oldEntityParam.FFCoordinates, oldEntityParam.FFOption);
    } else if (currentEntity.FFType == 'FFCircleEntity') {
      entity = ffCesium.addCircleEntity(oldEntityParam.FFCenterPoint, oldEntityParam.FFRadius, oldEntityParam.FFOption);
    }
    entity.ForItem = oldEntityParam.ForItem;
    entity.isPlot = true;
  }

  addMenuHandler();
  editFlag.value = false;
  isMoreShow.value = false;
};

const getIcon = imageType => {
  console.log('getIcon', imageType);
  let iconResult = null;
  iconList.forEach(icon => {
    if (icon.key == imageType) {
      console.log('getIcon--iconTemp', icon);
      iconResult = icon;
    }
  });
  return iconResult;
};

const stopEdit = () => {
  if (!currentEntity) {
    return;
  }
  if (currentEntity.FFType == 'FFPointEntity') {
    ffCesium.closePointEdit(currentEntity);
  } else if (currentEntity.FFType == 'FFBillboardEntity') {
    ffCesium.closeBillboardEdit(currentEntity);
  } else if (currentEntity.FFType == 'FFPolylineEntity') {
    ffCesium.closePolylineEdit(currentEntity);
  } else if (currentEntity.FFType == 'FFPolygonEntity') {
    ffCesium.closePolygonEdit(currentEntity);
  } else if (currentEntity.FFType == 'FFRectangleEntity') {
    ffCesium.closeRectangleEdit(currentEntity);
  } else if (currentEntity.FFType == 'FFCircleEntity') {
    ffCesium.closeCircleEdit(currentEntity);
  } else if (currentEntity.FFType == 'FFStraightArrowEntity') {
    ffCesium.closeStraightArrowEdit(currentEntity);
  } else if (currentEntity.FFType == 'FFTailedAttackArrowEntity') {
    ffCesium.closeTailedAttackArrowEdit(currentEntity);
  } else if (currentEntity.FFType == 'FFDoubleArrowEntity') {
    ffCesium.closeDoubleArrowEdit(currentEntity);
  } else if (currentEntity.FFType == 'FFRendezvousEntity') {
    ffCesium.closeRendezvousEdit(currentEntity);
  }
};
const save = async () => {
  //删除标签
  // console.log('save--textHtmlOverlayArray', textHtmlOverlayArray);
  // console.log('save--currentEntity.id', currentEntity.id);
  for (let i = 0; i < textHtmlOverlayArray.length; i++) {
    if (textHtmlOverlayArray[i].ForItem.plotObj.id == currentEntity.id) {
      ffCesium.removeHtml(textHtmlOverlayArray[i]);
      textHtmlOverlayArray.splice(i, 1);
    }
  }

  //停止编辑
  stopEdit();
  //更新实体数据
  updateCurrentEntityData();

  let paramObj = {};
  paramObj.id = currentEntity.id;
  paramObj.userID = workspace_id;
  paramObj.createTime = new Date().getTime();
  paramObj.text = currentEntity.ForItem.plotObj.text;
  paramObj.FFType = currentEntity.FFType;
  if (paramObj.FFType == 'FFPointEntity') {
    paramObj.color = currentEntity.ForItem.plotObj.color;
    paramObj.alpha = currentEntity.ForItem.plotObj.alpha;
    paramObj.FFCoordinates = currentEntity.FFCoordinates;
  } else if (paramObj.FFType == 'FFBillboardEntity') {
    paramObj.imageType = currentEntity.ForItem.plotObj.imageType;
    paramObj.area = currentEntity.ForItem.plotObj.area;
    paramObj.length = currentEntity.ForItem.plotObj.length;
    paramObj.FFCoordinates = currentEntity.FFCoordinates;
  } else if (paramObj.FFType == 'FFPolylineEntity') {
    paramObj.color = currentEntity.ForItem.plotObj.color;
    paramObj.alpha = currentEntity.ForItem.plotObj.alpha;
    paramObj.length = currentEntity.ForItem.plotObj.length;
    paramObj.FFCoordinates = currentEntity.FFCoordinates;
  } else if (paramObj.FFType == 'FFPolygonEntity') {
    paramObj.color = currentEntity.ForItem.plotObj.color;
    paramObj.alpha = currentEntity.ForItem.plotObj.alpha;
    paramObj.area = currentEntity.ForItem.plotObj.area;
    paramObj.length = currentEntity.ForItem.plotObj.length;
    paramObj.FFCoordinates = currentEntity.FFCoordinates;
  } else if (paramObj.FFType == 'FFRectangleEntity') {
    paramObj.color = currentEntity.ForItem.plotObj.color;
    paramObj.alpha = currentEntity.ForItem.plotObj.alpha;
    paramObj.area = currentEntity.ForItem.plotObj.area;
    paramObj.length = currentEntity.ForItem.plotObj.length;
    paramObj.FFCoordinates = currentEntity.FFCoordinates;
  } else if (paramObj.FFType == 'FFCircleEntity') {
    paramObj.color = currentEntity.ForItem.plotObj.color;
    paramObj.alpha = currentEntity.ForItem.plotObj.alpha;
    paramObj.area = currentEntity.ForItem.plotObj.area;
    paramObj.length = currentEntity.ForItem.plotObj.length;
    paramObj.FFCenterPoint = currentEntity.FFCenterPoint;
    paramObj.FFRadius = currentEntity.FFRadius;
  } else if (
    paramObj.FFType == 'FFStraightArrowEntity' ||
    paramObj.FFType == 'FFTailedAttackArrowEntity' ||
    paramObj.FFType == 'FFDoubleArrowEntity' ||
    paramObj.FFType == 'FFRendezvousEntity'
  ) {
    paramObj.color = currentEntity.ForItem.plotObj.color;
    paramObj.alpha = currentEntity.ForItem.plotObj.alpha;
    paramObj.area = currentEntity.ForItem.plotObj.area;
    paramObj.length = currentEntity.ForItem.plotObj.length;
    paramObj.FFCoordinates = currentEntity.FFCoordinates;
    paramObj.FFPlotKeyPoints = currentEntity.FFPlotKeyPoints;
  }
  console.log('currentEntity12323', currentEntity);
  if (!currentEntity.ForItem.plot_id) {
    let data = {
      alarm_id: props.alarmId,
      plot_obj: JSON.stringify(paramObj)
    };
    console.log('保存数据data1', data);
    let result = await plotSave(data);
    currentEntity.ForItem.plot_id = result;
  } else {
    let data = {
      plot_id: currentEntity.ForItem.plot_id,
      plot_obj: JSON.stringify(paramObj)
    };
    console.log('保存数据data2', data);
    let result = await plotEdit(data);
  }
  editFlag.value = false;
  isMoreShow.value = false;
  addMenuHandler();
  ElMessage({
    message: '保存数据成功',
    type: 'success'
  });
  addLabel(currentEntity.ForItem);
};

//菜单
let plotMenuHtmlOverlay = null;
let textHtmlOverlayArray = [];
let currentEntity = null;
let editFlag = ref(false);
let menuHandler = null;
//关闭弹窗
const closeplotMenuHtmlOverlay = () => {
  console.log('closeplotMenuHtmlOverlay');
  ffCesium.closeHtmlForVue(plotMenuHtmlOverlay);
};
//修改标绘实体
let oldEntityParam = {};
let editType = null;
const editCurrentEntity = typeParam => {
  editType = typeParam;

  console.log('editCurrentEntity--currentEntity', currentEntity);
  oldEntityParam = {};
  if (currentEntity.FFType == 'FFCircleEntity') {
    oldEntityParam.FFCenterPoint = _.cloneDeep(currentEntity.FFCenterPoint);
    oldEntityParam.FFRadius = _.cloneDeep(currentEntity.FFRadius);
    oldEntityParam.FFOption = _.cloneDeep(currentEntity.FFOption);
  } else {
    oldEntityParam.FFCoordinates = _.cloneDeep(currentEntity.FFCoordinates);
    oldEntityParam.FFOption = _.cloneDeep(currentEntity.FFOption);
    if (currentEntity.FFPlotKeyPoints) {
      oldEntityParam.FFPlotKeyPoints = _.cloneDeep(currentEntity.FFPlotKeyPoints);
    }
  }
  oldEntityParam.ForItem = _.cloneDeep(currentEntity.ForItem);

  console.log('editCurrentEntity--oldEntityParam', oldEntityParam);

  iconList.forEach(icon => {
    if (icon.key == currentEntity.ForItem.plotObj.imageType) {
      userChooseIcon.value = icon;
    }
  });

  //设置FormValue
  setFormValueFromCurrentEntity();
  //console.log('form123', form);
  closeMenuHandler();
  editFlag.value = true;
  isMoreShow.value = false;
  closeplotMenuHtmlOverlay();
  if (currentEntity.FFType == 'FFPointEntity') {
    editTitle.value = '点设置';
    ffCesium.pointEdit(currentEntity, editCurrentEntityCallback);
  } else if (currentEntity.FFType == 'FFBillboardEntity') {
    editTitle.value = '图标点设置';
    ffCesium.billboardEdit(currentEntity, editCurrentEntityCallback);
  } else if (currentEntity.FFType == 'FFPolylineEntity') {
    editTitle.value = '线设置';
    ffCesium.polylineEdit(currentEntity, editCurrentEntityCallback);
  } else if (currentEntity.FFType == 'FFPolygonEntity') {
    editTitle.value = '面设置';
    ffCesium.polygonEdit(currentEntity, editCurrentEntityCallback);
  } else if (currentEntity.FFType == 'FFRectangleEntity') {
    editTitle.value = '矩形设置';
    ffCesium.rectangleEdit(currentEntity, editCurrentEntityCallback);
  } else if (currentEntity.FFType == 'FFCircleEntity') {
    editTitle.value = '圆设置';
    ffCesium.circleEdit(currentEntity, editCurrentEntityCallback);
  } else if (currentEntity.FFType == 'FFStraightArrowEntity') {
    editTitle.value = '直线箭头设置';
    ffCesium.straightArrowEdit(currentEntity, editCurrentEntityCallback);
  } else if (currentEntity.FFType == 'FFTailedAttackArrowEntity') {
    editTitle.value = '自定义箭头设置';
    ffCesium.tailedAttackArrowEdit(currentEntity, editCurrentEntityCallback);
  } else if (currentEntity.FFType == 'FFDoubleArrowEntity') {
    editTitle.value = '嵌击箭头设置';
    ffCesium.doubleArrowEdit(currentEntity, editCurrentEntityCallback);
  } else if (currentEntity.FFType == 'FFRendezvousEntity') {
    editTitle.value = '集结地设置';
    ffCesium.rendezvousEdit(currentEntity, editCurrentEntityCallback);
  }
};
const editCurrentEntityCallback = entity => {
  save();
};
//更新实体数据
const updateCurrentEntityData = () => {
  currentEntity.ForItem.plotObj.text = form.value.text;
  if (currentEntity.FFType == 'FFPointEntity') {
    currentEntity.ForItem.plotObj.color = form.value.color.substring(0, 7);
    currentEntity.ForItem.plotObj.alpha = parseInt(form.value.color.substring(7, 9), 16) / 255;
    currentEntity.ForItem.plotObj.FFCoordinates = currentEntity.FFCoordinates;
    //更新实体
    currentEntity.point.color = ffCesium.Cesium.Color.fromCssColorString(currentEntity.ForItem.plotObj.color).withAlpha(
      currentEntity.ForItem.plotObj.alpha
    );
  } else if (currentEntity.FFType == 'FFBillboardEntity') {
    console.log('updateCurrentEntityData--userChooseIcon', userChooseIcon);
    currentEntity.ForItem.plotObj.imageType = userChooseIcon.value.key;

    currentEntity.ForItem.plotObj.FFCoordinates = currentEntity.FFCoordinates;

    currentEntity.billboard.image = userChooseIcon.value.value;
    currentEntity.billboard.width = userChooseIcon.value.iconSize[0];
    currentEntity.billboard.height = userChooseIcon.value.iconSize[1];

    currentEntity.billboard.pixelOffset = new ffCesium.Cesium.Cartesian2(
      userChooseIcon.value.pixelOffset[0],
      userChooseIcon.value.pixelOffset[1]
    );
  } else if (currentEntity.FFType == 'FFPolylineEntity') {
    currentEntity.ForItem.plotObj.color = form.value.color.substring(0, 7);
    currentEntity.ForItem.plotObj.alpha = parseInt(form.value.color.substring(7, 9), 16) / 255;
    currentEntity.ForItem.plotObj.FFCoordinates = currentEntity.FFCoordinates;
    let optionTemp = {};
    optionTemp.type = 'polyline';
    optionTemp.LngLatArr = currentEntity.ForItem.plotObj.FFCoordinates;
    currentEntity.ForItem.plotObj.length = ffCesium.countlength(optionTemp);
    //更新实体
    currentEntity.polyline.material = ffCesium.Cesium.Color.fromCssColorString(
      currentEntity.ForItem.plotObj.color
    ).withAlpha(currentEntity.ForItem.plotObj.alpha);
  } else if (currentEntity.FFType == 'FFPolygonEntity') {
    currentEntity.ForItem.plotObj.color = form.value.color.substring(0, 7);
    currentEntity.ForItem.plotObj.alpha = parseInt(form.value.color.substring(7, 9), 16) / 255;
    currentEntity.ForItem.plotObj.FFCoordinates = currentEntity.FFCoordinates;
    let optionTemp = {};
    optionTemp.type = 'polygon';
    optionTemp.LngLatArr = currentEntity.ForItem.plotObj.FFCoordinates;
    currentEntity.ForItem.plotObj.area = ffCesium.countArea(optionTemp);
    currentEntity.ForItem.plotObj.length = ffCesium.countlength(optionTemp);
    //更新实体
    currentEntity.polygon.material = ffCesium.Cesium.Color.fromCssColorString(
      currentEntity.ForItem.plotObj.color
    ).withAlpha(currentEntity.ForItem.plotObj.alpha);
  } else if (currentEntity.FFType == 'FFRectangleEntity') {
    currentEntity.ForItem.plotObj.color = form.value.color.substring(0, 7);
    currentEntity.ForItem.plotObj.alpha = parseInt(form.value.color.substring(7, 9), 16) / 255;
    currentEntity.ForItem.plotObj.FFCoordinates = currentEntity.FFCoordinates;
    //更新面积与长度值
    let optionTemp = {};
    optionTemp.type = 'rectangle';
    optionTemp.LngLat = currentEntity.ForItem.plotObj.FFCoordinates;
    currentEntity.ForItem.plotObj.area = ffCesium.countArea(optionTemp);
    currentEntity.ForItem.plotObj.length = ffCesium.countlength(optionTemp);
    //更新实体
    currentEntity.rectangle.material = ffCesium.Cesium.Color.fromCssColorString(
      currentEntity.ForItem.plotObj.color
    ).withAlpha(currentEntity.ForItem.plotObj.alpha);
  } else if (currentEntity.FFType == 'FFCircleEntity') {
    currentEntity.ForItem.plotObj.color = form.value.color.substring(0, 7);
    currentEntity.ForItem.plotObj.alpha = parseInt(form.value.color.substring(7, 9), 16) / 255;
    currentEntity.ForItem.plotObj.FFCenterPoint = currentEntity.FFCenterPoint;
    currentEntity.ForItem.plotObj.FFRadius = currentEntity.FFRadius;
    let optionTemp = {};
    optionTemp.type = 'circle';
    optionTemp.centerPoint = currentEntity.FFCenterPoint;
    optionTemp.radius = currentEntity.FFRadius;
    currentEntity.ForItem.plotObj.area = ffCesium.countArea(optionTemp);
    currentEntity.ForItem.plotObj.length = ffCesium.countlength(optionTemp);
    //更新实体
    currentEntity.ellipse.material = ffCesium.Cesium.Color.fromCssColorString(
      currentEntity.ForItem.plotObj.color
    ).withAlpha(currentEntity.ForItem.plotObj.alpha);
  } else if (
    currentEntity.FFType == 'FFStraightArrowEntity' ||
    currentEntity.FFType == 'FFTailedAttackArrowEntity' ||
    currentEntity.FFType == 'FFDoubleArrowEntity' ||
    currentEntity.FFType == 'FFRendezvousEntity'
  ) {
    currentEntity.ForItem.plotObj.color = form.value.color.substring(0, 7);
    currentEntity.ForItem.plotObj.alpha = parseInt(form.value.color.substring(7, 9), 16) / 255;
    currentEntity.ForItem.plotObj.FFCoordinates = currentEntity.FFCoordinates;
    let optionTemp = {};
    optionTemp.type = 'polygon';
    optionTemp.LngLatArr = currentEntity.ForItem.plotObj.FFCoordinates;
    currentEntity.ForItem.plotObj.area = ffCesium.countArea(optionTemp);
    currentEntity.ForItem.plotObj.length = ffCesium.countlength(optionTemp);
    //更新实体
    currentEntity.polygon.material = ffCesium.Cesium.Color.fromCssColorString(
      currentEntity.ForItem.plotObj.color
    ).withAlpha(currentEntity.ForItem.plotObj.alpha);
  }
};

const setFormValueFromCurrentEntity = () => {
  form.value.text = currentEntity.ForItem.plotObj.text;

  form.value.imageType = currentEntity.ForItem.plotObj.imageType ? currentEntity.ForItem.plotObj.imageType : '';

  let colorTemp = currentEntity.ForItem.plotObj.color + percentToHex(currentEntity.ForItem.plotObj.alpha * 100);

  form.value.color = currentEntity.ForItem.plotObj.color ? colorTemp : '';

  form.value.area = currentEntity.ForItem.plotObj.area ? currentEntity.ForItem.plotObj.area : '';
  form.value.length = currentEntity.ForItem.plotObj.length ? currentEntity.ForItem.plotObj.length : '';
};

const confirmFun = () => {
  ElMessageBox.confirm('确认后将删除此标绘数据，且无法进行恢复', '确认删除标绘数据？', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    closeOnClickModal: false,
    type: 'warning'
  })
    .then(() => {
      deleteCurrentEntity();
    })
    .catch(() => {
      closeplotMenuHtmlOverlay();
    });
};

//删除标绘实体并删除其对应的标签
const deleteCurrentEntity = () => {
  console.log('props.plotDataArray', props.plotDataArray);
  console.log('deletePlotMenuHtmlOverlay--currentEntity', currentEntity);
  for (let i = 0; i < props.plotDataArray.length; i++) {
    if (props.plotDataArray[i].plotObj.id == currentEntity.id) {
      props.plotDataArray.splice(i, 1);
    }
  }
  //移除标签
  for (let i = 0; i < textHtmlOverlayArray.length; i++) {
    if (textHtmlOverlayArray[i].ForItem.plotObj.id == currentEntity.id) {
      ffCesium.removeHtml(textHtmlOverlayArray[i]);
      textHtmlOverlayArray.splice(i, 1);
    }
  }

  plotDelete(currentEntity.ForItem.plot_id);

  //移除实体
  ffCesium.removeFFEntity(currentEntity);
  closeplotMenuHtmlOverlay();
  currentEntity = null;
};

onMounted(() => {
  //plotDelete('fd51f09b-f702-45ba-b7d2-d5d6fa5828d9');

  ffCesium.viewer.scene.globe.depthTestAgainstTerrain = false; //（开启）

  plotMenuHtmlOverlay = document.getElementById('plotMenuID');
  console.log('props.plotDataArray', props.plotDataArray);
  //叠加默认的标绘数据g
  props.plotDataArray.forEach(item => {
    let plotObj = item.plotObj;
    console.log('叠加实体：plotObj', plotObj);

    if (plotObj.FFType == 'FFPointEntity') {
      let option = {
        id: plotObj.id,
        pixelSize: 10,
        color: plotObj.color,
        alpha: plotObj.alpha,
        heightReference: ffCesium.Cesium.HeightReference.CLAMP_TO_GROUND
      };
      let pointEntity = ffCesium.addPointEntity(plotObj.FFCoordinates, option);
      pointEntity.ForItem = item;
      pointEntity.isPlot = true;
    } else if (plotObj.FFType == 'FFBillboardEntity') {
      let option = {
        id: plotObj.id
      };
      iconList.forEach(element => {
        if (plotObj.imageType == element.key) {
          option.image = element.value;
          option.pixelOffset = element.pixelOffset;
          option.height = element.iconSize[1];
          option.width = element.iconSize[0];
        }
      });

      option.heightReference = ffCesium.Cesium.HeightReference.CLAMP_TO_GROUND; //贴地
      let billboardEntity = ffCesium.addBillboardEntity(plotObj.FFCoordinates, option);
      billboardEntity.ForItem = item;
      billboardEntity.isPlot = true;
    } else if (plotObj.FFType == 'FFPolylineEntity') {
      let option = {
        id: plotObj.id,
        color: plotObj.color,
        alpha: plotObj.alpha,
        width: 5,
        clampToGround: true
      };
      let polygonObj = ffCesium.addPolylineEntity(plotObj.FFCoordinates, option);
      polygonObj.ForItem = item;
      polygonObj.isPlot = true;
    } else if (plotObj.FFType == 'FFPolygonEntity') {
      let option = {
        id: plotObj.id,
        color: plotObj.color,
        alpha: plotObj.alpha
      };
      let polygonObj = ffCesium.addPolygonEntity(
        ffCesium.getLngLatArrFromLngLatHeightArr(plotObj.FFCoordinates),
        option
      );
      polygonObj.ForItem = item;
      polygonObj.isPlot = true;
    } else if (plotObj.FFType == 'FFRectangleEntity') {
      let option = {
        id: plotObj.id,
        color: plotObj.color,
        alpha: plotObj.alpha
      };
      let rectangleObj = ffCesium.addRectangleEntity(plotObj.FFCoordinates, option);
      rectangleObj.ForItem = item;
      rectangleObj.isPlot = true;
    } else if (plotObj.FFType == 'FFCircleEntity') {
      let option = {
        id: plotObj.id,
        color: plotObj.color,
        alpha: plotObj.alpha
      };
      let circleEntity = ffCesium.addCircleEntity(plotObj.FFCenterPoint, plotObj.FFRadius, option);
      circleEntity.ForItem = item;
      circleEntity.isPlot = true;
    } else if (plotObj.FFType == 'FFStraightArrowEntity') {
      let option = {
        id: plotObj.id,
        color: plotObj.color,
        alpha: plotObj.alpha
      };
      let plotEntity = ffCesium.addStraightArrowEntity(plotObj.FFPlotKeyPoints, option);
      plotEntity.ForItem = item;
      plotEntity.isPlot = true;
    } else if (plotObj.FFType == 'FFTailedAttackArrowEntity') {
      let option = {
        id: plotObj.id,
        color: plotObj.color,
        alpha: plotObj.alpha
      };
      let plotEntity = ffCesium.addTailedAttackArrow(plotObj.FFPlotKeyPoints, option);
      plotEntity.ForItem = item;
      plotEntity.isPlot = true;
    } else if (plotObj.FFType == 'FFDoubleArrowEntity') {
      let option = {
        id: plotObj.id,
        color: plotObj.color,
        alpha: plotObj.alpha
      };
      let plotEntity = ffCesium.addDoubleArrowEntity(plotObj.FFPlotKeyPoints, option);
      plotEntity.ForItem = item;
      plotEntity.isPlot = true;
    } else if (plotObj.FFType == 'FFRendezvousEntity') {
      let option = {
        id: plotObj.id,
        color: plotObj.color,
        alpha: plotObj.alpha
      };
      let plotEntity = ffCesium.addRendezvousEntity(plotObj.FFPlotKeyPoints, option);
      plotEntity.ForItem = item;
      plotEntity.isPlot = true;
    }

    addLabel(item);
  });
  if (useType.value == 'edit') {
    addMenuHandler();
  }
});

const addLabel = async item => {
  if (!item.plotObj.text) {
    return;
  }
  console.log('addLabel--item', item);
  let option = {};
  option.offset = { top: 0, left: 0 };
  let centerPoint = null;
  //叠加文本标签
  if (item.plotObj.FFType == 'FFPointEntity') {
    centerPoint = item.plotObj.FFCoordinates;
    option.offset = { top: 10, left: 0 };
  } else if (item.plotObj.FFType == 'FFBillboardEntity') {
    centerPoint = item.plotObj.FFCoordinates;

    iconList.forEach(element => {
      if (item.plotObj.imageType == element.key) {
        console.log('temp1111', item.plotObj);
        if (
          item.plotObj.imageType == 'common' ||
          item.plotObj.imageType == 'fireEngine' ||
          item.plotObj.imageType == 'fireStation'
        ) {
          option.offset = { top: element.iconSize[1], left: 0 };
        } else {
          option.offset = { top: element.iconSize[1] / 2, left: 0 };
        }
      }
    });
    //option.offset = { top: 0, left: 0 };
  } else if (item.plotObj.FFType == 'FFPolylineEntity') {
    centerPoint = ffCesium.getCenterPointByPolyline(item.plotObj.FFCoordinates);
    option.offset = { top: 5, left: 0 };
  } else if (item.plotObj.FFType == 'FFPolygonEntity') {
    centerPoint = ffCesium.getCenterPointFromLngLatArr(item.plotObj.FFCoordinates);
    option.offset = { top: 5, left: 0 };
  } else if (item.plotObj.FFType == 'FFRectangleEntity') {
    let lngTemp = (item.plotObj.FFCoordinates.east + item.plotObj.FFCoordinates.west) / 2;
    let latTemp = (item.plotObj.FFCoordinates.north + item.plotObj.FFCoordinates.south) / 2;
    centerPoint = [lngTemp, latTemp];
    option.offset = { top: 10, left: 0 };
  } else if (item.plotObj.FFType == 'FFCircleEntity') {
    centerPoint = item.plotObj.FFCenterPoint;
    option.offset = { top: 10, left: 0 };
  } else if (
    item.plotObj.FFType == 'FFStraightArrowEntity' ||
    item.plotObj.FFType == 'FFTailedAttackArrowEntity' ||
    item.plotObj.FFType == 'FFDoubleArrowEntity' ||
    item.plotObj.FFType == 'FFRendezvousEntity'
  ) {
    centerPoint = ffCesium.getCenterPointFromLngLatArr(item.plotObj.FFCoordinates);
    option.offset = { top: 5, left: 0 };
  }

  //11253E 75% rgba(17,37,62,0.75);
  let html = '';
  html +=
    "<div style='cursor:pointer;color: white;background-color:rgba(17,37,62,0.75);padding:5px;border:1px solid #2FA5FF;'>" +
    item.plotObj.text +
    '</div>';
  let height = await ffCesium.getHeightAtPoint(centerPoint);
  centerPoint[2] = height;
  console.log('centerPoint1232gg', centerPoint, option);

  option.zIndex = 0;
  let htmlOverlay = ffCesium.addHtml(centerPoint, html, option);
  htmlOverlay.ForItem = item;
  textHtmlOverlayArray.push(htmlOverlay);
};

//关闭菜单事件
const closeMenuHandler = () => {
  if (menuHandler) {
    menuHandler.destroy();
    menuHandler = null;
  }
};

//判断实体数组里面是否含有图标实体
const isInBillboardEntity = EntityArr => {
  console.log('isInBillboardEntity--EntityArr', EntityArr);
  let isInBillboardEntity = false;
  for (let i = 0; i < EntityArr.length; i++) {
    if (EntityArr[i].id.FFType == 'FFBillboardEntity') {
      isInBillboardEntity = true;
      break;
    }
  }
  console.log('isInBillboardEntity--isInBillboardEntity', isInBillboardEntity);
  return isInBillboardEntity;
};
//打开菜单事件
const addMenuHandler = () => {
  menuHandler = new ffCesium.Cesium.ScreenSpaceEventHandler(ffCesium.viewer.scene.canvas);
  //鼠标点击事件
  menuHandler.setInputAction(event => {
    let pickedObjectArr = ffCesium.viewer.scene.drillPick(event.position);
    console.log('addMenuHandler--pickedObjectArr', pickedObjectArr);
    let pickedObject;
    if (pickedObjectArr.length > 1 && !isInBillboardEntity(pickedObjectArr)) {
      pickedObject = pickedObjectArr[pickedObjectArr.length - 1];
    } else {
      pickedObject = ffCesium.viewer.scene.pick(event.position);
    }

    var ray = ffCesium.viewer.camera.getPickRay(event.position);
    var cartesian = ffCesium.viewer.scene.globe.pick(ray, ffCesium.viewer.scene);

    if (ffCesium.Cesium.defined(pickedObject)) {
      console.log('addMenuHandler--选中的对象是：', pickedObject.id);
      if (pickedObject.id.isPlot) {
        currentEntity = pickedObject.id;
        let offset = { top: -90, left: -40 };
        let lngLatHeight = ffCesium.positionToLngLatHeight(cartesian);
        plotMenuHtmlOverlay.lngLatHeight = lngLatHeight;
        ffCesium.addHtmlForVue(lngLatHeight, plotMenuHtmlOverlay, offset);
      }
    } else {
      ffCesium.closeHtmlForVue(plotMenuHtmlOverlay);
    }
  }, ffCesium.Cesium.ScreenSpaceEventType.LEFT_DOWN);
};

const addToPlotDataArray = entity => {
  let obj = {};
  obj.jqid = 'jqid';
  obj.username = 'username';
  obj.createTime = 'createTime';
  obj.plotObj = {};
  obj.plotObj.id = entity.id;
  obj.plotObj.text = '';
  if (entity.FFType == 'FFPointEntity') {
    obj.plotObj.FFType = entity.FFType;
    obj.plotObj.color = entity.FFOption.color;
    obj.plotObj.alpha = entity.FFOption.alpha;
    obj.plotObj.FFCoordinates = ffCesium.getLngLatFromLngLatHeight(entity.FFCoordinates);
  } else if (entity.FFType == 'FFBillboardEntity') {
    obj.plotObj.FFType = entity.FFType;
    obj.plotObj.imageType = 'common';
    obj.plotObj.FFCoordinates = ffCesium.getLngLatFromLngLatHeight(entity.FFCoordinates);
  } else if (entity.FFType == 'FFPolylineEntity') {
    obj.plotObj.FFType = entity.FFType;
    obj.plotObj.color = entity.FFOption.color;
    obj.plotObj.alpha = entity.FFOption.alpha;
    obj.plotObj.FFCoordinates = ffCesium.getLngLatArrFromLngLatHeightArr(entity.FFCoordinates);
    let optionTemp = {};
    optionTemp.type = 'polyline';
    optionTemp.LngLatArr = obj.plotObj.FFCoordinates;
    obj.plotObj.length = ffCesium.countlength(optionTemp);
  } else if (entity.FFType == 'FFPolygonEntity') {
    obj.plotObj.FFType = entity.FFType;
    obj.plotObj.color = entity.FFOption.color;
    obj.plotObj.alpha = entity.FFOption.alpha;
    obj.plotObj.FFCoordinates = ffCesium.getLngLatArrFromLngLatHeightArr(entity.FFCoordinates);
    let optionTemp = {};
    optionTemp.type = 'polygon';
    optionTemp.LngLatArr = obj.plotObj.FFCoordinates;
    obj.plotObj.area = ffCesium.countArea(optionTemp);
    obj.plotObj.length = ffCesium.countlength(optionTemp);
  } else if (entity.FFType == 'FFRectangleEntity') {
    obj.plotObj.FFType = entity.FFType;
    obj.plotObj.color = entity.FFOption.color;
    obj.plotObj.alpha = entity.FFOption.alpha;
    obj.plotObj.FFCoordinates = entity.FFCoordinates;
    let optionTemp = {};
    optionTemp.type = 'rectangle';
    optionTemp.LngLat = obj.plotObj.FFCoordinates;
    obj.plotObj.area = ffCesium.countArea(optionTemp);
    obj.plotObj.length = ffCesium.countlength(optionTemp);
  } else if (entity.FFType == 'FFCircleEntity') {
    obj.plotObj.FFType = entity.FFType;
    obj.plotObj.color = entity.FFOption.color;
    obj.plotObj.alpha = entity.FFOption.alpha;
    obj.plotObj.FFCenterPoint = ffCesium.getLngLatFromLngLatHeight(entity.FFCenterPoint);
    obj.plotObj.FFRadius = entity.FFRadius;

    let optionTemp = {};
    optionTemp.type = 'circle';
    optionTemp.centerPoint = obj.plotObj.FFCenterPoint;
    optionTemp.radius = obj.plotObj.FFRadius;
    obj.plotObj.area = ffCesium.countArea(optionTemp);
    obj.plotObj.length = ffCesium.countlength(optionTemp);
  } else if (
    entity.FFType == 'FFStraightArrowEntity' ||
    entity.FFType == 'FFTailedAttackArrowEntity' ||
    entity.FFType == 'FFDoubleArrowEntity' ||
    entity.FFType == 'FFRendezvousEntity'
  ) {
    obj.plotObj.FFType = entity.FFType;
    obj.plotObj.color = entity.FFOption.color;
    obj.plotObj.alpha = entity.FFOption.alpha;
    obj.plotObj.FFCoordinates = ffCesium.getLngLatArrFromLngLatHeightArr(entity.FFCoordinates);
    let optionTemp = {};
    optionTemp.type = 'polygon';
    optionTemp.LngLatArr = obj.plotObj.FFCoordinates;
    obj.plotObj.area = ffCesium.countArea(optionTemp);
    obj.plotObj.length = ffCesium.countlength(optionTemp);
  }
  props.plotDataArray.push(obj);
  entity.ForItem = obj;
  console.log('addToPlotDataArray--props.plotDataArray', props.plotDataArray);
};

//采集点图标
const gatherBillboard = () => {
  closeMenuHandler();
  ffCesium.gatherHandlerDestroy();
  ffCesium.billboardGather(gatherBillboardCallback, {
    image: commonImage,
    heightReference: ffCesium.Cesium.HeightReference.CLAMP_TO_GROUND,
    pixelOffset: [0, -26] //数组第一个元素是左右方向，负值向左，第二个元素是上下方向，负值向上，
  });
};
const gatherBillboardCallback = billboard => {
  console.log('坐标采集成功,其对象为：', billboard);
  console.log('坐标采集成功,其坐标为：', billboard.FFCoordinates);
  //billboard.imageType = "fireEngine";
  billboard.isPlot = true;
  addToPlotDataArray(billboard);
  //进入编辑
  currentEntity = billboard;
  editCurrentEntity('auto');
};

const gatherPoint = () => {
  closeMenuHandler();
  ffCesium.gatherHandlerDestroy();
  ffCesium.pointGather(gatherPointCallback, {
    heightReference: ffCesium.Cesium.HeightReference.CLAMP_TO_GROUND,
    color: '#FBFF65',
    alpha: 1,
    pixelSize: 10
  });
};
const gatherPointCallback = gatherPoint => {
  console.log('采集成功,其对象为：', gatherPoint);
  console.log('采集成功,其坐标为：', gatherPoint.FFCoordinates);
  gatherPoint.isPlot = true;
  addToPlotDataArray(gatherPoint);
  //进入编辑
  currentEntity = gatherPoint;
  editCurrentEntity('auto');
};

//采集线
const gatherPolyline = () => {
  closeMenuHandler();
  ffCesium.gatherHandlerDestroy();
  ffCesium.polylineGather(gatherPolylineCallback, {
    color: '#FBFF65',
    alpha: 1,
    width: 5,
    clampToGround: true
  });
};
const gatherPolylineCallback = gatherPolyline => {
  console.log('坐标采集成功,其对象为：', gatherPolyline);
  console.log('坐标采集成功,其坐标为：', gatherPolyline.FFCoordinates);
  gatherPolyline.isPlot = true;
  addToPlotDataArray(gatherPolyline);
  //进入编辑
  currentEntity = gatherPolyline;
  editCurrentEntity('auto');
};
//采集面
const gatherPolygon = () => {
  closeMenuHandler();
  ffCesium.gatherHandlerDestroy();
  ffCesium.polygonGather(gatherPolygonCallback, {
    color: '#FBFF65',
    alpha: 0.5
  });
};
const gatherPolygonCallback = gatherPolygon => {
  console.log('坐标采集成功,其对象为：', gatherPolygon);
  console.log('坐标采集成功,其坐标为：', gatherPolygon.FFCoordinates);
  gatherPolygon.isPlot = true;
  addToPlotDataArray(gatherPolygon);
  //进入编辑
  currentEntity = gatherPolygon;
  editCurrentEntity('auto');

  // closeMenuHandler();
};
//采集矩形
const gatherRectangle = () => {
  closeMenuHandler();
  ffCesium.gatherHandlerDestroy();
  ffCesium.rectangleGather(gatherRectangleCallback, {
    color: '#FBFF65',
    alpha: 0.5
  });
};
const gatherRectangleCallback = gatherRectangle => {
  console.log('坐标采集成功,其对象为：', gatherRectangle);
  console.log('坐标采集成功,其坐标为：', gatherRectangle.FFCoordinates);
  gatherRectangle.isPlot = true;
  addToPlotDataArray(gatherRectangle);
  //进入编辑
  currentEntity = gatherRectangle;
  editCurrentEntity('auto');
};

//圆
const circleGatherFun = () => {
  closeMenuHandler();
  ffCesium.gatherHandlerDestroy();
  ffCesium.circleGather(circleGatherFunCallback, {
    color: '#FBFF65',
    alpha: 0.5
  });
};

const circleGatherFunCallback = gatherCircle => {
  console.log('采集成功,其对象为：', gatherCircle);
  console.log('采集成功,其坐标为：', gatherCircle.FFCenterPoint);
  console.log('采集成功,其半径为：', gatherCircle.FFRadius);
  gatherCircle.isPlot = true;
  addToPlotDataArray(gatherCircle);
  //进入编辑
  currentEntity = gatherCircle;
  editCurrentEntity('auto');
};

//直线箭头采集
const straightArrowGatherFun = () => {
  ffCesium.straightArrowGather(straightArrowGatherFunCallback, {
    color: '#FBFF65',
    alpha: 0.5
  });
};

const straightArrowGatherFunCallback = gatherObj => {
  console.log('采集成功,其对象为：', gatherObj);
  console.log('采集成功,其关键坐标为：', gatherObj.FFPlotKeyPoints);
  console.log('采集成功,其坐标为：', gatherObj.FFCoordinates);
  gatherObj.isPlot = true;
  addToPlotDataArray(gatherObj);
  //进入编辑
  currentEntity = gatherObj;
  editCurrentEntity('auto');
};
//攻击箭头
const tailedAttackArrowGatherFun = () => {
  ffCesium.tailedAttackArrowGather(tailedAttackArrowGatherFunCallback, {
    color: '#FBFF65',
    alpha: 0.5
  });
};
const tailedAttackArrowGatherFunCallback = gatherObj => {
  console.log('采集成功,其对象为：', gatherObj);
  console.log('采集成功,其关键坐标为：', gatherObj.FFPlotKeyPoints);
  console.log('采集成功,其坐标为：', gatherObj.FFCoordinates);
  gatherObj.isPlot = true;
  addToPlotDataArray(gatherObj);
  //进入编辑
  currentEntity = gatherObj;
  editCurrentEntity('auto');
};
//嵌击箭头采集
const doubleArrowGatherFun = () => {
  ffCesium.doubleArrowGather(doubleArrowGatherFunCallback, {
    color: '#FBFF65',
    alpha: 0.5
  });
};

const doubleArrowGatherFunCallback = gatherObj => {
  console.log('采集成功,其对象为：', gatherObj);
  console.log('采集成功,其关键坐标为：', gatherObj.FFPlotKeyPoints);
  console.log('采集成功,其坐标为：', gatherObj.FFCoordinates);
  gatherObj.isPlot = true;
  addToPlotDataArray(gatherObj);
  //进入编辑
  currentEntity = gatherObj;
  editCurrentEntity('auto');
};
//集结地采集
const rendezvousGatherFun = () => {
  ffCesium.rendezvousGather(rendezvousGatherFunCallback, {
    color: '#FBFF65',
    alpha: 0.5
  });
};

const rendezvousGatherFunCallback = gatherObj => {
  console.log('采集成功,其对象为：', gatherObj);
  console.log('采集成功,其关键坐标为：', gatherObj.FFPlotKeyPoints);
  console.log('采集成功,其坐标为：', gatherObj.FFCoordinates);
  gatherObj.isPlot = true;
  addToPlotDataArray(gatherObj);
  //进入编辑
  currentEntity = gatherObj;
  editCurrentEntity('auto');
};

//更多
const isMoreShow = ref(false);
const moreGatherFun = () => {
  console.log('moreGatherFun');
  if (isMoreShow.value == true) {
    isMoreShow.value = false;
  } else {
    isMoreShow.value = true;
  }
};

onUnmounted(() => {
  try {
    //删除所有标签
    for (let i = 0; i < textHtmlOverlayArray.length; i++) {
      ffCesium.removeHtml(textHtmlOverlayArray[i]);
    }
    textHtmlOverlayArray = [];
    console.log('onDestroyed--props.plotDataArray', props.plotDataArray);
    props.plotDataArray?.forEach(item => {
      ffCesium.removeFFEntityID(item.plotObj.id);
    });
    // 销毁采集
    ffCesium.forceGatherEnd();
    ffCesium.forceMilitaryGatherEnd();
    // 恢复手势
    document.getElementById(ffCesium.cesiumID).style.cursor = 'default';
    stopEdit();
  } catch (error) {
    console.log('onUnmounted--error', error);
  }
});
</script>
<style>
.popperClass {
  color: #ffffff;
}
</style>
<style scoped>
:deep(.el-popper .is-light) {
  background: var(--el-bg-color-overlay);
  color: white;
  border: 1px solid var(--el-border-color-light);
}

:deep(.el-popper .is-pure .is-light .el-dropdown__popper .quality-popper) {
  background: var(--el-bg-color-overlay);
  color: white;
  border: 1px solid var(--el-border-color-light);
}

::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 5px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}

::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 5px;
  background: #a8a9ab;
}

::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  border-radius: 5px;
  background: #f0f2f5;
}

.hover-div {
  transition: background-color 0.3s; /* 平滑过渡效果 */
  text-align: center;
  line-height: 30px;
  color: #ffffff;
}
.hover-div:hover {
  cursor: pointer;
  background-color: #3498db; /* 悬浮时的背景色 */
}
.el-divider--horizontal {
  border-top: 1px var(--el-border-color) var(--el-border-style);
  display: block;
  height: 1px;
  margin: 12px 0;
  width: 100%;
}

.iconList {
  display: flex;
  flex-wrap: wrap;
  background-color: #11253e;
  width: 250px;
  padding: 10px;
  height: 500px;
  overflow: auto;
  box-shadow: 0 0 0 1px #475467 inset;
}

.iconClass {
  cursor: pointer;
  border-radius: 2px;
  padding-top: 8px;
  padding-bottom: 6px;
  padding-left: 12.5px;
  padding-right: 12.5px;
}

:deep(.el-message-box) {
  background-color: #11253e;
  border: none;
  display: inline-block;
  max-width: var(--el-messagebox-width);
  width: 100%;
  padding-bottom: 10px;
  vertical-align: middle;
  border-radius: var(--el-messagebox-border-radius);
  font-size: var(--el-messagebox-font-size);
  box-shadow: var(--el-box-shadow-light);
  text-align: left;
  overflow: hidden;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  box-sizing: border-box;
}

:deep(.el-input__wrapper) {
  background-color: #11253e;
  box-shadow: 0 0 0 1px #475467 inset;
}
:deep(.el-input__inner) {
  color: #98a2b3;
}

:deep(.el-input.is-disabled .el-input__wrapper) {
  color: #98a2b3;
  background-color: #11253e;
  box-shadow: 0 0 0 1px #475467 inset;
}

:deep(.el-input-group__append) {
  color: #98a2b3;
  background-color: #11253e;
  box-shadow: 0 0 0 1px #475467 inset;
}
</style>
