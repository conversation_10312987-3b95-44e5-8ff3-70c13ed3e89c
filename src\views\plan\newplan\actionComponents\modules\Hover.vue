<template>
  <div class="Hover-wrapper">
    <Numbers v-model="dataRef" @changeHandle="onChangeHandle" />
  </div>
</template>
<script>
export default {
  name: 'Hover'
};
</script>
<script setup>
import '../../../style/common.css';
import { onMounted, onUnmounted, defineExpose, reactive, ref } from 'vue';
import Numbers from './/components/Numbers.vue';
import { useEditTrackerStore } from '@/store/modules/dataEditTracker.js';
const editTrackerStore = useEditTrackerStore();
//#region 初始化
const dataInfo = reactive({
  deviceInfo: null,
  action: null,
  deviceType: 'Matrice_3TD'
});
const dataRef = reactive({
  title: '悬停时间',
  unit: 's',
  acttionType: 'hover',
  min: 1,
  max: 900,
  value: 10 // 默认最小单位值
});
//#endregion

//#region 方法

// 数据变更后进行修改
const onChangeHandle = v => {
  // 获取动作中的参数进行涉资
  if (dataInfo.action) {
    dataInfo.action.wpml_actionActuatorFuncParam.wpml_hoverTime = v;
    editTrackerStore.dataTracker.markAsModified();
  }
};

/**
 * 设置组件数据
 * @param {*} options
 * @param {*} options.actionFuncParam // 动作参数
 * @param {*} options.deviceInfo // 设备信息
 * @param {*} options.action // 动作对象
 */
const setComponentData = options => {
  // 设置数据前先初始组件数据及界面
  const { actionFuncParam, action, deviceInfo } = options;
  //  获取设备信息
  const { droneSubEnumLabel } = deviceInfo;
  // 设备信息及型号
  dataInfo.deviceInfo = deviceInfo;
  dataInfo.action = action;
  dataInfo.deviceType = droneSubEnumLabel;
  const { wpml_hoverTime = 0 } = actionFuncParam;
  // 设置当前的组件数据
  dataRef.value = wpml_hoverTime;
};

const getComponentData = () => {
  return dataInfo;
};

//#endregion

//#region 对外抛出方法
defineExpose({
  setComponentData,
  getComponentData
});
//#endregion
//#region 生命周期
onMounted(() => {});
onUnmounted(() => {});
//#endregion
</script>
<style lang="scss" scoped>
.Hover-wrapper {
  position: absolute;
  width: 100%;
  color: white;
}
</style>
