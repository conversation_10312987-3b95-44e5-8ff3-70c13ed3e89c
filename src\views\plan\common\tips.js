let mapToolTips = null;

export const setMapToolTips = (title = '提示') => {
  removeMapToolTips();
  // 创建提示 提示内容为
  mapToolTips = document.createElement('div');
  mapToolTips.innerHTML = title; //点击地图设置参考起飞点
  mapToolTips.style = `position: absolute; 
  top: 20%;   
  color: white; pointer-events: none;
  font-size: 16px;
  left: 50%; 
  transform: translate(-50%, -50%); 
  z-index: 99; 
  background: rgba(58, 58, 58, 0.76); 
  padding: 25px 100px; 
  border-radius: 4px;  `;

  // // 创建关闭按钮
  // const closeButton = document.createElement('button');
  // closeButton.textContent = 'x';
  // closeButton.style.position = 'absolute';
  // closeButton.style.top = '5px'; // 根据需要调整位置
  // closeButton.style.right = '5px'; // 根据需要调整位置
  // closeButton.style.color = 'white';
  // closeButton.style.border = 'none';
  // closeButton.style.background = 'transparent';
  // closeButton.style.cursor = 'pointer';
  // closeButton.style.fontSize = '14px';
  // // 添加悬停效果
  // closeButton.style.hover = `color: red;`; // 注意这里使用了 hover 而不是 :hover
  // // 为关闭按钮添加点击事件监听器
  // closeButton.addEventListener('click', () => {
  //   // document.body.removeChild(mapToolTips); // 移除提示框
  //   removeMapToolTips();
  // }); // 将关闭按钮添加到提示框中
  // mapToolTips.appendChild(closeButton);

  document.body.appendChild(mapToolTips); // 添加到页面中
};
// 移除提示
export const removeMapToolTips = () => {
  if (mapToolTips) {
    document.body.removeChild(mapToolTips);
    mapToolTips = null;
  }
};
