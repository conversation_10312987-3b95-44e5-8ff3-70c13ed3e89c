// 拍照相关
// kml takePhoto 拍照默认结构如下
// <wpml:action>
// <wpml:actionId>0</wpml:actionId>
// <wpml:actionActuatorFunc>takePhoto</wpml:actionActuatorFunc>
// <wpml:actionActuatorFuncParam>
//   <wpml:fileSuffix>JPG</wpml:fileSuffix>
//   <wpml:payloadPositionIndex>0</wpml:payloadPositionIndex>
//   <wpml:useGlobalPayloadLensIndex>1</wpml:useGlobalPayloadLensIndex>
//   <wpml:payloadLensIndex>wide,zoom,ir</wpml:payloadLensIndex>
// </wpml:actionActuatorFuncParam>
// </wpml:action>
import { PAYLOAD_LENS_INDEX, STATUE_TYPE } from '../../props';
import { Action } from '../../waylines';
import { generateKey } from '@/utils';
import { ACTION_ACTUATOR_FUNC, ACTION_TRIGGER_TYPE } from '@/utils/constants';
import { useDeviceStore } from '@/store/modules/device.js';
const deviceStore = useDeviceStore();

//#region 拍照动作

/**
 * 创建拍照动作
 * @param {Object} options 动作配置项，应包含actionId、actionActuatorFunc
 * @param {Object|null} actionActuatorFuncParamOptions 动作执行器参数配置，可选
 * @returns {Action|null} 返回Action实例或在配置不正确时返回null
 */
export function createTakePhoteAction(options, actionActuatorFuncParamOptions = null) {
  try {
    // 创建动画组
    if (!options) {
      return null;
    }
    return new Action({
      actionId: options.actionId || 0,
      actionActuatorFunc: ACTION_ACTUATOR_FUNC.takePhoto,
      actionActuatorFuncParam: actionActuatorFuncParamOptions || getTakePhoteActionDefaultParam(),
      uuid: options.actionUuid || generateKey(), // 动作id
      trigger: ACTION_TRIGGER_TYPE.reachPoint
    });
  } catch (error) {
    console.error('创建 Action 实例失败:', error);
    return null;
  }
}

// 设置拍照默认参数
export function getTakePhoteActionDefaultParam() {
  let cameras = deviceStore?.getCameraSelect().join(',') ?? '';
  const actionActuatorFuncParam = {
    wpml_fileSuffix: '', // 这里如果为空则不写入
    wpml_payloadPositionIndex: 0,
    wpml_useGlobalPayloadLensIndex: 1,
    wpml_payloadLensIndex: cameras
  };
  return actionActuatorFuncParam;
}
//#endregion
