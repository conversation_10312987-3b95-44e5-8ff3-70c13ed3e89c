<template>
  <div class="">
    <el-form
      ref="loginFormRef"
      :model="loginFormData"
      :rules="rules"
      auto-complete="on"
    >
      <el-form-item prop="mobile" class="flex-item">
        <div class="login-form-input">
          <span class="ff-cloud-icon clound-mobile-icon" />
          <el-input
            v-model="loginFormData.mobile"
            maxlength="11"
            :placeholder="$t('login.phoneNumber')"
          />
          <el-button
            type="primary"
            class="code-button"
            :disabled="!!phoneTimer"
            @click="getPhoneCode(60)"
          >
            {{
              _.isNaN(phoneCodeText * 1) ? phoneCodeText : `${phoneCodeText} s`
            }}
          </el-button>
        </div>
      </el-form-item>
      <el-form-item prop="verifyCode">
        <div class="login-form-input">
          <span class="ff-cloud-icon cloud-valid-code" />
          <el-input
            v-model="loginFormData.verifyCode"
            class="py-1.5"
            :placeholder="$t('login.verificationCode')"
          />
        </div>
      </el-form-item>
      <div class="flex justify-end items-center login-box-foot">
        <el-link
          v-if="!isExclusive"
          :underline="false"
          type="primary"
          @click="$router.push({ name: 'Register' })"
        >
          {{ $t('login.registration') }}
        </el-link>
        <el-link
          v-if="accountType === 0"
          class="ml-2"
          :underline="false"
          type="primary"
          @click="toResetPwd"
        >
          {{ $t('login.retrievePassword') }}
        </el-link>
      </div>
      <el-button
        :loading="submitLoading"
        type="primary"
        class="login-form-btn"
        @click="handleLogin"
      >
        {{ $t('login.login') }}
      </el-button>
    </el-form>
  </div>
</template>

<script setup>
import _ from 'lodash';
import i18n from '@/lang';

import { ref, reactive, watch, onMounted, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

// import PopDragVerify from '../dragVerify/index.vue';
import { useUserStore } from '@/store/modules/user.js';
import { sendNote } from '@/api/auth';

const userStore = useUserStore();

const route = useRoute();
const router = useRouter();
const isExclusive = ref(false);
const loginFormRef = ref('loginFormRef');

const props = defineProps({
  // 账号类型 0主账号登录 1子账号登录
  accountType: {
    type: Number,
    default: 0
  },
  loginAction: {
    type: Function,
    default: () => {}
  }
});

const loginFormData = reactive({
  mobile: '',
  verifyCode: ''
});

const rules = {
  mobile: [
    {
      required: true,
      trigger: ['change', 'blur'],
      message: `${i18n.global.t('login.enterPhone')}`
    }
  ],
  verifyCode: [
    {
      required: true,
      trigger: ['change', 'blur'],
      message: i18n.global.t('login.enterVerificationCode')
    }
  ]
};

const submitLoading = ref(false);
const phoneCodeText = ref(i18n.global.t('login.getCode'));
const phoneTimer = ref(null);

const initConfig = () => {
  let startTime = null;
  const nowTime = Date.now();

  startTime = sessionStorage.getItem('phoneCodeText');
  if (startTime) {
    const time = Math.floor((nowTime - startTime) / 1000);
    setCodeText(60 - time);
  }
};

const handleLogin = () => {
  submitLoading.value = true;
  loginFormRef.value.validate(async valid => {
    if (valid) {
      props.loginAction();
    } else {
      submitLoading.value = false;
      return false;
    }
  });
};

const getPhoneCode = num => {
  loginFormRef.value.validateField('mobile', async valid => {
    if (valid) {
      const res = await sendCode({ mobile: loginFormData.mobile });
      if (res) {
        setCodeText(num);
      }
    }
  });
};

const setCodeText = num => {
  const startTime = sessionStorage.getItem('phoneCodeText');
  if (!startTime) {
    sessionStorage.setItem('phoneCodeText', Date.now());
  }
  phoneCodeText.value = num <= 0 ? i18n.global.t('login.getCode') : num;
  if (num <= 0) {
    sessionStorage.removeItem('phoneCodeText');
    return;
  }
  phoneTimer.value = setInterval(() => {
    phoneCodeText.value = Math.max(0, phoneCodeText.value - 1);
    if (phoneCodeText.value <= 0) {
      clearInterval(phoneTimer.value);
      phoneCodeText.value = i18n.global.t('login.getCode');
      phoneTimer.value = null;
      sessionStorage.removeItem('phoneCodeText');
    }
  }, 1000);
};

const sendCode = async data => {
  try {
    const res = await sendNote(data);
    return res.code * 1 === 200;
  } catch (err) {
    return false;
  }
};

const validSubmit = async validResult => {
  if (validResult) {
    try {
      if (isExclusive.value) {
        const form = _.cloneDeep(loginFormData);
        await userStore
          .exclusiveLogin({ data: form, tenantId: route.query.tenant })
          .then(() => {
            ElMessage.success(
              i18n.global.t('page.dialog.actionFb.successfullyLogin')
            );
            router.replace({ name: 'dashboard' });
          })
          .catch(() => {
            submitLoading.value = false;
          })
          .finally(() => {
            submitLoading.value = false;
          });
      } else {
        await userStore
          .loginBySms(loginFormData)
          .then(() => {
            ElMessage.success(
              i18n.global.t('page.dialog.actionFb.successfullyLogin')
            );
            router.replace({ name: 'dashboard' });
          })
          .catch(() => {
            submitLoading.value = false;
          })
          .finally(() => {
            submitLoading.value = false;
          });
      }
    } catch {
      console.log('login error');
    }
  }
  submitLoading.value = false;
};

const toResetPwd = () => {
  router.push({ name: 'ResetPassword', query: route.query });
};

watch(
  () => props.accountType,
  newVal => {
    // accountType 0 为主账号 1 为子账号
    if (newVal >= 0) {
      Object.assign(loginFormData, {
        mobile: '',
        verifyCode: ''
      });

      setTimeout(() => {
        loginFormRef?.value.clearValidate();
      }, 50);
    }
  }
);

onMounted(() => {
  if (JSON.stringify(route.query) !== '{}' && route.query.tenant) {
    isExclusive.value = true;
  }

  initConfig();

  window.$bus.on('dialogCancel', data => {
    if (data.emitName === 'dragVerify') {
      submitLoading.value = false;
    }
  });

  // 监听滑动验证码弹窗beforeClose事件
  window.$bus.on('dialogBeforeClose', data => {
    if (
      !_.isEmpty(data) &&
      data.emitData.loginType === 'smsLogin' &&
      data.emitName === 'dragVerify'
    ) {
      validSubmit(data.emitData);
    }
  });
});

onUnmounted(() => {
  window.$bus.off('dialogCancel');
  window.$bus.off('dialogBeforeClose');
});
</script>

<style lang="scss" scoped>
:deep() {
  .el-form-item.is-error .el-input__wrapper {
    box-shadow: none;
    &:hover {
      box-shadow: none;
    }
  }

  .el-form-item.is-error .el-input__wrapper.is-focus {
    box-shadow: none !important;
  }
}
.login-form-input {
  width: 100%;
  display: flex;
  padding: 7px 7px 7px 20px;
  border-radius: 4px;
  background: #f8fafe;
  line-height: 1;
  align-items: center;
  :deep() {
    .el-input__wrapper {
      box-shadow: none;
      background-color: #f8fafe;
    }
    .el-input__inner {
      height: unset;
      border: none;
      background: transparent;
      line-height: 1;

      flex: 1;
      -webkit-appearance: none;
      &:-webkit-autofill {
        box-shadow: 0 0 0 1000px #f8fafe inset !important;

        -webkit-text-fill-color: #000 !important;
      }
    }
    .el-input__prefix {
      position: relative;
    }

    .ff-cloud-icon {
      color: #097efc;
      font-size: 18px;
    }
  }
}
.login-form-input-icon {
  width: 20px;
}
.login-form-btn {
  margin-top: 25px;
  padding: 15px;
  width: 100%;
  font-weight: 500;
  font-size: 14px;
  background: linear-gradient(90deg, #4057fb, #2fb7ff);
  border: none;

  &:hover,
  &:focus {
    background: #2fb7ff;
    border: none;
  }
}
.code-button {
  min-width: 90px;
  background: rgba(9, 126, 252, 0.1);
  color: #097efc;
  border: none;
  font-size: 12px;
}
:deep() {
  .login-box-foot {
    .el-link--primary {
      color: #fff;
    }
  }
  .el-input__inner[maxlength] {
    padding-right: 35px;
  }
}
</style>
