$--colors: (
  'primary': (
    'base': #2e90fa
  ),
  'success': (
    'base': #39bfa4
  ),
  'warning': (
    'base': #fdb022
  ),
  'danger': (
    'base': #f97066
  ),
  'error': (
    'base': #f97066
  ),
  'info': (
    'base': #909399
  )
);

// You should use them in scss, because we calculate it by sass.
// comment next lines to use default color
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  // do not use same name, it will override.
  $colors: $--colors
);

.el-loading-mask {
  background-color: transparent; /* 设置你想要的颜色 */
}

// // 修改全局样式
// @use 'element-plus/theme-chalk/src/index.scss' as *;

// You can comment it to hide debug info.
// @debug $--colors;
.ellipsis {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.el-overlay-dialog {
  display: flex;
  flex-direction: column;
  justify-content: center;

  .el-dialog {
    --el-dialog-margin-top: 0;
    // margin: var(--el-dialog-margin-top, 15vh) auto 20px;
    margin: 0 auto !important;
    // top: 50%;
    // transform: translateY(-50%);
    padding: 0;
    .el-dialog__header {
      font-weight: bold;
      padding: 17.5px;
      .el-dialog__headerbtn{
        top:6px
      }
    }
    .el-dialog__body {
      max-height: calc(100vh - 150px);
      overflow: auto;
      padding: 16px;
    }
  }
}

.el-dialog__header {
  border-bottom: 1px solid #dcdfe6;
  // margin-right: 0px !important;
}

.el-form-item--default {
  margin-bottom: 16px !important;
}

.el-button {
  border-radius: var(--el-border-radius-small);
}

.el-checkbox__inner:after {
  box-sizing: content-box;
  content: '';
  border: 2px solid transparent;
  border-left: 0;
  border-top: 0;
  height: 7px;
  left: 4px;
  position: absolute;
  top: 0px;
  transform: rotate(45deg) scaleY(0);
  width: 3px;
  transition: transform 0.15s ease-in 0.05s;
  transform-origin: center;
}

.el-dialog {
  border-radius: var(--el-border-radius-mid);
  overflow: hidden;
}

.el-dialog__header {
  border-radius: var(--el-border-radius-mid);
  background: #eff6ff;
  margin-right: 0;
  border-bottom: none;
}

.el-dialog__footer {
  border-top: 1px solid #e4e7ec;
  text-align: var(--el-dialog__footer-text-align-center) !important;
  padding: 12px;

  & .el-button {
    padding: 0 46px 0 46px;
    height: 40px;
  }
}

.el-drawer__header{
  margin-bottom: 0 ;
}

$colors: (
  'primary': (
    'light-1': var(--el-color-primary),
    'light-3': var(--el-color-primary-light-3),
    'light-5': var(--el-color-primary-light-5),
    'light-7': var(--el-color-primary-light-7),
    'light-9': var(--el-color-primary-light-9)
  ),
  'success': (
    'light-1': var(--el-color-success),
    'light-3': var(--el-color-success-light-3),
    'light-5': var(--el-color-success-light-5),
    'light-7': var(--el-color-success-light-7),
    'light-9': var(--el-color-success-light-9)
  ),
  'warning': (
    'light-1': var(--el-color-warning),
    'light-3': var(--el-color-warning-light-3),
    'light-5': var(--el-color-warning-light-5),
    'light-7': var(--el-color-warning-light-7),
    'light-9': var(--el-color-warning-light-9)
  ),
  'danger': (
    'light-1': var(--el-color-danger),
    'light-3': var(--el-color-danger-light-3),
    'light-5': var(--el-color-danger-light-5),
    'light-7': var(--el-color-danger-light-7),
    'light-9': var(--el-color-danger-light-9)
  ),
  'error': (
    'light-1': var(--el-color-error),
    'light-3': var(--el-color-error-light-3),
    'light-5': var(--el-color-error-light-5),
    'light-7': var(--el-color-error-light-7),
    'light-9': var(--el-color-error-light-9)
  ),
  'info': (
    'light-1': var(--el-color-info),
    'light-3': var(--el-color-info-light-3),
    'light-5': var(--el-color-info-light-5),
    'light-7': var(--el-color-info-light-7),
    'light-9': var(--el-color-info-light-9)
  )
);

// 循环生成颜色类名
@for $i from 1 through 9 {
  @each $color, $shades in $colors {
    $colorName: map-get($shades, 'light-#{$i}');
    .text-#{$color}-light-#{$i} {
      color: $colorName;
    }
  }
}


//暗色系重写样式
.el-card__body {
  padding-top: 0;
}
.el-select-dropdown .el-select-dropdown__item {
  &:hover {
      background-color: #001129; /* 设置鼠标悬停时的背景色 */
  }
  &:not(:hover) {
      background-color: transparent; /* 设置非鼠标悬停时的背景色为透明 */
  }
}
.el-dialog__header {
  border-radius: 0;
  background: #1F2F49;
}
.el-dialog__title {
  color: #fff;
}
.el-dialog__footer {
  border-top: 1px solid #465467;
}
//日期组件
.el-date-table td.in-range .el-date-table-cell {
  background-color: #001129;
}
.el-date-table-cell:hover{
  background-color: #195EBF;
  color: #fff;
}
.el-message-box__title {
  color: #fff;
}
.el-checkbox__inner{
  background-color: #fff;
  border:1px solid #fff
}
.el-popper.is-dark {
  background: #EFF6FF;  // 设置背景色
  color: #475467;  // 设置文字颜色
}
.el-popper.is-dark .el-popper__arrow::before {
  border: 1px solid #EFF6FF;
  background: #EFF6FF;
}
.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background: #1F2F49 !important;
}
.el-pagination.is-background .btn-next, .el-pagination.is-background .btn-prev, .el-pagination.is-background .el-pager li{
  background: #11253e !important;
  border: 1px solid #344054;
}
.el-pagination.is-background .btn-next.is-active, .el-pagination.is-background .btn-prev.is-active, .el-pagination.is-background .el-pager li.is-active {
  background-color: #2e90fa !important;
  color: #fff;
}
.el-message-box__status.el-message-box-icon--warning {
  color: #2e90fa;
}
.el-image-viewer__actions__inner{
  color:#475467
}
.el-image-viewer__close{
  color:#475467
}