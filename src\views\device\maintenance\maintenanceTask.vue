<!--机场管理-->
<script>
export default {
  name: 'Maintenance'
};
</script>

<script setup>
import { reactive } from 'vue';
import { getTaskList, deleteTask } from '@/api/devices/maintenance';
import TaskEditDialog from './components/taskEditDialog.vue';
import TaskDetail from './components/taskDetail.vue';
import optionData from '@/utils/option-data';

const editDialogRef = ref(null);
const detailRef = ref(null);
const loading = ref(false);
const total = ref(0);
const queryParams = reactive({
  page_num: 1,
  page_size: 10,
  status: '',
  scheme_name: '',
});
const dataList = ref([]);
const editDialog = reactive({
  visible: false
});
const detailDialog = reactive({
  visible: false
});
let formData = reactive({});
let detailData = reactive({});
/**
 * 查询
 */
function handleQuery() {
  // Calculate the new total pages based on the new page size
  const newTotalPages = Math.ceil(total.value / queryParams.page_size);
  
  // If current page is greater than new total pages, adjust to the last page
  if (queryParams.page_num > newTotalPages) {
    queryParams.page_num = newTotalPages || 1;
  }
  getTaskList({
    ...queryParams,
  }).then(data => {
    const { records } = data;
    dataList.value = records || [];
    total.value = data.total;
  });
}
function handleSearch() {
  queryParams.page_num = 1;
  handleQuery();
}
/**
 * 重置查询
 */
function resetQuery() {
  queryParams.status = ''
  queryParams.scheme_name = '',
  queryParams.page_num = 1,
  queryParams.page_size = 10,
  handleQuery();
}

function openDetailDialog(row) {
  detailDialog.visible = true;
  Object.keys(detailData).map(key => {
    delete detailData[key];
  });
  Object.assign(detailData, { ...row });
}

/**
 * 打开表单弹窗
 */
function openEditDialog(row) {
  editDialog.visible = true;
  Object.keys(formData).map(key => {
    delete formData[key];
  });
  if (row) {
    editDialog.title = '编辑维保任务';
    Object.assign(formData, { ...row });
  }
}

function addAndEditPlan (row) {
  editDialog.visible = true;
  Object.keys(formData).map(key => {
    delete formData[key];
  });
  if (row) {
    editDialog.title = '编辑维保任务';
    Object.assign(formData, { ...row });
  }else {
    editDialog.title = '新增维保任务';
  }
}

/**
 * 删除
 */
 function handleDelete(row) {
  ElMessageBox.confirm('删除后无法恢复，是否确认删除？','确认删除所选维保任务？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    autofocus: false,
    closeOnClickModal: false,
    type: 'warning'
  }).then(() => {
    deleteTask({
      id: toRaw(row).id
    }).then(data => {
      ElMessage.success('删除成功');
      handleQuery();
    });
  });
}

onMounted(() => {
  handleQuery();
});
</script>
<style lang="scss" scoped>
.input-serach {
  width: 200px;
}
</style>
<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
          <el-form-item label="" prop="plan_name">
            <el-input
              class="input-serach"
              v-model="queryParams.scheme_name"
              placeholder="请输入方案名称"
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
            />
          </el-form-item>
          <el-form-item label="" prop="range">
            <el-select v-model="queryParams.status" placeholder="请选择维保任务状态" clearable  @change="handleSearch">
              <el-option
                v-for="item in optionData.maintenanceStatus"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="search-btn">
        <el-button type="primary" @click="handleSearch()"
          ><i-ep-search />搜索</el-button
        >
        <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
      </div>
    </div>
    <el-card shadow="never">
      <template #header>
        <el-button type="primary" @click="addAndEditPlan()">新增维保任务</el-button>
      </template>
      <el-table highlight-current-row :data="dataList" v-loading="loading" stripe height="540">
        <el-table-column label="序号" width="80">
          <template #default="scope">
            <span>{{ scope.$index + 1 + queryParams.page_size * (queryParams.page_num - 1) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="计划名称" prop="plan_name" show-overflow-tooltip />
        <el-table-column label="方案名称" prop="scheme_name" show-overflow-tooltip />
        <el-table-column label="项目" prop="task_item_names" show-overflow-tooltip />
        <el-table-column label="维保时间" prop="due_date_desc" show-overflow-tooltip />
        <el-table-column label="维保任务状态" prop="status_desc" show-overflow-tooltip />
        <el-table-column label="执行用户" prop="assign_user_name" show-overflow-tooltip />
        <el-table-column label="创建人" prop="creator" show-overflow-tooltip />
        <el-table-column fixed="right" label="操作" width="250">
          <template #default="scope">
            <el-button type="primary" link @click.stop="openEditDialog(scope.row)" v-if="scope.row.status == false">编辑</el-button>
            <el-button type="primary" link @click.stop="openDetailDialog(scope.row)" v-if="scope.row.status == true">查看</el-button>
            <el-button type="primary" link @click.stop="openDetailDialog(scope.row)" v-if="scope.row.status == false">维保</el-button>
            <el-button type="danger" link @click.stop="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.page_num"
        v-model:limit="queryParams.page_size"
        @pagination="handleQuery"
      />
    </el-card>
    <TaskEditDialog
      ref="editDialogRef"
      v-model:visible="editDialog.visible"
      :title="editDialog.title"
      :form-data="formData"
      @submit="resetQuery"
    />
    <TaskDetail
      ref="detailRef"
      v-model:visible="detailDialog.visible"
      :title="detailDialog.title"
      :form-data="detailData"
      @submit="resetQuery"
    />
  </div>
</template>
<style scoped lang="scss">
.flex-center {
  display: flex;
  align-items: center;
  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #4caf51;
  }
  .unstatus {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: red;
  }
}
.search {
  display: flex;
  align-items: center;
  padding: 24px;
  height: 64px;
  .search-form {
    flex: 1;
    padding-top: 16px;
  }
}
</style>
