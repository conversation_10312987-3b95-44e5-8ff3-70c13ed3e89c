# MapTools 图层功能增强总结

## 完成的功能

### 1. 围栏图层功能
- ✅ 在工具条的图层中增加了"围栏"图层
- ✅ 数据源从 `getFenceList` 接口获取围栏数据
- ✅ 支持不同围栏类型的颜色区分：
  - 入侵检测围栏：红色
  - 区域监控围栏：蓝色
  - 默认围栏：黄色
- ✅ 围栏显示包含名称标签和详细属性信息
- ✅ 支持围栏的显示/隐藏切换

### 2. 动态图层功能（全面升级）
- ✅ 在工具条的图层中增加了"自定义图层"分组
- ✅ 数据源从图层管理接口 `getAllLayers` 获取图层列表
- ✅ 图元数据从 `getFeaturesByLayer` 接口获取
- ✅ **按照 sort_order 进行排序加载图层顺序**
- ✅ **通过 min_level、max_level 控制图层在不同比例尺下的可见级别**
- ✅ **通过 layer_type 字段区分 point、line、polygon 图元要素格式**
- ✅ **通过 layer_config 添加图层样式配置**
- ✅ **通过 label_field、label_color、label_font 进行 entity 标签配置**
- ✅ 支持多种几何类型的图元：
  - Point（点）- 支持图标和点样式
  - LineString（线）- 支持线宽和颜色配置
  - Polygon（多边形）- 支持填充色、边框色和边框宽度
- ✅ 支持图层的动态加载和显示控制
- ✅ 每个图层都有独立的数据源和显示状态

### 3. 图层框样式优化
- ✅ 重新设计了图层管理面板的UI
- ✅ 使用现代化的渐变背景和毛玻璃效果
- ✅ 添加了图层分组和图标
- ✅ 优化了复选框样式和交互效果
- ✅ 添加了图层状态指示器
- ✅ 改进了滚动条样式
- ✅ 增加了悬停效果和过渡动画

## 技术实现细节

### 数据结构处理
- 围栏数据使用 `fence_coords` 字段获取坐标信息
- 图层数据按照新的接口格式处理：
  - `layer_name` - 图层名称
  - `layer_type` - 图层类型（point/line/polygon）
  - `sort_order` - 排序顺序
  - `min_level`/`max_level` - 可见级别范围
  - `label_field` - 标签字段名
  - `label_color` - 标签颜色
  - `label_font` - 标签字体配置
  - `layer_config` - 图层样式配置
- 图元几何数据支持 JSON 字符串和对象两种格式

### 比例尺控制系统
- 实现了基于相机高度的级别计算系统
- 监听相机移动事件，动态控制图层可见性
- 支持 1-18 级别的精确控制

### 样式配置系统
- **点图层样式**：支持图标和点样式，可配置大小、颜色
- **线图层样式**：支持线宽、颜色配置
- **多边形图层样式**：支持填充色、边框色、边框宽度配置
- **标签样式**：支持字体大小、字体族、颜色配置

### 性能优化
- 图层数据按需加载，只有在显示时才加载图元数据
- 使用 Cesium 的 CustomDataSource 进行数据源管理
- 添加了错误处理和日志记录
- 图层按 sort_order 排序，确保正确的显示顺序

### 用户体验
- 图层面板支持滚动，适应大量图层
- 添加了加载状态和错误提示
- 图层切换响应迅速，状态实时更新
- 比例尺自动控制图层显示，避免性能问题

## 文件修改

### 主要修改文件
- `src/views/homeView/component/mapTools.vue` - 主要功能实现

### 使用的API接口
- `src/api/fence/index.js` - 围栏数据接口
- `src/api/map/layer.js` - 图层管理接口
- `src/api/map/feature.js` - 图元数据接口

## 使用说明

1. **围栏图层**：
   - 在图层管理面板的"业务图层"分组中找到"围栏"选项
   - 勾选后会自动加载所有围栏数据并在地图上显示
   - 不同类型的围栏会以不同颜色显示

2. **自定义图层**：
   - 在图层管理面板的"自定义图层"分组中查看所有可用图层
   - 图层按照 `sort_order` 字段排序显示
   - 勾选图层后会自动加载该图层的所有图元数据
   - 图层会根据当前地图缩放级别自动显示/隐藏
   - 支持点、线、面等多种图元类型，每种类型都有独特的样式配置

3. **图层管理**：
   - 点击工具条的"图层控制"按钮打开图层管理面板
   - 使用复选框控制图层的显示/隐藏
   - 图层状态指示器显示当前图层的激活状态
   - 鼠标悬停在信息图标上可查看图层描述

## 接口数据格式要求

### 图层列表接口返回格式
```json
[
  {
    "id": 7,
    "layer_name": "取水点",
    "layer_type": "point",
    "min_level": 1,
    "max_level": 18,
    "is_visible": true,
    "label_field": "feature_addr",
    "label_color": "#FF0000",
    "label_font": "{\"size\":12,\"family\":\"Arial\"}",
    "layer_config": "{\"type\":\"point\",\"icon\":\"/resource/images/lyr_icon/build.png\",\"size\":[32,32]}",
    "sort_order": 0,
    "remark": ""
  }
]
```

### 图层配置格式
- **点图层配置**：`{"type":"point","icon":"/path/to/icon.png","size":[32,32]}`
- **线图层配置**：`{"type":"line","fillColor":"#FF8C00FF","borderWidth":3}`
- **面图层配置**：`{"type":"polygon","fillColor":"#5390D280","borderColor":"#B524B3FF","borderWidth":3}`

## 注意事项

- 确保后端API接口返回正确的数据格式
- 围栏坐标数据应为 `[[经度, 纬度], ...]` 格式
- 图元几何数据应符合 GeoJSON 标准格式
- 图层配置和标签字体配置应为有效的 JSON 字符串
- 建议在生产环境中添加数据缓存机制以提高性能
- 图标路径应为服务器可访问的相对或绝对路径
