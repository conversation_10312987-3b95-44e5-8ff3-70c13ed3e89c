<script setup>
import { computed, watchEffect, onMounted, onUnmounted, ref } from 'vue';
import { useWindowSize } from '@vueuse/core';
import {
  AppMain,
  HomeViewNavbar
} from './components/index';

import { useAppStore } from '@/store/modules/app';
import { useSettingsStore } from '@/store/modules/settings';

import JJSocketService from '@/utils/websocket/JJSocketService';
import router from '@/router';
import { fetchTaskReport, getJJConfig, executeAlarm } from '@/api/task';
const showTips = ref(false);

const { width } = useWindowSize();

/**
 * 响应式布局容器固定宽度
 *
 * 大屏（>=1200px）
 * 中屏（>=992px）
 * 小屏（>=768px）
 */
const SMALL_WIDTH = 768;
const LG_WIDTH = 1200;

const appStore = useAppStore();
const settingsStore = useSettingsStore();

const fixedHeader = computed(() => settingsStore.fixedHeader);
const showTagsView = computed(() => settingsStore.tagsView);
// const showSettings = computed(() => settingsStore.showSettings);

const classObj = computed(() => ({
  // hideSidebar: !appStore.sidebar.opened,
  openSidebar: appStore.sidebar.opened,
  withoutAnimation: appStore.sidebar.withoutAnimation,
  mobile: appStore.device === 'mobile'
}));

const dialogVisible = ref(false);
const alarmData = ref({});
const defaultTriggerEnable = ref(true); //是否自动出警 为true：显示确认按钮 false:显示立即出警
onMounted(() => {

});
/**
 * 接处警任务上报
 */
function taskReport(data) {
  openDialog(data);

  fetchTaskReport({
    alarm_id: data?.alarmId,
    alarm_name: data?.content,
    latitude: data?.lat,
    longitude: data?.lon,
    remark: JSON.stringify(data)
  }).then(res => {
  }).finally(()=>{
    window.$bus.emit('refreshJJTask');
  })

  
}
/**
 * 获取配置信息
 */
function getJJConfigAPI(data) {
  getJJConfig({}).then(res => {
    defaultTriggerEnable.value = res.default_trigger_enable;
    taskReport(data);
  });
}
function openDialog(data) {
  dialogVisible.value = true;
  alarmData.value = data;
}

function toReceive() {
  //是否自动出警 为true：显示确认按钮 false:显示立即出警
  if (defaultTriggerEnable.value) {
    dialogVisible.value = false;
  }
}

function toLiveStream() {
  router.push({
    path: '/live-stream',
    query: {
      tab: 'second'
    }
  });
  showTips.value = false;
}

function handleOutsideClick() {
  appStore.closeSideBar(false);
}
function toCancel() {
  dialogVisible.value = false;
}
function toExe() {
  let alarmInfo = alarmData.value;
  executeAlarm(alarmInfo.alarmId).then(data => {
    ElMessage.success('出警成功');
    dialogVisible.value = false;
  });
}
</script>

<template>
  <div :class="classObj" class="app-wrapper">
    <!-- 手机设备侧边栏打开遮罩层 -->
    <div v-if="classObj.mobile && classObj.openSidebar" class="drawer-bg" @click="handleOutsideClick" />
    <!-- <Sidebar class="sidebar-container" /> -->
    <div :class="{ hasTagsView: showTagsView }" class="main-container" style="margin-left: 0;">
      <div :class="{ 'fixed-header': fixedHeader} " class="head-box">
        <HomeViewNavbar />
      </div>
      <app-main style="height: 100%"/>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.main-container {
  height: 100%;
  position: relative;
}
.head-box {
  position: absolute;
  top: 0;
  left: 0;
  height: 102px;
  width: 100%;
  z-index: 1999;
}
.app-wrapper {
  &:after {
    content: '';
    display: table;
    clear: both;
  }

  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
}
.hideSidebar .fixed-header {
  width: calc(100% - #{$miniSideBarWidth});
}
.mobile .fixed-header {
  width: 100%;
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.tip-view {
  position: fixed;
  bottom: 50px;
  right: 30px;
  background: var(--el-color-primary);
  color: white;
  border-radius: 50px;
  padding: 20px;

  animation-name: scaleAnimation; // 动画名
  animation-duration: 2s; // 动画时长
  animation-iteration-count: infinite; // 永久动画
  transition-timing-function: ease-in-out; // 动画过渡
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); // 元素阴影

  a {
    text-decoration: underline;
  }
}
@keyframes scaleAnimation {
  // 动画设置
  0% {
    transform: scale(1);
  }

  25% {
    transform: scale(1.08);
  }

  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(1.08);
  }
}
</style>
