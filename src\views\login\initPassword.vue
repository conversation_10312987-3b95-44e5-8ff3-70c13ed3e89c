<!-- 设置新密码 -->
<template>
  <div>
    <PageBox :show-leftimg="false">
      <div class="pb-5 flex-1 max-h-screen overflow-y-scroll">
        <PageHead
          custom-class="custom-head"
          :show-language="true"
          :logo="logo"
        />
        <div class="flex justify-center">
          <el-card class="card-box mt-3">
            <div class="card-head">{{ $t('initPassword.setNewPassword') }}</div>
            <el-form
              ref="formRef"
              :model="formData"
              :rules="rules"
              class="demo-form mt-3"
            >
              <el-form-item prop="newPassword">
                <el-input
                  v-model="formData.newPassword"
                  autocomplete="new-password"
                  :type="newPasswordShow"
                  class="custom-input"
                  :placeholder="$t('initPassword.enterYourLoginPassword')"
                >
                  <template #suffix>
                    <span
                      class="show-pwd cursor-pointer"
                      @click="
                        newPasswordShow = newPasswordShow ? '' : 'password'
                      "
                    >
                      <i
                        class="ff-cloud-icon"
                        :class="
                          newPasswordShow === 'password'
                            ? 'clound-open-eye'
                            : 'clound-close-eye'
                        "
                      />
                    </span>
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item prop="confirmPassword">
                <el-input
                  v-model="formData.confirmPassword"
                  autocomplete="new-password"
                  :type="confirmPasswordShow"
                  class="custom-input"
                  :placeholder="$t('initPassword.enterPassword')"
                >
                  <template #suffix>
                    <span
                      class="show-pwd cursor-pointer"
                      @click="
                        confirmPasswordShow = confirmPasswordShow
                          ? ''
                          : 'password'
                      "
                    >
                      <i
                        class="ff-cloud-icon"
                        :class="
                          confirmPasswordShow === 'password'
                            ? 'clound-open-eye'
                            : 'clound-close-eye'
                        "
                      />
                    </span>
                  </template>
                </el-input>
              </el-form-item>
            </el-form>
            <el-button
              :loading="subLoading"
              type="primary"
              class="login-button"
              @click="submitForm"
            >
              {{ $t('page.confirm') }}
            </el-button>
          </el-card>
        </div>
      </div>
    </PageBox>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import i18n from '@/lang';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';

import { changePassword } from '@/api/auth';

import { useUserStore } from '@/store/modules/user';

import { valid_9, validatenull } from '@/utils/helper';

import logo from '@/assets/sx_logo_primary.png';

const userStore = useUserStore();
const router = useRouter();

// 密码验证规则
const validatePass = (rule, value, callback) => {
  if (validatenull(value)) {
    return callback(new Error(i18n.global.t('initPassword.pwdTips')));
  } else if (!valid_9(value)) {
    return callback(new Error(i18n.global.t('initPassword.pwdTips')));
  } else {
    callback();
  }
};

// 再次输入密码验证规则
const checkPass = (rule, value, callback) => {
  if (validatenull(value)) {
    callback(new Error(i18n.global.t('initPassword.enterPwdAgain')));
  } else if (value !== formData.value.newPassword) {
    callback(
      new Error(i18n.global.t('initPassword.enteredTwiceAreInconsistent'))
    );
  } else {
    callback();
  }
};

const formRef = ref('formRef');
const initForm = () => ({
  newPassword: '', // 登录密码
  confirmPassword: '' // 确认密码
});

const formData = ref(initForm());
const subLoading = ref(false);
const rules = {
  newPassword: [
    {
      required: true,
      validator: validatePass,
      trigger: ['change', 'blur']
    },
    {
      min: 6,
      max: 20,
      message: i18n.global.t('initPassword.pwdTips'),
      trigger: ['change', 'blur']
    }
  ],
  confirmPassword: [
    { required: true, validator: checkPass, trigger: ['change', 'blur'] }
  ]
};
const newPasswordShow = ref('password');
const confirmPasswordShow = ref('password');
// const logo = logo;

// 提交表单
const submitForm = async () => {
  subLoading.value = true;
  formRef.value.validate(async valid => {
    if (valid) {
      try {
        await changePassword(formData.value);
        await userStore.getInfo();

        ElMessage.success(i18n.global.t('initPassword.resetSucceeded'));
        router.replace({
          name: 'dashboard'
        });
      } catch (error) {
        console.log('error: ', error);
      }
    } else {
      subLoading.value = false;
      console.log('error submit!!');
      return false;
    }
  });
  subLoading.value = false;
};
</script>

<style scoped lang="scss">
:deep() {
  .card-box {
    width: 500px;

    .card-head {
      font-size: 28px;
      font-family: AliMedium;
      font-weight: 500;
      color: #222222;
      // height: 100px;
      padding-top: 30px;
      padding-bottom: 15px;
      line-height: 26px;
      text-align: center;
    }

    .card-tab {
      /*去掉tabs底部的下划线*/
      .el-tabs__nav-wrap::after {
        position: static !important;
      }

      .el-tabs__active-bar {
        border-radius: 2px;
        width: 30px !important;
        left: 15px;
        height: 4px;
      }

      .el-tabs__item {
        font-family: dfFont;
        font-weight: 500;
      }
    }
  }
  .flex-item {
    .el-form-item__content {
      display: flex;
      .ml-2 {
        margin-left: 0.5rem !important;
      }
    }
  }
  .justify-center-item {
    .el-form-item__content {
      justify-content: center;
    }
  }
  .justify-start-item {
    .el-form-item__content {
      justify-content: start;
    }
  }

  .custom-input {
    width: 100%;
    .el-input__inner {
      background: #f8fafe;
      border: none;
      height: 40px;
      border-radius: 4px;
    }
  }

  .code-button {
    min-width: 90px;
    position: absolute;
    right: 5px;
    top: 4px;
    background: rgba(9, 126, 252, 0.1);
    color: #097efc;
    border: none;
  }

  .el-checkbox:last-of-type {
    margin-right: 10px;
  }

  .login-button {
    margin-top: 10px;
    padding: 15px;
    width: 100%;
  }
  .custom-head {
    // padding-top: 50px;
    .logo-text {
      font-size: 28px;
      color: #097efc;
    }
  }
}

:deep(.el-form-item--default) {
  margin-bottom: 25px !important;
}
:deep(.el-input__wrapper) {
  background: #f8fafe;
}
</style>
