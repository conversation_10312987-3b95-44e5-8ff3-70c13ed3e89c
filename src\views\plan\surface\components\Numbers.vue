<template>
  <div class="number-wrapper bg-light-blue">
    <div class="title">
      {{ data.title }} ( <span class="unit"> {{ data.unit }} </span> )
    </div>
    <div class="header">
      <div class="left">
        <span
          v-for="(decrement, index) in data.range"
          :key="'decrease-' + index"
          class="uranus-btn"
          :class="{
            uranusBtnDisabled: data.value - decrement < data.min,
            notAllowed: data.value - decrement < data.min
          }"
          @click="onDecrease(decrement)"
        >
          -{{ decrement }}
        </span>
      </div>
      <div class="middle">
        <el-input-number
          :controls="false"
          class="input-number"
          type="number"
          size="small"
          v-model="data.value"
          :max="data.max"
          :min="data.min"
          :precision="computedPrecision"
          :step="data.step"
          @change="onInputChange"
        ></el-input-number>
      </div>
      <div class="right">
        <span
          v-for="(increment, index) in sortedRange"
          :key="'increase-' + index"
          class="uranus-btn"
          :class="{
            uranusBtnDisabled: data.value + increment > data.max,
            notAllowed: data.value + increment > data.max
          }"
          @click="onAdd(increment)"
        >
          +{{ increment }}
        </span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'Numbers'
};
</script>
<script setup>
import '../style/common.css';
import { onMounted, onUnmounted, computed, watch } from 'vue';
//#region 数据双向绑定
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
// const data = {
//   title: '间隔距离',
//   unit: 'm',
//   value: 0,
//   min:1,
//   max:900,
//   range:[0.1,1],
// step: 0.1
// };
const data = computed({
  get: () => props.modelValue,
  set: value => {
    emits('update:modelValue', value);
  }
});

const computedPrecision = computed(() => {
  return data.value.step < 1 ? 1 : 0;
});

// 对外定义事件
const emits = defineEmits(['update:modelValue', 'changeHandle']); // 触发事件
// 新增的输入变化处理函数
const onInputChange = value => {
  const { min, max } = props.modelValue;
  let newValue = Number(value);
  if (newValue <= min) {
    newValue = min;
  }
  if (newValue >= max) {
    newValue = max;
  }
  data.value.value = newValue;
  emits('changeHandle', newValue);
};

const onDecrease = v => {
  const newValue = Number(data.value.value) - Number(v);
  onInputChange(newValue);
};

const onAdd = v => {
  const newValue = Number(data.value.value) + Number(v);
  onInputChange(newValue);
};

// 监听 parmsData.value.autoFlightSpeed 的变化并同步更新 flightSpeedDataRect.value
watch(
  () => data.value.value,
  newVal => {}
);
const decreaseVisible = computed(() => {
  return data.value.value <= data.value.min;
});
const addVisible = computed(() => {
  return data.value.value >= data.value.max;
});
const sortedRange = computed(() => {
  return data.value.range.slice().sort((a, b) => a - b);
});
//#endregion
onMounted(() => {});
onUnmounted(() => {});
</script>
<style lang="scss" scoped>
::v-deep.el-input {
  background-color: #313131;
  color: white;
  width: 40px;
}

::v-deep.el-input-number .el-input__inner {
  background-color: #11253e;
  color: white;
  width: 40px;
  font-size: 20px !important;
  text-align: center;
  color: #2d8cf0 !important;
  font-weight: 600;
}

::v-deep.el-input-number .el-input__wrapper {
  background-color: #11253e;
  box-shadow: 0 0 0 1px #cfcfcf8f inset;
}

::v-deep.el-input-number.is-without-controls .el-input__wrapper {
  padding-left: 8px !important;
  padding-right: 8px !important;
}

.number-wrapper {
  background-color: #232323;
  color: white;
  padding: 5px;
  width: 100%;
  user-select: none;
  .header {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
    .left {
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }
    .right {
      display: flex;
      align-items: center;
    }
    .middle {
      display: flex;
      align-items: center;
      width: 80px;
      .input-number {
        font-size: 20px !important;
        height: 28px;
        line-height: 28px;
        text-align: center;
        color: #2d8cf0 !important;
        font-weight: 600;
      }
      .unit {
        width: 10px;
        margin-left: 5px;
      }
    }
  }

  .header-input {
    margin: 5px 0px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .btn {
      display: flex;
      align-items: center;
      margin-left: 10px;
    }
  }
}

.round {
  border: 5px;
  color: white;
}
.item {
  width: auto;
  padding: 2px 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  background: rgba(45, 140, 240, 0.35);
  color: #ffffff40;
  margin-left: 5px;
  user-select: none;
  &:hover {
    cursor: pointer;
  }
}
.active {
  cursor: pointer;
  text-decoration: none !important;
  background: #5d5f61;
}

.uranus-btn {
  background: #344054;
  color: #fff;
  margin: 2px 2px;
  padding: 2px 5px;
  user-select: none;
  cursor: pointer;
}

.uranus-btn:hover {
  cursor: pointer;
  text-decoration: none !important;
  background: #5d5f61;
}

.uranusBtnDisabled {
  cursor: not-allowed !important;
  color: hsla(0, 0%, 100%, 0.25);
}

.notAllowed {
  cursor: not-allowed !important;
  color: hsla(0, 0%, 100%, 0.527);
  background: #2a313f !important;
}

.color-blue {
  background: #2d8cf0;
  color: white;
}
</style>
