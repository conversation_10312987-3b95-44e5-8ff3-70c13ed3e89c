<template>
  <!-- <el-dialog
    title="告警信息"
    :model-value="visible"
    align-center
    
    width="1062"
  > -->
  <el-drawer :model-value="visible" title="告警信息" direction="rtl" @close="closeDialog" size="1260" :close-on-click-modal="false">
    <div class="left-content">
      <div style="display: flex; flex-direction: row; align-items: center">
        <div class="search-form" style="padding-top: 16px">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-position="right">
            <el-form-item label="" prop="message">
              <el-input
                class="input-serach"
                v-model="queryParams.message"
                placeholder="请输入告警内容"
                clearable
                style="width: 150px"
                @keyup.enter="handleSearch"
                maxlength="64"
              />
            </el-form-item>

            <el-form-item label="" prop="level">
              <el-select
                class="input-serach"
                v-model="queryParams.level"
                placeholder="请选择告警等级"
                clearable
                @change="handleSearch"
                style="width: 150px"
              >
                <el-option
                  v-for="(item, index) in optionData.hmsLevelOption"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="" prop="range">
              <el-date-picker
                class="input-serach"
                v-model="queryParams.range"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                @change="handleSearch"
                :disabled-date="disabledDate"
                style="width: 250px"
              />
            </el-form-item>
          </el-form>
        </div>
        <div style="display: flex; flex-direction: row; align-items: center">
          <el-button type="primary" @click="handleSearch()"><i-ep-search />搜索</el-button>
          <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
        </div>
      </div>

      <el-table highlight-current-row :data="dataList" stripe height="540">
        <el-table-column
          v-if="domain === DOMAIN.DOCK"
          label="告警时间"
          prop="create_time"
          width="200"
          show-overflow-tooltip
        >
          <template #default="scope">
            <div>
              <div>{{ scope.row.create_time }}</div>
              <div v-if="scope.row.update_time">{{ scope.row.update_time }}</div>

              <!-- <div v-else :style="{ color: HMS_COLOR[scope.row.level] }">正在发生...</div> -->
            </div>
          </template>
        </el-table-column>

        <el-table-column
          v-if="domain === DOMAIN.DRONE"
          label="告警时间"
          prop="create_time"
          width="200"
          show-overflow-tooltip
        />

        <el-table-column prop="level" label="告警等级" width="200" show-overflow-tooltip>
          <template #default="scope">
            <div class="flex-center">
              <div class="status" :style="{ backgroundColor: HMS_COLOR[scope.row.level] }"></div>
              <span>{{ optionData.hmsLevelOption.find(item => item.value === scope.row.level).label }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="错误码" prop="key" show-overflow-tooltip />
        <el-table-column label="告警内容" prop="message_zh" show-overflow-tooltip />
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </div>
  </el-drawer>
</template>
    
    <script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { getDevicesHms } from '@/api/devices';
import optionData from '@/utils/option-data';
import { HMS_COLOR } from '@/utils/constants';
import moment from 'moment';
import { DOMAIN } from '@/utils/constants';

const loading = ref(false);
const total = ref(0);
const queryFormRef = ref();
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  queryType: 2,
  message: ''
});

const dataList = ref([]);
const props = defineProps({
  domain: {
    type: String,
    default: DOMAIN.DOCK
  },
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  formData: {
    type: Object,
    default(rawProps) {
      return {};
    }
  }
});
const form = reactive({});
function disabledDate(time) {
  return new Date(time) > new Date();
}

/**
 * 查询
 */
function handleQuery() {
  let startTime = '';
  let endTime = '';
  if (queryParams.range && queryParams.range.length === 2) {
    startTime = new Date(moment(queryParams.range[0]).format('YYYY-MM-DD 00:00:00')).getTime();
    endTime = new Date(moment(queryParams.range[1]).format('YYYY-MM-DD 23:59:59')).getTime();
    // ;
  }
  getDevicesHms({
    device_sn: form.device_sn,
    page: queryParams.pageNum,
    page_size: queryParams.pageSize,
    begin_time: startTime,
    end_time: endTime,
    message: queryParams.message,
    level: queryParams.level
  }).then(data => {
    const { list, pagination } = data;
    dataList.value = list || [];
    total.value = pagination.total;
  });
}
function handleSearch() {
  queryParams.pageNum = 1;
  handleQuery();
}
/**
 * 重置查询
 */
function resetQuery(type = '') {
  queryParams.level = '';
  queryParams.message = '';
  queryParams.pageNum = 1;
  queryParams.pageSize = 10;
  queryParams.range = [];
  handleQuery();
}

watch(
  () => props.formData,
  (newVal, oldVal) => {
    if (!props.visible) return;
    Object.assign(form, newVal);
    handleSearch();
  },
  { deep: true }
);
const emit = defineEmits(['update:visible']);

// 关闭弹窗
function closeDialog() {
  queryFormRef.value.resetFields();
  emit('update:visible', false);
}

onMounted(() => {});
</script>
<style lang="scss" scoped>
.flex-center {
  display: flex;
  align-items: center;
  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
}
</style>
    