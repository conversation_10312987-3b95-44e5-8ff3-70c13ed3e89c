import * as turf from '@turf/turf';
import * as Cesium from 'cesium';
import { toNumber, isThreeDimensionalArray } from './common';
export const bufferPolygon = (polygonArr, margin) => {
  if (margin === 0) {
    return polygonArr;
  }
  // 注意：polygon首尾坐标要一致
  const polygon = turf.polygon(polygonArr, { name: 'poly1' }); // [[[x,y],[x,y],[x,y]...]]
  // 构建buffer 单位米
  const bufferedPolygon = turf.buffer(polygon, margin, { units: 'meters' });
  // 获取 坐标数组
  const poisArr = bufferedPolygon?.geometry.coordinates[0];
  return poisArr;
};

/**
 * 获取面积 传入经纬度二维数组
 * @param {*} polygonArr  [[[x,y],[x,y],[x,y]...]] 经纬度数组 
  这里要检查第一个点和最后一个点是否一样 如果不一样 会报错Error: First and last Position are not equivalent.
 * @returns
 */
export function getArea(polygonArr) {
  let result = {
    area: 0,
    unit: 'm²'
  };

  try {
    const polygon = turf.polygon(polygonArr); // [[[x,y],[x,y],[x,y]...]]
    const polygonArea = turf.area(polygon);
    if (polygonArea >= 10000) {
      // 如果面积大于等于 1,000,000 平方米，用平方公里表示
      const areaInSquareKilometers = (polygonArea / 1000000).toFixed(2);
      result.area = areaInSquareKilometers;
      result.unit = 'km²';
    } else {
      // 否则用平方米表示
      const areaInSquareMeters = polygonArea.toFixed(2);
      result.area = areaInSquareMeters;
      result.unit = 'm²';
    }
    return result;
  } catch (error) {
    return result;
  }
}

/**
 * 获取面积 传入经纬度二维数组
 * @param {*} polygonArr  [[[x,y],[x,y],[x,y]...]] 经纬度数组 
  这里要检查第一个点和最后一个点是否一样 如果不一样 会报错Error: First and last Position are not equivalent.
 * @returns
 */
export function getArea2(polygonArr) {
  try {
    const polygon = turf.polygon(polygonArr); // [[[x,y],[x,y],[x,y]...]]
    const polygonArea = turf.area(polygon);
    return polygonArea;
  } catch (error) {
    return 0;
  }
}
/**
 * 获取面积 传入经纬度二维数组
 * @param {*} polygonArr  [[[x,y],[x,y],[x,y]...]] 经纬度数组 
  这里要检查第一个点和最后一个点是否一样 如果不一样 会报错Error: First and last Position are not equivalent.
 * @returns
 */
export function getAreaWithUnit(polygonArr) {
  let result = {
    area: 0,
    unit: 'm²'
  };

  try {
    const polygon = turf.polygon(polygonArr); // [[[x,y],[x,y],[x,y]...]]
    const polygonArea = turf.area(polygon);
    if (polygonArea >= 10000) {
      // 如果面积大于等于 1,000,000 平方米，用平方公里表示
      const areaInSquareKilometers = (polygonArea / 1000000).toFixed(2);
      result.area = areaInSquareKilometers;
      result.unit = 'km²';
    } else {
      // 否则用平方米表示
      const areaInSquareMeters = polygonArea.toFixed(2);
      result.area = areaInSquareMeters;
      result.unit = 'm²';
    }
    return result;
  } catch (error) {
    return result;
  }
}

export function getCircleAreaAndPerimeter(center = [], radius = 0) {
  if (!radius || radius === 0 || !center || center.length !== 2) {
    throw new Error('计算圆面积的参数错误');
  }
  let polygonArea = 0;

  if (polygonArea >= 10000) {
    // 如果面积大于等于 1,000,000 平方米，用平方公里表示
    const areaInSquareKilometers = (polygonArea / 1000000).toFixed(2);
    // result = `${areaInSquareKilometers} km²`;
    result.area = areaInSquareKilometers;
    result.unit = 'km²';
  } else {
    // 否则用平方米表示
    const areaInSquareMeters = polygonArea.toFixed(2);
    // result = `${areaInSquareMeters} m²`;
    result.area = areaInSquareMeters;
    result.unit = 'm²';
  }
  return result;
}

export function getLength(lineArr) {
  let line = turf.lineString(lineArr, { units: 'kilometers' }); //[[x,y],[x,y],[x,y]...]
  // 注意：polygon首尾坐标要一致
  const length = turf.length(line); //
  let result = {
    length: 0,
    unit: 'm'
  };
  if (length >= 5) {
    // 如果长度大于等于1公里，用公里表示
    const lengthInKilometers = length.toFixed(2);
    result.length = lengthInKilometers;
    result.unit = 'km';
  } else {
    // 否则用米表示
    const lengthInMeters = (length * 1000).toFixed(2);
    // result = `${lengthInMeters} m`;
    result.length = lengthInMeters;
    result.unit = 'm';
  }
  return result;
}

/**
 *
 * @param {*} positions [[xyz],[xyz]]
 * @returns
 */
export function getCenter(positions) {
  try {
    if (!positions || positions.length === 0) {
      return null;
    }
    const features = positions.map(poi => turf.point(poi));
    const center = turf.center(turf.featureCollection(features));
    if (!center) {
      return null;
    }
    // 这里要判断是否有poi[2]

    const heights = positions.map(poi => poi[2]);
    const averageHeight = heights.reduce((sum, height) => sum + height, 0) / heights.length;
    const [longitude, latitude] = center.geometry.coordinates;
    return [toNumber(longitude, 7), toNumber(latitude, 7), toNumber(averageHeight, 3)];
  } catch (error) {
    return null;
  }
}

export function getCenter2(positions) {
  try {
    if (!positions) {
      return null;
    }
    if (positions && positions.length === 0) {
      return null;
    }
    let features = [];
    let hightList = [];
    positions.forEach(poi => {
      hightList.push(poi.height);
      features.push(turf.point([poi.lng, poi.lat, poi.height]));
    });
    if (features.length === 0) {
      return null;
    }
    let center = turf.center(turf.featureCollection(features));
    if (center) {
      const sum = hightList.reduce((accumulator, currentValue) => accumulator + currentValue, 0);
      const averageH = sum / hightList.length;
      let coordinates = center.geometry.coordinates; //[118.2344,24.553]
      let position = [coordinates[0], coordinates[1], averageH];
      return position;
    } else {
      return null;
    }
  } catch (error) {
    return null;
  }
}

/**
 * 经纬度转墨卡托
 * @param {object} latlng
 * @param {Number} latlng.lng
 * @param {Number} latlng.lat
 * @returns
 */
export const toMercator = latlng => {
  const { lng = 0, lat = 0 } = latlng;
  // 这里 如果坐标为NaN或0，则直接报错
  if (isNaN(lng) || isNaN(lat)) {
    throw new Error('Invalid coordinates');
  }

  if (lng === 0 && lat === 0) {
    throw new Error('Invalid coordinates');
  }

  let pt = turf.point([lng, lat]);
  let converted = turf.toMercator(pt);
  let coordinates = converted?.geometry.coordinates;
  let c2 = new Cesium.Cartesian2(coordinates[0], coordinates[1]);
  return (
    c2 || {
      x: 0,
      y: 0
    }
  );
};

/**
 * 墨卡托转经纬度
 * @param {Array} mercator [x,y]
 * @returns
 */
export const toWgs84 = mercator => {
  // 处理空或无效的输入
  if (!mercator || mercator.length === 0 || (mercator[0] === 0 && mercator[1] === 0)) {
    return { lng: 0, lat: 0, height: 0 };
  }
  let pt = turf.point(mercator); //[x,y]
  let converted = turf.toWgs84(pt);
  let coordinates = converted?.geometry.coordinates;
  return {
    lng: coordinates[0] || 0,
    lat: coordinates[1] || 0,
    height: 0
  };
};
