import { DRONE_ENUM_VALUE, DRONE_SUB_ENUM_VALUE } from '../../props';

//#region 获取主类型

export function isDroneEnumValueValid(enumValue) {
  // if (typeof enumValue !== 'string' || enumValue.trim() === '') {
  //   throw new Error('Invalid enumValue parameter: must be a non-empty string');
  // }
  if (
    (typeof enumValue !== 'string' && typeof enumValue !== 'number') ||
    (typeof enumValue === 'string' && enumValue.trim() === '')
  ) {
    throw new Error('Invalid enumValue parameter: must be a non-empty string or a number');
  }

  // Convert enumValue to a string if it is a number
  enumValue = typeof enumValue === 'number' ? enumValue.toString() : enumValue;
  const enumValueMatch = DRONE_ENUM_VALUE[enumValue];
  if (!enumValueMatch) {
    return false;
  }
  return true;
}

/**
 * 传入数字获取支持的设备数组
 * @param {*} enumValue 如67
 * @returns ['M30', 'M30T']
 */
export function getDroneEnumValueByDevicesCode(enumValue) {
  return DRONE_ENUM_VALUE[enumValue] || [];
}

/**
 * 传入设备类型获取设备枚举编号
 * @param {*} deviceType  如'M30'
 * @returns 返回 如67
 */
export function getDroneEnumValueByDeviceType(deviceType) {
  for (let enumValue in DRONE_ENUM_VALUE) {
    if (DRONE_ENUM_VALUE[enumValue].includes(deviceType)) {
      return parseInt(enumValue);
    }
  }
  return null;
}
//#endregion

//#region  获取子类型

/**
 * 获取子类型的设备编号
 * @param {*} mainType 飞行器机型主类型 如 67
 * @param {*} subType  飞行器机型子类型 如 0
 * @returns
 */
export function getDroneSubEnumValueBySubType(mainType, subType) {
  const subObj = DRONE_SUB_ENUM_VALUE[mainType] || null;
  if (!subObj) {
    return null;
  }

  if (subType === null || subType === undefined) {
    return null;
  }

  for (let keyValue in subObj) {
    if (keyValue === subType) {
      return subObj[keyValue];
    }
  }
  return null;
}

/**
 * 获取子类型的设备编号
 * @param {*} mainType 飞行器机型主类型 如 67
 * @param {*} subType  飞行器机型子类型 如 'M30T'
 * @returns 返回 1
 */
export function getDroneSubEnumValueByDeviceType(mainType, deviceType) {
  const subObj = DRONE_SUB_ENUM_VALUE[mainType] || null;
  if (!subObj) {
    return null;
  }

  if (deviceType === null || deviceType === undefined) {
    return null;
  }

  for (let keyValue in subObj) {
    if (subObj[keyValue] === deviceType) {
      return parseInt(keyValue);
    }
  }
  return null;
}

//#endregion
