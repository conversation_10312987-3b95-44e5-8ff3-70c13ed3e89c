import { onMounted, onUnmounted, ref } from 'vue';
import StompWebSocketService from '@/utils/websocket/StompWebSocketService';
import { useUserStoreHook } from '@/store/modules/user';

/**
 * STOMP WebSocket连接Hook
 * @param {Function} messageHandler - 消息处理函数
 * @param {Object} options - 配置选项
 * @param {string} options.path - WebSocket路径，默认为'api/v1/ws'
 * @param {boolean} options.debug - 是否启用调试，默认为false
 * @param {number} options.heartbeatOutgoing - 发送心跳间隔(ms)，默认为30000
 * @param {number} options.heartbeatIncoming - 接收心跳间隔(ms)，默认为30000
 * @param {number} options.reconnectDelay - 重连延迟(ms)，默认为5000
 * @param {number} options.maxReconnectAttempts - 最大重连次数，默认为10
 * @param {boolean} options.secure - 是否使用安全连接(wss)，默认根据当前页面协议决定
 * 
 * @returns {Object} - 返回WebSocket相关方法和状态
 * 
 * @example
 * ```
 * import { useStompWebSocket } from '@/hooks/useStompWebSocket';
 * 
 * const { isConnected, send, subscribe, unsubscribe } = useStompWebSocket(
 *   (message) => {
 *     // 处理接收到的消息
 *     console.log('收到消息:', message);
 *   }
 * );
 * 
 * // 发送消息
 * send('/app/some/destination', { data: 'example' });
 * 
 * // 订阅主题
 * const subId = subscribe('/topic/updates', (data) => {
 *   console.log('订阅更新:', data);
 * });
 * 
 * // 取消订阅
 * unsubscribe(subId);
 * ```
 */
export function useStompWebSocket(messageHandler, options = {}) {
  const userStore = useUserStoreHook();
  const isConnected = ref(false);
  const subscriptions = ref([]);
  
  // 默认配置
  const defaultOptions = {
    path: 'api/v1/ws',
    debug: false,
    heartbeatOutgoing: 10000,
    heartbeatIncoming: 10000,
    reconnectDelay: 5000,
    maxReconnectAttempts: 10,
    secure: window.location.protocol === 'https:'
  };
  
  // 合并选项
  const finalOptions = { ...defaultOptions, ...options }; 
  // 构建WebSocket URL
  let baseUrl = '';
  if (import.meta.env.VITE_APP_NODE_ENV === 'development') {
    baseUrl = import.meta.env.VITE_WS_BASE_URL || ''; 
  } else {
    baseUrl = location.host + (import.meta.env.VITE_APP_BASE_API || '') + '/';
  }
  
  // 确定协议
  const protocol = finalOptions.secure ? 'wss:' : 'ws:';
  // 如果baseUrl已包含协议，则移除
  if (baseUrl.startsWith('http://') || baseUrl.startsWith('https://')) {
    baseUrl = baseUrl.replace(/^https?:\/\//, '');
  }
  // 构建完整的WebSocket URL
  const wsUrl = `${protocol}//${baseUrl}${finalOptions.path}?x-auth-token=${userStore.token}`;
  
  // 初始化连接
  function initConnection() {
    try {
      console.log('正在初始化STOMP WebSocket连接:', wsUrl);
      
      // 注册连接成功处理器
      StompWebSocketService.registerHandler('connect', () => {
        console.log('STOMP WebSocket连接成功');
        isConnected.value = true;
      });
      
      // 注册连接断开处理器
      StompWebSocketService.registerHandler('disconnect', () => {
        console.log('STOMP WebSocket连接断开');
        isConnected.value = false;
      });
      
      // 注册错误处理器
      StompWebSocketService.registerHandler('error', (error) => {
        console.error('STOMP WebSocket错误:', error);
        isConnected.value = false;
      });
      
      // 注册消息处理器
      if (typeof messageHandler === 'function') {
        StompWebSocketService.registerHandler('message', messageHandler);
      }
      
      // 初始化STOMP连接
      StompWebSocketService.init(wsUrl, { 
        'deviceType': 'pc',
        'x-auth-token': userStore.token,
        'user_id': userStore.userData.user_id
      }, {
        debug: finalOptions.debug,
        heartbeatOutgoing: finalOptions.heartbeatOutgoing,
        heartbeatIncoming: finalOptions.heartbeatIncoming,
        reconnectDelay: finalOptions.reconnectDelay,
        maxReconnectAttempts: finalOptions.maxReconnectAttempts
      });
    } catch (error) {
      console.error('初始化STOMP WebSocket连接时出错:', error);
    }
  }
  
  // 发送消息
  function send(destination, body = {}, headers = {}) {
    return StompWebSocketService.send(destination, body, headers);
  }
  
  // 订阅主题
  function subscribe(topic, callback) {
    const subscriptionId = StompWebSocketService.subscribe(topic, callback);
    if (subscriptionId) {
      subscriptions.value.push(subscriptionId);
    }
    return subscriptionId;
  }
  
  // 取消订阅
  function unsubscribe(subscriptionId) {
    const result = StompWebSocketService.unsubscribe(subscriptionId);
    if (result) {
      subscriptions.value = subscriptions.value.filter(id => id !== subscriptionId);
    }
    return result;
  }
  
  // 断开连接
  function disconnect() {
    try {
      console.log('正在断开STOMP WebSocket连接...');
      
      // 清除所有订阅
      subscriptions.value.forEach(id => {
        StompWebSocketService.unsubscribe(id);
      });
      subscriptions.value = [];
      
      // 移除消息处理器
      if (typeof messageHandler === 'function') {
        StompWebSocketService.removeHandler('message', messageHandler);
      }
      
      // 移除其他处理器
      StompWebSocketService.removeHandler('connect', () => {});
      StompWebSocketService.removeHandler('disconnect', () => {});
      StompWebSocketService.removeHandler('error', () => {});
      
      // 断开连接
      if (isConnected.value) {
        StompWebSocketService.disconnect();
        isConnected.value = false;
        console.log('STOMP WebSocket连接已断开');
      }
    } catch (error) {
      console.error('断开STOMP WebSocket连接时出错:', error);
    }
  }
  
  // 重新连接
  function reconnect() {
    console.log('手动重新连接STOMP WebSocket');
    
    // 先断开现有连接
    disconnect();
    
    // 延迟一秒后重新初始化连接
    setTimeout(() => {
      initConnection();
    }, 1000);
    
    return true;
  }
  
  // 获取连接状态
  function getConnectionStatus() {
    return {
      isConnected: isConnected.value,
      subscriptions: subscriptions.value.length
    };
  }

  // 处理页面刷新或关闭时的WebSocket断开连接
  function handleBeforeUnload(event) {
    console.log('页面即将刷新或关闭，正在断开WebSocket连接...');
    
    // 尝试发送离线状态
    try {
      if (isConnected.value) {
        // 发送离线状态消息
        StompWebSocketService.send('/app/user/status/offline', {
          deviceType: 'pc',
          timestamp: Date.now()
        });
      }
    } catch (error) {
      console.error('发送离线状态消息失败:', error);
    }
    
    // 立即断开连接
    disconnect();
    
    // 在某些浏览器中，可能需要延迟页面卸载以确保消息发送完成
    // 但现代浏览器通常不允许这样做，所以这只是一个尝试
    // const now = Date.now();
    // while (Date.now() - now < 200) {
    //   // 尝试延迟页面卸载
    // }
  }
  
  onMounted(() => {
    initConnection();
    
    // 添加页面刷新/关闭事件监听
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    // 添加页面卸载事件监听（作为备用）
    window.addEventListener('unload', handleBeforeUnload);
    
    // 添加页面可见性变化监听（处理标签页切换）
    document.addEventListener('visibilitychange', handleVisibilityChange);
  });
  
  onUnmounted(() => {
    // 移除页面刷新/关闭事件监听
    window.removeEventListener('beforeunload', handleBeforeUnload);
    window.removeEventListener('unload', handleBeforeUnload);
    document.removeEventListener('visibilitychange', handleVisibilityChange);
    
    disconnect();
  });
  
  // 处理页面可见性变化
  function handleVisibilityChange() {
    if (document.visibilityState === 'hidden') {
      // 页面不可见（可能是切换到其他标签页或最小化）
      console.log('页面不可见，发送用户状态更新');
      try {
        if (isConnected.value) {
          StompWebSocketService.send('/app/user/status/update', {
            deviceType: 'pc',
            timestamp: Date.now(),
            status: 'away'
          });
        }
      } catch (error) {
        console.error('发送状态更新失败:', error);
      }
    } else if (document.visibilityState === 'visible') {
      // 页面重新可见
      console.log('页面重新可见，发送用户状态更新');
      try {
        if (isConnected.value) {
          StompWebSocketService.send('/app/user/status/update', {
            deviceType: 'pc',
            timestamp: Date.now(),
            status: 'online'
          });
        } else {
          // 如果连接已断开，尝试重新连接
          reconnect();
        }
      } catch (error) {
        console.error('发送状态更新失败:', error);
      }
    }
  }
  
  return {
    isConnected,
    send,
    subscribe,
    unsubscribe,
    disconnect,
    reconnect,
    getConnectionStatus
  };
} 