// 航面转为航线方法
import { generateCoordinates } from './helper';
/**
 * 航面转为航线方法
 * @returns
 */
export function generateWayLine() {
  function Util() {}
  /**
   * 设置并验证选项，然后根据给定参数创建并返回一个旋转后的多边形坐标数组。
   * @param {Object} opt - 配置对象，包含以下属性：
   *   - {Array} polygon - 多边形坐标数组，每个元素为{lat:Number,lng:Number}。
   *   - {Number} [rotate=0] - 旋转角度（默认0）。
   *   - {Number} [space=10] - 纬线间隔距离（默认10）。
   * @returns {Array} 旋转处理后的新多边形坐标数组。
   * @throws {Error} 当输入的polygon不是数组，或者rotate、space不是数字时抛出错误。
   * @throws {Error} 如果未先调用.setLatlng2PxFn和.setPx2LatlngFn方法，则抛出错误。
   */
  function setOptions(opt) {
    // 检查polygon是否为数组类型
    if (!(opt.polygon instanceof Array)) {
      throw new Error('attention: the "polygon" of options must be a Array like [{lat:Number,lng:Number}]');
    }
    // 检查rotate是否为数字
    if (opt.rotate && typeof opt.rotate !== 'number') {
      throw new Error('attention: the "rotate" of options must be a number!');
    }
    // 检查space是否为数字
    if (opt.space && typeof opt.space !== 'number') {
      throw new Error('attention: the "space" of options must be a number!');
    }

    // 创建原始多边形的边界
    let bounds = createPolygonBounds(opt.polygon);
    // 根据旋转角度创建旋转后的多边形
    let rPolygon = createRotatePolygon(opt.polygon, bounds, -opt.rotate || 0);

    // 检查旋转多边形是否有效
    if (!rPolygon) {
      throw new Error('attention: You must call ".setLatlng2PxFn" and ".setPx2LatlngFn" methods before setOptions ');
    }
    if (rPolygon.length === 0) {
      throw new Error('attention: rPolygon.length === 0 ');
    }
    // 获取旋转多边形的边界
    let rBounds = createPolygonBounds(rPolygon);
    // 根据边界和间距创建等距纬线  这里需要优化等距纬线
    let latline = createLats(rBounds, opt.space || 10);

    // 初始化辅助变量
    let line = [],
      polyline = [],
      check = null;

    // 遍历纬线，生成旋转多边形的平行线
    for (let i = 0; i < latline.len; i++) {
      line = [];
      for (let j = 0; j < rPolygon.length; j++) {
        let nt = si(j + 1, rPolygon.length); // 计算下一个顶点索引
        check = createInlinePoint(
          // 计算交点
          [rPolygon[j].lng, rPolygon[j].lat],
          [rPolygon[nt].lng, rPolygon[nt].lat],
          latline.lat[i]
          // rBounds.northLat - i * latline.lat
        );
        if (check) {
          line.push(check);
        }
      }

      // 跳过无效的线段
      if (line.length < 2) continue;
      // 根据纬线的奇偶性调整点的顺序，并添加到结果多边形中
      if (i % 2) {
        polyline.push(
          { lat: line[0][1], lng: Math.max(line[0][0], line[1][0]) },
          { lat: line[0][1], lng: Math.min(line[0][0], line[1][0]) }
        );
      } else {
        polyline.push(
          { lat: line[0][1], lng: Math.min(line[0][0], line[1][0]) },
          { lat: line[0][1], lng: Math.max(line[0][0], line[1][0]) }
        );
      }
    }
    // 最终返回根据原始边界和旋转角度恢复旋转的多边形坐标数组
    return createRotatePolygon(polyline, bounds, opt.rotate || 0);
  }

  function setDistanceFn(fn) {
    if (typeof fn !== 'function') {
      throw new Error("setDistanceFn's argument must be a function");
      return;
    }
    Util.prototype.distance = fn;
  }

  function setLatlng2PxFn(fn) {
    if (typeof fn !== 'function') {
      throw new Error("setLatlng2PxFn's argument must be a function");
      return;
    }
    Util.prototype.toMercator = fn;
  }

  function setPx2LatlngFn(fn) {
    if (typeof fn !== 'function') {
      throw new Error("setPx2LatlngFn's argument must be a function");
      return;
    }
    Util.prototype.toWgs84 = fn;
  }

  let U = new Util();

  function transform(x, y, tx, ty, deg, sx, sy) {
    deg = (deg * Math.PI) / 180;
    if (!sy) sy = 1;
    if (!sx) sx = 1;
    return [
      sx * ((x - tx) * Math.cos(deg) - (y - ty) * Math.sin(deg)) + tx,
      sy * ((x - tx) * Math.sin(deg) + (y - ty) * Math.cos(deg)) + ty
    ];
  }

  function si(i, l) {
    if (i > l - 1) {
      return i - l;
    }
    if (i < 0) {
      return l + i;
    }
    return i;
  }

  function createInlinePoint(p1, p2, y) {
    let s = p1[1] - p2[1];
    let x;
    if (s) {
      x = ((y - p1[1]) * (p1[0] - p2[0])) / s + p1[0];
    } else {
      return false;
    }
    if (x > p1[0] && x > p2[0]) {
      return false;
    }
    if (x < p1[0] && x < p2[0]) {
      return false;
    }
    return [x, y];
  }

  /**
   * 创建一个表示多边形边界的对象。
   * 该函数通过分析给定的经纬度坐标数组，计算出多边形的最小和最大纬度及经度，
   * 从而确定多边形的边界和中心点。
   *
   * @param {Array} latlngs - 包含多个经纬度坐标的数组。
   * @returns {Object} 返回一个对象，包含多边形的中心点、边界经纬度坐标数组和最北端的纬度。
   */
  function createPolygonBounds(latlngs) {
    // 初始化用于存储所有纬度和经度的数组
    let lats = [];
    let lngs = [];

    // 遍历经纬度坐标数组，提取并存储所有纬度和经度
    for (let i = 0; i < latlngs.length; i++) {
      lats.push(latlngs[i].lat);
      lngs.push(latlngs[i].lng);
    }

    // 计算所有纬度中的最大值和最小值
    let maxLat = Math.max.apply(Math, lats);
    let minLat = Math.min.apply(Math, lats);

    // 计算所有经度中的最大值和最小值
    let maxLng = Math.max.apply(Math, lngs);
    let minLng = Math.min.apply(Math, lngs);

    // 根据最大和最小纬度、经度计算多边形的中心点
    let center = {
      lat: (maxLat + minLat) / 2,
      lng: (maxLng + minLng) / 2
    };

    // 构造多边形的边界点数组，顺序为北边、东边、南边、西边
    latlngs = [
      {
        lat: maxLat,
        lng: minLng
      },
      {
        lat: maxLat,
        lng: maxLng
      },
      {
        lat: minLat,
        lng: maxLng
      },
      {
        lat: minLat,
        lng: minLng
      }
    ];

    // 返回包含中心点、边界点数组和最北端纬度的对象
    return {
      center: center,
      latlngs: latlngs,
      northLat: maxLat
    };
  }

  /**
   * 根据给定的经纬度坐标数组、边界对象和旋转角度，创建一个旋转后的多边形的经纬度坐标数组。
   * 这个函数首先检查是否存在将经纬度坐标转换为像素坐标和反之的函数，如果不存在则返回false。
   * 然后，它将每个经纬度坐标转换为像素坐标，应用旋转 transformation，再将结果转换回经纬度坐标。
   *
   * @param {Array} latlngs - 经纬度坐标数组，每个元素是一个包含latitude和longitude的对象。
   * @param {Object} bounds - 边界对象，包含中心点的经纬度坐标。
   * @param {Number} rotate - 旋转角度，以度为单位。
   * @return {Array} - 旋转后的经纬度坐标数组，如果转换过程中出错，则返回false。
   */
  function createRotatePolygon(latlngs, bounds, rotate) {
    // 检查是否存在必要的转换函数，如果不存在则返回false
    if (typeof U.toMercator !== 'function' && typeof U.toWgs84 !== 'function') {
      return false;
    }
    //  经纬度数组
    let res = [],
      a,
      b;
    let c = U.toMercator(bounds.center);
    try {
      // 遍历经纬度坐标数组，进行转换和旋转
      for (let i = 0; i < latlngs.length; i++) {
        a = U.toMercator(latlngs[i]);
        b = transform(a.x, a.y, c.x, c.y, rotate);
        res.push(U.toWgs84(b));
      }
    } catch (error) {
      console.log('error:', error);
    }
    return res;
  }

  function createLats(bounds, space) {
    let nw = bounds.latlngs[0]; // 最北经纬度
    let sw = bounds.latlngs[3]; // 最南经纬度
    if (typeof U.distance !== 'function') {
      throw new Error('You must call the ".setDistanceFn" method and set a function to calculate the distance!');
      return false;
    }
    return generateCoordinates(nw, sw, space);
  }

  function getPolygonArea(polygon) {
    let S = 0;
    for (let i = 0; i < polygon.length; i++) {
      S +=
        X(polygon[i]) * Y(polygon[si(i + 1, polygon.length)]) - Y(polygon[i]) * X(polygon[si(i + 1, polygon.length)]);
    }
    return Math.abs(S) / 2;

    function X(latlng) {
      return latlng.lng * lng2m(latlng);
    }

    function Y(latlng) {
      return latlng.lat * lat2m(latlng);
    }
  }

  function getPolylineArea(polyline, space) {
    let S = 0;
    space = space || 5;
    for (let i = 0; i < polyline.length; i += 2) {
      let j = si(i + 1, polyline.length);
      S += U.distance(polyline[i], polyline[j]);
    }
    return S * space * 2;
  }

  function lat2m(latlng) {
    return U.distance(latlng, {
      lat: latlng.lat + 1,
      lng: latlng.lng
    });
  }

  function lng2m(latlng) {
    return U.distance(latlng, {
      lat: latlng.lat,
      lng: latlng.lng + 1
    });
  }

  return {
    setOptions: setOptions,
    setDistanceFn: setDistanceFn,
    setLatlng2PxFn: setLatlng2PxFn,
    setPx2LatlngFn: setPx2LatlngFn,
    getPolygonArea: getPolygonArea,
    getPolylineArea: getPolylineArea
  };
}
