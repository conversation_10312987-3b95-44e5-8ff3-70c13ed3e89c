<script>
export default { name: 'DroneList' };
</script>

<script setup>
import { ref, reactive, toRaw } from 'vue';
import { getDevicesBound, homeCapacityList } from '@/api/devices';
import {
  DOMAIN,
} from '@/utils/constants';
import { useDeviceStateStore } from '@/store/modules/deviceState.js';
import { useConnectWebSocket } from '@/hooks/useConnectWebSocket';
import { EBizCode } from '@/utils/constants';

const deviceStateStore = useDeviceStateStore();
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
});
const dataList = ref([]);
const pilotList = ref([]);
const nowOsdVisible = deviceStateStore.nowOsdVisible;
const total = ref(0);
const activeName = ref('drone'); //默认tabs
const deviceSn = ref('')
const emit = defineEmits(['onClick','select']);

watch(
  () => deviceStateStore.deviceState.dock,
  async val => {
    if (val) {
      // dataList.value = deviceStateStore.getDockBySn(nowOsdVisible.dock_sn);
    }
  },
  { deep: true }
);

/**
 * 查询
 */
 function handleQuery() {
  getDevicesBound({
    domain: DOMAIN.DOCK,
    page: queryParams.pageNum,
    page_size: queryParams.pageSize
  }).then(data => {
    const { list, pagination } = data;
    dataList.value = list || [];
    total.value = pagination.total;
  });
}

useConnectWebSocket(payload => {
  if (!payload) {
    return;
  }
  switch (payload.biz_code) {
    // 设备上下线
    case EBizCode.DeviceOnline: {
      // 上线
      handleQuery();
			getPilotList();
      break;
    };
		case EBizCode.DeviceOffline: {
      // 下线
			handleQuery();
			getPilotList();
      break;
    }
  }
});

function getPilotList () {
	getDevicesBound({
		pilot: true,
    domain: 0,
    page: queryParams.pageNum,
    page_size: queryParams.pageSize
  }).then(data => {
    const { list, pagination } = data;
		console.log('list',list)
    pilotList.value = list || [];
  });
}

onMounted(() => {
	getPilotList();
  handleQuery();
});

function handleClick (type,item) {
	emit('onClick',type,item)
}

function locationAirport (item) {
	deviceSn.value = toRaw(item).device_sn
	emit('select',item)
}

function changeActive (tab,event) {
	console.log(tab,event)
}
</script>

<template>
	<div class="divide"></div>
	<el-tabs v-model="activeName" class="demo-tabs" @tab-click="changeActive">
		<el-tab-pane label="无人机列表" name="drone">
			<div class="alarm-ul">
				<el-scrollbar height="386px">
					<div class="alarm-item" v-for="(item,index) in dataList" :key="index">
						<div :class="deviceSn == item.device_sn ? ['flex','currentColor'] : 'flex'" @click="locationAirport(item)">
							<div><svg-icon icon-class="airfield" style="margin-right: 4;width: 14px;height: 14px" />{{ item.nickname }}</div>
							<div>
								<span :class="item.status ? 'green' :'grey'">{{ item.status ? '在线' : '离线' }}</span>
								<svg-icon class="pointer" icon-class="video_play" style="margin-left: 8" @click.stop="handleClick('airport',item)"/>
							</div>
						</div>
						<div :class="deviceSn == item?.children.device_sn ? ['flex','currentColor'] : 'flex'" @click="locationAirport(item?.children)">
							<div><svg-icon icon-class="drone" style="margin-right: 4" />{{ item?.children?.nickname || '' }}</div>
							<div>
								<span :class="item.children.status ? 'green' :'grey'">{{ item.children.status ? '在线' : '离线' }}</span>
								<svg-icon class="pointer" icon-class="video_play" style="margin-left: 8" @click.stop="handleClick('drone',item.children)"/>
							</div>
						</div>
					</div>
				</el-scrollbar>
			</div>
		</el-tab-pane>
    <el-tab-pane label="飞手列表" name="pilot">
			<div class="alarm-ul" v-if="pilotList.length >0">
				<el-scrollbar height="386px">
					<div class="alarm-item" v-for="(item,index) in pilotList" :key="index">
						<div :class="deviceSn == item.device_sn ? ['flex','currentColor'] : 'flex'" @click="locationAirport(item)">
							<div><svg-icon icon-class="drone" style="margin-right: 4;width: 14px;height: 14px" />{{ item.nickname }}</div>
							<div>
								<span :class="item.status ? 'green' :'grey'">{{ item.status ? '在线' : '离线' }}</span>
								<svg-icon class="pointer" icon-class="video_play" style="margin-left: 8" @click.stop="handleClick('pilot',item)"/>
							</div>
						</div>
					</div>
				</el-scrollbar>
			</div>
			<el-empty description="暂无数据" image-size="100" v-else/>
		</el-tab-pane>
	</el-tabs>
</template>

<style lang="scss" scoped>
:deep(.el-tabs) {
	height: 98%;
}
.demo-tabs {
	height: 98%;
}
:deep(.el-tabs__content) {
	height: 100%;
}
:deep(.el-tab-pane) {
	height: 100%;
}
:deep(.el-tabs__header) {
	margin-bottom: 0;
}
.right-icon{
	margin-top: 5px;
	display: flex;
}
.green {
	display: inline-block;
	background: rgba(42,139,125,0.30);
	color: #39BFA4;
	font-weight: 400;
	font-family: SourceHanSansSC-Regular;
	font-size: 12px;
	line-height: 24px;
	height: 24px;
	text-align: center;
	padding: 0 4px;
}
.grey {
	display: inline-block;
	background: rgba($color: #98A2B3 , $alpha: 0.2);
	border-radius: 2px;
	text-align: center;
	line-height: 24px;
	height: 24px;
	font-weight: 400;
	padding: 0 4px;
	font-family: SourceHanSansSC-Regular;
	font-size: 12px;
	color: #98A2B3;
	text-align: center;
}
.divide {
	height: 8px;
	background: #001129;
}
.pointer {
	cursor: pointer;
}
.currentColor {
	background: #175091 !important;
}
:deep(.el-tabs__header) {
	border-bottom: 1px solid #344054;
}
:deep(.el-tabs__nav-wrap) {
	background: #11253E;
	color: #fff;
	height: 38px;
	line-height: 38px;
	font-family: SourceHanSansSC-Bold;
	font-size: 14px;
	text-align: left;
	font-weight: 700;
	border-bottom: 1px solid #344054;
	padding-left: 8px;
}
.alarm-title {
	height: 38px;
	line-height: 38px;
	background: #11253E;
	color: #fff;
	font-family: SourceHanSansSC-Bold;
	font-size: 14px;
	text-align: left;
	font-weight: 700;
	border-bottom: 1px solid #344054;
	padding-left: 8px;
}
.alarm-ul {
	background: #001129;
	// height: 386px;
	height: 100%;
	overflow: hidden;
	.alarm-item {
		min-height: 76px;
		background: #11253E;
		margin-bottom: 12px;
		padding: 8px;
		font-family: SourceHanSansSC-Regular;
		font-size: 14px;
		color: #F5F6F8;
		text-align: left;
		line-height: 22px;
		font-weight: 400;
		.list {
			height: 38px;
			line-height: 38px;
			vertical-align: middle
		}
		.alarm-time {
			font-family: SourceHanSansSC-Regular;
			font-size: 12px;
			color: #98A2B3;
			text-align: right;
			line-height: 20px;
			font-weight: 400;
		}
		.alarm-address{
			font-family: SourceHanSansSC-Regular;
			font-size: 14px;
			color: #FFFFFF;
			text-align: justify;
			line-height: 22px;
			font-weight: 400;
			margin-top: 8px;
			overflow: hidden;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			text-overflow: ellipsis;
			white-space: normal;
		}
	}
}
.flex {
	height: 38px;
	line-height: 38px;
	display: flex;
	justify-content: space-between;
}
</style>
