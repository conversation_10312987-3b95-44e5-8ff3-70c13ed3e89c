<script>
export default { name: 'dashboard' };
</script>

<script setup>
import BarChart from './components/BarChart.vue';
import Pie<PERSON>hart from './components/PieChart.vue';
import lineChart from './components/lineChart.vue';
import { getUavInfo, getFlyInfo } from '@/api/dashboard';

const tabType = ref('Num'); //Num展示飞行次数排行  Distance展示距离排行
const uavInfo = ref({});
const flyInfo = ref({});
//tab切换
function changeTab(typeName) {
  tabType.value = typeName;
}
//获取第一排统计信息
function getUavCount() {
  getUavInfo().then(res => {
    uavInfo.value = res;
  });
}
//获取第二排飞行统计
function getFlyCount() {
  getFlyInfo().then(res => {
    flyInfo.value = res;
  });
}
onMounted(() => {
  getUavCount();
  getFlyCount();
});
</script>

<template>
  <div class="dashboard-container">
    <!-- 第一部分 -->
    <el-row :gutter="24" class="mb15">
      <!-- 无人机数量（架） -->
      <el-col :span="6">
        <div class="first-card">
          <div class="first-card-l">
            <div class="first-card-text">无人机数量（架）</div>
            <div class="first-card-num">{{ uavInfo.droneCount ? uavInfo.droneCount : 0 }}</div>
          </div>
          <div class="first-card-r">
            <img class="first-card-img" src="/src/assets/dashborad/nav.png" alt="" srcset="" />
          </div>
        </div>
      </el-col>

      <!--飞手人数（人）-->
      <el-col :span="6">
        <div class="first-card">
          <div class="first-card-l">
            <div class="first-card-text">飞手人数（人）</div>
            <div class="first-card-num">{{ uavInfo.pilotCount ? uavInfo.pilotCount : 0 }}</div>
          </div>
          <div class="first-card-r">
            <img class="first-card-img" src="/src/assets/dashborad/naver.png" alt="" srcset="" />
          </div>
        </div>
      </el-col>
      <!--在线设备（台）-->
      <el-col :span="6">
        <div class="first-card">
          <div class="first-card-l">
            <div class="first-card-text">在线设备（台）</div>
            <div class="first-card-num">{{ uavInfo.onlineDevices ? uavInfo.onlineDevices : 0 }}</div>
          </div>
          <div class="first-card-r">
            <img class="first-card-img" src="/src/assets/dashborad/device.png" alt="" srcset="" />
          </div>
        </div>
      </el-col>
      <!--机场数量（个）-->
      <el-col :span="6">
        <div class="first-card">
          <div class="first-card-l">
            <div class="first-card-text">机场数量（个）</div>
            <div class="first-card-num">{{ uavInfo.dockCount ? uavInfo.dockCount : 0 }}</div>
          </div>
          <div class="first-card-r">
            <img class="first-card-img" src="/src/assets/dashborad/airport.png" alt="" srcset="" />
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 第二部分 -->
    <el-row :gutter="24">
      <el-col :span="6" class="mb15">
        <div class="block-card">
          <div class="block-title">
            <img class="title-img" src="/src/assets/dashborad/title.png" />
            <span class="title-text">飞行统计</span>
          </div>
          <div class="block-content">
            <div class="block-count-item bg-distance">
              <div class="count-item-l">
                <img class="title-img" src="/src/assets/dashborad/flyDistance.png" /><span>飞行里程（千米）</span>
              </div>
              <div class="count-item-r">{{ flyInfo.total_flight_distance_km ? flyInfo.total_flight_distance_km : 0 }}</div>
            </div>
            <div class="block-count-item bg-time">
              <div class="count-item-l">
                <img class="title-img" src="/src/assets/dashborad/flyTime.png" /><span>飞行时间（小时）</span>
              </div>
              <div class="count-item-r">{{ flyInfo.total_flight_duration_hours ? flyInfo.total_flight_duration_hours : 0 }}</div>
            </div>
            <div class="block-count-item bg-num">
              <div class="count-item-l">
                <img class="title-img" src="/src/assets/dashborad/flyNum.png" /><span>飞行次数（次）</span>
              </div>
              <div class="count-item-r">{{ flyInfo.total_flight_count ? flyInfo.total_flight_count : 0 }}</div>
            </div>
            <div class="block-count-item bg-route">
              <div class="count-item-l">
                <img class="title-img" src="/src/assets/dashborad/flyRoute.png" /><span>飞行航线（条）</span>
              </div>
              <div class="count-item-r">{{ flyInfo.unique_route_count ? flyInfo.unique_route_count : 0 }}</div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="18" class="mb15">
        <div class="block-card">
          <div class="block-title">
            <img class="title-img" src="/src/assets/dashborad/title.png" />
            <span class="title-text">飞手排行</span>
            <div class="tabs">
              <div class="tab-item" :class="tabType == 'Num' ? 'tab-item-active' : ''" @click="changeTab('Num')">
                次数
              </div>
              <div
                class="tab-item"
                :class="tabType == 'Distance' ? 'tab-item-active' : ''"
                @click="changeTab('Distance')"
              >
                距离
              </div>
            </div>
          </div>
          <BarChart id="barChart" :type="tabType" height="260px" width="100%" />
        </div>
      </el-col>
    </el-row>

    <!-- 第三部分 -->
    <el-row :gutter="24">
      <el-col :span="6" class="mb15">
        <div class="block-card">
          <div class="block-title">
            <img class="title-img" src="/src/assets/dashborad/title.png" />
            <span class="title-text">飞行次数</span>
          </div>
          <PieChart id="pieChart" height="260px" width="100%" />
        </div>
      </el-col>

      <el-col :span="18" class="mb15">
        <div class="block-card">
          <div class="block-title">
            <img class="title-img" src="/src/assets/dashborad/title.png" />
            <span class="title-text">飞行里程</span>
          </div>
          <lineChart id="lineChart" height="260px" width="100%" />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-empty__image) {
  width: 120px;
}
.dashboard-container {
  padding: 24px;
  position: relative;
  background-color: #001129;
  height: auto;
  .user-avatar {
    height: 40px;
    width: 40px;
    border-radius: 50%;
  }

  .data-box {
    font-weight: bold;
    padding: 20px;
    color: var(--el-text-color-regular);
    background: var(--el-bg-color-overlay);
    box-shadow: var(--el-box-shadow-dark);
    border-color: var(--el-border-color);
    display: flex;
    justify-content: space-between;
  }
  .mb15 {
    margin-bottom: 15px;
  }
  .first-card {
    height: 128px;
    background: #11253e;
    border-radius: 8px;
    display: flex;
    padding: 24px 36px;
    justify-content: space-between;
    align-items: center;
  }
  .first-card-l {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 90px;
  }
  .first-card-text {
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;
    color: #d0d5dd;
    text-align: left;
    line-height: 22px;
    font-weight: 400;
  }
  .first-card-num {
    font-family: SourceHanSansSC-Bold;
    font-size: 36px;
    color: #ffffff;
    line-height: 44px;
    font-weight: 700;
  }
  .first-card-r {
    width: 90px;
    height: 90px;
  }
  .first-card-img {
    width: 100%;
  }
  .block-card {
    background: #11253e;
    height: 318px;
  }
  .block-title {
    height: 38px;
    position: relative;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #344054;
    padding: 0 6px;
    .title-img {
      width: 16px;
      height: 16px;
    }
    .title-text {
      font-size: 14px;
      color: #ffffff;
      text-align: left;
      line-height: 22px;
      font-weight: 700;
      margin-left: 8px;
    }
  }
  .block-content {
    padding: 20px 24px;
    .bg-distance {
      background: rgba(46, 144, 250, 0.25);
    }
    .bg-time {
      background: rgba(57, 191, 164, 0.25);
    }
    .bg-num {
      background: rgba(253, 176, 34, 0.25);
    }
    .bg-route {
      background: rgba(249, 112, 102, 0.25);
    }
  }
  .block-count-item {
    height: 46px;
    width: 100%;
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .count-item-l {
      display: flex;
      align-items: center;
    }
    .count-item-r {
      margin-right: 20px;
      font-size: 20px;
      color: #ffffff;
      font-weight: 700;
    }
  }
  .count-item-l img {
    width: 24px;
    height: 24px;
    margin-left: 20px;
  }
  .count-item-l span {
    font-size: 14px;
    color: #ffffff;
    margin-left: 16px;
  }
  .tabs {
    position: absolute;
    right: 8px;
    display: flex;
  }
  .tab-item {
    margin-right: 32px;
    font-size: 14px;
    color: #475467;
    text-align: center;
    line-height: 22px;
    font-weight: 400;
    height: 38px;
    line-height: 38px;
    cursor: pointer;
    width: 28px;
  }
  .tab-item-active {
    color: #2e90fa;
    border-bottom: 2px solid #2e90fa;
  }
}
</style>
