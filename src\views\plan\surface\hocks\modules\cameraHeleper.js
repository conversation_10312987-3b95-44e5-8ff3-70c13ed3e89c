import { toNumber } from '@/components/Cesium/libs/cesium';
import { DJ_WRJ_SPEED, DJ_WRJ_MAX_SPEED } from '../../props/config';
import { useDeviceStore } from '@/store/modules/device.js';
const deviceStore = useDeviceStore();
/**
 * 计算被激活的相机个数
 * @param {*} arr
 * @returns
 */
export function checkActiveCount(arr = []) {
  let activeCount = 0;
  for (let i = 0; i < arr.length; i++) {
    if (arr[i].active) {
      activeCount++;
    }
  }
  return activeCount > 1;
}

/**
 * 根据设备、相机和CMOS格式获取CMOS传感器数据
 * @param {Object} options - 查询条件
 * @param {string} options.camera - 相机类型: visible / ir /wide 等
 * @returns {Object|null} - CMOS数据对象,包含宽度、高度和实际焦距,如果未找到匹配则返回null
 */
export function getCmosData(options = null) {
  if (!options) {
    return;
  }
  if (!deviceStore.deviceAdapter) {
    return;
  }
  // 检查 deviceAdapter 对象是否存在 getCmosValue 方法
  let deviceAdapter = deviceStore.deviceAdapter;
  if (!deviceAdapter.getCmosValue) {
    return null;
  }
  let cmosList = deviceAdapter?.getCmosValue() ?? null;
  if (!cmosList) {
    return;
  }
  const { camera } = options;
  const cmos = cmosList.filter(item => item.cameraType === camera);
  if (!cmos) {
    return null;
  }
  if (cmos.length === 0) {
    console.log('未查询到相机信息');
    return null;
  }
  const data = {
    cameraType: cmos[0].cameraType,
    label: cmos[0].cameraLabel,
    format: cmos[0].format,
    width: cmos[0].sensorWidth,
    height: cmos[0].sensorHeight,
    focalLength: cmos[0].focalLength, // 真实焦距
    pixel: cmos[0].pixel, // 像元大小 um
    pixels: cmos[0].pixels, // 像素
    equivalentFocalLength: cmos[0].equivalentFocalLength, // 等效焦距
    pixels_width: cmos[0].pixels_width, // 像素宽度,
    pixels_height: cmos[0].pixels_height // 像素高度
  };
  return data;
}

// 本工具方法集合用于根据相机的画幅、等效焦距、相机高度计算当下的航向、旁向重叠率
// 非重叠距离公式
/**
 * 计算无人机拍摄过程中非重叠部分的距离  旁向、航向都可以通过该算法计算
 * @param {number} height - 无人机高度，单位米(m)
 * @param {number} frame - 画幅，单位毫米(mm)  4/3   1/1/32  1/2 ...
 * @param {number} focal - 焦距，单位毫米(mm)
 * @param {number} ratio - 重叠率，范围0-1之间
 * @returns {number} 非重叠部分的距离，单位米(m) 这里非重叠部分的距离就是航线之间的距离
 */
function getDistance(height, frame, focal, ratio) {
  // 如果焦距为0，则使用默认值24毫米
  focal = focal === 0 ? 24 : Number(focal);
  // 单位换成米
  focal /= 1000;
  frame /= 1000;
  // 计算拍摄到的距离
  const distance = (frame * Number(height)) / focal;
  // 计算重叠部分的距离
  const overlapDistance = Number(ratio) * distance;
  // 计算非重叠部分的距离
  const nonOverlapDistance = distance - overlapDistance;
  return nonOverlapDistance;
}

/**
 * 旁向重叠率 相机计算 根据相机高度及相关参数计算出相机的旁向重叠率
 * @param {*} height 相机高度 米
 * @param {*} frame 长画幅（24）mm
 * @param {*} focal 焦距 MM
 * @param {*} ratio 重叠率
 * @returns
 */
export function getSideOverlap(height = 100, frame = 35, focal, ratio = 80) {
  let p = getDistance(height, frame, focal, ratio / 100);
  return p;
}

/**
 * 航向重叠率 相机计算 根据相机高度及相关参数计算出相机的航向重叠率
 * @param {*} height 相机高度 米
 * @param {*} frame 短画幅（24）mm
 * @param {*} focal 焦距 MM
 * @param {*} ratio 重叠率
 * @returns
 */
export function getCourseOverlap(height = 100, frame = 24, focal, ratio = 80) {
  let p = getDistance(height, frame, focal, ratio / 100);
  return p;
}

/**
 * 根据当前的时间和距离获取速度
 * @returns
 */
export function getSpeedRange(dis) {
  const distance = Number(dis);
  if (distance > 0) {
    let speed = distance / DJ_WRJ_SPEED;
    if (speed >= DJ_WRJ_MAX_SPEED) {
      return DJ_WRJ_MAX_SPEED;
    }
    return toNumber(speed);
  }
  return 0;
}

/**
 * 根据给定的距离,计算无人机的拍摄时间间隔和速度
 * @param {number} dis - 无人机需要拍摄的距离(单位:米)
 * @returns {object} - 包含拍摄时间间隔`t`和速度`s`的对象
 */
export function calculateDroneSpeedAndTime(dis = 0) {
  // 速度范围
  const minSpeed = 2; // 最小速度 2 m/s
  const maxSpeed = 15; // 最大速度 15 m/s

  // 计算最小拍摄时间间隔
  let minInterval = dis / maxSpeed;
  if (minInterval < 2) {
    minInterval = 2; // 最小拍摄时间间隔不能小于2秒
  }
  // 计算最大拍摄时间间隔
  let maxInterval = dis / minSpeed;
  // 在速度范围内寻找合适的速度
  let speed = Math.max(minSpeed, Math.min(maxSpeed, dis / 2));
  let interval = dis / speed;

  return {
    t: toNumber(interval, 1), // 合适的时间间隔
    // s: toNumber(speed, 1), // 合适的速度
    maxSpeed: toNumber(Math.min(maxSpeed, dis / 2), 2)
  };
}
