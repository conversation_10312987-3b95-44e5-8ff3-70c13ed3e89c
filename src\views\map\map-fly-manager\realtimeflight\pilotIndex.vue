<template>
  <div class="map-100">
    <mainMapWidget ref="mainMapWidgetRef" />
    <div class="alarminfo" v-show="alarm_content !== '' && showAlarm">
      <img src="@/assets/gis/alarm.png" alt="" class="alarm-img" />
      <div class="alarm-content">{{ alarm_content }}</div>
    </div>
  </div>
  <SituationPlotting
    v-if="showFlag"
    :ffCesium="ffCesium"
    :plotDataArray="plotDataArray"
    :alarmId="nowAlarmId"
    useType="edit"
    style="position: absolute; top: 10px; right: 50px; width: 250px; background-color: white; z-index: 999"
  ></SituationPlotting>
</template>
<script>
export default {
  name: 'RealTimePilotFlyMap'
};
</script>
<script setup>
import { onMounted, onUnmounted, onBeforeUpdate, onBeforeUnmount, nextTick } from 'vue';
import * as Cesium from 'cesium';
import { getCesiumEngineInstance, setCameraLookAt } from '@/components/Cesium/libs/cesium';
import mainMapWidget from '../components/homeMainMap.vue';
import { updatePoi, createDockNamePopup } from '../homeMap/popup/popup';

//SituationPlotting参数与逻辑-start

import FFCesium from './SituationPlotting/FFCesium/core/index.js';
import SituationPlotting from './SituationPlotting/index.vue';
import { plotList, plotDelete } from '@/api/plot/index.js';
const props = defineProps({
  showAlarm: {
    type: Boolean,
    default: true
  }
});
let ffCesium = null;
const showFlag = ref(false);

let plotDataArray = [];
let nowAlarmId = null;
const initSituationPlotting = () => {
  if (!ffCesium) {
    const viewerTemp = getCesiumEngineInstance('homeMap-fly').viewer;
    ffCesium = new FFCesium('mainMapContainer', { viewer: viewerTemp });
    //取消双击选中跟踪对象
    var handler = new ffCesium.Cesium.ScreenSpaceEventHandler(ffCesium.viewer.scene.canvas);
    handler.setInputAction(function (event) {
      ffCesium.viewer.trackedEntity = undefined;
    }, ffCesium.Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);
  }
  plotDataArray = [];
  //删除数据
  plotList(nowAlarmId).then(res => {
    res.forEach(item => {
      item.plotObj = JSON.parse(item.plot_obj);
      plotDataArray.push(item);
    });
    showFlag.value = true;
  });
};
const mainMapWidgetRef = ref(null);
//SituationPlotting参数与逻辑-end
// 警情内容
const alarm_content = ref('');
// 定时器
let intervalTimer = null;
// 轮询当前出动消防车定时器
let car_intervalTimer = null;
const airModel = new URL('/resource/models/wrj2.glb', import.meta.url).href;
// 无人机列表
const navList = ref({});
// 首次定位
const firstToView = ref(true);

/**
 * 定位无人机
 * @param {*} nav 无人机实时OSD数据
 * @param {*} deviceInfo 无人机基础信息
 */
function setNavModel(nav, deviceInfo) {
  if (nav === undefined || nav === null) {
    // Todo 下线移出无人机
    return;
  }
  const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
  if (firstToView.value) {
    setTimeout(() => {
      setCameraLookAt(flyView, {
        lon: Number(nav.longitude),
        lat: Number(nav.latitude),
        height: Number(0),
        offsetH: 500,
        distance: 2000
      });
      firstToView.value = false;
    }, 200);
  }
  const position = Cesium.Cartesian3.fromDegrees(nav.longitude, nav.latitude, nav.height);
  let device_sn = deviceInfo.device_sn;
  // Todo 下线移出无人机
  if (nav.mode_code === 14) {
    console.log('飞机下线');
    if (navList.value[device_sn] !== null || navList.value[device_sn] !== undefined) {
      flyView.entities.remove(navList.value[device_sn]);
      navList.value[device_sn] = null;
    }
    return;
  }

  let hpr = new Cesium.HeadingPitchRoll(
    Cesium.Math.toRadians(nav.attitude_head),
    Cesium.Math.toRadians(nav.attitude_pitch),
    Cesium.Math.toRadians(nav.attitude_roll)
  );
  let orientation = Cesium.Transforms.headingPitchRollQuaternion(position, hpr);
  if (navList.value[device_sn] === null || navList.value[device_sn] === undefined) {
    deviceInfo.device_xyz = { x: nav.longitude, y: nav.latitude, z: nav.height };
    deviceInfo.airline_id = null;
    let navModel = flyView.entities.add({
      id: 'nav_' + deviceInfo.device_sn,
      name: deviceInfo[device_sn],
      position: position,
      orientation: orientation,
      model: {
        uri: airModel,
        minimumPixelSize: 64,
        maximumScale: 20000,
        scale: 0.15,
        incrementallyLoadTextures: true, // 加载模型后纹理是否可以绯续流入
        runAnimations: true, //是否启动模型中指定的gltf 动画
        clampAnimations: true, //指定 gltf 动画是否在没有关键帧的持续时间内保持最后一个姿势
        shadows: Cesium.ShadowMode.ENABLED,
        heightReference: Cesium.HeightReference.NONE
      }
    });
    navList.value[device_sn] = navModel;
  } else {
    navList.value[device_sn].position = position;
    navList.value[device_sn].orientation = orientation;
  }
}

// 暴露方法给父组件
defineExpose({
  setNavModel
});

onMounted(() => {
  intervalTimer = setInterval(() => {
    const flyView = getCesiumEngineInstance('homeMap-fly').viewer;
    updatePoi(flyView);
  }, 1000);
  setTimeout(() => {
    if (mainMapWidgetRef.value) {
      mainMapWidgetRef.value.contorlAirPortArea(false);
    }
  }, 500);
});
onUnmounted(() => {
  clearInterval(intervalTimer);
  if (car_intervalTimer) {
    clearInterval(car_intervalTimer);
  }
});
onBeforeUnmount(() => {});
</script>

<style lang="scss" scoped>
.map-100 {
  height: 100%;
  width: 100%;
  // position: relative;

  .tool {
    position: absolute;
    top: 50px;
    color: red;
    background-color: antiquewhite;
    z-index: 100;
  }

  .alarminfo {
    position: absolute;
    top: 56px;
    opacity: 0.7;
    background: #242424;
    width: calc(100% - 150px);
    height: 30px;
    left: 30px;
    display: flex;
    .alarm-img {
      width: 22px;
      height: 22px;
      margin: 5px;
    }
    .alarm-content {
      font-family: SourceHanSansSC-Regular;
      font-size: 14px;
      color: #ffffff;
      text-align: left;
      line-height: 30px;
      font-weight: 400;
    }
  }
}
</style>
