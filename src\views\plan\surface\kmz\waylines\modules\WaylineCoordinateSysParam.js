import { POSITIONING_TYPE_ENUM, HEIGHT_MODE_ENUM } from '@/views/plan/newplan/kmz/props';
class WaylineCoordinateSysParam {
  constructor(options = {}) {
    //  定义执行高度模式，这里设置为"WGS84"，意味着高度基于世界大地测量系统(WGS84)的海拔高度,它默认为 'WGS84'。
    this.wpml_coordinateMode = 'WGS84';
    this.wpml_heightMode = options.heightMode || HEIGHT_MODE_ENUM.EGM96;
    this.wpml_globalShootHeight = parseInt(options.globalShootHeight) || 0;
    // this.wpml_positioningType = parseInt(options.positioningType) || POSITIONING_TYPE_ENUM.GPS;
  }
  // setPositioningType(value) {
  //   this.wpml_positioningType = value;
  // }
  setCoordinateMode(value) {
    this.wpml_coordinateMode = value;
  }
  setGlobalShootHeight(value) {
    this.wpml_globalShootHeight = value;
  }
  setHeightMode(value) {
    this.wpml_heightMode = value;
  }
  setDroneSubEnumValue(value) {}
}
export { WaylineCoordinateSysParam };
