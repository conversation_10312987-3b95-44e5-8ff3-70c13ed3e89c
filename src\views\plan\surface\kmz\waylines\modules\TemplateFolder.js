import { generateKey } from '@/utils';
import { POSITIONING_TYPE_ENUM, TEMPLATE_TYPE_ENUM, HEIGHT_MODE_ENUM } from '@/views/plan/newplan/kmz/props';
import { WaylineCoordinateSysParam } from './WaylineCoordinateSysParam';
import { PayloadParam } from './PayloadParam';
import { CAMERA_TYPE_ENUM } from '@/config';

class TemplateFolder {
  constructor() {
    this.uuid = generateKey();
    // 航点集合
    this.Placemark = [];
  }
  init(options = {}) {
    this.wpml_templateType = options.templateType || TEMPLATE_TYPE_ENUM.mapping2d;
    this.wpml_templateId = options.templateId || 0;
    this.wpml_waylineCoordinateSysParam = new WaylineCoordinateSysParam({
      // positioningType: options.waylineCoordinateSysParam?.positioningType || POSITIONING_TYPE_ENUM.GPS,
      coordinateMode: 'WGS84', //WGS84：当前固定使用,WGS84坐标系
      globalShootHeight: options.waylineCoordinateSysParam?.globalShootHeight || 0,
      heightMode: options.waylineCoordinateSysParam?.heightMode || HEIGHT_MODE_ENUM.EGM96
    });
    // 初始化无人机信息，默认为空
    this.wpml_autoFlightSpeed = parseInt(options.autoFlightSpeed) || 10;
    this.wpml_payloadParam = new PayloadParam({
      payloadPositionIndex: options.payloadParam?.payloadPositionIndex || 0,
      imageFormat: options.payloadParam?.imageFormat || CAMERA_TYPE_ENUM.visable
    });
  }
  //#region

  // Getter methods
  getTemplateId() {
    return this.wpml_templateId;
  }
  getTemplateType() {
    return this.wpml_templateType;
  }

  getAutoFlightSpeed() {
    return this.wpml_autoFlightSpeed;
  }
  // 获取所有的 placemarks
  getPlacemark() {
    return this.Placemark;
  }

  setTemplateId(id) {
    this.wpml_templateId = id;
  }
  setTemplateType(v) {
    return (this.wpml_templateType = v);
  }
  setAutoFlightSpeed(speed) {
    this.wpml_autoFlightSpeed = parseInt(speed);
  }
  //#endregion
  addPlacemark(pm) {
    this.Placemark.push(pm);
  }

  updatePlacemark(index, newPlacemark) {
    this.Placemark[index] = newPlacemark;
  }
}

export { TemplateFolder };
