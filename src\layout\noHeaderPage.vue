<script setup>
import { computed } from 'vue';
import { AppMain } from './components/index';
import { useAppStore } from '@/store/modules/app';
import { useSettingsStore } from '@/store/modules/settings';
import { getDeptSysSetting } from '@/api/wayline';
const defaultLogoIcon = new URL('/resource/images/default-logo.jpeg', import.meta.url).href;;
 
const data = reactive({});
onMounted(() => {
  initData();
});
function initData() {
  getDeptSysSetting({}).then(res => {
    const { base_config = {} } = res;
    data.name = base_config.sys_name;
    data.logo = base_config.sys_logo_url;
  });
}
// 默认logo图片路径
function handleImageError() {
  data.logo = defaultLogoIcon;
}
const appStore = useAppStore();
const settingsStore = useSettingsStore();

const showTagsView = computed(() => settingsStore.tagsView);

const classObj = computed(() => ({
  openSidebar: appStore.sidebar.opened,
  withoutAnimation: appStore.sidebar.withoutAnimation,
  mobile: appStore.device === 'mobile'
}));

function handleOutsideClick() {
  appStore.closeSideBar(false);
}
</script>

<template>
  <div :class="classObj" class="app-wrapper">
    <!-- 手机设备侧边栏打开遮罩层 -->
    <div v-if="classObj.mobile && classObj.openSidebar" class="drawer-bg" @click="handleOutsideClick" />
    <div :class="{ hasTagsView: showTagsView }" class="main-container" style="margin-left: 0">
      <div class="navbar">
        <div class="flex logo">
          <el-image
            style="margin-left: 25px; width: 40px; height: 40px; margin-right: 10px"
            :src="data.logo"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            fit="cover"
            @error="handleImageError"
          />
          <span class="navbar-title">{{ data.name }}</span>
        </div>
      </div>
      <app-main style="height: calc(100% - 60px)" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.main-container {
  height: 100%;
  position: relative;
}

.app-wrapper {
  &:after {
    content: '';
    display: table;
    clear: both;
  }

  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}
.head-box {
  position: absolute;
  top: 0;
  left: 0;
  height: 102px;
  width: 100%;
  z-index: 1999;
}
.navbar {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 0 1px #0003;
  background-color: #11253e;
  padding-right: 10px;
  :deep(.el-switch__core) {
    border: 1px solid #fff;
  }
  .backstage {
    font-family: SourceHanSansSC-Regular;
    font-size: 16px;
    color: #ffffff;
    text-align: center;
    line-height: 24px;
    font-weight: 400;
    margin-right: 20px;
    cursor: pointer;
  }
  .navbar-title {
    font-family: SourceHanSansSC-Bold;
    font-size: 20px;
    color: #ffffff;
    text-align: left;
    line-height: 40px;
    font-weight: 700;
    margin-left: 16px;
  }
  .setting-container {
    display: flex;
    align-items: center;
    color: #fff;
  }

  .manual-icon {
    width: 21px;
    height: 21px;
  }
}
</style>
