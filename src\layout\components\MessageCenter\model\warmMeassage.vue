<!-- 消息中心 -->
<template>
  <div>
    <div v-loading="tableLoading">
      <el-scrollbar
        v-if="tablePage.length"
        ref="projectBarRef"
        class="message-content"
      >
        <div
          v-infinite-scroll="loadData"
          :infinite-scroll-disabled="isInfiniteScrollDisabled"
          :infinite-scroll-distance="10"
        >
          <div
            v-for="(item, index) in tablePage"
            :key="index"
            class="content-item"
          >
            <div class="content-item-left">
              <i-ep-bell-filled class="el-icon-message-solid message-icon" />
            </div>
            <div class="content-item-center">
              <div class="center-title">{{ item.warnTypeName || '' }}</div>
              <div class="center-content">
                {{ setText(item) }}
              </div>
              <div class="left-subtitle">{{ item.noticeTime }}</div>
            </div>
            <div class="content-item-right">
              <el-button
                class="handle-button"
                type="primary"
                @click="handleClick(item)"
              >
                {{ $t('page.handle') }}
              </el-button>
              <el-button class="handle-button" @click="seeInfo(item)">
                {{ $t('page.check') }}
              </el-button>
            </div>
          </div>
        </div>
      </el-scrollbar>
      <el-empty v-else />
    </div>
    <div v-if="tablePage.length" class="page-foot">
      <div class="header-handle" @click="batchHandle">
        <i-ep-setting class="el-icon-setting" />
        {{ $t('page.dialog.batching') }}
      </div>
    </div>
    <PopHandleMessage
      ref="popHandleMessageRef"
      @handleMessage="deleteMessage"
    />
    <PopBatchHandle
      ref="popBatchHandleRef"
      @beforeClose="resetTable"
      @deleteMessage="deleteMessage"
    />
    <PopMessageInfo
      ref="popMessageInfoRef"
      :message-type="1"
      @confirm="dialogConfirm"
    />
  </div>
</template>

<script setup>
import i18n from '@/lang';
import { ref, onMounted } from 'vue';

import PopHandleMessage from '../components/popHandleMessage.vue';
import PopBatchHandle from '../components/popBatchHandle.vue';
import PopMessageInfo from '../components/popMessageInfo.vue';

import { getWarnMessagePage } from '@/api/messageCenter';

const tablePage = ref([]);
const totalNum = ref('');
const tableLoading = ref(false);
const isInfiniteScrollDisabled = ref(false);
const total = ref(0);
const popHandleMessageRef = ref('popHandleMessageRef');
const popBatchHandleRef = ref('popBatchHandleRef');
const popMessageInfoRef = ref('popMessageInfoRef');
const projectBarRef = ref('projectBarRef');

const emit = defineEmits(['changeCount']);

const initFormData = () => ({
  pageSize: 10,
  pageNum: 1
});
const tableQueryData = ref(initFormData());

const resetTable = () => {
  tablePage.value = [];
  tableQueryData.value = initFormData();
  getTablePage();
};

const getTablePage = async () => {
  tableLoading.value = true;
  isInfiniteScrollDisabled.value = true;
  try {
    const res = await getWarnMessagePage(tableQueryData.value);
    tablePage.value = Object.assign(tablePage.value, res.data.records);
    total.value = res.data.total;
    totalNum.value = res.data.pages; // 总页码
    emit('changeCount');
    nextTick(() => {
      if (total.value) {
        closeTooltip();
      }
    });
    isInfiniteScrollDisabled.value = false;
  } catch (err) {
    console.log('获取分页信息错误', err);
  }
  tableLoading.value = false;
};

// 滚动加载
const loadData = () => {
  if (tableQueryData.value.pageNum < totalNum.value) {
    tableQueryData.value.pageNum++;
    getTablePage();
  }
};

// 单个处理
const handleClick = data => {
  popHandleMessageRef.value.handleOpen([data]);
};

// 批量处理
const batchHandle = () => {
  popBatchHandleRef.value.handleOpen();
};

// 弹窗关闭回调
// const closeDialog = () => {
//   $emit('beforeClose');
// };

// 单个删
const deleteMessage = ids => {
  tablePage.value = tablePage.value.filter(
    data => !ids.includes(data.noticeRecordId)
  );
  total.value = tablePage.value.length;
  totalNum.value = total.value / tableQueryData.value.pageSize; // 总页码
  emit('changeCount');
};

// 设置文字
const setText = item => {
  return `${i18n.global.t('page.dialog.deviceCode')}：${
    item.deviceCode || '——'
  } ，${i18n.global.t('page.dialog.alarmThreshold')}：${
    item.warnThreshold || '——'
  }， ${i18n.global.t('page.dialog.triggerValue')}：${
    item.triggerValue || '——'
  }，${i18n.global.t('page.dialog.pleaseHandle')}`;
};

// 查看
const seeInfo = data => {
  popMessageInfoRef.value.handleOpen({
    ...data,
    title: i18n.global.t('page.dialog.alarmMessage'),
    content: setText(data),
    confireText: i18n.global.t('page.dialog.toHandle')
  });
};

// 查看确定回调
const dialogConfirm = e => {
  handleClick(e);
};

const closeTooltip = () => {
  projectBarRef.value.wrapRef.onscroll = () => {
    const list = document.getElementsByClassName('el-tooltip__popper');
    if (list.length > 0) {
      list[list.length - 1].style.display = 'none';
    }
  };
};

onMounted(() => {
  getTablePage();
});
</script>

<style lang="scss" scoped>
:deep() {
  .el-scrollbar__view {
    height: 100%;
  }
}

:deep(.el-dialog__body) {
  padding-bottom: 0 !important;
}

.header-handle {
  font-size: 14px;
  color: #666666;
  line-height: 22px;
  font-weight: 400;
  cursor: pointer;
  width: max-content;

  &:hover {
    color: #097efc;
  }

  .el-icon-setting {
    color: #097efc;
    margin-right: 3px;
  }
}

.message-content {
  height: 455px;

  overflow-y: hidden;

  :deep(.el-scrollbar__wrap) {
    overflow-x: auto;
  }
}

.content-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border-bottom: 1px solid #ececec;

  .content-item-left {
    display: flex;
    align-items: center;
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background-color: rgba($color: #097efc, $alpha: 0.1);
    margin-right: 10px;

    .message-icon {
      font-size: 30px;
      color: #097efc;
      margin: 0 15px;
      width: 30px;
      height: 30px;
      color: #097efc;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .content-item-center {
    flex: 1;
    overflow: hidden;

    .center-title {
      font-size: 16px;
      color: #222222;
      font-weight: 600;
      margin-bottom: 7px;
    }

    .center-content {
      font-size: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .left-subtitle {
    line-height: 25px;
    color: #999999;
    margin-top: 2px;
  }

  .content-item-right {
    /* width: 150px; */
    margin-left: 10px;
  }
}
</style>
