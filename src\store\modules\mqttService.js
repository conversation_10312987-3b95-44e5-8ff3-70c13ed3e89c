import { defineStore } from 'pinia';
import { store } from '@/store';
import { getMqttInfo } from '@/api/auth';
import { UranusMqtt } from '@/utils/mqtt';

// setup
export const useMqttStore = defineStore('mqtt', () => {
  // state
  const mqttState = ref(null); // mqtt连接实例
  // actions
  
  function initMqtt() {
    if (mqttState.value) return
    getMqttInfo({}).then(result => {
      const { address, client_id, username, password, expire_time } = result;
      // @TODO: 校验 expire_time
      mqttState.value = new UranusMqtt(address, {
        clientId: client_id,
        username,
        password
      });
      mqttState.value?.initMqtt();
      mqttState.value?.on('onStatus', statusOptions => {
        // @TODO: 异常case
      });
    });
  }
  function destroyedMqtt() {
    if (mqttState.value) {
      mqttState.value?.destroyed();
      mqttState.value = null;
    }
  }
  return { mqttState, initMqtt,destroyedMqtt };
});

// 非setup
export function useMqttStoreHook() {
  return useMqttStore(store);
}
