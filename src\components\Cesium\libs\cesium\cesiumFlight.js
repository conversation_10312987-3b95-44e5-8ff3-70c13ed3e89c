import * as Cesium from 'cesium';
import {
  createPointEntity,
  circleInterpolation,
  calculateLinearSplinePoints,
  extractCoordinates,
  reorderPoints,
  calculateCirclePoints,
  calculateHermiteSplinePoints,
  addMarkPoint,
  interpolatePoints,
  pointAlongLineToEnd,
  toDegrees
} from './common';

import { DRONE_STATUE } from '@/config/Map';
/**
 * 飞行漫游
 */
class CesiumFlight {
  static instance = null;
  constructor(viewer) {
    this.viewer = viewer;
    this.startPoint = null;
    this.targetPoint = null;
    this.model = null;
    this.flightLine = null; // 航线
    this.points = []; // 航线点集合 用于展示
    this.pointsEntity = []; // 航线点的插值点集合实体
    this.autoPoints = []; // 航线点的插值点集合
    this.flightHeight = 100; //飞行高度
    this.flightSpeed = 10; // 飞行速度
    this.intervalTimer = null; //动画
    this.currentIndex = 0; // 当前航线点索引
    this.surroundRadius = 100; //在目标点环绕的半径
    this.statue = DRONE_STATUE.PREPARE; //  当前状态预备
    this.isFlying = false; // 是否正在飞行
    CesiumFlight.instance = this;
    // 绑定上下文
    // this.flyToNext = this.flyToNext.bind(this);
  }
  static getInstance(viewer) {
    if (!CesiumFlight.instance) {
      CesiumFlight.instance = new CesiumFlight(viewer);
    }
    return CesiumFlight.instance;
  }
  // 设置飞机的模型及起点和目的地
  setAirInfo(model, startPoint, targetPoint) {
    this.model = model;
    this.startPoint = startPoint;
    this.targetPoint = targetPoint;
  }
  //  并且设置是否可见
  /**
   * 创建航线和航点
   * @param {*} points 经纬度及高程的点集合 [x,y,z]
   * @returns
   */
  setFlightLineAndPoints() {
    this.points = [];
    // 根据起终点构建点集合并计算插值点集
    this.createFlightPoints();
    // 添加点实体 autoPoints
    this.points?.forEach(point => {
      const pointEntity = createPointEntity(point);
      this.pointsEntity.push(pointEntity);
    });
    // 创建航线实体
    this.createFlightLine();
    // 初始设置点可见
    this.setFlightPointsVisible(true);
    // 初始设置航线可见
    this.setFlightLineVisible(true);
  }

  createFlightPoints() {
    this.autoPoints = [];
    this.points.push([
      this.startPoint[0],
      this.startPoint[1],
      this.flightHeight
    ]);
    this.points.push([
      this.targetPoint[0],
      this.targetPoint[1],
      this.flightHeight
    ]);
    // 计算插值点集
    this.autoPoints.push(
      Cesium.Cartesian3.fromDegrees(
        this.startPoint[0],
        this.startPoint[1],
        this.startPoint[2] || 0.0
      )
    );
    const { points } = interpolatePoints(
      [this.startPoint[0], this.startPoint[1], this.flightHeight],
      [this.targetPoint[0], this.targetPoint[1], this.flightHeight],
      1
    );
    const arr = calculateLinearSplinePoints(
      this.points,
      points.length || 20000
    );
    arr.forEach(p => {
      this.autoPoints.push(p);
    });
    this.autoPoints.push(
      Cesium.Cartesian3.fromDegrees(
        this.targetPoint[0],
        this.targetPoint[1],
        this.targetPoint[2] || 0.0
      )
    );
    return this.autoPoints;
  }

  setFlightPointsVisible(visible = false) {
    if (visible) {
      this.pointsEntity?.forEach(e => {
        this.viewer.entities.add(e);
      });
    } else {
      this.pointsEntity?.forEach(e => {
        this.viewer.entities.remove(e);
      });
    }
  }

  // 创建航线要素
  createFlightLine() {
    try {
      if (this.flightLine != null) {
        if (!this.viewer.entities.getById(this.flightLine.id)) {
          this.viewer.entities.remove(this.flightLine);
        }
        this.flightLine = null;
      }
      this.flightLine = new Cesium.Entity({
        polyline: {
          positions: this.autoPoints,
          width: 3,
          material: Cesium.Color.YELLOW
        },
        show: true
      });
      if (!this.viewer.entities.getById(this.flightLine.id)) {
        this.viewer.entities.add(this.flightLine);
      }
    } catch (error) {
      console.log('eeeeeeeeee:', error);
    }
  }
  setFlightLineVisible(visible = false) {
    if (visible) {
      this.viewer.entities.remove(this.flightLine);
      this.viewer.entities.add(this.flightLine);
    } else if (this.flightLine) {
      this.viewer.entities.remove(this.flightLine);
    }
  }

  async startFlight() {
    this.isFlying = true;
    try {
      await this.flyUp();
      await this.flyLine();
      await this.flyRingLine();
      await this.flyBackLine();
      await this.flyDown();
    } catch (error) {
      this.isFlying = false;
    }
  }

  /**
   * 起飞
   */
  async flyUp() {
    if (!this.isFlying) {
      return;
    }
    // 返回插值点和插值的个数
    const { points } = interpolatePoints(
      [this.startPoint[0], this.startPoint[1], 0],
      [this.startPoint[0], this.startPoint[1], this.flightHeight],
      1
    );
    // 在这些点之间执行飞行任务
    await this.flying(points);
  }
  /**
   * 按航线飞
   */
  async flyLine() {
    if (!this.isFlying) {
      return;
    }

    // 如果要环绕需要计算目标点 根据环绕半径和目标点进行计算动态目标点
    const { x, y, z, target } = pointAlongLineToEnd(
      [this.startPoint[0], this.startPoint[1], this.flightHeight],
      [this.targetPoint[0], this.targetPoint[1], this.flightHeight],
      100
    );
    addMarkPoint(this.viewer, target);
    // 返回插值点和插值的个数
    const { count, points } = interpolatePoints(
      [this.startPoint[0], this.startPoint[1], this.flightHeight],
      [x, y, z],
      1
    );
    // 在这些点之间执行飞行任务
    await this.flying(points);
    // 返回插值点和插值的个数
    // const { count, points } = interpolatePoints([this.startPoint[0], this.startPoint[1], this.flightHeight], [this.targetPoint[0], this.targetPoint[1], this.flightHeight], 1)
    // 在这些点之间执行飞行任务
    // await this.flying(points);
  }

  /**
   * 返程飞行
   */
  async flyBackLine() {
    if (!this.isFlying) {
      return;
    }
    const { x, y, z, target } = pointAlongLineToEnd(
      [this.startPoint[0], this.startPoint[1], this.flightHeight],
      [this.targetPoint[0], this.targetPoint[1], this.flightHeight],
      100
    );
    // 返回插值点和插值的个数
    const { points } = interpolatePoints(
      [x, y, z],
      [this.startPoint[0], this.startPoint[1], this.flightHeight],
      1
    );
    // 在这些点之间执行飞行任务
    await this.flying(points);
  }

  /**
   * 降落
   */
  async flyDown() {
    if (!this.isFlying) {
      return;
    }
    const { points } = interpolatePoints(
      [this.startPoint[0], this.startPoint[1], this.flightHeight],
      [this.startPoint[0], this.startPoint[1], 0],
      1
    );
    await this.flying(points);
    this.statue = DRONE_STATUE.STOP;
  }

  /**
   * 环绕飞行
   */
  async flyRingLine() {
    if (!this.isFlying) {
      return;
    }
    // 以目标点作为圆心
    const center = [
      this.targetPoint[0],
      this.targetPoint[1],
      this.flightHeight
    ];
    // 以目标点作为圆心 半径100 获取圆上的点数组
    const circlePoints = calculateCirclePoints(
      center,
      this.surroundRadius || 100
    );
    // 加密这些圆上的点
    const caculateCirclePoints = circleInterpolation(circlePoints);
    // caculateCirclePoints.forEach(p => {
    //   addMarkPoint(this.viewer, p, 5)
    // });
    // 获取航线上的环绕起点
    const { target } = pointAlongLineToEnd(
      [this.startPoint[0], this.startPoint[1], this.flightHeight],
      [this.targetPoint[0], this.targetPoint[1], this.flightHeight],
      this.surroundRadius
    );
    // 对插值后的圆形点集合数组进行重新排序 使其数组起点为 target
    const reReorderCirclePoints = reorderPoints(target, caculateCirclePoints);
    // addMarkPoint(this.viewer, reReorderCirclePoints[0], 30)
    // addMarkPoint(this.viewer, reReorderCirclePoints[caculateCirclePoints.length - 1], 20)
    // reReorderCirclePoints.forEach(p => {
    //   addMarkPoint(this.viewer, p, 5)
    // });
    // 对重新排序的点基础上进行执行飞行任务（环绕）这里执行两次
    await this.flying(reReorderCirclePoints);
    // await this.flying(reReorderCirclePoints);
  }
  stopFlight() {
    this.isFlying = false;
    if (this.intervalTimer != null) {
      clearInterval(this.intervalTimer);
      this.intervalTimer = null;
    }
    this.viewer.entities.remove(this.flightLine);
    this.flightLine = null;
    this.statue = DRONE_STATUE.STOP;
    CesiumFlight.instance = null;
    // this.destroy();
  }
  // flyToNext () {
  //   if (!this.isFlying) return; // 非飞行状态，停止飞行
  //   const that = this;
  //   this.curPointIndex = 0;
  //   if (this.intervalTimer != null) {
  //     clearInterval(this.intervalTimer);
  //     this.intervalTimer = null;
  //   }
  //   this.intervalTimer = setInterval(function () {
  //     if (that.curPointIndex >= that.autoPoints.length) {
  //       that.stopFlight();
  //     }
  //     that.setCameraView(that.curPointIndex);
  //     that.curPointIndex++;
  //   }, (1 / that.flightSpeed) * 500);
  // }

  async flying(points) {
    await new Promise((resolve, reject) => {
      let curPointIndex = 0;
      const intervalTimer = setInterval(() => {
        if (!this.isFlying) {
          clearInterval(intervalTimer);
          reject();
          return;
        } // 非飞行状态，停止飞行
        if (curPointIndex > points.length - 1) {
          clearInterval(intervalTimer);
          resolve();
        } else {
          this.setModelAndCamera(points, curPointIndex);
          curPointIndex++;
        }
        this.statue = DRONE_STATUE.FLYING;
      }, (1 / this.flightSpeed) * 500);
    });
  }

  /**
   * 设置相机和模型位置
   * @param {*} points
   * @param {*} index
   */
  setModelAndCamera(points, index) {
    if (points.length - 1 > index) {
      let origin = points[index];
      this.setModel(origin);
    }
  }
  /**
   * 第三人称的模型设置
   * @param {*} position
   */
  setModel(position) {
    if (this.viewer) {
      this.model.position = position;
      this.viewer.trackedEntity = this.model;
    }
  }

  destroy() {
    // 移除实体
    this.viewer.entities.remove(this.model);
    this.viewer.entities.remove(this.flightLine);
    this.flightLine = null;
    this.statue = DRONE_STATUE.STOP;
    CesiumFlight.instance = null;
  }

  getModelInfo() {
    const statue = this.statue;
    let lng = 0;
    let lat = 0;
    let height = 0;
    if (this.model) {
      // lng = this.model.position._value.x;
      // lat = this.model.position._value.y;
      // height = this.model.position._value.z;
      const p = toDegrees(this.model.position._value);
      lng = p[0];
      lat = p[1];
      height = Math.ceil(p[2]); // p[2];
    }
    return {
      statue,
      lng,
      lat,
      height,
      speed: this.flightSpeed
    };
  }
}

export { CesiumFlight };
