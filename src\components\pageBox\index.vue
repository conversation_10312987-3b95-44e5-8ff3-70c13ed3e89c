<!-- 注册 -->
<template>
  <div class="page-content flex">
    <div v-if="showLeftimg" class="page-left" />
    <div class="pb-5 flex-1 max-h-screen overflow-y-scroll">
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  name: '<PERSON>Box',

  components: {},
  props: {
    showLeftimg: {
      type: Boolean,
      default: true
    }
  }
};
</script>
<style lang="scss" scoped>
.page-content {
  width: 100vw;
  min-height: 100vh;

  .page-left {
    width: 450px;
    background-image: url('@/assets/register_bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    // min-height: 100vh;
    min-height: auto;
  }
}
</style>
