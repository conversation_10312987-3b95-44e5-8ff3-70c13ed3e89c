import { defineStore } from 'pinia';

import { store } from '@/store';

import { getDeviceDetail } from '@/api/device';

export const useDeviceDetailStore = defineStore('deviceDetail', () => {
  const deviceData = ref({});
  const pageLoading = ref(false);

  function hooks_getDeviceDetail(param) {
    pageLoading.value = true;

    return new Promise((resolve, reject) => {
      getDeviceDetail(param)
        .then(({ data }) => {
          deviceData.value = data;
          pageLoading.value = false;

          resolve(data);
        })
        .catch(error => {
          pageLoading.value = false;
          reject(error);
        });
    });
  }

  return {
    deviceData,
    hooks_getDeviceDetail,
    pageLoading
  };
});

// 非setup
export function useDeviceDetailStoreHook() {
  return useDeviceDetailStore(store);
}
