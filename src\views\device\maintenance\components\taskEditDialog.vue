<template>
    <el-dialog
      :title="title"
      v-if="title"
      :model-value="visible"
      width="600px"
      align-center
			class="taskEditDialog"
      :close-on-click-modal="false"
      @close="closeDialog"
    >
      <el-form class="app-form" ref="dataFormRef" :model="form" :rules="rules" label-width="120px" label-position="left">
				<el-row>
					<el-col :span="12">
							<el-select v-model="form.plan_id" placeholder="请选择计划" style="width: 280px;">
								<el-option
									v-for="item in planList"
									:key="item.id"
									:label="item.plan_name"
									:value="item.id"
								/>
							</el-select>
					</el-col>
					<el-col :span="12">
						<el-select v-model="form.scheme_id" placeholder="请选择维保方案" style="width: 280px;" @change="changeScheme">
							<el-option
								v-for="item in schemeList"
								:key="item.id"
								:label="item.scheme_name"
								:value="item.id"
							/>
						</el-select>
					</el-col>
				</el-row>
				<div class="item-box" v-if="schemeValueList.length <=0">
					<img class="empty-img" src="../../../../assets/maintenanceEmpty.png">
					<span class="empty-title">请选择维保方案</span>
				</div>
        <div class="item-box-be" v-else>
          <span class="scheme-item" v-for="(item,index) in schemeValueList" :key="index" :title="item">{{ item }}</span>
				</div>
        <el-form-item label="维保时间" prop="due_date">
          <el-date-picker
            class="input-serach"
            v-model="form.due_date"
            type="date"
						placeholder="请选择维保时间"
            value-format="YYYY-MM-DD 00:00:00"
            @change="handleSearch"
          />
        </el-form-item>
        <el-form-item label="指派执行用户" prop="assign_user_id" required>
          <el-select v-model="form.assign_user_id" placeholder="请指派执行用户">
            <el-option
              v-for="item in userList"
              :key="item.id"
              :label="item.nickname"
              :value="item.id"
              clearable
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmit" v-loading="loading">确 定</el-button>
          <el-button @click="closeDialog">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </template>
  
  <script setup>
  import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
  import { ElMessage } from 'element-plus';
  import { editTask, addTask, getMaintenancePlan, getMaintenanceScheme, getSchemeDetail, getUserList } from '@/api/devices/maintenance.js';
  
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '标题'
    },
    formData: {
      type: Object,
      default(rawProps) {
        return {};
      }
    }
  });
  const form = reactive({});
  const dataFormRef = ref(ElForm);
  const userList = ref([]);
	const planList = ref([]);
	const schemeList = ref([]);
  const schemeValueList = ref([]);
  watch(
    () => props.formData,
    (newVal, oldVal) => {
      Object.assign(form, newVal);
      if(newVal.task_item_names) {
        schemeValueList.value = newVal.task_item_names.split('，')
      }
      let arr = []
      arr = userList.value.map(item=>item.id)
      if(arr.indexOf(props.formData.assign_user_id) == -1) {
        form.assign_user_id = ''
      }
      console.log('点击编辑获取详情', form);
    },
    { deep: true }
  );
  const emit = defineEmits(['update:visible', 'submit']);
  const rules = reactive({
    plan_name: [{ required: true, message: '请输入计划名称', trigger: 'blur' }],
    range: [{ required: true, message: '请选择计划开始时间', trigger: ['change', 'blur'] }],
    due_date: [{ required: true, message: '请选择维保时间', trigger: ['blur'] }],
    assign_user_id: [{ required: true, message: '请指派执行用户', trigger: ['blur'] }],
  });
  
  const loading = ref(false);
  
  // 关闭弹窗 
  function closeDialog() {
    resetForm();
    emit('update:visible', false);
  }

  function initList () {
    Promise.all([
      getMaintenancePlan({}),
      getMaintenanceScheme({}),
      getUserList({})
    ]).then(([res, resp, data])=>{
      planList.value = res
      schemeList.value = resp
      userList.value = data
    })
  }

  function changeScheme (value) {
    getSchemeDetail({
			id: value
		}).then(res=>{
			console.log(res)
			schemeValueList.value = res.items?.map(item=>item.item_name) || []
		})
  }
  
  /**
   * 重置表单
   */
  function resetForm() {
    dataFormRef.value.resetFields();
    dataFormRef.value.clearValidate();
    loading.value = false;
    Object.keys(form).map(key => {
      delete form[key];
    });
    schemeValueList.value = []
  }
  
  function handleSubmit() {
    if(!form.plan_id) {
      ElMessage.error('请选择计划')
      return;
    }
    if(!form.scheme_id) {
      ElMessage.error('请选择维保方案')
      return;
    }
    dataFormRef.value.validate(isValid => {
      if (isValid) {
        let params = { 
          ...form,
        };
        loading.value = true;
        if (props.title == '新增维保任务') {
          addTask(params)
            .then(res => {
              loading.value = false;
              ElMessage.success('新增成功');
              closeDialog();
              emit('submit');
            })
            .catch(e => {
              loading.value = false;
            });
        } else {
          //编辑保单
          editTask(params)
            .then(res => {
              loading.value = false;
              ElMessage.success('修改成功');
              closeDialog();
              emit('submit');
            })
            .catch(e => {
              loading.value = false;
            });
        }
      } else {
        loading.value = false;
      }
    });
  }
  onMounted(() => {
    initList();
  });
  
  defineExpose({ resetForm });
  </script>
	<style lang="scss">
	.taskEditDialog {
		.el-dialog__body{
			background-color: #001129 !important;
		}
	}
	</style>
  <style scoped lang="scss">
  .input-serach {
    width: 200px;
  }
	.taskEditDialog {
		.el-dialog__body{
			background-color: #001129 !important;
		}
	}
	.empty-title {
		font-family: SourceHanSansSC-Regular;
		font-size: 14px;
		color: #49515C;
		text-align: left;
		line-height: 22px;
		font-weight: 400;
		display: inline-block;
		margin-left: 10px;
	}
  .scheme-item {
    display: inline-block;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    background: #175091;
    border-radius: 2px;
    padding: 5px 8px;
    margin-right: 8px;
    margin-bottom: 5px;
  }
	.empty-img {
		transform: translateY(15px);
	}
  .item-box-be {
    width: 568px;
		height: 130px;
    overflow-y: auto;
		background-color: #11253E;
		border-radius: 4px;
		margin: 15px 0;
    padding: 16px;
  }
	.item-box {
		width: 568px;
		height: 130px;
		line-height: 130px;
		overflow-y: auto;
		background-color: #11253E;
		border-radius: 4px;
		margin: 15px 0;
		text-align: center;
	}
  .fly-bottom {
    margin-top: 40px;
    text-align: center;
  }
  .app-form {
    .select-time {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin: 5px 0;
    }
    ::v-deep {
      .el-input-number,
      .el-select {
        width: 100%;
      }
      .el-input-number .el-input__inner {
        text-align: left;
      }
      .el-input-number.is-controls-right .el-input__wrapper {
        padding-left: 11px;
      }
      .el-date-editor.el-input,
      .el-date-editor.el-input__wrapper {
        width: 100%;
      }
    }
  }
  </style>
  