import { Point } from './Point.js';
import { generateKey } from '@/utils';
class PlaceMark {
  /**
   * Wayline constructor
   * @param {object} options - The options object containing the Wayline parameters
   * @param {number[]} options.coordinates - The coordinates of the Waypoint
   * @param {number} [options.index=0] - The index of the Waypoint
   * @param {number} [options.executeHeight=100] - The execution height of the Waypoint
   * @param {number} [options.waypointSpeed=10] - The speed at the Waypoint
   * @param {any} options.waypointHeadingParam - The heading parameter of the Waypoint
   * @param {any} options.waypointTurnParam - The turn parameter of the Waypoint
   * @param {boolean} options.useStraightLine - Whether the Waypoint should use a straight line
   * @param {any} [options.waypointGimbalHeadingParam] - The gimbal heading parameter of the Waypoint
   */
  constructor(options) {
    this.uuid = options.uuid || generateKey();
    this.Point = new Point({ coordinates: options.coordinates });
    this.wpml_index = options.index || 0;
    this.wpml_executeHeight = options.executeHeight || 100; // 执行飞翔高度
    this.wpml_waypointSpeed = options.waypointSpeed | 10; //速度
    this.wpml_waypointHeadingParam = options.waypointHeadingParam; //偏航角模式参数
    this.wpml_waypointTurnParam = options.waypointTurnParam; //航点类型（航点转弯模式）
    this.wpml_useStraightLine = options.useStraightLine; //该航段是否贴合直线
    // this.waypointGimbalHeadingParam = options.waypointGimbalHeadingParam;
    this.wpml_actionGroup = [];
  }

  //#region

  /**
   * 动作组中添加动作
   * @param {Action} action
   */
  addAction(action) {
    this.wpml_actionGroup.push(action);
  }

  /**
   * 设置动作组
   * @param {ActionGroup []} actionGroup
   */
  setActionGroup(actionGroup) {
    this.wpml_actionGroup = actionGroup;
  }

  /**
   * 设置动作组
   */
  getActionGroup() {
    return this.wpml_actionGroup || [];
  }

  /**
   * 添加动作组
   * @param {ActionGroup} actionGroup
   */
  addActionGroup(actionGroup) {
    if (!actionGroup) {
      return null;
    }
    this.wpml_actionGroup = this.wpml_actionGroup || [];
    this.wpml_actionGroup.push(actionGroup);
    return actionGroup;
  }
  //#endregion;

  //#region  Setter methods

  /**
   *
   * @param {*} coordinates "coordinates": "118.03815008171695,24.61508368279456"
   */
  setPoint(coordinates) {
    this.Point = new Point(coordinates);
  }

  setIndex(value) {
    this.wpml_index = value;
  }

  setEexecuteHeight(value) {
    this.wpml_executeHeight = value;
  }

  setWaypointSpeed(value) {
    this.wpml_waypointSpeed = parseFloat(value);
  }

  setWaypointHeadingParam(value) {
    this.wpml_waypointHeadingParam = value;
  }

  setWaypointTurnParam(value) {
    this.wpml_waypointTurnParam = value;
  }

  setUseStraightLine(value) {
    this.wpml_useStraightLine = Boolean(value);
  }

  setWaypointGimbalHeadingParam(value) {
    this.waypointGimbalHeadingParam = value;
  }

  // Getter methods
  getPoint() {
    return this.Point;
  }

  getIndex() {
    return this.wpml_index;
  }

  getExecuteHeight() {
    return this.wpml_executeHeight;
  }

  getWaypointSpeed() {
    return this.wpml_waypointSpeed;
  }

  getWaypointHeadingParam() {
    return this.wpml_waypointHeadingParam;
  }

  getWaypointTurnParam() {
    return this.wpml_waypointTurnParam;
  }

  getUseStraightLine() {
    return this.wpml_useStraightLine;
  }

  getWaypointGimbalHeadingParam() {
    return this.waypointGimbalHeadingParam;
  }
  //#endregion
}

export { PlaceMark };
