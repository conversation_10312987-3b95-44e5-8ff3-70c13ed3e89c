import { loadDeviceJsonFile } from '@/utils/configHelper';
import { ElMessage } from 'element-plus';
export const deviceConfigs = await loadDeviceJsonFile();

export const getDroneInfoByKey = droneModelKey => {
  if (!droneModelKey) {
    return null;
  }
  const droneInfo = deviceConfigs[droneModelKey];
  if (!droneInfo) {
    ElMessage.error('当前配置表中未找到对该型号设备的支持,请核实！');
    return null;
  }
  if (!droneInfo.device) {
    ElMessage.error('当前配置信息未配置对应的设备信息,请核实！');
    return null;
  }
  return {
    device: {
      series: droneInfo.device.series ?? '',
      droneEnumVal: droneInfo.device.droneType,
      droneEnumLabel: droneInfo.device.droneName || droneInfo.device.deviceName,
      droneSubEnumVal: droneInfo.device.droneSub,
      droneSubEnumLabel: droneInfo.device.deviceName,
      payloadEnumVal: droneInfo.device.payloadType,
      payloadSubEnumVal: droneInfo.device.payloadSub,
      droneModelKey: droneInfo.device.droneKey,
      payloadModelKey: droneInfo.device.payloadKey
    },
    imageFormat: droneInfo.imageFormat || [],
    cmos: droneInfo.cmos || [],
    actions: droneInfo.actions || []
  };
};

export const getDroneInfoByPlanData = planData => {
  if (!planData) {
    return null;
  }
  const drone_model_key = planData.drone_model_key; // "0-91-1"
  const payload_model_keys = planData.payload_model_keys[0]; //
  // 这里对 drone_model_key 通过 - 进行分解获取 数据
  const payloadInfo = {
    payloadEnumVal: 0,
    payloadSubEnumVal: 91
  };
  if (payload_model_keys) {
    const arr = payload_model_keys.split('-');
    payloadInfo.payloadEnumVal = parseInt(arr[1]);
    payloadInfo.payloadSubEnumVal = parseInt(arr[2]);
  }
  const baseInfo = getDroneInfoByKey(drone_model_key);
  if (!baseInfo) {
    ElMessage.error('未找到基础设备信息！');
    return;
  }
  let droneInfo = baseInfo?.device ?? null;
  if (!droneInfo) {
    ElMessage.error('配置信息中未找到设备信息！');
    return;
  }
  return {
    planId: planData.id,
    planName: planData.name || '',
    series: droneInfo.series || '',
    droneEnumVal: droneInfo.droneEnumVal,
    droneSubEnumVal: droneInfo.droneSubEnumVal,
    droneSubEnumLabel: droneInfo.droneSubEnumLabel,
    payloadEnumVal: payloadInfo.payloadEnumVal || droneInfo.payloadEnumVal || '',
    payloadSubEnumVal: payloadInfo.payloadSubEnumVal || droneInfo.payloadSubEnumVal || '',
    droneModelKey: planData.drone_model_key || droneInfo.droneModelKey || '',
    payloadModelKey: planData.payload_model_keys[0] || droneInfo.payloadModelKey || '',
    waylineType: planData.template_types[0] || 0
  };
};

/**
 * 将设备配置对象转换为列表
 *
 * @param devConfigObj 设备配置对象
 * @returns 转换后的列表
 */
export const setDeviceConfigsToList = devConfigObj => {
  if (!devConfigObj) {
    return [];
  }
  // 用于存储结果的对象
  const list = [];
  // 遍历原始JSON对象
  for (const key in devConfigObj) {
    if (devConfigObj.hasOwnProperty(key)) {
      const device = devConfigObj[key].device;
      if (!device) {
        continue;
      }
      // 检查数组list中是否存在 对象  对象的 series 与 device.series 相等 如果存在则跳过 检查子项是否存在
      const seriesFind = list.find(item => {
        return item.series === device.series;
      });
      if (seriesFind) {
        let droneSubList = seriesFind.droneSubList;
        if (droneSubList.length === 0) {
          seriesFind.droneSubList.push({
            value: device.droneSub,
            type: device.deviceName ?? device.droneName,
            label: device.droneName,
            uuid: device.droneKey
          });
        } else if (droneSubList.length > 0) {
          const devFind = droneSubList.find(item => {
            return item.uuid === device.droneKey;
          });
          if (!devFind) {
            seriesFind.droneSubList.push({
              value: device.droneSub,
              type: device.deviceName ?? device.droneName,
              label: device.droneName,
              uuid: device.droneKey
            });
          }
        }
      } else {
        list.push({
          series: device.series,
          droneEnumVal: device.droneType,
          label: device.series,
          droneSubList: [
            {
              value: device.droneSub,
              type: device.deviceName ?? device.droneName,
              label: device.droneName,
              uuid: device.droneKey
            }
          ]
        });
      }
    }
  }
  return list;
};
