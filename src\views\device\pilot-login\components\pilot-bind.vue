<template>
  <div class="flex-display" style="height: 100vh; background-color: white;">
    <div class="height100 width100 flex-column flex-justify-start flex-align-start">
      <el-row class="pt20 pl20" style="height: 45px; width: 100vw" align="middle">
        <el-col :span="1">
          <span style="color: #1fa3f6" class="fz26"><Position rotate="90" /></span>
        </el-col>
        <el-col :span="20">
          <span class="fz20 pl5">{{ drone.data.model }}</span>
        </el-col>
        <el-col :span="3">
          <span class="fz16" v-if="drone.data.bound_status" style="color: #737373">绑定</span>
          <el-button type="primary" @click="onBindDevice" v-else>绑定</el-button>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Position } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import { bindDevice } from '@/api/pilot-login/manage.js'
import apiPilot from '@/api/pilot-login/pilot-bridge'
import { ELocalStorageKey } from '@/utils/constants'
const drone = reactive({
  data: JSON.parse(localStorage.getItem(ELocalStorageKey.Device)!)
})

onMounted(()=>{
  localStorage.setItem('isHome', 'false')
})

function onBindDevice () {
  const bindParam = {
    device_sn: drone.data.sn,
    user_id: localStorage.getItem(ELocalStorageKey.UserId)!,
    workspace_id: localStorage.getItem(ELocalStorageKey.WorkspaceId)
  }
  bindDevice(bindParam).then(bindRes => {
    // ElMessage.error('bind failed:' + bindRes.message)
    // console.error(bindRes.message)
    // return
    drone.data.bound_status = true
    localStorage.setItem(ELocalStorageKey.Device, JSON.stringify(drone.data))
  })
}
</script>

<style lang="scss" scoped>
</style>