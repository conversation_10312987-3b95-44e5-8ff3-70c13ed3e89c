// 航面转为航线辅助方法
import * as Cesium from 'cesium';
import { cloneDeep } from 'lodash';
import { useDeviceStore } from '@/store/modules/device.js';
import {
  createPolyline,
  createEndtPoint,
  createStartPoint,
  c3toDegress,
  toDegrees,
  getArea,
  getCenter,
  getLength,
  toMercator,
  toWgs84,
  toNumber,
  removeDuplicates,
  deepEqual
} from '@/components/Cesium/libs/cesium';
import { debounce } from 'lodash';
import { CAMERA_TYPE_ENUM } from '@/config';
import { calculateDroneSpeedAndTime, getCmosData } from './cameraHeleper';
import { bufferPolygon, ScreenEventHandler } from '@/components/Cesium/libs/cesium/index';
import {
  EXECUTE_HEIGHT_MODE,
  EXECUTE_RCLOST_ACTION,
  EXIT_ON_RCLOST,
  FINISH_ACTION,
  FLYTO_WAY_LINE_MODE,
  HEIGHT_MODE_ENUM,
  SHOOTTYPE_ENUM,
  TEMPLATE_TYPE_ENUM
} from '@/views/plan/newplan/kmz/props';
import { getCourseOverlap, getSideOverlap, caculateHeights } from '../index.js';
import { reactive, toRaw } from 'vue';
import { usePlanInfoStore } from '@/store/modules/planInfo.js';
import { generateWayLine } from './generateWayLine';
import { useEditTrackerStore } from '@/store/modules/dataEditTracker.js';
import { ElMessage } from 'element-plus';
import { removeMapToolTips, setMapToolTips } from '@/views/plan/common/tips';
const editTrackerStore = useEditTrackerStore();
const { setOptions, setDistanceFn, setLatlng2PxFn, setPx2LatlngFn, getPolygonArea, getPolylineArea } =
  generateWayLine();
const deviceStore = useDeviceStore();
const planInfoStore = usePlanInfoStore();
// 航线中涉及的实体集合
const wayLineEntity = [];
// 这里的 起止点和中点都是经纬度数组形式
let curViewer, pStart, pEnd;
// 安全飞行的航线集合
let saveFiightLine = [];

// 起飞点提示提变化
export const isAlreadySetAirPlace = ref(true);
// 飞机机场位置
export const aircraft = reactive({
  airPlaceMark: null
});
// 用于航线提交的数据结构
export const uploadWaylinesJson = {
  wayline_id: null,
  name: '航面-新建面状航线',
  drone_model: '',
  payload_model: '',
  template_type: '',
  airline_json: { template: null, wayLines: null, config: {} }
};
// 航线对象 返回的是经纬度坐标点集合（会有部分点是重复的前后点需要处理）
let lineLatlngs = null;
//#region  用于地图上绘制航面的相关参数及初始胡方法
export const dataInfoRect = reactive({
  rotate: 0, // 角度
  space: 10, // 航线间隔 米
  originPolygon: [], // 原始多边形坐标组
  polygon: [], //多边形坐标数组，每个元素为{lat:Number,lng:Number}。
  positions: [], // 飞机有效的航点集合 这里就是指绘制面转线部分的坐标
  aircraftRoutePositions: [], // 飞机飞行的所有点集合 包括机场航点和起降点等
  margin: 0, //外扩边距,
  height: 100, // 点和线距离地面默认100米
  airPlaceMarkPosition: [], // [0, 0, 0], 飞机机场的位置
  area: 0, // 飞行面积 平方米
  length: 0, //飞行里程 km
  time: 0, // 飞行时间 h m s
  done: false // 是否完成绘制
});

export const parmsInfoRect = reactive({
  alreadySetAirPlace: false,
  airPortPlace: [0, 0, 0], // 经度，纬度，高度
  airPortPlaceASL: 0, // 无人机相对高度 在有地形上
  planName: '新建面状航线',
  cameraType: [CAMERA_TYPE_ENUM.visable], // 这里默认可见光
  templateType: TEMPLATE_TYPE_ENUM.mapping2d, //  正射影像
  gsd: {},
  flyToWaylineMode: FLYTO_WAY_LINE_MODE.safely, //飞向首航点模式 默认安全
  exitOnRCLost: EXIT_ON_RCLOST.executeLostAction, // 失控时 退出航线，执行失控动作
  executeRCLostAction: EXECUTE_RCLOST_ACTION.goBack, //失控动作 返回
  takeOffSecurityHeight: 100, // 安全起飞高度
  flightHight: 100, // 飞行高度用于内部计算使用 展示使用
  height: 100, // egm96高
  ellipsoidHeight: 100, // 椭球高
  globalShootHeight: 100, // 飞行器离被摄面高度（相对地面高）必须
  realFlightHight: 100, // 组件上显示的飞行高度 真实飞行高度 gsd 影响的就是这个高度
  autoFlightSpeed: 9,
  executeHeightMode: EXECUTE_HEIGHT_MODE.WGS84, // wayline中使用
  heightMode: HEIGHT_MODE_ENUM.EGM96, // 航点高程参考平面 必须 template 中使用
  direction: 0,
  elevationOptimization: true,
  finishAction: FINISH_ACTION.goHome, // 完成动作后执行 自动返航 必须,
  globalTransitionalSpeed: 15, //全局航线过渡速度 起飞速度 必须
  globalRTHHeight: 100, //全局返航高度 必须
  projectAltitudeHeigh: 0,
  orthoCameraOverlapW: 80, // 正射的航旁向重叠率
  orthoCameraOverlapH: 80, // 正射的航向重叠率
  margin: 10,
  shootType: SHOOTTYPE_ENUM.time, // 等时间隔拍照 必须,
  shootParms: 2, // 拍照参数 距离就是米 时间就是秒
  space: 10, // 飞行间隔
  area: 0, // 飞行面积 平方米
  length: 0, //飞行里程 km
  time: 0, // 飞行时间 h m s
  minSpeed: 0, // 无人机这时候的最小飞行速度
  maxSpeed: 15, // 无人机这时候的最大飞行速度
  cameraFocalLength: 0, // 相机焦距
  cameraWidth: 0, // 相机画幅 宽度
  cameraHeight: 0, // 相机画幅 高度
  cameraPixels: 0, // 相机像素 例如 4800万
  cameraPixel: 0, // 相机像元大小 单位 um
  cameraCmosFormat: '', // 相机 Cmos 类型
  templateType: TEMPLATE_TYPE_ENUM.mapping2d, // 模板类型
  droneEnumVal: 91, // 无人机
  droneSubEnumVal: 81 // 设备信息
});

export function initDataInfoRect() {
  dataInfoRect.rotate = 0;
  dataInfoRect.space = 10;
  dataInfoRect.originPolygon = []; // 原始面坐标数组
  dataInfoRect.polygon = []; // 变更面坐标数组
  dataInfoRect.positions = []; //
  dataInfoRect.aircraftRoutePositions = [];
  dataInfoRect.margin = 0;
  dataInfoRect.height = 100;
  dataInfoRect.airPlaceMarkPosition = [];
  dataInfoRect.area = 0;
  dataInfoRect.length = 0;
  dataInfoRect.time = '';
  dataInfoRect.done = false;
}
export function initParmsInfoRect() {
  parmsInfoRect.alreadySetAirPlace = false;
  parmsInfoRect.airPortPlace = [0, 0, 0]; // 经度，纬度，高度
  parmsInfoRect.airPortPlaceASL = 0;
  parmsInfoRect.planName = '新建面状航线';
  parmsInfoRect.cameraType = [CAMERA_TYPE_ENUM.visable]; // 这里默认可见光
  parmsInfoRect.templateType = TEMPLATE_TYPE_ENUM.mapping2d;
  parmsInfoRect.gsd = {};
  parmsInfoRect.flyToWaylineMode = FLYTO_WAY_LINE_MODE.safely;
  parmsInfoRect.exitOnRCLost = EXIT_ON_RCLOST.executeLostAction;
  parmsInfoRect.executeRCLostAction = EXECUTE_RCLOST_ACTION.goBack;
  parmsInfoRect.takeOffSecurityHeight = 100;
  parmsInfoRect.flightHight = 100;
  parmsInfoRect.height = 100;
  parmsInfoRect.ellipsoidHeight = 100;
  parmsInfoRect.globalShootHeight = 100;
  parmsInfoRect.realFlightHight = 100;
  parmsInfoRect.autoFlightSpeed = 10;
  parmsInfoRect.executeHeightMode = EXECUTE_HEIGHT_MODE.WGS84;
  parmsInfoRect.heightMode = HEIGHT_MODE_ENUM.EGM96;
  parmsInfoRect.direction = 0;
  parmsInfoRect.elevationOptimization = true;
  parmsInfoRect.finishAction = FINISH_ACTION.goHome;
  parmsInfoRect.globalTransitionalSpeed = 15;
  parmsInfoRect.globalRTHHeight = 100;
  parmsInfoRect.projectAltitudeHeigh = 0;
  parmsInfoRect.orthoCameraOverlapW = 80;
  parmsInfoRect.orthoCameraOverlapH = 80;
  parmsInfoRect.margin = 10;
  parmsInfoRect.shootType = SHOOTTYPE_ENUM.time;
  parmsInfoRect.shootParms = 2;
  parmsInfoRect.space = 10;
  parmsInfoRect.area = '0 m²';
  parmsInfoRect.length = '0 m';
  parmsInfoRect.time = '0 s';
  parmsInfoRect.minSpeed = 0;
  parmsInfoRect.maxSpeed = 15;
  parmsInfoRect.cameraFocalLength = 0;
  parmsInfoRect.cameraWidth = 0;
  parmsInfoRect.cameraHeight = 0;
  parmsInfoRect.cameraPixels = 0;
  parmsInfoRect.cameraPixel = 0;
  parmsInfoRect.cameraCmosFormat = '';
  parmsInfoRect.templateType = TEMPLATE_TYPE_ENUM.mapping2d;
}
//#endregion

/**
 * setView 将主程序汇总高度Viewer传过来并且设置航线规划的所需方法
 * @param {*} v
 */
export function setView(v) {
  curViewer = v;
  setDistanceFn(distance);
  setLatlng2PxFn(toMercator);
  setPx2LatlngFn(toWgs84);
  //获取设备信息
  let cameraType = getCurrentDeviceCameraType();
  if (!cameraType) {
    throw new Error('未找到当前设备的相机类型！');
  }
  // 获取相机焦距信息
  let cameraInfo = getCmosData({ camera: cameraType }) ?? null;
  if (!cameraInfo) {
    throw new Error('未获取到设备相机信息');
  }
  // const { focalLength, sensorWidth, sensorHeight, pixels, pixel, format } = cameraInfo;
  // 设置相机的相关参数 焦距 尺寸宽高
  parmsInfoRect.cameraFocalLength = cameraInfo.focalLength;
  parmsInfoRect.cameraWidth = cameraInfo.sensorWidth;
  parmsInfoRect.cameraHeight = cameraInfo.sensorHeight;
  parmsInfoRect.cameraPixels = cameraInfo.pixels;
  parmsInfoRect.cameraPixel = cameraInfo.pixel;
  parmsInfoRect.cameraCmosFormat = cameraInfo.format;
  parmsInfoRect.cameraType = deviceStore?.getCameraSelect() ?? [];
}

/**
 * 创建飞机起飞到航线起点的飞行线
 */
export function createFirstFlightLine() {
  if (parmsInfoRect.airPortPlace && pStart) {
    if (saveFiightLine && saveFiightLine.length > 0) {
      saveFiightLine.forEach(l => {
        curViewer.entities.remove(l);
      });
      saveFiightLine.length = 0;
    }
    let takeOffSecurityHeight = Number(parmsInfoRect.takeOffSecurityHeight);
    // 这里涉及安全起飞高度线的绘制 一共三条线
    const {
      latitude: pStartLatitude,
      longitude: pStartLongitude,
      height: pStartHeight
    } = c3toDegress(pStart.position._value);
    let pStartlnglat = toDegrees(pStart.position._value);
    ///TODO...0913 修改 这个经纬度顺序
    const [longitude, latitude, height] = parmsInfoRect.airPortPlace;
    // 如果飞行的起点高度大于安全高度 这里就只有3个点
    if (pStartHeight >= takeOffSecurityHeight) {
      const midPoint = [longitude, latitude, pStartHeight || 100.0];
      let line = createPolyline(curViewer, [toRaw(parmsInfoRect.airPortPlace), midPoint, pStartlnglat]);
      saveFiightLine.push(line);
      // 添加飞机机场位置、 起点位置和两者的中间点位置
      dataInfoRect.aircraftRoutePositions.push(...[toRaw(parmsInfoRect.airPortPlace), midPoint, pStartlnglat]);
    } else {
      // 如果飞行的起点高度小于安全高度 这里就只有4个点
      let secondPoint = [longitude, latitude, takeOffSecurityHeight || 100.0];
      let thirdPoint = [pStartLongitude, pStartLatitude, takeOffSecurityHeight || 100.0];
      let line2 = createPolyline(curViewer, [toRaw(parmsInfoRect.airPortPlace), secondPoint, thirdPoint, pStartlnglat]);
      saveFiightLine.push(line2);
      // 添加飞机的机场位置、机场顶部位置、起飞点位置和起飞点顶部位置（该点在安全飞行高度的线中）
      dataInfoRect.aircraftRoutePositions.push(
        ...[toRaw(parmsInfoRect.airPortPlace), secondPoint, thirdPoint, pStartlnglat]
      );
    }
  } else {
    console.error('airPortPlace or pStart is undefined');
  }
}

/**
 * 渲染线
 * @param {*} opt
 * rotate: 0, // 角度
   space: 10, // 航线间隔 米
   polygon: [], //多边形坐标数组，每个元素为{lat:Number,lng:Number}。
   margin: 0, //外扩边距,
   height: 100, // 点和线距离地面默认100米
   airPlaceMark: [x,y,z],
   area: 0, // 飞行面积 平方米
   length: 0, //飞行里程 km
   time: '' // 飞行时间 h m s
 */
export function renderPolyline(opt, callback) {
  dataInfoRect.done = false;
  if (wayLineEntity && wayLineEntity.length > 0) {
    wayLineEntity.forEach(e => {
      curViewer.entities.remove(e);
    });
  }
  wayLineEntity.length = 0;
  // 获取当前航点信息
  lineLatlngs = setOptions({
    polygon: opt.polygon,
    rotate: parseFloat(opt.rotate) || 0,
    space: parseFloat(opt.space) || 10
  });
  // 移除相同的点位
  lineLatlngs = removeDuplicates(lineLatlngs);
  // 创建具体航点
  dataInfoRect.positions = [];
  // 添加航点到数组
  lineLatlngs.forEach(p => {
    dataInfoRect.positions.push([p.lng, p.lat, opt.height]);
  });
  // 添加起点 图标
  pStart = createStartPoint(curViewer, {
    position: Cesium.Cartesian3.fromDegrees(lineLatlngs[0].lng, lineLatlngs[0].lat, opt.height || 100)
  });
  wayLineEntity.push(pStart);
  // 添加终点 图标
  pEnd = createEndtPoint(curViewer, {
    position: Cesium.Cartesian3.fromDegrees(
      lineLatlngs[lineLatlngs.length - 1].lng,
      lineLatlngs[lineLatlngs.length - 1].lat,
      opt.height || 100
    )
  });
  wayLineEntity.push(pEnd);
  // 飞机路线点集合清空
  dataInfoRect.aircraftRoutePositions.length = 0;
  // 创建飞机到第一个点的飞行路线-安全起飞路线
  createFirstFlightLine();
  // 这里添加航线点
  dataInfoRect.aircraftRoutePositions.push(...dataInfoRect.positions);
  //#region 构建航线

  // 获取的是经纬度坐标数组 将对象数组处理成 二维数组
  const flattenedAireLineCorrdArr = lineLatlngs.map(item => {
    return [toNumber(item.lng, 7), toNumber(item.lat, 7), toNumber(opt?.height || 100, 3)];
  });
  // 高程优化
  if (parmsInfoRect.elevationOptimization && flattenedAireLineCorrdArr) {
    // 计算面的中心点
    const positions = opt.polygon.map(poi => [poi.lng, poi.lat, poi.height]);
    const pCenter = getCenter(positions);
    flattenedAireLineCorrdArr.push(pCenter);
    dataInfoRect.positions.push(pCenter);
    // 添加航面中心点
    dataInfoRect.aircraftRoutePositions.push(pCenter);
  }
  let line = createPolyline(curViewer, flattenedAireLineCorrdArr);
  wayLineEntity.push(line);
  //#endregion
  // 这里要添加倒数第二个点 再添加倒数第一个点 从中间点回到起飞点上空 再回到机场位置
  if (dataInfoRect.aircraftRoutePositions.length > 1) {
    dataInfoRect.aircraftRoutePositions.push(dataInfoRect.aircraftRoutePositions[1]);
    dataInfoRect.aircraftRoutePositions.push(dataInfoRect.aircraftRoutePositions[0]);
  }
  dataInfoRect.done = true;
  // 执行回调
  callback && callback();
}

/**
 * 清除实体
 * @param {*} opt
 */
export function clearWayLineEntity() {
  if (wayLineEntity && wayLineEntity.length > 0) {
    wayLineEntity.forEach(e => {
      curViewer.entities.remove(e);
    });
    wayLineEntity.length = 0;
  }
  if (saveFiightLine && saveFiightLine.length > 0) {
    saveFiightLine.forEach(l => {
      curViewer.entities.remove(l);
    });
    saveFiightLine.length = 0;
  }
}

/**
 * 创建航线方法 防抖 避免频繁构建航线
 */
const debouncedRenderPolyline = debounce(renderPolyline, 300); // 延迟300ms执行
export function createWayLines(dataInfoRect, cb) {
  if (dataInfoRect && dataInfoRect.polygon && dataInfoRect.polygon.length > 0) {
    // 执行方法
    debouncedRenderPolyline(dataInfoRect, cb);
  }
}

//#region 算法所需函数方法

/**
 * 计算两点坐标
 * @param {*} startPoi [lon, lat, height]
 * @param {*} endPoi [lon, lat, height]
 * @returns
 */
export function distance(startPoi, endPoi) {
  let distance = 0;
  const startCartographic = Cesium.Cartographic.fromDegrees(startPoi.lng, startPoi.lat);
  const endCartographic = Cesium.Cartographic.fromDegrees(endPoi.lng, endPoi.lat);
  const startCartesian = Cesium.Cartesian3.fromRadians(
    startCartographic.longitude,
    startCartographic.latitude,
    startCartographic.height
  );
  const endCartesian = Cesium.Cartesian3.fromRadians(
    endCartographic.longitude,
    endCartographic.latitude,
    endCartographic.height
  );
  distance = Cesium.Cartesian3.distance(startCartesian, endCartesian);
  return distance;
}

//#endregion

//#region 计算边距构建新面积
/**
 * 创建新面积
 * @param {*} polygons 坐标数组 【{lng,lat},{lng,lat}...】
 * @param {*} margin 外扩边界值
 * @returns
 */
export function createMarginPolygon(polygons, margin = 0) {
  if (!polygons) {
    return null;
  }
  // 判断数据长度不足以构成面则取消;
  if (polygons.length < 3) {
    return null;
  }
  if (margin === 0) {
    // 这里要一次转换
    return polygons.map(polygonObj => {
      return [polygonObj.lng, polygonObj.lat];
    });
  }
  let arr = [];
  polygons.forEach(polygonObj => {
    // 经度/维度
    arr.push([polygonObj.lng, polygonObj.lat]);
  });
  // 将第一个顶点坐标再次添加到数组的最后,形成闭合的多边形
  arr.push(arr[0]);
  // 做buffer 算法 按照预定的面外扩多少
  const buffer = bufferPolygon([arr], margin);
  return buffer;
}

export const caculatePolygonMargin = (dataInfoRect, value = 0) => {
  dataInfoRect.margin = value;
  const originPolygon = cloneDeep(dataInfoRect.originPolygon);
  let newPointsArr = createMarginPolygon(originPolygon, dataInfoRect.margin);
  if (!newPointsArr) {
    return;
  }
  dataInfoRect.polygon.length = 0;
  //  这里做一个转变
  newPointsArr.forEach(position => {
    let obj = {
      lng: position[0] || 0,
      lat: position[1] || 0,
      height: dataInfoRect.height || 100
    };
    dataInfoRect.polygon.push(obj);
  });
  return dataInfoRect.polygon;
};
//#endregion

//#region 计算其他参数
/**
 * 计算飞行多边形面积
 *
 * @returns 返回一个包含面积和单位的对象
 */
export const caculateFlightPolygonArea = () => {
  let arr = [];
  const polygon = cloneDeep(dataInfoRect.polygon);
  polygon.forEach(polygonObj => {
    // 经度/维度
    arr.push([polygonObj.lng, polygonObj.lat]);
  });
  // 将第一个顶点坐标再次添加到数组的最后,形成闭合的多边形
  arr.push(arr[0]);
  // 做buffer 算法 按照预定的面外扩多少
  const { area, unit } = getArea([arr]);
  dataInfoRect.area = area + ' ' + unit;
  return {
    area,
    unit
  };
};

/**
 * 计算飞行线路长度
 *
 * @returns 返回计算出的长度及单位，如果无输入则返回 null
 */
export const caculateFlightLength = () => {
  if (!lineLatlngs) {
    return null;
  }
  let cartesian3Positions = [];
  // 获取飞机起飞点的经纬度
  const airPortPlaceLonglat = toRaw(parmsInfoRect.airPortPlace);
  cartesian3Positions.push(airPortPlaceLonglat);
  // 遍历 saveFiightLine 获取线的坐标
  saveFiightLine.forEach(polylineEntity => {
    const polylinePositions = polylineEntity?.polyline?.positions.getValue(Cesium.JulianDate.now()) || [];
    for (let i = 0; i < polylinePositions.length; i++) {
      let c = polylinePositions[i];
      const longlat = toDegrees(c);
      cartesian3Positions.push(longlat);
    }
  });
  lineLatlngs.forEach(lnglat => {
    cartesian3Positions.push([lnglat.lng, lnglat.lat, lnglat.height || 100]);
  });
  // 加入最后一个就是飞机起飞点
  cartesian3Positions.push(airPortPlaceLonglat);
  // 加入 飞机起飞点起飞到第一个点和 最后点到起飞点距离
  const { length, unit } = getLength(cartesian3Positions);
  dataInfoRect.length = length + ' ' + unit;
  return {
    length,
    unit
  };
};

/**
 * 计算当下的路程及速度所用的时间
 * @param {*} distance
 * @param {*} unit
 * @param {*} speedMps
 */
export const caculateFlightTime = (distance, unit = 'km', speedMps = 10) => {
  const time = calculateTime(distance, unit, speedMps || 10);
  dataInfoRect.time = time || '0s';
  return time;
};

/**
 * 这里通过距离计算 距离单位未km
 * @param {*} distance 航线长度
 * @param {*} unit 默认km
 * @param {*} speedMps 当前全局速度 m/s
 * @returns
 */
export function calculateTime(distance, unit = 'km', speedMps) {
  if (speedMps <= 0) {
    throw new Error('Speed must be greater than zero');
  }
  // 提取距离中的数字
  if (typeof distance === 'string') {
    const match = distance.match(/[\d.]+/);
    if (match) {
      distance = parseFloat(match[0]);
    } else {
      throw new Error('Invalid distance format');
    }
  }
  if (unit === 'm') {
    distance = distance / 1000;
  }
  let speedKph = speedMps * 3.6; // 将速度从米每秒转换为公里每小时
  let totalSeconds = (distance / speedKph) * 3600; // 将飞行时间转换为秒
  let hours = Math.floor(totalSeconds / 3600);
  totalSeconds %= 3600;
  let minutes = Math.floor(totalSeconds / 60);
  let seconds = Math.floor(totalSeconds % 60);
  let result = '';
  if (hours > 0) {
    result += hours + 'h';
  }
  if (minutes > 0 || hours > 0) {
    // 如果有小时，则必须有分钟，即使是0分钟
    result += minutes + 'm';
  }
  result += seconds + 's';
  return result;
}

//#endregion

//#region 计算线实体数组总航都

/**
 * 计算线实体数组总航都
 * @param {*} polylineEntities entity 数组
 * 使用示例
 * const polylineEntities = viewer.entities.values.filter(entity => entity.polyline); // 假设你已经有了Cesium Viewer实例
 * const totalLength = calculateTotalPolylineLength(polylineEntities);
 * console.log(`Total Length of All Polylines: ${totalLength} meters`);
 * @returns
 */
export function calculateTotalPolylineLength(polylineEntities) {
  function calculatePolylineLength(positions) {
    let length = 0;
    for (let i = 1; i < positions.length; i++) {
      const start = positions[i - 1];
      const end = positions[i];
      length += Cesium.Cartesian3.distance(start, end);
    }
    return length;
  }

  function getPolylinePositions(polylineEntity) {
    const positions = [];
    const polylinePositions = polylineEntity.polyline.positions.getValue(Cesium.JulianDate.now());
    for (let i = 0; i < polylinePositions.length; i++) {
      positions.push(polylinePositions[i]);
    }
    return positions;
  }

  let totalLength = 0;
  for (const polylineEntity of polylineEntities) {
    if (polylineEntity.polyline && polylineEntity.polyline.positions) {
      const positions = getPolylinePositions(polylineEntity);
      const length = calculatePolylineLength(positions);
      totalLength += length;
    }
  }
  return totalLength;
}
//#endregion

/**
 * 销毁函数
 *
 * @returns 无返回值
 */
export function dispose() {
  planInfoStore.setCurPlanData(null);
  // 清除响应式数据 重要
  initDataInfoRect();
  initParmsInfoRect();
  editTrackerStore.dataTracker.reset();
}

//#region 绘制回调
/**
 *  绘制回调
 * options
 * @param {*} options  为支点坐标
 * @param {*} options.positions  为支点坐标
 * @param {*} options.polygon 面对象
 * @param {*} options.clear = false 面
 */
export const drawHandle = options => {
  const { positions = [], clear = false } = options;
  if (clear) {
    dataInfoRect.polygon = [];
    clearWayLineEntity();
    dataInfoRect.done = false;
    // 面积时间等清除
    parmsInfoRect.area = '0 m²';
    parmsInfoRect.length = '0 m';
    parmsInfoRect.time = '0 s';
    setMapToolTips('点击地图生成测绘区域');
    // 重新开始绘制
    drawFaceWaylineHandle();
    return;
  }
  let lnglats = [];
  (positions || []).forEach(position => {
    const cartographic = Cesium.Cartographic.fromCartesian(position);
    let obj = {
      lng: Cesium.Math.toDegrees(cartographic.longitude),
      lat: Cesium.Math.toDegrees(cartographic.latitude),
      height: cartographic.height
    };
    lnglats.push(obj);
  });
  dataInfoRect.originPolygon = cloneDeep(lnglats);
  dataInfoRect.polygon = cloneDeep(lnglats);
  reBuildWayLines();
  removeMapToolTips();
};

/**
 * 重新构建路径线
 * 参数变更重新绘制航线
 * @returns 无返回值
 */
export const reBuildWayLines = () => {
  if (dataInfoRect.polygon && dataInfoRect.polygon.length > 0) {
    updateNewParms();
    createWayLines(dataInfoRect, () => {
      createWayLinesDoneCallback();
      // 计算航线面积和长度及飞行时间
      parmsInfoRect.area = dataInfoRect.area;
      parmsInfoRect.length = dataInfoRect.length;
      parmsInfoRect.time = dataInfoRect.time;
    });
  }
};

/**
 * 更新新参数
 *
 * @returns 无返回值
 * @throws 当未获取到设备相机信息时，抛出错误
 */
const updateNewParms = () => {
  //获取设备信息
  let cameraType = getCurrentDeviceCameraType();
  if (!cameraType) {
    throw new Error('未找到当前设备的相机类型！');
  }
  // 线计算高度值
  caculateHeights();
  let cameraInfo = getCmosData({ camera: cameraType }) ?? null;
  if (!cameraInfo) {
    throw new Error('未获取到设备相机信息');
  }
  //#region 计算旁向重叠率 这里使用第一个相机计算即可
  const { equivalentFocalLength = 24, focalLength, width, height } = cameraInfo;
  let caculateSideOverlap = getSideOverlap(
    parmsInfoRect.realFlightHight,
    width || 35,
    focalLength || equivalentFocalLength,
    parmsInfoRect.orthoCameraOverlapW
  );
  dataInfoRect.space = Number(caculateSideOverlap) || Number(parmsInfoRect.space);
  //#endregion

  //#region 根据航向角度计算最大速度，通过该值限定速度值只能在0-最大值之间
  let caculateCourseOverlap = getCourseOverlap(
    parmsInfoRect.realFlightHight,
    height || 24,
    focalLength || equivalentFocalLength,
    parmsInfoRect.orthoCameraOverlapH
  );
  let courseOverlapDistance = Number(caculateCourseOverlap);
  // 通过传入当前的距离值计算合适的速度和间隔拍照时间
  const { t, maxSpeed } = calculateDroneSpeedAndTime(courseOverlapDistance);
  // 计算无人机的速度范围 比如无人机最低拍摄时间为2秒每张 速度最大为15米每秒
  parmsInfoRect.maxSpeed = maxSpeed;
  // 速度值不能超过最大值
  parmsInfoRect.autoFlightSpeed = parmsInfoRect.autoFlightSpeed > maxSpeed ? maxSpeed : parmsInfoRect.autoFlightSpeed;
  parmsInfoRect.shootParms = t;
  dataInfoRect.speed = Number(parmsInfoRect.autoFlightSpeed);
  //#endregion

  //#region 根据高程类型计算真实的飞行高度
  dataInfoRect.height = Number(parmsInfoRect.realFlightHight);
  //#endregion

  dataInfoRect.rotate = Number(parmsInfoRect.direction);
  dataInfoRect.margin = Number(parmsInfoRect.margin);

  // 创建面
  dataInfoRect.polygon = caculatePolygonMargin(dataInfoRect, dataInfoRect.margin);
};
/**
 * 创建航迹线完成回调函数
 *
 * @returns 无返回值
 */
const createWayLinesDoneCallback = () => {
  caculateFlightPolygonArea();
  const { length, unit } = caculateFlightLength();
  caculateFlightTime(length, unit || 'km', parmsInfoRect.autoFlightSpeed) || '0s';
};
//#endregion

//#region 初始化提交信息
export const initWayLineInfo = json => {
  try {
    if (!json) {
      return;
    }
    uploadWaylinesJson.wayline_id = json.id;
    uploadWaylinesJson.name = json.name || '航面-新建面状航线';
    uploadWaylinesJson.drone_model = json.drone_model_key;
    uploadWaylinesJson.payload_model = json.payload_model_keys;
    uploadWaylinesJson.template_type = json.template_types;

    if (typeof json.airline_json === 'string') {
      json.airline_json = JSON.parse(json.airline_json);
    }
    if (typeof uploadWaylinesJson.airline_json === 'string') {
      uploadWaylinesJson.airline_json = JSON.parse(uploadWaylinesJson.airline_json);
    }
    uploadWaylinesJson.airline_json.template = json.airline_json.template;
    uploadWaylinesJson.airline_json.config = json.airline_json.config;
    uploadWaylinesJson.airline_json.wayLines = json.airline_json.wayLines;
  } catch (error) {
    console.log('初始化航迹线信息失败', error);
  }
};
//#endregion

//#region 数据监听
watch(
  () => JSON.stringify(parmsInfoRect),
  (newValue, oldValue) => {
    // 比较新旧值是否相同
    if (!deepEqual(newValue, oldValue)) {
      // 如果不同,则说明数据被编辑过
      editTrackerStore.dataTracker.markAsModified();
    }
  },
  { deep: true }
);
//#endregion

//#region 当前相机获取

/**
 * 获取当前选中的设备的相机信息 选中其中一个 如果两个都选那么选中第一个 如果只选中一个那么就返回被选中的类型
 */

export const getCurrentDeviceCameraType = () => {
  if (!deviceStore) {
    return null;
  }
  let arr = deviceStore?.getCameraSelect() ?? [];
  if (arr.length === 0) {
    return null;
  }
  return arr[0] ?? null;
};

//#endregion

//#region 绘制事件
export let drawer = null;
export const drawFaceWaylineHandle = () => {
  if (!drawer) {
    return;
  }
  // removeMapToolTips();
  // reBuildWayLines();
  if (!dataInfoRect.done) {
    setMapToolTips('点击地图生成测绘区域');
  }
  if (drawer.state !== 0) {
    drawer.drawing(drawHandle);
  }
};

export const setCurrentDrawrObject = d => {
  drawer = d;
};
//#endregion
