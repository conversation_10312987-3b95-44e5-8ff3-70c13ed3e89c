import { ref, reactive } from 'vue';
import * as Cesium from 'cesium';
import {
  getOrCreateCesiumEngineInstance,
  CesiumLayerManager,
  veclayer,
  imglayer,
  cvalayer,
  cialayer,
  setCameraView,
  calculatArea,
  calculatPerimeter
} from '@/components/Cesium/libs/cesium';
import { drawPolygon, isPolygonCreate, polygonDispose } from '@/components/Cesium/libs/cesium/superEdit/PolygonCreater';
import { drawCircle, circleDispose, isCircleCreate } from '@/components/Cesium/libs/cesium/superEdit/CircleCreater';
import { ElMessage } from 'element-plus';
import { getDeptSysSetting } from '@/api/wayline';

// Cesium相关变量
export let viewer = null;
export let scene = null;

// 编辑状态
export const editStatus = ref(false);

// 围栏类型
export const FENCE_TYPE = {
  POLYGON: 'polygon',
  CIRCLE: 'circle'
};

// 围栏颜色
export const FENCE_COLOR = {
  DEFAULT: new Cesium.Color.fromCssColorString('#2E90FA').withAlpha(0.6),
  SELECTED: new Cesium.Color.fromCssColorString('#FF4500').withAlpha(0.6)
};

// 当前绘制的围栏数据
export const currentFenceData = reactive({
  type: FENCE_TYPE.POLYGON,
  coordinates: [],
  radius: 0,
  center: [],
  area: 0,
  perimeter: 0
});

/**
 * 初始化Cesium地图
 */
export const initMap = async () => {
  try {
    const engine = getOrCreateCesiumEngineInstance('fenceEdit');
    engine?.init('cesium-container');
    viewer = engine.viewer;
    scene = viewer.scene;

    // 添加图层
    const layerManager = new CesiumLayerManager(viewer);
    layerManager.addLayer(imglayer);
    layerManager.addLayer(cialayer); 
    // layerManager.addLayer(veclayer);
    // layerManager.addLayer(cvalayer); 

    // 设置相机视角
    await getDeptSysSetting({})
      .then(res => {
        const { wayline_config = {} } = res;
        setCameraView(viewer, {
          destination: Cesium.Cartesian3.fromDegrees(
            Number(wayline_config.longitude),
            Number(wayline_config.latitude),
            10000
          ),
          orientation: new Cesium.HeadingPitchRoll(
            Cesium.Math.toRadians(0),
            Cesium.Math.toRadians(-90),
            Cesium.Math.toRadians(0)
          ),
          duration: 0.75
        });
      })
      .catch(ex => {
        console.log(ex);
      });
  } catch (error) {
    console.error('初始化Cesium地图失败', error);
    ElMessage.error('初始化地图失败');
    return null;
  }
};

/**
 * 开始绘制多边形围栏
 * @param {Function} callback - 绘制完成后的回调函数
 */
export const startDrawPolygon = callback => {
  if (!viewer) {
    ElMessage.error('地图未初始化');
    return;
  }

  // 设置鼠标样式
  document.body.style.cursor = 'crosshair';

  // 清除现有实体
  clearMap();

  // 调用多边形绘制工具
  drawPolygon(viewer, { color: FENCE_COLOR.DEFAULT }, data => {
    if (!data) return;

    document.body.style.cursor = 'default';
    editStatus.value = true;
   
    // 更新当前围栏数据
    currentFenceData.type = FENCE_TYPE.POLYGON;
    currentFenceData.coordinates = data.data.cartesianPoints.map(position => {
      const cartographic = Cesium.Cartographic.fromCartesian(position);
      return [Cesium.Math.toDegrees(cartographic.longitude), Cesium.Math.toDegrees(cartographic.latitude)];
    });

    // 计算面积和周长
    currentFenceData.area = data.data.area;
    currentFenceData.perimeter = data.data.length;

    // 回调函数
    callback && callback(currentFenceData);
  });
};

/**
 * 清除地图上的围栏
 */
export const clearMap = () => {
  if (!viewer) return;

  // 取消正在进行的绘制
  if (isPolygonCreate.value) {
    polygonDispose();
  }

  if (isCircleCreate.value) {
    circleDispose();
  }

  // 清除所有实体
  viewer.entities.removeAll();

  // 重置鼠标样式
  document.body.style.cursor = 'default';

  // 重置当前围栏数据
  currentFenceData.type = FENCE_TYPE.POLYGON;
  currentFenceData.coordinates = [];
  currentFenceData.radius = 0;
  currentFenceData.center = [];
  currentFenceData.area = 0;
  currentFenceData.perimeter = 0;

  editStatus.value = false;
};

/**
 * 绘制已有的围栏
 * @param {Array} coordinates - 围栏坐标
 * @param {String} type - 围栏类型
 */
export const drawExistingFence = coordinates => {
  if (!viewer || !coordinates || coordinates.length === 0) return;

  // 清除现有实体
  clearMap();

  let entity = null;

  // 多边形围栏
  const positions = coordinates.map(coord => Cesium.Cartesian3.fromDegrees(coord[0], coord[1]));

  // 创建多边形实体
  entity = viewer.entities.add({
    polygon: {
      hierarchy: new Cesium.PolygonHierarchy(positions),
      material: FENCE_COLOR.DEFAULT,
      outline: true,
      outlineColor: Cesium.Color.fromCssColorString('#2E90FA'),
      outlineWidth: 2
    }
  });

  // 更新当前围栏数据
  currentFenceData.type = FENCE_TYPE.POLYGON;
  currentFenceData.coordinates = coordinates;
  currentFenceData.area = calculatArea(positions);
  currentFenceData.perimeter = calculatPerimeter(positions);

  // 调整视图以包含整个围栏
  viewer.zoomTo(entity);

  return entity;
};

/**
 * 销毁地图
 */
export const destroyMap = () => {
  if (viewer) {
    viewer.entities.removeAll();
    viewer.destroy();
    viewer = null;
    scene = null;
  }
};
