<template>
  <el-dialog
    :model-value="visible"
    :title="isEdit ? '编辑图层' : '新增图层'"
    width="800px"
    :close-on-click-modal="false"
    @update:model-value="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" label-position="right" class="layer-form">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="图层名称" prop="layerName">
            <el-input v-model="form.layerName" placeholder="请输入图层名称" maxlength="16" show-word-limit />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="图层类型" prop="layerType">
            <el-select v-model="form.layerType" placeholder="请选择图层类型" style="width: 100%">
              <el-option v-for="item in layerTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="最小级别" prop="minLevel">
            <div class="slider-container">
              <el-slider
                v-model="form.minLevel"
                :min="1"
                :max="18"
                :step="1"
                show-input
                :show-input-controls="false"
                input-size="small"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最大级别" prop="maxLevel">
            <div class="slider-container">
              <el-slider
                v-model="form.maxLevel"
                :min="1"
                :max="18"
                :step="1"
                show-input
                :show-input-controls="false"
                input-size="small"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="是否显示" prop="isVisible">
            <el-switch v-model="form.isVisible" active-text="显示" inactive-text="隐藏" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sortOrder">
            <el-input-number
              v-model="form.sortOrder"
              :min="0"
              :max="9999"
              style="width: 100%"
              placeholder="数值越小越靠前"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="标签字段" prop="labelField">
            <el-select v-model="form.labelField" placeholder="请选择标签字段" style="width: 100%">
              <el-option label="图元名称" value="feature_name" />
              <el-option label="图元地址" value="feature_addr" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="标签颜色">
            <el-color-picker
              v-model="form.labelColor"
              show-alpha
              :predefine="predefineColors"
              color-format="hex"
              size="default"
              class="square-color-picker"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="字体大小">
            <el-input-number
              v-model="fontConfig.size"
              :min="8"
              :max="72"
              :step="1"
              style="width: 100%"
              @change="updateLabelFont"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="字体类型">
            <el-select
              v-model="fontConfig.family"
              placeholder="请选择字体"
              style="width: 100%"
              @change="updateLabelFont"
            >
              <el-option v-for="font in fontOptions" :key="font.value" :label="font.label" :value="font.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 图层样式配置 - 独立区域 -->
      <div class="style-config-wrapper">
        <!-- 点图层样式 -->
        <div v-if="form.layerType === 'point'" class="style-config-content">
          <div class="style-section-subtitle">点图层样式</div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="图标URL" label-width="100px">
                <div class="icon-selector">
                  <div class="icon-input-wrapper">
                    <div v-if="styleConfig.point.icon" class="icon-preview-small">
                      <img :src="styleConfig.point.icon" alt="当前图标" />
                    </div>
                    <el-input
                      v-model="styleConfig.point.icon"
                      placeholder="请输入图标URL或选择预设图标"
                      @input="updateLayerConfig"
                    />
                  </div>
                  <el-button type="primary" @click="showIconSelector">选择图标</el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 线图层样式 -->
        <div v-if="form.layerType === 'line'" class="style-config-content">
          <div class="style-section-subtitle">线图层样式</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="填充颜色" label-width="100px">
                <el-color-picker
                  v-model="styleConfig.line.fillColor"
                  show-alpha
                  :predefine="predefineColors"
                  color-format="hex"
                  size="default"
                  @change="updateLayerConfig"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="边线颜色" label-width="100px">
                <el-color-picker
                  v-model="styleConfig.line.borderColor"
                  show-alpha
                  :predefine="predefineColors"
                  color-format="hex"
                  size="default"
                  @change="updateLayerConfig"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="边线宽度" label-width="100px">
                <div class="slider-container">
                  <el-slider
                    v-model="styleConfig.line.borderWidth"
                    :min="1"
                    :max="10"
                    :step="1"
                    show-input
                    :show-input-controls="false"
                    input-size="small"
                    @change="updateLayerConfig"
                  />
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 面图层样式 -->
        <div v-if="form.layerType === 'polygon'" class="style-config-content">
          <div class="style-section-subtitle">面图层样式</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="填充颜色" label-width="100px">
                <el-color-picker
                  v-model="styleConfig.polygon.fillColor"
                  show-alpha
                  :predefine="predefineColors"
                  color-format="hex"
                  size="default"
                  @change="updateLayerConfig"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="边框颜色" label-width="100px">
                <el-color-picker
                  v-model="styleConfig.polygon.borderColor"
                  show-alpha
                  :predefine="predefineColors"
                  color-format="hex"
                  size="default"
                  @change="updateLayerConfig"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="边框宽度" label-width="100px">
                <div class="slider-container">
                  <el-slider
                    v-model="styleConfig.polygon.borderWidth"
                    :min="1"
                    :max="10"
                    :step="1"
                    show-input
                    :show-input-controls="false"
                    input-size="small"
                    @change="updateLayerConfig"
                  />
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 其他图层类型的样式配置 -->
        <div v-if="['roadnetwork', 'tile'].includes(form.layerType)" class="style-config-content">
          <el-alert title="该图层类型暂不支持样式配置" type="info" :closable="false" show-icon />
        </div>
      </div>

      <el-form-item label="备注">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 图标选择器弹窗 -->
  <el-dialog
    v-model="iconSelectorVisible"
    title="选择图标"
    width="600px"
    :close-on-click-modal="false"
    @close="closeIconSelector"
  >
    <div class="icon-grid">
      <div
        v-for="icon in builtInIcons"
        :key="icon.path"
        class="icon-item"
        :title="icon.name"
        @click="selectIcon(icon.path)"
      >
        <div class="icon-preview">
          <img :src="icon.path" :alt="icon.name" />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeIconSelector">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { createLayer, updateLayer } from '@/api/map/layer';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'success']);

const formRef = ref();
const loading = ref(false);

// 字体配置
const fontConfig = reactive({
  size: 12,
  family: 'Arial'
});

// 样式配置
const styleConfig = reactive({
  point: {
    icon: ''
  },
  line: {
    fillColor: '#2E90FA',
    borderColor: '#1976D2',
    borderWidth: 2
  },
  polygon: {
    fillColor: '#2E90FA80', // 带透明度
    borderColor: '#1976D2',
    borderWidth: 2
  }
});

// 表单数据
const form = reactive({
  layerName: '',
  layerType: '',
  minLevel: 1,
  maxLevel: 20,
  isVisible: true,
  labelField: '',
  labelColor: '#FF0000',
  labelFont: JSON.stringify({ size: 12, family: 'Arial' }),
  layerConfig: '',
  sortOrder: 0,
  remark: ''
});

// 图层类型选项
const layerTypeOptions = [
  { label: '点图层', value: 'point' },
  { label: '线图层', value: 'line' },
  { label: '面图层', value: 'polygon' }
  // { label: '路网图层', value: 'roadnetwork' },
  // { label: '栅格图层', value: 'tile' }
];

// 预定义颜色
const predefineColors = [
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  '#FF0000',
  '#00FF00',
  '#0000FF'
];

// Cesium支持的字体选项
const fontOptions = [
  { label: 'Arial', value: 'Arial' },
  // { label: 'Arial Black', value: 'Arial Black' },
  { label: 'Helvetica', value: 'Helvetica' },
  // { label: 'Times New Roman', value: 'Times New Roman' },
  { label: 'Times', value: 'Times' },
  // { label: 'Courier New', value: 'Courier New' },
  { label: 'Courier', value: 'Courier' },
  { label: 'Verdana', value: 'Verdana' },
  { label: 'Georgia', value: 'Georgia' },
  { label: 'Palatino', value: 'Palatino' },
  { label: 'Garamond', value: 'Garamond' },
  { label: 'Bookman', value: 'Bookman' },
  // { label: 'Comic Sans MS', value: 'Comic Sans MS' },
  // { label: 'Trebuchet MS', value: 'Trebuchet MS' },
  { label: 'Impact', value: 'Impact' }
];

// 表单验证规则
const rules = {
  layerName: [
    { required: true, message: '请输入图层名称', trigger: 'blur' },
    { min: 1, max: 16, message: '长度在 1 到 16 个字符', trigger: 'blur' }
  ],
  layerType: [{ required: true, message: '请选择图层类型', trigger: 'change' }],
  minLevel: [
    { required: true, message: '请输入最小级别', trigger: 'blur' },
    { type: 'number', min: 1, max: 20, message: '最小级别范围为 1-20', trigger: 'blur' }
  ],
  maxLevel: [
    { required: true, message: '请输入最大级别', trigger: 'blur' },
    { type: 'number', min: 1, max: 20, message: '最大级别范围为 1-20', trigger: 'blur' }
  ],
  labelField: [
    {
      validator: (_, value, callback) => {
        // 如果"是否显示"为true，则标签字段必须有值
        if (form.isVisible && (!value || value.trim() === '')) {
          callback(new Error('当选择显示时，必须选择标签字段'));
        } else {
          callback();
        }
      },
      trigger: ['blur', 'change']
    }
  ]
};

/**
 * 更新标签字体
 */
function updateLabelFont() {
  form.labelFont = JSON.stringify({
    size: fontConfig.size,
    family: fontConfig.family
  });
}

/**
 * 解析字体字符串
 */
function parseLabelFont(fontStr) {
  if (!fontStr) return;

  try {
    // 尝试解析JSON格式
    const fontObj = JSON.parse(fontStr);
    if (fontObj.size && fontObj.family) {
      fontConfig.size = fontObj.size;
      fontConfig.family = fontObj.family;
      return;
    }
  } catch (error) {
    // 如果不是JSON格式，尝试解析传统格式 "12px Arial"
    const match = fontStr.match(/^(\d+)px\s+(.+)$/);
    if (match) {
      fontConfig.size = parseInt(match[1]) || 12;
      fontConfig.family = match[2] || 'Arial';
      // 转换为新格式
      updateLabelFont();
    }
  }
}

/**
 * 更新图层配置
 */
function updateLayerConfig() {
  let config = {};

  switch (form.layerType) {
    case 'point':
      config = {
        type: 'point',
        icon: styleConfig.point.icon || '',
        size: [32, 32]
      };
      break;
    case 'line':
      config = {
        type: 'line',
        fillColor: styleConfig.line.fillColor || '#2E90FA',
        borderColor: styleConfig.line.borderColor || '#1976D2',
        borderWidth: styleConfig.line.borderWidth || 2
      };
      break;
    case 'polygon':
      config = {
        type: 'polygon',
        fillColor: styleConfig.polygon.fillColor || '#2E90FA80',
        borderColor: styleConfig.polygon.borderColor || '#1976D2',
        borderWidth: styleConfig.polygon.borderWidth || 2
      };
      break;
    default:
      config = {};
  }

  form.layerConfig = JSON.stringify(config);
}

/**
 * 解析图层配置
 */
function parseLayerConfig(configStr) {
  if (!configStr) return;

  try {
    const config = JSON.parse(configStr);

    switch (config.type) {
      case 'point':
        styleConfig.point.icon = config.icon || '';
        break;
      case 'line':
        styleConfig.line.fillColor = config.fillColor || '#2E90FA';
        styleConfig.line.borderColor = config.borderColor || '#1976D2';
        styleConfig.line.borderWidth = config.borderWidth || 2;
        break;
      case 'polygon':
        styleConfig.polygon.fillColor = config.fillColor || '#2E90FA80';
        styleConfig.polygon.borderColor = config.borderColor || '#1976D2';
        styleConfig.polygon.borderWidth = config.borderWidth || 2;
        break;
    }
  } catch (error) {
    console.warn('解析图层配置失败:', error);
  }
}

// 图标选择器相关
const iconSelectorVisible = ref(false);
const builtInIcons = [
  { path: '/resource/images/lyr_icon/alarm.png' },
  { path: '/resource/images/lyr_icon/dzzh.png' },
  { path: '/resource/images/lyr_icon/flag.png' },
  { path: '/resource/images/lyr_icon/frame.png' },
  { path: '/resource/images/lyr_icon/home.png' },
  { path: '/resource/images/lyr_icon/hydrant.png' },
  { path: '/resource/images/lyr_icon/location.png' },
  { path: '/resource/images/lyr_icon/package.png' },
  { path: '/resource/images/lyr_icon/people.png' },
  { path: '/resource/images/lyr_icon/people2.png' },
  { path: '/resource/images/lyr_icon/pnt.png' },
  { path: '/resource/images/lyr_icon/rescuer.png' },
  { path: '/resource/images/lyr_icon/start.png' },
  { path: '/resource/images/lyr_icon/weather.png' },
  { path: '/resource/images/lyr_icon/car.png' },
  { path: '/resource/images/lyr_icon/build.png' }
];

/**
 * 显示图标选择器
 */
function showIconSelector() {
  iconSelectorVisible.value = true;
}

/**
 * 选择图标
 */
function selectIcon(iconPath) {
  styleConfig.point.icon = iconPath;
  updateLayerConfig();
  iconSelectorVisible.value = false;
}

/**
 * 关闭图标选择器
 */
function closeIconSelector() {
  iconSelectorVisible.value = false;
}

// 监听表单数据变化
watch(
  () => props.formData,
  newData => {
    if (newData && Object.keys(newData).length > 0) {
      Object.assign(form, {
        layerName: newData.layerName || '',
        layerType: newData.layerType || '',
        minLevel: newData.minLevel || 1,
        maxLevel: newData.maxLevel || 20,
        isVisible: newData.isVisible !== undefined ? newData.isVisible : true,
        labelField: newData.labelField || '',
        labelColor: newData.labelColor || '#FF0000',
        labelFont: newData.labelFont || JSON.stringify({ size: 12, family: 'Arial' }),
        layerConfig: newData.layerConfig || '',
        sortOrder: newData.sortOrder || 0,
        remark: newData.remark || ''
      });

      // 解析字体配置
      parseLabelFont(form.labelFont);
      // 解析样式配置
      parseLayerConfig(form.layerConfig);
    } else {
      // 重置表单
      Object.assign(form, {
        layerName: '',
        layerType: '',
        minLevel: 1,
        maxLevel: 20,
        isVisible: true,
        labelField: '',
        labelColor: '#FF0000',
        labelFont: JSON.stringify({ size: 12, family: 'Arial' }),
        layerConfig: '',
        sortOrder: 0,
        remark: ''
      });

      // 重置字体配置
      fontConfig.size = 12;
      fontConfig.family = 'Arial';

      // 重置样式配置
      styleConfig.point.icon = '';
      styleConfig.line.fillColor = '#2E90FA';
      styleConfig.line.borderColor = '#1976D2';
      styleConfig.line.borderWidth = 2;
      styleConfig.polygon.fillColor = '#2E90FA80';
      styleConfig.polygon.borderColor = '#1976D2';
      styleConfig.polygon.borderWidth = 2;
    }
  },
  { immediate: true, deep: true }
);

// 监听"是否显示"状态变化，重新验证标签字段
watch(
  () => form.isVisible,
  () => {
    // 当"是否显示"状态改变时，重新验证标签字段
    nextTick(() => {
      if (formRef.value) {
        formRef.value.validateField('labelField');
      }
    });
  }
);

/**
 * 关闭弹窗
 */
function handleClose() {
  emit('update:visible', false);
  // 重置表单验证
  nextTick(() => {
    formRef.value?.resetFields();
  });
}

/**
 * 字段名转换：驼峰转下划线
 */
function toSnakeCase(obj) {
  if (Array.isArray(obj)) {
    return obj.map(item => toSnakeCase(item));
  } else if (obj !== null && typeof obj === 'object') {
    const newObj = {};
    Object.keys(obj).forEach(key => {
      const snakeKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
      newObj[snakeKey] = toSnakeCase(obj[key]);
    });
    return newObj;
  }
  return obj;
}

/**
 * 提交表单
 */
async function handleSubmit() {
  try {
    await formRef.value.validate();

    // 验证最小级别不能大于最大级别
    if (form.minLevel > form.maxLevel) {
      ElMessage.error('最小级别不能大于最大级别');
      return;
    }

    // 验证JSON格式
    if (form.layerConfig) {
      try {
        JSON.parse(form.layerConfig);
      } catch (error) {
        ElMessage.error('图层配置必须是有效的JSON格式');
        return;
      }
    }

    loading.value = true;

    // 转换为下划线格式的数据
    const submitData = toSnakeCase({ ...form });
    if (props.isEdit) {
      submitData.id = props.formData.id;
      await updateLayer(submitData);
      ElMessage.success('更新成功');
    } else {
      await createLayer(submitData);
      ElMessage.success('创建成功');
    }

    emit('success');
  } catch (error) {
    console.error('提交失败:', error);
    ElMessage.error('操作失败');
  } finally {
    loading.value = false;
  }
}
</script>

<style lang="scss" scoped>
.layer-form {
  padding: 0 8px;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}

.slider-container {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;

  :deep(.el-slider) {
    flex: 1;
  }
}

// 样式配置区域包装器
.style-config-wrapper {
  width: 100%;
  overflow: hidden;
}

// 样式配置标题区域
.style-config-header {
  padding: 12px 16px;

  .style-config-title {
    font-size: 14px;
    font-weight: 500;
    color: #606266;
  }
}

// 样式配置内容区域
.style-config-content {
  // padding: 20px 16px;
  .style-section-subtitle {
    font-size: 13px;
    font-weight: 500;
    color: #606266;
    margin-bottom: 16px;
    position: relative;
  }
}

.icon-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;

  .icon-input-wrapper {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;

    .icon-preview-small {
      position: absolute;
      left: 8px;
      top: 50%;
      transform: translateY(-50%);
      width: 20px;
      height: 20px;
      z-index: 10;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }
    }

    .el-input {
      :deep(.el-input__inner) {
        padding-left: 36px;
      }
    }
  }

  .el-button {
    flex-shrink: 0;
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-color-picker) {
  width: 100%;
}

:deep(.el-slider__input) {
  width: 80px !important;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

// 优化颜色选择器样式
:deep(.el-color-picker__trigger) {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;

  &:hover {
    border-color: #409eff;
  }
}

// 优化滑块样式
:deep(.el-slider__runway) {
  background-color: #e4e7ed;
}

:deep(.el-slider__bar) {
  background-color: #409eff;
}

:deep(.el-slider__button) {
  border: 2px solid #409eff;
  background-color: #fff;
}

// 图标选择器样式
.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  gap: 12px;
  padding: 16px 0;
  max-height: 400px;
  overflow-y: auto;
}

.icon-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  padding: 8px;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fff;

  &:hover {
    border-color: #409eff;
    background-color: #f0f9ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  }

  &:active {
    transform: translateY(0);
  }
}

.icon-preview {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
}
</style>
