<template>
  <el-dialog
    :title="title"
    v-if="visible"
    :model-value="visible"
    width="600px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form class="app-form" ref="dataFormRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="任务名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入任务名称"
          maxlength="50"
          @blur="form.name = $event.target.value.trim()"
        />
      </el-form-item>
      <el-form-item label="任务执行方式" prop="task_type">
        <el-select v-model="form.task_type" placeholder="请选择任务执行方式" @change="changeTaskType">
          <el-option
            v-for="item in optionData.typeOptions"
            :key="item.value"
            :label="item?.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.task_type === TASK_TYPE.Timed" label="选择日期" required prop="timed_time">
        <el-date-picker
          v-model="form.timed_time"
          type="datetime"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择日期时间"
          :disabled-date="disabledDate"
        />
      </el-form-item>
      <el-form-item v-if="form.task_type === TASK_TYPE.Condition" label="选择日期" prop="rangTime">
        <el-date-picker
          v-model="form.rangTime"
          type="daterange"
          start-placeholder="开始日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          end-placeholder="结束日期"
          :disabled-date="disabledDate"
        />
      </el-form-item>
      <el-form-item
        v-if="form.task_type === TASK_TYPE.Condition"
        label="选择时间"
        prop="select_time"
        ref="select_execute_ref"
        required
      >
        <div class="select-time" v-for="(n, index) in select_time_number" :key="n">
          <el-time-picker
            style="width: 200px;"
            v-model="form.select_time[n - 1]"
            :placeholder="'请选择'"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            @change="timeChange"
          />
          <!-- <template v-if="form.task_type === TASK_TYPE.Condition">
            <div><span style="color: white">-</span></div>
            <el-time-picker v-model="form.select_time[n - 1][1]" placeholder="结束时间" format="HH:mm:ss" />
          </template> -->

          <el-button
            style="margin-left: 20px"
            :icon="Plus"
            type="primary"
            circle
            size="small"
            @click="addTime"
            v-if="form.task_type === TASK_TYPE.Condition"
          />
          <el-button
            :icon="Minus"
            type="danger"
            circle
            size="small"
            v-if="form.task_type === TASK_TYPE.Condition"
            @click="removeTime(index)"
            :disabled="select_time_number === 1"
          />
        </div>
      </el-form-item>

      <el-form-item v-if="form.task_type === TASK_TYPE.Condition" label="重复频率" required prop="period_type">
        <!-- <el-input-number v-model="form.num" controls-position="right" :min="1" :max="9999" style="width: 200px;margin-right: 15px;margin-left: 15px"/> -->
        <el-select v-model="form.period_type" placeholder="请选择重复频率">
          <el-option
            v-for="item in optionData.timeOptions"
            :key="item.value"
            :label="item?.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item v-if="form.period_type === 2" label="每周重复天数" required prop="days_of_week">
        <el-select v-model="form.days_of_week" placeholder="请选择每周重复天数" multiple>
          <el-option
            v-for="item in optionData.weekOptions"
            :key="item.value"
            :label="item?.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <!-- <el-form-item
        v-if="form.task_type === TASK_TYPE.Condition"
        label="电池电量达到多少时开始(%)"
        prop="min_battery_capacity"
      >
        <el-input-number
          v-model="form.min_battery_capacity"
          :min="50"
          :max="100"
          :step="1"
          controls-position="right"
          placeholder="请输入电池电量"
          @change="handleChange"
        />
      </el-form-item>
      <el-form-item
        v-if="form.task_type === TASK_TYPE.Condition"
        label="存储级别达到多少时启动任务(%)"
        prop="min_storage_capacity"
      >
        <el-input-number
          v-model="form.min_storage_capacity"
          :min="50"
          :max="100"
          :step="1"
          controls-position="right"
          placeholder="请输入电池电量"
          @change="handleChange"
        />
      </el-form-item> -->

      <el-form-item label="执行机场" prop="dock_sn">
        <el-select v-model="form.dock_sn" placeholder="请选择执行机场" :fit-input-width="true" @change="changeDock">
          <el-option
            v-for="item in exeAirportOptions"
            :key="item.device_sn"
            :label="item.nickname"
            :value="item.device_sn"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="无人机" prop="device_name" required>
        <el-input disabled v-model="form.device_name" placeholder='无人机型号'/>
      </el-form-item>

      <el-form-item label="选择航线" prop="file_id">
        <el-select v-model="form.file_id" placeholder="请选择航线" @change="changeFileID" filterable>
          <el-option v-for="item in airLineOptions" :key="item.id" :label="item?.name" :value="item.id" />
        </el-select>
      </el-form-item>

      <el-form-item label="返航高度(m)" prop="rth_altitude">
        <el-input disabled v-model="form.rth_altitude" />
      </el-form-item>
      <el-form-item label="航线失控动作" prop="out_of_control">
        <el-select disabled v-model="form.out_of_control" placeholder="请选择航线失控动作">
          <el-option
            v-for="item in optionData.outofControlActionList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-loading="loading">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import optionData from '@/utils/option-data';
import moment from 'moment';
import { getWaylines } from '@/api/wayline';
import { getDevicesBound } from '@/api/devices';
import {  addAirTaskList } from '@/api/task';
import { DOMAIN, TASK_TYPE } from '@/utils/constants';
import { Plus, Minus } from '@element-plus/icons-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  formData: {
    type: Object,
    default(rawProps) {
      return {};
    }
  }
});
const form = reactive({});
const dataFormRef = ref(ElForm);
const airLineOptions = ref([]);
const exeAirportOptions = ref([]);
const select_time_number = ref(1);
const select_execute_ref = ref();
const fileName = ref('')
const wayline_type = ref(null);
watch(
  () => props.formData,
  (newVal, oldVal) => {
    Object.assign(form, newVal);
  },
  { deep: true }
);
const emit = defineEmits(['update:visible', 'submit']);
const rules = reactive({
  name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  task_type: [{ required: true, message: '请选择任务执行方式', trigger: 'blur' }],
  rangTime: [{ required: true, message: '请选择日期', trigger: 'blur' }],
  select_time: [
    {
      validator: async (rule, value, callback) => {
        validStartTime(callback);
        if (form.select_time.length < select_time_number.value) {
          callback(new Error('选择时间'));
        }
        // validOverlapped(callback);
      }
    }
  ],
  timed_time: [{ required: true, message: '请选择日期时间', trigger: ['change','blur'] }],
  select_execute_rang_time: [{ required: true, message: '请选择周期时间', trigger: 'blur' }],
  dock_sn: [{ required: true, message: '请选择执行机场', trigger: ['change','blur'] }],
  device_name: [{ required: true, message: '请选择执行机场', trigger: ['change','blur'] }],
  file_id: [{ required: true, message: '请选择航线', trigger: ['change','blur']}],
  rth_altitude: [{ required: true, message: '请输入返航高度', trigger: 'blur' }],
  out_of_control: [
    {
      required: true,
      message: '请选择航线失控动作',
      trigger: ['blur']
    }
  ],
  days_of_week: [{ required: true, message: '请选择每周执行日期', trigger: 'blur' }],
  period_type: [{ required: true, message: '请选择重复频率', trigger: ['change','blur']}],
});

defineExpose({ setDefaultValue });
function timeChange() {
  select_execute_ref.value.validate();
}
function disabledDate(time) {
  return moment(time) < moment().subtract(1, 'days');
}
function validStartTime(callback) {
  for (let i = 0; i < form.select_time.length; i++) {
    if (!form.select_time[i]) {
      callback(new Error('请选择时间'));
    }
  }
}
function validOverlapped(callback) {
  if (TASK_TYPE.Condition !== form.task_type) return;
  const arr = form.select_time.slice();
  arr.sort((a, b) => moment(a[0]).unix() - moment(b[0]).unix());
  arr.forEach((v, i, arr) => {
    if (i > 0 && v[0] < arr[i - 1]) {
      throw new Error('Overlapping time periods.');
    }
  });
}

function changeDock (value) {
  exeAirportOptions.value.forEach(item=>{
   if(item.device_sn == value) {
    form.device_name = item.children.device_name
    if(fileName.value !='' && fileName.value != form.device_name) {
      ElMessage.warning('执行任务所选的设备与航线的设备不一致，请修改航线或执行设备。继续选择该设备可能导致部分航线动作无法正常执行。')
    }
   }
  })
}

function changeFileID (value) {
  airLineOptions.value.forEach(item=>{
   if(item.id == value) {
    fileName.value = item.drone_model_key == '0-91-1' ? 'M3TD' : 'Mavic 3T'
    if(form.device_name !='' && form.device_name != undefined && fileName.value != form.device_name) {
      ElMessage.warning('执行任务所选的设备与航线的设备不一致，请修改航线或执行设备。继续选择该设备可能导致部分航线动作无法正常执行。')
    }
   }
  })
}

function changeTaskType(value) {
  if (value == 0) {
    form.timed_time = '';
    form.rangTime = [];
    form.select_time = [];
    form.period_type = '';
    form.days_of_week = '';
    select_time_number.value = 1;
  } else if (value == 1) {
    form.rangTime = [];
    form.select_time = [];
    form.period_type = '';
    form.days_of_week = '';
    select_time_number.value = 1;
  } else {
    form.timed_time = '';
  }
}

// 设置默认值
function setDefaultValue() {
  if (!form.task_type && optionData.typeOptions.length > 0) {
    form.task_type = optionData.typeOptions[0].value;
  }
  if (!form.rth_altitude) {
    form.rth_altitude = 100;
  }
  if (!form.out_of_control && optionData.outofControlActionList.length > 0) {
    form.out_of_control = optionData.outofControlActionList[0].value;
  }
  if (!form.select_time) {
    form.select_time = [[]];
  }
}
const loading = ref(false);

// 关闭弹窗
function closeDialog() {
  resetForm();
  emit('update:visible', false);
}
/**
 * 重置表单
 */
function resetForm() {
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  loading.value = false;
  Object.keys(form).map(key => {
    delete form[key];
  });
}

function handleSubmit() {
  dataFormRef.value.validate(isValid => {
    if (isValid) {
      let arr = []
      form.select_time.forEach(item=>{
        if(arr.indexOf(item) == -1) {
          arr.push(item)
        }
      })
      if(arr.length != form.select_time.length && form.select_time.length != 0) {
        ElMessage.warning('不能同时选择两个相同时间点')
        return;
      }
      let wayline_type = '';
      if (form.file_id) {
        const findObj = airLineOptions.value.find(item => item.id === form.file_id);
        if (findObj && findObj.template_types && findObj.template_types.length > 0) {
          wayline_type = findObj.template_types[0];
        }
      }
      let drone_sn = ''
      exeAirportOptions.value.forEach(item=>{
        if(item.device_sn == form.dock_sn) {
          drone_sn = item.child_device_sn
        }
      })
      
      let period_time = form.select_time ? form.select_time?.join(',') : '';
      let begin_date = form.rangTime ? form.rangTime[0] : '';
      let end_date = form.rangTime ? form.rangTime[1] : '';
      let params = {
        ...form,
        wayline_type,
        begin_date,
        end_date,
        period_time,
        days_of_week: form.days_of_week ? form.days_of_week?.join(',') : '',
        drone_sn: drone_sn
      };
      delete params.select_time;
      delete params.rangTime;
      loading.value = true;
      addAirTaskList(params)
        .then(res => {
          loading.value = false;
          ElMessage.success('新增成功');
          closeDialog();
          emit('submit');
        })
        .catch(e => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}
function addTime() {
  dataFormRef.value.validateField('select_time', valid => {
    if (valid) {
      form.select_time.push([]);
      select_time_number.value = select_time_number.value + 1;
    }
  });
}
function removeTime(index) {
  if (select_time_number.value === 1) return;
  select_time_number.value = select_time_number.value - 1;
  form.select_time.splice(index, 1);
}
function getLines() {
  getWaylines({
    order_by: 'update_time desc',
    page: 1,
    page_size: 9999
  }).then(res => {
    const { list = [] } = res;
    let name = ''
    list.forEach(item=>{
      if(item.drone_model_key == '0-91-1') {
        name='M3TD'
      }else if(item.drone_model_key == '0-77-1') {
        name='Mavic 3T'
      }
      item.name = `${item.name}-${name}`
    })
    airLineOptions.value = list;
  });
}
function getDevices() {
  getDevicesBound({
    domain: DOMAIN.DOCK,
    page: 1,
    page_size: 50
  }).then(res => {
    const { list = [] } = res;
    exeAirportOptions.value = list;
  });
}
onMounted(() => {
  getLines();
  getDevices();
});
</script>
<style scoped lang="scss">
.input-serach {
  width: 200px;
}
:global(.el-loading-mask) {
  transform: opacity 0.9 !important;
  background-color: rgb(255 255 255 / 0.3);
}
.app-form {
  .select-time {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 0 0 5px 0;
  }
  ::v-deep {
    .el-input-number,
    .el-select {
      width: 100%;
    }
    .el-input-number .el-input__inner {
      text-align: left;
    }
    .el-input-number.is-controls-right .el-input__wrapper {
      padding-left: 11px;
    }
    .el-date-editor.el-input,
    .el-date-editor.el-input__wrapper {
      width: 100%;
    }
  }
}
</style>
