@font-face {
  font-family: 'ss-cloud';
  /* Project id 3842897 */
  src: url('iconfont.woff2?t=1674041722583') format('woff2'),
    url('iconfont.woff?t=1674041722583') format('woff'),
    url('iconfont.ttf?t=1674041722583') format('truetype');
}

.ff-cloud-icon {
  font-family: 'ss-cloud' !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 设备-转移 */
.clound-transfer:before {
  content: '\e6d6';
}

/* 设备-增加标签 */
.clound-add-label:before {
  content: '\e6da';
}

/* 设备-删除标签 */
.clound-del-label:before {
  content: '\e6ed';
}

/* 设备-平铺 */
.clound-card:before {
  content: '\e6d7';
}

/* 设备-列表 */
.clound-list:before {
  content: '\e6ee';
}

/* 设备-列设置 */
.clound-set:before {
  content: '\e6df';
}

/* 报警 */
.clound-notic:before {
  content: '\e6e6';
}

/* 运维 */
.clound-ops:before {
  content: '\e68e';
}

/* 日志 */
.clound-log:before {
  content: '\e6d8';
}

/* 运营 */
.clound-operation:before {
  content: '\e68d';
}

/* 首页 */
.clound-home:before {
  content: '\e6c3';
}

/* 系统管理 */
.clound-sys-setting:before {
  content: '\e6e9';
}

/* 设备 */
.clound-device:before {
  content: '\e6d5';
}

/* 仪表盘 */
.clound-dashboard:before {
  content: '\e6b8';
}

/* 设备-转移 */
.clound-transfer:before {
  content: '\e6d6';
}

/* 基本信息 */
.clound-basic-info:before {
  content: '\e6d9';
}

/* 网口 */
.clound-net-gape:before {
  content: '\e6f5';
}

/* SIM卡 */
.clound-SIM:before {
  content: '\e6f4';
}

/* WIFI */
.clound-wireless:before {
  content: '\e6f2';
}

/* 信号 */
.clound-signal:before {
  content: '\e6f3';
}

/* 下载 */
.clound-download:before {
  content: '\e6ea';
}

/* 进行中 */
.clound-underway:before {
  content: '\e6ec';
}

/* 账单 */
.clound-billing:before {
  content: '\e6c5';
}

/* 网页端 */
.clound-web-icon:before {
  content: '\e6e8';
}

/* 手机端 */
.clound-mobile-icon:before {
  content: '\e6f0';
}

/* 完成状态 */
.clound-success-icon:before {
  content: '\e6e4';
}

/* wan口 */
.clound-WAN:before {
  content: '\e6f7';
}

/* LAN口 */
.clound-LAN:before {
  content: '\e6f6';
}

/* 检测 */
.clound-detection:before {
  content: '\e6d4';
}

/* 图片 */
.clound-img-icon:before {
  content: '\e6c9';
}

/* 统计图 */
.clound-statistics:before {
  content: '\e6b9';
}

/* 地图 */
.clound-map:before {
  content: '\e6ef';
}

/* 编辑 */
.clound-edit:before {
  content: '\e6c4';
}

/* 关闭 */
.clound-close:before {
  content: '\e6dd';
}

/* 撤销 */
.clound-back:before {
  content: '\e6e1';
}

/* 删除 */
.clound-delete:before {
  content: '\e6e7';
}

/* 文件 */
.clound-file:before {
  content: '\e6d8';
}

/* 状态卡片-产品 */
.clound-status-product:before {
  content: '\e6d7';
}

/* 状态卡片-人员 */
.clound-status-person:before {
  content: '\e6b0';
}

/* 提示-面性 */
.clound-tips-full:before {
  content: '\e6f1';
}

/* 睁眼 */
.clound-open-eye:before {
  content: '\e6e3';
}

/* 闭眼 */
.clound-close-eye:before {
  content: '\e66f';
}

/* 等待 */
.cloud-await:before {
  content: '\e6f8';
}

/* 等待 */
.cloud-scene:before {
  content: '\e6b4';
}

/* 密码锁 */
.cloud-lock:before {
  content: '\e6f9';
}

/* 验证码 */
.cloud-valid-code:before {
  content: '\e6b7';
}

/* 租户 */
.cloud-tenant:before {
  content: '\e673';
}

/* 飞行 */
.cloud-airline:before {
  content: '\e6d2';
}

/* 航线 */
.cloud-air:before {
  content: '\e6d6';
}

/* 任务 */
.cloud-task:before {
  content: '\e68c';
}
