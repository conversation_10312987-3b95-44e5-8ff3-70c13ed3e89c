import * as Cesium from 'cesium';
class CesiumGLBLoader {
  constructor(viewer, glbUrl, position, orientation) {
    this.viewer = viewer;
    this.glbUrl = glbUrl;
    this.scale = 500.0;
    this.position = position || Cesium.Cartesian3.fromDegrees(0.0, 0.0, 0.0);
    this.orientation =
      orientation ||
      Cesium.Transforms.headingPitchRollQuaternion(
        this.position,
        0.0,
        0.0,
        0.0
      );
    this.loadModelPromise = this.loadModel(); // 返回 Promise 对象
  }

  loadModel() {
    const model = this.viewer.entities.add({
      name: '飞机',
      model: {
        uri: this.glbUrl,
        //不管缩放如何，模型的最小最小像素大小。
        minimumPixelSize: 64,
        //模型的最大比例尺大小。 minimumPixelSize的上限。
        maximumScale: this.scale
      },
      viewFrom: new Cesium.Cartesian3(0, -300, 100)
    });
    return model;
    // const model = this.viewer.scene.primitives.add(
    //   Cesium.Model.fromGltf({
    //     url: this.glbUrl,
    //     modelMatrix: modelMatrix,
    //     minimumPixelSize: 64,
    //     maximumScale: this.scale, // 设置模型的比例尺 20000,
    //   })
    // );
    // 等待模型加载完成
    // return model.readyPromise.then((loadedModel) => {
    //   this.model = loadedModel;
    //   loadedModel.activeAnimations.addAll({
    //     multiplier: 10,
    //     loop: Cesium.ModelAnimationLoop.REPEAT,
    //   });
    //   // 获取模型的动画集合
    //   this.animationCollection = loadedModel.activeAnimations;
    //   return loadedModel;
    // });
  }

  loadModel2() {
    const modelMatrix = Cesium.Transforms.eastNorthUpToFixedFrame(
      this.position
    );
    const model = this.viewer.scene.primitives.add(
      Cesium.Model.fromGltf({
        url: this.glbUrl,
        modelMatrix: modelMatrix,
        minimumPixelSize: 64,
        maximumScale: this.scale // 设置模型的比例尺 20000,
      })
    );
    // 等待模型加载完成
    return model.readyPromise.then(loadedModel => {
      this.model = loadedModel;
      loadedModel.activeAnimations.addAll({
        multiplier: 10,
        loop: Cesium.ModelAnimationLoop.REPEAT
      });
      // 获取模型的动画集合
      this.animationCollection = loadedModel.activeAnimations;
      return loadedModel;
    });
  }

  setPosition(position) {
    this.position = position;
    this.model.modelMatrix = Cesium.Transforms.eastNorthUpToFixedFrame(
      this.position
    );
  }

  setScale(scale) {
    this.scale = scale;
    if (this.model) {
      this.model.maximumScale = scale;
    }
  }

  setOrientation(orientation) {
    this.orientation = orientation;
    this.model.modelMatrix = Cesium.Transforms.headingPitchRollQuaternion(
      this.position,
      orientation.heading,
      orientation.pitch,
      orientation.roll
    );
  }
  getModel() {
    return this.loadModelPromise;
  }
  destroy() {
    this.viewer.scene.primitives.remove(this.model);
  }
}
export { CesiumGLBLoader };
// // 示例用法
// const cesiumViewer = new Cesium.Viewer('cesiumContainer');
// const glbUrl = 'path/to/your/model.glb';

// const glbLoader = new CesiumGLBLoader(cesiumViewer, glbUrl);

// // 播放名为 "YourAnimationName" 的动画，倍速为 2.0
// glbLoader.playAnimation('YourAnimationName', 2.0);

// // 修改模型位置和朝向
// glbLoader.setPosition(Cesium.Cartesian3.fromDegrees(10.0, 20.0, 0.0));
// glbLoader.setOrientation({
//   heading: Cesium.Math.toRadians(90.0),
//   pitch: Cesium.Math.toRadians(0.0),
//   roll: Cesium.Math.toRadians(0.0),
// });

// // 停止播放动画
// glbLoader.stopAnimation('YourAnimationName');

// // 销毁模型
// glbLoader.destroy();
