<template>
  <el-popover
    :visible="state.sVisible"
    trigger="click"
    v-bind="$attrs"
    :popper-class="overlayClassName"
    placement="bottom"
    v-on="$attrs"
  >
    <div class="title-content"></div>
    <slot name="formContent"></slot>
    <div class="uranus-popconfirm-btns">
      <el-button size="small" @click="onCancel">
        {{ cancelText || '取消' }}
      </el-button>
      <el-button
        size="small"
        :loading="loading"
        type="primary"
        class="confirm-btn"
        @click="onConfirm"
      >
        {{ okText || '确定' }}
      </el-button>
    </div>
    <template #reference>
      <span style="color: #00a0ff;">
        <slot></slot>

      </span>
    </template>
  </el-popover>
</template>
  
  <script  setup>
import { defineProps, defineEmits, reactive, watch, computed } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean
  },
  loading: {
    type: <PERSON>olean
  },
  disabled: {
    type: Boolean
  },
  title: {
    type: String
  },
  okText: {
    type: String
  },
  cancelText: {
    type: String
  },
  width: {
    type: Number
  }
});

const emit = defineEmits(['cancel', 'confirm']);

const state = reactive({
  sVisible: false,
  loading: false
});

watch(
  () => props.visible,
  val => {
    state.sVisible = val || false;
  }
);

const loading = computed(() => {
  return props.loading;
});
const okLabel = computed(() => {
  return props.loading ? '' : '确定';
});

const overlayClassName = computed(() => {
  const classList = ['device-setting-popconfirm'];
  return classList.join(' ');
});

function onConfirm(e) {
  if (props.disabled) {
    return;
  }
  emit('confirm', e);
}

function onCancel(e) {
  state.sVisible = false;
  emit('cancel', e);
}
</script>
  
  <style lang="scss">
.device-setting-popconfirm {
  min-width: 250px !important;

  .uranus-popconfirm-btns {
    display: flex;
    padding: 10px 0px;
    justify-content: flex-end;

    .confirm-btn {
      margin-left: 10px;
    }
  }

  .form-content {
    display: inline-flex;
    align-items: center;

    .form-label {
      padding-right: 10px;
    }
  }
}
</style>
  