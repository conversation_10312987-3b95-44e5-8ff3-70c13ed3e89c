import axios from 'axios';
import { ElMessage } from 'element-plus';
// import i18n from '@/lang';
import { encryptRequestData } from './encrypt';
import { useUserStoreHook } from '@/store/modules/user';
import { useAppStore } from '@/store/modules/app';
import i18n from '@/lang/index';
import { CURRENT_CONFIG } from '@/api/http/config'
import { isJSON } from './helper';

const service = axios.create({
  baseURL: CURRENT_CONFIG.VITE_APP_BASE_URL,
  timeout: 150000
});
// 请求拦截器
service.interceptors.request.use(
  config => {
    const userStore = useUserStoreHook();
    const appStore = useAppStore();
    if (userStore.token) {
      config.headers['X-Auth-Token'] = userStore.token;
    }

    config.headers['Cache-Control'] = 'no-cache, no-store';
    config.headers['Accept-Language'] = appStore.language;

    // 参数加密 VITE_APP_USEENCRYPT为true时加密
    if (!('responseType' in config) && import.meta.env.VITE_APP_USE_ENCRYPT === 'YES') {
      encryptRequestData(config);
    }
    console.log('config',config)
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应的拦截器
service.interceptors.response.use(
  // 处理正常响应的函数
  response => {
    let res = response.data;
    const hideErrorMsg = response.config?.hideErrorMsg;

    // 如果响应的 status 不是 200，则显示错误提示，并返回一个 Promise.reject()
    if (response.status * 1 !== 200) {
      return handleError(response, hideErrorMsg);
    }
    if (response.config.responseType === 'blob') {
      return new Promise((resolve, reject) => {
        if (response.data.type === 'application/json') {
          const reader = new FileReader();
          reader.readAsText(response.data, 'utf-8');

          reader.onload = function () {
            const readerResult = JSON.parse(reader.result);

            if (readerResult.code !== 0) {
              const errorMessage = readerResult.message || i18n.global.t('request.serverError');

              ElMessage({
                dangerouslyUseHTMLString: true,
                message: `<div>${errorMessage}</div>`,
                type: 'error',
                duration: 5 * 1000,
                offset: 105
              });
              reject(new Error(errorMessage));
            } else {
              resolve(response);
            }
          };
        } else {
          resolve(response.data);
        }
      });
    }
    if (response.headers['content-type'].includes('application/json')) {
      if (isJSON(res)) {
        res = JSON.parse(res);
      }
      
      if (res.code * 1 !== 0) {
        return handleError(res, hideErrorMsg);
      }
    }

    // 返回响应的数据
    return res.data;
  },

  // 处理错误响应的函数
  error => {
    return handleError(error);
  }
);

// 定义一个处理错误的函数
function handleError(error, hideErrorMsg = false) {
  if (error) {
    const message = error.message || error.msg;
    const errorCode = error?.response?.status || error.code;
    console.log('message',error,message,errorCode)
    if (errorCode) {
      if (errorCode == '401' || errorCode == '403') {
        // token失效的时候报错
        if(errorCode == '401' && error.code == 'ERR_BAD_REQUEST' && error.config?.url !== '/manage/api/v1/users/current') {
          ElMessage.error({
            message: '登录超时，请重新登录',
            offset: 105
          })
          localStorage.clear();
          return Promise.reject(error);
        }
        if(errorCode == '401' && error.config?.url === '/manage/api/v1/users/current') {
          return Promise.reject(error);
        }
        // 用户认证失败或未登录，需要注销用户并显示错误提示
        if(error.response?.data?.code == '100001') {
          // 证书失效
          ElMessage.error({
            message:error.response.data.msg,
            offset: 105
          })
        }else if(!hideErrorMsg) {
          ElMessage.error({message: message,offset: 105});
        }
        // window.location.href = '/';
      } else if (errorCode == '500' || errorCode == '999') {
        // 服务器内部错误或其他业务错误，需要显示错误提示
        let _message = i18n.global.t('request.serverError');
        if (!hideErrorMsg) {
          ElMessage.error({message:message || _message,offset: 105});
        }
        console.warn('接口出错!', error.config.url);
      }else if (errorCode == '13012') {
        // 服务器内部错误或其他业务错误，需要显示错误提示
        ElMessage.warning({message:'一代机场不支持机场与无人机同时直播，默认播放无人机直播',offset: 105});
        console.warn('接口出错!', error.config.url);
      } else {
        // 其他错误码，需要显示错误提示
        if (!hideErrorMsg) {
          if(error.config?.url !== '/manage/api/v1/users/current' && errorCode != '1002031000') {
            ElMessage.error({
              message: message,
              offset: 105
            });
          }
        }
      }
    } else {
      // 请求错误
      if (!hideErrorMsg) {
        ElMessage.error({
          message: message,
          offset: 105
        });
      }
    }
  }

  return Promise.reject(error);
}

// 导出 axios 实例
export default service;
