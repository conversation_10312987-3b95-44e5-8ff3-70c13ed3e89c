// Generated by CodiumAI
import EventBus from '../../../src/utils/eventbus';
describe('EventBus', () => {
  // should be able to add event listener using 'on' method
  it("should add event listener when 'on' method is called", () => {
    const eventBus = new EventBus();
    const eventName = 'testEvent';
    const listener = jest.fn();

    eventBus.on(eventName, listener);

    expect(eventBus.events[eventName]).toContain(listener);
  });

  // should be able to trigger event using 'emit' method
  it("should trigger event when 'emit' method is called", () => {
    const eventBus = new EventBus();
    const eventName = 'testEvent';
    const listener = jest.fn();

    eventBus.on(eventName, listener);
    eventBus.emit(eventName);

    expect(listener).toHaveBeenCalled();
  });

  // should be able to remove event listener using 'off' method
  it("should remove event listener when 'off' method is called", () => {
    const eventBus = new EventBus();
    const eventName = 'testEvent';
    const listener = jest.fn();

    eventBus.on(eventName, listener);
    eventBus.off(eventName, listener);

    expect(eventBus.events[eventName]).not.toContain(listener);
  });

  // should handle gracefully when trying to remove non-existing event listener using 'off' method
  it('should not throw error when trying to remove non-existing event listener', () => {
    const eventBus = new EventBus();
    const eventName = 'testEvent';
    const listener = jest.fn();

    expect(() => eventBus.off(eventName, listener)).not.toThrow();
  });

  // should handle gracefully when trying to remove event listener for a non-existing event using 'off' method
  it('should not throw error when trying to remove event listener for a non-existing event', () => {
    const eventBus = new EventBus();
    const eventName = 'testEvent';
    const listener = jest.fn();

    expect(() => eventBus.off(eventName, listener)).not.toThrow();
  });

  // should handle gracefully when trying to trigger non-existing event using 'emit' method
  it('should not throw error when trying to trigger non-existing event', () => {
    const eventBus = new EventBus();
    const eventName = 'testEvent';

    expect(() => eventBus.emit(eventName)).not.toThrow();
  });

  // should be able to add one-time event listener using 'once' method
  it("should add one-time event listener when 'once' method is called", () => {
    const eventBus = new EventBus();
    const eventName = 'testEvent';
    const listener = jest.fn();

    eventBus.once(eventName, listener);

    expect(eventBus.events[eventName]).toContain(listener);
  });

  // should be able to add multiple event listeners for same event using 'on' method
  it("should add multiple event listeners for same event when 'on' method is called", () => {
    const eventBus = new EventBus();
    const eventName = 'testEvent';
    const listener1 = jest.fn();
    const listener2 = jest.fn();

    eventBus.on(eventName, listener1);
    eventBus.on(eventName, listener2);

    expect(eventBus.events[eventName]).toContain(listener1);
    expect(eventBus.events[eventName]).toContain(listener2);
  });
});
