// 间隔动作相关
import { ACTION_TRIGGER_TYPE, ACTION_ACTUATOR_FUNC } from '@/utils/constants.js';
import { getWayPointsPlacemarkMap, getMinMaxPlacemarkInfo } from './waylineshandle.js';
//  数据结构模板
const createActionDetailTemplate = () => ({
  placemarkUuid: 0,
  actionGroupUuid: 0,
  actionUuid: 0,
  action: null,
  actionTriggerType: null,
  actionType: null,
  actionGroup: null,
  placemark: null,
  index: 0
});

// 获取需要进行规则校验的动作数据
const getRuleCheckData = () => {
  let actionList = [];
  let count = 0;
  // 获取所有的航点
  const wayPointsPlacemarkMap = getWayPointsPlacemarkMap();
  for (const placeMark of wayPointsPlacemarkMap.values()) {
    (placeMark.wpml_actionGroup || []).forEach(actionGroup => {
      let actions = actionGroup.getActions();
      (actions || []).forEach(action => {
        const d = {
          ...createActionDetailTemplate(),
          placemarkUuid: placeMark.uuid,
          actionGroupUuid: actionGroup.uuid,
          actionUuid: action.uuid,
          action: action,
          actionTriggerType: actionGroup.wpml_actionTrigger.wpml_actionTriggerType,
          actionGroup: actionGroup,
          actionType: action.wpml_actionActuatorFunc,
          placemark: placeMark,
          index: count
        };
        actionList.push(d);
        count++;
      });
    });
  }
  return actionList;
};

//#region 间隔动作相关方法汇总
/**
 * 获取间隔动作和停止间隔动作数据
 * @param {*} actionList
 * @returns 间隔动作和停止间隔动作数据数组
 */
const getMultipleActions = actionList => {
  try {
    if (!actionList) {
      return null;
    }
    // 提取间隔拍照的动作
    const multipleActionList = actionList.filter(d => {
      //  这里追加 ACTION_ACTUATOR_FUNC.timeIntervalTakePhoto ACTION_ACTUATOR_FUNC.distanceIntervalTakePhoto 主要是实际作为内部定义动作名称用于区分间隔动作和其他动作
      const isTakePhotoAction =
        d.actionType === ACTION_ACTUATOR_FUNC.takePhoto ||
        d.actionType === ACTION_ACTUATOR_FUNC.timeIntervalTakePhoto ||
        d.actionType === ACTION_ACTUATOR_FUNC.distanceIntervalTakePhoto;
      const isMultipleTimingOrDistance =
        d.actionTriggerType === ACTION_TRIGGER_TYPE.multipleTiming ||
        d.actionTriggerType === ACTION_TRIGGER_TYPE.multipleDistance;
      const isStopIntervalTakePhoto = d.actionType === ACTION_ACTUATOR_FUNC.stopIntervalTakePhoto;
      // 这里收集是间隔拍照的动作和停止间隔动作
      return (isTakePhotoAction && isMultipleTimingOrDistance) || isStopIntervalTakePhoto;
    });
    return multipleActionList || null;
  } catch {
    return null;
  }
};

/**
 *  查找上一个间隔动作的逻辑 返回包括三种 等时间隔拍照、等距离间隔拍照、 停止间隔操作
 * @param {*} action 动作类对象
 * @returns
 */
const findPreviewMultipAction = action => {
  // 获取动作列表
  let actionDetailList = getRuleCheckData();
  // 获取所有的间隔动作列表
  const multipleActionList = getMultipleActions(actionDetailList) || [];
  // 循环遍历 multipleActionList 判断当前动作是否在间隔动作列表中
  // 这里 previousActionInfo 是 actionDetailTemplate 的数据模板
  const currentIndex = multipleActionList.findIndex(item => item.actionUuid === action.uuid);
  const previousIndex = currentIndex - 1;
  const previousActionInfo = previousIndex >= 0 ? multipleActionList[previousIndex] : null;
  return previousActionInfo;
};
/**
 * 查找下一个间隔动作
 * @param {*} action 动作类对象
 * @returns
 */
const findNextMultipAction = action => {
  // 获取动作列表
  let actionDetailList = getRuleCheckData();
  // 获取所有的间隔动作列表
  const multipleActionList = getMultipleActions(actionDetailList) || [];
  // 这里 nextActionInfo 是 actionDetailTemplate 的数据模板
  const currentIndex = multipleActionList.findIndex(item => item.actionUuid === action.uuid);
  const nextIndex = currentIndex + 1;
  const nextActionInfo = nextIndex < multipleActionList.length ? multipleActionList[nextIndex] : null;
  return nextActionInfo;
};

/** 当前动作为停止间隔动作
 * @param {*} curAction 动作对象
 * @param {*} curActionGroup 当前的停止间隔动作所在的动作组对
 * @returns
 */
const setStopMultipAction = (curAction, curActionGroup) => {
  // 判断
  if (!curAction || !curActionGroup) {
    return;
  }
  //#region 数据结构
  // {
  //     ...actionDetailTemplate,
  //     placemarkUuid: placeMark.uuid,
  //     actionGroupUuid: actionGroup.uuid,
  //     actionUuid: action.uuid,
  //     action: action,
  //     actionTriggerType: actionGroup.wpml_actionTrigger.wpml_actionTriggerType,
  //     actionGroup: actionGroup,
  //     actionType: action.wpml_actionActuatorFunc,
  //     placemark: placeMark,
  //     index: count
  // };
  //#endregion
  if (ACTION_ACTUATOR_FUNC.stopIntervalTakePhoto === curAction.wpml_actionActuatorFunc) {
    const previewMultipAction = findPreviewMultipAction(curAction);
    if (!previewMultipAction) {
      return;
    }
    // 判断PreviewMultipAction是间隔动作还是停止间隔动作 1、停止间隔动作  2、间隔动作（等时、等距）
    const isStopIntervalTakePhoto = previewMultipAction?.actionType === ACTION_ACTUATOR_FUNC.stopIntervalTakePhoto;
    const isNoTtopIntervalTakePhoto =
      (previewMultipAction?.actionTriggerType === ACTION_TRIGGER_TYPE.multipleTiming ||
        previewMultipAction?.actionTriggerType === ACTION_TRIGGER_TYPE.multipleDistance) &&
      previewMultipAction?.actionType === ACTION_ACTUATOR_FUNC.takePhoto;
    // 1、停止间隔动作
    if (isStopIntervalTakePhoto) {
      // 如果前一个动作是停止动作则暂时不做处理
    } else if (isNoTtopIntervalTakePhoto) {
      // 找到前一个动作组合设置他的结束位置
      const actionGroup = previewMultipAction?.actionGroup;
      // 获取当前停止按钮所在的点位置
      let curPlacemarkIndex = curActionGroup.placemarkIndex;
      //   // 获取前一个间隔动作点的结束位置
      //   let wpml_actionGroupEndIndex = actionGroup.wpml_actionGroupEndIndex;
      // 设置前一个动作组合的结束位置为当前的停止间隔动作所在点位置
      actionGroup.wpml_actionGroupEndIndex = curPlacemarkIndex;
    }
  }
};

/** 判断当前动作为停止间隔动作
 * @param {*} curAction 动作对象
 * @param {*} curActionGroup 当前的停止间隔动作所在的动作组对
 * @returns
 * {
 *   isMultple: true, 是否是间隔动作
 *   actionType: ACTION_ACTUATOR_FUNC.stopIntervalTakePhoto 间隔动作类型
 * };
 */
const isMultpleAction = (curAction, curActionGroup) => {
  if (!curAction || !curActionGroup) {
    return {
      isMultple: false,
      actionType: null
    };
  }
  if (ACTION_ACTUATOR_FUNC.stopIntervalTakePhoto === curAction.wpml_actionActuatorFunc) {
    return {
      isMultple: true,
      actionType: ACTION_ACTUATOR_FUNC.stopIntervalTakePhoto
    };
  } else if (
    curAction.wpml_actionActuatorFunc === ACTION_ACTUATOR_FUNC.takePhoto &&
    curActionGroup?.actionTriggerType === ACTION_TRIGGER_TYPE.multipleTiming
  ) {
    return {
      isMultple: true,
      actionType: ACTION_ACTUATOR_FUNC.timeIntervalTakePhoto
    };
  } else if (
    curAction.wpml_actionActuatorFunc === ACTION_ACTUATOR_FUNC.takePhoto &&
    curActionGroup?.actionTriggerType === ACTION_TRIGGER_TYPE.multipleDistance
  ) {
    return {
      isMultple: true,
      actionType: ACTION_ACTUATOR_FUNC.distanceIntervalTakePhoto
    };
  }
  return {
    isMultple: false,
    actionType: null
  };
};

/**
 * 重建当前间隔动作索引
 * @returns
 */
const reBuildMultipActionEndIndex = () => {
  let actionDetailList = getRuleCheckData();
  // 获取所有的间隔动作列表
  const multipleActionList = getMultipleActions(actionDetailList) ?? [];
  // 判断只有一个的动作 且是间隔时间拍照或者是距离拍照 那么这时候
  if (multipleActionList.length === 1) {
    let actionInfo = multipleActionList[0];
    if (
      actionInfo.actionType === ACTION_ACTUATOR_FUNC.takePhoto ||
      actionInfo.actionTriggerType === ACTION_TRIGGER_TYPE.multipleTiming ||
      actionInfo.actionTriggerType === ACTION_TRIGGER_TYPE.multipleDistance
    ) {
      // 传入动作对象获取
      const nextMultipAction = findNextMultipAction(actionInfo.action);
      if (!nextMultipAction) {
        // 1、停止停止间隔动作 设置当前停止索引号 在最后一个动作组索引
        const curActionGroup = actionInfo.actionGroup;
        const { index: maxPlacemarkIndex } = getMinMaxPlacemarkInfo({ type: 'max' });
        curActionGroup.wpml_actionGroupEndIndex = maxPlacemarkIndex;
      }
    }
  }
  // 获取所有的间隔动作列表
  else if (multipleActionList.length > 1) {
    (multipleActionList || []).forEach(actionInfo => {
      // 当前的动作类型判断
      if (actionInfo.actionType === ACTION_ACTUATOR_FUNC.stopIntervalTakePhoto) {
        // 传入动作对象获取
        const previewMultipAction = findPreviewMultipAction(actionInfo.action);
        if (!previewMultipAction) {
          return;
        }
        // 判断PreviewMultipAction是间隔动作还是停止间隔动作 1、停止间隔动作  2、间隔动作（等时、等距）
        const isStopIntervalTakePhoto = previewMultipAction?.actionType === ACTION_ACTUATOR_FUNC.stopIntervalTakePhoto;
        const isNoTtopIntervalTakePhoto =
          (previewMultipAction?.actionTriggerType === ACTION_TRIGGER_TYPE.multipleTiming ||
            previewMultipAction?.actionTriggerType === ACTION_TRIGGER_TYPE.multipleDistance) &&
          previewMultipAction?.actionType === ACTION_ACTUATOR_FUNC.takePhoto;
        // 1、停止间隔动作
        if (isStopIntervalTakePhoto) {
          // 如果前一个动作是停止动作则暂时不做处理
        } else if (isNoTtopIntervalTakePhoto) {
          // 找到前一个动作组合设置他的结束位置
          const actionGroup = previewMultipAction?.actionGroup;
          const curActionGroup = actionInfo.actionGroup;
          // 获取当前停止按钮所在的点位置
          let curPlacemarkIndex = curActionGroup.placemarkIndex;
          actionGroup.wpml_actionGroupEndIndex = curPlacemarkIndex;
        }
      } else if (
        actionInfo.actionType === ACTION_ACTUATOR_FUNC.takePhoto ||
        actionInfo.actionTriggerType === ACTION_TRIGGER_TYPE.multipleTiming ||
        actionInfo.actionTriggerType === ACTION_TRIGGER_TYPE.multipleDistance
      ) {
        // 传入动作对象获取
        const nextMultipAction = findNextMultipAction(actionInfo.action);
        if (!nextMultipAction) {
          // 如果这里后续找不到了说明该动作已经是最后的 这时候需要将将结束位给到当前动作组
          // 1、停止停止间隔动作 设置当前停止索引号 在最后一个动作组索引
          const curActionGroup_ = actionInfo.actionGroup;
          const { index: maxPlacemarkIndex } = getMinMaxPlacemarkInfo({ type: 'max' });
          curActionGroup_.wpml_actionGroupEndIndex = maxPlacemarkIndex;
        } else {
          // 判断上一个动作nextMultipAction是间隔动作还是停止间隔动作 1、停止间隔动作  2、间隔动作（等时、等距）
          const isStopIntervalTakePhoto = nextMultipAction?.actionType === ACTION_ACTUATOR_FUNC.stopIntervalTakePhoto;
          // 1、停止间隔动作
          if (isStopIntervalTakePhoto) {
            // 设置当前停止索引号
            const curActionGroup = actionInfo.actionGroup;
            curActionGroup.wpml_actionGroupEndIndex = nextMultipAction?.actionGroup?.placemarkIndex;
          }
        }
      }
    });
  }
};

const addStopMultipAction = (placeMarkIndex = null, folder = null) => {
  if (!placeMarkIndex || !folder) {
    return;
  }
};

//#endregion

export {
  getRuleCheckData,
  setStopMultipAction,
  isMultpleAction,
  getMultipleActions,
  findPreviewMultipAction,
  findNextMultipAction,
  reBuildMultipActionEndIndex,
  addStopMultipAction
};
