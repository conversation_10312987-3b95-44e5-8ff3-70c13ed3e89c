import request from '@/utils/request';
import { ACHIEVEMENT_PATH, APPLICTION_PROJECT, API_VERSION } from '../config/index';
import { useUserStoreHook } from '@/store/modules/user';

// 工程管理API主路径
const BASE_URL = ACHIEVEMENT_PATH + API_VERSION + APPLICTION_PROJECT;
/**
 * 获取工程列表
 *
 * @param queryParams
 */
export function getProjectList(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/pages`,
    method: 'POST',
    data: data
  });
}
/**
 * 新增工程
 *
 * @param queryParams
 */
export function addProject(queryParams) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/save`,
    method: 'post',
    data: queryParams
  });
}
/**
 * 删除工程
 *
 * @param ids
 */
export function deleteProject(project_id) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/del/${project_id}`,
    method: 'get'
  });
}
