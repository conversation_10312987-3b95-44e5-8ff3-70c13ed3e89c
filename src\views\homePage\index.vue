<script>
export default { name: 'HomePage' };
</script>

<script setup>
import { ref } from 'vue';
import AlarmList from './component/alarmList.vue';
import DroneList from './component/droneList.vue';
import DroneVideo from './component/droneVideo.vue'
import taskDelivery from './component/taskDelivery.vue';
import HomeMap from '@/views/map/map-fly-manager/homeMap/index.vue';
import { toRaw } from '@vue/reactivity'
import { submitTask } from '@/api/task';
import { ElMessage } from 'element-plus';

const taskVisible = ref(false);
const videoVisible = ref(false);
const pilotVisible = ref(false);
const droneVisible = ref(false);
const homeMapRef = ref(null);
const alarmId = ref('')
const videoData = ref([])
const pilotData = ref([])
const droneData = ref([])

// 行点击
function selectDrone (item) {
	if (homeMapRef.value) {
		homeMapRef.value.clickDockOrNav(item);
	} 
}

function clickItem (type,item) {
	if(type == 'drone') {
		if(!item.status) {
			ElMessage.error('无人机已离线！')
			closeVideo()
			return;
		}
		droneVisible.value = true
		pilotVisible.value = false
		videoVisible.value = false
		droneData.value = [toRaw(item)]
		videoData.value = []
		pilotData.value = []
	}else if(type == 'airport'){
		if(!item.status) {
			ElMessage.error('机场已离线！')
			closeVideo()
			return;
		}
		videoVisible.value = true
		droneVisible.value = false
		pilotVisible.value = false
		videoData.value = [toRaw(item)]
		droneData.value = []
		pilotData.value = []
	}else {
		if(!item.status) {
			ElMessage.error('飞手无人机已离线')
			closeVideo()
			return;
		}
		pilotVisible.value = true
		videoVisible.value = false
		droneVisible.value = false
		pilotData.value = [toRaw(item)]
		droneData.value = []
		videoData.value = []
	}
}

// 任务下发取消
function cancelTask () {
	taskVisible.value = false
}

function closeVideo () {
	droneVisible.value = false
	videoVisible.value = false
	pilotVisible.value = false
}

// 一键下发
function oneClick (item) {
	alarmId.value = toRaw(item).alarm_id
	taskVisible.value = true
}

// 点击列表定位到地图
function locationJump (item) {
	const data = toRaw(item)
	if (homeMapRef.value) {
		homeMapRef.value.fireAlarmJump(data);
	} 
}

function handleTask (res) {
	let alarmConfig = JSON.parse(JSON.stringify(res))
	delete alarmConfig.dock_sn
	submitTask({alarmId: alarmId._value},{
		dock_sn: res.dock_sn,
		alarm_config: {
			...alarmConfig
		}
	}).then(resp => {
		ElMessage.success('任务已创建');
		taskVisible.value = false
		data.value = {}
	})
	.catch(e => {
		ElMessage.console.error();(e.message);
		loading.value = false;
	});
}
</script>

<template>
  <div class="home-container">
		<!-- 地图区域 -->
    <div class="map-container">
			<HomeMap ref="homeMapRef"/>
    </div>
		<!-- 视频播放 -->
		<div class="video-container">
			<drone-video player-id="video" :visible="videoVisible" @onClose="closeVideo" :device="videoData"/>
			<drone-video player-id="pilot" :visible="pilotVisible" @onClose="closeVideo" :device="pilotData"/>
			<drone-video player-id="drone" :visible="droneVisible" @onClose="closeVideo" :device="droneData"/>
		</div>
		<!-- 任务下发 -->
		<div class="task-container">
			<task-delivery v-if="taskVisible" :visible="taskVisible" @onCancel="cancelTask" @ok="handleTask"/>
		</div>
		<!-- 右侧警情列表以及无人机列表 -->
		<div class="alarm-box">
			<div class="alarm-list">
				<alarm-list @OneClickDelivery="oneClick" @LocationJump="locationJump"/>
			</div>
			<div class="drone-list">
				<drone-list @onClick="clickItem" @select="selectDrone"/>
			</div>
		</div>
  </div>
</template>

<style lang="scss" scoped>
.home-container {
	position: relative;
  display: flex;
  flex-direction: row;
  height: calc(100vh - 60px);
  width: 100%;
  background: #fff;
	overflow: hidden;
}
.task-container {
	width: 456px;
	position: absolute;
	right: 336px;
	top: 10px;
}
.video-container {
	width: 416px;
	height: 300px;
	position: absolute;
	left: 0;
	bottom: 0;
}
.map-container{
	width: 83%;
	height: 100%;
}
.alarm-box {
	width: 17%;
	height: 100%;
	overflow: hidden;
	border-top: 2px solid #001129;
	.alarm-list{
		height: 60%;
		overflow: hidden;
	}
	.drone-list {
		height: 40%;
		overflow: hidden;
	}
}
</style>
