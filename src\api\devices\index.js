import request from '@/utils/request';
import { MAIN_PATH, CONTROL_PATH, APPLICTION_DEVICE, API_VERSION, CARRIER_PATH } from '../config/index';
import { useUserStoreHook } from '@/store/modules/user';

// 主路径
const BASE_URL = MAIN_PATH + API_VERSION + APPLICTION_DEVICE;
const CONTROL_URL = CONTROL_PATH + API_VERSION + APPLICTION_DEVICE;
const BATTERY_URL = MAIN_PATH + API_VERSION + '/battery';

/**
 * 获取一个工作区中所有联机设备的拓扑列表
 *
 * @param queryParams
 */
export function getDevices(queryParams) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;

  return request({
    url: `${BASE_URL}/${workspace_id}/devices`,
    method: 'get',
    params: queryParams
  });
}

/**
 * 分页获取设备告警信息
 *
 * @param queryParams
 */
export function getDevicesHms(queryParams) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;

  return request({
    url: `${BASE_URL}/${workspace_id}/devices/hms`,
    method: 'get',
    params: queryParams
  });
}

/**
 * 更新未读告警信息状态
 */
export function putDeviceHms(device_sn) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/devices/hms/${device_sn}`,
    method: 'put',
    data: {}
  });
}

/**
 * 获取未读告警信息列表
 *
 * @param queryParams
 */
export function getUnreadDevicesHms(device_sn) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;

  return request({
    url: `${BASE_URL}/${workspace_id}/devices/hms/${device_sn}`,
    method: 'get',
    params: {}
  });
}

/**
 * 根据设备 sn 获取设备信息
 *
 * @param queryParams
 */
export function getDevicesBySn(device_sn) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;

  return request({
    url: `${BASE_URL}/${workspace_id}/devices/${device_sn}`,
    method: 'get',
    params: {}
  });
}

/**
 * 在一个工作区中获取绑定设备列表
 *
 * @param queryParams
 */
export function getDevicesBound(query) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;

  return request({
    url: `${BASE_URL}/${workspace_id}/devices/bound`,
    method: 'GET',
    params: query
  });
}

// 获取设备型号
export function getDeviceModal(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;

  return request({
    url: `/manage/api/v1/devices/${workspace_id}/getDeviceModelList`,
    method: 'get',
    params: data
  });
}

export function updateBindCode(data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;

  return request({
    url: `/manage/api/v1/devices/${workspace_id}/bindCode`,
    method: 'put',
    data: data
  });
}

// 刷新token
export function refreshToken(data) {
  return request({
    url: `/manage/api/v1/token/refresh`,
    method: 'POST',
    data: data
  });
}

export function login(data) {
  return request({
    url: `/manage/api/v1/login`,
    method: 'POST',
    data: data
  });
}

// 刷新视频
export function refreshStreams(data) {
  return request({
    url: `/manage/api/v1/live/streams/refresh`,
    method: 'POST',
    data: data
  });
}

// 切换直播模式
export function changeLiveStreams(data) {
  return request({
    url: `/manage/api/v1/live/streams/switch`,
    method: 'POST',
    data: data
  });
}

/**
 * 首页无人机列表
 *
 * @param queryParams
 */
export function homeCapacityList(queryParams) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;

  return request({
    url: `/manage/api/v1/live/capacityList`,
    method: 'get',
    params: queryParams
  });
}

/**
 * 根据device获取直播在线海康设备
 *
 * @param queryParams
 */
export function getCameraSelected(queryParams) {
  return request({
    url: `/manage/api/v1/live/getCapacityCameraByDeviceSn`,
    method: 'GET',
    params: queryParams
  });
}

/**
 * 删除设备的绑定状态
 *
 * @param ids
 */
export function deleteDevices(device_sn) {
  return request({
    url: `${BASE_URL}/${device_sn}/unbinding`,
    method: 'delete'
  });
}

/**
 * 设置设备属性
 */
export function putDeviceProps(device_sn, data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/devices/${device_sn}/property`,
    method: 'put',
    data
  });
}

/**
 * 发送机场控制指令
 */
export function postSendCmd(params, data) {
  return request({
    url: `${CONTROL_URL}/${params.dock_sn}/jobs/${params.device_cmd}`,
    method: 'post',
    data
  });
}

/**
 * 更新设备信息
 */
export function updateDeviceInfo(device_sn, data) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/devices/${device_sn}`,
    method: 'put',
    data
  });
}

/**
 * 获取无人机设备OSD信息
 *
 * @param queryParams
 */
export function getDevicesOsd(device_sn) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;

  return request({
    url: `${BASE_URL}/${workspace_id}/osd/${device_sn}`,
    method: 'get',
    params: {}
  });
}

/**
 * 负载管理-分页
 *
 * @param data
 */
export function loadManagementList(data) {
  return request({
    url: '/manage/api/v1/devicePayload/page',
    method: 'POST',
    data
  });
}

/**
 * 负载管理-新增
 *
 * @param data
 */
export function addLoadManage(data) {
  return request({
    url: '/manage/api/v1/devicePayload/add',
    method: 'POST',
    data
  });
}

/**
 * 负载管理-编辑
 *
 * @param data
 */
export function editLoadManage(data) {
  return request({
    url: '/manage/api/v1/devicePayload/update',
    method: 'POST',
    data
  });
}

/**
 * 负载管理-删除
 *
 * @param data
 */
export function deleteLoadManage(data) {
  return request({
    url: `/manage/api/v1/devicePayload/delete/${data.id}`,
    method: 'POST'
  });
}

/**
 * 获取无人机设备列表
 *
 * @param queryParams
 */
export function getUavList() {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${BASE_URL}/${workspace_id}/droneList`,
    method: 'get'
  });
}

/**
 * 获取电池列表
 *
 * @param queryParams
 */
export function getBatteryList(queryParams) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;

  return request({
    url: `${BATTERY_URL}/${workspace_id}/battery/page`,
    method: 'get',
    params: queryParams
  });
}

/**
 * 手动同步运载机设备数据
 */
export function carrierSyncData() {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `${CARRIER_PATH}/sync?workspace_id=${workspace_id}`,
    method: 'POST'
  });
}
