import * as Cesium from 'cesium';
class CesiumEngine {
  static instance = null;
  el = 'cesiumContainer';
  viewer = null;
  constructor() {}
  init(el) {
    // 使用在线的Cesium需要token
    Cesium.Ion.defaultAccessToken =
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiIzNmI0MDRkZi04NzhjLTQyYmMtYjQxOC1iNzc2MDA1MmM4ZjIiLCJpZCI6OTAwMTAsImlhdCI6MTY1MDA3NTM0MX0.mGMDeDFon6i_AOEfTs3pEq30wRCipCWL3O-bzLHswtw";
    this.el = el || 'cesiumContainer';
    this.viewer = new Cesium.Viewer(this.el, {
      // 是否显示信息窗口
      // infoBox: true,
      // 是否创建动画
      animation: false,
      // 动画自动播放
      shouldAnimate: true,
      // 是否显示图层选择器
      baseLayerPicker: false,
      // 是否显示全屏按钮
      fullscreenButton: false,
      // 是否显示右上角的查询按钮
      geocoder: false,
      // 是否显示HOME按钮
      homeButton: false,
      // 选择框去掉
      selectionIndicator: false,
      // 是否显示场景控制按钮
      sceneModePicker: false,
      // 是否显示帮助按钮
      navigationHelpButton: false,
      // 是否显示时间轴
      timeline: false,
      //是否指定仅为三维模式，全部使用三维模式可节约GPU资源
      scene3DOnly: false,
      //是否显示点击要素之后显示的信息
      infoBox: false,
      // 去掉访问官网底图
      imageryProvider: new Cesium.SingleTileImageryProvider({
        url: 'data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWNgYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg=='
      })
    });
    // 隐藏版权信息
    this.viewer._cesiumWidget._creditContainer.style.display = 'none'; // 隐藏版权信息
    // 删除默认底图
    this.viewer.imageryLayers.removeAll();
    // this.viewer.scene.skyAtmosphere.show = false; // 隐藏大气层
    this.viewer.scene.globe.baseColor = Cesium.Color.fromCssColorString('#585958').withAlpha(0.1);
    // 设置地球地下颜色
    this.viewer.scene.globe.undergroundColor = Cesium.Color.BLACK.withAlpha(0.7);
    // 设置cesium请求调度器的最大并发数量
    Cesium.RequestScheduler.maximumRequestsPerServer = 18;
    this.viewer.scene.globe.depthTestAgainstTerrain = true;
    //#region  抗锯齿（线，边框等）
    // 是否支持图像渲染像素化处理
    if (Cesium.FeatureDetection.supportsImageRenderingPixelated()) {
      this.viewer.resolutionScale = window.devicePixelRatio;
    }
    // 开启抗锯齿
    this.viewer.scene.postProcessStages.fxaa.enabled = true;
    // this.viewer.scene.fxaa = false;
    // this.viewer.scene.globe.maxmumScreenSpaceError = 4/3;
    //#endregion
    // 取消Cesium的默认双击事件
    this.viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);
  }

  static getInstance() {
    if (!CesiumEngine.instance) {
      CesiumEngine.instance = new CesiumEngine();
    }
    return CesiumEngine.instance;
  }
  destroyed() {
    this.viewer.entities.removeAll();
  }
}

export default CesiumEngine;
