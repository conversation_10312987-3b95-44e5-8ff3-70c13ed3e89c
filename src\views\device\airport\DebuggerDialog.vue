<template>
  <el-dialog
    width="1200px"
    title="远程调试"
    :model-value="visible"
    v-if="visible"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <div class="debugger-dialog-content">
      <osdWidget :isDialog="false" :showDeleteIcon="false" />
      <div class="line-middle"></div>
      <div class="right-pannel">
        <div class="device-control-text">设备控制</div>
        <DeviceSettingBox :sn="osdVisible.gateway_sn" :deviceInfo="deviceInfo"></DeviceSettingBox>
        <!-- cmd -->
        <div class="control-cmd-wrapper">
          <div class="control-cmd-header">
            设备远程调试
            <el-switch
              class="debug-btn"
              inline-prompt
              active-text="开"
              inactive-text="关"
              v-model="debugStatus"
              @change="onDeviceStatusChange"
            />
          </div>
          <div class="control-cmd-box">
            <div v-for="(cmdItem, index) in cmdList" :key="cmdItem.cmdKey" class="control-cmd-item">
              <div class="control-cmd-item-left">
                <div class="item-label">{{ cmdItem.label }}</div>
                <div class="item-status">{{ cmdItem.status }}</div>
              </div>
              <div class="control-cmd-item-right">
                <el-button
                  :disabled="!debugStatus || cmdItem.disabled"
                  :loading="cmdItem.loading"
                  size="small"
                  type="primary"
                  @click="sendControlCmd(cmdItem, index)"
                >
                  {{ cmdItem.operateText }}
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { useConnectWebSocket } from '@/hooks/useConnectWebSocket';
import { DOMAIN, EBizCode, EDockModeCode, EDeviceTypeName } from '@/utils/constants';
import { useDeviceStateStore } from '@/store/modules/deviceState.js';
import osdWidget from '@/views/map/map-fly-manager/components/osd.vue';
import DeviceSettingBox from './components/DeviceSettingBox.vue';
import { EModeCode } from '@/views/map/map-fly-manager/components/osdInfo';

import { updateDeviceCmdInfoByOsd, updateDeviceCmdInfoByExecuteInfo } from './utils/device-cmd';
import { useDockControl } from './utils/use-dock-control';

import { cmdList as baseCmdList } from '@/types/device-cmd';
import { wgs84togcj02 } from '@/utils';

const initCmdList = baseCmdList.map(cmdItem => Object.assign({}, cmdItem));
const cmdList = ref(initCmdList);
const deviceStateStore = useDeviceStateStore();
const deviceInfo = reactive({});
// dock 控制指令
const debugStatus = ref(deviceInfo.dock?.basic_osd?.mode_code === EDockModeCode.Remote_Debugging);
const { osdVisible } = deviceStateStore;

useConnectWebSocket(payload => {
  if (!payload || !props.visible) {
    return;
  }
  switch (payload.biz_code) {
    // 机场信息更新
    case EBizCode.DockOsd: {
      const info = payload.data;
      deviceStateStore.setCurrentDock(info);
      break;
    }
    // 遥控器信息更新
    case EBizCode.GatewayOsd: {
      deviceStateStore.setGatewayInfo(payload.data);
      break;
    }
    // 飞机信息更新
    case EBizCode.DeviceOsd: {
      const info = payload.data;
      deviceStateStore.setCurrentNav(info);
      break;
    }
    // 飞行器数据格式化
    case EBizCode.DeviceReboot:
    case EBizCode.DroneOpen:
    case EBizCode.DroneClose:
    case EBizCode.CoverOpen:
    case EBizCode.CoverClose:
    case EBizCode.PutterOpen:
    case EBizCode.PutterClose:
    case EBizCode.ChargeOpen:
    case EBizCode.ChargeClose:
    case EBizCode.DeviceFormat:
    case EBizCode.DroneFormat:
    case EBizCode.DroneFormat: {
      deviceStateStore.setDeviceCmdExecuteInfo({
        biz_code: payload.biz_code,
        timestamp: payload.timestamp,
        ...payload.data
      });
      break;
    }
    // 设备离线
    case EBizCode.DeviceOffline: {
      deviceStateStore.setDeviceOffline(payload.data);

      break;
    }
  }
});
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  formData: {
    type: Object,
    default(rawProps) {
      return {};
    }
  }
});

// 根据机场指令执行状态更新信息
watch(
  () => deviceStateStore.devicesCmdExecuteInfo,
  devicesCmdExecuteInfo => {
    if (!devicesCmdExecuteInfo) return;
    if (osdVisible.gateway_sn && devicesCmdExecuteInfo[osdVisible.gateway_sn]) {
      updateDeviceCmdInfoByExecuteInfo(cmdList.value, devicesCmdExecuteInfo[osdVisible.gateway_sn]);
    }
  },
  {
    immediate: true,
    deep: true
  }
);

watch(
  () => props.formData,
  (newVal, oldVal) => {
    if (!newVal) return;
    const gateway = newVal;
    const osdinfo = {};
    osdinfo.dock_sn = gateway?.device_sn;
    osdinfo.device_sn = gateway?.children.device_sn;
    osdinfo.dock_callsign = gateway?.nickname;
    osdinfo.device_callsign = gateway?.children.nickname;
    deviceStateStore.setNowOsdVisible(osdinfo);

    const child = gateway.children;
    const device = {
      model: child?.device_name,
      callsign: child?.nickname,
      sn: child?.device_sn,
      mode: EModeCode.Disconnected,
      gateway: {
        model: gateway?.device_name,
        callsign: gateway?.nickname,
        sn: gateway?.device_sn,
        domain: gateway?.domain
      },
      payload: []
    };
    const payloads_list = child?.payloads_list || [];
    payloads_list.forEach(payload => {
      device.payload.push({
        index: payload.index,
        model: payload.model,
        payload_name: payload.payload_name,
        payload_sn: payload.payload_sn,
        control_source: payload.control_source,
        payload_index: payload.payload_index
      });
    });
    if (DOMAIN.DOCK == gateway.domain) {
      // 设置远程控制中用到的osd 显示设备相关信息
      deviceStateStore.setOsdVisible(device);
    }
  },
  { deep: true }
);

watch(
  () => deviceStateStore.deviceState,
  data => {
    if (data.currentType === EDeviceTypeName.Gateway && data.gatewayInfo[data.currentSn]) {
      const coordinate = wgs84togcj02(
        data.gatewayInfo[data.currentSn].longitude,
        data.gatewayInfo[data.currentSn].latitude
      );
      // deviceTsaUpdateHook.moveTo(data.currentSn, coordinate[0], coordinate[1]);
      if (osdVisible.visible && osdVisible.gateway_sn !== '') {
        deviceInfo.gateway = data.gatewayInfo[osdVisible.gateway_sn];
      }
    }
    if (data.currentType === EDeviceTypeName.Aircraft && data.deviceInfo[data.currentSn]) {
      const coordinate = wgs84togcj02(
        data.deviceInfo[data.currentSn].longitude,
        data.deviceInfo[data.currentSn].latitude
      );
      // deviceTsaUpdateHook.moveTo(data.currentSn, coordinate[0], coordinate[1]);
      if (osdVisible.visible && osdVisible.sn !== '') {
        deviceInfo.device = data.deviceInfo[osdVisible.sn];
      }
    }
    if (data.currentType === EDeviceTypeName.Dock && data.dockInfo[data.currentSn]) {
      const coordinate = wgs84togcj02(
        data.dockInfo[data.currentSn].basic_osd?.longitude,
        data.dockInfo[data.currentSn].basic_osd?.latitude
      );
      // deviceTsaUpdateHook.initMarker(
      //   EDeviceTypeName.Dock,
      //   EDeviceTypeName[EDeviceTypeName.Dock],
      //   data.currentSn,
      //   coordinate[0],
      //   coordinate[1]
      // );
      if (osdVisible.visible && osdVisible.is_dock && osdVisible.gateway_sn !== '') {
        deviceInfo.dock = data.dockInfo[osdVisible.gateway_sn];
        deviceInfo.device = data.deviceInfo[deviceInfo.dock?.basic_osd?.sub_device?.device_sn ?? osdVisible.sn];
      }
    }
    debugStatus.value = deviceInfo.dock?.basic_osd?.mode_code === EDockModeCode.Remote_Debugging;
    // 根据设备osd信息更新信息
    updateDeviceCmdInfoByOsd(cmdList.value, deviceInfo);
  },
  {
    deep: true
  }
);

const emit = defineEmits(['update:visible']);

// 关闭弹窗
function closeDialog() {
  emit('update:visible', false);
}

async function onDeviceStatusChange(status) {
  console.log(status)
  let result = false;
  if (status) {
    result = await dockDebugOnOff(osdVisible.gateway_sn, true);
  } else {
    result = await dockDebugOnOff(osdVisible.gateway_sn, false);
  }
  if (!result) {
    if (status) {
      debugStatus.value = false;
    } else {
      debugStatus.value = true;
    }
  }
}

const { sendDockControlCmd, dockDebugOnOff } = useDockControl();

async function sendControlCmd(cmdItem, index) {
  const success = await sendDockControlCmd(
    {
      sn: osdVisible.gateway_sn,
      cmd: cmdItem.cmdKey,
      action: cmdItem.action
    },
    true
  );
  if (success) {
    // cmdList.value[index].loading=true
  }
}
onMounted(() => {});
</script>
<style lang="scss" scoped>
.debugger-dialog-content {
  display: flex;

  .line-middle {
    width: 1px;
    height: 600px;
    background: rgba(0, 0, 0, 0.5);
    margin: auto 32px;
  }
  .right-pannel {
    flex: 1;
    .device-control-text {
      color: #fff;
      font-size: 16px;
    }
    .control-cmd-wrapper {
      .control-cmd-header {
        font-size: 14px;
        font-weight: 600;
        padding: 10px 10px 0px;

        .debug-btn {
          margin-left: 10px;
        }
      }

      .control-cmd-box {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        padding: 4px 10px;
        .control-cmd-item {
          width: 220px;
          height: 58px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          border: 1px solid #666;
          margin: 4px 0;
          padding: 0 8px;

          .control-cmd-item-left {
            display: flex;
            flex-direction: column;

            .item-label {
              font-weight: 700;
            }
          }
        }
      }
    }
  }
  .line-right {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    .item {
      padding: 6px;
      width: 100%;
      color: #000000d9;
      .color {
        color: #000000a6;
      }
    }
  }
}
</style>
