<template>
  <div class="warp-bg">
    <div class="svg-icon" v-show="templateType === 0">
      <svg
        width="17px"
        height="17px"
        viewBox="0 0 34 34"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
      >
        <title>01图标/面性/航点航线</title>
        <g id="01图标/面性/航点航线" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
          <path
            d="M19.708519,6.84641414 L20.8304627,9.62872297 L10.0212851,13.9865686 L23.502702,19.1230103 C23.7799591,19.820482 24.2659893,20.504016 24.9080643,21.1661977 C25.2629027,21.532148 25.6419276,21.8657675 26.0210517,22.1612811 L26.2130936,22.3075731 L26.3032123,22.3737904 L26.5346562,22.5353846 C26.5832508,22.5671431 26.6374507,22.586353 26.6928263,22.5930146 C26.6783047,22.6673506 26.6590699,22.7418668 26.6361977,22.816026 L26.5826501,22.9715261 C26.3415092,23.6045209 25.8538311,24.1125709 25.231229,24.3794004 L6.50316406,32.4057139 L6.19493654,31.6864141 L6.30046586,31.6151102 C6.32831767,31.5954048 6.35693547,31.5749009 6.38627202,31.5536106 L6.47639073,31.4873933 L6.66843262,31.3411014 C7.04755671,31.0455878 7.42658167,30.7119682 7.78142004,30.346018 C8.53468859,29.5691605 9.07318528,28.7629151 9.31176961,27.9392537 L22.9132851,22.1085686 L7.76185519,16.336752 C7.17046929,16.1114622 6.68882387,15.6712857 6.41054264,15.1080548 L6.33325282,14.9354857 C6.28296313,14.8107723 6.24344721,14.6843881 6.21426645,14.5573646 L6.23996894,14.5396095 C6.29567257,14.5001988 6.35444011,14.4575937 6.41589382,14.4118926 L6.60793571,14.2656007 C6.9870598,13.9700871 7.36608476,13.6364676 7.72092313,13.2705173 C8.40387695,12.5661765 8.91028376,11.8376781 9.17668929,11.0939449 L19.708519,6.84641414 Z M5.91228511,24.0028189 C7.56562074,24.0028189 8.90905611,25.3133713 8.90905611,26.9340002 C8.90905611,27.9198876 8.3690529,28.8848027 7.47977788,29.8019257 C7.17567752,30.1155491 6.85084898,30.4014647 6.52593549,30.6547231 L6.36135349,30.7800969 C6.30868699,30.8192631 6.25832257,30.8557762 6.21058394,30.8895516 L6.08577086,30.9753338 C5.98037014,31.0442172 5.84420009,31.0442172 5.73879937,30.9753338 L5.54044946,30.8368458 L5.46321674,30.7800969 L5.29863474,30.6547231 C4.97372125,30.4014647 4.64889271,30.1155491 4.34479235,29.8019257 C3.45551733,28.8848027 2.91551411,27.9198876 2.91551411,26.9340002 C2.91551411,25.3133713 4.25894949,24.0028189 5.91228511,24.0028189 Z M5.91228511,26.048234 C5.3868723,26.048234 4.96092924,26.474177 4.96092924,26.9995899 C4.96092924,27.5250027 5.3868723,27.9509457 5.91228511,27.9509457 C6.43769793,27.9509457 6.86364099,27.5250027 6.86364099,26.9995899 C6.86364099,26.474177 6.43769793,26.048234 5.91228511,26.048234 Z M26.9122851,15.0028189 C28.5656207,15.0028189 29.9090561,16.3133713 29.9090561,17.9340002 C29.9090561,18.9198876 29.3690529,19.8848027 28.4797779,20.8019257 C28.1756775,21.1155491 27.850849,21.4014647 27.5259355,21.6547231 L27.3613535,21.7800969 C27.308687,21.8192631 27.2583226,21.8557762 27.2105839,21.8895516 L27.0857709,21.9753338 C26.9803701,22.0442172 26.8442001,22.0442172 26.7387994,21.9753338 L26.5404495,21.8368458 L26.4632167,21.7800969 L26.2986347,21.6547231 C25.9737212,21.4014647 25.6488927,21.1155491 25.3447923,20.8019257 C24.4555173,19.8848027 23.9155141,18.9198876 23.9155141,17.9340002 C23.9155141,16.3133713 25.2589495,15.0028189 26.9122851,15.0028189 Z M26.9122851,17.048234 C26.3868723,17.048234 25.9609292,17.474177 25.9609292,17.9995899 C25.9609292,18.5250027 26.3868723,18.9509457 26.9122851,18.9509457 C27.4376979,18.9509457 27.863641,18.5250027 27.863641,17.9995899 C27.863641,17.474177 27.4376979,17.048234 26.9122851,17.048234 Z M5.91228511,7.00281886 C7.56562074,7.00281886 8.90905611,8.31337129 8.90905611,9.93400021 C8.90905611,10.9198876 8.3690529,11.8848027 7.47977788,12.8019257 C7.17567752,13.1155491 6.85084898,13.4014647 6.52593549,13.6547231 L6.36135349,13.7800969 C6.30868699,13.8192631 6.25832257,13.8557762 6.21058394,13.8895516 L6.08577086,13.9753338 C5.98037014,14.0442172 5.84420009,14.0442172 5.73879937,13.9753338 L5.54044946,13.8368458 L5.46321674,13.7800969 L5.29863474,13.6547231 C4.97372125,13.4014647 4.64889271,13.1155491 4.34479235,12.8019257 C3.45551733,11.8848027 2.91551411,10.9198876 2.91551411,9.93400021 C2.91551411,8.31337129 4.25894949,7.00281886 5.91228511,7.00281886 Z M20.0062675,3.19141617 L20.0971257,3.19948075 L30.4054861,5.07375956 C30.6771741,5.12315822 30.8573752,5.3834504 30.8079766,5.65513843 C30.7878188,5.76600407 30.7308029,5.8668102 30.646174,5.94121095 L22.9066612,12.7453339 C22.6992691,12.9276609 22.3833393,12.9073415 22.2010124,12.6999494 C22.1207807,12.6086879 22.0765291,12.4913318 22.0765291,12.3698172 L22.0765291,8.04145182 L19.5808766,3.95187176 C19.5329279,3.87329913 19.5092373,3.78672878 19.5076907,3.70089519 L19.5086532,3.65958654 C19.5097164,3.64314161 19.5115887,3.62677612 19.514255,3.61055251 C19.5182073,3.58843892 19.521191,3.57513353 19.5246732,3.56207218 C19.5278462,3.5502908 19.5315109,3.53836448 19.535617,3.526583 C19.5419923,3.5080805 19.549435,3.49017937 19.5578219,3.47288985 L19.570222,3.44910269 C19.578982,3.43316211 19.5887552,3.41761718 19.5993055,3.40271409 C19.6064081,3.39265523 19.6137248,3.38306297 19.6214126,3.3737166 C19.6295492,3.36388203 19.6385539,3.35378805 19.6479361,3.34408824 C19.6594674,3.33205619 19.6718205,3.3204982 19.684848,3.30951328 C19.6933439,3.30240979 19.7021498,3.29546169 19.7111763,3.28881646 L19.7472252,3.26461045 L19.8125141,3.23141617 L19.8310485,3.22365265 C19.8473213,3.21742615 19.8635885,3.21220997 19.8801448,3.20783179 C19.9209056,3.19715814 19.9634574,3.19153764 20.0062675,3.19141617 Z M29.478,5.831 L29.4176941,5.85562141 L22.922,7.789 L22.9296488,7.80170298 L22.9294694,11.5883562 L29.478,5.831 Z M5.91228511,9.04823399 C5.3868723,9.04823399 4.96092924,9.47417705 4.96092924,9.99958986 C4.96092924,10.5250027 5.3868723,10.9509457 5.91228511,10.9509457 C6.43769793,10.9509457 6.86364099,10.5250027 6.86364099,9.99958986 C6.86364099,9.47417705 6.43769793,9.04823399 5.91228511,9.04823399 Z"
            id="形状结合"
            fill="#ffffff"
            fill-rule="nonzero"
          ></path>
        </g>
      </svg>
    </div>
    <div class="svg-icon" v-show="templateType === 1">
      <svg
        width="17px"
        height="17px"
        viewBox="0 0 34 34"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
      >
        <title>01图标/面性/面状航线</title>
        <g id="01图标/面性/面状航线" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
          <path
            d="M30,5 L30,9 L28.9992547,8.9997247 L28.9992547,26.9997247 L30,27 L30,31 L26,31 L25.9992547,29.9997247 L7.99925467,29.9997247 L8,31 L4,31 L4,27 L4.99925467,26.9997247 L4.99925467,20.6057247 L4.98959236,20.5961941 L4.99925467,20.5857247 L5,15.5147186 L7,15.5147186 L6.99925467,18.5857247 L17.5852547,7.9997247 L15.5301941,8 L15.5301941,6 L25.9992547,5.9997247 L26,5 L30,5 Z M26.9992547,9.4137247 L8.41325467,27.9997247 L14.5842547,27.9997247 L26.9992547,15.5847247 L26.9992547,9.4137247 Z M26.9992547,18.4147247 L17.4142547,27.9997247 L25.9992547,27.9997247 L26,27 L26.9992547,26.9997247 L26.9992547,18.4147247 Z M25.5852547,7.9997247 L20.4132547,7.9997247 L6.99925467,21.4137247 L6.99925467,26.5857247 L25.5852547,7.9997247 Z M4.52999821,2.95501562 C4.53335866,2.95599733 4.53660326,2.95688244 4.53984003,2.95780064 L4.61494397,2.98578508 L4.62495566,2.99059674 L14.0173663,7.63363835 C14.2649135,7.75601075 14.3663877,8.05588982 14.2440153,8.303437 C14.1940798,8.40445168 14.1114866,8.48563702 14.0096284,8.53382872 L4.69446076,12.9410727 C4.44484653,13.0591716 4.14675615,12.9525573 4.02865728,12.7029431 C3.97668867,12.593102 3.96649903,12.4680948 3.99999298,12.3512874 L5.19305218,8.19059556 L3.92131804,3.57154384 C3.90493422,3.51203633 3.90002616,3.45207853 3.90525532,3.39402613 C3.90655323,3.38100234 3.90828707,3.36766476 3.91055724,3.35440928 C3.92365675,3.27781299 3.95439439,3.2059136 3.99950415,3.14402751 C4.00437896,3.13721299 4.00974622,3.1302273 4.01527333,3.12341348 C4.02770151,3.10822661 4.04074501,3.09393486 4.05462306,3.08040637 L4.07709629,3.05978562 C4.09173176,3.04719391 4.10700933,3.03548989 4.12301952,3.02465454 C4.13255652,3.01827231 4.14223344,3.01220985 4.15209248,3.00649152 L4.19397655,2.98469893 C4.20992088,2.97744049 4.22580127,2.97110556 4.24196189,2.96559341 C4.25108703,2.9624645 4.26079696,2.95947262 4.27065755,2.95675777 L4.32925467,2.9447247 L4.36460429,2.94032521 C4.41897038,2.93591205 4.47502325,2.94056697 4.52999821,2.95501562 Z M12.9166198,8.10610317 L12.8523294,8.11293983 L6.07585147,8.1814507 L6.07920723,8.19528585 L5.03529173,11.8352011 L12.9166198,8.10610317 Z"
            id="形状结合"
            fill="#ffffff"
            fill-rule="nonzero"
          ></path>
        </g>
      </svg>
    </div>
    <div class="title" v-show="job_type === 1">警情航线</div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, computed, onMounted, onUnmounted } from 'vue';
const emits = defineEmits(['update:modelValue']);
const props = defineProps({
  job_type: {
    type: Number,
    default: 0
  },
  templateType: {
    type: Number,
    default: 0
  }
});

onMounted(() => {});
onUnmounted(() => {});
</script>
<style lang="scss" scoped>
// 背景横向渐变
.warp-bg {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  user-select: none;
  padding: 2px 5px;
  border-radius: 3px;
  background: linear-gradient(to right, #162e67 0%, #21479f 100%); /* 从左到右的渐变，包含三个颜色点 */
}
.svg-icon {
  //  padding: 2px 5px;
  margin-right: 5px;
}
</style>
