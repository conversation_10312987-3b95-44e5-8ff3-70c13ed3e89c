<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <span>GB28181配置列表</span>
      </div>
      <div class="search-btn">
        <el-button type="primary" @click="cascadeSetting()">国标平台配置</el-button>
        <el-button type="primary" @click="onAdd()">新增设备</el-button>
      </div>
    </div>
    <el-card shadow="never">
      <el-table :data="dataList" v-loading="loading" stripe height="630" style="width: 100%; margin-top: 10px">
        <!-- <el-table-column prop="id" label="编号" min-width="30" /> -->
        <el-table-column prop="device_sn" label="设备序列号" min-width="140" />
        <el-table-column prop="source" label="视频平台">
          <template #default="scope">
            {{ gbPlatformMap[scope.row.source] || '未知平台' }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="设备名称" min-width="100" />
        <el-table-column prop="online" label="外部视频">
          <template #default="scope">
            <el-tag type="success" v-if="scope.row.online">是</el-tag>
            <el-tag type="danger" v-else>否</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="视频类型" min-width="60">
          <template #default="scope">
            {{ gbVideoTypeMap[scope.row.type] || '未知' }}
          </template>
        </el-table-column>
        <el-table-column prop="local_port" label="本地端口" min-width="60" />
        <el-table-column prop="index_code" label="isc索引" min-width="195" />
        <el-table-column prop="no" label="视频编号" min-width="140" />
        <!-- <el-table-column prop="channel" label="通道编码" min-width="120" /> -->
        <el-table-column prop="url_type" label="推流方式">
          <template #default="scope">
            {{ gbUrlTypeMap[scope.row.url_type] || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="stream_transfer_type" label="流传输类型" />
        <el-table-column prop="agent_password" label="代理密码" />
        <el-table-column label="操作" min-width="80" >
          <template #default="scope">
            <el-button type="primary" link @click.stop="onEdit(scope.row)">编辑</el-button>
            <el-button type="danger" link @click.stop="onDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-content">
        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="handleSearch"
        />
      </div>
    </el-card>
    <CascadeDialog v-model:visible="cascadeDialog.visible" @setCascade="setCascade()" />
    <EditGBDeviceDialog
      ref="editDialogRef"
      v-model:visible="editGBDeviceDialog.visible"
      :title="editGBDeviceDialog.title"
      :form-data="editGBDeviceDialog.formData"
      @submit="handleSearch()"
    />
  </div>
</template>

<script setup>
import { ref, computed, reactive, onMounted } from 'vue';
import CascadeDialog from './CascadeDialog.vue';
import EditGBDeviceDialog from './EditGBDeviceDialog.vue';
import optionData from '@/utils/option-data';
import {
  getDeviceVideoConfig,
  createDeviceVideo,
  updateDeviceVideo,
  deleteDeviceVideo,
  pageDeviceVideo
} from '@/api/wayline';

const loading = ref(false);
const dataList = ref([]);
const total = ref(0);
const cascadeDialog = reactive({
  visible: false
});
const editGBDeviceDialog = reactive({
  visible: false,
  title: '编辑国标设备',
  formData: {}
});

//  国标平台配置信息
const cascadeInfo = ref({});
// 国标平台
const gbPlatformMap = computed(() => {
  const map = {};
  optionData.gbPlatform.forEach(item => {
    map[item.value] = item.label;
  });
  return map;
});

// 视频类型
const gbVideoTypeMap = computed(() => {
  const map = {};
  optionData.gbVideoType.forEach(item => {
    map[item.value] = item.label;
  });
  return map;
});

// 推流方式
const gbUrlTypeMap = computed(() => {
  const map = {};
  optionData.gbUrlType.forEach(item => {
    map[item.value] = item.label;
  });
  return map;
});

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
});

/**
 * 查询
 */
function handleQuery(params) {
  loading.value = true;
  pageDeviceVideo({
    ...params
  })
    .then(data => {
      const { list } = data;
      dataList.value = list || [];
      loading.value = false;
      total.value = data.pagination.total;
    })
    .catch(err => {
      loading.value = false;
      console.log(err);
    });
}

function handleSearch() {
  // Calculate the new total pages based on the new page size
  const newTotalPages = Math.ceil(total.value / queryParams.pageSize);

  // If current page is greater than new total pages, adjust to the last page
  if (queryParams.pageNo > newTotalPages) {
    queryParams.pageNo = newTotalPages || 1;
  }
  const params = {
    ...queryParams
  };
  handleQuery(params);
}
function cascadeSetting() {
  cascadeDialog.visible = true;
}

function onAdd() {
  let form = {};
  if (cascadeInfo.value?.enabled) {
    form = {
      ...cascadeInfo.value
    };
  }
  editGBDeviceDialog.visible = true;
  editGBDeviceDialog.title = '新增设备';
  editGBDeviceDialog.formData = form;
}
function onEdit(row) {
  editGBDeviceDialog.visible = true;
  editGBDeviceDialog.title = '编辑设备';
  editGBDeviceDialog.formData = row;
}

function onDelete(row) {
  deleteDeviceVideo(row.id).then(res => {
    if (res) {
      handleQuery({ ...queryParams });
    }
  });
}

//  初始化
onMounted(() => {
  setCascade();
  handleQuery({ ...queryParams });
});
// 获取国标平台配置信息
function setCascade() {
  getDeviceVideoConfig().then(res => {
    cascadeInfo.value = res;
  });
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 16px 20px;
  .search {
    display: flex;
    align-items: center;
    padding: 24px;
    height: 64px;
    .search-form {
      flex: 1;
      color: #fff;
    }
  }
  .app-content {
    width: 100%;
    max-height: calc(100vh - 154px);
    padding: 16px 24px;
    background: #fff;
    overflow: auto;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.03);
    border-radius: 4px;
    &::-webkit-scrollbar {
      width: 10px !important;
      height: 10px !important;
      background: #e4e7ec;
      border-radius: 4px;
    }
    &::-webkit-scrollbar-thumb {
      width: 10px !important;
      min-height: 20px !important;
      background: #b7d9fd !important;
      border-radius: 4px !important;
    }
    .btn-box {
      margin-bottom: 16px;
    }
    .textHidden {
      width: 180px;
      height: 20px;
      line-height: 20px;
      text-align: left;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
}
.pagination-content {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;

  &.hidden {
    display: none;
  }
}
</style>
