class CesiumLayerManager {
  constructor(viewer) {
    this.viewer = viewer;
    this.layers = [];
  }

  // 添加图层
  addLayer(layer) {
    this.layers.push(layer);
    this.viewer.imageryLayers.addImageryProvider(layer);
  }

  // 移除图层
  removeLayer(layer) {
    const index = this.layers.indexOf(layer);
    if (index !== -1) {
      this.layers.splice(index, 1);
      this.viewer.imageryLayers.remove(layer);
    }
  }

  // 获取所有图层
  getLayers() {
    return this.layers;
  }

  // 获取图层数量
  getLayerCount() {
    return this.layers.length;
  }

  // 通过名称获取图层
  getLayerByName(name) {
    return this.layers.find(layer => layer.name === name);
  }

  // 通过ID获取图层
  getLayerById(id) {
    return this.layers.find(layer => layer.id === id);
  }
}
export { CesiumLayerManager };
