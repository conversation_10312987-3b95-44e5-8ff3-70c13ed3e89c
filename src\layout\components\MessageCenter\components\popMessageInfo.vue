<template>
  <el-dialog
    v-model="dialogVisible"
    v-if="dialogVisible"
    width="800px"
    :append-to-body="true"
    :close-on-click-modal="false"
    :before-close="handleBeforClose"
    :title="formData.title || $t('page.dialog.title.platformNotification')"
  >
    <div class="mt-2 ml-3">
      {{ formData.content }}
    </div>
    <div class="mt-2 ml-3">
      <i class="el-icon-clock" /> {{ formData.noticeTime }}
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button v-if="showCancel" @click="confirm(false)">
          {{ $t('page.Cancel') }}
        </el-button>
        <el-button type="primary" @click="confirm(true)">
          {{ formData.confireText || $t('page.confirm') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { getMessageRead } from '@/api/messageCenter';
import useDialog from '@/hooks/useDialog';
import { validatenull } from '@/utils/helper';

const props = defineProps(['messageType', 'showCancel']);
const formData = ref({});

const { dialogVisible, dialogClose, dialogOpen } = useDialog();
const emit = defineEmits(['messageClickConfirm', 'messageCount']);

const handleOpen = data => {
  if (!validatenull(data)) {
    formData.value = data;
  }

  dialogOpen();
};

// 确定
const confirm = confirm => {
  if (confirm) {
    emit('messageClickConfirm', formData.value);
  }
  handleBeforClose();
};

const handleBeforClose = async () => {
  if (props.messageType === 0 || formData.value.hasRead) {
    dialogClose();
    return;
  }
  // 告警没有已读未读得概念 messageType预留
  await getMessageRead({
    messageId: formData.value.stationMessageId,
    messageType: props.messageType
  });
  emit('updateMessageCount');

  dialogClose();
};

defineExpose({
  handleOpen
});
</script>
<style lang="scss" scoped></style>
