import request from '@/utils/request';
import { MAIN_PATH, APPLICTION_TENANT, API_VERSION } from '../config/index';

// 主路径
const BASE_URL = MAIN_PATH + API_VERSION + APPLICTION_TENANT;

// 模块路径
const MODULES_AUTH_PATH = '/tenant-sys-user/';

// 设置用户语言环境
export function setLanguage(data) {
  return request({
    url: `${BASE_URL}${MODULES_AUTH_PATH}modify-tenant-language`,
    method: 'post',
    data
  });
}

// 修改用户密码
export function updateUserPwd(data) {
  return request({
    url: `${BASE_URL}${MODULES_AUTH_PATH}modify-password`,
    method: 'put',
    data
  });
}
