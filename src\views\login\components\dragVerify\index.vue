<template>
  <el-dialog
    v-if="dialogVisible"
    v-model="dialogVisible"
    width="400px"
    :append-to-body="true"
    :close-on-click-modal="false"
    :before-close="dialogCancel"
    :title="$t('login.dragVerify.title')"
  >
    <div class="verify-box">
      <drag-verify-img-chip
        ref="dragVerifyImgChipRef"
        :imgsrc="img"
        :width="360"
        v-model:isPassing="isPassing"
        :showRefresh="true"
        :showTips="true"
        :text="$t('login.dragVerify.text')"
        :success-tip="$t('login.dragVerify.successTip')"
        :successText="$t('login.dragVerify.successText')"
        :fail-tip="$t('login.dragVerify.failTip')"
        :success-icon="CircleCheckFilled"
        :handler-icon="DArrowRight"
        handler-bg="#ccc"
      />
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'popDragVerify'
};
</script>

<script setup>
import { DArrowRight, CircleCheckFilled } from '@element-plus/icons-vue';

import useDialog from '@/hooks/useDialog';
import { onUnmounted } from 'vue';
import { onMounted } from 'vue';

const isPassing = ref(false);
const imgIndex = ref(0);
const img = ref('');
const dragVerifyImgChipRef = ref('dragVerifyImgChipRef');
const { dialogClose, dialogOpen, dialogVisible, dialogCancel } = useDialog(
  null,
  { emitName: 'dragVerify' }
);

const imgSrc = populateImgSrc();

const props = defineProps({
  // 登录类型
  loginType: {
    type: String,
    default: 'pwdLogin'
  }
});

onMounted(() => {
  img.value = imgSrc[imgIndex.value];
  window.$bus.on('passfail', passFall);
  window.$bus.on('passcallback', pass);
  window.$bus.on('refresh', refreshImg);
});

onUnmounted(() => {
  window.$bus.off('passfail');
  window.$bus.off('passcallback');
  window.$bus.off('refresh');
});

function populateImgSrc() {
  const imgSrc = {};

  for (let i = 1; i <= 12; i++) {
    imgSrc[`img${i}`] = new URL(
      `/src/assets/validImg/${i}.png`,
      import.meta.url
    ).href;
  }
  return Object.values(imgSrc);
}

function handleOpen() {
  setTimeout(() => {
    dragVerifyImgChipRef.value.reset();
  }, 10);
  dialogOpen();
}

function pass() {
  setTimeout(() => {
    dialogClose({ success: isPassing.value, loginType: props.loginType });
  }, 100);
}

function refreshImg() {
  imgIndex.value = parseInt(Math.random() * 12);
  img.value = imgSrc[imgIndex.value];
}

function passFall() {
  refreshImg();
}

defineExpose({ handleOpen });
</script>

<style lang="scss" scoped>
.valid-wrap {
  padding-bottom: 20px;
  box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.08);
}
.verify-box {
  user-select: none;
}
</style>
