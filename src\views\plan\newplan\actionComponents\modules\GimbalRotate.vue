<template>
  <div class="GimbalRotate-wrapper">
    <Slide v-model="dataRef" @changeHandle="onChangeHandle" />
  </div>
</template>
<script>
export default {
  name: 'GimbalRotate'
};
</script>
<script setup>
import { onMounted, onUnmounted, defineExpose, reactive, ref } from 'vue';
import Slide from './components/Slide.vue';
import { updateFrustumWithActionValue } from '../../kmz/hocks';
import { ACTION_ACTUATOR_FUNC } from '@/utils/constants';
import { toNumber } from '@/components/Cesium/libs/cesium/common';
//#region 初始化
const dataInfo = reactive({
  deviceInfo: null,
  action: null,
  deviceType: 'Matrice_3TD'
});
const dataRef = reactive({
  title: '云台俯仰角',
  unit: '°',
  acttionType: 'GimbalRotate',
  min: -120,
  max: 45,
  value: 0.0 // 默认最小单位值
});
//#endregion

//#region 方法

// 数据变更后进行修改
const onChangeHandle = v => {
  // 获取动作中的参数进行涉资
  if (dataInfo.action) {
    dataInfo.action.wpml_actionActuatorFuncParam.wpml_gimbalPitchRotateEnable = 1;
    dataInfo.action.wpml_actionActuatorFuncParam.wpml_gimbalPitchRotateAngle = v;
  }
  // 更新视锥体状态
  const options = {
    action: dataInfo.action,
    actionUuid: dataInfo.action.uuid,
    type: ACTION_ACTUATOR_FUNC.gimbalRotate,
    min: dataRef.min,
    max: dataRef.max,
    value: v // 默认最小单位值
  };
  updateFrustumWithActionValue(options);
};

/**
 * 设置组件数据
 * @param {*} options
 * @param {*} options.actionFuncParam // 动作参数
 * @param {*} options.deviceInfo // 设备信息
 * @param {*} options.action // 动作对象
 */
const setComponentData = options => {
  // 设置数据前先初始组件数据及界面
  const { actionFuncParam, action, deviceInfo } = options;
  //  获取设备信息
  const { droneSubEnumLabel } = deviceInfo;
  // 设备信息及型号
  dataInfo.deviceInfo = deviceInfo;
  dataInfo.action = action;
  dataInfo.deviceType = droneSubEnumLabel;
  const { wpml_gimbalPitchRotateEnable = 0, wpml_gimbalPitchRotateAngle = 0 } = actionFuncParam;
  // 设置当前的组件数据
  if (wpml_gimbalPitchRotateEnable === 1) {
    dataRef.value = toNumber(wpml_gimbalPitchRotateAngle, 0);
  }
};

const updateComponentValue = options => {
  // 设置数据前先初始组件数据及界面
  const { value = 0 } = options;
  dataRef.value = toNumber(value, 0);
};
const getComponentData = () => {
  return dataInfo;
};

//#endregion

//#region 对外抛出方法
defineExpose({
  setComponentData,
  getComponentData,
  updateComponentValue
});
//#endregion
//#region 生命周期
onMounted(() => {});
onUnmounted(() => {});
//#endregion
</script>
<style lang="scss" scoped>
.GimbalRotate-wrapper {
}
</style>
