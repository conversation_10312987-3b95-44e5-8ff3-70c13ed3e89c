<template>
  <div class="page-wrap">
    <el-card v-loading="pageLoading" class="page-card content">
      <el-page-header
        :content="
          i18n.global.locale === 'en'
            ? paramsData.enTitle
            : paramsData.productTitle
        "
        class="border-b"
        @back="goBack"
      />
      <div
        v-if="paramsData.enIntroduce && i18n.global.locale === 'en'"
        class="editor-box"
        v-html="paramsData.enIntroduce"
      />
      <div
        v-if="paramsData.productIntroduce && i18n.global.locale !== 'en'"
        class="editor-box"
        v-html="paramsData.productIntroduce"
      />
      <div class="text-center my-5">
        <el-button
          size="default"
          plain
          @click="popContactUs.handleOpen(paramsData)"
        >
          {{ $t('page.contactUs') }}
        </el-button>
        <el-button size="default" plain @click="handleLink()">
          {{ $t('page.enterTheExperience') }}
        </el-button>
      </div>
    </el-card>
    <el-backtop target=".page-wrap" :bottom="50" :visibility-height="100" />
    <PopContactUs ref="popContactUs" />
  </div>
</template>
<script>
export default {
  name: 'IndustryPreview'
};
</script>

<script setup>
import { useRoute, useRouter } from 'vue-router';

import i18n from '@/lang';
import { getTenantInfoById } from '@/api/tenantApp/index.js';

import PopContactUs from './components/popContactUs.vue';

const popContactUs = ref('popContactUs');
const pageLoading = ref(true);
const paramsData = ref({});
const route = useRoute();
const router = useRouter();

const getParamsData = async () => {
  pageLoading.value = true;
  try {
    const res = await getTenantInfoById(route.query.applicationId);
    paramsData.value = res.data;
  } catch (err) {
    console.log(err);
  }
  pageLoading.value = false;
};

const goBack = () => {
  router.go(-1);
};

const handleLink = () => {
  window.open(
    paramsData.value.productUrl.indexOf('http') === -1
      ? 'https://' + paramsData.value.productUrl
      : paramsData.value.productUrl
  );
};

onMounted(() => {
  getParamsData();
});
</script>

<style lang="scss" scoped>
.page-wrap {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  overflow: auto;
}

:deep(.page-card) {
  border-radius: 4px;
  overflow: auto;
  margin: 12px;

  .border-b {
    border-bottom: 1px solid #ebeef5;
  }
  &.content {
    // flex: 1;
    min-height: calc(100% - 24px);
    display: flex;
    flex-direction: column;

    .el-card__body {
      display: flex;
      flex-direction: column;
      flex: 1;
      padding: 0;

      .el-page-header {
        line-height: 24px;
        padding: 15px 10px;

        .el-page-header__left {
          align-items: center;
        }
      }
    }

    .editor-box {
      padding: 10px;
      flex: 1;
      height: calc(100% - 36px);
    }
  }
}
</style>
