import { ref, reactive } from 'vue';
import * as Cesium from 'cesium';
import {
  getOrCreateCesiumEngineInstance,
  CesiumLayerManager,
  veclayer,
  imglayer,
  cvalayer,
  cialayer,
  setCameraView,
  calculatArea,
  calculatPerimeter
} from '@/components/Cesium/libs/cesium';
import { drawPolygon, isPolygonCreate, polygonDispose } from '@/components/Cesium/libs/cesium/superEdit/PolygonCreater';
// 线条绘制相关变量
let polylineHandler = null;
export const isPolylineCreate = ref(false);

/**
 * 绘制线条
 */
export const drawPolyline = (viewer, options, callback) => {
  if (!viewer) return;

  polylineDispose();

  let drawResultObject = {
    type: 'polyline',
    cartesianPoints: [],
    entityPoints: [],
    polyline: null,
    length: 0
  };

  isPolylineCreate.value = true;
  document.body.style.cursor = 'crosshair';

  polylineHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);

  // 左键点击添加点
  polylineHandler.setInputAction(click => {
    let cartesian = viewer.camera.pickEllipsoid(click.position, viewer.scene.globe.ellipsoid);
    if (!cartesian) return;

    drawResultObject.cartesianPoints.push(cartesian);

    // 创建点
    let point = viewer.entities.add({
      position: cartesian,
      point: {
        pixelSize: 8,
        color: Cesium.Color.YELLOW,
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 2,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    });
    drawResultObject.entityPoints.push(point);

    // 创建或更新线条
    if (drawResultObject.cartesianPoints.length >= 2) {
      if (drawResultObject.polyline) {
        viewer.entities.remove(drawResultObject.polyline);
      }

      drawResultObject.polyline = viewer.entities.add({
        polyline: {
          positions: drawResultObject.cartesianPoints,
          width: options.width || 3,
          material: options.color || Cesium.Color.BLUE,
          clampToGround: true
        }
      });

      // 计算长度
      drawResultObject.length = calculatPerimeter(drawResultObject.cartesianPoints);
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

  // 右键完成绘制
  polylineHandler.setInputAction(() => {
    if (drawResultObject.cartesianPoints.length >= 2) {
      document.body.style.cursor = 'default';
      isPolylineCreate.value = false;
      polylineHandler.destroy();
      polylineHandler = null;

      drawResultObject.data = {
        cartesianPoints: drawResultObject.cartesianPoints,
        length: drawResultObject.length
      };

      callback && callback(drawResultObject);
    }
  }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
};

/**
 * 销毁线条绘制
 */
export const polylineDispose = () => {
  if (polylineHandler) {
    polylineHandler.destroy();
    polylineHandler = null;
  }
  isPolylineCreate.value = false;
  document.body.style.cursor = 'default';
};
import { ElMessage } from 'element-plus';
import { getDeptSysSetting } from '@/api/wayline';

// Cesium相关变量
export let viewer = null;
export let scene = null;

// 编辑状态
export const editStatus = ref(false);

// 图元类型
export const FEATURE_TYPE = {
  POINT: 'point',
  LINE: 'line',
  POLYGON: 'polygon'
};

// 图元颜色
export const FEATURE_COLOR = {
  POINT: new Cesium.Color.fromCssColorString('#FF4500'),
  LINE: new Cesium.Color.fromCssColorString('#2E90FA'),
  POLYGON: new Cesium.Color.fromCssColorString('#2E90FA').withAlpha(0.6),
  SELECTED: new Cesium.Color.fromCssColorString('#FF4500').withAlpha(0.6)
};

// 当前绘制的图元数据
export const currentFeatureData = reactive({
  type: FEATURE_TYPE.POINT,
  coordinates: [],
  area: 0,
  length: 0
});

/**
 * 初始化Cesium地图
 */
export const initMap = async () => {
  try {
    const engine = getOrCreateCesiumEngineInstance('featureEdit');
    engine?.init('cesium-container');
    viewer = engine.viewer;
    scene = viewer.scene;

    // 添加图层
    const layerManager = new CesiumLayerManager(viewer);
    layerManager.addLayer(imglayer);
    layerManager.addLayer(cialayer);

    // 设置相机视角
    await getDeptSysSetting({})
      .then(res => {
        const { wayline_config = {} } = res;
        setCameraView(viewer, {
          destination: Cesium.Cartesian3.fromDegrees(
            Number(wayline_config.longitude),
            Number(wayline_config.latitude),
            10000
          ),
          orientation: new Cesium.HeadingPitchRoll(
            Cesium.Math.toRadians(0),
            Cesium.Math.toRadians(-90),
            Cesium.Math.toRadians(0)
          ),
          duration: 0.75
        });
      })
      .catch(ex => {
        console.log(ex);
      });
  } catch (error) {
    console.error('初始化Cesium地图失败', error);
    ElMessage.error('初始化地图失败');
    return null;
  }
};

/**
 * 开始绘制点
 * @param {Function} callback - 绘制完成后的回调函数
 */
export const startDrawPoint = callback => {
  if (!viewer) {
    ElMessage.error('地图未初始化');
    return;
  }

  // 设置鼠标样式
  document.body.style.cursor = 'crosshair';

  // 清除现有实体
  clearMap();

  // 添加点击事件监听器
  const handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
  
  handler.setInputAction(click => {
    const pickedPosition = viewer.camera.pickEllipsoid(click.position, viewer.scene.globe.ellipsoid);
    if (pickedPosition) {
      const cartographic = Cesium.Cartographic.fromCartesian(pickedPosition);
      const longitude = Cesium.Math.toDegrees(cartographic.longitude);
      const latitude = Cesium.Math.toDegrees(cartographic.latitude);

      // 清除之前的点
      viewer.entities.removeAll();

      // 创建点实体
      viewer.entities.add({
        position: pickedPosition,
        point: {
          pixelSize: 10,
          color: FEATURE_COLOR.POINT,
          outlineColor: Cesium.Color.WHITE,
          outlineWidth: 2,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        }
      });

      // 更新当前图元数据
      currentFeatureData.type = FEATURE_TYPE.POINT;
      currentFeatureData.coordinates = [[longitude, latitude]];
      currentFeatureData.area = 0;
      currentFeatureData.length = 0;

      // 重置鼠标样式
      document.body.style.cursor = 'default';
      
      // 移除事件监听器
      handler.destroy();
      
      editStatus.value = true;

      // 回调函数
      callback && callback(currentFeatureData);
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
};

/**
 * 开始绘制线条
 * @param {Function} callback - 绘制完成后的回调函数
 */
export const startDrawLine = callback => {
  if (!viewer) {
    ElMessage.error('地图未初始化');
    return;
  }

  // 设置鼠标样式
  document.body.style.cursor = 'crosshair';

  // 清除现有实体
  clearMap();

  // 调用线条绘制工具
  drawPolyline(viewer, { color: FEATURE_COLOR.LINE, width: 3 }, data => {
    if (!data) return;

    document.body.style.cursor = 'default';
    editStatus.value = true;
   
    // 更新当前图元数据
    currentFeatureData.type = FEATURE_TYPE.LINE;
    currentFeatureData.coordinates = data.data.cartesianPoints.map(position => {
      const cartographic = Cesium.Cartographic.fromCartesian(position);
      return [Cesium.Math.toDegrees(cartographic.longitude), Cesium.Math.toDegrees(cartographic.latitude)];
    });

    // 计算长度
    currentFeatureData.length = data.data.length || 0;
    currentFeatureData.area = 0;

    // 回调函数
    callback && callback(currentFeatureData);
  });
};

/**
 * 开始绘制多边形
 * @param {Function} callback - 绘制完成后的回调函数
 */
export const startDrawPolygon = callback => {
  if (!viewer) {
    ElMessage.error('地图未初始化');
    return;
  }

  // 设置鼠标样式
  document.body.style.cursor = 'crosshair';

  // 清除现有实体
  clearMap();

  // 调用多边形绘制工具
  drawPolygon(viewer, { color: FEATURE_COLOR.POLYGON }, data => {
    if (!data) return;

    document.body.style.cursor = 'default';
    editStatus.value = true;
   
    // 更新当前图元数据
    currentFeatureData.type = FEATURE_TYPE.POLYGON;
    currentFeatureData.coordinates = data.data.cartesianPoints.map(position => {
      const cartographic = Cesium.Cartographic.fromCartesian(position);
      return [Cesium.Math.toDegrees(cartographic.longitude), Cesium.Math.toDegrees(cartographic.latitude)];
    });

    // 计算面积和周长
    currentFeatureData.area = data.data.area || 0;
    currentFeatureData.length = data.data.length || 0;

    // 回调函数
    callback && callback(currentFeatureData);
  });
};

/**
 * 清除地图上的图元
 */
export const clearMap = () => {
  if (!viewer) return;

  // 取消正在进行的绘制
  if (isPolygonCreate.value) {
    polygonDispose();
  }

  if (isPolylineCreate.value) {
    polylineDispose();
  }

  // 清除所有实体
  viewer.entities.removeAll();

  // 重置鼠标样式
  document.body.style.cursor = 'default';

  // 重置当前图元数据
  currentFeatureData.type = FEATURE_TYPE.POINT;
  currentFeatureData.coordinates = [];
  currentFeatureData.area = 0;
  currentFeatureData.length = 0;

  editStatus.value = false;
};

/**
 * 绘制已有的图元
 * @param {Array} coordinates - 图元坐标
 * @param {String} type - 图元类型
 */
export const drawExistingFeature = (coordinates, type) => {
  if (!viewer || !coordinates || coordinates.length === 0) return;

  // 清除现有实体
  clearMap();

  let entity = null;

  if (type === FEATURE_TYPE.POINT) {
    // 点图元
    const position = Cesium.Cartesian3.fromDegrees(coordinates[0][0], coordinates[0][1]);
    entity = viewer.entities.add({
      position: position,
      point: {
        pixelSize: 10,
        color: FEATURE_COLOR.POINT,
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 2,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    });
  } else if (type === FEATURE_TYPE.LINE) {
    // 线图元
    const positions = coordinates.map(coord => Cesium.Cartesian3.fromDegrees(coord[0], coord[1]));
    entity = viewer.entities.add({
      polyline: {
        positions: positions,
        width: 3,
        material: FEATURE_COLOR.LINE,
        clampToGround: true
      }
    });
  } else if (type === FEATURE_TYPE.POLYGON) {
    // 面图元
    const positions = coordinates.map(coord => Cesium.Cartesian3.fromDegrees(coord[0], coord[1]));
    entity = viewer.entities.add({
      polygon: {
        hierarchy: new Cesium.PolygonHierarchy(positions),
        material: FEATURE_COLOR.POLYGON,
        outline: true,
        outlineColor: FEATURE_COLOR.LINE,
        outlineWidth: 2
      }
    });
  }

  // 更新当前图元数据
  currentFeatureData.type = type;
  currentFeatureData.coordinates = coordinates;
  
  if (type === FEATURE_TYPE.POLYGON) {
    const positions = coordinates.map(coord => Cesium.Cartesian3.fromDegrees(coord[0], coord[1]));
    currentFeatureData.area = calculatArea(positions);
    currentFeatureData.length = calculatPerimeter(positions);
  } else if (type === FEATURE_TYPE.LINE) {
    const positions = coordinates.map(coord => Cesium.Cartesian3.fromDegrees(coord[0], coord[1]));
    currentFeatureData.length = calculatPerimeter(positions);
    currentFeatureData.area = 0;
  } else {
    currentFeatureData.area = 0;
    currentFeatureData.length = 0;
  }

  // 调整视图以包含整个图元
  if (entity) {
    viewer.zoomTo(entity);
  }

  return entity;
};

/**
 * 销毁地图
 */
export const destroyMap = () => {
  if (viewer) {
    viewer.entities.removeAll();
    viewer.destroy();
    viewer = null;
    scene = null;
  }
};
