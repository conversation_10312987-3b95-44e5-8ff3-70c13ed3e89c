let disabledStartDate = 0;
import { fmtTime } from '@/utils/helper';
export default {
  data() {
    return {
      maxDay: 180
    };
  },
  computed: {
    pickerOptions() {
      const maxDay = this.maxDay;
      return {
        shortcuts: [
          {
            text: this.$t('One Day'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
              picker.$emit('pick', [
                fmtTime(start, 'yyyy-MM-dd 00:00:00'),
                fmtTime(end, 'yyyy-MM-dd 23:59:59')
              ]);
            }
          },
          {
            text: this.$t('Three Days'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
              picker.$emit('pick', [
                fmtTime(start, 'yyyy-MM-dd 00:00:00'),
                fmtTime(end, 'yyyy-MM-dd 23:59:59')
              ]);
            }
          },
          {
            text: this.$t('Seven Days'),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [
                fmtTime(start, 'yyyy-MM-dd 00:00:00'),
                fmtTime(end, 'yyyy-MM-dd 23:59:59')
              ]);
            }
          }
        ],
        disabledDate(time) {
          if (disabledStartDate > 0) {
            return (
              time.getTime() >
                Math.min(
                  Date.now(),
                  disabledStartDate + 7 * 24 * 3600 * 1000
                ) ||
              time.getTime() <
                Math.max(
                  disabledStartDate - 7 * 24 * 3600 * 1000,
                  Date.now() - maxDay * 24 * 3600 * 1000
                )
            );
          } else {
            return (
              time.getTime() >
                new Date(
                  new Date().getFullYear(),
                  new Date().getMonth(),
                  new Date().getDate() + 1
                ).getTime() -
                  1 || time.getTime() < Date.now() - maxDay * 24 * 3600 * 1000
            );
          }
        },
        onPick({ maxDate, minDate }) {
          if (maxDate && minDate) {
            disabledStartDate = 0;
          } else {
            disabledStartDate = minDate && new Date(minDate).getTime();
          }
        }
      };
    }
  },

  methods: {
    // 重置时间选择器
    handlePickerBlur() {
      disabledStartDate = 0;
    }
  }
};
