<template>
  <div
    @click="toggleClick"
    class="humburger-shadow px-[17px] cursor-pointer h-[60px] flex items-center justify-center"
  >
    <svg-icon
      iconClass="toggle"
      :class="{ 'is-active': isActive }"
      class="hamburger"
    />
  </div>
</template>

<script setup>
defineProps({
  isActive: {
    required: true,
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['toggleClick']);

function toggleClick() {
  emit('toggleClick');
}
</script>

<style lang="scss" scoped>
.hamburger {
  font-size: 20px;
  color: #98a2b3;
  &.is-active {
    transform: rotate(180deg);
  }
}
</style>
