/**
 * 菜单类型枚举，定义了不同类型的菜单项。
 */
const MenuTypeEnum = {
  CATALOG: 1, // 目录
  MENU: 2, // 菜单
  PAGE_TAG: 3, // 页面标签
  PAGE_BUTTON: 4, // 页面按钮

  STRUCTURE_MANAGER_LIST_PAGE: 8, // 分组管理列表页
  STRUCTURE_MANAGER_ADD_BUTTON: 9, // 分组管理添加按钮
  STRUCTURE_MANAGER_EDIT_BUTTON: 10, // 分组管理编辑按钮
  STRUCTURE_MANAGER_DELETE_BUTTON: 11, // 分组管理删除按钮

  USER_MANAGER_LIST_PAGE: 12, // 用户管理列表页
  USER_MANAGER_ADD_BUTTON: 13, // 用户管理添加按钮

  ROLE_MANAGER_LIST_PAGE: 18, // 角色管理列表页
  ROLE_MANAGER_ADD_BUTTON: 19, // 角色管理添加按钮
  ROLE_MANAGER_EDIT_BUTTON: 20, // 角色管理编辑按钮
  ROLE_MANAGER_DELETE_BUTTON: 21, // 角色管理删除按钮

  DEVICE_MANAGER_MENU: 32, // 设备管理菜单
  DEVICE_MANAGER_Add_BUTTON: 35, // 设备管理添加按钮
  DEVICE_MANAGER_EDIT_BUTTON: 36, // 设备管理编辑按钮
  DEVICE_MANAGER_DEVICE_TRANSFER_BUTTON: 38, // 设备管理设备转移按钮
  DEVICE_MANAGER_ADD_LABLE_BUTTON: 39, // 设备管理添加标签按钮
  DEVICE_MANAGER_DELETE_LABLE_BUTTON: 40, // 设备管理删除标签按钮
  DEVICE_MANAGER_DEBUG_BUTTON: 41, // 设备管理调试按钮
  DEVICE_MANAGER_CONFIG_BUTTON: 42, // 设备管理配置按钮
  DEVICE_MANAGER_UPGRADE_BUTTON: 43, // 设备管理升级按钮
  DEVICE_MANAGER_REBOOT_BUTTON: 44, // 设备管理重启按钮
  DEVICE_MANAGER_DELETE_BUTTON: 45, // 设备管理删除按钮
  DEVICE_MANAGER_CHECK_DEVICE_BUTTON: 46, // 设备管理查看设备按钮

  OPERATION_MANAGER_PKG_UPGRADE_LIST: 53, // 运维管理固件升级列表
  OPERATION_MANAGER_PKG_UPGRADE_ADD_BUTTON: 54, // 运维管理固件升级列表新增按钮
  OPERATION_MANAGER_PKG_UPGRADE_CHECK_BUTTON: 55, // 运维管理固件升级列表查看按钮
  OPERATION_MANAGER_PKG_UPGRADE_UNGRADE_BUTTON: 56, // 运维管理固件升级列表升级按钮
  OPERATION_MANAGER_PKG_UPGRADE_ENABLE_DISABLE_BUTTON: 57, // 运维管理固件升级列表启用/停用按钮
  OPERATION_MANAGER_PKG_UPGRADE_EDIT_BUTTON: 58, // 运维管理固件升级列表编辑按钮
  OPERATION_MANAGER_PKG_UPGRADE_DOWNLOAD_BUTTON: 59, // 运维管理固件升级列表下载按钮
  OPERATION_MANAGER_PKG_UPGRADE_DELETE_BUTTON: 60, // 运维管理固件升级列表删除按钮

  OPERATION_MANAGER_PROFILE_MANAGEMENT_LIST: 61, // 配置文件管理列表
  OPERATION_MANAGER_PROFILE_MANAGEMENT_ADD_BUTTON: 62, // 配置文件管理新增按钮
  OPERATION_MANAGER_PROFILE_MANAGEMENT_CHECK_BUTTON: 63, // 配置文件管理查看按钮
  OPERATION_MANAGER_PROFILE_MANAGEMENT_COPY_BUTTON: 64, // 配置文件管理复制按钮
  OPERATION_MANAGER_PROFILE_MANAGEMENT_ENABLE_DISABLE_BUTTON: 65, // 配置文件管理启用/停用按钮
  OPERATION_MANAGER_PROFILE_MANAGEMENT_EDIT_BUTTON: 66, // 配置文件管理编辑按钮
  OPERATION_MANAGER_PROFILE_MANAGEMENT_DELETE_BUTTON: 67, // 配置文件管理删除按钮

  OPERATION_MANAGER_PKG_UPGRADE_TASKS_PAGE: 68, // 运维管理固件升级任务页面
  OPERATION_MANAGER_PKG_UPGRADE_TASKS_ADD_BUTTON: 69, // 运维管理固件升级任务新增按钮
  OPERATION_MANAGER_PKG_UPGRADE_TASKS_CHECK_BUTTON: 70, // 运维管理固件升级任务查看按钮
  OPERATION_MANAGER_PKG_UPGRADE_TASKS_ABORT_BUTTON: 71, // 运维管理固件升级任务中止按钮

  OPERATION_MANAGER_REMOTE_CONFIG_TASK_LIST: 72, // 远程配置任务列表
  OPERATION_MANAGER_REMOTE_CONFIG_TASK_ADD_BUTTON: 73, // 远程配置任务新增按钮
  OPERATION_MANAGER_REMOTE_CONFIG_TASK_CHECK_BUTTON: 74, // 远程配置任务查看按钮

  USER_LOG_LIST_PAGE: 78, // 用户日志列表页
  USER_LOG_CHECK_LOG_BUTTON: 79, // 用户日志查看按钮

  DEVICE_LOG_LIST_PAGE: 80, // 设备日志列表页
  DEVICE_LOG_EXPORT_LOG_BUTTON: 81, // 设备日志导出按钮
  DEVICE_LOG_CHECK_LOG_BUTTON: 82, // 设备日志查看按钮

  ALARM_MANAGER_THRESHOLD_SETTING_LIST: 97, // 告警管理阈值配置列表
  ALARM_MANAGER_THRESHOLD_SETTING_BATCH_SETTINGS_BUTTON: 98, // 告警管理阈值配置批量设置按钮
  ALARM_MANAGER_THRESHOLD_SETTING_SETTINGS_BUTTON: 99, // 告警管理阈值配置设置按钮

  ALARM_MANAGER_NOTIFICATION_SETTING_LIST: 100, // 告警通知配置列表
  ALARM_MANAGER_NOTIFICATION_SETTING_BATCH_SETTING_BUTTON: 101, // 告警通知配置批量设置按钮
  ALARM_MANAGER_NOTIFICATION_SETTING_CONFIG_SETTING_BUTTON: 102, // 告警通知配置设置按钮
  ALARM_MANAGER_NOTIFICATION_SETTING_ALARM_SWITCH_BUTTON: 124, // 告警通知配置告警设置开关

  ALARM_MANAGER_NOTIFICATION_LOG_LIST: 103, // 告警管理通知日志列表
  ALARM_MANAGER_PROCESSING_STATISTICS_LOG_LIST: 104, // 告警管理处理统计日志列表
  ALARM_MANAGER_PROCESSING_STATISTICS_LOG_CHECK_BUTTOM: 105, // 告警管理处理统计日志查看按钮
  ALARM_MANAGER_ALARM_DATA_STATISTICS_LOG_LIST: 106, // 告警管理报警数据统计日志列表

  CARD_MANAGER_MENU: 107 // 卡片管理目录
};

export default MenuTypeEnum;
