
/**
 * 文件下载
*/
export function downLoadFile(fileName,url){
    const xhr = new window.XMLHttpRequest();
    xhr.open('GET', url, true);
    xhr.responseType = 'blob';
    xhr.send();
    xhr.onload = () => {
        if (xhr.status === 200) {
          const url = window.URL.createObjectURL(xhr.response);
          const a = document.createElement('a');
          a.href = url;
          a.download = `${fileName}`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          window.URL.revokeObjectURL(url);
    };
}
}
/**
 * 纬度校验 纬度：0～90，至多6位小数
*/
export function checkSiteLat(val){
    const latReg = /^(\+)?([0-8]?\d{1}\.\d{0,6}|90\.0{0,6}|[0-8]?\d{1}|90)$/
    if (latReg.test(val)) {
        // 合法纬度
        return true
      }
      return false
}

/**
 * 经度校验 经度：0～180，至多6位小数
*/
export function checkSiteLng(val){
    const lngReg = /^(\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,6})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180)$/
    if (lngReg.test(val)) {
        // 合法经度
        return true
      }
      return false
}