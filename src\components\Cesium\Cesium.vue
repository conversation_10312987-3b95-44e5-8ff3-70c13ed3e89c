<template>
  <div class="cesium-wrapper">
    <div class="cesium-Container" id="cesiumContainer-fly"></div>
  </div>
</template>
<script></script>
<script setup>
import { onMounted, onUnmounted, onBeforeUnmount } from 'vue';
import {
  getOrCreateCesiumEngineInstance,
  delCesiumEngineInstance
} from '@/components/Cesium/libs/cesium/index';
onMounted(() => {
  const engine = getOrCreateCesiumEngineInstance('fly');
  engine?.init('cesiumContainer-fly');
});
onBeforeUnmount(() => {
  delCesiumEngineInstance('fly');
});
onUnmounted(() => {});
</script>

<style lang="scss" scoped>
.cesium-wrapper {
  height: 100%;
  width: 100%;
}

.cesium-Container {
  height: 100%;
  width: 100%;
}
</style>
