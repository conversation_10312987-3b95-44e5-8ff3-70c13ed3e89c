<!--电池管理-->
<script>
export default {
  name: 'BatteryManage'
};
</script>

<script setup>
import { reactive } from 'vue';
import { getBatteryList } from '@/api/devices';
import moment from 'moment';

const loading = ref(false);
const total = ref(0);
const queryParams = reactive({
  page: 1,
  page_size: 10,
  sn: ''
});

const dataList = ref([]);

/**
 * 查询
 */
function handleQuery() {
  // Calculate the new total pages based on the new page size
  const newTotalPages = Math.ceil(total.value / queryParams.page_size);
  
  // If current page is greater than new total pages, adjust to the last page
  if (queryParams.page > newTotalPages) {
    queryParams.page = newTotalPages || 1;
  }
  getBatteryList(queryParams).then(data => {
    const { list, pagination } = data;
    dataList.value = list || [];
    total.value = pagination.total;
  });
}
function handleSearch() {
  queryParams.page = 1;
  handleQuery();
}
/**
 * 重置查询
 */
function resetQuery() {
  queryParams.sn = '';
  queryParams.page = 1;
  queryParams.page_size = 10;
  handleQuery();
}

onMounted(() => {
  handleQuery();
});
</script>

<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" @submit.native.prevent>
          <el-form-item label="" prop="sn">
            <el-input
              class="input-serach"
              v-model="queryParams.sn"
              placeholder="请输入电池序列号"
              clearable
              @keyup.enter="handleSearch"
              maxlength="32"
            />
          </el-form-item>
        </el-form>
      </div>
      <div class="search-btn">
        <el-button type="primary" @click="handleSearch()"><i-ep-search />搜索</el-button>
        <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
      </div>
    </div>
    <el-card shadow="never">
      <el-table highlight-current-row :data="dataList" v-loading="loading" stripe height="580">
        <el-table-column label="序号" align="center" width="80">
          <template #default="scope">
            <span>{{ scope.$index + 1 + queryParams.page_size * (queryParams.page - 1) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="电池序列号" prop="sn" show-overflow-tooltip> </el-table-column>
        <el-table-column label="组织名称" prop="dept_name" show-overflow-tooltip> </el-table-column>
        <el-table-column label="无人机名称" prop="nick_name" show-overflow-tooltip> </el-table-column>
        <el-table-column label="无人机序列号" width="200" prop="device_sn" show-overflow-tooltip> </el-table-column>
        <el-table-column label="循环次数" prop="loop_times" show-overflow-tooltip> </el-table-column>
        <el-table-column label="电压（mV）" prop="voltage" show-overflow-tooltip />
        <el-table-column label="温度（°C）" prop="temperature" show-overflow-tooltip />
        <el-table-column label="剩余电量" prop="capacity_percent" show-overflow-tooltip />
        <el-table-column label="高电压存储天数（日）" prop="high_voltage_storage_days" show-overflow-tooltip />
        <el-table-column label="最后上线时间" prop="update_time" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ moment(scope.row.update_time).format('YYYY-MM-DD HH:mm:ss') }}</span>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.page_size"
        @pagination="handleQuery"
      />
    </el-card>
  </div>
</template>
<style scoped lang="scss">
.input-serach {
  width: 200px;
}
.flex-center {
  display: flex;
  align-items: center;
  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
}
.search {
  display: flex;
  align-items: center;
  padding: 0 24px;
  .search-form {
    padding-top: 16px;
    flex: 1;
  }
}
</style>
