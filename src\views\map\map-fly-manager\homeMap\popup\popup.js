import { Cartesian3, SceneTransforms, JulianDate } from 'cesium';
import airportPopup from './airport_pop.vue';
import navPopup from './nav_pop.vue';
import airportNamePopup from './airportName_pop.vue';
import { createApp } from 'vue';
import { AnimationFrameManager } from '@/components/Cesium/libs/cesium/AnimationFrameManager';
import { distance2target, getCameraHeight } from '@/components/Cesium/libs/cesium/common';

const dockInfoMaps = new Map(); // 机场弹窗列表
const dockOffset = [-150, -280]; // 机场弹窗偏移

const dockNameMaps = new Map(); // 机场名称弹窗列表
const dockNameOffset = [-50, -60]; // 机机场名称弹窗偏移

const navfoMaps = new Map(); // 无人机弹窗列表
const navOffset = [-150, -200]; // 无人机弹窗偏移

// 获取机场弹窗Key
export function getDockKey(obj) {
  return obj.dock_sn;
}

// 获取机场名称弹窗Key
export function getDockNameKey(obj) {
  return 'label_' + obj.dock_sn;
}

// 获取无人机弹窗Key
export function getNavKey(obj) {
  return obj.device_sn;
}

/**
 * 1. 创建机场弹窗
 * @param {*} viewer 视图
 * @param {*} dockInfo 机场相关信息
 * @returns
 */
export const createDockInfoPopup = (viewer, dockInfo) => {
  if (!dockInfo && !viewer) {
    return;
  }

  const docn_sn = getDockKey(dockInfo);
  if (dockInfoMaps.has(docn_sn)) {
    const obj = dockInfoMaps.get(docn_sn).element;
    obj.remove();
    dockInfoMaps.delete(docn_sn);
    return;
  }
  const lon = Number(dockInfo.dock_xyz.x);
  const lat = Number(dockInfo.dock_xyz.y);
  const z = Number(dockInfo.dock_xyz.z);
  const vm = vueToComponentDom(airportPopup, { data: dockInfo });
  const popuDialogDom = vm.element;

  const div = insertDockInfoPopupDomToParent(popuDialogDom, dockInfo.dock_sn);
  dockInfoMaps.set(dockInfo.dock_sn, vm);
  const lineHeight = 100;
  const windowCoord = SceneTransforms.wgs84ToWindowCoordinates(
    viewer.scene,
    Cartesian3.fromDegrees(lon, lat, z + lineHeight)
  );
  div.style.left = windowCoord.x + dockOffset[0] + 'px';
  div.style.top = windowCoord.y + dockOffset[1] + 'px';
  div.style.position = 'absolute';

  // let options = {"dockInfo": dockInfo}
  // updateDockInfo(options);
};

// 1.----机场弹窗更新位置
function updateDockInfoPopup(viewer, key) {
  if (!dockInfoMaps.has(key)) {
    return;
  }
  const obj = dockInfoMaps.get(key).element;
  const dock_sn = obj.id;
  const entity = viewer.entities.getById('dock_' + dock_sn);
  if (!entity) {
    return;
  }
  const cartesian = entity.position._value;
  const div = document.getElementById(key);
  if (!div) {
    return;
  }

  // const distance = distance2target(viewer, cartesian);
  // const cameraHeight = getCameraHeight(viewer);
  // const scale = calculateScaleWithHeight(cameraHeight, distance, popupDialogConfig);
  // let dom = setDomScale(div, scale);
  let dom = null;
  if (!dom) {
    dom = div;
  }
  // popusDistance2ZindexArr.set(key, {
  //   id: key,
  //   distance,
  //   scale,
  //   dom
  // });
  const winPosi = transPosition(viewer, cartesian);
  if (winPosi !== undefined && winPosi !== null) {
    const res = enableShow(viewer, cartesian);
    if (res) {
      dom.style.display = '';
      dom.style.left = winPosi.x + dockOffset[0] + 'px';
      dom.style.top = winPosi.y + dockOffset[1] + 'px';
    } else {
      dom.style.display = 'none';
    }
  } else {
    dom.style.display = 'none';
  }
}

// 1.----机场内容更新
export function updateDockInfo(options) {
  const id = getDockKey(options.dockInfo);
  if (!id) {
    return;
  }
  if (dockInfoMaps.has(id)) {
    const vm = dockInfoMaps.get(id);
    if (vm) {
      const vueInstance = vm.instance;
      if (vueInstance) {
        vueInstance.setComponentData(options);
      }
    }
  }
}

/**
 * 2. 创建无人机弹窗
 * @param {*} viewer 视图
 * @param {*} navInfo 无人机相关信息
 * @param {*} byDockShow 是否通过机场点击关联机场弹窗
 * @returns
 */
export const createNavInfoPopup = (viewer, navInfo, byDockShow) => {
  if (!navInfo && !viewer) {
    return;
  }

  const device_sn = getNavKey(navInfo);
  if (navfoMaps.has(device_sn)) {
    if (!byDockShow) {
      const obj = navfoMaps.get(device_sn).element;
      obj.remove();
      navfoMaps.delete(device_sn);
      return;
    } else {
      return;
    }
  }
  if (navInfo.device_xyz === '' && byDockShow) {
    return '【' + navInfo.device_nickname + '】未上线';
  }
  const lon = Number(navInfo.device_xyz.x);
  const lat = Number(navInfo.device_xyz.y);
  const z = Number(navInfo.device_xyz.z);
  const vm = vueToComponentDom(navPopup, { data: navInfo });
  const popuDialogDom = vm.element;

  const div = insertDockInfoPopupDomToParent(popuDialogDom, navInfo.device_sn);
  navfoMaps.set(navInfo.device_sn, vm);
  const lineHeight = 100;
  const windowCoord = SceneTransforms.wgs84ToWindowCoordinates(
    viewer.scene,
    Cartesian3.fromDegrees(lon, lat, z + lineHeight)
  );
  div.style.left = windowCoord.x + navOffset[0] + 'px';
  div.style.top = windowCoord.y + navOffset[1] + 'px';
  div.style.position = 'absolute';
};

// 2.----无人机弹窗更新位置
function updateNavInfoPopup(viewer, key) {
  if (!navfoMaps.has(key)) {
    return;
  }
  const obj = navfoMaps.get(key).element;
  const device_sn = obj.id;
  const entity = viewer.entities.getById('nav_' + device_sn);
  if (!entity) {
    return;
  }
  const cartesian = entity.position._value;
  const div = document.getElementById(key);
  if (!div) {
    return;
  }

  // const distance = distance2target(viewer, cartesian);
  // const cameraHeight = getCameraHeight(viewer);
  // const scale = calculateScaleWithHeight(cameraHeight, distance, popupDialogConfig);
  // let dom = setDomScale(div, scale);
  let dom = null;
  if (!dom) {
    dom = div;
  }
  // popusDistance2ZindexArr.set(key, {
  //   id: key,
  //   distance,
  //   scale,
  //   dom
  // });
  const winPosi = transPosition(viewer, cartesian);
  if (winPosi !== undefined && winPosi !== null) {
    const res = enableShow(viewer, cartesian);
    if (res) {
      dom.style.display = '';
      dom.style.left = winPosi.x + navOffset[0] + 'px';
      dom.style.top = winPosi.y + navOffset[1] + 'px';
    } else {
      dom.style.display = 'none';
    }
  } else {
    dom.style.display = 'none';
  }
}

// 2.----无人机内容更新
export function updateNavInfo(options) {
  const id = getNavKey(options.navInfo);
  if (!id) {
    return;
  }
  if (navfoMaps.has(id)) {
    const vm = navfoMaps.get(id);
    if (vm) {
      const vueInstance = vm.instance;
      if (vueInstance) {
        vueInstance.setComponentData(options);
      }
    }
  }
}

/**
 * 3. 创建机场弹窗
 * @param {*} viewer 视图
 * @param {*} dockInfo 机场名称相关信息
 * @returns
 */
export const createDockNamePopup = (viewer, dockInfo) => {
  if (!dockInfo && !viewer) {
    return;
  }
  const label_docn_sn = getDockNameKey(dockInfo);
  if (dockNameMaps.has(label_docn_sn)) {
    return;
  }
  const lon = Number(dockInfo.dock_xyz.x);
  const lat = Number(dockInfo.dock_xyz.y);
  const z = Number(dockInfo.dock_xyz.z);
  const vm = vueToComponentDom(airportNamePopup, { data: dockInfo });
  const popuDialogDom = vm.element;

  const div = insertDockInfoPopupDomToParent(popuDialogDom, label_docn_sn);
  dockNameMaps.set(label_docn_sn, vm);
  const lineHeight = 100;
  const windowCoord = SceneTransforms.wgs84ToWindowCoordinates(
    viewer.scene,
    Cartesian3.fromDegrees(lon, lat, z + lineHeight)
  );
  const charCount = dockInfo.nickname.length;
  if (charCount > 5) {
    dockNameOffset[0] = charCount * -9;
  }
  if (dockInfo.nickname.length > 10) {
    dockNameOffset[0] = -100;
  }

  div.style.left = windowCoord.x + dockNameOffset[0] + 'px';
  div.style.top = windowCoord.y + dockNameOffset[1] + 'px';
  div.style.position = 'absolute';
  updateDockNameInfo(dockInfo);
};

// 3.----机场名称弹窗更新位置
function updateDockNamePopup(viewer, key) {
  if (!dockNameMaps.has(key)) {
    return;
  }
  if (!viewer) {
    return;
  }
  const obj = dockNameMaps.get(key).element;
  const dock_sn = obj.id.split('_')[1];
  const entity = viewer.entities.getById('dock_' + dock_sn);
  if (!entity) {
    return;
  }
  const cartesian = entity.position._value;
  const div = document.getElementById(key);
  if (!div) {
    if (obj) {
      insertDockInfoPopupDomToParent(obj, obj.id);
    }
    return;
  }

  let dom = null;
  if (!dom) {
    dom = div;
  }

  const winPosi = transPosition(viewer, cartesian);
  if (winPosi !== undefined && winPosi !== null) {
    const res = enableShow(viewer, cartesian);
    if (res) {
      dom.style.display = '';
      dom.style.left = winPosi.x + dockNameOffset[0] + 'px';
      dom.style.top = winPosi.y + dockNameOffset[1] + 'px';
    } else {
      dom.style.display = 'none';
    }
  } else {
    dom.style.display = 'none';
  }
}

// 3.----机场名称内容更新
export function updateDockNameInfo(dockInfo) {
  const id = getDockNameKey(dockInfo);
  if (!id) {
    return;
  }
  if (dockNameMaps.has(id)) {
    const vm = dockNameMaps.get(id);
    if (vm) {
      const vueInstance = vm.instance;
      if (vueInstance) {
        vueInstance.setComponentData(dockInfo);
      }
    }
  }
}

// 将机场dom插入到外层容器的content下
export function insertDockInfoPopupDomToParent(div, id) {
  // const bodyDom = document.getElementsByClassName('homeMainMap')[0];
  const bodyDom = document.getElementsByClassName('cesium-viewer')[0];
  bodyDom.insertBefore(div, bodyDom.lastChild);
  div.id = id;
  return div;
}

// 执行批量更新dom位置
export function updatePoi(viewer) {
  const ani = new AnimationFrameManager();
  ani.start(() => {
    updateDomPopupPoi(viewer);
  });
}

export function updateDomPopupPoi(viewer) {
  dockNameMaps.forEach((value, key) => {
    updateDockNamePopup(viewer, key);
  });
  dockInfoMaps.forEach((value, key) => {
    updateDockInfoPopup(viewer, key);
  });
  navfoMaps.forEach((value, key) => {
    updateNavInfoPopup(viewer, key);
  });
  // 执行根据当下 相机与目标位置点距离 设置层级关系
  // setZIndexByDis();
}

/**
 *
 * @param {*} cameraHeight
 * @param {*} viewer
 * @param {*} options  【 {cameraMinHeight,cameraMaxHeight,minDistance,maxDistance,minScale,maxScale},】
 * @returns
 */
function calculateScaleWithHeight(cameraHeight, distance, options) {
  const foundItem = options.find(item => cameraHeight < item.cameraMaxHeight && cameraHeight > item.cameraMinHeight);
  if (!foundItem) {
    return 0;
  }
  const { minDistance, maxDistance, minScale, maxScale } = foundItem;
  // 将距离限制在最小值和最大值之间
  distance = Math.max(minDistance, Math.min(maxDistance, distance));
  // 计算距离在最小值和最大值之间的比例
  const t = 1 - (distance - minDistance) / (maxDistance - minDistance);
  // 根据比例进行线性插值计算缩放比例
  const scale = minScale + t * (maxScale - minScale);
  return scale;
}

function enableShow(viewer, cartesian) {
  let res = false;
  const e = cartesian;
  const f = viewer.scene.camera.position;
  let g = viewer.scene.globe.ellipsoid.cartesianToCartographic(f).height;
  if (!((g += 1 * viewer.scene.globe.ellipsoid.maximumRadius), Cartesian3.distance(f, e) > (g * 4) / 5)) {
    res = true;
  }
  return res;
}

//三维笛卡尔坐标转屏幕坐标
function transPosition(viewer, position) {
  return SceneTransforms.wgs84ToWindowCoordinates(viewer.scene, position);
}

/**
 * 创建dom
 * @param {*} dom vue组件
 * @param {*} data
 * @returns element, instance
 */
export function vueToComponentDom(vueModel, data) {
  const element = document.createElement('div');
  const app = createApp(vueModel, { props: data });
  const instance = app.mount(element);
  return { element, instance };
}
