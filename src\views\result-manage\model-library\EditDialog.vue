<template>
  <el-dialog
    :title="title"
    :model-value="visible"
    v-if="visible"
    width="600px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form ref="dataFormRef" :model="modelRecord" label-width="110px" :rules="userRule" v-loading="loading">
      <div class="dataDialg-box">
        <el-form-item prop="model_name" label="模型名称：">
          <el-input maxlength="50" v-model="modelRecord.model_name" placeholder="请输入模型名称" clearable />
        </el-form-item>

        <el-row v-if="isShow">
          <el-col :span="12">
            <el-form-item prop="lng" label="经度：">
              <el-input maxlength="15" v-model="modelRecord.lng" placeholder="请输入经度" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="lat" label="纬度：">
              <el-input maxlength="15" v-model="modelRecord.lat" placeholder="请输入纬度" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" v-loading="loading">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';

import { updateModel } from '@/api/live';

const dataFormRef = ref('dataFormRef');
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  obj: {
    type: Object
  }
});

const modelRecord = reactive({
  file_id: '',
  model_name: '',
  lng: '',
  lat: ''
});
const userRule = reactive({
  model_name: [{ required: true, message: '模型名称不能为空', trigger: 'blur' }],
  lng: [
    { required: true, message: '经度不能为空', trigger: 'blur' },
    { validator: validateLng, message: '经度格式不正确，需小数6位', trigger: 'blur' }
  ],
  lat: [
    { required: true, message: '纬度不能为空', trigger: 'blur' },
    { validator: validateLat, message: '纬度格式不正确，需小数6位', trigger: 'blur' }
  ]
});

function validateLng(rule, value, callback) {
  const lngRegex = /^[\-\+]?(0?\d{1,2}\.\d{6}|1[0-7]?\d{1}\.\d{6}|180\.0{6})$/;
  if (!lngRegex.test(value)) {
    callback(new Error('经度格式不正确，需小数6位'));
  } else {
    callback();
  }
}

function validateLat(rule, value, callback) {
  const latRegex = /^[\-\+]?([0-8]?\d{1}\.\d{6}|90\.0{6})$/;
  if (!latRegex.test(value)) {
    callback(new Error('纬度格式不正确，需小数6位'));
  } else {
    callback();
  }
}

const isShow = ref(false);
onMounted(() => {
  // console.log(props.obj);
  modelRecord.file_id = props.obj.file_id;
  modelRecord.model_name = props.obj.model_name;
  console.log(' props.obj.', props.obj.model_type);
  if (props.obj.model_type === '2D' || props.obj.model_type === 'QJ') {
    isShow.value = true;
    modelRecord.lng = props.obj.lng;
    modelRecord.lat = props.obj.lat;
  }
});

const emit = defineEmits(['update:visible', 'submit']);
const loading = ref(false);
// 关闭弹窗
function closeDialog() {
  emit('update:visible', false);
}
//保存
function handleSubmit() {
  dataFormRef.value.validate(isValid => {
    if (isValid) {
      loading.value = true;
      updateModel(modelRecord)
        .then(res => {
          console.log('🚀 ~ handleSubmit ~ res:', res);
          loading.value = false;
          ElMessage.success('更新成功');
          closeDialog();
          emit('submit');
        })

        .catch(e => {
          loading.value = false;
        });
    } else {
      loading.value = false;
    }
  });
}
</script>
<style scoped lang="scss">
.upload-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 300px;
}

:deep(.el-upload--picture-card) {
  height: 100%;
  width: 100%;
}
:deep(.el-upload__text) {
  font-size: 13px;
  margin-bottom: 13px;
  margin-top: -5px;
}

:deep(.el-upload-dragger) {
  width: 565px;
  background-color: #f9f9f9;
}
:deep(.el-upload-dragger:hover) {
  // width: 565px;
  background-color: white;
}
.el-icon-upload {
  color: #b7d9fd;
  font-size: 70px;
  margin-top: -34px;
}
.tipB {
  font-size: 13px;
  color: #cccccc;
  margin-top: 10px;
}

:deep(.el-progress .el-progress-bar__outer) {
  height: 3px !important;
  // padding-left: 13px;
}

:deep(.lucky .el-upload-dragger) {
  padding: 0;
  height: 214px;
}

:deep(.lucky .el-icon-upload) {
  margin-top: 2px;
}
</style>
