// 航面转为航线辅助方法

import { latLong2Longlat, toNumber } from '@/components/Cesium/libs/cesium';
import { createMarginPolygon } from '../index.js';
import { toRaw } from 'vue';
import { parmsInfoRect, dataInfoRect } from './generateWayLineHandle';
//#region  用于地图上绘制航面的相关参数及初始胡方法
export const getConfigFromPlanData = planData => {
  let airline_json = JSON.parse(planData.airline_json);
  airline_json.config = {};
  parseTemplateWayLineInfo(airline_json.template, airline_json.wayLines);
  airline_json.config = { parmsInfo: toRaw(parmsInfoRect), dataInfo: toRaw(dataInfoRect) };
  return airline_json;
};

/**
 * 根据传入的data 转化 template 、 wayline
 * @param {*} templateData
 * @param {*} waylineData
 * @returns
 */
export const parseTemplateWayLineInfo = (templateData = null, waylineData = null) => {
  try {
    if (!templateData) {
      return;
    }
    dataInfoRect.done = false; // 是否完成绘制
    dataInfoRect.rotate = templateData.Folder[0].Placemark[0].wpml_direction || 0;
    dataInfoRect.margin = templateData.Folder[0].Placemark[0].wpml_margin || 0;
    dataInfoRect.height = waylineData.Folder[0].Placemark[0].wpml_executeHeight || 100; // 执行高度
    parmsInfoRect.shootType = templateData.Folder[0].Placemark[0].wpml_shootType || 0;
    parmsInfoRect.orthoCameraOverlapW = templateData.Folder[0].Placemark[0].wpml_orthoCameraOverlapH || 80;
    parmsInfoRect.orthoCameraOverlapH = templateData.Folder[0].Placemark[0].wpml_orthoCameraOverlapW || 80;
    parmsInfoRect.cameraType = templateData.Folder[0].wpml_payloadParam.wpml_imageFormat.split(',') || [];
    parmsInfoRect.elevationOptimization = (templateData.Folder[0].Placemark[0].wpml_elevationOptimizeEnable || 1) === 1;
    parmsInfoRect.autoFlightSpeed = waylineData.Folder[0].Placemark[0].wpml_waypointSpeed || 15;

    // 提取坐标组
    const positionsObject = extractPositions(templateData, waylineData);
    dataInfoRect.originPolygon = positionsObject.originPolygon;
    dataInfoRect.polygon = positionsObject.polygon;
    dataInfoRect.positions = positionsObject.positions; // 空
    dataInfoRect.aircraftRoutePositions = positionsObject.aircraftRoutePositions;
    dataInfoRect.airPlaceMarkPosition = positionsObject.takeOffRefPoint.map(Number);
    parmsInfoRect.airPortPlace = dataInfoRect.airPlaceMarkPosition;
  } catch (error) {
    console.log('error:', error);
  }
};

/**
 * 提取坐标构建面
 * @param {*} templateData
 * @param {*} waylineData
 * @returns
 */
export const extractPositions = (templateData = null, waylineData = null) => {
  if (!templateData || !waylineData) {
    return;
  }
  let originPolygon = [],
    polygon = [],
    positions = [],
    aircraftRoutePositions = [],
    takeOffRefPoint = [];
  // 提取起飞点 wpml_takeOffRefPoint
  let wpml_takeOffRefPoint = templateData.wpml_missionConfig.wpml_takeOffRefPoint ?? '';
  console.log('templateData.wpml_missionConfig', templateData.wpml_missionConfig);
  takeOffRefPoint = wpml_takeOffRefPoint.split(',').map(Number); // 转为数字数组
  takeOffRefPoint = latLong2Longlat(takeOffRefPoint) ?? [];
  console.log('takeOffRefPoint', takeOffRefPoint);
  //#region 提取坐标面 中的坐标点
  // 字符串
  // let coordinatesString = templateData.Folder[0].Placemark[0].Polygon.outerBoundaryIs.LinearRing.coordinates;
  // const coordinatesArray = coordinatesString.trim().split(' ');
  let coordinatesString = templateData.Folder[0].Placemark[0].Polygon.outerBoundaryIs.LinearRing.coordinates;
  let latLngArray = parseCoordinates(coordinatesString);
  console.log('coordinatesString', coordinatesString);
  console.log('latLngArray', latLngArray);
  polygon = latLngArray ?? [];
  if (polygon.length === 0) {
    throw new Error('提取面状航线坐标失败');
  }
  //#endregion

  //#region
  // 计算原始面
  if (dataInfoRect.margin > 0) {
    // 原始面和航线所在的面不是一个面
    polygon.forEach(position => {
      let obj = {
        lng: position.lng || 0,
        lat: position.lat || 0,
        height: dataInfoRect.height || 100
      };
      originPolygon.push(obj);
    });
    polygon = createMarginPolygon(polygon, dataInfoRect.margin);
    if (!polygon) {
      throw new Error('面状航线生成失败');
    }
  } else {
    polygon.forEach(position => {
      let obj = {
        lng: position.lng || 0,
        lat: position.lat || 0,
        height: dataInfoRect.height || 100
      };
      originPolygon.push(obj);
    });
  }
  //#endregion

  return {
    originPolygon: originPolygon, // 是原始的面
    polygon: polygon, // polygon 是航线的面 有margin
    positions: positions,
    aircraftRoutePositions: aircraftRoutePositions,
    takeOffRefPoint: takeOffRefPoint.map(Number) // 可能存在字符串数组 "24.567764,118.027401,27.151893"
  };
};

/**
 * coordinatesString 经纬度坐标字符串
 * @param {*} coordinatesString
 * @returns
 */
function parseCoordinates(coordinatesString = '') {
  if (coordinatesString === '') {
    return [];
  }
  // let coordinatesString1 =
  //   '118.01990420000001,24.56792939999999,0,118.01986820000002,24.565993299999995,0,118.0216332,24.566012799999992,0,118.02212507249929,24.567811932445874,0';
  // let coordinatesString2 =
  //   '118.01990420000001,24.56792939999999,0, 118.01986820000002,24.565993299999995,0,  118.0216332,24.566012799999992,0, 118.02212507249929,24.567811932445874,0';
  // let coordinatesString3 =
  //   '118.0199041875933,24.567929421132902,0 118.01986129119838,24.56600032953446,0 118.02163318753398,24.566012822308018,0 118.02164451167441,24.567994517062246,0';
  // let ss1 = parseCoordinatesString(coordinatesString1);
  // let ss2 = parseCoordinatesString(coordinatesString2);
  // let ss3 = parseCoordinatesString(coordinatesString3);
  // console.log('ss1', ss1);
  // console.log('ss2', ss2);
  // console.log('ss3', ss3);
  // coordinatesString = coordinatesString.replace(/\s/g, '');
  // console.log('coordinatesString', coordinatesString);
  // // 将字符串按逗号分隔成数组
  // const coordinatesArray = coordinatesString.trim().split(',');
  // // 去除坐标对之间的空格，保留逗号
  const coordinatesArray = coordinatesString.replace(/(\d+)\s+(\d+)/g, '$1,$2').split(',');
  // 使用一个对象来存储唯一的点，键为点的坐标（经度和纬度）
  console.log('coordinatesArray', coordinatesArray);
  const uniquePoints = {};
  for (let i = 0; i < coordinatesArray.length; i += 3) {
    const longitude = parseFloat(coordinatesArray[i]);
    const latitude = parseFloat(coordinatesArray[i + 1]);
    const pointKey = `${longitude},${latitude}`;
    // 如果这个点还没有被添加到uniquePoints中，则添加它
    if (!uniquePoints[pointKey]) {
      uniquePoints[pointKey] = { lng: toNumber(longitude, 7), lat: toNumber(latitude, 7) };
    }
  }
  return Object.values(uniquePoints);
}

function parseCoordinatesString(str) {
  // 去除坐标对之间的空格，保留逗号
  const coordinatesArray = str.replace(/(\d+)\s+(\d+)/g, '$1,$2').split(',');
  const result = [];
  for (let i = 0; i < coordinatesArray.length; i += 3) {
    if (isNaN(coordinatesArray[i]) || isNaN(coordinatesArray[i + 1])) {
      console.error('坐标数据格式错误');
      continue;
    }
    result.push({
      longitude: parseFloat(coordinatesArray[i]),
      latitude: parseFloat(coordinatesArray[i + 1])
    });
  }
  return result;
}
