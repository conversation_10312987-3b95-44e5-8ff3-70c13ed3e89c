import _ from 'lodash';
import CryptoJS from 'crypto-js';
import { sm2 } from 'sm-crypto';
import { validatenull } from './helper';

/**
 * 生成指定长度的随机字符串。
 *
 * @param {number} strLength - 要生成的随机字符串的长度，默认为 16。
 * @return {string} - 生成的随机字符串。
 * <AUTHOR>
 */
export function generateRandomStr(strLength = 16) {
  let code = '';
  const chars = 'abcdefghijkmnpqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ23456789';
  const charsArr = chars.split();

  for (let i = 0; i < strLength; i++) {
    const num = Math.floor(Math.random() * charsArr.length);
    code += charsArr[num];
  }

  return code;
}

/**
 * 使用AES加密请求参数。
 *
 * @param {string} key - 加密密钥。
 * @param {string} content - 要加密的内容。
 * @return {string} - 加密后的字符串。
 * <AUTHOR>
 */
export function encryptAESData(key, content) {
  let _key = CryptoJS.enc.Utf8.parse(key);
  const encryptedContent = CryptoJS.AES.encrypt(content, _key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  });
  const encStr = encryptedContent.ciphertext.toString();

  return encStr;
}

/**
 * 使用SM2算法加密RES密钥。
 *
 * @param {string} key - 待加密的密钥。
 * @return {string} - 加密后的密钥。
 * <AUTHOR>
 */
export function encryptRESKey(key) {
  const publicKey = import.meta.env.VITE_APP_PUBLICKEY;
  const encryptor = sm2.doEncrypt(key, publicKey, 1);

  return encryptor;
}

/**
 * 使用AES加密请求数据。
 *
 * @param {Object} config - 包含请求配置的对象。
 * <AUTHOR>
 */
export function encryptRequestData(config) {
  const AesKey = generateRandomStr();
  const encryptAesKey = encryptRESKey(AesKey);
  config.headers['Secret'] = encryptAesKey;

  if ('params' in config) {
    encryptParamsWithAES(config, AesKey);
  }

  if (config.data) {
    encryptDataWithAES(config, AesKey);
  }
}

/**
 * 使用AES加密请求参数。
 *
 * @param {Object} config - 包含请求参数的配置对象。
 * @param {string} AesKey - AES加密密钥。
 * <AUTHOR>
 */
function encryptParamsWithAES(config, AesKey) {
  const content = {};

  for (const key in config.params) {
    content[key] = !validatenull(config.params[key])
      ? encryptAESData(AesKey, config.params[key] + '')
      : '';
  }

  config.params = content;
}

/**
 * 使用AES加密请求数据。
 *
 * @param {Object} config - 包含请求数据的配置对象。
 * @param {string} AesKey - AES加密密钥。
 * <AUTHOR>
 */
function encryptDataWithAES(config, AesKey) {
  const content = _.isObject(config.data)
    ? JSON.stringify(config.data)
    : config.data;
  const encryptContent = encryptAESData(AesKey, content);
  config.data = encryptContent;
}
