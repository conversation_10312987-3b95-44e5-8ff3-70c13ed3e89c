// 航面转为航线辅助方法
import * as Cesium from 'cesium';
import { HEIGHT_MODE_ENUM } from '@/views/plan/newplan/kmz/props';
import { parmsInfoRect } from '../../hocks/index';
import { ellipsoidToEgm96, egm96ToEllipsoid, c3toDegress } from '@/components/Cesium/libs/cesium/index';
//#region 计算高度
export const caculateHeights = () => {
  try {
    // 通过计算当下的飞机位置获取经纬度
    let egm96Height = 0;
    let egm96Obj = null;
    let ellipsoidAirHeight = 0;
    let result = null;
    //   椭球高和Egm96高度转换;
    const [longitude, latitude, height] = parmsInfoRect.airPortPlace;
    ellipsoidAirHeight = height;
    // 如果是旋转绝对高 那么这里就是EGM96 的方式计算  需要计算三个数 椭球高、EGM96高、相对地形高 最后这里展示在地求上的是绝对高即椭球高
    if (parmsInfoRect.heightMode === HEIGHT_MODE_ENUM.EGM96) {
      // 飞机最高点
      egm96Obj = ellipsoidToEgm96(longitude, latitude, parmsInfoRect.flightHight);
      egm96Height = egm96Obj.egm96Height;
      ellipsoidAirHeight =
        parmsInfoRect.flightHight || egm96ToEllipsoid(longitude, latitude, egm96Height)?.ellipsoidHeight || 0;
      // 获取当前相对于地形的高度
      parmsInfoRect.height = egm96Height;
      parmsInfoRect.ellipsoidHeight = ellipsoidAirHeight;
      parmsInfoRect.globalShootHeight = ellipsoidAirHeight - parmsInfoRect.airPortPlaceASL;
      parmsInfoRect.realFlightHight = parmsInfoRect.flightHight;
      result = {
        globalShootHeight: parmsInfoRect.globalShootHeight, // 拍摄高度 对应的是 globalShootHeight
        height: egm96Height, // egm96 高度对应的是 hight
        ellipsoidHeight: ellipsoidAirHeight // 椭球高度 对应的是 ellipsoidHeight
      };
    } else if (parmsInfoRect.heightMode === HEIGHT_MODE_ENUM.relativeToStartPoint) {
      // 飞机最高点
      egm96Obj = ellipsoidToEgm96(longitude, latitude, parmsInfoRect.flightHight);
      egm96Height = egm96Obj.egm96Height;
      // 获取当前相对于地形的高度
      //  局航线高度（椭球高）
      // * 注：如果 wpml:height 选用相对起飞点高度，则 wpml:ellipsoidHeight 和 wpml:height 相同；如果 wpml:height
      // 选用 EGM96 海拔高度或 AGL 相对地面高度，则 wpml:wpml:ellipsoidHeight 由 wpml:height 做相应转换得到。
      parmsInfoRect.height = egm96Height;
      parmsInfoRect.ellipsoidHeight = egm96Height;
      parmsInfoRect.globalShootHeight = parmsInfoRect.flightHight - parmsInfoRect.airPortPlaceASL;
      parmsInfoRect.realFlightHight = parmsInfoRect.flightHight + parmsInfoRect.airPortPlaceASL;
      result = {
        globalShootHeight: parmsInfoRect.globalShootHeight, // 拍摄高度 对应的是 globalShootHeight
        height: egm96Height, // egm96 高度对应的是 hight
        ellipsoidHeight: egm96Height // 椭球高度 对应的是 ellipsoidHeight
      };
    } else if (parmsInfoRect.heightMode === HEIGHT_MODE_ENUM.aboveGroundLevel) {
    }
    return result;
  } catch (error) {
    console.log(error);
  }
};

//#endregion
