module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true,
    jest: true
  },
  parser: 'vue-eslint-parser',
  //  https://eslint.vuejs.org/user-guide/#bundle-configurations

  extends: [
    'plugin:vue/vue3-recommended',
    './.eslintrc-auto-import.json',
    'prettier',
    'plugin:prettier/recommended'
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  plugins: ['vue'],
  rules: {
    'vue/multi-word-component-names': 'off',
    'vue/no-v-model-argument': 'off'
  },
  // https://eslint.org/docs/latest/use/configure/language-options#specifying-globals
  globals: {
    DialogOption: 'readonly',
    OptionType: 'readonly'
  }
};
