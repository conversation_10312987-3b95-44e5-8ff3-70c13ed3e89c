// 停止操作相关 停止操作无具体的XML数据格式
// kml
import { STATUE_TYPE } from '../../props';
import { Action, ActionGroup } from '../../waylines';
import { generateKey } from '@/utils';
import { getMaxActionGroupIndex } from '../modules/waylineshandle';
import { ACTION_ACTUATOR_FUNC, ACTION_TRIGGER_TYPE, OPERATION_TYPE } from '@/utils/constants';
//#region 等时拍照
/**
 * 创建悬停动作
 * @param {Object} options 动作配置项，应包含actionId、actionActuatorFunc
 * @param {Object|null} placemark 航点对象
 * @returns {Action|null} 返回Action实例或在配置不正确时返回null
 */
export function createStopMultipleTakePhotoActionGroup(options = null) {
  if (!options) {
    return null;
  }
  try {
    // 创建动作
    let { actionTrigger, action } = getStopMultipleTakePhotoActionDefaultParam();
    const placemarkIndex = options?.placemark.wpml_index || 0;
    let maxActionGroupId = getMaxActionGroupIndex();
    if (!maxActionGroupId) {
      maxActionGroupId = 0;
    }
    // 构建一个
    const ac = new Action({
      actionId: options.actionId || 0,
      actionActuatorFunc: action?.wpml_actionActuatorFunc || ACTION_ACTUATOR_FUNC.stopIntervalTakePhoto,
      actionActuatorFuncParam: action?.wpml_actionActuatorFuncParam,
      uuid: options.actionUuid || generateKey(), // 动作id
      type: STATUE_TYPE.UNNORMAL,
      trigger: ACTION_TRIGGER_TYPE.stopMultiple
    });
    const ag = new ActionGroup({
      placemarkIndex: placemarkIndex,
      actionGroupId: maxActionGroupId,
      actionGroupStartIndex: placemarkIndex,
      actionGroupEndIndex: placemarkIndex,
      actionGroupMode: options?.actionGroupMode || 'sequence',
      actionTriggerType: actionTrigger?.wpml_actionTriggerType || 'reachPoint',
      actionTriggerParam: actionTrigger?.wpml_actionTriggerParam || null,
      actions: [],
      uuid: generateKey(),
      type: OPERATION_TYPE.stopInterval
    });
    ag && ag.addAction(ac);
    return {
      actionGroup: ag || null,
      action: ac || null
    };
  } catch (error) {
    console.error('创建 Action 实例失败:', error);
    return null;
  }
}

// 动作组和动作默认参数设置
export function getStopMultipleTakePhotoActionDefaultParam() {
  return {
    actionTrigger: {
      wpml_actionTriggerType: null,
      wpml_actionTriggerParam: null
    },
    action: {
      wpml_actionActuatorFunc: ACTION_ACTUATOR_FUNC.stopIntervalTakePhoto,
      wpml_actionActuatorFuncParam: {}
    }
  };
}

//#endregion
