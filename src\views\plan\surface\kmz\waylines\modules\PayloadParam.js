class PayloadParam {
  constructor(options = {}) {
    this.wpml_payloadPositionIndex = options.payloadPositionIndex;
    this.wpml_imageFormat = options.imageFormat || 'visable';
  }

  setPayloadPositionIndex(value) {
    this.wpml_payloadPositionIndex = value;
  }
  getPayloadPositionIndex() {
    return this.wpml_payloadPositionIndex;
  }

  setImageFormat(value) {
    this.wpml_imageFormat = value;
  }
  getImageFormat() {
    return this.wpml_imageFormat;
  }
}
export { PayloadParam };
