<script>
export default { name: 'LoadManagement' };
</script>

<script setup>
import { ref, reactive, toRaw } from 'vue';
import { loadManagementList } from '@/api/devices';

const queryParams = reactive({
  payloadName: '',
  payloadType: '',
  pageNum: 1,
  pageSize: 10
});
const dataList = ref([]);
const total = ref(0);
const deviceSn = ref('')
const emit = defineEmits(['onClick','select']);
const loading = ref(false);

/**
 * 查询
 */
 function handleQuery() {
	loading.value = true
	loadManagementList({
    payload_name: queryParams.payloadName,
    payload_type: queryParams.payloadType,
    page: queryParams.pageNum,
    page_size: queryParams.pageSize
  }).then(data => {
    const { list, pagination } = data;
    dataList.value = list || [];
    total.value = pagination.total;
		setTimeout(()=>{
			loading.value = false
		},500)
  });
}

onMounted(() => {
  handleQuery();
});
</script>

<template>
	<div class="alarm-ul" v-if="dataList.length > 0" v-loading="loading">
		<div class="alarm-item" v-for="(item,index) in dataList" :key="index">
			<div class="drone-name">
				<div class="load-name ellipsis" style="max-width: 180px;" :title="item.payload_name">{{ item.payload_name }}</div>
				<div class="load-name ellipsis" style="max-width: 180px;">{{ item.payload_type_name }}</div>
			</div>
			<div class="drone-name">
				<div class="line-name ellipsis" style="max-width: 180px;">{{ item.model }}</div>
				<div class="line-name ellipsis" style="max-width: 100px;" :title="item.brand">{{ item.brand }}</div>
			</div>
		</div>
	</div>
	<el-empty description="暂无数据" v-else>
		<template #image>
			<img src="../../../assets/empty_home.png" width="120px">
		</template>
	</el-empty>
</template>

<style lang="scss" scoped>
:deep(.el-tabs__header) {
	margin-bottom: 0;
}

.right-icon{
	margin-top: 5px;
	display: flex;
}
.open-status {
	display: inline-block;
	text-align: center;
	width: 32px;
	height: 24px;
	background: rgba(42,139,125,0.30);
	border-radius: 2px;
	font-family: SourceHanSansSC-Regular;
	font-size: 12px;
	color: #39BFA4;
	line-height: 24px;
	font-weight: 400;
}
.stop-status {
	display: inline-block;
	text-align: center;
	width: 32px;
	height: 24px;
	background: rgba(145, 124, 40,0.30);
	border-radius: 2px;
	font-family: SourceHanSansSC-Regular;
	font-size: 12px;
	color: rgb(145, 124, 40);
	line-height: 24px;
	font-weight: 400;
}
.off-status {
	display: inline-block;
	text-align: center;
	width: 32px;
	height: 24px;
	background: rgba(152, 162, 179,0.2);
	border-radius: 2px;
	font-family: SourceHanSansSC-Regular;
	font-size: 12px;
	color: rgb(152, 162, 179);
	line-height: 24px;
	font-weight: 400;
}
.grey {
	display: inline-block;
	background: rgba($color: #98A2B3 , $alpha: 0.2);
	border-radius: 2px;
	text-align: center;
	line-height: 24px;
	height: 24px;
	font-weight: 400;
	padding: 0 4px;
	font-family: SourceHanSansSC-Regular;
	font-size: 12px;
	color: #98A2B3;
	text-align: center;
}

.pointer {
	cursor: pointer;
}
.currentColor {
	background: #175091 !important;
}
:deep(.el-tabs__header) {
	border-bottom: 1px solid #344054;
}
:deep(.el-tabs__nav-wrap) {
	background: #11253E;
	color: #fff;
	height: 38px;
	line-height: 38px;
	font-family: SourceHanSansSC-Bold;
	font-size: 14px;
	text-align: left;
	font-weight: 700;
	border-bottom: 1px solid #344054;
	padding-left: 8px;
}
::-webkit-scrollbar {
  width: 8px;  /* 设置滚动条的宽度 */
}
::-webkit-scrollbar-thumb {
  background-color: rgba(46,144,255,0.5);
	border-radius: 2px; /* 设置滚动条滑块的背景色 */
}
.alarm-title {
	height: 38px;
	line-height: 38px;
	background: #11253E;
	color: #fff;
	font-family: SourceHanSansSC-Bold;
	font-size: 14px;
	text-align: left;
	font-weight: 700;
	border-bottom: 1px solid #344054;
	padding-left: 8px;
}
.alarm-ul {
	background: #001129;
	padding-bottom: 20px;
	margin: 8px;
	// height: 386px;
	height: 750px;
	overflow: auto;
	.alarm-item {
		min-height: 76px;
		background: #11253E;
		margin-bottom: 12px;
		padding: 8px;
		font-family: SourceHanSansSC-Regular;
		font-size: 14px;
		color: #F5F6F8;
		text-align: left;
		line-height: 22px;
		font-weight: 400;
		.list {
			height: 38px;
			line-height: 38px;
			vertical-align: middle
		}
		.load-name {
			font-family: SourceHanSansSC-Regular;
			font-size: 14px;
			color: #F5F6F8;
			text-align: left;
			line-height: 20px;
			font-weight: 400;
		}
		.drone-name {
			margin-top: 8px;
			display: flex;
			justify-content: space-between;
		}
		.alarm-time {
			font-family: SourceHanSansSC-Regular;
			font-size: 12px;
			color: #98A2B3;
			text-align: right;
			line-height: 20px;
			font-weight: 400;
		}
		.alarm-address{
			font-family: SourceHanSansSC-Regular;
			font-size: 14px;
			color: #FFFFFF;
			text-align: justify;
			line-height: 22px;
			font-weight: 400;
			margin-top: 8px;
			overflow: hidden;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			text-overflow: ellipsis;
			white-space: normal;
		}
		.task-name {
			font-family: SourceHanSansSC-Regular;
			font-size: 14px;
			color: #FFFFFF;
			text-align: justify;
			line-height: 22px;
			font-weight: 400;
			margin-bottom: 8px;
		}
		.line-name {
			font-family: SourceHanSansSC-Regular;
			font-size: 12px;
			color: #98A2B3;
			line-height: 18px;
			font-weight: 400;
		}
	}
}
.flex {
	height: 38px;
	line-height: 38px;
	display: flex;
	justify-content: space-between;
}
</style>
