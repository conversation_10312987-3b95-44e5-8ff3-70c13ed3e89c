import request from '@/utils/request';
import { useUserStoreHook } from '@/store/modules/user';

// 获取比对照片
// Query：
// 参数名称	是否必须	示例	备注
// begin_time	否
// end_time	否
// lng	否
// lat	否
export function getPictures(queryParams) {
  const userStore = useUserStoreHook();
  const { userData } = userStore;
  const { workspace_id } = userData;
  return request({
    url: `/media/api/v1/files/${workspace_id}/files-simple`,
    method: 'GET',
    params: queryParams
  });
}
