<!--宇视-->
<template>
  <div style="position: relative;">
    <div :id="playerId" v-if="deviceYsNo.length!=0"> </div>
    <div  :style="{width:width+'px',height:height+'px'}" class="empty" v-else></div>
    <div :class="['title-name',videoYsName.length==0?'title-name-left':''].join(' ')" v-if="showTitle">{{ videoYsName || '无视频' }}</div>
    <div class="close-icon" v-if="showClose && deviceYsNo.length!=0" @click="closeVideo"><i-ep-close /></div>
    <svg-icon
      icon-class="full_screen"
      class="fullScreen"
      v-if="deviceYsNo.length!=0 && height!=0"
      @click="wholeFullScreen()"
    />
    <el-dropdown
        class="quality"
        placement="bottom"
        size="small"
        popper-class="quality-popper"
        @command="commandQuality"
        trigger="hover"
        v-if="deviceYsNo.length!=0 && height!=0"
      >
        <div class="quality-title">
          {{ qualityTitle }}
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item :command="0">自动</el-dropdown-item>
            <el-dropdown-item :command="1">流畅</el-dropdown-item>
            <el-dropdown-item :command="2">标准</el-dropdown-item>
            <el-dropdown-item :command="3">高清</el-dropdown-item>
            <el-dropdown-item :command="4">超清</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
  </div>
</template>
<script>
export default {
  name: 'YsPlayer'
};
</script>

<script setup>
import { 
  ref,
  defineProps,
  onUnmounted,
  onMounted,
  defineExpose,
  nextTick,
 } from 'vue';
import { Base64, getKey } from './common';

import { getYSConfig ,setLivestreamQuality,startLivestream} from '@/api/live'
import { ElMessage } from 'element-plus';
// import JSEncrypt from 'jsencrypt';
import axios from 'axios'

const props = defineProps({
  width: {//宽
    type: Number,
    default: 400
  },
  height: {//高
    type: Number,
    default: 400
  },
  deviceNo:{//设备编码
    type: String,
    default: ''
  },
  playerId:{
    type: String,
    default: ''
  },
  showTitle:{//是否显示视频名称
    type: Boolean,
    default: false
  },
  videoName:{//视频名称
    type: String,
    default: ''
  },
  showClose:{//是否显示关闭按钮
    type: Boolean,
    default: false
  },
  from:{//来源：videoWall 视频墙
    type: String,
    default: ''
  },
  deviceData:{
    type: Object,
    default: () => {
      return {}
    }
  }
});
defineExpose({
  onStop,
  onStart
})
const deviceYsNo = ref('')
const videoYsName = ref('')
let _iframeId = new Map();
let timer = null;
const forgeUrl =
  import.meta.env.VITE_APP_NODE_ENV == 'development'
    ? '../../../public/yushi/forge.min.js'
    : '../../../yushi/forge.min.js';
const qualityTitle = ref('高清');

onMounted(() => {
    let script = document.createElement('script');
    script.src = forgeUrl;
    document.body.appendChild(script);
    script.onload = () => {
      init()
    };
});
onUnmounted(()=>{
  onStop()
})
function init(){
  const {playLive} = useYsPlayer()  
  const {deviceNo,playerId,width,from,videoName} = props
  deviceYsNo.value = deviceNo
  videoYsName.value = videoName
  if(deviceNo.length == 0){return}
  if(width == 0){return}
  if(from == 'videoWall'){return}
  // nextTick(()=>{
  //   playLive(deviceNo,playerId)
  // })
  getLiveStream()
}
function closeVideo(){
  if(props.from == 'videoWall'){
    window.$bus.emit('liveStreamClose', { ...props.deviceData });
  }
  onStop()
}
//停止播放
function onStop () {
  const {stopPlay} = useYsPlayer()
  const {deviceNo} = props
  stopPlay(deviceNo)
}
//开始播放
function onStart () {
  const {playLive} = useYsPlayer()
  const {deviceNo,playerId} = props
  // nextTick(()=>{
  //   playLive(deviceNo,playerId)
  // })
  getLiveStream()
}
//获取推流
function getLiveStream(){
  let droneParaObj = props.deviceData;
  startLivestream({
    url: '',
    video_id: `${droneParaObj?.droneSelected}/${droneParaObj?.cameraSelected}/normal-0`,
    url_type: droneParaObj.url_type || 1,
    video_quality: droneParaObj?.claritySelected
  })
    .then(res => {
      const {playLive} = useYsPlayer()
      const {deviceNo,playerId} = props
       nextTick(()=>{
        playLive(deviceNo,playerId)
      })  
    })
    .catch(err => {
      console.error(err);
    });
}
//全屏
function wholeFullScreen() { 
  if(window.imosPlayer){
    window.imosPlayer?.fullScreen(_iframeId.get(props.deviceNo),{
      status: true
    })
  }
}
//切换清晰度
function commandQuality(val) {
  switch (val) {
    case 0:
      qualityTitle.value = '自动';
      break;
    case 1:
      qualityTitle.value = '流畅';
      break;
    case 2:
      qualityTitle.value = '标准';
      break;
    case 3:
      qualityTitle.value = '高清';
      break;
    case 4:
      qualityTitle.value = '超清';
      break;
    default:
      console.warn('未知的清晰度值:', val);
      return;
  }
  let droneParaObj = props.deviceData;
  if (droneParaObj.droneSelected != null && droneParaObj.cameraSelected != null) {
    // 构建视频ID
    const videoId = `${droneParaObj?.droneSelected}/${droneParaObj?.cameraSelected}/${
      droneParaObj?.videoSelected || 'normal' + '-0'
    }`;
    // 调用接口设置清晰度
    setLivestreamQuality({
      video_id: videoId,
      video_quality: val
    })
      .then(res => {
        // 处理响应结果
        console.log('清晰度切换成功:', res);
      })
      .catch(err => {
        console.error('清晰度切换失败:', err);
      });
  }
}
function  useYsPlayer() {
  let ipaddr = ''; // VM服务器网络地址
  let VIIDPort = ''; // VIID所在端口

  let username = ''; // 用户名
  let password = ''; // 密码

  // 本地网络配置
  let liveNetProtocol = 'tcp'; // 实况
  let replayNetProtocol = 'tcp'; // 回放

  let liveByMS = true; // 实况是否过MS
  let replayByMS = true; // 回放是否过MS

  let linkPort = location.protocol.includes('https') ? '443' : '8093'; // 播放器通信端口

  
  /** 国产PC计算坐标参数 */
  // let windowParams = undefined;
  /** 外层css缩放比例 */
  let sca = 1;
  let topTitle = '';

  let nbsp = String.fromCharCode(160);
  let arr = [];
  arr.length = 40;
  let str = arr.fill(nbsp).join('');

  let token = '';

  let maxIframe = 10;

  let reConnect = 0;
  
  let isLogin = false;
  let waitTask = [];
  /** iframe偏移量 */
  let offset = [0, 0];

  // const crypt = new JSEncrypt({
  //   default_key_size: 2048,
  // });
  // crypt.setPrivateKey(getKey());
  // 保活
  const keepalive = () => {
    axios.post(`http://${ipaddr}:${VIIDPort}/VIID/hadesadapter/user/keepalive`,{}, {
      headers: {
        'Content-Type': 'application/json; charset=utf8',
        Authorization: token,
      },
      responseType: 'json',
      method: 'POST',
    }).then((res) => {
      // VM B3351以后的用法
      if (res.ErrCode !== 0) {
        reConnect++;
        if (reConnect > 5) {
          clearInterval(timer);
          reConnect = 0;
          console.warn('user dead');
        }
      } else {
        reConnect = 0;
      }
    });
  };
  // 校验身份信息
  const identityCheck = (data) => {
    return new Promise((resolve, reject) => {
      axios.post(`http://${ipaddr}:${VIIDPort}/VIID/login/v2`,data ?  data  : {}, {
        headers: {
          'Content-Type': 'application/json; charset=utf8',
        },
        responseType: 'json',
        method: 'POST',
        // ...(data ? { data } : {}),
      }).then((res) => {
        resolve(res);
      });
    });
  };
  // 登录
  const login = (callback) => {
    identityCheck().then((res) => {
      const { AccessCode } = res.data;

      var md1 = forge.md.md5.create(); //md5
      var md2 = forge.md.md5.create(); //md5

      md1.update(password); //md5

      var passwordMd5 = md1.digest().toHex(); //md5
      let LoginSignature = Base64.encode(username) + AccessCode + passwordMd5;
      md2.update(LoginSignature); //md5
      LoginSignature = md2.digest().toHex(); //md5
      identityCheck({
        UserName: username,
        AccessCode,
        LoginSignature,
      }).then((res) => {
        token = res.data?.AccessToken;
        timer = setInterval(keepalive, 30000);
        loadImosPlayerJS(callback);
      });
    });
  };

  // ***必须动态引入播放器库文件，以免第三方运维成本上升***
  const loadImosPlayerJS = (callback) => {
    let script = document.createElement('script');
    script.src = `http://${ipaddr}${linkPort && ':' + linkPort}/static/imosPlayer.min.js`;
    document.body.appendChild(script);
    script.onload = () => {
      initPlayer(callback);
    };
    // // 如果动态引入不存在则用本地的
    // script.onerror = (e) => {
    //   console.log(e);
    //   initPlayer(callback);
    // };
  };

  // ***动态引入加密文件 /public/yushi/forge.min.js***
  const loadForgeJS = (callback) => {
    let script = document.createElement('script');
    script.src = forgeUrl;
    document.body.appendChild(script);
    script.onload = () => {
      // initPlayer(callback);
    };
  };

  /**
   * 随机码
   */
  const getRandom = () => {
    function S4() {
      return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    }
    return S4() + S4() + '-' + S4() + '-' + S4() + '-' + S4() + '-' + S4() + S4() + S4();
  };

  // 初始化
  const initPlayer = (callback) => {
    // 跨nat映射时，如果对接端口不是一一映射到8093，则本接口必调。
    // 或https代理的对接端口不是443，本接口必调
    // 本接口必须在init之前调用
    window.imosPlayer.setLinkPort(linkPort);
    window.imosPlayer
      .init({
        ip: ipaddr, // 必传
        token, // 必传
        title: topTitle || document.title, // 必传
        offset: offset,
      })
      .then((res) => {
        if (res.ErrCode === 0) {
          // 悬浮方案要求网页title唯一，参照接口文档【悬浮播放器概念】
          document.title = document.title + str + getRandom();

          window.imosPlayer.cssScale(sca);

          window.imosPlayer.setLiveNetLinkMode(liveNetProtocol, liveByMS);
          window.imosPlayer.setReplayNetLinkMode(replayNetProtocol, replayByMS);

          // 国产电脑坐标计算
          // window.imosPlayer.setWindowParams(windowParams);
          //   window.__login = true;
          callback?.();
        } else {
          alert(res.ErrMsg);
        }
      })
      .catch((err) => {
        console.error(err);
      });
  };

  /**
   * 创建窗格
   */
  const createWindow = async (resCode, playerId, callback) => {
    if (_iframeId.size >= maxIframe) {
      ElMessage.error('播放器数量已达上限');
      return;
    }

    let videoDom = await window.imosPlayer.createPanelWindow();
    const className = 'window_class' + resCode;
    // 窗格默认400*400，通过样式改宽高
    videoDom.style.width = `${props.width}px`;
    videoDom.style.height = `${props.height}px`;

    window.imosPlayer
      .setVoidClassName(videoDom, {
        className,
      })
      .then((e) => {
        console.log(e);
      });

    _iframeId.set(resCode, videoDom.id);
    let divDom = document.getElementById(playerId);
    divDom.style.width = `${props.width}px`;
    divDom.style.height = `${props.height}px`;
    divDom.appendChild(videoDom);

    let array = divDom.querySelector('.imosPlayer');
    window.imosPlayer.setViewDomByClassName({
      className,
      doms: [array],
    });
    setTimeout(() => {
      callback?.();
    }, 500);
  };

  /**
   * 检查是否初始化成功
   */
  const checkInit = () => window.imosPlayer?.checkInit?.();
  // 获取登录信息
  const getLoginInfo = async () => {
    isLogin = true;
    let data = await getYSConfig({})
    //test
    // let data = {
    //   host:'*************',
    //   port:'8088',
    //   username:'loadmin',
    //   password:'BjstAI_2024'
    // }
    
    isLogin = false;
    // if (code === '00') {
      const { username:userName, password: pwd, host, port } = data;
      // username = crypt.decrypt(userName);
      // password = crypt.decrypt(pwd);
      username = userName;
      password = pwd;
      ipaddr = host;
      VIIDPort = port;
    // } else {
    //   ElMessage.error(msg);
    //   throw new Error(msg);
    // }
  };

  //#region 实况
  /**
   * 播放实况
   */
  const playLive = async function (resCode, playerId) {
    const play = () => {
      window.imosPlayer
        .playLive(_iframeId.get(resCode), {
          camera: resCode,
          title: resCode,
          stream: 1,
        })
        .then((e) => {
          console.log('playLive', e);
        });
        //标题栏和工具栏一直显示
      window.imosPlayer.setVideoBarStatus (_iframeId.get(resCode), { status:1 })
      //移除抓拍按钮
      window.imosPlayer.delPanelBtn(_iframeId.get(resCode), {
        buttonID: 'capture',
        position: 'bottom',
      });
      // 移除连续抓拍按钮
      window.imosPlayer.delPanelBtn(_iframeId.get(resCode), {
        buttonID: 'continuousCapture',
        position: 'bottom',
      });
      // 移除数字变焦按钮
      window.imosPlayer.delPanelBtn(_iframeId.get(resCode), {
        buttonID: 'digitalZoom',
        position: 'bottom',
      });
      // 移除录像按钮
      window.imosPlayer.delPanelBtn(_iframeId.get(resCode), {
        buttonID: 'recordView',
        position: 'bottom',
      });
      // 移除音频控制按钮
      window.imosPlayer.delPanelBtn(_iframeId.get(resCode), {
        buttonID: 'volume',
        position: 'bottom',
      });
      // 移除自适应按钮
      window.imosPlayer.delPanelBtn(_iframeId.get(resCode), {
        buttonID: 'fit',
        position: 'title',
      });
      // 移除关闭按钮
      window.imosPlayer.delPanelBtn(_iframeId.get(resCode), {
        buttonID: 'close',
        position: 'title',
      });
      // 移除全屏按钮
      window.imosPlayer.delPanelBtn(_iframeId.get(resCode), {
        buttonID: 'zoom',
        position: 'title',
      });
    };

    if (isLogin) {
      waitTask.push({ resCode, playerId });
      return;
    }
    
    if (!checkInit()) {
      if (!username && !password) {
        await getLoginInfo();
      }
      login(() => {
        createWindow(resCode, playerId, play);
        if (waitTask.length > 0) {
          const task = waitTask.shift();
          playLive(task.resCode, task.playerId);
        }
      });
    } else {
      createWindow(resCode, playerId, play);
    }
  };
  //#endregion

  /**
   * 停止播放
   */
  const stopPlay = function (resCode) {
    const iframeId = _iframeId.get(resCode);
    iframeId &&
      window.imosPlayer.playStop(iframeId).then((e) => {
        console.log("yushi stop play!")
        _iframeId.delete(resCode);
        deviceYsNo.value = ''
        videoYsName.value = ''
      });
    if(timer){
      console.log("stop yushi keepAlive!")
      // clearInterval(timer);
    }
  };

  return {
    playLive,
    stopPlay
  }
}
</script>
<style lang="scss">
  .quality-popper {
    border: none;
    .el-dropdown-menu {
      background-color: #161616;
      opacity: 0.7;
      border-color: #161616;
    }
    .el-dropdown-menu--small .el-dropdown-menu__item {
      color: #fff;
    }
    .el-dropdown-menu__item {
      color: #fff;
    }
    .el-dropdown-menu__item:not(.is-disabled):focus {
      background-color: #161616;
      opacity: 0.3;
    }
  }
  .el-dropdown__popper.el-popper {
    border: none;
  }
</style>
<style lang="scss" scoped>
  :deep(.imosPlayer) {
    border-width: 0 !important;
  }
  .empty {
    position: relative;
    border: 1px solid #101010;
    background: url('../../assets/empty.png') no-repeat center center;
    z-index: 999;
    background-color: #262c33;
  }
  .title-name {
    position: absolute;
    color: white;
    font-size: 14px;
    top: 2px;
    right: 44px;
    z-index: 999;
  }

  .title-name-left {
    position: absolute;
    color: white;
    font-size: 14px;
    top: 10px;
    left: 10px;
    z-index: 999;
  }
  .close-icon {
    background-color: #262c33;
    position: absolute;
    color: white;
    font-size: 14px;
    top: 4px;
    right: 15px;
    cursor: pointer;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;
  }
  .fullScreen {
    position: absolute;
    bottom: 4px;
    right: 15px;
    z-index: 999;
    color:#fff;
    cursor: pointer;
  }
  .quality {
  position: absolute;
  bottom: 5px;
  right: 44px;
  z-index: 999;
  cursor: pointer;
  .quality-title {
    color: #fff;
    border: none;
    &.active {
      border: none;
    }
    &.hover {
      border: none;
    }
  }
}
</style>

