// 动作相关
import { ACTION_ACTUATOR_FUNC } from '@/utils/constants';
import { wayLineHandleGetPlan } from './waylineshandle';
import CreateFrustum from '@/components/Cesium/libs/cesium/frustum';
import { useEditTrackerStore } from '@/store/modules/dataEditTracker.js';
const editTrackerStore = useEditTrackerStore();
export const actionFrustumMap = new Map();
// 用于保存 frustum的集合 这里一般有两个 frustum 对象 一个是用于相机视角的一个是用于动作对应视角的
export const frustumMap = new Map();
let planObject = null;
export const globalZoom = 5;
export const globalFrustumOption = {
  fov: 26, //视场角。角度越大，视野越大
  heading: 0, // 默认向北（左右朝向）
  pitch: 0, // 绕着Y轴旋转 这个基本不动
  roll: -90 // 默认向北（上下朝向-俯仰角跳转）
};
//#region 视锥体相关参数和方法let

/**
 * 视锥体开始时会有一个默认的配置，随着动态的配置调整，
 * 下面的 actionFrustumObject.frustumOptions 将会是当下的实时的 视锥体配置，
 * 该配置会影响到下一个动作以及下一个点
 */
export const actionFrustumObject = {
  frustumObject: null,
  frustumOptions: {
    viewer: null,
    eyeViewer: null,
    fov: 26, //视场角。角度越大，视野越大
    near: 0.1, //近截面距离
    far: 200, //远截面距离
    heading: 0, // 默认向北（左右朝向）
    pitch: 0, // 绕着Y轴旋转 这个基本不动
    roll: -90, // 默认向北（上下朝向-俯仰角跳转）
    width: 60, // 宽
    height: 45, // 长
    position: [], //视锥体原点（相机位置）
    colorType: 'camera' // 默认是第一种颜色方案
  },
  actionUuid: null,
  action: null
};

//#region 创建相机视角视锥体方法

//#endregion
export const createFrustum = (options = null, type = 'camera', action = null) => {
  if (!options) {
    return null;
  }
  const newOptions = {
    viewer: options.viewer || actionFrustumObject.frustumOptions.viewer, //视场角。角度越大，视野越大
    eyeViewer: options.eyeViewer || actionFrustumObject.frustumOptions.eyeViewer, //视场角。角度越大，视野越大
    fov: options.fov || actionFrustumObject.frustumOptions.fov, //视场角。角度越大，视野越大
    near: options.near || actionFrustumObject.frustumOptions.near, //近截面距离
    far: options.far || actionFrustumObject.frustumOptions.far, //远截面距离
    position: options.position, //视锥体原点（相机位置）[X,Y,Z]
    width: options.width || actionFrustumObject.frustumOptions.width,
    height: options.height || actionFrustumObject.frustumOptions.height,
    heading: options.heading !== null ? options.heading : actionFrustumObject.frustumOptions.heading,
    pitch: 0,
    roll: options.roll !== null ? options.roll : actionFrustumObject.frustumOptions.roll,
    colorType: options.colorType || 'camera',
    zoom: globalZoom || 5
  };
  let frustum = new CreateFrustum(newOptions);
  frustumMap.set(type, frustum);
  setGlobalFrustumOptions(action, frustum, newOptions);
  return frustum;
};

//#region 方法
/**
 * 设置视锥体
 * @param {*} action
 * @param {*} frustum
 * @param {*} options
 */
export const setGlobalFrustumOptions = (action = null, frustum, options = null) => {
  // 设置当前的视锥体相关信息
  actionFrustumObject.frustumObject = frustum || actionFrustumObject.frustumObject;
  actionFrustumObject.frustumOptions.viewer = options.viewer || actionFrustumObject.frustumOptions.viewer;
  actionFrustumObject.frustumOptions.eyeViewer = options.eyeViewer || actionFrustumObject.frustumOptions.eyeViewer; //视场角。角度越大，视野越大
  actionFrustumObject.frustumOptions.fov = options.fov || actionFrustumObject.frustumOptions.fov;
  actionFrustumObject.frustumOptions.near = options.near || actionFrustumObject.frustumOptions.near;
  actionFrustumObject.frustumOptions.far = options.far || actionFrustumObject.frustumOptions.far;
  actionFrustumObject.frustumOptions.position = options.position || actionFrustumObject.frustumOptions.position;
  // actionFrustumObject.frustumOptions.hpr = options.hpr || actionFrustumObject.frustumOptions.hpr;
  actionFrustumObject.frustumOptions.pitch = options.pitch || actionFrustumObject.frustumOptions.pitch;
  actionFrustumObject.frustumOptions.roll = options.roll || actionFrustumObject.frustumOptions.roll;
  actionFrustumObject.frustumOptions.heading = options.heading || actionFrustumObject.frustumOptions.heading;
  actionFrustumObject.frustumOptions.width = options.width || actionFrustumObject.frustumOptions.width;
  actionFrustumObject.frustumOptions.height = options.height || actionFrustumObject.frustumOptions.height;
  actionFrustumObject.actionUuid = action?.uuid ?? actionFrustumObject.actionUuid;
  actionFrustumObject.action = action ?? actionFrustumObject.action;
  // 保存动作对应的视锥体参数信息
  actionFrustumMap.set(actionFrustumObject.actionUuid, {
    frustumObject: frustum,
    frustumOptions: actionFrustumObject.frustumOptions,
    actionUuid: action?.uuid ?? actionFrustumObject.actionUuid,
    action: action ?? actionFrustumObject.action
  });
};

/**
 * 通过动作的uuid获取对应的视锥体
 * @param {Action} actionUuid 动作对象唯一索引
 * @returns
 */
export const getFrustumObjectByActionUuid = actionUuid => {
  if (!actionUuid) {
    return null;
  }
  const obj = actionFrustumMap.get(actionUuid);
  if (obj) {
    const { frustumObject, frustumOptions, actionUuid, action } = obj;
    return {
      frustumObject,
      frustumOptions,
      actionUuid,
      action
    };
  }
  return {
    frustumObject: null,
    frustumOptions: null,
    actionUuid: null,
    action: null
  };
};

export const updateAllFrustum = (options = null) => {
  if (!options) {
    return null;
  }
  // 检查 fov 的值是否在 0-180 之间，如果大于等于 180 或者小于等于 0 则提示
  const { fov } = options;
  if (fov !== undefined && (fov >= 180 || fov <= 0)) {
    console.warn('FOV must be between 0 and 180 degrees.');
    options.fov = 0; // 移除无效的 fov
  }
  for (let [key, f] of frustumMap) {
    f?.setVisible(true);
    f?.update(options);
  }
  const { position, heading, pitch, roll } = options;
  if (!planObject) {
    planObject = wayLineHandleGetPlan();
  }
  planObject?.updateEyeViewer(position, heading, pitch, roll);
};

// 只更新动作

export const updateActionFrustum = (options = null) => {
  // 判断 frustumMap 是否是一个 map 类型
  let ac = null;
  if (frustumMap instanceof Map) {
    ac = frustumMap.get(options?.actionUuid) ?? null;
  } else {
    ac = frustumMap.getFrustum('action') ?? null;
  }
  if (!options || !ac) {
    return null;
  }
  ac?.setVisible(true);
  ac?.update(options);
  const { position, heading, pitch, roll } = options;
  if (!planObject) {
    planObject = wayLineHandleGetPlan();
  }
  planObject?.updateEyeViewer(position, heading, pitch, roll);
  editTrackerStore.dataTracker.markAsModified();
};

export const updateFrustum = (actionFrustumObj = null, options = null, action = null) => {
  if (!actionFrustumObj || !options) {
    return null;
  }
  const newOptions = {
    viewer: options.viewer || actionFrustumObject.frustumOptions.viewer,
    fov: options.fov || options.fov, //视场角。角度越大，视野越大
    near: options.near || options.near, //近截面距离
    far: options.far || options.far, //远截面距离
    width: options.width || options.width, //截面宽
    height: options.height || options.height, //截面搞
    position: options.position || options.position, //视锥体原点 位置
    heading: options.heading || options.heading,
    pitch: options.pitch || options.pitch,
    roll: options.roll || options.roll
  };
  actionFrustumObj?.update(newOptions);
  // 设置当前的视锥体相关信息
  setGlobalFrustumOptions(action, actionFrustumObj, newOptions);
  editTrackerStore.dataTracker.markAsModified();
};
/**
 * 根据动作组件面板调整更新视锥体参数
 * @param {*} options
 *  type: 'GimbalYawRotate', 动作类型
    min: dataRef.min,最小值
    max: dataRef.max,最大值
    value: v // 当前值
 */
export const updateFrustumWithActionValue = options => {
  // 获取传值数据
  const { type, value = 0, action = null, actionUuid = null } = options;
  if (!action || !actionUuid) {
    return;
  }
  // 创建或者获取当前动作视锥体
  let actionFrustum = frustumMap.get('action');
  if (!actionFrustum) {
    actionFrustumObject.frustumOptions.colorType = 'action';
    actionFrustum = createFrustum(actionFrustumObject.frustumOptions, 'action');
  }
  let currentActionFrustunOptions = actionFrustum?.getOptions() ?? {};
  actionFrustumObject.frustumOptions = currentActionFrustunOptions;
  // 根据type类似 和 min max value 更新视锥体参数
  let originHeading = currentActionFrustunOptions.heading;
  let originRoll = currentActionFrustunOptions.roll;

  switch (type) {
    // 计算云台偏航角
    case ACTION_ACTUATOR_FUNC.gimbalRotate:
      currentActionFrustunOptions.heading = originHeading;
      currentActionFrustunOptions.roll = value - 90; //; // 这里减去90 是为了让角度和司空上面效果一致
      break;
    // 计算飞行器偏航角
    case ACTION_ACTUATOR_FUNC.rotateYaw:
      // 这里主要计算云台偏航角 heading值
      currentActionFrustunOptions.roll = originRoll;
      currentActionFrustunOptions.heading = value;
      break;
    // 缩放
    case ACTION_ACTUATOR_FUNC.zoom:
      currentActionFrustunOptions.fov = value;
      // 120 = 5 X 24
      const { sensorSize = 22.5, focalLength = 120, fovDeg = 0, zoom } = options;
      const opt = {
        sensorSize,
        focalLength,
        fovDeg,
        zoom,
        position: currentActionFrustunOptions.position
      };
      currentActionFrustunOptions.zoom = zoom;
      // 这里更新鹰眼图的角度值 传入的是 for 和 模型高度值
      updateEyeViewerFov(opt);
      break;
    default:
      break;
  }

  updateFrustum(actionFrustum, currentActionFrustunOptions, action);
  // 联动更新相机的视锥体
  let cameraFrustum = frustumMap.get('camera');
  if (cameraFrustum) {
    updateFrustum(cameraFrustum, currentActionFrustunOptions, action);
    planObject = wayLineHandleGetPlan();
    const { position, heading, pitch, roll, zoom } = currentActionFrustunOptions;
    planObject.updateEyeViewer(position, heading, pitch, roll, zoom);
  }
  editTrackerStore.dataTracker.markAsModified();
};

/**
 * 更新鹰眼图的缩放级
 * @param {*} options
 * @returns
 */
export const updateEyeViewerFov = (options = null) => {
  if (!options) {
    return;
  }
  editTrackerStore.dataTracker.markAsModified();
  // 根据倍率计算大小
  window.$bus.emit('updateRectDom', {
    zoom: options?.zoom || 5
  });
};

export const clearFrustum = () => {
  actionFrustumObject.frustumObject?.clear();
  const actionFrustum = frustumMap.get('action');
  if (actionFrustum) {
    actionFrustum.clear();
    frustumMap.delete('action');
  }
};

export const getFrustum = key => {
  if (!key) {
    return null;
  }
  return frustumMap.get(key) || null;
};

/**
 * 默认 M3TD
 * @param {*} sensorSize 传感器尺寸
 * @param {*} focalLength 焦距
 * @returns fov 对应的角度
 */
export const calculateFOV = (focalLength, sensorSize = 22.5) => {
  if (focalLength === 0) {
    focalLength = 48;
  }
  const fov = 2 * Math.atan(sensorSize / (2 * focalLength)); // 弧度
  // 弧度转角度
  let deg = (180 / Math.PI) * fov;
  return deg;
};

//#endregion

//#endregion

/**
 * 通过动作对象获取动作名称和参数配置
 * @param {Action} action
 * @returns
 */
export const getActionFrustumOptions = (action = null) => {
  if (!action) {
    return null;
  }
  let actionActuatorFunc = action.wpml_actionActuatorFunc ?? null;
  if (!actionActuatorFunc) {
    return null;
  }
  let actionActuatorFuncParam = action.wpml_actionActuatorFuncParam ?? null;
  switch (actionActuatorFunc) {
    case ACTION_ACTUATOR_FUNC.rotateYaw:
      return {
        heading: actionActuatorFuncParam.wpml_aircraftHeading
      };
    case ACTION_ACTUATOR_FUNC.zoom:
      return {
        // heading: actionActuatorFuncParam.wpml_focalLength * 24
        zoom: actionActuatorFuncParam.wpml_focalLength / 24
      };
    case ACTION_ACTUATOR_FUNC.gimbalRotate:
      return {
        roll: actionActuatorFuncParam.wpml_gimbalPitchRotateAngle - 90
      };
    default:
      return null;
  }
};
