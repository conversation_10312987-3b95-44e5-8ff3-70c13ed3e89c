// 全景拍照相关
// kml panoShot 拍照默认结构如下
// <wpml:action>
// <wpml:actionId>0</wpml:actionId>
// <wpml:actionActuatorFunc>takePhoto</wpml:actionActuatorFunc>
// <wpml:actionActuatorFuncParam>
//   <wpml:payloadPositionIndex>0</wpml:payloadPositionIndex>
//   <wpml:useGlobalPayloadLensIndex>0</wpml:useGlobalPayloadLensIndex>
//   <wpml:payloadLensIndex>wide,zoom,ir</wpml:payloadLensIndex>
//   <wpml:panoShotSubMode>panoShot_360</wpml:panoShotSubMode>
// </wpml:actionActuatorFuncParam>
// </wpml:action>
import { PAYLOAD_LENS_INDEX } from '../../props';
import { Action } from '../../waylines';
import { generateKey } from '@/utils';
import { ACTION_ACTUATOR_FUNC, ACTION_TRIGGER_TYPE } from '@/utils/constants';
//#region 拍照动作

/**
 * 创建拍照动作
 * @param {Object} options 动作配置项，应包含actionId、actionActuatorFunc
 * @param {Object|null} actionActuatorFuncParamOptions 动作执行器参数配置，可选
 * @returns {Action|null} 返回Action实例或在配置不正确时返回null
 */
export function createPanoShotAction(options, actionActuatorFuncParamOptions = null) {
  try {
    // 创建动画组
    if (!options) {
      return null;
    }
    return new Action({
      actionId: options.actionId || 0,
      actionActuatorFunc: ACTION_ACTUATOR_FUNC.panoShot,
      actionActuatorFuncParam: actionActuatorFuncParamOptions || getPanoShotActionDefaultParam(),
      uuid: options.actionUuid || generateKey(), // 动作id
      trigger: ACTION_TRIGGER_TYPE.reachPoint // 动作触发类型
    });
  } catch (error) {
    console.error('创建 Action 实例失败:', error);
    return null;
  }
}

// 设置拍照默认参数
export function getPanoShotActionDefaultParam() {
  const actionActuatorFuncParam = {
    wpml_payloadPositionIndex: 0,
    wpml_useGlobalPayloadLensIndex: 0,
    wpml_panoShotSubMode: 'panoShot_360',
    wpml_payloadLensIndex: `${PAYLOAD_LENS_INDEX.visable}`
  };
  return actionActuatorFuncParam;
}
//#endregion
