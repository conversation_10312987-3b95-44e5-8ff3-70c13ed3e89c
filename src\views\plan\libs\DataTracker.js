// 判断数据的保存状态,并根据不同的状态执行相应的逻辑
class DataTracker {
  constructor() {
    this.hasBeenModified = false;
    this.isSaved = false;
    this.saveCount = 0;
  }

  markAsModified() {
    this.hasBeenModified = true;
  }

  save() {
    this.isSaved = true;
    this.saveCount++;
    this.hasBeenModified = false;
  }

  reset() {
    this.hasBeenModified = false;
    this.isSaved = false;
    this.saveCount = 0;
  }

  get isSavedAndUnmodified() {
    return this.isSaved && !this.hasBeenModified;
  }

  getDataStatus() {
    return this.isSavedAndUnmodified
      ? DataTracker.DataStatus.SAVED_AND_UNMODIFIED
      : this.hasBeenModified
      ? DataTracker.DataStatus.MODIFIED_AND_UNSAVED
      : DataTracker.DataStatus.UNMODIFIED_AND_UNSAVED;
  }
}

DataTracker.DataStatus = {
  SAVED_AND_UNMODIFIED: '数据已保存且未修改',
  MODIFIED_AND_UNSAVED: '数据已修改但未保存',
  UNMODIFIED_AND_UNSAVED: '数据未修改且未保存'
};

export default DataTracker;
