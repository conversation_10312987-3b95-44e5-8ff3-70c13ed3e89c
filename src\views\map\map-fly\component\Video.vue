<template>
  <div class="wrapper">
    <Video
      ref="videoRef"
      :src="src"
      :poster="poster"
      :width="width"
      :height="height"
      :autoplay="autoplay"
      :showPlay="showPlay"
      :fit="fit"
      :preload="preload"
      :muted="muted"
      :controls="controls"
      :second="second"
    >
    </Video>
  </div>
</template>
<script>
export default { name: 'Video' };
</script>
<script setup>
import {
  ref,
  reactive,
  onUpdated,
  toRefs,
  onBeforeMount,
  onMounted,
  onUnmounted,
  onBeforeUpdate,
  onBeforeUnmount,
  onErrorCaptured
} from 'vue';
import { CesiumFlight } from '@/components/Cesium/libs/cesium/cesiumFlight';
import { getOrCreateCesiumEngineInstance } from '@/components/Cesium/libs/cesium/global';
import Video from '@/components/Video/index.vue';
const videoRef = ref(null);

//#region 属性代码块
const props = defineProps({
  autoplay: {
    // 视频就绪后是否马上播放
    type: Boolean,
    default: true
  },
  src: {
    type: String,
    default: ''
  },
  width: {
    // 中间播放暂停按钮的边长
    type: Number,
    default: 800
  },
  height: {
    // 中间播放暂停按钮的边长
    type: Number,
    default: 450
  },
  controls: {
    // 是否向用户显示控件，比如进度条，全屏
    type: Boolean,
    default: false
  },
  loop: {
    // 视频播放完成后，是否循环播放
    type: Boolean,
    default: false
  },
  muted: {
    // 是否静音
    type: Boolean,
    default: false
  },
  preload: {
    // 是否在页面加载后载入视频，如果设置了autoplay属性，则preload将被忽略；
    type: String,
    default: 'metadata' // auto:一旦页面加载，则开始加载视频; metadata:当页面加载后仅加载视频的元数据 none:页面加载后不应加载视频
  },
  showPlay: {
    // 播放暂停时是否显示播放器中间的暂停图标
    type: Boolean,
    default: false
  },
  playWidth: {
    // 中间播放暂停按钮的边长
    type: Number,
    default: 1
  },
  fit: {
    // video的poster默认图片和视频内容缩放规则
    type: String,
    default: 'contain' // none:(默认)保存原有内容，不进行缩放; fill:不保持原有比例，内容拉伸填充整个内容容器; contain:保存原有比例，内容以包含方式缩放; cover:保存原有比例，内容以覆盖方式缩放
  }
});
//#endregion
let engine = null,
  fly = null;
let width = ref(580);
let height = ref(327);
const init = () => {
  engine = getOrCreateCesiumEngineInstance('fly');
  fly = CesiumFlight.getInstance(engine.viewer);
};

//#region 生命周期
onMounted(() => {
  init();
  // videoRef?.value.play();
});

onUnmounted(() => {});
onBeforeUnmount(() => {});
//#endregion
</script>
<style scoped lang="scss">
.wrapper {
  position: absolute;
  width: 584px;
  height: 331px;
  bottom: 70px;
  right: 15px;
  background-color: antiquewhite;
  border: 2px solid #e9e7e7;
}

.dinfo {
  .item {
    display: flex;
    justify-content: flex-start;
    align-content: center;
    background-color: rgba(165, 165, 165, 0.261);
    margin-top: 5px;
    padding: 5px 10px;
    border-radius: 5px;
    width: 250px;
    .label {
      width: 100px;
      user-select: none;
      // text-align: right;
      // padding-right: 15px;
    }
  }
}

.warn {
  color: red;
}
</style>
