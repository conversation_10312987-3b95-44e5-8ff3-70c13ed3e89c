<script>
export default { name: '<PERSON><PERSON><PERSON>' };
</script>

<script setup>
import { ref, defineExpose } from 'vue';
import { RainfallEnum, EDockModeCode } from '@/views/map/map-fly-manager/components/osdInfo';
import { getDevicesBound } from '@/api/devices';
const emit = defineEmits(['OneClickDelivery','noAuth']);

const airList = ref([]);
const airportValue = ref('');
let dockInfo = reactive({
  nickname: '', //机场名
  dock_sn: '', //机场SN号
  dock_xyz: '', //机场XYZ
  device_nickname: '', //无人机名
  device_sn: '', //无人机SN号
  device_xyz: '', //无人机XYZ
  alarm_id: '', //警情ID
  flight_id: '', //飞行任务ID
  wayline_id: '' //航线文件ID
});

let osdInfo = reactive({
  mode_code: -1,
  network_state: '--',
  working_voltage: '--',
  working_current: '--',
  wind_speed: 0,
  rainfall: '--',
  environment_temperature: '--',
  temperature: '--'
});

function oneClickDelibery() {
  emit('OneClickDelivery');
}

function initAirportList () {
  getDevicesBound({
    domain: '3',
    page: 1,
    page_size: 50
  }).then(data => {
    const { list, pagination } = data;
    if (list.length > 0) {
      list.forEach(item => {
        // 根据机场SN为主键
        let oneItem = {
          nickname: item.nickname, //机场名
          dock_sn: item.device_sn, //机场SN号
          device_nickname: item.children.nickname, //无人机名
          device_sn: item.children.device_sn, //无人机SN号
          airline_id: null,
          dock_xyz: { x: item.longitude, y: item.latitude, z: item.altitude }
        };
        airList.value.push(oneItem);
      });
    }else {
      // emit('noauth')
    }
  });
}

// 枚举获取值
const getEnumKey = (enumObject, value) => {
  return Object.keys(enumObject).find(key => enumObject[key] === value);
};

/**
 * 设置组件数据
 * @param {*} options {donkInfo:{},osdInfo:{}}
 */
const setComponentData = options => {
  if (options && typeof options === 'object') {
    const nowDockInfo = options.dockInfo;
    airportValue.value = options.dockInfo.dock_sn
    for (const key in dockInfo) {
      if (nowDockInfo.hasOwnProperty(key)) {
        dockInfo[key] = nowDockInfo[key];
      }
    }
    const str = '--';
    const nowOsdInfo = options.osdInfo;
    if (nowOsdInfo !== null && nowOsdInfo !== undefined) {
      osdInfo.mode_code = nowOsdInfo.basic_osd?.mode_code;
      osdInfo.network_state = nowOsdInfo.basic_osd?.network_state?.rate;
      osdInfo.working_voltage = nowOsdInfo.work_osd?.working_voltage ?? str;
      osdInfo.working_current = nowOsdInfo.work_osd?.working_current ?? str;
      osdInfo.wind_speed = nowOsdInfo.basic_osd?.wind_speed;
      osdInfo.rainfall = nowOsdInfo.basic_osd?.rainfall;
      osdInfo.environment_temperature = nowOsdInfo.basic_osd?.environment_temperature;
      osdInfo.temperature = nowOsdInfo.basic_osd?.temperature;
    }
  }
};
// 对外抛出方法
defineExpose({
  setComponentData
});

onMounted(() => {
  initAirportList();
});
onUnmounted(() => {});
onBeforeUnmount(() => {});
</script>

<template>
  <div style="height: 100%;">
    <div class="alarm-title">
      <el-row>
        <el-col :span="2" class="nick">
          <svg-icon icon-class="airfield" style="width: 20px; height: 20px; margin-left: 4px; margin-top: 9px" />
        </el-col>
        <el-col :span="10" class="nick ellipsis" :title="dockInfo.nickname"> {{ dockInfo.nickname }}</el-col>
        <!-- 下拉框切换机场 -->
        <!-- <el-col :span="10" class="nick"> 
          <el-select
            v-model="airportValue"
            placeholder="请选择机场"
            size="small"
            style="width: 100px"
          >
            <el-option
              v-for="item in airList"
              :key="item.dock_sn"
              :label="item.nickname"
              :value="item.dock_sn"
            />
          </el-select>
        </el-col> -->
        <el-col :span="4"></el-col>
        <el-col :span="8">
          <div class="status" :class="{ 'is-active': osdInfo.mode_code === -1 }">
            {{ getEnumKey(EDockModeCode, osdInfo.mode_code) }}
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="info-container">
      <el-row class="c-row">
        <el-col :span="5" class="c-title">网速</el-col>
        <el-col :span="7" class="c-text">{{ osdInfo.network_state + ' kb/s' }} </el-col>
        <el-col :span="5" class="c-title">电压</el-col>
        <el-col :span="7" class="c-text">{{ osdInfo.working_voltage + ' mV' }}</el-col>
      </el-row>
      <el-row class="c-row">
        <el-col :span="5" class="c-title">电流</el-col>
        <el-col :span="7" class="c-text">{{ osdInfo.working_current + ' mA' }}</el-col>
        <el-col :span="5" class="c-title">风速</el-col>
        <el-col :span="7" class="c-text">{{ (osdInfo.wind_speed / 10).toFixed(2) + ' m/s' }}</el-col>
      </el-row>
      <el-row class="c-row">
        <el-col :span="5" class="c-title">雨量</el-col>
        <el-col :span="7" class="c-text">{{ getEnumKey(RainfallEnum, osdInfo.rainfall) }}</el-col>
        <el-col :span="5" class="c-title">环境</el-col>
        <el-col :span="7" class="c-text">{{ osdInfo.environment_temperature + ' °C' }}</el-col>
      </el-row>
      <el-row class="c-row">
        <el-col :span="5" class="c-title">机场</el-col>
        <el-col :span="7" class="c-text">{{ osdInfo.temperature + ' °C' }}</el-col>
      </el-row>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.alarm-title {
  height: 21.6%;
  line-height: 40px;
  background: #11253e;
  color: #fff;
  font-family: SourceHanSansSC-Bold;
  font-size: 14px;
  text-align: left;
  font-weight: 700;
  border-bottom: 1px solid #344054;
  padding-left: 8px;
  .nick {
    font-size: 14px;
    color: #f5f6f8;
    text-align: left;
    font-weight: 400;
  }
  .status {
    margin-top: 7px;
    background: rgba(42, 139, 125, 0.5);
    border-radius: 2px;
    width: 110px;
    height: 24px;
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;
    color: #39bfa4;
    text-align: center;
    line-height: 22px;
    font-weight: 400;
    &.is-active {
      color: #98A2B3;
      background: rgba($color: #98A2B3 , $alpha: 0.2);
    }
  }
}
.info-container {
  color: #fff;
  width: 100%;
  height: 16vh;
  background: #001129;
  // padding-top: 20px;
  .c-row {
    font-size: 16px;
    color: #e4e7ec;
    line-height: 22px;
    font-weight: 500;
    padding: 10px;
    padding-bottom: 0;
  }
  .c-title {
    text-align: right;
    font-family: SourceHanSansSC-Bold;
    font-size: 14px;
    color: #E4E7EC;
    text-align: right;
    line-height: 22px;
    font-weight: 700;
  }
  .c-text {
    text-align: left;
    padding-left: 14px;
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;
    color: #E4E7EC;
    text-align: left;
    line-height: 22px;
    font-weight: 400;
  }
}
.info-item {
  width: 50%;
  text-align: center;
}
.flex {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
</style>
