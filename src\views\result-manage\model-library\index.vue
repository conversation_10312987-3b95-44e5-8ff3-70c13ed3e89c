<!--模型库-->
<script>
export default {
  name: 'ModelLibrary'
};
</script>

<script setup>
import { reactive, ref } from 'vue';
import optionData from '@/utils/option-data';
import EditDialog from './EditDialog.vue';
import { getModelFiles, deleteModelT } from '@/api/live';
import moment from 'moment';
import { useRouter } from 'vue-router';
import { authorityShow } from '@/utils/authority';

const editDialogRef = ref(null);
const editDialog = reactive({
  visible: false
});

const loading = ref(false);
const total = ref(0);
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  queryType: 2,
  keyWord: ''
});

const dataList = ref([]);

const dialog = reactive({
  visible: false
});

let formData = reactive({});
let pathSuffix = '';
if (import.meta.env.VITE_APP_NODE_ENV === 'development') {
  pathSuffix = 'http://**************:24176';
} else {
  pathSuffix = window.location.origin;
}
pathSuffix = pathSuffix + '/uavfile/';
/**
 * 查询
 */
function handleQuery() {
  // Calculate the new total pages based on the new page size
  const newTotalPages = Math.ceil(total.value / queryParams.pageSize);
  
  // If current page is greater than new total pages, adjust to the last page
  if (queryParams.pageNum > newTotalPages) {
    queryParams.pageNum = newTotalPages || 1;
  }
  let startTime = '';
  let endTime = '';
  if (queryParams.range && queryParams.range.length === 2) {
    // startTime = new Date(queryParams.range[0] + ' 00:00:00');
    // endTime = new Date(queryParams.range[1] + ' 23:59:59');
    startTime = new Date(moment(queryParams.range[0]).format('YYYY-MM-DD 00:00:00')).getTime();
    endTime = new Date(moment(queryParams.range[1]).format('YYYY-MM-DD 23:59:59')).getTime();
    //   form.insurance_begin_time = form.time_range[0] + ' 00:00:00';
    // form.insurance_end_time = form.time_range[1] + ' 23:59:59';
    // ;
  }
  getModelFiles({
    // order_by: 'update_time desc',
    keyword: queryParams.keyWord,
    page: queryParams.pageNum,
    page_size: queryParams.pageSize,
    begin_time: startTime,
    end_time: endTime,
    model_type: queryParams.model_type
  }).then(data => {
    const { list, pagination } = data;
    dataList.value = list || [];
    total.value = pagination.total;
  });
}
function handleSearch() {
  queryParams.pageNum = 1;
  handleQuery();
}
/**
 * 重置查询
 */
function resetQuery() {
  queryParams.model_type = '';
  queryParams.keyWord = '';
  queryParams.range = [];
  queryParams.pageSize = 10;
  queryParams.pageNum = 1;
  handleQuery();
}
let $router = useRouter();

/**
 * 打开上传页面
 * @param {*} row
 */
function openEditDialog() {
  // $router.push({ path: '/model-upload' });
  const newRoute = $router.resolve({ path: '/model-upload' });
  window.open(newRoute.href, '_blank');
}
/**
 * 打开应用表单弹窗
 *
 * @param dicTypeId 应用ID
 */
function togglePlay(row) {
  dialog.visible = true;
  dialog.title = row.file_name;
  Object.keys(formData).map(key => {
    delete formData[key];
  });
  if (row) {
    Object.assign(formData, { ...row });
  }
}

function disabledDate(time) {
  return new Date(time) > new Date();
}

onMounted(() => {
  handleQuery();
});
//查看
const openDialog = obj => {
  editDialog.visible = true;
  editDialog.title = '编辑模型信息';

  editDialog.obj = obj;
};

/**
 * 删除
 */
function handleDelete(row) {
  ElMessageBox.confirm(`确认后将删除此模型，且无法进行恢复`, '确认删除所选模型？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    autofocus: false,
    closeOnClickModal: false,
    type: 'warning'
  }).then(() => {
    deleteModelT(row.file_id).then(response => {
      console.log('🚀 ~ deleteModelT ~ data:', response);
      ElMessage.success('删除成功');
      handleQuery();
    });
  });
}
</script>

<template>
  <div class="app-container">
    <div class="search">
      <div class="search-form">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-position="right">
          <el-form-item label="" prop="keyWord">
            <el-input
              class="input-serach"
              v-model="queryParams.keyWord"
              placeholder="请输入模型名称"
              clearable
              @keyup.enter="handleSearch"
              maxlength="64"
            />
          </el-form-item>

          <el-form-item label="" prop="keyWord">
            <el-date-picker
              class="input-serach"
              v-model="queryParams.range"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @change="handleSearch"
              :disabled-date="disabledDate"
            />
          </el-form-item>
          <el-form-item label="" prop="model_type">
            <el-select
              class="input-serach"
              v-model="queryParams.model_type"
              placeholder="请选择模型类型"
              clearable
              @change="handleSearch"
            >
              <el-option
                v-for="(item, index) in optionData.modelTypeOption"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="search-btn">
        <el-button type="primary" @click="handleSearch()"><i-ep-search />搜索</el-button>
        <el-button @click="resetQuery()"><i-ep-refresh />重置</el-button>
      </div>
    </div>
    <el-card shadow="never">
      <template #header>
        <el-button type="primary" @click="openEditDialog()" v-if="authorityShow('importModel')"><i-ep-upload />导入</el-button>
      </template>

      <el-table highlight-current-row :data="dataList" v-loading="loading" stripe height="540">
        <el-table-column label="模型类型" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ optionData.modelTypeOption.find(item => item.value == scope.row.model_type)?.label }}</span>
          </template>
        </el-table-column>

        <el-table-column label="模型名称" prop="model_name" show-overflow-tooltip />
        <el-table-column label="文件名称" prop="file_name" show-overflow-tooltip />
        <el-table-column label="上传时间" prop="create_time" show-overflow-tooltip />
        <el-table-column label="上传用户" prop="creator" show-overflow-tooltip />

        <el-table-column fixed="right" label="操作" align="center" v-if="authorityShow('editModel') || authorityShow('deleteModel')">
          <template #default="scope">
            <el-button type="primary" link @click.stop="openDialog(scope.row)" v-if="authorityShow('editModel')">编辑</el-button>
            <el-button type="danger" link @click.stop="handleDelete(scope.row)" v-if="authorityShow('deleteModel')">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </el-card>

    <div v-if="editDialog.visible">
      <EditDialog
        ref="editDialogRef"
        v-model:visible="editDialog.visible"
        :title="editDialog.title"
        v-model:obj="editDialog.obj"
        @submit="resetQuery"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.input-serach {
  width: 200px;
}

.flex-center {
  display: flex;
  align-items: center;

  .status {
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #6e6e6e;
  }
}

.search {
  display: flex;
  align-items: center;
  padding: 0 24px;

  .search-form {
    padding-top: 16px;
    flex: 1;
  }
}

.dialog-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;

  .select-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 4px;
    cursor: pointer;
    height: 204px;
    width: 176px;
    background: #f8f9fb;
    border-radius: 4px;
    border: 1px solid #e7e8f2;
    text-align: center;

    img {
      width: 80px;
      height: 80px;
      margin: 24px auto 5px;
    }

    .title {
      font-size: 16px;
      font-weight: 700;
      margin-bottom: 8px;
    }

    .desc {
      font-size: 12px;
      color: #000;
      padding: 0 28px;
    }
  }

  .select-item:hover {
    background: #eff6ff;
  }
}

.plan-info {
  display: flex;
  flex-wrap: wrap;

  .plan-item {
    margin: 0 20px 20px 0;
    padding: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #fff;
    border: 1px solid #e7e8f2;
    box-shadow: 0 8px 14px #959cb600;
    border-radius: 6px;
    width: 18%;

    cursor: pointer;

    .plan-item-left {
      position: relative;
      height: 142px;

      .video-play-view {
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        .video-play-btn {
          cursor: pointer;
          margin: auto;
        }
      }

      .left-img {
        width: 100%;
        height: 100%;
      }
      .vedio-img {
        opacity: 0.5;
        width: 100%;
        height: 100%;
      }

      .btn-box {
        position: absolute;
        top: 10px;
        left: 10px;
        display: flex;
        flex-direction: column;

        .delete {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 20px;
          background: #0000007a;
          border-radius: 4px;
          opacity: 0;
          margin-bottom: 6px;
          padding: 5px;
        }
      }

      .time-box {
        position: absolute;
        left: 0;
        bottom: 0;
        display: inline-flex;
        justify-content: space-between;
        padding: 0 12px;
        width: 176px;
        height: 26px;
        line-height: 26px;
        font-size: 12px;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
      }
    }

    .plan-item-right {
      padding: 0 16px 0;
      width: 206px;
      line-height: 20px;
      color: #0009;
      font-size: 12px;

      .name {
        line-height: 18px;
        font-weight: 700;
        color: #000;
        font-size: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .info-div {
        .item-info {
          display: flex;
          align-items: center;
          line-height: 22px;

          img {
            margin-right: 4px;
            width: 16px;
            height: 16px;
          }
        }
      }
    }
  }
}
</style>
