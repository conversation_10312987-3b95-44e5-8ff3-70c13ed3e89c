// 飞行器偏航角相关
// kml 录像默认结构如下
// clockwise：顺时针旋转
// counterClockwise：逆时针旋转

{
  /* <wpml:action>
<wpml:actionId>3</wpml:actionId>
<wpml:actionActuatorFunc>rotateYaw</wpml:actionActuatorFunc>
<wpml:actionActuatorFuncParam>
  <wpml:aircraftHeading>66.2</wpml:aircraftHeading>
  <wpml:aircraftPathMode>counterClockwise</wpml:aircraftPathMode>
</wpml:actionActuatorFuncParam>
</wpml:action> */
}
import { ACTION_ACTUATOR_FUNC, WAY_POINT_HEADING_PATH_MODE } from '../../props';
import { Action } from '../../waylines';
import { ACTION_TRIGGER_TYPE } from '@/utils/constants';
import { generateKey } from '@/utils';
//#region 悬停动作
/**
 * 创建悬停动作
 * @param {Object} options 动作配置项，应包含actionId、actionActuatorFunc
 * @param {Object|null} actionActuatorFuncParamOptions 动作执行器参数配置，可选
 * @returns {Action|null} 返回Action实例或在配置不正确时返回null
 */
export function createRotateYawAction(options, actionActuatorFuncParamOptions = null) {
  try {
    // 创建动作
    if (!options) {
      return null;
    }
    return new Action({
      actionId: options.actionId || 0,
      actionActuatorFunc: ACTION_ACTUATOR_FUNC.rotateYaw,
      actionActuatorFuncParam: actionActuatorFuncParamOptions || getRotateYawActionDefaultParam(),
      uuid: options.actionUuid || generateKey(), // 动作id
      trigger: ACTION_TRIGGER_TYPE.reachPoint
    });
  } catch (error) {
    console.error('创建 Action 实例失败:', error);
    return null;
  }
}
// 获取悬停默认参数
export function getRotateYawActionDefaultParam() {
  const actionActuatorFuncParam = {
    wpml_aircraftHeading: 0, // 角度 -180 到 + 180
    wpml_aircraftPathMode: WAY_POINT_HEADING_PATH_MODE.counterClockwise
  };
  return actionActuatorFuncParam;
}
//#endregion
