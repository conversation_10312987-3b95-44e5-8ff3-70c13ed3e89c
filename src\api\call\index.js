import request from '@/utils/request'; 

/**
 * 创建新的通话会话
 * @param {Object} data - 通话会话数据
 * @returns {Promise<Object>} - 创建的通话会话
 */
export function createCallSession(data) {
  return request({
    url: `/call/session/create`,
    method: 'post',
    data
  });
}

/**
 * 根据ID获取通话会话详情
 * @param {string} sessionId - 通话会话ID
 * @returns {Promise<Object>} - 通话会话详情
 */
export function getCallSession(sessionId) {
  return request({
    url: `/call/session/${sessionId}`,
    method: 'get'
  });
}

/**
 * 接听通话
 * @param {Object} data - 接听数据，包含sessionId和userId
 * @returns {Promise<Object>} - 响应数据
 */
export function answerCallSession(data) {
  return request({
    url: `/call/answer`,
    method: 'post',
    data
  });
}

/**
 * 拒绝通话
 * @param {Object} data - 拒绝数据，包含sessionId和userId
 * @returns {Promise<Object>} - 响应数据
 */
export function rejectCallSession(data) {
  return request({
    url: `/call/reject`,
    method: 'post',
    data
  });
}

/**
 * 结束通话会话
 * @param {Object} data - 结束数据，包含userId
 * @returns {Promise<Object>} - 响应数据
 */
export function endCallSession(data) {
  return request({
    url: `/call/session/end`,
    method: 'put',
    data
  });
}

/**
 * 离开通话会话
 * @param {Object} data - 离开数据，包含sessionId和userId
 * @returns {Promise<Object>} - 响应数据
 */
export function leaveCallSession(data) {
  return request({
    url: `/call/leave`,
    method: 'post',
    data
  });
}

/**
 * 获取用户的活动通话会话
 * @param {string} userId - 用户ID
 * @returns {Promise<Object|null>} - 活动通话会话或null
 */
export function getActiveCallSession(userId) {
  return request({
    url: `/call/session/active`,
    method: 'get',
    params: { userId }
  });
}

/**
 * 获取声网通道的Token
 * @param {string} uid - 用户播放id
 * @param {string} channelName - 通道名称
 * @returns {Promise<Object>} - Token数据
 */
export function getAgoraToken(uid, channelName) {
  return request({
    url: `/call/token`,
    method: 'get',
    params: { uid, channelName }
  });
}

/**
 * 刷新声网Token
 * @param {string} uid - 用户ID
 * @param {string} channelName - 通道名称
 * @returns {Promise<Object>} - 新的Token数据
 */
export function refreshAgoraToken(uid, channelName) {
  return request({
    url: `/call/token/refresh`,
    method: 'post',
    params: { uid, channelName }
  });
} 


/**
 * 添加通话参与者
 * @param {object} data - 参与者数据
 * @returns {Promise<Object>} - 
 */
export function addParticipant(data) {
  return request({
    url: `/call/add-participant`,
    method: 'post',
    data
  });
} 