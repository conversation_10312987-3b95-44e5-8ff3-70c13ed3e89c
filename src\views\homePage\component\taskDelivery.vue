<script>
export default { name: 'Drone<PERSON>ist' };
</script>

<script setup>
import { ref, reactive, toRaw } from 'vue';
import { getDevicesBound } from '@/api/devices';
import { DOMAIN } from '@/utils/constants';
import optionData from '@/utils/option-data';
import { getJJConfig } from '@/api/task';

const dataFormRef = ref(ElForm);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
});
const airOption = ref([]); //执行机场options
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
});
const dataList = ref([]);
const total = ref(0);
const form = reactive({
	isRadius:false,
});
const emit = defineEmits(['onClick','onCancel','ok']);
const rules = reactive({
  dock_sn: [{ required: true, message: '请选择执行任务机场', trigger: 'change' }],
  default_flight_height: [{ required: true, message: '请输入默认航线高度', trigger: 'blur',type: 'Number'}],
  default_flight_speed: [{ required: true, message: '请输入默认飞行速度', trigger: 'blur',type: 'Number'}],
  max_flight_distance: [{ required: true, message: '请输入最大飞行距离', trigger: 'blur',type: 'Number'}],
  default_reach_action: [{ required: true, message: '请选择到达目标动作', trigger: 'change' }],
  default_stay_time: [{ required: true, message: '请输入悬停时间', trigger: 'blur',type: 'Number'}],
  circle_radius: [{ required: true, message: '请输入环绕飞行的半径', trigger: 'blur',type: 'Number'}],
  photo_interval: [{ required: true, message: '请输入拍照时间间隔', trigger: 'blur',type: 'Number'}],
  video_interval: [{ required: true, message: '请输入录像时长', trigger: 'blur',type: 'Number'}],
  open_photo: [{ required: true, message: '请选择到达目标拍照', trigger: 'change' }],
	open_video: [{ required: true, message: '请选择到达目标录像', trigger: 'change' }],
});

function cancelTask () {
	dataFormRef.value.resetFields();
	emit('onCancel')
}

onMounted(() => {
	form.task_type = true
	form.disable = true
	form.isRadius = false
	getJJConfigAPI();
	getAirport();
});

onUnmounted(() => {
	form.task_type = true
	form.disable = true
  form.isRadius = false
});

function handleClick (type,item) {
	emit('onClick',type,item)
}

/**
 * 查询
 */
 function getAirport() {
  getDevicesBound({
    domain: DOMAIN.DOCK,
    page: 1,
    page_size: 100
  }).then(data => {
    const { list } = data;
    airOption.value = list || [];
  });
}

//保存
function handleSubmit() {
  dataFormRef.value.validate(isValid => {
    if (isValid) {
			let params = { ...form };
			emit('ok',params)
    }
  });
}

/**
 * 获取配置信息
 */
 function getJJConfigAPI() {
  getJJConfig({}).then(data => {
		if(toRaw(data).default_reach_action == 1) {
			form.isRadius = true
			// form.default_stay_time = null
		}
    Object.assign(form, data);
  });
}

function changeSetting (val) {
	if(!val) {
		form.disable = false
	}else {
		form.disable = true
	}
}

// 切换目标动作
function changeAction (value) {
  if(value) {
    form.circle_radius = 50
    form.isRadius = true
  }else {
    // form.circle_radius = null
    // form.default_stay_time = null
    form.isRadius = false
  }
}

</script>

<template>
	<div v-if="visible">
		<div class="task-title">
			<span>任务下发</span>
		</div>
		<div class="form-list">
			<el-form class="app-form" ref="dataFormRef" :model="form" :rules="rules" label-width="120px" label-position="top">
				<el-form-item label="选择执行任务机场" prop="dock_sn">
					<el-select v-model="form.dock_sn" placeholder="选择执行任务机场" style="width: 390px;">
						<el-option
							v-for="item in airOption"
							:key="item.device_sn"
							:label="item.nickname"
							:value="item.device_sn"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="使用默认配置" prop="task_type">
					<el-switch
						v-model="form.task_type"
						class="ml-2"
						@change="changeSetting"
					/>
					<!-- <el-select v-model="form.task_type" placeholder="请选择任务类型">
						<el-option v-for="item in optionData.typeOptions" :key="item.value" :label="item.label" :value="item.value" />
					</el-select> -->
				</el-form-item>
				<el-row>
					<el-col :span="12">
						<el-form-item label="默认航线高度（米）" prop="default_flight_height" >
							<el-input-number
								:disabled="form.disable"
								v-model="form.default_flight_height"
								:min="100"
								:step="1"
								style="width: 180px;"
								controls-position="right"
								placeholder="请输入最大飞行距离"
							/>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="默认飞行速度（米/秒）" prop="default_flight_speed" >
							<el-input-number
								:disabled="form.disable"
								v-model="form.default_flight_speed"
								:min="0"
								:step="1"
								style="width: 180px;"
								controls-position="right"
								placeholder="请输入最大飞行距离"
							/>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="12">
						<el-form-item label="最大飞行距离（米）" prop="max_flight_distance">
							<el-input-number
								v-model="form.max_flight_distance"
								:disabled="form.disable"
								:min="0"
								:step="1"
								style="width: 180px;"
								controls-position="right"
								placeholder="请输入最大飞行距离"
							/>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="12">
						<el-form-item label="达到目标动作" prop="default_reach_action">
							<el-select v-model="form.default_reach_action" placeholder="请选择" style="width: 180px;" :disabled="form.disable" @change="changeAction">
								<el-option
									v-for="item in optionData.targetOptions"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12" v-if="form.isRadius">
						<el-form-item label="环绕飞行的半径（米）" prop="circle_radius" v-if="form.isRadius">
							<el-input-number
								v-model="form.circle_radius"
								:disabled="form.disable"
								:min="50"
								:max="500"
								:step="10"
								controls-position="right"
								placeholder="请输入环绕飞行的半径"
								style="width: 180px;"
							/>
						</el-form-item>
					</el-col>
					<el-col :span="12" v-else>
						<el-form-item label="悬停时间（秒）" prop="default_stay_time">
							<el-input-number
								v-model="form.default_stay_time"
								:disabled="form.disable"
								:min="0"
								:step="1"
								controls-position="right"
								placeholder="请输入悬停时间"
								style="width: 180px;"
							/>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="12">
						<el-form-item label="到达目标拍照" prop="open_photo">
							<el-switch v-model="form.open_photo" :disabled="form.disable" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="拍照间隔（秒）" prop="photo_interval">
							<el-input-number
								v-model="form.photo_interval"
								:disabled="form.disable"
								:min="0"
								:step="1"
								controls-position="right"
								placeholder="请输入拍照间隔"
								style="width: 180px;"
							/>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="12">
						<el-form-item label="到达目标录像" prop="open_video">
							<el-switch v-model="form.open_video" :disabled="form.disable"/>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</div>
		<div class="task-bottom">
			<el-button type="primary" @click="handleSubmit">确认</el-button>
			<el-button @click="cancelTask">取消</el-button>
		</div>
	</div>
</template>

<style lang="scss">

</style>
<style lang="scss" scoped>
.task-bottom {
	background: #11253E;
	height: 48px;
	line-height: 48px;
	border-top: 1px solid #475467;
	text-align: center;
}
:deep(.el-input__wrapper) {
	background-color: #11253E;
	border: 1px solid #475467;
	border-color: #475467 !important;
	box-shadow: none;
}
:deep(.el-input-number__decrease)  {
	background-color: #11253E;
	color: #fff;
	box-shadow: none;
	border-left: 1px solid #475467;
}
:deep(.el-input.is-disabled .el-input__wrapper) {
	background-color: #11253E;
	box-shadow: none;
	color: #606266;
}
:deep(.el-select) {
	--el-select-border-color-hover: none;
	--el-select-disabled-border:none;
}
:deep(.el-input-number.is-controls-right .el-input-number__increase) {
	border-bottom: 1px solid #475467;
	border-left: 1px solid #475467;
	box-shadow: none;
}
:deep(.el-input-number.is-controls-right .el-input-number__decrease) {
	border-left: 1px solid #475467;
	box-shadow: none;
}
:deep(.el-input-number__increase) {
	color: #fff;
	box-shadow: none;
	border-left: 1px solid #475467;
	background-color: #11253E;
}
:deep(.el-input__inner) {
	color: #98A2B3;
}
.task-title {
	height: 38px;
	line-height: 38px;
	background: #11253E;
	color: #fff;
	font-family: SourceHanSansSC-Bold;
	font-size: 14px;
	text-align: left;
	font-weight: 700;
	border-bottom: 1px solid #344054;
	padding-left: 8px;
}
.form-list {
	background: #11253E;
	height: 560px;
	padding: 17px;
	:deep(.el-form-item__label) {
		color: #fff;
	}
}
.flex {
	height: 38px;
	line-height: 38px;
	display: flex;
	justify-content: space-between;
}
</style>
