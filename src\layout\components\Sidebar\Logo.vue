<script setup>
import { onMounted, reactive } from 'vue';
import { useSettingsStore } from '@/store/modules/settings.js';
import { getDeptSysSetting } from '@/api/wayline';
const defaultLogoIcon = new URL('/resource/images/default-logo.jpeg', import.meta.url).href;;
const settingsStore = useSettingsStore();
defineProps({
  collapse: {
    type: Boolean,
    required: true
  }
});
const data = reactive({});
onMounted(() => {
  initData();
});

function initData() {
  getDeptSysSetting({}).then(res => {
    const { base_config = {} } = res;
    data.name = base_config.sys_name;
    data.logo = base_config.sys_logo_url;
  });
}
// 默认logo图片路径
function handleImageError() {
  data.logo = defaultLogoIcon;
}

const logo = ref(new URL(`../../../assets/logo.png`, import.meta.url).href);
</script>

<template>
  <div class="titleBox">
    <transition name="sidebarLogoFade">
      <div
        v-if="collapse"
        key="collapse"
        class="linkStyle"
      >
        <el-image
          class="logo"
          :src="data.logo"
          fit="cover"
          @error="handleImageError"
        />
        <span class="title text-white"
          >{{ data.name }}</span>
      </div>

      <div
        v-else
        key="expand"
        class="linkStyle"
      >
         <el-image
          class="logo"
          :src="data.logo"
          fit="cover"
          @error="handleImageError"
        />
        <span class="title text-white"
          >{{ data.name }}</span>
      </div>
    </transition>
  </div>
</template>

<style lang="scss" scoped>
.titleBox {
  width: 100%;
  height: 60px;
  background-color: #11253e;
  .linkStyle {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .logo {
      width: 24px;
      height: 24px;
    }
    .title {
      font-size: 16px;
      margin-left: 10px;
      font-weight: 600;
      color: #fff;
    }
  }
}
// https://cn.vuejs.org/guide/built-ins/transition.html#the-transition-component
.sidebarLogoFade-enter-active {
  transition: opacity 2s;
}

.sidebarLogoFade-leave-active,
.sidebarLogoFade-enter-from,
.sidebarLogoFade-leave-to {
  opacity: 0;
}
</style>
