// 全局SCSS变量

:root {
  --menuBg: #11253e; // 菜单栏背景色
  --menuText: #bfcbd9; // 菜单栏文字颜色
  --menuActiveText: #409eff; // 菜单栏选中文字颜色
  --menuHover: #11253e; // 菜单栏hover颜色
  --menuHoverText: #409eff; // 菜单栏hover文字颜色

  --subMenuBg: #11253e; // 子菜单背景色
  --subMenuActiveText: #f4f4f5; // 子菜单选中文字颜色
  --subMenuHover: #001129; // 子菜单hover背景颜色
  --subMenuHoverText: #f4f4f5; // 子菜单hover文字颜色
  --subMenuActiveHoverText: #f4f4f5; // 子菜单选中时hover文字颜色

  --el-menu-item-height: 48px; // 菜单栏高度
  --miniSideBarWidth: 54px; // 菜单栏收缩宽度
  --sideBarWidth: 210px; // 菜单栏宽度

  --hideSideBarWidth: -210px; //

  --tableHeaderBg: #eff6ff; // 表格头部背景色
  --tableHeaderColor: #475467; // 表格头部字体颜色
  --tableHeaderPadding: 15px 0; // 表格头部内边距

  --appMainMinWidth: 1346px; // 主体最小宽度

  --el-dropdown-menuItem-hover-fill: var(
    --el-color-primary-light-9
  ); // el-dropdown-menu hover背景色
  --el-dropdown-menuItem-hover-color: var(
    --el-color-primary
  ); // el-dropdown-menu hover文字颜色
  --dialogheaderBg: #eff6ff;
  --el-border-radius-mid: 8px; // border-radius 8px
  --el-dialog__footer-text-align-center: center; // footer操作栏按钮居中

  //暗色系重写样式
  --el-bg-color-page:#001129; //全局背景色
  --el-bg-color-overlay:#11253E; //搜索框背景色
  --el-fill-color-blank:#11253E;//卡片背景色
  --el-border-color-light:#11253E;//去除虚线亮色
  --el-border-color:#465467;//输入框边框颜色
  --el-input-text-color:#fff;//输入框文字颜色
  --el-text-color-regular:#fff;//输入框文字颜色
  --el-text-color-primary:#fff;//默认文字颜色
  --el-text-color-placeholder:#475467;//输入框placeHolder文字颜色
  --el-input-focus-border-color:#0094FF;//输入框选中高亮色
  --el-bg-color:#11253E;//表格背景色
  --el-color-primary-light-9:#1F2F49;
  --el-text-color-secondary:#fff;//表头字体颜色
  --el-table-tr-bg-color:#11253E;//表格行背景色
  --el-table-border-color:#11253E;//表格边框颜色
  --el-border-color-lighter:#11253E;//表格边框颜色
  --el-fill-color-light:#1F2F49;//鼠标滑过表格行
  --tableHeaderBg: #1F2F49; // 表格头部背景色
  --tableHeaderColor: #fff; // 表格头部字体颜色
  --dialogheaderBg: #1F2F49; //弹窗头部颜色
  --el-border-color-extra-light:#195EBF;
  --el-overlay-color-lighter:rgba(0,0,0,0.7);
}

$menuBg: var(--menuBg);
$menuText: var(--menuText);
$menuActiveText: var(--menuActiveText);
$menuHover: var(--menuHover);
$menuHoverText: var(--menuHoverText);

$subMenuBg: var(--subMenuBg);
$subMenuActiveText: var(--subMenuActiveText);
$subMenuHover: var(--subMenuHover);
$subMenuHoverText: var(--subMenuHoverText);
$subMenuActiveHoverText: var(--subMenuActiveHoverText);

$sideBarWidth: var(--sideBarWidth);
$miniSideBarWidth: var(--miniSideBarWidth);
$appMainMinWidth: var(--appMainMinWidth);
$hideSideBarWidth: var(--hideSideBarWidth);

$tableHeaderColor: var(--tableHeaderColor);
$tableHeaderPadding: var(--tableHeaderPadding);
$tableHeaderBg: var(--tableHeaderBg);
